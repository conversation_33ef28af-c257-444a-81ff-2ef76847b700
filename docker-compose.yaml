version: "3.3"
services:
  api:
    restart: always
    image: **************/foradar/api:latest
    network_mode: overlay
    volumes:
      - /etc/foradar:/etc/foradar
      - /data/:/data
      - /etc/resolv.conf:/etc/resolv.conf
    logging:
      driver: syslog
      options:
        syslog-address: "udp://***********:514"
        tag: "imageName:{{.ImageName}},containerName:{{.Name}},containerId:{{.ID}}"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      TZ: Asia/Shanghai
      CONSUL_ADDRESS: ***********
      CONSUL_PORT: 8500
      CONSUL_PREFIX: /foradar
      CONSUL_TOKEN: bfcadd76-e100-b71e-46fc-55d2d0ceb581
    deploy:
      replicas: 1
      labels:
        bmh.foradar.service.type: api
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 5s
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
    ports:
      - "8080:8080"
    ulimits:
      memlock:
        soft: -1
        hard: -1
  core:
    restart: always
    image: **************/foradar/core:latest
    network_mode: overlay
    volumes:
      - /etc/foradar:/etc/foradar
      - /data:/data
      - /etc/resolv.conf:/etc/resolv.conf
    logging:
      driver: syslog
      options:
        syslog-address: "udp://***********:514"
        tag: "imageName:{{.ImageName}},containerName:{{.Name}},containerId:{{.ID}}"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      TZ: Asia/Shanghai
      CONSUL_ADDRESS: ***********
      CONSUL_PORT: 8500
      CONSUL_PREFIX: /foradar
      CONSUL_TOKEN: bfcadd76-e100-b71e-46fc-55d2d0ceb581
    depends_on:
      - jaeger
    deploy:
      replicas: 5
      labels:
        bmh.foradar.service.type: core
      placement:
        constraints:
          - node.role != manager
      restart_policy:
        condition: on-failure
        delay: 5s
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
    ulimits:
      memlock:
        soft: -1
        hard: -1
  crawler:
    restart: always
    image: **************/foradar/crawler:latest
    network_mode: overlay
    volumes:
      - /etc/foradar:/etc/foradar
      - /data:/data
      - /etc/resolv.conf:/etc/resolv.conf
    logging:
      driver: syslog
      options:
        syslog-address: "udp://***********:514"
        tag: "imageName:{{.ImageName}},containerName:{{.Name}},containerId:{{.ID}}"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      TZ: Asia/Shanghai
      CONSUL_ADDRESS: ***********
      CONSUL_PORT: 8500
      CONSUL_PREFIX: /foradar
      CONSUL_TOKEN: bfcadd76-e100-b71e-46fc-55d2d0ceb581
    depends_on:
      - jaeger
    deploy:
      replicas: 10
      labels:
        bmh.foradar.service.type: crawler
      placement:
        constraints:
          - node.role != manager
      restart_policy:
        condition: on-failure
        delay: 5s
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
    ulimits:
      nproc: 1024
      nofile:
        soft: 1024
        hard: 1024
      memlock:
        soft: -1
        hard: -1
  web:
    restart: always
    image: **************/foradar/web:latest
    network_mode: overlay
    volumes:
      - /etc/foradar:/etc/foradar
      - /data:/data
      - /etc/resolv.conf:/etc/resolv.conf
    logging:
      driver: syslog
      options:
        syslog-address: "udp://***********:514"
        tag: "imageName:{{.ImageName}},containerName:{{.Name}},containerId:{{.ID}}"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      TZ: Asia/Shanghai
      CONSUL_ADDRESS: ***********
      CONSUL_PORT: 8500
      CONSUL_PREFIX: /foradar
      CONSUL_TOKEN: bfcadd76-e100-b71e-46fc-55d2d0ceb581
    deploy:
      replicas: 2
      labels:
        bmh.foradar.service.type: web
      placement:
        constraints:
          - node.role != manager
      restart_policy:
        condition: on-failure
        delay: 5s
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
    ulimits:
      memlock:
        soft: -1
        hard: -1
  scan:
    restart: always
    image: **************/foradar/scan:latest
    network_mode: overlay
    volumes:
      - /etc/foradar:/etc/foradar
      - /data:/data
      - /etc/resolv.conf:/etc/resolv.conf
    logging:
      driver: syslog
      options:
        syslog-address: "udp://***********:514"
        tag: "imageName:{{.ImageName}},containerName:{{.Name}},containerId:{{.ID}}"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      TZ: Asia/Shanghai
      CONSUL_ADDRESS: ***********
      CONSUL_PORT: 8500
      CONSUL_PREFIX: /foradar
      CONSUL_TOKEN: bfcadd76-e100-b71e-46fc-55d2d0ceb581
    deploy:
      replicas: 5
      labels:
        bmh.foradar.service.type: scan
      placement:
        constraints:
          - node.role != manager
      restart_policy:
        condition: on-failure
        delay: 5s
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
    ulimits:
      memlock:
        soft: -1
        hard: -1
  cron:
    restart: always
    image: **************/foradar/cron:latest
    network_mode: overlay
    volumes:
      - /etc/foradar:/etc/foradar
      - /data:/data
      - /etc/resolv.conf:/etc/resolv.conf
    logging:
      driver: syslog
      options:
        syslog-address: "udp://***********:514"
        tag: "imageName:{{.ImageName}},containerName:{{.Name}},containerId:{{.ID}}"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      TZ: Asia/Shanghai
      CONSUL_ADDRESS: ***********
      CONSUL_PORT: 8500
      CONSUL_PREFIX: /foradar
      CONSUL_TOKEN: bfcadd76-e100-b71e-46fc-55d2d0ceb581
    deploy:
      replicas: 1
      labels:
        bmh.foradar.service.type: cron
      placement:
        constraints:
          - node.role != manager
      restart_policy:
        condition: on-failure
        delay: 5s
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: continue
        monitor: 60s
        max_failure_ratio: 0.3
    ulimits:
      memlock:
        soft: -1
        hard: -1
