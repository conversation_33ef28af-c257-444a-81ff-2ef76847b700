# 定义 stages
stages:
  - lint
  - vuln_test
  - build
  - deploy
  - local_build
variables:
  AUTO_DEVOPS_DISABLE_FETCH: "true"
  GIT_STRATEGY: none
  MICRO_DIR: "/data/micro-service"

lint:
  stage: lint
  script:
    # git pull
    - cd $MICRO_DIR && git reset --hard HEAD && git checkout $CI_COMMIT_BRANCH && git pull origin $CI_COMMIT_BRANCH
    - cd $MICRO_DIR && go mod tidy
    # generate proto
    - cd $MICRO_DIR/coreService && make
    - cd $MICRO_DIR/webService && make
    - cd $MICRO_DIR/crawlerService && make
    - cd $MICRO_DIR/scanService && make
    - cd $MICRO_DIR/cronService && make
    - cd $MICRO_DIR/apiService && make
  only:
    - develop
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - deploy-11.14
  allow_failure: false
  variables:
    GOROOT: "/usr/local/go"
    GORELEASER_CURRENT_TAG: "0.0.1-alpha.1"
    GORELEASER_PREVIOUS_TAG: "0.0.1-alpha.1"
    DOCKER_REGISTER_ADDR: "************"

vuln_test:
  stage: vuln_test
  coverage: '/total:\s+\(statements\)\s+([\d.]+%)/'
  needs:
    - lint
  when: manual
  allow_failure: true
  only:
    - develop
  tags:
    - deploy-11.14
  script:
    - cd $MICRO_DIR
    # 使用新的覆盖率脚本
    - sudo chmod +x accurate_coverage.sh
    - ./accurate_coverage.sh
    # import pkg vuln scan
    # - osv-scanner .

build:
  stage: build
  needs:
    - lint
  script:
    - docker_login_117
    - docker volume prune -f && docker image prune -f && docker system prune -f
    - cd $MICRO_DIR && git tag -l| xargs -r git tag -d && sudo chmod 777 -Rf $MICRO_DIR/bin
    - cd $MICRO_DIR && goreleaser --snapshot --skip=validate --skip=publish --skip=sign --clean
    - docker images|grep foradar|grep $DOCKER_REGISTER_ADDR|grep -v "<none>"|awk '{printf "%s:%s\n",$1,$2}'|xargs -r -t -I {} docker push {}
    - docker images|grep foradar|grep -v php |grep $DOCKER_REGISTER_ADDR|awk '{print $3}'|xargs -r docker rmi -f
  only:
    - develop
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - deploy-11.14
  variables:
    GOROOT: "/usr/local/go"
    GORELEASER_CURRENT_TAG: "0.0.1-alpha.1"
    GORELEASER_PREVIOUS_TAG: "0.0.1-alpha.1"
    DOCKER_REGISTER_ADDR: "************"

update-image-amd64:
  stage: local_build
  script:
    - sh -c 'if [ "${UPDATE_VERSION}" == "" ];then echo "请输入升级版本号!"; exit 1; fi'
    - sh -c 'if [ "${UPDATE_SERVICE}" == "" ];then echo "请输入要升级的镜像服务,只支持:[api,scan,web,cron,crawler]!"; exit 1; fi'
    - cd /data/foradar_local/amd64
    - sudo rm -rf ${UPDATE_VERSION}_${UPDATE_SERVICE}_update ${UPDATE_VERSION}_${UPDATE_SERVICE}_update.tar.gz
    - sudo cp -rf image_update_template/  ${UPDATE_VERSION}_${UPDATE_SERVICE}_update/
    - sudo docker pull ************/foradar/${UPDATE_SERVICE}:0.0.1
    - sudo docker tag ************/foradar/${UPDATE_SERVICE}:0.0.1 foradar_${UPDATE_SERVICE}:latest
    - cd ${UPDATE_VERSION}_${UPDATE_SERVICE}_update/
    - sudo docker save -o foradar_${UPDATE_SERVICE} foradar_${UPDATE_SERVICE}:latest
    - sudo rm -rf foradar_${UPDATE_SERVICE}.bz2 && sudo bzip2 foradar_${UPDATE_SERVICE} && sudo rm -rf foradar_${UPDATE_SERVICE}
    - cd /data/foradar_local/amd64
    - sudo tar -cvf - ${UPDATE_VERSION}_${UPDATE_SERVICE}_update |sudo pigz -p 4 > ${UPDATE_VERSION}_${UPDATE_SERVICE}_update.tar.gz
    - sudo rm -rf ${UPDATE_VERSION}_${UPDATE_SERVICE}_update
    - echo -e "\033[40;33m升级包下载：  scp root@************:/data/foradar_local/amd64/${UPDATE_VERSION}_${UPDATE_SERVICE}_update.tar.gz ./ \033[0m"
  after_script:
    - sh -c 'if [ "$CI_JOB_STATUS" == "failed" ]; then cd /data/foradar_local/amd64; sudo rm -rf ${UPDATE_VERSION}_${UPDATE_SERVICE}_update; fi'
  only:
    - develop
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - deploy-11.14
  when: manual

deploy:
  stage: deploy
  when: on_success
  needs: [build]
  script:
    - docker_login_117
    - docker volume prune -f && docker image prune -f && docker system prune -f
    - docker service ls|grep foradar |awk 'split($5,addr,":");{printf "docker service update %s --image %s:%s --with-registry-auth --update-parallelism 2 -d --update-failure-action=rollback \n",$2,addr[1],"0.0.1"}'|grep docker|while read x; do echo -e "$x";eval "$x"; done
  only:
    - develop
  environment:
    name: $CI_COMMIT_BRANCH
  tags:
    - foradar2.0_develop