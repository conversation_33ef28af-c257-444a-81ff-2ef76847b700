package ksubdomain

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/pkg/cfg"
	"strconv"
	"strings"
	"time"

	"micro-service/middleware/mysql/auth_access_client"
	"micro-service/middleware/mysql/domain_burst"
	"micro-service/middleware/mysql/domain_task"
	"micro-service/middleware/mysql/domain_task_golang"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	scanBin "micro-service/scanService/bin_blast"
	"micro-service/scanService/fofa"
	corePb "micro-service/scanService/proto"

	"github.com/panda843/ksubdomain/core"
)

func QueryBurstSubdomains(ctx context.Context, req *corePb.ParamsRequest, rsp *corePb.SubdomainResponse) error {
	req.DomainList = utils.ListDistinctNonZero(req.DomainList)
	if len(req.DomainList) == 0 {
		return errors.New("爆破域名不能为空")
	}

	taskName := fmt.Sprintf("子域名爆破任务%s", time.Now().Format("2006_01_02_15_04_05"))
	jsonStr, _ := json.Marshal(req.DomainList)
	stu := &domain_task_golang.Task{
		Name:       taskName,
		DomainList: &sql.NullString{String: string(jsonStr), Valid: true},
		Status:     domain_task_golang.TaskStatusDoing,
		Level:      uint8(req.Level),
		Bandwidth:  strconv.Itoa(int(req.Bandwidth)),
		Modify:     uint8(req.Modify), // 任务模式
	}
	err := domain_task_golang.NewTasker().Create(stu)
	if err != nil {
		log.WithContextErrorf(ctx, "[Scan] Domain Burst: create golang_domain_task failed: %v", err)
		return errors.New("子域名爆破任务创建失败")
	}

	// 执行爆破任务
	go asyncBurst(ctx, stu.Id, req)

	rsp.GolangTaskId = stu.Id
	log.Infof("[Scan] Domain Burst: got golangDomainTaskId: %d", stu.Id)
	return nil
}

func asyncBurst(ctx context.Context, taskId uint64, req *corePb.ParamsRequest) {
	log.Infof("[Scan] Domain Burst by asyncBurst, taskId: %d", taskId)
	// 解析子域名爆破参数
	options, err := parseOptions(req)
	// 子域名爆破
	burstDomains(ctx, options, req, int(taskId))
	if err != nil {
		log.WithContextErrorf(ctx, "[Scan] Domain Burst: run task_id: %d asyncDomainBurst failed: %v", taskId, err)
	}
}

func burstDomains(ctx context.Context, options *core.Options, req *corePb.ParamsRequest, taskId int) {
	domainChan := make(chan string, 10)
	domainTask, err := domain_task_golang.NewTasker().FindById(uint64(taskId))
	if err != nil {
		log.Errorf("[Scan] Domain Burst find taskId: %d info failed: %v", taskId, err)
	}

	go func() {
		var verifyDomainList []string
		for data := range domainChan {
			switch req.Modify {
			case domain_task.VERIFY_TYPE: // 验证模式
				verifyDomainList = append(verifyDomainList, data)
			case domain_task.BRUST_TYPE: // 枚举模式
				// 更新总表数据
				fromDomain := data[(strings.Index(data, ".") + 1):]
				companyName := sql.NullString{String: "", Valid: false}
				nowTime := time.Now()
				// 拆分域名入库
				stu := &domain_burst.DomainBurst{
					FromDomain:  fromDomain,
					Domain:      data,
					CompanyName: &companyName,
					BurstTime:   nowTime,
					CreatedAt:   nowTime,
					UpdatedAt:   nowTime,
				}

				// 数据是否存在
				res := domain_burst.NewDomainBurstModel().
					FirstOrCreate(stu, &domain_burst.DomainBurst{FromDomain: fromDomain, Domain: data})
				if res.Error != nil {
					log.WithContextInfof(ctx, "[Scan] Domain Burst: 插入数据异常: %s", res.Error)
				}
			}
		}

		if !phpTaskIsExist(uint64(taskId)) {
			return
		}

		// 验证模式
		if req.Modify == 1 && len(verifyDomainList) > 0 {
			// 验证模式下将字符串存储在对应字段中
			domainTask.VerifyDomainList = &sql.NullString{
				String: utils.AnyToStr(verifyDomainList),
				Valid:  true}
		}

		domainTask.Progress = 100.00
		domainTask.Status = domain_task_golang.TaskStatusDone
		err = domain_task_golang.NewTasker().Updates(&domainTask)
		if err != nil {
			log.WithContextErrorf(ctx, "[Scan] Domain Burst, update golang_domain_tasks: %+v", err)
			return
		}

		if !req.SkipCallback {
			// 调用回调接口通知PHP
			accessModel, _ := auth_access_client.NewAuthAccessClientModel().FindById(1)
			token := Md5(fmt.Sprintf("%s_%s", accessModel.Secret, accessModel.ClientId))
			param := callbackParam{Id: taskId, Token: token}
			paramJson := utils.AnyToStr(param)
			log.WithContextInfof(ctx, "[Scan] Domain Burst, callback PHP api: %s, param: %s", req.CallbackUrl, paramJson)
			err = callbackFinish(req.CallbackUrl, param)
			if err != nil {
				log.WithContextErrorf(ctx, "[Scan] Domain Burst, callback PHP api failed: %v, params: %s", err, paramJson)
			} else {
				log.WithContextInfof(ctx, "[Scan] Domain Burst, callback PHP api success, params: %s", err, paramJson)
			}
		}
		log.WithContextInfof(ctx, "[Scan] Domain Burst: task_id: %d 域名爆破服务任务结束", taskId)
	}()

	opt := &scanBin.SubDomain{
		DomainList: options.Domain,
		Verify:     options.Verify,
	}
	level := utils.If(options.DomainLevel <= 0, 1, options.DomainLevel) // 爆破等级
	if !options.Verify {
		opt.OutsideDict = false
		rate := strconv.FormatInt(utils.If(options.Rate <= 0, 10, options.Rate), 10) + "K" // 速率
		opt.Params = []string{"-b", rate, "-l", strconv.Itoa(level), "-skip-wild"}         // []string{"-b", "1M", "-l", "1"}
	}

	// 调用二进制程序进行域名爆破
	if !cfg.IsLocalClient() {
		log.Infof("[Scan] Domain Burst by ksubdomain binary starting, domains: %v", options.Domain)
		err = scanBin.BlastStart(opt, domainChan)
		if err != nil {
			log.WithContextErrorf(ctx, "[Scan] Domain Burst: 爆破二进制程序调用异常: %+v", err)
		}
	}
	// 枚举模式: 调用FOFA查询域名进行爆破
	if !options.Verify && len(options.Domain) > 0 {
		log.Infof("[Scan] Domain Burst by FOFA query starting, domains: %v", options.Domain)
		fofa.BurstDomains(ctx, options.Domain, level, domainChan)
	}
	close(domainChan)

	// core.StartAt(&options, domainChan) 包文件因进程问题舍弃
}

func BurstTaskDelete(_ context.Context, taskId uint64) error {
	db := domain_task_golang.NewTasker()
	task, err := db.FindById(taskId)
	if err != nil {
		return err
	}
	if task.Id == 0 {
		return fmt.Errorf("task_id: %d not found", taskId)
	}

	err = db.DeleteByIds(taskId)
	return err
}
