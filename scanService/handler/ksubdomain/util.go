package ksubdomain

import (
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/domain_task"
	corePb "micro-service/scanService/proto"

	"github.com/panda843/ksubdomain/core"
	"github.com/panda843/ksubdomain/gologger"
	"github.com/tidwall/gjson"
)

//nolint:unused,gocritic
func cacheKey(taskId uint64) string {
	return fmt.Sprintf("foradar_cache:domain_burst:go_task_id:%d", taskId)
}

func phpTaskIsExist(goTaskId uint64) bool {
	_, err := domain_task.NewDomainTaskModel().
		First(mysql.WithColumnValue("golang_task_id", goTaskId))
	return err == nil
}

func parseOptions(req *corePb.ParamsRequest) (*core.Options, error) {
	// 域名列表
	singleDomain := make([]string, 0, len(req.DomainList))
	singleDomain = append(singleDomain, req.DomainList...)
	options := core.Options{}
	options.Domain = singleDomain
	// 爆破层级
	options.DomainLevel = int(req.Level)
	bandwidth := strconv.Itoa(int(req.Bandwidth)) + "K"

	if req.Modify == 1 {
		options.Verify = true
		options.DomainLevel = 0
	}
	var rate int64
	suffix := string([]rune(bandwidth)[len(bandwidth)-1])
	rate, _ = strconv.ParseInt(string([]rune(bandwidth)[0:len(bandwidth)-1]), 10, 64)
	switch suffix {
	case "g", "G":
		rate *= 1000000000
	case "m", "M":
		rate *= 1000000
	case "k", "K":
		rate *= 1000
	default:
		gologger.Fatalf("unknown bandwidth suffix '%s' (supported suffixes are G,M and K)\n", suffix)
	}
	defaultDns := []string{"*********", "*********", "************", "************", "***************", "***************"}
	options.Resolvers = defaultDns
	packSize := int64(100) // 一个DNS包大概有74byte
	rate /= packSize
	options.Rate = rate
	options.Silent = true
	if options.Silent {
		gologger.MaxLevel = gologger.Silent
	}
	options.SkipWildCard = true
	options.NetworkId = -1
	return &options, nil
}

type callbackParam struct {
	Id    int    `json:"id"`
	Token string `json:"token"`
}

func callbackFinish(url string, param callbackParam) error {
	bs, _ := json.Marshal(param)
	req, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(bs))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	c := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			}},
		Timeout: 5 * time.Minute,
	}
	resp, err := c.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	errMsg := gjson.GetBytes(body, "message").String()
	if resp.StatusCode != http.StatusOK {
		return errors.New(errMsg)
	}

	if gjson.GetBytes(body, "code").Int() != 0 {
		return errors.New(errMsg)
	}
	return nil
}

func Md5(src string) string {
	m := md5.New()
	m.Write([]byte(src))
	res := hex.EncodeToString(m.Sum(nil))
	return res
}
