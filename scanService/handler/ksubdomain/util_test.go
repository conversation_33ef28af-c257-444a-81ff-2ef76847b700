package ksubdomain

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"

	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql/auth_access_client"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

func initcfg() {
	godotenv.Load("./../../../.env")

	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())
}

func Test_cacheExist(t *testing.T) {
	initcfg()
	var taskId uint64 = 100
	err := redis.GetInstance().SetNX(context.TODO(), cacheKey(taskId), 0, time.Minute).Err()
	assert.Nil(t, err)

	b := phpTaskIsExist(taskId)
	assert.Equal(t, true, b)

	err = redis.GetInstance().Del(context.TODO(), cacheKey(taskId)).Err()
	assert.Nil(t, err)
}

func Test_callback(t *testing.T) {
	initcfg()

	accessModel, _ := auth_access_client.NewAuthAccessClientModel().FindById(1)
	token := Md5(fmt.Sprintf("%s_%s", accessModel.Secret, accessModel.ClientId))
	err := callbackFinish("https://10.10.11.28/api/v1/domain_task/finish", callbackParam{
		Id:    1418,
		Token: token,
	})
	assert.Nil(t, err)
}
