package handler

import (
	"context"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	rule_engine "micro-service/pkg/rule_engine"
	"micro-service/pkg/utils"
	"micro-service/scanService/handler/domain_search"
	"micro-service/scanService/handler/fofa_task"
	"micro-service/scanService/handler/ksubdomain"
	"micro-service/scanService/handler/login"
	"micro-service/scanService/handler/microkernel"
	pb "micro-service/scanService/proto"

	"go-micro.dev/v4/errors"
)

type Scan struct{}

func (e *Scan) DomainBurst(ctx context.Context, req *pb.ParamsRequest, rsp *pb.SubdomainResponse) error {
	log.WithContextInfof(ctx, "Received Scan.DomainBurst request: %v", req)
	err := ksubdomain.QueryBurstSubdomains(ctx, req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Scan) DomainBurstTaskDelete(ctx context.Context, req *pb.SubdomainResponse, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Scan.DomainBurstTaskDelete request: %v", utils.AnyToStr(req))
	err := ksubdomain.BurstTaskDelete(ctx, req.GolangTaskId)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Scan) LoginPageScreenshotByTopDomain(ctx context.Context, req *pb.LoginPageScreenshotByTopDomainRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Scan.LoginPageScreenshotByTopDomain request: %v", req)
	go login.ScreenshotByTopDomain(ctx, req)
	return nil
}

func (e *Scan) FofaParseQuery(ctx context.Context, req *pb.FofaParseQueryRequest, rsp *pb.FofaParseQueryResponse) error {
	log.WithContextInfof(ctx, "[FofaParse] Received Scan.FofaParseQuery request: %s", utils.AnyToStr(req))
	err := microkernel.QueryEsByFOFAParse(ctx, req, rsp)
	if code, msg := errx.DecodeCode(err, "查询语法错误"); code != errx.SUCCESS {
		log.WithContextErrorf(ctx, "[FofaParse] Request Scan.FofaParseQuery failed: %v", err)
		return errors.BadRequest(pb.ServiceName, msg)
	}
	return nil
}

func (e *Scan) FofaParseStatistics(ctx context.Context, req *pb.FofaParseQueryRequest, rsp *pb.FofaParseAggResponse) error {
	log.WithContextInfof(ctx, "[FofaParse] Received Scan.FofaParseStatistics request: %s", utils.AnyToStr(req))
	err := microkernel.ParseSearchStatistics(ctx, req, rsp)
	if code, msg := errx.DecodeCode(err, "查询语法错误"); code != errx.SUCCESS {
		log.WithContextErrorf(ctx, "[FofaParse] Request Scan.FofaParseStatistics failed: %v", err)
		return errors.BadRequest(pb.ServiceName, msg)
	}
	return nil
}

// CreateFofaScanTask 获取FOFA扫描任务状态
func (e *Scan) CreateFofaScanTask(ctx context.Context, req *pb.FofaScanTaskRequest, rsp *pb.FofaScanTaskResponse) error {
	log.WithContextInfof(ctx, "[FofaScanTask] Received Scan.FofaScanTaskRequest request: %s", utils.AnyToStr(req))
	return fofa_task.CreateFofaScanTask(ctx, req, rsp)
}

// StopFofaScanTask 停止FOFA扫描任务
func (e *Scan) StopFofaScanTask(ctx context.Context, req *pb.FofaScanTaskRequest, _ *pb.FofaScanTaskResponse) error {
	log.WithContextInfof(ctx, "[StopFofaScanTask] Received Scan.FofaScanTaskRequest request: %s", utils.AnyToStr(req))
	return fofa_task.StopFofaTask(req.TaskId)
}

// DomainSearchTaskInfo 任务详情
func (e *Scan) DomainSearchTaskInfo(ctx context.Context, req *pb.DomainSearchTaskInfoRequest, rsp *pb.DomainSearchTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Domain-Search] Received scan.DomainSearchTaskInfo request: %s", utils.AnyToStr(req))
	err := domain_search.TaskInfo(req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DomainSearchTaskCreate 创建任务
func (e *Scan) DomainSearchTaskCreate(ctx context.Context, req *pb.DomainSearchTaskCreateRequest, rsp *pb.DomainSearchTaskInfoRequest) error {
	log.WithContextInfof(ctx, "[Domain-Search] Received scan.DomainSearchTaskCreate request: %s", utils.AnyToStr(req))
	err := domain_search.TaskCreate(req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DomainSearchResultList 结果列表
func (e *Scan) DomainSearchResultList(ctx context.Context, req *pb.DomainSearchResultListRequest, rsp *pb.DomainSearchResultListResponse) error {
	log.WithContextInfof(ctx, "[Domain-Search] Received scan.DomainSearchResultList request: %s", utils.AnyToStr(req))
	err := domain_search.ResultList(req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// EngineRuleAssetMatch 规则引擎匹配
func (e *Scan) EngineRuleAssetMatch(ctx context.Context, req *pb.EngineRuleAssetMatchRequest, rsp *pb.EngineRuleAssetMatchResponse) error {
	log.WithContextInfof(ctx, "[Engine-rules] Received scan.EngineRuleAssetMatch request: %s", utils.AnyToStr(req))
	err := rule_engine.AssetMatch(ctx, req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}
