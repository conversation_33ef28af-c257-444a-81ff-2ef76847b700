package fofa_task

import (
	"bufio"
	"bytes"
	"compress/gzip"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	core "micro-service/coreService/proto"
	"micro-service/middleware/elastic/fofaee_service"
	"micro-service/middleware/elastic/fofaee_subdomain"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/category"
	"micro-service/pkg/cfg"
	"micro-service/pkg/localization"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/scanService/proto"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/spf13/cast"
)

const (
	TaskStatusStarting = iota + 1
	TaskStatusRunning
	TaskStatusStopping
	TaskStatusStopped
	TaskStatusSuccess
	TaskStatusFailed
	TaskStatusPaused
)
const OK = "OK"

var RunningTaskIds []uint64

var client *http.Client

func sendProcessCallback(req *pb.FofaScanTaskRequest, status int, process float32) {
	// 判断127是否联通,不联通时,使用host.docker.internal
	if strings.Contains(req.ProcessCallback, "127.0.0.1") {
		conn, dialErr := net.Dial("tcp", strings.Split(req.ProcessCallback, "/")[2])
		if dialErr != nil {
			req.ProcessCallback = strings.ReplaceAll(req.ProcessCallback, "127.0.0.1", "host.docker.internal")
		}
		if conn != nil {
			defer conn.Close()
		}
	}
	// 创建一个回调CURL对象，并设置超时时间为3秒,忽略SSL证书错误
	if client == nil {
		client = &http.Client{Timeout: 3 * time.Second}
		tr := &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}
		client.Transport = tr
	}
	httpStatus := OK
	processUrl := fmt.Sprintf(req.ProcessCallback, req.TaskId, status, fmt.Sprintf("%.2f", process))
	pRes, pErr := client.Get(processUrl)
	if pRes != nil {
		httpStatus = pRes.Status
	}
	log.Info(fmt.Sprintf("call task process %s rsp:%v,err:%s", processUrl, httpStatus, pErr))
}

func sendFinishCallback(req *pb.FofaScanTaskRequest, status int, err string) {
	// 判断127是否联通,不联通时,使用host.docker.internal
	if strings.Contains(req.FinishCallback, "127.0.0.1") {
		conn, dialErr := net.Dial("tcp", strings.Split(req.FinishCallback, "/")[2])
		if dialErr != nil {
			req.FinishCallback = strings.ReplaceAll(req.FinishCallback, "127.0.0.1", "host.docker.internal")
		}
		if conn != nil {
			defer conn.Close()
		}
	}
	// 创建一个回调CURL对象，并设置超时时间为3秒,忽略SSL证书错误
	if client == nil {
		client = &http.Client{Timeout: 3 * time.Second}
		tr := &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}
		client.Transport = tr
	}
	httpStatus := OK
	url := fmt.Sprintf(req.FinishCallback, req.TaskId, status, err)
	cRes, cErr := client.Get(url)
	if cRes != nil {
		httpStatus = cRes.Status
	}
	log.Info(fmt.Sprintf("call task finish %s rsp:%v,err:%s", url, httpStatus, cErr))
	if cErr != nil {
		cRes, cErr = client.Get(url)
		if cRes != nil {
			httpStatus = cRes.Status
		}
		log.Info(fmt.Sprintf("call task finish %s rsp:%v,err:%s", url, httpStatus, cErr))
	}
	if cErr != nil {
		cRes, cErr = client.Get(url)
		if cRes != nil {
			httpStatus = cRes.Status
		}
		log.Info(fmt.Sprintf("call task finish %s rsp:%v,err:%s", url, httpStatus, cErr))
	}
}

// 处理FOFA扫描任务
func handleScanTask(fofaTaskId string, req *pb.FofaScanTaskRequest) {
	req.ProcessCallback = strings.TrimSuffix(req.ProcessCallback, "/") + "?task_id=%d&state=%d&progress=%s"
	req.FinishCallback = strings.TrimSuffix(req.FinishCallback, "/") + "?task_id=%d&state=%d&message=%s"
	// 等待FOFA扫描完成
	if ok := waitForFofaTask(fofaTaskId, req); !ok {
		_ = StopFofaTask(req.TaskId)
		return
	}
	// 获取扫描任务结果
	data, ok := getFofaTaskResult(fofaTaskId, req)
	if !ok {
		_ = StopFofaTask(req.TaskId)
		return
	}
	sendProcessCallback(req, TaskStatusRunning, 80)
	// 添加到本地扫描结果
	insertScanInfo(data, fofaTaskId, req)
	_ = StopFofaTask(req.TaskId)
}

// getSubdomainRules 获取Subdomain组件
func getSubdomainRules(fofaTaskId string, taskId uint64, info map[string]any) []fofaee_subdomain.Rule {
	ruleTags := make([]fofaee_subdomain.Rule, 0)
	index := 0
	for index <= 5 {
		if layerList, ok := info["layer"+cast.ToString(index)]; ok {
			layers := layerList.([]any)
			for i := range layers {
				rInfo := layers[i].(map[string]any)
				dInfo, dErr := category.NewRuleModel().FindOne(mysql.WithWhere("id", rInfo["id"]))
				if dErr != nil {
					log.Warnf("FOFA任务:%s,Foradar任务:%d,IP:%s,Port:%d,未找到组件ID:%d", fofaTaskId, taskId, info["ip"], cast.ToInt(info["port"]), cast.ToInt(rInfo["id"]))
					continue
				}
				pInfo, dErr := category.NewSecondCategoriesModel().FindOne(mysql.WithWhere("id", rInfo["pid"]))
				if dErr != nil {
					log.Warnf("FOFA任务:%s,Foradar任务:%d,IP:%s,Port:%d,未找到父组件ID:%d", fofaTaskId, taskId, info["ip"], cast.ToInt(info["port"]), cast.ToInt(rInfo["pid"]))
					continue
				}
				cInfo, dErr := category.NewSecondCategoriesModel().FindOne(mysql.WithWhere("id", rInfo["cid"]))
				if dErr != nil {
					log.Warnf("FOFA任务:%s,Foradar任务:%d,IP:%s,Port:%d,未找到组件分组ID:%d", fofaTaskId, taskId, info["ip"], cast.ToInt(info["port"]), cast.ToInt(rInfo["cid"]))
					continue
				}
				ruleTags = append(ruleTags, fofaee_subdomain.Rule{
					Level:            cast.ToString(index),
					RuleId:           cast.ToString(rInfo["id"]),
					Product:          dInfo.Product,
					Company:          dInfo.CompanyName,
					SoftHard:         dInfo.SoftHardCode,
					CnProduct:        dInfo.Product,
					CnCompany:        dInfo.CompanyName,
					ParentCategory:   pInfo.EnTitle,
					CnParentCategory: pInfo.Title,
					Category:         cInfo.EnTitle,
					CnCategory:       cInfo.Title,
				})
			}
		}
		index += 1
	}
	return ruleTags
}

// getServiceRules 获取Service组件
func getServiceRules(fofaTaskId string, taskId uint64, info map[string]any) []fofaee_service.Rule {
	ruleTags := make([]fofaee_service.Rule, 0)
	index := 0
	for index <= 5 {
		if layerList, ok := info["layer"+cast.ToString(index)]; ok {
			layers := layerList.([]any)
			for i := range layers {
				rInfo := layers[i].(map[string]any)
				dInfo, dErr := category.NewRuleModel().FindOne(mysql.WithWhere("id", cast.ToUint64(rInfo["id"])))
				if dErr != nil {
					log.Warnf("FOFA任务:%s,Foradar任务:%d,IP:%s,Port:%d,未找到组件ID:%d", fofaTaskId, taskId, info["ip"], cast.ToInt(info["port"]), cast.ToInt(rInfo["id"]))
					continue
				}
				pInfo, dErr := category.NewSecondCategoriesModel().FindOne(mysql.WithWhere("id", cast.ToUint64(rInfo["pid"])))
				if dErr != nil {
					log.Warnf("FOFA任务:%s,Foradar任务:%d,IP:%s,Port:%d,未找到父组件ID:%d", fofaTaskId, taskId, info["ip"], cast.ToInt(info["port"]), cast.ToInt(rInfo["pid"]))
					continue
				}
				cInfo, dErr := category.NewSecondCategoriesModel().FindOne(mysql.WithWhere("id", cast.ToUint64(rInfo["cid"])))
				if dErr != nil {
					log.Warnf("FOFA任务:%s,Foradar任务:%d,IP:%s,Port:%d,未找到组件分组ID:%d", fofaTaskId, taskId, info["ip"], cast.ToInt(info["port"]), cast.ToInt(rInfo["cid"]))
					continue
				}
				ruleTags = append(ruleTags, fofaee_service.Rule{
					Level:            cast.ToString(index),
					RuleID:           cast.ToString(rInfo["id"]),
					Product:          dInfo.Product,
					Company:          dInfo.CompanyName,
					SoftHard:         dInfo.SoftHardCode,
					CnProduct:        dInfo.Product,
					CnCompany:        dInfo.CompanyName,
					ParentCategory:   pInfo.EnTitle,
					CnParentCategory: pInfo.Title,
					Category:         cInfo.EnTitle,
					CnCategory:       cInfo.Title,
				})
			}
		}
		index += 1
	}
	return ruleTags
}

// 插入本地扫描结果
func insertScanInfo(data []string, fofaTaskId string, req *pb.FofaScanTaskRequest) {
	taskAssets := make(map[string]fofaee_task_assets.FofaeeTaskAssets, 0)
	for i := range data {
		if !utils.ListContains(RunningTaskIds, req.TaskId) {
			log.Infof("FOFA任务:%s,Foradar任务:%d,用户停止任务,跳出等待循环", fofaTaskId, req.TaskId)
			return
		}
		info := make(map[string]any, 0)
		if err := json.Unmarshal([]byte(data[i]), &info); err != nil {
			log.Warnf("FOFA任务:%s,Foradar任务:%d,扫描数据解析失败:%s", fofaTaskId, req.TaskId, data[i])
			continue
		}
		tid := cast.ToString(req.TaskId) + "_" + cast.ToString(info["ip"])
		if _, ok := info["body"]; ok {
			// subdomain
			var subdomain fofaee_subdomain.FofeeSubdomain
			if err := json.Unmarshal([]byte(data[i]), &subdomain); err != nil {
				log.Warnf("FOFA任务:%s,Foradar任务:%d,扫描数据解析失败:%s", fofaTaskId, req.TaskId, data[i])
				continue
			}
			// 提取组件数据
			subdomain.RuleTags = getSubdomainRules(fofaTaskId, req.TaskId, info)
			// 补充任务数据
			tAsset, tOk := taskAssets[tid]
			if !tOk {
				tAsset = fofaee_task_assets.FofaeeTaskAssets{}
			}
			tAsset.UserId = req.UserId
			tAsset.TaskId = cast.ToString(req.TaskId)
			tAsset.Ip = subdomain.Ip
			tAsset.State = 1
			tAsset.IsIpv6 = utils.IsIPv6(subdomain.Ip)
			// 补充端口
			portStr := cast.ToString(subdomain.Port)
			found := false
			for _, p := range tAsset.Ports {
				if cast.ToString(p) == portStr {
					found = true
					break
				}
			}
			if !found {
				tAsset.Ports = append(tAsset.Ports, cast.ToString(subdomain.Port))
			}
			// 补充协议
			if !utils.ListContains(tAsset.Protocols, subdomain.Protocol) {
				tAsset.Protocols = append(tAsset.Protocols, subdomain.Protocol)
			}
			// 补充title_list
			if !utils.ListContains(utils.ListColumn(tAsset.TitleList, func(t fofaee_task_assets.TitleListInfo) uint {
				return cast.ToUint(t.Port)
			}), cast.ToUint(subdomain.Port)) {
				tAsset.TitleList = append(tAsset.TitleList, fofaee_task_assets.TitleListInfo{
					Title: subdomain.Title,
					Host:  subdomain.Host,
					Port:  cast.ToUint(subdomain.Port),
				})
			}
			// 补充Rules
			tRuleIds := utils.ListColumn(tAsset.Rules, func(t fofaee_task_assets.Rule) string { return t.RuleID })
			for xi := range subdomain.RuleTags {
				if !utils.ListContains(tRuleIds, subdomain.RuleTags[xi].RuleId) {
					tRuleIds = append(tRuleIds, subdomain.RuleTags[xi].RuleId)
					tAsset.Rules = append(tAsset.Rules, fofaee_task_assets.Rule{
						Category:         subdomain.RuleTags[xi].Category,
						CnCategory:       subdomain.RuleTags[xi].CnCategory,
						CnCompany:        subdomain.RuleTags[xi].CnCompany,
						CnParentCategory: subdomain.RuleTags[xi].CnParentCategory,
						CnProduct:        subdomain.RuleTags[xi].CnProduct,
						Company:          subdomain.RuleTags[xi].Company,
						Level:            subdomain.RuleTags[xi].Level,
						ParentCategory:   subdomain.RuleTags[xi].ParentCategory,
						Product:          subdomain.RuleTags[xi].Product,
						RuleID:           subdomain.RuleTags[xi].RuleId,
						SoftHard:         subdomain.RuleTags[xi].SoftHard,
					})
				}
			}
			// 补充port_list
			if !utils.ListContains(utils.ListColumn(tAsset.PortList, func(t fofaee_task_assets.PortListInfo) uint {
				return cast.ToUint(t.Port)
			}), cast.ToUint(subdomain.Port)) {
				tAsset.PortList = append(tAsset.PortList, fofaee_task_assets.PortListInfo{
					Protocol: subdomain.Protocol,
					Port:     cast.ToUint(subdomain.Port),
					Banner:   subdomain.Header,
					Certs:    fofaee_task_assets.PortListCertInfo{},
				})
			}
			tAsset.PortSize = cast.ToUint(len(tAsset.Ports))
			if tAsset.Createtime == "" {
				tAsset.Createtime = utils.CurrentTime()
			}
			tAsset.UpdatedAt = utils.CurrentTime()
			tAsset.Lastupdatetime = subdomain.LastUpdateTime
			taskAssets[tid] = tAsset
			// 入库subdomain
			_, _, _ = fofaee_subdomain.NewFofeeSubdomainModel().Upsert(context.TODO(), req.UserId, []*fofaee_subdomain.FofeeSubdomain{&subdomain})
		} else {
			// service
			var service fofaee_service.FofaeeService
			if err := json.Unmarshal([]byte(data[i]), &service); err != nil {
				log.Warnf("FOFA任务:%s,Foradar任务:%d,扫描数据解析失败:%s", fofaTaskId, req.TaskId, data[i])
				continue
			}
			// 提取组件数据
			service.RuleTags = getServiceRules(fofaTaskId, req.TaskId, info)
			// 补充任务数据
			tAsset, tOk := taskAssets[tid]
			if !tOk {
				tAsset = fofaee_task_assets.FofaeeTaskAssets{}
			}
			tAsset.UserId = req.UserId
			tAsset.TaskId = cast.ToString(req.TaskId)
			tAsset.Ip = service.IP
			tAsset.State = 1
			tAsset.IsIpv6 = utils.IsIPv6(service.IP)
			// 补充端口
			portStr := cast.ToString(service.Port)
			found := false
			for _, p := range tAsset.Ports {
				if cast.ToString(p) == portStr {
					found = true
					break
				}
			}
			if !found {
				tAsset.Ports = append(tAsset.Ports, cast.ToString(service.Port))
			}
			// 补充协议
			if !utils.ListContains(tAsset.Protocols, service.Protocol) {
				tAsset.Protocols = append(tAsset.Protocols, service.Protocol)
			}
			// 补充Rules
			tRuleIds := utils.ListColumn(tAsset.Rules, func(t fofaee_task_assets.Rule) string { return t.RuleID })
			for xi := range service.RuleTags {
				if !utils.ListContains(tRuleIds, service.RuleTags[xi].RuleID) {
					tRuleIds = append(tRuleIds, service.RuleTags[xi].RuleID)
					tAsset.Rules = append(tAsset.Rules, fofaee_task_assets.Rule{
						Category:         service.RuleTags[xi].Category,
						CnCategory:       service.RuleTags[xi].CnCategory,
						CnCompany:        service.RuleTags[xi].CnCompany,
						CnParentCategory: service.RuleTags[xi].CnParentCategory,
						CnProduct:        service.RuleTags[xi].CnProduct,
						Company:          service.RuleTags[xi].Company,
						Level:            service.RuleTags[xi].Level,
						ParentCategory:   service.RuleTags[xi].ParentCategory,
						Product:          service.RuleTags[xi].Product,
						RuleID:           service.RuleTags[xi].RuleID,
						SoftHard:         service.RuleTags[xi].SoftHard,
					})
				}
			}
			// 补充port_list
			if !utils.ListContains(utils.ListColumn(tAsset.PortList, func(t fofaee_task_assets.PortListInfo) uint {
				return cast.ToUint(t.Port)
			}), cast.ToUint(service.Port)) {
				tAsset.PortList = append(tAsset.PortList, fofaee_task_assets.PortListInfo{
					Protocol: service.Protocol,
					Port:     cast.ToUint(service.Port),
					Banner:   service.Banner,
					Certs: fofaee_task_assets.PortListCertInfo{
						IssuerCn:  service.Certs.IssuerCN,
						CertDate:  service.Certs.CertDate,
						NotAfter:  service.Certs.NotAfter,
						NotBefore: service.Certs.NotBefore,
						IssuerCns: service.Certs.IssuerCNs,
						SigAlth:   service.Certs.SigAlth,
						SubjectCn: service.Certs.SubjectCN,
						V:         service.Certs.V,
						ValidType: service.Certs.ValidType,
						IsValid:   service.Certs.IsValid,
						Sn:        service.Certs.Sn,
					},
				})
			}
			tAsset.PortSize = cast.ToUint(len(tAsset.Ports))
			if tAsset.Createtime == "" {
				tAsset.Createtime = utils.CurrentTime()
			}
			tAsset.UpdatedAt = utils.CurrentTime()
			tAsset.Lastupdatetime = service.Lastupdatetime
			taskAssets[tid] = tAsset
			// 入库service
			_, _, _ = fofaee_service.NewFofeeServiceModel().Upsert(context.TODO(), req.UserId, []*fofaee_service.FofaeeService{&service})
		}
	}
	// 写入任务表
	sendProcessCallback(req, TaskStatusRunning, 90)
	for x := range taskAssets {
		tAsset := taskAssets[x]
		_, _, _ = fofaee_task_assets.NewFofaeeTaskAssetsModel().Upsert(context.TODO(), req.UserId, []*fofaee_task_assets.FofaeeTaskAssets{&tAsset})
	}
	// 任务完成回调,
	sendFinishCallback(req, TaskStatusSuccess, "success")
}

// 获取FOFA扫描结果
func getFofaTaskResult(fofaTaskId string, req *pb.FofaScanTaskRequest) ([]string, bool) {
	data := make([]string, 0)
	var err error
	rsp := &core.FofaTaskResultResponse{
		Result: make([]byte, 0),
	}
	if cfg.IsLocalClient() {
		err = localization.NewCaller().CoreFofaGetTaskResult(&core.FofaTaskStatusRequest{Id: fofaTaskId}, rsp)
	} else {
		rsp, err = core.GetProtoCoreClient().GetTaskResult(context.TODO(), &core.FofaTaskStatusRequest{Id: fofaTaskId})
	}
	if err != nil {
		sendFinishCallback(req, TaskStatusFailed, fmt.Sprintf("FOFA任务:%s,Foradar任务:%d,获取FOFA扫描结果失败:%s", fofaTaskId, req.TaskId, err.Error()))
		return data, false
	}
	// 解压扫描结果
	gzr, err := gzip.NewReader(bytes.NewReader(rsp.Result))
	if err != nil {
		sendFinishCallback(req, TaskStatusFailed, fmt.Sprintf("FOFA任务:%s,Foradar任务:%d,获取FOFA扫描结果失败2:%s", fofaTaskId, req.TaskId, err.Error()))
		return data, false
	}
	defer gzr.Close()
	reader := bufio.NewReader(gzr)
	// 按行读取文本文件
	for {
		// 每次读取一行数据，直到出现错误或者文件结束
		line, rErr := reader.ReadBytes('\n')
		if rErr != nil {
			break
		}
		data = append(data, string(line))
	}
	return data, true
}

// 等待FOFA任务扫描完成
func waitForFofaTask(fofaTaskId string, req *pb.FofaScanTaskRequest) bool {
	// var lastValue int64          // 上一次的值
	// var lastTime time.Time       // 上一次更新值的时间戳
	// interval := 10 * time.Minute // 时间间隔设为10分钟
	statusResponse := &core.FofaTaskStatusResponse{
		Data: &core.FofaTaskStatus{},
	}
	// 定义一个定时器，每隔interval触发一次tick函数
	// ticker := time.NewTicker(interval)
	// defer ticker.Stop()
	// 捕获协成内错误,并回调结果
	if waitErr := recover(); waitErr != nil {
		sendFinishCallback(req, TaskStatusFailed, fmt.Sprintf("FOFA任务:%s,Foradar任务:%d,获取FOFA扫描结果失败:%v", fofaTaskId, req.TaskId, waitErr))
		return false
	}
	taskProcess := cast.ToInt64(1)
	if !utils.ListContains(RunningTaskIds, req.TaskId) {
		RunningTaskIds = append(RunningTaskIds, req.TaskId)
	}
	for {
		if !utils.ListContains(RunningTaskIds, req.TaskId) {
			log.Infof("FOFA任务:%s,Foradar任务:%d,用户停止任务,跳出等待循环", fofaTaskId, req.TaskId)
			return false
		}
		// 获取扫描进度
		if err := GetFofaTaskStatus(context.TODO(), &core.FofaTaskStatusRequest{Id: fofaTaskId}, statusResponse); err != nil {
			log.Warnf("FOFA任务:%s,Foradar任务:%d,获取FOFA任务状态失败:%s", fofaTaskId, req.TaskId, err.Error())
			continue
		}
		if statusResponse.Data.Progress != 0 {
			taskProcess = statusResponse.Data.Progress
		}
		log.Infof("FOFA任务:%s,Foradar任务:%d,FOFA进度:%d,Foradar进度:%.2f", fofaTaskId, req.TaskId, taskProcess, cast.ToFloat32(taskProcess)*0.7)
		// 检查进度变化
		// if taskProcess != lastValue {
		//	lastValue = taskProcess
		//	lastTime = time.Now()
		// }
		// 进度等于100时,跳出循环
		if taskProcess == 100 {
			log.Infof("FOFA任务:%s,Foradar任务:%d,FOFA任务执行完成,结果文件:%v,跳出等待循环", fofaTaskId, req.TaskId, statusResponse.Data.ResultFile)
			if !statusResponse.Data.ResultFile {
				if statusResponse.Data.State != "success" {
					sendFinishCallback(req, TaskStatusSuccess, "扫描失败,结果文件生成失败")
				} else {
					sendFinishCallback(req, TaskStatusSuccess, "扫描完成,结果文件为空")
				}
				return false
			}
			return true
		}
		// 回调进度
		sendProcessCallback(req, TaskStatusRunning, cast.ToFloat32(taskProcess)*0.7)
		// 每3秒请求一次
		time.Sleep(3 * time.Second)
		// 10分钟内,进度无变化时退出任务
		// select {
		// case <-ticker.C:
		//	// 判断是否超过时间间隔
		//	if time.Since(lastTime) >= interval {
		//		sendFinishCallback(req, TaskStatusFailed, fmt.Sprintf("任务超过10分钟进度无变化,FOFA任务:%s,Foradar任务:%d", fofaTaskId, req.TaskId))
		//		return false
		//	}
		// default:
		//	log.Infof("FOFA任务:%s,Foradar任务:%d,等待FOFA任务执行完成,当前FOFA任务进度:%d", fofaTaskId, req.TaskId, taskProcess)
		// }
	}
}

// CreateFofaScanTask 获取FOFA扫描任务状态
func CreateFofaScanTask(ctx context.Context, req *pb.FofaScanTaskRequest, rsp *pb.FofaScanTaskResponse) error {
	log.WithContextInfof(ctx, "[FofaScanTask] Received Scan.FofaScanTaskRequest request: %s", utils.AnyToStr(req))
	var err error
	// 组装请求参数
	createReq := &core.FofaScanTaskRequest{}
	coreRsp := &core.FofaDetectionResponse{
		Data: &core.FofaTaskIdData{},
	}
	for i := range req.Targets {
		createReq.Targets = append(createReq.Targets, &core.FofaScanTarget{Ip: req.Targets[i].Ip, Ports: req.Targets[i].Ports})
	}
	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		err = localization.NewCaller().CoreFofaCreateScanTask(createReq, coreRsp)
	} else {
		// 否则rpc微服务调用
		coreRsp, err = core.GetProtoCoreClient().CreateScanTask(ctx, createReq, utils.SetRpcTimeoutOpt(30))
	}
	if err != nil {
		return err
	}
	if coreRsp != nil {
		rsp.Code = coreRsp.Code
		rsp.Message = coreRsp.Message
		rsp.Data = &pb.FofaTaskIdData{
			Id: coreRsp.Data.Id,
		}
	}
	go handleScanTask(rsp.Data.Id, req)
	return nil
}

// GetFofaTaskStatus 获取FOFA扫描任务状态
func GetFofaTaskStatus(ctx context.Context, req *core.FofaTaskStatusRequest, rsp *core.FofaTaskStatusResponse) error {
	log.WithContextInfof(ctx, "[FofaScanTask] Received Scan.FofaTaskStatusRequest request: %s", utils.AnyToStr(req))
	var err error
	var coreRsp *core.FofaTaskStatusResponse
	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		err = localization.NewCaller().CoreFofaGetTaskStatus(req, rsp)
	} else {
		// 否则rpc微服务调用
		coreRsp, err = core.GetProtoCoreClient().GetTaskStatus(ctx, req, utils.SetRpcTimeoutOpt(30))
		if coreRsp != nil {
			rsp.Code = coreRsp.Code
			rsp.Message = coreRsp.Message
			rsp.Data = &core.FofaTaskStatus{
				Progress:   coreRsp.Data.Progress,
				TargetNum:  coreRsp.Data.TargetNum,
				ResultFile: coreRsp.Data.ResultFile,
				State:      coreRsp.Data.State,
			}
		}
	}
	if err != nil {
		return err
	}
	return nil
}

// StopFofaTask 停止扫描任务
func StopFofaTask(taskId uint64) error {
	for i := range RunningTaskIds {
		if RunningTaskIds[i] == taskId {
			RunningTaskIds = append(RunningTaskIds[:i], RunningTaskIds[i+1:]...)
			return nil
		}
	}
	return errors.New("not Found Task")
}
