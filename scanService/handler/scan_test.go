package handler

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"micro-service/initialize/es"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	pb "micro-service/scanService/proto"
)

func TestScan_FofaParseQuery(t *testing.T) {
	cfg.InitLoadCfg()
	es.GetInstance(cfg.LoadElastic())
	redis.GetInstance(cfg.LoadRedis())

	var rsp = &pb.FofaParseQueryResponse{}
	sc := new(Scan)
	err := sc.FofaParseQuery(context.TODO(), &pb.FofaParseQueryRequest{
		Search:  "",
		UserId:  []uint64{1},
		Page:    1,
		PerPage: 10,
	}, rsp)
	assert.Nil(t, err)
}

func TestScan_CreateFofaScanTask(t *testing.T) {
	cfg.InitLoadCfg()
	cfg.LoadLogger()
	es.GetInstance(cfg.LoadElastic())
	redis.GetInstance(cfg.LoadRedis())
	var rsp = &pb.FofaScanTaskResponse{}
	sc := new(Scan)
	err := sc.CreateFofaScanTask(context.TODO(), &pb.FofaScanTaskRequest{
		TaskId: 13801,
		UserId: 1,
		Targets: []*pb.FofaScanTarget{
			{
				Ip:    []string{"*************"},
				Ports: "443",
			},
		},
		FinishCallback:  "http://127.0.0.1:5200/api/scan/asset/finish",
		ProcessCallback: "http://127.0.0.1:5200/api/scan/asset/progress",
	}, rsp)
	assert.Nil(t, err)
}

func TestScan_FofaParseStatistics(t *testing.T) {
	cfg.InitLoadCfg()
	es.GetInstance(cfg.LoadElastic())
	redis.GetInstance(cfg.LoadRedis())

	var rsp = &pb.FofaParseAggResponse{}
	sc := new(Scan)
	err := sc.FofaParseStatistics(context.TODO(), &pb.FofaParseQueryRequest{
		Search: "",
		UserId: []uint64{1},
	}, rsp)
	assert.Nil(t, err)
}
