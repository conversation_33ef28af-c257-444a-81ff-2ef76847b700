package microkernel

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

// MicroModule 微内核模块信息
type MicroModule struct {
	Asset           int64 `json:"asset"`
	Compliance      int64 `json:"compliance"`
	CustomPoc       int64 `json:"custom_poc"`
	CustomProtocol  int64 `json:"custom_protocol"`
	FeatureEngine   int64 `json:"feature_engine"`
	KafkaConnection int64 `json:"kafka_connection"`
	Report          int64 `json:"report"`
	Threat          int64 `json:"threat"`
	FofaLink        int64 `json:"fofa_link"`
	RiskModule      int64 `json:"risk_module"`
	AssetPanorama   int64 `json:"asset_panorama"`
}

type FofaQuery struct {
	Query    string   `json:"query"`
	RawQuery string   `json:"raw_query"`
	Index    []string `json:"index"`
	Keywords []string `json:"keywords"`
}

// MicroInfo 微内核信息
type MicroInfo struct {
	Version          string      `json:"version"`            // 版本号
	LicenseState     bool        `json:"license_state"`      // 激活状态 true：激活 false：未激活
	AssetLimitNum    int64       `json:"asset_limit_num"`    // 资产数
	ProductLimitDate string      `json:"product_limit_date"` // 测试到期日期
	UpgradeLimitDate string      `json:"upgrade_limit_date"` // 服务到期日期
	Modules          MicroModule `json:"modules"`            // foeye模块控制
}

type TaskInfo struct {
	TaskId              string         `json:"task_id"`               // 任务id
	Ports               string         `json:"ports"`                 // 扫描目标端口 支持格式：80,443  T:80,T:443:https,U:161
	TaskType            string         `json:"task_type"`             // 扫描类型 quick:masscan扫描 common:nmap扫描 ping：端口探活
	IpList              string         `json:"ip_list"`               // 扫描目标列表 支持格式：*********** **********/24 **********,**********/24
	IpListFilename      string         `json:"ip_list_filename"`      // 扫描目标文件绝对路径,文件内容一行一条数据,行数据支持格式： *********** **********/24
	Bandwidth           string         `json:"bandwidth"`             // 扫描发包速率
	RepeatTimes         uint64         `json:"repeat_times"`          // 端口扫描重复发包次数
	Blacklist           string         `json:"blacklist"`             // 排除扫描目标,支持格式同ip_list
	ProtocolUpdateCycle uint64         `json:"protocol_update_cycle"` // 协议更新周期（秒）0：表示无限制 其他表示更新间隔
	UnknownProtocolIndb bool           `json:"unknown_protocol_indb"` // 未知协议入库  true:表示入库 false：默认值，不入库
	PingScan            bool           `json:"ping_scan"`             // 是否启用ping识别 true:表示开启ping false：默认值，不开启
	DeepGetOs           bool           `json:"deep_get_os"`           // 深度识别操作系统 透传参数，foeye自有业务
	DeepGetMac          bool           `json:"deep_get_mac"`          // 深度获取MAC地址
	GatewayMac          string         `json:"gateway_mac"`           // 设置端口扫描的网关MAC地址
	Hostinfos           []string       `json:"hostinfos"`             // 域名列表 ["baimaohui.net","huashunxinan.net"]
	ResumeFilename      string         `json:"resume_filename"`       // 暂停的任务继续运行的时候必须要 兼容foeye使用,建议使用任务恢复接口
	MaxAssetNum         uint64         `json:"max_asset_num"`         // 最大资产数,大于0表示限制
	IsIpv6              bool           `json:"is_ipv6"`               // 是否IPv6扫描 true:是 false：否
	TreckScan           bool           `json:"treck_scan"`            // 是否treck协议识别 true:是 false：否
	GrabConcurrent      uint64         `json:"grab_concurrent"`       // 协议识别并发数,兼用旧内核参数（目前未实现功能）
	SendEth             string         `json:"send_eth"`              // 扫描使用的网卡名称
	CrawlerAllUrl       bool           `json:"crawler_all_url"`       // 是否启用全站爬虫
	CrawlerUrlBlackKey  string         `json:"crawler_url_black_key"` // 爬取url黑名单,如果url包含就不爬取  支持格式：url1|url2|url3
	CrawlerSpecificUrl  string         `json:"crawler_specific_url"`  // 爬取指定URL,支持格式同上
	PortGroup           []*PortIPGroup `json:"port_group"`            // 端口分组扫描,如果此字段有值，就使用端口分组扫描,支持格式：[{"80":["***********"]},{"3306,6379,9200":["***********"]}]
	Extra               map[string]any `json:"extra"`                 // 扩展字段
}

// PortIPGroup 端口IP组合
type PortIPGroup struct {
	Ports  string   `json:"ports"`
	IPList []string `json:"ip_list"`
}

// RuleInfo 规则信息
type RuleInfo struct {
	Product          string `json:"product"`            // 规则名称，中文
	EnProduct        string `json:"en_product"`         // 规则名称，英文
	Rule             string `json:"rule"`               // 规则内容
	RuleId           string `json:"rule_id"`            // 规则id
	Level            uint8  `json:"level"`              // 组件层级0，1，2，3，4
	Category         string `json:"category"`           // 分类，中文
	EnCategory       string `json:"en_category"`        // 分类，英文
	ParentCategory   string `json:"parent_category"`    // 父级分类
	EnParentCategory string `json:"en_parent_category"` // 父级分类，英文
	Company          string `json:"company"`            // 厂商，中文
	EnCompany        string `json:"en_company"`         // 厂商，英文
	UserId           string `json:"user_id"`            // 用户id
}

type MicroKernel struct {
	TimeOut int
}

var once sync.Once

var microkernel *MicroKernel

func GetInstance() *MicroKernel {
	if microkernel == nil {
		once.Do(func() { microkernel = &MicroKernel{} })
	}
	return microkernel
}

func (m *MicroKernel) getServerAddr() string {
	addr := getMicrokernelAddrCache()
	if addr != "" {
		return strings.TrimSuffix(addr, "/")
	}

	for i := 0; i < 10; i++ {
		addr = cfg.LoadMicrokernel().Addr
		if addr != cfg.MicroKernelDefaultAddr && addr != "" {
			setMicrokernelAddr(addr)
			return strings.TrimSuffix(addr, "/")
		}
		time.Sleep(100 * time.Millisecond)
	}

	return cfg.MicroKernelDefaultAddr
}

// FofaQuery FOFA Query 查询
func (m *MicroKernel) FofaQuery(query string) (*FofaQuery, error) {
	fofaQuery := &FofaQuery{}
	// 检查是否存在越权字段
	if query == "" || strings.Contains(query, "extra.user_id") {
		return fofaQuery, fmt.Errorf("查询语法错误")
	}

	addr := m.getServerAddr() + "/api/v1/query_parse"
	reqBody := utils.AnyToStr(map[string]string{"query": utils.QBase64(query)})
	resp, err := http.Post(addr, "application/json", strings.NewReader(reqBody)) //nolint:gosec,gocritic
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != 200 {
		return nil, errors.New(gjson.Get(string(body), "message").String())
	}

	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return nil, fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	fofaQuery.Query = gjson.Get(string(body), "data.query.query").String()
	fofaQuery.Query = strings.ReplaceAll(fofaQuery.Query, "\"product\":", "\"product.raw\":")
	fofaQuery.RawQuery = gjson.Get(string(body), "data.raw_query").String()
	fofaQuery.Index = utils.ListColumn[string](gjson.Get(string(body), "data.index").Array(), func(result gjson.Result) string {
		return result.String()
	})
	fofaQuery.Keywords = utils.ListColumn[string](gjson.Get(string(body), "data.keywords").Array(), func(result gjson.Result) string {
		return result.String()
	})
	return fofaQuery, nil
}

func (m *MicroKernel) NewQuery(fofaESQuery string, userId ...uint64) *elastic.BoolQuery {
	fofaESQuery = m.queryPreprocess(fofaESQuery)
	var q = elastic.NewBoolQuery()
	q.Must(elastic.NewRawStringQuery(fofaESQuery))
	if len(userId) > 0 {
		q.Must(elastic.NewTermQuery("extra.user_id", userId[0]))
	}
	q.Must(elastic.NewRangeQuery("extra.user_id").Gte(0))
	return q
}

func (m *MicroKernel) queryPreprocess(q string) string {
	if !strings.Contains(q, `"asn.as_organization.raw"`) {
		q = strings.ReplaceAll(q, `"asn.as_organization":`, `"asn.as_organization.raw":`)
	}
	q = strings.ReplaceAll(q, `"dom.hash"`, `"dom.ehash.raw"`)
	q = wildcardMatchHandle(q, hostMatchReg, hostMatchFunc) // 处理host匹配
	// q = wildcardMatchHandle(q, titleMatchReg, titleMatchFunc) // 处理title匹配

	return q
}

func wildcardMatchHandle(src string, re *regexp.Regexp, f func(string) string) string {
	return re.ReplaceAllStringFunc(src, f)
}

var (
	// anyReg, _     = regexp2.Compile(`(?<match_phrase>{"match_phrase":{"title":{"query":"(.*?)"}}})`, regexp2.RE2)
	hostMatchReg  = regexp.MustCompile(`{"match_phrase":{"host":{"query":"[A-Za-z0-9\-.]*"}}}`)
	titleMatchReg = regexp.MustCompile(`{"match_phrase":{"title":{"query":"([^\"]+)}}}`) //nolint:unused,gocritic
)

type matchPhrase struct {
	MatchPhrase any `json:"match_phrase,omitempty"`
}

type wildcard struct {
	Wildcard any `json:"wildcard"`
}

func hostMatchFunc(match string) string {
	var read matchPhrase
	err := json.Unmarshal([]byte(match), &read)
	if err != nil {
		return match
	}

	var host struct {
		Host struct {
			Query string `json:"query,omitempty"`
		} `json:"host"`
	}
	bs, _ := json.Marshal(read.MatchPhrase)
	_ = json.Unmarshal(bs, &host)

	var host2 struct {
		Host string `json:"host"`
	}
	host2.Host = "*" + host.Host.Query + "*"
	return utils.AnyToStr(wildcard{Wildcard: host2})
}

//nolint:unused,gocritic
func titleMatchFunc(match string) string {
	var read matchPhrase
	err := json.Unmarshal([]byte(match), &read)
	if err != nil {
		return match
	}

	var title struct {
		Title struct {
			Query string `json:"title,omitempty"`
		} `json:"title,omitempty"`
	}
	bs, _ := json.Marshal(read.MatchPhrase)
	_ = json.Unmarshal(bs, &title)

	var host2 struct {
		Title string `json:"title"`
	}
	host2.Title = "*" + title.Title.Query + "*"
	return utils.AnyToStr(wildcard{Wildcard: host2})
}

// ----------------------任务管理

// TaskInfo 获取微内核任务信息
func (m *MicroKernel) TaskInfo(taskId uint64) ([]uint64, error) {
	client := &http.Client{}
	req, err := http.NewRequest(
		http.MethodGet,
		m.getServerAddr()+"/api/v1/tasks/"+cast.ToString(taskId),
		strings.NewReader(`{"task_id":"`+cast.ToString(taskId)+`"}`),
	)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("微内核接口异常,Http状态码:%d,Http返回内容:%s", resp.StatusCode, string(body))
	}
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return nil, fmt.Errorf("%s", gjson.Get(string(body), "data.errdesc").String())
	}
	// println(gjson.Get(string(body), "data.task_id").String())
	return nil, nil
}

// AddTask 添加扫描任务
func (m *MicroKernel) AddTask(task *TaskInfo) error {
	paramStr, _ := json.Marshal(task)
	log.Infof("AddTask,paramStr:%s", string(paramStr))
	resp, err := http.Post(m.getServerAddr()+"/api/v1/tasks/add", "application/json", strings.NewReader(string(paramStr)))
	if err != nil {
		return err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New(gjson.Get(string(body), "message").String())
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	return nil
}

// Pause 暂停任务
func (m *MicroKernel) Pause(taskId uint64) error {
	resp, err := http.Post(
		m.getServerAddr()+"/api/v1/tasks/pause",
		"application/json",
		strings.NewReader(`{"task_id":"`+cast.ToString(taskId)+`"}`),
	)
	if err != nil {
		return err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New(gjson.Get(string(body), "message").String())
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	return nil
}

// Resume 恢复暂停任务
func (m *MicroKernel) Resume(taskId uint64) error {
	resp, err := http.Post(
		m.getServerAddr()+"/api/v1/tasks/resume",
		"application/json",
		strings.NewReader(`{"task_id":"`+cast.ToString(taskId)+`"}`),
	)
	if err != nil {
		return err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New(gjson.Get(string(body), "message").String())
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	return nil
}

// Stop 停止任务
func (m *MicroKernel) Stop(taskId uint64) error {
	resp, err := http.Post(
		m.getServerAddr()+"/api/v1/tasks/stop",
		"application/json",
		strings.NewReader(`{"task_id":"`+cast.ToString(taskId)+`"}`),
	)
	if err != nil {
		return err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New(gjson.Get(string(body), "message").String())
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	return nil
}

// ----------------------微内核管理

// SystemInfo 获取微内核信息
func (m *MicroKernel) SystemInfo() (*MicroInfo, error) {
	resp, err := http.Get(m.getServerAddr() + "/api/v1/system/infos")
	if err != nil {
		return nil, err
	}
	info := MicroInfo{}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("微内核接口异常,Http状态码:%d,Http返回内容:%s", resp.StatusCode, string(body))
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return nil, fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	if unErr := json.Unmarshal([]byte(gjson.Get(string(body), "data").String()), &info); unErr != nil {
		return nil, unErr
	}
	return &info, nil
}

// Activated 激活微内核
func (m *MicroKernel) Activated(license string) error {
	resp, err := http.Post(
		m.getServerAddr()+"/api/v1/activated",
		"application/json",
		strings.NewReader(`{"license":"`+license+`"}`),
	)
	if err != nil {
		return err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New(gjson.Get(string(body), "message").String())
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	return nil
}

// GetIdKey 获取IdKey信息
func (m *MicroKernel) GetIdKey() (string, error) {
	resp, err := http.Get(m.getServerAddr() + "/api/v1/id-key")
	if err != nil {
		return "", err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != 200 {
		return "", errors.New(gjson.Get(string(body), "message").String())
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return "", fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	return gjson.Get(string(body), "data.id_key").String(), nil
}

// ----------------------规则管理

// AddRule 添加规则
func (m *MicroKernel) AddRule(rule *RuleInfo) error {
	paramStr, _ := json.Marshal(rule)
	resp, err := http.Post(m.getServerAddr()+"/api/v1/rules", "application/json", strings.NewReader(string(paramStr)))
	if err != nil {
		return err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New(gjson.Get(string(body), "message").String())
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	return nil
}

// UpdateRule 更新规则
func (m *MicroKernel) UpdateRule(ruleId string, rule *RuleInfo) error {
	client := http.Client{}
	paramStr, _ := json.Marshal(rule)
	req, err := http.NewRequest(http.MethodPut, m.getServerAddr()+"/api/v1/rules/"+ruleId, strings.NewReader(string(paramStr)))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New(gjson.Get(string(body), "message").String())
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	return nil
}

// DeleteRule 删除规则
func (m *MicroKernel) DeleteRule(ruleId string) error {
	client := http.Client{}
	req, err := http.NewRequest(http.MethodDelete, m.getServerAddr()+"/api/v1/rules/"+ruleId, http.NoBody)
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != 200 {
		return errors.New(gjson.Get(string(body), "message").String())
	}
	defer resp.Body.Close()
	if gjson.Get(string(body), "status_code").Uint() != 200 {
		return fmt.Errorf("%s", gjson.Get(string(body), "message").String())
	}
	return nil
}
