package microkernel

import (
	"fmt"
	"testing"

	"github.com/dlclark/regexp2"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"go.uber.org/goleak"
	"golang.org/x/net/context"

	"micro-service/initialize/es"
	"micro-service/pkg/cfg"
)

func Test_SystemInfo(t *testing.T) {
	rsp, e := microkernel.SystemInfo()
	if e != nil {
		println(e.Error())
	}
	println(fmt.Sprintf("%+v", rsp))
	assert.NotEmpty(t, rsp)
}

func Test_TaskInfo(t *testing.T) {
	rsp, e := microkernel.TaskInfo(10332)
	if e != nil {
		println(e.Error())
	}
	println(fmt.Sprintf("%+v", rsp))
	assert.NotEmpty(t, rsp)
}

func Test_FofaQuery(t *testing.T) {
	edb := es.GetInstance(cfg.LoadElastic())
	fofaQuery, err := microkernel.FofaQuery(`domain="baidu.com"`)
	if err != nil {
		println(err.Error())
	} else {
		q := edb.Search(fofaQuery.Index...).Query(elastic.NewRawStringQuery(fofaQuery.Query)).From(0).Size(10)
		res, f := q.Do(context.TODO())
		if f != nil {
			println(f.Error())
		} else {
			for _, v := range res.Hits.Hits {
				bs, _ := v.Source.MarshalJSON()
				println(fmt.Sprintf("%s", string(bs)))
			}
		}
	}
}

func Test_Activated(t *testing.T) {
	e := microkernel.Activated("AAAAAA")
	if e != nil {
		println(e.Error())
	}
}
func Test_GetIdKey(t *testing.T) {
	keyStr, e := microkernel.GetIdKey()
	if e != nil {
		println(e.Error())
	}
	println(keyStr)
}

func Test_hostMatch(t *testing.T) {
	defer goleak.VerifyNone(t)

	var q = `{"bool":{"must":{"constant_score":{"filter":{"bool":{"must":[{"bool":{"filter":{"match_phrase":{"host":{"query":"fofa.info"}}}}},{"range":{"lastupdatetime":{"gte":"2022-09-11 00:00:00"}}},{"bool":{"must_not":{"term":{"is_honeypot":true}}}},{"bool":{"should":[{"bool":{"must":{"term":{"is_fraud":false}}}},{"bool":{"must_not":{"exists":{"field":"is_fraud"}}}}]}}]}}}}}}`
	got := wildcardMatchHandle(q, hostMatchReg, hostMatchFunc)
	fmt.Println("after:", got)

	q = `{"match_phrase":{"host":{"query":"fofa.info"}}},{"match_phrase":{"title":{"query":"北京 google.com"}}}`
	//got = wildcardMatchHandle(q, titleMatchReg, titleMatchFunc)
	//assert.Equal(t, `{"wildcard":{"host":"*fofa.info*"}},{"wildcard":{"title":"*北京 google.com*"}}`, got)

	re, err := regexp2.Compile(`(?<match_phrase>{"match_phrase":{"title":{"query":"(.*?)"}}})`, regexp2.RE2)
	assert.Nil(t, err)
	if re != nil {
		m, errFind := re.FindStringMatch(q)
		assert.Nil(t, errFind)
		if m != nil {
			fmt.Println(m.String())
		}
	}
}
