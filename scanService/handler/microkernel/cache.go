package microkernel

import (
	"context"

	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
)

const microkernelAddrKey = "foradar_cache:microkernel:addr"

func init() {
	// 在测试环境中或Redis实例为nil时跳过初始化
	if redisInstance := redis.GetInstance(); redisInstance != nil {
		redisInstance.Del(context.TODO(), microkernelAddrKey)
	}
}

func getMicrokernelAddrCache() string {
	addr, _ := redis.GetInstance().Get(context.TODO(), microkernelAddrKey).Result()
	if addr != cfg.MicroKernelDefaultAddr {
		return addr
	}
	return ""
}

func setMicrokernelAddr(addr string) {
	_ = redis.GetInstance().
		SetNX(context.TODO(), microkernelAddrKey, addr, utils.Day).Err()
}
