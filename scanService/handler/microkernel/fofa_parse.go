package microkernel

import (
	"context"
	"encoding/json"
	"net/url"
	"strings"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"micro-service/initialize/es"
	elastic2 "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_subdomain"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/scanService/proto"
)

const (
	_http  = "http"
	_https = "https"
)

func GetFOFAParse(search string) (FofaQuery, error) {
	search, _ = utils.Base64Decode(search)
	search, _ = url.QueryUnescape(search)
	search = utils.If(search != "", search, `ip != ""`)

	var rsp FofaQuery
	key := "foradar_cache:space_search:fofa_parse:" + utils.QBase64(search)
	if redis.GetCache(key, &rsp) {
		return rsp, nil
	}
	x, err := GetInstance().FofaQuery(search)
	if err != nil || x == nil {
		return FofaQuery{}, err
	}

	bs, _ := json.Marshal(x)
	_ = redis.Set(context.TODO(), key, string(bs), 10*time.Minute)
	return *x, nil
}

// QueryEsByFOFAParse 用户fofa查询语句解析为es查询语句，并返回对应的查询结果
func QueryEsByFOFAParse(ctx context.Context, req *pb.FofaParseQueryRequest, rsp *pb.FofaParseQueryResponse) error {
	fofaRsp, err := GetFOFAParse(req.Search)
	if err != nil {
		return err
	}

	booQuery := GetInstance().NewQuery(fofaRsp.Query, req.UserId...)
	query := es.GetInstance(cfg.LoadElastic()).Search(fofaRsp.Index...).Query(booQuery).Sort("lastupdatetime", false)
	from := elastic2.GetFrom(int(req.Page), int(req.PerPage))
	result, err := query.From(from).Size(int(req.PerPage)).Do(ctx)
	x, _ := booQuery.Source()
	bs, _ := json.Marshal(x)
	log.WithContextInfof(ctx, "[QueryEsByFOFAParse] Req param: %v, es query: %s", req, string(bs))
	if err != nil {
		return errx.NewError(errx.ERROR+1, err)
	}
	if result.TotalHits() == 0 {
		return nil
	}

	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	rsp.Total = result.TotalHits()
	for _, v := range result.Hits.Hits {
		b, errMarshal := v.Source.MarshalJSON()
		if errMarshal != nil {
			continue
		}

		var record fofaee_subdomain.FofeeSubdomain
		if errJson := json.Unmarshal(b, &record); errJson != nil {
			log.WithContextWarnf(ctx, "[Scan-ParseQueryEsByFOFA] es result items json.Unmarshal failed: %v", errJson)
			continue
		}
		item := &pb.FofaParseQueryResponseItem{
			UserId:    gjson.GetBytes(b, "extra.user_id").Int(),
			Ip:        record.Ip,
			Port:      cast.ToInt64(record.Port),
			Domain:    record.Domain,
			Subdomain: record.Subdomain,
			Asn: &pb.FofaParseQueryResponseAsn{
				AsNumber:       cast.ToInt64(record.Asn.AsNumber),
				AsOrganization: record.Asn.AsOrganization,
			},
			Geoip: &pb.FofaParseQueryResponseGeo{
				CityName:       record.GeoIp.CityName,
				ContinentCode:  record.GeoIp.ContinentCode,
				CountryCode2:   record.GeoIp.CountryCode2,
				CountryCode3:   record.GeoIp.CountryCode3,
				CountryName:    record.GeoIp.CountryName,
				DmaCode:        cast.ToInt64(record.GeoIp.DmaCode),
				Latitude:       record.GeoIp.Latitude,
				Longitude:      record.GeoIp.Longitude,
				PostalCode:     record.GeoIp.PostalCode,
				RealRegionName: record.GeoIp.RealRegionName,
				RegionName:     record.GeoIp.RegionName,
				Timezone:       record.GeoIp.Timezone,
			},
			Fid:        gjson.GetBytes(b, "dom.ehash").String(),
			Header:     record.Header,
			Banner:     gjson.GetBytes(b, "banner").String(),
			IsHoneypot: gjson.GetBytes(b, "is_honeypot").Bool(),
			Title:      record.Title,
			Body:       record.Body,
			Cert:       record.Cert,
			Certs: &pb.FofaParseQueryResponse_Certs{
				CertDate:  gjson.GetBytes(b, "certs.cert_date").String(),
				IsValid:   gjson.GetBytes(b, "certs.is_valid").Bool(),
				IssuerCn:  gjson.GetBytes(b, "certs.issuer_cn").String(),
				IssuerCns: nil,
				NotAfter:  gjson.GetBytes(b, "certs.not_after").String(),
				NotBefore: gjson.GetBytes(b, "certs.not_before").String(),
				SigAlth:   gjson.GetBytes(b, "certs.sig_alth").String(),
				Sn:        gjson.GetBytes(b, "certs.sn").String(),
				SubjectCn: gjson.GetBytes(b, "certs.subject_cn").String(),
				ValidType: gjson.GetBytes(b, "certs.valid_type").String(),
			},
			Server:         record.Server,
			Protocol:       record.Protocol,
			Product:        record.Product,
			LastUpdateTime: record.LastUpdateTime,
		}
		item.Host = getHost(record.Protocol, record.Host, record.Ip, item.Port)
		item.JumpLink = getJumpLink(item.Protocol, item.Subdomain, item.Domain, item.Ip, item.Port)
		rsp.Items = append(rsp.Items, item)
	}
	return nil
}

func getHost(protocol, host, ip string, port int64) string {
	if host != "" {
		return host
	}

	protocol = utils.If(protocol == _http || protocol == _https, protocol+"://", "")
	host = utils.If(utils.IsIPv6(ip), "["+ip+"]", ip)
	portStr := utils.If(port == 80 || port == 443, "", ":"+cast.ToString(port))

	return protocol + host + portStr
}

func getJumpLink(protocol, subdomain, domain, ip string, port int64) string {
	if !(protocol == _http || protocol == _https) {
		return ""
	}

	var portStr string
	if !(port == 80 || port == 443 || port == 0) {
		portStr = ":" + cast.ToString(port)
	}

	var body = domain
	if domain != "" {
		if subdomain != "" {
			body = subdomain + "." + domain
		}
	} else {
		body = utils.If[string](utils.IsIPv6(ip), "["+ip+"]", ip)
	}

	if body == "" {
		return ""
	}
	return protocol + "://" + body + portStr
}

var fieldMap = map[string]string{
	"company":       "公司排名",
	"country":       "国家排名",
	"fid":           "资产特征聚类",
	"port":          "端口排名",
	"server":        "Server排名",
	"os":            "操作系统排名",
	"subdomain":     "网站",
	"service":       "协议",
	"lastchecktime": "",
}

func ParseSearchStatistics(ctx context.Context, req *pb.FofaParseQueryRequest, rsp *pb.FofaParseAggResponse) error {
	fofaRsp, err := GetFOFAParse(req.Search)
	if err != nil {
		return err
	}

	q := GetInstance().NewQuery(fofaRsp.Query, req.UserId...)
	query := es.GetInstance(cfg.LoadElastic()).Search(fofaRsp.Index...).Query(q)
	// agg field: port, subdomain, country, fid, os, lastchecktime, service, protocol, took, company
	query.Aggregation("port", elastic.NewTermsAggregation().Field("port").Size(5).OrderByCountDesc())
	query.Aggregation("fid", elastic.NewTermsAggregation().Field("dom.ehash.raw").Size(5).OrderByCountDesc())
	query.Aggregation("country", elastic.NewTermsAggregation().Field("geoip.country_code2.raw").Size(5).OrderByCountDesc())
	query.Aggregation("server", elastic.NewTermsAggregation().Field("server.raw").Size(5).OrderByCountDesc())
	// query.Aggregation("company", elastic.NewTermsAggregation().Field("rule_tags.company.raw").Size(5).OrderByCountDesc())
	query.Aggregation("lastchecktime", elastic.NewMaxAggregation().Field("lastchecktime")).Size(0)
	query.Aggregation("by_index", elastic.NewTermsAggregation().Field("_index").Size(len(fofaRsp.Index)))
	do, err := query.Size(0).Do(ctx)
	if err != nil {
		return errx.NewError(errx.ERROR+1, err)
	}
	if len(do.Aggregations) == 0 {
		return nil
	}

	for name, raw := range do.Aggregations {
		singleAgg := new(elastic.AggregationBucketKeyItems)
		_ = json.Unmarshal(*raw, singleAgg)
		switch name {
		case "lastchecktime":
			continue
		case "by_index":
			for _, v := range singleAgg.Buckets {
				if strings.HasPrefix(cast.ToString(v.Key), "fofaee_") {
					key := cast.ToString(v.Key)[len("fofaee_"):]
					rsp.Items = append(rsp.Items,
						&pb.FofaParseAggResponse_Item{Key: key, Title: fieldMap[key], DocCount: v.DocCount})
				}
			}
		default:
			item := &pb.FofaParseAggResponse_Item{Key: name, Title: fieldMap[name]}
			for _, v := range singleAgg.Buckets {
				item.Children = append(item.Children,
					&pb.FofaParseAggResponse_Item_DocItem{Key: cast.ToString(v.Key), Title: cast.ToString(v.Key), DocCount: v.DocCount})
			}
			rsp.Items = append(rsp.Items, item)
		}
	}

	// lastchecktime
	if maxDate, found := do.Aggregations.Max("lastchecktime"); found {
		if maxDate.Value != nil {
			lastCheckTime := time.UnixMilli(cast.ToInt64(*maxDate.Value)).UTC().Format(utils.DateTimeLayout)
			rsp.Items = append(rsp.Items, &pb.FofaParseAggResponse_Item{Key: "lastchecktime", Lastchecktime: lastCheckTime})
		}
	}
	// company
	if len(rsp.Items) > 0 {
		rsp.Items = append(rsp.Items, &pb.FofaParseAggResponse_Item{Key: "company", Title: fieldMap["company"]})
	}

	return nil
}
