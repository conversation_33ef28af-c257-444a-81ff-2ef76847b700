package microkernel

import (
	"context"
	"fmt"
	"micro-service/pkg/log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"micro-service/initialize/es"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
	pb "micro-service/scanService/proto"
)

func Test_Parse(t *testing.T) {
	cfg.InitLoadCfg()
	es.GetInstance(cfg.LoadElastic())

	fofaRsp, err := GetInstance().FofaQuery(`title="301 Moved Permanently" & ip="************ && port=443"`)
	assert.Nil(t, err)
	fmt.Println(fofaRsp.Query)
}

func Test_QueryEsByFOFAParse(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()
	es.GetInstance(cfg.LoadElastic())
	redis.GetInstance(cfg.LoadRedis())

	var req = &pb.FofaParseQueryRequest{
		Search:  utils.QBase64(`app="REALOR-天翼应用虚拟化系统"`),
		UserId:  []uint64{},
		Page:    1,
		PerPage: 10,
	}
	rsp := &pb.FofaParseQueryResponse{}
	err := QueryEsByFOFAParse(context.TODO(), req, rsp)
	assert.Nil(t, err)

	fmt.Printf("total: %d\n", rsp.GetTotal())
	for _, v := range rsp.GetItems() {
		fmt.Printf("ip: %s, port: %d, host: %s jump_link:%s, fid: %s\n", v.GetIp(), v.GetPort(), v.Host, v.JumpLink, v.Fid)
	}
	time.Sleep(time.Second)
}

func Test_ParseSearchStatistics(t *testing.T) {
	cfg.InitLoadCfg()
	es.GetInstance(cfg.LoadElastic())
	redis.GetInstance(cfg.LoadRedis())

	var req = &pb.FofaParseQueryRequest{
		Search: utils.QBase64(``),
		UserId: []uint64{1},
	}

	var rsp = &pb.FofaParseAggResponse{}
	err := ParseSearchStatistics(context.TODO(), req, rsp)
	assert.Nil(t, err)
}
