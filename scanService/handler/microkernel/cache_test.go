package microkernel

import (
	"fmt"
	"testing"

	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"

	"github.com/stretchr/testify/assert"
)

func Test_GetMicrokernelAddr(t *testing.T) {
	cfg.InitLoadCfg()
	_ = redis.GetInstance(cfg.LoadRedis())

	addr := GetInstance().getServerAddr()
	assert.NotEqual(t, addr, cfg.MicroKernelDefaultAddr)
}

func Test_getMicrokernelAddrCache(t *testing.T) {
	cfg.InitLoadCfg()
	_ = redis.GetInstance(cfg.LoadRedis())

	addr := getMicrokernelAddrCache()
	fmt.Println(addr)
}
