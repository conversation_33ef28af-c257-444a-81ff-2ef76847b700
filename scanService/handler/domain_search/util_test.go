package domain_search

import (
	"fmt"
	"strconv"
	"testing"

	ds "micro-service/middleware/mysql/domain_search"
	"micro-service/pkg/utils"

	"github.com/stretchr/testify/assert"
)

func Test_searchSyntax(t *testing.T) {
	var cases = []struct {
		name    string
		domain  string
		sources []string
		want    string
	}{
		{name: "empty-domain", sources: []string{ds.SourceBing, ds.SourceGoogle}, want: ""},
		{name: "empty-source", domain: "example.com", want: ""},
		{
			name: "test-example.com", domain: "example.com",
			sources: []string{ds.SourceBing, ds.SourceGoogle},
			want: utils.AnyToStr([]engineSyntax{
				{Name: ds.SourceBing, Syntax: engineMap[ds.SourceBing] + "example.com"},
				{Name: ds.SourceGoogle, Syntax: engineMap[ds.SourceGoogle] + "example.com"},
			}),
		},
	}

	for _, v := range cases {
		t.Run(v.name, func(t *testing.T) {
			got := searchSyntax(v.domain, v.sources...)
			if got != v.want {
				t.Errorf("got: %s, but wanted: %s", got, v.want)
			}
		})
	}
}

func Test_calGroup(t *testing.T) {
	domain := []string{"baidu.com", "google.com"}
	subdomain := []string{"www.baidu.com", "www.google.com"}

	groups, split := calGroup(domain, subdomain)
	assert.Equal(t, false, split)
	fmt.Println(groups)

	for i := 0; i < 100; i++ {
		domain = append(domain, strconv.Itoa(i))
	}
	for i := 100; i < 2600; i++ {
		subdomain = append(subdomain, strconv.Itoa(i))
	}
	groups, split = calGroup(domain, subdomain)
	assert.Equal(t, true, split)
	fmt.Println(groups)
}
