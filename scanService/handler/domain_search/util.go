package domain_search

import (
	ds "micro-service/middleware/mysql/domain_search"
	"micro-service/pkg/utils"
)

const groupMaxStrLenLimit = 3000

type group struct {
	domainType int
	domains    []string
}

func domainLen(domain string) int {
	return len(domain) + 3
}

func calGroup(domain, subdomain []string) ([]group, bool) {
	merge := utils.ListMerge(domain, subdomain)
	if len(utils.AnyToStr(merge)) < groupMaxStrLenLimit {
		return []group{{domainType: ds.TypeMixed, domains: merge}}, false
	}

	groupFunc := func(all []string, dt int) []group {
		var currentLen = 2
		var groups []group
		var currentGroup = group{domainType: dt}
		for _, v := range all {
			// 检查当前组的长度是否已超过最大长度限制
			if currentLen+domainLen(v) > groupMaxStrLenLimit {
				// 当前组已满
				groups = append(groups, currentGroup)

				currentLen = 2
				currentGroup = group{domainType: dt}
			}
			// 将域名添加到当前组中
			currentLen += domainLen(v)
			currentGroup.domains = append(currentGroup.domains, v)
		}
		if len(currentGroup.domains) > 0 {
			groups = append(groups, currentGroup)
		}
		return groups
	}

	var groups []group
	groups = append(groups, groupFunc(domain, ds.TypeDomain)...)
	groups = append(groups, groupFunc(subdomain, ds.TypeSubdomain)...)

	return groups, true
}

type engineSyntax struct {
	Name   string `json:"name"`
	Syntax string `json:"syntax"`
}

func searchSyntax(domain string, engines ...string) string {
	if domain == "" || len(engines) == 0 {
		return ""
	}

	var l = make([]engineSyntax, 0, len(engines))
	for _, v := range engines {
		syntax, ok := engineMap[v]
		if ok {
			l = append(l, engineSyntax{Name: v, Syntax: syntax + domain})
		}
	}
	return utils.AnyToStr(l)
}
