package domain_search

import (
	"context"
	"encoding/json"
	"strconv"
	"sync"
	"time"

	"micro-service/middleware/mysql"
	ds "micro-service/middleware/mysql/domain_search"
	lpa "micro-service/middleware/mysql/login_page_assets"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/scanService/handler/login"
	pb "micro-service/scanService/proto"

	cmap "github.com/orcaman/concurrent-map/v2"
)

func percent(index, total int) string {
	p := utils.Percent(index, total, 2)
	return strconv.FormatFloat(p, 'f', 2, 10)
}

func mixedPage(domain string) int {
	page := 10
	if utils.IsRootDomain(domain) {
		page = 50
	}
	return page
}

// 执行搜索引擎数据爬取
func domainSearchFetch(ctx context.Context, master *ds.Task, children []*ds.Task) {
	if master.HasChildren == ds.HasChildren {
		children = append(children, master)
	}
	var db = ds.NewDomainSearchModel()
	byId := mysql.WithId[uint64](master.Id)
	for i := range children {
		if errFetch := fetchByTask(children[i]); errFetch != nil {
			log.WithContextErrorf(ctx, "[Domain-Search] update master task, id: %d, %v", master.Id, errFetch)
		}
		//_ = db.TaskUpdateAny(map[string]any{"progress": percent(i+1, len(children))}, byId)
	}

	err := db.TaskUpdateAny(map[string]any{"progress": "100", "status": ds.StatusFinished}, byId)
	if err != nil {
		log.WithContextErrorf(ctx, "[Domain-Search] update master task, id: %d, %v", master.Id, err)
	}
}

func fetchByTask(task *ds.Task) error {
	var domains []string
	_ = json.Unmarshal(utils.String2Bytes(task.Search), &domains)
	if len(domains) == 0 {
		return nil
	}
	var page int
	switch task.DomainType {
	case ds.TypeDomain:
		page = 50
	case ds.TypeSubdomain:
		page = 10
	}

	var ch = make(chan *ds.Result, 10)
	var result = make([]*ds.Result, 0, len(domains)*10*20*3) // length * 10*20(page) * engines(google/bing/baidu)
	go func() {
		domainGroup := utils.ListSplit[string](domains, 20)
		for i := range domainGroup {
			var wg sync.WaitGroup
			for _, v := range domainGroup[i] {
				wg.Add(1)
				if task.DomainType == ds.TypeMixed {
					page = mixedPage(v)
				}
				go func(domain string) {
					defer wg.Done()
					ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
					defer cancel()
					searchParam := callReq{
						userId:    0,
						companyId: 0,
						domain:    domain,
						maxPage:   page,
					}
					rs := domainSearch(ctx, searchParam)
					for j := range rs {
						ch <- rs[j]
					}
				}(v)
			}
			wg.Wait()
		}
		close(ch)
	}()

	for tt := range ch {
		tt.TaskId = task.Id
		result = append(result, tt)
	}

	// logic
	err := ds.NewDomainSearchModel().ResultCreateInBatch(1000, result...)
	return err
}

var engineMap = map[string]string{
	ds.SourceGoogle: "site:",
	ds.SourceBing:   "site:",
	ds.SourceBaidu:  "site:",
}

type callReq struct {
	userId    uint64
	companyId uint64
	domain    string
	maxPage   int
}

func call(ctx context.Context, engine string, req callReq, m cmap.ConcurrentMap[string, *lpa.LoginPageAssets]) {
	var err error
	param := &pb.LoginPageScreenshotByTopDomainRequest{
		UserId:           req.userId,
		CompanyId:        req.companyId,
		OnlyDomainSearch: true,
	}
	switch engine {
	case ds.SourceGoogle:
		err = login.GetByGoogle(ctx, engineMap[ds.SourceGoogle]+req.domain, param, m, req.maxPage)
	case ds.SourceBing:
		err = login.GetByBing(ctx, engineMap[ds.SourceBing]+req.domain, param, m, req.maxPage, req.domain)
	case ds.SourceBaidu:
		err = login.GetByBaidu(ctx, engineMap[ds.SourceBaidu]+req.domain, param, m, req.maxPage)
	}
	if err != nil {
		log.WithContextWarnf(ctx, "[Domain-Search] search domain->%s by %s: %v", req.domain, engine, err)
	}
}

func domainSearch(ctx context.Context, req callReq) []*ds.Result {
	var wg sync.WaitGroup
	wg.Add(3)
	var baiduAsset = cmap.New[*lpa.LoginPageAssets]()
	go func() {
		defer wg.Done()
		call(ctx, ds.SourceBaidu, req, baiduAsset)
	}()

	var bingAsset = cmap.New[*lpa.LoginPageAssets]()
	go func() {
		defer wg.Done()
		call(ctx, ds.SourceBing, req, bingAsset)
	}()

	var googleAsset = cmap.New[*lpa.LoginPageAssets]()
	go func() {
		defer wg.Done()
		call(ctx, ds.SourceGoogle, req, googleAsset)
	}()
	wg.Wait()
	length := 2 * (googleAsset.Count() + bingAsset.Count() + baiduAsset.Count()) / 3
	var urlMap = make(map[string]string, length)
	merge := func(engine string, m map[string]*lpa.LoginPageAssets) []*ds.Result {
		source := utils.AnyToStr([]string{engine})
		l := make([]*ds.Result, 0, len(m))
		for _, v := range m {
			if _, ok := urlMap[v.OriginUrl]; !ok {
				urlMap[v.OriginUrl] = ""
				l = append(l, &ds.Result{
					Url:          v.OriginUrl,
					Domain:       req.domain,
					Title:        v.Title,
					Source:       source,
					SearchSyntax: searchSyntax(req.domain, engine),
				})
			}
		}
		return l
	}

	var result = make([]*ds.Result, 0, length)
	result = append(result, merge(ds.SourceGoogle, googleAsset.Items())...) // Google
	result = append(result, merge(ds.SourceBaidu, baiduAsset.Items())...)   // Baidu
	result = append(result, merge(ds.SourceBing, bingAsset.Items())...)     // Bing

	return result
}
