package domain_search

import (
	"context"
	"errors"
	"fmt"
	"time"

	"micro-service/middleware/mysql"
	ds "micro-service/middleware/mysql/domain_search"
	"micro-service/pkg/utils"
	pb "micro-service/scanService/proto"
)

func taskInfo(userId, taskId uint64, columns ...string) (ds.Task, error) {
	//需要去掉user_id条件，因为这个是客户端调用这个接口，拿不到user_id参数，加上的话，数据就查询不到了
	info, err := ds.NewDomainSearchModel().TaskFirst(
		mysql.WithId[uint64](taskId),
		mysql.WithSelect(columns...),
	)
	switch {
	case mysql.IsNotFound(err):
		return ds.Task{}, errors.New("查询记录不存在")
	case err != nil:
		return ds.Task{}, err
	}
	return info, nil
}

func TaskInfo(req *pb.DomainSearchTaskInfoRequest, rsp *pb.DomainSearchTaskInfoResponse) error {
	info, err := taskInfo(req.UserId, req.TaskId)
	if err != nil {
		return err
	}
	rsp.Id = info.Id
	rsp.Progress = info.Progress
	rsp.Status = int32(info.Status)
	rsp.CreatedAt = info.CreatedAt.Format(utils.DateTimeLayout)
	return nil
}

func TaskCreate(req *pb.DomainSearchTaskCreateRequest, rsp *pb.DomainSearchTaskInfoRequest) error {
	master := utils.ListDistinctNonZero(req.Domain.Master)
	req.Domain.Second = append(req.Domain.Second, req.Domain.Three...)
	second := utils.ListDistinctNonZero(req.Domain.Second)
	if len(master) == 0 && len(second) == 0 {
		return errors.New("缺失有效的查询域名")
	}

	if len(master) > 100 {
		return errors.New("主域名超过100最大查询限制")
	}
	if len(second) > 3000 {
		return errors.New("子域名超过3000最大查询限制")
	}

	groups, isSplit := calGroup(master, second)
	// 主任务
	masterTask := &ds.Task{
		UserId:      req.UserId,
		SafeUserId:  req.SafeUserId,
		CompanyId:   req.CompanyId,
		HasChildren: utils.If(isSplit, ds.HasChildren, 0),
		Status:      ds.StatusProcessing,
		Progress:    "0",
	}

	// 未分割任务
	if !isSplit {
		first := utils.ListFirstEle(groups)
		masterTask.DomainType = first.domainType
		masterTask.Search = utils.AnyToStr(first.domains)
	}

	err := ds.NewDomainSearchModel().TasksSave(masterTask)
	if err != nil {
		return fmt.Errorf("create domain search master task: %w", err)
	}

	rsp.TaskId = masterTask.Id

	// 分割任务
	var childrenTasks = make([]*ds.Task, len(groups))
	for i := range groups {
		childrenTasks[i] = &ds.Task{
			UserId:     req.UserId,
			SafeUserId: req.SafeUserId,
			CompanyId:  req.CompanyId,
			ParentId:   masterTask.Id,
			Status:     ds.StatusProcessing,
			DomainType: groups[i].domainType,
			Search:     utils.AnyToStr(groups[i].domains),
			Progress:   "0",
		}
	}
	err = ds.NewDomainSearchModel().TasksSave(childrenTasks...)
	if err != nil {
		return fmt.Errorf("create domain search children tasks: %w, master_task: %d", err, masterTask.Id)
	}

	// async domain search fetch
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Hour)
		defer cancel()
		domainSearchFetch(ctx, masterTask, childrenTasks)
	}()

	return nil
}

func ResultList(req *pb.DomainSearchResultListRequest, rsp *pb.DomainSearchResultListResponse) error {
	info, err := taskInfo(req.UserId, req.TaskId, "id", "has_children")
	if err != nil {
		return err
	}
	var handler []mysql.HandleFunc
	db := ds.NewDomainSearchModel()
	// 如果有子任务，获取所有子任务的任务ID
	if info.HasChildren == ds.HasChildren {
		all, _, errList := db.TaskList(0, 0, mysql.WithSelect("id"), mysql.WithColumnValue("parent_id", info.Id))
		if errList != nil {
			return err
		}
		ids := utils.ListColumn[uint64](all, func(x *ds.Task) uint64 { return x.Id })
		handler = append(handler, mysql.WithValuesIn("task_id", ids))
	} else {
		all, _, errList := db.TaskList(0, 0, mysql.WithSelect("id"), mysql.WithColumnValue("parent_id", info.Id))
		if errList != nil {
			return err
		}
		ids := utils.ListColumn[uint64](all, func(x *ds.Task) uint64 { return x.Id })
		ids = append(ids, info.Id)
		handler = append(handler, mysql.WithValuesIn("task_id", ids))
	}
	result, total, err := db.ResultFind(int(req.Page), int(req.PerPage), handler...)
	if err != nil {
		return err
	}

	rsp.Total = total
	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	rsp.Items = make([]*pb.DomainSearchResultListResponseUnit, len(result))
	for i := range result {
		rsp.Items[i] = &pb.DomainSearchResultListResponseUnit{
			Id:           result[i].Id,
			Url:          result[i].Url,
			Domain:       result[i].Domain,
			Title:        result[i].Title,
			Source:       result[i].Source,
			SearchSyntax: result[i].SearchSyntax,
			CreatedAt:    result[i].CreatedAt.Format(utils.DateTimeLayout),
		}
	}
	return nil
}
