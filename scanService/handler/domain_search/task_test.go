package domain_search

import (
	"micro-service/pkg/log"
	"testing"

	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	pb "micro-service/scanService/proto"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
)

func initCfg() {
	_ = godotenv.Load("../../../.env")

	cfg.InitLoadCfg()
	log.Init()
	mysql.GetInstance(cfg.LoadMysql())
}

func Test_ResultList(t *testing.T) {
	initCfg()

	var req = &pb.DomainSearchResultListRequest{
		TaskId:  1,
		UserId:  0,
		Page:    1,
		PerPage: 10,
	}
	var rsp = &pb.DomainSearchResultListResponse{}
	err := ResultList(req, rsp)
	assert.Nil(t, err)
}
