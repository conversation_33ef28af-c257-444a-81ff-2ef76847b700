package login

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	crawler "micro-service/crawlerService/proto"
	lpa "micro-service/middleware/mysql/login_page_assets"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/scanService/proto"

	"github.com/antchfx/htmlquery"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/spf13/cast"
	"golang.org/x/net/html"
)

const (
	// 管理 登陆 后台 系统 中心 用户 运维 运营 录入 客户 中台 login
	baiduLoginKeyword = `inurl:login login 登录`
)

func ScreenshotByTopDomain(ctx context.Context, req *pb.LoginPageScreenshotByTopDomainRequest) {
	domains := utils.ListDistinctNonZero(req.Domains)
	db := lpa.NewModel()
	for _, domain := range domains {
		assetMap := cmap.New[*lpa.LoginPageAssets]()
		// 获取百度登录页截图
		_ = GetByBaidu(ctx, "site:"+domain+" "+baiduLoginKeyword, req, assetMap, 10)
		// 获取bing截图
		_ = GetByBing(ctx, "site:"+domain+" "+baiduLoginKeyword, req, assetMap, 10, domain)
		// 获取google截图
		_ = GetByGoogle(ctx, "site:"+domain+" "+baiduLoginKeyword, req, assetMap, 10)

		var list = make([]*lpa.LoginPageAssets, 0, assetMap.Count())
		for _, value := range assetMap.Items() {
			list = append(list, value)
		}
		if errUpsert := db.UpsertByKey(req.UserId, list); errUpsert != nil {
			log.WithContextErrorf(ctx, "[Scan] LoginAsset UserId->%d, domain->%s, Search engine result write db failure: %v", req.UserId, domain, errUpsert)
		}
	}
}

const googleUrl = "https://www.google.com"

func parseGoogleSearchItem(node *html.Node) (string, string) {
	nodes := htmlquery.Find(node, `/div/div/a`)
	if len(nodes) == 0 {
		return "", ""
	}
	href := nodes[0]
	title := htmlquery.InnerText(href)
	if tag := htmlquery.FindOne(href, "//span"); tag != nil {
		title = htmlquery.InnerText(tag)
	}
	x := htmlquery.SelectAttr(href, "href")
	u := parseGoogleResultUrl(x)
	p, _ := url.QueryUnescape(u)
	if index := strings.Index(p, "&"); index > 0 {
		p = p[:index]
	}
	return title, p
}

func parseGoogleResultUrl(u string) string {
	if index := strings.Index(u, "http://"); index >= 0 {
		return u[index:]
	}
	if index := strings.Index(u, "https://"); index >= 0 {
		return u[index:]
	}
	return ""
}

func parseGooglePages(root *html.Node) ([]*html.Node, string) {
	var nextPageUrl string
	loginPages := htmlquery.Find(root, `//*[@id="main"]/div[position()>2]`)
	if len(loginPages) != 0 {
		tag := htmlquery.FindOne(root, "//footer//a")
		if tag != nil {
			nextPageUrl = htmlquery.SelectAttr(tag, "href")
		}
		return loginPages, googleUrl + nextPageUrl
	}
	loginPages = htmlquery.Find(root, "//div[@class='ezO2md']")
	href := htmlquery.FindOne(root, "//a[@class='frGj1b']")
	if href != nil {
		if u := htmlquery.SelectAttr(href, "href"); u != "" {
			nextPageUrl = googleUrl + u
		}
	}
	return loginPages, nextPageUrl
}

// GetByGoogle Google搜索
func GetByGoogle(ctx context.Context,
	query string,
	req *pb.LoginPageScreenshotByTopDomainRequest,
	pages cmap.ConcurrentMap[string, *lpa.LoginPageAssets],
	maxPage int) error {
	// first page url
	requestUrl := googleUrl + "/search?q=" + url.QueryEscape(query)
	// firstUrl := requestUrl
	for currentPage := 0; currentPage < maxPage && requestUrl != ""; currentPage++ {
		rsp, err := crawler.GetProtoClient().ChromeGet(ctx, &crawler.ChromeGetRequest{
			Vpn:  1,
			Url:  requestUrl,
			Wait: 3,
		}, utils.SetRpcTimeoutOpt(45), utils.SetRpcTimeoutOpt(45))
		if err != nil {
			return fmt.Errorf("query: %s login assets in Google failure: %w", query, err)
		}

		body := crawler.DecodeBy(rsp.Body)
		rootNode, err := htmlquery.Parse(strings.NewReader(body))
		if err != nil {
			return fmt.Errorf("query: %s, 解析Google页面失败: %w", query, err)
		}

		var loginPages []*html.Node
		loginPages, requestUrl = parseGooglePages(rootNode)
		// requestUrl = utils.If(requestUrl != "", requestUrl, firstUrl+fmt.Sprintf("&start=%d", currentPage*10))

		// 登录页面截图
		for i := range loginPages {
			title, originUrl := parseGoogleSearchItem(loginPages[i])
			if originUrl == "" {
				continue
			}

			var imageUrl, ip, Url string
			if !req.OnlyDomainSearch {
				sRsp, sErr := crawler.GetProtoClient().Screenshot(ctx, &crawler.ScreenshotRequest{
					Url: originUrl, Proxy: "disable"},
					utils.SetRpcTimeoutOpt(30), microx.ServerTimeout(15))
				if sErr != nil {
					log.WithContextWarn(ctx, fmt.Sprintf("query: %s, 获取截图页面失败, url:%s,ERROR:%s", query, originUrl, sErr))
					continue
				}
				Url = sRsp.Url
				imageUrl = storage.SaveDownload(originUrl, crawler.DecodeBy(sRsp.Img), req.UserId)
				ip = utils.ResolveDomain(utils.GetSubdomain(sRsp.Url))
			}
			port := utils.If(strings.HasPrefix(originUrl, "https"), "443", "80")
			uniqueKey := fmt.Sprintf("%d-%s-%s-%s", req.UserId, ip, port, Url)
			setItem := &lpa.LoginPageAssets{
				UserId:    req.UserId,
				CompanyId: int64(req.CompanyId),
				Title:     title,
				Url:       Url,
				OriginUrl: originUrl,
				ImgUrl:    imageUrl,
				Status:    0,
				Node:      "",
				Ip:        ip,
				Port:      port,
				UniqueKey: uniqueKey,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}
			pages.Set(uniqueKey, setItem)
		}
	}
	return nil
}

const bingUrl = "https://cn.bing.com"

// 获取bing的下一页数据-因为bing的搜索需要在一个窗口点进去，直接爬取到对应页面的url地址也不行，所以就爬取2页暂时
func bingNextPageUrl(node *html.Node, page int) string {
	if page > 1 || node == nil {
		return ""
	}
	var nodes []*html.Node
	if page == 1 {
		nodes = htmlquery.Find(node, `//*[@id="b_results"]/li[@class='b_pag']/nav/ul[@class='sb_pagF']/li`)
	} else {
		nodes = htmlquery.Find(node, `//*[@id="b_results"]/li[@class='b_pag']/nav/ul/li`)
	}

	if len(nodes) == 0 {
		return ""
	}
	var num int
	if page == 1 {
		num = page
	} else {
		num = page + 1
	}
	if num >= len(nodes) {
		return ""
	}
	a := htmlquery.FindOne(nodes[num], "/a")
	if a == nil {
		return ""
	}
	if s := htmlquery.SelectAttr(a, "href"); s != "" {
		return bingUrl + s
	}
	return ""
}

// bing的重试
func bingQueryByPageRetry(ctx context.Context, queryUrl string, retry int) (*crawler.ChromeGetResponse, error) {
	var err error
	var rsp = new(crawler.ChromeGetResponse)
	for i := 0; i <= retry; i++ {
		rsp, err = crawler.GetProtoClient().ChromeGet(ctx, &crawler.ChromeGetRequest{
			Url:  queryUrl,
			Wait: 3,
		}, utils.SetRpcTimeoutOpt(45))
		if err == nil {
			break
		}
	}
	return rsp, err
}

// 重试截图
func screenshotByRetry(ctx context.Context, url string, vpn, retry int) (*crawler.ScreenshotResponse, error) {
	var err error
	var rsp = new(crawler.ScreenshotResponse)
	for i := 0; i <= retry; i++ {
		rsp, err = crawler.GetProtoClient().Screenshot(ctx, &crawler.ScreenshotRequest{
			Url:   url,
			Vpn:   int32(vpn),
			Proxy: "disable"},
			utils.SetRpcTimeoutOpt(30), microx.ServerTimeout(20))
		if err == nil {
			break
		}
	}
	return rsp, err
}

// 解析bing的数据
func parseBingPage(ctx context.Context, query string, isShot bool, pageNode *html.Node, domain string) (*lpa.LoginPageAssets, string, error) {
	if pageNode == nil {
		return nil, "", errors.New("node is nil")
	}
	node, nErr := htmlquery.Query(pageNode, "/h2/a")
	if nErr != nil {
		log.WithContextWarn(ctx, fmt.Sprintf("query: %s,获取登录页面链接失败: %s", query, nErr.Error()))
		return nil, "", nErr
	}
	title := ""
	screenshotUrl := ""
	if node != nil {
		title = htmlquery.InnerText(node)
		screenshotUrl = htmlquery.SelectAttr(node, "href")
	}

	// 过滤不相关的域名结果
	if domain != "" && screenshotUrl != "" && !strings.Contains(screenshotUrl, domain) {
		log.WithContextWarnf(ctx, "过滤不相关域名结果: query=%s, url=%s, expected_domain=%s", query, screenshotUrl, domain)
		return nil, "", nil
	}

	// 调用截图
	var Url, imageUrl, ip string
	if !isShot {
		sRsp, sErr := screenshotByRetry(ctx, screenshotUrl, 0, 1)
		if sErr != nil {
			log.WithContextWarnf(ctx, "Get query: %s, url->%s screenshot failure: %v", query, screenshotUrl, sErr)
			return nil, "", sErr
		}
		Url = sRsp.Url
		imageUrl = sRsp.GetImg()
		ip = utils.ResolveDomain(utils.GetSubdomain(sRsp.Url))
	} else {
		Url = screenshotUrl
	}
	return &lpa.LoginPageAssets{
		Title:     title,
		Url:       Url,
		OriginUrl: screenshotUrl,
		ImgUrl:    imageUrl,
		Status:    0,
		Node:      "",
		Ip:        ip,
		Port:      utils.If(strings.HasPrefix(Url, "https"), "443", "80"),
	}, screenshotUrl, nil
}

// GetByBing Bing搜索
func GetByBing(ctx context.Context,
	query string,
	req *pb.LoginPageScreenshotByTopDomainRequest,
	pages cmap.ConcurrentMap[string, *lpa.LoginPageAssets],
	maxPage int,
	domain string) error {
	maxPage = utils.If(maxPage >= 1, maxPage, 10)
	// first page query url
	bingQueryUrl := bingUrl + "/search?q=" + url.QueryEscape(query)
	// bingQueryUrl = "https://www.bing.com/search?q=site%3a+fofa.info&FPIG=********************************&FPIG=FD5BD9DD08D74BF782759A25DA535242&first=37&FORM=PERE3"
	for page := 1; page <= maxPage && bingQueryUrl != ""; page++ {
		rsp, err := bingQueryByPageRetry(ctx, bingQueryUrl, 1)
		if err != nil {
			return fmt.Errorf("query: %s, 访问Bing(必应)页面失败: %w", query, err)
		}
		htmlRootNode, err := htmlquery.Parse(strings.NewReader(crawler.DecodeBy(rsp.Body)))
		if err != nil {
			return fmt.Errorf("query: %s, 解析Bing(必应)页面失败: %s", query, err.Error())
		}
		loginPages, findErr := htmlquery.QueryAll(htmlRootNode, `//ol[@id='b_results']//li[@class='b_algo']`)
		if findErr != nil {
			return fmt.Errorf("query: %s, 解析登录数据失败: %w", query, findErr)
		}

		// bing search next page
		bingQueryUrl = bingNextPageUrl(htmlRootNode, page)

		for _, node := range loginPages {
			item, originUrl, parsePageErr := parseBingPage(ctx, query, req.OnlyDomainSearch, node, domain)
			if item == nil || parsePageErr != nil || originUrl == "" {
				continue
			}
			item.UserId = req.UserId
			item.CompanyId = int64(req.CompanyId)
			if !req.OnlyDomainSearch {
				item.ImgUrl = storage.SaveDownload(originUrl, crawler.DecodeBy(item.ImgUrl), req.UserId)
			}
			uniqueKey := fmt.Sprintf("%d-%s-%s-%s", req.UserId, item.Ip, item.Port, item.Url)
			pages.Set(uniqueKey, item)
		}
	}
	return nil
}

// GetByBaidu 百度搜索
func GetByBaidu(ctx context.Context,
	query string,
	req *pb.LoginPageScreenshotByTopDomainRequest,
	pages cmap.ConcurrentMap[string, *lpa.LoginPageAssets],
	maxPage int) error {
	currentPage := 0
	requestUrl := "https://www.baidu.com/s?wd=" + url.QueryEscape(query) + "&rn=10"
	for currentPage < maxPage && requestUrl != "" {
		currentPage++
		rsp, err := crawler.GetProtoClient().ChromeGet(ctx, &crawler.ChromeGetRequest{
			Url:  requestUrl,
			Wait: 3,
		}, utils.SetRpcTimeoutOpt(45))
		if err != nil {
			return fmt.Errorf("query: %s, 访问百度页面失败: %s", query, err.Error())
		}
		if rsp.Title == "百度安全验证" {
			return fmt.Errorf("query: %s,百度安全认证拦截", query)
		}
		rsp.Body = crawler.DecodeBy(rsp.Body)
		xpath, err := htmlquery.Parse(strings.NewReader(rsp.Body))
		if err != nil {
			return fmt.Errorf("query: %s, 解析百度页面失败:%s", query, err.Error())
		}
		loginPages, findErr := htmlquery.QueryAll(xpath, `//*[@id="content_left"]/div`)
		if findErr != nil {
			return fmt.Errorf("query: %s, 解析登录数据失败:%s", query, err.Error())
		}
		// 登录页面截图
		for i := range loginPages {
			// 并发截图
			node, nErr := htmlquery.Query(loginPages[i], "/div/div[1]/h3/a")
			if nErr != nil {
				log.WithContextWarn(ctx, fmt.Sprintf("query: %s, 获取登录页面链接失败:%s", query, nErr.Error()))
				continue
			}
			if node == nil {
				continue
			}

			title := htmlquery.InnerText(node)
			urlAttr := htmlquery.SelectAttr(node, "href")
			var port = 80
			var ip, imageUrl, rspUrl string
			if !req.OnlyDomainSearch {
				sRsp, sErr := crawler.GetProtoClient().Screenshot(ctx, &crawler.ScreenshotRequest{Url: urlAttr}, utils.SetRpcTimeoutOpt(30))
				if sErr != nil {
					log.WithContextWarn(ctx, fmt.Sprintf("query: %s,获取截图页面失败,urlAttr:%s,ERROR:%s", query, urlAttr, sErr.Error()))
					continue
				}
				if strings.HasPrefix(sRsp.Url, "https") {
					port = 443
				}
				ip = utils.ResolveDomain(utils.GetSubdomain(sRsp.Url))
				imageUrl = storage.SaveDownload(urlAttr, crawler.DecodeBy(sRsp.Img), req.UserId)
				rspUrl = sRsp.Url
			} else {
				// 唯一值校验有问题，如果是域名搜索的话，不走上边逻辑，然后他这个rspUrl永远都是空的
				rspUrl = urlAttr
			}
			uniqueKey := fmt.Sprintf("%d-%s-%d-%s", req.UserId, ip, port, rspUrl)
			pages.Set(uniqueKey, &lpa.LoginPageAssets{
				UserId:    cast.ToUint64(req.UserId),
				CompanyId: cast.ToInt64(req.CompanyId),
				Title:     title,
				Url:       rspUrl,
				OriginUrl: urlAttr,
				ImgUrl:    imageUrl,
				Status:    0,
				Node:      "",
				Ip:        ip,
				Port:      cast.ToString(port),
				UniqueKey: uniqueKey,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			})
		}
		if nextPageNode, nextPageErr := htmlquery.Query(xpath, `//*[@id="page"]/div/a[last()]`); nextPageErr != nil {
			log.WithContextWarn(ctx, fmt.Sprintf("query: %s,获取百度下一页失败:%s", query, nextPageErr.Error()))
			requestUrl = ""
		} else {
			if nextPageNode != nil {
				href := htmlquery.SelectAttr(nextPageNode, "href")
				if htmlquery.InnerText(nextPageNode) == "下一页 >" && href != "" {
					requestUrl = "https://www.baidu.com" + href
				} else {
					log.WithContextWarn(ctx, fmt.Sprintf("query: %s, 百度下一页内容为空:%s", query, nextPageNode.Data))
					requestUrl = ""
				}
			} else {
				requestUrl = ""
			}
		}
	}
	return nil
}
