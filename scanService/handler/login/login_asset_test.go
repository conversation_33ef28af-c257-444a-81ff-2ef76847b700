package login

import (
	"context"
	"fmt"
	"github.com/joho/godotenv"
	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"strings"
	"testing"

	lpa "micro-service/middleware/mysql/login_page_assets"
	"micro-service/pkg/log"
	pb "micro-service/scanService/proto"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/stretchr/testify/assert"
)

func init() {
	// 初始化测试环境的日志系统
	log.Init()
}

func TestGetByBaidu(t *testing.T) {
	pages := cmap.New[*lpa.LoginPageAssets]()
	err := GetByBaidu(context.TODO(), "site: fofa.info", &pb.LoginPageScreenshotByTopDomainRequest{
		UserId:           1,
		OnlyDomainSearch: true,
	}, pages, 1)
	if err != nil {
		println(err.Error())
	}

	for _, v := range pages.Items() {
		fmt.Println(v)
	}

}

func Test_GetBing(t *testing.T) {
	_ = godotenv.Load("../../../.env")

	cfg.InitLoadCfg()
	log.Init()
	mysql.GetInstance(cfg.LoadMysql())
	m := cmap.New[*lpa.LoginPageAssets]()
	err := GetByBing(context.TODO(), "site: fofa.info", &pb.LoginPageScreenshotByTopDomainRequest{
		UserId:           1,
		OnlyDomainSearch: true,
	}, m, 3, "hxyhaboei18n.h.kypzh.net")
	for _, v := range m.Items() {
		fmt.Println(v.Title)
		fmt.Println(v.Url)
	}
	assert.Nil(t, err)
}

func Test_GetByGoogle(t *testing.T) {
	m := cmap.New[*lpa.LoginPageAssets]()
	_ = GetByGoogle(context.TODO(), "site: fofa.info", &pb.LoginPageScreenshotByTopDomainRequest{
		UserId:           1,
		OnlyDomainSearch: true,
	}, m, 1)
	for _, v := range m.Items() {
		fmt.Println(v)
	}
}

func Test_DomainFilter(t *testing.T) {
	// 测试域名过滤逻辑
	testCases := []struct {
		domain      string
		url         string
		shouldPass  bool
		description string
	}{
		{
			domain:      "pzhsteelmobile.cn",
			url:         "https://api-docs.deepseek.com/zh-cn/quick_start/token_usage/",
			shouldPass:  false,
			description: "不相关域名应该被过滤",
		},
		{
			domain:      "pzhsteelmobile.cn",
			url:         "https://pzhsteelmobile.cn/login",
			shouldPass:  true,
			description: "相关域名应该通过",
		},
		{
			domain:      "example.com",
			url:         "https://sub.example.com/admin",
			shouldPass:  true,
			description: "子域名应该通过",
		},
		{
			domain:      "",
			url:         "https://any-domain.com/page",
			shouldPass:  true,
			description: "空域名时应该通过所有URL",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			// 模拟新的简化过滤逻辑
			shouldFilter := tc.domain != "" && tc.url != "" && !strings.Contains(tc.url, tc.domain)

			if tc.shouldPass {
				assert.False(t, shouldFilter, "URL应该通过过滤: %s", tc.url)
			} else {
				assert.True(t, shouldFilter, "URL应该被过滤: %s", tc.url)
			}
		})
	}
}
