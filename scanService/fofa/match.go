package fofa

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	core "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

func BurstDomains(ctx context.Context, domains []string, level int, ch chan<- string) {
	for _, v := range domains {
		burstDomain := domainCleaning(v)
		if burstDomain == "" {
			continue
		}
		// queryDomain, b := rootDomain(v)
		// if !b {
		// 	continue
		// }

		log.Infof("[Scan] Domain Burst: FOFA query domain: %s", burstDomain)
		fofaDomains, err := queryWithFofa(context.TODO(), burstDomain)
		if err != nil || len(fofaDomains) == 0 {
			if err != nil {
				log.WithContextErrorf(ctx, "[Scan] Domain Burst: Get domain by FOFA: %+v", err)
			}
			continue
		}

		m := fofaDomainDistinct(fofaDomains)
		domainListMatch(burstDomain, m, level, ch)

		time.Sleep(10 * time.Second)
	}
	log.Infof("[Scan] Domain Burst by FOFA query ending!")
}

func fofaDomainDistinct(list []string) map[string]struct{} {
	m := make(map[string]struct{}, len(list))
	for _, v := range list {
		s := domainCleaning(v)
		if s != "" {
			m[s] = struct{}{}
		}
	}
	return m
}

func domainListMatch(burstDomain string, m map[string]struct{}, maxLevel int, ch chan<- string) {
	var list []string
	for domain := range m {
		for level := 1; level <= maxLevel; level++ {
			x, isValid := domainWithLevel(burstDomain, domain, level)
			if !isValid {
				continue
			}
			list = append(list, x)
		}
	}

	ds := utils.ListDistinctNonZero(list)
	for i := range ds {
		// sleep 150ms
		time.Sleep(50 * time.Millisecond)
		ch <- ds[i]
	}
}

// 获取二级域名
// www.baidu.com ==> baidu.com
// a.b.c.d ==> c.d
// ..com ==> ""
// nolint:unused,gocritic
func rootDomain(domain string) (string, bool) {
	split := strings.Split(domain, ".")
	list := make([]string, 0, len(split))
	for _, v := range split {
		if v != "" {
			list = append(list, v)
		}
	}

	l := len(list)
	if len(list) <= 1 {
		return "", false
	}

	return strings.Join(list[l-2:], "."), true
}

// 域名清洗
func domainCleaning(domain string) string {
	domain = strings.TrimSpace(domain)
	if domain == "" {
		return ""
	}

	if index := strings.Index(domain, "://"); index >= 0 { //nolint:gocritic
		domain = domain[index+3:]
	}
	if index := strings.Index(domain, ":"); index >= 0 {
		domain = domain[:index]
	}

	return domain
}

// origin: baidu.com ==> www.baidu.com
func domainWithLevel(burstDomain, fofaDomain string, level int) (string, bool) {
	if !strings.HasSuffix(fofaDomain, burstDomain) {
		return "", false
	}

	if burstDomain == fofaDomain {
		return "", false
	}

	burstSplit := strings.Split(burstDomain, ".")
	fofaSplit := strings.Split(fofaDomain, ".")

	n := len(burstSplit) + level
	if n > len(fofaSplit) {
		return "", false
	}

	domain := strings.Join(fofaSplit[len(fofaSplit)-n:], ".")
	return domain, true
}

const defaultPageSize = 500

func queryWithFofa(ctx context.Context, domain string) ([]string, error) {
	var err error
	var domains = make([]string, 0)
	root, _ := utils.FindRootDomain(domain)
	queryReq := &core.FofaQueryRequest{
		Qbase64: fmt.Sprintf(`domain=%q && host=%q`, root, domain),
		Field:   []string{"port", "host"},
		Size:    uint32(defaultPageSize),
	}
	for i := 1; i <= 20; i++ {
		queryReq.Page = uint32(i)
		queryRsp := &core.FofaQueryResponse{}
		if cfg.IsLocalClient() {
			// 本地化通过HTTP调用saas
			err = core.HttpClient(http.MethodPost, "/api/v1/fofa/query", queryReq, queryRsp)
		} else {
			// 否则rpc微服务调用
			queryRsp, err = core.GetProtoCoreClient().FofaQuery(ctx, queryReq, utils.SetRpcTimeoutOpt(30))
		}
		if len(queryRsp.GetSdata()) == 0 {
			break
		}
		for _, v := range queryRsp.GetSdata() {
			domains = append(domains, v.GetHost())
		}
	}
	if err != nil {
		log.WithContextErrorf(ctx, "[Scan] Domain Burst, query by FOFA: %+v", err)
	}

	return domains, nil
}
