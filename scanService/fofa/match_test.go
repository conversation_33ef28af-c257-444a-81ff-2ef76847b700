package fofa

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
)

func TestDomainCleaning(t *testing.T) {
	cases := []struct {
		in  string
		exp string
	}{
		{in: "", exp: ""},
		{in: "www.baidu.com", exp: "www.baidu.com"},
		{in: "http://baidu.com", exp: "baidu.com"},
		{in: "ssh://baidu.com", exp: "baidu.com"},
		{in: "https://www.baidu.com", exp: "www.baidu.com"},
		{in: "://baidu.com", exp: "baidu.com"},
		{in: "http://baidu.com:8080", exp: "baidu.com"},
		{in: "https:www//baidu.com:9090", exp: "baidu.com"},
	}

	for _, v := range cases {
		x := domainCleaning(v.in)
		assert.Equal(t, x, v.exp)
	}
}

func TestRootDomain(t *testing.T) {
	cases := []struct {
		in   string
		exp  string
		exp2 bool
	}{
		{in: "", exp: "", exp2: false},
		{in: "www.baidu.com", exp: "baidu.com", exp2: true},
		{in: "http://baidu.com", exp: "baidu.com", exp2: true},
		{in: "ssh://baidu.com", exp: "baidu.com", exp2: true},
		{in: "cloud.www.baidu.com", exp: "baidu.com", exp2: true},
		{in: "abc.d.baidu.com", exp: "baidu.com", exp2: true},
	}

	for _, v := range cases {
		x, b := rootDomain(domainCleaning(v.in))
		assert.Equal(t, v.exp, x)
		assert.Equal(t, v.exp2, b)
	}
}

func TestDomainWithLevel(t *testing.T) {
	cases := []struct {
		in1, in2 string // in1: brute, in2: fofa
		level    int
		exp      string
		exp2     bool
	}{
		{in1: "", exp: "", exp2: false},
		{in1: "x.y.z", in2: "a.b.c.x.y.c", level: 1, exp: "", exp2: false},
		{in1: "x.y.z", in2: "x.y.z", level: 1, exp: "", exp2: false},
		{in1: "x.y.z", in2: "a.b.c.x.y.z", level: 2, exp: "b.c.x.y.z", exp2: true},
		{in1: "y.z", in2: "a.b.c.x.y.z", level: 3, exp: "b.c.x.y.z", exp2: true},
		{in1: "x.y.z", in2: "a.b.c.x.y.z", level: 1, exp: "c.x.y.z", exp2: true},
		{in1: "a.b.c", in2: "x.y.z.a.b.c", level: 4, exp: "", exp2: false},
		{in1: "y.z", in2: "a.b.c.x.y.z", level: 4, exp: "a.b.c.x.y.z", exp2: true},
	}

	for _, v := range cases {
		x, b := domainWithLevel(v.in1, v.in2, v.level)
		assert.Equal(t, v.exp, x)
		assert.Equal(t, v.exp2, b)
	}
}

func Test_queryWithFofa(t *testing.T) {
	cfg.LoadCommon()
	redis.GetInstance(cfg.LoadRedis())

	const domain = "kjj.gz.gov.cn"
	root, _ := utils.FindRootDomain(domain)
	_ = root

	hosts, _ := queryWithFofa(context.TODO(), domain)
	// fmt.Println("length:", len(hosts), utils.ListDistinctNonZero(hosts))

	var domains []string
	distinct := utils.ListDistinctNonZero(hosts)
	for _, v := range distinct {
		after := utils.DomainFromUrl(v)
		fmt.Printf("origin: %s, after: %s \n", v, after)
		if !strings.HasSuffix(after, domain) {
			continue
		}
		domains = append(domains, after)
		// fmt.Printf("OK: %s %s")
	}
	// fmt.Println(domains)
	fmt.Printf("valid domain total: %d\n", len(utils.ListDistinctNonZero(domains)))

	ch := make(chan string)
	go func() {
		domainListMatch(domain, utils.ListToSet(domains), 3, ch)
		close(ch)
	}()

	for v := range ch {
		fmt.Println(v)
	}
}

func Test_BurstDomain(t *testing.T) {
	cfg.LoadCommon()

	ch := make(chan string)
	go func() {
		domains := []string{"gzsi.gov.cn", "kjj.gz.gov.cn", "sop.gzsi.gov.cn", "gzsti.gzsi.gov.cn"}
		BurstDomains(context.TODO(), domains, 3, ch)
		close(ch)
	}()

	for v := range ch {
		fmt.Println(v)
	}
}
