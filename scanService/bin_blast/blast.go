package bin_blast

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	flog "micro-service/pkg/log"
	"micro-service/pkg/utils"
)

const (
	subDomainBinName = "ksubdomain"
	subDomainFileDir = "./subdomain_files/"
	dictFilePath     = "/data/subdomain_files/subdomain_dic.txt"
)

type SubDomain struct {
	DomainList  []string
	Verify      bool // 是否验证模式，否则爆破模式
	OutsideDict bool // 是否使用外置字典, 可进行深度爆破
	Params      []string
	fileName    string
}

func BlastStart(option *SubDomain, ch chan string) error {
	flog.Infof("[Scan] Domain burst by ksubdomain binary starting")
	defer func() {
		if err := recover(); err != nil {
			flog.Panicf("[Panic] subdomain blast: %+v", err)
		}
	}()

	err := execDomain(option, ch)
	return err
}

func genVerifyFile(domainList []string) (string, error) {
	origin := strings.Join(domainList, ",") + utils.TimeFormat(time.Now(), time.RFC3339Nano) + utils.RandString(25)
	filePath := subDomainFileDir + utils.Md5sHash(origin, false) + ".txt"

	err := utils.Mkdir(subDomainFileDir)
	if err != nil {
		return "", err
	}

	file, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	for _, v := range domainList {
		if _, err = file.WriteString(v + "\n"); err != nil {
			flog.Error(err)
			continue
		}
	}

	return filePath, nil
}

func parseParam(opt *SubDomain) error {
	params := []string{"-silent"} // 静默模式
	opt.DomainList = utils.ListDistinct(opt.DomainList)

	if opt.Verify {
		fileName, err := genVerifyFile(opt.DomainList)
		if err != nil {
			return err
		}
		opt.fileName = fileName
		params = append(params, "-verify", "-f", fileName)
	} else {
		params = append(params, "-d")
		params = append(params, opt.DomainList...)
		if opt.OutsideDict {
			params = append(params, "-f", dictFilePath)
		}
	}

	opt.Params = append(opt.Params, params...)

	return nil
}

func execDomain(opt *SubDomain, ch chan string) error {
	l, _ := utils.ListFunc(opt.DomainList, func(s string) (string, bool) { return s, s != "" })
	ld := utils.ListDistinct(l)
	if len(ld) == 0 {
		return nil
	}
	opt.DomainList = ld

	err := parseParam(opt)
	if err != nil {
		return err
	}

	cmd := exec.Command(subDomainBinName, opt.Params...)
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("call %s: %+v", subDomainBinName, err)
	}
	defer stdout.Close()

	scanout := bufio.NewScanner(stdout)
	if startErr := cmd.Start(); startErr != nil {
		return startErr
	}
	for scanout.Scan() {
		x := scanout.Text()
		if x == "" {
			continue
		}
		flog.Infof("[Scan] Domain burst, targetDomains: %v, got: %s by ksubdomain", opt.DomainList, x)
		ch <- x
	}

	if opt.fileName != "" {
		_ = os.Remove(opt.fileName)
	}

	return nil
}

// 杀死超时进程
//	func killTimeoutProcess(cmd *exec.Cmd) error {
//		cmd.SysProcAttr = &syscall.SysProcAttr{Setpgid: true}
//		err := syscall.Kill(-cmd.Process.Pid, syscall.SIGKILL)
//
//		return err
//	}
