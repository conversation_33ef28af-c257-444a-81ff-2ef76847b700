package bin_blast

import (
	"fmt"
	"micro-service/pkg/log"
)

func ExampleBlastStart() {
	ch := make(chan string, 5)
	go func() {
		for v := range ch {
			fmt.Println(v)
		}
	}()

	opt := &SubDomain{
		DomainList: []string{"happyday.com", "baidu.com"},
		Verify:     false,
		Params:     []string{"-b", "1M"}, // []string{"-b", "1M", "-l", "1"}
	}

	err := BlastStart(opt, ch)
	if err != nil {
		log.Error(err)
	}
}
