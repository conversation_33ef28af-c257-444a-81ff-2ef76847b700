syntax = "proto3";

package scan;

option go_package = "./proto;scan";

service Scan {
  // -------------------- 域名扫描任务
  // 域名扫描任务
  rpc DomainBurst(ParamsRequest) returns (SubdomainResponse) {}
  // 删除go域名爆破任务
  rpc DomainBurstTaskDelete(SubdomainResponse) returns (Empty) {}

  // -------------------- 登录入库
  // rpc LoginPageScreenshot(ParamsRequest) returns (SubdomainResponse) {}
  rpc LoginPageScreenshotByTopDomain(LoginPageScreenshotByTopDomainRequest) returns (Empty) {}

  // 空间搜索
  rpc FofaParseQuery(FofaParseQueryRequest) returns(FofaParseQueryResponse) {}
  // 聚合查询
  rpc FofaParseStatistics(FofaParseQueryRequest) returns(FofaParseAggResponse) {}

  // 域名搜索
  rpc DomainSearchTaskInfo(DomainSearchTaskInfoRequest) returns(DomainSearchTaskInfoResponse) {} // 创建任务
  rpc DomainSearchTaskCreate(DomainSearchTaskCreateRequest) returns(DomainSearchTaskInfoRequest) {} // 任务详情
  rpc DomainSearchResultList(DomainSearchResultListRequest) returns(DomainSearchResultListResponse) {} // 结果列表
  // -------------------- FOFA扫描任务
  // 创建扫描任务
  rpc CreateFofaScanTask(FofaScanTaskRequest) returns (FofaScanTaskResponse) {}
  //停止扫描任务
  rpc StopFofaScanTask(FofaScanTaskRequest) returns (FofaScanTaskResponse) {}
  // --------------------Engine rules 规则引擎
  rpc EngineRuleAssetMatch(EngineRuleAssetMatchRequest) returns (EngineRuleAssetMatchResponse) {} // 资产匹配
  // -------------------- 资产任务
  // 添加资产扫描任务
  //  rpc AddAssetScanTask(Empty) returns (Empty) {}
  // 删除资产扫描任务
  //  rpc DelAssetScanTask(Empty) returns (Empty) {}
  // 资产扫描任务列表
  //  rpc AssetScanTaskList(Empty) returns (Empty) {}

  // -------------------- POC任务
  // 添加Poc扫描任务
  //  rpc AddPocScanTask(Empty) returns (Empty) {}
  // 删除Poc扫描任务
  //  rpc DelPocScanTask(Empty) returns (Empty) {}
  // Poc扫描任务列表
  //  rpc PocScanTaskList(Empty) returns (Empty) {}

  // -------------------- 禁扫时间管理
  // 禁扫时间设置
  //  rpc BanTimeSetting(Empty) returns (Empty) {}
  // 禁扫时间信息
  //  rpc BanTimeInfo(Empty) returns (Empty) {}

  // -------------------- 禁扫IP管理
  // 禁扫IP设置
  //  rpc BanIpSetting(Empty) returns (Empty) {}
  // 禁扫IP信息
  //  rpc BanIpInfo(Empty) returns (Empty) {}
}
message Empty{}
// FOFA扫描任务
message FofaScanTarget {
  repeated string ip = 1; // @tag validate:"required" zh:"扫描IP"
  string ports = 2; // @tag validate:"required" zh:"扫描端口"
}
message FofaScanTaskRequest{
  repeated FofaScanTarget targets = 1; // @tag form:"targets" validate:"required,dive" zh:"扫描目标"
  string process_callback = 2; // @tag form:"process_callback" validate:"required" zh:"进度回调地址"
  string finish_callback = 3; // @tag form:"finish_callback" validate:"required" zh:"完成回调地址"
  uint64 task_id = 4; // @tag form:"task_id" validate:"required,number" zh:"任务ID"
  uint64 user_id = 5; // @tag form:"user_id" validate:"required,number" zh:"用户ID"
}
message FofaTaskIdData {
  string id = 1;
}
message FofaScanTaskResponse {
  int32 code = 1;
  string message = 2;
  FofaTaskIdData data  = 3;
}
message EngineRuleAssetMatchRequest {
  string ip_assets = 1;
//  string index = 1;// @tag validate:"required" zh:"es索引"
//  string id = 2;  // @tag validate:"required" zh:"es下标"

//  repeated string title = 1;
//  repeated string component = 2;
//  string body = 3;
//  string banner = 4;
//  string header = 5;
//  string service = 6;
//  int64 type = 7; // @tag validate:"required,number" zh:"场景类型"
}

//message EngineRuleAssetMatchResponse {
//    map<int64, ThreatsRuleMatchs> data = 1;
//}
//message ThreatsRuleMatchs {
//  repeated ThreatsRuleMatch ThreatsRuleMatch =1;
//}
//message ThreatsRuleMatch{
//  string name = 1;
//  string event_impact = 2;
//  string event_solution = 3;
//  string event_desc = 4;
//  uint64 type = 5;
//  uint64 tag = 6;
//}

message EngineRuleAssetMatchResponse {
  repeated uint64 ip_risk_type = 1;
   map<uint64,PortList> port_risk_types  = 2;
}
message PortList{
  repeated uint64 risk_types = 2;
}

message FofaTaskStatusRequest {
  string id = 1; // @tag form:"id" validate:"required" zh:"任务ID"
}

message FofaTaskResultResponse {
  bytes result = 1;
}
message FofaTaskStatus {
  int64 progress = 1; // 任务进度
  int64 target_num = 2; // 目标数量
  bool result_file = 3; // 结果文件是否也生成
  string state = 4; // 结果状态说明
}
message FofaTaskStatusResponse {
  int32 code = 1;
  string message = 2;
  FofaTaskStatus data = 3;
}

// 登录页面请求
message LoginPageScreenshotByTopDomainRequest {
  repeated string domains = 1;
  uint64 user_id = 2; // @tag form:"user_id" validate:"required,number" zh:"用户ID"
  uint64 company_id = 3; // @tag form:"company_id" validate:"omitempty,number" zh:"企业ID"
  int32 quality = 4; // @tag form:"quality" validate:"omitempty,number" zh:"清晰度"
  bool only_domain_search = 5;
}

message DelAssetScanTaskRequest {
  uint64 task_id = 1; // @tag form:"task_id" validate:"required,number" zh:"任务ID"
}

message ParamsRequest {
  repeated string domain_list = 1;
  uint32 level = 2;
  uint32 modify = 3; // @tag validate:"required,oneof=0 1" zh:"模式"
  uint32 bandwidth = 4;
  string callback_url = 5; // @tag validate:"required" zh:"回调地址"
  bool skip_callback = 6;
}

message SubdomainResponse{
  uint64 golang_task_id = 1; // @tag validate:"required" zh:"任务ID"
}

message FofaParseQueryRequest {
  string search = 1; // @tag form:"search"
  repeated uint64 user_id = 2;
  uint32 page = 3; // @tag form:"page" validate:"required" zh:"页码"
  uint64 per_page = 4; // @tag form:"per_page" validate:"required,lte=50" zh:"页大小"
}

message FofaParseQueryResponse {
  message asn {
    int64 as_number = 1;
    string as_organization = 2;
  }
  message geo {
    string city_name = 1;
    string continent_code = 2;
    string country_code2 = 3;
    string country_code3 = 4;
    string country_name = 5;
    int64 dma_code = 6;
    double latitude = 7;
    double longitude = 8;
    string postal_code = 9;
    string real_region_name = 10;
    string region_name = 11;
    string timezone = 12;
  }
  message Certs {
    string cert_date = 1;
    bool is_valid = 2;
    string issuer_cn = 3;
    repeated string issuer_cns = 4;
    string not_after = 5;
    string not_before = 6;
    string sig_alth = 7;
    string sn = 8;
    string subject_cn = 9;
    string valid_type = 10;
  }
  message item {
    string ip = 1;
    int64 port = 2;
    string domain = 3;
    string fid = 4;
    string header = 5;
    string banner = 6;
    string title = 7;
    string body = 8;
    string cert = 9;
    string server = 10;
    string protocol = 11;
    string host = 12;
    string jump_link = 13;
    asn asn = 14;
    repeated string product = 15;
    string subdomain = 16;
    bool is_honeypot = 17;
    geo geoip = 18;
    string last_update_time = 19;
    int64 user_id = 20;
    Certs certs = 21;
  }
  repeated item items = 1;
  int64 total = 2;
  uint32 page = 3;
  uint64 per_page = 4;
}

message FofaParseAggResponse {
  message Item {
    message DocItem {
      string key = 1;
      string title = 2;
      int64 doc_count = 3;
    }
    string key = 1;
    string title = 2;
    string lastchecktime = 3;
    int64 doc_count = 4;
    repeated DocItem children = 5;
  }
  repeated Item items = 1;
}

message DomainSearchTaskInfoRequest {
  uint64 task_id = 1;
  uint64 user_id = 2;
}

message DomainSearchTaskInfoResponse {
  uint64 id = 1;
  int32 status = 2;
  string progress = 3;
  string created_at = 4;
}

message DomainSearchTaskCreateRequest {
  message param {
    repeated string master = 4;
    repeated string second = 5;
    repeated string three = 6;
  }
  uint64 user_id = 1;
  uint64 safe_user_id = 2;
  uint64 company_id = 3;
  param domain = 4;
}

message DomainSearchResultListRequest {
  uint64 task_id = 1; // @tag form:"task_id" validate:"required" zh:"任务"
  uint64 user_id = 2;
  int32 page = 3; // @tag form:"page" validate:"required" zh:"页码"
  int32 per_page = 4; // @tag form:"per_page" validate:"required" zh:"页大小"
}

message DomainSearchResultListResponse {
  message unit {
    uint64 id = 1;
    string url = 2;
    string domain = 3;
    string title = 4;
    string source = 5;
    string created_at = 6;
    string search_syntax = 7;
  }
  repeated unit items = 1;
  int64 total = 2;
  int32 page = 3;
  int32 per_page = 4;
}


