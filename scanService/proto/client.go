package scan

import (
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"sync"
	"time"

	ocplugin "github.com/go-micro/plugins/v4/wrapper/trace/opentracing"
	"github.com/opentracing/opentracing-go"
	"go-micro.dev/v4/client"
)

var once sync.Once

var singleInstance client.Client

func getInstance() client.Client {
	if singleInstance == nil {
		once.Do(func() {
			srv := micro.NewService(
				micro.RegisterTTL(5*time.Second),      // TTL指定从上一次心跳间隔起，超过这个时间服务会被服务发现移除
				micro.RegisterInterval(4*time.Second), // 让服务在指定时间内重新注册，保持TTL获取的注册时间有效
				micro.Logger(logger.DefaultLogger),
				micro.WrapHandler(ocplugin.NewHandlerWrapper(opentracing.GlobalTracer())),
				micro.WrapClient(ocplugin.NewClientWrapper(opentracing.GlobalTracer())),
			)
			singleInstance = srv.Client()
			singleInstance.Init(
				client.Wrap(ocplugin.NewClientWrapper(opentracing.GlobalTracer())),
				client.Retries(0),
				client.PoolSize(20),
				client.DialTimeout(15*time.Second),
				client.RequestTimeout(30*time.Second),
			)
		})

	}
	return singleInstance
}

func GetMicroClient() client.Client {
	return getInstance()
}

func GetProtoClient() ScanService {
	return NewScanService(
		ServiceName,
		getInstance(),
	)
}
