package brokers

import (
	"context"
	"micro-service/pkg/rule_engine/engine"

	"micro-service/pkg/cfg"
	pb "micro-service/scanService/proto"

	"github.com/go-micro/plugins/v4/broker/rabbitmq"
	"go-micro.dev/v4"
	"go-micro.dev/v4/broker"
	"go-micro.dev/v4/server"
)

func NewRuleEngineBroker() broker.Broker {
	mqConfig := cfg.LoadRabbitMq()
	// 创建 RabbitMQ Broker
	return rabbitmq.NewBroker(
		broker.Addrs(cfg.GetRabbitMqAddr(mqConfig)),           // RabbitMQ访问地址，含VHost
		rabbitmq.ExchangeName(pb.MqExchangeScan),              // 交换机的名称
		rabbitmq.ExchangeType(rabbitmq.ExchangeTypeTopic),
		rabbitmq.DurableExchange(),                            // 消息在Exchange中时会进行持久化处理
		rabbitmq.PrefetchCount(mqConfig.PrefetchCount),        // 使用配置的预取数量，提高并发性能
	)
}

func RegisterRuleEngineSubscriber(regSrv server.Server) error {
	subOpts := broker.NewSubscribeOptions(
		rabbitmq.DurableQueue(),   // 队列持久化，消费者断开连接后，消息仍然保存到队列中
		rabbitmq.RequeueOnError(), // 消息处理函数返回error时，消息再次入队列
		broker.DisableAutoAck(),   // 关闭自动ACK
		rabbitmq.AckOnSuccess(),   // 消息处理函数没有error返回时，go-micro发送Ack给RabbitMQ
	)
	// 注册Subscriber Rule Engine
	err := micro.RegisterSubscriber(
		pb.TopicRuleEngine,
		regSrv,
		ruleEngineHandler,
		server.SubscriberContext(subOpts.Context),
		server.SubscriberQueue(pb.QueueRuleEngine),
	)
	return err
}

func ruleEngineHandler(_ context.Context, arg any) error {
	msg, ok := arg.(*engine.RuleEngineMsg)
	if !ok {
		return nil
	}

	// delete rule from engine
	if !msg.IsAdd {
		return engine.DeleteByRules(msg.RulesName...)
	}
	// clear rule from engine
	if msg.IsClear {
		engine.ClearRule()
		return nil
	}
	// update rule to engine
	return engine.UpdateRule(msg.RuleContent)
}
