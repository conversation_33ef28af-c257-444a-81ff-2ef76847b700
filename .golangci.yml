# Options for analysis running.
run:
  # Number of operating system threads (`GOMAXPROCS`) that can execute golangci-lint simultaneously.
  # If it is explicitly set to 0 (i.e. not the default) then golangci-lint will automatically set the value to match Linux container CPU quota.
  # Default: the number of logical CPUs in the machine
  concurrency: 4
  # Timeout for analysis, e.g. 30s, 5m.
  # Default: 1m
  timeout: 1m
  # Exit code when at least one issue was found.
  # Default: 1
  issues-exit-code: 2
  # Include test files or not.
  # Default: true
  tests: false
  # List of build tags, all linters use it.
  # Default: []
  build-tags:
    - mytag
  # If set, we pass it to "go list -mod={option}". From "go help modules":
  # If invoked with -mod=readonly, the go command is disallowed from the implicit
  # automatic updating of go.mod described above. Instead, it fails when any changes
  # to go.mod are needed. This setting is most useful to check that go.mod does
  # not need updates, such as in a continuous integration and testing system.
  # If invoked with -mod=vendor, the go command assumes that the vendor
  # directory holds the correct copies of dependencies and ignores
  # the dependency descriptions in go.mod.
  #
  # Allowed values: readonly|vendor|mod
  # Default: ""
  modules-download-mode: "readonly"
  # Allow multiple parallel golangci-lint instances running.
  # If false, golangci-lint acquires file lock on start.
  # Default: false
  allow-parallel-runners: false
  # Allow multiple golangci-lint instances running, but serialize them around a lock.
  # If false, golangci-lint exits with an error if it fails to acquire file lock on start.
  # Default: false
  allow-serial-runners: false
  # Define the Go version limit.
  # Mainly related to generics support since go1.18.
  # Default: use Go version from the go.mod file, fallback on the env var `GOVERSION`, fallback on 1.17
  go: '1.19'
# output configuration options
output:
  # The formats used to render issues.
  # Format: `colored-line-number`, `line-number`, `json`, `colored-tab`, `tab`, `checkstyle`, `code-climate`, `junit-xml`, `github-actions`, `teamcity`
  # Output path can be either `stdout`, `stderr` or path to the file to write to.
  #
  # For the CLI flag (`--out-format`), multiple formats can be specified by separating them by comma.
  # The output can be specified for each of them by separating format name and path by colon symbol.
  # Example: "--out-format=checkstyle:report.xml,json:stdout,colored-line-number"
  # The CLI flag (`--out-format`) override the configuration file.
  #
  # Default:
  #   formats:
  #     - format: colored-line-number
  #       path: stdout
  formats:
    - format: colored-line-number
      path: stdout
  # Print lines of code with issue.
  # Default: true
  print-issued-lines: true
  # Print linter name in the end of issue text.
  # Default: true
  print-linter-name: true
  # Make issues output unique by line.
  # Default: true
  uniq-by-line: true
  # Add a prefix to the output file references.
  # Default: ""
  path-prefix: ""
  # Sort results by the order defined in `sort-order`.
  # Default: false
  sort-results: true
  # Order to use when sorting results.
  # Require `sort-results` to `true`.
  # Possible values: `file`, `linter`, and `severity`.
  #
  # If the severity values are inside the following list, they are ordered in this order:
  #   1. error
  #   2. warning
  #   3. high
  #   4. medium
  #   5. low
  # Either they are sorted alphabetically.
  #
  # Default: ["file"]
  sort-order:
    - linter
    - severity
    - file # filepath, line, and column.
  # Show statistics per linter.
  # Default: false
  show-stats: false
# All available settings of specific linters.
linters-settings:

linters:
  # Disable all linters.
  # Default: false
  disable-all: false
  # Enable all available linters.
  # Default: false
  enable-all: false
  # Enable specific linter
  # https://golangci-lint.run/usage/linters/#enabled-by-default
  enable:
    - errcheck
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - unused
  # Enable presets.
  # https://golangci-lint.run/usage/linters
  presets:
    - bugs
    - comment
    - complexity
    - test
    - unused
issues:
  exclude-rules:
    - path: '(.+)pb\.go'
      linters:
        - all
    - path: '(.+)pb\.micro\.go'
      linters:
        - all
severity:
  # See the dedicated "severity" documentation section.
  option: value