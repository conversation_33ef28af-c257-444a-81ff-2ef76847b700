package api

import (
	"encoding/json"
	"fmt"
	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// Recommend 云端推荐
// 对应PHP中的recommend方法
func Recommend(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 获取用户信息失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend", err)
	}

	// 验证请求参数
	var param struct {
		Data             []map[string]interface{} `json:"data" binding:"required"`
		GroupID          string                   `json:"group_id" binding:"required"`
		IsNeedHunter     *int32                   `json:"is_need_hunter"`
		IsNeedDnsChecker *int32                   `json:"is_need_dnschecker"`
		IsAutoExpendIP   *int32                   `json:"is_auto_expend_ip"`
		FofaRange        *int32                   `json:"fofa_range"`
		OperateCompanyID *int64                   `json:"operate_company_id"`
		Keyword          map[string]string        `json:"keyword"`
	}

	if err = ctx.ShouldBindJSON(&param); err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 参数绑定失败: %v", err)
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "参数错误")
	}
	gid, _ := strconv.ParseUint(param.GroupID, 10, 64)
	if gid == 0 {
		log.WithContextErrorf(ctx, "[云端推荐] 分组ID不能为空")
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "分组ID不能为空")
	}

	// 设置默认值
	isNeedHunter := int32(0)
	if param.IsNeedHunter != nil {
		isNeedHunter = *param.IsNeedHunter
	}

	isNeedDnsChecker := int32(0)
	if param.IsNeedDnsChecker != nil {
		isNeedDnsChecker = *param.IsNeedDnsChecker
	}

	isAutoExpendIP := int32(0)
	if param.IsAutoExpendIP != nil {
		isAutoExpendIP = *param.IsAutoExpendIP
	}

	fofaRange := int32(0)
	if param.FofaRange != nil {
		fofaRange = *param.FofaRange
	}

	// 构建RPC请求
	req := &pb.CloudRecommendRequest{
		UserId:           userID,
		CompanyId:        uint64(companyID),
		Data:             convertDataToProto(param.Data),
		GroupId:          gid,
		IsNeedHunter:     isNeedHunter,
		IsNeedDnschecker: isNeedDnsChecker,
		IsAutoExpendIp:   isAutoExpendIP,
		FofaRange:        fofaRange,
		Keyword:          param.Keyword,
		ClientIp:         utils.GetClientIP(ctx.Request),
		OperateCompanyId: param.OperateCompanyID,
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().CloudRecommend(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 云端推荐失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend", err)
	}

	// 返回结果
	responseData := map[string]interface{}{
		"flag":      rsp.Flag,
		"expend_id": rsp.ExpendId,
	}
	return response.Gen(ctx).SendSuccess("cloud_recommend", response.MsgSuccess, responseData)
}

// convertDataToProto 将前端数据转换为protobuf格式
func convertDataToProto(data []map[string]interface{}) []*pb.CloudRecommendDataItem {
	result := make([]*pb.CloudRecommendDataItem, 0, len(data))
	for _, item := range data {
		protoItem := &pb.CloudRecommendDataItem{}

		if id, ok := item["id"]; ok {
			if idSlice, ok := id.([]interface{}); ok {
				protoItem.Id = make([]uint64, 0, len(idSlice))
				for _, v := range idSlice {
					if idVal, ok := v.(float64); ok {
						protoItem.Id = append(protoItem.Id, uint64(idVal))
					}
				}
			}
		}

		if isAll, ok := item["is_all"]; ok {
			if isAllVal, ok := isAll.(float64); ok {
				protoItem.IsAll = int32(isAllVal)
			}
		}

		if itemType, ok := item["type"]; ok {
			if typeVal, ok := itemType.(float64); ok {
				protoItem.Type = int32(typeVal)
			}
		}

		result = append(result, protoItem)
	}
	return result
}

// CloudRecommendResults 云端推荐结果
// 对应路由: v1.GET("cloud/recommend/:flag", wrapper.Auth(api.CloudRecommendResults, scopeAll))
func CloudRecommendResults(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果] 获取用户信息失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend", err)
	}
	log.WithContextInfof(ctx, "[云端推荐结果] 获取用户信息成功: userID=%d", userID)
	// 构建请求参数
	req := &pb.CloudRecommendResultsRequest{
		UserId:  int64(userID),
		Flag:    ctx.Param("flag"),
		Page:    cast.ToInt32(ctx.DefaultQuery("page", "1")),
		PerPage: cast.ToInt32(ctx.DefaultQuery("per_page", "10")),
		Keyword: ctx.Query("keyword"),
		Ip:      ctx.Query("ip"),
		Confirm: cast.ToInt32(ctx.DefaultQuery("confirm", "-1")),
	}

	// 处理多值参数
	if domains := ctx.QueryArray("domain"); len(domains) > 0 {
		req.Domain = domains
	}
	if certs := ctx.QueryArray("cert"); len(certs) > 0 {
		req.Cert = certs
	}
	if icps := ctx.QueryArray("icp"); len(icps) > 0 {
		req.Icp = icps
	}
	if logos := ctx.QueryArray("logo"); len(logos) > 0 {
		req.Logo = logos
	}

	// 处理可能带有下标的参数格式 (如 port[0]=80&port[1]=443)
	// 处理port参数
	portParams := parseIndexedParams(ctx, "port")
	if len(portParams) > 0 {
		req.Port = portParams
	} else if ports := ctx.QueryArray("port"); len(ports) > 0 {
		// 如果没有带下标的参数，尝试用QueryArray方法获取
		req.Port = ports
	}

	// 处理protocol参数
	protocolParams := parseIndexedParams(ctx, "protocol")
	if len(protocolParams) > 0 {
		req.Protocol = protocolParams
	} else if protocols := ctx.QueryArray("protocol"); len(protocols) > 0 {
		// 如果没有带下标的参数，尝试用QueryArray方法获取
		req.Protocol = protocols
	}

	// 处理subdomain参数
	subdomainParams := parseIndexedParams(ctx, "subdomain")
	if len(subdomainParams) > 0 {
		req.Subdomain = subdomainParams
	} else if subdomains := ctx.QueryArray("subdomain"); len(subdomains) > 0 {
		// 如果没有带下标的参数，尝试用QueryArray方法获取
		req.Subdomain = subdomains
	}

	// 处理时间范围
	if createdStart := ctx.Query("created_start"); createdStart != "" {
		if createdEnd := ctx.Query("created_end"); createdEnd != "" {
			req.CreatedAt = []string{createdStart, createdEnd}
		}
	}
	if updatedStart := ctx.Query("updated_start"); updatedStart != "" {
		if updatedEnd := ctx.Query("updated_end"); updatedEnd != "" {
			req.UpdatedAt = []string{updatedStart, updatedEnd}
		}
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().CloudRecommendResults(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果] 获取云端推荐结果失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend", err)
	}

	// 特殊处理chain_list字段，将一维数组转换为二维数组格式
	responseData := map[string]interface{}{
		"total":    rsp.Total,
		"page":     rsp.Page,
		"per_page": rsp.PerPage,
		"items":    processChainListForItems(rsp.Items),
	}

	return response.Gen(ctx).SendSuccess("cloud_recommend", response.MsgSuccess, responseData)
}

// CloudRecommendResultsClues 云端推荐结果线索展示
// 对应路由: v1.GET("cloud/recommend_clues/:flag", wrapper.Auth(api.CloudRecommendResultsClues, scopeAll))
func CloudRecommendResultsClues(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果-线索展示] 获取用户信息失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend_clues", err)
	}

	// 构建请求参数
	req := &pb.CloudRecommendResultsRequest{
		UserId:           int64(userID),
		Flag:             ctx.Param("flag"),
		OperateCompanyId: int64(uint64(companyID)),
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().CloudRecommendResultsClues(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果-线索展示] 获取云端推荐结果线索展示失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend_clues", err)
	}

	// 特殊处理logo字段，将JSON字符串数组转换为对象数组
	responseData := map[string]interface{}{
		"domain":                    rsp.Domain,
		"cert":                      rsp.Cert,
		"icp":                       rsp.Icp,
		"subdomain":                 rsp.Subdomain,
		"port":                      rsp.Port,
		"protocol":                  rsp.Protocol,
		"title":                     rsp.Title,
		"clue_company_name":         rsp.ClueCompanyName,
		"cloud_name":                rsp.CloudName,
		"organization_company_name": rsp.OrganizationCompanyName,
		"url":                       rsp.Url,
	}

	// 处理logo字段 - 将JSON字符串数组转换为对象数组
	logoObjects := make([]interface{}, 0, len(rsp.Logo))
	for _, logoStr := range rsp.Logo {
		var logoObj map[string]interface{}
		if err := json.Unmarshal([]byte(logoStr), &logoObj); err == nil {
			logoObjects = append(logoObjects, logoObj)
		}
	}
	responseData["logo"] = logoObjects

	return response.Gen(ctx).SendSuccess("cloud_recommend_clues", response.MsgSuccess, responseData)
}

// processChainListForItems 处理items中的chain_list字段，将一维数组转换为二维数组格式
func processChainListForItems(items []*pb.CloudRecommendResultItem) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, len(items))

	for _, item := range items {
		// 将protobuf结构转换为map
		itemMap := map[string]interface{}{
			"id":                      item.Id,
			"ip":                      item.Ip,
			"port":                    item.Port,
			"protocol":                item.Protocol,
			"title":                   item.Title,
			"domain":                  item.Domain,
			"subdomain":               item.Subdomain,
			"cert":                    item.Cert,
			"icp":                     item.Icp,
			"status":                  item.Status,
			"audit":                   item.Audit,
			"online_state":            item.OnlineState,
			"source_updated_at":       item.SourceUpdatedAt,
			"created_at":              item.CreatedAt,
			"updated_at":              item.UpdatedAt,
			"reasons":                 item.Reasons,
			"group_id":                item.GroupId,
			"group_name":              item.GroupName,
			"is_ipv6":                 item.IsIpv6,
			"url":                     item.Url,
			"base_protocol":           item.BaseProtocol,
			"banner":                  item.Banner,
			"certs_valid":             item.CertsValid,
			"cloud_name":              item.CloudName,
			"is_cdn":                  item.IsCdn,
			"cert_raw":                item.CertRaw,
			"product":                 item.Product,
			"assets_source":           item.AssetsSource,
			"all_assets_source":       item.AllAssetsSource,
			"oneforall_source":        item.OneforallSource,
			"cname":                   item.Cname,
			"all_company_name":        item.AllCompanyName,
			"clue_company_name":       item.ClueCompanyName,
			"flag":                    item.Flag,
			"assets_confidence_level": item.AssetsConfidenceLevel,
		}

		// 只有当ip_status不为特殊值100时才添加该字段（100表示没有匹配到资产数据）
		if item.IpStatus != 100 {
			itemMap["ip_status"] = item.IpStatus
		}

		// 处理logo字段
		if item.Logo != nil {
			itemMap["logo"] = map[string]interface{}{
				"hash":    item.Logo.Hash,
				"content": item.Logo.Content,
			}
		}

		// 处理chain_list字段 - 转换为二维数组格式
		if len(item.ChainList) > 0 {
			// 按照PHP格式，每个reason对应一个独立的链数组
			// 这里我们需要根据实际的链结构来分组
			chainLists := make([][]map[string]interface{}, 0)

			// 简化处理：将所有链作为一个组（实际应该根据reason分组）
			if len(item.ChainList) > 0 {
				chainList := make([]map[string]interface{}, 0, len(item.ChainList))
				for _, chain := range item.ChainList {
					chainMap := map[string]interface{}{
						"content": chain.Content,
					}
					// 添加其他字段（如果存在）
					if chain.Id > 0 {
						chainMap["id"] = chain.Id
						chainMap["user_id"] = chain.UserId
						chainMap["company_id"] = chain.CompanyId
						chainMap["parent_id"] = chain.ParentId
						chainMap["group_id"] = chain.GroupId
						chainMap["comment"] = chain.Comment
						chainMap["clue_company_name"] = chain.ClueCompanyName
						chainMap["hash"] = chain.Hash
						chainMap["source"] = chain.Source
						chainMap["count"] = chain.Count
						chainMap["type"] = chain.Type
						chainMap["status"] = chain.Status
						chainMap["created_at"] = chain.CreatedAt
						chainMap["updated_at"] = chain.UpdatedAt
						chainMap["safe_user_id"] = chain.SafeUserId
						chainMap["is_expand"] = chain.IsExpand
						chainMap["from_ip"] = chain.FromIp
						chainMap["is_deleted"] = chain.IsDeleted
						chainMap["punycode_domain"] = chain.PunycodeDomain
						chainMap["is_from_check_table"] = chain.IsFromCheckTable
						chainMap["is_supply_chain"] = chain.IsSupplyChain
						chainMap["is_fake_icp"] = chain.IsFakeIcp
						chainMap["source_url"] = chain.SourceUrl
					}
					chainList = append(chainList, chainMap)
				}
				chainLists = append(chainLists, chainList)
			}
			itemMap["chain_list"] = chainLists
		} else {
			itemMap["chain_list"] = [][]map[string]interface{}{}
		}

		result = append(result, itemMap)
	}

	return result
}

// parseIndexedParams 解析带有下标的URL参数和表单参数
// 例如: protocol[0]=http&protocol[1]=tls 或 protocol[]=http&protocol[]=tls 将返回 []string{"http", "tls"}
func parseIndexedParams(ctx *gin.Context, paramName string) []string {
	results := make([]string, 0)

	// 处理URL查询参数
	queryParams := ctx.Request.URL.Query()
	for key, values := range queryParams {
		// 检查参数是否匹配模式 paramName[n] 或 paramName[]
		if strings.HasPrefix(key, paramName+"[") && strings.HasSuffix(key, "]") {
			// 将所有参数值添加到结果中（支持同一个key有多个值的情况）
			for _, value := range values {
				if value != "" {
					results = append(results, value)
				}
			}
		}
	}

	// 处理POST表单参数
	if ctx.Request.Method == "POST" {
		formParams := ctx.Request.PostForm
		for key, values := range formParams {
			// 检查参数是否匹配模式 paramName[n] 或 paramName[]
			if strings.HasPrefix(key, paramName+"[") && strings.HasSuffix(key, "]") {
				// 将所有参数值添加到结果中（支持同一个key有多个值的情况）
				for _, value := range values {
					if value != "" {
						results = append(results, value)
					}
				}
			}
		}
	}

	log.Infof("[parseIndexedParams] %s解析结果: %v", paramName, results)
	return results
}

// CloudRecommendResultsExport 云端推荐结果导出
// 对应路由: v1.POST("cloud/recommend/:flag/export", wrapper.Auth(api.CloudRecommendResultsExport, scopeAll))
func CloudRecommendResultsExport(ctx *gin.Context) error {

	// 手动获取operate_company_id参数，因为POST请求通过URL查询参数传递
	operateCompanyIdStr := ctx.Query("operate_company_id")
	var operateCompanyId int64
	if operateCompanyIdStr != "" {
		operateCompanyId = cast.ToInt64(operateCompanyIdStr)
	}
	log.Infof("[云端推荐结果-导出] 获取到operate_company_id参数: %s -> %d", operateCompanyIdStr, operateCompanyId)

	// 获取用户ID和企业ID，传入operate_company_id
	_, userID, _, err := safe_company.GetSafeCompanyUser(ctx, operateCompanyId)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果-线索展示] 获取用户信息失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend_clues", err)
	}
	flag := ctx.Param("flag")
	if flag == "" {
		return errorResponse(ctx, http.StatusBadRequest, "flag参数不能为空")
	}
	log.Infof("[云端推荐结果-导出] 开始处理导出请求: flag=%s, userId=%d", flag, userID)
	// 首先尝试解析POST表单数据
	if err := ctx.Request.ParseForm(); err != nil {
		log.Errorf("[云端推荐结果-导出] 解析表单数据失败: %v", err)
	}

	req := &pb.CloudRecommendExportRequest{
		UserId: userID,
		Flag:   flag,
	}

	// 处理ID参数，支持id[]数组形式，可能来自URL查询参数或POST表单
	idParams := parseFormOrQueryArray(ctx, "id")
	if len(idParams) > 0 {
		req.Id = idParams
	}

	// 处理整体导出参数
	wholeStr := getFormOrQueryParam(ctx, "whole")
	if wholeStr != "" {
		req.Whole = cast.ToUint32(wholeStr)
	}

	// 添加搜索条件
	if req.Whole != 0 {
		req.Keyword = getFormOrQueryParam(ctx, "keyword")
		req.Ip = getFormOrQueryParam(ctx, "ip")

		// 处理数组参数
		req.Domain = parseFormOrQueryArray(ctx, "domain")
		req.Cert = parseFormOrQueryArray(ctx, "cert")
		req.Icp = parseFormOrQueryArray(ctx, "icp")
		req.Logo = parseFormOrQueryArray(ctx, "logo")
		req.Port = parseFormOrQueryArray(ctx, "port")
		req.Protocol = parseFormOrQueryArray(ctx, "protocol")
		req.Subdomain = parseFormOrQueryArray(ctx, "subdomain")
		req.IpArray = parseFormOrQueryArray(ctx, "ip_array")

		// 添加调试日志
		log.Infof("[云端推荐结果-导出] 解析到的参数: port=%v, domain=%v, protocol=%v", req.Port, req.Domain, req.Protocol)

		// 处理时间范围
		createdStart := getFormOrQueryParam(ctx, "created_start")
		createdEnd := getFormOrQueryParam(ctx, "created_end")
		if createdStart != "" && createdEnd != "" {
			req.CreatedAt = []string{createdStart, createdEnd}
		}

		updatedStart := getFormOrQueryParam(ctx, "updated_start")
		updatedEnd := getFormOrQueryParam(ctx, "updated_end")
		if updatedStart != "" && updatedEnd != "" {
			req.UpdatedAt = []string{updatedStart, updatedEnd}
		}
	}

	// 调用RPC
	rsp, err := pb.GetProtoClient().CloudRecommendResultsExport(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		log.Errorf("[云端推荐结果-导出] RPC调用失败: %v", err)
		return errorResponse(ctx, http.StatusInternalServerError, fmt.Sprintf("导出失败: %v", err))
	}

	// 返回结果
	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": gin.H{
			"url": rsp.Url,
		},
	})
	return nil
}

// getFormOrQueryParam 获取表单或查询参数的值，优先从表单获取
func getFormOrQueryParam(ctx *gin.Context, key string) string {
	// 先尝试从POST表单获取
	if val := ctx.PostForm(key); val != "" {
		return val
	}
	// 再尝试从URL查询参数获取
	return ctx.Query(key)
}

// parseFormOrQueryArray 解析表单或查询参数的数组值，优先从表单获取
func parseFormOrQueryArray(ctx *gin.Context, key string) []string {
	// 先尝试从POST表单获取
	if values := ctx.PostFormArray(key); len(values) > 0 {
		log.Infof("[参数解析] %s从POST表单获取到: %v", key, values)
		return values
	}

	// 再尝试从URL查询参数获取
	if values := ctx.QueryArray(key); len(values) > 0 {
		log.Infof("[参数解析] %s从URL查询参数获取到: %v", key, values)
		return values
	}

	// 最后尝试解析带索引的参数
	indexedValues := parseIndexedParams(ctx, key)
	if len(indexedValues) > 0 {
		log.Infof("[参数解析] %s从索引参数获取到: %v", key, indexedValues)
	} else {
		log.Infof("[参数解析] %s未获取到任何值", key)
	}
	return indexedValues
}

// errorResponse 返回错误响应
func errorResponse(ctx *gin.Context, code int, message string) error {
	ctx.JSON(code, gin.H{
		"code": code,
		"msg":  message,
	})
	return nil
}

// CloudRecommendStats 云端推荐结果统计
// 对应路由: v1.GET("cloud/recommend_stats/:flag", wrapper.Auth(api.CloudRecommendStats, scopeAll))
func CloudRecommendStats(ctx *gin.Context) error {
	flag := ctx.Param("flag")
	if flag == "" {
		return errorResponse(ctx, http.StatusBadRequest, "flag参数不能为空")
	}

	// 获取用户ID
	userIdVal, ok := ctx.Get("user_id_with_token")
	if !ok {
		return errorResponse(ctx, http.StatusUnauthorized, "获取用户信息失败")
	}

	// 安全地转换用户ID，支持string和float64类型
	var userId int64
	switch v := userIdVal.(type) {
	case float64:
		userId = int64(v)
	case string:
		userId, _ = strconv.ParseInt(v, 10, 64)
	default:
		// 尝试使用cast包进行转换
		userId = cast.ToInt64(userIdVal)
	}

	if userId == 0 {
		return errorResponse(ctx, http.StatusUnauthorized, "无效的用户ID")
	}

	// 构建请求数据
	req := &pb.CloudRecommendResultsRequest{
		UserId: userId,
		Flag:   flag,
	}

	// 发送RPC请求
	rsp, err := pb.GetProtoClient().CloudRecommendResults(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.Errorf("[云端推荐结果-统计] RPC调用失败: %v", err)
		return errorResponse(ctx, http.StatusInternalServerError, "获取统计数据失败: "+err.Error())
	}

	// 返回统计数据
	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": map[string]interface{}{
			"total": rsp.Total,
		},
		"msg": "获取成功",
	})
	return nil
}

// GroupIpResults 获取IP维度推荐结果
func GroupIpResults(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "参数错误!")
	}

	// 获取请求参数
	flag := ctx.Param("flag")
	if flag == "" {
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "参数错误!")
	}

	// 验证请求参数
	var param struct {
		OperateCompanyID        int64    `json:"operate_company_id" form:"operate_company_id"`
		Page                    int32    `json:"page" form:"page"`
		PerPage                 int32    `json:"per_page" form:"per_page"`
		IsCycle                 bool     `json:"is_cycle" form:"is_cycle"`
		Ip                      string   `json:"ip" form:"ip"`
		Keyword                 string   `json:"keyword" form:"keyword"`
		Domain                  []string `json:"domain" form:"domain"`
		Cert                    []string `json:"cert" form:"cert"`
		Icp                     []string `json:"icp" form:"icp"`
		Logo                    []string `json:"logo" form:"logo"`
		Title                   []string `json:"title" form:"title"`
		Port                    []string `json:"port" form:"port"`
		Protocol                []string `json:"protocol" form:"protocol"`
		Subdomain               []string `json:"subdomain" form:"subdomain"`
		ClueCompanyName         string   `json:"clue_company_name" form:"clue_company_name"`
		DateRangeType           int32    `json:"date_range_type" form:"date_range_type"`
		IpArray                 []string `json:"ip_array" form:"ip_array"`
		ReasonType              int32    `json:"reason_type" form:"reason_type"`
		OrganizationCompanyID   int64    `json:"organization_company_id" form:"organization_company_id"`
		AssetsLevel             int32    `json:"assets_level" form:"assets_level"`
		ChainType               int32    `json:"chain_type" form:"chain_type"`
		AllCompanyName          []string `json:"all_company_name" form:"all_company_name"`
		TitleNot                []string `json:"title_not" form:"title_not"`
		DomainNot               []string `json:"domain_not" form:"domain_not"`
		CloudName               string   `json:"cloud_name" form:"cloud_name"`
		OrganizationCompanyName string   `json:"organization_company_name" form:"organization_company_name"`
		Level                   []int32  `json:"level" form:"level"`
		AssetsConfidenceLevel   []int32  `json:"assets_confidence_level" form:"assets_confidence_level"`
		Url                     string   `json:"url" form:"url"`
		CreatedAt               []string `json:"created_at" form:"created_at"`
		UpdatedAt               []string `json:"updated_at" form:"updated_at"`
		Whole                   bool     `json:"whole" form:"whole"`
	}

	// 先尝试绑定Query参数，因为这是GET请求最常用的方式
	if err := ctx.ShouldBindQuery(&param); err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] Query参数绑定失败: %v", err)
	}

	// 如果是POST请求，可能会有JSON格式的参数
	if ctx.Request.Method == "POST" {
		// 尝试绑定JSON参数
		if err := ctx.ShouldBindJSON(&param); err != nil {
			log.WithContextErrorf(ctx, "[IP维度推荐结果] JSON参数绑定失败: %v", err)
		}
	}

	// 打印接收到的参数，用于调试
	log.WithContextInfof(ctx, "[IP维度推荐结果] 接收到的参数: %+v", param)

	// 特殊处理date_range_type参数，确保能正确获取
	dateRangeTypeStr := ctx.Query("date_range_type")
	if dateRangeTypeStr != "" {
		dateRangeType, err := strconv.Atoi(dateRangeTypeStr)
		if err == nil {
			param.DateRangeType = int32(dateRangeType)
			log.WithContextInfof(ctx, "[IP维度推荐结果] 从查询字符串获取date_range_type: %d", param.DateRangeType)
		}
	}

	// 特殊处理level参数，确保能正确获取数组
	levelStr := ctx.Query("level")
	if levelStr != "" {
		// 如果是单个值且不为空
		levelVal, err := strconv.Atoi(levelStr)
		if err == nil {
			param.Level = append(param.Level, int32(levelVal))
			log.WithContextInfof(ctx, "[IP维度推荐结果] 从查询字符串获取level: %d", levelVal)
		}
	} else {
		// 检查是否存在level参数但值为空
		if ctx.Request.URL.Query().Has("level") {
			// level参数存在但值为空，设置为-1表示特殊处理
			param.Level = []int32{-1}
			log.WithContextInfof(ctx, "[IP维度推荐结果] level参数存在但值为空，设置为特殊值-1")
		} else {
			// level参数不存在，不做处理
			log.WithContextInfof(ctx, "[IP维度推荐结果] level参数不存在，不添加level查询条件")
		}
	}

	// 处理level[0]、level[1]等格式的参数
	levelParams := parseIndexedParams(ctx, "level")
	for _, lv := range levelParams {
		levelVal, err := strconv.Atoi(lv)
		if err == nil {
			param.Level = append(param.Level, int32(levelVal))
			log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取level: %d", levelVal)
		}
	}

	// 处理带索引的数组参数，如subdomain[0]、subdomain[1]等
	// 处理subdomain参数
	subdomainParams := parseIndexedParams(ctx, "subdomain")
	if len(subdomainParams) > 0 {
		param.Subdomain = append(param.Subdomain, subdomainParams...)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取subdomain: %v", subdomainParams)
	}

	// 处理domain参数
	domainParams := parseIndexedParams(ctx, "domain")
	if len(domainParams) > 0 {
		param.Domain = append(param.Domain, domainParams...)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取domain: %v", domainParams)
	}

	// 处理cert参数
	certParams := parseIndexedParams(ctx, "cert")
	if len(certParams) > 0 {
		param.Cert = append(param.Cert, certParams...)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取cert: %v", certParams)
	}

	// 处理icp参数
	icpParams := parseIndexedParams(ctx, "icp")
	if len(icpParams) > 0 {
		param.Icp = append(param.Icp, icpParams...)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取icp: %v", icpParams)
	}

	// 处理logo参数
	logoParams := parseIndexedParams(ctx, "logo")
	if len(logoParams) > 0 {
		param.Logo = append(param.Logo, logoParams...)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取logo: %v", logoParams)
	}

	// 处理title参数
	titleParams := parseIndexedParams(ctx, "title")
	if len(titleParams) > 0 {
		param.Title = append(param.Title, titleParams...)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取title: %v", titleParams)
	}

	// 处理port参数
	portParams := parseIndexedParams(ctx, "port")
	if len(portParams) > 0 {
		param.Port = append(param.Port, portParams...)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取port: %v", portParams)
	}

	// 处理protocol参数
	protocolParams := parseIndexedParams(ctx, "protocol")
	if len(protocolParams) > 0 {
		param.Protocol = append(param.Protocol, protocolParams...)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取protocol: %v", protocolParams)
	}

	// 处理ip_array参数
	ipArrayParams := parseIndexedParams(ctx, "ip_array")
	if len(ipArrayParams) > 0 {
		param.IpArray = append(param.IpArray, ipArrayParams...)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 从索引参数获取ip_array: %v", ipArrayParams)
	}

	log.WithContextInfof(ctx, "[IP维度推荐结果] 处理后的参数: date_range_type=%d, level=%v, subdomain=%v", param.DateRangeType, param.Level, param.Subdomain)

	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PerPage <= 0 {
		param.PerPage = 10
	}

	// 构建请求参数
	req := &pb.GroupIpResultsRequest{
		UserId:                  int64(userID),
		CompanyId:               int64(companyID),
		Flag:                    flag,
		Page:                    param.Page,
		PerPage:                 param.PerPage,
		IsCycle:                 param.IsCycle,
		Ip:                      param.Ip,
		Keyword:                 param.Keyword,
		Domain:                  param.Domain,
		Cert:                    param.Cert,
		Icp:                     param.Icp,
		Logo:                    param.Logo,
		Title:                   param.Title,
		Port:                    param.Port,
		Protocol:                param.Protocol,
		Subdomain:               param.Subdomain,
		ClueCompanyName:         param.ClueCompanyName,
		DateRangeType:           param.DateRangeType,
		IpArray:                 param.IpArray,
		ReasonType:              param.ReasonType,
		OrganizationCompanyId:   param.OrganizationCompanyID,
		AssetsLevel:             param.AssetsLevel,
		ChainType:               param.ChainType,
		AllCompanyName:          param.AllCompanyName,
		TitleNot:                param.TitleNot,
		DomainNot:               param.DomainNot,
		CloudName:               param.CloudName,
		OrganizationCompanyName: param.OrganizationCompanyName,
		Level:                   param.Level,
		AssetsConfidenceLevel:   param.AssetsConfidenceLevel,
		Url:                     param.Url,
		CreatedAt:               param.CreatedAt,
		UpdatedAt:               param.UpdatedAt,
		Whole:                   param.Whole,
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().GroupIpResults(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(300))
	if err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] 获取IP维度推荐结果失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend", err)
	}

	// 解析RPC返回的字符串格式数据为对象
	var resultData map[string]interface{}
	if err := json.Unmarshal([]byte(rsp.Items), &resultData); err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] 解析结果数据失败: %v", err)
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "解析数据失败")
	}

	// 处理data字段，移除ip_status为100的字段
	if data, ok := resultData["data"].(map[string]interface{}); ok {
		for _, ipRecords := range data {
			if records, ok := ipRecords.([]interface{}); ok {
				for _, record := range records {
					if recordMap, ok := record.(map[string]interface{}); ok {
						// 如果ip_status为100，则移除该字段
						if ipStatus, exists := recordMap["ip_status"]; exists {
							if status, ok := ipStatus.(float64); ok && status == 100 {
								delete(recordMap, "ip_status")
							}
						}
					}
				}
			}
		}
	}

	// 构造与PHP接口一致的响应格式
	responseData := gin.H{
		"total":        rsp.Total,
		"per_page":     rsp.PerPage,
		"current_page": rsp.CurrentPage,
		"last_page":    rsp.LastPage,
		"from":         rsp.From,
		"to":           rsp.To,
		"items":        resultData["data"], // 使用处理后的data字段
	}

	// 返回成功响应
	return response.Gen(ctx).SendSuccess("cloud_recommend", response.MsgSuccess, responseData)
}

// DeleteRecommendResult 删除推荐结果
func DeleteRecommendResult(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "[删除推荐结果] 获取用户信息失败: %v", err)
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "参数错误!")
	}

	// 获取flag参数
	flag := ctx.Param("flag")
	if flag == "" {
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "参数错误!")
	}

	// 验证请求参数
	var param struct {
		IpArray          []string `json:"ip_array" form:"ip_array"`
		Whole            string   `json:"whole" form:"whole"`
		OperateCompanyID int64    `json:"operate_company_id" form:"operate_company_id"`
	}

	// 解析请求体
	if err := ctx.ShouldBindJSON(&param); err != nil {
		log.WithContextErrorf(ctx, "[删除推荐结果] 参数绑定失败: %v", err)
	}

	// 处理whole参数，将字符串转换为布尔值
	whole := false
	if param.Whole == "true" || param.Whole == "1" {
		whole = true
	}

	// 构建请求参数
	req := &pb.DeleteRecommendResultRequest{
		UserId:           int64(userID),
		CompanyId:        int64(companyID),
		Flag:             flag,
		IpArray:          param.IpArray,
		Whole:            whole,
		OperateCompanyId: param.OperateCompanyID,
	}

	// 调用RPC服务
	_, err = pb.GetProtoClient().DeleteRecommendResult(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextErrorf(ctx, "[删除推荐结果] 删除推荐结果失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend", err)
	}
	return response.Gen(ctx).SendSuccess("cloud_recommend", response.MsgSuccess, nil)
}

// UpdateAssetsConfidenceLevel 更新资产可信度
func UpdateAssetsConfidenceLevel(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "参数错误!")
	}

	// 获取路径参数flag
	flag := ctx.Param("flag")
	if flag == "" {
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "缺少标识参数!")
	}

	// 使用map接收JSON数据，以便处理字符串类型的参数
	var rawParam map[string]interface{}
	if err := ctx.ShouldBindJSON(&rawParam); err != nil {
		log.WithContextErrorf(ctx, "[更新资产可信度] JSON参数绑定失败: %v", err)
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "参数错误!")
	}

	// 打印原始参数
	log.WithContextInfof(ctx, "[更新资产可信度] 接收到的原始参数: %+v", rawParam)

	// 提取并转换参数
	var confidenceLevel int32
	var ipArray []string
	var operateCompanyID int64

	// 处理set_assets_confidence_level参数，支持字符串和数字类型
	if confLevel, ok := rawParam["set_assets_confidence_level"]; ok {
		switch v := confLevel.(type) {
		case float64:
			confidenceLevel = int32(v)
		case string:
			level, err := strconv.ParseInt(v, 10, 32)
			if err != nil {
				log.WithContextErrorf(ctx, "[更新资产可信度] 解析可信度级别失败: %v", err)
				return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "可信度级别参数格式错误!")
			}
			confidenceLevel = int32(level)
		default:
			log.WithContextErrorf(ctx, "[更新资产可信度] 可信度级别参数类型错误: %T", v)
			return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "可信度级别参数类型错误!")
		}
	}

	// 处理ip_array参数
	if ipArr, ok := rawParam["ip_array"]; ok {
		if arr, ok := ipArr.([]interface{}); ok {
			for _, ip := range arr {
				if ipStr, ok := ip.(string); ok {
					ipArray = append(ipArray, ipStr)
				}
			}
		}
	}

	// 处理operate_company_id参数
	if opCompanyID, ok := rawParam["operate_company_id"]; ok {
		switch v := opCompanyID.(type) {
		case float64:
			operateCompanyID = int64(v)
		case string:
			id, err := strconv.ParseInt(v, 10, 64)
			if err != nil {
				log.WithContextErrorf(ctx, "[更新资产可信度] 解析操作公司ID失败: %v", err)
				// 不返回错误，使用默认值0
			} else {
				operateCompanyID = id
			}
		}
	}

	// 打印处理后的参数
	log.WithContextInfof(ctx, "[更新资产可信度] 处理后的参数: confidenceLevel=%d, ipArray=%v, operateCompanyID=%d",
		confidenceLevel, ipArray, operateCompanyID)

	// 验证可信度级别参数
	if confidenceLevel <= 0 {
		return response.Gen(ctx).SendByErrorMsg("cloud_recommend", "缺少可信度级别参数!")
	}

	// 构建请求参数
	req := &pb.UpdateAssetsConfidenceLevelRequest{
		UserId:                   int64(userID),
		CompanyId:                int64(companyID),
		Flag:                     flag,
		IpArray:                  ipArray,
		SetAssetsConfidenceLevel: confidenceLevel,
		OperateCompanyId:         operateCompanyID,
	}

	// 调用RPC服务
	_, err = pb.GetProtoClient().UpdateAssetsConfidenceLevel(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(300))
	if err != nil {
		log.WithContextErrorf(ctx, "[更新资产可信度] 更新资产可信度失败: %v", err)
		return response.Gen(ctx).SendByError("cloud_recommend", err)
	}

	return response.Gen(ctx).SendSuccess("cloud_recommend", response.MsgSuccess, nil)
}
