# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
dist: bin
builds:
  - id: foradar_api
    dir: apiService
    binary: "foradar_api_{{ .Os }}_{{ .Arch }}"
    goos:
      - linux
    ldflags:
      - "-s -w"
    goarch:
      - amd64
    no_unique_dist_dir: true
    hooks:
      pre:
        - sh -c "chmod +x gen-dockerfile.sh && ./gen-dockerfile.sh api"
  - id: foradar_core
    dir: coreService
    binary: "foradar_core_{{ .Os }}_{{ .Arch }}"
    goos:
      - linux
    ldflags:
      - "-s -w"
    goarch:
      - amd64
    no_unique_dist_dir: true
    hooks:
      pre:
        - sh -c "chmod +x gen-dockerfile.sh && ./gen-dockerfile.sh core"
  - id: foradar_crawler
    dir: crawlerService
    binary: "foradar_crawler_{{ .Os }}_{{ .Arch }}"
    goos:
      - linux
    ldflags:
      - "-s -w"
    goarch:
      - amd64
    no_unique_dist_dir: true
    hooks:
      pre:
        - sh -c "chmod +x gen-dockerfile.sh && ./gen-dockerfile.sh crawler"
  - id: foradar_web
    dir: webService
    binary: "foradar_web_{{ .Os }}_{{ .Arch }}"
    goos:
      - linux
    ldflags:
      - "-s -w"
    goarch:
      - amd64
    no_unique_dist_dir: true
    hooks:
      pre:
        - sh -c "chmod +x gen-dockerfile.sh && ./gen-dockerfile.sh web"
  - id: foradar_cli
    dir: cliService
    binary: "foradar_cli_{{ .Os }}_{{ .Arch }}"
    goos:
      - linux
    ldflags:
      - "-s -w"
    goarch:
      - amd64
    no_unique_dist_dir: true
  - id: foradar_scan
    dir: scanService
    binary: "foradar_scan_{{ .Os }}_{{ .Arch }}"
    goos:
      - linux
    ldflags:
      - "-s -w"
    goarch:
      - amd64
    no_unique_dist_dir: true
    hooks:
      pre:
        - sh -c "chmod +x create-centos-image.sh && ./create-centos-image.sh"
        - sh -c "chmod +x gen-dockerfile.sh && ./gen-dockerfile.sh scan"
        - \cp -rf /data/micro-service/scanService/assets/ksubdomain_linux_amd64 /data/micro-service/bin/ksubdomain
        - \cp -rf /data/micro-service/scanService/assets/subdomain_dic.txt /data/micro-service/bin/subdomain_dic.txt
  - id: foradar_cron
    dir: cronService
    binary: "foradar_cron_{{ .Os }}_{{ .Arch }}"
    goos:
      - linux
    ldflags:
      - "-s -w"
    goarch:
      - amd64
    no_unique_dist_dir: true
    hooks:
      pre:
        - sh -c "chmod +x gen-dockerfile.sh && ./gen-dockerfile.sh cron"
dockers:
  - id: foradar_api
    goos: linux
    goarch: amd64
    ids:
      - foradar_api
      - foradar_cli
    dockerfile: bin/dockerfile_api
    image_templates:
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/api:{{ .Version }}"
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/api:latest"
    skip_push: "false"
    use: docker
    build_flag_templates:
      - "--platform=amd64"
      - "--label=image.created={{.Date}}"
      - "--label=image.title=foradar_api"
      - "--label=image.revision={{.FullCommit}}"
      - "--label=image.version={{.Version}}"
    push_flags:
      - --tls-verify=false
  - id: foradar_core
    goos: linux
    goarch: amd64
    ids:
      - foradar_core
      - foradar_cli
    dockerfile: bin/dockerfile_core
    image_templates:
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/core:{{ .Version }}"
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/core:latest"
    skip_push: "false"
    use: docker
    build_flag_templates:
      - "--platform=amd64"
      - "--label=image.created={{.Date}}"
      - "--label=image.title=foradar_core"
      - "--label=image.revision={{.FullCommit}}"
      - "--label=image.version={{.Version}}"
    push_flags:
      - --tls-verify=false
  - id: foradar_crawler
    goos: linux
    goarch: amd64
    ids:
      - foradar_crawler
      - foradar_cli
    dockerfile: bin/dockerfile_crawler
    image_templates:
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/crawler:{{ .Version }}"
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/crawler:latest"
    skip_push: "false"
    use: docker
    build_flag_templates:
      - "--platform=amd64"
      - "--label=image.created={{.Date}}"
      - "--label=image.title=foradar_crawler"
      - "--label=image.revision={{.FullCommit}}"
      - "--label=image.version={{.Version}}"
    push_flags:
      - --tls-verify=false
  - id: foradar_scan
    goos: linux
    goarch: amd64
    ids:
      - foradar_scan
      - foradar_cli
    dockerfile: bin/dockerfile_scan
    extra_files:
      - ./bin/ksubdomain
      - ./bin/subdomain_dic.txt
    image_templates:
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/scan:{{ .Version }}"
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/scan:latest"
    skip_push: "false"
    use: docker
    build_flag_templates:
      - "--platform=amd64"
      - "--label=image.created={{.Date}}"
      - "--label=image.title=foradar_scan"
      - "--label=image.revision={{.FullCommit}}"
      - "--label=image.version={{.Version}}"
    push_flags:
      - --tls-verify=false
  - id: foradar_web
    goos: linux
    goarch: amd64
    ids:
      - foradar_web
      - foradar_cli
    dockerfile: bin/dockerfile_web
    image_templates:
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/web:{{ .Version }}"
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/web:latest"
    skip_push: "false"
    use: docker
    build_flag_templates:
      - "--platform=amd64"
      - "--label=image.created={{.Date}}"
      - "--label=image.title=foradar_web"
      - "--label=image.revision={{.FullCommit}}"
      - "--label=image.version={{.Version}}"
    push_flags:
      - --tls-verify=false
  - id: foradar_cron
    goos: linux
    goarch: amd64
    ids:
      - foradar_cron
      - foradar_cli
    dockerfile: bin/dockerfile_cron
    image_templates:
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/cron:{{ .Version }}"
      - "{{ .Env.DOCKER_REGISTER_ADDR }}/foradar/cron:latest"
    skip_push: "false"
    use: docker
    build_flag_templates:
      - "--platform=amd64"
      - "--label=image.created={{.Date}}"
      - "--label=image.title=foradar_cron"
      - "--label=image.revision={{.FullCommit}}"
      - "--label=image.version={{.Version}}"
    push_flags:
      - --tls-verify=false
archives:
  - format: binary
checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ incpatch .Version }}"
changelog:
  sort: asc
  filters:
    exclude:
      - '^docs:'
      - '^test:'
release:
  prerelease: auto
  header: |
    ## Some title ({{ .Date }})

    Welcome to this new release!

  # Footer template for the release body.
  # Defaults to empty.
  footer: |
    ## Thanks!

    Those were the changes on {{ .Tag }}!

gitlab_urls:
  api: https://git.baimaohui.net/api/v4/
  download: https://git.baimaohui.net
