# API配额扣费系统部署指南

## 快速部署步骤

### 1. 数据库变更

#### 1.1 添加API配额字段
```sql
ALTER TABLE `auth_access_clients` ADD COLUMN `api_quota_remaining` int(11) unsigned NOT NULL DEFAULT '5000000' COMMENT 'API剩余次数';
```

#### 1.2 创建配置表
```sql
CREATE TABLE `api_quota_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `api_path` varchar(255) NOT NULL COMMENT 'API路径（支持通配符）',
  `method` varchar(10) NOT NULL DEFAULT '*' COMMENT 'HTTP方法（GET/POST/*）',
  `cost_multiplier` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '扣费倍率',
  `description` varchar(500) DEFAULT NULL COMMENT '配置说明',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1启用，2禁用',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_api_path` (`api_path`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API配额扣费配置表';
```

#### 1.3 运行数据库迁移
```bash
# 运行迁移创建表并导入配置数据
go run cliService/databases/2025_07_04_create_api_quota_config_table.go
go run cliService/databases/2025_07_04_alter_auth_access_clients_add_quota.go
```

### 2. 代码部署

所有代码已经集成到现有系统中，重新编译部署即可：

```bash
# 编译
go build -o your_app_name

# 重启服务
systemctl restart your_service_name
```

### 3. 验证部署

#### 3.1 检查数据库
```sql
-- 检查字段是否添加成功
DESCRIBE auth_access_clients;

-- 检查配置表是否创建成功
SELECT COUNT(*) FROM api_quota_config;

-- 查看配置数据
SELECT api_path, method, cost_multiplier, description FROM api_quota_config LIMIT 10;
```

#### 3.2 检查日志
```bash
# 查看扣费日志
tail -f /path/to/logs | grep "ApiQuota"

# 应该能看到类似的日志：
# [ApiQuota] 扣费成功 clientId=xxx, path=/api/v1/fofa/query, method=POST, cost=5
```

#### 3.3 测试API调用
```bash
# 调用一个API，检查是否正常扣费
curl -X POST "http://your-api-host/api/v1/fofa/query" \
  -H "Authorization: Bearer your_token" \
  -d '{"query": "test"}'

# 检查客户端配额是否减少
SELECT client_id, api_quota_remaining FROM auth_access_clients WHERE client_id = 'your_client_id';
```

## 配置调优

### 1. 调整扣费倍率

```sql
-- 所有API统一使用1倍扣费倍率
UPDATE api_quota_config SET cost_multiplier = 1;

-- 添加新的API配置
INSERT INTO api_quota_config (api_path, method, cost_multiplier, description, status)
VALUES ('/api/v1/new/endpoint', 'POST', 1, '新API接口', 1);
```

### 2. 性能调优

#### 2.1 Redis缓存配置
```go
// 在 api_quota.go 中调整这些常量
const (
    ApiQuotaCacheExpire = 300      // 缓存过期时间（秒）
    BatchUpdateThreshold = 10      // 批量更新阈值
)
```

#### 2.2 数据库索引优化
```sql
-- 如果查询性能不佳，可以添加复合索引
CREATE INDEX idx_api_path_method_status ON api_quota_config(api_path, method, status);
```

### 3. 监控配置

#### 3.1 配额监控
```sql
-- 创建配额监控视图
CREATE VIEW quota_monitor AS
SELECT 
    client_id,
    company_name,
    api_quota_remaining,
    CASE 
        WHEN api_quota_remaining < 1000 THEN '紧急'
        WHEN api_quota_remaining < 10000 THEN '警告'
        ELSE '正常'
    END as status,
    status as client_status
FROM auth_access_clients 
WHERE scope != '*';
```

#### 3.2 使用统计
```sql
-- 查看API使用统计（需要结合request_api_record表）
SELECT
    r.api_path,
    COUNT(*) as call_count,
    c.cost_multiplier,
    COUNT(*) * c.cost_multiplier as total_cost
FROM request_api_record r
LEFT JOIN api_quota_config c ON c.api_path = r.api_path
WHERE r.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY r.api_path, c.cost_multiplier
ORDER BY total_cost DESC;

-- 查看高消耗API使用情况
SELECT
    api_path,
    cost_multiplier,
    description
FROM api_quota_config
WHERE cost_multiplier >= 10
ORDER BY cost_multiplier DESC;
```

## 故障处理

### 1. 常见问题

#### 1.1 扣费不生效
```bash
# 检查客户端scope字段
SELECT client_id, scope FROM auth_access_clients WHERE client_id = 'your_client_id';

# 检查API配置
SELECT * FROM api_quota_config WHERE api_path LIKE '%your_api_path%';

# 检查Redis连接
redis-cli ping
```

#### 1.2 配额计算错误
```bash
# 清除Redis缓存
redis-cli DEL "api_quota:your_client_id"
redis-cli DEL "api_quota_counter:your_client_id"

# 重新从数据库加载配额
```

#### 1.3 客户端被误禁用
```sql
-- 重新启用客户端
UPDATE auth_access_clients SET status = 1 WHERE client_id = 'your_client_id';

-- 重置配额
UPDATE auth_access_clients SET api_quota_remaining = 5000000 WHERE client_id = 'your_client_id';
```

### 2. 紧急恢复

#### 2.1 临时禁用扣费
```sql
-- 临时禁用所有扣费规则
UPDATE api_quota_config SET status = 2;
```

#### 2.2 批量重置配额
```sql
-- 批量重置所有客户端配额
UPDATE auth_access_clients SET api_quota_remaining = 5000000 WHERE scope != '*';
```

## 维护建议

1. **定期备份配置**: 定期备份 `api_quota_config` 表
2. **监控配额使用**: 设置配额不足的告警
3. **日志轮转**: 确保扣费日志不会占用过多磁盘空间
4. **性能监控**: 监控Redis和数据库的性能指标
5. **配置审计**: 定期审查API扣费配置的合理性
