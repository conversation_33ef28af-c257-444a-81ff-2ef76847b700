package handler

import (
	"context"
	"errors"
	"github.com/spf13/cast"

	"micro-service/cronService/crontab"
	pb "micro-service/cronService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/cron"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

type Cron struct{}

func (c *Cron) AddCron(ctx context.Context, req *pb.AddCronRequest, rsp *pb.AddCronResponse) error {
	log.WithContextInfof(ctx, "Received Cron.add request: %v", req)
	insert := &cron.Cron{
		Method: req.Method,
		Type:   cast.ToInt(req.Type),
		Name:   req.Name,
		Status: cast.ToInt(req.Status),
		Params: req.Params,
		Spec:   req.Spec,
		UserId: req.UserId,
	}
	if task, _ := cron.NewCronModel().First(
		mysql.WithWhere("method", insert.Method),
		mysql.WithWhere("user_id", insert.UserId),
	); task != nil {
		return errors.New("任务已存在")
	}
	if err := cron.NewCronModel().Create(insert); err != nil {
		return err
	}

	rsp.Id = insert.Id
	rsp.Status = true
	// 加载定时任务
	_ = crontab.GetCronInstance().LoadMysqlJob()
	return nil
}

func (c *Cron) ListCron(_ context.Context, req *pb.ListCronRequest, rsp *pb.ListCronResponse) error {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	if req.Status > 0 {
		handlers = append(handlers, mysql.WithColumnValue("status", req.Status))
	}
	if req.Type > 0 {
		handlers = append(handlers, mysql.WithColumnValue("type", req.Type))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLike("name", "%"+req.Name+"%"))
	}
	if req.Spec != "" {
		handlers = append(handlers, mysql.WithWhere("spec", req.Spec))
	}
	if req.Params != "" {
		handlers = append(handlers, mysql.WithLike("params", "%"+req.Params+"%"))
	}
	if req.Method != "" {
		handlers = append(handlers, mysql.WithLike("method", "%"+req.Method+"%"))
	}
	if len(req.CreatedAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("created_at >= ?", req.CreatedAt[0]))
	}
	if len(req.CreatedAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("created_at >= ?", req.CreatedAt[0]))
		handlers = append(handlers, mysql.WithWhere("created_at <= ?", req.CreatedAt[1]))
	}
	if len(req.UpdatedAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("updated_at >= ?", req.UpdatedAt[0]))
	}
	if len(req.UpdatedAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("updated_at >= ?", req.UpdatedAt[0]))
		handlers = append(handlers, mysql.WithWhere("updated_at <= ?", req.UpdatedAt[1]))
	}
	list, total, err := cron.NewCronModel().List(int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}
	for i := range list {
		item := &pb.CronInfo{
			Id:        list[i].Id,
			Type:      cast.ToInt32(list[i].Type),
			UserId:    list[i].UserId,
			Name:      list[i].Name,
			Spec:      list[i].Spec,
			Method:    list[i].Method,
			Params:    list[i].Params,
			Status:    cast.ToInt32(list[i].Status),
			CreatedAt: list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt: list[i].UpdatedAt.Format(utils.DateTimeLayout),
		}
		rsp.Items = append(rsp.Items, item)
		rsp.CurrentPage = cast.ToInt64(req.Page)
		rsp.PerPage = req.PerPage
	}
	rsp.Total = total
	return nil
}

func (c *Cron) UpdateCron(ctx context.Context, req *pb.UpdateCronRequest, rsp *pb.UpdateCronResponse) error {
	cronInfo, err := cron.NewCronModel().First(mysql.WithColumnValue("id", req.Id))
	if err != nil {
		return err
	}
	cronInfo.Name = req.Name
	cronInfo.Type = cast.ToInt(req.Type)
	cronInfo.Spec = req.Spec
	cronInfo.Method = req.Method
	cronInfo.Status = cast.ToInt(req.Status)
	cronInfo.Params = req.Params

	err = cron.NewCronModel().Updates(*cronInfo, mysql.WithSelect("name", "type", "spec", "status", "method", "params"))
	if err != nil {
		log.WithContextErrorf(ctx, "[Cron Service] Update Cron Task failed: %v", err)
		return err
	}
	if cronInfo.Status == 0 {
		// 停止任务
		crontab.GetCronInstance().StopJob(cast.ToString(cronInfo.UserId) + ":" + cronInfo.Method)
	} else {
		// 更新Job
		_ = crontab.GetCronInstance().LoadMysqlJob()
	}

	rsp.Status = true
	return nil
}

func (c *Cron) DeleteCron(_ context.Context, req *pb.DeleteCronRequest, rsp *pb.DeleteCronResponse) error {
	if len(req.Id) == 0 {
		return errors.New("任务ID不能为空")
	}
	crons, _, err := cron.NewCronModel().List(0, 0, mysql.WithValuesIn("id", req.Id))
	if err != nil {
		return err
	}
	for i := range crons {
		// 停止任务
		crontab.GetCronInstance().StopJob(cast.ToString(crons[i].UserId) + ":" + crons[i].Method)
	}
	// 删除历史执行数据
	if hErr := cron.NewHistoryModel().Where("cron_id in (?)", req.Id).Delete(&cron.History{}).Error; hErr != nil {
		return hErr
	}
	if cErr := cron.NewCronModel().Delete(req.Id); cErr != nil {
		return cErr
	}
	rsp.Status = true
	return nil
}

func (c *Cron) ClearCronHistory(_ context.Context, req *pb.DeleteCronRequest, rsp *pb.DeleteCronResponse) error {
	if len(req.Id) == 0 {
		return errors.New("任务ID不能为空")
	}
	// 删除历史执行数据
	if hErr := cron.NewHistoryModel().Where("cron_id in (?)", req.Id).Delete(&cron.History{}).Error; hErr != nil {
		return hErr
	}
	rsp.Status = true
	return nil
}

func (c *Cron) ListCronHistory(_ context.Context, req *pb.ListCronHistoryRequest, rsp *pb.ListCronHistoryResponse) error {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithColumnValue("cron_id", req.CronId))
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	if req.Status > 0 {
		handlers = append(handlers, mysql.WithWhere("status != ?", req.Status))
	}
	if req.Status < 0 {
		handlers = append(handlers, mysql.WithWhere("status = ?", req.Status))
	}
	if len(req.StartAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("start_at >= ?", req.StartAt[0]))
	}
	if len(req.StartAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("start_at >= ?", req.StartAt[0]))
		handlers = append(handlers, mysql.WithWhere("start_at <= ?", req.StartAt[1]))
	}
	if len(req.EndAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("end_at >= ?", req.EndAt[0]))
	}
	if len(req.EndAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("end_at >= ?", req.EndAt[0]))
		handlers = append(handlers, mysql.WithWhere("end_at <= ?", req.EndAt[1]))
	}
	list, total, err := cron.NewHistoryModel().List(int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}
	for i := range list {
		item := &pb.CronHistoryInfo{
			Id:        list[i].Id,
			CronId:    list[i].CronId,
			StartAt:   list[i].StartAt.Format(utils.DateTimeLayout),
			EndAt:     list[i].EndAt.Format(utils.DateTimeLayout),
			Status:    cast.ToInt32(list[i].Status),
			Result:    list[i].Result,
			CreatedAt: list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt: list[i].UpdatedAt.Format(utils.DateTimeLayout),
		}
		rsp.Items = append(rsp.Items, item)
		rsp.CurrentPage = cast.ToInt64(req.Page)
		rsp.PerPage = req.PerPage
	}
	rsp.Total = total
	return nil
}
