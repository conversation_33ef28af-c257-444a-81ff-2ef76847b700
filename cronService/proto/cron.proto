syntax = "proto3";

package cron;

option go_package = "./proto;cron";

service Cron {
  rpc AddCron (AddCronRequest) returns (AddCronResponse) {}
  rpc ListCron (ListCronRequest) returns (ListCronResponse) {}
  rpc DeleteCron (DeleteCronRequest) returns (DeleteCronResponse) {}
  rpc UpdateCron (UpdateCronRequest) returns (UpdateCronResponse) {}
  rpc ListCronHistory (ListCronHistoryRequest) returns (ListCronHistoryResponse) {}
  rpc ClearCronHistory (DeleteCronRequest) returns (DeleteCronResponse) {}
}

message DeleteCronRequest {
  repeated uint64 id = 1; // @tag validate:"required,dive,required,number" zh:"任务ID"
}

message DeleteCronResponse {
  bool status = 1;
}

message UpdateCronRequest {
  uint64 id = 1; // @tag form:"id" validate:"required,number" zh:"任务ID"
  uint64 user_id = 2; // @tag form:"user_id" validate:"required,number" zh:"用户ID"
  string name = 3; // @tag form:"name" validate:"required" zh:"定时任务名称"
  int32 status = 4; // @tag form:"status" validate:"omitempty,number" zh:"任务状态"
  int32 type = 5; // @tag form:"type" validate:"required,number,oneof=1" zh:"任务类型"
  string spec = 6; // @tag form:"spec" validate:"required" zh:"定时参数"
  string method = 7; // @tag form:"method" validate:"required" zh:"调用方法"
  string params = 8; // @tag form:"params" validate:"omitempty" zh:"任务参数"
}

message UpdateCronResponse {
  bool status = 1;
}

message CronInfo {
  uint64 id = 1; // @tag validate:"required,number" zh:"任务ID"
  uint64 user_id = 2; // @tag validate:"required,number" zh:"用户ID"
  string name = 3; // @tag validate:"required" zh:"定时任务名称"
  int32 status = 4; // @tag validate:"required,number,oneof=0 1" zh:"任务状态"
  int32 type = 5; // @tag validate:"required,number,oneof=1" zh:"任务类型"
  string spec = 6; // @tag validate:"required" zh:"定时参数"
  string method = 7; // @tag validate:"required" zh:"调用方法"
  string params = 8; // @tag validate:"omitempty" zh:"任务参数"
  string created_at = 9; // @tag validate:"required" zh:"创建时间"
  string updated_at = 10; // @tag validate:"required" zh:"更新时间"
}
message CronHistoryInfo {
  uint64 id = 1; // @tag form:"id" validate:"required,number" zh:"任务记录ID"
  uint64 cron_id = 2; // @tag form:"cron_id" validate:"required,number" zh:"任务ID"
  int32 status = 3; // @tag form:"status" validate:"required,number,oneof=0 1" zh:"任务执行状态"
  string start_at = 4; // @tag form:"start_at" validate:"required" zh:"开始执行时间"
  string end_at = 5; // @tag form:"end_at" validate:"required" zh:"执行结束时间"
  string result = 6; // @tag form:"result" validate:"required" zh:"任务执行结果"
  string created_at = 7; // @tag form:"created_at" validate:"required" zh:"创建时间"
  string updated_at = 8; // @tag form:"updated_at" validate:"required" zh:"更新时间"
}

message ListCronRequest {
  uint64 user_id = 2; // @tag form:"user_id" validate:"omitempty,number" zh:"用户ID"
  string name = 3; // @tag form:"name" validate:"omitempty" zh:"定时任务名称"
  int32 status = 4; // @tag form:"status" validate:"omitempty,number" zh:"任务状态"
  int32 type = 5; // @tag form:"type" validate:"omitempty,number,oneof=1" zh:"任务类型"
  string spec = 6; // @tag form:"spec" validate:"omitempty" zh:"定时参数"
  string method = 7; // @tag form:"method" validate:"omitempty" zh:"调用方法"
  string params = 8; // @tag form:"params" validate:"omitempty" zh:"任务参数"
  repeated string created_at = 9; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
  repeated string updated_at = 10; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
  uint32 page = 11;//@tag form:"page" zh:"页数"
  uint32 per_page = 12;//@tag form:"per_page" zh:"条数"
}

message ListCronResponse {
  int64 total = 1;
  int64 current_page = 2; //@tag form:"page" validate:"omitempty,number"
  uint32 page = 3; //@tag form:"page"  validate:"omitempty,number"
  uint32 per_page = 4; //@tag form:"per_page"  validate:"omitempty,number"
  repeated CronInfo items =  5;
}

message ListCronHistoryRequest {
  uint64 cron_id = 1; // @tag form:"cron_id" validate:"omitempty,number" zh:"任务ID"
  int32 status = 2; // @tag form:"status" validate:"omitempty,number" zh:"执行状态"
  repeated string start_at = 3; // @tag form:"start_at" validate:"omitempty,dive" zh:"开始执行时间"
  repeated string end_at = 4; // @tag form:"end_at" validate:"omitempty,dive" zh:"执行结束时间"
  uint32 page = 5;//@tag validate:"required,number" form:"page" zh:"页数"
  uint32 per_page = 6;//@tag validate:"required,number" form:"per_page" zh:"条数"
}

message ListCronHistoryResponse {
  int64 total = 1;
  int64 current_page = 2; //@tag form:"page"
  uint32 page = 3; //@tag form:"page"
  uint32 per_page = 4; //@tag form:"per_page"
  repeated CronHistoryInfo items =  5;
}

message AddCronRequest {
  uint64 user_id = 2; // @tag form:"user_id" validate:"omitempty,number" zh:"用户ID"
  string name = 3; // @tag form:"name" validate:"required" zh:"定时任务名称"
  int32 status = 4; // @tag form:"status" validate:"omitempty,number,oneof=0 1" zh:"任务状态"
  int32 type = 5; // @tag form:"type" validate:"required,number,oneof=1" zh:"任务类型"
  string spec = 6; // @tag form:"spec" validate:"required" zh:"定时参数"
  string method = 7; // @tag form:"method" validate:"required" zh:"调用方法"
  string params = 8; // @tag form:"params" validate:"omitempty" zh:"任务参数"
}

message AddCronResponse{
  bool status = 1;
  uint64 id = 2;
}



