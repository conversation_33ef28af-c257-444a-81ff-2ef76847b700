package crontab

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"io"
	"micro-service/middleware/mysql"
	mcron "micro-service/middleware/mysql/cron"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// SyncUrlHausThreatIntelligence 同步URLHaus威胁情报
func (j *Job) SyncUrlHausThreatIntelligence(_ *Cron, _ *mcron.Cron) string {
	newCount, reCount := 0, 0
	requst, _ := http.NewRequest(http.MethodGet, "https://urlhaus.abuse.ch/downloads/json_recent/", nil)
	httpClient := http.Client{
		Timeout: 60 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:    100,
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	resp, err := httpClient.Do(requst)
	if err != nil {
		return "情报中心的风险情报模块-同步失败，连接风险情报网站，创建client失败:" + err.Error()
	}
	result := make(map[string]any, 0)
	jsonStr, err := io.ReadAll(resp.Body)
	if err != nil {
		return "情报中心的风险情报模块-同步失败，读取body失败:" + err.Error()
	}
	if err = json.Unmarshal(jsonStr, &result); err != nil {
		return "情报中心的风险情报模块-同步失败，json解析失败:" + err.Error()
	}
	for _, val := range result {
		list := val.([]any)
		for x := range list {
			v := list[x].(map[string]any)
			if _, fErr := intelligence.NewThreat().First(mysql.WithWhere("url", v["url"])); fErr != nil {
				if !errors.Is(fErr, gorm.ErrRecordNotFound) {
					log.Warn(fmt.Sprintf("情报中心的风险情报模块-入库威胁信息失败,Info:%+v,Error:%v", v, fErr.Error()))
					continue
				}
				threatInfo := intelligence.Threat{
					Domain:  utils.GetTopDomain(v["url"].(string)),
					Status:  utils.If(v["url_status"].(string) == "offline", 2, 1),
					FoundAt: utils.TimeStringToGoTime(v["dateadded"].(string)),
					Url:     v["url"].(string),
					Source:  "URLhaus",
					Ip:      getIpByUrl(v["url"].(string)),
					Type:    v["threat"].(string),
					Tags:    "",
				}
				if v["tags"] != nil {
					threatInfo.Tags = strings.Join(utils.ListDistinctNonZero(utils.ListColumn(v["tags"].([]any), func(t any) string { return t.(string) })), ",")
				}
				if utils.ListContains([]string{"page.link", "google.com", "github.com", "dropbox.com", "vk.com", "discordapp.com", "autodesk360.com"}, threatInfo.Domain) {
					log.Warn(fmt.Sprintf("情报中心的风险情报模块-过滤指定域名数据,Info:%+v", v))
					continue
				}
				if err = intelligence.NewThreat().Create(&threatInfo); err != nil {
					log.Warn(fmt.Sprintf("情报中心的风险情报模块-入库威胁信息失败,Info:%+v,Error:%v", v, err.Error()))
					continue
				}
				newCount += 1
			} else {
				reCount += 1
			}
		}
	}
	return fmt.Sprintf("情报中心的风险情报模块-同步成功,新增:%d个,重复:%d个", newCount, reCount)
}

func getIpByUrl(urls string) string {
	if domain := utils.GetSubdomain(urls); domain != "" {
		ips, err := net.LookupIP(domain)
		if err != nil {
			return ""
		}
		if len(ips) > 0 {
			return ips[0].String()
		}
		return ""
	} else {
		u, err := url.Parse(urls)
		if err != nil {
			return ""
		}
		return u.Hostname()
	}
}
