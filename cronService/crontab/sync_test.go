package crontab

import (
	"fmt"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"testing"
	"time"

	"github.com/joho/godotenv"
)

func Init() {
	godotenv.Load("./../../../.env")

	cfg.InitLoadCfg()
	// _ = es.GetInstance(cfg.LoadElastic())
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	log.Init()
}

func TestSync(t *testing.T) {
	Init()
	fmt.Println(utils.SubDayTime(time.Duration(cfg.LoadCommon().ClueCacheDay)))
	//job := Job{}
	//fmt.Println(job.SyncOpenPhishFakeIntelligence(nil, nil))
}

func TestSyncUpdateDataIntelligence(t *testing.T) {
	Init()
	job := Job{}
	msg := job.SyncUpdateDataIntelligence(nil, nil)
	if msg != "数据泄露信息同步完成" {
		t.Error("SyncUpdateDataIntelligence error")
	}
}

func TestSyncUpdateEventIntelligence(t *testing.T) {
	Init()
	job := Job{}
	msg := job.SyncUpdateEventIntelligence(nil, nil)
	if msg != "执行成功" {
		t.Error("SyncUpdateEventIntelligence error")
	}
}
