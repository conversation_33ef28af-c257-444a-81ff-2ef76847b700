package crontab

import (
	"context"
	"crypto/tls"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-playground/validator/v10"
	"github.com/tidwall/gjson"
	"io"
	"micro-service/coreService/handler/fofa"
	corePb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	mcron "micro-service/middleware/mysql/cron"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"net/http"
	"strings"
	"time"

	"bufio"

	"github.com/antchfx/htmlquery"
	"gorm.io/gorm"
)

func detectSeparator(line string) rune {
	separators := []rune{'\t', ',', ';', '|'}
	maxCount := 0
	selectedSep := '\t' // 默认制表符

	for _, sep := range separators {
		count := strings.Count(line, string(sep))
		if count > maxCount {
			maxCount = count
			selectedSep = sep
		}
	}
	return selectedSep
}

// SyncOpenPhishFakeIntelligence 同步PhishTank钓鱼仿冒
func (j *Job) SyncOpenPhishFakeIntelligence(_ *Cron, _ *mcron.Cron) string {
	// 创建HTTP客户端
	httpClient := http.Client{
		Timeout: 300 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:    100,
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	newCount, repeatCount := 0, 0

	// 获取截止时间
	lastFound, err := intelligence.NewFake().GetLastFoundDate()
	if err != nil {
		log.Warnf("SyncOpenPhishFakeIntelligence-获取上次更新时间失败: %v", err)
		lastFound = time.Now().Add(-24 * time.Hour)
	}

	log.Infof("SyncOpenPhishFakeIntelligence-上次更新时间: %s", lastFound.Format(time.DateTime))

	// 读取Json数据
	respJson, err := httpClient.Get("https://gh-proxy.com/raw.githubusercontent.com/Sky-ey/mirror-phishtank/refs/heads/main/hosts.json")
	defer respJson.Body.Close()
	if err != nil {
		log.Error("SyncOpenPhishFakeIntelligence-Json数据获取失败:" + err.Error())
	}

	// 检查响应状态
	if respJson.StatusCode != http.StatusOK {
		log.Error("SyncOpenPhishFakeIntelligence-HTTP错误状态码:" + fmt.Sprintf("%d", respJson.StatusCode))
	}

	if err == nil && respJson.StatusCode == http.StatusOK {
		err, n, r := OpenPhishFakeIntelligenceJsonReader(respJson.Body, lastFound)
		if err != nil {
			log.Error("SyncOpenPhishFakeIntelligence-Json同步失败:" + err.Error())
		} else {
			newCount += n
			repeatCount += r
		}
	}

	// 获取截止时间
	lastFound, err = intelligence.NewFake().GetLastFoundDate()
	if err != nil {
		log.Warnf("SyncOpenPhishFakeIntelligence-获取上次更新时间失败: %v", err)
		lastFound = time.Now().Add(-24 * time.Hour)
	}

	// 获取CSV数据，两者都读求最新
	resp, err := httpClient.Get("https://gh-proxy.com/raw.githubusercontent.com/Sky-ey/mirror-phishtank/refs/heads/main/hosts.csv")
	defer resp.Body.Close()
	if err != nil {
		log.Error("SyncOpenPhishFakeIntelligence-CSV数据获取失败:" + err.Error())
	}

	// 检查响应状态
	if err == nil && resp.StatusCode != http.StatusOK {
		log.Error("SyncOpenPhishFakeIntelligence-HTTP错误状态码:" + fmt.Sprintf("%d", resp.StatusCode))
	}

	if err == nil && resp.StatusCode == http.StatusOK {
		err, n, r := OpenPhishFakeIntelligenceCSVReader(resp.Body, lastFound)
		if err != nil {
			log.Error("SyncOpenPhishFakeIntelligence-CSV同步失败:" + err.Error())
		} else {
			newCount += n
			repeatCount += r
		}
	}

	return fmt.Sprintf("PhishTank钓鱼仿冒数据同步完成, 新增: %d, 重复: %d", newCount, repeatCount)
}

// OpenPhishFakeIntelligenceCSVReader 读取PhishTank钓鱼仿冒数据 - CSV数据
func OpenPhishFakeIntelligenceCSVReader(reader io.Reader, latest time.Time) (err error, add, repeat int) {
	newCount, reCount := 0, 0
	// 创建带缓冲的读取器
	bufferedReader := bufio.NewReader(reader)

	// 读取第一行来检测分隔符
	firstLine, err := bufferedReader.ReadString('\n')
	if err != nil && err != io.EOF {
		return errors.New("读取CSV第一行失败:" + err.Error()), 0, 0
	}

	// 创建CSV Reader，使用检测到的分隔符
	csvReader := csv.NewReader(bufferedReader)
	if len(firstLine) > 0 {
		csvReader.Comma = detectSeparator(firstLine)
	}
	csvReader.LazyQuotes = true

	// 读取并跳过标题行
	if _, err := csvReader.Read(); err != nil {
		return errors.New("读取CSV标题失败:" + err.Error()), 0, 0
	}

	// 读取所有记录到切片
	var records [][]string
	for {
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Warn("CSV读取失败 - " + err.Error())
			if errors.Is(err, context.DeadlineExceeded) {
				break
			}
			continue
		}
		// 解析提交时间
		submissionTime, err := time.Parse(time.RFC3339, record[3])
		if err != nil {
			log.Warn(fmt.Sprintf("时间解析失败: %s - %v", record[3], err))
			continue
		}
		// 过滤旧数据
		if submissionTime.Before(latest) {
			break
		}
		records = append(records, record)
	}

	log.Infof("CSV数据读取完成, 记录数: %d", len(records))

	// 反向迭代
	for i := len(records) - 1; i >= 0; i-- {
		record := records[i]
		fmt.Printf("%+v\n", record)

		// 校验字段数量
		if len(record) < 7 {
			log.Warn(fmt.Sprintf("无效记录字段数: %d", len(record)))
			continue
		}

		// 解析提交时间
		submissionTime, err := time.Parse(time.RFC3339, record[3])
		if err != nil {
			log.Warn(fmt.Sprintf("时间解析失败: %s - %v", record[3], err))
			continue
		}

		title, _, _ := getWebSiteInfo(record[1])
		// 构建基础信息
		fake := intelligence.Fake{
			Title:     title, // 通过后续获取
			Country:   "",
			CloudName: "",
			Status:    utils.If(record[6] == "yes", 1, 2),
			Ip:        "", // 需要后续解析
			FoundAt:   submissionTime,
			Url:       record[1],
			Source:    "phishtank",
			Target:    record[7],
		}

		// 获取网站信息
		if title, isOnline, err := getWebSiteInfo(record[1]); err == nil {
			fake.Title = strings.TrimSpace(title)
			fake.Status = utils.If(isOnline, 1, 2)
		} else {
			log.Warn(fmt.Sprintf("获取网站信息失败: %s - %v", record[1], err))
		}

		// 解析IP地址（从URL获取）
		fake.Ip = getIpByUrl(record[1])

		// 补充云厂商信息
		if !cfg.IsLocalClient() && fake.Ip != "" {
			rsp := corePb.FofaQueryResponse{}
			req := corePb.FofaQueryRequest{
				Qbase64: fmt.Sprintf(`ip="%s"`, fake.Ip),
				Page:    1,
				Size:    1,
				Field:   []string{"cloud_name", "country"},
			}
			if fErr := fofa.FofaQuery(context.TODO(), &rsp, &req); fErr == nil {
				if len(rsp.Sdata) > 0 {
					if rsp.Sdata[0].Country != nil {
						fake.Country = *rsp.Sdata[0].Country
					}
					if rsp.Sdata[0].CloudName != nil {
						fake.CloudName = *rsp.Sdata[0].CloudName
					}
				}
			}
		}

		// 数据库操作
		if _, dErr := intelligence.NewFake().First(mysql.WithWhere("url", record[1])); dErr != nil {
			if !errors.Is(dErr, gorm.ErrRecordNotFound) {
				log.Warn(fmt.Sprintf("数据库查询失败: %+v - %v", fake, dErr))
				continue
			}
			if err = intelligence.NewFake().Create(&fake); err != nil {
				log.Warn(fmt.Sprintf("记录创建失败: %v", fake))
				continue
			}
			newCount++
		} else {
			reCount++
		}
	}

	return nil, newCount, reCount
}

// OpenPhishFakeIntelligenceJsonReader 读取PhishTank钓鱼仿冒数据 - Json数据
func OpenPhishFakeIntelligenceJsonReader(reader io.Reader, latest time.Time) (err error, add, repeat int) {
	newCount, reCount := 0, 0
	type PhishFakeData struct {
		PhishId        int       `json:"phish_id" validate:"required"`
		Url            string    `json:"url" validate:"required"`
		SubmissionTime time.Time `json:"submission_time" validate:"required"`
		Online         string    `json:"online" validate:"required"`
		Target         string    `json:"target" validate:"required"`
	}

	raw, err := io.ReadAll(reader)
	if err != nil {
		return errors.New("读取Json数据失败:" + err.Error()), 0, 0
	}

	var dataRow []PhishFakeData
	// Json迭代器
	result := gjson.ParseBytes(raw)
	result.ForEach(func(key, value gjson.Result) bool {
		var data PhishFakeData
		err = json.Unmarshal([]byte(value.Raw), &data)
		if err != nil {
			log.Warn("Json解析失败 - " + err.Error())
			return true
		}
		// 需要添加的数据为数据库最新日期之后的数据
		if data.SubmissionTime.Before(latest) {
			return false
		}
		dataRow = append(dataRow, data)
		return true
	})

	log.Infof("Json数据读取完成, 记录数: %d", len(dataRow))

	// 初始化验证器
	var validate = validator.New()

	// 逐行处理记录
	for i := len(dataRow) - 1; i >= 0; i-- {
		record := dataRow[i]
		fmt.Printf("%+v\n", record)

		// 校验字段
		if err = validate.Struct(&record); err != nil {
			log.Warn(fmt.Sprintf("记录无效: %s", err.Error()))
			continue
		}

		// 过滤旧数据
		if record.SubmissionTime.Before(latest) {
			continue
		}

		title, _, _ := getWebSiteInfo(record.Url)
		// 构建基础信息
		fake := intelligence.Fake{
			Title:     title, // 通过后续获取
			Country:   "",
			CloudName: "",
			Status:    utils.If(record.Online == "yes", 1, 2),
			Ip:        "", // 需要后续解析
			FoundAt:   record.SubmissionTime,
			Url:       record.Url,
			Source:    "phishtank",
			Target:    record.Target,
		}

		// 获取网站信息
		if title, isOnline, err := getWebSiteInfo(record.Url); err == nil {
			fake.Title = strings.TrimSpace(title)
			fake.Status = utils.If(isOnline, 1, 2)
		} else {
			log.Warn(fmt.Sprintf("获取网站信息失败: %s - %v", record.Url, err))
		}

		// 解析IP地址（从URL获取）
		fake.Ip = getIpByUrl(record.Url)

		// 补充云厂商信息
		if !cfg.IsLocalClient() && fake.Ip != "" {
			rsp := corePb.FofaQueryResponse{}
			req := corePb.FofaQueryRequest{
				Qbase64: fmt.Sprintf(`ip="%s"`, fake.Ip),
				Page:    1,
				Size:    1,
				Field:   []string{"cloud_name", "country"},
			}
			if fErr := fofa.FofaQuery(context.TODO(), &rsp, &req); fErr == nil {
				if len(rsp.Sdata) > 0 {
					if rsp.Sdata[0].Country != nil {
						fake.Country = *rsp.Sdata[0].Country
					}
					if rsp.Sdata[0].CloudName != nil {
						fake.CloudName = *rsp.Sdata[0].CloudName
					}
				}
			}
		}

		// 数据库操作
		if _, dErr := intelligence.NewFake().First(mysql.WithWhere("url", record.Url)); dErr != nil {
			if !errors.Is(dErr, gorm.ErrRecordNotFound) {
				log.Warn(fmt.Sprintf("数据库查询失败: %+v - %v", fake, dErr))
				continue
			}
			if err = intelligence.NewFake().Create(&fake); err != nil {
				log.Warn(fmt.Sprintf("记录创建失败: %v", fake))
				continue
			}
			newCount++
		} else {
			reCount++
		}
	}

	return nil, newCount, reCount
}

// getWebSiteInfo 和 onlineByCode 函数保持原样

func getWebSiteInfo(urls string) (string, bool, error) {
	title := ""
	isOnline := false
	requst, err := http.NewRequest(http.MethodGet, urls, nil)
	if err != nil {
		return title, false, err
	}
	httpClient := http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 5 {
				return fmt.Errorf("stopped after %d redirects", 5)
			}
			return nil
		},
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:    100,
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	resp, err := httpClient.Do(requst)
	if err != nil {
		return title, false, err
	}
	isOnline = onlineByCode(resp.StatusCode)
	bodyStr, err := io.ReadAll(resp.Body)
	if err != nil {
		return title, isOnline, err
	}
	node, err := htmlquery.Parse(strings.NewReader(string(bodyStr)))
	if err != nil {
		return title, isOnline, err
	}
	if titleNode, tErr := htmlquery.Query(node, "//title"); tErr == nil {
		if titleNode != nil {
			title = htmlquery.InnerText(titleNode)
		}
	}
	return title, isOnline, nil
}

func onlineByCode(code int) bool {
	return http.StatusOK <= code && code < http.StatusBadRequest
}
