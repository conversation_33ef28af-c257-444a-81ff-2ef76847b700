package crontab

import (
	"micro-service/pkg/cfg"
	"reflect"
	"testing"
	"time"
)

func TestGetEventFromAPI(t *testing.T) {
	cfg.InitLoadCfg()
	eventData, err := getEventDataFromAPI()
	if err != nil {
		t.<PERSON><PERSON><PERSON>("getDataFromAPI failed: %v", err)
		return
	}

	if eventData.Data == nil || len(eventData.Data) < 1 {
		t.<PERSON><PERSON><PERSON>("Data is nil")
	}

	if eventData.Data[1].IP == nil || len(eventData.Data[1].IP) < 1 {
		t.Errorf("IP is nil")
	}

	if eventData.Data[2].PDF == nil || len(eventData.Data[2].PDF) < 1 {
		t.<PERSON><PERSON><PERSON>("PDF is nil")
	}
}

func TestDownloadIntelligenceFile(t *testing.T) {
	cfg.InitLoadCfg()

	downloadLink := "/api/download/事件专项/IP/中国/APT-C-23(双尾蝎) Android端-IP.xlsx"
	result, err := downloadIntelligenceFile(downloadLink)

	if err != nil {
		t.<PERSON><PERSON><PERSON>("downloadIntelligenceFile failed: %v", err)
	}

	if len(result) < 1 {
		t.Errorf("result is nil")
	}
}

func TestRemoveRepByLoop(t *testing.T) {
	testCases := []struct {
		slc    []string
		expect []string
	}{
		{[]string{"apple", "banana", "apple", "orange", "banana", "grape"}, []string{"apple", "banana", "orange", "grape"}},
		{[]string{"apple", "banana", "apple"}, []string{"apple", "banana"}},
		{[]string{"apple"}, []string{"apple"}},
		{[]string{}, []string{}},
		{[]string{"apple", "apple", "apple"}, []string{"apple"}},
	}

	for _, tc := range testCases {
		result := RemoveRepByLoop(tc.slc)
		if !reflect.DeepEqual(result, tc.expect) {
			t.Errorf("Expected %v, but got %v for input %v", tc.expect, result, tc.slc)
		}
	}
}

func TestFloat64ToTime(t *testing.T) {
	// 测试正常情况
	f := 1708396843.643092
	want := time.Unix(1708396843, 643092000)
	got := float64ToTime(f)
	if want.Sub(got).Milliseconds() > 1 {
		t.Errorf("want: %v, got: %v", want, got)
	}

	// 测试边界情况
	f = 0
	want = time.Unix(0, 0)
	got = float64ToTime(f)
	if want != got {
		t.Errorf("want: %v, got: %v", want, got)
	}
}
