package crontab

import (
	"context"
	"github.com/spf13/cast"
	"strconv"
	"time"

	"micro-service/middleware/mysql"
	apicounter "micro-service/middleware/mysql/api_counter"
	"micro-service/middleware/mysql/auth_access_client"
	mcron "micro-service/middleware/mysql/cron"
	"micro-service/middleware/redis/client_counter"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

func (j *Job) SyncClientReqCount(_ *Cron, _ *mcron.Cron) string {
	client := client_counter.NewClientCounterModel()
	keys, err := client.Keys(context.Background())
	if err != nil {
		log.Errorf("[Client request api counter] Get all cache key failed: %v", err)
		return "任务执行失败:" + err.Error()
	}
	if len(keys) == 0 {
		return "任务执行完成: 请求数据为空"
	}

	for _, key := range keys {
		time.Sleep(500 * time.Millisecond)
		go clientReqCountWrite(key)
	}
	return "任务执行完成,同步Key总数:" + cast.ToString(len(keys))
}

func clientReqCountWrite(key string) {
	clientId := client_counter.GetClientId(key)
	if clientId == "" {
		return
	}

	info, err := auth_access_client.NewAuthAccessClientModel().First(
		mysql.WithColumnValue("client_id", clientId), mysql.WithSelect("user_id"))
	if err != nil {
		log.Errorf("[Client request api counter] Get client_id: %s info: %v", clientId, err)
		return
	}

	userId, _ := strconv.Atoi(info.UserId)
	m, err := client_counter.NewClientCounterModel().Get(context.Background(), key)
	if err != nil {
		log.Errorf("[Client request api counter] Get cache key: %s value: %v", key, err)
		return
	}

	list, err := apicounter.NewCounter().ListAll(
		mysql.WithColumnValue("client_id", clientId),
		mysql.WithColumnValue("year", time.Now().Year()),
		mysql.WithColumnValue("month", time.Now().Month()),
	)
	if err != nil {
		log.Errorf("[Client request api counter] Get client_id: %s counter list in mysql: %v", clientId, err)
		return
	}

	insertList := make([]*apicounter.ApiCount, 0, len(m))
	for api, count := range m {
		var item = apicounter.ApiCount{}
		isInsert := true
		isUpdate := false
		for i := range list {
			if list[i].Path != api {
				continue
			}
			isInsert = false
			if list[i].Total == uint64(count) {
				continue
			}
			item = *list[i]
			item.UserId = utils.If(item.UserId == 0, uint64(userId), item.UserId)
			item.Total = uint64(count)
			isUpdate = true
			isInsert = false
			break
		}

		if isInsert {
			item = apicounter.ApiCount{
				ClientId: clientId,
				UserId:   uint64(userId),
				Year:     uint64(time.Now().Year()),
				Month:    uint64(time.Now().Month()),
				Total:    uint64(count),
				Path:     api,
			}
		}

		if isUpdate || isInsert {
			insertList = append(insertList, &item)
		}
	}

	err = apicounter.NewCounter().Upsert(insertList)
	if err != nil {
		log.Errorf("[Client request api counter] Client_id: %s counter list write mysql failed: %v", clientId, err)
	}
}
