package crontab

import (
	"fmt"
	"reflect"
	"sync"
	"time"

	"github.com/rfyiamcool/cronlib"
	"github.com/spf13/cast"

	"micro-service/middleware/mysql"
	mcron "micro-service/middleware/mysql/cron"
	"micro-service/pkg/log"

	cronmodel "micro-service/middleware/mysql/cron"
)

var once sync.Once
var cron *Cron
var jobOnce sync.Once
var jobs *Job

type Cron struct {
	Scheduler *cronlib.CronSchduler
}
type Job struct {
}

func GetJobsInstance() *Job {
	if jobs == nil {
		jobOnce.Do(func() { jobs = &Job{} })
	}
	return jobs
}

func GetCronInstance() *Cron {
	if cron == nil {
		once.Do(func() {
			cron = &Cron{
				cronlib.New(),
			}
		})
	}
	return cron
}

func (c *Cron) StopJob(serviceName string) {
	c.Scheduler.StopService(serviceName)
}

// LoadMysqlJob 加载MysqlJob
func (c *Cron) LoadMysqlJob() error {
	if list, _, err := mcron.NewCronModel().List(0, 0, mysql.WithWhere("status", mcron.StatusEnable)); err != nil {
		log.Warnf("获取数据定时任务失败:%s", err.Error())
		return fmt.Errorf("获取数据定时任务失败:%s", err.Error())
	} else {
		for i := range list {
			cronTab := list[i]
			fmt.Println("------------", cronTab)
			// 获取任务执行方法
			method, ok := reflect.TypeOf(GetJobsInstance()).MethodByName(cronTab.Method)
			if !ok {
				// 更新任务信息
				cronTab.Running = mcron.RunningException
				cronTab.Reason = "未找到Job执行对象:" + cronTab.Method
				_ = mcron.NewCronModel().Save(&cronTab)
				log.Warnf("未找到Job执行对象:%v", cronTab)
				continue
			}
			if job, nJobErr := cronlib.NewJobModel(cronTab.Spec, c.GetRunJobFunc(&cronTab, &method)); nJobErr != nil {
				// 创建Job失败
				cronTab.Running = mcron.RunningException
				cronTab.Reason = "生成Job失败:" + nJobErr.Error()
				_ = mcron.NewCronModel().Save(&cronTab)
				log.Warnf("生成Job失败:%v", cronTab)
			} else {
				if _, srvErr := c.Scheduler.GetServiceCron(cast.ToString(cronTab.UserId) + ":" + cronTab.Method); srvErr != nil {
					//  不存在任务,注册Job
					fmt.Println("不存在任务,注册Job")
					c.InsertJobModel(&cronTab, job)
				} else {
					// 存在任务,更新Job
					fmt.Println("存在任务,更新Job")
					c.UpdateJobModel(&cronTab, job)
				}
			}
		}
	}
	return nil
}

// GetRunJobFunc 生成Job方法
func (c *Cron) GetRunJobFunc(cronTab *mcron.Cron, method *reflect.Method) func() {
	var runLock sync.Mutex
	return func() {
		runLock.Lock()
		defer runLock.Unlock()
		var result []reflect.Value
		startNow := time.Now()
		defer func() {
			// 捕捉失败任务
			if reErr := recover(); reErr != nil {
				mcron.NewHistoryModel().Create(&mcron.History{
					CronId: cronTab.Id, StartAt: startNow, EndAt: time.Now(), Status: mcron.StatusFail, Result: fmt.Sprintf("%v", reErr),
				})
				log.Infof("任务执行失败,返回结果:%v", reErr)
			} else {
				// 添加任务执行记录
				mcron.NewHistoryModel().Create(&mcron.History{
					CronId: cronTab.Id, StartAt: startNow, EndAt: time.Now(), Status: mcron.StatusSuccess, Result: fmt.Sprintf("%v", result),
				})
			}
			// 标记任务未执行
			cronItem := mcron.Cron{Running: mcron.RunningNo}
			cronItem.Id = cronTab.Id
			_ = mcron.NewCronModel().Updates(cronItem, mysql.WithSelect("running"))
			log.Infof("任务执行结束:" + cronTab.Name)
		}()

		log.Infof("开始执行任务:" + cronTab.Name)
		// 标记任务执行中
		cronItem := mcron.Cron{Running: mcron.RunningYes}
		cronItem.Id = cronTab.Id
		_ = mcron.NewCronModel().Updates(cronItem)
		// 调用执行任务
		result = method.Func.Call([]reflect.Value{reflect.ValueOf(GetJobsInstance()), reflect.ValueOf(c), reflect.ValueOf(cronTab)})
		log.Infof("任务执行成功,返回结果:", result)
	}
}

// InsertJobModel 新增JobModel
func (c *Cron) InsertJobModel(cronTab *mcron.Cron, job *cronlib.JobModel) bool {
	//  不存在任务,注册Job
	if err := c.Scheduler.DynamicRegister(cast.ToString(cronTab.UserId)+":"+cronTab.Method, job); err != nil {
		cronTab.Running = mcron.RunningException
		cronTab.Reason = "注册Job失败:" + err.Error()
		_ = mcron.NewCronModel().Save(cronTab)
		log.Warnf("注册Job失败:%v", cronTab)
		return false
	}
	cronTab.Reason = "注册Job成功"
	cronTab.Running = mcron.RunningNo
	_ = mcron.NewCronModel().Save(cronTab)
	log.Infof("注册Job成功:%v", cronTab)
	return true
}

// UpdateJobModel 更新JobModel
func (c *Cron) UpdateJobModel(cronTab *mcron.Cron, job *cronlib.JobModel) bool {
	// 存在任务,更新Job
	if uErr := c.Scheduler.UpdateJobModel(cast.ToString(cronTab.UserId)+":"+cronTab.Method, job); uErr != nil {
		cronTab.Running = mcron.RunningException
		cronTab.Reason = "更新Job失败:" + uErr.Error()
		_ = mcron.NewCronModel().Save(cronTab)
		log.Warnf("更新Job失败:%v", cronTab)
		return false
	}
	cronTab.Reason = "更新Job成功"
	cronTab.Running = mcron.RunningNo
	_ = mcron.NewCronModel().Save(cronTab)
	log.Infof("更新Job成功:%v", cronTab)
	return true
}

// Start 启动定时任务服务
func (c *Cron) Start() error {
	// 开始执行
	c.Scheduler.Start()
	// 判断是否存在定时任务SyncUpdatePocIntelligence
	TaskUpdatePocCheck()
	time.Sleep(time.Second * 1)
	// 加载数据库Job
	if err := c.LoadMysqlJob(); err != nil {
		return err
	}
	// 等待执行
	c.Scheduler.Wait()
	return nil
}

func TaskUpdatePocCheck() {
	// 判断定时任务 SyncUpdatePocIntelligence 是否存在 ，不存在则添加到cron数据表
	res, err := cronmodel.NewCronModel().First(mysql.WithColumnValue("name", "同步网站漏洞的最新漏洞信息"))
	if err != nil {
		log.Warn("查询 cron数据库表异常")
	}
	if res == nil {
		err := cronmodel.NewCronModel().Create(&mcron.Cron{
			Name:    "同步网站漏洞的最新漏洞信息",
			UserId:  1,
			Status:  1,
			Type:    1,
			Spec:    "0 0 9 * * *", // 秒 分 时 日 月 年
			Method:  "SyncUpdatePocIntelligence",
			Params:  "",
			Running: mcron.RunningYes,
			Reason:  "",
		})
		if err != nil {
			return
		}
	}
}
