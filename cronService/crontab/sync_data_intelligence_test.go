package crontab

import (
	"micro-service/pkg/cfg"
	"testing"
)

func TestGetDataFromAPI(t *testing.T) {
	cfg.InitLoadCfg()
	eventData, err := getEventDataFromAPI()
	if err != nil {
		t.<PERSON><PERSON><PERSON>("getDataFromAPI failed: %v", err)
		return
	}

	if eventData.Data == nil || len(eventData.Data) < 1 {
		t.<PERSON><PERSON><PERSON>("Data is nil")
	}

	if eventData.Data[0].Summary == nil || len(eventData.Data[0].Summary) < 1 {
		t.<PERSON>rrorf("Summary is nil")
	}

	if eventData.Data[2].PDF == nil || len(eventData.Data[2].PDF) < 1 {
		t.<PERSON><PERSON><PERSON>("PDF is nil")
	}
}
