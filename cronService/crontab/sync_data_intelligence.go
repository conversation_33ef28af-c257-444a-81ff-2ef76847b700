package crontab

import (
	"encoding/json"
	"fmt"
	"io"
	mcron "micro-service/middleware/mysql/cron"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/tealeg/xlsx"
)

func (j *Job) SyncUpdateDataIntelligence(_ *Cron, _ *mcron.Cron) string {
	// 1、从API查询
	leakData, err := getDataFromAPI()
	if err != nil {
		log.Error("【定时任务-SyncUpdateDataIntelligence】数据泄露接口请求失败:\n", err)
		return fmt.Sprintf("数据泄露接口请求失败:%v", err.Error())
	}
	if len(leakData.Data) > 0 && len(leakData.Data[0].Summary) > 0 {
		var summaryFile = leakData.Data[0].Summary[0]
		//根据汇总文件hash删除旧数据
		err := intelligence.NewData().DeleteBySummaryFileHash(summaryFile.Hash)
		if err != nil {
			log.Error("【定时任务-SyncUpdateDataIntelligence】删除已有数据泄露信息失败:\n", err)
			return fmt.Sprintf("删除已有数据泄露信息失败:%v", err.Error())
		}
		//解析汇总文件
		fileContent, err := downloadIntelligenceFile(summaryFile.DownloadLink)
		if err != nil {
			log.Error("【定时任务-SyncUpdateDataIntelligence】下载文件失败:\n", err)
			return fmt.Sprintf("下载文件失败:%v", err.Error())
		}
		summaryList, dataList, err := parseDataSummaryExcel(fileContent, leakData.Data[1].PDF)
		if err != nil {
			log.Error("【定时任务-SyncUpdateDataIntelligence】解析数据泄露汇总Excel文件失败:\n", err)
			return fmt.Sprintf("解析数据泄露汇总Excel文件失败:%v", err.Error())
		}

		// 确保数据泄露汇总信息中的资产数量和企业数量不因为更新而丢失
		existDsNames := make([]string, 0)
		for _, s := range summaryList {
			ds, err := intelligence.NewDataSummary().GetByName(s.SpecialProjectName)
			if err != nil {
				log.Error("【定时任务-SyncUpdateDataIntelligence】查询数据泄露汇总信息失败:\n", err)
				continue
			}
			if ds != nil {
				s.AssetNum = ds.AssetNum
				s.CompanyNum = ds.CompanyNum
				existDsNames = append(existDsNames, s.SpecialProjectName)
			}
		}
		// 删除旧数据
		intelligence.NewDataSummary().DeleteByNames(existDsNames)
		// 批量插入新数据
		err = intelligence.NewDataSummary().BatchSave(summaryList)
		if err != nil {
			log.Error("【定时任务-SyncUpdateDataIntelligence】数据泄露汇总信息入库失败:\n", err)
			return fmt.Sprintf("数据泄露汇总信息入库失败:%v", err.Error())
		}
		// 详细数据入库
		var dataBatch []*intelligence.Data
		for i, d := range dataList {
			//补充汇总文件hash字段值
			d.SummaryFileHash = summaryFile.Hash
			//保存PDF报告
			if d.Report.Hash != "" {
				//下载PDF报告
				pdfFileContent, err := downloadIntelligenceFile(d.Report.DownloadLink)
				if err != nil {
					log.Error("【定时任务-SyncUpdateDataIntelligence】下载PDF报告失败:\n", err)
				} else {
					//文件保存到本地
					reportPath := storage.SaveIntelligenceReport(pdfFileContent, d.Report.Filename)
					if reportPath == "" {
						log.Error("【定时任务-SyncUpdateDataIntelligence】保存PDF报告失败:\n", err)
					} else {
						d.Report.LocalLink = reportPath
					}
				}
			}

			dataBatch = append(dataBatch, d)
			if i != 0 && i%50 == 0 {
				err = intelligence.NewData().Save(dataBatch)
				dataBatch = []*intelligence.Data{}
				if err != nil {
					log.Error("【定时任务-SyncUpdateDataIntelligence】插入数据泄露信息失败:\n", err)
					continue
				}
			} else {
				if i == len(dataList)-1 {
					err = intelligence.NewData().Save(dataBatch)
					dataBatch = nil
					if err != nil {
						log.Error("【定时任务-SyncUpdateDataIntelligence】插入数据泄露信息失败:\n", err)
						continue
					}
				}
			}
		}
		return "数据泄露信息同步完成"
	}
	return "数据泄露信息没有数据需要同步"
}

// 从API查询数据
func getDataFromAPI() (*IntelliResponseRoot, error) {
	//http://106.75.48.83:63137/api/data?key=yE3okcG2but9FekLtZ6GORORicttjGAwbsoOofWr&keyword=中华人民共和国
	// 拼接URL
	urlStr := fmt.Sprintf("%s/api/data?key=%s&keyword=%s", getDomain(), getKey(), url.QueryEscape("中华人民共和国"))
	// 发送HTTP GET请求
	resp, err := http.Get(urlStr)
	if err != nil {
		log.Warn("【定时任务-SyncUpdateDataIntelligence】数据泄露接口请求失败:\n", err)
		return nil, err
	}
	defer resp.Body.Close()

	//读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response body:", err)
		return nil, err
	}
	//解析接口响应内容
	var eventData IntelliResponseRoot
	if err = json.Unmarshal(body, &eventData); err != nil {
		log.Warn("【定时任务-SyncUpdateDataIntelligence】解析接口响应内容失败:\n", err)
		return nil, err
	}
	return &eventData, nil
}

// 解析数据泄露汇总excel
func parseDataSummaryExcel(fileContent []byte, pdfList []File) ([]*intelligence.DataSummary, []*intelligence.Data, error) {
	file, err := xlsx.OpenBinary(fileContent)
	if err != nil {
		log.Warn("【定时任务-SyncUpdateDataIntelligence】解析数据泄露汇总Excel文件失败:\n", err)
		return nil, nil, err
	}
	// 汇总信息
	summaryList := make([]*intelligence.DataSummary, 0)
	// 数据详情
	var dataList []*intelligence.Data
	sheet := file.Sheets[0]

	for i := 1; i < len(sheet.Rows); i++ {
		row := sheet.Rows[i]
		var d = &intelligence.Data{}
		d.SpecialProjectName = row.Cells[0].String()
		d.EventName = row.Cells[1].String()
		d.SpecialProjectCategory = row.Cells[2].String()
		d.Overview = row.Cells[3].String()
		d.DisclosureTime, _ = getTimeFromExcelCell(row.Cells[4])
		d.LeakSourceIP = row.Cells[5].String()
		d.IPRelatedLocation = row.Cells[6].String()
		d.LeakServiceComponent = row.Cells[7].String()
		d.DataVolume, err = row.Cells[8].Int()
		if err != nil {
			dataVolumeStr := strings.Replace(row.Cells[8].String(), ",", "", -1)
			d.DataVolume, _ = strconv.Atoi(dataVolumeStr)
		}
		d.DataContent = row.Cells[9].String()
		d.DataEntity = row.Cells[10].String()
		d.DataEntityLocation = row.Cells[11].String()
		d.LeakReason = row.Cells[12].String()
		d.FixSolution = row.Cells[13].String()
		//组装报告信息
		//从pdfList中根据文件名找到pdf报告
		var pdfFile *File
		for _, pdf := range pdfList {
			if pdf.Filename == fmt.Sprintf("%s.pdf", row.Cells[1].String()) {
				pdfFile = &pdf
				break
			}
		}
		if pdfFile != nil {
			t := float64ToTime(pdfFile.CreationTime)
			d.Report.Filename = pdfFile.Filename
			d.Report.CreationTime = &t
			d.Report.DownloadLink = pdfFile.DownloadLink
			d.Report.Tags = pdfFile.Tags
			d.Report.Hash = pdfFile.Hash
		}
		dataList = append(dataList, d)

		// 收集汇总信息
		var ds = &intelligence.DataSummary{
			CompanyNum: 0,
			AssetNum:   0,
		}
		existDs := false
		// 汇总信息中是否已存在该专项
		for _, v := range summaryList {
			if v.SpecialProjectName == row.Cells[0].String() {
				existDs = true
				ds = v
				break
			}
		}
		ds.SpecialProjectName = d.SpecialProjectName
		if ds.Entities == "" {
			ds.Entities = d.DataEntity
		} else {
			ds.Entities = fmt.Sprintf("%s,%s", ds.Entities, d.DataEntity)
		}
		ds.DataVolume += d.DataVolume
		if d.DisclosureTime != nil && (ds.LastUpdateTime == nil || d.DisclosureTime.After(*ds.LastUpdateTime)) {
			ds.LastUpdateTime = d.DisclosureTime
		}
		if !existDs {
			summaryList = append(summaryList, ds)
		}
	}
	return summaryList, dataList, nil
}
