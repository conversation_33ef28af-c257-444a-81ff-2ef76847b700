package crontab

import (
	"context"
	"encoding/json"
	"time"

	mcron "micro-service/middleware/mysql/cron"
	"micro-service/pkg/log"
	"micro-service/webService/handler/asset_account/domain_assets"
	web "micro-service/webService/proto"
)

func (j *Job) DomainAssetsCronParse(_ *Cron, item *mcron.Cron) string {
	var param domain_assets.CronParam
	_ = json.Unmarshal([]byte(item.Params), &param)
	if param.UserId == 0 {
		return "用ID为0,不执行任务"
	}

	req := &web.DomainAssetsUpdateByCronRequest{
		UserId:        param.UserId,
		OperateUserId: int64(param.OperateUserId),
		Update:        true,
		CallByCron:    true,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Hour)
	defer cancel()
	_, err := web.GetProtoClient().DomainAssetUpdateByCron(ctx, req)
	if err != nil {
		log.Errorf("[定时任务] 域名资产定时解析任务执行失败, user_id: %d, %v", param.UserId, err)
		return "任务执行失败:" + err.Error()
	}
	return "任务执行完成"
}
