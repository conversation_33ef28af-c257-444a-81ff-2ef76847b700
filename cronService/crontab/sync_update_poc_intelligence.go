package crontab

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/tealeg/xlsx"

	"micro-service/coreService/handler/fofa"
	corePb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	mcron "micro-service/middleware/mysql/cron"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"micro-service/scanService/handler/microkernel"
)

var pdfInfo *[]File

func (j *Job) SyncUpdatePocIntelligence(_ *Cron, _ *mcron.Cron) string {
	// step1 下载consul文件的url地址的excel
	err := downloadPocSummaryExcel()
	if err != nil {
		log.Warn("下载文件异常")
		return fmt.Sprintf("下载文件异常:%v", err.Error())
	}
	// 创建通道保存汇总文件解析结果
	pocChan := make(chan *xlsx.Row, 10)
	// step2 解析excel文件
	var wg sync.WaitGroup
	var once sync.Once
	go parseSummaryExcel(pocChan, &wg) //子过程：1、解析名字的CVE编号 2、获取FOFACount的调用结果
	// step3 将数据录入数据库
	go storeToDb(pocChan, &wg, &once)

	wg.Wait()
	return "执行成功"
}

// downloadPocSummaryExcel 下载汇总文件
func downloadPocSummaryExcel() error {
	common := cfg.GetInstance().Common //读取consul的配置文件
	//http://106.75.48.83:63137/api/download/漏洞专项///漏洞专项汇总.xlsx?key=yE3okcG2but9FekLtZ6GORORicttjGAwbsoOofWr
	url := common.CronUrl.PocUpdateUrl
	// 发送HTTP GET请求
	resp, err := http.Get(url)
	if err != nil {
		log.Warn("【定时任务-SyncUpdatePocIntelligence】下载Excel文件失败:\n", err)
		return err
	}
	defer resp.Body.Close()

	//filePath := "./static/漏洞专项汇总.xlsx "
	// 检查文件是否存在
	filePath := excelPath()
	if _, err = os.Stat(filePath); err == nil {
		log.Warn("【定时任务-SyncUpdatePocIntelligence】文件 %s 存在，将删除并重新下载\n", filePath)
		// 删除文件
		if err = os.Remove(filePath); err != nil {
			log.Warn("【定时任务-SyncUpdatePocIntelligence】文件删除失败:\n", err)
			return err
		}
		log.Warn("文件 %s 删除成功\n", filePath)
	}

	// 创建一个文件用于保存Excel文件
	out, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer func(out *os.File) {
		err = out.Close()
		if err != nil {
			log.Warn("█ █ █ - - - - - - -,Excel文件关闭失败 ")
		}
	}(out)

	// 将响应的数据复制到文件
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return err
	}

	log.Info("█ █ █ - - - - - - -,Excel文件已保存为 漏洞专项汇总.xlsx ")
	return err
}

// parseSummaryExcel step2 解析excel文件
func parseSummaryExcel(pocChan chan *xlsx.Row, wg *sync.WaitGroup) []intelligence.HotPoc {
	xlFile, err := xlsx.OpenFile(excelPath())
	if err != nil {
		log.Info("█ █ █ - - - - - - -,接口下载漏洞文件异常，无法打开Excel文件:", err)
		return nil
	}
	var pocs []intelligence.HotPoc
	// 遍历所有工作表
	for _, sheet := range xlFile.Sheets {
		// 遍历所有行
		head := true
		for _, row := range sheet.Rows {
			if head { //
				head = false
				continue
			}
			pocChan <- row
			wg.Add(1)
		}
	}
	// debug 使用
	// for i := 0; i < len(pocs); i++ {
	//  	fmt.Println(pocs[i].Name)
	//	 //fmt.Println(vulners[i].CveNumber)
	// }
	log.Info("█ █ █ █ █ █ - - - -,数据解析完成，共", len(pocs), "条数据")
	return pocs
}

// storeToDb step3 将数据录入数据库
func storeToDb(pocChan chan *xlsx.Row, wg *sync.WaitGroup, once *sync.Once) {
	for row := range pocChan {
		defer func() {
			wg.Done()
		}()
		pocName := row.Cells[0].String()
		res, _ := intelligence.NewHotPoc().First(mysql.WithColumnValue("name", pocName))
		if res.Id == 0 { // 如果查不到数据，创建数据库内容
			poc := intelligence.HotPoc{}
			poc.Name = row.Cells[0].String()                  // 漏洞名称
			poc.Cve = utils.ExtractCve(row.Cells[0].String()) // 提取cve
			// = row.Cells[1].String()   // 专项分类
			poc.RiskLevel = row.Cells[2].String() //风险级别
			// = row.Cells[3].String()   //漏洞类型
			poc.ImpactProduct = row.Cells[4].String()                                               //关联设备
			poc.Introduce = row.Cells[5].String()                                                   //概述
			poc.Solution = row.Cells[6].String()                                                    //修复建议
			poc.FofaQuery = row.Cells[7].String()                                                   //FOFA语句
			discloseTime, _ := time.Parse("2006-01-02 15:04:05", row.Cells[8].String()+" 00:00:00") //timeStr := dateStr + " 00:00:00"
			poc.FofaCount = uint64(getCountByFOFAQuery(poc.FofaQuery))
			//poc.FofaCount = 2          // 测试接口使用 mock：uint64(getFOFAQueryCount(poc.FofaQuery))
			poc.FoundAt = discloseTime // 披露时间

			if poc.FofaQuery != "" {
				//查询FOFA语句
				//url encode & base64
				search := url.QueryEscape(poc.FofaQuery)
				search = base64.StdEncoding.EncodeToString([]byte(search))
				fofaParse, err := microkernel.GetFOFAParse(search)
				if err != nil {
					log.Warn("【定时任务-SyncUpdatePocIntelligence】查询FOFA语句异常:\n", err)
				}
				poc.ESQuery = fofaParse.Query
				poc.ESIndex = strings.Join(fofaParse.Index, ",")
				poc.ESKeywords = strings.Join(fofaParse.Keywords, ",")
			}

			report, err := parsePocReport(pocName, once)
			if err != nil {
				log.Warn("【定时任务-SyncUpdatePocIntelligence】存储PDF报告失败:\n", err)
			}
			poc.Report = *report

			err = intelligence.NewHotPoc().Create(&poc)
			if err != nil {
				log.Warnf("【定时任务-SyncUpdatePocIntelligence】创建数据库内容异常,poc name:%s,error:%v\n", pocName, err.Error())
			}
		}
	}
}

// getPdfInfoFromAPI 从API获取PDF信息
func getPdfInfoFromAPI() (*IntelliResponseRoot, error) {
	common := cfg.GetInstance().Common
	apiUrl := fmt.Sprintf("%s/api/vul?keyword=%s&key=%s", common.CronUrl.EventUpdateUrl, url.QueryEscape("中华人民共和国"), common.CronUrl.EventUpdateUrlToken)
	// 发送HTTP GET请求
	resp, err := http.Get(apiUrl)
	if err != nil {
		log.Warn("【定时任务-SyncUpdatePocIntelligence】下载PDF信息失败:\n", err)
		return nil, err
	}
	defer resp.Body.Close()
	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warn("【定时任务-SyncUpdatePocIntelligence】读取PDF信息失败:\n", err)
		return nil, err
	}
	// 反序列化响应内容
	var data IntelliResponseRoot
	if err = json.Unmarshal(body, &data); err != nil {
		log.Warn("【定时任务-SyncUpdatePocIntelligence】解析接口响应内容失败:\n", err)
		return nil, err
	}
	if data.Data == nil || len(data.Data) < 3 {
		log.Warn("【定时任务-SyncUpdatePocIntelligence】接口返回数据异常")
		return nil, fmt.Errorf("接口返回数据异常")
	}
	// 给全局变量赋值
	pdfInfo = &data.Data[2].PDF
	return &data, nil
}

// parsePocReport 解析PDF报告并返回报告实体
func parsePocReport(pocName string, once *sync.Once) (*intelligence.Report, error) {
	once.Do(func() {
		// 触发到保存报告说明有新数据，此时才需要查询pdf信息，而且要确保进查询一次
		getPdfInfoFromAPI()
	})
	pdfName := fmt.Sprintf("%s.pdf", pocName)
	common := cfg.GetInstance().Common
	download_link := fmt.Sprintf("%s/api/download/漏洞专项/PDF/%s?key=%s", common.CronUrl.EventUpdateUrl, pdfName, common.CronUrl.EventUpdateUrlToken)
	// 发送HTTP GET请求
	resp, err := http.Get(download_link)
	if err != nil {
		log.Warn("【定时任务-SyncUpdatePocIntelligence】下载PDF文件失败:\n", err)
		return nil, err
	}
	defer resp.Body.Close()
	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warn("【定时任务-SyncUpdatePocIntelligence】读取PDF文件失败:\n", err)
		return nil, err
	}
	//存储PDF文件
	pdfPath := storage.SaveIntelligenceReport(body, pdfName)
	if pdfPath == "" {
		log.Error("【定时任务-SyncUpdatePocIntelligence】存储PDF报告失败", err)
	}

	report := intelligence.Report{
		Filename:  pdfName,
		LocalLink: pdfPath,
	}
	// 查找pdf信息
	if pdfInfo != nil {
		for _, v := range *pdfInfo {
			if v.Filename == pdfName {
				report.Hash = v.Hash
				t := float64ToTime(v.CreationTime)
				report.CreationTime = &t
			}
		}
	}
	return &report, nil
}

// getCountByFOFAQuery 根据FOFA语句查询总数
func getCountByFOFAQuery(query string) uint32 {
	rsp := corePb.FofaQueryCountResponse{}
	req := corePb.FofaQueryCountRequest{
		Qbase64:          query,
		Full:             true,
		CanGetLimitCount: false,
	}
	if fErr := fofa.FofaQueryCount(context.TODO(), &rsp, &req); fErr != nil {
		log.Warn("█ █ █ █ █ █ - - - -查询异常")
	}
	log.Info("█ █ █ █ █ █ - - - -,查询语句的总数为", rsp.Count)
	return rsp.Count
}

// excelPath 获取excel文件路径
func excelPath() string {
	//1 获取当前所在目录
	//2 检测当前目录下是否存在static文件夹
	//3 如果不存在，创建文件夹
	//4 存在则 根据文件夹路径拼接excel的地址
	dir, err := os.Getwd()
	if err != nil {
		fmt.Println("无法获取当前工作目录:", err)
		return ""
	}

	folderName := dir + "/static"
	// 使用 Stat 函数检查文件夹是否存在
	_, err = os.Stat(folderName)
	// 如果文件夹不存在
	if os.IsNotExist(err) {
		// 使用 Mkdir 函数创建文件夹
		err := os.Mkdir(folderName, 0755)
		if err != nil {
			log.Warn("【定时任务-SyncUpdatePocIntelligence】创建文件夹失败:", err)
		}
		log.Info("【定时任务-SyncUpdatePocIntelligence】文件夹创建成功")
	}

	return dir + "/static/漏洞专项汇总.xlsx"
}
