package crontab

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	mysql2 "micro-service/middleware/mysql"
	mcron "micro-service/middleware/mysql/cron"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/tealeg/xlsx"
)

type IntelliResponseRoot struct {
	Data []DataItem `json:"data"`
}
type DataItem struct {
	Summary []File `json:"/"`
	IP      []File `json:"IP"`
	PDF     []File `json:"PDF"`
}

type File struct {
	CreationTime float64 `json:"creation_time"`
	DownloadLink string  `json:"download_link"`
	Filename     string  `json:"filename"`
	Hash         string  `json:"hash"`
	Tags         string  `json:"tags"`
}

func (j *Job) SyncUpdateEventIntelligence(_ *<PERSON>ron, _ *mcron.Cron) string {
	// 1、从API查询
	eventData, err := getEventDataFromAPI()
	if err != nil {
		log.Error("【定时任务-SyncUpdateEventIntelligence】事件专项接口请求失败:\n", err)
		return fmt.Sprintf("事件专项接口请求失败:%v", err.Error())
	}
	// 2、下载文件
	// 2.1 下载汇总文件
	if len(eventData.Data) > 0 && len(eventData.Data[0].Summary) > 0 {
		downloadLink := eventData.Data[0].Summary[0].DownloadLink
		fileContent, err := downloadIntelligenceFile(downloadLink)
		if err != nil {
			log.Error("【定时任务-SyncUpdateEventIntelligence】下载汇总文件失败:\n", err)
			return fmt.Sprintf("下载汇总文件失败:%v", err.Error())
		}
		// 2.2 解析汇总文件
		summaryChan := make(chan intelligence.Event)
		go parseEventSummaryExcel(fileContent, summaryChan)
		// if err != nil {
		// 	log.Error("【定时任务-SyncUpdateEventIntelligence】解析汇总文件失败:\n", err)
		// 	return fmt.Sprintf("解析汇总文件失败:%v", err.Error())
		// }

		for event := range summaryChan {
			// event := *e
			// 遍历IP下的文件元数据
			for _, file := range eventData.Data[1].IP {
				if !strings.HasPrefix(file.Filename, event.Name) {
					continue
				}
				// 判断文件是否已经存在，如果存在判断是否需要更新
				existEvent, err := intelligence.NewEvent().IsExist(mysql2.WithColumnValue("hash", file.Hash))
				if err != nil {
					log.Error("【定时任务-SyncUpdateEventIntelligence】查询已有事件专项信息失败:\n", err)
					continue
				} else {
					if existEvent {
						log.Info("【定时任务-SyncUpdateEventIntelligence】事件专项信息已存在:\n", existEvent)
						continue
					}
				}
				eventName, hasReport := strings.CutSuffix(file.Filename, "-IP.xlsx")
				// 2、补充事件数据
				event.CreationTime = float64ToTime(file.CreationTime)
				event.DownloadLink = file.DownloadLink
				event.Filename = file.Filename
				event.Hash = file.Hash
				event.Category = "事件专项"
				if hasReport {
					event.Name = eventName
				}
				// 3、下载事件详情文件
				fileContent, err := downloadIntelligenceFile(file.DownloadLink)
				if err != nil {
					log.Error("【定时任务-SyncUpdateEventIntelligence】下载文件失败:\n", err)
					continue
				}
				// 4、解析事件详情文件
				eventDetail, ipCount, err := parseEventExcel(fileContent)
				if err != nil {
					log.Error("【定时任务-SyncUpdateEventIntelligence】解析文件失败:\n", err)
					continue
				}
				if len(eventDetail) <= 0 {
					log.Warn("【定时任务-SyncUpdateEventIntelligence】解析文件中不存在数据:\n")
					continue
				}

				// 保存事件对应的PDF报告
				if len(eventData.Data[2].PDF) > 0 {
					if hasReport {
						for _, pdf := range eventData.Data[2].PDF {
							if pdf.Filename == fmt.Sprintf("%s.pdf", eventName) {
								// 下载PDF文件
								fileContent, err := downloadIntelligenceFile(pdf.DownloadLink)
								if err != nil {
									log.Error("【定时任务-SyncUpdateEventIntelligence】下载PDF文件失败:\n", err)
								} else {
									// 存储PDF文件
									pdfPath := storage.SaveIntelligenceReport(fileContent, pdf.Filename)
									if pdfPath == "" {
										log.Error("【定时任务-SyncUpdateEventIntelligence】存储PDF报告失败", err)
									}
									// 更新事件专项信息
									event.LocalLink = pdfPath
								}
								break
							}
						}
					}
				}

				// 把事件详情中的IP数量赋值到事件中
				event.IpCount = ipCount
				// 5、存储事件数据
				err = intelligence.NewEvent().Create(&event)
				if err != nil {
					log.Error("【定时任务-SyncUpdateEventIntelligence】事件专项信息保存失败:\n", err)
					continue
				}

				var eventDetailBatch []*intelligence.EventDetail
				// 把事件ID赋值给事件详情
				for i, d := range eventDetail {
					// 补充事件ID
					d.EventId = uint(event.ID)

					eventDetailBatch = append(eventDetailBatch, d)
					// 每50条数据触发一次保存
					if i != 0 && i%50 == 0 {
						// 6、存储事件详情
						err = intelligence.NewEventDetail().Save(eventDetailBatch)
						eventDetailBatch = []*intelligence.EventDetail{}
						if err != nil {
							log.Error("【定时任务-SyncUpdateEventIntelligence】事件专项详情信息保存失败:\n", err)
							continue
						}
					} else {
						// 最后一个批次不到50条也要保存一下
						if i == len(eventDetail)-1 {
							// 6、存储事件详情
							err = intelligence.NewEventDetail().Save(eventDetailBatch)
							eventDetailBatch = nil
							if err != nil {
								log.Error("【定时任务-SyncUpdateEventIntelligence】事件专项详情信息保存失败:\n", err)
								continue
							}
						}
					}
				}
			}
		}
	}
	return "执行成功"
}

func getDomain() string {
	common := cfg.GetInstance().Common
	domain := common.CronUrl.EventUpdateUrl
	if domain == "" {
		return "http://106.75.48.83:63137"
	}
	return domain
}
func getKey() string {
	common := cfg.GetInstance().Common
	key := common.CronUrl.EventUpdateUrlToken
	if key == "" {
		return "yE3okcG2but9FekLtZ6GORORicttjGAwbsoOofWr"
	}
	return key
}

// 从API查询数据
func getEventDataFromAPI() (*IntelliResponseRoot, error) {
	//http://106.75.48.83:63137/api/event?key=yE3okcG2but9FekLtZ6GORORicttjGAwbsoOofWr&keyword=中华人民共和国
	// 拼接URL
	urlStr := fmt.Sprintf("%s/api/event?key=%s&keyword=%s", getDomain(), getKey(), url.QueryEscape("中华人民共和国"))
	// 发送HTTP GET请求
	resp, err := http.Get(urlStr)
	if err != nil {
		log.Warn("【定时任务-SyncUpdateEventIntelligence】请求事件专项失败:\n", err)
		return nil, err
	}
	defer resp.Body.Close()

	//读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response body:", err)
		return nil, err
	}
	//解析接口响应内容
	var eventData IntelliResponseRoot
	if err = json.Unmarshal(body, &eventData); err != nil {
		log.Warn("【定时任务-SyncUpdateEventIntelligence】解析接口响应内容失败:\n", err)
		return nil, err
	}
	return &eventData, nil
}

// 下载文件
func downloadIntelligenceFile(downloadLink string) ([]byte, error) {
	//http://106.75.48.83:63137/api/download/filename.xlsx?key=yE3okcG2but9FekLtZ6GORORicttjGAwbsoOofWr
	// 拼接URL
	downloadLinkurl := fmt.Sprintf("%s%s?key=%s", getDomain(), downloadLink, getKey())
	// 发送HTTP GET请求ll
	resp, err := http.Get(downloadLinkurl)
	if err != nil {
		log.Warn("【定时任务-SyncUpdateEventIntelligence/SyncUpdateDataIntelligence】通过API下载文件失败:\n", err)
		return nil, err
	}
	defer resp.Body.Close()
	//读取响应内容
	body, _ := io.ReadAll(resp.Body)
	return body, nil
}

// 解析Excel文件
// 返回事件详情数据和IP数量
func parseEventExcel(fileContent []byte) ([]*intelligence.EventDetail, int64, error) {
	file, err := xlsx.OpenBinary(fileContent)
	if err != nil {
		log.Warn("【定时任务-SyncUpdateEventIntelligence】解析Excel文件失败:\n", err)
		return nil, 0, err
	}
	var eventDetail []*intelligence.EventDetail
	sheet := file.Sheets[0]
	var ipList = make([]string, len(sheet.Rows)-1)

	for i := 1; i < len(sheet.Rows); i++ {
		row := sheet.Rows[i]
		//收集IP数据
		ipList[i-1] = row.Cells[7].String()
		//组装事件详情数据
		var e intelligence.EventDetail
		e.RelatedDevice = row.Cells[0].String()
		e.Vulnerability = row.Cells[1].String()
		e.RiskLevel = row.Cells[2].String()
		e.RiskType = row.Cells[3].String()
		e.IsVulnerable = row.Cells[4].String() == "是"
		e.FirstDetected, _ = getTimeFromExcelCell(row.Cells[5])
		e.LastDetected, _ = getTimeFromExcelCell(row.Cells[6])
		e.IPAddress = row.Cells[7].String()
		e.Port = row.Cells[8].String()
		e.Protocol = row.Cells[9].String()
		e.Country = row.Cells[10].String()
		e.City = row.Cells[11].String()
		e.URL = row.Cells[12].String()
		e.Case = row.Cells[13].String()
		e.Object = row.Cells[14].String()
		e.IsCDN = row.Cells[15].String() == "是"
		e.Tags = row.Cells[16].String()
		e.OS = row.Cells[17].String()
		e.StatusCode = row.Cells[18].String()
		e.Title = row.Cells[19].String()
		e.Domain = row.Cells[20].String()
		e.Certificate = row.Cells[21].String()
		e.Organization = row.Cells[22].String()
		e.Institution = row.Cells[23].String()
		e.Component = row.Cells[24].String()
		e.Category = row.Cells[25].String()
		e.Icon = row.Cells[26].String()
		e.Fingerprint = row.Cells[27].String()
		e.AssetCount = row.Cells[28].String()
		e.VulnAssetCnt = row.Cells[29].String()
		e.LastUpdated, _ = getTimeFromExcelCell(row.Cells[30])
		eventDetail = append(eventDetail, &e)
	}

	//IP数据去重
	ipList = RemoveRepByLoop(ipList)

	return eventDetail, int64(len(ipList)), nil
}

// 解析事件专项汇总文件
func parseEventSummaryExcel(fileContent []byte, ch chan intelligence.Event) ([]intelligence.Event, error) {
	file, err := xlsx.OpenBinary(fileContent)
	if err != nil {
		log.Warn("【定时任务-SyncUpdateEventIntelligence】解析汇总Excel文件失败:\n", err)
		return nil, err
	}
	var eventList []intelligence.Event
	sheet := file.Sheets[0]
	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		event := intelligence.Event{}
		event.Name = row.Cells[0].String()
		event.Tags = row.Cells[1].String()
		dt, err := getTimeFromExcelCell(row.Cells[2])
		if err == nil {
			event.DisclosureTime = dt
		}
		event.Summary = row.Cells[4].String()
		event.FofaQuery = row.Cells[5].String()
		eventList = append(eventList, event)
		ch <- event
	}
	close(ch)
	return eventList, nil
}

func getTimeFromExcelCell(cell *xlsx.Cell) (*time.Time, error) {
	if cell.Value == "" {
		return nil, errors.New("Excel cell is empty")
	}

	c := strings.Replace(cell.Value, "'", "", -1)
	t, err := time.Parse("2006-01-02", c)
	if err != nil {
		return nil, nil
	}
	return &t, err
}

func float64ToTime(f float64) time.Time {
	// 获取整数部分和小数部分
	seconds := int64(f)
	nanoseconds := int64((f - float64(seconds)) * 1e9)

	// 根据整数部分和小数部分构造 time.Time 对象
	t := time.Unix(seconds, nanoseconds)

	return t
}

// 通过两重循环过滤重复元素，slice元素数量小于1024时推荐使用，大于1024时使用map
func RemoveRepByLoop(slc []string) []string {
	result := []string{} // 存放结果
	for i := range slc {
		flag := true
		for j := range result {
			if slc[i] == result[j] {
				flag = false // 存在重复元素，标识为false
				break
			}
		}
		if flag { // 标识为false，不添加进结果
			result = append(result, slc[i])
		}
	}
	return result
}
