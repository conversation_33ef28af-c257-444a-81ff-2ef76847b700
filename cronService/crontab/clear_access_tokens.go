package crontab

import (
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/auth_access_token"
	mcron "micro-service/middleware/mysql/cron"
	"time"
)

// ClearAccessTokens 清理过期Token
func (j *Job) ClearAccessTokens(_ *Cron, _ *mcron.Cron) string {
	timeNow := time.Now().Format("2006-01-02 15:04:05")
	if err := auth_access_token.NewAuthAccessTokenModel().Delete(mysql.WithWhere("expired_at  <= ?", timeNow)); err == nil {
		return "清理" + timeNow + "之前过期的Token,Ok"
	} else {
		return "清理" + timeNow + "之前过期的Token,失败:" + err.Error()
	}
}
