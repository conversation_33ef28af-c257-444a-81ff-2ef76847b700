package main

import (
	"fmt"
	"micro-service/pkg/email"
	"time"

	microZap "github.com/go-micro/plugins/v4/logger/zap"
	ocplugin "github.com/go-micro/plugins/v4/wrapper/trace/opentracing"
	"github.com/hashicorp/go-hclog"
	"github.com/opentracing/opentracing-go"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"

	"micro-service/cronService/crontab"
	"micro-service/cronService/handler"
	pb "micro-service/cronService/proto"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/traces"
	"micro-service/pkg/utils"
)

// shouldInitializeEmailConfig 判断是否应该初始化邮件配置
// 只有在明确确认为SaaS环境且配置完整时才初始化
func shouldInitializeEmailConfig() bool {
	common := cfg.LoadCommon()

	// 如果明确配置为本地化环境，则跳过
	if common.Client.Platform == "local" {
		return false
	}

	// 如果Client配置不完整，可能是本地化环境配置缺失，采用保守策略
	if common.Client.ClientId == "" || common.Client.Secret == "" {
		log.Warn("[CRON] Client配置不完整，可能为本地化环境，跳过邮件配置初始化")
		return false
	}

	// 只有明确为SaaS环境且配置完整时才初始化
	return common.Client.Platform == "saas"
}

func onInit() {
	cfg.InitLoadCfg()
	// 初始化日志

	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// Mysql & Redis
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	_ = es.GetInstance(cfg.LoadElastic())
}

func main() {
	onInit()
	// jaeger 链路追踪
	if jaegerTrace, io, err := traces.GetJaegerTracer(pb.ServiceName, cfg.LoadCommon().JaegerAdder); err != nil {
		log.Errorf("Jaeger: %s Error:", cfg.LoadCommon().JaegerAdder, err.Error())
		panic(err)
	} else {
		defer io.Close()
		opentracing.SetGlobalTracer(jaegerTrace)
		log.Infof("Jaeger: %s Connection successful", cfg.LoadCommon().JaegerAdder)
	}
	consulReg := cfg.GetInstance().GetConsulReg()
	// Create service
	srv := micro.NewService(
		micro.Name(pb.ServiceName),
		micro.Version(pb.ServiceVersion),
		micro.RegisterTTL(10*time.Second),     // TTL指定从上一次心跳间隔起，超过这个时间服务会被服务发现移除
		micro.RegisterInterval(8*time.Second), // 让服务在指定时间内重新注册，保持TTL获取的注册时间有效
		micro.Logger(logger.DefaultLogger),
		micro.Address(utils.GetListenAddress(cfg.LoadCommon().Network)),
		micro.WrapHandler(ocplugin.NewHandlerWrapper(opentracing.GlobalTracer())),
		micro.WrapClient(ocplugin.NewClientWrapper(opentracing.GlobalTracer())),
	)
	srv.Init(micro.Registry(consulReg))

	// 启动定时任务
	go func() {
		// 检查是否正常加载定时任务,未正常加载时,抛出异常
		if err := crontab.GetCronInstance().Start(); err != nil {
			panic(err)
		}
	}()
	// Register handler - 所有环境都需要注册 handler
	if err := pb.RegisterCronHandler(srv.Server(), new(handler.Cron)); err != nil {
		logger.Fatal(err)
	}

	// 检查环境类型并初始化相应配置
	if shouldInitializeEmailConfig() {
		log.Info("[CRON] SaaS环境，开始初始化邮件相关配置")
		err := cfg.LoadCommonConfigFromConsul()
		if err != nil {
			log.Errorf("初始化配置失败: %v", err)
			log.Warn("[CRON] 邮件配置初始化失败，跳过邮件相关功能")
		} else {
			err = cfg.LoadDingtalkConfigFromConsul()
			if err != nil {
				log.Errorf("钉钉基础初始化配置失败:%v", err)
				log.Warn("[CRON] 钉钉配置初始化失败，跳过钉钉相关功能")
			} else {
				//email.WatchCommonConfig()
				email.AutoMispSchedulers.Restart()
				log.Info("[CRON] SaaS环境邮件配置初始化完成")
			}
		}
	} else {
		log.Info("[CRON] 本地化环境或配置不完整，跳过邮件相关配置")
	}
	if err := srv.Run(); err != nil {
		fmt.Printf("cronService srv.Run error: %v", err)
		logger.Fatal(err)
	}
}
