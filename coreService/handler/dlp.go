package handler

// 数据泄露总库

import (
	"context"
	"time"

	"go-micro.dev/v4/errors"

	"micro-service/coreService/handler/dlp"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

func (e *Core) DlpGitHubByCode(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubCodeResponse) error {
	cacheKey := "github:" + utils.Md5Hash(req)
	if len(req.Keyword) == 0 {
		return errors.New(pb.ServiceName, "Keyword不能为空!", 400)
	}
	if req.MaxTotal == 0 {
		req.MaxTotal = 10
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: DlpGitHubByCode -> request:%v,Cache:%s", req, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: DlpGitHubByCode -> request:%v,NoCache:%s", req, cacheKey)
	if err := dlp.GetGitHubClient().SearchCode(ctx, req.Keyword, rsp, int(req.MaxTotal)); err == nil {
		redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

// DlpGitHubTaskCreate 数据泄露总库GitHub-创建任务
func (e *Core) DlpGitHubTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Github] Received core.DlpGitHubTaskCreate param: %v", req)
	err := dlp.GithubSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core]dlp: GitHub search by task: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpGitHubTaskInfo 数据泄露总库GitHub-任务详情
func (e *Core) DlpGitHubTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Github] Received core.DlpGitHubTaskInfo param: %v", req)
	err := dlp.GithubTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core]dlp: Get GitHub task info: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpGitHubTaskResult 数据泄露总库GitHub-任务结果
func (e *Core) DlpGitHubTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubCodeResultResponse) error {
	log.WithContextInfof(ctx, "[Github] Received core.DlpGitHubTaskResult param: %v", req)
	l, err := dlp.GithubTaskResult(req.TaskId)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core]dlp: Get GitHub task result: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	rsp.Items = l
	return nil
}

func (e *Core) DlpGiteeByRepo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubCodeResponse) error {
	if len(req.Keyword) == 0 {
		return errors.BadRequest(pb.ServiceName, "查询关键字不可为空")
	}
	maxTotal := utils.If(req.MaxTotal > 0, int(req.MaxTotal), 10)
	if err := dlp.GetGiteeRepoSearch(ctx, req.Keyword, rsp, maxTotal); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpGiteeTaskCreate 数据泄露Gitee-创建任务
func (e *Core) DlpGiteeTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Gitee] Received core.DlpGiteeTaskCreate param: %v", req)
	err := dlp.GiteeSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core]dlp: GitHub search by task: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpGiteeTaskInfo 数据泄露Gitee-任务详情
func (e *Core) DlpGiteeTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Gitee] Received core.DlpGiteeTaskInfo param: %v", req)
	err := dlp.GiteeTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Gitee] Get Gitee task_id: %d info failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpGiteeTaskResult 数据泄露总库Gitee-任务结果
func (e *Core) DlpGiteeTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubCodeResultResponse) error {
	log.WithContextInfof(ctx, "[Gitee] Received core.DlpGiteeTaskResult param: %v", req)
	err := dlp.GiteeTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Gitee] Get Gitee task_id: %d result failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) Dlp56NetDisk(ctx context.Context, req *pb.NetDiskRequest, rsp *pb.NetDiskResponse) error {
	cacheKey := "56pan:" + utils.Md5Hash(req)
	if len(req.Keyword) == 0 {
		return errors.New(pb.ServiceName, "Keyword不能为空!", 400)
	}
	if req.MaxTotal == 0 {
		req.MaxTotal = 10
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: Dlp56NetDisk -> request:%v,Cache:%s", req, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: Dlp56NetDisk -> request:%v,NoCache:%s", req, cacheKey)
	if err := dlp.Search56pan(ctx, req.Keyword, rsp, int(req.MaxTotal)); err == nil {
		redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

// 百度文库

func (e *Core) DlpBaiduLibrary(ctx context.Context, req *pb.BaiduLibraryRequest, rsp *pb.BaiduLibraryResponse) error {
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "Keyword不能为空!", 400)
	}

	// 有缓存时取缓存数据
	cacheKey := "baidulibrary:" + utils.Md5Hash(req)
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: DlpBaiduLibrary -> request:%v,Cache:%s", req, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: DlpBaiduLibrary -> request:%v, NoCache:%s", req, cacheKey)
	if err := dlp.GetBaiduLibrary(ctx, rsp, req.Keyword, 3); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}

	redis.SetCache(cacheKey, 3*time.Minute, rsp)
	return nil
}

// DlpBaiduLibraryTaskCreate 数据泄露总库-百度文库-创建任务
func (e *Core) DlpBaiduLibraryTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[BaiduLibrary] Received core.DlpBaiduLibraryTaskCreate param: %v", req)
	err := dlp.BaiduLibrarySearch(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[BaiduLibrary] Baidu-Library search by task, keywords: %v failed: %v", req.Keyword, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpBaiduLibraryTaskInfo 数据泄露-百度文库-任务详情
func (e *Core) DlpBaiduLibraryTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[BaiduLibrary] received core.DlpBaiduLibraryTaskInfo param: %v", req)
	err := dlp.BaiduLibraryTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[BaiduLibrary] Get BaiduLibrary task_id: %d info failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpBaiduLibraryTaskResult 数据泄露-百度文库-任务结果
func (e *Core) DlpBaiduLibraryTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.BaiduLibraryResultResponse) error {
	log.WithContextInfof(ctx, "[BaiduLibrary] Received core.DlpBaiduLibraryTaskResult param: %v", req)
	err := dlp.BaiduLibraryTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[BaiduLibrary] Get dlp BaiduLibrary task_id: %d result failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// 盘搜搜网盘

func (e *Core) DlpPanSoSoNetDisk(ctx context.Context, req *pb.NetDiskRequest, rsp *pb.NetDiskResponse) error {
	cacheKey := "pansoso:" + utils.Md5Hash(req)
	if len(req.Keyword) == 0 {
		return errors.New(pb.ServiceName, "Keyword不能为空!", 400)
	}
	if req.MaxTotal == 0 {
		req.MaxTotal = 10
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: DlpPanSoSoNetDisk -> request:%v,Cache:%s", req, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: DlpPanSoSoNetDisk -> request:%v,NoCache:%s", req, cacheKey)
	if err := dlp.SearchPansoso(ctx, req.Keyword, rsp, int(req.MaxTotal)); err == nil {
		// redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

// DlpPanSosoTaskCreate 数据泄露总库-Pansoso-创建任务
func (e *Core) DlpPanSosoTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Pansoso] Received core.DlpPanSosoTaskCreate param: %+v", req)
	err := dlp.PansosoSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Pansoso] Pansoso search by task, keywords->%+v: %v", req.Keyword, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpPanSosoTaskInfo 数据泄露总库-PanSoso-任务详情
func (e *Core) DlpPanSosoTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Pansoso] Received core.DlpPanSosoTaskInfo param: %+v", req)
	err := dlp.PansosoTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Pansoso] Get Pansoso task_id: %d info failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpPanSosoTaskResult 数据泄露总库-PanSoso-任务结果
func (e *Core) DlpPanSosoTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.Wangpan56ResultResponse) error {
	log.WithContextInfof(ctx, "[Pansoso] Received core.DlpPanSosoTaskResult param: %+v", req)
	err := dlp.PansosoTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Pansoso] Get Pansoso task result: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) DlpDouin(ctx context.Context, req *pb.BaiduLibraryRequest, rsp *pb.BaiduLibraryResponse) error {
	cacheKey := "douin:" + utils.Md5Hash(req)
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "Keyword不能为空!", 400)
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: Douin -> request:%v,Cache:%s", req, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: Douin -> request:%v,NoCache:%s", req, cacheKey)
	if err := dlp.GetDouin(ctx, rsp, req.Keyword); err == nil {
		redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

// renrendoc 人人文档

// DlpRenrendocTaskCreate 数据泄露总库-Renrendoc-创建任务
func (e *Core) DlpRenrendocTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Renrendoc] Received core.DlpRenrendocTaskCreate param: %+v", req)
	err := dlp.RenrendocSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Renrendoc] Renrendoc search keywords: %v by task failed: %v", req.Keyword, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpRenrendocTaskInfo 数据泄露总库-人人文档任务详情
func (e *Core) DlpRenrendocTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Renrendoc] Received core.DlpRenrendocTaskInfo param: %+v", req)
	err := dlp.RenrendocTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Renrendoc]dlp: Get Renrendoc task info: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpRenrendocTaskResult 数据泄露总库-人人文档任务结果
func (e *Core) DlpRenrendocTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.DocinResultResponse) error {
	log.WithContextInfof(ctx, "[Renrendoc] Received core.DlpRenrendocTaskResult param: %+v", req)
	l, err := dlp.RenrendocTaskResult(req.TaskId)
	if err != nil {
		log.WithContextErrorf(ctx, "[Renrendoc]dlp: Get Renrendoc task result: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	rsp.Items = l
	return nil
}

// docin 豆丁文档

// DlpDocinTaskCreate 数据泄露总库-docin-创建任务
func (e *Core) DlpDocinTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Docin] Received core.DlpDocinTaskCreate param: %+v", req)
	err := dlp.DocinSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Docin] Docin search keywords: %v by task failed: %v", req.Keyword, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpDocinTaskInfo 数据泄露总库-豆丁任务详情
func (e *Core) DlpDocinTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Docin] Received core.DlpDocinTaskInfo param: %+v", req)
	err := dlp.DocinTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Docin]dlp: Get docin task info: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpDocinTaskResult 数据泄露总库-豆丁任务结果
func (e *Core) DlpDocinTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.DocinResultResponse) error {
	log.WithContextInfof(ctx, "[Docin] Received core.DlpDocinTaskResult param: %+v", req)
	l, err := dlp.DocinTaskResult(req.TaskId)
	if err != nil {
		log.WithContextErrorf(ctx, "[Docin]dlp: Get docin task result: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	rsp.Items = l
	return nil
}

func (e *Core) DlpYuque(_ context.Context, req *pb.YuequeLibraryRequest, rsp *pb.YuequeLibraryResponse) error {
	r, err := dlp.SearchYuque(req.Keyword, req.Page)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	rsp.Total = r.Total
	rsp.Data = r.Data
	return nil
}

// 道客巴巴

// DlpDoc88TaskCreate 总库 新建
func (e *Core) DlpDoc88TaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[doc88] Received core.DlpDoc88TaskCreate param: %v", req)
	err := dlp.Doc88TaskCreate(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[doc88] Dlp Doc88 Task create failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpDoc88TaskInfo 总库 任务详情
func (e *Core) DlpDoc88TaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[doc88] Received core.DlpDoc88TaskInfo param: %v", req)
	err := dlp.Doc88TaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[doc88] Get task_id: %d info failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpDoc88TaskResult 总库 任务结果
func (e *Core) DlpDoc88TaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.Doc88ResultResponse) error {
	log.WithContextInfof(ctx, "[doc88] Received core.DlpDoc88TaskResult param: %v", req)
	err := dlp.Doc88TaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[doc88] Get task_id: %d result failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// 56网盘

// Dlp56WangpanTaskCreate 数据泄露总库-56网盘-创建任务
func (e *Core) Dlp56WangpanTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[56wangpan] Received core.DlpDoc88TaskResult param: %v", req)
	err := dlp.Wangpan56SearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[56wangpan] 56wangpan search by task failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// Dlp56WangpanTaskInfo 数据泄露总库-56网盘-任务详情
func (e *Core) Dlp56WangpanTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[56wangpan] Received core.Dlp56WangpanTaskInfo param: %v", req)
	err := dlp.Wangpan56TaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[56wangpan] Get 56wangpan task_id: %d info: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// Dlp56WangpanTaskResult 数据泄露总库-56网盘-任务结果
func (e *Core) Dlp56WangpanTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.Wangpan56ResultResponse) error {
	log.WithContextInfof(ctx, "[56wangpan] Received core.Dlp56WangpanTaskResult param: %v", req)
	err := dlp.Wangpan56TaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[56wangpan] Get 56wangpan task_id: %d result: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpPostmanTaskCreate 数据泄露Postman-创建任务
func (e *Core) DlpPostmanTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Gitee] Received core.DlpPostmanTaskCreate param: %v", req)
	err := dlp.PostmanSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core]dlp: Postman search by task: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpPostmanTaskInfo 数据泄露Postman-任务详情
func (e *Core) DlpPostmanTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Postman] Received core.DlpPostmanTaskInfo param: %v", req)
	err := dlp.PostmanTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Postman] Get Postman task_id: %d info failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpPostmanTaskResult 数据泄露总库Postman-任务结果
func (e *Core) DlpPostmanTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubCodeResultResponse) error {
	log.WithContextInfof(ctx, "[Postman] Received core.DlpPostmanTaskResult param: %v", req)
	err := dlp.PostmanTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Postman] Get Postman task_id: %d result failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// 秒搜网盘

// DlpMiaosouTaskCreate 数据泄露总库-秒搜网盘-创建任务
func (e *Core) DlpMiaosouTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Miaosou] Received core.DlpMiaosouTaskCreate param: %v", req)
	err := dlp.WangpanMiaosouSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Miaosou] Miaosou search by task failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpMiaosouTaskInfo 数据泄露总库-秒搜网盘-任务详情
func (e *Core) DlpMiaosouTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Miaosou] Received core.DlpMiaosouTaskInfo param: %v", req)
	err := dlp.WangpanMiaosouTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Miaosou] Get Miaosou task_id: %d info: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpMiaosouTaskResult 数据泄露总库-秒搜网盘-任务结果
func (e *Core) DlpMiaosouTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.Wangpan56ResultResponse) error {
	log.WithContextInfof(ctx, "[Miaosou] Received core.DlpMiaosouTaskResult param: %v", req)
	err := dlp.WangpanMiaosouTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Miaosou] Get Miaosou task_id: %d result: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpMagicalSearchTaskCreate 数据泄露总库-奇妙搜索-创建任务
func (e *Core) DlpMagicalSearchTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {

	log.WithContextInfof(ctx, "[Magicalsearch] Received core.DlpMagicalSearchTaskCreate param: %v", req)
	err := dlp.MagicalsearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Magicalsearch] Magicalsearch search by task failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpMagicalSearchTaskInfo  数据泄露总库-奇妙搜索-任务详情
func (e *Core) DlpMagicalSearchTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {

	log.WithContextInfof(ctx, "[Magicalsearch] Received core.DlpMagicalSearchTaskInfo param: %v", req)
	err := dlp.MagicalsearchTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Magicalsearch] Get Magicalsearch task_id: %d info: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpMagicalSearchTaskResult 数据泄露总库-奇妙搜索-任务结果
func (e *Core) DlpMagicalSearchTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.Wangpan56ResultResponse) error {

	log.WithContextInfof(ctx, "[Magicalsearch] Received core.DlpMagicalSearchTaskResult param: %v", req)
	err := dlp.MagicalsearchTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Magicalsearch] Get Magicalsearch task_id: %d result: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpDashengpanTaskCreate 数据泄露总库-大圣盘-创建任务
func (e *Core) DlpDashengpanTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Dashengpan] Received core.DlpDashengpanTaskCreate param: %v", req)
	err := dlp.WangpanDashengpanByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Dashengpan] Dashengpan search by task failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpDashengpanTaskInfo 数据泄露总库-大圣盘-任务详情
func (e *Core) DlpDashengpanTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Dashengpan] Received core.DlpDashengpanTaskInfo param: %v", req)
	err := dlp.WangpanDashengpanTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Dashengpan] Get Dashengpan task_id: %d info: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpDashengpanTaskResult 数据泄露总库-大圣盘-任务结果
func (e *Core) DlpDashengpanTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.Wangpan56ResultResponse) error {
	log.WithContextInfof(ctx, "[Dashengpan] Received core.DlpDashengpanTaskResult param: %v", req)
	err := dlp.WangpanDashengpanTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Dashengpan] Get Dashengpan task_id: %d result: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpGitcodeTaskCreate 数据泄露Gitcode-创建任务
func (e *Core) DlpGitcodeTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Gitcode] Received core.DlpGitcodeTaskCreate param: %v", req)
	err := dlp.GitcodeSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core]dlp: Gitcode search by task: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpGitcodeTaskInfo 数据泄露Gitcode-任务详情
func (e *Core) DlpGitcodeTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Gitcode] Received core.DlpGitcodeTaskInfo param: %v", req)
	err := dlp.GitcodeTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Gitcode] Get Gitcode task_id: %d info failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpGitcodeTaskResult 数据泄露总库Gitcode-任务结果
func (e *Core) DlpGitcodeTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubCodeResultResponse) error {
	log.WithContextInfof(ctx, "[Gitcode] Received core.DlpGitcodeTaskResult param: %v", req)
	err := dlp.GitcodeTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Gitcode] Get Gitcode task_id: %d result failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}
