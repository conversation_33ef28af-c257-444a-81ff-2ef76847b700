package handler

import (
	"context"

	"micro-service/coreService/handler/clue"
	"micro-service/pkg/log"

	pb "micro-service/coreService/proto"

	"go-micro.dev/v4/errors"
)

// GetExpandResult 获取扩展结果
func (e *Core) GetExpandResult(ctx context.Context, req *pb.ExpandResultRequest, rsp *pb.ExpandResultResponse) error {
	return clue.GetExpandResult(ctx, req, rsp)
}

// SearchByCompanyName 根据企业名称查找线索
func (e *Core) SearchByCompanyName(ctx context.Context, req *pb.ExpandKeywordSearchRequest, rsp *pb.ExpandResultResponse) error {
	return clue.SearchByCompanyName(ctx, req, rsp)
}

// ExpandCompanyName 企业名称扩展线索
func (e *Core) ExpandCompanyName(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	return clue.ExpandCompanyName(ctx, req, rsp)
}

// ExpandIcp ICP扩展线索
func (e *Core) ExpandIcp(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	return clue.ExpandIcp(ctx, req, rsp)
}

// ExpandDomain 域名扩展线索
func (e *Core) ExpandDomain(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	return clue.ExpandDomain(ctx, req, rsp)
}

// ExpandSubDomain 子域名扩展线索
func (e *Core) ExpandSubDomain(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	return clue.ExpandSubDomain(ctx, req, rsp)
}

// ExpandKeyword 关键字扩展线索
func (e *Core) ExpandKeyword(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	return clue.ExpandKeyword(ctx, req, rsp)
}

// ExpandIp IP扩展线索
func (e *Core) ExpandIp(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	return clue.ExpandIp(ctx, req, rsp)
}

// ExpandCert 证书扩展线索
func (e *Core) ExpandCert(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	return clue.ExpandCert(ctx, req, rsp)
}

// ExpandIcon ICON扩展线索
func (e *Core) ExpandIcon(ctx context.Context, req *pb.ExpandIconRequest, rsp *pb.ExpandClueResponse) error {
	return clue.ExpandIcon(ctx, req, rsp)
}

// GetClueList 获取线索列表
func (e *Core) GetClueList(ctx context.Context, req *pb.ClueListRequest, rsp *pb.ClueListResponse) error {
	return clue.GetClueList(ctx, req, rsp)
}

// UpdateClueInfo 更新线索信息
func (e *Core) UpdateClueInfo(ctx context.Context, req, rsp *pb.ClueInfo) error {
	return clue.UpdateClueInfo(ctx, req, rsp)
}

// UpdateByKeyword 更新线索信息
func (e *Core) UpdateByKeyword(ctx context.Context, req *pb.UpdateClueByKeywordRequest, rsp *pb.ClueInfo) error {
	return clue.UpdateByKeyword(ctx, req, rsp)
}

// CreateClue 创建线索
func (e *Core) CreateClue(ctx context.Context, req, rsp *pb.ClueInfo) error {
	return clue.CreateClue(ctx, req, rsp)
}

// ExpandClueTaskList 扩展任务列表
func (e *Core) ExpandClueTaskList(ctx context.Context, req *pb.ExpandClueTaskListRequest, rsp *pb.ExpandClueTaskListResponse) error {
	return clue.ExpandClueTaskList(ctx, req, rsp)
}

func (e *Core) CluesBatchUpdate(ctx context.Context, req *pb.ClueListRequest, rsp *pb.Empty) error {
	return clue.CluesBatchUpdate(ctx, req, rsp)
}

func (e *Core) ClueFilterGroup(ctx context.Context, req *pb.ClueListRequest, rsp *pb.ClueFilterGroupResponse) error {
	log.Infof("Received core.ClueFilterGroup request: %s", req.String())
	if err := clue.ClueFilterGroup(ctx, req, rsp); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}
