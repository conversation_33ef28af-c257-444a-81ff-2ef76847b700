package handler

import (
	"context"
	"go-micro.dev/v4/errors"
	"micro-service/coreService/handler/tianyancha"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/log"
)

func (e *Core) QCCBranchResult(ctx context.Context, req *pb.QCCBranchRequest, rsp *pb.QCCBranchResponse) error {
	log.WithContextInfof(ctx, "[tianyancha] Received core.QCCBranchResult, params: %+v", req)
	// err = qichacha.GetBranchResult(req.Keyword, rsp)
	err := tianyancha.Branch(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[tianyancha] get branch info failed, keyword: %s, caused by: %v", req.Keyword, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) QCCNameSearch(ctx context.Context, req *pb.QCCNameSearchRequest, rsp *pb.QCCNameSearchResponse) error {
	log.WithContextInfof(ctx, "[tianyancha] Received core.QCCNameSearch, params: %+v", req)
	// err := qichacha.GetNameSearchResult(req.Search, rsp)
	err := tianyancha.Search(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Tianyancha] Get keyword: %s name search result failed: %v", req.Search, err)
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	return nil
}

// QCCGetBasicDetailsByName 企查查,企业工商照面接口
func (e *Core) QCCGetBasicDetailsByName(ctx context.Context, req *pb.QCCNameSearchRequest, rsp *pb.QCCGetBasicDetailsByNameResponse) error {
	log.WithContextInfof(ctx, "[tianyancha] Received core.QCCGetBasicDetailsByName, params: %+v", req)
	// err = qichacha.QCCGetBasicDetailsByName(req.Search, req.Force, rsp)
	err := tianyancha.CompanyInfo(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[tianyancha] get QCCGetBasicDetailsByName keyword: %s failed: %v", req.Search, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// QCCGetBasicDetailSearch 企查查,企业工商照面模糊搜索
func (e *Core) QCCGetBasicDetailSearch(ctx context.Context, req *pb.QCCBasicDetailSearchRequest, rsp *pb.QCCBasicDetailSearchResponse) error {
	// err := qichacha.QCCGetBasicDetailSearch(req, rsp)
	log.WithContextInfof(ctx, "[tianyancha] Received core.QCCGetBasicDetailSearch, params: %+v", req)
	err := tianyancha.CompanyInfoBySearch(ctx, req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// QCCInvestmentThrough 股权穿透
func (e *Core) QCCInvestmentThrough(ctx context.Context, req *pb.QCCInvestmentThroughRequest, rsp *pb.QCCInvestmentThroughResponse) error {
	log.WithContextInfof(ctx, "[tianyancha] Received core.QCCInvestmentThrough, params: %+v", req)
	if err := tianyancha.InvestTree(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[tianyancha] request method QCCInvestmentThrough, params: %+v", req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) TYCWeiboAccountSearch(ctx context.Context, req *pb.QCCNameSearchRequest, rsp *pb.TYCWeiboAccountsResponse) error {
	log.WithContextInfof(ctx, "[tianyancha] Received core.TYCWeiboAccountSearch, params-> %v", req)
	err := tianyancha.WeiboSearch(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[tianyancha] request method TYCWeiboAccountSearch failed: %v, params: %+v", err, req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}
