package handler

import (
	"context"
	"errors"
	"micro-service/apiService/upload"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/cast"
)

// IntelligenceHotPocList 热点POC列表
func (e *Core) IntelligenceHotPocList(ctx context.Context, req *pb.IntelligenceHotPocListRequest, rsp *pb.IntelligenceHotPocListResponse) error {
	queries := make([]mysql.HandleFunc, 0)
	if req.Name != "" {
		queries = append(queries, mysql.WithLike("name", req.Name))
	}
	if req.Cve != "" {
		queries = append(queries, mysql.WithWhere("cve", req.Cve))
	}
	if req.Cnnvd != "" {
		queries = append(queries, mysql.WithWhere("cnnvd", req.Cnnvd))
	}
	if req.RiskLevel != "" {
		queries = append(queries, mysql.WithWhere("risk_level", req.RiskLevel))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("found_at", req.FoundAt))
	}
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("created_at", req.CreatedAt))
	}
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("updated_at", req.UpdatedAt))
	}
	if len(req.Tag) > 0 {
		s := make([]string, 0)
		for _, v := range req.Tag {
			s = append(s, "tag like '%"+v+"%'")
		}
		queries = append(queries, mysql.WithSQl(strings.Join(s, " or ")))
	}
	queries = append(queries, mysql.WithOrder("id desc"))
	list, count, err := intelligence.NewHotPoc().List(cast.ToInt(req.Page), cast.ToInt(req.PerPage), queries...)
	if err != nil {
		return err
	}
	rsp.CurrentPage = cast.ToInt64(req.Page)
	rsp.Total = count
	rsp.Page = req.PerPage
	for x := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceHotPoc{
			Id:            list[x].Id,
			Name:          list[x].Name,
			Cve:           list[x].Cve,
			Cnnvd:         list[x].Cnnvd,
			ImpactProduct: list[x].ImpactProduct,
			ImpactVersion: list[x].ImpactVersion,
			ImpactRange:   list[x].ImpactRange,
			RiskLevel:     list[x].RiskLevel,
			Introduce:     list[x].Introduce,
			FofaCount:     list[x].FofaCount,
			FoundAt:       list[x].FoundAt.Format("2006-01-02 15:04:05"),
			FofaQuery:     list[x].FofaQuery,
			Solution:      list[x].Solution,
			Tag: func() []string {
				if len(list[x].Tag) > 0 {
					return strings.Split(list[x].Tag, ",")
				} else {
					return []string{}
				}
			}(),
		})
	}
	return nil
}

// IntelligenceHotPocCreate 新建热点POC
func (e *Core) IntelligenceHotPocCreate(ctx context.Context, req *pb.IntelligenceHotPocCreateRequest, _ *pb.Empty) error {
	if _, err := intelligence.NewHotPoc().First(mysql.WithWhere("name", req.Name)); err == nil {
		return errors.New("漏洞名已存在")
	}
	return intelligence.NewHotPoc().Create(&intelligence.HotPoc{
		Name:          req.Name,
		Cve:           req.Cve,
		Cnnvd:         req.Cnnvd,
		ImpactProduct: req.ImpactProduct,
		ImpactVersion: req.ImpactVersion,
		ImpactRange:   req.ImpactRange,
		RiskLevel:     req.RiskLevel,
		Introduce:     req.Introduce,
		FofaQuery:     req.FofaQuery,
		Solution:      req.Solution,
		FofaCount:     req.FofaCount,
		FoundAt:       utils.TimeStringToGoTime(req.FoundAt),
		Tag:           strings.Join(req.Tag, ","),
	})
}

// IntelligenceHotPocUpdate 更新热点POC
func (e *Core) IntelligenceHotPocUpdate(ctx context.Context, req *pb.IntelligenceHotPocUpdateRequest, _ *pb.Empty) error {
	params := map[string]any{
		"name":           req.Name,
		"cve":            req.Cve,
		"cnnvd":          req.Cnnvd,
		"impact_product": req.ImpactProduct,
		"impact_version": req.ImpactVersion,
		"impact_range":   req.ImpactRange,
		"risk_level":     req.RiskLevel,
		"introduce":      req.Introduce,
		"fofa_query":     req.FofaQuery,
		"solution":       req.Solution,
		"fofa_count":     req.FofaCount,
		"tag":            strings.Join(req.Tag, ","),
	}
	if req.FoundAt != "" {
		params["found_at"] = utils.TimeStringToGoTime(req.FoundAt)
	}
	if _, err := intelligence.NewHotPoc().First(mysql.WithWhere("name", req.Name), mysql.WithWhere("id != ?", req.Id)); err == nil {
		return errors.New("漏洞名已存在")
	}
	return intelligence.NewHotPoc().UpdateAny(params, mysql.WithWhere("id", req.Id))
}

// IntelligenceHotPocDelete 删除热点POC
func (e *Core) IntelligenceHotPocDelete(ctx context.Context, req *pb.IntelligenceHotPocDeleteRequest, _ *pb.Empty) error {
	return intelligence.NewHotPoc().DeleteByIds(req.Id)
}

// IntelligenceFakeList 仿冒列表
func (e *Core) IntelligenceFakeList(ctx context.Context, req *pb.IntelligenceFakeListRequest, rsp *pb.IntelligenceFakeListResponse) error {
	queries := make([]mysql.HandleFunc, 0)
	if req.Url != "" {
		queries = append(queries, mysql.WithLike("url", req.Url))
	}
	if req.Title != "" {
		queries = append(queries, mysql.WithLike("title", req.Title))
	}
	if req.Ip != "" {
		queries = append(queries, mysql.WithLike("ip", req.Ip))
	}
	if req.CloudName != "" {
		queries = append(queries, mysql.WithLike("cloud_name", req.CloudName))
	}
	if req.Country != "" {
		queries = append(queries, mysql.WithWhere("country", req.Country))
	}
	if req.Status != 0 {
		queries = append(queries, mysql.WithWhere("status", req.Status))
	}
	if req.Source != "" {
		queries = append(queries, mysql.WithWhere("source", req.Source))
	}
	if req.Target != "" {
		queries = append(queries, mysql.WithWhere("target", req.Target))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("found_at", req.FoundAt))
	}
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("created_at", req.CreatedAt))
	}
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("updated_at", req.UpdatedAt))
	}
	queries = append(queries, mysql.WithOrder("id desc"))
	list, count, err := intelligence.NewFake().List(cast.ToInt(req.Page), cast.ToInt(req.PerPage), queries...)
	if err != nil {
		return err
	}
	rsp.CurrentPage = cast.ToInt64(req.Page)
	rsp.Total = count
	rsp.Page = req.PerPage
	for x := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceFake{
			Id:        list[x].Id,
			Ip:        list[x].Ip,
			Target:    list[x].Target,
			Source:    list[x].Source,
			Url:       list[x].Url,
			Title:     list[x].Title,
			CloudName: list[x].CloudName,
			Country:   list[x].Country,
			Status:    cast.ToUint32(list[x].Status),
			FoundAt:   list[x].FoundAt.Format("2006-01-02 15:04:05"),
			CreatedAt: list[x].CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return nil
}

// IntelligenceFakeCreate 新建仿冒
func (e *Core) IntelligenceFakeCreate(ctx context.Context, req *pb.IntelligenceFakeCreateRequest, _ *pb.Empty) error {
	if _, err := intelligence.NewFake().First(mysql.WithWhere("url", req.Url)); err == nil {
		return errors.New("url已存在")
	}
	fake := &intelligence.Fake{
		Ip:        req.Ip,
		Target:    req.Target,
		Source:    req.Source,
		Url:       req.Url,
		Title:     req.Title,
		CloudName: req.CloudName,
		Country:   req.Country,
		Status:    cast.ToInt(req.Status), //  1/在线 2/离线
		FoundAt:   utils.TimeStringToGoTime(req.FoundAt),
	}
	return intelligence.NewFake().Create(fake)
}

// IntelligenceFakeUpdate 更新仿冒
func (e *Core) IntelligenceFakeUpdate(ctx context.Context, req *pb.IntelligenceFakeUpdateRequest, _ *pb.Empty) error {
	params := map[string]any{
		"ip":         req.Ip,
		"target":     req.Target,
		"source":     req.Source,
		"url":        req.Url,
		"title":      req.Title,
		"cloud_name": req.CloudName,
		"country":    req.Country,
		"status":     req.Status, //  1/在线 2/离线
		"updated_at": time.Now(),
	}
	if req.FoundAt != "" {
		params["found_at"] = utils.TimeStringToGoTime(req.FoundAt)
	}
	if _, err := intelligence.NewFake().First(mysql.WithWhere("url", req.Url), mysql.WithWhere("id != ?", req.Id)); err == nil {
		return errors.New("url已存在")
	}
	return intelligence.NewFake().UpdateAny(params, mysql.WithWhere("id", req.Id))
}

// IntelligenceFakeDelete 删除仿冒
func (e *Core) IntelligenceFakeDelete(ctx context.Context, req *pb.IntelligenceFakeDeleteRequest, _ *pb.Empty) error {
	return intelligence.NewFake().DeleteByIds(req.Id)
}

// IntelligenceThreatList 威胁列表
func (e *Core) IntelligenceThreatList(ctx context.Context, req *pb.IntelligenceThreatListRequest, rsp *pb.IntelligenceThreatListResponse) error {
	queries := make([]mysql.HandleFunc, 0)
	if req.Url != "" {
		queries = append(queries, mysql.WithLike("url", req.Url))
	}
	if req.Ip != "" {
		queries = append(queries, mysql.WithLike("ip", req.Ip))
	}
	if req.Country != "" {
		queries = append(queries, mysql.WithWhere("country", req.Country))
	}
	if req.Status != 0 {
		queries = append(queries, mysql.WithWhere("status", req.Status))
	}
	if req.Source != "" {
		queries = append(queries, mysql.WithWhere("source", req.Source))
	}
	if req.Type != "" {
		queries = append(queries, mysql.WithWhere("type", req.Type))
	}
	if req.Status != 0 {
		queries = append(queries, mysql.WithWhere("status", req.Status))
	}
	if req.Tags != "" {
		queries = append(queries, mysql.WithWhere("tags", req.Tags))
	}
	if req.Domain != "" {
		queries = append(queries, mysql.WithLike("domain", req.Domain))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("found_at", req.FoundAt))
	}
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("created_at", req.CreatedAt))
	}
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("updated_at", req.UpdatedAt))
	}
	queries = append(queries, mysql.WithOrder("id desc"))
	list, count, err := intelligence.NewThreat().List(cast.ToInt(req.Page), cast.ToInt(req.PerPage), queries...)
	if err != nil {
		return err
	}
	rsp.CurrentPage = cast.ToInt64(req.Page)
	rsp.Total = count
	rsp.Page = req.PerPage
	for x := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceThreat{
			Id:      list[x].Id,
			Ip:      list[x].Ip,
			Source:  list[x].Source,
			Url:     list[x].Url,
			Country: list[x].Country,
			Status:  cast.ToUint32(list[x].Status),
			FoundAt: list[x].FoundAt.Format("2006-01-02 15:04:05"),
			Type:    list[x].Type,
			Tags:    list[x].Tags,
			Domain:  list[x].Domain,
		})
	}
	return nil
}

// IntelligenceThreatCreate 新建威胁
func (e *Core) IntelligenceThreatCreate(ctx context.Context, req *pb.IntelligenceThreatCreateRequest, _ *pb.Empty) error {
	if _, err := intelligence.NewThreat().First(mysql.WithWhere("url", req.Url)); err == nil {
		return errors.New("url已存在")
	}
	return intelligence.NewThreat().Create(&intelligence.Threat{
		Type:    req.Type,
		Url:     req.Url,
		Ip:      req.Ip,
		Source:  req.Source,
		Country: req.Country,
		Status:  cast.ToInt(req.Status), //  1/在线 2/离线
		FoundAt: utils.TimeStringToGoTime(req.FoundAt),
		Tags:    req.Tags,
		Domain:  req.Domain,
	})
}

// IntelligenceThreatUpdate 更新威胁
func (e *Core) IntelligenceThreatUpdate(ctx context.Context, req *pb.IntelligenceThreatUpdateRequest, _ *pb.Empty) error {
	params := map[string]any{
		"url":        req.Url,
		"ip":         req.Ip,
		"source":     req.Source,
		"country":    req.Country,
		"type":       req.Type,
		"tags":       req.Tags,
		"status":     cast.ToInt(req.Status),
		"domain":     req.Domain,
		"updated_at": time.Now(),
	}
	if req.FoundAt != "" {
		params["found_at"] = utils.TimeStringToGoTime(req.FoundAt)
	}
	if _, err := intelligence.NewThreat().First(mysql.WithWhere("url", req.Url), mysql.WithWhere("id != ?", req.Id)); err == nil {
		return errors.New("url已存在")
	}
	return intelligence.NewThreat().UpdateAny(params, mysql.WithWhere("id", req.Id))
}

// IntelligenceThreatDelete 删除威胁
func (e *Core) IntelligenceThreatDelete(ctx context.Context, req *pb.IntelligenceThreatDeleteRequest, _ *pb.Empty) error {
	return intelligence.NewThreat().DeleteByIds(req.Id)
}

// IntelligenceOtherList 其他情报列表
func (e *Core) IntelligenceOtherList(ctx context.Context, req *pb.IntelligenceOtherListRequest, rsp *pb.IntelligenceOtherListResponse) error {
	queries := make([]mysql.HandleFunc, 0)
	if req.Url != "" {
		queries = append(queries, mysql.WithLike("url", req.Url))
	}
	if req.Keyword != "" {
		queries = append(queries, mysql.WithLike("keyword", req.Keyword))
	}
	if req.Title != "" {
		queries = append(queries, mysql.WithLike("title", req.Title))
	}
	if req.Company != "" {
		queries = append(queries, mysql.WithLike("company", req.Company))
	}
	if req.Platform != "" {
		queries = append(queries, mysql.WithWhere("platform", req.Platform))
	}
	if req.ArticleId != "" {
		queries = append(queries, mysql.WithWhere("article_id", req.ArticleId))
	}
	if req.IsPublic != 0 {
		queries = append(queries, mysql.WithWhere("is_public", req.IsPublic))
	}
	if req.ArticleContext != "" {
		queries = append(queries, mysql.WithLike("article_context", req.ArticleContext))
	}
	if req.Poster != "" {
		queries = append(queries, mysql.WithLike("poster", req.Poster))
	}
	if len(req.ArticleCreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("article_created_at", req.ArticleCreatedAt))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("found_at", req.FoundAt))
	}
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("created_at", req.CreatedAt))
	}
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("updated_at", req.UpdatedAt))
	}
	queries = append(queries, mysql.WithOrder("id desc"))
	list, count, err := intelligence.NewOther().List(cast.ToInt(req.Page), cast.ToInt(req.PerPage), queries...)
	if err != nil {
		return err
	}
	rsp.CurrentPage = cast.ToInt64(req.Page)
	rsp.Total = count
	rsp.Page = req.PerPage
	for x := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceOther{
			Id:               list[x].Id,
			Url:              list[x].Url,
			FoundAt:          list[x].FoundAt.Format("2006-01-02 15:04:05"),
			Company:          list[x].Company,
			Poster:           list[x].Poster,
			Platform:         list[x].Platform,
			Keyword:          list[x].Keyword,
			Title:            list[x].Title,
			Screenshot:       storage.GenAPIDownloadPath(filepath.Base(list[x].Screenshot), list[x].Screenshot),
			Sample:           storage.GenAPIDownloadPath(filepath.Base(list[x].Sample), list[x].Sample),
			SampleFileName:   filepath.Base(list[x].Sample),
			IsPublic:         cast.ToUint32(list[x].IsPublic),
			ArticleId:        list[x].ArticleId,
			ArticleContext:   list[x].ArticleContext,
			ArticleCreatedAt: list[x].ArticleCreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return nil
}

// IntelligenceOtherCreate 新建其他情报
func (e *Core) IntelligenceOtherCreate(ctx context.Context, req *pb.IntelligenceOtherCreateRequest, _ *pb.Empty) error {
	if _, err := intelligence.NewOther().First(mysql.WithWhere("url", req.Url)); err == nil {
		return errors.New("url已存在")
	}
	return intelligence.NewOther().Create(&intelligence.Other{
		Url:              req.Url,
		FoundAt:          utils.TimeStringToGoTime(req.FoundAt),
		Company:          req.Company,
		Poster:           req.Poster,
		Platform:         req.Platform,
		Keyword:          req.Keyword,
		Title:            req.Title,
		Screenshot:       upload.GetUploadDecryptPath(req.Screenshot),
		Sample:           upload.GetUploadDecryptPath(req.Sample),
		IsPublic:         cast.ToInt(req.IsPublic),
		ArticleId:        req.ArticleId,
		ArticleContext:   req.ArticleContext,
		ArticleCreatedAt: time.Now(),
	})
}

// IntelligenceOtherUpdate 更新其他情报
func (e *Core) IntelligenceOtherUpdate(ctx context.Context, req *pb.IntelligenceOtherUpdateRequest, _ *pb.Empty) error {
	params := map[string]any{
		"url":             req.Url,
		"title":           req.Title,
		"company":         req.Company,
		"poster":          req.Poster,
		"platform":        req.Platform,
		"keyword":         req.Keyword,
		"screenshot":      upload.GetUploadDecryptPath(req.Screenshot),
		"sample":          upload.GetUploadDecryptPath(req.Sample),
		"is_public":       req.IsPublic,
		"article_id":      req.ArticleId,
		"article_context": req.ArticleContext,
		"updated_at":      time.Now(),
	}
	if req.FoundAt != "" {
		params["found_at"] = utils.TimeStringToGoTime(req.FoundAt)
	}
	if req.ArticleCreatedAt != "" {
		params["article_created_at"] = utils.TimeStringToGoTime(req.ArticleCreatedAt)
	}
	if _, err := intelligence.NewOther().First(mysql.WithWhere("url", req.Url), mysql.WithWhere("id != ?", req.Id)); err == nil {
		return errors.New("url已存在")
	}
	return intelligence.NewOther().UpdateAny(params, mysql.WithWhere("id", req.Id))
}

// IntelligenceOtherDelete 删除其他情报
func (e *Core) IntelligenceOtherDelete(ctx context.Context, req *pb.IntelligenceOtherDeleteRequest, _ *pb.Empty) error {
	return intelligence.NewOther().DeleteByIds(req.Id)
}
