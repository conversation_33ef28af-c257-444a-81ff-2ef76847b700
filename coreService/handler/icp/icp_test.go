package icp

import (
	"context"
	"fmt"
	"micro-service/coreService/handler/chinaz"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql/company_icp"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"testing"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
)

func Init() {
	godotenv.Load("./../../../.env")

	cfg.InitLoadCfg()
	_ = es.GetInstance(cfg.LoadElastic())
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	log.Init()
}

func Test_GetICPByChild(t *testing.T) {
	Init()

	var rsp = new(pb.IcpResponse)
	err := GetICPByChild(context.Background(), &pb.IcpRequest{Icp: "京ICP备09013372号-57", Force: true}, rsp)
	assert.Nil(t, err)
}

func Test_DomainIcpNoExist(t *testing.T) {
	Init()
	rsp := pb.IcpResponse{}
	err := Domain(context.Background(), &pb.IcpDomainRequest{Domain: "vip.aa"}, &rsp)
	if err != nil {
		assert.Error(t, err)
	}
}
func Test_DomainIcpExist(t *testing.T) {
	Init()
	rsp := &pb.IcpResponse{}
	err := Domain(context.Background(), &pb.IcpDomainRequest{Domain: "azure757.top", GetEquals: true}, rsp)
	if err != nil {
		assert.Error(t, err)
	}
	println(fmt.Sprintf("%+v", rsp))
}

func Test_tianyanchaIcp(t *testing.T) {
	Init()
	rsp := &pb.IcpResponse{}
	err := chinaz.ApiCompanyName(rsp, "北京华顺信安科技有限公司")
	if err != nil {
		return
	}
	fmt.Println(rsp)

}

func Test_CompanyBeian(t *testing.T) {
	Init()

	rsp := &pb.IcpResponse{}
	req := &pb.IcpCompanyNameRequest{CompanyName: "北京华顺信安科技有限公司", GetEquals: true, Force: true}
	ctx := context.Background()
	err := CompanyNameQuery(ctx, req, rsp)
	if err != nil {
		return
	}
	fmt.Println(rsp)

}

func Test_CompanyName(t *testing.T) {
	Init()
	println(fmt.Sprintf("%+v", cfg.LoadMysql()))
	rsp := &pb.IcpResponse{}
	err := CompanyName(context.Background(), &pb.IcpCompanyNameRequest{CompanyName: "商丘市第一人民医院", GetEquals: true, Force: true}, rsp)
	if err != nil {
		assert.Error(t, err)
	}
	println(fmt.Sprintf("%+v", rsp.Info))
	for i := range rsp.Equals {
		println(fmt.Sprintf("%+v", rsp.Equals[i]))
	}
	println(fmt.Sprintf("%d", 1+len(rsp.Equals)))

}

func Test_Domain(t *testing.T) {
	Init()
	rsp := &pb.IcpResponse{}
	err := Domain(context.Background(), &pb.IcpDomainRequest{Domain: "jjkfq.bengbu.gov.cn", GetEquals: true, Force: true, GetSubdomain: true}, rsp)
	if err != nil {
		assert.Error(t, err)
	}
	println(fmt.Sprintf("%+v", rsp))
	rspNew := &pb.IcpResponse{}
	err = Domain(context.Background(), &pb.IcpDomainRequest{Domain: "jjkfq.bengbu.gov.cn", GetEquals: true, Force: true}, rspNew)
	if err != nil {
		assert.Error(t, err)
	}
	println(fmt.Sprintf("%+v", rspNew))
}

func Test_IcpNoExist(t *testing.T) {
	Init()
	rsp := &pb.IcpResponse{}
	err := Icp(context.Background(), &pb.IcpRequest{Icp: "京ICP备18024709号", GetEquals: true, Force: true}, rsp)
	if err != nil {
		assert.Error(t, err)
	}
	println(fmt.Sprintf("%+v", rsp))
}
func Test_IcpExist(t *testing.T) {
	Init()
	rsp := pb.IcpResponse{}
	err := Icp(context.Background(), &pb.IcpRequest{Icp: "京ICP备18024708号"}, &rsp)
	if err != nil {
		assert.Error(t, err)
	}
}

func Test_UnrebisteredIcp(t *testing.T) {
	Init()
	re, err := company_icp.NewUnregisteredIcpModel().NewHistory("vip.aa", utils.SubDayTime(30))
	println(fmt.Sprintf("%v", re), fmt.Sprintf("%v", err))
}
