package icp

import (
	"context"
	"database/sql"
	errstd "errors"
	"fmt"
	"net"
	"strings"
	"time"

	"go-micro.dev/v4/errors"
	"golang.org/x/net/idna"
	"gorm.io/gorm"

	"micro-service/coreService/handler/chinaz"
	"micro-service/coreService/handler/fofatools"
	"micro-service/coreService/handler/miit"
	corePb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company_icp"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

const emptyTimeStr = "0001-01-01 00:00:00 +0000 UTC"
const personStr = "个人"

func Icp(_ context.Context, req *corePb.IcpRequest, rsp *corePb.IcpResponse) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*60)
	defer cancel()
	if req.Icp == "" {
		return errors.New(corePb.ServiceName, "ICP不能为空!", 400)
	}
	req.Icp = utils.RemoveIcpNumber(req.Icp)
	cacheKey := "icp-i:" + utils.Md5Hash(req)
	if !req.Force {
		// 有缓存时取缓存数据
		if redis.GetCache(cacheKey, &rsp) {
			if !req.GetEquals {
				rsp.Equals = nil
			}
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,ICP:%s,取到缓存数据,key:%s,Rsp:%+v", req.Icp, cacheKey, rsp))
			return nil
		}
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,ICP:%s,无缓存数据", req.Icp))
		// 查询数据库
		if err := selectDbIcpByIcp(req.Icp, rsp); err == nil {
			redis.SetCache(cacheKey, 3*time.Minute, rsp)
			if !req.GetEquals {
				rsp.Equals = nil
			}
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,ICP:%s,取到DB数据,Rsp:%+v", req.Icp, rsp))
			return nil
		} else {
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,ICP:%s,未取到DB数据,原因:%+v", req.Icp, err.Error()))
		}
		// 查询未备案记录
		if company_icp.NewUnregisteredIcpModel().Exists(req.Icp, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay))) {
			rsp.Ba = false
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,ICP:%s,DB已存在未备案记录", req.Icp))
			return nil
		}
	}

	// 加载fofa的icp备案信息 --优先查询 fofa的icp
	//配置项配置了优先加载fofa的话，就再加载fofa
	var foficp = cfg.LoadCommon().FofaIcp
	log.WithContextInfof(ctx, fmt.Sprintf("CompanyNameQuery-备案查询服务-fofaicp状态 %v,企业名称查询备案,ICP备案号:%s,", foficp, req.Icp))
	if foficp {
		if err := loadFofaICPInfo(ctx, rsp, "icp", req.Icp); err == nil {
			if rsp.Info != nil {
				// Fofa查询成功且有备案信息
				go insertDbIcpByCompanyName(ctx, rsp.Info.CompanyName, rsp, "fofa_api", "Icp")
				redis.SetCache(cacheKey, 3*60*time.Second, rsp)
				if !req.GetEquals {
					rsp.Equals = nil
				}
				log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询-Fofa查询,ICP:%s,取到Fofa查询数据,Rsp:%+v", req.Icp, rsp))
				return nil
			} else {
				// Fofa查询成功但无备案信息
				rsp.Ba = false
				// Fofa未备案时, 检查数据库备案号是否存在,存在更新为已注销
				if uErr := company_icp.NewCompanyIcpModel().SetOfflineByIcp(req.Icp); uErr != nil {
					log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询-Fofa查询,备案信息入库更新,ICP:%s,入库失败,Fofa更新备案注销信息失败:%s", req.Icp, uErr.Error()))
				}
				// 未备案时,入到未备案记录表
				_, _ = company_icp.NewUnregisteredIcpModel().NewHistory(req.Icp, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay)))
				log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询-Fofa查询,ICP:%s,取到Fofa查询数据,Rsp:ICP未备案", req.Icp))
				redis.SetCache(cacheKey, 3*60*time.Second, rsp)
				if !req.GetEquals {
					rsp.Equals = nil
				}
				return nil
			}
		} else {
			// Fofa查询失败，尝试使用站长之家查询
			log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,ICP查询-Fofa查询,ICP:%s,Fofa查询异常:%s,尝试使用站长之家查询", req.Icp, err.Error()))
		}
	}
	// 查询站长之家接口
	if err := chinaz.ApiCompanyName(rsp, req.Icp); err == nil {
		if rsp.Info == nil {
			rsp.Ba = false
			// ChinazApi未备案时, 检查数据库备案号是否存在,存在更新为已注销
			if uErr := company_icp.NewCompanyIcpModel().SetOfflineByIcp(req.Icp); uErr != nil {
				log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,备案信息入库更新,ICP:%s,入库失败,ChinazApi更新备案注销信息失败:%s", req.Icp, uErr.Error()))
			}
			// 未备案时,入到未备案记录表
			_, _ = company_icp.NewUnregisteredIcpModel().NewHistory(req.Icp, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay)))
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,ICP:%s,取到ChinazApi查询数据,Rsp:ICP未备案", req.Icp))
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			if !req.GetEquals {
				rsp.Equals = nil
			}
			return nil
		}
		go insertDbIcpByCompanyName(ctx, rsp.Info.CompanyName, rsp, "chinaz_api", "Icp")
		redis.SetCache(cacheKey, 3*60*time.Second, rsp)
		if !req.GetEquals {
			rsp.Equals = nil
		}
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,ICP:%s,取到ChinazApi查询数据,Rsp:%+v", req.Icp, rsp))
		return nil
	} else if strings.Contains(err.Error(), "暂无备案信息") {
		// ChinazApi未备案时, 检查数据库备案号是否存在,存在更新为已注销
		if uErr := company_icp.NewCompanyIcpModel().SetOfflineByIcp(req.Icp); uErr != nil {
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,备案信息入库更新,ICP:%s,入库失败,ChinazApi更新备案注销信息失败:%s", req.Icp, uErr.Error()))
		}
		// 未备案时,入到未备案记录表
		_, _ = company_icp.NewUnregisteredIcpModel().NewHistory(req.Icp, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay)))
		rsp.Ba = false
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,ICP查询,ICP:%s,取到ChinazApi查询数据,Rsp:ICP未备案", req.Icp))
		return nil
	} else {
		log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,ICP查询,ICP:%s,ChinazApi查询异常4:%s", req.Icp, err.Error()))
	}

	return errors.New(corePb.ServiceName, fmt.Sprintf("查询 %s ICP信息失败!", req.Icp), 400)
}

// GetICPByChild 查询子ICP
func GetICPByChild(ctx context.Context, req *corePb.IcpRequest, rsp *corePb.IcpResponse) error {
	if req.Icp == "" {
		return errors.New(corePb.ServiceName, "icp不能为空!", 400)
	}

	if !strings.Contains(req.Icp, "-") {
		return Icp(ctx, req, rsp)
	}

	var childIcpPrefix = "child_icp:miit:" + req.Icp
	db := company_icp.NewCompanyIcpModel()
	if !req.Force {
		if redis.GetCache(childIcpPrefix, rsp) {
			return nil
		}

		x, err := db.First(mysql.WithColumnValue("icp", req.Icp))
		if err != nil {
			return err
		}
		rsp.Info.CompanyName = x.Name
		rsp.Info.Icp = x.Icp
		rsp.Info.WebsiteUrl = x.Domain
		rsp.Info.AuditTime = x.RecordTime.Time.Format(utils.DateTimeLayout)
		rsp.Info.Status = uint32(x.Status)
		return nil
	}

	err := miit.QueryIcp(ctx, rsp, req.Icp)
	if err != nil {
		return err
	}
	if rsp.Info == nil {
		return nil
	}
	if rsp.Info.Icp == "" {
		return nil
	}

	go redis.SetCache(childIcpPrefix, 5*time.Minute, rsp)

	rsp.Equals = append(rsp.Equals, rsp.Info)

	ls, err := db.ListAll(mysql.WithColumnValue("icp", req.Icp))
	if err != nil {
		log.WithContextWarnf(ctx, "[IcpByChild] Get icp: %s all records failed: %v", req.Icp, err)
		return err
	}

	var otherCompany, emptyCompany []uint64
	for _, v := range ls {
		if v.Name == "" {
			emptyCompany = append(emptyCompany, v.Id)
		}
		if v.Name != rsp.Info.CompanyName {
			otherCompany = append(otherCompany, v.Id)
		}
	}

	icpNum := utils.RemoveIcpNumber(rsp.Info.Icp)
	mainIcp, errFirst := db.First(mysql.WithWhere("`icp`=? AND `parent_id`=0", icpNum))
	switch {
	case errstd.Is(errFirst, gorm.ErrRecordNotFound):
	case err != nil:
		return err
	}

	if errstd.Is(errFirst, gorm.ErrRecordNotFound) || (mainIcp.Name != rsp.Info.CompanyName && mainIcp.Name == "") {
		mainIcp.Icp = icpNum
		mainIcp.Type = company_icp.GetTypeInt(rsp.Info.CompanyType)
		mainIcp.Name = rsp.Info.CompanyName
		mainIcp.Status = company_icp.IcpStatusOnline
		mainIcp.RecordTime = sql.NullTime{}
		mainIcp.Source = company_icp.SourceMIIT
		if errCreate := db.Upsert(&mainIcp); errCreate != nil {
			return errCreate
		}
	}

	var upsert = make([]*company_icp.CompanyIcp, 0, len(ls))
	for _, x := range rsp.Equals {
		rt, _ := utils.ParseTime(x.AuditTime)
		var item = &company_icp.CompanyIcp{
			Name:       x.CompanyName,
			Icp:        x.Icp,
			ParentId:   mainIcp.Id,
			Type:       company_icp.GetTypeInt(x.CompanyType),
			Domain:     x.WebsiteUrl,
			RecordTime: sql.NullTime{Time: rt, Valid: !rt.IsZero()},
			Source:     utils.If(mainIcp.Source != "", mainIcp.Source, company_icp.SourceMIIT),
			Status:     company_icp.IcpStatusOnline,
		}
		for _, v := range ls {
			if x.WebsiteUrl == v.Domain {
				item.Id = v.Id
				item.CreatedAt = v.CreatedAt
				item.Source = utils.If(v.Source != "", v.Source, item.Source)
				break
			}
		}
		upsert = append(upsert, item)
	}

	errUpsert := db.Upsert(upsert...)
	if errUpsert != nil {
		log.WithContextWarnf(ctx, "[IcpByChild] Upsert icp records failed: %v", errUpsert)
	}

	if len(otherCompany) > 0 {
		m := map[string]any{"status": company_icp.IcpStatusOffline}
		errAny := db.UpdateAny(m, mysql.WithValuesIn("id", otherCompany))
		if errAny != nil {
			log.WithContextWarnf(ctx, "[IcpByChild] UpdateAny icp records failed: %v", errUpsert)
		}
	}
	errDel := db.DeleteByIds(emptyCompany)
	if errDel != nil {
		log.WithContextWarnf(ctx, "[IcpByChild] DeleteByIds icp records failed: %v", errUpsert)
	}
	return nil
}

// 根据企业名称或者备案信息-站长之家
func CompanyNameQuery(_ context.Context, req *corePb.IcpCompanyNameRequest, rsp *corePb.IcpResponse) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3*60)
	log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第一步-rongjiale,企业名: %s,force字段值: %v", req.CompanyName, req.Force))
	defer cancel()
	if req.CompanyName == "" {
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第零步-rongjiale,企业名:%s,", req.CompanyName))
		return errors.New(corePb.ServiceName, "企业名称不能为空!", 400)
	}
	cacheKey := "icp-c:" + utils.Md5Hash(req)
	if !req.Force {
		// 有缓存时取缓存数据
		if redis.GetCache(cacheKey, &rsp) {
			if !req.GetEquals {
				rsp.Equals = nil
			}
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-无强制刷新,走数据库查询,企业名:%s,取到缓存数据,key:%s,Rsp:%+v", req.CompanyName, cacheKey, rsp))
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第二步-rongjiale,企业名:%s,", req.CompanyName))
			return nil
		}
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-无强制刷新,走数据库查询,企业名:%s,无缓存数据", req.CompanyName))
		// 查询数据库
		if err := selectDbIcpByCompanyName(req.CompanyName, rsp); err == nil {
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			if !req.GetEquals {
				rsp.Equals = nil
			}
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-无强制刷新,走数据库查询,企业名:%s,取到DB数据,Rsp:%+v", req.CompanyName, rsp))
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第三步-rongjiale,企业名:%s,", req.CompanyName))
			return nil
		} else {
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-无强制刷新,走数据库查询,企业名:%s,未取到DB数据,原因:%+v", req.CompanyName, err.Error()))
		}
		// 查询未备案记录
		if company_icp.NewUnregisteredIcpModel().Exists(req.CompanyName, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay))) {
			rsp.Ba = false
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-无强制刷新,走数据库查询,企业名:%s,DB已存在未备案记录", req.CompanyName))
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第四步-rongjiale,企业名:%s,", req.CompanyName))
			return nil
		}
	}
	var foficp = cfg.LoadCommon().FofaIcp
	log.WithContextInfof(ctx, fmt.Sprintf("CompanyNameQuery-备案查询服务-fofaicp状态 %v,企业名称查询备案,企业名:%s,", foficp, req.CompanyName))
	// 加载fofa的 根据org查询备案
	if foficp {
		if err := loadFofaICPInfo(ctx, rsp, "org", req.CompanyName); err == nil {
			if rsp.Info != nil {
				go insertDbIcpByCompanyName(ctx, req.CompanyName, rsp, "fofa_api", "CompanyNameQuery")
				redis.SetCache(cacheKey, 3*60*time.Second, rsp)
				if !req.GetEquals {
					rsp.Equals = nil
				}
				log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-Fofa查询,企业名:%s,取到Fofa查询数据,Rsp:%+v", req.CompanyName, rsp))
				return nil
			} else {
				rsp.Ba = false
				// Fofa未备案时, 检查数据库备案号是否存在,存在更新为已注销
				if uErr := company_icp.NewCompanyIcpModel().SetOfflineByCompanyName(req.CompanyName); uErr != nil {
					log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-Fofa查询,备案信息入库更新,企业名:%s,入库失败,Fofa更新备案注销信息失败:%s", req.CompanyName, uErr.Error()))
				}
				// 未备案时,入到未备案记录表
				_, _ = company_icp.NewUnregisteredIcpModel().NewHistory(req.CompanyName, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay)))
				log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-Fofa查询,企业名:%s,取到Fofa查询数据,Rsp:ICP未备案", req.CompanyName))
				redis.SetCache(cacheKey, 3*60*time.Second, rsp)
				if !req.GetEquals {
					rsp.Equals = nil
				}
				return nil
			}
		} else {
			log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,企业名称查询-Fofa查询,企业名:%s,Fofa查询异常:%s", req.CompanyName, err.Error()))
		}
	}
	// 查询站长之家接口-实时查询
	if err := chinaz.ApiCompanyName(rsp, req.CompanyName); err == nil {
		go insertDbIcpByCompanyName(ctx, req.CompanyName, rsp, "chinaz_api", "CompanyNameQuery")
		if rsp.Info == nil {
			rsp.Ba = false
			// ChinazApi未备案时, 检查数据库备案号是否存在,存在更新为已注销
			if uErr := company_icp.NewCompanyIcpModel().SetOfflineByCompanyName(req.CompanyName); uErr != nil {
				log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家实时查询,备案信息入库更新,企业名:%s,入库失败,ChinazApi更新备案注销信息失败:%s", req.CompanyName, uErr.Error()))
			}
			// 未备案时,入到未备案记录表
			_, _ = company_icp.NewUnregisteredIcpModel().NewHistory(req.CompanyName, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay)))
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家实时查询,企业名:%s,取到ChinazApi查询数据,Rsp:ICP未备案", req.CompanyName))
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			if !req.GetEquals {
				rsp.Equals = nil
			}
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第五步-rongjiale,企业名:%s,", req.CompanyName))
			return nil
		}
		redis.SetCache(cacheKey, 3*60*time.Second, rsp)
		if !req.GetEquals {
			rsp.Equals = nil
		}
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家实时查询,企业名:%s,取到ChinazApi查询数据,Rsp:%+v", req.CompanyName, rsp))
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第六步-rongjiale,企业名:%s,", req.CompanyName))
		return nil
	} else if strings.Contains(err.Error(), "暂无备案信息") {
		// ChinazApi未备案时, 检查数据库备案号是否存在,存在更新为已注销
		if uErr := company_icp.NewCompanyIcpModel().SetOfflineByCompanyName(req.CompanyName); uErr != nil {
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家实时查询,备案信息入库更新,企业名:%s,入库失败,ChinazApi更新备案注销信息失败:%s", req.CompanyName, uErr.Error()))
		}
		// 未备案时,入到未备案记录表
		_, _ = company_icp.NewUnregisteredIcpModel().NewHistory(req.CompanyName, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay)))
		rsp.Ba = false
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家实时查询,企业名:%s,取到ChinazApi查询数据,Rsp:ICP未备案", req.CompanyName))
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第七步-rongjiale,企业名:%s,", req.CompanyName))
		return nil
	} else {
		log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家实时查询,企业名:%s,ChinazApi查询异常3:%s", req.CompanyName, err.Error()))
	}

	// 查询站长之家历史接口
	if err := chinaz.ApiLishiCompanyName(rsp, req.CompanyName); err == nil {
		if rsp.Info == nil {
			rsp.Ba = false
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家历史备案查询,企业名:%s,取到ChinazLiShi查询数据,Rsp:ICP未备案", req.CompanyName))
			if !req.GetEquals {
				rsp.Equals = nil
			}
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第八步-rongjiale,企业名:%s,", req.CompanyName))
			return nil
		}
		redis.SetCache(cacheKey, 3*60*time.Second, rsp)
		if !req.GetEquals {
			rsp.Equals = nil
		}
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家历史备案查询,企业名:%s,取到ChinazLiShi查询数据,Rsp:%+v", req.CompanyName, rsp))
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第九步-rongjiale,企业名:%s,", req.CompanyName))
		return nil
	} else if strings.Contains(err.Error(), "暂无备案信息") {
		rsp.Ba = false
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家历史备案查询,企业名:%s,取到ChinazLiShi查询数据,Rsp:ICP未备案", req.CompanyName))
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第十步-rongjiale,企业名:%s,", req.CompanyName))
		return nil
	} else {
		log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,企业名称查询-站长之家历史备案查询,企业名:%s,ChinazLiShi查询异常:%s", req.CompanyName, err.Error()))
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务-CompanyNameQuery,企业名称查询备案-进来查询第十一步-rongjiale,企业名:%s,", req.CompanyName))
	}
	return errors.New(corePb.ServiceName, fmt.Sprintf("CompanyNameQuery-查询 %s  ICP信息失败!", req.CompanyName), 400)
}

func CompanyName(_ context.Context, req *corePb.IcpCompanyNameRequest, rsp *corePb.IcpResponse) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3*60)
	defer cancel()
	// 检查英文括号,存在英文括号未查出结果时,查一下中文括号
	if strings.Contains(req.CompanyName, "(") {
		if err := CompanyNameQuery(ctx, req, rsp); err != nil {
			req.CompanyName = strings.ReplaceAll(req.CompanyName, "(", "（")
			req.CompanyName = strings.ReplaceAll(req.CompanyName, ")", "）")
			return CompanyNameQuery(ctx, req, rsp)
		} else {
			if rsp.Info == nil {
				req.CompanyName = strings.ReplaceAll(req.CompanyName, "(", "（")
				req.CompanyName = strings.ReplaceAll(req.CompanyName, ")", "）")
				return CompanyNameQuery(ctx, req, rsp)
			} else {
				return err
			}
		}
	}
	// 检查中文括号,存在中文括号未查出结果时,查一下英文括号
	if strings.Contains(req.CompanyName, "（") {
		if err := CompanyNameQuery(ctx, req, rsp); err != nil {
			req.CompanyName = strings.ReplaceAll(req.CompanyName, "（", "(")
			req.CompanyName = strings.ReplaceAll(req.CompanyName, "）", ")")
			return CompanyNameQuery(ctx, req, rsp)
		} else {
			if rsp.Info == nil {
				req.CompanyName = strings.ReplaceAll(req.CompanyName, "（", "(")
				req.CompanyName = strings.ReplaceAll(req.CompanyName, "）", ")")
				return CompanyNameQuery(ctx, req, rsp)
			} else {
				return err
			}
		}
	}
	// 没有走默认
	return CompanyNameQuery(ctx, req, rsp)
}

// 根据域名查询备案信息
func Domain(_ context.Context, req *corePb.IcpDomainRequest, rsp *corePb.IcpResponse) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*60)
	defer cancel()
	if req.Domain == "" {
		return errors.New(corePb.ServiceName, "域名不能为空!", 400)
	}
	// 处理中文域名,域名为中文时,转成Unicode之后在查备案
	if strings.Contains(req.Domain, "xn--") {
		if decodedDomain, err := idna.ToUnicode(req.Domain); err == nil {
			req.Domain = decodedDomain
		}
	}
	// 查子域名备案
	if req.GetSubdomain {
		if err := queryDomain(ctx, req, rsp); err != nil {
			return err
		}
		return nil
	}
	// 不是IP时,查定级域名
	if net.ParseIP(req.Domain) == nil {
		req.Domain = utils.GetTopDomain(req.Domain)
	}
	if err := queryDomain(ctx, req, rsp); err != nil {
		return err
	}
	return nil
}

func DomainOnly(ctx context.Context, req *corePb.IcpDomainRequest, rsp *corePb.IcpResponse) error {
	cacheKey := "icp-d:" + utils.Md5Hash(req)
	if !req.Force {
		// 有缓存时取缓存数据
		if redis.GetCache(cacheKey, &rsp) {
			rsp.Equals = nil
			rsp.Ba = true
			log.WithContextInfof(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询,Domain:%s,Only,取到缓存数据,key:%s,Rsp:%+v", req.Domain, cacheKey, rsp))
			return nil
		}
		log.WithContextInfof(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询,Domain:%s,Only,无缓存数据", req.Domain))
		// 查询数据库
		if err := selectDbIcpByDomain(req.Domain, rsp); err == nil {
			rsp.Equals = nil
			rsp.Ba = true
			log.WithContextInfof(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询,Domain:%s,Only,取到DB数据,Rsp:%+v", req.Domain, rsp))
			return nil
		} else {
			log.WithContextInfof(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询,Domain:%s,Only,未取到DB数据,原因:%+v", req.Domain, err.Error()))
		}
		// 查询未备案记录
		if company_icp.NewUnregisteredIcpModel().Exists(req.Domain, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay))) {
			rsp.Ba = false
			log.WithContextInfof(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询,Domain:%s,Only,DB已存在未备案记录", req.Domain))
			return nil
		}
	}
	// 判断是不是子域名，如果是的话，就默认返回子域名，不是的话取主域名
	returnDomain := utils.GetTopDomain(req.Domain)
	if req.GetSubdomain {
		returnDomain = req.Domain
	}
	// 调用fofa的ICP查询
	if result, err := fofatools.QueryICP(ctx, &fofatools.ICPQueryRequest{
		QueryType: fofatools.ICPQueryTypeDomain,
		Value:     returnDomain,
		Size:      1000,
	}); err == nil {
		if result != nil && len(result.Records) > 0 {
			// 设置主记录
			if rsp.Info == nil {
				websiteUrl := result.Records[0].Domain
				if websiteUrl == "" && result.Records[0].IP != "" {
					websiteUrl = result.Records[0].IP
				}
				rsp.Info = &corePb.BeiAn{
					CompanyName: result.Records[0].Org,
					Icp:         result.Records[0].ICP,
					WebsiteUrl:  websiteUrl,
				}
				rsp.Ba = true
			}

			// 添加其他记录到Equals
			for i := 0; i < len(result.Records); i++ {
				record := result.Records[i]
				websiteUrl := record.Domain
				if websiteUrl == "" && record.IP != "" {
					websiteUrl = record.IP
				}
				beiAn := &corePb.BeiAn{
					CompanyName: record.Org,
					Icp:         record.ICP,
					WebsiteUrl:  websiteUrl,
				}
				rsp.Equals = append(rsp.Equals, beiAn)
			}

			// Fofa查询成功且有备案信息
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			if !req.GetEquals {
				rsp.Equals = nil
			}
			log.WithContextInfof(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询-Fofa查询,Domain:%s,Only,取到Fofa查询数据,Rsp:%+v", req.Domain, rsp))
			return nil
		} else {
			return nil
		}
	} else {
		log.WithContextWarnf(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询-Fofa查询,Domain:%s,Only,Fofa查询异常:%s", req.Domain, err.Error()))
	}

	// 查找chinaz API
	if err := chinaz.ApiDomain(rsp, req.Domain); err == nil {
		if rsp.Info == nil {
			rsp.Ba = false
			rsp.Equals = nil
			log.WithContextInfof(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询,Domain:%s,Only,取到ChinazApi查询数据,Rsp:ICP未备案", req.Domain))
			return nil
		}
		rsp.Ba = true
		log.WithContextInfof(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询,Domain:%s,Only,取到ChinazApi查询数据,Rsp:%+v", req.Domain, rsp))
		return nil
	} else if strings.Contains(err.Error(), "DomainOnly-暂无备案信息") {
		rsp.Ba = false
		log.WithContextInfof(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询,Domain:%s,Only,取到ChinazApi查询数据,Rsp:ICP未备案", req.Domain))
		return nil
	} else {
		log.WithContextWarnf(ctx, fmt.Sprintf("DomainOnly-备案查询服务,域名查询,Domain:%s,Only,ChinazApi查询异常2:%s", req.Domain, err.Error()))
	}
	return errors.New(corePb.ServiceName, fmt.Sprintf("DomainOnly-查询 %s ICP信息失败!", req.Domain), 400)
}

// 根据域名查询备案信息
func queryDomain(ctx context.Context, req *corePb.IcpDomainRequest, rsp *corePb.IcpResponse) error {
	cacheKey := "icp-d:" + utils.Md5Hash(req)
	if !req.Force {
		// 有缓存时取缓存数据
		if redis.GetCache(cacheKey, &rsp) {
			if !req.GetEquals {
				rsp.Equals = nil
			}
			log.WithContextInfof(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询,Domain:%s,取到缓存数据,key:%s,Rsp:%+v", req.Domain, cacheKey, rsp))
			return nil
		}
		log.WithContextInfof(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询,Domain:%s,无缓存数据", req.Domain))
		// 查询数据库
		if err := selectDbIcpByDomain(req.Domain, rsp); err == nil {
			redis.SetCache(cacheKey, 3*time.Minute, rsp)
			if !req.GetEquals {
				rsp.Equals = nil
			}
			log.WithContextInfof(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询,Domain:%s,取到DB数据,Rsp:%+v", req.Domain, rsp))
			return nil
		} else {
			log.WithContextInfof(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询,Domain:%s,未取到DB数据,原因:%+v", req.Domain, err.Error()))
		}
		// 查询未备案记录
		if company_icp.NewUnregisteredIcpModel().Exists(req.Domain, utils.SubDayTime(time.Duration(cfg.LoadCommon().IcpCacheDay))) {
			rsp.Ba = false
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			log.WithContextInfof(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询,Domain:%s,DB已存在未备案记录", req.Domain))
			return nil
		}
	}
	var foficp = cfg.LoadCommon().FofaIcp
	log.WithContextInfof(ctx, fmt.Sprintf("CompanyNameQuery-备案查询服务-fofaicp状态 %v,域名:%s,", foficp, req.Domain))
	if foficp {
		// 判断是不是子域名，如果是的话，就默认返回子域名，不是的话取主域名
		returnDomain := utils.GetTopDomain(req.Domain)
		if req.GetSubdomain {
			returnDomain = req.Domain
		}
		// 调用fofa的ICP查询
		if err := loadFofaICPInfo(ctx, rsp, "domain", returnDomain); err == nil {
			if rsp.Info != nil {
				// Fofa查询成功且有备案信息
				redis.SetCache(cacheKey, 3*60*time.Second, rsp)
				if !req.GetEquals {
					rsp.Equals = nil
				}
				log.WithContextInfof(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询-Fofa查询,Domain:%s,取到Fofa查询数据,Rsp:%+v", req.Domain, rsp))
				return nil
			} else {
				return nil
			}
		} else {
			log.WithContextWarnf(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询-Fofa查询,Domain:%s,Fofa查询异常:%s", req.Domain, err.Error()))
		}
	}
	// 查找chinaz API 历史 （根据域名查询对应的历史备案单位信息，这个接口没有返回domain字段（icp备案官网的那个备案域名），需要自己处理）
	if err := chinaz.ApiLishiDomain(rsp, req.Domain, req.GetSubdomain); err == nil {
		if rsp.Info == nil {
			rsp.Ba = false
			log.WithContextInfof(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询,Domain:%s,取到ChinazApiLiShi查询数据,Rsp:ICP未备案", req.Domain))
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
			if !req.GetEquals {
				rsp.Equals = nil
			}
			return nil
		}
		log.WithContextInfof(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询,Domain:%s,取到ChinazApiLiShi查询数据,Rsp:%+v", req.Domain, rsp))
		return nil
	} else if strings.Contains(err.Error(), "queryDomain-暂无备案信息") {
		rsp.Ba = false
		log.WithContextInfof(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询,Domain:%s,取到ChinazApiLiShi查询数据,Rsp:ICP未备案", req.Domain))
		return nil
	} else {
		log.WithContextWarnf(ctx, fmt.Sprintf("queryDomain-备案查询服务,域名查询,Domain:%s,ChinazApiLiShi查询异常:%s", req.Domain, err.Error()))
	}
	return errors.New(corePb.ServiceName, fmt.Sprintf("queryDomain-查询 %s ICP信息失败!", req.Domain), 400)
}

// selectDbIcpByDomain 根据域名查询数据库数据
func selectDbIcpByDomain(domain string, rsp *corePb.IcpResponse) error {
	if domain == "" {
		return errors.New(corePb.ServiceName, "未查询到最近记录", 400)
	}
	mainIcp, subIcps, err := company_icp.NewCompanyIcpModel().FindOnlineByDomain(domain)
	if err != nil {
		return err
	}
	if mainIcp == nil {
		return errors.New(corePb.ServiceName, "数据库备案信息异常,Domain:"+domain, 400)
	}
	checkTimeDay := time.Now().Add(time.Duration(cfg.LoadCommon().IcpCacheDay) * -24 * time.Hour)
	// 在缓存时间内
	if mainIcp.UpdatedAt.After(checkTimeDay) {
		formatIcpResponse(subIcps, rsp)
		return nil
	}
	return errors.New(corePb.ServiceName, "已超过ICP缓存日期,Domain:"+domain, 400)
}

// selectDbIcpByIcp 根据ICP数据库数据
func selectDbIcpByIcp(icp string, rsp *corePb.IcpResponse) error {
	if icp == "" {
		return errors.New(corePb.ServiceName, "未查询到最近记录", 400)
	}
	mainIcp, subIcps, err := company_icp.NewCompanyIcpModel().FindOnlineByIcp(icp)
	if err != nil {
		return err
	}
	if mainIcp == nil {
		return errors.New(corePb.ServiceName, "数据库备案信息异常,ICP:"+icp, 400)
	}
	checkTimeDay := time.Now().Add(time.Duration(cfg.LoadCommon().IcpCacheDay) * -24 * time.Hour)
	// 在缓存时间内
	if mainIcp.UpdatedAt.After(checkTimeDay) {
		formatIcpResponse(subIcps, rsp)
		return nil
	}
	return errors.New(corePb.ServiceName, "已超过ICP缓存日期,ICP:"+icp, 400)
}

// selectDbIcpByCompanyName 根据企业名称查询数据库数据
func selectDbIcpByCompanyName(companyName string, rsp *corePb.IcpResponse) error {
	if companyName == "" {
		return errors.New(corePb.ServiceName, "未查询到最近记录", 400)
	}
	mainIcp, subIcps, err := company_icp.NewCompanyIcpModel().FindOnlineByCompanyName(companyName)
	if err != nil {
		return err
	}
	if mainIcp == nil {
		return errors.New(corePb.ServiceName, "数据库备案信息异常,企业名称:"+companyName, 400)
	}
	checkTimeDay := time.Now().Add(time.Duration(cfg.LoadCommon().IcpCacheDay) * -24 * time.Hour)
	// 在缓存时间内
	if mainIcp.UpdatedAt.After(checkTimeDay) {
		formatIcpResponse(subIcps, rsp)
		return nil
	}
	return errors.New(corePb.ServiceName, "已超过ICP缓存日期,企业名称:"+companyName, 400)
}

// 执行备案数据入库，线索总库和company_icp表数据
func insertDbIcpByOrg(ctx context.Context, companyName string, rsp *corePb.IcpResponse, source string, method string) {
	RecordTime := sql.NullTime{Valid: true, Time: time.Now()}
	icpNumber := utils.RemoveIcpNumber(rsp.Info.Icp)
	parsedAuditTime := utils.TimeStringToGoTime(rsp.Info.AuditTime)
	if !parsedAuditTime.IsZero() {
		RecordTime = sql.NullTime{Time: parsedAuditTime, Valid: true}
	}
	log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,企业名称:%s,来源:%s,方法:%s", companyName, source, method))
	// 检查数据库企业名称,和备案号是否存在,存在且不一致更新为已注销
	if uErr := company_icp.NewCompanyIcpModel().SetOfflineBy(icpNumber, companyName); uErr != nil {
		log.WithContextWarnf(ctx, "备案查询服务,备案信息入库更新,ICP:%s,企业名称:%s,入库失败,更新备案注销信息失败:%s,来源:%s,方法:%s", icpNumber, companyName, uErr.Error(), source, method)
	}
	log.WithContextInfof(ctx, "C---------------------")
	// 如果存在数据,更新为在线
	_ = company_icp.NewCompanyIcpModel().Updates(
		company_icp.CompanyIcp{Status: company_icp.IcpStatusOnline},
		mysql.WithWhere("name = ? and icp = ? and parent_id = 0", companyName, icpNumber),
	)
	log.WithContextInfof(ctx, "B---------------------")
	// 数据数据库同企业备案信息
	mainIcp, subIcp, err := company_icp.NewCompanyIcpModel().FindOnlineByCompanyName(companyName)
	if err != nil {
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,企业名称:%s,入库失败:%s,来源:%s,方法:%s", companyName, err.Error(), source, method))
	}
	log.WithContextInfof(ctx, "S---------------------%+v", mainIcp)
	// 数据库不存在该企业正常备案信息
	if mainIcp == nil {
		// 写入根信息
		mainIcp = &company_icp.CompanyIcp{
			Name:       companyName,
			Icp:        icpNumber,
			ParentId:   0,
			Type:       company_icp.GetTypeInt(rsp.Info.CompanyType),
			RecordTime: RecordTime,
			Status:     company_icp.IcpStatusOnline,
			Source:     source,
		}
		if iErr := company_icp.NewCompanyIcpModel().Create(mainIcp); iErr != nil {
			log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,企业名称:%s,入库失败,写入根信息异常:%s,,来源:%s,方法:%s", companyName, iErr.Error(), source, method))
			return
		}
	} else {
		mainIcp.Name = companyName
		mainIcp.Status = company_icp.IcpStatusOnline
		mainIcp.Type = company_icp.GetTypeInt(rsp.Info.CompanyType)
		mainIcp.RecordTime = RecordTime
		mainIcp.Source = source
		mainIcp.UpdatedAt = time.Now()
		// 更新时间,刷新数据查询时的更新实现
		_ = company_icp.NewCompanyIcpModel().Updates(*mainIcp, mysql.WithWhere("id", mainIcp.Id))
	}
	tmpIcpList := make([]*corePb.BeiAn, 0, len(rsp.Equals)+1)
	tmpIcpList = append(tmpIcpList, rsp.Info)
	tmpIcpList = append(tmpIcpList, rsp.Equals...)
	dbDomains := utils.ListColumn(subIcp, func(t company_icp.CompanyIcp) string { return t.Domain })
	icpDomains := utils.ListColumn(tmpIcpList, func(t *corePb.BeiAn) string { return t.WebsiteUrl })
	// 清除未备案信息,域名,企业名称,ICP
	_, _ = company_icp.NewUnregisteredIcpModel().Delete(mysql.WithValuesIn("keyword", append(icpDomains, []string{companyName, icpNumber}...)))
	// 不存在于备案数据中,已注销该域名备案,更新为注销状态
	for i := range subIcp {
		if !utils.ListContains(icpDomains, subIcp[i].Domain) {
			_ = company_icp.NewCompanyIcpModel().Updates(company_icp.CompanyIcp{Status: company_icp.IcpStatusOffline}, mysql.WithWhere("id", subIcp[i].Id))
		}
	}
	// 不存在于数据库数据中,该域名为新增备案,添加新的记录
	for x := range tmpIcpList {
		if tmpIcpList[x].WebsiteUrl == "" || tmpIcpList[x].CompanyName == "" {
			log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,企业名称:%s,备案信息:%+v,入库失败,备案信息获取异常,跳过该数据,来源:%s,方法:%s", companyName, tmpIcpList[x], source, method))
			continue
		}
		if !utils.ListContains(dbDomains, tmpIcpList[x].WebsiteUrl) {
			record := &company_icp.CompanyIcp{
				Status: company_icp.IcpStatusOnline, ParentId: mainIcp.Id, Icp: tmpIcpList[x].Icp,
				Name: companyName, Type: company_icp.GetTypeInt(tmpIcpList[x].CompanyType),
				Domain: strings.TrimSpace(tmpIcpList[x].WebsiteUrl), Source: source, RecordTime: RecordTime,
			}
			// 检查备案是否已存在
			if firstIcp, qErr := company_icp.NewCompanyIcpModel().First(
				mysql.WithWhere("parent_id", mainIcp.Id),
				mysql.WithWhere("domain", tmpIcpList[x].WebsiteUrl),
			); qErr != nil {
				if errstd.Is(qErr, gorm.ErrRecordNotFound) {
					if cErr := company_icp.NewCompanyIcpModel().Create(record); cErr != nil {
						log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,企业名称:%s,备案信息:%+v,入库失败,写入备案域名信息异常:%s,来源:%s,方法:%s", companyName, record, cErr.Error(), source, method))
					}
				} else {
					if upErr := company_icp.NewCompanyIcpModel().Updates(*record,
						mysql.WithWhere("parent_id", mainIcp.Id),
						mysql.WithWhere("domain", tmpIcpList[x].WebsiteUrl),
					); upErr != nil {
						log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,企业名称:%s,备案信息:%+v,入库失败,更新备案域名信息异常:%s,来源:%s,方法:%s", companyName, record, upErr.Error(), source, method))
					}
				}
			} else {
				if upErr := company_icp.NewCompanyIcpModel().Updates(*record, mysql.WithWhere("id", firstIcp.Id)); upErr != nil {
					log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,企业名称:%s,备案信息:%+v,入库失败,更新备案域名信息异常:%s,来源:%s,方法:%s", companyName, record, upErr.Error(), source, method))
				}
			}
		}
	}
}

func insertDbIcpByPerson(ctx context.Context, companyName string, rsp *corePb.IcpResponse, source string) {
	rsp.Equals = append(rsp.Equals, rsp.Info)
	beiArrByIcps := make(map[string][]*corePb.BeiAn, 0)
	for y := range rsp.Equals {
		bei := rsp.Equals[y]
		icpNumber := utils.RemoveIcpNumber(bei.Icp)
		beiArrByIcps[icpNumber] = append(beiArrByIcps[icpNumber], bei)
	}
	for icpNoNum, beis := range beiArrByIcps {
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,个人名称:%s,入库信息:%+v,来源:%s", companyName, beis, source))
		if icpNoNum == "" {
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,个人名称:%s,Error:ICP号为空,跳过该条数据,入库信息:%+v,来源:%s", icpNoNum, beis, source))
			continue
		}
		// 检查数据库备案号是否存在,存在统一更新为注销
		if uErr := company_icp.NewCompanyIcpModel().SetOfflineByIcpAndName(icpNoNum, companyName); uErr != nil {
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,ICP:%s,个人名称:%s,入库失败,更新备案注销信息失败:%s,来源:%s", icpNoNum, companyName, uErr.Error(), source))
		}
		// 如果存在数据,更新为在线
		_ = company_icp.NewCompanyIcpModel().Updates(
			company_icp.CompanyIcp{Status: company_icp.IcpStatusOnline},
			mysql.WithWhere("name = ? and icp = ? and parent_id = 0", companyName, icpNoNum),
		)
		RecordTime := sql.NullTime{Valid: true, Time: time.Now()}
		// 数据数据库同企业备案信息
		mainIcp, subIcp, err := company_icp.NewCompanyIcpModel().FindOnlineByIcp(icpNoNum)
		if err != nil {
			log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,个人名称:%s,入库失败:%s,来源:%s", companyName, err.Error(), source))
		}
		// 数据库不存在该企业正常备案信息
		if mainIcp == nil {
			// 写入根信息
			mainIcp = &company_icp.CompanyIcp{
				Name:       companyName,
				Icp:        icpNoNum,
				ParentId:   0,
				Type:       company_icp.GetTypeInt(rsp.Info.CompanyType),
				RecordTime: RecordTime,
				Status:     company_icp.IcpStatusOnline,
				Source:     source,
			}
			if iErr := company_icp.NewCompanyIcpModel().Create(mainIcp); iErr != nil {
				log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,个人名称:%s,入库失败,写入根信息异常:%s,来源:%s", companyName, iErr.Error(), source))
				return
			}
		} else {
			mainIcp.Name = companyName
			mainIcp.Status = company_icp.IcpStatusOnline
			mainIcp.Type = company_icp.GetTypeInt(rsp.Info.CompanyType)
			mainIcp.RecordTime = RecordTime
			mainIcp.Source = source
			mainIcp.UpdatedAt = time.Now()
			// 更新时间,刷新数据查询时的更新实现
			_ = company_icp.NewCompanyIcpModel().Updates(*mainIcp, mysql.WithWhere("id", mainIcp.Id))
		}
		dbDomains := utils.ListColumn(subIcp, func(t company_icp.CompanyIcp) string { return t.Domain })
		icpDomains := utils.ListColumn(beis, func(t *corePb.BeiAn) string { return t.WebsiteUrl })
		// 清除未备案信息,域名,企业名称,ICP
		_, _ = company_icp.NewUnregisteredIcpModel().Delete(mysql.WithValuesIn("keyword", append(icpDomains, []string{companyName, icpNoNum}...)))
		// 不存在于备案数据中,已注销该域名备案,更新为注销状态
		for i := range subIcp {
			if !utils.ListContains(icpDomains, subIcp[i].Domain) {
				_ = company_icp.NewCompanyIcpModel().Updates(company_icp.CompanyIcp{Status: company_icp.IcpStatusOffline}, mysql.WithWhere("id", subIcp[i].Id))
			}
		}
		// 不存在于数据库数据中,该域名为新增备案,添加新的记录
		for x := range beis {
			if !utils.ListContains(dbDomains, beis[x].WebsiteUrl) {
				record := &company_icp.CompanyIcp{
					Status: company_icp.IcpStatusOnline, ParentId: mainIcp.Id, Icp: beis[x].Icp,
					Name: companyName, Type: company_icp.GetTypeInt(beis[x].CompanyType),
					Domain: strings.TrimSpace(beis[x].WebsiteUrl), Source: source, RecordTime: RecordTime,
				}
				// 检查备案是否已存在
				if firstIcp, qErr := company_icp.NewCompanyIcpModel().First(
					mysql.WithWhere("parent_id", mainIcp.Id),
					mysql.WithWhere("domain", beis[x].WebsiteUrl),
				); qErr != nil {
					if errstd.Is(qErr, gorm.ErrRecordNotFound) {
						if cErr := company_icp.NewCompanyIcpModel().Create(record); cErr != nil {
							log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,个人名称:%s,备案信息:%+v,入库失败,写入备案域名信息异常:%s,来源:%s", companyName, record, cErr.Error(), source))
						}
					} else {
						if upErr := company_icp.NewCompanyIcpModel().Updates(*record,
							mysql.WithWhere("parent_id", mainIcp.Id),
							mysql.WithWhere("domain", beis[x].WebsiteUrl),
						); upErr != nil {
							log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,个人名称:%s,备案信息:%+v,入库失败,更新备案域名信息异常:%s,来源:%s", companyName, record, upErr.Error(), source))
						}
					}
				} else {
					if upErr := company_icp.NewCompanyIcpModel().Updates(*record, mysql.WithWhere("id", firstIcp.Id)); upErr != nil {
						log.WithContextWarnf(ctx, fmt.Sprintf("备案查询服务,备案信息入库更新,个人名称:%s,备案信息:%+v,入库失败,更新备案域名信息异常:%s,来源:%s", companyName, record, upErr.Error(), source))
					}
				}
			}
		}
	}
}

// insertDbIcpByCompanyName 入库备案信息
func insertDbIcpByCompanyName(ctx context.Context, companyName string, rsp *corePb.IcpResponse, source string, method string) {
	if companyName == "" {
		return
	}
	if rsp.Info == nil {
		return
	}
	// 加锁,避免数据并发入库
	if !redis.Lock("L:ICP:"+companyName, 10*time.Second) {
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称:%s,并发入库处理,来源:%s,方法:%s", companyName, source, method))
		return
	}
	defer func() {
		redis.UnLock("L:ICP:" + companyName)
	}()
	// 背面外层操作,引发数据丢失问题
	insertRsp := rsp
	// 个人备案走个人备案入库逻辑
	if rsp.Info.CompanyType == personStr {
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称:%s,走个人入库逻辑,来源:%s,方法:%s", companyName, source, method))
		insertDbIcpByPerson(ctx, companyName, insertRsp, source)
	} else {
		log.WithContextInfof(ctx, fmt.Sprintf("备案查询服务,企业名称:%s,走企业入库逻辑,来源:%s,方法:%s", companyName, source, method))
		insertDbIcpByOrg(ctx, companyName, insertRsp, source, method)
	}
}

func diffResponseIcpInfos(icps []company_icp.CompanyIcp, rsp *corePb.IcpResponse) []company_icp.CompanyIcp {
	var unique []string
	if rsp.Info != nil {
		unique = append(unique, rsp.Info.Icp+rsp.Info.WebsiteUrl)
	}
	if len(rsp.Equals) != 0 {
		for i := range rsp.Equals {
			unique = append(unique, rsp.Equals[i].Icp+rsp.Equals[i].WebsiteUrl)
		}
	}
	for i := 0; i < len(icps); i++ {
		if utils.ListContains(unique, icps[i].Icp+icps[i].Domain) {
			icps = append(icps[:i], icps[i+1:]...)
			i--
		}
	}
	return icps
}

// formatIcpResponse 格式化返回数据
func formatIcpResponse(icps []company_icp.CompanyIcp, rsp *corePb.IcpResponse) {
	if len(icps) == 0 {
		return
	}
	icps = diffResponseIcpInfos(icps, rsp)
	for index := range icps {
		if index == 0 && rsp.Info == nil {
			rsp.Info = &corePb.BeiAn{
				CompanyName: icps[index].Name,
				CompanyType: company_icp.GetTypeString(icps[index].Type),
				Icp:         icps[index].Icp,
				WebsiteUrl:  icps[index].Domain,
			}
			if icps[index].RecordTime.Time.String() != emptyTimeStr {
				rsp.Info.AuditTime = icps[index].RecordTime.Time.String()
			} else {
				rsp.Info.AuditTime = ""
			}
		} else {
			bInfo := &corePb.BeiAn{
				CompanyName: icps[index].Name,
				CompanyType: company_icp.GetTypeString(icps[index].Type),
				Icp:         icps[index].Icp,
				WebsiteUrl:  icps[index].Domain,
			}
			if icps[index].RecordTime.Time.String() != emptyTimeStr {
				bInfo.AuditTime = icps[index].RecordTime.Time.String()
			} else {
				bInfo.AuditTime = ""
			}
			rsp.Equals = append(rsp.Equals, bInfo)
		}
	}
}

// 加载fofa的icp备案信息
func loadFofaICPInfo(ctx context.Context, rsp *corePb.IcpResponse, queryType string, value string) error {
	// 调用mcp的ICP查询
	mcpResult, err := fofatools.QueryICP(ctx, &fofatools.ICPQueryRequest{
		QueryType:   fofatools.ICPQueryType(queryType),
		Value:       value,
		Size:        300,
		InclueHuman: true,
	})
	if err != nil {
		log.WithContextWarnf(ctx, "mcp ICP查询失败: %v", err)
		return nil // 不返回错误，继续执行其他查询
	}

	// 将mcp结果转换为IcpResponse格式
	if mcpResult != nil && len(mcpResult.Records) > 0 {
		// 设置主记录
		if rsp.Info == nil {
			websiteUrl := mcpResult.Records[0].Domain
			if websiteUrl == "" && mcpResult.Records[0].IP != "" {
				websiteUrl = mcpResult.Records[0].IP
			}
			rsp.Info = &corePb.BeiAn{
				CompanyName: mcpResult.Records[0].Org,
				Icp:         mcpResult.Records[0].ICP,
				WebsiteUrl:  websiteUrl,
				CompanyType: mcpResult.Records[0].OrgNature,
			}
			rsp.Ba = true
		}

		// 添加其他记录到Equals
		for i := 0; i < len(mcpResult.Records); i++ {
			record := mcpResult.Records[i]
			websiteUrl := record.Domain
			if websiteUrl == "" && record.IP != "" {
				websiteUrl = record.IP
			}
			beiAn := &corePb.BeiAn{
				CompanyName: record.Org,
				Icp:         record.ICP,
				WebsiteUrl:  websiteUrl,
				CompanyType: mcpResult.Records[0].OrgNature,
			}
			rsp.Equals = append(rsp.Equals, beiAn)
		}
	}

	return nil
}
