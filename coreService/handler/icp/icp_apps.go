package icp

import (
	"context"
	"errors"
	"fmt"
	"time"

	"micro-service/coreService/handler/miit"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/icp_apps"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"gorm.io/gorm"
)

type icpAppSearch struct {
	Search   string // 搜索内容
	searchBy string // 搜索类型 name/icp/app
	Type     int    // 类型
	recordId uint64
	force    bool // 是否强制更新
	getAll   bool // 是否获取所有备案信息
}

type icpApp struct {
	cacheKey string
}

func (ia *icpApp) preprocess(param icpAppSearch) {
	ia.cacheKey = ia.genKey(param)
}

func (ia *icpApp) genKey(param icpAppSearch) string {
	var key = utils.Md5Hash(param)
	switch param.Type {
	case icp_apps.MiniProgram:
		key = "icp_apps:mini-program:" + key
	case icp_apps.App:
		key = "icp_apps:app:" + key
	case icp_apps.FastApp:
		key = "icp_apps:fast-app:" + key
	}
	return key
}

func AppByIcp(ctx context.Context, req *pb.IcpAppRequest, rsp *pb.IcpAppResponse) error {
	c := &icpApp{}
	err := c.search(ctx, icpAppSearch{
		Search:   req.Search,
		searchBy: "icp",
		Type:     int(req.AppType),
		force:    req.Force,
		getAll:   req.GetEquals,
	}, rsp)
	return err
}

func AppByCompanyName(ctx context.Context, req *pb.IcpAppRequest, rsp *pb.IcpAppResponse) error {
	c := &icpApp{}
	err := c.search(ctx, icpAppSearch{
		Search:   req.Search,
		searchBy: "name",
		Type:     int(req.AppType),
		force:    req.Force,
		getAll:   req.GetEquals,
	}, rsp)
	return err
}

func AppByAppName(ctx context.Context, req *pb.IcpAppRequest, rsp *pb.IcpAppResponse) error {
	c := &icpApp{}
	err := c.search(ctx, icpAppSearch{
		Search:   req.Search,
		searchBy: "app",
		Type:     int(req.AppType),
		force:    req.Force,
		getAll:   req.GetEquals,
	}, rsp)
	return err
}

func (ia *icpApp) search(ctx context.Context, param icpAppSearch, rsp *pb.IcpAppResponse) error {
	ia.preprocess(param)
	if !param.force {
		// 1. 查缓存
		if redis.GetCache(ia.cacheKey, &rsp) {
			if !param.getAll {
				rsp.Equals = nil
			}
			log.WithContextInfof(ctx, "[icp-apps] Got key: %s cache, req param: %v", ia.cacheKey, param)
			return nil
		}

		// 2. 查数据库
		// 2.1 是否查无结果
		var db = icp_apps.NewModel()
		unregistered, err := db.UnregisteredFirst(param.Search, param.Type)
		switch {
		case errors.Is(err, gorm.ErrRecordNotFound):
		case err != nil:
			return err
		case unregistered.UpdatedAt.Add(time.Duration(cfg.LoadCommon().IcpCacheDay) * utils.Day).After(time.Now()):
			return nil
		}

		// 2.2 查询搜索历史
		recordItem, err := db.RecordFirst(mysql.WithWhere("name=? AND `type`=?", param.Search, param.Type))
		switch {
		case errors.Is(err, gorm.ErrRecordNotFound):
			goto force
		case err != nil:
			return err
		case recordItem.UpdatedAt.Add(time.Duration(cfg.LoadCommon().IcpCacheDay) * utils.Day).Before(time.Now()):
			param.recordId = recordItem.Id
			goto force
		}

		// 2.3 数据库查询
		err = ia.icpAppsFromDb(param, rsp)
		return err
	}

force:
	err := ia.searchByMIIT(ctx, param, rsp)
	return err
}

func (ia *icpApp) icpAppsFromDb(param icpAppSearch, rsp *pb.IcpAppResponse) error {
	handle := make([]mysql.HandleFunc, 0)
	handle = append(handle, mysql.WithColumnValue("type", param.Type), mysql.WithColumnValue("status", icp_apps.StatusOnline))
	switch param.searchBy {
	case "name":
		handle = append(handle, mysql.WithColumnValue("`name`", param.Search))
	case "icp":
		handle = append(handle, mysql.WithColumnValue("`icp`", param.Search))
	case "app":
		handle = append(handle, mysql.WithColumnValue("`app_name`", param.Search))
	}

	db := icp_apps.NewModel()
	l, _, err := db.EquityList(1, 1, handle...)
	if len(l) == 0 {
		return err
	}

	var icpApps []*icp_apps.Equity
	if param.getAll {
		icpApps, _, err = db.EquityList(0, 0,
			mysql.WithWhere("name=? AND parent_id !=0 AND status=? AND `type`=?", l[0].Name, icp_apps.StatusOnline, param.Type))
	}
	if err != nil {
		return err
	}

	_ = redis.SetCache(ia.cacheKey, 5*time.Minute, rsp)

	ia.formatResponse(l[0], icpApps, rsp)

	return nil
}

// 工信部查询
func (ia *icpApp) searchByMIIT(ctx context.Context, param icpAppSearch, rsp *pb.IcpAppResponse) error {
	appType := ia.convert(param.Type)
	queryRsp, err := miit.QueryApp(ctx, appType, param.Search)
	if err != nil {
		return err
	}

	db := icp_apps.NewModel()
	// 未查到搜索内容的ICP备案信息
	parent := queryRsp.Parent
	if parent.Name == "" {
		return db.UnregisteredUpsert(&icp_apps.UnregisteredIcpApp{Keyword: param.Search, Type: param.Type})
	}

	x, err := db.EquityFirst(mysql.WithWhere("`name`=? AND `parent_id`=0 AND `icp`=?", parent.Name, parent.Icp))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		err = db.EquityCreate(&parent)
	case err != nil:
		return fmt.Errorf("query parent info failed, caused by %v", err)
	default:
		parent.Id = x.Id
		parent.CreatedAt = x.CreatedAt
		err = db.EquitySave([]*icp_apps.Equity{&parent})
	}
	if err != nil {
		return err
	}

	go ia.insertAppIcpToDbWithLock(ctx, &parent, queryRsp.Children)
	go ia.upsertRecord(param.recordId, param.Search, param.Type)

	ia.formatResponse(&queryRsp.QueryItem, queryRsp.Children, rsp)

	return nil
}

func (ia *icpApp) convert(x int) int {
	var miitType int
	switch x {
	case icp_apps.MiniProgram:
		miitType = miit.TypeMiniProgram
	case icp_apps.App:
		miitType = miit.TypeAPP
	case icp_apps.FastApp:
		miitType = miit.TypeFastApp
	}
	return miitType
}

func (ia *icpApp) equityType(appType int) int {
	switch appType {
	case miit.TypeMiniProgram:
		return icp_apps.MiniProgram
	case miit.TypeAPP:
		return icp_apps.App
	case miit.TypeFastApp:
		return icp_apps.FastApp
	}
	return 0
}

func (ia *icpApp) upsertRecord(id uint64, search string, xType int) {
	var err error
	var item = icp_apps.Record{
		Name:     search,
		Type:     xType,
		EquityId: 0,
	}
	item.Id = id
	if id > 0 {
		err = icp_apps.NewModel().RecordUpdate(item)
	} else {
		err = icp_apps.NewModel().RecordCreate(&item)
	}
	if err != nil {
		log.Errorf("[ICP-APP] upsert icp_record failed: %v", err)
	}
}

func (ia *icpApp) formatResponse(searchItem *icp_apps.Equity, children []*icp_apps.Equity, rsp *pb.IcpAppResponse) {
	if searchItem != nil {
		rsp.Info = &pb.IcpAppResponse_EquityBasic{
			Name:    searchItem.Name,
			AppName: searchItem.AppName,
			Icp:     searchItem.Icp,
			Status:  int32(searchItem.Status),
		}
	}

	for i := range children {
		rsp.Equals = append(rsp.Equals, &pb.IcpAppResponse_EquityBasic{
			Name:    children[i].Name,
			AppName: children[i].AppName,
			Icp:     children[i].Icp,
			Status:  int32(children[i].Status),
		})
	}
}

func (ia *icpApp) insertAppIcpToDbWithLock(ctx context.Context, parent *icp_apps.Equity, items []*icp_apps.Equity) {
	var err error
	key := fmt.Sprintf("lock:icp_apps:%s:update_children", parent.Name)
	timer := time.NewTimer(5 * time.Minute)
	for {
		time.Sleep(time.Second)
		select {
		case <-timer.C:
			goto exit
		default:
			if !redis.Lock(key, 10*time.Second) {
				continue
			}
			err = ia.insertAppIcpToDb(parent, items)
			redis.UnLock(key)
			goto exit
		}
	}

exit:
	if err != nil {
		log.WithContextErrorf(ctx, "[ICP-APP] insertAppIcpToDb, parent_name: %s, %v", parent.Name, err)
	}
}

func (ia *icpApp) insertAppIcpToDb(parent *icp_apps.Equity, items []*icp_apps.Equity) error {
	db := icp_apps.NewModel()
	list, _, errList := db.EquityList(0, 0, mysql.WithColumnValue("parent_id", parent.Id))
	if errList != nil {
		return fmt.Errorf("get parent_id: %d children failed: %w", parent.Id, errList)
	}

	save := make([]*icp_apps.Equity, 0, len(items))
	for _, child := range items {
		exist := false
		child.ParentId = parent.Id
		child.Type = ia.equityType(child.Type)
		for _, y := range list {
			if child.AppName != y.AppName || child.Icp != y.Icp {
				continue
			}
			exist = true
			child.Id = y.Id
			child.CreatedAt = y.CreatedAt
			save = append(save, child)
			break
		}
		if !exist {
			save = append(save, child)
		}
	}
	return icp_apps.NewModel().EquitySave(save)
}
