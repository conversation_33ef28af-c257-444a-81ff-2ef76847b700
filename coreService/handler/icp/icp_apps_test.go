package icp

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql/icp_apps"
)

func Test_IcpApp_CompanyName(t *testing.T) {
	Init()

	req := &pb.IcpAppRequest{
		Search:    "深圳市腾讯计算机系统有限公司",
		AppType:   icp_apps.MiniProgram,
		Force:     true,
		GetEquals: false,
	}

	var rsp = &pb.IcpAppResponse{}
	err := AppByCompanyName(context.TODO(), req, rsp)
	time.Sleep(5 * time.Second)
	assert.Nil(t, err)
}

func Test_IcpApp_Icp(t *testing.T) {
	Init()

	req := &pb.IcpAppRequest{
		Search:    "粤B2-20090059",
		AppType:   icp_apps.App,
		Force:     false,
		GetEquals: false,
	}

	var rsp = &pb.IcpAppResponse{}
	err := AppByCompanyName(context.TODO(), req, rsp)
	time.Sleep(3 * time.Second)
	assert.Nil(t, err)
}
