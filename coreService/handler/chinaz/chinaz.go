package chinaz

import (
	"context"
	"fmt"
	"strings"

	corePb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/PuerkitoBio/goquery"
)

const NotBeiAnStr = "当前域名未备案或者备案取消"
const BeiAnNumberStr = "备案号"

func QueryDomain(ctx context.Context, rsp *corePb.IcpResponse, domain string) error {
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    fmt.Sprintf("https://micp.chinaz.com?query=%s", domain),
		Method: crawlerPb.MethodChromeGet,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return err
	}
	log.WithContextInfof(ctx, "crawler Chinaz11111 domain-> domain:%s,code:%d,size:%d", domain, crawlerRes.Code, len(crawlerRes.Body))
	// Load the HTML document
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
	if err != nil {
		return err
	}
	// 检查域名是否备案
	if doc.Find("#icp-cont").First().Contents().Text() == NotBeiAnStr {
		rsp.Ba = false
		return nil
	}
	infoNode := doc.Find("#icp-cont > .wt-mian > table").First()
	if len(infoNode.Nodes) != 0 {
		rsp.Info = &corePb.BeiAn{
			CompanyName: infoNode.Find("tbody > tr:nth-child(1) > td:nth-child(2)").First().Contents().Text(),
			Icp:         infoNode.Find("tbody > tr:nth-child(3) > td:nth-child(2)").First().Contents().Text(),
			CompanyType: infoNode.Find("tbody > tr:nth-child(2) > td:nth-child(2)").First().Contents().Text(),
			WebsiteUrl:  infoNode.Find("tbody > tr:nth-child(5) > td:nth-child(2)").First().Contents().Text(),
			AuditTime:   infoNode.Find("tbody > tr:nth-child(7) > td:nth-child(2)").First().Contents().Text(),
		}
		rsp.Ba = true
	}
	equalsNode := doc.Find("#icp-cont > .wt-mian > table").Last()
	if len(equalsNode.Nodes) != 0 {
		equalsNode.Find("tbody > tr").Each(func(i int, rowNode *goquery.Selection) {
			// 跳过表头
			if rowNode.Find("td:nth-child(1)").First().Text() == BeiAnNumberStr {
				return
			}
			rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
				CompanyName: rsp.Info.CompanyName,
				Icp:         rowNode.Find("td:nth-child(1)").First().Contents().Text(),
				CompanyType: rsp.Info.CompanyType,
				WebsiteUrl:  rowNode.Find("td:nth-child(2)").First().Contents().Last().Text(),
				AuditTime:   rowNode.Find("td:nth-child(3)").First().Contents().Text(),
			})
		})
	}
	return nil
}

func QueryIcp(ctx context.Context, rsp *corePb.IcpResponse, icp string) error {
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:     "https://micp.chinaz.com",
		Scripts: []*crawlerPb.Script{{JsCode: fmt.Sprintf("document.querySelector('#query').value='%s';", icp)}},
		Clicks: []*crawlerPb.Click{
			{Selector: "#dropsch > button"},
			{Selector: "#dropsch > ul > li:nth-child(2) > a"},
			{Selector: "#icp-form > div.input-group > span > button", WaitTime: 500},
		},
		Method: crawlerPb.MethodChromeGet,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return err
	}
	log.WithContextInfof(ctx, "crawler Chinaz Icp-> script: %v", crawlerRes.ScriptRes)
	log.WithContextInfof(ctx, "crawler Chinaz Icp-> icp:%s,code:%d,size:%d", icp, crawlerRes.Code, len(crawlerRes.Body))
	// Load the HTML document
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
	if err != nil {
		return err
	}
	// 检查域名是否备案
	if doc.Find("#icp-cont").First().Contents().Text() == NotBeiAnStr {
		rsp.Ba = false
		return nil
	}
	infoNode := doc.Find("#icp-cont > .wt-mian > table").First()
	if len(infoNode.Nodes) != 0 {
		rsp.Info = &corePb.BeiAn{
			CompanyName: infoNode.Find("tbody > tr:nth-child(1) > td:nth-child(2)").First().Contents().Text(),
			Icp:         infoNode.Find("tbody > tr:nth-child(3) > td:nth-child(2)").First().Contents().Text(),
			CompanyType: infoNode.Find("tbody > tr:nth-child(2) > td:nth-child(2)").First().Contents().Text(),
			WebsiteUrl:  infoNode.Find("tbody > tr:nth-child(5) > td:nth-child(2)").First().Contents().Text(),
			AuditTime:   infoNode.Find("tbody > tr:nth-child(7) > td:nth-child(2)").First().Contents().Text(),
		}
		rsp.Ba = true
	}
	equalsNode := doc.Find("#icp-cont > .wt-mian > table").Last()
	if len(equalsNode.Nodes) != 0 {
		equalsNode.Find("tbody > tr").Each(func(i int, rowNode *goquery.Selection) {
			// 跳过表头
			if rowNode.Find("td:nth-child(1)").First().Text() == BeiAnNumberStr {
				return
			}
			rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
				CompanyName: rsp.Info.CompanyName,
				Icp:         rowNode.Find("td:nth-child(1)").First().Contents().Text(),
				CompanyType: rsp.Info.CompanyType,
				WebsiteUrl:  rowNode.Find("td:nth-child(2)").First().Contents().Last().Text(),
				AuditTime:   rowNode.Find("td:nth-child(3)").First().Contents().Text(),
			})
		})
	}
	return nil
}
