package chinaz

import (
	"fmt"
	"github.com/joho/godotenv"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

func TestApiCompanyName(t *testing.T) {
	_ = godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	rsp := &pb.IcpResponse{}
	if err := ApiLishiCompanyName(rsp, "沧州日报社"); err != nil {
		println(err.Error())
	}
	println(fmt.Sprintf("%+v", rsp))
}

func TestApiDomain(t *testing.T) {
	_ = godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	rsp := &pb.IcpResponse{}
	if err := ApiLishiDomain(rsp, "foradar.net", true); err != nil {
		println(err.Error())
	}
	println(fmt.Sprintf("%+v", rsp))
}

func TestApiDomainNow(t *testing.T) {
	_ = godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	rsp := &pb.IcpResponse{}
	if err := ApiDomain(rsp, "andexpo.com"); err != nil {
		println(err.Error())
	}
	println(fmt.Sprintf("%+v", rsp))
}
