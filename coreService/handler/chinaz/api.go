package chinaz

import (
	"context"
	"database/sql"
	"errors"
	corePb "micro-service/coreService/proto"
	"micro-service/middleware/mysql/whois"
	"micro-service/pkg/cfg"
	"micro-service/pkg/chinaz"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"
	"time"

	"golang.org/x/net/idna"
)

// 域名实时备案查询-站长之家
func ApiDomain(rsp *corePb.IcpResponse, domain string) error {
	token := cfg.LoadChinaz().ICPToken
	if token == "" {
		log.Warnf("[Core]: ApiDomain CompanyName 没取到consul配置的token,Domain:%s, token:%s", domain, token)
		token = "apiuser_quantity_e322d1b527154346cfd6e99c39284c74_617118b4da01429696e3077ba79425d4"
	}
	r, e := chinaz.NewDefaultClient(token).Domain(context.Background(), &chinaz.ICPRequest{Domain: domain})
	if e != nil {
		log.Warnf("[Core]: ApiDomain Domain  域名实时备案查询-站长之家 接口报错了，不正常,域名：%s, Error:%v", domain, e)
		return e
	}

	if r.StateCode != 1 || strings.Contains(r.Reason, "暂无备案信息") {
		log.Warnf("[Core]: ApiDomain Domain  域名实时备案查询-站长之家 接口告诉我暂无备案信息或者状态码不对,域名：%s, StateCode:%v", domain, r.StateCode)
		return errors.New(r.Reason)
	}
	domains := utils.ListColumn(rsp.Equals, func(t *corePb.BeiAn) string {
		return t.WebsiteUrl
	})
	if !utils.ListContains(domains, r.Result.Domain) {
		rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
			CompanyName: r.Result.CompanyName,
			CompanyType: r.Result.CompanyType,
			Icp:         r.Result.SiteLicense,
			WebsiteUrl:  r.Result.Domain,
			AuditTime:   r.Result.VerifyTime,
		})
	}

	if len(rsp.Equals) > 0 {
		rsp.Info = rsp.Equals[0]
		rsp.Ba = true
	}
	return nil
}

// 实时备案查询-站长之家
func ApiCompanyName(rsp *corePb.IcpResponse, companyName string) error {
	// 企业名称为空，直接返回
	if companyName == "" {
		log.Warnf("[Core]: ApiCompanyName CompanyName resp, Error:查询的企业名称为空")
		return nil
	}
	token := cfg.LoadChinaz().CompanyNameToken
	if token == "" {
		log.Warnf("[Core]: ApiCompanyName CompanyName 没取到consul配置的token,companyName:%s, token:%s", companyName, token)
		token = "apiuser_quantity_1a0cfcd348cdd4fed482782a588e3204_416a1072967c482b808de9f2b5735c2b"
	}
	r, e := chinaz.NewDefaultClient(token).CompanyName(context.Background(), &chinaz.CompanyNameRequest{CompanyName: companyName})
	if e != nil {
		log.Warnf("[Core]: ApiCompanyName CompanyName  实时备案查询-站长之家 接口报错了，不正常,CompanyName：%s, Error:%v", companyName, e)
		return e
	}
	if r.StateCode != 1 {
		log.Warnf("[Core]: ApiCompanyName CompanyName  实时备案查询-站长之家 状态码不是1，不正常,CompanyName：%s, StateCode:%v", companyName, r.StateCode)
		return errors.New(r.Reason)
	}
	for i := range r.Result {
		domains := utils.ListColumn(rsp.Equals, func(t *corePb.BeiAn) string {
			return t.WebsiteUrl
		})
		if !utils.ListContains(domains, r.Result[i].Domain) {
			rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
				CompanyName: r.Result[i].UnitName,
				CompanyType: r.Result[i].CompanyType,
				Icp:         r.Result[i].ServiceLicence,
				WebsiteUrl:  r.Result[i].Domain,
				AuditTime:   r.Result[i].VerifyTime,
			})
		}
	}
	if len(rsp.Equals) > 0 {
		rsp.Info = rsp.Equals[0]
		rsp.Ba = true
	}
	return nil
}

func undecodeSponsorsunit(companyName string, in *chinaz.SponsorsunitResult) []*corePb.BeiAn {
	resp := make([]*corePb.BeiAn, 0)
	resp = append(resp, &corePb.BeiAn{
		CompanyName: companyName,
		CompanyType: in.CompanyType,
		Icp:         in.SiteLicense,
		WebsiteUrl:  in.Domain,
		AuditTime:   in.VerifyTime,
	})
	return resp
}

// 历史企业名称备案查询-站长之家
func ApiLishiCompanyName(rsp *corePb.IcpResponse, companyName string) error {
	if companyName == "" {
		return nil
	}
	token := cfg.LoadChinaz().HistoryCompanyNameToken
	if token == "" {
		log.Warnf("[Core]: ApiLishiCompanyName CompanyName 没取到consul配置的token,companyName:%s, token:%s", companyName, token)
		token = "apiuser_quantity_869aa7fe89d171aaf04531bdcdffb9ed_168e55f804b14edb888b1a37d65d1460"
	}
	r, e := chinaz.NewDefaultClient(token).Sponsorsunit(context.Background(), companyName)
	if e != nil {
		log.Warnf("[Core]: ApiLishiCompanyName CompanyName 历史企业名称备案查询 接口报错了，不正常,companyName:%s, Error:%v", companyName, e)
		return e
	}
	if r.StateCode != 1 {
		log.Warnf("[Core]: ApiLishiCompanyName CompanyName 历史企业名称备案查询 状态码不是1，不正常,companyName:%s, StateCode:%v", companyName, r.StateCode)
		return errors.New(r.Reason)
	}
	for i := range r.Result {
		rsp.Equals = append(rsp.Equals, undecodeSponsorsunit(companyName, &r.Result[i])...)
	}
	if len(rsp.Equals) > 0 {
		rsp.Info = rsp.Equals[0]
		rsp.Ba = true
	}
	return nil
}

// ApiLishiDomain 历史domain信息 ，这个接口站长之家没有返回domain字段，只有 MainPage字段，但是MainPage字段是给的整体域名，并不是备案的域名，需要处理
func ApiLishiDomain(rsp *corePb.IcpResponse, domain string, getSubdomain bool) error {
	token := cfg.LoadChinaz().HistoryDomainToken
	if token == "" {
		log.Warnf("[Core]: ApiLishiDomain CompanyName 没取到consul配置的token,companyName:%s, domain:%s", domain, token)
		token = "apiuser_quantity_1b3ce90389f3e35a9277a743bf7b56de_204584ddce84409b860d7dc3d81159c2"
	}
	r, e := chinaz.NewDefaultClient(token).LiShiDomain(context.Background(), domain)
	if e != nil {
		log.Warnf("[Core]: Api Lishi ApiLishiDomain 站长之家历史域名备案查询接口报错了, domain: %s  Error:%v", domain, e)
		return e
	}
	if r.StateCode != 1 {
		log.Warnf("[Core]: Api Lishi ApiLishiDomain 站长之家历史域名备案查询接口返回的状态码不正常, domain: %s, StateCode:%v", domain, r.StateCode)
		return errors.New(r.Reason)
	}
	if r.Reason == "暂无备案信息" {
		return nil
	}
	// 判断是不是子域名，如果是的话，就默认返回子域名，不是的话取主域名
	returnDomain := utils.GetTopDomain(domain)
	if getSubdomain {
		returnDomain = domain
	}
	rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
		CompanyName: r.Result.CompanyName,
		CompanyType: r.Result.CompanyType,
		Icp:         r.Result.SiteLicense,
		WebsiteUrl:  returnDomain,
		AuditTime:   r.Result.VerifyTime,
	})
	if len(rsp.Equals) > 0 {
		rsp.Info = rsp.Equals[0]
		rsp.Ba = true
	}
	return nil
}

// 反查whois
func ApiReverseWhois(ctx context.Context, phone, email, registrant string) []*corePb.Whois {
	var reverse = map[string]string{
		chinaz.QueryTypePhone:      phone,
		chinaz.QueryTypeEmail:      email,
		chinaz.QueryTypeRegistrant: registrant,
	}

	var resp []*corePb.Whois
	for k, v := range reverse {
		if v == "" {
			continue
		}

		r, e := chinaz.NewDefaultClient(cfg.LoadChinaz().ReverseWhoisToken).ReverseWhois(ctx, &chinaz.ReverseWhoisRequest{QueryData: v, QueryType: k})
		if e != nil {
			log.WithContextErrorf(ctx, "chinaz whois err:%v", e.Error())
			continue
		}

		for i := range r.Result {
			idnaDomain, _ := idna.New().ToASCII(r.Result[i].Host)
			if r.Result[i].Host == idnaDomain {
				idnaDomain = ""
			}

			resp = append(resp, &corePb.Whois{
				Domain:   r.Result[i].Host,
				Idna:     idnaDomain,
				Register: r.Result[i].Registrar,
				Email:    r.Result[i].Email,
				Mobile:   r.Result[i].Phone,
			})
		}
	}
	return resp
}

func toTime(str string) time.Time {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	st, _ := time.ParseInLocation("2006-01-02 15:04:05", str, loc) // string转time
	return st
}

// ApiWhois 实时whois
func ApiWhois(ctx context.Context, domain string) (*whois.Whois, error) {
	token := cfg.LoadChinaz().WhoisToken
	if token == "" {
		log.Warnf("[Core]: ApiWhois CompanyName 没取到consul配置的token,domain:%s, token:%s", domain, token)
		token = "apiuser_quantity_37028e83d31cc276da910f5be72eef43_f97bc35d5d8b4aec8bbd9e756ec495a0"
	}
	r, err := chinaz.NewDefaultClient(token).Whois(ctx, &chinaz.WhoisRequest{Domain: domain})
	if err != nil {
		log.WithContextErrorf(ctx, "chinaz whois 查询错误，有报错信息: %v, domain: %s ", err, domain)
		return nil, err
	}

	if r.Code != "1" {
		log.WithContextErrorf(ctx, "chinaz whois 查询结果错误，状态码不正常: %s, domain:%s ", r.Msg, domain)
		return nil, errors.New(r.Msg)
	}

	return &whois.Whois{
		Domain:              r.Data.Domain,
		SponsoringRegistrar: &sql.NullString{String: r.Data.Whois.Registrar, Valid: true},
		RegistrantName:      &sql.NullString{String: r.Data.Whois.Contacts, Valid: true},
		RegistrantMobile:    &sql.NullString{String: r.Data.Whois.RegistrarPhone, Valid: true},
		RegistrantEmail:     &sql.NullString{String: r.Data.Whois.RegistrarEmail, Valid: true},
		RegistrantOrg:       &sql.NullString{String: r.Data.Whois.Company, Valid: true},
		RegistrationDate:    &sql.NullTime{Time: toTime(r.Data.Whois.RegistrationTime), Valid: true},
		ExpirationDate:      &sql.NullTime{Time: toTime(r.Data.Whois.ExpirationTime), Valid: true},
		Dns:                 r.Data.Whois.NameServer,
		Status:              &sql.NullString{String: "ok", Valid: true},
		Raw:                 r.String(),
	}, nil
}
