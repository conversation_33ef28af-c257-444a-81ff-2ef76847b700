package clue

import (
	"context"
	"fmt"
	"time"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"

	"github.com/spf13/cast"
	"go-micro.dev/v4/errors"
)

// GetExpandResult 获取扩展结果
func GetExpandResult(_ context.Context, req *pb.ExpandResultRequest, rsp *pb.ExpandResultResponse) error {
	if req.TaskId == 0 {
		return errors.New(pb.ServiceName, "扩展关键字为空", 400)
	}
	if his, err := general_clues.NewHistoryModel().FindById(req.TaskId); err != nil {
		return err
	} else {
		rsp.Process = cast.ToUint64(his.Process)
		clues := general_clues.NewGeneralCluesModel().GetTaskResult(cast.ToUint64(req.TaskId))
		for i := range clues {
			cl := &pb.ClueInfo{
				Id:          clues[i].Id,
				Type:        cast.ToUint32(clues[i].Type),
				CompanyName: clues[i].CompanyName,
				Content:     clues[i].Content,
				Hash:        clues[i].Hash,
				Platform:    clues[i].Platform,
				Source:      clues[i].Source,
				CertValid:   cast.ToUint32(clues[i].CertValid),
				Confirmed:   cast.ToInt32(clues[i].Confirmed),
				CreatedAt:   clues[i].CreatedAt.Format("2006-01-02 15:04:05"),
				UpdatedAt:   clues[i].UpdatedAt.Format("2006-01-02 15:04:05"),
			}
			// 如果是图片,转换下载地址
			if cl.Type == general_clues.ClueTypeIcon {
				cl.Content = storage.GenDownloadUrl(cl.Content, cast.ToString(cl.Hash))
			}
			rsp.Items = append(rsp.Items, cl)
		}
	}
	return nil
}

// SearchByCompanyName 根据企业名称查找线索
func SearchByCompanyName(_ context.Context, req *pb.ExpandKeywordSearchRequest, rsp *pb.ExpandResultResponse) error {
	if len(req.CompanyName) == 0 {
		return nil
	}
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithValuesIn("company_name", req.CompanyName))
	rsp.Process = 100
	list, _, err := general_clues.NewGeneralCluesModel().List(0, 0, handlers...)
	if err != nil {
		return err
	}
	for i := range list {
		item := &pb.ClueInfo{
			Id:          list[i].Id,
			Type:        cast.ToUint32(list[i].Type),
			CompanyName: list[i].CompanyName,
			Content:     list[i].Content,
			Hash:        list[i].Hash,
			Platform:    list[i].Platform,
			Source:      list[i].Source,
			CertValid:   cast.ToUint32(list[i].CertValid),
			Confirmed:   cast.ToInt32(list[i].Confirmed),
			CreatedAt:   list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   list[i].UpdatedAt.Format(utils.DateTimeLayout),
		}
		if item.Type == general_clues.ClueTypeIcon {
			item.Content = storage.GenDownloadUrl(item.Content, cast.ToString(item.Hash))
		}
		rsp.Items = append(rsp.Items, item)
	}
	return nil
}

// ExpandCompanyName 企业名称扩展线索
func ExpandCompanyName(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	log.WithContextInfof(ctx, fmt.Sprintf("企业名称扩展线索-ExpandCompanyName-core服务-开始,keyword:%s,force:%v,", req.Keyword, req.Force))
	expand := false
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "企业名称为空", 400)
	}
	his, err := general_clues.NewHistoryModel().FindByKeyword(req.Keyword, general_clues.ClueTypeCompanyName, utils.SubDayTime(time.Duration(cfg.LoadCommon().ClueCacheDay)))
	if err == nil {
		expand = req.Force
		log.WithContextInfof(ctx, fmt.Sprintf("企业名称扩展线索-ExpandCompanyName,keyword:%s,force:%v,", req.Keyword, expand))
		// 任务进度不是100时,重新扩展
		if his.Process != 100 {
			expand = true
		}
		rsp.TaskId = his.Id
	} else {
		expand = true
		// 不存在记录,添加记录并返回
		his = general_clues.NewHistoryModel().UpdateHistory(req.Keyword, general_clues.ClueTypeCompanyName)
		rsp.TaskId = his.Id
	}
	if expand {
		log.WithContextInfof(ctx, fmt.Sprintf("企业名称扩展线索-ExpandCompanyName-强制刷新或者没库里的线索总库数据超过了7天进行强制刷新,keyword:%s,force:%v,", req.Keyword, expand))
		his.UpdatedAt = time.Now()
		// 把进度改成14，代表重新跑线索数据
		his.Process = 14
		general_clues.NewHistoryModel().Save(his)
		// 清除历史任务结果关联数据记录
		_ = general_clues.NewGeneralCluesTaskModel().ClearByTask(his.Id)
		time.Sleep(time.Second * 3) // 暂停3秒，因为下边扩展任务可能先执行，上面的mysql保存可能还没保存上呢
		// 开始扩展线索
		go GetExpandClueInstance().StartExpandByCompanyName(ctx, his, &general_clues.GeneralClues{
			Type:        general_clues.ClueTypeCompanyName,
			Content:     req.Keyword,
			CompanyName: req.CompanyName,
			Source:      req.Source,
			Platform:    "INPUT",
		}, expand)
	}
	return nil
}

// ExpandIcp ICP扩展线索
func ExpandIcp(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	expand := false
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "企业名称为空", 400)
	}
	// 去除ICP编号
	req.Keyword = utils.RemoveIcpNumber(req.Keyword)
	his, err := general_clues.NewHistoryModel().FindByKeyword(req.Keyword, general_clues.ClueTypeIcp, utils.SubDayTime(time.Duration(cfg.LoadCommon().ClueCacheDay)))
	if err == nil {
		expand = req.Force
		// 任务进度不是100时,重新扩展
		if his.Process != 100 {
			expand = true
		}
		// 存在记录直接返回
		rsp.TaskId = his.Id
	} else {
		expand = true
		// 不存在记录,添加记录并返回
		his = general_clues.NewHistoryModel().UpdateHistory(req.Keyword, general_clues.ClueTypeIcp)
		rsp.TaskId = his.Id
	}
	if expand {
		// 清除历史任务记录
		_ = general_clues.NewGeneralCluesTaskModel().ClearByTask(his.Id)
		his.UpdatedAt = time.Now()
		general_clues.NewHistoryModel().Save(his)
		// 开始扩展线索
		go GetExpandClueInstance().StartExpandByIcp(ctx, his, &general_clues.GeneralClues{
			Type:        general_clues.ClueTypeIcp,
			Content:     req.Keyword,
			Source:      req.Source,
			CompanyName: req.CompanyName,
			Platform:    "INPUT",
		}, expand)
	}
	return nil
}

// ExpandDomain 域名扩展线索
func ExpandDomain(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	expand := false
	// 获取定级域名
	req.Keyword = utils.GetTopDomain(req.Keyword)
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "域名为空", 400)
	}
	his, err := general_clues.NewHistoryModel().FindByKeyword(req.Keyword, general_clues.ClueTypeDomain, utils.SubDayTime(time.Duration(cfg.LoadCommon().ClueCacheDay)))
	if err == nil {
		expand = req.Force
		// 任务进度不是100时,重新扩展
		if his.Process != 100 {
			expand = true
		}
		// 存在记录直接返回
		rsp.TaskId = his.Id
	} else {
		expand = true
		// 不存在记录,添加记录并返回
		his = general_clues.NewHistoryModel().UpdateHistory(req.Keyword, general_clues.ClueTypeDomain)
		rsp.TaskId = his.Id
	}
	if expand {
		// 清除历史人物记录
		_ = general_clues.NewGeneralCluesTaskModel().ClearByTask(his.Id)
		his.UpdatedAt = time.Now()
		general_clues.NewHistoryModel().Save(his)
		// 开始扩展线索
		go GetExpandClueInstance().StartExpandByDomain(ctx, his, &general_clues.GeneralClues{
			Type:        general_clues.ClueTypeDomain,
			Content:     req.Keyword,
			Source:      req.Source,
			CompanyName: req.CompanyName,
			Platform:    "INPUT",
		})
	}
	return nil
}

// ExpandSubDomain 子域名扩展线索
func ExpandSubDomain(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	expand := false
	// 获取定级域名
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "域名为空", 400)
	}
	his, err := general_clues.NewHistoryModel().FindByKeyword(req.Keyword, general_clues.ClueTypeSubdomain, utils.SubDayTime(time.Duration(cfg.LoadCommon().ClueCacheDay)))
	if err == nil {
		expand = req.Force
		// 任务进度不是100时,重新扩展
		if his.Process != 100 {
			expand = true
		}
		// 存在记录直接返回
		rsp.TaskId = his.Id
	} else {
		expand = true
		// 不存在记录,添加记录并返回
		his = general_clues.NewHistoryModel().UpdateHistory(req.Keyword, general_clues.ClueTypeSubdomain)
		rsp.TaskId = his.Id
	}
	if expand {
		// 清除历史人物记录
		_ = general_clues.NewGeneralCluesTaskModel().ClearByTask(his.Id)
		his.UpdatedAt = time.Now()
		general_clues.NewHistoryModel().Save(his)
		// 开始扩展线索
		go GetExpandClueInstance().StartExpandBySubDomain(ctx, his, &general_clues.GeneralClues{
			Type:        general_clues.ClueTypeSubdomain,
			Content:     req.Keyword,
			Source:      req.Source,
			CompanyName: req.CompanyName,
			Platform:    "INPUT",
		})
	}
	return nil
}

// ExpandKeyword 关键字扩展线索
func ExpandKeyword(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	expand := false
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "关键字为空", 400)
	}
	// 获取定级域名
	his, err := general_clues.NewHistoryModel().FindByKeyword(req.Keyword, general_clues.ClueTypeKeyword, utils.SubDayTime(time.Duration(cfg.LoadCommon().ClueCacheDay)))
	if err == nil {
		expand = req.Force
		// 任务进度不是100时,重新扩展
		if his.Process != 100 {
			expand = true
		}
		// 存在记录直接返回
		rsp.TaskId = his.Id
	} else {
		expand = true
		// 不存在记录,添加记录并返回
		his = general_clues.NewHistoryModel().UpdateHistory(req.Keyword, general_clues.ClueTypeKeyword)
		rsp.TaskId = his.Id
	}
	if expand {
		// 清除历史人物记录
		_ = general_clues.NewGeneralCluesTaskModel().ClearByTask(his.Id)

		his.UpdatedAt = time.Now()
		general_clues.NewHistoryModel().Save(his)
		// 开始扩展线索
		go GetExpandClueInstance().StartExpandByKeyword(ctx, his, &general_clues.GeneralClues{
			Type:        general_clues.ClueTypeKeyword,
			Source:      req.Source,
			CompanyName: req.CompanyName,
			Content:     req.Keyword,
			Platform:    "INPUT",
		})
	}
	return nil
}

// ExpandIp IP扩展线索
func ExpandIp(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	expand := false
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "IP/IP段为空", 400)
	}
	// 获取定级域名
	his, err := general_clues.NewHistoryModel().FindByKeyword(req.Keyword, general_clues.ClueTypeIp, utils.SubDayTime(time.Duration(cfg.LoadCommon().ClueCacheDay)))
	if err == nil {
		expand = req.Force
		// 任务进度不是100时,重新扩展
		if his.Process != 100 {
			expand = true
		}
		// 存在记录直接返回
		rsp.TaskId = his.Id
	} else {
		expand = true
		// 不存在记录,添加记录并返回
		his = general_clues.NewHistoryModel().UpdateHistory(req.Keyword, general_clues.ClueTypeIp)
		rsp.TaskId = his.Id
	}
	if expand {
		// 清除历史人物记录
		_ = general_clues.NewGeneralCluesTaskModel().ClearByTask(his.Id)
		his.UpdatedAt = time.Now()
		general_clues.NewHistoryModel().Save(his)
		// 开始扩展线索
		go GetExpandClueInstance().StartExpandByIp(ctx, his, &general_clues.GeneralClues{
			Type:        general_clues.ClueTypeIp,
			Content:     req.Keyword,
			Source:      req.Source,
			CompanyName: req.CompanyName,
			Platform:    "INPUT",
		})
	}
	return nil
}

// ExpandCert 证书扩展线索
func ExpandCert(ctx context.Context, req *pb.ExpandKeywordRequest, rsp *pb.ExpandClueResponse) error {
	expand := false
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "证书为空", 400)
	}
	oStr, cnStr := utils.PluckCert(req.Keyword)
	if oStr != "" && cnStr != "" {
		return errors.New(pb.ServiceName, "O和CN请分开下发任务", 400)
	}
	// 获取定级域名
	his, err := general_clues.NewHistoryModel().FindByKeyword(req.Keyword, general_clues.ClueTypeCert, utils.SubDayTime(time.Duration(cfg.LoadCommon().ClueCacheDay)))
	if err == nil {
		expand = req.Force
		// 任务进度不是100时,重新扩展
		if his.Process != 100 {
			expand = true
		}
		// 存在记录直接返回
		rsp.TaskId = his.Id
	} else {
		expand = true
		// 不存在记录,添加记录并返回
		his = general_clues.NewHistoryModel().UpdateHistory(req.Keyword, general_clues.ClueTypeCert)
		rsp.TaskId = his.Id
	}
	if expand {
		// 清除历史人物记录
		_ = general_clues.NewGeneralCluesTaskModel().ClearByTask(his.Id)
		his.UpdatedAt = time.Now()
		general_clues.NewHistoryModel().Save(his)
		// 开始扩展线索
		if oStr == "" && cnStr == "" {
			go GetExpandClueInstance().StartExpandByCert(ctx, his, &general_clues.GeneralClues{
				Type:        general_clues.ClueTypeCert,
				Content:     req.Keyword,
				Source:      req.Source,
				CompanyName: "",
				CertValid:   cast.ToUint8(req.CertValid),
				Platform:    "INPUT",
			})
		} else {
			if req.CompanyName == "" && oStr != "" {
				req.CompanyName = oStr
			}
			if oStr != "" {
				go GetExpandClueInstance().StartExpandByCert(ctx, his, &general_clues.GeneralClues{
					Type:        general_clues.ClueTypeCert,
					Content:     fmt.Sprintf(`O="%s"`, oStr),
					Source:      req.Source,
					CompanyName: oStr,
					CertValid:   cast.ToUint8(req.CertValid),
					Platform:    "INPUT",
				})
			}
			if cnStr != "" {
				go GetExpandClueInstance().StartExpandByCert(ctx, his, &general_clues.GeneralClues{
					Type:        general_clues.ClueTypeCert,
					Content:     fmt.Sprintf(`CN="%s"`, cnStr),
					Source:      req.Source,
					CompanyName: req.CompanyName,
					CertValid:   cast.ToUint8(req.CertValid),
					Platform:    "INPUT",
				})
			}
		}
	}
	return nil
}

// ExpandIcon ICON扩展线索
func ExpandIcon(ctx context.Context, req *pb.ExpandIconRequest, rsp *pb.ExpandClueResponse) error {
	expand := false
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "ICON为空", 400)
	}
	hashPath, hash := storage.SaveUrlMmh3(req.Keyword, cast.ToString(req.Hash), true)
	if hashPath != "" {
		req.Keyword = hashPath
	}
	if hash != 0 && req.Hash == 0 {
		req.Hash = hash
	}
	if req.Hash == 0 {
		return errors.New(pb.ServiceName, "获取ICON,Hash值失败", 400)
	}
	// 获取定级域名
	his, err := general_clues.NewHistoryModel().FindByKeyword(cast.ToString(req.Hash), general_clues.ClueTypeIcon, utils.SubDayTime(time.Duration(cfg.LoadCommon().ClueCacheDay)))
	if err == nil {
		expand = req.Force
		// 任务进度不是100时,重新扩展
		if his.Process != 100 {
			expand = true
		}
		// 存在记录直接返回
		rsp.TaskId = his.Id
	} else {
		expand = true
		// 不存在记录,添加记录并返回
		his = general_clues.NewHistoryModel().UpdateHistory(cast.ToString(req.Hash), general_clues.ClueTypeIcon)
		rsp.TaskId = his.Id
	}
	if expand {
		// 清除历史人物记录
		_ = general_clues.NewGeneralCluesTaskModel().ClearByTask(his.Id)
		his.UpdatedAt = time.Now()
		general_clues.NewHistoryModel().Save(his)
		// 开始扩展线索
		go GetExpandClueInstance().StartExpandByIcon(ctx, his, &general_clues.GeneralClues{
			Type:        general_clues.ClueTypeIcon,
			Content:     req.Keyword,
			Hash:        req.Hash,
			Source:      req.Source,
			CompanyName: req.CompanyName,
			Platform:    general_clues.PlatformInput,
		})
	}
	return nil
}

// GetClueList 获取线索列表
func GetClueList(_ context.Context, req *pb.ClueListRequest, rsp *pb.ClueListResponse) error {
	req.Ids = nil
	handlers := cluesListHandlerBuilder(req)
	handlers = append(handlers, mysql.WithOrder("`created_at` DESC"))
	list, total, err := general_clues.NewGeneralCluesModel().List(int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}

	for i := range list {
		item := &pb.ClueInfo{
			Id:          list[i].Id,
			Type:        cast.ToUint32(list[i].Type),
			CompanyName: list[i].CompanyName,
			Content:     list[i].Content,
			Hash:        list[i].Hash,
			Platform:    list[i].Platform,
			Source:      list[i].Source,
			CertValid:   cast.ToUint32(list[i].CertValid),
			Confirmed:   cast.ToInt32(list[i].Confirmed),
			CreatedAt:   list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   list[i].UpdatedAt.Format(utils.DateTimeLayout),
		}
		if item.Type == general_clues.ClueTypeIcon {
			item.Content = storage.GenDownloadUrl(item.Content, cast.ToString(item.Hash))
		}
		rsp.Items = append(rsp.Items, item)
		rsp.CurrentPage = req.Page
		rsp.PerPage = req.PerPage
	}
	rsp.Total = total
	return nil
}

// UpdateClueInfo 更新线索信息
func UpdateClueInfo(_ context.Context, req, _ *pb.ClueInfo) error {
	if req.Id == 0 {
		return errors.New(pb.ServiceName, "线索ID错误", 400)
	}
	if req.Type == general_clues.ClueTypeIcon {
		req.Content, req.Hash = storage.SaveUrlMmh3(req.Content, cast.ToString(req.Hash), true)
		if req.Hash == 0 {
			return errors.New(pb.ServiceName, "获取ICON,Hash值失败", 400)
		}
	}
	tempClue := general_clues.GeneralClues{
		Confirmed:   cast.ToUint8(req.Confirmed),
		Platform:    req.Platform,
		Source:      req.Source,
		Type:        cast.ToUint8(req.Type),
		Hash:        cast.ToInt64(req.Hash),
		CertValid:   cast.ToUint8(req.CertValid),
		CompanyName: req.CompanyName,
		Content:     req.Content,
	}
	tempClue.Id = req.Id
	if err := general_clues.NewGeneralCluesModel().Update(tempClue); err != nil {
		return fmt.Errorf("更新线索失败,ID:%d,Error:%w", req.Id, err)
	}
	return nil
}

// UpdateByKeyword 更新线索信息
func UpdateByKeyword(_ context.Context, req *pb.UpdateClueByKeywordRequest, _ *pb.ClueInfo) error {
	if req.Clue.Type == general_clues.ClueTypeIcon {
		req.Clue.Content, req.Clue.Hash = storage.SaveUrlMmh3(req.Clue.Content, cast.ToString(req.Clue.Hash), true)
		if req.Hash == 0 {
			return errors.New(pb.ServiceName, "获取ICON,Hash值失败", 400)
		}
	}
	tempClue := general_clues.GeneralClues{
		Confirmed:   cast.ToUint8(req.Clue.Confirmed),
		Platform:    req.Clue.Platform,
		Source:      req.Clue.Source,
		Type:        cast.ToUint8(req.Clue.Type),
		Hash:        cast.ToInt64(req.Clue.Hash),
		CertValid:   cast.ToUint8(req.Clue.CertValid),
		CompanyName: req.Clue.CompanyName,
		Content:     req.Clue.Content,
	}
	if req.Type == general_clues.ClueTypeIcon {
		if err := general_clues.NewGeneralCluesModel().UpdateByKeyword(req.Hash, cast.ToUint8(req.Type), tempClue); err != nil {
			return fmt.Errorf("更新线索失败,Error:%w", err)
		}
	} else {
		if err := general_clues.NewGeneralCluesModel().UpdateByKeyword(req.Keyword, cast.ToUint8(req.Type), tempClue); err != nil {
			return fmt.Errorf("更新线索失败,Error:%w", err)
		}
	}
	return nil
}

// CreateClue 创建线索
func CreateClue(_ context.Context, req, _ *pb.ClueInfo) error {
	if req.Type == general_clues.ClueTypeIcon {
		req.Content, req.Hash = storage.SaveUrlMmh3(req.Content, cast.ToString(req.Hash), true)
		if req.Hash == 0 {
			return errors.New(pb.ServiceName, "获取ICON,Hash值失败", 400)
		}
	}
	if _, err := general_clues.NewGeneralCluesModel().NewClue(&general_clues.GeneralClues{
		Confirmed:   cast.ToUint8(req.Confirmed),
		Platform:    req.Platform,
		Source:      req.Source,
		Type:        cast.ToUint8(req.Type),
		Hash:        cast.ToInt64(req.Hash),
		CertValid:   cast.ToUint8(req.CertValid),
		CompanyName: req.CompanyName,
		Content:     req.Content,
	}, true); err != nil {
		return err
	}
	return nil
}

// ExpandClueTaskList 扩展任务列表
func ExpandClueTaskList(_ context.Context, req *pb.ExpandClueTaskListRequest, rsp *pb.ExpandClueTaskListResponse) error {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	if req.Keyword != "" {
		handlers = append(handlers, mysql.WithLike("keyword", "%"+req.Keyword+"%"))
	}
	if req.Type != 0 {
		handlers = append(handlers, mysql.WithColumnValue("type", req.Type))
	}
	if req.Process == 100 {
		handlers = append(handlers, mysql.WithWhere("process = ?", req.Process))
	}
	if req.Process == -1 {
		handlers = append(handlers, mysql.WithWhere("process != ?", 100))
	}
	if len(req.CreatedAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("created_at >= ?", req.CreatedAt[0]))
	}
	if len(req.CreatedAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("created_at >= ?", req.CreatedAt[0]))
		handlers = append(handlers, mysql.WithWhere("created_at <= ?", req.CreatedAt[1]))
	}
	if len(req.UpdatedAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("updated_at >= ?", req.UpdatedAt[0]))
	}
	if len(req.UpdatedAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("updated_at >= ?", req.UpdatedAt[0]))
		handlers = append(handlers, mysql.WithWhere("updated_at <= ?", req.UpdatedAt[1]))
	}
	list, total, err := general_clues.NewHistoryModel().List(int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}
	for i := range list {
		item := &pb.ExpandClueTaskInfo{
			Id:        list[i].Id,
			Type:      cast.ToUint32(list[i].Type),
			Keyword:   list[i].Keyword,
			Process:   list[i].Process,
			CreatedAt: list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt: list[i].UpdatedAt.Format(utils.DateTimeLayout),
		}
		rsp.Items = append(rsp.Items, item)
		rsp.CurrentPage = int64(req.Page)
		rsp.PerPage = int64(req.PerPage)
	}
	rsp.Total = total
	return nil
}

func CluesBatchUpdate(ctx context.Context, req *pb.ClueListRequest, _ *pb.Empty) error {
	err := cluesBatchUpdate(req)
	if err != nil {
		log.WithContextErrorf(ctx, "[Clues] Batch update general clues failure: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func cluesBatchUpdate(req *pb.ClueListRequest) error {
	handlers := cluesListHandlerBuilder(req)
	list, _, err := general_clues.NewGeneralCluesModel().List(0, 0, handlers...)
	if len(list) == 0 {
		return err
	}

	for i := range list {
		list[i].CompanyName = req.UpdateCompanyName
	}

	return general_clues.NewGeneralCluesModel().Save(list)
}

func ClueFilterGroup(_ context.Context, _ *pb.ClueListRequest, rsp *pb.ClueFilterGroupResponse) error {
	rsp.Platform = general_clues.Platforms
	return nil
}

func cluesListHandlerBuilder(req *pb.ClueListRequest) []mysql.HandleFunc {
	if len(req.Ids) > 0 {
		return []mysql.HandleFunc{mysql.WithValuesIn("`id`", utils.ListDistinct(req.Ids))}
	}
	var handlers = make([]mysql.HandleFunc, 0, 10)
	if req.Confirmed != "" {
		handlers = append(handlers, mysql.WithColumnValue("confirmed", cast.ToInt(req.Confirmed)))
	}
	if len(req.Platform) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("platform", req.Platform))
	}
	if req.Source != "" {
		handlers = append(handlers, mysql.WithLRLike("source", req.Source))
	}
	if req.Content != "" {
		handlers = append(handlers, mysql.WithLike("content", "%"+req.Content+"%"))
	}
	if req.CompanyName != "" {
		handlers = append(handlers, mysql.WithLike("company_name", "%"+req.CompanyName+"%"))
	}
	if req.Type > 0 {
		handlers = append(handlers, mysql.WithColumnValue("type", req.Type))
	}
	if req.Hash > 0 {
		handlers = append(handlers, mysql.WithColumnValue("hash", req.Hash))
	}
	if req.CertValid > 0 {
		handlers = append(handlers, mysql.WithColumnValue("cert_valid", req.CertValid))
	}
	if req.CreatedAtAfter != "" {
		handlers = append(handlers, mysql.WithWhere("created_at >= ?", req.CreatedAtAfter))
	}
	if req.CreatedAtBefore != "" {
		handlers = append(handlers, mysql.WithWhere("created_at <= ?", req.CreatedAtBefore))
	}
	if req.UpdatedAtAfter != "" {
		handlers = append(handlers, mysql.WithWhere("updated_at >= ?", req.UpdatedAtAfter))
	}
	if req.UpdatedAtBefore != "" {
		handlers = append(handlers, mysql.WithWhere("updated_at <= ?", req.UpdatedAtBefore))
	}
	if req.Keyword != "" {
		if cast.ToInt(req.Keyword) != 0 {
			handlers = append(handlers, mysql.WithWhere("(content like ? or company_name like ? or source like ? or platform like ? or hash = ?)", "%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%", cast.ToInt(req.Keyword)))
		} else {
			handlers = append(handlers, mysql.WithWhere("(content like ? or company_name like ? or source like ? or platform like ?)", "%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%"))
		}
	}
	return handlers
}
