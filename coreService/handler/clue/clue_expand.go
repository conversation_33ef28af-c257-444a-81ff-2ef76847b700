package clue

import (
	"context"
	"fmt"
	"micro-service/coreService/handler/fofa"
	"micro-service/coreService/handler/icp"
	"micro-service/coreService/handler/whois"
	corePb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/pkg/log"
	"micro-service/pkg/process"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"net"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cast"
)

var once sync.Once

var expandClue *ExpandClue

type ExpandClue struct {
	expandIng sync.Map
}

func GetExpandClueInstance() *ExpandClue {
	if expandClue == nil {
		once.Do(func() { expandClue = &ExpandClue{} })
	}
	return expandClue
}

// StartExpandByCompanyName 通过企业名称扩展线索
func (e *ExpandClue) StartExpandByCompanyName(ctx context.Context, his *general_clues.History, generalClue *general_clues.GeneralClues, force bool) {
	// 写入关键词线索
	clue, newErr := general_clues.NewGeneralCluesModel().NewClue(generalClue, false)
	if newErr != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索-企业名称,源:%s,写入线索失败:%v", generalClue.Content, newErr))
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
		return
	}
	// 扩展线索结束时,更新数据
	defer func() {
		// 删除锁
		e.expandIng.Delete(clue.Id)
		// 完成
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
	}()
	general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, clue)
	// 检查任务是否重复执行
	if _, ok := e.expandIng.Load(clue.Id); ok {
		log.WithContextInfof(ctx, "扩展线索-企业名称,源:"+clue.Content+",任务执行中,不在重复执行")
		return
	}
	// 添加任务ID && 更新进度
	e.expandIng.Store(clue.Id, nil)
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 5)
	// 获取备案数据
	rsp := &corePb.IcpResponse{}
	// 获取企业名称的备案的域名和icp信息（调用站长之家）
	if err := icp.CompanyName(ctx, &corePb.IcpCompanyNameRequest{CompanyName: clue.Content, GetEquals: true, Force: force}, rsp); err != nil {
		log.WithContextWarnf(ctx, "扩展线索-企业名称,源:"+clue.Content+",根据企业名称获取ICP失败,Error:"+err.Error())
		e.expandIng.Delete(clue.Id)
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
		return
	}
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 51)
	// 同步所有数据
	if rsp.Info != nil {
		rsp.Equals = append(rsp.Equals, rsp.Info)
	}
	bar := process.NewBar(0, len(rsp.Equals))
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 91)
	// 备案信息入库
	e.icpWriteDb(ctx, clue, his, bar, rsp.Equals)
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 99)
}

// StartExpandByIcp 通过ICP扩展线索
func (e *ExpandClue) StartExpandByIcp(ctx context.Context, his *general_clues.History, generalClue *general_clues.GeneralClues, force bool) {
	clue, newErr := general_clues.NewGeneralCluesModel().NewClue(generalClue, false)
	if newErr != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索-ICP,源:%s,写入线索失败:%v", generalClue.Content, newErr))
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
		return
	}
	// 扩展线索结束时,更新数据
	defer func() {
		// 删除锁
		e.expandIng.Delete(clue.Id)
		// 完成
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
	}()
	general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, clue)
	// 检查任务是否重复执行
	if _, ok := e.expandIng.Load(clue.Id); ok {
		log.WithContextInfof(ctx, "扩展线索-ICP,源:"+clue.Content+",任务执行中,不在重复执行")
		return
	}
	// 添加任务ID && 更新进度
	e.expandIng.Store(clue.Id, nil)
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 5)
	// 获取备案数据
	rsp := &corePb.IcpResponse{}
	if err := icp.CompanyName(ctx, &corePb.IcpCompanyNameRequest{CompanyName: clue.CompanyName, GetEquals: true, Force: force}, rsp); err != nil {
		log.WithContextWarnf(ctx, "扩展线索-ICP,源:"+clue.Content+",根据备案号获取ICP失败,Error:"+err.Error())
	}
	// 同步所有数据
	if rsp.Info != nil {
		rsp.Equals = append(rsp.Equals, rsp.Info)
	}
	// 获取FOFA处理线索总数
	count, fofaClueHandler := e.getFofaCount(
		ctx,
		[]int{general_clues.ClueTypeIcon, general_clues.ClueTypeDomain, general_clues.ClueTypeCert},
		fmt.Sprintf(`icp="%s"`, clue.Content),
	)
	bar := process.NewBar(0, len(rsp.Equals)+cast.ToInt(count))
	// 备案信息入库
	e.icpWriteDb(ctx, clue, his, bar, rsp.Equals)
	// FOFA线索入库
	if fofaClueHandler != nil {
		fofaClueHandler(bar, his, generalClue)
	}
}

// StartExpandBySubDomain 通过子域名扩展线索
func (e *ExpandClue) StartExpandBySubDomain(ctx context.Context, his *general_clues.History, generalClue *general_clues.GeneralClues) {
	clue, newErr := general_clues.NewGeneralCluesModel().NewClue(generalClue, false)
	if newErr != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索-SubDomain,源:%s,写入线索失败:%v", generalClue.Content, newErr))
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
		return
	}
	// 扩展线索结束时,更新数据
	defer func() {
		// 删除锁
		e.expandIng.Delete(clue.Id)
		// 完成
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
	}()
	general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, clue)
	// 检查任务是否重复执行
	if _, ok := e.expandIng.Load(clue.Id); ok {
		log.WithContextInfof(ctx, "扩展线索-SubDomain,源:"+clue.Content+",任务执行中,不在重复执行")
		return
	}
	// 添加任务ID && 更新进度
	e.expandIng.Store(clue.Id, nil)
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 5)
	// 获取备案数据
	rsp := &corePb.IcpResponse{}
	if err := icp.Domain(ctx, &corePb.IcpDomainRequest{Domain: clue.Content, GetEquals: true, GetSubdomain: true}, rsp); err != nil {
		log.WithContextWarnf(ctx, "扩展线索-SubDomain,源:"+clue.Content+",根据备案号获取ICP失败,Error:"+err.Error())
	}
	// 同步所有数据
	if rsp.Info != nil {
		rsp.Equals = append(rsp.Equals, rsp.Info)
	}
	// 获取FOFA处理线索总数
	count, fofaClueHandler := e.getFofaCount(
		ctx,
		[]int{general_clues.ClueTypeIcon, general_clues.ClueTypeIcp, general_clues.ClueTypeCert},
		fmt.Sprintf(`(domain="%s" || host*="*.%s" || header="%s/" || banner="%s;")`, clue.Content, clue.Content, clue.Content, clue.Content),
	)
	bar := process.NewBar(0, len(rsp.Equals)+cast.ToInt(count))
	// 备案信息入库
	e.icpWriteDb(ctx, clue, his, bar, rsp.Equals)
	// FOFA线索入库
	if fofaClueHandler != nil {
		fofaClueHandler(bar, his, generalClue)
	}
}

// StartExpandByDomain 通过域名扩展线索
func (e *ExpandClue) StartExpandByDomain(ctx context.Context, his *general_clues.History, generalClue *general_clues.GeneralClues) {
	clue, newErr := general_clues.NewGeneralCluesModel().NewClue(generalClue, false)
	if newErr != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索-SubDomain,源:%s,写入线索失败:%v", generalClue.Content, newErr))
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
		return
	}
	// 扩展线索结束时,更新数据
	defer func() {
		// 删除锁
		e.expandIng.Delete(clue.Id)
		// 完成
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
	}()
	general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, clue)
	// 检查任务是否重复执行
	if _, ok := e.expandIng.Load(clue.Id); ok {
		log.WithContextInfof(ctx, "扩展线索-Domain,源:"+clue.Content+",任务执行中,不在重复执行")
		return
	}
	// 添加任务ID && 更新进度
	e.expandIng.Store(clue.Id, nil)
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 5)
	// 获取备案数据
	rsp := &corePb.IcpResponse{}
	if err := icp.Domain(ctx, &corePb.IcpDomainRequest{Domain: clue.Content, GetEquals: true}, rsp); err != nil {
		log.WithContextWarnf(ctx, "扩展线索-Domain,源:"+clue.Content+",根据备案号获取ICP失败,Error:"+err.Error())
	}
	// 同步所有数据
	if rsp.Info != nil {
		rsp.Equals = append(rsp.Equals, rsp.Info)
	}
	// 获取FOFA处理线索总数
	count, fofaClueHandler := e.getFofaCount(
		ctx,
		[]int{general_clues.ClueTypeIcon, general_clues.ClueTypeIcp, general_clues.ClueTypeCert},
		fmt.Sprintf(`(domain="%s" || host*="*.%s" || header="%s/" || banner="%s;")`, clue.Content, clue.Content, clue.Content, clue.Content),
	)
	bar := process.NewBar(0, len(rsp.Equals)+cast.ToInt(count))
	// 备案信息入库
	e.icpWriteDb(ctx, clue, his, bar, rsp.Equals)
	// FOFA线索入库
	if fofaClueHandler != nil {
		fofaClueHandler(bar, his, generalClue)
	}
}

// StartExpandByCert 通过证书扩展线索
func (e *ExpandClue) StartExpandByCert(ctx context.Context, his *general_clues.History, generalClue *general_clues.GeneralClues) {
	var icpResponse corePb.IcpResponse
	var icpErr error
	var fofaQueryStr string
	oStr, cnStr := utils.PluckCert(generalClue.Content)
	domain := ""
	clue, newErr := general_clues.NewGeneralCluesModel().NewClue(generalClue, false)
	if newErr != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索-Cert,源:%s,写入线索失败:%v", generalClue.Content, newErr))
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
		return
	}
	// 扩展线索结束时,更新数据
	defer func() {
		// 删除锁
		e.expandIng.Delete(clue.Id)
		// 完成
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
	}()

	general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, clue)
	// 检查任务是否重复执行
	if _, ok := e.expandIng.Load(clue.Id); ok {
		log.WithContextInfof(ctx, "扩展线索-Cert,源:"+clue.Content+",任务执行中,不在重复执行")
		return
	}
	// 添加任务ID && 更新进度
	e.expandIng.Store(clue.Id, nil)
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 5)
	// 获取证书域名
	if oStr == "" && cnStr == "" {
		fofaQueryStr = fmt.Sprintf("(cert=\"CommonName: %s\" || cert=\"CommonName: *.%s\") || (cert.subject=\"%s\")", strings.TrimPrefix(clue.Content, "*."), strings.TrimPrefix(clue.Content, "*."), strings.TrimPrefix(clue.Content, "*."))
		if d := utils.GetTopDomain(clue.Content); d != "" {
			domain = d
		}
	} else {
		if cnStr != "" {
			if d := utils.GetTopDomain(cnStr); d != "" {
				domain = d
			}
			fofaQueryStr = fmt.Sprintf("(cert=\"CommonName: %s\" || cert=\"CommonName: *.%s\")", strings.TrimPrefix(cnStr, "*."), strings.TrimPrefix(cnStr, "*."))
		} else {
			fofaQueryStr = fmt.Sprintf("(cert.subject=\"%s\")", oStr)
		}
	}
	if domain != "" {
		if icpErr = icp.Domain(ctx, &corePb.IcpDomainRequest{Domain: domain}, &icpResponse); icpErr != nil {
			log.WithContextWarnf(ctx, "扩展线索-Cert,源:"+domain+",根据备案号获取ICP失败,Error:"+icpErr.Error())
		}
		// 同步所有数据
		if icpResponse.Info != nil {
			// 有备案信息时,更新企业名称
			if icpResponse.Info.CompanyName != "" {
				clue.CompanyName = icpResponse.Info.CompanyName
				_ = general_clues.NewGeneralCluesModel().Update(*clue)
			}
			icpResponse.Equals = append(icpResponse.Equals, icpResponse.Info)
		}
	}
	// 获取FOFA处理线索总数
	count, fofaClueHandler := e.getFofaCount(
		ctx,
		[]int{general_clues.ClueTypeIcon, general_clues.ClueTypeIcp, general_clues.ClueTypeDomain},
		fofaQueryStr,
	)

	bar := process.NewBar(0, len(icpResponse.Equals)+cast.ToInt(count))
	// 备案信息入库
	e.icpWriteDb(ctx, clue, his, bar, icpResponse.Equals)
	// FOFA线索入库
	if fofaClueHandler != nil {
		fofaClueHandler(bar, his, generalClue)
	}
}

// StartExpandByIp 通过IP扩展线索
func (e *ExpandClue) StartExpandByIp(ctx context.Context, his *general_clues.History, generalClue *general_clues.GeneralClues) {
	clue, newErr := general_clues.NewGeneralCluesModel().NewClue(generalClue, false)
	if newErr != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索-IP,源:%s,写入线索失败:%v", generalClue.Content, newErr))
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
		return
	}
	// 扩展线索结束时,更新数据
	defer func() {
		// 删除锁
		e.expandIng.Delete(clue.Id)
		// 完成
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
	}()
	general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, clue)
	// 检查任务是否重复执行
	if _, ok := e.expandIng.Load(clue.Id); ok {
		log.WithContextInfof(ctx, "扩展线索-IP,源:"+clue.Content+",任务执行中,不在重复执行")
		return
	}
	// 添加任务ID && 更新进度
	e.expandIng.Store(clue.Id, nil)
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 5)
	// 获取FOFA处理线索总数
	count, fofaClueHandler := e.getFofaCount(
		ctx,
		[]int{general_clues.ClueTypeIcon, general_clues.ClueTypeIcp, general_clues.ClueTypeCert, general_clues.ClueTypeDomain},
		fmt.Sprintf("ip=\"%s\"", clue.Content),
	)
	bar := process.NewBar(0, cast.ToInt(count))
	// FOFA线索入库
	if fofaClueHandler != nil {
		fofaClueHandler(bar, his, generalClue)
	}
}

// StartExpandByKeyword 通过关键字扩展线索
func (e *ExpandClue) StartExpandByKeyword(ctx context.Context, his *general_clues.History, generalClue *general_clues.GeneralClues) {
	clue, newErr := general_clues.NewGeneralCluesModel().NewClue(generalClue, false)
	if newErr != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索-Keyword,源:%s,写入线索失败:%v", generalClue.Content, newErr))
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
		return
	}
	// 扩展线索结束时,更新数据
	defer func() {
		// 删除锁
		e.expandIng.Delete(clue.Id)
		// 完成
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
	}()
	general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, clue)
	// 检查任务是否重复执行
	if _, ok := e.expandIng.Load(clue.Id); ok {
		log.WithContextInfof(ctx, "扩展线索-Keyword,源:"+clue.Content+",任务执行中,不在重复执行")
		return
	}
	// 添加任务ID && 更新进度
	e.expandIng.Store(clue.Id, nil)
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 5)
	// 获取FOFA处理线索总数
	count, fofaClueHandler := e.getFofaCount(
		ctx,
		[]int{general_clues.ClueTypeIcon, general_clues.ClueTypeIcp, general_clues.ClueTypeCert, general_clues.ClueTypeDomain},
		fmt.Sprintf("(title=\"%s\" || body=\"%s\")", clue.Content, clue.Content),
	)
	bar := process.NewBar(0, cast.ToInt(count))
	// FOFA线索入库
	if fofaClueHandler != nil {
		fofaClueHandler(bar, his, generalClue)
	}
}

// StartExpandByIcon 通过Icon扩展线索
func (e *ExpandClue) StartExpandByIcon(ctx context.Context, his *general_clues.History, generalClue *general_clues.GeneralClues) {
	clue, newErr := general_clues.NewGeneralCluesModel().NewClue(generalClue, false)
	if newErr != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索-Icon,源:%s,写入线索失败:%v", cast.ToString(generalClue.Hash), newErr))
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
		return
	}
	// 扩展线索结束时,更新数据
	defer func() {
		// 删除锁
		e.expandIng.Delete(clue.Id)
		// 完成
		_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 100)
	}()
	general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, clue)
	// 检查任务是否重复执行
	if _, ok := e.expandIng.Load(clue.Id); ok {
		log.WithContextInfof(ctx, "扩展线索-Icon,源:"+cast.ToString(generalClue.Hash)+",任务执行中,不在重复执行")
		return
	}
	// 添加任务ID && 更新进度
	e.expandIng.Store(clue.Id, nil)
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 5)

	// 获取FOFA处理线索总数
	count, fofaClueHandler := e.getFofaCount(
		ctx,
		[]int{general_clues.ClueTypeDomain, general_clues.ClueTypeIcp, general_clues.ClueTypeCert},
		fmt.Sprintf("icon_hash=\"%d\"", clue.Hash),
	)

	bar := process.NewBar(0, cast.ToInt(count))
	// FOFA线索入库
	if fofaClueHandler != nil {
		fofaClueHandler(bar, his, generalClue)
	}
}

// IcpWriteDb 备案信息入库
func (e *ExpandClue) icpWriteDb(ctx context.Context, clue *general_clues.GeneralClues, his *general_clues.History, _ *process.Bar, an []*corePb.BeiAn) {
	log.WithContextInfof(ctx, fmt.Sprintf("icpWriteDb-备案信息入库GeneralClues-线索总库写入数据,企业名称:%s,备案查询结果:%v", clue.CompanyName, an))
	var writeClue *general_clues.GeneralClues
	waitIcon := sync.WaitGroup{}
	var writeErr error
	// 获取备案信息ICON
	// 备案信息
	for _, beian := range an {
		log.WithContextInfof(ctx, fmt.Sprintf("icpWriteDb-备案信息入库GeneralClues-线索总库写入数据-进入循环了,企业名称:%s,当前循环的备案线索数据:%v", clue.CompanyName, beian))
		waitIcon.Add(1)
		if beian.CompanyName == "" {
			beian.CompanyName = clue.CompanyName
		}
		// 写入ICP 线索
		writeClue, writeErr = general_clues.NewGeneralCluesModel().NewClue(&general_clues.GeneralClues{
			Content: utils.RemoveIcpNumber(beian.Icp), CompanyName: beian.CompanyName, Type: general_clues.ClueTypeIcp, Source: "ICP备案", Platform: "MIIT", Confirmed: 1,
		}, true)
		general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, writeClue)
		log.WithContextInfof(ctx, fmt.Sprintf("icpWriteDb-备案信息入库GeneralClues-企业名称,源:%s,写入ICP线索:%v,结果:%v", clue.Content, writeClue, writeErr))
		// 写入 关键字 企业名称 线索
		if beian.CompanyName != "" {
			writeClue, writeErr = general_clues.NewGeneralCluesModel().NewClue(&general_clues.GeneralClues{
				Content: beian.CompanyName, CompanyName: beian.CompanyName, Type: general_clues.ClueTypeKeyword, Source: "ICP备案", Platform: "MIIT", Confirmed: 1,
			}, true)
			general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, writeClue)
			log.WithContextInfof(ctx, fmt.Sprintf("icpWriteDb-备案信息入库GeneralClues-企业名称,源:%s,写入关键字线索:%v,结果:%v", clue.Content, writeClue, writeErr))
		}
		// 写入 域名 线索
		if beian.WebsiteUrl != "" {
			if net.ParseIP(beian.WebsiteUrl) != nil {
				// IP地址
				writeClue, writeErr = general_clues.NewGeneralCluesModel().NewClue(&general_clues.GeneralClues{
					Content: beian.WebsiteUrl, CompanyName: beian.CompanyName, Type: general_clues.ClueTypeIp, Source: "ICP备案", Platform: "MIIT", Confirmed: 1,
				}, true)
				general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, writeClue)
				log.WithContextInfof(ctx, fmt.Sprintf("icpWriteDb-备案信息入库GeneralClues-企业名称,源:%s,写入IP线索:%v,结果:%v", clue.Content, writeClue, writeErr))
			} else {
				// 域名
				writeClue, writeErr = general_clues.NewGeneralCluesModel().NewClue(&general_clues.GeneralClues{
					Content: beian.WebsiteUrl, CompanyName: beian.CompanyName, Type: general_clues.ClueTypeDomain, Source: "ICP备案", Platform: "MIIT", Confirmed: 1,
				}, true)
				general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, writeClue)
				log.WithContextInfof(ctx, fmt.Sprintf("icpWriteDb-备案信息入库GeneralClues-企业名称,源:%s,写入域名线索:%v,结果:%v", clue.Content, writeClue, writeErr))
			}
		}
		// 获取备案域名的ICON线索
		go func(b *corePb.BeiAn, wa *sync.WaitGroup) {
			defer wa.Done()
			if b.WebsiteUrl != "" {
				icoRsp, _ := crawlerPb.GetProtoClient().Icon(ctx, &crawlerPb.IconRequest{Url: b.WebsiteUrl}, utils.SetRpcTimeoutOpt(30))
				if icoRsp != nil {
					if icoRsp.Img != "" && icoRsp.Hash != 0 {
						// 写入备案域名ICON线索
						writeClue, writeErr = general_clues.NewGeneralCluesModel().NewClue(&general_clues.GeneralClues{
							Content: icoRsp.Img, CompanyName: b.CompanyName, Type: general_clues.ClueTypeIcon, Source: b.WebsiteUrl, Platform: "Crawler", Confirmed: 1, Hash: icoRsp.Hash,
						}, true)
						// 添加任务关联关系
						general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, writeClue)
						log.WithContextInfof(ctx, fmt.Sprintf("icpWriteDb-备案信息入库GeneralClues-企业名称,源:%s,写入ICON线索:%v,结果:%v", clue.Content, writeClue, writeErr))
					}
				}
			}
		}(beian, &waitIcon)
	}
	waitIcon.Wait()
}

func (e *ExpandClue) getFofaUrl(asset *corePb.Asset) string {
	if *asset.Link != "" {
		return *asset.Link
	}
	// 添加协议
	if !strings.Contains(*asset.Host, "://") {
		*asset.Host = *asset.Protocol + "://" + *asset.Host
	}
	return *asset.Host
}

// fofaClueHandler 获取FOFA线索,并入库
func (e *ExpandClue) fofaClueHandler(ctx context.Context, expandClue *general_clues.GeneralClues, _ *process.Bar, his *general_clues.History, expandTypes []int, keyword ...string) {
	var clues sync.Map
	log.WithContextInfof(ctx, fmt.Sprintf("扩展线索,任务ID:%d,fofaClueHandler 开始处理FOFA线索:%s", his.Id, strings.Join(keyword, " || ")))
	retry, retryTotal, page, currentTotal := 0, 3, 1, 0
	for {
		rsp := corePb.FofaQueryResponse{}
		if err := fofa.FofaQuery(ctx, &rsp, &corePb.FofaQueryRequest{Qbase64: strings.Join(keyword, " || "), Page: cast.ToUint32(page)}); err != nil {
			// 重试三次
			if retry < retryTotal {
				retry += 1
				log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索,任务ID:%d,fofaClueHandler 处理线索:%s 获取数据失败:%s, 重试:%d", his.Id, strings.Join(keyword, " || "), err.Error(), retry))
				time.Sleep(3 * time.Second)
				continue
			} else {
				log.WithContextWarnf(ctx, fmt.Sprintf("扩展线索,任务ID:%d,fofaClueHandler 处理线索:%s 获取数据失败:%s, 重试结束", his.Id, strings.Join(keyword, " || "), err.Error()))
				break
			}
		}
		page++ // 翻页
		if len(rsp.Sdata) == 0 || currentTotal >= 10000 {
			if currentTotal >= 10000 {
				log.WithContextInfof(ctx, fmt.Sprintf("扩展线索,任务ID:%d,fofaClueHandler 处理线索:%s 达到一万条数据限制,不在拉取数据", his.Id, strings.Join(keyword, " || ")))
			}
			break
		}
		size := cast.ToInt(*rsp.Size)
		log.WithContextInfof(ctx, fmt.Sprintf("扩展线索,任务ID:%d,fofaClueHandler 处理FOFA线索:%s,当前页:%d,总数:%d", his.Id, strings.Join(keyword, " || "), page, *rsp.Size))
		// 更新 进度
		if size >= 10000 {
			size = 10000
		}
		clueProcess := cast.ToInt((cast.ToFloat32(currentTotal) / cast.ToFloat32(size)) * 100)
		if clueProcess < 80 {
			_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, clueProcess)
		}
		for _, asset := range rsp.Sdata {
			currentTotal += 1
			// 获取ICP时处理
			if utils.ListContains(expandTypes, general_clues.ClueTypeIcp) {
				if icp := utils.GetIcpNumber(*asset.Icp); icp != "" {
					e.canAddFofaClue(&clues, &general_clues.GeneralClues{
						Type:      general_clues.ClueTypeIcp,
						Content:   utils.RemoveIcpNumber(icp),
						Source:    e.getFofaUrl(asset),
						CertValid: cast.ToUint8(cast.ToBool(*asset.CertsValid)),
						Platform:  "FOFA",
					})
				}
			}
			// 获取ICON时处理
			if utils.ListContains(expandTypes, general_clues.ClueTypeIcon) {
				if path := storage.SaveIco(*asset.Icon, cast.ToInt64(*asset.IconHash), true); path != "" {
					e.canAddFofaClue(&clues, &general_clues.GeneralClues{
						Type:      general_clues.ClueTypeIcon,
						Content:   path,
						Hash:      cast.ToInt64(*asset.IconHash),
						Source:    e.getFofaUrl(asset),
						CertValid: cast.ToUint8(cast.ToBool(*asset.CertsValid)),
						Platform:  "FOFA",
					})
				}
			}
			// 获取证书时处理
			if utils.ListContains(expandTypes, general_clues.ClueTypeCert) {
				e.canAddFofaClue(&clues, &general_clues.GeneralClues{
					Type:      general_clues.ClueTypeCert,
					Content:   strings.Join(utils.GetCert(*asset.Cert, true), " "),
					Source:    e.getFofaUrl(asset),
					CertValid: cast.ToUint8(cast.ToBool(*asset.CertsValid)),
					Platform:  "FOFA",
				})
			}
			// 获取域名时处理
			if utils.ListContains(expandTypes, general_clues.ClueTypeDomain) {
				if domain := utils.GetTopDomain(*asset.Domain); domain != "" {
					e.canAddFofaClue(&clues, &general_clues.GeneralClues{
						Type:      general_clues.ClueTypeDomain,
						Content:   domain,
						Source:    e.getFofaUrl(asset),
						CertValid: cast.ToUint8(cast.ToBool(*asset.CertsValid)),
						Platform:  "FOFA",
					})
				}
			}
		}
		log.WithContextInfof(ctx, fmt.Sprintf("扩展线索,任务ID:%d,fofaClueHandler 处理线索:%s 处理数据总数:%d", his.Id, strings.Join(keyword, " || "), currentTotal))
	}
	// 补充企业名称
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 85)
	cluesCount := getCluesCount(&clues)
	cY := 0
	e.fixCompanyName(ctx, &clues, func() {
		cY += 1
		log.WithContextInfof(ctx, fmt.Sprintf("扩展线索,任务ID:%d,fixCompanyName,线索总数:%d,当前到:%d", his.Id, cluesCount, cY))
	})
	log.WithContextInfof(ctx, fmt.Sprintf("扩展线索,任务ID:%d,扩展线索-FOFA,Query:%s,扩展结果:%p", his.Id, strings.Join(keyword, " || "), &clues))
	// 线索入库
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 90)
	cX := 0
	clues.Range(func(_, value any) bool {
		cX += 1
		// 写入线索
		if value.(*general_clues.GeneralClues).Type != general_clues.ClueTypeCert {
			cl := value.(*general_clues.GeneralClues)
			writeClue, writeErr := general_clues.NewGeneralCluesModel().NewClue(cl, false)
			general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, writeClue)
			log.WithContextInfof(ctx, fmt.Sprintf("扩展线索,任务ID:%d,线索总数:%d,当前到:%d,扩展线索-FOFA,Query:%s,源:%s,线索入库:%v,结果:%v", his.Id, cluesCount, cX, strings.Join(keyword, " || "), cl.Content, writeClue, writeErr))
		} else {
			// 拆分证书写入
			oStr, cnStr := utils.PluckCert(value.(*general_clues.GeneralClues).Content)
			if oStr != "" {
				ocl := value.(*general_clues.GeneralClues)
				ocl.CompanyName = oStr
				ocl.Content = fmt.Sprintf(`O="%s"`, oStr)
				// 替换企业名称的括号问题,以传入的线索企业名称为准
				if expandClue != nil {
					if utils.CompanyNameEquals(ocl.CompanyName, expandClue.CompanyName) {
						ocl.CompanyName = expandClue.CompanyName
					}
				}
				writeClue, writeErr := general_clues.NewGeneralCluesModel().NewClue(ocl, true)
				general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, writeClue)
				log.WithContextInfof(ctx, fmt.Sprintf("扩展线索,任务ID:%d,线索总数:%d,当前到:%d,扩展线索-FOFA,Query:%s,源:%s,线索入库:%v,结果:%v", his.Id, cluesCount, cX, strings.Join(keyword, " || "), ocl.Content, writeClue, writeErr))
			}
			if cnStr != "" && utils.CheckOpenIpOrDomain(cnStr) {
				cnCl := value.(*general_clues.GeneralClues)
				cnCl.Content = fmt.Sprintf(`CN="%s"`, cnStr)
				writeClue, writeErr := general_clues.NewGeneralCluesModel().NewClue(cnCl, true)
				general_clues.NewGeneralCluesTaskModel().AddTaskClue(his.Id, writeClue)
				log.WithContextInfof(ctx, fmt.Sprintf("扩展线索,任务ID:%d,线索总数:%d,当前到:%d,扩展线索-FOFA,Query:%s,源:%s,线索入库:%v,结果:%v", his.Id, cluesCount, cX, strings.Join(keyword, " || "), cnCl.Content, writeClue, writeErr))
			}
		}
		return true
	})
	_ = general_clues.NewHistoryModel().UpdateProcess(his.Id, 99)
}

func getCluesCount(clues *sync.Map) int {
	clueLen := 0
	clues.Range(func(k, v interface{}) bool {
		clueLen++
		return true
	})
	return clueLen
}

func (e *ExpandClue) fixCompanyName(ctx context.Context, clues *sync.Map, f func()) {
	var wg sync.WaitGroup
	clues.Range(func(key, value any) bool {
		wg.Add(1)
		go func(key, value any, f func()) {
			defer func() {
				wg.Done()
				f()
			}()
			clue := value.(*general_clues.GeneralClues)
			// 更新证书的企业名称
			if clue.Type == general_clues.ClueTypeCert {
				oStr, cnStr := utils.PluckCert(clue.Content)
				// 更新企业名称
				if (clue.CompanyName == "" || oStr == "") && (cnStr != "" || (oStr != "" && !utils.CheckChinese(oStr))) {
					// CN 是域名,或者是公网IP时,才进行备案查询
					if utils.IsDomain(cnStr) || !utils.CheckPrivateIp(cnStr) {
						cnStr = strings.ReplaceAll(cnStr, "*.", "")
						rsp := &corePb.IcpResponse{}
						if err := icp.Domain(ctx, &corePb.IcpDomainRequest{Domain: cnStr, GetEquals: false, GetSubdomain: true}, rsp); err != nil {
							log.WithContextWarnf(ctx, fmt.Sprintf("fixCompanyName:%s 从cnStr查询备案更新企业名称,获取备案信息失败,Error:%s", clue.Content, err.Error()))
						} else if rsp.Info != nil {
							if rsp.Info.CompanyName != "" {
								clue.CompanyName = rsp.Info.CompanyName
								log.WithContextInfof(ctx, fmt.Sprintf("fixCompanyName:%s 从cnStr查询备案更新企业名称,企业名称:%s", clue.Content, rsp.Info.CompanyName))
							}
						}
						if err := icp.Domain(ctx, &corePb.IcpDomainRequest{Domain: utils.GetTopDomain(cnStr), GetEquals: false}, rsp); err != nil {
							log.WithContextWarnf(ctx, fmt.Sprintf("fixCompanyName:%s 从cnStr查询备案更新企业名称,获取备案信息失败,Error:%s", clue.Content, err.Error()))
						} else if rsp.Info != nil {
							if rsp.Info.CompanyName != "" {
								clue.CompanyName = rsp.Info.CompanyName
								log.WithContextInfof(ctx, fmt.Sprintf("fixCompanyName:%s 从cnStr查询备案更新企业名称,企业名称:%s", clue.Content, rsp.Info.CompanyName))
							}
						}
						// 备案未查出企业名称时,从Whois查询企业名称
						if clue.CompanyName == "" {
							whoisRsp := &corePb.WhoisBasicResponse{}
							if whoisErr := whois.QueryBasicDomain(context.TODO(), whoisRsp, utils.GetTopDomain(cnStr)); whoisErr != nil {
								log.WithContextWarnf(ctx, fmt.Sprintf("fixCompanyName:%s 从domain查询Whois更新企业名称,获取Whois信息失败,Error:%s", clue.Content, whoisErr.Error()))
							} else if whoisRsp.RegistrantName != whois.PrivateTips {
								clue.CompanyName = whoisRsp.RegistrantName
								log.WithContextInfof(ctx, fmt.Sprintf("fixCompanyName:%s 从domain查询Whois更新企业名称,企业名称:%s", clue.Content, whoisRsp.RegistrantName))
							}
						}
					}
				}
				if oStr != "" && clue.CompanyName == "" {
					clue.CompanyName = oStr
					log.WithContextInfof(ctx, fmt.Sprintf("fixCompanyName:%s 从oStr更新企业名称,企业名称:%s", clue.Content, oStr))
				}
				e.updateFofaClue(clues, clue)
			}
			// 更新域名的企业名称
			if clue.Type == general_clues.ClueTypeDomain && clue.CompanyName == "" {
				// 备案查询企业名称
				rsp := &corePb.IcpResponse{}
				if err := icp.Domain(ctx, &corePb.IcpDomainRequest{Domain: clue.Content, GetEquals: false}, rsp); err != nil {
					log.WithContextWarnf(ctx, fmt.Sprintf("fixCompanyName:%s 从domain查询备案更新企业名称,获取备案信息失败,Error:%s", clue.Content, err.Error()))
				} else if rsp.Info != nil {
					if rsp.Info.CompanyName != "" {
						clue.CompanyName = rsp.Info.CompanyName
						e.updateFofaClue(clues, clue)
						log.WithContextInfof(ctx, fmt.Sprintf("fixCompanyName:%s 从domain查询备案更新企业名称,企业名称:%s", clue.Content, rsp.Info.CompanyName))
					}
				}
				// 备案未查出企业名称时,从Whois查询企业名称
				if clue.CompanyName == "" {
					whoisRsp := &corePb.WhoisBasicResponse{}
					if whoisErr := whois.QueryBasicDomain(context.TODO(), whoisRsp, clue.Content); whoisErr != nil {
						log.WithContextWarnf(ctx, fmt.Sprintf("fixCompanyName:%s 从domain查询Whois更新企业名称,获取Whois信息失败,Error:%s", clue.Content, whoisErr.Error()))
					} else if whoisRsp.RegistrantName != whois.PrivateTips {
						clue.CompanyName = whoisRsp.RegistrantName
						e.updateFofaClue(clues, clue)
						log.WithContextInfof(ctx, fmt.Sprintf("fixCompanyName:%s 从domain查询Whois更新企业名称,企业名称:%s", clue.Content, whoisRsp.RegistrantName))
					}
				}
			}
			// 更新ICP的企业名称
			if clue.Type == general_clues.ClueTypeIcp && clue.CompanyName == "" {
				rsp := &corePb.IcpResponse{}
				if err := icp.Icp(ctx, &corePb.IcpRequest{Icp: clue.Content, GetEquals: false}, rsp); err != nil {
					log.WithContextWarnf(ctx, fmt.Sprintf("fixCompanyName:%s 从icp查询备案更新企业名称,获取备案信息失败,Error:%s", clue.Content, err.Error()))
				} else if rsp.Info != nil {
					if rsp.Info.CompanyName != "" {
						clue.CompanyName = rsp.Info.CompanyName
						e.updateFofaClue(clues, clue)
						log.WithContextInfof(ctx, fmt.Sprintf("fixCompanyName:%s 从icp查询备案更新企业名称,企业名称:%s", clue.Content, rsp.Info.CompanyName))
					}
				}
			}
			// 更新ICON的企业名称
			if clue.Type == general_clues.ClueTypeIcon && clue.CompanyName == "" {
				if domain := utils.GetTopDomain(clue.Source); domain != "" {
					rsp := &corePb.IcpResponse{}
					if err := icp.Domain(ctx, &corePb.IcpDomainRequest{Domain: domain, GetEquals: false}, rsp); err != nil {
						log.WithContextWarnf(ctx, fmt.Sprintf("fixCompanyName:%s 从icon查询备案更新企业名称,获取备案信息失败,Error:%s", domain, err.Error()))
					} else if rsp.Info != nil {
						if rsp.Info.CompanyName != "" {
							clue.CompanyName = rsp.Info.CompanyName
							e.updateFofaClue(clues, clue)
							log.WithContextInfof(ctx, fmt.Sprintf("fixCompanyName:%s 从icon查询备案更新企业名称,企业名称:%s", domain, rsp.Info.CompanyName))
						}
					}
					// 备案未查出企业名称时,从Whois查询企业名称
					if clue.CompanyName == "" {
						whoisRsp := &corePb.WhoisBasicResponse{}
						if whoisErr := whois.QueryBasicDomain(context.TODO(), whoisRsp, domain); whoisErr != nil {
							log.WithContextWarnf(ctx, fmt.Sprintf("fixCompanyName:%s 从domain查询Whois更新企业名称,获取Whois信息失败,Error:%s", clue.Content, whoisErr.Error()))
						} else if whoisRsp.RegistrantName != whois.PrivateTips {
							clue.CompanyName = whoisRsp.RegistrantName
							e.updateFofaClue(clues, clue)
							log.WithContextInfof(ctx, fmt.Sprintf("fixCompanyName:%s 从domain查询Whois更新企业名称,企业名称:%s", clue.Content, whoisRsp.RegistrantName))
						}
					}
				}
			}
		}(key, value, f)
		return true
	})
	wg.Wait()
}

// canAddFofaClue 检测是否能添加FOFA线索,忽略已存在的
func (e *ExpandClue) canAddFofaClue(clues *sync.Map, clue *general_clues.GeneralClues) {
	var key string
	if clue.Type == general_clues.ClueTypeIcon {
		key = cast.ToString(clue.Type) + cast.ToString(clue.Hash)
		if oldClue, ok := clues.Load(key); ok {
			// 如果是ICON,新线索存在域名时,更新线索来源
			if !utils.IsDomain(oldClue.(*general_clues.GeneralClues).Source) {
				clues.Store(key, clue)
			}
		} else {
			clues.LoadOrStore(key, clue)
		}
	} else {
		key = cast.ToString(clue.Type) + clue.Content
		clues.LoadOrStore(key, clue)
	}
}

// updateFofaClue 更新FOFA线索
func (e *ExpandClue) updateFofaClue(clues *sync.Map, clue *general_clues.GeneralClues) {
	var key string
	if clue.Type == general_clues.ClueTypeIcon {
		key = cast.ToString(clue.Type) + cast.ToString(clue.Hash)
	} else {
		key = cast.ToString(clue.Type) + clue.Content
	}
	// 已存在的数据,根据企业名称判断是否需要更新
	if cl, ok := clues.Load(key); ok {
		sClue := cl.(*general_clues.GeneralClues)
		// 新的企业名称为空, 旧的企业名称不为空
		if sClue.CompanyName != "" && clue.CompanyName == "" {
			return
		}
		// 已存在的企业名称是中文, 新的数据企业名称不是中文
		if utils.IsChineseChar(sClue.CompanyName) && !utils.IsChineseChar(clue.CompanyName) {
			return
		}
	}
	clues.Store(key, clue)
}

// GetFofaCount 获取FOFA拉取数据总数
func (e *ExpandClue) getFofaCount(ctx context.Context, expandTypes []int, keyword ...string) (uint32, func(*process.Bar, *general_clues.History, ...*general_clues.GeneralClues)) {
	rsp := &corePb.FofaQueryCountResponse{}
	err := fofa.FofaQueryCount(ctx, rsp, &corePb.FofaQueryCountRequest{Qbase64: strings.Join(keyword, " || "), CanGetLimitCount: true})
	if err != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("GetFofaCount,Query:%s,Error:%s", strings.Join(keyword, " || "), err.Error()))
		// 重试一次,
		err = fofa.FofaQueryCount(ctx, rsp, &corePb.FofaQueryCountRequest{Qbase64: strings.Join(keyword, " || "), CanGetLimitCount: true})
	}
	if err != nil {
		// 未取到总数时,默认按1000条数据处理
		rsp = &corePb.FofaQueryCountResponse{Count: 1000}
		log.WithContextWarnf(ctx, fmt.Sprintf("GetFofaCount,重试失败:按默认1000条数据处理,Query:%s,Error:%s", strings.Join(keyword, " || "), err.Error()))
	}
	f := func(bar *process.Bar, his *general_clues.History, generalClues ...*general_clues.GeneralClues) {
		if len(generalClues) == 0 {
			e.fofaClueHandler(ctx, nil, bar, his, expandTypes, strings.Join(keyword, " || "))
		} else {
			e.fofaClueHandler(ctx, generalClues[0], bar, his, expandTypes, strings.Join(keyword, " || "))
		}
	}
	log.WithContextInfof(ctx, fmt.Sprintf("GetFofaCount,Query:%s,OK:%d", strings.Join(keyword, " || "), rsp.Count))
	return rsp.Count, f
}
