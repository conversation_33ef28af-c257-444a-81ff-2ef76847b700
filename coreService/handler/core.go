package handler

import (
	"context"
	"micro-service/coreService/handler/dnschecker"
	"net"
	"net/http"
	"strings"
	"time"

	"go-micro.dev/v4/errors"

	"micro-service/coreService/handler/chaziyu"
	"micro-service/coreService/handler/count"
	"micro-service/coreService/handler/engine"
	"micro-service/coreService/handler/ip138"
	"micro-service/coreService/handler/ip_domain_history"
	km "micro-service/coreService/handler/keyword_manage"
	"micro-service/coreService/handler/vuln"
	"micro-service/coreService/handler/whois"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql/cdn"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/rule_engine"
	"micro-service/pkg/utils"
)

type Core struct{}

func (e *Core) Subdomain(ctx context.Context, req *pb.SubdomainRequest, rsp *pb.SubdomainResponse) error {
	log.WithContextInfof(ctx, "[Core]: Subdomain -> domain: %v", req)
	if req.Domain == "" {
		return errors.New(pb.ServiceName, "域名不能为空!", 400)
	}
	cacheKey := "czy:" + utils.Md5Hash(req)
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: Subdomain -> domain:%s,Cache:%s", req.Domain, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: Subdomain -> domain:%s,NoCache:%s", req.Domain, cacheKey)
	if err := chaziyu.QueryDomain(ctx, rsp, req.Domain); err == nil {
		redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

func (e *Core) Whois(ctx context.Context, req *pb.WhoisDomainRequest, rsp *pb.WhoisResponse) error {
	cacheKey := "whois-d:" + utils.Md5Hash(req)
	if req.Domain == "" {
		return errors.New(pb.ServiceName, "域名不能为空!", 400)
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: Whois -> domain:%s,Cache:%s", req.Domain, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: Whois -> domain:%s,NoCache:%s", req.Domain, cacheKey)
	if err := whois.QueryDomain(ctx, rsp, req.Domain); err == nil {
		redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

func (e *Core) Ip138ByDomain(ctx context.Context, req *pb.Ip138DomainRequest, rsp *pb.Ip138DomainResponse) error {
	cacheKey := "ip138:" + utils.Md5Hash(req)
	if len(req.Domain) == 0 {
		return errors.New(pb.ServiceName, "域名不能为空!", 400)
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: Ip138ByDomain -> request:%v,Cache:%s", req, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: Ip138ByDomain -> request:%v,NoCache:%s", req, cacheKey)
	if err := ip138.QueryDomain(ctx, req, rsp); err == nil {
		redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

func (e *Core) Ip138ByIp(ctx context.Context, req *pb.Ip138IpRequest, rsp *pb.Ip138IpResponse) error {
	cacheKey := "ip138:" + utils.Md5Hash(req)
	if len(req.Ip) == 0 {
		return errors.New(pb.ServiceName, "Ip不能为空!", 400)
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: Ip138ByIp -> request:%v,Cache:%s", req, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: Ip138ByIp -> request:%v,NoCache:%s", req, cacheKey)
	if err := ip138.QueryIp(ctx, req, rsp); err == nil {
		redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

func (e *Core) EngineWebSite(ctx context.Context, req *pb.EngineSearchRequest, rsp *pb.EngineSearchResponse) error {
	log.WithContextInfof(ctx, "Received Core.EngineWebSite.Domain request: %v", req)
	if req.Domain == "" {
		return errors.New(pb.ServiceName, "域名不能为空!", 400)
	}
	cacheKey := "engine:" + utils.Md5Hash(req)
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: engine-Subdomain-cache -> domain:%s,Cache:%s", req.Domain, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: engine-Subdomain -> domain:%s,NoCache:%s", req.Domain, cacheKey)
	if err := engine.QueryDomain(ctx, rsp, req.Domain); err == nil {
		redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

func (e *Core) WhoisBasic(ctx context.Context, req *pb.WhoisDomainBasicRequest, rsp *pb.WhoisBasicResponse) error {
	if req.Domain == "" {
		return errors.BadRequest(pb.ServiceName, "查询域名不能为空")
	}
	err := whois.QueryBasicDomain(ctx, rsp, req.Domain)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core] Get Whois domain: %s info failed: %v", req.Domain, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) HoldingCompanyCount(_ context.Context, req *pb.HoldingCompanyCountRequest, rsp *pb.HoldingCompanyCountResponse) error {
	resp, err := count.CompanyCount(req.Name)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	rsp.Count = resp.Count
	return nil
}

// CVEVulnsByIPAssets 根据banner或header数据匹配CVE漏洞
func (e *Core) CVEVulnsByIPAssets(ctx context.Context, req *pb.CVEVulnsByIPAssetsRequest, rsp *pb.CVEVulnsByIPAssetsResponse) error {
	reqData := make([]*vuln.AssetMetadata, 0, len(req.List))
	for _, v := range req.List {
		reqData = append(reqData, &vuln.AssetMetadata{Banner: v.Banner, Protocol: v.Protocol})
	}
	l, err := vuln.GetVulnsWithHB(ctx, reqData)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}

	for i := range l {
		rsp.List = append(rsp.List, nil)
		_ = l[i]
	}

	return nil
}

// CheckIsCdn 检查是否是CDN
func (e *Core) CheckIsCdn(_ context.Context, req *pb.IsCdnRequest, rsp *pb.IsCdnResponse) error {
	if len(req.Hosts) < 1 {
		return nil
	}
	ipcards := cdn.NewCdnModel().GetCdnIpCards()
	domains := cdn.NewCdnModel().GetCdnDomains()
	for _, host := range req.Hosts {
		if ip := net.ParseIP(host); ip != nil {
			isCdn := false
			for _, ipRange := range ipcards {
				_, ipNet, err := net.ParseCIDR(ipRange)
				if err != nil {
					continue
				}
				if ipNet.Contains(ip) {
					isCdn = true
					break
				}
			}
			rsp.CdnList = append(rsp.CdnList, &pb.CdnList{Host: host, IsCdn: isCdn})
		} else {
			isCdn := false
			for _, cdnDomain := range domains {
				if strings.Contains(host, cdnDomain) {
					isCdn = true
					break
				} else if strings.Contains(host, "cdn") {
					isCdn = true
					break
				}
			}
			rsp.CdnList = append(rsp.CdnList, &pb.CdnList{Host: host, IsCdn: isCdn})
		}
	}
	return nil
}

// EngineRuleCreate 引擎规则-新建
func (e *Core) EngineRuleCreate(ctx context.Context, req *pb.EngineRuleCreateRequest, rsp *pb.Empty) error {
	if err := rule_engine.Create(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[Core] Engine_rules: user_id: %d, Create rule failed: %v", req.UserId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// EngineRuleList 引擎规则-列表
func (e *Core) EngineRuleList(ctx context.Context, req *pb.EngineRuleListRequest, rsp *pb.EngineRuleListResponse) error {
	if err := rule_engine.List(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Core] Engine_rules: user_id: %d, Get rule list failed: %v", req.UserId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// EngineRuleCategory 引擎规则-分类
func (e *Core) EngineRuleCategory(ctx context.Context, req *pb.EngineRuleCategoryRequest, rsp *pb.EngineRuleCategoryResponse) error {
	if err := rule_engine.Category(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Core] Engine_rules: user_id: %d, Get rule category failed: %v", req.UserId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// EngineRuleUpdate 引擎规则-更新
func (e *Core) EngineRuleUpdate(ctx context.Context, req *pb.EngineRuleUpdateRequest, rsp *pb.Empty) error {
	if err := rule_engine.Update(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[Core] Engine_rules: record_id: %d, Update rule failed: %v", req.Id, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// EngineRuleUpdateStatus 引擎规则-更新规则状态
func (e *Core) EngineRuleUpdateStatus(ctx context.Context, req *pb.EngineRuleListRequest, rsp *pb.Empty) error {
	if err := rule_engine.UpdateStatus(req); err != nil {
		log.WithContextErrorf(ctx, "[Core] Engine_rules: Update rule status failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// EngineRuleDelete 引擎规则-删除
func (e *Core) EngineRuleDelete(ctx context.Context, req *pb.EngineRuleListRequest, rsp *pb.Empty) error {
	if err := rule_engine.Delete(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[Core] Engine_rules: Delete rules failed: %v, user_id: %d", err, req.UserId)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) IpDomainHistory(ctx context.Context, req *pb.IpDomainHistoryRequest, rsp *pb.IpDomainHistoryResponse) error {
	err := ip_domain_history.IpDomainHistory(ctx, req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) KeywordManageDataSync(ctx context.Context, req *pb.KeywordManageDataSyncRequest, rsp *pb.KeywordManageDataSyncResponse) error {
	err := km.KeywordDataSync(req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core] KeywordManageDataSync: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) DnsChecker(ctx context.Context, req *pb.DnscheckerDomainRequest, rsp *pb.DnscheckerResponse) error {
	cacheKey := "dnschecker:" + req.Domain
	if len(req.Domain) == 0 {
		return errors.New(pb.ServiceName, "域名不能为空!", 400)
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: DnsChecker -> request:%v,Cache:%s", req.Domain, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: DnsChecker -> request:%v,NoCache:%s", req.Domain, cacheKey)
	if err := dnschecker.GetParseIp(ctx, req, rsp); err == nil {
		redis.SetCache(cacheKey, 72*3600*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}
