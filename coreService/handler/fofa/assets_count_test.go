package fofa

import (
	"context"
	"testing"
	"time"

	core "micro-service/coreService/proto"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/redis"
	"micro-service/pkg/fortest"
	"micro-service/pkg/utils"
	"github.com/bytedance/sonic"
)

func TestFofaAllAssetsCount(t *testing.T) {
	fortest.InitCfg()
	rsp := core.FofaAllAssetsCountResponse{}
	err := FofaAllAssetsCount(context.Background(), &core.FofaAllAssetsCountRequest{}, &rsp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log("domain", rsp.Domain)
	t.Log("Icp", rsp.Icp)
	t.Log("Cert", rsp.Cert)
	t.Log("Icon", rsp.Icon)
	t.Log("Org", rsp.Org)
	t.Log("DomainMom", rsp.DomainMom)
	t.Log("IcpMom", rsp.IcpMom)
	t.Log("CertMom", rsp.CertMom)
	t.Log("IconMom", rsp.IconMom)
	t.Log("OrgMom", rsp.OrgMom)
}

func Test_fofaCountWeekly(t *testing.T) {
	fortest.InitCfg()
	type args struct {
		ctx context.Context
		rsp *pb.FofaAllAssetsCountResponse
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				ctx: context.Background(),
				rsp: &pb.FofaAllAssetsCountResponse{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := fofaCountWeekly(tt.args.ctx, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("fofaCountWeekly() error = %v, wantErr %v", err, tt.wantErr)
			}
			t.Log(tt.args.rsp)
		})
	}
}

// Test_fofaCountWeekly_Debug 调试超时问题的详细测试
func Test_fofaCountWeekly_Debug(t *testing.T) {
	fortest.InitCfg()

	// 创建带超时的上下文，模拟实际调用场景
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	rsp := &pb.FofaAllAssetsCountResponse{}

	// 记录开始时间
	startTime := time.Now()
	t.Logf("开始执行 fofaCountWeekly，时间: %v", startTime)

	// 测试各个步骤的耗时
	lastWeekKey := GetStartOfWeek(time.Now(), 1).Format(utils.DateLayout)
	nowWeekKey := GetStartOfWeek(time.Now(), 0).Format(utils.DateLayout)
	t.Logf("生成周键值 - lastWeekKey: %s, nowWeekKey: %s", lastWeekKey, nowWeekKey)

	// 测试 Redis 连接
	redisStart := time.Now()
	nowData := redis.GetClient().HGet(ctx, FofaAllAssetsCountRedisKey, nowWeekKey).Val()
	redisElapsed := time.Since(redisStart)
	t.Logf("Redis HGet 耗时: %v, 结果长度: %d", redisElapsed, len(nowData))

	if nowData == "" {
		t.Log("Redis 中没有数据，需要调用 allAssetsCount")

		// 测试 allAssetsCount 的耗时
		countStart := time.Now()
		nowWeek, countErr := allAssetsCount(ctx)
		countElapsed := time.Since(countStart)
		t.Logf("allAssetsCount 耗时: %v, 错误: %v", countElapsed, countErr)
		t.Logf("获取到的数据: %+v", nowWeek)

		if countErr == nil {
			// 测试 Redis 写入
			writeStart := time.Now()
			data, marshalErr := sonic.Marshal(nowWeek)
			if marshalErr != nil {
				t.Errorf("序列化失败: %v", marshalErr)
			} else {
				err := redis.GetClient().HSet(ctx, FofaAllAssetsCountRedisKey, nowWeekKey, string(data)).Err()
				writeElapsed := time.Since(writeStart)
				t.Logf("Redis HSet 耗时: %v, 错误: %v", writeElapsed, err)
			}
		}
	} else {
		t.Log("从 Redis 获取到缓存数据")
	}

	// 执行完整的函数
	err := fofaCountWeekly(ctx, rsp)
	totalElapsed := time.Since(startTime)

	t.Logf("总耗时: %v, 错误: %v", totalElapsed, err)
	t.Logf("响应数据: %+v", rsp)

	// 检查上下文是否超时
	select {
	case <-ctx.Done():
		t.Logf("上下文已取消，原因: %v", ctx.Err())
	default:
		t.Log("上下文仍然有效")
	}
}

// Test_allAssetsCount_Debug 专门测试 allAssetsCount 函数的超时问题
func Test_allAssetsCount_Debug(t *testing.T) {
	fortest.InitCfg()

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	startTime := time.Now()
	t.Logf("开始执行 allAssetsCount，时间: %v", startTime)

	// 测试单个查询的耗时
	queries := []string{
		queryDomainNotEmpty,
		queryIconNotEmpty,
		queryCertNotEmpty,
		queryICPNotEmpty,
		queryOrgNotEmpty,
	}

	for i, query := range queries {
		queryStart := time.Now()
		count, err := assetsCount(query)
		queryElapsed := time.Since(queryStart)
		t.Logf("查询 %d (%s) 耗时: %v, 结果: %d, 错误: %v", i+1, query, queryElapsed, count, err)
	}

	// 测试并发查询
	concurrentStart := time.Now()
	result, err := allAssetsCount(ctx)
	concurrentElapsed := time.Since(concurrentStart)

	t.Logf("并发查询总耗时: %v, 错误: %v", concurrentElapsed, err)
	t.Logf("结果: %+v", result)

	totalElapsed := time.Since(startTime)
	t.Logf("总耗时: %v", totalElapsed)
}

func Test_allAssetsCount(t *testing.T) {
	fortest.InitCfg()
	gotAt, err := allAssetsCount(context.TODO())
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("%+v\n", gotAt)
}

// Test_assetsCountWithRetry 测试重试机制
func Test_assetsCountWithRetry(t *testing.T) {
	fortest.InitCfg()

	query := queryDomainNotEmpty
	startTime := time.Now()

	count, err := assetsCountWithRetry(query, 3)
	elapsed := time.Since(startTime)

	t.Logf("查询: %s", query)
	t.Logf("耗时: %v", elapsed)
	t.Logf("结果: %d", count)
	t.Logf("错误: %v", err)

	if err != nil {
		t.Errorf("assetsCountWithRetry failed: %v", err)
	}

	if count <= 0 {
		t.Errorf("Expected count > 0, got %d", count)
	}
}

// Test_fofaCountWeeklyOptimized 测试优化版本
func Test_fofaCountWeeklyOptimized(t *testing.T) {
	fortest.InitCfg()

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	rsp := &pb.FofaAllAssetsCountResponse{}

	startTime := time.Now()
	t.Logf("开始执行优化版本 fofaCountWeeklyOptimized，时间: %v", startTime)

	err := fofaCountWeeklyOptimized(ctx, rsp)
	elapsed := time.Since(startTime)

	t.Logf("优化版本总耗时: %v", elapsed)
	t.Logf("错误: %v", err)
	t.Logf("响应数据: %+v", rsp)

	if err != nil {
		t.Errorf("fofaCountWeeklyOptimized failed: %v", err)
	}

	// 验证响应数据的合理性
	if rsp.AllCount <= 0 {
		t.Errorf("Expected AllCount > 0, got %d", rsp.AllCount)
	}

	if rsp.DomainCount <= 0 {
		t.Errorf("Expected DomainCount > 0, got %d", rsp.DomainCount)
	}

	// 检查上下文是否超时
	select {
	case <-ctx.Done():
		t.Logf("上下文状态: %v", ctx.Err())
	default:
		t.Log("上下文仍然有效")
	}

	// 等待一下异步保存完成
	time.Sleep(2 * time.Second)
	t.Log("异步保存应该已完成")
}

// Test_allAssetsCountRobust 测试健壮版本的资产统计
func Test_allAssetsCountRobust(t *testing.T) {
	fortest.InitCfg()

	startTime := time.Now()
	t.Logf("开始执行 allAssetsCountRobust，时间: %v", startTime)

	result, err := allAssetsCountRobust()
	elapsed := time.Since(startTime)

	t.Logf("allAssetsCountRobust 耗时: %v", elapsed)
	t.Logf("错误: %v", err)
	t.Logf("结果: %+v", result)

	if err != nil {
		t.Errorf("allAssetsCountRobust failed: %v", err)
	}

	// 验证结果的合理性
	if result.Domain <= 0 {
		t.Errorf("Expected Domain > 0, got %d", result.Domain)
	}

	if result.Icon <= 0 {
		t.Errorf("Expected Icon > 0, got %d", result.Icon)
	}
}

// Test_ContextCanceled 测试上下文取消的情况
func Test_ContextCanceled(t *testing.T) {
	fortest.InitCfg()

	// 创建一个已经取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消

	rsp := &pb.FofaAllAssetsCountResponse{}

	startTime := time.Now()
	t.Logf("开始执行已取消上下文的测试，时间: %v", startTime)

	err := fofaCountWeeklyOptimized(ctx, rsp)
	elapsed := time.Since(startTime)

	t.Logf("已取消上下文测试耗时: %v", elapsed)
	t.Logf("错误: %v", err)
	t.Logf("响应数据: %+v", rsp)

	// 即使上下文已取消，函数也应该能够完成（因为使用了独立上下文）
	if err != nil {
		t.Logf("预期行为：上下文已取消但函数仍能完成: %v", err)
	}
}
