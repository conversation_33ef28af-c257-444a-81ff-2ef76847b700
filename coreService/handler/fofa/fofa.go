package fofa

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"

	"github.com/idoubi/goz"

	corePb "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

const HttpUrlByFofa = "https://fofa.info"

const serverErrorMsg = "server error, please try again later"

// generateRequestUrl 生成完整的请求URL用于调试
func generateRequestUrl(baseUrl string, params map[string]interface{}) string {
	if len(params) == 0 {
		return baseUrl
	}

	// 使用url.Values来安全地构建查询参数
	values := url.Values{}
	for key, value := range params {
		valueStr := fmt.Sprintf("%v", value)
		values.Add(key, valueStr)
	}

	// 构建完整URL
	fullUrl := baseUrl + "?" + values.Encode()
	return fullUrl
}

var searchField = [...]string{
	"ip", "port", "protocol", "base_protocol", "host", "link", "domain",
	"title", "icp", "cert", "isp", "fid", "lastupdatetime", "icon", "header",
	"icon_hash", "certs_valid", "cloud_name", "status_code", "banner"}

type (
	fofaData struct {
		Error   bool    `json:"error"`
		Size    uint32  `json:"size"`
		Page    uint32  `json:"page"`
		Mode    string  `json:"mode"`
		Query   string  `json:"query"`
		Results [][]any `json:"results"`
		Errmsg  string  `json:"errmsg"`
	}

	fofaPureDnsData struct {
		Error   bool                `json:"error"`
		Size    uint32              `json:"size"`
		Page    uint32              `json:"page"`
		Query   string              `json:"query"`
		Results []map[string]string `json:"results"`
		Errmsg  string              `json:"errmsg"`
	}

	fofaCount struct {
		Error  bool   `json:"error"`
		Size   uint32 `json:"size"`
		Page   uint32 `json:"page"`
		Errmsg string `json:"errmsg"`
	}

	hostData struct {
		Error         bool   `json:"error"`
		Errmsg        string `json:"errmsg"`
		RemainApiData uint32 `json:"remain_api_data"`
	}

	FofaQueryHotResp struct {
		Error  bool                   `json:"error"`
		Errmsg string                 `json:"errmsg"`
		Data   []*corePb.FofaQueryHot `json:"data,omitempty"`
	}
)

// CurlRequest CURL 请求
func CurlRequest(method, url string, opts ...goz.Options) (goz.ResponseBody, error) {
	cli := goz.NewClient()

	resp, err := cli.Request(method, url, opts...)
	if err != nil {
		return nil, err
	}

	content, err := resp.GetBody()
	if err != nil {
		return nil, err
	}

	return content, nil
}

func getFofaConfig(_ context.Context) cfg.Fofa {
	fofaList := cfg.LoadFofa()
	if len(fofaList) == 0 {
		fofaKeyStr, _ := utils.LaravelDecrypt("eyJpdiI6ImxOdnY4SUd1U1hKbi9kbGk0RGk5TlE9PSIsIm1hYyI6IjI4YjA1Y2FmMDI3Y2M3YTEzMzUwOWRkNWE0OWEwMjQyZmFiZjVhYmMwZjA0MDA5NmM2YWVjYzQ4NTRkOWFiNjMiLCJ2YWx1ZSI6Ik05Q3QzQnFiNHd3NlhsTW41bFJCT2xkSm0wRVFOZmRNZ3h3cjNBSGdSMDVoLzVOa2RtSStEbGphSThtTEtvK3BKY2l5b0RMR0FmWGdZcW00VEh5NVVHZ3cyTUh4eW8yR0o5YmRNalJDNVRRNzltSnIvZERkVHdxY3dRVTkralJHNG5pSFMyUElOalN0eUFCc2tKNlNQdm9xdkpnUmdBaUwvdEFuU1lvYVZBc1lQZ1FIRlh6RlAvR0dsRHdwVFE0WVNrdGRPV3J1YXdwYlNKR1Fqei94eHprMU83Ykh1cmVZZTJkbGszKzFBRnc9In0")
		fofaKeyStr = strings.Replace(strings.Replace(strings.Replace(fofaKeyStr, `"{`, "{", -1), `}"`, "}", -1), `\"`, `"`, -1)
		cfgFofa := cfg.Fofa{}
		_ = json.Unmarshal([]byte(fofaKeyStr), &cfgFofa)
		return cfgFofa
	}
	// 获取fofa配置
	return fofaList[rand.Intn(len(fofaList))]
}

// HttpRequest 发起 HTTP 请求，支持 GET 和 POST 方法，body 参数为 map 格式
func HttpRequest(method, reqUrl string, headers, body map[string]any) (string, error) {
	client := &http.Client{Timeout: 60 * time.Second} // 增加到60秒，适应FOFA查询的复杂性

	var reqBody io.Reader

	method = strings.ToUpper(method)
	switch method {
	case "GET":
		// 序列化 body 为 query string，拼接到 URL
		if len(body) > 0 {
			values := url.Values{}
			for k, v := range body {
				values.Set(k, utils.SafeString(v))
			}
			if strings.Contains(reqUrl, "?") {
				reqUrl += "&" + values.Encode()
			} else {
				reqUrl += "?" + values.Encode()
			}
		}
	case "POST":
		if body != nil {
			jsonBytes, err := json.Marshal(body)
			if err != nil {
				return "", err
			}
			reqBody = bytes.NewBuffer(jsonBytes)
		}
	default:
		return "", errors.New("暂不支持的请求方法: " + method)
	}

	req, err := http.NewRequest(method, reqUrl, reqBody)
	if err != nil {
		return "", err
	}

	// 设置请求头
	for k, v := range headers {
		req.Header.Set(k, utils.SafeString(v))
	}
	if method == "POST" && req.Header.Get("Content-Type") == "" {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return "", errors.New("请求失败，状态码: " + resp.Status)
	}

	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(respBytes), nil
}

// CurlClient CURL 客户端
func CurlClient(ctx context.Context, url string, headers, formParams map[string]interface{}, isPost bool) (goz.ResponseBody, error) {
	log.Infof("CurlClient", "开始请求fofa接口,url:", url, "formParams:", formParams)
	cli := goz.NewClient()
	if isPost {
		resp, err := cli.Post(url, goz.Options{
			Headers:    headers,
			FormParams: formParams,
		})
		if err != nil {
			log.WithContextInfof(ctx, "fofa接口post请求%v", err)
		}
		if resp.GetStatusCode() != http.StatusOK {
			log.WithContextErrorf(ctx, "fofa接口post请求状态码异常:%v", resp.GetStatusCode())
			return nil, errors.New("fofa接口post请求状态码异常:" + strconv.Itoa(resp.GetStatusCode()))
		}
		content, err := resp.GetBody()
		if err != nil {
			log.WithContextInfof(ctx, "fofa接口post请求body异常:%v", err)
		}
		return content, nil
	}
	resp, err := cli.Get(url, goz.Options{
		Headers: headers,
		Query:   formParams,
	})
	if err != nil {
		log.WithContextInfof(ctx, "fofa接口get请求%v", err)
	}
	if resp != nil {
		statusCode := resp.GetStatusCode()
		if statusCode != http.StatusOK {
			log.WithContextErrorf(ctx, "fofa接口get请求状态码异常:%v", statusCode)
			return nil, errors.New("fofa接口get请求状态码异常:" + strconv.Itoa(statusCode))
		}
		content, err := resp.GetBody()
		if err != nil {
			log.WithContextInfof(ctx, "fofa接口get请求body异常:%v", err)
		}
		return content, nil
	}
	return goz.ResponseBody{}, fmt.Errorf("未获取到响应")
}

// FofaQuery FOFA Search
func FofaQuery(ctx context.Context, rsp *corePb.FofaQueryResponse, param *corePb.FofaQueryRequest) error {
	// 记录开始时间
	startTime := time.Now()

	// 生成参数的MD5用于追踪
	paramStr := utils.AnyToStr(param)
	paramMD5 := utils.Md5sHash(paramStr, false)

	log.WithContextInfof(ctx, "[FOFA查询开始] 追踪ID: %s, 开始时间: %s, 请求参数: %s",
		paramMD5, startTime.Format("2006-01-02 15:04:05.000"), paramStr)

	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	fofaUrl := HttpUrlByFofa + "/api/v1/search/all"
	headers := make(map[string]interface{})
	params := make(map[string]interface{})
	q, err := joinQueryRange(param.Qbase64, param.DateAfter, after)
	if err != nil {
		return err
	}

	param.Field = utils.ListDistinct(param.Field)
	switch len(param.Field) {
	case 0:
		param.Field = searchField[:]
	case 1:
		param.Field = append(param.Field, "port")
	}
	params["fields"] = strings.Join(param.Field, ",")
	params["qbase64"] = base64.StdEncoding.EncodeToString([]byte(q))
	params["email"] = fofaConfig.FofaEmail
	params["key"] = fofaConfig.FofaKey
	params["full"] = strconv.FormatBool(param.Full)
	if param.Page < 1 {
		param.Page = 1
	}
	if uint(param.Size) > fofaConfig.FofaSize || param.Size == 0 {
		param.Size = uint32(fofaConfig.FofaSize)
	}
	params["page"] = strconv.Itoa(int(param.Page))
	params["size"] = strconv.Itoa(int(param.Size))

	// 记录查询信息，便于调试
	log.WithContextInfof(ctx, "[FOFA查询参数] 追踪ID: %s, 查询条件: %s, 页码: %d, 大小: %d, FOFA URL: %s",
		paramMD5, param.Qbase64, param.Page, param.Size, fofaUrl)

	// 记录实际发送给FOFA的参数
	log.WithContextInfof(ctx, "[FOFA请求参数] 追踪ID: %s, 发送参数: %s", paramMD5, utils.AnyToStr(params))

	// 生成完整请求URL用于调试
	requestUrl := generateRequestUrl(fofaUrl, params)
	log.WithContextInfof(ctx, "[FOFA调试URL] 追踪ID: %s, 请求URL: %s", paramMD5, requestUrl)

	// 添加重试机制，最多重试3次
	var response string
	maxRetries := 3

	for i := 0; i < maxRetries; i++ {
		// 记录每次请求的开始时间
		requestStartTime := time.Now()
		log.WithContextInfof(ctx, "[FOFA HTTP请求开始] 追踪ID: %s, 第%d次请求, 开始时间: %s",
			paramMD5, i+1, requestStartTime.Format("2006-01-02 15:04:05.000"))

		response, err = HttpRequest("POST", fofaUrl, headers, params)

		// 记录每次请求的结束时间和耗时
		requestEndTime := time.Now()
		requestDuration := requestEndTime.Sub(requestStartTime)

		if err == nil {
			log.WithContextInfof(ctx, "[FOFA HTTP请求成功] 追踪ID: %s, 第%d次请求成功, 结束时间: %s, 耗时: %v, 响应长度: %d",
				paramMD5, i+1, requestEndTime.Format("2006-01-02 15:04:05.000"), requestDuration, len(response))
			break // 成功，跳出重试循环
		}

		// 记录请求失败信息
		log.WithContextErrorf(ctx, "[FOFA HTTP请求失败] 追踪ID: %s, 第%d次请求失败, 结束时间: %s, 耗时: %v, 错误: %s",
			paramMD5, i+1, requestEndTime.Format("2006-01-02 15:04:05.000"), requestDuration, err.Error())

		// 检查是否是429限流错误
		if strings.Contains(err.Error(), "429") || strings.Contains(err.Error(), "Too Many Requests") {
			log.WithContextWarnf(ctx, "[FOFA_QUERY_RATE_LIMIT] FOFA API限流, 追踪ID: %s, 第%d次请求, 错误: %v, 请求URL: %s",
				paramMD5, i+1, err, requestUrl)

			// 429错误也可以重试，但等待时间更长
			if i < maxRetries-1 {
				sleepDuration := time.Duration(i+1) * 10 * time.Second // 限流错误等待更长时间
				log.WithContextInfof(ctx, "[FOFA限流重试等待] 追踪ID: %s, 等待 %v 后进行第%d次重试", paramMD5, sleepDuration, i+2)
				time.Sleep(sleepDuration)
				continue
			}
		}

		// 检查是否是超时错误
		if strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "deadline exceeded") {
			log.WithContextWarnf(ctx, "[FOFA请求超时重试] 追踪ID: %s, 第%d次重试 - %s", paramMD5, i+1, err.Error())
			if i < maxRetries-1 {
				// 等待一段时间后重试（指数退避）
				sleepDuration := time.Duration(i+1) * 2 * time.Second
				log.WithContextInfof(ctx, "[FOFA重试等待] 追踪ID: %s, 等待 %v 后进行第%d次重试", paramMD5, sleepDuration, i+2)
				time.Sleep(sleepDuration)
				continue
			}
		}

		// 非超时错误或达到最大重试次数，直接返回错误
		log.WithContextErrorf(ctx, "[FOFA请求最终失败] 追踪ID: %s, 请求fofa报错了第一处 Core.FofaQuery request: %v, 报错信息: %s", paramMD5, param, err.Error())

		// 特殊标记429错误
		if strings.Contains(err.Error(), "429") || strings.Contains(err.Error(), "Too Many Requests") {
			log.WithContextErrorf(ctx, "[FOFA_QUERY_FINAL_RATE_LIMIT] FOFA API最终限流失败, 追踪ID: %s, 重试%d次后仍然失败, 错误: %v",
				paramMD5, maxRetries, err)
		}

		// 记录总耗时
		totalDuration := time.Since(startTime)
		log.WithContextErrorf(ctx, "[FOFA查询结束-失败] 追踪ID: %s, 结束时间: %s, 总耗时: %v",
			paramMD5, time.Now().Format("2006-01-02 15:04:05.000"), totalDuration)
		return err
	}

	// 记录JSON解析开始
	parseStartTime := time.Now()
	log.WithContextInfof(ctx, "[FOFA响应解析开始] 追踪ID: %s, 解析开始时间: %s, 响应前500字符: %.500s",
		paramMD5, parseStartTime.Format("2006-01-02 15:04:05.000"), response)

	var data fofaData
	err = json.Unmarshal([]byte(response), &data)

	parseEndTime := time.Now()
	parseDuration := parseEndTime.Sub(parseStartTime)

	if err != nil {
		log.WithContextErrorf(ctx, "[FOFA响应解析失败] 追踪ID: %s, 请求fofa报错了第二处 Core.FofaQuery request: %v, 报错信息: %s, 解析耗时: %v",
			paramMD5, param, err.Error(), parseDuration)

		// 记录总耗时
		totalDuration := time.Since(startTime)
		log.WithContextErrorf(ctx, "[FOFA查询结束-解析失败] 追踪ID: %s, 结束时间: %s, 总耗时: %v",
			paramMD5, time.Now().Format("2006-01-02 15:04:05.000"), totalDuration)
		return err
	}

	log.WithContextInfof(ctx, "[FOFA响应解析成功] 追踪ID: %s, 解析耗时: %v, 数据条数: %d, 是否有错误: %v",
		paramMD5, parseDuration, len(data.Results), data.Error)

	if data.Error {
		log.WithContextErrorf(ctx, "[FOFA接口返回错误] 追踪ID: %s, fofa接口返回有错误码, 错误消息: %s, 完整响应: %v",
			paramMD5, data.Errmsg, response)

		if data.Errmsg == serverErrorMsg {
			log.WithContextInfof(ctx, "[FOFA服务器错误重试] 追踪ID: %s, 检测到服务器错误，等待3秒后重试", paramMD5)
			time.Sleep(time.Duration(3) * time.Second)

			retryStartTime := time.Now()
			log.WithContextInfof(ctx, "[FOFA服务器错误重试开始] 追踪ID: %s, 重试开始时间: %s",
				paramMD5, retryStartTime.Format("2006-01-02 15:04:05.000"))

			response, err := HttpRequest("POST", fofaUrl, headers, params)

			retryEndTime := time.Now()
			retryDuration := retryEndTime.Sub(retryStartTime)

			if err != nil {
				log.WithContextErrorf(ctx, "[FOFA服务器错误重试失败] 追踪ID: %s, 重试请求失败, 耗时: %v, 错误: %s",
					paramMD5, retryDuration, err.Error())

				// 记录总耗时
				totalDuration := time.Since(startTime)
				log.WithContextErrorf(ctx, "[FOFA查询结束-重试失败] 追踪ID: %s, 结束时间: %s, 总耗时: %v",
					paramMD5, time.Now().Format("2006-01-02 15:04:05.000"), totalDuration)
				return err
			}

			log.WithContextInfof(ctx, "[FOFA服务器错误重试成功] 追踪ID: %s, 重试请求成功, 耗时: %v, 响应长度: %d",
				paramMD5, retryDuration, len(response))

			err = json.Unmarshal([]byte(response), &data)
			if err != nil {
				log.WithContextErrorf(ctx, "[FOFA服务器错误重试解析失败] 追踪ID: %s, 重试响应解析失败, 错误: %s",
					paramMD5, err.Error())

				// 记录总耗时
				totalDuration := time.Since(startTime)
				log.WithContextErrorf(ctx, "[FOFA查询结束-重试解析失败] 追踪ID: %s, 结束时间: %s, 总耗时: %v",
					paramMD5, time.Now().Format("2006-01-02 15:04:05.000"), totalDuration)
				return err
			}

			if data.Error {
				log.WithContextErrorf(ctx, "[FOFA服务器错误重试仍有错误] 追踪ID: %s, 重试后仍有错误, 响应: %v", paramMD5, response)
				rsp.Error = true

				// 记录总耗时
				totalDuration := time.Since(startTime)
				log.WithContextInfof(ctx, "[FOFA查询结束-重试后仍有错误] 追踪ID: %s, 结束时间: %s, 总耗时: %v",
					paramMD5, time.Now().Format("2006-01-02 15:04:05.000"), totalDuration)
				return nil
			}

			log.WithContextInfof(ctx, "[FOFA服务器错误重试成功] 追踪ID: %s, 重试后成功获取数据", paramMD5)
		} else {
			log.WithContextErrorf(ctx, "[FOFA接口业务错误] 追踪ID: %s, 非服务器错误，直接返回错误状态", paramMD5)
			rsp.Error = true

			// 记录总耗时
			totalDuration := time.Since(startTime)
			log.WithContextInfof(ctx, "[FOFA查询结束-业务错误] 追踪ID: %s, 结束时间: %s, 总耗时: %v",
				paramMD5, time.Now().Format("2006-01-02 15:04:05.000"), totalDuration)
			return nil
		}
	}

	// 记录数据处理开始
	processStartTime := time.Now()
	log.WithContextInfof(ctx, "[FOFA数据处理开始] 追踪ID: %s, 处理开始时间: %s, 原始数据条数: %d",
		paramMD5, processStartTime.Format("2006-01-02 15:04:05.000"), len(data.Results))

	// 打印所有FOFA返回的原始数据（过滤掉icon和cert字段）
	log.WithContextInfof(ctx, "[FOFA原始数据] 追踪ID: %s, 字段列表: %v", paramMD5, param.Field)
	for i, result := range data.Results {
		// 创建过滤后的数据用于打印
		filteredResult := make([]interface{}, len(result))
		copy(filteredResult, result)

		// 找到icon和cert字段的索引并替换为占位符
		for j, field := range param.Field {
			if j < len(filteredResult) && filteredResult[j] != nil {
				if field == "icon" {
					iconStr := utils.SafeString(filteredResult[j])
					if len(iconStr) > 50 {
						filteredResult[j] = fmt.Sprintf("[ICON数据已省略,长度:%d]", len(iconStr))
					}
				} else if field == "cert" {
					certStr := utils.SafeString(filteredResult[j])
					if len(certStr) > 50 {
						filteredResult[j] = fmt.Sprintf("[CERT数据已省略,长度:%d]", len(certStr))
					}
				}
			}
		}

		log.WithContextInfof(ctx, "[FOFA原始数据] 追踪ID: %s, 第%d条: %v", paramMD5, i+1, filteredResult)
	}

	fields := param.Field
	processedCount := 0
	skippedCount := 0
	ipSet := make(map[string]bool)         // 用于统计去重IP
	ipPortMap := make(map[string][]string) // 用于统计每个IP的端口列表

	for i, v := range data.Results {
		if len(v) == 0 {
			skippedCount++
			continue
		}

		m := make(map[string]any, len(fields))
		for key, value := range v {
			m[fields[key]] = value
		}

		// 提取IP和端口信息用于统计
		var ip, port string
		if ipVal, exists := m["ip"]; exists && ipVal != nil {
			ip = utils.SafeString(ipVal)
		}
		if portVal, exists := m["port"]; exists && portVal != nil {
			port = utils.SafeString(portVal)
		}

		// 统计IP和端口
		if ip != "" {
			ipSet[ip] = true
			if port != "" {
				if _, exists := ipPortMap[ip]; !exists {
					ipPortMap[ip] = make([]string, 0)
				}
				// 检查端口是否已存在，避免重复
				portExists := false
				for _, existingPort := range ipPortMap[ip] {
					if existingPort == port {
						portExists = true
						break
					}
				}
				if !portExists {
					ipPortMap[ip] = append(ipPortMap[ip], port)
				}
			}
		}

		// 打印每条处理的数据详情（过滤掉icon和cert字段）
		filteredM := make(map[string]any)
		for k, v := range m {
			if v != nil {
				if k == "icon" {
					iconStr := utils.SafeString(v)
					if len(iconStr) > 50 {
						filteredM[k] = fmt.Sprintf("[ICON数据已省略,长度:%d]", len(iconStr))
					} else {
						filteredM[k] = v
					}
				} else if k == "cert" {
					certStr := utils.SafeString(v)
					if len(certStr) > 50 {
						filteredM[k] = fmt.Sprintf("[CERT数据已省略,长度:%d]", len(certStr))
					} else {
						filteredM[k] = v
					}
				} else {
					filteredM[k] = v
				}
			} else {
				filteredM[k] = v
			}
		}
		log.WithContextInfof(ctx, "[FOFA数据处理] 追踪ID: %s, 第%d条处理后: IP=%s, Port=%s, 完整数据: %v, 请求URL: %s",
			paramMD5, i+1, ip, port, filteredM, requestUrl)

		bs, err := json.Marshal(m)
		if err != nil {
			log.WithContextWarnf(ctx, "[FOFA数据处理警告] 追踪ID: %s, 第%d条数据JSON序列化失败: %v", paramMD5, i+1, err)
			skippedCount++
			continue
		}

		initData := corePb.Asset{}
		if err = json.Unmarshal(bs, &initData); err != nil {
			log.WithContextWarnf(ctx, "[FOFA数据处理警告] 追踪ID: %s, 第%d条数据反序列化失败: %v", paramMD5, i+1, err)
			skippedCount++
			continue
		}

		rsp.Sdata = append(rsp.Sdata, &initData)
		processedCount++
	}

	// 记录数据处理结束
	processEndTime := time.Now()
	processDuration := processEndTime.Sub(processStartTime)

	// 统计去重IP信息
	uniqueIpCount := len(ipSet)
	var ipList []string
	for ip := range ipSet {
		ipList = append(ipList, ip)
	}

	// 打印去重IP列表
	log.WithContextInfof(ctx, "[FOFA去重IP统计] 追踪ID: %s, 去重IP数量: %d, IP列表: %v",
		paramMD5, uniqueIpCount, ipList)

	// 打印每个IP的端口详情
	for ip, ports := range ipPortMap {
		log.WithContextInfof(ctx, "[FOFA IP端口详情] 追踪ID: %s, IP: %s, 端口列表: %v, 端口数量: %d",
			paramMD5, ip, ports, len(ports))
	}

	rsp.Error = data.Error
	rsp.Size = &data.Size
	rsp.Page = &data.Page

	// 记录总耗时和最终结果
	totalDuration := time.Since(startTime)
	log.WithContextInfof(ctx, "[FOFA数据处理完成] 追踪ID: %s, 处理耗时: %v, 成功处理: %d条, 跳过: %d条, 最终返回: %d条, 去重IP数量: %d",
		paramMD5, processDuration, processedCount, skippedCount, len(rsp.Sdata), uniqueIpCount)

	log.WithContextInfof(ctx, "[FOFA查询结束-成功] 追踪ID: %s, 结束时间: %s, 总耗时: %v, 请求参数: %v",
		paramMD5, time.Now().Format("2006-01-02 15:04:05.000"), totalDuration, param)

	return nil
}

// FofaQueryCount FOFA Search Count
func FofaQueryCount(ctx context.Context, rsp *corePb.FofaQueryCountResponse, param *corePb.FofaQueryCountRequest) error {
	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	fofaUrl := HttpUrlByFofa + "/api/v1/search/all"
	headers := make(map[string]interface{})
	params := make(map[string]interface{})
	params["email"] = fofaConfig.FofaEmail
	params["key"] = fofaConfig.FofaKey
	params["size"] = 1
	params["full"] = fmt.Sprintf("%t", param.Full)
	params["page"] = 1
	q, err := joinQueryRange(param.Qbase64, param.DateAfter, after)
	if err != nil {
		return err
	}
	params["qbase64"] = base64.StdEncoding.EncodeToString([]byte(q))
	response, err := HttpRequest("GET", fofaUrl, headers, params)
	if err != nil {
		return err
	}

	// 解析获取值
	var count fofaCount
	// string 转为 fofaCount
	err = json.Unmarshal([]byte(response), &count)
	if err != nil {
		return err
	}

	if count.Error {
		log.WithContextInfof(ctx, "Received Core.FofaQueryCount response: %v", response)
		if count.Errmsg == serverErrorMsg {
			time.Sleep(time.Duration(3) * time.Second)
			response, err := CurlClient(ctx, fofaUrl, headers, params, false)
			if err != nil {
				return err
			}
			err = json.Unmarshal([]byte(response.GetContents()), &count)
			if err != nil {
				return err
			}
			if count.Error {
				log.WithContextInfof(ctx, "Received Core.FofaQueryCount try begin response: %v", response)
				rsp.Error = true
				return nil
			}
		} else {
			rsp.Error = true
			return nil
		}
	}

	if param.CanGetLimitCount {
		if count.Size > uint32(fofaConfig.FofaPullMax) {
			count.Size = uint32(fofaConfig.FofaPullMax)
		}
	}
	rsp.Count = count.Size
	return nil
}

// FofaHost FOFA Host
func FofaHost(ctx context.Context, rsp *corePb.FofaHostResponse, param *corePb.FofaHostRequest) error {
	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	detailStr := ""
	if param.HasDetail {
		detailStr = "?detail=true"
	}
	fofaUrl := HttpUrlByFofa + "/api/v1/host/" + param.Host + detailStr
	headers := make(map[string]interface{})
	params := make(map[string]interface{})
	params["email"] = fofaConfig.FofaEmail
	params["key"] = fofaConfig.FofaKey
	response, err := CurlClient(ctx, fofaUrl, headers, params, false)
	if err != nil {
		return err
	}
	// 解析获取值
	var hData hostData
	err = json.Unmarshal([]byte(response.GetContents()), &hData)
	if err != nil {
		return err
	}

	if hData.Error {
		log.WithContextInfof(ctx, "Received Core.FofaHost response: %v", response)
		if hData.Errmsg == serverErrorMsg {
			time.Sleep(time.Duration(3) * time.Second)
			response, err = CurlClient(ctx, fofaUrl, headers, params, false)
			if err != nil {
				return err
			}
			err = json.Unmarshal([]byte(response.GetContents()), &rsp)
			if err != nil {
				return err
			}
			if hData.Error {
				log.WithContextInfof(ctx, "Received Core.FofaHost try begin response: %v", response)
				rsp.Error = true
				return nil
			}
		} else {
			rsp.Error = true
			return nil
		}
	} else {
		err = json.Unmarshal([]byte(response.GetContents()), &rsp)
		if err != nil {
			return err
		}
	}
	return nil
}

// FofaQueryHot 热点数据
func FofaQueryHot(ctx context.Context, count string, param *FofaQueryHotResp) error {
	fofaUrl := HttpUrlByFofa + "/api/v1/search/suggestion"
	headers := make(map[string]interface{})
	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	params := make(map[string]interface{})
	params["email"] = fofaConfig.FofaEmail
	params["key"] = fofaConfig.FofaKey
	params["count"] = count

	response, err := CurlClient(ctx, fofaUrl, headers, params, false)
	if err != nil {
		return err
	}

	// 解析获取值
	err = json.Unmarshal([]byte(response.GetContents()), &param)
	if err != nil {
		return err
	}

	if param.Error {
		return errors.New(param.Errmsg)
	}
	return nil
}

// FofaPureDns 纯解析数据
func FofaPureDns(ctx context.Context, rsp *corePb.FofaPureDnsResponse, param *corePb.FofaPureDnsRequest) error {
	// 记录请求开始
	startTime := time.Now()
	log.WithContextInfof(ctx, "[FOFA_PUREDNS_CORE_START] 开始处理PureDns核心请求, 域名: %s, 页码: %d, 大小: %d, 开始时间: %s",
		param.Domain, param.Page, param.Size, startTime.Format("2006-01-02 15:04:05.000"))

	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	fofaUrl := HttpUrlByFofa + "/api/v1/search/puredns"
	headers := make(map[string]interface{})
	params := make(map[string]interface{})

	params["email"] = fofaConfig.FofaEmail
	params["key"] = fofaConfig.FofaKey
	params["domain"] = param.Domain
	params["page"] = param.Page
	if param.Page < 1 {
		params["page"] = 1
	}
	if param.Size > 0 {
		params["size"] = strconv.Itoa(int(param.Size))
	}
	params["page"] = strconv.Itoa(int(param.Page))

	// 生成完整请求URL用于调试
	requestUrl := generateRequestUrl(fofaUrl, params)
	log.WithContextInfof(ctx, "[FOFA_PUREDNS_REQUEST_URL] 请求URL: %s", requestUrl)

	// 添加全局限流控制
	log.WithContextInfof(ctx, "[FOFA_PUREDNS_RATE_LIMIT_CHECK] 检查限流, 域名: %s", param.Domain)
	time.Sleep(3 * time.Second) // 强制等待2秒，确保不超过0.5 QPS

	// 记录开始调用FOFA API
	apiCallStart := time.Now()
	log.WithContextInfof(ctx, "[FOFA_PUREDNS_API_CALL_START] 开始调用FOFA API, 域名: %s, API调用时间: %s",
		param.Domain, apiCallStart.Format("2006-01-02 15:04:05.000"))

	response, err := HttpRequest("GET", fofaUrl, headers, params)

	// 记录API调用结果
	apiCallEnd := time.Now()
	apiCallDuration := apiCallEnd.Sub(apiCallStart)

	if err != nil {
		log.WithContextErrorf(ctx, "[FOFA_PUREDNS_API_ERROR] FOFA API调用失败, 域名: %s, 调用耗时: %v, 错误详情: %v, 请求URL: %s",
			param.Domain, apiCallDuration, err, requestUrl)

		// 检查是否是429错误
		if strings.Contains(err.Error(), "429") || strings.Contains(err.Error(), "Too Many Requests") {
			log.WithContextWarnf(ctx, "[FOFA_PUREDNS_RATE_LIMIT] FOFA API限流, 域名: %s, 错误: %v", param.Domain, err)
		}

		// 检查是否是超时错误
		if strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "deadline") {
			log.WithContextWarnf(ctx, "[FOFA_PUREDNS_TIMEOUT] FOFA API超时, 域名: %s, 错误: %v", param.Domain, err)
		}

		return err
	}

	log.WithContextInfof(ctx, "[FOFA_PUREDNS_API_SUCCESS] FOFA API调用成功, 域名: %s, 调用耗时: %v, 响应长度: %d",
		param.Domain, apiCallDuration, len(response))

	// 开始解析响应数据
	parseStart := time.Now()
	log.WithContextInfof(ctx, "[FOFA_PUREDNS_PARSE_START] 开始解析响应数据, 域名: %s", param.Domain)

	var data fofaPureDnsData
	err = json.Unmarshal([]byte(response), &data)
	if err != nil {
		log.WithContextErrorf(ctx, "[FOFA_PUREDNS_PARSE_ERROR] JSON解析失败, 域名: %s, 错误: %v, 响应内容: %s",
			param.Domain, err, response)
		return err
	}

	log.WithContextInfof(ctx, "[FOFA_PUREDNS_PARSE_SUCCESS] JSON解析成功, 域名: %s, 错误状态: %t, 结果数量: %d, 错误信息: %s",
		param.Domain, data.Error, len(data.Results), data.Errmsg)

	if data.Error {
		log.WithContextWarnf(ctx, "[FOFA_PUREDNS_API_RESPONSE_ERROR] FOFA API返回错误, 域名: %s, 错误信息: %s, 完整响应: %s",
			param.Domain, data.Errmsg, response)

		if data.Errmsg == serverErrorMsg {
			log.WithContextInfof(ctx, "[FOFA_PUREDNS_RETRY] 服务器错误，3秒后重试, 域名: %s", param.Domain)
			time.Sleep(time.Duration(3) * time.Second)

			// 重试请求
			retryStart := time.Now()
			response, err := HttpRequest("GET", fofaUrl, headers, params)
			retryDuration := time.Since(retryStart)

			if err != nil {
				log.WithContextErrorf(ctx, "[FOFA_PUREDNS_RETRY_ERROR] 重试请求失败, 域名: %s, 重试耗时: %v, 错误: %v",
					param.Domain, retryDuration, err)
				return err
			}

			log.WithContextInfof(ctx, "[FOFA_PUREDNS_RETRY_SUCCESS] 重试请求成功, 域名: %s, 重试耗时: %v",
				param.Domain, retryDuration)

			err = json.Unmarshal([]byte(response), &data)
			if err != nil {
				log.WithContextErrorf(ctx, "[FOFA_PUREDNS_RETRY_PARSE_ERROR] 重试响应解析失败, 域名: %s, 错误: %v",
					param.Domain, err)
				return err
			}

			if data.Error {
				log.WithContextErrorf(ctx, "[FOFA_PUREDNS_RETRY_STILL_ERROR] 重试后仍然错误, 域名: %s, 错误信息: %s, 响应: %s",
					param.Domain, data.Errmsg, response)
				rsp.Error = true
				return nil
			}
		} else {
			log.WithContextErrorf(ctx, "[FOFA_PUREDNS_NON_SERVER_ERROR] 非服务器错误, 域名: %s, 错误信息: %s",
				param.Domain, data.Errmsg)
			rsp.Error = true
			return nil
		}
	}

	// 处理结果数据
	processedCount := 0
	for i, v := range data.Results {
		var initData corePb.PureDnsInfo
		initData.Ip = v["ip"]
		initData.Host = v["host"]
		initData.LastUpdateTime = v["last_update_time"]
		rsp.Items = append(rsp.Items, &initData)
		processedCount++

		// 每10条记录打印一次处理进度
		if (i+1)%10 == 0 || i == len(data.Results)-1 {
			log.WithContextInfof(ctx, "[FOFA_PUREDNS_PROCESS] 处理进度, 域名: %s, 已处理: %d/%d, 当前记录: IP=%s, Host=%s",
				param.Domain, i+1, len(data.Results), initData.Ip, initData.Host)
		}
	}

	// 记录处理完成
	parseEnd := time.Now()
	parseDuration := parseEnd.Sub(parseStart)
	totalDuration := time.Since(startTime)

	rsp.Error = data.Error
	rsp.Total = &data.Size
	rsp.Page = &data.Page

	log.WithContextInfof(ctx, "[FOFA_PUREDNS_CORE_SUCCESS] PureDns核心处理完成, 域名: %s, 总耗时: %v, 解析耗时: %v, 处理记录数: %d, 页码: %d, 总数: %d",
		param.Domain, totalDuration, parseDuration, processedCount, data.Page, data.Size)

	return nil
}

// FofaAccountInfo fofa账号信息
func FofaAccountInfo(ctx context.Context, rsp *corePb.FofaAccountResponse) error {
	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	fofaUrl := HttpUrlByFofa + "/api/v1/info/my"
	headers := make(map[string]interface{})
	params := make(map[string]interface{})
	params["email"] = fofaConfig.FofaEmail
	params["key"] = fofaConfig.FofaKey
	if params["key"] == "" {
		params["key"] = "bf96e714021457f6808b2aed62a208aa"
	}
	response, err := CurlClient(ctx, fofaUrl, headers, params, false)
	if err != nil {
		return err
	}
	// 解析获取值
	var hData hostData
	err = json.Unmarshal([]byte(response.GetContents()), &hData)
	if err != nil {
		return err
	}

	if hData.Error {
		log.WithContextInfof(ctx, "Received Core.FofaAccountInfo response: %v", response)
		if hData.Errmsg == serverErrorMsg {
			time.Sleep(time.Duration(3) * time.Second)
			response, err = CurlClient(ctx, fofaUrl, headers, params, false)
			if err != nil {
				return err
			}
			err = json.Unmarshal([]byte(response.GetContents()), &rsp)
			if err != nil {
				return err
			}
			if hData.Error {
				log.WithContextInfof(ctx, "Received Core.FofaAccountInfo try begin response: %v", response)
				rsp.Error = true
				return nil
			}
		} else {
			rsp.Error = true
			return nil
		}
	} else {
		err = json.Unmarshal([]byte(response.GetContents()), &rsp)
		if err != nil {
			return err
		}
	}
	return nil
}

// CreateDetectionTask 创建存活探测任务
func CreateDetectionTask(ctx context.Context, req *corePb.FofaDetectionRequest, rsp *corePb.FofaDetectionResponse) error {
	if len(req.Targets) == 0 {
		return errors.New("探测目标不能为空")
	}
	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	fofaUrl := HttpUrlByFofa + fmt.Sprintf("/api/v1/scan_task/task/asset_update?email=%s&key=%s", fofaConfig.FofaEmail, fofaConfig.FofaKey)
	headers := map[string]any{"Content-Type": "application/json"}
	params := map[string]any{"targets": strings.Join(req.Targets, "\n")}
	// 发送请求
	response, err := CurlClient(ctx, fofaUrl, headers, params, true)
	if err != nil {
		return err
	}
	// 解析返回值
	err = json.Unmarshal([]byte(response.GetContents()), rsp)
	if err != nil {
		return err
	}
	return nil
}

// CreateScanTask 创建扫描任务
func CreateScanTask(ctx context.Context, req *corePb.FofaScanTaskRequest, rsp *corePb.FofaDetectionResponse) error {
	if len(req.Targets) == 0 {
		return errors.New("探测目标不能为空")
	}
	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	fofaUrl := HttpUrlByFofa + fmt.Sprintf("/api/v1/scan_task/task/create?email=%s&key=%s", fofaConfig.FofaEmail, fofaConfig.FofaKey)
	headers := map[string]any{"Content-Type": "application/json"}
	target, targetErr := json.Marshal(req.Targets)
	if targetErr != nil {
		return targetErr
	}
	params := map[string]any{"targets": string(target)}
	// 发送请求
	response, err := CurlClient(ctx, fofaUrl, headers, params, true)
	if err != nil {
		return err
	}
	// 解析返回值
	err = json.Unmarshal([]byte(response.GetContents()), rsp)
	if err != nil {
		return err
	}
	return nil
}

// GetTaskStatus 获取任务状态
func GetTaskStatus(ctx context.Context, req *corePb.FofaTaskStatusRequest, rsp *corePb.FofaTaskStatusResponse) error {
	if req.Id == "" {
		return errors.New("任务ID不能为空")
	}
	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	fofaUrl := HttpUrlByFofa + "/api/v1/scan_task/task/status"
	headers := map[string]any{"Content-Type": "application/json"}
	params := map[string]any{"email": fofaConfig.FofaEmail, "key": fofaConfig.FofaKey, "id": req.Id}
	// 发送请求
	response, err := CurlClient(ctx, fofaUrl, headers, params, false)
	if err != nil {
		return err
	}
	// 解析返回值
	err = json.Unmarshal([]byte(response.GetContents()), rsp)
	if err != nil {
		return err
	}
	return nil
}

// GetTaskResult 获取任务结果
func GetTaskResult(ctx context.Context, req *corePb.FofaTaskStatusRequest, rsp *corePb.FofaTaskResultResponse) error {
	if req.Id == "" {
		return errors.New("任务ID不能为空")
	}
	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	fofaUrl := HttpUrlByFofa + "/api/v1/scan_task/task/result"
	headers := map[string]any{"Content-Type": "application/json"}
	params := map[string]any{"email": fofaConfig.FofaEmail, "key": fofaConfig.FofaKey, "id": req.Id}
	// 发送请求
	response, err := CurlClient(ctx, fofaUrl, headers, params, false)
	if err != nil {
		return err
	}
	if !json.Valid([]byte(response.GetContents())) {
		rsp.Result = []byte(response.GetContents())
	} else {
		re := make(map[string]any, 0)
		jsonErr := json.Unmarshal([]byte(response.GetContents()), &re)
		if jsonErr != nil {
			return jsonErr
		}
		if cast.ToInt(re["code"]) != 0 {
			return errors.New(cast.ToString(re["message"]))
		}
	}
	return nil
}

// CurlTextClient 发送纯文本格式的POST请求
func CurlTextClient(ctx context.Context, url string, headers map[string]any, body string) (goz.ResponseBody, error) {
	// 创建 goz 客户端
	cli := goz.NewClient()

	// 设置请求选项
	options := goz.Options{
		Headers: headers,
		JSON:    body, // 使用 JSON 字段传递纯文本（假设 goz 支持）
	}
	// 发送POST请求
	resp, err := cli.Post(url, options)
	if err != nil {
		log.WithContextInfof(ctx, "批量下发给fofa去更新相关的域名数据的扫描任务-fofa接口post请求body异常:%v", err)
		return nil, fmt.Errorf("批量下发给fofa去更新相关的域名数据的扫描任务-POST请求失败: %v", err)
	}

	// 获取响应体
	content, err := resp.GetBody()
	if err != nil {
		log.WithContextInfof(ctx, "批量下发给fofa去更新相关的域名数据的扫描任务-fofa接口post请求body异常:%v", err)
		return nil, fmt.Errorf("批量下发给fofa去更新相关的域名数据的扫描任务-获取响应体失败: %v", err)
	}
	return content, nil
}

// CreateDomainUpdateTask 批量下发给fofa去更新相关的域名数据的扫描任务
func CreateDomainUpdateTask(ctx context.Context, req *corePb.FofaDomainTaskRequest, rsp *corePb.FofaDomainTaskResponse) error {
	// 检查域名列表是否为空
	if len(req.Domains) == 0 {
		return errors.New("域名探测目标不能为空")
	}

	// 获取fofa配置
	fofaConfig := getFofaConfig(ctx)
	fofaUrl := HttpUrlByFofa + fmt.Sprintf("/api/v1/import/hosts?force=true&temp=true&email=%s&key=%s", fofaConfig.FofaEmail, fofaConfig.FofaKey)

	// 设置请求头
	headers := map[string]any{"Content-Type": "text/plain"}

	// 将域名列表拼接为纯文本格式
	body := strings.Join(req.Domains, "\n")

	// 使用新的 CurlTextClient 发送请求
	response, err := CurlTextClient(ctx, fofaUrl, headers, body)
	if err != nil {
		return fmt.Errorf("请求FOFA接口失败: %v", err)
	}
	// 解析返回值
	err = json.Unmarshal([]byte(response.GetContents()), rsp)
	if err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}
	return nil
}
