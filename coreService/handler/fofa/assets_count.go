package fofa

import (
	"context"
	"encoding/json"
	"errors"
	"math/rand"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/fofa_count"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

const (
	acceptHeader = "application/json"
	userAgent    = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:98.0) Gecko/20100101 Firefox/98.0"

	queryURL            = HttpUrlByFofa + "/api/v1/search/all"
	queryDomainNotEmpty = `domain!=""`
	queryIconNotEmpty   = `icon_hash!=""`
	queryCertNotEmpty   = `cert!=""`
	queryICPNotEmpty    = `icp!=""`
	queryOrgNotEmpty    = `org!=""`
)

type _simpleFofaQuery struct {
	Mode   string `json:"mode"`
	Error  bool   `json:"error"`
	ErrMsg string `json:"errmsg"`
	Page   int    `json:"page"`
	Size   int    `json:"size"`
}

func assetsCount(query string) (int64, error) {
	return assetsCountWithRetry(query, 3)
}

func assetsCountWithRetry(query string, maxRetries int) (int64, error) {
	fofaList := cfg.LoadFofa()

	var lastErr error
	for attempt := 0; attempt < maxRetries; attempt++ {
		cfgInfo := fofaList[rand.Intn(len(fofaList))]

		params := make(map[string]string, 6)
		params["page"] = "1"
		params["size"] = "1"
		params["full"] = "false"
		params["fields"] = "ip"
		params["qbase64"] = utils.QBase64(query)
		params["key"] = cfgInfo.FofaKey
		params["email"] = cfgInfo.FofaEmail

		// 设置30秒超时，避免单个请求耗时过长
		resp, err := resty.New().SetTimeout(30*time.Second).R().
			SetQueryParams(params).SetHeader("Accept", acceptHeader).SetHeader("User-Agent", userAgent).
			EnableTrace().Get(queryURL)
		if err != nil {
			lastErr = err
			if attempt < maxRetries-1 {
				time.Sleep(time.Duration(attempt+1) * time.Second) // 递增延迟
				continue
			}
			return 0, err
		}

		data := _simpleFofaQuery{}
		err = json.Unmarshal(resp.Body(), &data)
		if err != nil || data.Error {
			lastErr = err
			if attempt < maxRetries-1 {
				time.Sleep(time.Duration(attempt+1) * time.Second)
				continue
			}
			return 0, err
		}

		return int64(data.Size), nil
	}

	return 0, lastErr
}

type assetsTotal struct {
	Domain int64
	Icon   int64
	Cert   int64
	ICP    int64
	Org    int64
}

// allAssetsCountRobust 更健壮的资产统计函数，不依赖外部上下文
func allAssetsCountRobust() (at assetsTotal, err error) {
	var query = []string{
		queryDomainNotEmpty,
		queryIconNotEmpty,
		queryCertNotEmpty,
		queryICPNotEmpty,
		queryOrgNotEmpty,
	}
	var mux sync.Mutex
	var wg sync.WaitGroup

	// 创建独立的上下文，不依赖外部上下文
	ctx, cancel := context.WithTimeout(context.Background(), 150*time.Second)
	defer cancel()

	log.Infof("开始并发查询 FOFA 资产统计，查询数量: %d", len(query))

	for _, v := range query {
		wg.Add(1)
		go func(q string) {
			defer wg.Done()

			// 检查上下文是否已取消
			select {
			case <-ctx.Done():
				log.Errorf("查询被取消: %s, 原因: %v", q, ctx.Err())
				return
			default:
			}

			startTime := time.Now()
			count, err := assetsCount(q)
			elapsed := time.Since(startTime)

			if err != nil {
				log.Errorf("FOFA 查询失败, query:%s, 耗时:%v, error: %+v", q, elapsed, err)
				return
			}

			log.Infof("FOFA 查询成功, query:%s, 耗时:%v, 结果:%d", q, elapsed, count)

			mux.Lock()
			defer mux.Unlock()
			switch q {
			case queryDomainNotEmpty:
				at.Domain = count
			case queryIconNotEmpty:
				at.Icon = count
			case queryCertNotEmpty:
				at.Cert = count
			case queryICPNotEmpty:
				at.ICP = count
			case queryOrgNotEmpty:
				at.Org = count
			}
		}(v)
	}

	// 等待所有查询完成或超时
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Infof("所有 FOFA 查询完成，结果: %+v", at)
		return at, nil
	case <-ctx.Done():
		log.Errorf("FOFA 查询超时: %v", ctx.Err())
		return at, ctx.Err()
	}
}

func allAssetsCount(ctx context.Context) (at assetsTotal, err error) {

	// 检查传入的上下文状态
	select {
	case <-ctx.Done():
		log.WithContextWarnf(ctx, "传入的上下文已取消，使用独立上下文查询: %v", ctx.Err())
		return allAssetsCountRobust()
	default:
		log.WithContextInfof(ctx, "使用传入的上下文查询 FOFA")
		return allAssetsCountRobust()
	}
}

func fofaAssetsCount(ctx context.Context) (list []fofa_count.FofaCount, err error) {
	currentMonth := time.Now().Format("2006-01")

	client := fofa_count.NewFofaCountModel(mysql.GetInstance())
	info, err := client.First(currentMonth)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.WithContextErrorf(ctx, "[公共线索库]获取fofa资产数据统计%+v", err)
	}
	if info.UpdateTime != currentMonth {
		// get fofa count, and update mysql table
		fofaCount, countErr := allAssetsCount(ctx)
		if countErr != nil {
			return nil, err
		}

		var insertItem fofa_count.FofaCount
		insertItem.UpdateTime = currentMonth
		insertItem.Cert = fofaCount.Cert
		insertItem.Icp = fofaCount.ICP
		insertItem.Icon = fofaCount.Icon
		insertItem.Domain = fofaCount.Domain
		insertItem.Org = fofaCount.Org
		err = client.Add(insertItem)
		if err != nil {
			return nil, err
		}
	}

	list, err = client.Find(15)
	return list, err
}
func GetStartOfWeek(t time.Time, weeksAgo int) time.Time {
	weekDay := int(t.Weekday())
	if weekDay == 0 {
		weekDay = 7
	}
	days := -(weekDay - 1) - (7 * weeksAgo)
	monday := t.AddDate(0, 0, days)

	return time.Date(monday.Year(), monday.Month(), monday.Day(), 0, 0, 0, 0, t.Location())
}

const FofaAllAssetsCountRedisKey = "foradar:core:fofa:all_assets_count:weekly"

// fofaCountWeeklyOptimized 优化版本的周统计函数，解决超时问题
func fofaCountWeeklyOptimized(ctx context.Context, rsp *pb.FofaAllAssetsCountResponse) error {
	lastWeekKey := GetStartOfWeek(time.Now(), 1).Format(utils.DateLayout)
	nowWeekKey := GetStartOfWeek(time.Now(), 0).Format(utils.DateLayout)

	var needRecord bool
	var nowWeek, lastWeek assetsTotal

	// 1. 优先从 Redis 获取数据，设置短超时
	redisCtx, redisCancel := context.WithTimeout(ctx, 3*time.Second)
	defer redisCancel()

	nowData := redis.GetClient().HGet(redisCtx, FofaAllAssetsCountRedisKey, nowWeekKey).Val()
	if nowData == "" {
		// 2. Redis 中没有数据，需要查询 FOFA
		log.WithContextInfof(ctx, "Redis 中没有本周数据，开始查询 FOFA，key: %s", nowWeekKey)

		var countErr error
		nowWeek, countErr = allAssetsCount(ctx)
		if countErr != nil {
			log.WithContextErrorf(ctx, "查询 FOFA 失败，nowWeekKey:%s, %v", nowWeekKey, countErr)
			return errx.ErrFrontEndGetDataFailed
		}
		needRecord = true
		log.WithContextInfof(ctx, "FOFA 查询成功，数据: %+v", nowWeek)
	} else {
		if err := sonic.Unmarshal([]byte(nowData), &nowWeek); err != nil {
			log.WithContextErrorf(ctx, "反序列化本周数据失败: %v", err)
			// 如果反序列化失败，重新查询
			return fofaCountWeeklyOptimized(ctx, rsp)
		}
		log.WithContextInfof(ctx, "从 Redis 获取本周数据成功")
	}

	// 3. 获取上周数据
	redisCtx2, redisCancel2 := context.WithTimeout(ctx, 3*time.Second)
	defer redisCancel2()

	lastData := redis.GetClient().HGet(redisCtx2, FofaAllAssetsCountRedisKey, lastWeekKey).Val()
	if lastData != "" {
		if err := sonic.Unmarshal([]byte(lastData), &lastWeek); err != nil {
			log.WithContextErrorf(ctx, "反序列化上周数据失败: %v", err)
			lastWeek = nowWeek // 使用本周数据作为默认值
		}
	} else {
		lastWeek = nowWeek // 如果没有上周数据，使用本周数据
		log.WithContextInfof(ctx, "没有上周数据，使用本周数据作为基准")
	}

	// 4. 计算各项指标
	rsp.IconMom = utils.MonthOnMonth(nowWeek.Icon, lastWeek.Icon)
	rsp.DomainMom = utils.MonthOnMonth(nowWeek.Domain, lastWeek.Domain)
	rsp.CertMom = utils.MonthOnMonth(nowWeek.Cert, lastWeek.Cert)
	rsp.IcpMom = utils.MonthOnMonth(nowWeek.ICP, lastWeek.ICP)
	rsp.OrgMom = utils.MonthOnMonth(nowWeek.Org, lastWeek.Org)

	rsp.IconChange = nowWeek.Icon - lastWeek.Icon
	rsp.DomainChange = nowWeek.Domain - lastWeek.Domain
	rsp.CertChange = nowWeek.Cert - lastWeek.Cert
	rsp.IcpChange = nowWeek.ICP - lastWeek.ICP
	rsp.OrgChange = nowWeek.Org - lastWeek.Org

	rsp.DomainCount = nowWeek.Domain
	rsp.IconCount = nowWeek.Icon
	rsp.CertCount = nowWeek.Cert
	rsp.IcpCount = nowWeek.ICP
	rsp.OrgCount = nowWeek.Org

	nowAll := nowWeek.Icon + nowWeek.Domain + nowWeek.Cert + nowWeek.ICP
	lastAll := lastWeek.Icon + lastWeek.Domain + lastWeek.Cert + lastWeek.ICP
	rsp.AllCount = nowAll
	rsp.AllMom = utils.MonthOnMonth(nowAll, lastAll)
	rsp.AllChange = rsp.IconChange + rsp.DomainChange + rsp.CertChange + rsp.IcpChange

	// 5. 异步保存到 Redis，不阻塞响应
	if needRecord {
		go saveToRedisAsync(nowWeek, nowWeekKey)
	}

	return nil
}

// saveToRedisAsync 异步保存数据到 Redis
func saveToRedisAsync(data assetsTotal, key string) {
	// 创建独立的上下文，不依赖请求上下文
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	jsonData, err := sonic.Marshal(data)
	if err != nil {
		log.WithContextErrorf(ctx, "序列化数据失败，key:%s, error:%v", key, err)
		return
	}

	// 重试机制
	maxRetries := 3
	for attempt := 0; attempt < maxRetries; attempt++ {
		err = redis.GetClient().HSet(ctx, FofaAllAssetsCountRedisKey, key, string(jsonData)).Err()
		if err == nil {
			log.WithContextInfof(ctx, "异步保存到 Redis 成功，key:%s", key)
			return
		}

		log.WithContextErrorf(ctx, "保存到 Redis 失败，尝试 %d/%d，key:%s, error:%v",
			attempt+1, maxRetries, key, err)

		if attempt < maxRetries-1 {
			time.Sleep(time.Duration(attempt+1) * time.Second)
		}
	}

	log.WithContextErrorf(ctx, "异步保存到 Redis 最终失败，key:%s, error:%v", key, err)
}

func fofaCountWeekly(ctx context.Context, rsp *pb.FofaAllAssetsCountResponse) error {
	lastWeekKey := GetStartOfWeek(time.Now(), 1).Format(utils.DateLayout)
	nowWeekKey := GetStartOfWeek(time.Now(), 0).Format(utils.DateLayout)

	var needRecord bool
	var nowWeek, lastWeek assetsTotal

	// 为 Redis 操作设置较短的超时时间
	redisCtx, redisCancel := context.WithTimeout(ctx, 5*time.Second)
	defer redisCancel()

	nowData := redis.GetClient().HGet(redisCtx, FofaAllAssetsCountRedisKey, nowWeekKey).Val()
	if nowData == "" {
		var countErr error
		nowWeek, countErr = allAssetsCount(ctx)
		if countErr != nil {
			log.WithContextErrorf(ctx, "redis没有查fofa失败,nowWeekKey:%s, %v\n", nowWeekKey, countErr)
			return errx.ErrFrontEndGetDataFailed
		}
		needRecord = true
	} else {
		sonic.Unmarshal([]byte(nowData), &nowWeek)
	}

	// 为获取上周数据创建新的 Redis 上下文
	redisCtx2, redisCancel2 := context.WithTimeout(ctx, 5*time.Second)
	defer redisCancel2()

	lastData := redis.GetClient().HGet(redisCtx2, FofaAllAssetsCountRedisKey, lastWeekKey).Val()
	sonic.Unmarshal([]byte(lastData), &lastWeek)
	if lastWeek.Domain == 0 {
		lastWeek = nowWeek
	}

	rsp.IconMom = utils.MonthOnMonth(nowWeek.Icon, lastWeek.Icon)
	rsp.DomainMom = utils.MonthOnMonth(nowWeek.Domain, lastWeek.Domain)
	rsp.CertMom = utils.MonthOnMonth(nowWeek.Cert, lastWeek.Cert)
	rsp.IcpMom = utils.MonthOnMonth(nowWeek.ICP, lastWeek.ICP)
	rsp.OrgMom = utils.MonthOnMonth(nowWeek.Org, lastWeek.Org)

	rsp.IconChange = nowWeek.Icon - lastWeek.Icon
	rsp.DomainChange = nowWeek.Domain - lastWeek.Domain
	rsp.CertChange = nowWeek.Cert - lastWeek.Cert
	rsp.IcpChange = nowWeek.ICP - lastWeek.ICP  // 修复这里的错误：应该是 nowWeek.ICP 而不是 nowWeek.Icon
	rsp.OrgChange = nowWeek.Org - lastWeek.Org

	rsp.DomainCount = nowWeek.Domain
	rsp.IconCount = nowWeek.Icon
	rsp.CertCount = nowWeek.Cert
	rsp.IcpCount = nowWeek.ICP
	rsp.OrgCount = nowWeek.Org

	nowALl := nowWeek.Icon + nowWeek.Domain + nowWeek.Cert + nowWeek.ICP      //+ nowWeek.Org
	lastALl := lastWeek.Icon + lastWeek.Domain + lastWeek.Cert + lastWeek.ICP //+ lastWeek.Org
	rsp.AllCount = nowALl
	rsp.AllMom = utils.MonthOnMonth(nowALl, lastALl)
	rsp.AllChange = rsp.IconChange + rsp.DomainChange + rsp.CertChange + rsp.IcpChange //+ rsp.OrgChange

	// 异步保存到 Redis，避免阻塞响应
	if needRecord {
		go func() {
			// 创建独立的上下文用于异步操作
			asyncCtx, asyncCancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer asyncCancel()

			t, err := sonic.Marshal(nowWeek)
			if err != nil {
				log.WithContextErrorf(asyncCtx, "fofaCountWeekly sonic marshal failed,nowWeekKey:%s, %v\n", nowWeekKey, err)
				return
			}

			err = redis.GetClient().HSet(asyncCtx, FofaAllAssetsCountRedisKey, nowWeekKey, string(t)).Err()
			if err != nil {
				log.WithContextErrorf(asyncCtx, "fofaCountWeekly save to redis failed,nowWeekKey:%s, %v\n", nowWeekKey, err)
			} else {
				log.WithContextInfof(asyncCtx, "fofaCountWeekly save to redis success,nowWeekKey:%s\n", nowWeekKey)
			}
		}()
	}
	return nil
}

func FofaAllAssetsCount(ctx context.Context, req *pb.FofaAllAssetsCountRequest, rsp *pb.FofaAllAssetsCountResponse) error {
	// 处理周变更,fofa_count表的数据哪里来的，周数据怎么存或者现查？ todo wufj
	if req.QueryMode > 3 {
		return errors.New("不支持的查询模式")
	}
	// 周变更，从redis中取，若取不到就去查，redis更新的日期没有本周的或上周的也去查
	if req.QueryMode == 1 {
		return fofaCountWeeklyOptimized(ctx, rsp)  // 使用优化版本解决超时问题
	}
	list, err := fofaAssetsCount(ctx)
	if err != nil {
		return err
	}

	month := time.Now().Format(utils.DateMonthLayout)
	lastMonth := time.Now().AddDate(0, -1, 0).Format(utils.DateMonthLayout)
	quarterMonth := utils.GetQuarterStartDate(time.Now()).Format(utils.DateMonthLayout)
	lastQuarterMonth := utils.GetLastQuarterStartDate(time.Now()).Format(utils.DateMonthLayout)

	var monthRecord, lastMonthRecord, quarterMonthRecord, lastQuarterMonthRecord fofa_count.FofaCount
	var count int = 0
	i := len(list) - 1
	if len(list) >= 12 {
		i = 11
	}
	for ; i >= 0; i-- {
		var pbTime = list[i].UpdateTime
		if pbTime == "" {
			continue
		}
		rsp.Icon = append(rsp.Icon, &pb.FofaCount{Count: list[i].Icon, Time: pbTime})
		rsp.Domain = append(rsp.Domain, &pb.FofaCount{Count: list[i].Domain, Time: pbTime})
		rsp.Cert = append(rsp.Cert, &pb.FofaCount{Count: list[i].Cert, Time: pbTime})
		rsp.Icp = append(rsp.Icp, &pb.FofaCount{Count: list[i].Icp, Time: pbTime})
		rsp.Org = append(rsp.Org, &pb.FofaCount{Count: list[i].Org, Time: pbTime})

		if pbTime == month {
			monthRecord = list[i]
		}
		if pbTime == lastMonth {
			lastMonthRecord = list[i]
		}
		if pbTime == quarterMonth {
			quarterMonthRecord = list[i]
		}
		if pbTime == lastQuarterMonth {
			lastQuarterMonthRecord = list[i]
		}
		count++
	}

	switch req.QueryMode {
	case 0, 2:
		rsp.IconMom = utils.MonthOnMonth(monthRecord.Icon, lastMonthRecord.Icon)
		rsp.DomainMom = utils.MonthOnMonth(monthRecord.Domain, lastMonthRecord.Domain)
		rsp.CertMom = utils.MonthOnMonth(monthRecord.Cert, lastMonthRecord.Cert)
		rsp.IcpMom = utils.MonthOnMonth(monthRecord.Icp, lastMonthRecord.Icp)
		rsp.OrgMom = utils.MonthOnMonth(monthRecord.Org, lastMonthRecord.Org)

		rsp.IconChange = monthRecord.Icon - lastMonthRecord.Icon
		rsp.DomainChange = monthRecord.Domain - lastMonthRecord.Domain
		rsp.CertChange = monthRecord.Cert - lastMonthRecord.Cert
		rsp.IcpChange = monthRecord.Icp - lastMonthRecord.Icp
		rsp.OrgChange = monthRecord.Org - lastMonthRecord.Org

		rsp.DomainCount = monthRecord.Domain
		rsp.IconCount = monthRecord.Icon
		rsp.CertCount = monthRecord.Cert
		rsp.IcpCount = monthRecord.Icp
		rsp.OrgCount = monthRecord.Org

		monthALl := monthRecord.Icon + monthRecord.Domain + monthRecord.Cert + monthRecord.Icp                //+ monthRecord.Org
		lastALl := lastMonthRecord.Icon + lastMonthRecord.Domain + lastMonthRecord.Cert + lastMonthRecord.Icp //+ lastMonthRecord.Org
		rsp.AllCount = monthALl
		rsp.AllMom = utils.MonthOnMonth(monthALl, lastALl)
		rsp.AllChange = rsp.IconChange + rsp.DomainChange + rsp.CertChange + rsp.IcpChange // + rsp.OrgChange

	//季度
	case 3:
		rsp.IconMom = utils.MonthOnMonth(quarterMonthRecord.Icon, lastQuarterMonthRecord.Icon)
		rsp.DomainMom = utils.MonthOnMonth(quarterMonthRecord.Domain, lastQuarterMonthRecord.Domain)
		rsp.CertMom = utils.MonthOnMonth(quarterMonthRecord.Cert, lastQuarterMonthRecord.Cert)
		rsp.IcpMom = utils.MonthOnMonth(quarterMonthRecord.Icp, lastQuarterMonthRecord.Icp)
		rsp.OrgMom = utils.MonthOnMonth(quarterMonthRecord.Org, lastQuarterMonthRecord.Org)

		rsp.IconChange = quarterMonthRecord.Icon - lastQuarterMonthRecord.Icon
		rsp.DomainChange = quarterMonthRecord.Domain - lastQuarterMonthRecord.Domain
		rsp.CertChange = quarterMonthRecord.Cert - lastQuarterMonthRecord.Cert
		rsp.IcpChange = quarterMonthRecord.Icp - lastQuarterMonthRecord.Icp
		rsp.OrgChange = quarterMonthRecord.Org - lastQuarterMonthRecord.Org

		rsp.DomainCount = quarterMonthRecord.Domain
		rsp.IconCount = quarterMonthRecord.Icon
		rsp.CertCount = quarterMonthRecord.Cert
		rsp.IcpCount = quarterMonthRecord.Icp
		rsp.OrgCount = quarterMonthRecord.Org

		monthALl := quarterMonthRecord.Icon + quarterMonthRecord.Domain + quarterMonthRecord.Cert + quarterMonthRecord.Icp                //+ monthRecord.Org
		lastALl := lastQuarterMonthRecord.Icon + lastQuarterMonthRecord.Domain + lastQuarterMonthRecord.Cert + lastQuarterMonthRecord.Icp // + threeLastMonthRecord.Org
		rsp.AllCount = monthALl
		rsp.AllMom = utils.MonthOnMonth(monthALl, lastALl)
		rsp.AllChange = rsp.IconChange + rsp.DomainChange + rsp.CertChange + rsp.IcpChange //+ rsp.OrgChange
	}
	if req.QueryMode == 2 || req.QueryMode == 3 {
		rsp.Icon = []*pb.FofaCount{}
		rsp.Domain = []*pb.FofaCount{}
		rsp.Cert = []*pb.FofaCount{}
		rsp.Icp = []*pb.FofaCount{}
		rsp.Org = []*pb.FofaCount{}
	}

	return nil
}

// FofaAssetsCountForCron 设定每周更新一次
func FofaAssetsCountForCron(ctx context.Context) error {
	currentMonth := time.Now().Format("2006-01")

	client := fofa_count.NewFofaCountModel(mysql.GetInstance())
	info, err := client.First(currentMonth)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.WithContextErrorf(ctx, "[公共线索库]获取fofa资产数据统计: %+v", err)
		return err
	}

	fofaCount, countErr := allAssetsCount(ctx)
	if countErr != nil {
		return err
	}

	info.Cert = fofaCount.Cert
	info.Icp = fofaCount.ICP
	info.Icon = fofaCount.Icon
	info.Domain = fofaCount.Domain
	info.Org = fofaCount.Org

	if info.UpdateTime != currentMonth {
		// insert new line
		err = client.Add(info)
	} else {
		// update current month count
		err = client.Update(info)
	}

	if err != nil {
		return err
	}
	return nil
}
