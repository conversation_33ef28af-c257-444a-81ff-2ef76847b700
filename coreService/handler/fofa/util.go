package fofa

import (
	"fmt"
	"regexp"

	"micro-service/pkg/utils"
)

// match format: 2006-01-02
var reg = regexp.MustCompile(`^\d{4}-(0?[1-9]|1[012])-(0?[1-9]|[12]\d|3[01])$`)

const (
	before = iota + 1
	after
)

func joinQueryRange(origin, date string, flag int) (string, error) {
	if date == "" {
		return origin, nil
	}

	if !reg.MatchString(date) {
		return "", fmt.Errorf("invalid date format: %s", date)
	}

	var joinRange string
	and := utils.If(len(origin) > 0, " &&", "")
	switch flag {
	case before:
		joinRange = fmt.Sprintf(`%s before="%s"`, and, date)
	case after:
		joinRange = fmt.Sprintf(`%s after="%s"`, and, date)
	}
	return origin + joinRange, nil
}
