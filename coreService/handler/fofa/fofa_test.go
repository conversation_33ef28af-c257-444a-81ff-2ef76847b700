package fofa

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	microZap "github.com/go-micro/plugins/v4/logger/zap"
	"github.com/hashicorp/go-hclog"
	"github.com/joho/godotenv"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"

	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/stretchr/testify/assert"

	pb "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
)

func initCfg() {
	_ = godotenv.Load("../../../.env")
	cfg.InitLoadCfg()

	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

func Test_FofaQueryA(t *testing.T) {
	initCfg()
	cfg.InitLoadCfg()

	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	rsp := pb.FofaQueryResponse{}

	err := FofaQuery(context.TODO(), &rsp, &pb.FofaQueryRequest{Qbase64: `domain="avicnet.cn"`, Field: []string{"ip", "product", "country", "region", "longitude", "latitude"}, Page: 1, Size: 10})
	if err != nil {
		panic(err)
	}
	for i := range rsp.Sdata {
		println(fmt.Sprintf("%+v", rsp.Sdata[i]))
	}
	return
}

func Test_FofaDetectionTask(t *testing.T) {
	initCfg()
	rsp := pb.FofaDetectionResponse{}
	err := CreateDetectionTask(context.TODO(), &pb.FofaDetectionRequest{Targets: []string{"*************:443"}}, &rsp)
	if err != nil {
		panic(err)
	} else {
		fmt.Printf(fmt.Sprintf("task_id:%+v\n", rsp.Data.Id))
	}
}

func Test_FofaScanTask(t *testing.T) {
	initCfg()
	rsp := pb.FofaDetectionResponse{}
	err := CreateScanTask(context.TODO(), &pb.FofaScanTaskRequest{Targets: []*pb.FofaScanTarget{
		{
			Ip:    []string{"************", "*************", "**********", "************", "************"},
			Ports: "80,8080,443,9200,8500,3306,22,5200",
		},
	}}, &rsp)
	if err != nil {
		panic(err)
	} else {
		fmt.Printf(fmt.Sprintf("task_id:%+v\n", rsp.Data.Id))
	}
}

func Test_GetTaskStatus(t *testing.T) {
	initCfg()
	str := `{"asn":{"as_number":37963,"as_organization":"Hangzhou Alibaba Advertising Co.,Ltd."},"asn_category":[{"cat":29,"sub_cats":[30]},{"cat":1,"sub_cats":[4]}],"ban_len":316,"banner":"HTTP/1.1 200 OK\r\nServer: openresty/********\r\nDate: Sat, 28 Oct 2023 09:46:59 GMT\r\nContent-Type: text/html\r\nContent-Length: 99528\r\nLast-Modified: Tue, 07 Mar 2023 10:11:53 GMT\r\nConnection: keep-alive\r\nVary: Accept-Encoding\r\nETag: \"64070de9-184c8\"\r\nCache-Control: no-cache\r\nCache-Control: private\r\nAccept-Ranges: bytes","base_protocol":"tcp","geoip":{"city_name":"Beijing","continent_code":"AS","country_code2":"CN","country_code3":"CN","country_name":"China","latitude":39.9143,"location":{"lat":39.9143,"lon":116.3861},"longitude":116.3861,"real_region_name":"Beijing","region_name":"BJ","timezone":"Asia/Shanghai"},"geoip2":{"city":"北京","city_code":"110000","continent_code":"AP","country":"中国","country_code2":"CN","location":{"lat":39.904989,"lon":116.405285},"phone_prefix":"86","province":"北京","timezone":"Asia/Shanghai","timezone2":"UTC+8"},"geoip5":{"country_name":"China","country_code2":"CN","real_region_name":"Zhejiang","city_name":"Hangzhou","latitude":30.293649673461914,"longitude":120.16158294677734},"ip":"**********","ipcnet":"*********","is_ipv6":false,"lastchecktime":"2023-10-28 17:47:01","lastupdatetime":"2023-10-28 17:47:01","layer3":[{"id":217,"cid":161,"pid":153}],"port":9000,"protocol":"http","server":"openresty/********","v":7}`
	info := make(map[string]any, 0)
	json.Unmarshal([]byte(str), &info)
	if layer, ok := info["layer3"]; ok {
		an := layer.([]any)
		for i := range an {
			println(fmt.Sprintf("%+v", an[i].(map[string]any)["cid"]))
		}

	}
	//rsp := pb.FofaTaskStatusResponse{}
	//err := GetTaskStatus(context.TODO(), &pb.FofaTaskStatusRequest{Id: "30f8a1e4-bd3f-498f-a82d-b05366da0a88"}, &rsp)
	//if err != nil {
	//	panic(err)
	//} else {
	//	fmt.Printf(fmt.Sprintf("%+v\n", rsp))
	//}
}

func Test_GetTaskResult(t *testing.T) {
	initCfg()
	rsp := pb.FofaTaskResultResponse{}
	err := GetTaskResult(context.TODO(), &pb.FofaTaskStatusRequest{Id: "81efc179-00d2-4340-9f23-29bfdea35001"}, &rsp)
	if err != nil {
		panic(err)
	} else {
		fmt.Printf(fmt.Sprintf("%+v", rsp))
	}
}

func Test_FofaQuery(t *testing.T) {
	initCfg()

	var err error
	q := `domain="dell.com"`
	req := &pb.FofaQueryRequest{
		Qbase64: q,
		Page:    1,
		Size:    0,
	}
	total := utils.Page(10000, 500)
	for i := 1; i <= total; i++ {
		var rsp = new(pb.FofaQueryResponse)
		req.Page = uint32(i)
		err = FofaQuery(context.Background(), rsp, req)
		assert.Nil(t, err)
		fmt.Printf("page: %d, got: %d\n", i, len(rsp.Sdata))
		for _, v := range rsp.Sdata {
			fmt.Printf("header: %d, banner:%d \n", len(v.GetHeader()), len(v.GetBanner()))
		}
	}
}

func Test_FofaDns(t *testing.T) {
	initCfg()
	var err error
	rsp := &pb.FofaPureDnsResponse{}
	err = FofaPureDns(context.Background(), rsp, &pb.FofaPureDnsRequest{
		Domain: "fofa.info",
		Page:   1,
		Size:   0,
	})
	if err != nil {
		println(err.Error())
	} else {
		println(fmt.Sprintf("%+v", rsp))
	}
}

func Test_FofaAccountInfo(t *testing.T) {
	initCfg()
	var err error
	rsp := &pb.FofaAccountResponse{}
	err = FofaAccountInfo(context.Background(), rsp)
	if err != nil {
		println(err.Error())
	} else {
		println(fmt.Sprintf("%+v", rsp))
	}
}

func Test_FofaQueryCount(t *testing.T) {
	initCfg()

	rsp := pb.FofaQueryCountResponse{}
	req := pb.FofaQueryCountRequest{
		Qbase64:          "body=\"/wp-content/themes/bricks/\"",
		Full:             true,
		CanGetLimitCount: true,
	}
	if fErr := FofaQueryCount(context.TODO(), &rsp, &req); fErr != nil {
		assert.Nil(t, fErr)
	}
	fmt.Println(rsp.Count)
}

func Test_CreateDomainUpdateTask(t *testing.T) {
	initCfg()
	rsp := pb.FofaDomainTaskResponse{}
	err := CreateDomainUpdateTask(context.TODO(), &pb.FofaDomainTaskRequest{Domains: []string{"fofa.info", "beat.fofa.info"}}, &rsp)
	if err != nil {
		panic(err)
	} else {
		fmt.Println("------")
		fmt.Println(rsp.Message)
		fmt.Println("------11111")
	}
}
