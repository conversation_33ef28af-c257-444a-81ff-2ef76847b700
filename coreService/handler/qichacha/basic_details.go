package qichacha

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql/qcc"
	"micro-service/pkg/cfg"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"gorm.io/gorm"
)

func getBasicDetailsByName(name string, rsp *pb.QCCGetBasicDetailsByNameResponse) {
	reqParams := make(map[string]string, 2)
	reqParams["keyword"] = name

	resp, err := reqBuilderWithGET(reqParams, "/ECIV4/GetBasicDetailsByName")
	if err != nil {
		rsp.Status = "400"
		rsp.Message = gjson.Get(string(resp.Body()), "Message").String()
		return
	}
	body := string(resp.Body())
	rsp.Result = &pb.QCCBasicDetail{
		Name:        gjson.Get(body, "Result.Name").String(),
		KeyNo:       gjson.Get(body, "Result.KeyNo").String(),
		No:          gjson.Get(body, "Result.No").String(),
		BelongOrg:   gjson.Get(body, "Result.BelongOrg").String(),
		OperId:      gjson.Get(body, "Result.OperId").String(),
		OperName:    gjson.Get(body, "Result.OperName").String(),
		StartDate:   gjson.Get(body, "Result.StartDate").String(),
		EndDate:     gjson.Get(body, "Result.EndDate").String(),
		Status:      gjson.Get(body, "Result.Status").String(),
		Province:    gjson.Get(body, "Result.Province").String(),
		UpdatedDate: gjson.Get(body, "Result.UpdatedDate").String(),
		CreditCode:  gjson.Get(body, "Result.CreditCode").String(),
		RegistCapi:  gjson.Get(body, "Result.RegistCapi").String(),
		EconKind:    gjson.Get(body, "Result.EconKind").String(),
		Address:     gjson.Get(body, "Result.Address").String(),
		Scope:       gjson.Get(body, "Result.Scope").String(),
		TermStart:   gjson.Get(body, "Result.TermStart").String(),
		TeamEnd:     gjson.Get(body, "Result.TeamEnd").String(),
		CheckDate:   gjson.Get(body, "Result.CheckDate").String(),
		OrgNo:       gjson.Get(body, "Result.OrgNo").String(),
		IsOnStock:   gjson.Get(body, "Result.IsOnStock").String(),
		StockNumber: gjson.Get(body, "Result.StockNumber").String(),
		ImageUrl:    gjson.Get(body, "Result.ImageUrl").String(),
		EntType:     gjson.Get(body, "Result.EntType").String(),
		RecCap:      gjson.Get(body, "Result.RecCap").String(),
		RevokeInfo: &pb.QCCRevokeInfo{
			CancelDate:   gjson.Get(body, "Result.RevokeInfo.CancelDate").String(),
			CancelReason: gjson.Get(body, "Result.RevokeInfo.CancelReason").String(),
			RevokeDate:   gjson.Get(body, "Result.RevokeInfo.RevokeDate").String(),
			RevokeReason: gjson.Get(body, "Result.RevokeInfo.RevokeReason").String(),
		},
		Area: &pb.QCCArea{
			Province: gjson.Get(body, "Result.Area.Province").String(),
			City:     gjson.Get(body, "Result.Area.City").String(),
			County:   gjson.Get(body, "Result.Area.County").String(),
		},
		AreaCode: gjson.Get(body, "Result.AreaCode").String(),
	}
	rsp.OrderNumber = gjson.Get(body, "OrderNumber").String()
	rsp.Status = gjson.Get(body, "Status").String()
	rsp.Message = gjson.Get(body, "Message").String()
	// 解析历史变更
	_ = json.Unmarshal(
		[]byte(
			strings.Replace(
				strings.Replace(gjson.Get(body, "Result.OriginalName").String(), "Name", "name", -1),
				"ChangeDate", "change_date", -1,
			),
		),
		&rsp.Result.OriginalName,
	)
}

func searchBasicDetails(req *pb.QCCBasicDetailSearchRequest, rsp *pb.QCCBasicDetailSearchResponse) error {
	reqParams := make(map[string]string, 2)
	reqParams["searchKey"] = req.Keyword
	reqParams["provinceCode"] = req.ProvinceCode
	reqParams["cityCode"] = req.CityCode
	reqParams["pageSize"] = cast.ToString(req.PerPage)
	reqParams["pageIndex"] = cast.ToString(req.Page)
	resp, err := reqBuilderWithGET(reqParams, "/FuzzySearch/GetList")
	if err != nil {
		rsp.Message = gjson.Get(string(resp.Body()), "Message").String()
		return errors.New(gjson.Get(string(resp.Body()), "Message").String())
	}
	body := string(resp.Body())
	if gjson.Get(body, "Status").String() != "200" {
		rsp.Message = gjson.Get(string(resp.Body()), "Message").String()
		return errors.New(gjson.Get(string(resp.Body()), "Message").String())
	}
	for _, item := range gjson.Get(body, "Result").Array() {
		rsp.Items = append(rsp.Items, &pb.QCCBasicDetailSearchInfo{
			Name:      item.Get("Name").String(),
			KeyNo:     item.Get("KeyNo").String(),
			No:        item.Get("No").String(),
			OperName:  item.Get("OperName").String(),
			StartDate: item.Get("StartDate").String(),
			Status:    item.Get("Status").String(),
			Address:   item.Get("Address").String(),
		})
	}
	rsp.OrderNumber = gjson.Get(body, "OrderNumber").String()
	rsp.Message = gjson.Get(body, "Message").String()
	return nil
}

func QCCGetBasicDetailsByName(searchName string, force bool, rsp *pb.QCCGetBasicDetailsByNameResponse) error {
	if searchName == "" {
		return errx.ErrSearchCantEmpty
	}
	if !force {
		// 公司名、注册号、统一社会信用代码或KeyNo
		if his, err := qcc.NewQCCBasicDetailHistoryModel().FindByKeyword(searchName, utils.SubDayTime(time.Duration(cfg.LoadQcc().BasicDetailCacheDay))); err == nil {
			// 无查询结果
			if his.Status != "200" {
				rsp.Status = his.Status
				rsp.Message = his.Message
				rsp.OrderNumber = his.OrderNumber
				return nil
			}
			// 有查询结果,查询数据库
			if res, qErr := qcc.NewQCCBasicDetailModel().Search(searchName); qErr != nil {
				rsp.Status = "400"
				rsp.Message = "获取查询数据失败:" + qErr.Error()
				return fmt.Errorf("获取查询数据失败:%s", qErr.Error())
			} else {
				var originalNames []*pb.QCCOriginalName
				var area pb.QCCArea
				var revokeInfo pb.QCCRevokeInfo
				_ = json.Unmarshal([]byte(res.OriginalName), &originalNames)
				_ = json.Unmarshal([]byte(res.Area), &area)
				_ = json.Unmarshal([]byte(res.RevokeInfo), &revokeInfo)
				rsp.Result = &pb.QCCBasicDetail{
					Name:         res.Name,
					KeyNo:        res.KeyNo,
					No:           res.No,
					BelongOrg:    res.BelongOrg,
					OperId:       res.OperId,
					OperName:     res.OperName,
					StartDate:    res.StartDate,
					EndDate:      res.EndDate,
					Status:       res.Status,
					Province:     res.Province,
					UpdatedDate:  res.UpdatedDate,
					CreditCode:   res.CreditCode,
					RegistCapi:   res.RegistCapi,
					EconKind:     res.EconKind,
					Address:      res.Address,
					Scope:        res.Scope,
					TermStart:    res.TermStart,
					TeamEnd:      res.TeamEnd,
					CheckDate:    res.CheckDate,
					OrgNo:        res.OrgNo,
					IsOnStock:    res.IsOnStock,
					StockNumber:  res.StockNumber,
					ImageUrl:     res.ImageUrl,
					EntType:      res.EntType,
					RecCap:       res.RecCap,
					RevokeInfo:   &revokeInfo,
					OriginalName: originalNames,
					Area:         &area,
					AreaCode:     res.AreaCode,
				}
				rsp.Status = his.Status
				rsp.Message = his.Message
				rsp.OrderNumber = his.OrderNumber

				rsp.Status = his.Status
				rsp.Message = his.Message
				rsp.OrderNumber = his.OrderNumber
				return nil
			}
		}
	}
	// 查询QCC数据
	getBasicDetailsByName(searchName, rsp)
	// 不存在记录,添加记录并返回
	his := qcc.NewQCCBasicDetailHistoryModel().UpdateHistory(searchName, rsp)
	if his.Status == "200" {
		search, sErr := qcc.NewQCCBasicDetailModel().Search(searchName)
		if sErr == nil {
			var originalNames []*pb.QCCOriginalName
			var area pb.QCCArea
			var revokeInfo pb.QCCRevokeInfo
			_ = json.Unmarshal([]byte(search.OriginalName), &originalNames)
			_ = json.Unmarshal([]byte(search.Area), &area)
			_ = json.Unmarshal([]byte(search.RevokeInfo), &revokeInfo)
			rsp.Result = &pb.QCCBasicDetail{
				Name:         search.Name,
				KeyNo:        search.KeyNo,
				No:           search.No,
				BelongOrg:    search.BelongOrg,
				OperId:       search.OperId,
				OperName:     search.OperName,
				StartDate:    search.StartDate,
				EndDate:      search.EndDate,
				Status:       search.Status,
				Province:     search.Province,
				UpdatedDate:  search.UpdatedDate,
				CreditCode:   search.CreditCode,
				RegistCapi:   search.RegistCapi,
				EconKind:     search.EconKind,
				Address:      search.Address,
				Scope:        search.Scope,
				TermStart:    search.TermStart,
				TeamEnd:      search.TeamEnd,
				CheckDate:    search.CheckDate,
				OrgNo:        search.OrgNo,
				IsOnStock:    search.IsOnStock,
				StockNumber:  search.StockNumber,
				ImageUrl:     search.ImageUrl,
				EntType:      search.EntType,
				RecCap:       search.RecCap,
				RevokeInfo:   &revokeInfo,
				OriginalName: originalNames,
				Area:         &area,
				AreaCode:     search.AreaCode,
			}
			rsp.Status = his.Status
			rsp.Message = his.Message
			rsp.OrderNumber = his.OrderNumber
			return nil
		} else if errors.Is(sErr, gorm.ErrRecordNotFound) {
			originNameStr, _ := json.Marshal(rsp.Result.OriginalName)
			areaStr, _ := json.Marshal(rsp.Result.Area)
			revokeInfoStr, _ := json.Marshal(rsp.Result.RevokeInfo)
			insertRow := qcc.QCCBasicDetail{
				Name:         rsp.Result.Name,
				KeyNo:        rsp.Result.KeyNo,
				No:           rsp.Result.No,
				BelongOrg:    rsp.Result.BelongOrg,
				OperId:       rsp.Result.OperId,
				OperName:     rsp.Result.OperName,
				StartDate:    rsp.Result.StartDate,
				EndDate:      rsp.Result.EndDate,
				Status:       rsp.Result.Status,
				Province:     rsp.Result.Province,
				UpdatedDate:  rsp.Result.UpdatedDate,
				CreditCode:   rsp.Result.CreditCode,
				RegistCapi:   rsp.Result.RegistCapi,
				EconKind:     rsp.Result.EconKind,
				Address:      rsp.Result.Address,
				Scope:        rsp.Result.Scope,
				TermStart:    rsp.Result.TermStart,
				TeamEnd:      rsp.Result.TeamEnd,
				CheckDate:    rsp.Result.CheckDate,
				OrgNo:        rsp.Result.OrgNo,
				IsOnStock:    rsp.Result.IsOnStock,
				StockNumber:  rsp.Result.StockNumber,
				StockType:    rsp.Result.StockType,
				ImageUrl:     rsp.Result.ImageUrl,
				EntType:      rsp.Result.EntType,
				RecCap:       rsp.Result.RecCap,
				RevokeInfo:   string(revokeInfoStr),
				Area:         string(areaStr),
				AreaCode:     rsp.Result.AreaCode,
				OriginalName: string(originNameStr),
			}
			if cErr := qcc.NewQCCBasicDetailModel().Create(&insertRow); cErr != nil {
				log.Warnf(fmt.Sprintf("数据:%+v,入库失败:%s", rsp, cErr.Error()))
				_ = qcc.NewQCCBasicDetailHistoryModel().DeleteById(his.Id)
			}
			return nil
		} else {
			rsp.Status = "400"
			rsp.Message = "获取查询数据失败:" + sErr.Error()
			return fmt.Errorf("获取查询数据失败:%s", sErr.Error())
		}
	}
	return nil
}

func QCCGetBasicDetailSearch(req *pb.QCCBasicDetailSearchRequest, rsp *pb.QCCBasicDetailSearchResponse) error {
	if req.Keyword == "" {
		return errx.ErrSearchCantEmpty
	}
	if !req.Force {
		return searchBasicDetails(req, rsp)
	}
	return nil
}
