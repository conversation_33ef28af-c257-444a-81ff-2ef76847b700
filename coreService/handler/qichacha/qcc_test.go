package qichacha

import (
	"fmt"
	pb "micro-service/coreService/proto"
	initmysql "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"testing"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = initmysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

func Test_inv(t *testing.T) {
	initCfg()
	aa := &pb.QCCNameSearchResponse{}
	err := GetNameSearchResult("北京华顺信安信息技术有限公司", aa)
	if err != nil {
		panic(err)
	}
	println(fmt.Sprintf("%+v", aa))
}
