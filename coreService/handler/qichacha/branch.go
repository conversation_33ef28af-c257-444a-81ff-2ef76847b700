package qichacha

import (
	"encoding/json"
	"errors"
	"time"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/redis/qichacha"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"

	"github.com/go-redis/redis/v8"
)

const branchAPI = "/ECIBranch/GetList"

type branch struct{}

type Paging struct {
	PageSize     int `json:"PageSize"`
	PageIndex    int `json:"PageIndex"`
	TotalRecords int `json:"TotalRecords"`
}

type OperInfo struct {
	KeyNo string `json:"KeyNo"`
	Name  string `json:"Name"`
}

type Result struct {
	KeyNo    string   `json:"KeyNo"`
	Name     string   `json:"Name"`
	Status   string   `json:"Status"`
	OperInfo OperInfo `json:"OperInfo"`
}

type BranchResult struct {
	qccBaseUnit
	Paging Paging   `json:"Paging"`
	Result []Result `json:"Result"`
}

func (bh *branch) reqDo(keyword string) (br BranchResult, err error) {
	params := make(map[string]string, 4)
	params["pageIndex"] = "1"
	params["pageSize"] = "20"
	params["searchKey"] = keyword

	resp, err := reqBuilderWithGET(params, branchAPI)
	if err != nil {
		return br, err
	}

	err = json.Unmarshal(resp.Body(), &br)
	if err != nil {
		return br, err
	}
	if br.Status != httpStatusOK && !isEmptyResult(br.Message) {
		return br, errors.New(br.Message)
	}

	return
}

func (*branch) setBranchCache(keyword string, result *BranchResult, d time.Duration) {
	keyBytes, err := json.Marshal(result)
	if err != nil {
		log.Errorf("%s Marshal qichacha result failed, query keyword: %s, because of %+v", pb.ServiceName, keyword, err)
		return
	}

	conn := qichacha.NewCacheClient(0)
	err = conn.SetBranchCache(keyword, string(keyBytes), d)
	if err != nil {
		log.Errorf("%s set qichacha branch result cache failed, query keyword: %s, because of %+v", pb.ServiceName, keyword, err)
	}
}

func GetBranchResult(keyword string, rsp *pb.QCCBranchResponse) error {
	if keyword == "" {
		return errx.ErrSearchCantEmpty
	}

	var result BranchResult
	err := getCache(qichacha.BranchPrefix, keyword, &result)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			log.Errorf("get qichacha branch result cache failed, because of %+v", err)
		}
		item := &branch{}
		x, reqErr := item.reqDo(keyword)
		if reqErr != nil {
			return reqErr
		}
		result = x

		// set branch cache with 7 days
		go item.setBranchCache(keyword, &result, 7*24*time.Hour)
	}

	rsp.Total = int64(result.Paging.TotalRecords)
	for _, v := range result.Result {
		rsp.List = append(rsp.List, &pb.QCCBranchResponseBranchInfo{
			KeyNo:  v.KeyNo,
			Name:   v.Name,
			Status: v.Status,
			OperInfo: &pb.QCCBranchResponseOper{
				KeyNo: v.OperInfo.KeyNo,
				Name:  v.OperInfo.Name,
			},
		})
	}

	return nil
}
