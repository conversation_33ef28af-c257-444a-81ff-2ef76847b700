package qichacha

import (
	"encoding/json"
	"fmt"
	"micro-service/pkg/log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"

	"micro-service/middleware/redis/qichacha"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
)

type qccBaseUnit struct {
	Status      string `json:"Status"`
	Message     string `json:"Message"`
	OrderNumber string `json:"OrderNumber"`
}

var httpStatusOK = strconv.Itoa(http.StatusOK)

// tokenAndTimespanInReqHeader return request param in header, token and Unix timestamp
func tokenAndTimespanInReqHeader(cfgInfo cfg.QCC) (string, string) {
	now := time.Now().Unix()
	nowString := strconv.Itoa(int(now))
	token := utils.Md5sHash(cfgInfo.Key+nowString+cfgInfo.SecretKey, true)

	return token, nowString
}

func reqBuilderWithGET(reqParams map[string]string, apiPath string) (*resty.Response, error) {
	cfgInfo := cfg.LoadQcc()
	if cfgInfo.APIUrl == "" {
		cfgInfo.APIUrl = "https://api.qichacha.com"
		cfgInfo.Key = "920ef5059e7c4fc79c0b4ff7b0f84725"
		cfgInfo.SecretKey = "AE0A7D06E5B8FA121C5941C72C05573B"
		cfgInfo.Switch = true
		log.Warnf(fmt.Sprintf("企查查API地址为空:%+v,config:%+v", cfgInfo, cfg.GetInstance()))
	}
	token, timespan := tokenAndTimespanInReqHeader(cfgInfo)
	reqParams["key"] = cfgInfo.Key
	resp, err := resty.New().R().SetQueryParams(reqParams).
		SetHeader("Token", token).
		SetHeader("Timespan", timespan).
		EnableTrace().Get(cfgInfo.APIUrl + apiPath)

	return resp, err
}

func getCache(prefix, search string, receive any) error {
	client := qichacha.NewCacheClient(0)
	key := client.GenCacheKey(prefix, search)

	str, err := client.GetCache(key)
	if err != nil {
		return err
	}

	err = json.Unmarshal(utils.String2Bytes(str), receive)

	return err
}

func isEmptyResult(s string) bool {
	return strings.Contains(s, "查询无结果")
}
