package qichacha

import (
	"encoding/json"
	"errors"
	"time"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/redis/qichacha"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/go-redis/redis/v8"
)

const nameSearchAPI = "/NameSearch/GetList"

type nameSearch struct{}

type nameSearchUnit struct {
	Name      string `json:"Name"`
	HitReason string `json:"HitReason"`
}

type nameSearchResult struct {
	VerifyResult int64            `json:"VerifyResult"`
	Data         []nameSearchUnit `json:"Data"`
}

type nameSearchResponse struct {
	qccBaseUnit
	Result nameSearchResult `json:"Result"`
}

type rpcNameSearchResponse struct {
	OrderNumber string           `json:"OrderNumber"`
	Result      nameSearchResult `json:"Result"`
}

func (*nameSearch) reqDo(name string) (nsr nameSearchResponse, err error) {
	reqParams := make(map[string]string, 2)
	reqParams["searchName"] = name

	resp, err := reqBuilderWithGET(reqParams, nameSearchAPI)
	if err != nil {
		return nsr, err
	}

	err = json.Unmarshal(resp.Body(), &nsr)
	if err != nil {
		return nsr, err
	}
	if nsr.Status != httpStatusOK && !isEmptyResult(nsr.Message) {
		return nsr, errors.New(nsr.Message)
	}

	return
}

func (*nameSearch) setCache(search string, result rpcNameSearchResponse, d time.Duration) {
	keyBytes, err := json.Marshal(result)
	if err != nil {
		log.Errorf("%s Marshal qichacha name_search result failed, query: %s, because of %+v", pb.ServiceName, search, err)
		return
	}

	conn := qichacha.NewCacheClient(0)
	key := conn.GenCacheKey(qichacha.NameSearchPrefix, search)
	err = conn.SetCache(key, string(keyBytes), d)
	if err != nil {
		log.Errorf("%s set qichacha name_search result cache failed, query: %s, because of %+v", pb.ServiceName, search, err)
	}
}

func GetNameSearchResult(searchName string, rsp *pb.QCCNameSearchResponse) error {
	if searchName == "" {
		return errx.ErrSearchCantEmpty
	}

	// get cache
	var result rpcNameSearchResponse
	err := getCache(qichacha.NameSearchPrefix, searchName, &result)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			log.Errorf("get qichacha name_search result cache failed, because of %+v", err)
		}

		item := &nameSearch{}
		resp, reqErr := item.reqDo(searchName)
		if reqErr != nil {
			return reqErr
		}

		result.OrderNumber = resp.OrderNumber
		result.Result = resp.Result

		//	set cache
		go item.setCache(searchName, result, 7*utils.Day)
	}

	rsp.OrderNumber = result.OrderNumber
	rsp.VerifyResult = result.Result.VerifyResult
	for _, v := range result.Result.Data {
		rsp.Data = append(rsp.Data, &pb.QCCNameSearchResponseNameSearchUnit{
			Name:      v.Name,
			HitReason: v.HitReason,
		})
	}

	return nil
}
