package qichacha

import (
	"encoding/json"
	"errors"
	"strings"
	"time"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/redis"
	"micro-service/middleware/redis/qichacha"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

const (
	investmentThroughAPI = "/ECIInvestmentThroughLevel/GetInfo" // API: 企业对外投资穿透-4层
	throughLevel         = "4"
)

type investmentThrough struct{}

type investmentResult struct {
	VerifyResult int64                                       `json:"VerifyResult"`
	Data         []pb.QCCInvestmentThroughResponseResultUnit `json:"Data"`
}

type InvestmentResponse struct {
	qccBaseUnit
	Result investmentResult `json:"Result"`
}

type InvestmentResultDataStruct struct {
	Name         string                        `json:"name,omitempty"`
	Level        string                        `json:"level,omitempty"`
	Percent      string                        `json:"percent,omitempty"`
	ShouldCapi   string                        `json:"should_capi,omitempty"`
	ChildrenList []*InvestmentResultDataStruct `json:"children_list,omitempty"`
}

type InvestmentResultStruct struct {
	VerifyResult int64                         `json:"VerifyResult"`
	Data         []*InvestmentResultDataStruct `json:"Data"`
}

type InvestmentResponseStruct struct {
	qccBaseUnit
	Result InvestmentResultStruct `json:"Result"`
}

func (it *investmentThrough) reqDo(search string) (ir InvestmentResponse, err error) {
	reqParams := make(map[string]string, 2)
	reqParams["level"] = throughLevel
	reqParams["searchKey"] = search

	resp, err := reqBuilderWithGET(reqParams, investmentThroughAPI)
	if err != nil {
		return ir, err
	}

	body := it.contentJsonKeyReplace(resp.Body())
	err = json.Unmarshal(body, &ir)
	if err != nil {
		return ir, err
	}
	if ir.Status != httpStatusOK && !isEmptyResult(ir.Message) {
		return ir, errors.New(ir.Message)
	}

	return ir, nil
}

func (*investmentThrough) contentJsonKeyReplace(bs []byte) []byte {
	x := string(bs)
	x = strings.ReplaceAll(x, `"Name"`, `"name"`)
	x = strings.ReplaceAll(x, `"Level"`, `"level"`)
	x = strings.ReplaceAll(x, `"Percent"`, `"percent"`)
	x = strings.ReplaceAll(x, `"ShouldCapi"`, `"should_capi"`)
	x = strings.ReplaceAll(x, `"ChildrenList"`, `"children_list"`)

	return utils.String2Bytes(x)
}

func (*investmentThrough) setCache(search string, result *InvestmentResponse, d time.Duration) {
	keyBytes, err := json.Marshal(result)
	if err != nil {
		log.Errorf("%s Marshal qichacha investment_through result failed, query: %s, because of %+v", pb.ServiceName, search, err)
		return
	}

	client := qichacha.NewCacheClient(0)

	key := client.GenCacheKey(qichacha.InvestmentThroughPrefix, search)
	err = client.SetCache(key, string(keyBytes), d)
	if err != nil {
		log.Errorf("%s set qichacha investment_through result cache failed, query: %s, because of %+v", pb.ServiceName, search, err)
	}
}

func GetInvestmentThroughResult(search string) (data InvestmentResponse, err error) {
	if search == "" {
		return data, errx.ErrSearchCantEmpty
	}

	err = getCache(qichacha.InvestmentThroughPrefix, search, &data)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			log.Errorf("get qichacha investment_through result cache failed, because of %+v", err)
		}

		item := &investmentThrough{}
		resp, reqErr := item.reqDo(search)
		if reqErr != nil {
			return data, reqErr
		}
		data = resp

		//	set cache
		go item.setCache(search, &data, 7*utils.Day)
	}

	return data, nil
}

func GetInvestmentThroughResultByStruct(search string) (data InvestmentResponseStruct, err error) {
	tmp, err := GetInvestmentThroughResult(search)
	if err != nil {
		return data, err
	}
	tmpByte, err := json.Marshal(tmp)
	if err != nil {
		return data, err
	}
	if uErr := json.Unmarshal(tmpByte, &data); uErr != nil {
		return data, uErr
	}
	return data, nil
}
