package ip138

import (
	"context"
	"fmt"
	corePb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"
	"sync"

	"github.com/antchfx/htmlquery"
	"golang.org/x/net/html"
)

type HandlerNode func(node *html.Node)

func QueryDomain(ctx context.Context, request *corePb.Ip138DomainRequest, response *corePb.Ip138DomainResponse) error {
	var wg sync.WaitGroup
	domainCount := len(request.Domain)
	results := make([]*corePb.Ip138DomainResponse_Ip, domainCount)
	wg.Add(domainCount)
	for i, domain := range request.Domain {
		go func(index int, domain string) {
			defer wg.Done()
			result := &corePb.Ip138DomainResponse_Ip{
				Domain: domain,
			}
			getQueryItDog(ctx, domain, result)
			results[index] = result
		}(i, domain)
	}
	wg.Wait()
	response.List = results
	return nil
}

// QueryIp 根据IP查询域名
func QueryIp(ctx context.Context, request *corePb.Ip138IpRequest, response *corePb.Ip138IpResponse) error {
	var wg sync.WaitGroup
	ipCount := len(request.Ip)
	results := make([]*corePb.Ip138IpResponse_Ip138Domain, ipCount)
	wg.Add(ipCount)
	for i, ip := range request.Ip {
		go func(index int, ipAddr string) {
			defer wg.Done()
			result := &corePb.Ip138IpResponse_Ip138Domain{
				Ip: ipAddr,
			}
			getQueryIp138(ctx, ipAddr, func(node *html.Node) {
				item := &corePb.Ip138IpResponse_ListDomain{
					Domain: htmlquery.InnerText(htmlquery.FindOne(node, "//a")),
					Time:   strings.Replace(htmlquery.InnerText(htmlquery.FindOne(node, "//span")), "-----", ",", -1),
				}
				result.List = append(result.List, item)
			})
			results[index] = result
		}(i, ip)
	}
	wg.Wait()
	response.List = results
	return nil
}

func getQueryIp138(ctx context.Context, str string, handler HandlerNode) {
	encodeBody, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:     fmt.Sprintf("https://site.ip138.com/%s/", str),
		Method:  crawlerPb.MethodCurlGet,
		Headers: map[string]string{"Host": "site.ip138.com"},
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextWarnf(ctx, "IP138 -> Crawler -> query:%s Error:%v", str, err)
		return
	}

	body := crawlerPb.DecodeBy(encodeBody.Body)
	doc, err := htmlquery.Parse(strings.NewReader(body))
	if err != nil {
		log.WithContextWarnf(ctx, "IP138 -> XpathParse -> query:%s Error:%v", str, err)
		return
	}
	// 判断有没有数据
	_, err = htmlquery.Query(doc, "//ul[@id=\"list\"]/li[3]")
	if err != nil {
		q := fmt.Sprintf("https://site.ip138.com/%s/", str)
		log.WithContextWarnf(ctx, "Request ip138: %s 未查询到有效的数据, body: %s", q, body)
		return
	}

	textNode := htmlquery.FindOne(doc, "//ul[@id=\"list\"]/li[3]")
	if textNode == nil {
		log.WithContextWarnf(ctx, "IP138 -> Xpath Node -> query: %s, body: %s", str, err, body)
		return
	}

	isHasData := htmlquery.InnerText(textNode)
	log.WithContextInfof(ctx, "content: %s", isHasData)
	if isHasData == "暂无结果" {
		log.WithContextInfof(ctx, "content: %s", isHasData)
		return
	}
	liNode, err := htmlquery.QueryAll(doc, "//ul[@id=\"list\"]/li[position() > 2]")
	if err != nil {
		log.WithContextWarnf(ctx, "IP138 -> Xpath Node -> query:%s Error:%v", str, err)
		return
	}
	for _, node := range liNode {
		handler(node)
	}
}

func getQueryItDog(ctx context.Context, str string, rsp *corePb.Ip138DomainResponse_Ip) {
	encodeBody, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:      fmt.Sprintf("https://www.itdog.cn/dns/%s", str),
		Method:   crawlerPb.MethodChromeGet,
		WaitTime: 1,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		log.WithContextWarnf(ctx, "ItDog -> Crawler -> query:%s Error:%v", str, err)
		return
	}
	doc, err := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(encodeBody.Body)))
	if err != nil {
		log.WithContextWarnf(ctx, "ItDog -> XpathParse -> query:%s Error:%v", str, err)
		return
	}
	if ipNode := htmlquery.FindOne(doc, "//a[contains(@class,'copy_ip')]"); ipNode != nil {
		ipResult := strings.Split(htmlquery.SelectAttr(ipNode, "data-clipboard-text"), "\n")
		if len(ipResult) != 0 {
			if ipResult[0] != "没有记录" {
				rsp.Ips = append(rsp.Ips, ipResult...)
			}
		}
	}
}
