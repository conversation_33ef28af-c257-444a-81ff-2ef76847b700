package whois

import (
	"context"
	"database/sql"
	"errors"
	whois2 "github.com/likexian/whois"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/whois"
	"micro-service/pkg/cfg"
	"net"
	"net/url"
	"strconv"
	"strings"
	"time"

	"micro-service/coreService/handler/chinaz"
	corePb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/PuerkitoBio/goquery"
	whoisQuery "github.com/domainr/whois"
	whoisparser "github.com/likexian/whois-parser"
	"github.com/tidwall/gjson"
	"go-micro.dev/v4"
	"golang.org/x/net/idna"
)

type Whois struct {
	Result []*corePb.Whois
}

type HandleResult func(selector *goquery.Selection)

// GetWhoisInfoByDomain 查询Whois基本信息
func GetWhoisInfoByDomain(ctx context.Context, domain string) *whois.Whois {
	var whoisRaw string
	var err error
	whoisRes, _ := whois.NewWhoisModel().FindByDomain(domain) // 从数据库查询whois domain信息
	if whoisRes != nil {
		// 对比上次更新时间：7天
		var week = time.Duration(cfg.LoadCommon().WhoisCacheDay) * 24 * time.Hour
		isUpdate := whoisRes.UpdatedAt.Before(time.Now().Add(-week))
		if !isUpdate {
			log.WithContextInfof(ctx, "whois query db %s ok", domain)
			return whoisRes
		}
		// 查询Whois API, 并更新数据库记录
		//r, qErr := chinaz.ApiWhois(ctx, domain)
		//if qErr == nil {
		//	if err = whois.NewWhoisModel().UpdatesById(whoisRes.Id, r); err != nil {
		//		log.Errorf("[Whois] Update domain: %s record failed: %v", domain, err)
		//	}
		//	return r
		//}
	}

	// 数据库不存在domain
	//r, qErr := chinaz.ApiWhois(ctx, domain)
	//if qErr == nil {
	//	if err = whois.NewWhoisModel().Create(r); err != nil {
	//		log.WithContextInfof(ctx, "[Whois] Insert domain %s record failed: %v", domain, err)
	//	}
	//	return r
	//}

	resp, err := whoisQuery.Fetch(domain)
	if err != nil {
		log.WithContextWarnf(ctx, "whois query fail, Error: %v", err)
		return whoisRes
	}
	whoisRaw = resp.String()
	result, pErr := whoisparser.Parse(whoisRaw)
	if pErr != nil {
		log.WithContextWarnf(ctx, "whois query raw: %v", whoisRaw)
		log.WithContextWarnf(ctx, "whois parse fail, Error: %v", err)
		return whoisRes
	}
	if result.Registrant == nil {
		// 注册商为空时向下查一级
		lines := strings.Split(whoisRaw, "\n")
		for i := range lines {
			if strings.Contains(lines[i], "Registrar WHOIS Server:") {
				whoisClient := whois2.NewClient().SetTimeout(2 * time.Second)
				if ss, qrErr := whoisClient.Whois(domain, strings.TrimSpace(strings.Split(lines[i], ":")[1])); qrErr == nil {
					if ssResult, prErr := whoisparser.Parse(ss); prErr == nil {
						whoisRaw = ss
						result = ssResult
						break
					}
				}
			}
		}
	}
	insertWhois := whois.Whois{
		Domain: domain,
		Raw:    whoisRaw,
	}
	if result.Registrant != nil {
		// Email 过滤无效数据
		if strings.Contains(result.Registrant.Email, "please query the rdds service") {
			result.Registrant.Email = ""
		}
		if strings.Contains(result.Registrant.Email, "request email form") {
			result.Registrant.Email = ""
		}
		insertWhois.RegistrantName = &sql.NullString{String: result.Registrant.Name, Valid: true}
		insertWhois.RegistrantEmail = &sql.NullString{String: result.Registrant.Email, Valid: true}
		insertWhois.RegistrantMobile = &sql.NullString{String: result.Registrant.Phone, Valid: true}
		insertWhois.RegistrantOrg = &sql.NullString{String: result.Registrant.Organization, Valid: true}
	}
	if result.Registrar != nil {
		insertWhois.SponsoringRegistrar = &sql.NullString{String: result.Registrar.Name, Valid: true}
	}
	if result.Domain != nil {
		if result.Domain.CreatedDate != "" {
			insertWhois.RegistrationDate = &sql.NullTime{Time: utils.TimeStringToGoTime(result.Domain.CreatedDate), Valid: true}
		}
		if result.Domain.ExpirationDate != "" {
			insertWhois.ExpirationDate = &sql.NullTime{Time: utils.TimeStringToGoTime(result.Domain.ExpirationDate), Valid: true}
		}
		insertWhois.Dns = strings.Join(result.Domain.NameServers, ",")
		insertWhois.Status = &sql.NullString{String: strings.Join(result.Domain.Status, ","), Valid: true}
	}

	// 注册人为隐私或为空时&公司不为空时使用公司名称
	if whoisRes == nil {
		// 创建数据
		_ = whois.NewWhoisModel().Create(&insertWhois)
		log.WithContextInfof(ctx, "whois insert db %s ok", domain)
	} else {
		// 更新数据
		_ = whois.NewWhoisModel().UpdatesById(whoisRes.Id, &insertWhois)
		log.WithContextInfof(ctx, "whois update db %s ok", domain)
	}
	wInfo, _ := whois.NewWhoisModel().FindByDomain(domain)
	return wInfo
}

func GetWhoisByDomain(ctx context.Context, domain string) (*Whois, error) {
	whoisResult := Whois{}
	whoisInfo := GetWhoisInfoByDomain(ctx, domain)
	if whoisInfo == nil {
		return nil, errors.New("未查到Whois信息")
	}

	// chinaz
	whoisChinz := chinaz.ApiReverseWhois(ctx, whoisInfo.RegistrantMobile.String, whoisInfo.RegistrantEmail.String, whoisInfo.RegistrantName.String)
	if len(whoisChinz) > 0 {
		whoisResult.Result = whoisChinz
		return &whoisResult, nil
	}

	// 注册人为空||为隐私信息时,使用公司名称
	if whoisInfo.RegistrantName.String == "" || whoisInfo.RegistrantName.String == whois.RedactedForPrivacy {
		if whoisInfo.RegistrantOrg.String != whois.RedactedForPrivacy {
			whoisInfo.RegistrantName.String = whoisInfo.RegistrantOrg.String
		} else {
			whoisInfo.RegistrantName.String = ""
		}
	}
	// 反查联系人
	if whoisInfo.RegistrantName.String != "" {
		if err := whoisResult.ReverseContact(ctx, whoisInfo.RegistrantName.String); err != nil {
			log.WithContextWarnf(ctx, "crawler Chinaz Whois-> ReverseContact:%s,Error;%s", whoisInfo.RegistrantName.String, err)
		}
	}
	// 反查邮箱
	if whoisInfo.RegistrantEmail.String != "" && whoisInfo.RegistrantEmail.String != whois.RedactedForPrivacy {
		if err := whoisResult.ReverseEmail(ctx, whoisInfo.RegistrantEmail.String, domain); err != nil {
			log.WithContextWarnf(ctx, "crawler Chinaz Whois-> ReverseEmail:%s,Error:%s", whoisInfo.RegistrantEmail.String, err)
		}
	}
	// 反查电话
	if whoisInfo.RegistrantMobile.String != "" && whoisInfo.RegistrantMobile.String != whois.RedactedForPrivacy {
		if err := whoisResult.ReverseMobile(ctx, whoisInfo.RegistrantMobile.String, domain); err != nil {
			log.WithContextWarnf(ctx, "crawler Chinaz Whois-> ReverseMobile:%s,Error:%s", whoisInfo.RegistrantMobile.String, err)
		}
	}
	return &whoisResult, nil
}

func GetWhoisBasicByDomain(ctx context.Context, rsp *corePb.WhoisBasicResponse, domain string) error {
	log.WithContextInfof(ctx, "GetWhoisByDomain查询, 参数domain: %s", domain)
	// 查询whois数据库信息
	whoisInfo := GetWhoisInfoByDomain(ctx, domain)
	if whoisInfo == nil {
		return nil
	}
	if whoisInfo.SponsoringRegistrar != nil {
		rsp.SponsoringRegistrar = whoisInfo.SponsoringRegistrar.String
	}

	if whoisInfo.RegistrantName != nil {
		companyName := whoisInfo.RegistrantName.String
		// REDACTED FOR PRIVACY,该域名已采取WHOIS隐私保护服务
		if strings.Contains(strings.ToUpper(companyName), "PRIVACY") ||
			strings.Contains(companyName, "隐私") ||
			strings.Contains(strings.ToUpper(companyName), "PROTECTION") ||
			strings.Contains(strings.ToUpper(companyName), "DISCLOSED") {
			companyName = PrivateTips
		}
		rsp.RegistrantName = companyName
	}
	if whoisInfo.RegistrantMobile != nil {
		rsp.RegistrantMobile = whoisInfo.RegistrantMobile.String
	}
	if whoisInfo.RegistrantEmail != nil {
		rsp.RegistrantEmail = whoisInfo.RegistrantEmail.String
	}
	if whoisInfo.RegistrantOrg != nil {
		rsp.RegistrantOrg = whoisInfo.RegistrantOrg.String
	}
	if whoisInfo.RegistrationDate != nil {
		rsp.RegistrationDate = whoisInfo.RegistrationDate.Time.String()
	}
	if whoisInfo.ExpirationDate != nil {
		rsp.ExpirationDate = whoisInfo.ExpirationDate.Time.String()
	}
	return nil
}

func (w *Whois) FullDnsIp() error {
	if len(w.Result) != 0 {
		for i := range w.Result {
			if ips, err := net.LookupIP(w.Result[i].Domain); err != nil {
				log.Warnf("whois -> domain:%s -> lookup -> err:", w.Result[i].Domain, err)
				continue
			} else {
				for _, ip := range ips {
					w.Result[i].Ip = append(w.Result[i].Ip, ip.String())
				}
			}
		}
	}
	return nil
}

func (w *Whois) ReverseContact(ctx context.Context, name string) error {
	name = strings.Trim(name, " ")
	// 查库
	whoisList, _, _ := whois.NewWhoisModel().List(0, 0, mysql.WithWhere("(registrant_name = ? || registrant_org = ?)", name, name))
	if whoisList == nil {
		return nil
	}
	for i := range whoisList {
		if whoisList[i].RegistrantName.String == whois.RedactedForPrivacy {
			whoisList[i].RegistrantName.String = whoisList[i].RegistrantOrg.String
		}
		w.FormatResult(whoisList[i].Domain, whoisList[i].RegistrantEmail.String, whoisList[i].RegistrantMobile.String, whoisList[i].RegistrantName.String)
	}
	// 查Chianz
	err := w.getQuery(ctx, name, "2", "", func(selector *goquery.Selection) {
		fDomain := strings.Trim(selector.Find("td:nth-child(1)").First().Contents().Text(), " ")
		email := strings.Trim(strings.Trim(selector.Find("td:nth-child(2)").First().Contents().Text(), " "), "-")
		mobile := strings.Trim(strings.Trim(selector.Find("td:nth-child(3)").First().Contents().Text(), " "), "-")
		log.WithContextInfof(ctx, "whois -> contact -> domain:%s,email:%s,mobile:%s,register:%s", fDomain, email, mobile, name)
		if _, ok := w.findInWhoisByDomain(fDomain); !ok {
			w.FormatResult(fDomain, email, mobile, name)
			// 异步写入数据库域名Whois
			if err := micro.NewEvent(corePb.RoutingKeyWhois, corePb.GetMicroClient()).Publish(context.Background(), map[string]string{
				"domain":   fDomain,
				"email":    email,
				"mobile":   mobile,
				"register": name,
			}); err != nil {
				log.WithContextWarnf(ctx, "写入队列失败!,Domain:%s", fDomain)
			}
			go GetWhoisInfoByDomain(ctx, fDomain)
		}
	})
	if err != nil {
		return err
	}
	return nil
}

func (w *Whois) ReverseEmail(ctx context.Context, email, domain string) error {
	email = strings.Trim(email, " ")
	// 查库
	whoisList, _, _ := whois.NewWhoisModel().List(0, 0, mysql.WithWhere("registrant_name = ?", email))
	if whoisList == nil {
		return nil
	}
	for i := range whoisList {
		if whoisList[i].RegistrantName.String == whois.RedactedForPrivacy {
			whoisList[i].RegistrantName.String = whoisList[i].RegistrantOrg.String
		}
		w.FormatResult(whoisList[i].Domain, whoisList[i].RegistrantEmail.String, whoisList[i].RegistrantMobile.String, whoisList[i].RegistrantName.String)
	}
	if err := w.getQuery(ctx, email, "1", domain, func(selector *goquery.Selection) {
		fDomain := strings.Trim(selector.Find("td:nth-child(1)").First().Contents().Text(), " ")
		register := strings.Trim(strings.Trim(selector.Find("td:nth-child(2)").First().Contents().Text(), " "), "-")
		mobile := strings.Trim(strings.Trim(selector.Find("td:nth-child(3)").First().Contents().Text(), " "), "-")
		log.WithContextInfof(ctx, "whois -> email -> domain:%s,email:%s,mobile:%s,register:%s", fDomain, email, mobile, register)
		if _, ok := w.findInWhoisByDomain(fDomain); !ok {
			w.FormatResult(fDomain, email, mobile, register)
			// 异步写入数据库域名Whois
			if err := micro.NewEvent(corePb.RoutingKeyWhois, corePb.GetMicroClient()).Publish(context.Background(), map[string]string{
				"domain":   fDomain,
				"email":    email,
				"mobile":   mobile,
				"register": register,
			}); err != nil {
				log.WithContextWarnf(ctx, "写入队列失败!,Domain:%s", fDomain)
			}
		}
	}); err != nil {
		return err
	}
	return nil
}

func (w *Whois) ReverseMobile(ctx context.Context, mobile, domain string) error {
	mobile = strings.Trim(mobile, " ")
	// 查库
	whoisList, _, _ := whois.NewWhoisModel().List(0, 0, mysql.WithWhere("registrant_mobile = ?", mobile))
	if whoisList == nil {
		return nil
	}
	for i := range whoisList {
		if whoisList[i].RegistrantName.String == whois.RedactedForPrivacy {
			whoisList[i].RegistrantName.String = whoisList[i].RegistrantOrg.String
		}
		w.FormatResult(whoisList[i].Domain, whoisList[i].RegistrantEmail.String, whoisList[i].RegistrantMobile.String, whoisList[i].RegistrantName.String)
	}
	// 查Chianz
	if err := w.getQuery(ctx, mobile, "3", domain, func(selector *goquery.Selection) {
		fDomain := strings.Trim(selector.Find("td:nth-child(1)").First().Contents().Text(), " ")
		register := strings.Trim(strings.Trim(selector.Find("td:nth-child(2)").First().Contents().Text(), " "), "-")
		email := strings.Trim(strings.Trim(selector.Find("td:nth-child(3)").First().Contents().Text(), " "), "-")
		log.WithContextInfof(ctx, "whois -> mobile -> domain:%s,email:%s,mobile:%s,register:%s", fDomain, email, mobile, register)
		if _, ok := w.findInWhoisByDomain(fDomain); !ok {
			w.FormatResult(fDomain, email, mobile, register)
			// 异步写入数据库域名Whois
			if err := micro.NewEvent(corePb.RoutingKeyWhois, corePb.GetMicroClient()).Publish(context.Background(), map[string]string{
				"domain":   fDomain,
				"email":    email,
				"mobile":   mobile,
				"register": register,
			}); err != nil {
				log.WithContextWarnf(ctx, "写入队列失败!,Domain:%s", fDomain)
			}
		}
	}); err != nil {
		return err
	}
	return nil
}

func (w *Whois) FormatResult(fDomain, email, mobile, register string) {
	if ix, ok := w.findInWhoisByDomain(fDomain); ok {
		if w.Result[ix].Email == "" && email != "" {
			w.Result[ix].Email = email
		}
		if w.Result[ix].Mobile == "" && mobile != "" {
			w.Result[ix].Mobile = mobile
		}
		if w.Result[ix].Register == "" && register != "" && register != "REDACTED FOR PRIVACY" {
			w.Result[ix].Register = register
		}
	} else {
		idnaDomain, _ := idna.New().ToASCII(fDomain)
		if fDomain == idnaDomain {
			idnaDomain = ""
		}
		w.Result = append(w.Result, &corePb.Whois{
			Domain:   fDomain,
			Idna:     idnaDomain,
			Email:    email,
			Mobile:   mobile,
			Register: register,
		})
	}
}

func (w *Whois) getQuery(ctx context.Context, host, pType, domain string, handle HandleResult) error {
	index := 1
	for {
		host = strings.Trim(strings.Trim(host, "+"), " ")
		postData := url.Values{"host": []string{host}, "type": []string{pType}, "isupdate": []string{"0"}, "page": []string{strconv.Itoa(index)}}
		if domain != "" {
			postData.Add("domain", domain)
		}
		log.WithContextInfof(ctx, "whois -> getQuery -> query:%s,domain:%s,page:%d", host, domain, index)
		crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
			Url:    "https://mwhois.chinaz.com/Whois/GetData/",
			Method: crawlerPb.MethodCurlPost,
			Headers: map[string]string{
				"origin":       "https://mwhois.chinaz.com",
				"authority":    "mwhois.chinaz.com",
				"content-type": "application/x-www-form-urlencoded; charset=UTF-8",
			},
			Body: postData.Encode(),
		}, utils.SetRpcTimeoutOpt(10))
		if err != nil {
			return err
		}
		log.WithContextInfof(ctx, "crawler Chinaz Whois-> host:%s,code:%d,size:%d", host, crawlerRes.Code, len(crawlerRes.Body))
		// Load the HTML document
		body := crawlerPb.DecodeBy(crawlerRes.Body)
		if gjson.Get(body, "status").Int() == 1 {
			return errors.New(gjson.Get(body, "msg").String())
		}
		html := gjson.Get(body, "html").String()
		if html == "" {
			return nil
		}
		doc, err := goquery.NewDocumentFromReader(strings.NewReader("<table><tbody>" + html + "</tbody></table>"))
		if err != nil {
			return err
		}
		if trNode := doc.Find("tbody > tr"); trNode != nil {
			trNode.Each(func(i int, selection *goquery.Selection) {
				handle(selection)
			})
			// 小于20条时,不用请求第二次
			if len(trNode.Nodes) < 20 {
				return nil
			}
		}
		index += 1
	}
}

func (w *Whois) findInWhoisByDomain(val string) (int, bool) {
	for i, item := range w.Result {
		if item.Domain == val {
			return i, true
		}
	}
	return -1, false
}
