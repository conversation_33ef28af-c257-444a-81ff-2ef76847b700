package whois

import (
	"context"
	"fmt"
	"time"

	corePb "micro-service/coreService/proto"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
)

const PrivateTips = "该域名已采取WHOIS隐私保护服务"

func QueryDomain(ctx context.Context, rsp *corePb.WhoisResponse, domain string) error {
	// 有缓存时取环境数据
	cacheKey := fmt.Sprintf("whois:%s", domain)
	if redis.GetCache(cacheKey, &rsp.Whois) {
		log.WithContextInfof(ctx, "Received Whois cacheKey:%s", cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "Received Whois NotCache:%s", cacheKey)
	whois, err := GetWhoisByDomain(ctx, domain)
	if err != nil {
		return err
	}
	rsp.Whois = append(rsp.Whois, whois.Result...)
	// 设置缓存
	redis.SetCache(cacheKey, 60*3*time.Second, rsp.Who<PERSON>)
	return nil
}

// QueryBasicDomain
// 查询逻辑：
// 1-查询缓存
// 2-查询数据库信息
// 3-查询whois api, 插入/更新whois数据库
func QueryBasicDomain(ctx context.Context, rsp *corePb.WhoisBasicResponse, domain string) error {
	// 有缓存时取环境数据
	cacheKey := fmt.Sprintf("whois_basic:%s", domain)
	if redis.GetCache(cacheKey, rsp) {
		return nil
	}
	// 缓存不存在
	err := GetWhoisBasicByDomain(ctx, rsp, domain)
	if err != nil {
		return err
	}
	// 设置缓存
	redis.SetCache(cacheKey, 24*time.Hour, rsp)

	return nil
}
