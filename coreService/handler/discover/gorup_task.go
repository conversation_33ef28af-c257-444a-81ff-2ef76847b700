package discover

import (
	"context"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"micro-service/coreService/handler/icp"
	"micro-service/coreService/handler/tianyancha"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/discover"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dbx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"
	"sync"
	"time"
	"unicode/utf8"
)

var discoverGroupTaskOnce sync.Once

var discoverGroupTask *GroupTask

type GroupTask struct {
	taskRunning sync.Map
}

func GetGroupTaskInstance() *GroupTask {
	if discoverGroupTask == nil {
		discoverGroupTaskOnce.Do(func() { discoverGroupTask = &GroupTask{} })
	}
	return discoverGroupTask
}

// CreateGroupTask 创建任务
func (t *GroupTask) CreateGroupTask(ctx context.Context, req *pb.DiscoverCreateGroupTaskRequest, rsp *pb.DiscoverCreateTaskResponse) (uint64, error) {
	taskType := req.Model
	companyName := req.CompanyName
	percent := cast.ToFloat32(req.Percent)
	force := req.Force
	if req.IsDomain {
		icpInfo := pb.IcpResponse{}
		if icpErr := icp.DomainOnly(context.TODO(), &pb.IcpDomainRequest{Domain: companyName, Force: force}, &icpInfo); icpErr != nil {
			return 0, icpErr
		}
		if icpInfo.Info == nil || !icpInfo.Ba {
			return 0, errors.New(fmt.Sprintf("获取 %s 的ICP备案信息失败", companyName))
		}
		if icpInfo.Info.CompanyName == "" {
			return 0, errors.New(fmt.Sprintf("获取 %s 的ICP备案信息失败", companyName))
		}
		companyName = icpInfo.Info.CompanyName
		rsp.CompanyName = companyName
	}
	// 并发处理
	key := "discover-group:" + companyName + cast.ToString(taskType) + cast.ToString(percent)
	lock, err := redis.GetInstance().SetNX(context.TODO(), key, 1, 5*time.Second).Result()
	if err != nil {
		return 0, fmt.Errorf("操作失败,请稍后再试,Error:%s", err.Error())
	}
	if !lock {
		return 0, errors.New("操作失败,任务正在处理中请稍后再试")
	}
	defer redis.GetInstance().Del(context.TODO(), key)
	// 检查企业名称
	if nameErr := CheckCompanyName(companyName); nameErr != nil {
		return 0, nameErr
	}
	task, err := discover.NewGroupTaskModel().First(
		mysql.WithWhere("company_name", companyName),
		mysql.WithWhere("type", taskType),
		mysql.WithWhere("percent", percent),
	)
	// 任务不存在
	if err != nil {
		// 创建任务
		force = true
		task = &discover.GroupTask{CompanyName: companyName, Type: cast.ToInt(taskType), Percent: cast.ToFloat32(percent), Process: 1}
		if err = discover.NewGroupTaskModel().Create(task); err != nil {
			return 0, fmt.Errorf("创建任务失败:%s", err.Error())
		}
	}
	// 缓存时间超过指定时间
	if time.Now().After(task.UpdatedAt.Add(time.Duration(cfg.LoadCommon().AssetCacheDay) * utils.Day)) {
		force = true
		// 重置进度
		task.Process = 1
		task.Reason = ""
		if err = discover.NewGroupTaskModel().Update(task); err != nil {
			return 0, fmt.Errorf("创建任务失败:%s", err.Error())
		}
	}
	if force || task.Process != 100 || task.Reason != "" {
		// 检查是否有已在执行的任务
		if !t.CheckTaskRunning(task.Id) {
			// 异步执行任务
			go t.StartRun(ctx, task, force)
		} else {
			log.WithContextInfof(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,任务执行中,跳过流程", task.Id, task.CompanyName, task.Type))
		}
		// 有执行中的任务直接返回
	}
	return task.Id, nil
}

func CheckCompanyName(companyName string) error {
	if utf8.RuneCountInString(companyName) < 3 {
		return fmt.Errorf("操作失败,企业名称不正确")
	}
	names := &pb.QCCNameSearchResponse{}
	// err := qichacha.GetNameSearchResult(companyName, names)
	err := tianyancha.Search(context.TODO(), &pb.QCCNameSearchRequest{Search: companyName}, names)
	if err != nil {
		return fmt.Errorf("操作失败,检查企业名称失败:%s", err.Error())
	}
	if len(names.Data) == 0 {
		return fmt.Errorf("操作失败,企业名称不正确")
	}
	ok := false
	for i := range names.Data {
		if names.Data[i].Name == companyName {
			ok = true
		}
	}
	if !ok {
		return fmt.Errorf("操作失败,企业名称不正确")
	}

	return nil
}

// GetGroupTaskProcess 获取任务进度
func (t *GroupTask) GetGroupTaskProcess(_ context.Context, companyName string, taskType uint32, percent float32) (*discover.GroupTask, error) {
	if companyName == "" {
		return nil, fmt.Errorf("获取任务信息失败")
	}
	task, err := discover.NewGroupTaskModel().First(
		mysql.WithWhere("company_name", companyName),
		mysql.WithWhere("type", taskType),
		mysql.WithWhere("percent", percent),
	)
	if err != nil {
		return nil, fmt.Errorf("获取任务进度失败:%s", err.Error())
	}
	return task, nil
}

// CheckTaskRunning 检查任务是否在执行中
func (t *GroupTask) CheckTaskRunning(taskId uint64) bool {
	if _, ok := t.taskRunning.Load(taskId); ok {
		return true
	}
	return false
}

// updateProcess 更新任务进度
func (t *GroupTask) updateProcess(_ context.Context, task *discover.GroupTask) (int, error) {
	subQueryStr := "discover_task.company_name in (select discover_group_company.company_name from discover_group_company where discover_group_company.task_id = ?) and discover_task.type = ?"
	tasks, _, err := discover.NewTaskModel().List(0, 0, mysql.WithWhere(subQueryStr, task.Id, task.Type))
	if err != nil {
		return 0, err
	}
	total := len(tasks) * 100
	current := 0
	for _, dbTask := range tasks {
		current += cast.ToInt(dbTask.Process)
	}
	if total == 0 {
		total = 1
	}
	process := (cast.ToFloat32(current) / cast.ToFloat32(total)) * 100
	_ = discover.NewGroupTaskModel().SetProcess(task.Id, cast.ToFloat64(process))
	return cast.ToInt(process), nil
}

// StartRun 开始执行任务
func (t *GroupTask) StartRun(ctx context.Context, task *discover.GroupTask, force bool) {
	// 写入执行中的任务
	t.taskRunning.Store(task.Id, task)
	// 结束后释放
	defer func() {
		if err := recover(); err != nil {
			_ = discover.NewGroupTaskModel().SetReason(task.Id, fmt.Sprintf("任务执行失败:%v", err))
			log.WithContextError(ctx, err)
		}
		// 释放任务锁
		t.taskRunning.Delete(task.Id)
		// 更新任务为完成
		if err := discover.NewGroupTaskModel().SetProcess(task.Id, 100); err != nil {
			log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,更新任务进度为100失败", task.Id, task.CompanyName, task.Type))
		}
	}()

	// 获取股权信息
	// data, err := qichacha.GetInvestmentThroughResultByStruct(task.CompanyName)
	var rsp = &pb.QCCInvestmentThroughResponse{}
	err := tianyancha.InvestTree(ctx, &pb.QCCInvestmentThroughRequest{Search: task.CompanyName}, rsp)
	if err != nil {
		_ = discover.NewGroupTaskModel().SetReason(task.Id, "获取股权信息失败:"+err.Error())
		return
	}
	// 添加集团公司信息
	company := &discover.GroupCompany{
		Model:  dbx.Model{CreatedAt: time.Now(), UpdatedAt: time.Now()},
		TaskId: task.Id, CompanyName: task.CompanyName, Percent: 100, ShouldCapi: "",
		Level: 0, ParentId: 0, Path: "",
	}
	// 查找数据是否存在
	if info, companyErr := discover.NewGroupCompanyModel().First(
		mysql.WithWhere("task_id", task.Id),
		mysql.WithWhere("company_name", task.CompanyName),
	); companyErr != nil {
		// 不存在创建
		if cErr := discover.NewGroupCompanyModel().Create(company); cErr != nil {
			log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,写入集团公司失败,Infos:%+v", task.Id, task.CompanyName, task.Type, company))
		}
	} else {
		// 存在更新
		company.Id = info.Id
		if cErr := discover.NewGroupCompanyModel().Update(company); cErr != nil {
			log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,写入集团公司失败,Infos:%+v", task.Id, task.CompanyName, task.Type, company))
		}
	}
	// 下发企业任务
	taskRequest := pb.DiscoverCreateTaskRequest{IsDomain: false, CompanyName: company.CompanyName, Model: cast.ToUint32(task.Type), Force: force}
	if _, companyErr := GetTaskInstance().CreateTask(ctx, &taskRequest, &pb.DiscoverCreateTaskResponse{}); companyErr != nil {
		log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,下发集团公司任务失败,Error:%s", task.Id, task.CompanyName, task.Type, companyErr.Error()))
	}
	// 写入任务企业信息
	limitGet := false

	if len(rsp.List) > 0 {
		t.AddGroupCompanyDiscoverTask(ctx, task, rsp.List[0].ChildrenList, company.Id, cast.ToString(company.Id), force)
	}
	// 限制等待6个小时
	time.AfterFunc(6*time.Hour, func() {
		limitGet = true
	})
	for !limitGet {
		// 进度等于100 跳出等待
		process, _ := t.updateProcess(ctx, task)
		if 100 == process {
			break
		}
		time.Sleep(1 * time.Second)
	}
}

// AddGroupCompanyDiscoverTask 添加子企业
func (t *GroupTask) AddGroupCompanyDiscoverTask(ctx context.Context, task *discover.GroupTask, data []*pb.QCCInvestmentThroughResponseResultUnit, parentId uint64, parentPath string, force bool) {
	for i := range data {
		percent := cast.ToFloat32(strings.Replace(data[i].Percent, "%", "", -1))
		if percent < task.Percent {
			log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,股权比例小于指定比例,跳过,current:%.2f,task:%.2f", task.Id, task.CompanyName, task.Type, percent, task.Percent))
			continue
		} else {
			log.WithContextInfof(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,股权比例大于指定比例,下发任务,current:%.2f,task:%.2f", task.Id, data[i].Name, task.Type, percent, task.Percent))
		}
		// 入库
		company := &discover.GroupCompany{
			Model:       dbx.Model{CreatedAt: time.Now(), UpdatedAt: time.Now()},
			TaskId:      task.Id,
			CompanyName: data[i].Name,
			Percent:     percent,
			ShouldCapi:  data[i].ShouldCapi,
			Level:       cast.ToInt(data[i].Level),
			ParentId:    0,
			Path:        "",
		}
		// 查找数据是否存在
		if info, err := discover.NewGroupCompanyModel().First(
			mysql.WithWhere("task_id", task.Id),
			mysql.WithWhere("company_name", data[i].Name),
		); err != nil {
			// 不存在创建
			if cErr := discover.NewGroupCompanyModel().Create(company); cErr != nil {
				log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,写入控股公司失败,Infos:%+v", task.Id, task.CompanyName, task.Type, company))
				continue
			}
		} else {
			// 存在更新
			company.Id = info.Id
			if cErr := discover.NewGroupCompanyModel().Update(company); cErr != nil {
				log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,写入控股公司失败,Infos:%+v", task.Id, task.CompanyName, task.Type, company))
				continue
			}
		}
		// 更新父级信息
		if parentId != 0 {
			company.ParentId = parentId
			company.Path = parentPath + "-" + cast.ToString(company.Id)
			if uErr := discover.NewGroupCompanyModel().Update(company); uErr != nil {
				log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,更新公司层级信息失败,Infos:%+v", task.Id, task.CompanyName, task.Type, company))
			}
		}
		// 下发企业任务
		taskRequest := pb.DiscoverCreateTaskRequest{IsDomain: false, CompanyName: company.CompanyName, Model: cast.ToUint32(task.Type), Force: force}
		if _, companyErr := GetTaskInstance().CreateTask(ctx, &taskRequest, &pb.DiscoverCreateTaskResponse{}); companyErr != nil {
			log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,下发企业任务失败,Error:%s", task.Id, task.CompanyName, task.Type, companyErr.Error()))
		}
		// if len(data[i].ChildrenList) != 0 {
		// 	t.AddGroupCompanyDiscoverTask(ctx, task, data[i].ChildrenList, company.Id, company.Path, force)
		// }
	}
}

func (t *GroupTask) GetTaskClueTree(taskIds [][2]uint64) map[uint64]map[uint64]*pb.DiscoverClue {
	taskClues := make(map[uint64]map[uint64]*pb.DiscoverClue, 0)
	for x := range taskIds {
		taskId, rTaskId := taskIds[x][0], taskIds[x][1]
		taskClues[rTaskId] = GetTaskInstance().GetTaskClueTree(taskId)
	}
	return taskClues
}
