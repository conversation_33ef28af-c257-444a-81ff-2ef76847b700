package discover

import (
	"context"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	hClue "micro-service/coreService/handler/clue"
	"micro-service/coreService/handler/fofa"
	"micro-service/coreService/handler/icp"
	"micro-service/coreService/handler/recommend"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/discover"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"strings"
	"sync"
	"time"
)

var discoverTaskOnce sync.Once

var discoverTask *Task

type Task struct {
	taskRunning sync.Map
}

func GetTaskInstance() *Task {
	if discoverTask == nil {
		discoverTaskOnce.Do(func() { discoverTask = &Task{} })
	}
	return discoverTask
}

// CreateTask 创建任务
func (t *Task) CreateTask(ctx context.Context, req *pb.DiscoverCreateTaskRequest, rsp *pb.DiscoverCreateTaskResponse) (uint64, error) {
	taskType := req.Model
	companyName := req.CompanyName
	force := req.Force
	if req.IsDomain {
		icpInfo := pb.IcpResponse{}
		if icpErr := icp.DomainOnly(context.TODO(), &pb.IcpDomainRequest{Domain: companyName, Force: force}, &icpInfo); icpErr != nil {
			return 0, icpErr
		}
		if icpInfo.Info == nil || !icpInfo.Ba {
			return 0, errors.New(fmt.Sprintf("获取 %s 的ICP备案信息失败", companyName))
		}
		if icpInfo.Info.CompanyName == "" {
			return 0, errors.New(fmt.Sprintf("获取 %s 的ICP备案信息失败", companyName))
		}
		companyName = icpInfo.Info.CompanyName
		rsp.CompanyName = companyName
	}
	// 并发处理
	key := "discover:" + companyName + cast.ToString(taskType)
	lock, err := redis.GetInstance().SetNX(context.TODO(), key, 1, 5*time.Second).Result()
	if err != nil {
		return 0, fmt.Errorf("操作失败,请稍后再试,Error:%s", err.Error())
	}
	if !lock {
		return 0, errors.New("操作失败,任务正在处理中请稍后再试")
	}
	defer redis.GetInstance().Del(context.TODO(), key)
	// 检查企业名称
	if nameErr := CheckCompanyName(companyName); nameErr != nil {
		return 0, nameErr
	}
	task, err := discover.NewTaskModel().First(mysql.WithWhere("company_name", companyName), mysql.WithWhere("type", taskType))
	// 任务不存在
	if err != nil {
		// 创建任务
		force = true
		task = &discover.Task{CompanyName: companyName, Type: cast.ToInt(taskType), Process: 1}
		if err = discover.NewTaskModel().Create(task); err != nil {
			return 0, fmt.Errorf("创建任务失败:%s", err.Error())
		}
	}
	// 缓存时间超过指定时间
	if time.Now().After(task.UpdatedAt.Add(time.Duration(cfg.LoadCommon().AssetCacheDay) * utils.Day)) {
		force = true
		// 重置进度
		task.Process = 1
		task.Reason = ""
		if err = discover.NewTaskModel().Update(task); err != nil {
			return 0, fmt.Errorf("创建任务失败:%s", err.Error())
		}
	}
	if force || task.Process != 100 || task.Reason != "" {
		// 检查是否有已在执行的任务
		if !t.CheckTaskRunning(task.Id) {
			// 异步执行任务
			go t.StartRun(ctx, task, force)
		} else {
			log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,任务执行中,跳过流程", task.Id, task.CompanyName, task.Type))
		}
		// 有执行中的任务直接返回
	}
	return task.Id, nil
}

// GetTaskProcess 获取任务进度
func (t *Task) GetTaskProcess(_ context.Context, companyName string, taskType uint32) (*discover.Task, error) {
	if companyName == "" {
		return nil, fmt.Errorf("获取任务信息失败")
	}
	task, err := discover.NewTaskModel().First(
		mysql.WithWhere("company_name", companyName),
		mysql.WithWhere("type", taskType),
	)
	if err != nil {
		return nil, fmt.Errorf("获取任务进度失败:%s", err.Error())
	}
	return task, nil
}

// StartRun 开始执行任务
func (t *Task) StartRun(ctx context.Context, task *discover.Task, force bool) {
	// 写入执行中的任务
	t.taskRunning.Store(task.Id, task)
	// 结束后释放
	defer func() {
		if err := recover(); err != nil {
			_ = discover.NewTaskModel().SetReason(task.Id, fmt.Sprintf("任务执行失败:%v", err))
			log.WithContextWarn(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,任务执行失败:%v", task.Id, task.CompanyName, task.Type, err))
		}
		// 释放任务锁
		t.taskRunning.Delete(task.Id)
		// 更新任务为完成
		if err := discover.NewTaskModel().SetProcess(task.Id, 100); err != nil {
			log.WithContextWarn(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,更新任务进度为100失败", task.Id, task.CompanyName, task.Type))
		}
	}()
	// 扩展线索
	clues := t.RunClueExpandFlow(ctx, task, force)
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索扩展完成", task.Id, task.CompanyName, task.Type))
	// 推荐资产
	if clues != nil {
		t.RunRecommendAsset(ctx, task, clues, force)
	}
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,任务处理完成", task.Id, task.CompanyName, task.Type))
}

// RunRecommendAsset 推荐任务资产
func (t *Task) RunRecommendAsset(ctx context.Context, task *discover.Task, clues *sync.Map, force bool) {
	reClues := make([]*pb.CloudClue, 0)
	rTaskProcess, rTaskProcessInfo := &pb.RecommendProcessResponse{}, &pb.RecommendProcessResponse{}
	rTaskInfo := &pb.RecommendTaskResponse{}
	limitGet := false
	var err error
	clues.Range(func(key, value any) bool {
		clueInfo := value.(*pb.ClueInfo)
		reClues = append(reClues, &pb.CloudClue{
			Content:     clueInfo.Content,
			Hash:        clueInfo.Hash,
			Type:        clueInfo.Type,
			CompanyName: clueInfo.CompanyName,
		})
		return true
	})
	if len(reClues) == 0 {
		_ = discover.NewTaskModel().SetReason(task.Id, "未发现该企业资产线索")
		return
	}
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索:%+v,开始拉取FOFA数据", task.Id, task.CompanyName, task.Type, reClues))
	if err = recommend.AddRecommendTask(context.TODO(), &pb.RecommendTaskRequest{Clue: reClues, Force: force}, rTaskInfo); err != nil {
		_ = discover.NewTaskModel().SetReason(task.Id, fmt.Sprintf("拉取FOFA数据失败:%s", err.Error()))
		log.WithContextWarn(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索:%+v,拉取FOFA数据失败:%s", task.Id, task.CompanyName, task.Type, reClues, err.Error()))
		return
	}
	// 更新推荐任务ID,第一次拉取资产时直接写入推荐任务ID,后续更新时,更新完成在写入任务ID
	if force && task.ReTaskId == 0 {
		if err = discover.NewTaskModel().UpSet(task.Id, "re_task_id", rTaskInfo.TaskId); err != nil {
			_ = discover.NewTaskModel().SetReason(task.Id, fmt.Sprintf("拉取FOFA数据失败:%s", err.Error()))
			log.WithContextWarn(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,re_task_id:%d,更新推荐任务ID失败:%s", task.Id, task.CompanyName, task.Type, rTaskInfo.TaskId, err.Error()))
			return
		}
	}
	// 限制等待3小时
	time.AfterFunc(3*time.Hour, func() {
		limitGet = true
	})
	// 循环检测,任务结果
	for rTaskProcess.Process != 100 && !limitGet {
		err = recommend.GetRecommendProcess(context.TODO(), &pb.RecommendProcessRequest{TaskId: rTaskInfo.TaskId}, rTaskProcessInfo)
		time.Sleep(1 * time.Second)
		if err != nil {
			continue
		} else {
			rTaskProcess = rTaskProcessInfo
		}
		log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,推荐任务ID:%v,进度:%+v", task.Id, task.CompanyName, task.Type, rTaskInfo.TaskId, rTaskProcess.Process))
		// 更新进度
		if rTaskProcess.Process > 50 {
			_ = discover.NewTaskModel().SetProcess(task.Id, cast.ToFloat64(rTaskProcess.Process))
		} else {
			_ = discover.NewTaskModel().SetProcess(task.Id, cast.ToFloat64("46"+"."+fmt.Sprintf("%0*d", 2, rTaskProcess.Process)))
		}
	}
	// 强制更新时,推荐任务完成后在写入任务ID
	if err = discover.NewTaskModel().UpSet(task.Id, "re_task_id", rTaskInfo.TaskId); err != nil {
		_ = discover.NewTaskModel().SetReason(task.Id, fmt.Sprintf("拉取FOFA数据失败:%s", err.Error()))
		log.WithContextWarn(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,re_task_id:%d,更新推荐任务ID失败:%s", task.Id, task.CompanyName, task.Type, rTaskInfo.TaskId, err.Error()))
		return
	}
}

// RunClueExpandFlow 线索扩展流程
func (t *Task) RunClueExpandFlow(ctx context.Context, task *discover.Task, force bool) *sync.Map {
	clues, oldClues, allClues := &sync.Map{}, &sync.Map{}, &sync.Map{}
	bas := t.GetBeiAnClues(ctx, task, force)
	beiAnTopDomains := t.GetBeiAnTopDomains(bas)
	if len(bas) == 0 {
		_ = discover.NewTaskModel().SetReason(task.Id, "未取到备案信息")
		log.WithContextWarn(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,未取到备案信息,任务结束", task.Id, task.CompanyName, task.Type))
		return nil
	}
	for i := range bas {
		// 记录所有线索,ICON需要单独处理层级问题
		if bas[i].Type != general_clues.ClueTypeIcon {
			allClues.LoadOrStore(bas[i].Id, &discover.TaskClue{
				TaskId: task.Id, ClueId: bas[i].Id, ParentId: 0, Path: cast.ToString(bas[i].Id), Status: discover.NoConfirm,
			})
		} else {
			// 备案网站ICON,证据链排在域名下面
			for x := range bas {
				domainMap := []int{general_clues.ClueTypeDomain, general_clues.ClueTypeSubdomain}
				if utils.ListContains(domainMap, cast.ToInt(bas[x].Type)) && bas[x].Content == bas[i].Source {
					allClues.LoadOrStore(bas[i].Id, &discover.TaskClue{
						TaskId: task.Id, ClueId: bas[i].Id, ParentId: bas[x].Id, Path: cast.ToString(bas[x].Id) + "-" + cast.ToString(bas[i].Id), Status: discover.NoConfirm,
					})
				}
			}
		}
		// 精准模式,跳过关键字,企业名称
		if utils.ListContains([]uint32{general_clues.ClueTypeKeyword, general_clues.ClueTypeCompanyName}, bas[i].Type) && task.Type == discover.TypePrecise {
			allClues.LoadOrStore(bas[i].Id, &discover.TaskClue{
				TaskId: task.Id, ClueId: bas[i].Id, ParentId: bas[i].Id, Path: cast.ToString(bas[i].Id), Status: discover.NoConfirm,
			})
			continue
		}
		// 精准模式,跳过ICON
		if bas[i].Type == general_clues.ClueTypeIcon && task.Type == discover.TypePrecise {
			continue
		}
		// 全量模式,资产数量大于3000的ICON和关键字线索过滤
		if task.Type == discover.TypeFull && utils.ListContains([]int{general_clues.ClueTypeIcon, general_clues.ClueTypeKeyword, general_clues.ClueTypeCompanyName}, cast.ToInt(bas[i].Type)) {
			if t.getFofaCount(ctx, bas[i]) > 3000 {
				log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,忽略线索,资产数量大于3000:%+v", task.Id, task.CompanyName, task.Type, bas[i]))
				continue
			}
		}
		// 保存备案线索信息,处理ICON
		if bas[i].Type == general_clues.ClueTypeIcon {
			oldClues.LoadOrStore(bas[i].Id, bas[i])
			// 备案网站ICON,证据链排在域名下面
			for x := range bas {
				domainMap := []int{general_clues.ClueTypeDomain, general_clues.ClueTypeSubdomain}
				if utils.ListContains(domainMap, cast.ToInt(bas[x].Type)) && bas[x].Content == bas[i].Source {
					clues.LoadOrStore(bas[i].Id, &discover.TaskClue{
						TaskId: task.Id, ClueId: bas[i].Id, ParentId: bas[x].Id, Path: cast.ToString(bas[x].Id) + "-" + cast.ToString(bas[i].Id),
					})
				}
			}
			continue
		}
		oldClues.LoadOrStore(bas[i].Id, bas[i])
		clues.LoadOrStore(bas[i].Id, &discover.TaskClue{TaskId: task.Id, ClueId: bas[i].Id, ParentId: 0, Path: cast.ToString(bas[i].Id)})
	}

	// 更新进度
	_ = discover.NewTaskModel().SetProcess(task.Id, 10)
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展IP线索", task.Id, task.CompanyName, task.Type))
	// 扩展IP
	t.handlerClue(ctx, task, oldClues, clues, allClues, beiAnTopDomains, general_clues.ClueTypeIp, 15, force)
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展域名线索", task.Id, task.CompanyName, task.Type))
	// 扩展域名
	t.handlerClue(ctx, task, oldClues, clues, allClues, beiAnTopDomains, general_clues.ClueTypeDomain, 20, force)
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展子域名域名线索", task.Id, task.CompanyName, task.Type))
	// 扩展子域名
	t.handlerClue(ctx, task, oldClues, clues, allClues, beiAnTopDomains, general_clues.ClueTypeSubdomain, 23, force)
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展证书线索", task.Id, task.CompanyName, task.Type))
	// 扩展证书
	t.handlerClue(ctx, task, oldClues, clues, allClues, beiAnTopDomains, general_clues.ClueTypeCert, 25, force)
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展ICP线索", task.Id, task.CompanyName, task.Type))
	// 扩展ICP
	t.handlerClue(ctx, task, oldClues, clues, allClues, beiAnTopDomains, general_clues.ClueTypeIcp, 30, force)
	// 全量模式
	if task.Type == discover.TypeFull {
		log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展ICON线索", task.Id, task.CompanyName, task.Type))
		// 扩展ICON
		t.handlerClue(ctx, task, oldClues, clues, allClues, beiAnTopDomains, general_clues.ClueTypeIcon, 35, force)
		log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展Keyword线索", task.Id, task.CompanyName, task.Type))
		// 扩展Keyword
		t.handlerClue(ctx, task, oldClues, clues, allClues, beiAnTopDomains, general_clues.ClueTypeKeyword, 40, force)
	}
	// 线索记录入库
	clues.Range(func(key, value any) bool {
		cl := value.(*discover.TaskClue)
		cl.Status = discover.OkConfirm
		allClues.Delete(key)
		// 不存在记录时,创建
		if !discover.NewTaskClueModel().Exists(cl.TaskId, cl.ClueId) {
			if err := discover.NewTaskClueModel().Create(cl); err != nil {
				log.WithContextWarnf(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索记录入库失败:%+v", task.Id, task.CompanyName, task.Type, value.(*discover.TaskClue)))
			} else {
				log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索记录入库成功:%+v", task.Id, task.CompanyName, task.Type, value.(*discover.TaskClue)))
			}
		} else {
			log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索记录已存在:%+v", task.Id, task.CompanyName, task.Type, value.(*discover.TaskClue)))
		}
		return true
	})
	// 线索记录入库(未确认的线索)
	// allClues.Range(func(key, value any) bool {
	//	cl := value.(*discover.TaskClue)
	//	// 不存在记录时,创建
	//	if !discover.NewTaskClueModel().Exists(cl.TaskId, cl.ClueId) {
	//		if err := discover.NewTaskClueModel().Create(cl); err != nil {
	//			log.WithContextWarnf(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索记录入库失败:%+v", task.Id, task.CompanyName, task.Type, value.(*discover.TaskClue)))
	//		} else {
	//			log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索记录入库成功:%+v", task.Id, task.CompanyName, task.Type, value.(*discover.TaskClue)))
	//		}
	//	} else {
	//		log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索记录已存在:%+v", task.Id, task.CompanyName, task.Type, value.(*discover.TaskClue)))
	//	}
	//	return true
	// })
	// 更新进度
	_ = discover.NewTaskModel().SetProcess(task.Id, 45)
	// 返回线索内容
	return oldClues
}

// 计算OldClues线索总数
func (t *Task) countCluesNumber(oldClues *sync.Map, clueType uint32) int {
	count := 0
	oldClues.Range(func(key, value any) bool {
		clueInfo := value.(*pb.ClueInfo)
		// 非指定线索,不处理
		if clueInfo.Type != clueType {
			return true
		}
		count += 1
		return true
	})
	return count
}

func (t *Task) handlerClue(ctx context.Context, task *discover.Task, oldClues, clues, allClues *sync.Map, beiAnTopDomains []string, clueType, process uint32, force bool) {
	clueCount := t.countCluesNumber(oldClues, clueType)
	currentNumber := 0
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,开始扩展线索,扩展线索类型:%d,需要扩展的线索数量:%d", task.Id, task.CompanyName, task.Type, clueType, clueCount))
	oldClues.Range(func(key, value any) bool {
		clueInfo := value.(*pb.ClueInfo)
		// 非指定线索,不处理
		if clueInfo.Type != clueType {
			return true
		}
		currentNumber += 1
		taskClue, _ := clues.Load(clueInfo.Id)
		log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展线索,扩展线索类型:%d,需要扩展的线索数量:%d,当前到:%d,扩展目标:%+v", task.Id, task.CompanyName, task.Type, clueType, clueCount, currentNumber, clueInfo))
		tmpClues := t.GetExpandClues(ctx, task, clueInfo, force)
		log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展线索结束,扩展线索类型:%d,需要扩展的线索数量:%d,当前到:%d,扩展目标:%+v,扩展线索数量:%d", task.Id, task.CompanyName, task.Type, clueType, clueCount, currentNumber, clueInfo, len(tmpClues)))
		for x := range tmpClues {
			allClues.LoadOrStore(tmpClues[x].Id, &discover.TaskClue{
				TaskId:   task.Id,
				ClueId:   tmpClues[x].Id,
				ParentId: clueInfo.Id,
				Path:     taskClue.(*discover.TaskClue).Path + "-" + cast.ToString(tmpClues[x].Id),
				Status:   discover.NoConfirm,
			})
			// 精准模式,跳过关键字
			if tmpClues[x].Type == general_clues.ClueTypeKeyword && task.Type == discover.TypePrecise {
				continue
			}
			// 精准模式,跳过ICON
			if tmpClues[x].Type == general_clues.ClueTypeIcon && task.Type == discover.TypePrecise {
				continue
			}
			// 忽略企业名称不相等或者CN顶级域名不匹配的线索
			if utils.CompanyNameEquals(tmpClues[x].CompanyName, task.CompanyName) || t.CheckCertCN(tmpClues[x], beiAnTopDomains) == 1 || tmpClues[x].Type == general_clues.ClueTypeIp {
				oldClues.LoadOrStore(tmpClues[x].Id, tmpClues[x])
				clues.LoadOrStore(tmpClues[x].Id, &discover.TaskClue{
					TaskId:   task.Id,
					ClueId:   tmpClues[x].Id,
					ParentId: clueInfo.Id,
					Path:     taskClue.(*discover.TaskClue).Path + "-" + cast.ToString(tmpClues[x].Id),
				})
				log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索OK:%+v", task.Id, task.CompanyName, task.Type, tmpClues[x]))
			} else if task.Type == discover.TypeFull && utils.ListContains([]int{general_clues.ClueTypeIcon, general_clues.ClueTypeCert, general_clues.ClueTypeKeyword, general_clues.ClueTypeCompanyName}, cast.ToInt(tmpClues[x].Type)) {
				// 忽略资产大于3000的ICON
				if t.getFofaCount(ctx, tmpClues[x]) <= 3000 {
					oldClues.LoadOrStore(tmpClues[x].Id, tmpClues[x])
					clues.LoadOrStore(tmpClues[x].Id, &discover.TaskClue{
						TaskId:   task.Id,
						ClueId:   tmpClues[x].Id,
						ParentId: clueInfo.Id,
						Path:     taskClue.(*discover.TaskClue).Path + "-" + cast.ToString(tmpClues[x].Id),
					})
					log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,线索OK:%+v", task.Id, task.CompanyName, task.Type, tmpClues[x]))
				} else {
					log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,忽略线索,数量大于3000:%+v", task.Id, task.CompanyName, task.Type, tmpClues[x]))
				}
			} else {
				log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,忽略线索:%+v", task.Id, task.CompanyName, task.Type, tmpClues[x]))
			}
		}
		return true
	})
	// 更新进度
	_ = discover.NewTaskModel().SetProcess(task.Id, cast.ToFloat64(process))
}

// CheckCertCN 检查证书CN顶级是否一致 0/不存在,1/存在,-1/不是证书
func (t *Task) CheckCertCN(clue *pb.ClueInfo, domains []string) int {
	if clue.Type == general_clues.ClueTypeCert {
		_, cn := utils.PluckCert(clue.Content)
		if utils.ListContains(domains, utils.GetTopDomain(cn)) {
			return 1
		} else {
			return 0
		}
	}
	return -1
}

// GetBeiAnTopDomains 获取备案定级域名
func (t *Task) GetBeiAnTopDomains(clues []*pb.ClueInfo) []string {
	domains := make([]string, 0)
	for i := range clues {
		if clues[i].Type == general_clues.ClueTypeDomain {
			if !utils.ListContains(domains, clues[i].Content) {
				domains = append(domains, clues[i].Content)
			}
		}
		if clues[i].Type == general_clues.ClueTypeSubdomain {
			if !utils.ListContains(domains, utils.GetTopDomain(clues[i].Content)) {
				domains = append(domains, utils.GetTopDomain(clues[i].Content))
			}
		}
	}
	return domains
}

// GetBeiAnClues 获取备案线索
func (t *Task) GetBeiAnClues(ctx context.Context, task *discover.Task, force bool) []*pb.ClueInfo {
	cTaskResult, cTaskResultInfo, cTaskInfo := &pb.ExpandResultResponse{}, &pb.ExpandResultResponse{}, &pb.ExpandClueResponse{}
	limitGet := false
	log.WithContextInfof(ctx, fmt.Sprintf("获取备案线索-GetBeiAnClues,keyword:%s,", task.CompanyName))
	err := hClue.ExpandCompanyName(context.TODO(), &pb.ExpandKeywordRequest{Keyword: task.CompanyName, CompanyName: task.CompanyName, Force: force}, cTaskInfo)
	if err != nil {
		log.WithContextWarn(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,获取备案信息失败:%s", task.Id, task.CompanyName, task.Type, err.Error()))
		return nil
	}
	// 限制等待4分钟，这个是为了跳出等待备案线索获取的任务，最多等待4分钟
	time.AfterFunc(4*time.Minute, func() {
		limitGet = true
	})
	// 循环检测,任务结果
	for cTaskResult.Process != 100 && !limitGet {
		err = hClue.GetExpandResult(context.TODO(), &pb.ExpandResultRequest{TaskId: cTaskInfo.TaskId}, cTaskResultInfo)
		if err != nil {
			continue
		} else {
			cTaskResult = cTaskResultInfo
		}
		time.Sleep(3 * time.Second)
		log.WithContextInfof(ctx, fmt.Sprintf("task_id:%d,CompanyName:%s,type:%d,备案查询,任务ID:%v,进度:%+v", task.Id, task.CompanyName, task.Type, cTaskInfo.TaskId, cTaskResult.Process))
	}
	// 写入线索信息
	return cTaskResult.Items
}

// GetExpandClues 获取扩展线索
func (t *Task) GetExpandClues(ctx context.Context, task *discover.Task, clue *pb.ClueInfo, force bool) []*pb.ClueInfo {
	cTaskResult, cTaskResultInfo, cTaskInfo := &pb.ExpandResultResponse{}, &pb.ExpandResultResponse{}, &pb.ExpandClueResponse{}
	companyName := ""
	if clue.Confirmed == 1 || clue.CompanyName != "" {
		companyName = clue.CompanyName
	}
	var err error
	limitGet := false
	if clue.Type == general_clues.ClueTypeDomain {
		err = hClue.ExpandDomain(context.TODO(), &pb.ExpandKeywordRequest{Keyword: clue.Content, CompanyName: companyName, Force: force}, cTaskInfo)
	} else if clue.Type == general_clues.ClueTypeSubdomain {
		err = hClue.ExpandSubDomain(context.TODO(), &pb.ExpandKeywordRequest{Keyword: clue.Content, CompanyName: companyName, Force: force}, cTaskInfo)
	} else if clue.Type == general_clues.ClueTypeIcp {
		err = hClue.ExpandIcp(context.TODO(), &pb.ExpandKeywordRequest{Keyword: clue.Content, CompanyName: companyName, Force: force}, cTaskInfo)
	} else if clue.Type == general_clues.ClueTypeCert {
		err = hClue.ExpandCert(context.TODO(), &pb.ExpandKeywordRequest{Keyword: clue.Content, CompanyName: companyName, Force: force}, cTaskInfo)
	} else if clue.Type == general_clues.ClueTypeIp {
		err = hClue.ExpandIp(context.TODO(), &pb.ExpandKeywordRequest{Keyword: clue.Content, CompanyName: companyName, Force: force}, cTaskInfo)
	} else if clue.Type == general_clues.ClueTypeIcon {
		err = hClue.ExpandIcon(context.TODO(), &pb.ExpandIconRequest{Keyword: clue.Content, Hash: clue.Hash, CompanyName: companyName, Force: force}, cTaskInfo)
	} else if clue.Type == general_clues.ClueTypeCompanyName {
		err = hClue.ExpandCompanyName(context.TODO(), &pb.ExpandKeywordRequest{Keyword: clue.Content, CompanyName: companyName, Force: force}, cTaskInfo)
	} else {
		err = hClue.ExpandKeyword(context.TODO(), &pb.ExpandKeywordRequest{Keyword: clue.Content, CompanyName: companyName, Force: force}, cTaskInfo)
	}
	if err != nil {
		log.WithContextWarn(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展目标:%+v,扩展线索失败:%s", task.Id, task.CompanyName, task.Type, clue, err.Error()))
		return nil
	}
	// 限制等待三分钟
	time.AfterFunc(10*time.Minute, func() {
		limitGet = true
	})
	// 循环检测,任务结果
	for cTaskResult.Process != 100 && !limitGet {
		err = hClue.GetExpandResult(ctx, &pb.ExpandResultRequest{TaskId: cTaskInfo.TaskId}, cTaskResultInfo)
		if err != nil {
			continue
		} else {
			cTaskResult = cTaskResultInfo
		}
		time.Sleep(1 * time.Second)
		log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,task_id:%d,CompanyName:%s,type:%d,扩展目标:%+v,扩展线索中,线索扩展任务ID:%v,进度:%+v", task.Id, task.CompanyName, task.Type, clue, cTaskInfo.TaskId, cTaskResult.Process))
	}
	// 写入线索信息
	return cTaskResult.Items
}

// CheckTaskRunning 检查任务是否在执行中
func (t *Task) CheckTaskRunning(taskId uint64) bool {
	if _, ok := t.taskRunning.Load(taskId); ok {
		return true
	}
	return false
}

func (t *Task) handlerClueTree(relation *discover.TaskClue, relations map[uint64]discover.TaskClue, clues map[uint64]general_clues.GeneralClues) *pb.DiscoverClue {
	if relation.ParentId == 0 {
		return &pb.DiscoverClue{
			Id:          clues[relation.ClueId].Id,
			Content:     clues[relation.ClueId].Content,
			Hash:        clues[relation.ClueId].Hash,
			Type:        cast.ToUint32(clues[relation.ClueId].Type),
			CompanyName: clues[relation.ClueId].CompanyName,
			UpdateAt:    clues[relation.ClueId].UpdatedAt.Format(utils.DateTimeLayout),
			ParentId:    relation.ParentId,
			ParentClue:  nil,
		}
	} else {
		parentRe := relations[relation.ParentId]
		return &pb.DiscoverClue{
			Id:          clues[relation.ClueId].Id,
			Content:     clues[relation.ClueId].Content,
			Hash:        clues[relation.ClueId].Hash,
			Type:        cast.ToUint32(clues[relation.ClueId].Type),
			CompanyName: clues[relation.ClueId].CompanyName,
			UpdateAt:    clues[relation.ClueId].UpdatedAt.Format(utils.DateTimeLayout),
			ParentId:    relation.ParentId,
			ParentClue:  t.handlerClueTree(&parentRe, relations, clues),
		}
	}
}

func (t *Task) GetTaskClueTree(taskId uint64) map[uint64]*pb.DiscoverClue {
	disClues := make(map[uint64]*pb.DiscoverClue, 0)
	relations := t.getTaskRelationMap(taskId)
	clues := t.getTaskCluesMap(taskId)
	for i := range relations {
		re := relations[i]
		disClues[relations[i].ClueId] = t.handlerClueTree(&re, relations, clues)
	}
	return disClues
}

func (t *Task) GetTaskClue(clues map[uint64]*pb.DiscoverClue, clue *pb.CloudClue) *pb.DiscoverClue {
	for i := range clues {
		if clues[i].Type == general_clues.ClueTypeIcon {
			if clues[i].Hash == clue.Hash {
				return clues[i]
			}
		} else {
			if clues[i].Content == clue.Content && clues[i].Type == cast.ToUint32(clue.Type) {
				return clues[i]
			}
		}
	}
	return nil
}

func (t *Task) getTaskRelationMap(taskId uint64) map[uint64]discover.TaskClue {
	relationMaps := make(map[uint64]discover.TaskClue, 0)
	relations, _, err := discover.NewTaskClueModel().List(0, 0, mysql.WithWhere("task_id", taskId))
	if err != nil {
		return relationMaps
	}
	for i := range relations {
		relationMaps[relations[i].ClueId] = relations[i]
	}
	return relationMaps
}

func (t *Task) getTaskCluesMap(taskId uint64) map[uint64]general_clues.GeneralClues {
	clues := make(map[uint64]general_clues.GeneralClues, 0)
	oldClues := general_clues.NewGeneralCluesModel().GetDiscoverTaskResult(taskId)
	for i := range oldClues {
		if oldClues[i].Type == general_clues.ClueTypeIcon {
			oldClues[i].Content = storage.GenAPIDownloadPath("favicon.ico", oldClues[i].Content)
		}
		clues[oldClues[i].Id] = oldClues[i]
	}
	return clues
}

// GetFofaCount 获取FOFA拉取数据总数
func (t *Task) getFofaCount(ctx context.Context, clue *pb.ClueInfo) uint32 {
	rsp := pb.FofaQueryCountResponse{}
	err := fofa.FofaQueryCount(context.TODO(), &rsp, &pb.FofaQueryCountRequest{
		Qbase64:          t.GetFofaQuery(clue),
		Full:             true,
		CanGetLimitCount: true,
	})
	if err != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("资产发现任务,GetFofaCount,Query:%s,Error:%s", t.GetFofaQuery(clue), err.Error()))
		// 重试一次,
		err = fofa.FofaQueryCount(context.TODO(), &rsp, &pb.FofaQueryCountRequest{
			Qbase64:          t.GetFofaQuery(clue),
			Full:             true,
			CanGetLimitCount: true,
		})
	}
	if err != nil {
		rsp = pb.FofaQueryCountResponse{Count: 5000}
		log.WithContextWarnf(ctx, fmt.Sprintf("资产发现任务,GetFofaCount,重试失败:按默认10000条数据处理,Query:%s,Error:%s", t.GetFofaQuery(clue), err.Error()))
	}
	log.WithContextInfof(ctx, fmt.Sprintf("资产发现任务,GetFofaCount,Query:%s,OK:%d", t.GetFofaQuery(clue), rsp.Count))
	return rsp.Count
}

// GetFofaQuery 根据线索获取FOFA查询语句
func (t *Task) GetFofaQuery(clue *pb.ClueInfo) string {
	if clue.Type == general_clues.ClueTypeIcon {
		return fmt.Sprintf(`icon_hash="%s"`, cast.ToString(clue.Hash))
	} else if clue.Type == general_clues.ClueTypeIp {
		return fmt.Sprintf(`ip="%s"`, clue.Content)
	} else if clue.Type == general_clues.ClueTypeKeyword || clue.Type == general_clues.ClueTypeCompanyName {
		return fmt.Sprintf(`title="%s"`, clue.Content)
	} else if clue.Type == general_clues.ClueTypeDomain || clue.Type == general_clues.ClueTypeSubdomain {
		// `(domain="%s" || host*="*.%s" || header="%s/" || banner="%s;" || ip ="%s")`
		if utils.IsIP(clue.Content) {
			return fmt.Sprintf(`(domain="%s" || host*="*.%s" || ip ="%s")`, clue.Content, clue.Content, clue.Content)
		} else {
			return fmt.Sprintf(`(domain="%s" || host*="*.%s")`, clue.Content, clue.Content)
		}
	} else if clue.Type == general_clues.ClueTypeIcp {
		return fmt.Sprintf(`(icp="%s" || icp="%s" || body="%s")`, clue.Content, strings.TrimSuffix(clue.Content, "号"), clue.Content)
	} else if clue.Type == general_clues.ClueTypeCert {
		oStr, cnStr := utils.PluckCert(clue.Content)
		if oStr == "" && cnStr == "" {
			return fmt.Sprintf(`(cert="CommonName: %s" || cert="CommonName: *.%s") || (cert.subject="%s")`, clue.Content, clue.Content, clue.Content)
		} else {
			cn := strings.TrimPrefix(cnStr, "*.")
			if cnStr != "" {
				return fmt.Sprintf(`(cert="CommonName: %s" || cert="CommonName: *.%s")`, cn, cn)
			} else {
				return fmt.Sprintf(`(cert.subject="%s")`, oStr)
			}
		}
	} else {
		return fmt.Sprintf(`title="%s"`, clue.Content)
	}
}
