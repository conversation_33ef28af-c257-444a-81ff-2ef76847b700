package quake

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/idoubi/goz"

	corePb "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

const HttpUrlQuake = "https://quake.360.net"

type QuakeResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    []struct {
		Components []struct {
			ProductLevel   string   `json:"product_level"`
			ProductType    []string `json:"product_type"`
			ProductVendor  string   `json:"product_vendor"`
			ProductNameCn  string   `json:"product_name_cn"`
			ProductNameEn  string   `json:"product_name_en"`
			Id             string   `json:"id"`
			ProductCatalog []string `json:"product_catalog"`
			Version        string   `json:"version"`
		} `json:"components"`
		Hostname string `json:"hostname"`
		Org      string `json:"org"`
		Port     int    `json:"port"`
		Service  struct {
			Response string `json:"response"`
			Name     string `json:"name"`
			Version  string `json:"version"`
			Http     struct {
				XPoweredBy      string `json:"x_powered_by"`
				Server          string `json:"server"`
				Path            string `json:"path"`
				HtmlHash        string `json:"html_hash"`
				ResponseHeaders string `json:"response_headers"`
				StatusCode      int    `json:"status_code"`
				Favicon         struct {
					Data     string `json:"data"`
					Location string `json:"location"`
					Hash     string `json:"hash"`
					S3Url    string `json:"s3_url"`
				} `json:"favicon"`
				Host         string `json:"host"`
				Body         string `json:"body"`
				MetaKeywords string `json:"meta_keywords"`
				Title        string `json:"title"`
			} `json:"http,omitempty"`
			Cert string `json:"cert,omitempty"`
		} `json:"service"`
		Ip       string `json:"ip"`
		OsName   string `json:"os_name,omitempty"`
		Location struct {
			Owner       string    `json:"owner"`
			ProvinceCn  string    `json:"province_cn"`
			Isp         string    `json:"isp"`
			ProvinceEn  string    `json:"province_en"`
			CountryEn   string    `json:"country_en"`
			DistrictCn  string    `json:"district_cn"`
			Gps         []float64 `json:"gps"`
			StreetCn    string    `json:"street_cn"`
			CityEn      string    `json:"city_en"`
			DistrictEn  string    `json:"district_en"`
			CountryCn   string    `json:"country_cn"`
			StreetEn    string    `json:"street_en"`
			CityCn      string    `json:"city_cn"`
			CountryCode string    `json:"country_code"`
			Asname      string    `json:"asname"`
			SceneCn     string    `json:"scene_cn"`
			SceneEn     string    `json:"scene_en"`
			Radius      int       `json:"radius"`
		} `json:"location"`
		IsIpv6    bool      `json:"is_ipv6"`
		Transport string    `json:"transport"`
		Time      time.Time `json:"time"`
		Asn       int       `json:"asn"`
		Id        string    `json:"id"`
		Domain    string    `json:"domain,omitempty"`
	} `json:"data"`
	Meta struct {
		Pagination struct {
			Count     int `json:"count"`
			PageIndex int `json:"page_index"`
			PageSize  int `json:"page_size"`
			Total     int `json:"total"`
		} `json:"pagination"`
	} `json:"meta"`
}

// CurlRequest CURL 请求
func CurlRequest(method, url string, opts ...goz.Options) (goz.ResponseBody, error) {
	cli := goz.NewClient()

	resp, err := cli.Request(method, url, opts...)
	if err != nil {
		return nil, err
	}
	content, err := resp.GetBody()
	if err != nil {
		return nil, err
	}
	return content, nil
}

// QuakeQuery Quake Search
func QuakeQuery(ctx context.Context, rsp *corePb.FofaQueryResponse, param *corePb.HunterQueryRequest) error {
	log.WithContextInfof(ctx, "Received Core.QuakeQuery request: %s", utils.AnyToStr(param))
	// 获取Quake配置
	quakeConfig := cfg.LoadQuake()
	hunterUrl := HttpUrlQuake + "/api/v3/search/quake_service"

	// 构建查询参数
	values := make(map[string]any)
	var apiKey = quakeConfig.ApiKey
	if apiKey == "" {
		apiKey = "53553f80-fb5f-46c9-86d8-873179985387"
	}
	if param.Page < 1 {
		param.Page = 1
	}
	values["query"] = param.Search
	values["start"] = (param.Page - 1) * param.PageSize
	values["size"] = param.PageSize
	if param.StartTime != "" {
		values["start_time"] = param.StartTime
	}
	if param.EndTime != "" {
		values["end_time"] = param.EndTime
	}
	// 默认排除CDN和蜜罐
	values["shortcuts"] = []string{
		"635fcb52cc57190bd8826d09",
		"635fcbaacc57190bd8826d0b",
		"63734bfa9c27d4249ca7261c",
	}
	// 仅排除无效请求
	if param.IsWeb != 0 {
		values["shortcuts"] = []string{"63734bfa9c27d4249ca7261c"}
	}
	values["latest"] = true

	// 将所有参数拼接到URL中
	log.WithContextInfof(ctx, "Received Core.QuakeQuery Request URL: %s", hunterUrl)

	response, err := CurlRequest(http.MethodPost, hunterUrl, goz.Options{
		Headers: map[string]interface{}{
			"Content-Type": "application/json",
			"X-QuakeToken": apiKey,
		},
		JSON: values,
	})
	if err != nil {
		log.WithContextErrorf(ctx, "Quake请求错误: %v   报错信息，%s", param, err.Error())
		return err
	}

	var data QuakeResponse
	err = json.Unmarshal([]byte(response.GetContents()), &data)
	if err != nil {
		log.WithContextErrorf(ctx, "Quake返回解析错误: %v   报错信息: %s   返回内容: %s", param, err.Error(), response.GetContents())
		return err
	}

	if data.Code != 0 {
		log.WithContextErrorf(ctx, "Quake接口返回错误码: %d   错误信息: %s   返回内容: %s", data.Code, data.Message, response.GetContents())
		rsp.Error = true
		return nil
	}

	for i, _ := range data.Data {
		item := data.Data[i]
		port := strconv.Itoa(item.Port)
		var product []string
		if len(item.Components) > 0 {
			for _, component := range item.Components {
				if component.ProductNameCn != "" {
					product = append(product, component.ProductNameCn)
				}
			}
		}
		productStr := strings.Join(product, ",") // 使用逗号将所有的 product 连接起来
		url := item.Ip
		if item.Domain != "" {
			url = item.Domain
		}
		if item.Port != 80 && item.Port != 443 {
			url += ":" + port
		}
		url += item.Service.Http.Path
		lastUpdateTime := item.Time.Format("2006-01-02 15:04:05")
		asnNum := strconv.Itoa(item.Asn)
		statCode := strconv.Itoa(item.Service.Http.StatusCode)
		longitude := fmt.Sprintf("%f", item.Location.Gps[0])
		latitude := fmt.Sprintf("%f", item.Location.Gps[1])
		asset := &corePb.Asset{
			Ip:             &item.Ip,
			Port:           &port,
			Protocol:       &item.Service.Name,
			BaseProtocol:   &item.Transport,
			Host:           &item.Hostname,
			Link:           &url,
			Domain:         &item.Domain,
			Title:          &item.Service.Http.Title,
			Isp:            &item.Location.Isp,
			Lastupdatetime: &lastUpdateTime,
			Icon:           &item.Service.Http.Favicon.S3Url,
			IconHash:       &item.Service.Http.Favicon.Hash,
			Version:        &item.Service.Version,
			Product:        &productStr,
			AsNumber:       &asnNum,
			AsOrganization: &item.Location.Asname,
			City:           &item.Location.CityCn,
			StatusCode:     &statCode,
			Header:         &item.Service.Http.ResponseHeaders,
			Longitude:      &longitude,
			Latitude:       &latitude,
			Region:         &item.Location.ProvinceCn,
			Country:        &item.Location.CountryCn,
		}
		rsp.Sdata = append(rsp.Sdata, asset)
	}
	rsp.Error = false
	size := uint32(data.Meta.Pagination.Total)
	rsp.Size = &size
	page := param.Page
	rsp.Page = &page
	log.WithContextInfof(ctx, "Core.QuakeQuery request: %v", param)
	return nil
}
