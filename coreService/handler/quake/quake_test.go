package quake

import (
	"context"
	"fmt"
	"testing"

	"github.com/joho/godotenv"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

func initCfg() {
	_ = godotenv.Load("../../../.env")
	cfg.InitLoadCfg()

	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

func Test_QuakeQuery(t *testing.T) {
	//initCfg()
	//cfg.InitLoadCfg()

	log.Init()
	rsp := pb.FofaQueryResponse{}

	err := QuakeQuery(context.TODO(), &rsp, &pb.HunterQueryRequest{Search: `hostname:"git" AND NOT domain: "nv-games.com" AND NOT (ip: *************)`, Page: 1, PageSize: 10})
	if err != nil {
		panic(err)
	}
	for i := range rsp.Sdata {
		println(fmt.Sprintf("%+v", rsp.Sdata[i]))
	}
	return
}
