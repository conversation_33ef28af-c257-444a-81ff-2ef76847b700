package handler

import (
	"context"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company_clues"
	"micro-service/pkg/fortest"
	"micro-service/pkg/log"
	"os"
	"testing"
)

func TestCore_ClueCompanyDropList(t *testing.T) {
	fortest.InitCfg()
	h := &Core{}
	req := pb.ClueCompanyDropListRequest{
		UserId:           659,
		OperateCompanyId: 134,
	}
	resp := pb.ClueCompanyDropListResponse{}
	err := h.ClueCompanyDropList(context.Background(), &req, &resp)
	if err != nil && err.Error() == "您没有该企业的操作权限" {
		t.Log("校验生效")
		return
	}
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp.Data)
}

func TestCore_ClueCompanyListt(t *testing.T) {
	fortest.InitCfg()
	log.Init()
	h := &Core{}
	req := pb.ClueCompanyClueListRequest{
		UserId:           659,
		OperateCompanyId: 83,
		CompanyName:      "北京伯乐互联科技发展有限公司2",
	}
	resp := pb.ClueCompanyClueListResponse{}
	err := h.ClueCompanyClueList(context.Background(), &req, &resp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp.Data)
}

func TestCore_ClueCompanyListExport(t *testing.T) {
	fortest.InitCfg()
	os.Setenv("go_test", "true")
	h := &Core{}
	req := pb.ClueCompanyListExportRequest{
		UserId:           659,
		OperateCompanyId: 83,
		CompanyName:      "北京伯乐互联科技发展有限公司2",
	}
	resp := pb.ClueCompanyListExportResponse{}
	ctx := context.Background()
	err := h.ClueCompanyListExport(ctx, &req, &resp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp.Url)
}

func Test_clueSaveAsExcel(t *testing.T) {
	fortest.InitCfg()
	os.Setenv("go_test", "true")
	records, err := company_clues.NewCompanyCluesModel().ListAll(mysql.WithSelect("type,content,created_at"),
		mysql.WithWhere("user_id = ?", 659))
	if err != nil {
		t.Error(err)
	}
	url, err := clueSaveAsExcel(659, records)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(url)
}

func Test_genClueCompanySceneMap(t *testing.T) {
	if len(genClueCompanySceneMap()) != 3 {
		t.Fatal("生成错误")
	}
	t.Log("!!!")
}

func Test_getLastMonthCompanyCluesAllCount(t *testing.T) {
	fortest.InitCfg()
	type args struct {
		ctx      context.Context
		scene    string
		nowCount int64
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "test1",
			args: args{
				ctx:      context.Background(),
				scene:    "chushi",
				nowCount: 233342352454,
			},
			want: 233342352454,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getLastMonthCompanyCluesAllCount(tt.args.ctx, tt.args.scene, tt.args.nowCount); got != tt.want {
				t.Errorf("getLastMonthCompanyCluesAllCount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClueLibCompanyClueOverview(t *testing.T) {
	fortest.InitCfg()
	h := &Core{}

	req := pb.ClueLibCompanyClueOverviewRequest{
		UserId:           659,
		OperateCompanyId: 134,
	}
	resp := pb.ClueLibCompanyClueOverviewResponse{}
	err := h.ClueLibCompanyClueOverview(context.Background(), &req, &resp)
	if err != nil && err.Error() == "您没有该企业的操作权限" {
		t.Log("校验生效")
		return
	}
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp.ClueInfo)
}
