package zerozone

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"micro-service/pkg/cfg"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"net/http"
	"time"
)

const (
	publicMiniAppApi         = "/api/data/"
	publicMiniAppCachePrefix = cachePrefix + "mini_app:"
)

func MiniAppAccount(ctx context.Context, search string) ([]MiniAppItem, error) {
	if search == "" {
		return nil, errx.ErrSearchCantEmpty
	}

	log.WithContextInfof(ctx, "[zerozone] get MiniApp accounts, keyword: %s", search)
	var err error
	var data MiniAppResp
	var key = publicMiniAppCachePrefix + search
	if !getCache(ctx, key, &data) {
		log.WithContextInfof(ctx, "[zerozone] get key: %s cache is empty", key)
		data, err = getMiniAppByApi(ctx, search)
		if err != nil {
			return nil, err
		}
	}
	return data.Result.Items, nil
}

type MiniAppResp struct {
	RespBase
	Result MiniAppResult `json:"result"`
}

type MiniAppResult struct {
	Total int           `json:"total"`
	Items []MiniAppItem `json:"items,omitempty"`
}

type MiniAppItem struct {
	AppId        string    `json:"app_id"`       // 小程序id
	WechatId     string    `json:"wechat_id"`    // 微信id
	Name         string    `json:"name"`         // 名称
	IconUrl      string    `json:"icon_url"`     // 图片地址
	CompanyName  string    `json:"company_name"` // 企业名称
	Icp          string    `json:"icp"`          // Icp
	Introduction string    `json:"introduction"` // Icp
	UpdateTime   time.Time `json:"update_time"`  // 更新时间
}

func getMiniAppByApi(ctx context.Context, search string) (MiniAppResp, error) {
	rsp, err := _getMiniAppByApi(ctx, search)
	return rsp, err
}

// 调用天眼查API获取微信小程序信息
func _getMiniAppByApi(ctx context.Context, search string) (MiniAppResp, error) {
	var data MiniAppResp
	// {"query":"(type==微信小程序)&&(company==华顺信安信息技术有限公司)", "query_type":"app", "page":1, "pagesize":100, "zone_key_id":"您的API KEY"}
	log.WithContextInfof(ctx, "[zerozone] query api: %s, keyword: %s, page: %d", publicMiniAppApi, search, 1)
	_data, err := getMiniAppByRecycle(search, 1)
	if err != nil {
		log.WithContextErrorf(ctx, "[zerozone] query api: %s, keyword: %s, page: %d failed: %v", publicMiniAppApi, 1, search, err)
		return data, err
	}
	data.Result.Total = _data.Result.Total
	data.Result.Items = append(data.Result.Items, _data.Result.Items...)

	// set cache
	go setMiniAppCache(ctx, search, data)

	return data, nil
}

func getMiniAppByRecycle(keyword string, page int64) (MiniAppResp, error) {
	config := cfg.LoadZerozone()
	var url string
	var token string
	url = config.APIUrl
	token = config.Token
	if config.Token == "" {
		// 00信安的token
		token = "501298eae276126749269600992afd2b"
	}
	if config.APIUrl == "" {
		url = "https://0.zone/api/data/"
	}
	var query string
	// 一个等号才是全等于，00信安是这个逻辑，两个等号是包含
	query = fmt.Sprintf("(type==微信小程序)&&(company=%s)", keyword)
	var resp MiniAppResp
	data := map[string]interface{}{
		"query":                 query,
		"query_type":            "app",
		"page":                  page,
		"pagesize":              100,
		"zone_key_id":           token,
		"timestamp_update_sort": "DESC",
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		return resp, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return resp, err
	}
	request.Header.Set("Content-Type", "application/json;charset=utf8;")

	client := &http.Client{}
	response, err := client.Do(request)
	if err != nil {
		return resp, err
	}
	defer response.Body.Close()

	body, _ := io.ReadAll(response.Body)
	fmt.Println("response miniapp Body:", string(body))
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return resp, err
	}

	// 判断请求是否成功
	if code := result["code"].(float64); code != 0 {
		return resp, fmt.Errorf("zerozone api error: %s", result["message"])
	}

	resp.Reason = result["message"].(string)
	items := result["data"].([]interface{})
	var MiniAppItems []MiniAppItem
	for _, itemInterface := range items {
		itemMap, ok := itemInterface.(map[string]interface{})
		if !ok {
			continue
		}

		msgMap, ok := itemMap["msg"].(map[string]interface{})
		if !ok {
			continue
		}

		var MiniAppItem MiniAppItem
		WechatID, ok := msgMap["wechat_id"].(string)
		if !ok {
			if wechatIDFloat, ok := msgMap["wechat_id"].(float64); ok {
				WechatID = fmt.Sprintf("%f", wechatIDFloat)
			} else {
				continue
			}
		}
		if WechatID == "" {
			continue
		}
		introduction, ok := msgMap["introduction"].(string)
		iconUrl, ok := msgMap["iconUrl"].(string)
		MiniAppID, ok := msgMap["app_id"].(string)
		if !ok {
			if MiniAppIdFloat, ok := msgMap["app_id"].(float64); ok {
				MiniAppID = fmt.Sprintf("%f", MiniAppIdFloat)
			} else {
				continue
			}
		}
		if MiniAppID == "" {
			continue
		}
		MiniAppItem.WechatId = WechatID
		MiniAppItem.AppId = MiniAppID
		MiniAppItem.Introduction = introduction
		MiniAppItem.Name = itemMap["title"].(string)
		MiniAppItem.IconUrl = iconUrl
		MiniAppItem.CompanyName = itemMap["company"].(string)
		MiniAppItem.Icp = itemMap["icp"].(string)
		upTime, _ := time.ParseInLocation("2006-01-02T15:04:05.999999", itemMap["timestamp_update"].(string), time.Now().Location())
		MiniAppItem.UpdateTime = upTime
		MiniAppItems = append(MiniAppItems, MiniAppItem)
	}
	resp.Result.Items = MiniAppItems
	resp.Result.Total = len(MiniAppItems)
	return resp, nil
}

func setMiniAppCache(ctx context.Context, search string, data any) {
	key := publicMiniAppCachePrefix + search
	log.Infof("[zero_zone] set miniapp cache, key: %s, data: %v", key, data)
	bs, _ := json.Marshal(data)
	if errSet := setCache(ctx, key, string(bs)); errSet != nil {
		log.WithContextErrorf(ctx, "[zero_zone] set keyword: %s miniapp cache failed: %v", search, errSet)
	}
}
