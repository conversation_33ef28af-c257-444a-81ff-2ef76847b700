package zerozone

import (
	"context"
	"github.com/joho/godotenv"
	"micro-service/initialize/mysql"
	"micro-service/pkg/log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
)

func Test_WeChatAccount(t *testing.T) {
	godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	_, err := WeChatAccount(context.TODO(), "华夏银行股份有限公司")
	assert.Nil(t, err)
	time.Sleep(time.Second)
}

func Test_MiniApp(t *testing.T) {
	godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	_, err := MiniAppAccount(context.TODO(), "华夏银行股份有限公司")
	assert.Nil(t, err)
	time.Sleep(time.Second)
}
