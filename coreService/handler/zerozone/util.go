package zerozone

import (
	"context"
	"fmt"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

// tianyancha log keyword
// 0. log level: INFO
// 1. call api: "[0zone] query api:"
// 2. api cost: "[0zone] COST: query api:"
// 3. api may not cost: "[0zone] NOT-COST: query api:"

func costLog(ctx context.Context, cost bool, format string, args ...any) {
	title := utils.If(cost, "[0zone] COST:", "[0zone] NOT-COST:")
	p := fmt.Sprintf(format, args...)
	log.WithContextInfof(ctx, fmt.Sprintf("%s %s", title, p))
}
