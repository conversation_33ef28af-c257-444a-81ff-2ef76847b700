package zerozone

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"micro-service/pkg/cfg"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"net/http"
	"time"
)

const (
	publicWeChatApi         = "/api/data/"
	publicWeChatCachePrefix = cachePrefix + "wechat:"
)

func WeChatAccount(ctx context.Context, search string) ([]WeChatItem, error) {
	if search == "" {
		return nil, errx.ErrSearchCantEmpty
	}

	log.WithContextInfof(ctx, "[zerozone] get WeChat accounts, keyword: %s", search)
	var err error
	var data WeChatResp
	var key = publicWeChatCachePrefix + search
	if !getCache(ctx, key, &data) {
		log.WithContextInfof(ctx, "[zerozone] get key: %s cache is empty", key)
		data, err = getWeChatByApi(ctx, search)
		if err != nil {
			return nil, err
		}
	}
	return data.Result.Items, nil
}

type WeChatResp struct {
	RespBase
	Result WeChatResult `json:"result"`
}

type WeChatResult struct {
	Total int          `json:"total"`
	Items []WeChatItem `json:"items,omitempty"`
}

type WeChatItem struct {
	CodeImg     string    `json:"codeImg"`     // 二维码
	PublicNum   string    `json:"publicNum"`   // 公众号
	Title       string    `json:"title"`       // 公众号名称
	TitleImgURL string    `json:"titleImgURL"` // 封面图片
	Recommend   string    `json:"recommend"`   // 描述
	UpdateTime  time.Time `json:"updateTime"`  // 更新时间
}

func getWeChatByApi(ctx context.Context, search string) (WeChatResp, error) {
	rsp, err := _getWeChatByApi(ctx, search)
	return rsp, err
}

// 调用天眼查API获取微信公众号信息
func _getWeChatByApi(ctx context.Context, search string) (WeChatResp, error) {
	var data WeChatResp
	// {"query":"(type==微信公众号)&&(company==华顺信安信息技术有限公司)", "query_type":"app", "page":1, "pagesize":100, "zone_key_id":"您的API KEY"}
	log.WithContextInfof(ctx, "[zerozone] query api: %s, keyword: %s, page: %d", publicWeChatApi, search, 1)
	_data, err := getWeChatByRecycle(search, 1)
	if err != nil {
		log.WithContextErrorf(ctx, "[zerozone] query api: %s, keyword: %s, page: %d failed: %v", publicWeChatApi, 1, search, err)
		return data, err
	}
	data.Result.Total = _data.Result.Total
	data.Result.Items = append(data.Result.Items, _data.Result.Items...)

	// set cache
	go setWeChatCache(ctx, search, data)

	return data, nil
}

// 判断是否可以继续获取下一页
func wechatFetchNextPage(page, total int) bool {
	if total == 0 {
		return false
	}
	return total > page*20
}

func getWeChatByRecycle(keyword string, page int64) (WeChatResp, error) {
	config := cfg.LoadZerozone()
	var url string
	var token string
	url = config.APIUrl
	token = config.Token
	if config.Token == "" {
		// 00信安的token
		token = "501298eae276126749269600992afd2b"
	}
	if config.APIUrl == "" {
		url = "https://0.zone/api/data/"
	}
	var query string
	query = fmt.Sprintf("(type=微信公众号)&&(company=%s)", keyword)
	var resp WeChatResp
	data := map[string]interface{}{
		"query":                 query,
		"query_type":            "app",
		"page":                  page,
		"pagesize":              100,
		"zone_key_id":           token,
		"timestamp_update_sort": "DESC",
	}
	jsonData, err := json.Marshal(data)
	if err != nil {
		return resp, err
	}

	request, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return resp, err
	}
	request.Header.Set("Content-Type", "application/json;charset=utf8;")

	client := &http.Client{}
	response, err := client.Do(request)
	if err != nil {
		return resp, err
	}
	defer response.Body.Close()

	body, _ := io.ReadAll(response.Body)
	fmt.Println("response Body:", string(body))
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return resp, err
	}

	// 判断请求是否成功
	if code := result["code"].(float64); code != 0 {
		return resp, fmt.Errorf("zerozone api error: %s", result["message"])
	}

	resp.Reason = result["message"].(string)
	items := result["data"].([]interface{})
	var WeChatItems []WeChatItem
	for _, itemInterface := range items {
		itemMap, ok := itemInterface.(map[string]interface{})
		if !ok {
			continue
		}

		msgMap, ok := itemMap["msg"].(map[string]interface{})
		if !ok {
			continue
		}

		var WeChatItem WeChatItem

		wechatID, ok := msgMap["wechat_id"].(string)
		if !ok {
			if wechatIDFloat, ok := msgMap["wechat_id"].(float64); ok {
				wechatID = fmt.Sprintf("%f", wechatIDFloat)
			} else {
				continue
			}
		}
		if wechatID == "" {
			continue
		}
		introduction, ok := msgMap["introduction"].(string)
		if !ok {
			continue
		}
		WeChatItem.CodeImg = wechatID
		WeChatItem.PublicNum = wechatID
		WeChatItem.Recommend = introduction
		WeChatItem.Title = itemMap["title"].(string)
		upTime, err := time.ParseInLocation("2006-01-02T15:04:05.999999", itemMap["timestamp_update"].(string), time.Now().Location())
		if err != nil {
			log.Errorf("[zerozone] parse wechat update time failed: %v", err)
			continue
		}
		WeChatItem.UpdateTime = upTime

		WeChatItems = append(WeChatItems, WeChatItem)
	}
	resp.Result.Items = WeChatItems
	resp.Result.Total = len(WeChatItems)
	return resp, nil
}

func setWeChatCache(ctx context.Context, search string, data any) {
	key := publicWeChatCachePrefix + search
	log.Infof("[tianyacha] set wechat cache, key: %s, data: %v", key, data)
	bs, _ := json.Marshal(data)
	if errSet := setCache(ctx, key, string(bs)); errSet != nil {
		log.WithContextErrorf(ctx, "[tianyancha] set keyword: %s wechat cache failed: %v", search, errSet)
	}
}
