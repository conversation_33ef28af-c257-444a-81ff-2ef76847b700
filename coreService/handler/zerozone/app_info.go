package zerozone

import (
	"context"
	"encoding/json"
	"fmt"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql/apps"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
	"strings"
	"time"
)

type ZZAppResponse struct {
	Code     int         `json:"code"`
	Message  string      `json:"message"`
	Page     interface{} `json:"page"`
	Next     int64       `json:"next"`
	Pagesize int         `json:"pagesize"`
	Total    int         `json:"total"`
	Data     []struct {
		Id              string  `json:"_id"`
		Sort            []int64 `json:"sort"`
		Title           string  `json:"title"`
		Company         string  `json:"company"`
		TimestampUpdate string  `json:"timestamp_update"`
		Type            string  `json:"type"`
		Timestamp       string  `json:"timestamp"`
		Msg             struct {
			WechatFakeid string `json:"wechat_fakeid"`
			WechatId     string `json:"wechat_id"`
			IconUrl      string `json:"iconUrl"`
			Introduction string `json:"introduction"`
			ServiceType  int    `json:"service_type"`
			Code         string `json:"code"`
			AppUrl       string `json:"app_url"`
		} `json:"msg"`
	} `json:"data"`
}

func FetchZeroZoneApps(ctx context.Context, keyword string, isFilter bool) ([]apps.Apps, error) {
	config := cfg.LoadZerozone()
	var url string
	var token string
	url = config.APIUrl
	token = config.Token
	if config.Token == "" {
		// 00信安的token
		token = "501298eae276126749269600992afd2b"
	}
	if config.APIUrl == "" {
		// 00信安的api_url
		url = "https://0.zone/api/data/"
	}
	requestBody, _ := json.Marshal(map[string]any{
		"query":                 fmt.Sprintf("(type==安卓APK)&&(company=%s)", keyword),
		"query_type":            "app",
		"page":                  1,
		"pagesize":              100,
		"zone_key_id":           token,
		"timestamp_update_sort": "DESC",
	})
	param := &crawlerPb.GetRequest{
		Method:  crawlerPb.MethodCurlPost,
		Url:     url,
		Body:    string(requestBody),
		Headers: map[string]string{"Content-Type": "application/json"},
	}
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return nil, err
	}
	body := crawlerPb.DecodeBy(crawlerRes.Body)
	var result ZZAppResponse
	_ = json.Unmarshal([]byte(body), &result)
	if result.Code != 0 {
		return nil, fmt.Errorf("zerozone api error: %s", result.Message)
	}
	if result.Total == 0 {
		return nil, nil
	}

	l := result.Data
	appItems := make([]apps.Apps, 0, len(l))
	for i := range l {
		if isFilter {
			l[i].Company = strings.ReplaceAll(l[i].Company, "（", "(")
			l[i].Company = strings.ReplaceAll(l[i].Company, "）", ")")
			keyword = strings.ReplaceAll(keyword, "（", "(")
			keyword = strings.ReplaceAll(keyword, "）", ")")
			if !strings.Contains(l[i].Company, keyword) {
				continue
			}
		}
		bs, _ := json.Marshal(l[i])
		update, _ := time.ParseInLocation("2006-01-02T15:04:05.999999", l[i].TimestampUpdate, time.Now().Location())
		appItems = append(appItems, apps.Apps{
			Name:          l[i].Title,
			Logo:          l[i].Msg.IconUrl,
			Url:           l[i].Msg.AppUrl,
			Category:      "",
			CompanyName:   l[i].Company,
			Platform:      apps.PlatformAndroid,
			Developer:     l[i].Company,
			Product:       "zerozone",
			YybOriginInfo: string(bs),
			Area:          "",
			IsOnline:      1,
			UpdateTime:    update,
		})
	}
	return appItems, nil
}

func FetchZeroZoneIOSApps(ctx context.Context, keyword string, isFilter bool) ([]apps.Apps, error) {
	config := cfg.LoadZerozone()
	var url string
	var token string
	url = config.APIUrl
	token = config.Token
	if config.Token == "" {
		// 00信安的token
		token = "501298eae276126749269600992afd2b"
	}
	if config.APIUrl == "" {
		// 00信安的api_url
		url = "https://0.zone/api/data/"
	}
	requestBody, _ := json.Marshal(map[string]any{
		"query":                 fmt.Sprintf("(type==iOS)&&(company=%s)", keyword),
		"query_type":            "app",
		"page":                  1,
		"pagesize":              100,
		"zone_key_id":           token,
		"timestamp_update_sort": "DESC",
	})
	param := &crawlerPb.GetRequest{
		Method:  crawlerPb.MethodCurlPost,
		Url:     url,
		Body:    string(requestBody),
		Headers: map[string]string{"Content-Type": "application/json"},
	}
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return nil, err
	}
	body := crawlerPb.DecodeBy(crawlerRes.Body)
	var result ZZAppResponse
	_ = json.Unmarshal([]byte(body), &result)
	if result.Code != 0 {
		return nil, fmt.Errorf("zerozone api error: %s", result.Message)
	}
	if result.Total == 0 {
		return nil, nil
	}

	l := result.Data
	appItems := make([]apps.Apps, 0, len(l))
	for i := range l {
		if isFilter {
			l[i].Company = strings.ReplaceAll(l[i].Company, "（", "(")
			l[i].Company = strings.ReplaceAll(l[i].Company, "）", ")")
			keyword = strings.ReplaceAll(keyword, "（", "(")
			keyword = strings.ReplaceAll(keyword, "）", ")")
			if !strings.Contains(l[i].Company, keyword) {
				continue
			}
		}
		bs, _ := json.Marshal(l[i])
		update, _ := time.ParseInLocation("2006-01-02T15:04:05.999999", l[i].TimestampUpdate, time.Now().Location())
		appItems = append(appItems, apps.Apps{
			Name:          l[i].Title,
			Logo:          l[i].Msg.IconUrl,
			Url:           l[i].Msg.AppUrl,
			Category:      "",
			CompanyName:   l[i].Company,
			Platform:      apps.PlatformApple,
			Developer:     l[i].Company,
			Product:       "zerozone",
			YybOriginInfo: string(bs),
			Area:          "",
			IsOnline:      1,
			UpdateTime:    update,
		})
	}
	return appItems, nil
}
