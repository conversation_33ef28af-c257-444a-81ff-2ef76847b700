package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	hDiscover "micro-service/coreService/handler/discover"
	"micro-service/coreService/handler/recommend"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/cloud_assets"
	"micro-service/middleware/mysql/discover"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
)

// DiscoverCreateTask 创建资产发现任务
func (e *Core) DiscoverCreateTask(ctx context.Context, req *pb.DiscoverCreateTaskRequest, rsp *pb.DiscoverCreateTaskResponse) error {
	_, err := hDiscover.GetTaskInstance().CreateTask(ctx, req, rsp)
	if err != nil {
		return err
	}
	return nil
}

// DiscoverTaskProcess 资产发现任务进度查询
func (e *Core) DiscoverTaskProcess(ctx context.Context, req *pb.DiscoverTaskRequest, rsp *pb.DiscoverTaskProcessResponse) error {
	task, err := hDiscover.GetTaskInstance().GetTaskProcess(ctx, req.CompanyName, req.Model)
	if err != nil {
		return err
	}
	rsp.Process = cast.ToFloat32(task.Process)
	if task.Reason != "" {
		return errors.New(task.Reason)
	}
	return nil
}

// DiscoverTaskClues 资产发现任务线索
func (e *Core) DiscoverTaskClues(_ context.Context, req *pb.DiscoverTaskCluesRequest, rsp *pb.ClueListResponse) error {
	if req.CompanyName == "" {
		return fmt.Errorf("获取任务信息失败")
	}
	task, err := discover.NewTaskModel().First(
		mysql.WithWhere("company_name", req.CompanyName),
		mysql.WithWhere("type", req.Model),
	)
	if err != nil {
		return fmt.Errorf("获取任务信息失败:%s", err.Error())
	}
	var clues []general_clues.GeneralClues
	var total int64
	taskClues, _, err := discover.NewTaskClueModel().List(0, 0, mysql.WithWhere("task_id", task.Id))
	if err != nil {
		return fmt.Errorf("获取任务线索失败:%s", err.Error())
	}
	clueIds := utils.ListColumn(taskClues, func(clue discover.TaskClue) any {
		return clue.ClueId
	})
	if len(clueIds) == 0 {
		clues, total, err = make([]general_clues.GeneralClues, 0), 0, nil
	} else {
		clues, total, err = general_clues.NewGeneralCluesModel().List(
			cast.ToInt(req.Page), cast.ToInt(req.PerPage), mysql.WithValuesIn("id", clueIds),
		)
	}
	if err != nil {
		return err
	}
	rspClues := make([]*pb.ClueInfo, 0)
	for i := range clues {
		clInfo := &pb.ClueInfo{
			Id:          clues[i].Id,
			Type:        cast.ToUint32(clues[i].Type),
			CompanyName: clues[i].CompanyName,
			Content:     clues[i].Content,
			Hash:        clues[i].Hash,
			Platform:    clues[i].Platform,
			Source:      clues[i].Source,
			CertValid:   cast.ToUint32(clues[i].CertValid),
			Confirmed:   cast.ToInt32(clues[i].Confirmed),
			UpdatedAt:   clues[i].UpdatedAt.Format("2006-01-02 15:04:05"),
			CreatedAt:   clues[i].CreatedAt.Format("2006-01-02 15:04:05"),
		}
		// 更新ICON地址
		if clInfo.Type == general_clues.ClueTypeIcon {
			clInfo.Content = storage.GenAPIDownloadPath("favicon.ico", clInfo.Content)
		}
		rspClues = append(rspClues, clInfo)
	}
	rsp.CurrentPage = req.Page
	rsp.PerPage = req.PerPage
	rsp.Total = total
	rsp.Items = rspClues
	return nil
}

// DiscoverTaskResult 资产发现任务结果
func (e *Core) DiscoverTaskResult(_ context.Context, req *pb.DiscoverTaskResultRequest, rsp *pb.DiscoverTaskResultResponse) error {
	if req.CompanyName == "" {
		return fmt.Errorf("获取任务信息失败")
	}
	task, err := discover.NewTaskModel().First(
		mysql.WithWhere("company_name", req.CompanyName),
		mysql.WithWhere("type", req.Model),
	)
	if err != nil {
		return fmt.Errorf("获取任务信息失败:%s", err.Error())
	}
	rTaskResult := &pb.RecommendResultResponse{}
	clueHashs := make([]string, 0)
	// 获取线索HASH
	if len(req.ClueId) != 0 {
		list, _, lErr := general_clues.NewGeneralCluesModel().List(0, 0, mysql.WithValuesIn("id", req.ClueId))
		if lErr != nil {
			return fmt.Errorf("过滤指定线索失败:%s", lErr.Error())
		}
		for i := range list {
			clueHashs = append(clueHashs, cloud_assets.GetClueHash(list[i].Content, list[i].Hash, list[i].Type))
		}
	}
	if rErr := recommend.GetRecommendResult(context.TODO(), &pb.RecommendResultRequest{
		TaskId: []uint64{task.ReTaskId}, Page: req.Page, PerPage: req.PerPage, Fields: req.Fields, Level: req.Level,
		ClueHash: clueHashs, Domain: req.Domain, Ip: req.Ip, Port: req.Port, IsIpv6: req.IsIpv6, IsWildcardDomain: req.IsWildcardDomain,
	}, rTaskResult); rErr != nil {
		return rErr
	}
	rsp.PerPage = rTaskResult.PerPage
	rsp.CurrentPage = rTaskResult.CurrentPage
	rsp.Total = rTaskResult.Total
	clues := hDiscover.GetTaskInstance().GetTaskClueTree(task.Id)
	for i := range rTaskResult.Items {
		asset := &pb.DiscoverAsset{}
		t, _ := json.Marshal(rTaskResult.Items[i])
		_ = json.Unmarshal(t, asset)
		asset.Hash = cast.ToString(rTaskResult.Items[i].Hash)
		asset.Clues = nil
		clueUnique := make([]uint64, 0)
		// 获取线索链
		if len(rTaskResult.Items[i].Clues) != 0 {
			for x := range rTaskResult.Items[i].Clues {
				if clue := hDiscover.GetTaskInstance().GetTaskClue(clues, rTaskResult.Items[i].Clues[x]); clue != nil {
					if !utils.ListContains(clueUnique, clue.Id) {
						clueUnique = append(clueUnique, clue.Id)
						asset.Clues = append(asset.Clues, clue)
					}
				} else {
					log.Infof(fmt.Sprintf("clue:%+v,未取到线索线索信息", rTaskResult.Items[i].Clues[x]))
				}
			}
		}
		rsp.Items = append(rsp.Items, asset)
	}
	return nil
}
