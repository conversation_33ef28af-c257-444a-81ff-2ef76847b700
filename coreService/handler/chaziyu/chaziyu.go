package chaziyu

import (
	"context"
	"encoding/json"
	"fmt"
	corePb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"

	"github.com/antchfx/htmlquery"
)

func QueryDomain(ctx context.Context, rsp *corePb.SubdomainResponse, domain string) error {
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    fmt.Sprintf("https://chaziyu.com/%s/", domain),
		Method: crawlerPb.MethodCurlGet,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return err
	}
	log.WithContextInfof(ctx, "crawler Chaziyu domain-> domain:%s,code:%d,size:%d", domain, crawlerRes.Code, len(crawlerRes.Body))
	// Load the HTML document
	doc, err := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
	if err != nil {
		log.WithContextInfof(ctx, "crawler Chaziyu-有报错信息1-> domain:%s,body:%s,err:%s", domain, crawlerPb.DecodeBy(crawlerRes.Body), err)
		return nil
	}
	list, err := htmlquery.QueryAll(doc, "//table[1]/tbody/tr")
	if err != nil {
		log.WithContextInfof(ctx, "crawler Chaziyu-有报错信息2-> domain:%s,body:%s,err:%s", domain, crawlerPb.DecodeBy(crawlerRes.Body), err)
		return nil
	}
	for _, n := range list {
		a := htmlquery.FindOne(n, "//td[2]")
		if a != nil {
			var subdomain = htmlquery.InnerText(htmlquery.FindOne(n, "//td[2]"))
			log.WithContextInfof(ctx, "Received Core.CompanyName 我来循环添加子域名数据啊啊: %v", subdomain)
			rsp.Subdomains = append(rsp.Subdomains, subdomain)
		}
	}
	list2, err := htmlquery.QueryAll(doc, "//table[2]/tbody/tr")
	if err != nil {
		log.WithContextInfof(ctx, "crawler Chaziyu-有报错信息3-> domain:%s,body:%s,err:%s", domain, crawlerPb.DecodeBy(crawlerRes.Body), err)
		return nil
	}

	for _, m := range list2 {
		a2 := htmlquery.FindOne(m, "//td[2]")
		log.WithContextInfof(ctx, "crawler Chaziyu-打印4-> domain:%s", domain)
		if a2 != nil {
			var subdomain2 = htmlquery.InnerText(htmlquery.FindOne(m, "//td[2]"))
			log.WithContextInfof(ctx, "Received Core.CompanyName request2222: %v", subdomain2)
			rsp.Subdomains = append(rsp.Subdomains, subdomain2)
		}
	}

	// 翻页数据请求接口
	page := 2
	for {
		curlRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
			Url:    fmt.Sprintf("https://chaziyu.com/ipchaxun.do?domain=%s&page=%d", domain, page),
			Method: crawlerPb.MethodCurlGet,
		}, utils.SetRpcTimeoutOpt(30))
		if err != nil {
			return nil
		}
		log.WithContextInfof(ctx, "crawler Chaziyu-ipchaxun domain-> domain:%s,code:%d,size:%d", domain, curlRes.Code, len(curlRes.Body))
		// Load the HTML document
		body4 := crawlerPb.DecodeBy(curlRes.Body)
		if body4 == "" {
			log.WithContextInfof(ctx, "crawler Chaziyu-ipchaxun domain-> domain:%s,code:%d,size:%d", domain, curlRes.Code, len(curlRes.Body))
			return nil
		}
		if curlRes.Code == 200 {
			var result map[string]interface{}
			err := json.Unmarshal([]byte(body4), &result)
			if err != nil {
				log.WithContextInfof(ctx, "crawler Chaziyu-ipchaxun domain-json解析错误-> domain:%s,body:%s,url:%s", domain, body4, fmt.Sprintf("https://chaziyu.com/ipchaxun.do?domain=%s&page=%d", domain, page))
				return nil
			}
			data := result["data"]
			a := data.(map[string]interface{})
			domainList := a["result"]

			if len(domainList.([]interface{})) == 0 {
				log.WithContextInfof(ctx, "当前页码没数据了page%d", page)
				break
			}
			log.WithContextInfof(ctx, "chaziyu-ipchaxun当前页码有数据page%d", page)
			for _, v := range domainList.([]interface{}) {
				log.WithContextInfof(ctx, "Received Core.CompanyName request3333: %v", v.(string))
				rsp.Subdomains = append(rsp.Subdomains, v.(string))
			}
		} else {
			log.WithContextInfof(ctx, "chaziyu-ipchaxun服务器返回状态码不是200,跳出for循环,page:%d", page)
			break
		}
		page++
	}

	// 翻页数据请求接口
	pageB := 2
	for {
		curlRes2, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
			Url:    fmt.Sprintf("https://chaziyu.com/zhaokuaizhao.do?domain=%s&page=%d", domain, pageB),
			Method: crawlerPb.MethodCurlGet,
		}, utils.SetRpcTimeoutOpt(30))
		if err != nil {
			return err
		}
		log.WithContextInfof(ctx, "crawler Chaziyu-zhaokuaizhao domain-> domain:%s,code:%d,size:%d", domain, curlRes2.Code, len(curlRes2.Body))
		// Load the HTML document
		body5 := crawlerPb.DecodeBy(curlRes2.Body)
		if body5 == "" {
			return nil
		}
		if curlRes2.Code == 200 {
			var result2 map[string]interface{}
			err := json.Unmarshal([]byte(body5), &result2)
			if err != nil {
				log.WithContextInfof(ctx, "crawler Chaziyu-ipchaxun domain-json解析错误-> domain:%s,body:%s,url:%s", domain, body5, fmt.Sprintf("https://chaziyu.com/zhaokuaizhao.do?domain=%s&page=%d", domain, pageB))
				return nil
			}
			data2 := result2["data"]
			a2 := data2.(map[string]interface{})
			domainList2 := a2["result"]
			if len(domainList2.([]interface{})) == 0 {
				log.WithContextInfof(ctx, "当前页码没数据了pageB:%d", pageB)
				break
			}
			log.WithContextInfof(ctx, "chaziyu-zhaokuaizhao当前页码有数据page%d", pageB)
			for _, v2 := range domainList2.([]interface{}) {
				log.WithContextInfof(ctx, "Received Core.CompanyName request44444: %v", v2.(string))
				rsp.Subdomains = append(rsp.Subdomains, v2.(string))
			}
		} else {
			log.WithContextInfof(ctx, "chaziyu-zhaokuaizhao服务器返回状态码不是200,跳出for循环,pageB:%d", pageB)
			break
		}
		pageB++
	}
	return nil
}
