package handler

import (
	"context"

	"go-micro.dev/v4/errors"

	"micro-service/coreService/handler/icp"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/log"
)

func (e *Core) Icp(ctx context.Context, req *pb.IcpRequest, rsp *pb.IcpResponse) error {
	return icp.Icp(ctx, req, rsp)
}

func (e *Core) IcpByChild(ctx context.Context, req *pb.IcpRequest, rsp *pb.IcpResponse) error {
	return icp.GetICPByChild(ctx, req, rsp)
}

func (e *Core) CompanyName(ctx context.Context, req *pb.IcpCompanyNameRequest, rsp *pb.IcpResponse) error {
	return icp.CompanyName(ctx, req, rsp)
}

func (e *Core) Domain(ctx context.Context, req *pb.IcpDomainRequest, rsp *pb.IcpResponse) error {
	return icp.Domain(ctx, req, rsp)
}

func (e *Core) DomainOnly(ctx context.Context, req *pb.IcpDomainRequest, rsp *pb.IcpResponse) error {
	return icp.DomainOnly(ctx, req, rsp)
}

func (e *Core) IcpAppByIcp(ctx context.Context, req *pb.IcpAppRequest, rsp *pb.IcpAppResponse) error {
	log.WithContextInfof(ctx, "[ICP-APP] Received core.IcpAppByIcp param: %v", req)
	err := icp.AppByIcp(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[ICP-APP] Request core.IcpAppByIcp failed, param: %v, caused by %v", req, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) IcpAppByCompanyName(ctx context.Context, req *pb.IcpAppRequest, rsp *pb.IcpAppResponse) error {
	log.WithContextInfof(ctx, "[ICP-APP] Received core.IcpAppByCompanyName param: %v", req)
	err := icp.AppByCompanyName(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[ICP-APP] Request core.IcpAppByCompanyName failed, param: %v, caused by %v", req, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (e *Core) IcpAppByAppName(ctx context.Context, req *pb.IcpAppRequest, rsp *pb.IcpAppResponse) error {
	log.WithContextInfof(ctx, "[ICP-APP] Received core.IcpAppByAppName param: %v", req)
	err := icp.AppByAppName(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[ICP-APP] Request core.IcpAppByAppName failed, param: %v, caused by %v", req, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}
