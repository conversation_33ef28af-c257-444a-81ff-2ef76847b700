package vuln

// cpe format => cpe:2.3:part:vendor:product:version:update:edition:lang:sw_edition:target_sw:target_hw:other
// References:
// 		1. https://nvd.nist.gov/vuln/vulnerability-detail-pages#:~:text=A%20CPE%20Match%20string%20is,in%20the%20dictionary%20that%20match.
// 		2. https://cpe.mitre.org/specification/CPE_2.3_for_ITSAC_Nov2011.pdf
//		3. https://cpe.mitre.org/specification/

import (
	"context"
	"fmt"
	"io"
	"strconv"
	"strings"

	"github.com/dlclark/regexp2"

	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/middleware/elastic/cvevulns"
	mysqllib "micro-service/middleware/mysql"
	afp "micro-service/middleware/mysql/asset_fingerprint"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

type VulnCVEMatchUnit struct {
	Ip                   string // 资产IP
	Port                 string // 资产端口
	cvevulns.CVEMetaData        // CVE元数据
}

type AssetMetadata struct {
	Ip       string
	Port     string
	Banner   string // banner(first) OR header(second)
	Protocol string
	cpe      string // cpe for banner matched
}

const emptyProtocol = "empty-protocol"

// GetVulnsWithHB return vuln list with assets header or banner.
func GetVulnsWithHB(ctx context.Context, list []*AssetMetadata) ([]*VulnCVEMatchUnit, error) {
	if len(list) == 0 {
		return nil, nil
	}

	protocols := distinctProtocols(list)                   // 获取去重的协议
	fingerprintMap, err := getFingerprintMap(protocols...) // 获取资产指纹
	if err != nil {
		return nil, err
	}

	cpeList := make([]*AssetMetadata, 0, len(list))
	for i := range list {
		if list[i].Banner == "" {
			continue
		}

		protocol := utils.If(list[i].Protocol != "", list[i].Protocol, emptyProtocol)
		protocolCpes := fingerprintMap[protocol]
		cpe := cpeMatch(list[i].Banner, protocolCpes)
		if cpe != "" {
			list[i].cpe = cpe
			cpeList = append(cpeList, list[i])
		}
	}

	// get vulns with asset matched cpe info
	vulns, err := getVulnWithCpes(ctx, cpeList)
	if err != nil {
		return nil, err
	}

	return vulns, nil
}

func distinctProtocols(l []*AssetMetadata) []string {
	list := make([]string, 0, len(l))
	for i := range l {
		if l[i].Protocol != "" {
			list = append(list, l[i].Protocol)
		}
	}
	return utils.ListDistinct(list)
}

type fingerprintUnit struct {
	regCompile   regexp2.Regexp
	cpeStringAll string // table asset_fingerprint column: cpe_string_all
	protocol     string // table asset_fingerprint column: service
}

func cpeMatch(data string, l []fingerprintUnit) (cpeString string) {
	for i := range l {
		matchList, _ := matchList(data, &l[i].regCompile)
		if len(matchList) == 0 {
			continue
		}

		cpeString = l[i].cpeStringAll
		if strings.Contains(l[i].cpeStringAll, "$") {
			cpeString = getCpeWithSubst(l[i].cpeStringAll, matchList)
		}

		break
	}

	return cpeString
}

func getCpeWithSubst(s string, m map[string]string) (cpeString string) {
	var regData []string // 匹配出来的$序列号
	// "cpe:2.3:a:goyal:caliber:$2.$3.$4" => ["cpe:2.3:a:goyal:caliber:", "2.", "3.", "4"]
	splitList := strings.Split(s, "$")
	for i, v := range splitList {
		if i == 0 {
			continue
		}
		if strings.Contains(v, "SUBST") {
			if numStr := abstractSubstNum(v); numStr != "" {
				// 5,SUBST(5,"_",".")
				// regData = append(regData, numStr+fmt.Sprintf(`%s,$SUBST(%s,"_",".")`, numStr, numStr)) // origin
				regData = append(regData, fmt.Sprintf(`%s,$SUBST(%s,"_",".")`, numStr, numStr))
			}
		} else {
			regData = append(regData, v[0:1]) // 拿到$后的数字
		}
	}

	// cpe替换$n的版本号信息
	for _, v := range regData {
		version, ok := m[v] // ?
		if !ok {
			continue
		}
		if strings.Contains(v, "$SUBST") {
			itemStmp := strings.Split(v, ",")
			oldStr := itemStmp[2][1:2]
			newStr := itemStmp[3][1:2]
			str := strings.ReplaceAll(version, oldStr, newStr)
			cpeString = strings.ReplaceAll(s, v[2:], str)
		} else {
			cpeString = strings.ReplaceAll(s, "$"+v, version)
		}
	}

	return cpeString
}

// SUBST(2,"_",".") => 2
func abstractSubstNum(s string) (str string) {
	index1 := strings.Index(s, "SUBST(") + 5
	index2 := strings.Index(s, ",")

	if index2-index1 >= 1 {
		str = s[index1+1 : index2]
	}

	return str
}

// 正则的匹配结果
func matchList(s string, re *regexp2.Regexp) (l map[string]string, err error) {
	l = make(map[string]string, 0)
	var match *regexp2.Match
	match, err = re.FindStringMatch(s)

	index := 1 // 正则的第几个匹配项
	for {
		if match == nil || err != nil {
			break
		}
		l[strconv.Itoa(index)] = match.String()
		match, err = re.FindNextMatch(match)
		index++
	}

	if err != nil {
		return nil, err
	}
	return l, nil
}

// 根据cpe列表获取CVE ID数据
func getVulnWithCpes(ctx context.Context, cpes []*AssetMetadata) (vulns []*VulnCVEMatchUnit, err error) {
	if len(cpes) == 0 {
		return nil, nil
	}

	esConn := cvevulns.NewCVEVulnerModel(es.GetInstance())

	const batchSize = 100
	var batchCnt int
	batchCpe := make([]string, 0, batchSize)
	index := 0
	for i, v := range cpes {
		index++

		batchCpe = append(batchCpe, v.cpe)
		if !(index%batchSize == 0 || i == len(cpes)-1) {
			continue
		}

		result, err := esConn.GetCVEIdsWithCpes(batchCpe)
		batchCpe = make([]string, 0, batchSize)
		batchCnt++
		if len(result) == 0 {
			if err != nil && err != io.EOF {
				log.WithContextErrorf(ctx, "[漏洞匹配]GetCVEIdsWithCpes from ES IndexName: %+v", err)
			}
			continue
		}
		start := utils.If(index-batchSize < 0, 0, (batchCnt-1)*batchSize)
		for x := start; x < index; x++ {
			for _, v := range result {
				if !strings.HasPrefix(v.CpeUri, cpes[x].cpe) {
					continue
				}
				vulns = append(vulns, &VulnCVEMatchUnit{
					Ip:   cpes[x].Ip,
					Port: cpes[x].Port,
					CVEMetaData: cvevulns.CVEMetaData{
						CVEId:         v.CVEId,
						CvssVersion:   v.CvssVersion,
						Level:         v.Level,
						Score:         v.Score,
						Description:   v.Description,
						PublishedDate: v.PublishedDate,
					},
				})
			}
		}
	}

	return vulns, nil
}

func findFingerprints(protocols ...string) ([]afp.AssetFingerprint, error) {
	conn := afp.NewDBClient(mysql.GetInstance())
	handles := []mysqllib.HandleFunc{mysqllib.WithSelect("service,match_string,cpe_string_all")}
	handles = append(handles, mysqllib.WithWhere("match_string != ? AND cpe_string_all != ?", "", ""))
	if len(protocols) > 0 {
		handles = append(handles, mysqllib.WithValuesIn("service", protocols))
	}

	list, err := conn.List(handles...)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func getFingerprintMap(protocols ...string) (map[string][]fingerprintUnit, error) {
	m := make(map[string][]fingerprintUnit)

	list, err := findFingerprints(protocols...)
	if err != nil {
		return nil, fmt.Errorf("can't get fingerprint data from mysql: %+v", err)
	}

	protocol := ""
	for i := range list {
		protocol = utils.If(list[i].Service != "", list[i].Service, emptyProtocol)
		m[protocol] = append(m[protocol], fingerprintUnit{
			regCompile:   *regexp2.MustCompile(list[i].MatchString, regexp2.RE2),
			protocol:     list[i].Service,
			cpeStringAll: list[i].CpeStringAll,
		})
	}

	return m, nil
}
