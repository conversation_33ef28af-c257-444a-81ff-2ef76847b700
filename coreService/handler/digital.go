package handler

import (
	"context"
	"micro-service/coreService/handler/digital"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/apps"
	"micro-service/middleware/mysql/official_account"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"time"

	"github.com/spf13/cast"
	"go-micro.dev/v4/errors"
)

func (e *Core) DigitalWechatList(_ context.Context, req *pb.DigitalWechatRequest, rsp *pb.DigitalWechatResponse) error {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLike("name", "%"+req.Name+"%"))
	}
	if req.Platform != "" {
		handlers = append(handlers, mysql.WithColumnValue("platform", req.Platform))
	}
	if req.Describe != "" {
		handlers = append(handlers, mysql.WithColumnValue("describe", "%"+req.Describe+"%"))
	}
	if req.Account != "" {
		handlers = append(handlers, mysql.WithColumnValue("account", "%"+req.Account+"%"))
	}
	if req.CompanyName != "" {
		handlers = append(handlers, mysql.WithColumnValue("company_name", "%"+req.CompanyName+"%"))
	}
	if len(req.CreatedAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("created_at >= ?", req.CreatedAt[0]))
	}
	if len(req.CreatedAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("created_at >= ?", req.CreatedAt[0]))
		handlers = append(handlers, mysql.WithWhere("created_at <= ?", req.CreatedAt[1]))
	}
	if len(req.UpdatedAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("updated_at >= ?", req.UpdatedAt[0]))
	}
	if len(req.UpdatedAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("updated_at >= ?", req.UpdatedAt[0]))
		handlers = append(handlers, mysql.WithWhere("updated_at <= ?", req.UpdatedAt[1]))
	}
	list, total, err := official_account.NewOfficialAccountModel().List(int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}
	for i := range list {
		item := &pb.Wechat{
			Id:          list[i].Id,
			Name:        list[i].Name,
			CompanyName: list[i].CompanyName,
			Qrcode:      list[i].QRCode,
			Account:     list[i].Account,
			Platform:    list[i].Platform,
			IsOnline:    cast.ToUint32(list[i].IsOnline),
			Describe:    list[i].Describe,
			CreatedAt:   list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   list[i].UpdatedAt.Format(utils.DateTimeLayout),
			UpdateTime:  list[i].UpdateTime.Format(utils.DateTimeLayout),
		}
		rsp.Items = append(rsp.Items, item)
		rsp.CurrentPage = int64(req.Page)
		rsp.PerPage = req.PerPage
	}
	rsp.Total = total
	return nil
}

func (e *Core) DigitalAppsList(_ context.Context, req *pb.DigitalAppsRequest, rsp *pb.DigitalAppsResponse) error {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLike("name", "%"+req.Name+"%"))
	}
	if req.Category != "" {
		handlers = append(handlers, mysql.WithColumnValue("category", req.Category))
	}
	if req.Platform != "" {
		handlers = append(handlers, mysql.WithColumnValue("platform", req.Platform))
	}
	if req.Area != "" {
		handlers = append(handlers, mysql.WithColumnValue("area", req.Area))
	}
	if req.Developer != "" {
		handlers = append(handlers, mysql.WithColumnValue("developer", "%"+req.Developer+"%"))
	}
	if req.CompanyName != "" {
		handlers = append(handlers, mysql.WithColumnValue("company_name", "%"+req.CompanyName+"%"))
	}
	if len(req.CreatedAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("created_at >= ?", req.CreatedAt[0]))
	}
	if len(req.CreatedAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("created_at >= ?", req.CreatedAt[0]))
		handlers = append(handlers, mysql.WithWhere("created_at <= ?", req.CreatedAt[1]))
	}
	if len(req.UpdatedAt) == 1 {
		handlers = append(handlers, mysql.WithWhere("updated_at >= ?", req.UpdatedAt[0]))
	}
	if len(req.UpdatedAt) == 2 {
		handlers = append(handlers, mysql.WithWhere("updated_at >= ?", req.UpdatedAt[0]))
		handlers = append(handlers, mysql.WithWhere("updated_at <= ?", req.UpdatedAt[1]))
	}
	list, total, err := apps.NewAppsModel().List(int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}
	for i := range list {
		item := &pb.Appstore{
			Id:          list[i].Id,
			Name:        list[i].Name,
			CompanyName: list[i].CompanyName,
			Logo:        list[i].Logo,
			Url:         list[i].Url,
			Platform:    list[i].Platform,
			Product:     list[i].Product,
			Category:    list[i].Category,
			Area:        list[i].Area,
			IsOnline:    cast.ToInt32(list[i].IsOnline),
			Developer:   list[i].Developer,
			CreatedAt:   list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   list[i].UpdatedAt.Format(utils.DateTimeLayout),
			UpdateTime:  list[i].UpdateTime.Format(utils.DateTimeLayout),
		}
		rsp.Items = append(rsp.Items, item)
		rsp.CurrentPage = int64(req.Page)
		rsp.PerPage = req.PerPage
	}
	rsp.Total = total
	return nil
}

func (e *Core) DigitalAppstore(ctx context.Context, req *pb.DigitalCompanyNameRequest, rsp *pb.DigitalAppstoreResponse) error {
	cacheKey := "appstore:" + utils.Md5Hash(req)
	if req.CompanyName == "" {
		return errors.New(pb.ServiceName, "企业名称不能为空!", 400)
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: DigitalAppstore -> request:%v,Cache:%s", req, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: DigitalAppstore -> request:%v,NoCache:%s", req, cacheKey)
	if err := digital.GetAppstore(ctx, req.CompanyName, rsp, req.IsCompany); err == nil {
		if len(rsp.Result) == 0 {
			// 没有数据时缓存7天
			redis.SetCache(cacheKey, 7*24*time.Hour, rsp)
		} else {
			// 有数据时缓存30天
			redis.SetCache(cacheKey, 30*24*time.Hour, rsp)
		}
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

func (e *Core) DigitalSougouWeChat(ctx context.Context, req *pb.SougouRequest, rsp *pb.SougouResponse) error {
	cacheKey := "sougou:" + utils.Md5Hash(req)
	if req.Keyword == "" {
		return errors.New(pb.ServiceName, "Keyword不能为空!", 400)
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: DigitalSougouWeChat -> request:%v,Cache:%s", req, cacheKey)
		return nil
	}
	log.WithContextInfof(ctx, "[Core]: DigitalSougouWeChat -> request:%v,NoCache:%s", req, cacheKey)
	if err := digital.GetSougouWeChat(ctx, rsp, req.Keyword); err == nil {
		redis.SetCache(cacheKey, 3*time.Minute, rsp)
		return nil
	} else {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
}

// DigitalAndroidAppTaskCreate 创建android app关键词任务
func (e *Core) DigitalAndroidAppTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Digital-APP] Received core.DigitalAndroidAppTaskCreate request: %s", utils.AnyToStr(req))
	if err := digital.AndroidTaskFetchApps(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Digital] Create Android app task failure, keyword: %v, %v", req.Keyword, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DigitalAndroidAppTaskInfo 获取任务信息
func (e *Core) DigitalAndroidAppTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Digital-APP] Received core.DigitalAndroidAppTaskInfo request: %s", utils.AnyToStr(req))
	if err := digital.AndroidTaskInfo(req.TaskId, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Digital] Get Android app task->%d info failure: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DigitalAndroidAppTaskResult 获取任务结果
func (e *Core) DigitalAndroidAppTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.DigitalAppstoreResponse) error {
	log.WithContextInfof(ctx, "[Digital-APP] Received core.DigitalAndroidAppTaskResult request: %s", utils.AnyToStr(req))
	if err := digital.AndroidTaskResult(req.TaskId, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Digital] Get Android app task->%d result failure: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DigitalWechatTaskUpsert 数字资产-微信公众号 任务模式 新建任务
func (e *Core) DigitalWechatTaskUpsert(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Digital-Wechat] Received core.DigitalWechatTaskUpsert request: %s", utils.AnyToStr(req))
	err := digital.WechatTaskUpsert(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core] Digital-WechatTaskUpsert: keywords: %v, %v", req.Keyword, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DigitalWechatTaskInfo 数字资产-微信公众号 任务模式 任务详情
func (e *Core) DigitalWechatTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Digital-Wechat] Received core.DigitalWechatTaskInfo request: %s", utils.AnyToStr(req))
	if err := digital.WechatTaskInfo(req.TaskId, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Core] Digital-WechatTaskInfo: taskId->%d, %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DigitalWechatTaskResult 数字资产-微信公众号 任务模式 任务结果
func (e *Core) DigitalWechatTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.DigitalWechatAccounts) error {
	log.WithContextInfof(ctx, "[Digital-Wechat] Received core.DigitalWechatTaskResult request: %s", utils.AnyToStr(req))
	if err := digital.WechatTaskResult(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Core] Digital-WechatTaskResult: taskId->:%d, %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DigitalMiniAppTaskCreate 数字资产-微信小程序 任务模式 新建任务
func (e *Core) DigitalMiniAppTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.WithContextInfof(ctx, "[Digital-Miniapp] Received core.DigitalMiniAppTaskCreate request: %s", utils.AnyToStr(req))
	err := digital.MiniappTaskUpsert(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core] Digital-DigitalMiniAppTaskCreate: keywords: %v, %v", req.Keyword, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DigitalMiniAppTaskInfo 数字资产-微信小程序 任务模式 任务详情
func (e *Core) DigitalMiniAppTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[Digital-Miniapp] Received core.DigitalMiniAppTaskInfo request: %s", utils.AnyToStr(req))
	if err := digital.MiniappTaskInfo(req.TaskId, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Core] Digital-DigitalMiniappTaskInfo: taskId->%d, %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DigitalMiniappTaskResult 数字资产-微信小程序 任务模式 任务结果
func (e *Core) DigitalMiniAppTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.MiniappAssets) error {
	log.WithContextInfof(ctx, "[Digital-Miniapp] Received core.DigitalMiniAppTaskResult request: %s", utils.AnyToStr(req))
	if err := digital.MiniappTaskResult(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Core] Digital-DigitalMiniAppTaskResult: taskId->:%d, %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}
