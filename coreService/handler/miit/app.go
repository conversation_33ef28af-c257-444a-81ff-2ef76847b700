package miit

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/panda843/icp/model"

	"micro-service/pkg/log"
)

type queryResponse struct {
	Msg     string            `json:"msg"`
	Params  appParamsResponse `json:"params"`
	Success bool              `json:"success"`
}

type appParamsResponse struct {
	EndRow           int   `json:"endRow"`
	FirstPage        int   `json:"firstPage"`
	HasNextPage      bool  `json:"hasNextPage"`     // 是否有下一页
	HasPreviousPage  bool  `json:"hasPreviousPage"` // 是否有前一页
	IsFirstPage      bool  `json:"isFirstPage"`     // 是否首页
	IsLastPage       bool  `json:"isLastPage"`      // 是否最后一页
	List             []any `json:"list"`            // 结果列表
	LastPage         int   `json:"lastPage"`
	NavigatePages    int   `json:"navigatePages"`
	NavigatePageNums []int `json:"navigatepageNums"`
	NextPage         int   `json:"nextPage"`
	PageNum          int   `json:"pageNum"`
	PageSize         int   `json:"pageSize"`
	Pages            int   `json:"pages"`
	PrePage          int   `json:"prePage"`
	Size             int   `json:"size"`
	StartRow         int   `json:"startRow"`
	Total            int   `json:"total"` // 总数
}

type appItem struct {
	CityId           int    `json:"cityId"`
	ContentTypeName  string `json:"contentTypeName"`
	CountyId         int    `json:"countyId"`
	DataId           int    `json:"dataId"`
	LeaderName       string `json:"leaderName"`
	MainId           string `json:"mainId"`
	MainLicence      string `json:"mainLicence"`
	MainUnitAddress  string `json:"mainUnitAddress"`
	MainUnitCertNo   string `json:"mainUnitCertNo"`
	MainUnitCertType int    `json:"mainUnitCertType"`
	NatureId         int    `json:"natureId"`
	NatureName       string `json:"natureName"`
	ProvinceId       int    `json:"provinceId"`
	ServiceId        int    `json:"serviceId"`
	ServiceLicence   string `json:"serviceLicence"`
	ServiceName      string `json:"serviceName"`
	ServiceType      int    `json:"serviceType"`
	UnitName         string `json:"unitName"`
	UpdateRecordTime string `json:"updateRecordTime"`
	Version          string `json:"version"`
}

func (i *Icp) appQuery(ctx context.Context, queryInfo *model.QueryRequest) ([]appItem, error) {
	var page = 1
	var hasNextPage = true
	var items []appItem
	for hasNextPage {
		queryInfo.PageNum = strconv.Itoa(page)
		var rsp = &queryResponse{}
		errQuery := i.query(ctx, queryInfo, rsp)
		if errQuery != nil {
			x, _ := json.Marshal(queryInfo)
			log.WithContextWarnf(ctx, "[ICP-APP] query miit, param: %s, failed: %v", string(x), errQuery)
		}
		for _, v := range rsp.Params.List {
			var item appItem
			bs, _ := json.Marshal(v)
			if errUnmarshal := json.Unmarshal(bs, &item); errUnmarshal != nil {
				log.WithContextWarnf(ctx, "[ICP-APP] json.Unmarshal failed: %v", errUnmarshal)
				continue
			}
			items = append(items, item)
		}
		hasNextPage = len(rsp.Params.List) > 0
		page++
	}
	return items, nil
}

// rsp must be a pointer
func (i *Icp) query(ctx context.Context, queryInfo *model.QueryRequest, rsp any) error {
	queryRequest, _ := json.Marshal(queryInfo)

	err := i.post(ctx,
		"icpAbbreviateInfo/queryByCondition",
		string(queryRequest),
		"application/json;charset=UTF-8",
		i.token,
		rsp)
	if err != nil {
		if err.Error() == "miit 请求被拦截" {
			time.Sleep(time.Second * 1)
			err = i.post(ctx,
				"icpAbbreviateInfo/queryByCondition",
				string(queryRequest),
				"application/json;charset=UTF-8",
				i.token,
				rsp)
		}
	}
	return err
}
