package miit

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"github.com/fghwett/icp/abbreviateinfo"

	corePb "micro-service/coreService/proto"
	"micro-service/middleware/mysql/icp_apps"
	"micro-service/pkg/utils"
)

const sourceMIIT = "miit"

type QueryAppRsp struct {
	Parent    icp_apps.Equity
	QueryItem icp_apps.Equity
	Children  []*icp_apps.Equity
}

func QueryApp(ctx context.Context, serviceType int, content string) (QueryAppRsp, error) {
	req := newIcpRequest(serviceType, content)
	result, err := req.QueryByService(ctx)
	switch {
	case errors.Is(err, abbreviateinfo.IcpNotForRecord):
		return QueryAppRsp{}, nil
	case err != nil:
		return QueryAppRsp{}, err
	}

	if len(result) == 0 {
		return QueryAppRsp{}, nil
	}

	var rsp QueryAppRsp
	rsp.Parent = icp_apps.Equity{
		Name:        result[0].UnitName,
		Icp:         result[0].MainLicence,
		AppName:     "",
		Source:      sourceMIIT,
		ParentId:    0,
		CompanyType: utils.If(result[0].NatureName == "企业", icp_apps.TypeCompany, icp_apps.TypePerson), //nolint:goconst,gocritic
		Type:        0,
		Status:      icp_apps.StatusOnline,
	}
	rsp.QueryItem = rsp.Parent
	rsp.QueryItem.Icp = result[0].ServiceLicence
	rsp.QueryItem.AppName = result[0].ServiceName
	rsp.QueryItem.Type = serviceType

	if content != rsp.Parent.Name {
		req.content = rsp.Parent.Name
		result, err = req.QueryByService(ctx)
		if err != nil {
			return rsp, err
		}
	}

	var parentRecordTime time.Time
	for i := range result {
		recordTime, errParse := utils.ParseTime(result[i].UpdateRecordTime)
		if recordTime.After(parentRecordTime) {
			parentRecordTime = recordTime
		}
		if errParse != nil {
			recordTime = time.Now()
		}

		item := &icp_apps.Equity{
			Name:        result[i].UnitName,
			Icp:         result[i].ServiceLicence,
			AppName:     result[i].ServiceName,
			Source:      sourceMIIT,
			ParentId:    0,
			CompanyType: utils.If(result[0].NatureName == "企业", icp_apps.TypeCompany, icp_apps.TypePerson), //nolint:goconst,gocritic
			Type:        serviceType,
			Status:      icp_apps.StatusOnline,
			RecordTime:  sql.NullTime{Valid: true, Time: recordTime},
		}
		rsp.Children = append(rsp.Children, item)
	}
	parentRecordTime = utils.If(parentRecordTime.IsZero(), time.Now(), parentRecordTime)
	rsp.Parent.RecordTime = sql.NullTime{Time: parentRecordTime, Valid: !parentRecordTime.IsZero()}

	return rsp, nil
}

func QueryDomainOnly(ctx context.Context, rsp *corePb.IcpResponse, domain string) error {
	miitQuery := &Icp{}
	domainInfo, err := miitQuery.Query(ctx, domain)
	if errors.Is(err, abbreviateinfo.IcpNotForRecord) {
		rsp.Ba = false
		return nil
	} else if err != nil {
		return err
	}
	if len(domainInfo.List) == 0 {
		rsp.Ba = false
		return nil
	}
	rsp.Info = &corePb.BeiAn{
		CompanyName: domainInfo.List[0].UnitName,
		CompanyType: domainInfo.List[0].NatureName,
		Icp:         domainInfo.List[0].ServiceLicence,
		AuditTime:   domainInfo.List[0].UpdateRecordTime,
		WebsiteUrl:  domainInfo.List[0].Domain,
	}
	return nil
}

func QueryDomain(ctx context.Context, rsp *corePb.IcpResponse, domain string) error {
	miitQuery := &Icp{}
	domainInfo, err := miitQuery.Query(ctx, domain)
	if errors.Is(err, abbreviateinfo.IcpNotForRecord) {
		rsp.Ba = false
		return nil
	} else if err != nil {
		return err
	}
	if len(domainInfo.List) == 0 {
		rsp.Ba = false
		return nil
	}
	rsp.Info = &corePb.BeiAn{
		CompanyName: domainInfo.List[0].UnitName,
		CompanyType: domainInfo.List[0].NatureName,
		Icp:         domainInfo.List[0].ServiceLicence,
		AuditTime:   domainInfo.List[0].UpdateRecordTime,
		WebsiteUrl:  domainInfo.List[0].Domain,
	}
	forList, err := miitQuery.Query(ctx, rsp.Info.CompanyName)
	if err != nil {
		return err
	}
	for _, bei := range forList.List {
		domains := utils.ListColumn(rsp.Equals, func(t *corePb.BeiAn) string {
			return t.WebsiteUrl
		})
		if !utils.ListContains(domains, bei.Domain) {
			rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
				CompanyName: bei.UnitName,
				CompanyType: bei.NatureName,
				Icp:         bei.ServiceLicence,
				AuditTime:   bei.UpdateRecordTime,
				WebsiteUrl:  bei.Domain,
			})
		}
	}
	return nil
}

func QueryCompanyName(ctx context.Context, rsp *corePb.IcpResponse, companyName string) error {
	miitQuery := &Icp{}
	domainInfo, err := miitQuery.Query(ctx, companyName)
	if err == abbreviateinfo.IcpNotForRecord {
		rsp.Ba = false
		return nil
	} else if err != nil {
		return err
	}
	if len(domainInfo.List) == 0 {
		rsp.Ba = false
		return nil
	}
	rsp.Ba = true
	for _, bei := range domainInfo.List {
		if rsp.Info == nil {
			rsp.Info = &corePb.BeiAn{
				CompanyName: bei.UnitName,
				CompanyType: bei.NatureName,
				Icp:         bei.ServiceLicence,
				AuditTime:   bei.UpdateRecordTime,
				WebsiteUrl:  bei.Domain,
			}
		} else {
			domains := utils.ListColumn(rsp.Equals, func(t *corePb.BeiAn) string {
				return t.WebsiteUrl
			})
			if !utils.ListContains(domains, bei.Domain) {
				rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
					CompanyName: bei.UnitName,
					CompanyType: bei.NatureName,
					Icp:         bei.ServiceLicence,
					AuditTime:   bei.UpdateRecordTime,
					WebsiteUrl:  bei.Domain,
				})
			}
		}
	}
	return nil
}

func QueryIcp(ctx context.Context, rsp *corePb.IcpResponse, icp string) error {
	miitQuery := &Icp{}
	domainInfo, err := miitQuery.Query(ctx, icp)
	if errors.Is(err, abbreviateinfo.IcpNotForRecord) {
		rsp.Ba = false
		return nil
	} else if err != nil {
		return err
	}
	if len(domainInfo.List) == 0 {
		rsp.Ba = false
		return nil
	}
	rsp.Ba = true
	for _, bei := range domainInfo.List {
		if rsp.Info == nil {
			rsp.Info = &corePb.BeiAn{
				CompanyName: bei.UnitName,
				CompanyType: bei.NatureName,
				Icp:         bei.ServiceLicence,
				AuditTime:   bei.UpdateRecordTime,
				WebsiteUrl:  bei.Domain,
			}
		} else {
			domains := utils.ListColumn(rsp.Equals, func(t *corePb.BeiAn) string {
				return t.WebsiteUrl
			})
			if !utils.ListContains(domains, bei.Domain) {
				rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
					CompanyName: bei.UnitName,
					CompanyType: bei.NatureName,
					Icp:         bei.ServiceLicence,
					AuditTime:   bei.UpdateRecordTime,
					WebsiteUrl:  bei.Domain,
				})
			}
		}
	}
	return nil
}
