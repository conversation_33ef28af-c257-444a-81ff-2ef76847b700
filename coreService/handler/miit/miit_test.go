package miit

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	core "micro-service/coreService/proto"
)

func Test_QueryApp(t *testing.T) {
	rsp, err := QueryApp(context.TODO(), TypeMiniProgram, "深圳市腾讯计算机系统有限公司")
	assert.Nil(t, err)
	fmt.Println(rsp.Parent)
	fmt.Println(rsp.Children)
}

func Test_QueryCompanyName(t *testing.T) {
	var rsp = &core.IcpResponse{}
	err := QueryCompanyName(context.TODO(), rsp, "深圳市腾讯计算机系统有限公司")
	assert.Nil(t, err)
	fmt.Println(rsp)
}
