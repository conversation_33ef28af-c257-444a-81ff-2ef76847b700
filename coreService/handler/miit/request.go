package miit

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/utils"
	"strconv"
	"strings"
	"time"

	"github.com/panda843/icp/model"
	"github.com/panda843/icp/util"
)

const (
	TypeDomain      = 1
	TypeAPP         = 6
	TypeMiniProgram = 7
	TypeFastApp     = 8
)

var IcpNotForRecord = errors.New("域名未备案")

type Icp struct {
	token       string
	ip          string
	content     string
	serviceType int
}

func newIcpRequest(serviceType int, content string) *Icp {
	return &Icp{
		content:     content,
		serviceType: serviceType,
	}
}

func (i *Icp) QueryByService(ctx context.Context) ([]appItem, error) {
	i.getIp()
	if err := i.auth(ctx); err != nil {
		return nil, err
	}

	list, err := i.appQuery(ctx, &model.QueryRequest{
		PageSize:    "40",
		UnitName:    i.content,
		ServiceType: i.serviceType,
	})

	return list, err
}

func (i *Icp) Query(ctx context.Context, queryStr string) (*model.QueryParams, error) {
	i.getIp()

	if err := i.auth(ctx); err != nil {
		return nil, err
	}

	index := 1
	req := &model.QueryRequest{
		PageNum:     strconv.Itoa(index),
		PageSize:    "40",
		UnitName:    queryStr,
		ServiceType: TypeDomain,
	}
	result, err := i.domainQuery(ctx, req)
	if err != nil {
		return nil, err
	}
	tryNumber := 0
	for result.Pages > index {
		req.PageNum = strconv.Itoa(index)
		response, forErr := i.domainQuery(ctx, req)
		if forErr == nil {
			index++
			result.List = append(result.List, response.List...)
		} else {
			tryNumber += 1
			if tryNumber >= 2 {
				tryNumber = 0
				index++
			}
			time.Sleep(1 * time.Second)
			log.WithContextWarnf(ctx, "[icp] query miit, request: %s, failed: %v", utils.AnyToStr(req), forErr)
		}
	}
	return result, nil
}

func (i *Icp) domainQuery(ctx context.Context, queryInfo *model.QueryRequest) (*model.QueryParams, error) {
	result := &model.IcpResponse{Params: &model.QueryParams{}}
	err := i.query(ctx, queryInfo, result)
	if err != nil {
		return nil, err
	}

	if !result.Success {
		return nil, fmt.Errorf("查询：%s", result.Msg)
	}

	queryParams := result.Params.(*model.QueryParams)
	if len(queryParams.List) == 0 {
		return nil, IcpNotForRecord
	}

	return queryParams, nil
}

func (i *Icp) auth(ctx context.Context) error {
	timestamp := time.Now().Unix()
	authKey := util.Md5(fmt.Sprintf("testtest%d", timestamp))
	authBody := fmt.Sprintf("authKey=%s&timeStamp=%d", authKey, timestamp)

	result := &model.IcpResponse{Params: &model.AuthParams{}}
	err := i.post(ctx, "auth", authBody, "application/x-www-form-urlencoded;charset=UTF-8", "0", result)
	if err != nil {
		if err.Error() == "miit 请求被拦截" {
			time.Sleep(time.Second * 1)
			err = i.post(ctx, "auth", authBody, "application/x-www-form-urlencoded;charset=UTF-8", "0", result)
		}
		if err != nil {
			return fmt.Errorf("获取token失败：%s", err.Error())
		}
	}
	if !result.Success {
		return fmt.Errorf("获取token失败：%s", result.Msg)
	}
	authParams := result.Params.(*model.AuthParams)
	i.token = authParams.Bussiness
	return nil
}

func (i *Icp) post(ctx context.Context, url, data, contentType, token string, result interface{}) error {
	client := crawlerPb.GetProtoClient()
	crawlerRes, err := client.Get(ctx, &crawlerPb.GetRequest{
		Method: crawlerPb.MethodCurlPost,
		Url:    fmt.Sprintf("https://hlwicpfwc.miit.gov.cn/icpproject_query/api/%s", url),
		Headers: map[string]string{
			"Content-Type":    contentType,
			"Origin":          "https://beian.miit.gov.cn/",
			"Referer":         "https://beian.miit.gov.cn/",
			"token":           token,
			"CLIENT_IP":       i.ip,
			"X-FORWARDED-FOR": i.ip,
		},
		Body:    data,
		Timeout: 60, // 添加超时时间
	}, utils.RpcTimeoutDur(1*time.Minute), microx.ServerTimeoutDur(1*time.Minute))
	if err != nil {
		return err
	}
	// Load the HTML document
	bodyStr := crawlerPb.DecodeBy(crawlerRes.Body)
	if crawlerRes.Body == "" {
		return errors.New("miit response empty")
	}
	if strings.Contains(bodyStr, "当前访问疑似黑客攻击") {
		return errors.New("miit 请求被拦截")
	} else {
		log.WithContextInfof(ctx, "crawler miit keyword:%s,code:%d,body:%s", data, crawlerRes.Code, crawlerPb.DecodeBy(crawlerRes.Body))
	}
	return json.Unmarshal([]byte(bodyStr), result)
}

func (i *Icp) getIp() {
	if i.ip != "" {
		return
	}
	i.ip = util.RandIp()
}
