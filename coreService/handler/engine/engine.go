package engine

import (
	"context"
	corePb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"net/url"
	"strconv"
	"strings"

	"github.com/antchfx/htmlquery"
)

func QueryDomain(ctx context.Context, rsp *corePb.EngineSearchResponse, domain string) error {
	page := 2 // 暂定义为两页
	var strMap = make(map[string]string)
	for i := 0; i <= page; i++ {
		subdomains, err := getQueryList(ctx, domain, 10*i)
		if err != nil {
			return err
		}
		for _, value := range subdomains {
			strMap[value] = value
		}
	}
	result := make([]string, 0)
	for _, value := range strMap {
		result = append(result, value)
	}
	rsp.Subdomains = result
	return nil
}

func getQueryList(ctx context.Context, domain string, pn int) ([]string, error) {
	baiduUrl := "https://www.baidu.com/s?wd=site%3A" + domain + "&pn=" + strconv.Itoa(pn) + "&rsv_spt=1&rsv_iqid=0xbf60c73500062585&issp=1&f=8&rsv_bp=1&rsv_idx=2&ie=utf-8&rqlang=cn&tn=baiduhome_pg&rsv_enter=1&rsv_dl=tb&oq=site%253Asanquan.com&rsv_btype=t&inputT=5681&rsv_t=1e784MWuHxY7lm1amWf4lGbNeJ1Z3Ezw7kfWkT5u%2BTky1xL9DyRrWP%2Fxzjc0xC9A6f3d&rsv_sug3=46&rsv_pq=c00ccc00001f7ebb&rsv_sug2=0&rsv_sug4=842239"
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    baiduUrl,
		Method: crawlerPb.MethodChromeGet,
	}, utils.SetRpcTimeoutOpt(30))

	subdomainMap := make([]string, 0)

	if err != nil {
		return subdomainMap, err
	}
	log.WithContextInfof(ctx, "crawler engine-baidu domain-> domain:%s,code:%d,size:%d", domain, crawlerRes.Code, len(crawlerRes.Body))
	doc, err := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
	if err != nil {
		return subdomainMap, err
	}
	list, err := htmlquery.QueryAll(doc, "//div[@id=\"content_left\"]/div[@class=\"result c-container xpath-log new-pmd\"]")
	if err != nil {
		return subdomainMap, err
	}

	for _, n := range list {
		a := htmlquery.SelectAttr(n, "mu")
		u, err := url.Parse(a)
		if err != nil {
			log.Fatal(err)
		}
		subdomain := u.Hostname()
		subdomainMap = append(subdomainMap, subdomain)
	}
	return subdomainMap, nil
}
