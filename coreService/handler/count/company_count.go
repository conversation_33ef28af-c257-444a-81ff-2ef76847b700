package count

import (
	"errors"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/company_equity"
)

func CompanyCount(names []string) (*pb.HoldingCompanyCountResponse, error) {
	if len(names) == 0 {
		return nil, errors.New("参数错误")
	}

	r, err := company_equity.NewCompanyEquityModel(mysql.GetInstance()).CountByNameWithParent(names)
	if err != nil {
		return nil, err
	}

	datas := make([]*pb.HoldingCompanyCount, 0, len(r))
	for i := range r {
		datas = append(datas, &pb.HoldingCompanyCount{Name: r[i].Name, Percent: r[i].Percent})
	}
	return &pb.HoldingCompanyCountResponse{Count: datas}, nil
}
