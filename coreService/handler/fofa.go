package handler

import (
	"context"
	"go-micro.dev/v4/errors"
	"micro-service/coreService/handler/fofa"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strconv"
	"time"
)

func (e *Core) FofaQuery(ctx context.Context, req *pb.FofaQueryRequest, rsp *pb.FofaQueryResponse) error {
	log.WithContextInfof(ctx, "Received Core.FofaQuery request: %v", req)
	if req.Qbase64 == "" {
		return errors.New(pb.ServiceName, "qbase64不能为空!", 400)
	}
	return fofa.FofaQuery(ctx, rsp, req)
}

func (e *Core) FofaQueryCount(ctx context.Context, req *pb.FofaQueryCountRequest, rsp *pb.FofaQueryCountResponse) error {
	log.WithContextInfof(ctx, "Received Core.FofaQueryCount request: %v", req)
	if req.Qbase64 == "" {
		return errors.New(pb.ServiceName, "qbase64不能为空!", 400)
	}
	return fofa.FofaQueryCount(ctx, rsp, req)
}

func (e *Core) FofaHost(ctx context.Context, req *pb.FofaHostRequest, rsp *pb.FofaHostResponse) error {
	log.WithContextInfof(ctx, "Received Core.FofaHost request: %v", req)
	if req.Host == "" {
		return errors.New(pb.ServiceName, "host不能为空!", 400)
	}
	return fofa.FofaHost(ctx, rsp, req)
}

func (e *Core) FofaAllAssetsCount(ctx context.Context, req *pb.FofaAllAssetsCountRequest, rsp *pb.FofaAllAssetsCountResponse) error {
	err := fofa.FofaAllAssetsCount(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}

	return nil
}

func (*Core) FofaQueryHot(ctx context.Context, req *pb.FofaQueryHotRequest, rsp *pb.FofaQueryHotResponse) error {
	countHot, _ := strconv.Atoi(req.Count)
	if countHot < 1 || countHot > 20 {
		return errors.New(pb.ServiceName, "参数错误", 400)
	}

	cacheKey := "fofa:hot:" + utils.Md5Hash(req)
	if redis.GetCache(cacheKey, rsp) {
		log.WithContextInfof(ctx, "[Core]: FofaQueryHot -> Cache:%s", cacheKey)
		return nil
	}

	var r fofa.FofaQueryHotResp
	err := fofa.FofaQueryHot(ctx, req.Count, &r)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}

	rsp.Data = r.Data
	redis.SetCache(cacheKey, 24*60*time.Minute, rsp)
	return nil
}

func (e *Core) FofaPureDns(ctx context.Context, req *pb.FofaPureDnsRequest, rsp *pb.FofaPureDnsResponse) error {
	if req.Domain == "" {
		return errors.New(pb.ServiceName, "域名不能为空!", 400)
	}
	log.WithContextInfof(ctx, "Received Core.FofaPureDns request: %v", req)
	return fofa.FofaPureDns(ctx, rsp, req)
}

func (e *Core) FofaAccountInfo(ctx context.Context, _ *pb.Empty, rsp *pb.FofaAccountResponse) error {
	log.WithContextInfof(ctx, "Received Core.FofaAccountInfo")
	return fofa.FofaAccountInfo(ctx, rsp)
}

// CreateDetectionTask 创建存活探测任务
func (e *Core) CreateDetectionTask(ctx context.Context, req *pb.FofaDetectionRequest, rsp *pb.FofaDetectionResponse) error {
	return fofa.CreateDetectionTask(ctx, req, rsp)
}

// CreateScanTask 创建扫描任务
func (e *Core) CreateScanTask(ctx context.Context, req *pb.FofaScanTaskRequest, rsp *pb.FofaDetectionResponse) error {
	return fofa.CreateScanTask(ctx, req, rsp)
}

// GetTaskStatus 获取任务状态
func (e *Core) GetTaskStatus(ctx context.Context, req *pb.FofaTaskStatusRequest, rsp *pb.FofaTaskStatusResponse) error {
	return fofa.GetTaskStatus(ctx, req, rsp)
}

// GetTaskResult 获取任务结果
func (e *Core) GetTaskResult(ctx context.Context, req *pb.FofaTaskStatusRequest, rsp *pb.FofaTaskResultResponse) error {
	return fofa.GetTaskResult(ctx, req, rsp)
}

// CreateDomainUpdateTask-批量下发给fofa去更新相关的域名数据的扫描任务
func (e *Core) CreateDomainUpdateTask(ctx context.Context, req *pb.FofaDomainTaskRequest, rsp *pb.FofaDomainTaskResponse) error {
	log.WithContextInfof(ctx, "Received Core.CreateDomainUpdateTask request: %v", req)
	if req.Domains == nil {
		return errors.New(pb.ServiceName, "域名扫描目标不能为空!", 400)
	}
	return fofa.CreateDomainUpdateTask(ctx, req, rsp)
}
