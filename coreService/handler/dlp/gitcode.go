package dlp

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/url"
	"strings"
	"time"

	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	dlt "micro-service/middleware/mysql/dataleak_gitcode"
	dlg "micro-service/middleware/mysql/dataleak_github"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

type GitcodeChanUnit struct {
	RepoUrl    string `json:"web_url"`
	RepoDesc   string `json:"description"`
	RepoName   string `json:"human_name"`
	CodeUrl    string
	Screenshot string
	Sha        string
	Language   string `json:"language"`
}

const gitcodeSearchRepoAPI = "https://api.gitcode.com/api/v5/search/repositories?"

var AccessGitcodeToken = getGitcodePersonalToken() //获取consul配置的token

func getGitcodePersonalToken() string {
	common := cfg.GetInstance().Common
	tokens := common.GitcodeTokens
	index := rand.Intn(len(tokens)) //  rand.Intn(max-min+1) + min ,min = 0
	return tokens[index]
}
func gitcodeRepoReqUrl(keywords []string, page, size int) string {
	q := fmt.Sprintf("q=%s&page=%d&per_page=%d&sort=last_push_at", url.QueryEscape(strings.Join(keywords, " ")), page, size)
	log.Info("githubRepoSearchApi:", gitcodeSearchRepoAPI+q)
	return gitcodeSearchRepoAPI + q
}

// GitcodeRepoSearch step2-1 异步获取数据 ---start
func GitcodeRepoSearch(ctx context.Context, keywords []string, token string, maxTotal int, ch chan<- GitcodeChanUnit) error {
	defer close(ch)
	var (
		page       = 0
		perPage    = 50 // gitcode 网站最大限制是50条每页
		currentNum = 0
		maxPage    = maxTotal / perPage
	)
	if maxTotal%perPage != 0 {
		maxPage++
	}
	// step 2-1 更新爬虫并爬取数据
	for currentNum < maxTotal { // 若获取的数据总条数小于500 则持续访问
		page += 1
		if page > maxPage {
			break
		}
		// 2-1-1 构造请求url
		reqUrl := gitcodeRepoReqUrl(keywords, page, perPage)
		log.WithContextInfof(ctx, "[Gitcode]  githcodeUrl: %s, Keyword: %v", reqUrl, keywords)
		// 2-1-2 发起请求并获取响应
		crawlerRes, err := gitcodeCrawlerReq(ctx, reqUrl, token) // 获取code代码泄露的api请求的响应
		if err != nil {
			log.WithContextWarnf(ctx, "[Gitcode] Search %v Error:%s", keywords, err.Error())
			break
		}
		// 2-1-3 解码响应body
		var items []GitcodeChanUnit
		if crawlerRes.Body == "" {
			log.WithContextWarnf(ctx, "[Gitcode] Search %v 响应为空", keywords)
			break
		}
		err = json.Unmarshal([]byte(crawlerPb.DecodeBy(crawlerRes.Body)), &items)
		if err != nil {
			log.WithContextErrorf(ctx, "[Gitcode] Search %v json解码异常 Error:%s", keywords, err.Error())
		}
		for _, item := range items { // 2-1-4 将解码数据通过通道发送到对应数据
			if item.Language == "" {
				item.Language = Other
			}
			item.Sha, _ = SortAndHash(keywords)
			ch <- item
		}
		time.Sleep(1 * time.Second)
		currentNum++
	}
	return nil
}

// GitcodeSearchByTask step1  创建Gitcode 数据泄露任务
func GitcodeSearchByTask(_ context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.Info("[Gitcode]Search keyword by task: %v", req.Keyword)
	var (
		isQuery               bool
		keywordHash, keywords = dlg.SortAndHash(req.Keyword)
		client                = dlt.NewTaskModel()
	)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil { // 如果不存在历史查询 则新建任务行记录
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		join := dlg.KeywordJoin(keywords)
		var info = dlt.Task{Keyword: join, KeywordHash: keywordHash, Progress: 1, Status: dlg.StatusDoing}
		err = client.Create(&info)
		if err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else { // 如果强制更新 则初始化原有行记录状态为1
		rsp.TaskId = item.Id
		if req.Force == 1 || cacheIsExpired(item.UpdatedAt) || item.Status == dlg.StatusInvalid {
			if err := client.UpdateAny(uint(item.Id), map[string]any{
				"status":   dlg.StatusDoing,
				"progress": 1,
			}); err != nil {
				return err
			}
			isQuery = true
		}
	}

	if isQuery {
		// step2 异步 启动爬虫任务
		//searchKeyWords = keywords
		go queryGitcodeWithCode(context.TODO(), uint(rsp.TaskId), AccessGitcodeToken, 500, keywords)
	}

	return nil
}

// step2 异步获取Gitcode数据 并写入数据库
func queryGitcodeWithCode(ctx context.Context, taskId uint, token string, maxTotal uint, keyword []string) {
	var (
		ch         = make(chan GitcodeChanUnit)
		list       = make([]*dlt.GitcodeResult, 0, 300)
		conn       = dlt.NewResultModel()
		taskClient = dlt.NewTaskModel()
	)
	go func() { // token方式获取 step 2-1 异步获取gitcode数据
		err := GitcodeRepoSearch(ctx, keyword, token, int(maxTotal), ch)
		if err != nil {
			log.WithContextWarnf(ctx, "[Gitcode] search keyword: %v by task_id: %d failed: %v", keyword, taskId, err)
		}
	}()

	repoUrls := make([]string, 0, 500)
	for v := range ch {
		repoUrls = append(repoUrls, v.RepoUrl)
		list = append(list, &dlt.GitcodeResult{
			TaskId:      taskId,
			RepoName:    v.RepoName,
			RepoUrl:     v.RepoUrl,
			RepoDesc:    v.RepoDesc,
			CodeUrl:     v.CodeUrl,
			ScreenShot:  v.Screenshot,
			Sha:         v.Sha,
			Language:    v.Language,
			CodeSnippet: "", // 目前获取不到代码片段
		})
	}
	// step 2-2 将获取的gitcode数据写入数据库 并更新任务进度
	upsert, err := conn.CompareAndUpdate(list)
	if err != nil {
		log.WithContextErrorf(ctx, "[Gitcode] Gitcode search with code in task_id: %d: %v", taskId, err)
	}

	insertRelation := make([]*dlt.Relation, 0)
	oldResult, _ := conn.RelationList(mysql.WithColumnValue("`task_id`", taskId))
	for _, v := range upsert {
		exist := false
		for _, x := range oldResult {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insertRelation = append(insertRelation, &dlt.Relation{TaskId: uint64(taskId), ResultId: v.Id})
		}
	}
	if errSave := conn.RelationSave(insertRelation); errSave != nil {
		log.WithContextErrorf(ctx, "[Gitcode] Save task_id: %d relation result failure: %v", taskId, errSave)
	}

	err = taskClient.UpdateAny(taskId, map[string]any{"progress": 100, "status": dlt.StatusFinished})
	if err != nil {
		log.WithContextErrorf(ctx, "[Gitcode] Update task_id: %d progress to 100 failure: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[Gitcode] Task_id: %v, keyword: %v, task finished!", taskId, keyword)
	}
}

// GitcodeTaskInfo 总库 任务详情
func GitcodeTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := dlt.NewTaskModel().First(mysql.WithId(taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}
	rsp.TaskId = int64(info.Id)
	rsp.Keywords = dlg.KeywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

// GitcodeTaskResult 总库 任务结果
func GitcodeTaskResult(taskId uint64, rsp *pb.GitHubCodeResultResponse) error {

	l, err := dlt.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return err
	}

	for i := range l {
		rsp.Items = append(rsp.Items, &pb.GitHubCode{
			RepoName:    l[i].RepoName,
			RepoUrl:     l[i].RepoUrl,
			RepoDesc:    l[i].RepoDesc,
			CodeUrl:     l[i].CodeUrl,
			Sha:         l[i].Sha,
			CodeSnippet: l[i].CodeSnippet,
			Language:    l[i].Language,
			Screenshot:  l[i].ScreenShot,
		})
	}
	return nil
}

// 构造gitcode API 搜索请求结构并启动crawlerService进行代理请求
func gitcodeCrawlerReq(ctx context.Context, url string, token string) (*crawlerPb.GetResponse, error) {
	param := &crawlerPb.GetRequest{
		Url:    url,
		Method: crawlerPb.MethodCurlGet,
		Headers: map[string]string{
			"Authorization": fmt.Sprintf("Bearer %s", token),
			"Content-Type":  "application/json;charset=UTF-8",
		},
		WaitTime: 45,
	}
	return crawlerPb.GetProtoClient().Get(ctx, param)
}
