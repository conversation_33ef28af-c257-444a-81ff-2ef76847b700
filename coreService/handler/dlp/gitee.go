package dlp

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/url"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	dlt "micro-service/middleware/mysql/dataleak_gitee"
	dlg "micro-service/middleware/mysql/dataleak_github"
	redis "micro-service/middleware/redis/dlp"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

type GiteeChanUnit struct {
	RepoUrl    string `json:"html_url"`
	RepoDesc   string `json:"description"`
	RepoName   string `json:"name"`
	CodeUrl    string
	Screenshot string
	Sha        string
	Language   string `json:"language"`
}

// PartialFields noToken 请求结构体解析
type PartialFields struct {
	Langs       []string `json:"langs"`
	Description []string `json:"description"`
	RepoUrl     []string `json:"url"`
	//RepoDesc    string   `json:"description"`
	RepoName   []string `json:"title"`
	CodeUrl    string
	Screenshot string
	Sha        string
}

// HitItem struct for hits array with only the fields we need
type HitItem struct {
	Fields PartialFields `json:"fields"`
}

// Hits struct containing the hits array
type Hits struct {
	Hits []HitItem `json:"hits"`
}

// NoTokenResp  struct containing the top-level hits object
type NoTokenResp struct {
	Hits Hits `json:"hits"`
}

const giteeSearchRepoAPI = "https://gitee.com/api/v5/search/repositories?"
const giteeSearchRepoAPINoToken = "https://api.indexea.com/v1/search/widget/wjawvtmm7r5t25ms1u3d"
const Other = "Other"
const searchStrategy = "token"

var AccessToken = getGiteePersonalToken() //获取consul配置的token

func getGiteePersonalToken() string {
	common := cfg.GetInstance().Common
	tokens := common.GiteeTokens
	index := rand.Intn(len(tokens)) //  rand.Intn(max-min+1) + min ,min = 0
	return tokens[index]
}

// GetGiteeRepoSearch 同步查询
func GetGiteeRepoSearch(ctx context.Context, keywords []string, response *pb.GitHubCodeResponse, maxTotal int) error {
	ch := make(chan GiteeChanUnit)
	go func() {
		if err := GiteeRepoSearch(ctx, keywords, AccessToken, maxTotal, ch); err != nil {
			log.WithContextErrorf(ctx, "[Gitee] search repo: %v", err.Error())
		}
	}()

	for v := range ch {
		response.Result = append(response.Result, &pb.GitHubCode{
			RepoUrl:    v.RepoUrl,
			RepoDesc:   v.RepoDesc,
			RepoName:   v.RepoName,
			CodeUrl:    v.CodeUrl,
			Screenshot: v.Screenshot,
			Sha:        v.Sha,
			Language:   v.Language,
		})
	}

	// 设置缓存
	var gt = gitee{}
	cacheKey := gt.cacheKey(keywords, maxTotal)
	gt.setCache(ctx, cacheKey, response.Result)

	return nil
}

func giteeRepoReqUrl(APIAddress string, keywords []string, page, size int) string {
	switch APIAddress {
	case giteeSearchRepoAPINoToken:
		// giteeSearchRepoAPINoToken: 'https://api.indexea.com/v1/search/widget/wjawvtmm7r5t25ms1u3d?q=XXX&from=0&size=20&sort_by_f=',
		q := fmt.Sprintf("?q=%s&from=%d&size=%d&sort_by_f=", url.QueryEscape(strings.Join(keywords, " ")), page*size, size)
		return giteeSearchRepoAPINoToken + q

	case giteeSearchRepoAPI:
		// githubRepoSearchApi: 'https://api.github.com/search/repositories?q=XXX&page=1&per_page=20&&order=desc',
		q := fmt.Sprintf("q=%s&page=%d&per_page=%d&order=desc", url.QueryEscape(strings.Join(keywords, " ")), page, size)
		log.Info("githubRepoSearchApi:", giteeSearchRepoAPI+q)
		return giteeSearchRepoAPI + q

	default:
		return ""
	}
}

// GiteeRepoSearch step2-1 异步获取数据 ---start
func GiteeRepoSearch(ctx context.Context, keywords []string, token string, maxTotal int, ch chan<- GiteeChanUnit) error {
	defer close(ch)
	var (
		page       = 0
		perPage    = 10 // gitee 网站最大限制是100条每页
		currentNum = 0
		maxPage    = maxTotal / perPage
	)
	if maxTotal%perPage != 0 {
		maxPage++
	}
	// step 2-1 更新爬虫并爬取数据
	for currentNum < maxTotal { // 若获取的数据总条数小于500 则持续访问
		page += 1
		if page > maxPage {
			break
		}
		// 2-1-1 构造请求url
		reqUrl := giteeRepoReqUrl(giteeSearchRepoAPI, keywords, page, perPage)
		log.WithContextInfof(ctx, "[Gitee]  githeeUrl: %s, Keyword: %v", reqUrl, keywords)
		// 2-1-2 发起请求并获取响应
		crawlerRes, err := giteeCrawlerReq(ctx, reqUrl, token) // 获取code代码泄露的api请求的响应
		if err != nil {
			log.WithContextWarnf(ctx, "[Gitee] Search %v Error:%s", keywords, err.Error())
			break
		}
		// 2-1-3 解码响应body
		var items []GiteeChanUnit
		if crawlerRes.Body == "" {
			log.WithContextWarnf(ctx, "[Gitee] Search %v 响应为空", keywords)
			break
		}
		err = json.Unmarshal([]byte(crawlerPb.DecodeBy(crawlerRes.Body)), &items)
		if err != nil {
			log.WithContextErrorf(ctx, "[Gitee] Search %v json解码异常 Error:%s", keywords, err.Error())
		}
		for _, item := range items { // 2-1-4 将解码数据通过通道发送到对应数据
			if item.Language == "" {
				item.Language = Other
			}
			item.Sha, _ = SortAndHash(keywords)
			ch <- item
		}
		time.Sleep(1 * time.Second)
		currentNum++
	}
	return nil
}

// GiteeRepoSearch 异步获取数据 ---end

type gitee struct{}

func (gitee) cacheKey(l []string, maxTotal int) string {
	key := "gitee:" + utils.Md5sHash(strings.Join(l, "|+|"), false) + ":req_total_" + strconv.Itoa(maxTotal)
	return key
}

type giteeCache struct {
	List []*pb.GitHubCode `json:"list"`
}

func (gitee) setCache(ctx context.Context, key string, list []*pb.GitHubCode) {
	if len(list) > 0 {
		gc := giteeCache{List: list}
		if b := redis.NewGiteeStrategyModel().SetCache(key, 30*time.Minute, gc); !b {
			log.WithContextErrorf(ctx, "[Gitee] cacheKey: %s, set cache failed", key)
		}
	}
}

// GiteeSearchByTask step1  创建Gitee 数据泄露任务
func GiteeSearchByTask(_ context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.Info("[GitHub]Search keyword by task: %v", req.Keyword)
	var (
		isQuery               bool
		keywordHash, keywords = dlg.SortAndHash(req.Keyword)
		client                = dlt.NewTaskModel()
	)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil { // 如果不存在历史查询 则新建任务行记录
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		join := dlg.KeywordJoin(keywords)
		var info = dlt.Task{Keyword: join, KeywordHash: keywordHash, Progress: 1, Status: dlg.StatusDoing}
		err = client.Create(&info)
		if err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else { // 如果强制更新 则初始化原有行记录状态为1
		rsp.TaskId = item.Id
		if req.Force == 1 || cacheIsExpired(item.UpdatedAt) || item.Status == dlg.StatusInvalid {
			if err := client.UpdateAny(uint(item.Id), map[string]any{
				"status":   dlg.StatusDoing,
				"progress": 1,
			}); err != nil {
				return err
			}
			isQuery = true
		}
	}

	if isQuery {
		// step2 异步 启动爬虫任务
		//searchKeyWords = keywords
		go queryGiteeWithCode(context.TODO(), uint(rsp.TaskId), AccessToken, 500, keywords)
	}

	return nil
}

// step2 异步获取Gitee数据 并写入数据库
func queryGiteeWithCode(ctx context.Context, taskId uint, token string, maxTotal uint, keyword []string) {
	var (
		ch         = make(chan GiteeChanUnit)
		list       = make([]*dlt.GiteeResult, 0, 300)
		conn       = dlt.NewResultModel()
		taskClient = dlt.NewTaskModel()
	)
	configValue, _ := redis.NewGiteeStrategyModel().GetStrategy(ctx)

	if configValue != searchStrategy {
		go func() { // 非token方式获取 step 2-1 异步获取gitee数据
			err := GiteeRepoSearchNoToken(ctx, keyword, int(maxTotal), ch)
			if err != nil {
				log.WithContextWarnf(ctx, "[Gitee] search keyword: %v Which using NoToken Method by task_id: %d failed: %v", keyword, taskId, err)
			}
		}()
	} else {
		go func() { // token方式获取 step 2-1 异步获取gitee数据
			err := GiteeRepoSearch(ctx, keyword, token, int(maxTotal), ch)
			if err != nil {
				log.WithContextWarnf(ctx, "[Gitee] search keyword: %v by task_id: %d failed: %v", keyword, taskId, err)
			}
		}()
	}

	repoUrls := make([]string, 0, 500)
	for v := range ch {
		repoUrls = append(repoUrls, v.RepoUrl)
		list = append(list, &dlt.GiteeResult{
			TaskId:      taskId,
			RepoName:    v.RepoName,
			RepoUrl:     v.RepoUrl,
			RepoDesc:    v.RepoDesc,
			CodeUrl:     v.CodeUrl,
			ScreenShot:  v.Screenshot,
			Sha:         v.Sha,
			Language:    v.Language,
			CodeSnippet: "", // 目前获取不到代码片段
		})
	}
	// step 2-2 将获取的gitee数据写入数据库 并更新任务进度
	upsert, err := conn.CompareAndUpdate(list)
	if err != nil {
		log.WithContextErrorf(ctx, "[Gitee] Gitee search with code in task_id: %d: %v", taskId, err)
	}

	insertRelation := make([]*dlt.Relation, 0)
	oldResult, _ := conn.RelationList(mysql.WithColumnValue("`task_id`", taskId))
	for _, v := range upsert {
		exist := false
		for _, x := range oldResult {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insertRelation = append(insertRelation, &dlt.Relation{TaskId: uint64(taskId), ResultId: v.Id})
		}
	}
	if errSave := conn.RelationSave(insertRelation); errSave != nil {
		log.WithContextErrorf(ctx, "[Gitee] Save task_id: %d relation result failure: %v", taskId, errSave)
	}

	err = taskClient.UpdateAny(taskId, map[string]any{"progress": 100, "status": dlt.StatusFinished})
	if err != nil {
		log.WithContextErrorf(ctx, "[Gitee] Update task_id: %d progress to 100 failure: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[Gitee] Task_id: %v, keyword: %v, task finished!", taskId, keyword)
	}
}

// GiteeTaskInfo 总库 任务详情
func GiteeTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := dlt.NewTaskModel().First(mysql.WithId(taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}
	rsp.TaskId = int64(info.Id)
	rsp.Keywords = dlg.KeywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

// GiteeTaskResult 总库 任务结果
func GiteeTaskResult(taskId uint64, rsp *pb.GitHubCodeResultResponse) error {

	l, err := dlt.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return err
	}

	for i := range l {
		rsp.Items = append(rsp.Items, &pb.GitHubCode{
			RepoName:    l[i].RepoName,
			RepoUrl:     l[i].RepoUrl,
			RepoDesc:    l[i].RepoDesc,
			CodeUrl:     l[i].CodeUrl,
			Sha:         l[i].Sha,
			CodeSnippet: l[i].CodeSnippet,
			Language:    l[i].Language,
			Screenshot:  l[i].ScreenShot,
		})
	}
	return nil
}

// 构造gitee API 搜索请求结构并启动crawlerService进行代理请求
func giteeCrawlerReq(ctx context.Context, url string, token string) (*crawlerPb.GetResponse, error) {
	param := &crawlerPb.GetRequest{
		Url:    url,
		Method: crawlerPb.MethodCurlGet,
		Headers: map[string]string{
			"Authorization": fmt.Sprintf("Bearer %s", token),
			"Content-Type":  "application/json;charset=UTF-8",
		},
		WaitTime: 30,
	}
	return crawlerPb.GetProtoClient().Get(ctx, param)
}

// GiteeRepoSearchNoToken step2-1 noToken
func GiteeRepoSearchNoToken(ctx context.Context, keywords []string, maxTotal int, ch chan<- GiteeChanUnit) error {
	defer close(ch)
	var (
		page       = 0
		perPage    = 20 // gitee 网站使用的是20每页
		currentNum = 0
		maxPage    = maxTotal / perPage
		sha, _     = SortAndHash(keywords)
	)
	if maxTotal%perPage != 0 {
		maxPage++
	}
	// step 2-1 更新爬虫并爬取数据
	for currentNum < maxTotal { // 若获取的数据总条数小于500 则持续访问
		// 2-1-1 构造请求url
		reqUrl := giteeRepoReqUrl(giteeSearchRepoAPINoToken, keywords, page, perPage)
		page += 1
		if page > maxPage {
			break
		}
		log.Info("请求URL---", reqUrl)
		log.WithContextInfof(ctx, "[Gitee]  githeeUrl-noToken: %s, Keyword: %v", reqUrl, keywords)
		// 2-1-2 发起请求并获取响应
		crawlerRes, err := giteeCrawlReqNoToken(ctx, reqUrl) // 获取code代码泄露的api请求的响应
		if err != nil {
			log.WithContextWarnf(ctx, "[Gitee] Search-noToken %v Error:%s", keywords, err.Error())
			break
		}
		// 2-1-3 解码响应body
		var resp NoTokenResp
		var item GiteeChanUnit
		if crawlerRes.Body == "" {
			log.WithContextWarnf(ctx, "[Gitee] Search-noToken %v 响应为空", keywords)
			break
		}
		err = json.Unmarshal([]byte(crawlerPb.DecodeBy(crawlerRes.Body)), &resp)
		if err != nil {
			log.WithContextErrorf(ctx, "[Gitee] Search-noToken %v json解码异常 Error:%s", keywords, err.Error())
		}
		for _, hit := range resp.Hits.Hits { // 2-1-4 将解码数据通过通道发送到对应数据
			if len(hit.Fields.RepoName) > 0 {
				item.RepoName = hit.Fields.RepoName[0]
			}
			if len(hit.Fields.RepoUrl) > 0 {
				item.RepoUrl = hit.Fields.RepoUrl[0]
			}
			if len(hit.Fields.Description) > 0 {
				item.RepoDesc = hit.Fields.Description[0]
			}
			if len(hit.Fields.Langs) == 0 {
				item.Language = Other
			} else {
				item.Language = hit.Fields.Langs[0]
			}
			item.Sha = sha
			ch <- item
		}
		log.WithContextInfof(ctx, "[Gitee] Search-noToken %v 获取数据条数: %d", keywords, len(resp.Hits.Hits))
		time.Sleep(1 * time.Second)
		currentNum++
	}
	return nil
}

func giteeCrawlReqNoToken(ctx context.Context, url string) (*crawlerPb.GetResponse, error) {
	param := &crawlerPb.GetRequest{
		Url:    url,
		Method: crawlerPb.MethodCurlGet,
		Headers: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
		},
		WaitTime: 30,
	}
	return crawlerPb.GetProtoClient().Get(ctx, param)
}
