package dlp

import (
	"context"
	"errors"
	"fmt"
	"github.com/tidwall/gjson"
	"gorm.io/gorm"
	"net/url"
	"strconv"
	"strings"
	"time"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	magicalsearch "micro-service/middleware/mysql/dataleak_magicalsearch"
)

// 设置奇妙搜索的请求url  步骤1
func magicalsearchReqUrl(keywords []string, page int) []string {
	queryword := url.QueryEscape(keywords[0]) //转中文格式为浏览器传输的字符格式

	// 云盘搜索
	reqUrl_yiyunsou := "https://www.magicalsearch.cc/api/pshou/getData?type=%E4%BA%91%E7%9B%98%E6%90%9C%E7%B4%A2&word="
	reqUrl_yiyunsou += queryword

	// 阿里网盘
	reqUrl_aliNetdisk := "https://www.magicalsearch.top/api/pshou/getNextPage?url=https://api.upyunso2.com/" + "search?s_type=2@page=" + strconv.Itoa(page)
	reqUrl_aliNetdisk += "@keyword=" + queryword + "&website=%E9%98%BF%E9%87%8C%E7%BD%91%E7%9B%98"
	reqUrl := []string{reqUrl_yiyunsou, reqUrl_aliNetdisk}
	return reqUrl
}

// 获取奇妙搜索-阿里网盘的响应数据 步骤2-1
func getMagicalsearchResponse_ali(ctx context.Context, rsp *pb.NetDiskResponse, keywords []string, page int) error {
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:     magicalsearchReqUrl(keywords, page)[1],
		Method:  crawlerPb.MethodCurlGet,
		Options: map[string]string{"proxy": "disable"},
	}, utils.SetRpcTimeoutOpt(120))
	if err != nil {
		log.WithContextWarnf(ctx, "[magicalsearch]: Search %v Error:%s", keywords, err.Error())
		return err
	}
	body := crawlerPb.DecodeBy(crawlerRes.Body)
	//去除开头的"
	str := strings.TrimPrefix(string(body), `"`)
	//去除结尾的引号
	content := strings.TrimSuffix(str, `"`)
	//将返回的字符串改成json格式，第一步去除api返回的
	modified := strings.ReplaceAll(content, "\\", "")
	result := gjson.Get(modified, "result.items")

	if len(result.Array()) > 1 {
		for _, everyData := range result.Array() {
			Title := gjson.Get(everyData.String(), "title").String()
			if strings.Contains(Title, "style") {
				continue
			}
			// 如果标题里面不包含搜索的关键词的话，遍历content字段，取content里面的title，追加到这个数据的title
			searchKeyword := keywords[0]
			if !strings.Contains(Title, searchKeyword) {
				contentArr := gjson.Get(everyData.String(), "content").Array()
				if len(contentArr) > 0 {
					for _, contentData := range contentArr {
						contentTitle := gjson.Get(contentData.String(), "title").String()
						if strings.Contains(contentTitle, searchKeyword) {
							Title = Title + "_" + contentTitle
							break
						}
					}
				}
			}
			Url := gjson.Get(everyData.String(), "page_url").String()
			rsp.Result = append(rsp.Result, &pb.NetDisk{
				FileName:   Title,
				OriginUrl:  Url,
				Url:        Url,
				Screenshot: "",
			})
		}
	} else {
		log.WithContextWarnf(ctx, "[magicalsearch]: 数据为空或者被封禁1小时不让访问 keywords: %v, response:%v", keywords, modified)
	}
	return nil
}

// 获取 奇妙搜索-云盘搜索 模块的结果
// 子步骤3: 拼接地址即可获取下载地址,组合成需要的字段结构
func getMagicalsearchResponse_yiyunsou(ctx context.Context, rsp *pb.NetDiskResponse, keywords []string, page int) error {
	// 子步骤1
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:     magicalsearchReqUrl(keywords, page)[0],
		Method:  crawlerPb.MethodCurlGet,
		Options: map[string]string{"proxy": "disable"},
	}, utils.SetRpcTimeoutOpt(120))
	if err != nil {
		log.WithContextWarnf(ctx, "[magicalsearch]: Search %v Error:%s", keywords, err.Error())
		return err
	}
	body := crawlerPb.DecodeBy(crawlerRes.Body)
	//去除开头的"
	str := strings.TrimPrefix(string(body), `"`)
	//去除结尾的引号
	content := strings.TrimSuffix(str, `"`)
	//将返回的字符串改成json格式，第一步去除api返回的\
	modified := strings.ReplaceAll(content, "\\", "")
	result := gjson.Get(modified, "data.list")
	for _, everyData := range result.Array() {
		Title := gjson.Get(everyData.String(), "disk_name").String()
		Url := gjson.Get(everyData.String(), "link").String()
		rsp.Result = append(rsp.Result, &pb.NetDisk{
			FileName:   Title,
			OriginUrl:  Url,
			Url:        Url,
			Screenshot: "",
		})
	}
	return nil
}

// 步骤 3 对请求的数据做条数处理，最多为100条，不够则取前10页数据
func searchMagicalsearch(ctx context.Context, keywords []string, rsp *pb.NetDiskResponse, maxTotal int) error {
	err := getMagicalsearchResponse_yiyunsou(ctx, rsp, keywords, 1)
	if err != nil {
		return nil
	}
	var currentPage int
	for len(rsp.Result) < maxTotal {
		currentPage++
		if currentPage > 5 {
			return nil
		}
		err = getMagicalsearchResponse_ali(ctx, rsp, keywords, currentPage)
		if err != nil {
			return err
		}
		time.Sleep(5 * time.Second)
	}
	if len(rsp.Result) > maxTotal {
		rsp.Result = rsp.Result[:maxTotal]
	}
	return nil
}

// 步骤 4
func getMagicalsearchSearchResult(ctx context.Context, taskId uint64, keywords []string) error {
	var rsp = new(pb.NetDiskResponse)
	err := searchMagicalsearch(ctx, keywords, rsp, 100) // 最大50条
	if err != nil {
		return err
	}
	got := make([]*magicalsearch.MagicalsearchResult, 0, len(rsp.Result))
	for i := range rsp.Result {
		got = append(got, &magicalsearch.MagicalsearchResult{
			TaskId:     uint(taskId),
			FileName:   rsp.Result[i].FileName,
			FileUrl:    rsp.Result[i].Url,
			Screenshot: rsp.Result[i].Screenshot,
		})
	}

	client := magicalsearch.NewResultModel()
	upsert, err := client.Upsert(got)
	if len(upsert) == 0 {
		return err
	}

	list, err := client.RelationListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("get task_id: %d relation result failed: %v", taskId, err)
	}

	var insert = make([]*magicalsearch.Relation, 0, len(got))
	for _, v := range upsert {
		exist := false
		for _, x := range list {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insert = append(insert, &magicalsearch.Relation{TaskId: taskId, ResultId: v.Id})
		}
	}

	err = client.RelationSave(insert)
	return err
}

// MagicalSearchTaskSync 步骤5 同步信息
func MagicalSearchTaskSync(ctx context.Context, taskId uint64, keywords []string) {
	//开启爬虫
	err := getMagicalsearchSearchResult(ctx, taskId, keywords)
	if err != nil {
		log.WithContextError(ctx, "[magicalsearch] task_id: %d, keywords: %v,magicalsearhSearchTaskSync failed: %v", keywords, err)
	}
	// 爬虫数据进度更新
	client := magicalsearch.NewTaskModel()
	err = client.UpdateAny(uint(taskId), map[string]any{"status": magicalsearch.StatusFinished, "progress": 100})
	if err != nil {
		log.WithContextErrorf(ctx, "[magicalsearch] update task_id: %d progress to 100 failed: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[magicalsearch] task_id: %d, keywords: %v, task finished!", taskId, keywords)
	}
}

// MagicalsearchByTask 创建爬虫任务 并将内容写入数据库中 步骤6
func MagicalsearchByTask(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keywordHash, keywords := SortAndHash(req.Keyword)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	var (
		isQuery = false
		client  = magicalsearch.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		var info = magicalsearch.Task{Keyword: keywordJoin(keywords), KeywordHash: keywordHash, Progress: 1, Status: magicalsearch.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt, 1) {
			errUpdate := client.UpdateAny(uint(item.Id), map[string]any{"status": magicalsearch.StatusDoing, "progress": 1})
			if errUpdate != nil {
				return errUpdate
			}
			isQuery = true
		}

	}

	if isQuery {
		//	异步获取秒搜网盘数据
		go MagicalSearchTaskSync(context.Background(), rsp.TaskId, req.Keyword)
	}
	return nil
}

// MagicalsearchTaskInfo 获取任务信息
func MagicalsearchTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := magicalsearch.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress

	return nil
}

// MagicalsearchTaskResult 获取任务结果
func MagicalsearchTaskResult(taskId uint64, rsp *pb.Wangpan56ResultResponse) error {
	l, err := magicalsearch.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return err
	}

	items := make([]*pb.Wangpan56ResultResponseItem, 0, len(l))
	for i := range l {
		items = append(items, &pb.Wangpan56ResultResponseItem{
			Id:         l[i].Id,
			FileName:   l[i].FileName,
			FileUrl:    l[i].FileUrl,
			Screenshot: l[i].Screenshot,
		})
	}

	rsp.Items = items

	return nil
}

// StringCast 存在多个@符号的字符串，取第一个@符号前的字符串
// 例如 ySN6tQM2dFZ@LEag5dYWpR9@TrhdfVADrWV 返回: ySN6tQM2dFZ
func StringCast(str string) string {
	res := str
	for i := 0; i < len(str); i++ {
		if str[i] == '@' {
			res = str[:i]
			break
		}
	}

	return res
}
