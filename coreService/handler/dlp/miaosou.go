package dlp

import (
	"context"
	"errors"
	"fmt"
	"github.com/tidwall/gjson"
	"net/url"
	"time"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	miaosou "micro-service/middleware/mysql/dataleak_miaosou"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"gorm.io/gorm"
)

func searchMiaosou(ctx context.Context, keywords []string, rsp *pb.NetDiskResponse, maxTotal int) error {
	var currentPage int
	for len(rsp.Result) < maxTotal {
		currentPage++
		if currentPage > 10 {
			return nil
		}
		getMiaosouResponse(ctx, rsp, keywords, currentPage, maxTotal)
		time.Sleep(3 * time.Second)
	}
	if len(rsp.Result) > maxTotal {
		rsp.Result = rsp.Result[:maxTotal]
	}
	return nil
}

func miaoSouReqUrl(keywords []string, page int) string {
	reqUrl := fmt.Sprintf("https://miaosou.fun/api/secendsearch?name=%s&pageNo=%d", url.QueryEscape(keywords[0]), page)
	return reqUrl
}

func getMiaosouResponse(ctx context.Context, rsp *pb.NetDiskResponse, keywords []string, page, maxTotal int) error {
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    miaoSouReqUrl(keywords, page),
		Method: crawlerPb.MethodCurlGet,
	}, utils.SetRpcTimeoutOpt(120))
	if err != nil {
		log.WithContextWarnf(ctx, "[miaosou]: Search %v Error:%s", keywords, err.Error())
		return err
	}
	body := crawlerPb.DecodeBy(crawlerRes.Body)
	if err != nil {
		return err
	}
	result := gjson.Get(body, "data.list")
	for _, everyData := range result.Array() {
		name := gjson.Get(everyData.String(), "fileInfos.0.fileName").String()
		url := gjson.Get(everyData.String(), "url").String()
		rsp.Result = append(rsp.Result, &pb.NetDisk{
			FileName:   name,
			OriginUrl:  url,
			Url:        url,
			Screenshot: "",
		})
	}
	return nil
}

// WangpanMiaosouSearchByTask 下发任务
func WangpanMiaosouSearchByTask(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keywordHash, keywords := SortAndHash(req.Keyword)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	var (
		isQuery = false
		client  = miaosou.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		var info = miaosou.Task{Keyword: keywordJoin(keywords), KeywordHash: keywordHash, Progress: 1, Status: miaosou.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt, 1) {
			errUpdate := client.UpdateAny(uint(item.Id), map[string]any{"status": miaosou.StatusDoing, "progress": 1})
			if errUpdate != nil {
				return errUpdate
			}
			isQuery = true
		}
	}

	if isQuery {
		//	异步获取秒搜网盘数据
		go MiaosouSearchTaskSync(context.Background(), rsp.TaskId, req.Keyword)
	}

	return nil
}

func getMiaosouSearchResult(ctx context.Context, taskId uint64, keywords []string) error {
	var rsp = new(pb.NetDiskResponse)
	err := searchMiaosou(ctx, keywords, rsp, 50) // 最大50条
	if err != nil {
		return err
	}
	got := make([]*miaosou.Pan56Result, 0, len(rsp.Result))
	for i := range rsp.Result {
		got = append(got, &miaosou.Pan56Result{
			TaskId:     uint(taskId),
			FileName:   rsp.Result[i].FileName,
			FileUrl:    rsp.Result[i].Url,
			Screenshot: rsp.Result[i].Screenshot,
		})
	}

	client := miaosou.NewResultModel()
	upsert, err := client.Upsert(got)
	if len(upsert) == 0 {
		return err
	}

	list, err := client.RelationListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("get task_id: %d relation result failed: %v", taskId, err)
	}

	var insert = make([]*miaosou.Relation, 0, len(got))
	for _, v := range upsert {
		exist := false
		for _, x := range list {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insert = append(insert, &miaosou.Relation{TaskId: taskId, ResultId: v.Id})
		}
	}

	err = client.RelationSave(insert)
	return err
}

func MiaosouSearchTaskSync(ctx context.Context, taskId uint64, keywords []string) {
	err := getMiaosouSearchResult(ctx, taskId, keywords)
	if err != nil {
		log.WithContextError(ctx, "[miaosou] task_id: %d, keywords: %v, miaosouSearchTaskSync failed: %v", keywords, err)
	}

	client := miaosou.NewTaskModel()
	err = client.UpdateAny(uint(taskId), map[string]any{"status": miaosou.StatusFinished, "progress": 100})
	if err != nil {
		log.WithContextErrorf(ctx, "[miaosou] update task_id: %d progress to 100 failed: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[miaosou] task_id: %d, keywords: %v, task finished!", taskId, keywords)
	}
}

// WangpanMiaosouTaskInfo 获取任务详情
func WangpanMiaosouTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := miaosou.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

// WangpanMiaosouTaskResult 获取任务结果
func WangpanMiaosouTaskResult(taskId uint64, rsp *pb.Wangpan56ResultResponse) error {
	l, err := miaosou.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return err
	}

	items := make([]*pb.Wangpan56ResultResponseItem, 0, len(l))
	for i := range l {
		items = append(items, &pb.Wangpan56ResultResponseItem{
			Id:         l[i].Id,
			FileName:   l[i].FileName,
			FileUrl:    l[i].FileUrl,
			Screenshot: l[i].Screenshot,
		})
	}

	rsp.Items = items
	return nil
}
