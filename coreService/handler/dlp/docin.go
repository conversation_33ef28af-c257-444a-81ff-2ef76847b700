package dlp

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	docin "micro-service/middleware/mysql/dataleak_docin"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/antchfx/htmlquery"
	"golang.org/x/net/html"
	"gorm.io/gorm"
)

func GetDouin(ctx context.Context, rsp *pb.BaiduLibraryResponse, keyword string) error {
	l, err := GetDocinSearch(ctx, keyword, 10)
	if err != nil {
		return err
	}

	for i := range l {
		rsp.Result = append(rsp.Result, &pb.BaiduLibrary{
			Title:      l[i].Title,
			Url:        l[i].Url,
			Screenshot: l[i].Screenshot,
		})
	}
	return nil
}

type DocinItem struct {
	Title      string
	Url        string
	Screenshot string
}

func GetDocinSearch(ctx context.Context, keyword string, maxReqPage int) ([]*DocinItem, error) {
	var result = make([]*DocinItem, 0, 10*maxReqPage)
	for page := 1; page <= maxReqPage; page++ {
		l, viewPage, err := docinWithPage(ctx, keyword, page)
		if err != nil {
			// 如果已经爬取一部分数据，跳出，然后把这些已经爬取到的数据入库
			if result != nil {
				break
			}
			return nil, err
		}
		result = append(result, l...)
		if page+1 > viewPage {
			break
		}
	}

	return result, nil
}

const (
	docinUrl = "https://www.docin.com"
	docinExp = `//div[@class="wrap_elastic clear"]/div[@class="result-list-wrapper"]/div[@class="result-list result-list-tips"]`
)

func docinReqUrl(keyword string, page int) string {
	reqUrl := fmt.Sprintf("%s/search.do?nkey=%s&searchcat=1001", docinUrl, keyword)
	if page > 1 {
		reqUrl += fmt.Sprintf("&fnorePage=&currentPage=%d", page)
	}
	return reqUrl
}

func docinWithPage(ctx context.Context, keyword string, page int) ([]*DocinItem, int, error) {
	var (
		list        = make([]*DocinItem, 0, 10)
		viewMaxpage int // 当前页可看到的最大页
	)
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    docinReqUrl(keyword, page),
		Method: crawlerPb.MethodChromeGet,
	}, utils.SetRpcTimeoutOpt(30), utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return nil, 0, err
	}

	body := crawlerPb.DecodeBy(crawlerRes.Body)
	doc, err := htmlquery.Parse(strings.NewReader(body))
	if err != nil {
		return nil, 0, nil
	}
	// 默认每页最大10页
	viewMaxpage = 10

	nodes, err := htmlquery.QueryAll(doc, `//dd[@class="fc-baidu title"]`)
	if err != nil {
		return nil, 0, nil
	}

	for _, node := range nodes {
		list = append(list, parseHtmlNode(node))
	}
	return list, viewMaxpage, nil
}

func parseHtmlNode(node *html.Node) *DocinItem {
	var a2 = htmlquery.FindOne(node, "//a[2]")
	address := htmlquery.SelectAttr(a2, "href")
	var newurl string
	if address != "" {
		if strings.Contains(address, "docin.com") {
			newurl = address
		} else {
			newurl, _ = url.JoinPath(docinUrl, address)
		}
	}
	return &DocinItem{
		Title:      htmlquery.SelectAttr(a2, "title"),
		Url:        newurl,
		Screenshot: "",
	}
}

// DocinSearchByTask 数据泄露-豆丁
func DocinSearchByTask(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keyword := firstKeyword(req.Keyword)
	if keyword == "" {
		return errors.New("关键字不可为空")
	}

	var (
		isQuery     = false
		keywordHash = utils.Md5sHash(keyword, false)
		client      = docin.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		var info = docin.Task{Keyword: keyword, KeywordHash: keywordHash, Progress: 1, Status: docin.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt, item.Progress) {
			errUpdate := client.UpdateAny(uint(item.Id), map[string]any{"status": docin.StatusDoing, "progress": 1})
			if errUpdate != nil {
				return errUpdate
			}
			isQuery = true
		}
	}

	if isQuery {
		// 异步获取豆丁数据
		go docinSearchTaskSync(ctx, rsp.TaskId, keyword)
	}

	return nil
}

func docinSearchTaskSync(ctx context.Context, taskId uint64, keyword string) {
	err := docinSearch(ctx, taskId, keyword)
	if err != nil {
		log.WithContextErrorf(ctx, "[Docin] docSearch keyword: %s failed: %v", keyword, err)
	}

	err = docin.NewTaskModel().UpdateAny(uint(taskId), map[string]any{"status": docin.StatusFinished, "progress": 100})
	if err != nil {
		log.WithContextErrorf(ctx, "[Docin] Update task_id: %d progress to 100 failed: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[Docin] task_id: %d, keyword: %s, task finished!", taskId, keyword)
	}
}

func docinSearch(ctx context.Context, taskId uint64, keyword string) error {
	docinResult, err := GetDocinSearch(ctx, keyword, 20) // fetch 5 pages, max 200 items
	if err != nil {
		return fmt.Errorf("[Docin]Get task_id->%d docin data failed in task mode, because of %v", taskId, err)
	}
	got := make([]*docin.DocinResult, 0, len(docinResult))
	for i := range docinResult {
		got = append(got, &docin.DocinResult{
			TaskId:     uint(taskId),
			DocTitle:   docinResult[i].Title,
			DocUrl:     docinResult[i].Url,
			Screenshot: docinResult[i].Screenshot,
		})
	}

	resultClient := docin.NewResultModel()
	// 获取当前任务的关联结果
	relationList, err := resultClient.RelationListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return err
	}

	// 本次获取的数据upsert到总库
	upsertList, err := resultClient.Upsert(got)
	if err != nil {
		return err
	}

	var insertList = make([]*docin.Relation, 0, len(got))
	for _, v := range upsertList {
		exist := false
		for _, x := range relationList {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insertList = append(insertList, &docin.Relation{TaskId: taskId, ResultId: v.Id})
		}
	}

	// 更新关联结果
	err = resultClient.RelationSave(insertList)
	return err
}

func DocinTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := docin.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress

	return nil
}

func DocinTaskResult(taskId uint64) ([]*pb.DocinResultResponseItem, error) {
	l, err := docin.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return nil, err
	}

	var result = make([]*pb.DocinResultResponseItem, 0, len(l))
	for i := range l {
		result = append(result, &pb.DocinResultResponseItem{
			Id:         l[i].Id,
			Title:      l[i].DocTitle,
			Url:        l[i].DocUrl,
			Screenshot: l[i].Screenshot,
		})
	}
	return result, nil
}
