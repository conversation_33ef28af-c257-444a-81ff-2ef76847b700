package dlp

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_FetchDoc88(t *testing.T) {
	_, err := fetchDoc88(context.TODO(), doc88ReqUrl(1, []string{"建设银行"}))
	assert.Nil(t, err)
}

func Test_doc88ReqUrl(t *testing.T) {
	r := doc88ReqUrl(1, []string{"建设银行", "北京银行"})
	fmt.Println(r)
}

func Test_fetchDoc88WithPage(t *testing.T) {
	l, err := fetchDoc88WithPage(context.TODO(), []string{"北京银行"}, 1)
	assert.Nil(t, err)
	for _, v := range l {
		fmt.Println(v.Title, v.UploadDate)
	}
}
