package dlp

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"testing"

	"github.com/joho/godotenv"

	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

var (
	chNew = make(chan GitcodeChanUnit)
)

func TestGitcodeSearchCodeResult(t *testing.T) {
	_ = godotenv.Load("../../../.env")

	cfg.InitLoadCfg()
	log.Init()
	ctx := context.Background()
	token := getGitcodePersonalToken()
	maxTotal := 20
	keyword := []string{"test"}

	// 创建一个 WaitGroup
	var wg sync.WaitGroup
	// 添加一个等待的 goroutine
	wg.Add(1)

	go func() {
		for data := range chNew {
			v := reflect.ValueOf(data)
			t := v.Type()
			for i := 0; i < v.NumField(); i++ {
				fmt.Printf("%s: %v\n", t.Field(i).Name, v.Field(i).Interface())
			}
			fmt.Println("----------------------") // 用于区分不同的数据
		}
	}()

	err := GitcodeRepoSearch(ctx, keyword, token, maxTotal, chNew)
	if err != nil {
		fmt.Printf("[Gitcode] search keyword: %v  failed: %v\n", keyword, err)
	}

	// 等待所有 goroutine 完成
	wg.Wait()
}
