package dlp

import (
	"context"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/yuque"
	"net/url"
	"strconv"
)

func SearchYuque(query string, page int64) (*pb.YuequeLibraryResponse, error) {
	unescape, _ := url.QueryUnescape(query)
	r, err := yuque.NewDefaultClient("LsRGpaeJqiIKwc8XZ8VGRMr5xrpjb7x5i8haQtLn").
		Search(context.Background(), &yuque.SearchRequest{Query: unescape, Type: "doc", Scope: "/", Tab: "public", Page: page})
	if err != nil {
		return nil, err
	}

	yuequeDatas := make([]*pb.YuequeLibrary, 0, len(r.Data))
	for i := range r.Data {
		yuequeDatas = append(yuequeDatas, &pb.YuequeLibrary{
			Name:   r.Data[i].Target.Title,
			UserId: strconv.Itoa(r.Data[i].Target.UserId),
			Url:    r.Data[i].Url,
			Type:   r.Data[i].Type,
		})
	}
	return &pb.YuequeLibraryResponse{Total: int64(r.Meta.Total), Data: yuequeDatas}, nil
}
