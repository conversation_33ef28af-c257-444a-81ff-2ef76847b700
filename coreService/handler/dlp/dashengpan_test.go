package dlp

import (
	"context"
	core "micro-service/coreService/proto"
	"testing"
	"time"
)

func TestDashengpanTaskSync(t *testing.T) {
	initCfg()
	var resp core.NetDiskResponse
	searchDashengpan(context.TODO(), []string{"渗透"}, &resp, 10, "baidu")
	time.Sleep(1 * time.Second)
	searchDashengpan(context.TODO(), []string{"渗透"}, &resp, 20, "aliyundrive")
	time.Sleep(1 * time.Second)
	searchDashengpan(context.TODO(), []string{"渗透"}, &resp, 30, "quark")
	time.Sleep(1 * time.Second)
	searchDashengpan(context.TODO(), []string{"渗透"}, &resp, 40, "xunleipan")
	for _, v := range resp.Result {
		t.Logf("name: %+v", v)
	}
}
