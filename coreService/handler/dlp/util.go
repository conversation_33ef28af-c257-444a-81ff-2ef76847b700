package dlp

import (
	"micro-service/pkg/cfg"
	"sort"
	"strings"
	"time"

	"micro-service/pkg/utils"

	"golang.org/x/exp/constraints"
)

const force = 1

func batchProgress(current, batch, total int) float32 {
	maxBatch := total / batch
	if total%batch > 0 {
		maxBatch++
	}
	return float32(current * 100 / maxBatch)
}

func SortAndHash(l []string) (hash string, keywords []string) {
	list, _ := utils.ListFunc(l, func(s string) (string, bool) {
		return strings.ToLower(s), s != ""
	})
	list = utils.ListDistinct(list)
	if len(list) == 0 {
		return "", nil
	}
	sort.Strings(list)
	return utils.Md5sHash(keywordJoin(list), false), list
}

func keywordJoin(l []string) string {
	return strings.Join(l, "|+|")
}

func keywordSplit(keyword string) []string {
	return strings.Split(keyword, "|+|")
}

func isTaskUpdate[T constraints.Integer](force T, updatedAt time.Time, progress float32) bool {
	return force == 1 || time.Now().After(updatedAt.Add(7*utils.Day)) || progress == 0
}

func firstKeyword(keywords []string) string {
	for _, v := range keywords {
		if v != "" {
			return v
		}
	}
	return ""
}

func cacheIsExpired(t time.Time) bool {
	cacheDay := int(cfg.LoadCommon().DataLeakCacheDay)
	return t.AddDate(0, 0, cacheDay).Before(time.Now())
}
