package dlp

import (
	"context"
	"errors"
	"fmt"
	"golang.org/x/text/encoding/simplifiedchinese"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/google/uuid"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	redisInit "micro-service/initialize/redis"
	"micro-service/middleware/mysql"
	dlg "micro-service/middleware/mysql/dataleak_github"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"

	"github.com/tidwall/gjson"
	"gorm.io/gorm"
)

type Github struct {
	Tokens []string
}

// var github Github
//
// func init() {
// 	github = Github{
// 		Tokens: cfg.LoadCommon().GitHubTokens,
// 	}
// }

func GetGitHubClient() *Github {
	var gt = Github{
		Tokens: cfg.LoadCommon().GitHubTokens,
	}
	return &gt
}

const githubCodeSearchApi = "https://api.github.com/search/code"
const githubRepoSearchApi = "https://api.github.com/search/repositories"

func githubReqUrl(keywords string, apiAddress string, page, size int) string {
	q := fmt.Sprintf("q=%s&per_page=%d&page=%d", keywords, size, page)
	return apiAddress + "?" + q
}

// 构造api搜索请求结构，启动crawlerService来获取响应结果
func githubCrawlerReq(ctx context.Context, url, token string) (*crawlerPb.GetResponse, error) {
	param := &crawlerPb.GetRequest{
		Url:    url,
		Method: crawlerPb.MethodCurlGet,
		Headers: map[string]string{
			"Authorization":        fmt.Sprintf("Bearer %s", token), // github账号的token，来自配置文件
			"Accept":               "application/vnd.github.text-match+json",
			"X-GitHub-Api-Version": "2022-11-28", // github api 版本 截止至2024年，版本未变化
		},
		WaitTime: 30,
	}
	return crawlerPb.GetProtoClient().Get(ctx, param)
}

// 用于github空页面的重试
func (g *Github) retrySearchWithPage(ctx context.Context, url string, maxReq int) *crawlerPb.GetResponse {
	index := 0
	for index <= maxReq {
		index++
		token := g.getToken()
		if token == "" {
			continue
		}

		crawlerRes, err := githubCrawlerReq(ctx, url, token)
		if err != nil {
			log.WithContextErrorf(ctx, "[GitHub]Search retry %v", err)
			continue
		}
		if crawlerRes.Code == http.StatusForbidden {
			g.setTokenLimited(token)
			continue
		}

		g.updateToken(token)
		body := crawlerPb.DecodeBy(crawlerRes.Body)
		totalCount := gjson.Get(body, "total_count").Int()
		if totalCount > 0 {
			return crawlerRes
		}
	}
	return nil
}

// GetSearchCodeResult   step3 获取github数据 核心逻辑
func (g *Github) GetSearchCodeResult(ctx context.Context, keywords []string, maxTotal int, ch chan<- dlg.GithubResult) error {
	defer close(ch)
	var (
		page           = 0
		perPage        = 100
		currentCodeNum = 0
		currentRepoNum = 0
		maxPage        = maxTotal / perPage
		totalNotEmpty  bool
		emptyCodePages = make([]int, 0, 10)
		emptyRepoPages = make([]int, 0, 10)
	)
	if maxTotal%perPage != 0 {
		maxPage++
	}

	// 转换Keywords
	convertedKeywords := convertKeywords(keywords)

	// 获取数据
	for currentCodeNum+currentRepoNum < maxTotal {
		page += 1
		if page > maxPage {
			break
		}

		token := g.getToken()
		if token == "" {
			log.WithContextWarnf(ctx, "[GitHub] Search %v Error:%s", keywords, "Token Empty")
			continue
		}

		// step3-1 构造请求url
		reqCodeUrl := githubReqUrl(convertedKeywords, githubCodeSearchApi, page, perPage) // https://api.github.com/search/code + keywords + page + perPage
		reqRepoUrl := githubReqUrl(convertedKeywords, githubRepoSearchApi, page, perPage) // https://api.github.com/search/repositories  + keywords + page + perPage
		// log.WithContextInfof(ctx, "[GitHub]  githubUrl-codeAPI: %s, Keyword: %v", reqCodeUrl, keywords)
		// log.WithContextInfof(ctx, "[GitHub]  githubUrl-repoAPI: %s, Keyword: %v", reqRepoUrl, keywords)

		// step3-2 发起请求并获取响应
		crawlerCodeRes, err := githubCrawlerReq(ctx, reqCodeUrl, token) // 获取code代码泄露的api请求的响应
		if err != nil {
			log.WithContextWarnf(ctx, "[GitHub] Search %v Error:%s", keywords, err.Error())
			break
		}
		crawlerRepoRes, err := githubCrawlerReq(ctx, reqRepoUrl, token) // 获取repo仓库泄露的api请求的响应
		if err != nil {
			log.WithContextWarnf(ctx, "[GitHub] Search %v Error:%s", keywords, err.Error())
			break
		}

		// step3-3  解码响应内容并通过channel发送数据到step2
		codeBody := crawlerPb.DecodeBy(crawlerCodeRes.Body)
		if crawlerCodeRes.Code == http.StatusForbidden {
			g.setTokenLimited(token)
			log.WithContextWarnf(ctx, "[GitHub] Request url: %s, token->%s, err: %s", reqCodeUrl, token, gjson.Get(codeBody, "message").String())
			continue
		}
		repoBody := crawlerPb.DecodeBy(crawlerRepoRes.Body)
		if crawlerRepoRes.Code == http.StatusForbidden {
			g.setTokenLimited(token)
			log.WithContextWarnf(ctx, "[GitHub] Request url: %s, token->%s, err: %s", reqRepoUrl, token, gjson.Get(repoBody, "message").String())
			continue
		}

		g.updateToken(token)

		// func SeekBodyDataToChannel( body string, ch  channel )
		// 遍历codeBody中的数据 解析响应内容 -- start
		for _, item := range gjson.Get(codeBody, "items").Array() {
			if currentCodeNum > maxTotal/2 {
				break
			}
			codeSnippet := make([]string, 0, 10)
			for _, v := range item.Get("text_matches").Array() {
				codeSnippet = append(codeSnippet, v.Get("fragment").String())
			}
			repoDesc := item.Get("repository.description").String()
			if repoDesc == "" {
				repoDesc = "No description, website, or topics provided."
			}
			repoDesc, _ = formatString(repoDesc, 1000)
			ch <- dlg.GithubResult{
				CodeUrl:     item.Get("html_url").String(),
				RepoUrl:     item.Get("repository.html_url").String(),
				RepoDesc:    repoDesc,
				RepoName:    item.Get("repository.full_name").String(),
				Sha:         item.Get("sha").String(),
				CodeSnippet: strings.Join(codeSnippet, `\n`),
			}
			currentCodeNum++
		}
		log.WithContextInfof(ctx, "[GitHub] github_url: %s, Keyword: %v,Status ReqURl is Running  ", reqCodeUrl, keywords)
		// 遍历codeBody中的数据 解析响应内容 -- end

		// 遍历repoBody中的数据 解析仓库api的内容 ---start
		for _, item := range gjson.Get(repoBody, "items").Array() {
			if currentRepoNum > maxTotal/2 {
				break
			}
			//var html_url string := item.Get("html_url").String()
			uuidWithHyphen := uuid.New()
			unique_ID := fmt.Sprintf("%s", uuidWithHyphen)
			unique_ID = unique_ID[0:15] // 截取前15个字符
			repoDesc := item.Get("description").String()
			if repoDesc == "" {
				repoDesc = "No description, website, or topics provided."
			}
			repoDesc, _ = formatString(repoDesc, 1000)
			ch <- dlg.GithubResult{
				CodeUrl:  item.Get("html_url").String(), // 仓库接口无对应字段，写入 repoUrl
				RepoUrl:  item.Get("html_url").String(),
				RepoDesc: repoDesc,
				RepoName: item.Get("full_name").String(),
				Sha:      unique_ID + "-repo",
				//Sha:         "repo", // 将sha写入数据库判断是否实现repo功能
			}
			currentRepoNum++
		}
		log.WithContextInfof(ctx, "[GitHub] github_url: %s, Keyword: %v,Repo ReqURl is Running  ", reqRepoUrl, keywords)
		// 遍历repoBody中的数据 解析仓库api的内容 -- end

		codeCount := gjson.Get(codeBody, "total_count").Int()
		repoCount := gjson.Get(repoBody, "total_count").Int()
		totalCount := codeCount + repoCount
		switch {
		case codeCount == 0:
			emptyCodePages = append(emptyCodePages, page)
			fallthrough
		case repoCount == 0:
			emptyRepoPages = append(emptyRepoPages, page)
			fallthrough
		case totalCount > 0:
			totalNotEmpty = true
		}
		//log.WithContextInfof(ctx, "[GitHub] github_url: %s, Keyword: %v, codeTotal: %d", reqCodeUrl, keywords, codeCount)
		//log.WithContextInfof(ctx, "[GitHub] github_url: %s, Keyword: %v, repoTotal: %d", reqRepoUrl, keywords, repoCount)

		if totalCount != 0 && currentCodeNum+currentRepoNum > int(totalCount) {
			break
		}
		time.Sleep(5 * time.Second)
	}
	// step 3-4 特殊情况：若存在未成功抓取内容的页面，则重新尝试将对应页面爬取
	if totalNotEmpty && (len(emptyCodePages) > 0 || len(emptyRepoPages) > 0) {
		log.WithContextInfof(ctx, "[GitHub] Existing pages which are not getting response. ")
		switch {
		case len(emptyCodePages) > 0: // 若存在未成功页面 则重新爬取code
			for _, v := range emptyCodePages {
				reqCodeUrl := githubReqUrl(convertedKeywords, githubCodeSearchApi, v, perPage)
				rspCode := g.retrySearchWithPage(ctx, reqCodeUrl, 5)
				if rspCode == nil {
					continue
				}
				getRetryCodeSearchResult(rspCode, ch)
			}
			fallthrough // 可能存在 emptyCodePages ==0 &&  emptyRepoPages== 0 的极端情况
		case len(emptyRepoPages) > 0: // 若存在未成功页面 则重新爬取repo
			for _, v := range emptyRepoPages {
				reqRepoUrl := githubReqUrl(convertedKeywords, githubRepoSearchApi, v, perPage)
				rspRepo := g.retrySearchWithPage(ctx, reqRepoUrl, 5)
				if rspRepo == nil {
					continue
				}
				getRetryRepoSearchResult(rspRepo, ch)
			}
		}
	}
	return nil
}

func getRetryCodeSearchResult(rsp *crawlerPb.GetResponse, ch chan<- dlg.GithubResult) {
	for _, item := range gjson.Get(crawlerPb.DecodeBy(rsp.Body), "items").Array() {
		codeSnippet := make([]string, 0, 10)
		for _, v := range item.Get("text_matches").Array() {
			codeSnippet = append(codeSnippet, v.Get("fragment").String())
		}
		repoDesc := item.Get("repository.description").String()
		if repoDesc == "" {
			repoDesc = "No description, website, or topics provided."
		}
		repoDesc, _ = formatString(repoDesc, 1000)
		ch <- dlg.GithubResult{
			CodeUrl:     item.Get("html_url").String(),
			RepoUrl:     item.Get("repository.html_url").String(),
			RepoDesc:    repoDesc,
			RepoName:    item.Get("repository.full_name").String(),
			Sha:         item.Get("sha").String(),
			CodeSnippet: strings.Join(codeSnippet, `\n`),
		}
	}
}

func formatString(s string, maxLen int) (string, error) {
	// 检测是否为UTF-8编码
	if !utf8.ValidString(s) {
		// 尝试转换编码
		decoder := simplifiedchinese.GBK.NewDecoder()
		decoded, err := decoder.String(s)
		if err != nil {
			return "", fmt.Errorf("转换编码失败: %v", err)
		}
		s = decoded
	}
	if maxLen <= 0 {
		return "", nil
	}
	// 将字符串转为 rune 切片（按字符处理）
	runes := []rune(s)
	if len(runes) <= maxLen {
		return s, nil
	}
	// 截断并转换回字符串
	return string(runes[:maxLen]), nil
}

func getRetryRepoSearchResult(rsp *crawlerPb.GetResponse, ch chan<- dlg.GithubResult) {
	for _, item := range gjson.Get(crawlerPb.DecodeBy(rsp.Body), "items").Array() {
		uuidWithHyphen := uuid.New()
		uniqueId := fmt.Sprintf("%s", uuidWithHyphen)
		uniqueId = uniqueId[0:15] // 截取前15个字符
		repoDesc := item.Get("description").String()
		if repoDesc == "" {
			repoDesc = "No description, website, or topics provided."
		}
		repoDesc, _ = formatString(repoDesc, 1000)
		ch <- dlg.GithubResult{
			CodeUrl:     item.Get("html_url").String(), // 仓库接口无对应字段，写入 repoUrl
			RepoUrl:     item.Get("html_url").String(),
			RepoDesc:    repoDesc,
			RepoName:    item.Get("full_name").String(),
			Sha:         uniqueId + "-repo",
			CodeSnippet: "",
			//Sha:         "repo", // 将sha写入数据库判断是否实现repo功能
		}
	}
}

func (g *Github) SearchCode(ctx context.Context, keywords []string, response *pb.GitHubCodeResponse, maxTotal int) error {
	page := 0
	for len(response.Result) < maxTotal {
		page += 1
		if page > 50 {
			return nil
		}
		if token := g.getToken(); token == "" {
			log.WithContextWarnf(ctx, "[GitHub]: Search %v Error:%s", keywords, "Token Empty")
			continue
		} else {
			crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
				Url:    githubCodeSearchApi + "?" + fmt.Sprintf("q=%s&per_page=50&page=%d", convertKeywords(keywords), page),
				Method: crawlerPb.MethodCurlGet,
				Headers: map[string]string{
					"Authorization":        fmt.Sprintf("Bearer %s", token),
					"Accept":               "application/vnd.github+json",
					"X-GitHub-Api-Version": "2022-11-28",
				},
				Vpn:      1,
				WaitTime: 15,
			})
			if err != nil {
				log.WithContextWarnf(ctx, "[GitHub]: Search %v Error:%s", keywords, err.Error())
				break
			}
			g.updateToken(token)
			body := crawlerPb.DecodeBy(crawlerRes.Body)
			log.WithContextInfof(ctx, "[GitHub] Keyword:%v,Total:%d", keywords, gjson.Get(body, "total_count").Int())
			for _, item := range gjson.Get(body, "items").Array() {
				if len(response.Result) < maxTotal {
					response.Result = append(response.Result, &pb.GitHubCode{
						CodeUrl:  item.Get("html_url").String(),
						Sha:      item.Get("sha").String(),
						RepoUrl:  item.Get("repository.html_url").String(),
						RepoDesc: item.Get("repository.description").String(),
						RepoName: item.Get("repository.full_name").String(),
					})
				} else {
					continue
				}
			}
		}
	}
	return nil
}

const (
	tokenCacheExpired = 2 * time.Minute
	tokenLimit        = 10
	githubCachePrefix = "github:token:"
)

// 更新GitHub Token限制
func (g *Github) updateToken(token string) {
	key := githubCachePrefix + token

	ctx := context.Background()
	_, err := redisInit.GetInstance().Get(ctx, key).Int()
	if err == redis.Nil {
		redisInit.GetInstance().Set(ctx, key, 1, tokenCacheExpired)
	} else {
		redisInit.GetInstance().Incr(ctx, key) // 使用次数+1
	}
}

func (g *Github) setTokenLimited(token string) {
	key := githubCachePrefix + token
	err := redisInit.GetInstance().Set(context.Background(), key, tokenLimit+1, time.Minute).Err()
	if err != nil {
		log.Errorf("[Github] Set Github token key->%s rate limiting failed: %v\n", key, err)
	}
}

// 获取GitHub Token
func (g *Github) getToken() string {
	client := redisInit.GetInstance()
	for _, token := range g.Tokens {
		key := githubCachePrefix + token
		count, err := client.Get(context.Background(), key).Int()
		if count > tokenLimit {
			continue
		}
		if err != nil && err != redis.Nil {
			log.Errorf("[Github] Get github token key->%s counter failed: %v\n", key, err)
			continue
		}
		return token
	}
	return ""
}

// GithubSearchByTask 创建搜索任务
func GithubSearchByTask(_ context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.Info("[GitHub] Search keyword by task: %v", req.Keyword)
	var (
		isQuery               bool
		keywordHash, keywords = dlg.SortAndHash(req.Keyword)
		client                = dlg.NewTaskModel()
	)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}
	// step1 查询数据库判断是否属于历史查询
	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil { // step 1-1  如果关键词未被查询 创建新的任务行记录并初始化进度
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		join := dlg.KeywordJoin(keywords)
		var info = dlg.Task{Keyword: join, KeywordHash: keywordHash, Progress: 1, Status: dlg.StatusDoing}
		err = client.Create(&info)
		if err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else { // step 1-1 如果是强制更新，则更新原来的任务记录为初始状态，重新爬取数据
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt, 1) {
			errUpdate := client.UpdateAny(uint(item.Id), map[string]any{"status": dlg.StatusDoing, "progress": 1})
			if errUpdate != nil {
				return errUpdate
			}
			isQuery = true
		}
	}

	if isQuery {
		// step1-2 异步 获取GitHub查询数据 发起搜索任务 最大入库量为maxTotal的值
		go queryGitHubWithCode(context.Background(), uint(rsp.TaskId), 800, keywords)
	}

	return nil
}

// step2
func queryGitHubWithCode(ctx context.Context, taskId, maxTotal uint, keyword []string) {
	// step2-1 启动爬虫并获取数据
	err := _queryGitHubWithCode(ctx, taskId, maxTotal, keyword) //更新数据库
	if err != nil {
		log.WithContextWarnf(ctx, "[Github] task_id: %d queryGitHubWithCode failed: %v", taskId, err)
	}
	// step2-2 处理完毕后更新数据库状态为2 完成
	err = dlg.NewTaskModel().UpdateAny(taskId, map[string]any{"progress": 100, "status": dlg.StatusFinished})
	if err != nil {
		log.WithContextWarnf(ctx, "[Github] Github search on task_id: %d, %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[Github] task_id: %d, keyword: %v, task finished!", taskId, keyword)
	}
}

// step2-1 启动爬虫并获取数据 具体实现
func _queryGitHubWithCode(ctx context.Context, taskId, maxTotal uint, keyword []string) error {
	var (
		batch = 150
		ch    = make(chan dlg.GithubResult) // 构造无缓冲通道 用来将爬取结果从crawlerService发送到coreService
		list  = make([]*dlg.GithubResult, 0, batch)
		conn  = dlg.NewResultModel()
	)

	go func() { // step3 异步 请求具体逻辑
		err := GetGitHubClient().GetSearchCodeResult(ctx, keyword, int(maxTotal), ch) //获取github网站数据 并通过通道传输
		if err != nil {
			log.WithContextErrorf(ctx, "[Github] Search with code in task_id: %d failed: %v", taskId, err)
		}
	}()
	// 添加通道的数据到列表中
	for v := range ch {
		list = append(list, &dlg.GithubResult{
			TaskId:      taskId,
			RepoName:    v.RepoName,
			RepoUrl:     v.RepoUrl,
			RepoDesc:    v.RepoDesc,
			CodeUrl:     v.CodeUrl,
			CodeSnippet: v.CodeSnippet,
			ScreenShot:  v.ScreenShot,
			Sha:         v.Sha,
			Language:    getLanguageBySuffix(filepath.Ext(v.CodeUrl)),
		})
	}
	// 结果数据入库：dataleak_github_result
	upsert, err := conn.Upsert(list)
	if err != nil {
		return fmt.Errorf("search task_id: %d, upsert result failed: %w", taskId, err)
	}
	// 数据入库： dataleak_github_result_relation
	relationList, err := conn.RelationListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("get task_id: %d relation result failed: %w", taskId, err)
	}

	var insert = make([]*dlg.Relation, 0, len(upsert))
	for i := range upsert {
		exist := false
		for j := range relationList {
			if upsert[i].Id == relationList[j].ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insert = append(insert, &dlg.Relation{TaskId: uint64(taskId), ResultId: upsert[i].Id})
		}
	}

	return conn.RelationSave(insert)
}

// GithubTaskInfo 获取任务详情
func GithubTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := dlg.NewTaskModel().First(mysql.WithId(taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	// rsp.Keywords = dlg.KeywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress

	return nil
}

// GithubTaskResult 获取任务结果
func GithubTaskResult(taskId uint64) ([]*pb.GitHubCode, error) {
	l, err := dlg.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return nil, err
	}

	var result = make([]*pb.GitHubCode, 0, len(l))
	for i := range l {
		result = append(result, &pb.GitHubCode{
			RepoName:    l[i].RepoName,
			RepoUrl:     l[i].RepoUrl,
			RepoDesc:    l[i].RepoDesc,
			CodeUrl:     l[i].CodeUrl,
			Screenshot:  l[i].ScreenShot,
			Sha:         l[i].Sha,
			CodeSnippet: l[i].CodeSnippet,
			Language:    l[i].Language,
		})
	}
	return result, nil
}

func getLanguageBySuffix(suffix string) string {
	suffix = strings.ToLower(suffix)
	if v, ok := githubLanguageSuffix[suffix]; ok {
		return v
	}
	return "Other"
}

var githubLanguageSuffix = map[string]string{
	".c":          "C",
	".h":          "C",
	".cpp":        "C++",
	".c++":        "C++",
	".cp":         "C++",
	".cmake":      "CMake",
	".lisp":       "Lisp",
	".java":       "Java",
	".jsp":        "Java Server Pages",
	".php":        "PHP",
	".go":         "Go",
	".lua":        "Lua",
	".md":         "Markdown",
	".py":         "Python",
	".pyp":        "Python",
	".pyt":        "Python",
	".sh":         "Shell",
	".bash":       "Shell",
	".zsh":        "Shell",
	".ksh":        "Shell",
	".js":         "JavaScript",
	".ts":         "TypeScript",
	".tsx":        "TypeScript",
	".vue":        "Vue",
	".csv":        "CSV",
	".html":       "HTML",
	".txt":        "Text",
	".xml":        "XML",
	".css":        "CSS",
	".json":       "JSON",
	".yaml":       "YAML",
	".yml":        "YAML",
	".sql":        "SQL",
	".plsql":      "PLSQL",
	".dockerfile": "Dockerfile",
	".mk":         "Makefile",
	".mkfile":     "Makefile",
	".swift":      "Swift",
}

func convertKeywords(keywords []string) string {
	for i := range keywords {
		keywords[i] = url.QueryEscape(keywords[i])
	}
	return strings.Join(keywords, "+")
}
