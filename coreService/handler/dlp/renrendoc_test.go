package dlp

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRenrendocSearchWithPage(t *testing.T) {
	l, page, err := renrendocWithPage(context.Background(), "九江银行", 1)
	assert.Nil(t, err)
	fmt.Println("page:", page)
	fmt.Println("length", len(l))
	for i := range l {
		fmt.Println(l[i])
	}
}

func TestRenrendocSearch(t *testing.T) {
	l, err := GetRenrendocSearch(context.Background(), "银行", 30)
	assert.Nil(t, err)
	fmt.Println("length:", len(l))
}
