package dlp

import (
	"context"
	"fmt"
	"testing"

	"micro-service/initialize/redis"
	dlg "micro-service/middleware/mysql/dataleak_github"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"

	"github.com/joho/godotenv"
)

func TestGetSearchCodeResult(t *testing.T) {
	_ = godotenv.Load("../../../.env")

	cfg.InitLoadCfg()
	log.Init()

	redis.GetInstance(cfg.LoadRedis())
	ch := make(chan dlg.GithubResult)
	keywords := []string{"中国银行", "建设银行"}

	go GetGitHubClient().GetSearchCodeResult(context.Background(), keywords, 1, ch)
	for v := range ch {
		fmt.Println(v)
	}
}
