package dlp

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	wp56 "micro-service/middleware/mysql/dataleak_56wangpan"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/antchfx/htmlquery"
	"gorm.io/gorm"
)

func Search56pan(ctx context.Context, keywords []string, rsp *pb.NetDiskResponse, maxTotal int) error {
	var currentPage int
	for len(rsp.Result) < maxTotal {
		currentPage++
		if currentPage > 50 {
			return nil
		}
		get56panResponse(ctx, rsp, keywords, currentPage, maxTotal)
		time.Sleep(time.Second)
	}

	if len(rsp.Result) > maxTotal {
		rsp.Result = rsp.Result[:maxTotal]
	}
	return nil
}

const (
	host56wangpan = "www.56wangpan.net"
	url56wangpan  = "https://www.56wangpan.net"
)

func wangpan56ReqUrl(keywords []string, page int) string {
	reqUrl := fmt.Sprintf("%s/search/kw%s", url56wangpan, strings.Join(keywords, "+"))
	if page > 1 {
		reqUrl += "pg" + strconv.Itoa(page)
	}
	return reqUrl
}

func get56panResponse(ctx context.Context, response *pb.NetDiskResponse, keywords []string, page, maxTotal int) {
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    wangpan56ReqUrl(keywords, page),
		Method: crawlerPb.MethodCurlGet,
	}, utils.SetRpcTimeoutOpt(120))
	if err != nil {
		log.WithContextWarnf(ctx, "[56Pan]: Search %v Error:%s", keywords, err.Error())
		return
	}

	body := crawlerPb.DecodeBy(crawlerRes.Body)
	doc, err := htmlquery.Parse(strings.NewReader(body))
	if err != nil {
		log.WithContextWarnf(ctx, "[56Pan]: Search %v Error:%s", keywords, err.Error())
		return
	}
	list, err := htmlquery.QueryAll(doc, "//ul[@class=\"sellListContent\"]/li")
	if err != nil {
		log.WithContextWarnf(ctx, "[56Pan]: Search %v Error:%s", keywords, err.Error())
		return
	}

	var wg sync.WaitGroup
	for _, node := range list {
		if len(response.Result) == maxTotal {
			break
		}
		aNode, err := htmlquery.Query(node, "//a")
		if err != nil {
			continue
		}
		if aNode == nil {
			continue
		}

		redirectUrl := url56wangpan + "/redirect/file?id=" + get56panUrlId(htmlquery.SelectAttr(aNode, "href"))
		if strings.Contains(htmlquery.OutputHTML(node, true), "ifolder") { // 目录
			redirectUrl += "&t=dir"
		}
		netDisk := &pb.NetDisk{
			Url:      redirectUrl,
			FileName: strings.Trim(htmlquery.SelectAttr(aNode, "title"), " "),
		}
		wg.Add(1)
		go get56panBaiduUrl(ctx, response, &wg, netDisk, htmlquery.SelectAttr(aNode, "href"))
	}
	wg.Wait()
}

var compile56pan = regexp.MustCompile(`\d+`)

func get56panUrlId(url string) string {
	return compile56pan.FindString(url)
}

func get56panBaiduUrl(ctx context.Context, response *pb.NetDiskResponse, wg *sync.WaitGroup, disk *pb.NetDisk, p string) {
	defer wg.Done()
	// 获取百度地址
	referer, _ := url.JoinPath(url56wangpan, p)
	crawlerRes, getErr := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url: disk.Url,
		Headers: map[string]string{
			"Host":    host56wangpan,
			"Referer": referer,
		},
		Method: crawlerPb.MethodCurlGet,
	}, utils.SetRpcTimeoutOpt(300))
	if getErr != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("[56Pan]: Search %v Error:爬取网盘地址页面失败,err:%s", disk.Url, getErr.Error()))
		return
	}

	if crawlerRes.Code == 200 {
		doc, err := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
		if err != nil {
			return
		}
		urlNode := htmlquery.FindOne(doc, "//body/div/p[2]")
		if urlNode == nil {
			log.WithContextWarnf(ctx, "[56Pan]: Search %v Error:获取网盘地址失败,Body:%s", disk.Url, crawlerPb.DecodeBy(crawlerRes.Body))
			return
		}
		disk.Url = strings.Trim(htmlquery.InnerText(urlNode), " ")
		response.Result = append(response.Result, disk)
	} else {
		return
	}
}

func Wangpan56SearchByTask(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keywordHash, keywords := SortAndHash(req.Keyword)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	var (
		isQuery = false
		client  = wp56.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		var info = wp56.Task{Keyword: keywordJoin(keywords), KeywordHash: keywordHash, Progress: 1, Status: wp56.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt, 1) {
			errUpdate := client.UpdateAny(uint(item.Id), map[string]any{"status": wp56.StatusDoing, "progress": 1})
			if errUpdate != nil {
				return errUpdate
			}
			isQuery = true
		}
	}

	if isQuery {
		//	异步获取56网盘数据
		go _56wangpanSearchTaskSync(context.Background(), rsp.TaskId, req.Keyword)
	}

	return nil
}

func get56wangpanSearchResult(ctx context.Context, taskId uint64, keywords []string) error {
	var rsp = new(pb.NetDiskResponse)
	err := Search56pan(ctx, keywords, rsp, 200) // 最大200条
	if err != nil {
		return err
	}
	got := make([]*wp56.Pan56Result, 0, len(rsp.Result))
	for i := range rsp.Result {
		got = append(got, &wp56.Pan56Result{
			TaskId:     uint(taskId),
			FileName:   rsp.Result[i].FileName,
			FileUrl:    rsp.Result[i].Url,
			Screenshot: rsp.Result[i].Screenshot,
		})
	}

	client := wp56.NewResultModel()
	upsert, err := client.Upsert(got)
	if len(upsert) == 0 {
		return err
	}

	list, err := client.RelationListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("get task_id: %d relation result failed: %v", taskId, err)
	}

	var insert = make([]*wp56.Relation, 0, len(got))
	for _, v := range upsert {
		exist := false
		for _, x := range list {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insert = append(insert, &wp56.Relation{TaskId: taskId, ResultId: v.Id})
		}
	}

	err = client.RelationSave(insert)
	return err
}

func _56wangpanSearchTaskSync(ctx context.Context, taskId uint64, keywords []string) {
	err := get56wangpanSearchResult(ctx, taskId, keywords)
	if err != nil {
		log.WithContextError(ctx, "[56wangpan] task_id: %d, keywords: %v, 56wangpanSearchTaskSync failed: %v", keywords, err)
	}

	client := wp56.NewTaskModel()
	err = client.UpdateAny(uint(taskId), map[string]any{"status": wp56.StatusFinished, "progress": 100})
	if err != nil {
		log.WithContextErrorf(ctx, "[56wangpan] update task_id: %d progress to 100 failed: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[56wangpan] task_id: %d, keywords: %v, task finished!", taskId, keywords)
	}
}

func Wangpan56TaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := wp56.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

func Wangpan56TaskResult(taskId uint64, rsp *pb.Wangpan56ResultResponse) error {
	l, err := wp56.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return err
	}

	items := make([]*pb.Wangpan56ResultResponseItem, 0, len(l))
	for i := range l {
		items = append(items, &pb.Wangpan56ResultResponseItem{
			Id:         l[i].Id,
			FileName:   l[i].FileName,
			FileUrl:    l[i].FileUrl,
			Screenshot: l[i].Screenshot,
		})
	}

	rsp.Items = items
	return nil
}
