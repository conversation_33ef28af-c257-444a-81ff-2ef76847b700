package dlp

import (
	"context"
	"errors"
	"fmt"
	"github.com/chromedp/chromedp"
	"strings"
	"sync"
	"time"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	pss "micro-service/middleware/mysql/dataleak_pansoso"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/antchfx/htmlquery"
	"golang.org/x/net/html"
	"gorm.io/gorm"
)

type PanInfo struct {
	FileName   string
	BaiduUrl   string
	Screenshot string
}

const PansosoUrl = "https://www.pansoso.org"

func PansosoReqUrl(keywords []string, page int) string {
	keyword := strings.Join(keywords, " ")
	reqUrl := fmt.Sprintf("%s/zh/%s", PansosoUrl, keyword)
	if page > 1 {
		reqUrl += fmt.Sprintf("/pn%d.html", page)
	}
	return reqUrl
}

// SearchPansoso 同步模式获取结果
func SearchPansoso(ctx context.Context, keywords []string, rsp *pb.NetDiskResponse, maxTotal int) error {
	err := searchPansoso(ctx, keywords, rsp, maxTotal)
	if err != nil {
		return err
	}
	if len(rsp.Result) > maxTotal {
		rsp.Result = rsp.Result[:maxTotal]
	}
	return nil
}

func searchPansoso(ctx context.Context, keywords []string, response *pb.NetDiskResponse, maxTotal int) error {
	currentPage := 0
	for len(response.Result) < maxTotal {
		currentPage += 1
		if currentPage > 20 {
			break
		}
		urlCrawler := PansosoReqUrl(keywords, currentPage)
		if currentPage == 1 {
			urlCrawler = PansosoReqUrl(keywords, currentPage) + "/"
		}
		crawlerRes, err := crawlerPb.GetProtoClient().ChromeGet(ctx, &crawlerPb.ChromeGetRequest{
			Url: urlCrawler,
		}, utils.SetRpcTimeoutOpt(60), utils.SetRpcTimeoutOpt(60))
		if err != nil {
			log.WithContextWarnf(ctx, "[Pansoso] Search %v with crawler service failed: %s", keywords, err.Error())
			continue
		}
		body := crawlerPb.DecodeBy(crawlerRes.Body)
		if body == "" {
			return nil
		}
		doc, err := htmlquery.Parse(strings.NewReader(body))

		if err != nil {
			return err
		}

		list, err := htmlquery.QueryAll(doc, "//div[@id=\"content\"]/div[@class=\"pss\"]")
		if err != nil || len(list) == 0 {
			return err
		}

		wg := &sync.WaitGroup{}
		for _, node := range list {
			if len(response.Result) < maxTotal {
				wg.Add(1)
				go func(node *html.Node) {
					defer wg.Done()
					getPansosoInfo(ctx, response, node, maxTotal)
				}(node)
			}
		}
		wg.Wait()
	}

	return nil
}

func getPansosoInfo(ctx context.Context, response *pb.NetDiskResponse, node *html.Node, maxTotal int) {
	if len(response.Result) >= maxTotal {
		return
	}
	titleNode := htmlquery.FindOne(node, "//h2/a")
	if titleNode == nil {
		return
	}
	panInfo := &PanInfo{
		FileName: htmlquery.InnerText(titleNode),
	}
	// 处理href如果里面已经有访问地址，就不加原本的网站地址了
	urlNew := htmlquery.SelectAttr(titleNode, "href")
	if !strings.HasPrefix(urlNew, PansosoUrl) {
		urlNew = PansosoUrl + urlNew
	}
	joinUrl := urlNew
	jumpUrl, step := getPansosoBaiduUrl(ctx, joinUrl)
	if jumpUrl == "" {
		log.WithContextWarnf(ctx, "[Pansoso] Origin url: %s, FileName: %s, Step: %v, get jump url failed", joinUrl, panInfo.FileName, step)
		return
	}
	if strings.TrimRight(jumpUrl, `/`) == PansosoUrl {
		return
	}
	if len(response.Result) < maxTotal {
		response.Result = append(response.Result, &pb.NetDisk{
			OriginUrl:  joinUrl,
			Url:        jumpUrl,
			FileName:   panInfo.FileName,
			Screenshot: "",
		})
	}
}

// 获取Pansoso百度地址
func getPansosoBaiduUrl(ctx context.Context, infoUrl string) (string, int64) {
	infoPage, err := crawlerPb.GetProtoClient().ChromeGet(ctx, &crawlerPb.ChromeGetRequest{
		Url: infoUrl,
	}, utils.SetRpcTimeoutOpt(60), utils.SetRpcTimeoutOpt(60))
	if err != nil {
		log.WithContextWarnf(ctx, "[Pansoso] getPansosoUrl -> %s failed: %v", infoUrl, err)
		return "", 1
	}

	body := crawlerPb.DecodeBy(infoPage.Body)
	doc, err := htmlquery.Parse(strings.NewReader(body))
	if err != nil {
		log.WithContextWarnf(ctx, "[Pansoso] Jump url: %s, parse body failed: %v", infoUrl, err)
		return "", 2
	}

	urlNode := htmlquery.FindOne(doc, "//div[@class=\"down\"]/span[2]/a")
	if urlNode == nil {
		log.WithContextWarnf(ctx, "[Pansoso] getPansosoUrl -> Url:%s, not found jump url node, body: %s", infoUrl, body)
		return "", 3
	}
	if nodeUrl := htmlquery.SelectAttr(urlNode, "href"); nodeUrl == "" {
		log.WithContextWarnf(ctx, "[Pansoso]getPansosoUrl -> Url:%s,Error:%s", infoUrl, "PansosoUrl Empty")
		return "", 4
	} else {
		jumpUrl := PansosoUrl + nodeUrl
		if jumpUrl == PansosoUrl {
			return "", 5
		}
		ctx, cancel := chromedp.NewContext(context.Background())
		defer cancel()
		var result string
		err := chromedp.Run(ctx,
			chromedp.Navigate(jumpUrl),
			chromedp.Sleep(3*time.Second), // wait for 2 seconds
			chromedp.Evaluate(`window.location.href`, &result),
		)
		if err != nil {
			log.WithContextInfof(ctx, "[Pansoso] jump url: %s, parse jump request response body %s failed", jumpUrl, err.Error())
			return "", 7
		}
		return result, 8
	}
}

func PansosoSearchByTask(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keywordHash, keywords := SortAndHash(req.Keyword)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	client := pss.NewTaskModel()
	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	create := false
	isFetch := false
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		create = true
		isFetch = true
	case err != nil:
		return err
	}

	if create {
		info := pss.Task{Keyword: keywordJoin(keywords), KeywordHash: keywordHash, Progress: 1, Status: pss.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt, item.Progress) {
			errUpdate := client.UpdateAny(uint(item.Id), map[string]any{"status": pss.StatusDoing, "progress": 1})
			if errUpdate != nil {
				return errUpdate
			}
			isFetch = true
		}
	}

	if isFetch {
		// 异步获取Pansoso网盘数据
		go syncPansosoSearch(ctx, rsp.TaskId, req.Keyword)
	}

	return nil
}

func getPansosoSearchResult(ctx context.Context, taskId uint64, keywords []string) error {
	rsp := &pb.NetDiskResponse{}
	err := SearchPansoso(ctx, keywords, rsp, 200) // 爬取上限最大200条
	if err != nil {
		return err
	}

	got := make([]*pss.PansosoResult, 0, len(rsp.Result))
	for i := range rsp.Result {
		got = append(got, &pss.PansosoResult{
			TaskId:     uint(taskId),
			OriginUrl:  rsp.Result[i].OriginUrl,
			FileName:   rsp.Result[i].FileName,
			FileUrl:    rsp.Result[i].Url,
			Screenshot: rsp.Result[i].Screenshot,
		})
	}

	client := pss.NewResultModel()
	list, err := client.RelationListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("[Pansoso] Get task_id->%d result from MySQL failed: %v", taskId, err)
	}

	err = client.Upsert(got)
	if err != nil {
		return fmt.Errorf("pansoso result upsert failed: %v", err)
	}

	var insert = make([]*pss.Relation, 0, len(got))
	for _, v := range got {
		exist := false
		for _, x := range list {
			if v.Id == x.Id {
				exist = true
				break
			}
		}
		if !exist {
			insert = append(insert, &pss.Relation{TaskId: taskId, ResultId: v.Id})
		}
	}

	return client.RelationSave(insert)
}

func syncPansosoSearch(ctx context.Context, taskId uint64, keywords []string) {
	err := getPansosoSearchResult(ctx, taskId, keywords)
	if err != nil {
		log.WithContextError(ctx, err)
	}

	client := pss.NewTaskModel()
	err = client.UpdateAny(uint(taskId), map[string]any{"status": pss.StatusFinished, "progress": 100})
	if err != nil {
		log.Errorf("[Pansoso] Update task_id: %d progress to 100 failed: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[Pansoso] task_id: %d, keywords: %v, task finished!", taskId, keywords)
	}
}

// PansosoTaskInfo 任务详情
func PansosoTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := pss.NewTaskModel().First(mysql.WithId(taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

// PansosoTaskResult 任务结果
func PansosoTaskResult(taskId uint64, rsp *pb.Wangpan56ResultResponse) error {
	_, err := pss.NewTaskModel().First(mysql.WithId(taskId), mysql.WithSelect("id"))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	l, err := pss.NewResultModel().ListByTask(taskId)
	if err != nil {
		return err
	}

	for i := range l {
		rsp.Items = append(rsp.Items, &pb.Wangpan56ResultResponseItem{
			Id:         l[i].Id,
			OriginUrl:  l[i].OriginUrl,
			FileName:   l[i].FileName,
			FileUrl:    l[i].FileUrl,
			Screenshot: l[i].Screenshot,
		})
	}
	return nil
}
