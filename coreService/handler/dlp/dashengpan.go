package dlp

import (
	"context"
	"errors"
	"fmt"
	"github.com/tidwall/gjson"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"net/url"
	"strings"
	"time"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	dashengpan "micro-service/middleware/mysql/dataleak_dashengpan"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"gorm.io/gorm"
)

const (
	PlatformUnknown = iota
	PlatFormBaidu
	PlatFormAliyun
	PlatFormQuark
	PlatFormXunlei
)

func searchDashengpan(ctx context.Context, keywords []string, rsp *pb.NetDiskResponse, maxTotal int, resType string) {
	var currentPage int
	for len(rsp.Result) < maxTotal {
		currentPage++
		if currentPage > 6 {
			return
		}
		err := getDashengpanResponse(ctx, rsp, keywords, currentPage, resType)
		if err != nil {
			log.WithContextErrorf(ctx, "[dashengpan]: Search %v Error:%s", keywords, err.Error())
			break
		}
		time.Sleep(3 * time.Second)
	}
	//if len(rsp.Result) > maxTotal {
	//	rsp.Result = rsp.Result[:maxTotal]
	//}
	return
}

func dashengpanReqUrl(keywords []string, page int, resType string) string {
	reqUrl := fmt.Sprintf("https://fc-resource-node-api.krzb.net/api/v1/pan/search?version=v2&kw=%s&page=%d&line=0&site=dalipan&resType=%s", url.QueryEscape(keywords[0]), page, resType)
	return reqUrl
}

func dashengpanReqUrl2(id string) string {
	reqUrl := fmt.Sprintf("https://fc-resource-node-api.krzb.net/api/v1/pan/detail?version=v2&id=%s&size=15&parent=undefined", id)
	return reqUrl
}

func dashengpanReqUrl3(id string) string {
	reqUrl := fmt.Sprintf("https://fc-resource-node-api.krzb.net/api/v1/pan/url?version=v2&id=%s", id)
	return reqUrl
}

// 设置失效的大圣盘的token
func setNotToken(token string) error {
	cacheKey := "dahsengpan_not_token"
	var redisNotToken []string
	if redis.GetCache(cacheKey, &redisNotToken) {
		if !contains(redisNotToken, token) {
			redisNotToken = append(redisNotToken, token)
		}
	} else {
		redisNotToken = append(redisNotToken, token)
	}
	redis.SetCache(cacheKey, 1000000*time.Hour, redisNotToken)
	return nil
}

// 检查切片是否包含某个值
func contains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

// 设置大圣盘已经请求达到大圣盘当天次数上限的token,有效时间到当天的凌晨24点
func setLimitToken(token string) error {
	cacheKey := "dahsengpan_limit_token"
	var redisLimitToken []string
	if redis.GetCache(cacheKey, &redisLimitToken) {
		if !contains(redisLimitToken, token) {
			redisLimitToken = append(redisLimitToken, token)
		}
	} else {
		redisLimitToken = append(redisLimitToken, token)
	}
	now := time.Now()
	// 计算下一个凌晨的时间
	nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
	// 计算剩余的秒数
	secondsRemaining := int(nextMidnight.Sub(now).Seconds())
	duration := time.Duration(secondsRemaining) * time.Second
	redis.SetCache(cacheKey, duration, redisLimitToken)
	return nil
}

// 选择大圣盘可以用的token
func selectToken() string {
	tokenArr := cfg.LoadCommon().DaShengPanToken
	token := ""
	cacheKey := "dahsengpan_not_token"
	cacheLimitKey := "dahsengpan_limit_token"
	var redisNotToken []string
	var redisLimitToken []string
	redis.GetCache(cacheKey, &redisNotToken)
	redis.GetCache(cacheLimitKey, &redisLimitToken)
	if redisNotToken == nil {
		redisNotToken = []string{}
	}
	if redisLimitToken == nil {
		redisLimitToken = []string{}
	}
	combined := append(redisNotToken, redisLimitToken...)
	redisNotToken = combined
	if len(redisNotToken) > 0 {
		if tokenArr == nil {
			token = ""
		} else {
			// 选择token，如果有时效的token就从配置里面选出来的时候去掉
			for _, val := range tokenArr {
				if !contains(redisNotToken, val) {
					return val
				}
			}
		}
	} else {
		if len(tokenArr) > 0 {
			return tokenArr[0]
		} else {
			return token
		}
	}
	return token
}

func getDashengpanResponse(ctx context.Context, rsp *pb.NetDiskResponse, keywords []string, page int, resType string) error {
start:
	token := selectToken()
	if token == "" {
		return errors.New("大圣盘的token都失效了")
	}
	log.WithContextInfof(ctx, "[dashengpan]  keywords: %v,resType: %s,page: %d,token: %s", keywords, resType, page, token)
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    dashengpanReqUrl(keywords, page, resType),
		Method: crawlerPb.MethodCurlGet,
		Headers: map[string]string{
			"X-Authorization": token,
		},
	}, utils.SetRpcTimeoutOpt(120))
	if err != nil {
		log.WithContextWarnf(ctx, "[dashengpan]: Search %v Error:%s", keywords, err.Error())
		return err
	}
	body := crawlerPb.DecodeBy(crawlerRes.Body)
	if strings.Contains(body, "会员用户搜索频次") {
		// 将当前token设置为频率限制的token，有效期到当前24点失效
		_ = setLimitToken(token)
		log.Infof("[dashengpan]  token已失效: %s", token)
		goto start
	}

	result := gjson.Get(body, "resources")
	for _, everyData := range result.Array() {
		name := gjson.Get(everyData.String(), "res.filename").String()
		path := gjson.Get(everyData.String(), "highs.filelist\\.filename.0").String()
		// 如果path为空，取下标为1的数据。如果下标1为空，取下标2
		if path == "" {
			path = gjson.Get(everyData.String(), "highs.filelist\\.filename.1").String()
			if path == "" {
				path = gjson.Get(everyData.String(), "highs.filelist\\.filename.2").String()
				if path == "" {
					path = gjson.Get(everyData.String(), "highs.filelist\\.filename.3").String()
				}
			}
		}
		// 替换path的<mark>和</mark>
		if path != "" {
			path = strings.ReplaceAll(path, "<mark>", "")
			path = strings.ReplaceAll(path, "</mark>", "")
		}
		name = name + "-" + path
		// 去根据id获取url路径
		id := gjson.Get(everyData.String(), "res.id").String()
		if id == "" {
			fmt.Printf("[dashengpan]: Search %v id为空，跳过这个数据", keywords)
			continue
		}
		if id == "login_first" {
			_ = setNotToken(token)
			log.Infof("[dashengpan]  token已失效: %s", token)
			goto start
		}
		pwdRes, _ := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
			Url:    dashengpanReqUrl2(id),
			Method: crawlerPb.MethodCurlGet,
			Headers: map[string]string{
				"X-Authorization": token,
			},
		}, utils.SetRpcTimeoutOpt(120))

		bodyNewRes := crawlerPb.DecodeBy(pwdRes.Body)
		// 获取网盘密码
		pwd := gjson.Get(bodyNewRes, "pwd").String()
		// https://pan.baidu.com/share/init?surl=6nNhsegTV2YIlxyD67OsaQ&pwd=1234
		// 在发送请求去获取url地址
		detailRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
			Url:    dashengpanReqUrl3(id),
			Method: crawlerPb.MethodCurlGet,
			Headers: map[string]string{
				"X-Authorization": token,
			},
		}, utils.SetRpcTimeoutOpt(120))
		if err != nil {
			log.WithContextWarnf(ctx, "[dashengpan]: Search %v Error:%s", keywords, err.Error())
			return err
		}
		bodyRes := crawlerPb.DecodeBy(detailRes.Body)
		u := gjson.Get(bodyRes, "data").String()
		if u == "" {
			fmt.Printf("[dashengpan]: Search %v url为空，跳过这个数据", keywords)
			continue
		}
		if pwd != "" {
			u = u + "&pwd=" + pwd
		}
		rsp.Result = append(rsp.Result, &pb.NetDisk{
			FileName:   name,
			OriginUrl:  u,
			Url:        u,
			Screenshot: "",
			ResType:    resType,
		})
	}
	return nil
}

// WangpanDashengpanByTask 下发任务
func WangpanDashengpanByTask(_ context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keywordHash, keywords := SortAndHash(req.Keyword)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	var (
		isQuery = false
		client  = dashengpan.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		var info = dashengpan.Task{Keyword: keywordJoin(keywords), KeywordHash: keywordHash, Progress: 1, Status: dashengpan.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt, 1) {
			errUpdate := client.UpdateAny(uint(item.Id), map[string]any{"status": dashengpan.StatusDoing, "progress": 1})
			if errUpdate != nil {
				return errUpdate
			}
			isQuery = true
		}
	}

	if isQuery {
		//	异步获取大圣网盘数据
		go DashengpanTaskSync(context.Background(), rsp.TaskId, req.Keyword)
	}

	return nil
}

func getDashengpanResult(ctx context.Context, taskId uint64, keywords []string) error {
	var rsp = new(pb.NetDiskResponse)
	//先搜索百度网盘的
	searchDashengpan(ctx, keywords, rsp, 100, "baidu") // 最大100条
	// 阿里云盘
	time.Sleep(10 * time.Second)
	searchDashengpan(ctx, keywords, rsp, 150, "aliyundrive") // 最大50条
	// 夸克网盘
	time.Sleep(10 * time.Second)
	searchDashengpan(ctx, keywords, rsp, 230, "quark") // 最大80条
	// 迅雷网盘
	time.Sleep(10 * time.Second)
	searchDashengpan(ctx, keywords, rsp, 260, "xunleipan") // 最大30条

	got := make([]*dashengpan.PanDashengResult, 0, len(rsp.Result))
	for i := range rsp.Result {
		got = append(got, &dashengpan.PanDashengResult{
			TaskId:       uint(taskId),
			FileName:     rsp.Result[i].FileName,
			FileUrl:      rsp.Result[i].Url,
			Screenshot:   rsp.Result[i].Screenshot,
			PlatformType: WangpanGetPlatformType(rsp.Result[i].ResType),
		})
	}

	client := dashengpan.NewResultModel()
	upsert, err := client.Upsert(got)
	if len(upsert) == 0 {
		return err
	}

	list, err := client.RelationListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("get task_id: %d relation result failed: %v", taskId, err)
	}

	var insert = make([]*dashengpan.Relation, 0, len(got))
	for _, v := range upsert {
		exist := false
		for _, x := range list {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insert = append(insert, &dashengpan.Relation{TaskId: taskId, ResultId: v.Id})
		}
	}

	err = client.RelationSave(insert)
	return err
}

func DashengpanTaskSync(ctx context.Context, taskId uint64, keywords []string) {
	err := getDashengpanResult(ctx, taskId, keywords)
	if err != nil {
		log.WithContextError(ctx, "[dashengpan] task_id: %d, keywords: %v, dashengpanSearchTaskSync failed: %v", keywords, err)
	}

	client := dashengpan.NewTaskModel()
	err = client.UpdateAny(uint(taskId), map[string]any{"status": dashengpan.StatusFinished, "progress": 100})
	if err != nil {
		log.WithContextErrorf(ctx, "[dashengpan] update task_id: %d progress to 100 failed: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[dashengpan] task_id: %d, keywords: %v, task finished!", taskId, keywords)
	}
}

// WangpanDashengpanTaskInfo 获取任务详情
func WangpanDashengpanTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := dashengpan.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

// WangpanDashengpanTaskResult 获取任务结果
func WangpanDashengpanTaskResult(taskId uint64, rsp *pb.Wangpan56ResultResponse) error {
	l, err := dashengpan.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return err
	}

	items := make([]*pb.Wangpan56ResultResponseItem, 0, len(l))
	for i := range l {
		items = append(items, &pb.Wangpan56ResultResponseItem{
			Id:         l[i].Id,
			FileName:   l[i].FileName,
			FileUrl:    l[i].FileUrl,
			Screenshot: l[i].Screenshot,
		})
	}

	rsp.Items = items
	return nil
}

// WangpanGetPlatformType 获取平台类型ID
func WangpanGetPlatformType(resType string) int {
	switch resType {
	case "baidu":
		return PlatFormBaidu
	case "aliyundrive":
		return PlatFormAliyun
	case "quark":
		return PlatFormQuark
	case "xunleipan":
		return PlatFormXunlei
	default:
		return PlatformUnknown
	}
}
