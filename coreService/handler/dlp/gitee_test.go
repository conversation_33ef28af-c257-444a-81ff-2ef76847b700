package dlp

import (
	"context"
	"fmt"
	"testing"

	"github.com/joho/godotenv"

	"micro-service/initialize/redis"
	dlt "micro-service/middleware/mysql/dataleak_gitee"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

var (
	ch   = make(chan GiteeChanUnit)
	list = make([]*dlt.GiteeResult, 0, 300)
)

func TestGiteeSearchCodeResult(t *testing.T) {
	_ = godotenv.Load("../../../.env")

	cfg.InitLoadCfg()
	log.Init()
	ctx := context.Background()
	token := getGiteePersonalToken()
	maxTotal := 20
	client := redis.GetInstance(cfg.LoadRedis())
	keyword := []string{"test"}
	configValue := client.Get(context.Background(), "foradar_cache:GiteeSearchStrategy")
	fmt.Println(configValue.Val())
	if configValue.Val() != searchStrategy {
		fmt.Println(" no token 请求")
		go func() { // 非token方式获取 step 2-1 异步获取gitee数据
			err := GiteeRepoSearchNoToken(ctx, keyword, maxTotal, ch)
			if err != nil {
				fmt.Printf("[Gitee] search keyword: %v Which using NoToken Method  failed: %v\n", keyword, err)
			}
		}()
	} else {
		go func() { // token方式获取 step 2-1 异步获取gitee数据
			err := GiteeRepoSearch(ctx, keyword, token, maxTotal, ch)
			if err != nil {
				fmt.Printf("[Gitee] search keyword: %v  failed: %v\n", keyword, err)
			}
		}()
	}
}
