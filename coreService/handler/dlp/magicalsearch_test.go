package dlp

import (
	"context"
	"fmt"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"testing"
)

func initCfg2() {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

func TestAlinSesularch(t *testing.T) {
	initCfg2()
	var rsp = new(pb.NetDiskResponse)
	var keywords = []string{"一汽"}
	err := getMagicalsearchResponse_ali(context.TODO(), rsp, keywords, 2)
	if err != nil {
		fmt.Println(err)
	}
	for v := range rsp.Result {
		fmt.Println("vvvvvvvv")
		fmt.Println(rsp.Result[v].FileName)
		fmt.Println("vvvvvvvv")
	}
}

// 云盘搜索
func TestYunpanSesularch(t *testing.T) {
	initCfg()
	var rsp = new(pb.NetDiskResponse)
	var keywords = []string{"一汽"}
	err := getMagicalsearchResponse_yiyunsou(context.TODO(), rsp, keywords, 2)
	if err != nil {
		fmt.Println(err)
	}
	for v := range rsp.Result {
		fmt.Println("vvvvvvvv")
		fmt.Println(rsp.Result[v].FileName)
		fmt.Println("vvvvvvvv")
	}
}
