package dlp

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	renrendoc "micro-service/middleware/mysql/dataleak_renrendoc"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"gorm.io/gorm"
)

type RenrendocItem struct {
	Title      string `json:"OriginTitle"`
	Url        string `json:"HrefVal"`
	Screenshot string
}

func GetRenrendocSearch(ctx context.Context, keyword string, maxReqPage int) ([]RenrendocItem, error) {
	var result = make([]RenrendocItem, 0, 10*maxReqPage)
	for page := 1; page <= maxReqPage; page++ {
		l, _, err := renrendocWithPage(ctx, keyword, page)
		if err != nil {
			// 如果已经爬取一部分数据，跳出，然后把这些已经爬取到的数据入库
			if result != nil {
				break
			}
			return nil, err
		}
		result = append(result, l...)
		//一个关键词最多就爬取10页数据
		if page+1 > 10 {
			break
		}
	}

	return result, nil
}

const (
	RenrendocUrl = "https://www.renrendoc.com"
)

func RenrendocReqUrl(keyword string, page int) string {
	reqUrl := fmt.Sprintf("%s/renrendoc_v1/Search/searchV2?pagesize=10&_keywords=%s", RenrendocUrl, keyword)
	if page > 1 {
		reqUrl += fmt.Sprintf("&page=%d", page)
	}
	return reqUrl
}

func renrendocWithPage(ctx context.Context, keyword string, page int) ([]RenrendocItem, int, error) {
	param := &crawlerPb.GetRequest{
		Url:    RenrendocReqUrl(keyword, page),
		Method: crawlerPb.MethodCurlGet,
		Headers: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
		},
		WaitTime: 45,
	}
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, param)
	if err != nil {
		return nil, 0, err
	}
	if crawlerRes.Body == "" {
		return nil, 0, errors.New("body is empty")
	}

	var data map[string]interface{}
	err = json.Unmarshal([]byte(crawlerPb.DecodeBy(crawlerRes.Body)), &data)
	if err != nil {
		log.WithContextErrorf(ctx, "[Gitcode] Search %v json解码异常 Error:%s", keyword, err.Error())
		return nil, 0, err
	}

	// 获取 "data" 字段的内容
	dataField, ok := data["data"].(map[string]interface{})
	if !ok {
		return nil, 0, errors.New("unexpected data format")
	}

	// 获取 "list" 字段的内容并进行遍历
	listField, ok := dataField["list"].([]interface{})
	if !ok {
		return nil, 0, errors.New("unexpected data format")
	}

	items := make([]RenrendocItem, len(listField))
	for i, itemData := range listField {
		itemMap, ok := itemData.(map[string]interface{})
		if !ok {
			return nil, 0, errors.New("unexpected data format")
		}

		item := RenrendocItem{
			Title:      itemMap["OriginTitle"].(string),
			Url:        itemMap["HrefVal"].(string),
			Screenshot: "", // 这个字段在 JSON 中不存在，所以我们只能将它设置为一个空字符串或者其他默认值
		}
		items[i] = item
	}
	return items, 0, nil
}

// RenrendocSearchByTask 数据泄露-人人文档
func RenrendocSearchByTask(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keyword := firstKeyword(req.Keyword)
	if keyword == "" {
		return errors.New("关键字不可为空")
	}

	var (
		isQuery     = false
		keywordHash = utils.Md5sHash(keyword, false)
		client      = renrendoc.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		var info = renrendoc.Task{Keyword: keyword, KeywordHash: keywordHash, Progress: 1, Status: renrendoc.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt, item.Progress) {
			errUpdate := client.UpdateAny(uint(item.Id), map[string]any{"status": renrendoc.StatusDoing, "progress": 1})
			if errUpdate != nil {
				return errUpdate
			}
			isQuery = true
		}
	}

	if isQuery {
		// 异步获取人人文档
		go renrendocSearchTaskSync(ctx, rsp.TaskId, keyword)
	}

	return nil
}

func renrendocSearchTaskSync(ctx context.Context, taskId uint64, keyword string) {
	err := renrendocSearch(ctx, taskId, keyword)
	if err != nil {
		log.WithContextErrorf(ctx, "[Renrendoc] docSearch keyword: %s failed: %v", keyword, err)
	}

	err = renrendoc.NewTaskModel().UpdateAny(uint(taskId), map[string]any{"status": renrendoc.StatusFinished, "progress": 100})
	if err != nil {
		log.WithContextErrorf(ctx, "[Renrendoc] Update task_id: %d progress to 100 failed: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[Renrendoc] task_id: %d, keyword: %s, task finished!", taskId, keyword)
	}
}

func renrendocSearch(ctx context.Context, taskId uint64, keyword string) error {
	renrendocResult, err := GetRenrendocSearch(ctx, keyword, 10) // fetch 10 pages, max 100 items
	if err != nil {
		return fmt.Errorf("[Renrendoc]Get task_id->%d Renrendoc data failed in task mode, because of %v", taskId, err)
	}
	got := make([]*renrendoc.RenrendocResult, 0, len(renrendocResult))
	for i := range renrendocResult {
		got = append(got, &renrendoc.RenrendocResult{
			TaskId:     uint(taskId),
			DocTitle:   renrendocResult[i].Title,
			DocUrl:     renrendocResult[i].Url,
			Screenshot: renrendocResult[i].Screenshot,
		})
	}

	resultClient := renrendoc.NewResultModel()
	// 获取当前任务的关联结果
	relationList, err := resultClient.RelationListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return err
	}

	// 本次获取的数据upsert到总库
	upsertList, err := resultClient.Upsert(got)
	if err != nil {
		return err
	}

	var insertList = make([]*renrendoc.Relation, 0, len(got))
	for _, v := range upsertList {
		exist := false
		for _, x := range relationList {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insertList = append(insertList, &renrendoc.Relation{TaskId: taskId, ResultId: v.Id})
		}
	}

	// 更新关联结果
	err = resultClient.RelationSave(insertList)
	return err
}

func RenrendocTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := renrendoc.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress

	return nil
}

func RenrendocTaskResult(taskId uint64) ([]*pb.DocinResultResponseItem, error) {
	l, err := renrendoc.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return nil, err
	}

	var result = make([]*pb.DocinResultResponseItem, 0, len(l))
	for i := range l {
		result = append(result, &pb.DocinResultResponseItem{
			Id:         l[i].Id,
			Title:      l[i].DocTitle,
			Url:        l[i].DocUrl,
			Screenshot: l[i].Screenshot,
		})
	}
	return result, nil
}
