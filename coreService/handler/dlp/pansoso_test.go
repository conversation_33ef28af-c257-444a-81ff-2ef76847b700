package dlp

import (
	"context"
	"fmt"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

func initCfg() {
	cfg.InitLoadCfg()
	log.Init()
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

//func TestPansosoSea2rch(t *testing.T) {
//	initCfg()
//	var rsp = new(pb.BaiduLibraryResponse)
//	err := GetBaiduLibrary(context.TODO(), rsp, "fofa.info", 20)
//	if err != nil {
//		fmt.Println(err)
//	}
//	fmt.Println("++++++++++++++++")
//	fmt.Println(rsp)
//
//}

func TestPostmanSearch(t *testing.T) {
	initCfg()
	var rsp = new(pb.GitHubCodeResponse)
	err := postmanRepoSearch(context.TODO(), "fofa.info", 1, rsp)
	if err != nil {
		fmt.Println(err)
	}
	for v := range rsp.Result {
		fmt.Println("vvvvvvvv")
		fmt.Println(rsp.Result[v].RepoUrl)
		fmt.Println("vvvvvvvv")
	}
}

func TestMiaosouSearch(t *testing.T) {
	initCfg()
	var rsp = new(pb.NetDiskResponse)
	var keywords = []string{"歌曲"}
	err := searchMiaosou(context.TODO(), keywords, rsp, 10)
	if err != nil {
		fmt.Println(err)
	}
	for v := range rsp.Result {
		fmt.Println("vvvvvvvv")
		fmt.Println(rsp.Result[v].FileName)
		fmt.Println("vvvvvvvv")
	}
}

func TestDashengpanSearch(t *testing.T) {
	initCfg()

	//json := `{
	//    "highs": {
	//        "filelist.filename": [
	//            "“企业微生活”展示案例——企业微博优秀案例：中国<mark>光大</mark><mark>银行</mark>.pdf"
	//        ]
	//    }
	//}`
	//value := gjson.Get(json, "highs.filelist\\.filename.0").String()
	//fmt.Println(value)

	var rsp = new(pb.NetDiskResponse)
	var keywords = []string{"中海油"}
	//err := searchDashengpan(context.TODO(), keywords, rsp, 10, "baidu")
	//err := searchDashengpan(context.TODO(), keywords, rsp, 10, "aliyundrive")
	//err := searchDashengpan(context.TODO(), keywords, rsp, 10, "xunleipan")
	searchDashengpan(context.TODO(), keywords, rsp, 10, "baidu")
	for v := range rsp.Result {
		fmt.Println("vvvvvvvv")
		fmt.Println(rsp.Result[v].FileName)
		fmt.Println("vvvvvvvv")
	}
}
