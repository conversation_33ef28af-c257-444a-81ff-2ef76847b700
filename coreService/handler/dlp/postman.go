package dlp

import (
	"context"
	"errors"
	"fmt"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	crawlerPb "micro-service/crawlerService/proto"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	dlg "micro-service/middleware/mysql/dataleak_github"
	dlt "micro-service/middleware/mysql/dataleak_postman"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

type PostmanChanUnit struct {
	RepoUrl    string
	RepoDesc   string
	RepoName   string
	CodeUrl    string
	Screenshot string
	Sha        string
	Language   string
}

func postmanRepoSearch(ctx context.Context, keyword string, totalPage int, rsp *pb.GitHubCodeResponse) error {
	jsonParam := `{
    "service": "search",
    "method": "POST",
    "path": "/search-all",
    "body": {
			"queryIndices": [
				"collaboration.workspace",
				"runtime.collection",
				"runtime.request",
				"adp.api",
				"flow.flow",
				"apinetwork.team"
			],
			"queryText": "fofa.info",
			"size": 10,
			"from": 0,
			"clientTraceId": "e9e33627-028f-40ae-ae0b-b86aebf768df",
			"requestOrigin": "srp",
			"mergeEntities": true,
			"nonNestedRequests": true
    	}
	}`
	value, _ := sjson.Set(jsonParam, "body.queryText", keyword)
	for n := 1; n <= totalPage; n++ {
		valueNew2, _ := sjson.Set(value, "body.from", (n-1)*10)
		param := &crawlerPb.GetRequest{
			Method: crawlerPb.MethodCurlPost,
			Url:    "https://www.postman.com/_api/ws/proxy",
			Headers: map[string]string{
				"origin":       "https://www.postman.com",
				"Referer":      "https://www.postman.com",
				"content-type": "application/json;charset=UTF-8",
			},
			Options: map[string]string{"proxy": "disable"}, // postman的请求不能走快代理，用的代理ip服务商禁止访问国外的域名
			Body:    valueNew2,
		}
		crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, param, utils.SetRpcTimeoutOpt(60))
		if err != nil {
			return err
		}
		body := crawlerPb.DecodeBy(crawlerRes.Body)
		if body == "" {
			continue
		}
		result := gjson.Get(body, "data")
		for _, everyData := range result.Array() {
			entityType := gjson.Get(everyData.String(), "document.entityType").String()
			var url string
			var api_url string
			if entityType == "collection" {
				//url结构特殊，需要自己拼接
				publisherHandle := gjson.Get(everyData.String(), "document.publisherHandle").String()
				workSpace := gjson.Get(everyData.String(), "document.workspaces").Array()
				var workSpaceString string
				if len(workSpace) > 0 {
					firstRole := workSpace[0].Map()
					workSpaceString = firstRole["slug"].String()
				} else {
					continue
				}
				id := gjson.Get(everyData.String(), "document.id").String()
				if publisherHandle == "" || id == "" || workSpaceString == "" {
					continue
				}
				url = fmt.Sprintf("https://www.postman.com/%s/workspace/%s/collection/%s ", publisherHandle, workSpaceString, id)
				api_url = gjson.Get(everyData.String(), "document.name").String()
			} else if entityType == "request" {
				//url结构特殊，需要自己拼接
				publisherHandle := gjson.Get(everyData.String(), "document.publisherHandle").String()
				workSpace := gjson.Get(everyData.String(), "document.workspaces").Array()
				var workSpaceString string
				if len(workSpace) > 0 {
					firstRole := workSpace[0].Map()
					workSpaceString = firstRole["slug"].String()
				} else {
					continue
				}
				id := gjson.Get(everyData.String(), "document.id").String()
				if publisherHandle == "" || id == "" || workSpaceString == "" {
					continue
				}
				url = fmt.Sprintf("https://www.postman.com/%s/workspace/%s/request/%s ", publisherHandle, workSpaceString, id)
				api_url = gjson.Get(everyData.String(), "document.url").String()
			} else if entityType == "team" {
				continue
			} else {
				url = gjson.Get(everyData.String(), "document.url").String()
				api_url = url
			}
			if url == "" {
				continue
			}
			publishName := gjson.Get(everyData.String(), "document.publisherName").String()
			collEctionName := gjson.Get(everyData.String(), "document.collection.name").String()
			resultStr := fmt.Sprintf("Api文档的发布者：%s ，api的目录：%s ", publishName, collEctionName)
			name := gjson.Get(everyData.String(), "document.name").String()
			rsp.Result = append(rsp.Result, &pb.GitHubCode{
				RepoName: name,
				CodeUrl:  api_url,
				RepoUrl:  url,
				RepoDesc: resultStr,
			})
		}
	}
	return nil
}

type postman struct{}

func (postman) cacheKey(l []string, maxTotal int) string {
	key := "postman:" + utils.Md5sHash(strings.Join(l, "|+|"), false) + ":req_total_" + strconv.Itoa(maxTotal)
	return key
}

type postmanCache struct {
	List []*pb.GitHubCode `json:"list"`
}

func (postman) setCache(ctx context.Context, key string, list []*pb.GitHubCode) {
	if len(list) > 0 {
		gc := postmanCache{List: list}
		if b := redis.SetCache(key, 30*time.Minute, gc); !b {
			log.WithContextErrorf(ctx, "[Postman] cacheKey: %s, set cache failed", key)
		}
	}
}

func (postman) getCache(key string) []*pb.GitHubCode {
	var gc postmanCache
	redis.GetCache(key, &gc)
	return gc.List
}

func PostmanSearchByTask(_ context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.Info("[Postman]Search keyword by task: %v", req.Keyword)
	var (
		isQuery               bool
		keywordHash, keywords = dlg.SortAndHash(req.Keyword)
		client                = dlt.NewTaskModel()
	)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		join := dlg.KeywordJoin(keywords)
		var info = dlt.Task{Keyword: join, KeywordHash: keywordHash, Progress: 1, Status: dlg.StatusDoing}
		err = client.Create(&info)
		if err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if req.Force == 1 || cacheIsExpired(item.UpdatedAt) || item.Status == dlg.StatusInvalid {
			if err := client.UpdateAny(uint(item.Id), map[string]any{
				"status":   dlg.StatusDoing,
				"progress": 1,
			}); err != nil {
				return err
			}
			isQuery = true
		}
	}
	if isQuery {
		// 异步获取Postman查询数据-因为postman的数据只能每页10条，然后最多请求20页数据，也就200条，否则报错。如果不每页10条，返回的数据和postman页面数据不一致
		go queryPostmanWithCode(context.TODO(), uint(rsp.TaskId), 20, keywords)
	}
	return nil
}

func queryPostmanWithCode(ctx context.Context, taskId, maxTotal uint, keyword []string) {
	var (
		list       = make([]*dlt.PostmanResult, 0, 300)
		conn       = dlt.NewResultModel()
		taskClient = dlt.NewTaskModel()
		rsp        = new(pb.GitHubCodeResponse)
	)
	err := postmanRepoSearch(ctx, keyword[0], int(maxTotal), rsp)
	if err != nil {
		log.WithContextWarnf(ctx, "[Postman] search keyword: %v by task_id: %d failed: %v", keyword, taskId, err)
	}
	for v := range rsp.Result {
		list = append(list, &dlt.PostmanResult{
			TaskId:      taskId,
			RepoName:    rsp.Result[v].RepoName,
			RepoUrl:     rsp.Result[v].RepoUrl,
			RepoDesc:    rsp.Result[v].RepoDesc,
			CodeUrl:     rsp.Result[v].CodeUrl,
			ScreenShot:  "",
			Sha:         "",
			Language:    "other",
			CodeSnippet: "", // 目前获取不到代码片段
		})
	}
	upsert, err := conn.CompareAndUpdate(list)
	if err != nil {
		log.WithContextErrorf(ctx, "[Postman] Postman search with code in task_id: %d: %v", taskId, err)
	}

	insertRelation := make([]*dlt.Relation, 0)
	oldResult, _ := conn.RelationList(mysql.WithColumnValue("`task_id`", taskId))
	for _, v := range upsert {
		exist := false
		for _, x := range oldResult {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insertRelation = append(insertRelation, &dlt.Relation{TaskId: uint64(taskId), ResultId: v.Id})
		}
	}
	if errSave := conn.RelationSave(insertRelation); errSave != nil {
		log.WithContextErrorf(ctx, "[Postman] Save task_id: %d relation result failure: %v", taskId, errSave)
	}

	err = taskClient.UpdateAny(taskId, map[string]any{"progress": 100, "status": dlt.StatusFinished})
	if err != nil {
		log.WithContextErrorf(ctx, "[Postman] Update task_id: %d progress to 100 failure: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[Postman] Task_id: %v, keyword: %v, task finished!", taskId, keyword)
	}
}

// PostmanTaskInfo 总库 任务详情
func PostmanTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := dlt.NewTaskModel().First(mysql.WithId(taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = dlg.KeywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

// PostmanTaskResult 总库 任务结果
func PostmanTaskResult(taskId uint64, rsp *pb.GitHubCodeResultResponse) error {

	l, err := dlt.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return err
	}

	for i := range l {
		rsp.Items = append(rsp.Items, &pb.GitHubCode{
			RepoName:    l[i].RepoName,
			RepoUrl:     l[i].RepoUrl,
			RepoDesc:    l[i].RepoDesc,
			CodeUrl:     l[i].CodeUrl,
			Sha:         l[i].Sha,
			CodeSnippet: l[i].CodeSnippet,
			Language:    l[i].Language,
			Screenshot:  l[i].ScreenShot,
		})
	}
	return nil
}
