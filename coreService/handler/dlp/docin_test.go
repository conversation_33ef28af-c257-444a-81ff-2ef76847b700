package dlp

import (
	"context"
	"fmt"
	pb "micro-service/coreService/proto"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDocinSearchWithPage(t *testing.T) {
	l, page, err := docinWithPage(context.Background(), "银行", 21)
	assert.Nil(t, err)
	fmt.Println("page:", page)
	fmt.Println("length", len(l))
	for i := range l {
		fmt.Println(l[i])
	}
}

func TestDocinSearch(t *testing.T) {
	l, err := GetDocinSearch(context.Background(), "银行", 30)
	assert.Nil(t, err)
	fmt.Println("length:", len(l))
}

func TestGetDouin(t *testing.T) {
	rsp := new(pb.BaiduLibraryResponse)
	err := GetDouin(context.Background(), rsp, "银行")
	assert.Nil(t, err)
	fmt.Println("result:", len(rsp.Result))
}
