package dlp

import (
	"context"
	"errors"
	"fmt"
	"strings"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	baidu "micro-service/middleware/mysql/baidulibrary"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"gorm.io/gorm"
)

// GetBaiduLibrary 同步获取结果
func GetBaiduLibrary(ctx context.Context, rsp *pb.BaiduLibraryResponse, keyword string, totalPage int) error {
	jsonParam := `{"requests":[{"sceneID":"PCSearch","params":{"word":"广发银行","searchType":0,"lm":"0","od":"0","fr":"search","ie":"utf-8","pn":2}},{"sceneID":"PCSearchRec","params":{"word":"广发银行","searchType":0,"lm":"0","od":"0","fr":"search","ie":"utf-8","pn":2}}]}`
	value, _ := sjson.Set(jsonParam, "requests.0.params.word", keyword)
	valueNew, _ := sjson.Set(value, "requests.1.params.word", keyword)
	for n := 1; n <= totalPage; n++ {
		valueNew1, _ := sjson.Set(valueNew, "requests.0.params.pn", n)
		valueNew2, _ := sjson.Set(valueNew1, "requests.1.params.pn", n)
		crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
			Url:    "https://wenku.baidu.com/gsula/sula/syncmrecall",
			Method: crawlerPb.MethodCurlPost,
			Headers: map[string]string{
				"origin":       "https://wenku.baidu.com",
				"Referer":      "https://wenku.baidu.com",
				"content-type": "application/json;charset=UTF-8",
			},
			Body: valueNew2,
		}, utils.SetRpcTimeoutOpt(30))
		if err != nil {
			return err
		}
		log.WithContextInfof(ctx, "crawler baidu -> keyword:%s, status_code:%d, page:%d", keyword, crawlerRes.Code, n)
		body := crawlerPb.DecodeBy(crawlerRes.Body)
		if body == "" {
			continue
		}
		result := gjson.Get(body, "data.items.PCSearch.result.items.#.data")
		for _, name := range result.Array() {
			url := gjson.Get(name.String(), "url").String()
			screenshot := ""
			rsp.Result = append(rsp.Result, &pb.BaiduLibrary{
				Title:      gjson.Get(name.String(), "title").String(),
				Url:        url,
				Screenshot: screenshot,
			})
		}
	}
	return nil
}

// BaiduLibrarySearch 总库-百度文库 获取关键词任务
func BaiduLibrarySearch(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keyword := firstKeyword(req.Keyword)
	if keyword == "" {
		return errors.New("关键字不可为空")
	}

	var (
		isQuery     = false
		keywordHash = utils.Md5sHash(keyword, false)
		client      = baidu.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		var info = baidu.Task{Keyword: keyword, KeywordHash: keywordHash, Progress: 1, Status: baidu.StatusDoing}
		if errCreate := client.Create(&info); errCreate != nil {
			return errCreate
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt, 1) {
			errUpdate := client.UpdateAny(uint(item.Id), map[string]any{"status": baidu.StatusDoing, "progress": 1})
			if errUpdate != nil {
				return errUpdate
			}
			isQuery = true
		}
	}

	if isQuery {
		// 异步获取百度文库数据
		go asyncBaiduLibrarySearchTask(ctx, rsp.TaskId, keyword)
	}

	return nil
}

func asyncBaiduLibrarySearchTask(ctx context.Context, taskId uint64, keyword string) {
	err := baiduLibrarySearch(context.Background(), taskId, keyword)
	if err != nil {
		log.WithContextErrorf(ctx, "[BaiduLibrary] baiduLibrarySearch keyword: %s failed: %v", keyword, err)
	}

	err = baidu.NewTaskModel().UpdateAny(uint(taskId), map[string]any{"status": baidu.StatusFinished, "progress": 100})
	if err != nil {
		log.Errorf("[BaiduLibrary] Update task_id: %d progress to 100 failed: %v", taskId, err)
	} else {
		log.WithContextInfof(ctx, "[BaiduLibrary] task_id: %d, keyword: %s, task finished!")
	}
}

func baiduLibrarySearch(ctx context.Context, taskId uint64, keyword string) error {
	rsp := &pb.BaiduLibraryResponse{}
	err := GetBaiduLibrary(ctx, rsp, keyword, 20) // fetch 20 pages, 10 items/per-page
	if err != nil {
		return fmt.Errorf("GetBaiduLibrary result failed: %v", err)
	}

	got := make([]*baidu.BaiduLibraryResult, 0, len(rsp.Result))
	for i := range rsp.Result {
		got = append(got, &baidu.BaiduLibraryResult{
			TaskId:     uint(taskId),
			Title:      rsp.Result[i].Title,
			Url:        rsp.Result[i].Url,
			Address:    extractAddress(rsp.Result[i].Url),
			Screenshot: rsp.Result[i].Screenshot,
		})
	}

	resultClient := baidu.NewResultModel()
	// 获取任务的关联数据
	relationList, err := resultClient.RelationListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("get task_id: %d relation result failed: %v", taskId, err)
	}
	log.Infof("[BaiduLibrary-relationList] Update task_id: %d len-relationList: %d", taskId, len(relationList))
	// 获取本次的upsert数据
	got, err = resultClient.Upsert(got)
	if err != nil {
		return err
	}

	var insertList = make([]*baidu.Relation, 0, len(got))
	for _, v := range got {
		exist := false
		for _, x := range relationList {
			if v.Id == x.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insertList = append(insertList, &baidu.Relation{TaskId: taskId, ResultId: v.Id})
		}
	}
	log.Infof("[BaiduLibrary-insertList] Update task_id: %d len-relationList: %d", taskId, len(insertList))
	// 插入新的关联关系
	err = resultClient.RelationSave(insertList)
	return err
}

func extractAddress(s string) string {
	index := strings.Index(s, "?")
	if index > 0 {
		s = s[:index]
	}
	return s
}

// BaiduLibraryTaskInfo 总库-百度文库任务详情
func BaiduLibraryTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := baidu.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return fmt.Errorf("task_id: %d not found", taskId)
	case err != nil:
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress

	return nil
}

// BaiduLibraryTaskResult 总库-百度文库 任务结果
func BaiduLibraryTaskResult(taskId uint64, rsp *pb.BaiduLibraryResultResponse) error {
	l, err := baidu.NewResultModel().ListAllByTask(taskId)
	if err != nil {
		return err
	}

	for i := range l {
		rsp.Items = append(rsp.Items, &pb.BaiduLibraryResultResponseItem{
			Id:         l[i].Id,
			Address:    l[i].Address,
			Title:      l[i].Title,
			Url:        l[i].Url,
			Screenshot: l[i].Screenshot,
		})
	}
	return nil
}
