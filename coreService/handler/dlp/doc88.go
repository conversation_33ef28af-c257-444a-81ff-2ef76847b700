package dlp

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/antchfx/htmlquery"
	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	crawler "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	dlc "micro-service/middleware/mysql/dataleak_doc88"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

func Doc88TaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	hash, keywords := SortAndHash(req.Keyword)
	if len(keywords) == 0 {
		return errors.New("keyword is empty")
	}

	db := dlc.NewDoc88Fetcher()
	item, err := db.TaskFirst(mysql.WithColumnValue("`keyword_hash`", hash))
	isAdd := false
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		isAdd = true
	case err != nil:
		return err
	}

	if isAdd {
		item.Progress = 1
		item.KeywordHash = hash
		item.Keyword = keywordJoin(keywords)
		if e := db.TaskCreate(&item); e != nil {
			return e
		}
		rsp.TaskId = item.Id

		go asyncFetchUpdateDoc88Task(ctx, &item)
		return nil
	}

	isUpdate := req.Force == force || cacheIsExpired(item.UpdatedAt)
	rsp.TaskId = item.Id
	if !isUpdate {
		return nil
	}

	item.Progress = 1
	err = db.TaskUpdates(item)
	go asyncFetchUpdateDoc88Task(ctx, &item)

	return err
}

func Doc88TaskInfo(id uint64, rsp *pb.GitHubTaskInfoResponse) error {
	item, err := dlc.NewDoc88Fetcher().TaskFirst(mysql.WithId(id))
	if err != nil {
		return err
	}
	rsp.TaskId = int64(item.Id)
	rsp.Progress = item.Progress
	return nil
}

func Doc88TaskResult(id uint64, rsp *pb.Doc88ResultResponse) error {
	err := Doc88TaskInfo(id, &pb.GitHubTaskInfoResponse{})
	if err != nil {
		return err
	}

	l, err := dlc.NewDoc88Fetcher().ResultAllByTask(id)
	if err != nil {
		return err
	}

	for _, v := range l {
		rsp.Items = append(rsp.Items, &pb.Doc88ResultResponseItem{
			Id:         v.Id,
			Title:      v.Title,
			Url:        v.Url,
			UploadDate: v.UploadDate,
		})
	}
	return nil
}

const (
	doc88Url         = "https://www.doc88.com"
	doc88SearchParam = `/search/post.do?f=0&h=1&t=1651200664&pageRange=0&pageNum=0&format=&p=%d&q=%s&sDate=&eDate=&ct=0&_=%d`
)

func doc88ReqUrl(page int, keywords []string) string {
	search := url.QueryEscape(strings.Join(keywords, " "))
	reqParam := fmt.Sprintf(doc88SearchParam, page, search, time.Now().UnixMilli())
	reqUrl := fmt.Sprintf("%s%s", doc88Url, reqParam)
	return reqUrl
}

// 异步获取并更新指定任务的doc88数据
func asyncFetchUpdateDoc88Task(ctx context.Context, item *dlc.Task) {
	log.Infof("[doc88] run asyncFetchUpdateDoc88Task Starting, task_id: %d", item.Id)
	err := fetchUpdateDoc88Task(ctx, item)
	if err != nil {
		log.WithContextErrorf(ctx, "[doc88] run fetchUpdateDoc88Task task_id: %d failed: %v", item.Id, err)
	}

	item.Progress = 100
	err = dlc.NewDoc88Fetcher().TaskUpdates(*item)
	if err != nil {
		log.WithContextErrorf(ctx, "[doc88] update task_id: %d progress to 100 failed: %v", item.Id, err)
	} else {
		log.WithContextInfof(ctx, "[doc88] task_id: %d, keyword: %s, task finished!", item.Id, item.Keyword)
	}
}

// 获取并更新指定任务的doc88数据
func fetchUpdateDoc88Task(ctx context.Context, item *dlc.Task) error {
	keywords := keywordSplit(item.Keyword)
	fetchData, err := fetchDoc88WithPage(ctx, keywords, 20) // 获取数据
	log.WithContextInfof(ctx, "[doc88-doc88] runing fetchDoc88WithPage, keyword: %v, got fetchData length: %d", keywords, len(fetchData))
	if len(fetchData) == 0 {
		return err
	}

	db := dlc.NewDoc88Fetcher()
	upsert, err := db.ResultUpsert(fetchData)
	if len(upsert) == 0 {
		return err
	}

	// 获取指定任务的关联数据
	relation, err := db.RelationListByTask(item.Id)
	if err != nil {
		return err
	}

	var insertRelation = make([]dlc.Relation, 0, len(fetchData))
	for _, v := range upsert {
		exist := false
		for _, vv := range relation {
			if v.Id == vv.ResultId {
				exist = true
				break
			}
		}
		if !exist {
			insertRelation = append(insertRelation, dlc.Relation{TaskId: item.Id, ResultId: v.Id})
		}
	}
	log.WithContextInfof(ctx, "[doc88] insert relation result, taskId->%d, result length: %d", item.Id, len(insertRelation))
	err = db.RelationCreate(insertRelation)
	return err
}

func fetchDoc88WithPage(ctx context.Context, keywords []string, page int) ([]*dlc.Result, error) {
	var err error
	var l []*dlc.Result
	items := make([]*dlc.Result, 0, 10*page)
	for i := 1; i <= page; i++ {
		reqUrl := doc88ReqUrl(i, keywords)
		l, err = fetchDoc88(ctx, reqUrl)
		log.Infof("[Core] dlp: Doc88 search keyword: %v, page: %d, result length: %d", keywords, i, len(l))
		if len(l) == 0 {
			break
		}

		time.Sleep(500 * time.Millisecond)
		items = append(items, l...)
	}

	if err != nil {
		log.Errorf("[Core] dlp: Doc88-error fetchDoc88WithPage: %v", err)
	}
	return items, nil
}

func fetchDoc88(_ context.Context, reqUrl string) ([]*dlc.Result, error) {
	rsp, err := crawler.GetProtoClient().Get(context.TODO(), &crawler.GetRequest{
		Url:     reqUrl,
		Timeout: 30,
		Method:  crawler.MethodChromeGet,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return nil, err
	}

	body := crawler.DecodeBy(rsp.Body)
	top, err := htmlquery.Parse(strings.NewReader(body))
	if err != nil {
		return nil, err
	}

	nodes, err := htmlquery.QueryAll(top, `//div[@class="sd-list-con"]`)
	if len(nodes) == 0 {
		log.Warnf("[Core] dlp: Doc88 request url: %s, parse html elements got nodes=0, the resp body: %s", reqUrl, body)
		return nil, err
	}

	var items = make([]*dlc.Result, 0, len(nodes))
	for _, node := range nodes {
		var item dlc.Result
		titleNode := htmlquery.FindOne(node, `//h3/a`)
		detailNode := htmlquery.FindOne(node, `//div/div[@class='sd-intro']`)
		if titleNode != nil {
			item.Url = htmlquery.SelectAttr(titleNode, "href")
			item.Title = htmlquery.SelectAttr(titleNode, "title")
		}
		if detailNode != nil {
			item.UploadDate = abstractDate(htmlquery.InnerText(detailNode))
		}
		items = append(items, &item)
	}

	return items, nil
}

var doc88DateReg = regexp.MustCompile(`\d{4}-\d{2}-\d{2}`)

func abstractDate(s string) string {
	return doc88DateReg.FindString(s)
}
