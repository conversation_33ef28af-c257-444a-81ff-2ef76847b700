package handler

import (
	"context"
	"micro-service/coreService/handler/recommend"
	pb "micro-service/coreService/proto"
)

// AddRecommendTask 添加推荐任务
func (e *Core) AddRecommendTask(ctx context.Context, req *pb.RecommendTaskRequest, rsp *pb.RecommendTaskResponse) error {
	return recommend.AddRecommendTask(ctx, req, rsp)
}

// GetRecommendProcess 获取推荐进度
func (e *Core) GetRecommendProcess(ctx context.Context, req *pb.RecommendProcessRequest, rsp *pb.RecommendProcessResponse) error {
	return recommend.GetRecommendProcess(ctx, req, rsp)
}

// GetRecommendResult 获取推荐结果
func (e *Core) GetRecommendResult(ctx context.Context, req *pb.RecommendResultRequest, rsp *pb.RecommendResultResponse) error {
	return recommend.GetRecommendResult(ctx, req, rsp)
}
