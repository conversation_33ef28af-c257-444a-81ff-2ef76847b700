package ip_domain_history

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	pb "micro-service/coreService/proto"
)

func Test_IpDomainHistory(t *testing.T) {
	var rsp = new(pb.IpDomainHistoryResponse)
	err := IpDomainHistory(context.TODO(), &pb.IpDomainHistoryRequest{
		Ip: []string{"************"},
	}, rsp)
	assert.Nil(t, err)
	fmt.Println(rsp)
}

func Test_ipDomainHistoryByFOFA(t *testing.T) {
	list, err := ipDomainHistoryByFOFA(context.TODO(), "*******", false)
	assert.Nil(t, err)
	fmt.Println(list)
}

func Test_ipDomainHistoryByIp138(t *testing.T) {
	ip := "*************"
	t.Run(ip, func(t *testing.T) {
		list, err := ipDomainHistoryByIp138(context.TODO(), ip)
		assert.Nil(t, err)
		fmt.Println(list)
	})
}
