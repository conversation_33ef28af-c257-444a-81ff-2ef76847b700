package ip_domain_history

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"

	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = redis.GetInstance(cfg.LoadRedis())
}

func Test_setCache(t *testing.T) {
	initCfg()

	ip := "127.0.0.1"
	data := []ipDomainHistory{
		{Domain: "baidu.com", Source: sourceFOFA, FoundTime: time.Now()},
		{Domain: "google.com", Source: sourceIp138, FoundTime: time.Now()},
	}

	setCache(context.TODO(), ip, data, true)
}

func Test_getCache(t *testing.T) {
	initCfg()

	ip := "127.0.0.1"
	data, err := getCache(context.TODO(), ip, true)
	assert.Nil(t, err)
	fmt.Println(data)
}
