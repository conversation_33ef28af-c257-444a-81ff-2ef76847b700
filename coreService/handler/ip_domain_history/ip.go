package ip_domain_history

import (
	"context"
	"fmt"
	"net"
	"sort"
	"time"

	"micro-service/coreService/handler/fofa"
	"micro-service/coreService/handler/ip138"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/utils"
)

type ipDomainHistory struct {
	Domain     string    `json:"domain"`
	IcpCompany string    `json:"icp_company"`
	Source     int       `json:"source"`     // 1-fofa 2-ip138
	FoundTime  time.Time `json:"found_time"` // order by column DESC
}

const (
	sourceFOFA = iota + 1
	sourceIp138
)

func IpDomainHistory(ctx context.Context, req *pb.IpDomainHistoryRequest, rsp *pb.IpDomainHistoryResponse) error {
	ip := utils.ListFirstEle(req.Ip)
	if x := net.ParseIP(ip); x == nil {
		return fmt.Errorf("%s not a valid ip", ip)
	}

	var err error
	var data []ipDomainHistory
	data, err = getCache(ctx, ip, req.IsAll)
	if err != nil {
		data, err = ipDomainHistoryByQuery(ctx, ip, req.IsAll)
		if err != nil {
			return err
		}
		// set cache
		go setCache(ctx, ip, data, req.IsAll)
	}

	if len(data) == 0 {
		return nil
	}

	// sort by field FoundTime desc
	sort.Slice(data, func(i, j int) bool { return data[i].FoundTime.After(data[j].FoundTime) })

	var item = &pb.IpDomainHistoryResponse_Item{Ip: ip}
	for i := range data {
		item.Items = append(item.Items, &pb.IpDomainHistoryResponse_DomainItem{
			Domain:     data[i].Domain,
			IcpCompany: data[i].IcpCompany,
			Source:     int64(data[i].Source),
			FoundTime:  data[i].FoundTime.Format(utils.DateTimeLayout),
		})
	}
	rsp.Items = append(rsp.Items, item)

	return nil
}

func ipDomainHistoryByQuery(ctx context.Context, ip string, isAll bool) ([]ipDomainHistory, error) {
	fofaDomains, err := ipDomainHistoryByFOFA(ctx, ip, isAll)
	if err != nil {
		return nil, err
	}

	if len(fofaDomains) == 0 {
		return nil, nil
	}

	// var ip138Domains []ipDomainHistory
	// if isAll {
	// 	ip138Domains, err = ipDomainHistoryByIp138(ctx, ip)
	// }
	// if err != nil {
	// 	return nil, err
	// }

	// if len(fofaDomains) == 0 && len(ip138Domains) == 0 {
	// 	return nil, nil
	// }

	// data := merge(fofaDomains, ip138Domains)
	data := fofaDomains
	var topDomains = make([]string, 0, len(data))
	for i := range data {
		if topDomain, _ := utils.FindRootDomain(data[i].Domain); topDomain != "" {
			topDomains = append(topDomains, topDomain)
		}
	}
	icpCompanyMap, _ := GetIcpCompany(ctx, topDomains)

	for i := range data {
		topDomain, _ := utils.FindRootDomain(data[i].Domain)
		companyName, _ := icpCompanyMap.Get(topDomain)
		data[i].IcpCompany = companyName
	}
	return data, nil
}

func ipDomainHistoryByFOFA(ctx context.Context, ip string, isAll bool) ([]ipDomainHistory, error) {
	var rsp = new(pb.FofaQueryResponse)
	err := fofa.FofaQuery(ctx, rsp, &pb.FofaQueryRequest{
		Qbase64: fmt.Sprintf(`ip=%q && domain != "" && host != "" && is_domain=true`, ip),
		Full:    false,
		Field:   []string{"host", "lastupdatetime"},
		Page:    1,
		Size:    uint32(utils.If(isAll, 500, 3)),
	})
	if err != nil {
		return nil, err
	}

	var result = make([]ipDomainHistory, 0, len(rsp.Sdata))
	domainsMap := make(map[string]struct{}, len(rsp.Sdata)/2)
	for _, v := range rsp.Sdata {
		host := utils.DomainFromUrl(v.GetHost())
		if _, ok := domainsMap[host]; ok {
			continue
		}
		domainsMap[host] = struct{}{}
		x, _ := time.Parse(utils.DateTimeLayout, v.GetLastupdatetime())
		result = append(result, ipDomainHistory{
			Domain:    host,
			FoundTime: x,
			Source:    sourceFOFA,
		})
	}
	return result, nil
}

//nolint:unused,gocritic
func ipDomainHistoryByIp138(ctx context.Context, ip string) ([]ipDomainHistory, error) {
	var rsp = new(pb.Ip138IpResponse)
	err := ip138.QueryIp(ctx, &pb.Ip138IpRequest{Ip: []string{ip}}, rsp)
	if err != nil {
		return nil, err
	}

	var result = make([]ipDomainHistory, 0, len(rsp.List))
	for _, v := range rsp.List {
		if v.Ip != ip {
			continue
		}
		for _, x := range v.List {
			result = append(result, ipDomainHistory{
				Domain:    x.Domain,
				Source:    sourceIp138,
				FoundTime: parseIp138FoundTime(x.Time),
			})
		}
	}
	return result, nil
}
