package ip_domain_history

import (
	"context"
	"strings"
	"time"

	core "micro-service/coreService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/sourcegraph/conc/pool"
)

//nolint:unused,gocritic
func parseIp138FoundTime(s string) time.Time {
	split := strings.Split(s, ",")
	if len(split) == 0 {
		return time.Time{}
	}
	x := split[len(split)-1]
	t, _ := time.Parse(utils.DateLayout, x)
	return t
}

func GetIcpCompany(ctx context.Context, domains []string) (cmap.ConcurrentMap[string, string], error) {
	if len(domains) == 0 {
		return cmap.ConcurrentMap[string, string]{}, nil
	}
	domains = utils.ListDistinctNonZero(domains)

	n := utils.If(len(domains)/5 <= 1, 1, len(domains)/5)
	p := pool.New().WithMaxGoroutines(n)
	m := cmap.New[string]()
	for _, v := range domains {
		domain := v
		p.Go(func() {
			rsp, err := core.GetProtoCoreClient().Domain(ctx, &core.IcpDomainRequest{Domain: domain})
			if err != nil {
				log.WithContextErrorf(ctx, "[Core] Get icp company by domain: %s failed: %v", domain, err)
				return
			}
			if rsp != nil {
				m.Set(domain, rsp.Info.GetCompanyName())
			}
		})
	}
	p.Wait()

	return m, nil
}

//nolint:unused,gocritic
func merge(fofa, ip138 []ipDomainHistory) []ipDomainHistory {
	var fofaDomainsMap = make(map[string]struct{}, len(fofa))
	for i := range fofa {
		fofaDomainsMap[fofa[i].Domain] = struct{}{}
	}

	var l = make([]ipDomainHistory, 0, len(ip138))
	for i := range ip138 {
		if _, ok := fofaDomainsMap[ip138[i].Domain]; !ok {
			l = append(l, ip138[i])
		}
	}

	ipDomains := make([]ipDomainHistory, len(fofa)+len(l))
	copy(ipDomains, fofa)
	copy(ipDomains[len(fofa):], l)
	return ipDomains
}
