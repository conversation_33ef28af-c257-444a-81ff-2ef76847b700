package ip_domain_history

import (
	"context"
	"time"

	"github.com/bytedance/sonic"

	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

var cacheExpired = 4 * time.Hour

func cacheKey(ip string, isAll bool) string {
	getAll := utils.If(isAll, ":all", "")
	return "foradar_cache:ip_domain_icp_history:" + ip + getAll
}

func getCache(ctx context.Context, ip string, isAll bool) ([]ipDomainHistory, error) {
	key := cacheKey(ip, isAll)
	var data = make([]ipDomainHistory, 0)
	err := redis.Get(ctx, key, &data)
	return data, err
}

func setCache(ctx context.Context, ip string, list []ipDomainHistory, isAll bool) {
	key := cacheKey(ip, isAll)

	bs, _ := sonic.Marshal(list)
	err := redis.Set(ctx, key, string(bs), cacheExpired)
	if err != nil {
		log.WithContextErrorf(ctx, "[Core] Set ip: %s domain_icp_history cache failed: %v", ip, err)
	}
}
