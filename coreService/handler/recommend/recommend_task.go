package recommend

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"golang.org/x/net/idna"
	"micro-service/coreService/handler/fofa"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/elastic/general_assets"
	"micro-service/middleware/mysql/cloud_assets"
	"micro-service/middleware/mysql/company_icp"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/process"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"net"
	"strings"
	"sync"
	"time"
)

const TrueStr = "true"

var recommendTaskOnce sync.Once

var recommendTask *Task

type Task struct {
	taskRunning sync.Map
	clueRunning sync.Map
	taskCount   sync.Map
}

func GetTaskInstance() *Task {
	if recommendTask == nil {
		recommendTaskOnce.Do(func() { recommendTask = &Task{} })
	}
	return recommendTask
}

// CheckTaskRunning 检查任务是否在执行中
func (t *Task) CheckTaskRunning(hash string) bool {
	if _, ok := t.taskRunning.Load(hash); ok {
		return true
	}
	return false
}

// CheckClueRunning 检查线索是否在执行中
func (t *Task) CheckClueRunning(hash string) bool {
	// 检查任务是否重复执行
	if _, ok := t.clueRunning.Load(hash); ok {
		return true
	}
	return false
}

// addCount 添加每个类型资产总数
func (t *Task) addClueCount(task *cloud_assets.Task, clueType int) {
	if val, ok := t.taskCount.Load(task.Hash); ok {
		counts := val.(map[int]int)
		counts[clueType] += 1
		log.WithContextInfof(context.TODO(), fmt.Sprintf("推荐资产,任务:%d,线索类型计数,类型:%d, 数量:%d", task.Id, clueType, counts[clueType]))
		t.taskCount.Store(task.Hash, counts)
	} else {
		counts := make(map[int]int, 0)
		counts[clueType] = 1
		t.taskCount.Store(task.Hash, counts)
	}
}

// checkClueLimit 检查线索数量限制
func (t *Task) checkClueCountLimit(taskHash string, clueType int) bool {
	if val, ok := t.taskCount.Load(taskHash); ok {
		if val == nil {
			return false
		}
		counts := val.(map[int]int)
		if count, countOk := counts[clueType]; countOk {
			return count > 20000
		} else {
			return false
		}
	} else {
		return false
	}
}

// GetFofaCount 获取FOFA拉取数据总数
func (t *Task) getFofaCount(ctx context.Context, task *cloud_assets.Task, clue *cloud_assets.Clue) (uint32, func(*process.Bar, []string)) {
	isWildDomain := false
	rsp := pb.FofaQueryCountResponse{}
	var err error
	err = fofa.FofaQueryCount(context.TODO(), &rsp, &pb.FofaQueryCountRequest{
		Qbase64:          t.GetFofaQuery(clue),
		Full:             true,
		CanGetLimitCount: true,
	})
	if err != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("推荐资产,任务:%d,GetFofaCount,Query:%s,Error:%s", task.Id, t.GetFofaQuery(clue), err.Error()))
		// 重试一次,
		err = fofa.FofaQueryCount(context.TODO(), &rsp, &pb.FofaQueryCountRequest{
			Qbase64: t.GetFofaQuery(clue), Full: true, CanGetLimitCount: true,
		})
	}
	if err != nil {
		rsp = pb.FofaQueryCountResponse{Count: 1000}
		log.WithContextWarnf(ctx, fmt.Sprintf("推荐资产,任务:%d,GetFofaCount,重试失败:按默认1000条数据处理,Query:%s,Error:%s", task.Id, t.GetFofaQuery(clue), err.Error()))
	}
	// 如果是域名线索,判断是否是泛解析域名
	if clue.Type == general_clues.ClueTypeDomain || clue.Type == general_clues.ClueTypeSubdomain {
		isWildDomain = utils.IsWildDomain(clue.Content)
	}
	f := func(bar *process.Bar, icpDomains []string) {
		t.fofaClueHandler(ctx, bar, task, clue, isWildDomain, icpDomains)
	}
	log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,GetFofaCount,Query:%s,OK:%d", task.Id, t.GetFofaQuery(clue), rsp.Count))
	return rsp.Count, f
}

// fofaClueHandler 获取FOFA资产,并入库
func (t *Task) fofaClueHandler(ctx context.Context, bar *process.Bar, task *cloud_assets.Task, clue *cloud_assets.Clue, isWildDomain bool, icpDomains []string) {
	defer func(clue *cloud_assets.Clue, task *cloud_assets.Task) {
		// 更新线索推荐进度
		clue.Process = 100
		_ = cloud_assets.NewClueModel().UpdateProcess(clue.Id, 100)
		// 更新任务进度
		_ = cloud_assets.NewTaskModel().UpdateProcess(task.Id)
		// 线索推荐完成
		log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler 线索:%+v,defer资产推荐完成", task.Id, clue))
	}(clue, task)
	log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler 线索:%+v,开始处理FOFA资产", task.Id, clue))
	retry, retryTotal, page, limit := 0, 3, 1, false
	// wildDomainLimits := make(map[string]int)
	// 限制单个线索拉取时间,限制1个小时
	timeLimit := false
	time.AfterFunc(time.Hour*1, func() {
		timeLimit = true
	})
	for !timeLimit || !limit {
		log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler FOR处理资产,线索:%+v,", task.Id, clue))
		rsp := pb.FofaQueryResponse{}
		if err := fofa.FofaQuery(context.TODO(), &rsp, &pb.FofaQueryRequest{
			Qbase64: t.GetFofaQuery(clue),
			Full:    true,
			Field:   []string{"ip", "port", "protocol", "base_protocol", "host", "link", "domain", "title", "icp", "cert", "isp", "fid", "lastupdatetime", "icon", "icon_hash", "certs_valid", "cloud_name", "cname", "server", "version"},
			Page:    cast.ToUint32(page),
			Size:    100,
		}); err != nil {
			// 重试三次
			if retry < retryTotal {
				retry += 1
				log.WithContextWarnf(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler 线索:%+v,获取数据失败:%s, 重试:%d", task.Id, clue, err.Error(), retry))
				time.Sleep(3 * time.Second)
				continue
			} else {
				log.WithContextWarnf(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler 线索:%+v,获取数据失败:%s, 重试结束", task.Id, clue, err.Error()))
				break
			}
		}
		page++ // 翻页
		if len(rsp.Sdata) == 0 {
			log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler 线索:%+v,Sdata为空跳出循环", task.Id, clue))
			break
		}
		// 计算线索资产是否达到限制
		if t.checkClueCountLimit(task.Hash, cast.ToInt(clue.Type)) {
			log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler 线索:%+v,达到限制数量,跳出资产拉取", task.Id, clue))
			break
		}
		for _, asset := range rsp.Sdata {
			log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler 线索:%+v,当前页:%d,资产总数:%d", task.Id, clue, *rsp.Page, *rsp.Size))
			// 添加线索资产计数
			t.addClueCount(task, cast.ToInt(clue.Type))
			// 计算线索资产是否达到限制
			if t.checkClueCountLimit(task.Hash, cast.ToInt(clue.Type)) {
				limit = true
				log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler 线索:%+v,达到限制数量,跳出资产拉取", task.Id, clue))
				break
			}
			t.formatInsertOrUpdateAssets(ctx, task, clue, asset, "FOFA", icpDomains)
			// 设置进度
			bar.Add(1, func() {
				// 更新线索推荐进度
				_ = cloud_assets.NewClueModel().UpdateProcess(clue.Id, cast.ToFloat32(bar.GetPercent()))
				// 更新任务进度
				_ = cloud_assets.NewTaskModel().UpdateProcess(task.Id)
			})
		}
	}
	bar.Finish(func() {
		// 更新线索推荐进度,达到限制时,更新为0.1,其他按完成处理
		if !limit {
			_ = cloud_assets.NewClueModel().UpdateProcess(clue.Id, 100)
		} else {
			_ = cloud_assets.NewClueModel().UpdateProcess(clue.Id, 0.1)
		}
		// 更新任务进度
		_ = cloud_assets.NewTaskModel().UpdateProcess(task.Id)
	})
	log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,fofaClueHandler 线索:%+v,推荐完成", task.Id, clue))
}

func (t *Task) formatInsertOrUpdateAssets(ctx context.Context, task *cloud_assets.Task, clue *cloud_assets.Clue, fofaAsset *pb.Asset, source string, icpDomains []string) {
	if *fofaAsset.Link == "" {
		*fofaAsset.Link = *fofaAsset.Host
	}
	id := general_assets.GenId(*fofaAsset.Ip, *fofaAsset.Protocol, *fofaAsset.Port, utils.GetSubdomain(*fofaAsset.Link))
	dbAsset := general_assets.NewGeneralAssetModel().FindById(context.TODO(), id)
	isCdn := false
	// 判断Cname是否是CDN
	if fofaAsset.Cname != nil {
		if *fofaAsset.Cname != "" {
			isCdn = true
		}
	}
	tempAsset := general_assets.GeneralAsset{
		Id:           id,
		TaskId:       []uint64{task.Id},
		IsIPv6:       utils.IsIPv6(*fofaAsset.Ip),
		Ip:           *fofaAsset.Ip,
		Port:         *fofaAsset.Port,
		Url:          *fofaAsset.Link,
		Protocol:     *fofaAsset.Protocol,
		BaseProtocol: *fofaAsset.BaseProtocol,
		IsCDN:        isCdn,
		Clues: []general_assets.GeneralAssetClue{
			{Type: cast.ToInt(clue.Type), Hash: clue.Hash, Content: clue.Content, CompanyName: clue.CompanyName, ClueHash: clue.ClueHash, UpdatedAt: time.Now().Format("2006-01-02 15:04:05")},
		},
		Source: source,
		Risk:   []string{},
	}
	if fofaAsset.Title != nil {
		tempAsset.Title = *fofaAsset.Title
	}
	if fofaAsset.Link != nil {
		tempAsset.Domain = utils.GetTopDomain(*fofaAsset.Link)
		tempAsset.Subdomain = utils.GetSubdomain(*fofaAsset.Link)
	}
	if fofaAsset.Cert != nil {
		tempAsset.Cert = strings.Join(utils.GetCert(*fofaAsset.Cert, false), " ")
	}
	if fofaAsset.Icp != nil {
		tempAsset.Icp = *fofaAsset.Icp
	}
	if fofaAsset.IconHash != nil {
		tempAsset.Hash = cast.ToInt64(*fofaAsset.IconHash)
	}
	if fofaAsset.Icon != nil {
		tempAsset.Icon = *fofaAsset.Icon
	}
	if fofaAsset.Cert != nil {
		tempAsset.CertRaw = *fofaAsset.Cert
	}
	if fofaAsset.CertsValid != nil {
		tempAsset.CertsValid = cast.ToBool(*fofaAsset.CertsValid)
	}
	if fofaAsset.CloudName != nil {
		tempAsset.CloudName = *fofaAsset.CloudName
	}
	if fofaAsset.Lastupdatetime != nil {
		tempAsset.SourceUpdatedAt = *fofaAsset.Lastupdatetime
	}
	if fofaAsset.Cname != nil {
		tempAsset.Cname = *fofaAsset.Cname
	}
	if fofaAsset.Isp != nil {
		tempAsset.Isp = *fofaAsset.Isp
	}
	if fofaAsset.Server != nil {
		tempAsset.Server = *fofaAsset.Server
	}
	if fofaAsset.Version != nil {
		tempAsset.Version = *fofaAsset.Version
	}
	if fofaAsset.Version != nil {
		tempAsset.Version = *fofaAsset.Version
	}
	// 如果DB中存在该数据
	if dbAsset != nil {
		log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,线索ID:%d,推荐资产入库,ES已存在,IP:%s,Protocol:%s,Port:%s,Url:%s,IsCdn:%t", task.Id, clue.Id, *fofaAsset.Ip, *fofaAsset.Protocol, *fofaAsset.Port, *fofaAsset.Link, isCdn))
		// 追加taskId
		if !utils.ListContains(dbAsset.TaskId, task.Id) {
			dbAsset.TaskId = append(dbAsset.TaskId, task.Id)
			tempAsset.TaskId = dbAsset.TaskId
		}
		// 追加线索,线索不存在时
		if !general_assets.CanExistsClue(dbAsset.Clues, clue.ClueHash) {
			dbAsset.Clues = append(dbAsset.Clues, general_assets.GeneralAssetClue{
				Type:        cast.ToInt(clue.Type),
				Hash:        clue.Hash,
				CompanyName: clue.CompanyName,
				Content:     clue.Content,
				ClueHash:    clue.ClueHash,
				UpdatedAt:   time.Now().Format("2006-01-02 15:04:05"),
			})
			tempAsset.Clues = dbAsset.Clues
		}
		// 更新ICON_HASH
		if tempAsset.Hash == 0 && dbAsset.Hash != 0 {
			tempAsset.Hash = dbAsset.Hash
		}
		// 更新ICON
		if tempAsset.Icon == "" && dbAsset.Icon != "" {
			tempAsset.Icon = dbAsset.Icon
		}
		// 更新Cname
		if tempAsset.Cname == "" && dbAsset.Cname != "" {
			tempAsset.Cname = dbAsset.Cname
		}
		// 更新CloudName
		if tempAsset.CloudName == "" && dbAsset.CloudName != "" {
			tempAsset.CloudName = dbAsset.CloudName
		}
		// 更新CertsValid
		if !tempAsset.CertsValid && dbAsset.CertsValid {
			tempAsset.CertsValid = dbAsset.CertsValid
		}
		// 更新Isp
		if tempAsset.Isp == "" && dbAsset.Isp != "" {
			tempAsset.Isp = dbAsset.Isp
		}
		// 更新Cert
		if tempAsset.Cert == "" && dbAsset.Cert != "" {
			tempAsset.Cert = dbAsset.Cert
		}
		// 更新CertRaw
		if tempAsset.CertRaw == "" && dbAsset.CertRaw != "" {
			tempAsset.CertRaw = dbAsset.CertRaw
		}
		// 更新ICP
		if tempAsset.Icp == "" && dbAsset.Icp != "" {
			tempAsset.Icp = dbAsset.Icp
		}
		// 更新Title
		if tempAsset.Title == "" && dbAsset.Title != "" {
			tempAsset.Title = dbAsset.Title
		}
		// 更新Server
		if tempAsset.Server == "" && dbAsset.Server != "" {
			tempAsset.Server = dbAsset.Server
		}
		// 更新Version
		if tempAsset.Version == "" && dbAsset.Version != "" {
			tempAsset.Version = dbAsset.Version
		}
		// 更新Fid
		if tempAsset.Fid == "" && dbAsset.Fid != "" {
			tempAsset.Fid = dbAsset.Fid
		}
		// 更新风险信息
		tempAsset.Risk = utils.ListDistinct(append(tempAsset.Risk, dbAsset.Risk...))
		// 检查最新时间的资产
		if dbAsset.SourceUpdatedAt != nil {
			// 更新时间
			if t.checkIsNewAssetTime(tempAsset.SourceUpdatedAt.(string), dbAsset.SourceUpdatedAt.(string)) {
				tempAsset.SourceUpdatedAt = dbAsset.SourceUpdatedAt
				// 更新ICON_HASH
				if dbAsset.Hash != 0 && tempAsset.Hash != dbAsset.Hash {
					tempAsset.Hash = dbAsset.Hash
				}
				// 更新ICON
				if dbAsset.Icon != "" && tempAsset.Icon != dbAsset.Icon {
					tempAsset.Icon = dbAsset.Icon
				}
				// 更新Cname
				if dbAsset.Cname != "" && tempAsset.Cname != dbAsset.Cname {
					tempAsset.Cname = dbAsset.Cname
				}
				// 更新CloudName
				if dbAsset.CloudName != "" && tempAsset.CloudName != dbAsset.CloudName {
					tempAsset.CloudName = dbAsset.CloudName
				}
				// 更新CertsValid
				if tempAsset.CertsValid != dbAsset.CertsValid {
					tempAsset.CertsValid = dbAsset.CertsValid
				}
				// 更新Isp
				if dbAsset.Isp != "" && tempAsset.Isp != dbAsset.Isp {
					tempAsset.Isp = dbAsset.Isp
				}
				// 更新Cert
				if dbAsset.Cert != "" && tempAsset.Cert != dbAsset.Cert {
					tempAsset.Cert = dbAsset.Cert
				}
				// 更新CertRaw
				if dbAsset.CertRaw != "" && tempAsset.CertRaw != dbAsset.CertRaw {
					tempAsset.CertRaw = dbAsset.CertRaw
				}
				// 更新ICP
				if dbAsset.Icp != "" && tempAsset.Icp != dbAsset.Icp {
					tempAsset.Icp = dbAsset.Icp
				}
				// 更新Title
				if dbAsset.Title != "" && tempAsset.Title != dbAsset.Title {
					tempAsset.Title = dbAsset.Title
				}
				// 更新Server
				if dbAsset.Server != "" && tempAsset.Server != dbAsset.Server {
					tempAsset.Server = dbAsset.Server
				}
				// 更新Version
				if dbAsset.Version != "" && tempAsset.Version != dbAsset.Version {
					tempAsset.Version = dbAsset.Version
				}
				// 更新Fid
				if dbAsset.Fid != "" && tempAsset.Fid != dbAsset.Fid {
					tempAsset.Fid = dbAsset.Fid
				}
			}
		}
	}
	// 检测是否是泛解析域名
	tempAsset.IsWildCardDomain = t.CheckWildDomain(&tempAsset)
	// 评估资产等级
	t.SetAssetLevel(&tempAsset, icpDomains, clue)

	// 资产入库
	log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,线索ID:%d,推荐资产入库,Source:%s,IP:%s,Protocol:%s,Port:%s,Url:%s,IsCdn:%t,Level:%s", task.Id, clue.Id, source, *fofaAsset.Ip, *fofaAsset.Protocol, *fofaAsset.Port, *fofaAsset.Link, isCdn, tempAsset.Level))
	if err := general_assets.NewGeneralAssetModel().Create(context.TODO(), []general_assets.GeneralAsset{tempAsset}); err != nil {
		log.WithContextWarnf(ctx, fmt.Sprintf("推荐资产,任务:%d,线索ID:%d,推荐资产入库失败,Source:%s,Error:%s", task.Id, clue.Id, source, err.Error()))
	}
}

func (t *Task) CheckWildDomain(temAsset *general_assets.GeneralAsset) (isWildDomain bool) {
	cacheKey := "c-wild:"
	if temAsset.Domain == "" {
		return false
	}
	if temAsset.Subdomain == "" {
		return false
	}
	// 根域直接检测
	if temAsset.Subdomain == temAsset.Domain {
		// 获取缓存
		if redis.GetCache(cacheKey+temAsset.Domain, &isWildDomain) {
			return isWildDomain
		}
		// 检测
		isWildDomain = utils.IsWildDomain(temAsset.Domain)
		redis.SetCache(cacheKey, 5*time.Second, isWildDomain)
		return isWildDomain
	}
	// 子域名取上一级进行检测
	parentDomain := utils.GetParentDomain(temAsset.Subdomain)
	// 获取缓存
	if redis.GetCache(cacheKey+parentDomain, &isWildDomain) {
		return isWildDomain
	}
	// 检测
	isWildDomain = utils.IsWildDomain(parentDomain)
	redis.SetCache(cacheKey+parentDomain, 5*time.Second, isWildDomain)
	return isWildDomain
}

func (t *Task) SetAssetLevel(tempAsset *general_assets.GeneralAsset, icpDomains []string, clue *cloud_assets.Clue) {
	// A级:已确认是该企业资产,
	// B级:疑似是该企业的资产,无法准确判断,缺少关键信息的(存在一定供应关系的,CDN,产品相关),
	// C级:和该企业线索相关资产(信息被盗用,或杂音相关数数据)

	// 备案信息为空时,默认都为A级
	if len(icpDomains) == 0 {
		tempAsset.Level = general_assets.LevelB
		return
	}
	// 获取资产证书信息
	_, cnStr := utils.PluckCert(tempAsset.Cert)
	// 存在域名时,并且域名和ICP备案域名不一致,降级到C
	if tempAsset.Domain != "" && !utils.ListContains(icpDomains, tempAsset.Domain) {
		tempAsset.Level = general_assets.LevelC
	}
	// 不存在域名&线索只有ICP时,降级到C
	keywords := []string{"管理", "系统", "平台", "Login", "CRM", "OA", "Manager", "Login", "Admin", "登录"}
	if tempAsset.Domain == "" && len(tempAsset.Clues) == 1 && !utils.StrContains(tempAsset.Title, keywords...) {
		if tempAsset.Clues[0].Type == general_clues.ClueTypeIcp {
			tempAsset.Level = general_assets.LevelC
		}
	}
	// 资产不为C级时,检查Title黑名单标,降级到C
	if tempAsset.Level != general_assets.LevelC {
		bluckKeywrods := []string{
			"日韩", "美女", "押注", "小视频", "在线观看", "色情", "试玩", "投注",
			"图库", "自拍", "代理", "拉斯维加斯", "体育", "赌博", "国产", "押注", "真人",
			"水蜜桃", "18禁", "免费观看", "免费高清", "bob体彩", "威斯尼斯人", "亚洲入口", "维多利亚",
			"太阳集团", "太阳娱乐", "娱场城", "六合彩", "皇家娱乐", "买球", "电子游戏平台",
			"HD高清", "下注", "直播", "开奖", "黄片", "维多利亚", "正规平台", "皇宫", "高清视频在线",
			"网投", "太阳成", "888", "路线", "竞猜", "亚洲", "欧美", "备用网址", "在线观看",
			"扑克", "纯净版", "荷官",
		}
		if utils.StrContains(tempAsset.Title, bluckKeywrods...) {
			tempAsset.Level = general_assets.LevelC
		}
	}

	// 级别为空时,默认为B级
	if tempAsset.Level == "" {
		tempAsset.Level = general_assets.LevelB
	}
	// 证书为真&CN域名与备案域名匹配&URL域名不匹配,设为B级
	if tempAsset.CertsValid && cnStr != "" && utils.ListContains(icpDomains, utils.GetTopDomain(cnStr)) && tempAsset.Domain != "" && !utils.ListContains(icpDomains, utils.GetTopDomain(tempAsset.Domain)) {
		tempAsset.Level = general_assets.LevelB
		// 添加资产风险,域名证书不匹配
		if !utils.ListContains(tempAsset.Risk, general_assets.RiskDomainCertNoMatch) {
			tempAsset.Risk = append(tempAsset.Risk, general_assets.RiskDomainCertNoMatch)
		}
	} else {
		// 条件不匹配时,移除证书不匹配风险
		tempAsset.Risk, _ = utils.ListDelete(tempAsset.Risk, general_assets.RiskDomainCertNoMatch)
	}
	// 证书为真&CN域名与备案域名匹配,设为A级
	if tempAsset.CertsValid && cnStr != "" && utils.ListContains(icpDomains, utils.GetTopDomain(cnStr)) && tempAsset.Domain == "" {
		tempAsset.Level = general_assets.LevelA
	}
	// 域名在备案域中,设为A级资产 punycode域名处理
	if utils.ListContains(icpDomains, tempAsset.Domain) {
		tempAsset.Level = general_assets.LevelA
	}
	// 域名在备案域中,设为A级资产 punycode域名处理
	if decodedDomain, err := idna.ToUnicode(tempAsset.Domain); err == nil {
		if utils.ListContains(icpDomains, decodedDomain) {
			tempAsset.Level = general_assets.LevelA
		}
	}
	// IP在资产中,设为A级资产
	if utils.ListContains(icpDomains, tempAsset.Ip) {
		tempAsset.Level = general_assets.LevelA
	}
	// 检查备案为子域名的资产等级
	for i := range icpDomains {
		// 备案是IP,跳出
		if utils.IsIP(icpDomains[i]) {
			break
		}
		// 子域名
		if utils.GetTopDomain(icpDomains[i]) != icpDomains[i] {
			// 检查域名是否匹配
			if strings.HasSuffix(tempAsset.Subdomain, "."+icpDomains[i]) {
				tempAsset.Level = general_assets.LevelA
			}
			// 检查域名是否匹配
			if tempAsset.Subdomain == icpDomains[i] {
				tempAsset.Level = general_assets.LevelA
			}
		}
	}
	// 添加资产风险,ICP盗用(资产等级是C,只有一个线索源且线索类型是ICP,且有域名,且域名不是备案的域名)
	if tempAsset.Level == general_assets.LevelC && len(tempAsset.Clues) == 1 && tempAsset.Domain != "" {
		if tempAsset.Clues[0].Type == general_clues.ClueTypeIcp && !utils.ListContains(icpDomains, utils.GetTopDomain(tempAsset.Domain)) {
			if !utils.ListContains(tempAsset.Risk, general_assets.RiskIcpEmbezzle) {
				tempAsset.Risk = append(tempAsset.Risk, general_assets.RiskIcpEmbezzle)
			}
		}
	}

	// 存在多个线索时,移除ICP盗用风险
	if len(tempAsset.Clues) > 1 && utils.ListContains(tempAsset.Risk, general_assets.RiskIcpEmbezzle) {
		tempAsset.Risk, _ = utils.ListDelete(tempAsset.Risk, general_assets.RiskIcpEmbezzle)
	}
}

// 最新的资产时间
func (t *Task) checkIsNewAssetTime(t1Str, t2str string) bool {
	if t1Str == "" {
		return true
	}
	if t2str == "" {
		return false
	}
	t1, err := time.Parse("2006-01-02 15:04:05", t1Str)
	if err != nil {
		return true
	}
	t2, err := time.Parse("2006-01-02 15:04:05", t2str)
	if err != nil {
		return true
	}
	if t1.Before(t2) {
		return true
	} else {
		return false
	}
}

func (t *Task) StartRecommend(ctx context.Context, task *cloud_assets.Task, clues []*cloud_assets.Clue) {
	go func() {
		lockClueHashs := make([]string, 0)
		defer func(lockClueHashs []string, task *cloud_assets.Task) {
			// 释放任务锁
			t.taskRunning.Delete(task.Hash)
			// 释放线索锁
			for i := range lockClueHashs {
				t.clueRunning.Delete(lockClueHashs[i])
			}
			// 更新任务进度为,完成
			_ = cloud_assets.NewTaskModel().UpSet(task.Id, "process", 100)
			log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",任务处理完成")
		}(lockClueHashs, task)
		log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",线索总数:"+cast.ToString(len(clues)))
		for i := range clues {
			log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,线索ID:%d,线索总数:%d,当前到:%d", task.Id, clues[i].Id, len(clues), i))
			if clues[i].Process == 100 {
				total, err := general_assets.NewGeneralAssetModel().AppendTaskIdByClueHash(task.Id, clues[i].ClueHash)
				if err != nil {
					log.WithContextWarn(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",线索已推荐过,线索ID:"+cast.ToString(clues[i].Id)+",更新该线索任务ID失败:"+err.Error())
				} else {
					log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",线索已推荐过,线索ID:"+cast.ToString(clues[i].Id)+",更新该线索任务ID成功,更新资产总数:"+cast.ToString(total))
				}
				_ = cloud_assets.NewTaskModel().UpdateProcess(task.Id)
				continue
			}
			// 根据线索获取资产
			if !t.CheckClueRunning(clues[i].ClueHash) {
				if t.checkClueCountLimit(task.Hash, cast.ToInt(clues[i].Type)) {
					log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,线索ID:%d,该类型线索资产达到数量限制,该线索不再推荐", task.Id, clues[i].Id))
					// 更新线索推荐进度
					clues[i].Process = 100
					_ = cloud_assets.NewClueModel().UpdateProcess(clues[i].Id, 0.1)
					// 更新任务进度
					_ = cloud_assets.NewTaskModel().UpdateProcess(task.Id)
					continue
				}
				log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",处理线索资产:"+cast.ToString(clues[i].Id))
				lockClueHashs = append(lockClueHashs, clues[i].ClueHash)
				t.clueRunning.Store(clues[i].ClueHash, nil)
				// 获取资产总数
				count, fofaHandler := t.getFofaCount(ctx, task, clues[i])
				// 获取备案域名列表
				icpDomains := t.GetIcpDomainsByCompanyName(clues[i].CompanyName)
				log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,线索:%+v,线索同企业备案域名:%+v", task.Id, clues[i], icpDomains))
				// 计算进度
				bar := process.NewBar(0, cast.ToInt(count))
				// 处理资产数据
				if fofaHandler != nil {
					fofaHandler(bar, icpDomains)
				}
				// 域名获取FOFA Dns数据
				if clues[i].Type == general_clues.ClueTypeDomain {
					log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,线索ID:%d,域名线索开始处理FOFA纯解析数据,domain:%s:", task.Id, clues[i].Id, clues[i].Content))
					t.PullFofaDnsAsset(ctx, task, clues[i], icpDomains)
				}
				t.clueRunning.Delete(clues[i].ClueHash)
				// 更新任务进度为完成
				clues[i].Process = 100
				_ = cloud_assets.NewClueModel().UpdateProcess(clues[i].Id, 100)
				log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",处理线索:"+cast.ToString(clues[i].Id)+" 处理完成")
			} else {
				log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",处理线索:"+cast.ToString(clues[i].Id)+" 其他任务正常处理该线索,等待线索处理完成,追加任务ID")
				go func(task *cloud_assets.Task, clue *cloud_assets.Clue) {
					for {
						time.Sleep(1 * time.Second)
						cProcess, err := cloud_assets.NewClueModel().GetProcess(clue.Id)
						log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",等待线索处理完成,线索ID:"+cast.ToString(clue.Id)+",线索处理进度:"+cast.ToString(cProcess))
						if err != nil {
							continue
						}
						if cProcess == 0.1 || cProcess == 100 {
							clue.Process = 100
							break
						}
					}
					total, err := general_assets.NewGeneralAssetModel().AppendTaskIdByClueHash(task.Id, clue.ClueHash)
					if err != nil {
						log.WithContextWarn(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",等待线索处理完成,线索ID:"+cast.ToString(clue.Id)+",更新该线索任务ID失败:"+err.Error())
					} else {
						log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",等待线索处理完成,线索ID:"+cast.ToString(clue.Id)+",更新该线索任务ID成功,更新资产总数:"+cast.ToString(total))
					}
				}(task, clues[i])
			}
		}
	}()
}

// GetIcpDomainsByCompanyName 根据企业名称获取ICP备案域名
func (t *Task) GetIcpDomainsByCompanyName(companyName string) []string {
	domains := make([]string, 0)
	if companyName != "" {
		// 获取企业备案域名
		icps, err := company_icp.NewCompanyIcpModel().GetByName(companyName, true)
		if err == nil {
			// 获取ICP Domains
			icpDomains := utils.ListColumn[string](icps, func(icp company_icp.CompanyIcp) string {
				return icp.Domain
			})
			// 提取非空域名
			for x := range icpDomains {
				if icpDomains[x] != "" {
					domains = append(domains, icpDomains[x])
				}
			}
		}
	}
	return domains
}

func (t *Task) PullFofaDnsAsset(ctx context.Context, task *cloud_assets.Task, clue *cloud_assets.Clue, icpDomains []string) {
	page := cast.ToUint32(1)
	retry, max, limitMaxPage := 0, 3, cast.ToUint32(100)
	limit := false
	certsValid, port, protocol, baseProtocol := "false", "80", "http", "tcp"
	// 最多拉取1W条数据
	for page < limitMaxPage || !limit {
		rsp := &pb.FofaPureDnsResponse{}
		if err := fofa.FofaPureDns(context.TODO(), rsp, &pb.FofaPureDnsRequest{Domain: clue.Content, Page: page, Size: 100}); err != nil {
			if retry < max {
				retry += 1
				log.WithContextWarnf(ctx, fmt.Sprintf("推荐资产,任务:%d,拉取纯解析数据,domain:%s,重试次数:%d,数据拉取失败:%s", task.Id, clue.Content, retry, err.Error()))
				continue
			} else {
				log.WithContextWarnf(ctx, fmt.Sprintf("推荐资产,任务:%d,拉取纯解析数据,domain:%s,重试次数:%d,达到最大失败次数:%s", task.Id, clue.Content, retry, err.Error()))
				break
			}
		}
		if len(rsp.Items) == 0 {
			log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,拉取纯解析数据,domain:%s,Item为空,跳出循环", task.Id, clue.Content))
			break
		}
		if t.checkClueCountLimit(task.Hash, cast.ToInt(clue.Type)) {
			log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,拉取纯解析数据,domain:%s,到达资产类型限制跳出循环", task.Id, clue.Content))
			break
		}
		for i := range rsp.Items {
			log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,拉取纯解析数据,domain:%s,当前页:%d,存解析总数:%d", task.Id, clue.Content, *rsp.Page, *rsp.Total))
			host, domain := "http://"+rsp.Items[i].Host, utils.GetTopDomain(rsp.Items[i].Host)
			if rsp.Items[i].Ip == "" {
				// IP 为空的数据,解析子域名
				ips, loupErr := net.LookupIP(utils.GetSubdomain(rsp.Items[i].Host))
				if loupErr != nil {
					log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,拉取纯解析数据,domain:%s,IP为空并且解析IP失败,跳过当前数据:%s", task.Id, clue.Content, rsp.Items[i].Host))
					continue
				}
				// 解析IP入库
				for x := range ips {
					// 添加线索资产计数
					t.addClueCount(task, cast.ToInt(clue.Type))
					// 计算线索资产是否达到限制
					if t.checkClueCountLimit(task.Hash, cast.ToInt(clue.Type)) {
						limit = true
						log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,拉取纯解析数据,domain:%s,到达资产类型限制跳出循环", task.Id, clue.Content))
						break
					}
					rsp.Items[i].Ip = ips[x].String()
					asset := &pb.Asset{
						Domain:         &domain,
						Ip:             &rsp.Items[i].Ip,
						Port:           &port,
						Lastupdatetime: &rsp.Items[i].LastUpdateTime,
						Protocol:       &protocol,
						BaseProtocol:   &baseProtocol,
						Host:           &host,
						CertsValid:     &certsValid,
						Link:           &host,
					}
					t.formatInsertOrUpdateAssets(ctx, task, clue, asset, "FofaPureDns", icpDomains)
				}
			} else {
				// 添加线索资产计数
				t.addClueCount(task, cast.ToInt(clue.Type))
				// 计算线索资产是否达到限制
				if t.checkClueCountLimit(task.Hash, cast.ToInt(clue.Type)) {
					limit = true
					log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,拉取纯解析数据,domain:%s,到达资产类型限制跳出循环", task.Id, clue.Content))
					break
				}
				asset := &pb.Asset{
					Domain:         &domain,
					Ip:             &rsp.Items[i].Ip,
					Port:           &port,
					Lastupdatetime: &rsp.Items[i].LastUpdateTime,
					Protocol:       &protocol,
					BaseProtocol:   &baseProtocol,
					Host:           &host,
					CertsValid:     &certsValid,
					Link:           &host,
				}
				t.formatInsertOrUpdateAssets(ctx, task, clue, asset, "FofaPureDns", icpDomains)
			}
		}
		page += 1
	}
	if limit {
		log.Info(fmt.Sprintf("推荐资产,任务:%d,域名:%s,该类型线索资产达到数量限制,FOFA纯解析,停止推荐", task.Id, clue.Content))
		_ = cloud_assets.NewClueModel().UpdateProcess(clue.Id, 0.1)
	}
}

// GetFofaQuery 根据线索获取FOFA查询语句
func (t *Task) GetFofaQuery(clue *cloud_assets.Clue) string {
	if clue.Type == general_clues.ClueTypeIcon {
		return fmt.Sprintf(`icon_hash="%s"`, cast.ToString(clue.Hash))
	} else if clue.Type == general_clues.ClueTypeIp {
		return fmt.Sprintf(`ip="%s"`, clue.Content)
	} else if clue.Type == general_clues.ClueTypeKeyword || clue.Type == general_clues.ClueTypeCompanyName {
		return fmt.Sprintf(`title="%s"`, clue.Content)
	} else if clue.Type == general_clues.ClueTypeDomain || clue.Type == general_clues.ClueTypeSubdomain {
		// `(domain="%s" || host*="*.%s" || header="%s/" || banner="%s;" || ip ="%s")`
		if utils.IsIP(clue.Content) {
			return fmt.Sprintf(`(domain="%s" || host*="*.%s" || ip ="%s")`, clue.Content, clue.Content, clue.Content)
		} else {
			return fmt.Sprintf(`(domain="%s" || host*="*.%s")`, clue.Content, clue.Content)
		}
	} else if clue.Type == general_clues.ClueTypeIcp {
		return fmt.Sprintf(`(icp="%s" || icp="%s" || body="%s")`, clue.Content, strings.TrimSuffix(clue.Content, "号"), clue.Content)
	} else if clue.Type == general_clues.ClueTypeCert {
		oStr, cnStr := utils.PluckCert(clue.Content)
		if oStr == "" && cnStr == "" {
			return fmt.Sprintf(`(cert="CommonName: %s" || cert="CommonName: *.%s") || (cert.subject="%s")`, clue.Content, clue.Content, clue.Content)
		} else {
			cn := strings.TrimPrefix(cnStr, "*.")
			if cnStr != "" {
				return fmt.Sprintf(`(cert="CommonName: %s" || cert="CommonName: *.%s")`, cn, cn)
			} else {
				return fmt.Sprintf(`(cert.subject="%s")`, oStr)
			}
		}
	} else {
		return fmt.Sprintf(`title="%s"`, clue.Content)
	}
}

// CreateTask 创建推荐任务
func (t *Task) CreateTask(ctx context.Context, req *pb.RecommendTaskRequest, rsp *pb.RecommendTaskResponse) (*cloud_assets.Task, []*cloud_assets.Clue, error) {
	force := false
	hash := cloud_assets.TaskHash(req.Clue)
	task, err := cloud_assets.NewTaskModel().FindByHash(hash, utils.SubDayTime(time.Duration(cfg.LoadCommon().AssetCacheDay)))
	if err == nil {
		force = req.Force
		rsp.TaskId = task.Id
		if force {
			_ = cloud_assets.NewTaskModel().InitProcess(task.Id)
		}
	} else {
		force = true
		// 不存在记录,添加记录并返回
		task = cloud_assets.NewTaskModel().UpdateByHash(hash)
		rsp.TaskId = task.Id
	}
	// 添加线索
	clues, clueErr := t.addClue(req, force)
	if clueErr != nil {
		return nil, nil, clueErr
	}
	if aErr := t.addTaskClue(ctx, task.Id, clues, req); aErr != nil {
		return nil, nil, aErr
	}
	return task, clues, nil
}

// addTaskClue 添加任务线索
func (t *Task) addTaskClue(ctx context.Context, taskId uint64, clues []*cloud_assets.Clue, req *pb.RecommendTaskRequest) error {
	for i := range req.Clue {
		cl := t.GetClueByHash(cloud_assets.GetClueHash(req.Clue[i].Content, req.Clue[i].Hash, cast.ToUint8(req.Clue[i].Type)), clues)
		nErr := cloud_assets.NewTaskClueModel().NewTaskClue(&cloud_assets.TaskClue{
			TaskId: taskId,
			ClueId: cl.Id,
		})
		if nErr != nil {
			log.WithContextWarnf(ctx, fmt.Sprintf("推荐资产,任务:%d,添加线索关系失败:%s", taskId, nErr.Error()))
		} else {
			log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,线索:%+v,添加线索关系OK", taskId, cl))
		}
	}
	return nil
}

// GetPureDnsCount 获取Dns数据总数
func (t *Task) GetPureDnsCount(ctx context.Context, domain string) uint64 {
	rsp := &pb.FofaPureDnsResponse{}
	if err := fofa.FofaPureDns(context.TODO(), rsp, &pb.FofaPureDnsRequest{Domain: domain, Page: 1, Size: 1}); err != nil {
		// 失败重试一次
		log.WithContextWarnf(ctx, "获取Dns数据总数失败重试一次,domain:"+domain)
		if err = fofa.FofaPureDns(context.TODO(), rsp, &pb.FofaPureDnsRequest{Domain: domain, Page: 1, Size: 1}); err != nil {
			// 失败,按默认1000处理
			total := cast.ToUint32(1000)
			rsp = &pb.FofaPureDnsResponse{Total: &total}
		}
	}
	return cast.ToUint64(rsp.Total)
}

// GetClueByHash 根据线索hash查找线索
func (t *Task) GetClueByHash(hash string, clues []*cloud_assets.Clue) *cloud_assets.Clue {
	for i := range clues {
		if clues[i].ClueHash == hash {
			return clues[i]
		}
	}
	return nil
}

// addClue 添加推荐线索
func (t *Task) addClue(req *pb.RecommendTaskRequest, force bool) ([]*cloud_assets.Clue, error) {
	cls := make([]*cloud_assets.Clue, 0)
	for _, cl := range req.Clue {
		if cl.Type == general_clues.ClueTypeIcon {
			cl.Content, cl.Hash = storage.SaveUrlMmh3(cl.Content, cast.ToString(cl.Hash), true)
		}
		cls = append(cls, &cloud_assets.Clue{
			ClueHash:    cloud_assets.GetClueHash(cl.Content, cl.Hash, cast.ToUint8(cl.Type)),
			Hash:        cl.Hash,
			Content:     cl.Content,
			Type:        cast.ToUint8(cl.Type),
			CompanyName: cl.CompanyName,
		})
	}
	dbClues, clueErr := cloud_assets.NewClueModel().NewClue(cls, cfg.LoadCommon().AssetCacheDay, force)
	if clueErr != nil {
		return nil, clueErr
	}
	return dbClues, nil
}

// AddRecommendTask 添加推荐任务
func AddRecommendTask(ctx context.Context, req *pb.RecommendTaskRequest, rsp *pb.RecommendTaskResponse) error {
	task, clues, err := GetTaskInstance().CreateTask(ctx, req, rsp)
	if err != nil {
		return err
	}
	rsp.TaskId = task.Id
	// 检查任务是否重复执行
	if GetTaskInstance().CheckTaskRunning(task.Hash) {
		log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",任务执行中,不在重复执行")
		return nil
	}
	clueIds := utils.ListColumn(clues, func(clue *cloud_assets.Clue) any {
		return clue.Id
	})
	log.WithContextInfof(ctx, "推荐资产,任务:"+cast.ToString(task.Id)+",开始执行推荐任务")
	log.WithContextInfof(ctx, fmt.Sprintf("推荐资产,任务:%d,线索总数:%d,hash列表:%+v", task.Id, len(clueIds), clueIds))
	// 执行推荐任务
	GetTaskInstance().StartRecommend(ctx, task, clues)
	return nil
}

// GetRecommendProcess 获取推荐进度
func GetRecommendProcess(_ context.Context, req *pb.RecommendProcessRequest, rsp *pb.RecommendProcessResponse) error {
	task, err := cloud_assets.NewTaskModel().FindById(req.TaskId)
	if err != nil {
		return err
	}
	rsp.Process = cast.ToUint64(task.Process)
	rsp.TaskId = task.Id
	// 获取任务关系线索信息
	clues, cErr := cloud_assets.NewClueModel().GetCluesByTaskId(req.TaskId)
	if cErr != nil {
		return cErr
	}
	// 返回线索进度
	for i := range clues {
		rsp.Clues = append(rsp.Clues, &pb.ClueProcessInfo{
			Id:          clues[i].Id,
			Content:     clues[i].Content,
			Hash:        clues[i].Hash,
			CompanyName: clues[i].CompanyName,
			Process:     cast.ToUint64(clues[i].Process),
		})
	}
	return nil
}

// GetRecommendResult 获取推荐结果
func GetRecommendResult(_ context.Context, req *pb.RecommendResultRequest, rsp *pb.RecommendResultResponse) error {
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PerPage == 0 {
		req.PerPage = 10
	}
	query := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("task_id", utils.ListColumn(req.TaskId, func(t uint64) any { return t })...))
	if len(req.Level) != 0 {
		query = query.Must(elastic.NewTermsQuery("level.keyword", utils.ListColumn(req.Level, func(t string) any { return t })...))
	}
	if len(req.Domain) != 0 {
		query = query.Must(elastic.NewTermsQuery("domain", utils.ListColumn(req.Domain, func(t string) any { return t })...))
	}
	if len(req.Ip) != 0 {
		query = query.Must(elastic.NewTermsQuery("ip", utils.ListColumn(req.Ip, func(t string) any { return t })...))
	}
	if len(req.ClueHash) != 0 {
		query = query.Must(elastic.NewTermsQuery("clues.clue_hash", utils.ListColumn(req.ClueHash, func(t string) any { return t })...))
	}
	if len(req.Port) != 0 {
		query = query.Must(elastic.NewTermsQuery("port", utils.ListColumn(req.Port, func(t uint32) any { return t })...))
	}
	// 泛解析域名 过滤
	if req.IsWildcardDomain != "" {
		if req.IsWildcardDomain == TrueStr {
			query = query.Must(elastic.NewTermQuery("is_wildcard_domain", true))
		} else {
			query = query.Must(elastic.NewTermQuery("is_wildcard_domain", false))
		}
	}
	// IPV6 过滤
	if req.IsIpv6 != "" {
		if req.IsIpv6 == TrueStr {
			query = query.Must(elastic.NewTermQuery("is_ipv6", true))
		} else {
			query = query.Must(elastic.NewTermQuery("is_ipv6", false))
		}
	}
	if total, assets, err := general_assets.NewGeneralAssetModel().List(context.TODO(), query, cast.ToInt(req.Page), cast.ToInt(req.PerPage), req.Fields); err != nil {
		return err
	} else {
		rsp.Total = total
		rsp.PerPage = req.PerPage
		rsp.CurrentPage = req.Page
		for k := range assets {
			dbAsset := pb.CloudAsset{}
			t, _ := json.Marshal(assets[k])
			_ = json.Unmarshal(t, &dbAsset)
			for i := range dbAsset.Clues {
				if dbAsset.Clues[i].Type == general_clues.ClueTypeIcon {
					dbAsset.Clues[i].Content = storage.GenAPIDownloadPath("favicon.ico", dbAsset.Clues[i].Content)
				}
			}
			rsp.Items = append(rsp.Items, &dbAsset)
		}
	}
	return nil
}
