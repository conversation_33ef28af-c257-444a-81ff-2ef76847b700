package dnschecker

import (
	"context"
	"errors"
	"fmt"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/utils"
	"time"

	crawlerPb "micro-service/crawlerService/proto"
)

// GetParseIp RPC接口实现
func GetParseIp(ctx context.Context, request *pb.DnscheckerDomainRequest, response *pb.DnscheckerResponse) error {
	if request == nil || request.Domain == "" {
		return fmt.Errorf("域名不能为空")
	}

	// 调用 crawler 服务的 CheckDNS 方法
	for i := 0; i < 3; i++ {
		crawlerResp, err := crawlerPb.GetProtoClient().CheckDNS(ctx, &crawlerPb.DnsCheckRequest{
			Domain: request.Domain,
		}, utils.RpcTimeoutDur(1*time.Minute), microx.ServerTimeoutDur(1*time.Minute))
		if err == nil {
			response.Ip = crawlerResp.Ips
			return nil
		}
		log.WithContextWarnf(ctx, "Retry %d - Dnschecker -> DNS解析失败 -> query:%s Error:%v", i+1, request.Domain, err)
		time.Sleep(time.Second)
	}
	return errors.New("DNS解析失败")
}
