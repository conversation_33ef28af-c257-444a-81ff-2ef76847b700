package icplishi

import (
	"context"
	"fmt"
	corePb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"

	"github.com/antchfx/htmlquery"
	"golang.org/x/net/html"
)

const NotBeiAnStr = "未备案"
const UserBeiAnStr = "个人"

func checkIsCompany(doc *html.Node) bool {
	baTypeNode := htmlquery.FindOne(doc, "//table[1]/tbody/tr[1]/td[2]")
	if baTypeNode != nil {
		return htmlquery.InnerText(baTypeNode) != UserBeiAnStr
	}
	return true
}

func QueryDomain(ctx context.Context, rsp *corePb.IcpResponse, domain string) error {
	defer func() {
		if err := recover(); err != nil {
			log.WithContextWarnf(ctx, "QueryDomain,Err:", err)
		}
	}()
	var err error
	// 获取info
	rsp.Ba, rsp.Info, _ = getDomainInfo(ctx, domain)
	if !rsp.Ba {
		return nil
	}
	// 获取同备案信息
	_, rsp.Equals, err = getCompanyList(ctx, rsp.Info.CompanyName, rsp.Info.CompanyType)
	if err != nil {
		return err
	}
	// 清除主备案信息
	for index, beian := range rsp.Equals {
		if beian.Icp == rsp.Info.Icp {
			rsp.Equals = append(rsp.Equals[:index], rsp.Equals[index+1:]...)
		}
	}
	return nil
}

func QueryCompanyName(ctx context.Context, rsp *corePb.IcpResponse, companyName string) error {
	var err error
	rsp.Ba, rsp.Equals, err = getCompanyList(ctx, companyName, "")
	if err != nil {
		return err
	}
	if len(rsp.Equals) != 0 {
		rsp.Ba, rsp.Info, _ = getDomainInfo(ctx, rsp.Equals[0].WebsiteUrl)
	}
	// 清除主备案信息
	for index, beian := range rsp.Equals {
		if beian.Icp == rsp.Info.Icp {
			rsp.Equals = append(rsp.Equals[:index], rsp.Equals[index+1:]...)
		}
	}
	return nil
}

func QueryIcp(ctx context.Context, rsp *corePb.IcpResponse, icp string) error {
	defer func() {
		if err := recover(); err != nil {
			log.WithContextWarnf(ctx, "QueryIcp,Err:", err)
		}
	}()
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    fmt.Sprintf("https://icplishi.com/%s/", icp),
		Method: crawlerPb.MethodCurlGet,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return err
	}
	log.WithContextInfof(ctx, "crawler IcpLiShi icp-> icp:%s,code:%d,size:%d", icp, crawlerRes.Code, len(crawlerRes.Body))
	// Load the HTML document
	doc, err := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
	if err != nil {
		return err
	}
	if extNode := htmlquery.FindOne(doc, "/html/body/div/div[2]/div/div[1]/div/div/div[2]/div/p"); extNode != nil {
		if htmlquery.InnerText(extNode) == "暂无此备案号相关信息" {
			rsp.Ba = false
			return nil
		}
	}
	rsp.Ba = true
	// 判断有没有数据
	node := htmlquery.FindOne(doc, "//tr[1]/td[2]")
	if node == nil {
		log.WithContextInfof(ctx, "icplishi-content-取不到body数据-%s", fmt.Sprintf("https://icplishi.com/%s/", icp))
		return nil
	}
	companyType := htmlquery.InnerText(htmlquery.FindOne(doc, "//tr[1]/td[2]"))
	companyName := htmlquery.InnerText(htmlquery.FindOne(doc, "//tr[2]/td[2]"))
	list, err := htmlquery.QueryAll(doc, "//div[@class=\"table-inner\"]/table/tbody/tr")
	if err != nil {
		return nil
	}
	var parentIcpNumber string
	for _, node := range list {
		if rsp.Info == nil {
			if htmlquery.FindOne(node, "//td[3]/span") != nil {
				rsp.Info = &corePb.BeiAn{
					CompanyName: companyName,
					Icp:         htmlquery.InnerText(htmlquery.FindOne(node, "//td[1]/a")),
					CompanyType: companyType,
					WebsiteUrl:  htmlquery.InnerText(htmlquery.FindOne(node, "//td[2]/a")),
					AuditTime:   htmlquery.InnerText(htmlquery.FindOne(node, "//td[3]/span")),
				}
				parentIcpNumber = rsp.Info.Icp
			} else {
				rsp.Info = &corePb.BeiAn{
					CompanyName: companyName,
					Icp:         parentIcpNumber,
					CompanyType: companyType,
					WebsiteUrl:  htmlquery.InnerText(htmlquery.FindOne(node, "//td[1]/a")),
					AuditTime:   htmlquery.InnerText(htmlquery.FindOne(node, "//td[2]/span")),
				}
			}
		} else {
			if htmlquery.FindOne(node, "//td[3]/span") != nil {
				rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
					CompanyName: companyName,
					Icp:         htmlquery.InnerText(htmlquery.FindOne(node, "//td[1]/a")),
					CompanyType: companyType,
					WebsiteUrl:  htmlquery.InnerText(htmlquery.FindOne(node, "//td[2]/a")),
					AuditTime:   htmlquery.InnerText(htmlquery.FindOne(node, "//td[3]/span")),
				})
			} else {
				rsp.Equals = append(rsp.Equals, &corePb.BeiAn{
					CompanyName: companyName,
					Icp:         parentIcpNumber,
					CompanyType: companyType,
					WebsiteUrl:  htmlquery.InnerText(htmlquery.FindOne(node, "//td[1]/a")),
					AuditTime:   htmlquery.InnerText(htmlquery.FindOne(node, "//td[2]/span")),
				})
			}
		}
	}
	return nil
}

func getDomainInfo(ctx context.Context, domain string) (bool, *corePb.BeiAn, error) {
	defer func() {
		if err := recover(); err != nil {
			log.WithContextWarnf(ctx, "getDomainInfo,Err:", err)
		}
	}()
	var icpBan corePb.BeiAn
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    fmt.Sprintf("https://icplishi.com/%s", domain),
		Method: crawlerPb.MethodCurlGet,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return true, nil, err
	}
	log.WithContextInfof(ctx, "crawler IcpLiShi domain-> domain:%s,code:%d,size:%d", domain, crawlerRes.Code, len(crawlerRes.Body))
	// Load the HTML document
	doc, err := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
	if err != nil {
		return true, nil, err
	}
	//
	// 禁止查询
	if exNode := htmlquery.FindOne(doc, "/html/body/div/div[2]/div/div[1]/div[2]/div[1]/div[2]/div/p"); exNode != nil {
		if htmlquery.InnerText(exNode) == "该域名禁止查询" {
			return true, nil, nil
		}
	}
	// 判断node是否存在 ,检查域名是否备案
	node1 := htmlquery.FindOne(doc, "//table[1]/tbody/tr[1]/td[2]")
	if node1 != nil {
		noBaNode := htmlquery.FindOne(doc, "//table[1]/tbody/tr[1]/td[2]")
		if noBaNode != nil {
			if htmlquery.InnerText(noBaNode) == NotBeiAnStr {
				return false, nil, nil
			}
		}
	}
	// 检查域名是否备案
	node2 := htmlquery.FindOne(doc, "//table[1]/tbody/tr[2]/td[2]")
	if node2 != nil {
		noBaNode2 := htmlquery.FindOne(doc, "//table[1]/tbody/tr[2]/td[2]")
		if noBaNode2 != nil {
			if htmlquery.InnerText(noBaNode2) == NotBeiAnStr {
				return false, nil, nil
			}
		}
	}

	if checkIsCompany(doc) {
		if htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[1]/td[1]")) == "网站首页" {
			icpBan = corePb.BeiAn{
				CompanyName: htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[3]/td[2]")),
				Icp:         htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[4]/td[2]")),
				CompanyType: htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[2]/td[2]")),
				WebsiteUrl:  domain,
				AuditTime:   htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[5]/td[2]/span[1]")),
			}
		} else {
			icpBan = corePb.BeiAn{
				CompanyName: htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[2]/td[2]")),
				Icp:         htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[3]/td[2]")),
				CompanyType: htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[1]/td[2]")),
				WebsiteUrl:  domain,
				AuditTime:   htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[4]/td[2]/span[1]")),
			}
		}
	} else { // 个人备案
		icpBan = corePb.BeiAn{
			CompanyName: htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[2]/td[2]")),
			Icp:         htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[3]/td[2]")),
			CompanyType: htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[1]/td[2]")),
			WebsiteUrl:  domain,
			AuditTime:   htmlquery.InnerText(htmlquery.FindOne(doc, "//table[1]/tbody/tr[4]/td[2]/span[1]")),
		}
	}
	return true, &icpBan, nil
}

func getCompanyList(ctx context.Context, companyName, companyType string) (bool, []*corePb.BeiAn, error) {
	defer func() {
		if err := recover(); err != nil {
			log.WithContextWarnf(ctx, "getDomainInfo,Err:", err)
		}
	}()
	blist := make([]*corePb.BeiAn, 0)
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    fmt.Sprintf("https://icplishi.com/company/%s/", companyName),
		Method: crawlerPb.MethodCurlGet,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return true, nil, err
	}
	log.WithContextInfof(ctx, "crawler IcpLiShi company-> company:%s,code:%d,size:%d", companyName, crawlerRes.Code, len(crawlerRes.Body))
	// Load the HTML document
	doc, err := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
	if err != nil {
		return true, nil, err
	}
	// 没有备案信息
	if exNode := htmlquery.FindOne(doc, "/html/body/div/div[2]/div/div[1]/div/div/p"); exNode != nil {
		if htmlquery.InnerText(exNode) == fmt.Sprintf("暂无 %s 相关信息", companyName) {
			return false, nil, nil
		}
	}
	list, err := htmlquery.QueryAll(doc, "//table/tbody/tr")
	if err != nil {
		return true, nil, err
	}
	var parentIcpNumber string
	for _, node := range list {
		var icp corePb.BeiAn
		if htmlquery.FindOne(node, "//td[3]/span") != nil {
			icp = corePb.BeiAn{
				CompanyName: companyName,
				Icp:         htmlquery.InnerText(htmlquery.FindOne(node, "//td[1]/a")),
				CompanyType: companyType,
				WebsiteUrl:  htmlquery.InnerText(htmlquery.FindOne(node, "//td[2]/a")),
				AuditTime:   htmlquery.InnerText(htmlquery.FindOne(node, "//td[3]/span")),
			}
			parentIcpNumber = icp.Icp
		} else {
			icp = corePb.BeiAn{
				CompanyName: companyName,
				Icp:         parentIcpNumber,
				CompanyType: companyType,
				WebsiteUrl:  htmlquery.InnerText(htmlquery.FindOne(node, "//td[1]/a")),
				AuditTime:   htmlquery.InnerText(htmlquery.FindOne(node, "//td[2]/span")),
			}
		}

		if companyType == "" && icp.Icp != "" {
			if crawlerTypeRes, err := crawlerPb.GetProtoClient().Get(context.Background(), &crawlerPb.GetRequest{
				Url:    fmt.Sprintf("https://icplishi.com/%s/", icp.Icp),
				Method: crawlerPb.MethodCurlGet,
			}, utils.SetRpcTimeoutOpt(30)); err == nil {
				if companyTypeNode, pErr := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(crawlerTypeRes.Body))); pErr == nil {
					if htmlquery.FindOne(companyTypeNode, "//tr[1]/td[2]") != nil {
						companyType = htmlquery.InnerText(htmlquery.FindOne(companyTypeNode, "//tr[1]/td[2]"))
					}
				} else {
					log.WithContextInfof(ctx, "crawler IcpLiShi company-> company:%s,code:%d,size:%d,queryCompanyTypeErr:%s", companyName, crawlerTypeRes.Code, len(crawlerTypeRes.Body), err.Error())
				}
			} else {
				log.WithContextInfof(ctx, "crawler IcpLiShi company-> company:%s,code:%d,size:%d,queryCompanyTypeErr:%s", companyName, crawlerTypeRes.Code, len(crawlerTypeRes.Body), err.Error())
			}
		}
		blist = append(blist, &icp)
	}

	if companyType != "" && len(blist) != 0 {
		for i := 0; i < len(blist); i++ {
			ban := &blist[i]
			(*ban).CompanyType = companyType
		}
	}
	return true, blist, nil
}
