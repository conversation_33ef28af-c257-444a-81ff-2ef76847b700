package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	hDiscover "micro-service/coreService/handler/discover"
	"micro-service/coreService/handler/recommend"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/cloud_assets"
	"micro-service/middleware/mysql/discover"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
)

// DiscoverCreateGroupTask 创建集团资产发现任务
func (e *Core) DiscoverCreateGroupTask(ctx context.Context, req *pb.DiscoverCreateGroupTaskRequest, rsp *pb.DiscoverCreateTaskResponse) error {
	_, err := hDiscover.GetGroupTaskInstance().CreateGroupTask(ctx, req, rsp)
	if err != nil {
		return err
	}
	return nil
}

// DiscoverGroupTaskProcess 集团资产发现任务进度查询
func (e *Core) DiscoverGroupTaskProcess(ctx context.Context, req *pb.DiscoverGroupTaskRequest, rsp *pb.DiscoverTaskProcessResponse) error {
	task, err := hDiscover.GetGroupTaskInstance().GetGroupTaskProcess(ctx, req.CompanyName, req.Model, req.Percent)
	if err != nil {
		return err
	}
	rsp.Process = cast.ToFloat32(task.Process)
	if task.Reason != "" {
		return errors.New(task.Reason)
	}
	return nil
}

// DiscoverGroupTaskOrgs 集团资产发现投资信息
func (e *Core) DiscoverGroupTaskOrgs(_ context.Context, req *pb.DiscoverGroupTaskCluesRequest, rsp *pb.DiscoverGroupTaskOrgsResponse) error {
	if req.CompanyName == "" {
		return fmt.Errorf("获取任务信息失败")
	}
	task, err := discover.NewGroupTaskModel().First(
		mysql.WithWhere("company_name", req.CompanyName),
		mysql.WithWhere("type", req.Model),
		mysql.WithWhere("percent", req.Percent),
	)
	if err != nil {
		return fmt.Errorf("获取任务信息失败:%s", err.Error())
	}
	orgs, total, err := discover.NewGroupCompanyModel().List(
		cast.ToInt(req.Page),
		cast.ToInt(req.PerPage),
		mysql.WithWhere("task_id", task.Id),
		mysql.WithWhere("level", 1),
	)
	if err != nil {
		return err
	}
	for i := range orgs {
		rsp.Items = append(rsp.Items, &pb.DiscoverGroupTaskOrgsResponseResultUnit{
			CompanyName: orgs[i].CompanyName,
			Percent:     cast.ToString(orgs[i].Percent) + "%",
			Level:       cast.ToString(orgs[i].Level),
			ShouldCapi:  orgs[i].ShouldCapi,
		})
	}
	rsp.Total = total
	rsp.CurrentPage = req.Page
	rsp.PerPage = req.PerPage
	return nil
}

// DiscoverGroupTaskClues 集团资产发现任务线索
func (e *Core) DiscoverGroupTaskClues(_ context.Context, req *pb.DiscoverGroupTaskCluesRequest, rsp *pb.ClueListResponse) error {
	if req.CompanyName == "" {
		return fmt.Errorf("获取任务信息失败")
	}
	task, err := discover.NewGroupTaskModel().First(
		mysql.WithWhere("company_name", req.CompanyName),
		mysql.WithWhere("type", req.Model),
		mysql.WithWhere("percent", req.Percent),
	)
	if err != nil {
		return fmt.Errorf("获取任务信息失败:%s", err.Error())
	}
	clues, total, err := general_clues.NewGeneralCluesModel().ListByDiscoverGroupTask(
		cast.ToInt(req.Page), cast.ToInt(req.PerPage), task.Type, task.Id,
	)
	if err != nil {
		return err
	}
	rspClues := make([]*pb.ClueInfo, 0)
	for i := range clues {
		clInfo := &pb.ClueInfo{
			Id:          clues[i].Id,
			Type:        cast.ToUint32(clues[i].Type),
			CompanyName: clues[i].CompanyName,
			Content:     clues[i].Content,
			Hash:        clues[i].Hash,
			Platform:    clues[i].Platform,
			Source:      clues[i].Source,
			CertValid:   cast.ToUint32(clues[i].CertValid),
			Confirmed:   cast.ToInt32(clues[i].Confirmed),
			UpdatedAt:   clues[i].UpdatedAt.Format("2006-01-02 15:04:05"),
			CreatedAt:   clues[i].CreatedAt.Format("2006-01-02 15:04:05"),
		}
		// 更新ICON地址
		if clInfo.Type == general_clues.ClueTypeIcon {
			clInfo.Content = storage.GenAPIDownloadPath("favicon.ico", clInfo.Content)
		}
		rspClues = append(rspClues, clInfo)
	}
	rsp.CurrentPage = req.Page
	rsp.PerPage = req.PerPage
	rsp.Total = total
	rsp.Items = rspClues
	return nil
}

// DiscoverGroupTaskResult 集团资产发现任务结果
func (e *Core) DiscoverGroupTaskResult(_ context.Context, req *pb.DiscoverGroupTaskResultRequest, rsp *pb.DiscoverTaskResultResponse) error {
	if req.CompanyName == "" {
		return fmt.Errorf("获取任务信息失败")
	}
	clueHashs := make([]string, 0)
	// 获取集团任务信息
	task, err := discover.NewGroupTaskModel().First(
		mysql.WithWhere("company_name", req.CompanyName),
		mysql.WithWhere("type", req.Model),
		mysql.WithWhere("percent", req.Percent),
	)
	if err != nil {
		return fmt.Errorf("获取任务信息失败:%s", err.Error())
	}
	req.Fields = append(req.Fields, "task_id")
	// 获取单个任务列表
	companyTasks, err := discover.NewTaskModel().GetReTaskByGroupTaskId(task.Id, task.Type)
	if err != nil {
		return fmt.Errorf("获取任务信息失败:%s", err.Error())
	}
	// 获取线索HASH
	if len(req.ClueId) != 0 {
		list, _, lErr := general_clues.NewGeneralCluesModel().List(0, 0, mysql.WithValuesIn("id", req.ClueId))
		if lErr != nil {
			return fmt.Errorf("过滤指定线索失败:%s", lErr.Error())
		}
		for i := range list {
			clueHashs = append(clueHashs, cloud_assets.GetClueHash(list[i].Content, list[i].Hash, list[i].Type))
		}
	}
	// 获取对接结果
	rTaskResult := &pb.RecommendResultResponse{}
	if rErr := recommend.GetRecommendResult(context.TODO(), &pb.RecommendResultRequest{
		TaskId: getReTaskIds(companyTasks, req.Org...), Page: req.Page, PerPage: req.PerPage, Fields: req.Fields,
		Level: req.Level, ClueHash: clueHashs, Ip: req.Ip, Port: req.Port, Domain: req.Domain, IsIpv6: req.IsIpv6, IsWildcardDomain: req.IsWildcardDomain,
	}, rTaskResult); rErr != nil {
		return rErr
	}
	// 组装分页信息
	rsp.PerPage = rTaskResult.PerPage
	rsp.CurrentPage = rTaskResult.CurrentPage
	rsp.Total = rTaskResult.Total
	// 获取集团所有公司名称
	companyNames := getCompanyNamesByTasks(companyTasks)
	// 获取集团线索
	clues := hDiscover.GetGroupTaskInstance().GetTaskClueTree(getCompanyTaskIds(companyTasks))
	for i := range rTaskResult.Items {
		tmpAsset := rTaskResult.Items[i]
		asset := &pb.DiscoverAsset{}
		t, _ := json.Marshal(tmpAsset)
		_ = json.Unmarshal(t, asset)
		asset.Clues = nil
		// 获取线索链
		updateAssetInfo(tmpAsset, asset, clues, companyNames)
		rsp.Items = append(rsp.Items, asset)
	}
	return nil
}

func updateAssetInfo(tmpAsset *pb.CloudAsset, asset *pb.DiscoverAsset, clues map[uint64]map[uint64]*pb.DiscoverClue, companyNames map[uint64]string) {
	// 遍历任务列表
	clueUinques := make([]uint64, 0)
	for x := range tmpAsset.TaskId {
		// 任务ID
		rTaskId := tmpAsset.TaskId[x]
		// 处理企业名称
		if !utils.ListContains(asset.CompanyName, companyNames[rTaskId]) {
			asset.CompanyName = utils.ListDistinct(append(asset.CompanyName, companyNames[rTaskId]))
		}
		// 处理线索
		for y := range tmpAsset.Clues {
			// 获取任务线索
			if clue := hDiscover.GetTaskInstance().GetTaskClue(clues[rTaskId], tmpAsset.Clues[y]); clue != nil {
				if !utils.ListContains(clueUinques, clue.Id) {
					clueUinques = append(clueUinques, clue.Id)
					asset.Clues = append(asset.Clues, clue)
				}
			} else {
				log.Infof(fmt.Sprintf("clue:%+v,未取到线索信息,任务ID:%d", tmpAsset.Clues[y], rTaskId))
			}
		}
	}
}

// getCompanyNamesByTasks 获取task的RecommendID
func getReTaskIds(companyTasks []discover.Task, filterCompanyNames ...string) []uint64 {
	ids := make([]uint64, 0)
	for i := range companyTasks {
		if len(filterCompanyNames) != 0 {
			// 忽略指定企业
			if utils.ListContains(filterCompanyNames, companyTasks[i].CompanyName) {
				continue
			}
		}
		ids = append(ids, companyTasks[i].ReTaskId)
	}
	return ids
}

// getCompanyNamesByTasks 获取taskID
func getCompanyTaskIds(companyTasks []discover.Task) [][2]uint64 {
	ids := make([][2]uint64, 0)
	for i := range companyTasks {
		ids = append(ids, [2]uint64{companyTasks[i].Id, companyTasks[i].ReTaskId})
	}
	return ids
}

// getCompanyNamesByTasks 获取task企业名称
func getCompanyNamesByTasks(companyTasks []discover.Task) map[uint64]string {
	companyNames := make(map[uint64]string, 0)
	for i := range companyTasks {
		companyNames[companyTasks[i].ReTaskId] = companyTasks[i].CompanyName
	}
	return companyNames
}
