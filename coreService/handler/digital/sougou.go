package digital

import (
	"context"
	"fmt"
	"strings"
	"time"

	corePb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/official_account"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/antchfx/htmlquery"
	"github.com/antchfx/xpath"
)

func getWechatInfos(ctx context.Context, keyword string) ([]*official_account.OfficialAccount, error) {
	var allAccount []*official_account.OfficialAccount
	total := 3
	for n := 1; n <= total && n <= 10; n++ {
		var url = fmt.Sprintf("https://weixin.sogou.com/weixin?query=%s&_sug_type_=&s_from=input&_sug_=y&type=1&page=%d&ie=utf8", keyword, n)
		crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
			Url:    url,
			Method: crawlerPb.MethodCurlGet,
		}, utils.SetRpcTimeoutOpt(30))
		if err != nil {
			return allAccount, err
		}
		log.WithContextInfof(ctx, "crawler wechat-> keyword: %s, code: %d, body_size: %d, page: %d", keyword, crawlerRes.Code, len(crawlerRes.Body), n)
		doc, err := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
		if err != nil {
			log.WithContextInfof(ctx, "crawler wechat-> keyword:%s 爬虫数据返回为空", keyword)
			return allAccount, err
		}
		// 判断是否遇到验证码
		infoNode := htmlquery.FindOne(doc, "//p[@class=\"p2\"]")
		if infoNode != nil {
			log.WithContextInfof(ctx, "crawler wechat-> keyword:%v", crawlerPb.DecodeBy(crawlerRes.Body))
			yanzhengma := htmlquery.InnerText(infoNode)
			yanzhengmaArr := strings.Split(yanzhengma, "异常访问请求")
			if len(yanzhengmaArr) > 0 {
				log.WithContextInfof(ctx, "crawler wechat，遇到了验证码，返回空数据-> keyword:%s", keyword)
				return allAccount, nil
			}
		}
		list, err := htmlquery.QueryAll(doc, "//ul[@class=\"news-list2\"]/li")
		if err != nil {
			return allAccount, nil
		}
		for _, node := range list {
			var accountString string
			accountNode := htmlquery.FindOne(node, "//div[@class=\"gzh-box2\"]/div[@class=\"txt-box\"]/p[2]")
			if accountNode == nil {
				continue
			}
			accountCraw := htmlquery.InnerText(accountNode)
			if accountCraw != "" {
				account := strings.Split(accountCraw, "微信号：")
				if len(account) != 0 {
					accountString = account[1]
					if accountString != "" {
						accountNewArr := strings.Split(accountString, "\n")
						if len(accountNewArr) != 0 {
							accountString = accountNewArr[0]
						}
					}
				}
			}
			var owner string
			expr, _ := xpath.Compile("count(//dl/dd)")
			v := expr.Evaluate(htmlquery.CreateXPathNavigator(node)).(float64)
			if v > 1 {
				owner = htmlquery.InnerText(htmlquery.FindOne(node, "//dl[2]/dd"))
				if owner != "" {
					if len(strings.Split(owner, "write")) > 1 {
						owner = ""
					}
				}
			}
			if owner != "" {
				owner = strings.Replace(owner, "\n", "", -1)
			}
			title := htmlquery.InnerText(htmlquery.FindOne(node, "//div[@class=\"gzh-box2\"]/div[@class=\"txt-box\"]/p[1]"))
			if title != "" {
				title = strings.Replace(title, "\n", "", -1)
			}
			allAccount = append(allAccount, &official_account.OfficialAccount{
				Name:        title,
				CompanyName: owner,
				Account:     accountString,
				QRCode:      "https://open.weixin.qq.com/qr/code?username=" + accountString,
				Platform:    "wechat",
				IsOnline:    1,
				UpdateTime:  time.Now(),
			})
		}
		htmlBody := htmlquery.InnerText(doc)
		if !strings.Contains(htmlBody, "下一页") && !strings.Contains(htmlBody, "条结果") {
			return allAccount, nil
		} else {
			// 分页查询
			if pageNodes, qErr := htmlquery.QueryAll(doc, "//div[@id=\"pagebar_container\"]/a"); qErr != nil || len(pageNodes) <= 1 {
				return allAccount, nil
			} else {
				total = len(pageNodes) - 1
			}
		}
	}
	return allAccount, nil
}

// formatResponse 格式化返回数据
func formatWechatResponse(officialAccounts []*official_account.OfficialAccount, rsp *corePb.SougouResponse) {
	for _, accountInfo := range officialAccounts {
		rsp.Result = append(rsp.Result, &corePb.Sougou{
			Qrcode:     joinQRCode(accountInfo.Account),
			Title:      accountInfo.Name,
			Owner:      accountInfo.CompanyName,
			Account:    accountInfo.Account,
			UpdateTime: accountInfo.UpdateTime.Format(time.DateTime),
		})
	}
}

func GetSougouWeChat(ctx context.Context, rsp *corePb.SougouResponse, keyword string) error {
	var allAccount []*official_account.OfficialAccount
	// 7天内有查询记录,返回数据库数据
	if _, err := official_account.NewHistoryModel(mysql.GetInstance()).FindByKeyword(keyword, utils.SubDayTime(time.Duration(cfg.LoadCommon().OfficialAccountCacheDay))); err == nil {
		if accountList, getErr := official_account.NewOfficialAccountModel(mysql.GetInstance()).GetByCompanyName(keyword); getErr != nil {
			// 未找到退出
			return getErr
		} else {
			if len(accountList) != 0 {
				// 找到Apps 直接返回
				for i := range accountList {
					allAccount = append(allAccount, &accountList[i])
				}
				formatWechatResponse(allAccount, rsp)
			}
			return nil
		}
	}
	// 获取微信数据
	if accounts, err := getWechatInfos(ctx, keyword); err != nil {
		return err
	} else if len(accounts) != 0 {
		allAccount = append(allAccount, accounts...)
		formatWechatResponse(allAccount, rsp)
		if insertErr := official_account.NewOfficialAccountModel(mysql.GetInstance()).BatchUpdateOrInsert(keyword, allAccount); insertErr != nil {
			return insertErr
		} else {
			official_account.NewHistoryModel(mysql.GetInstance()).UpdateHistory(keyword)
		}
	}
	return nil
}
