package digital

import (
	"fmt"
	"micro-service/pkg/cfg"
	"net/url"
	"strconv"
	"time"
)

const force = 1

func joinQRCode(account string) string {
	return "https://open.weixin.qq.com/qr/code?username=" + account
}

//nolint:unused,gocritic
func baiduPN(page int) string {
	if page == 1 {
		return ""
	}
	return "&pn=" + strconv.Itoa((page-1)*10)
}

//nolint:unused,gocritic
func baiduSearchFormat(keyword string, page int) string {
	keyword = fmt.Sprintf("%s site:weixin.qq.com intitle:%s -filetype:all", keyword, keyword)
	keyword = url.QueryEscape(keyword)
	return fmt.Sprintf("https://www.baidu.com/s?wd=%s%s&ie=utf-8", keyword, baiduPN(page))
}

func cacheIsExpired(t time.Time) bool {
	cacheDay := int(cfg.LoadCommon().DataLeakCacheDay)
	return t.AddDate(0, 0, cacheDay).Before(time.Now())
}
