package digital

import (
	"context"
	"errors"
	"fmt"
	"micro-service/coreService/handler/zerozone"
	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/apps"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"

	"golang.org/x/net/html"

	"github.com/antchfx/htmlquery"
)

// 追加平台参数
func appendParams(appstoreUrl, params string) string {
	if strings.Contains(appstoreUrl, "?") {
		appstoreUrl = strings.Split(appstoreUrl, "?")[0] + "?" + params
	} else {
		appstoreUrl = appstoreUrl + "?" + params
	}
	return appstoreUrl
}

// 获取APP名称
func getAppName(node *html.Node) string {
	return strings.Trim(
		strings.Replace(
			htmlquery.InnerText(
				htmlquery.FindOne(node, "//div[@class=\"we-lockup__copy\"]/div/div[1]/div/div"),
			),
			"\n", "", -1,
		),
		" ")
}

// getCategory 获取APP分类名称
func getCategory(node *html.Node) string {
	return strings.Trim(
		strings.Replace(
			htmlquery.InnerText(
				htmlquery.FindOne(node, "//div[@class=\"we-lockup__copy\"]/div/div[2]"),
			),
			"\n", "", -1,
		),
		" ")
}

// GetApps 获取Apps
func GetApps(ctx context.Context, companyName, url, product string) ([]*apps.Apps, error) {
	var appList []*apps.Apps
	url = appendParams(url, "see-all="+product)
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:      url,
		Method:   crawlerPb.MethodChromeGet,
		WaitTime: 1,
	})
	if err != nil {
		return appList, err
	}
	body := crawlerPb.DecodeBy(crawlerRes.Body)
	if strings.Contains(body, "<title>Connecting to Apple&nbsp;Music</title>") {
		return appList, fmt.Errorf("获取 %s 的 %s 的Apps失败,Error: Connecting to Apple Music", companyName, product)
	}
	doc, err := htmlquery.Parse(strings.NewReader(body))
	if err != nil {
		return appList, err
	}
	list, err := htmlquery.QueryAll(doc, "//div[@class=\"l-row\"]/a")
	if err == nil {
		for _, node := range list {
			var logo string
			logos := strings.Split(htmlquery.SelectAttr(htmlquery.FindOne(node, "div[@class=\"we-lockup__overlay\"]/picture/source"), "srcset"), " ")
			if len(logos) != 0 {
				logo = logos[0]
			}
			appList = append(appList, &apps.Apps{
				Logo:        logo,
				Name:        getAppName(node),
				Category:    getCategory(node),
				Url:         htmlquery.SelectAttr(node, "href"),
				CompanyName: companyName,
				Product:     product,
				Platform:    apps.PlatformApple,
				IsOnline:    1,
				UpdateTime:  time.Now(),
			})
		}
	} else {
		return appList, fmt.Errorf("获取 %s APP失败,Error:%s", companyName, err.Error())
	}
	return appList, nil
}

// formatResponse 格式化返回数据
func formatResponse(apps []*apps.Apps, rsp *pb.DigitalAppstoreResponse) {
	for _, appInfo := range apps {
		updateTimeStr := "" // 默认值
		if !appInfo.UpdateTime.IsZero() {
			// 如果 UpdateTime 不是零值，则进行格式化
			updateTimeStr = appInfo.UpdateTime.Format(utils.DateTimeLayout)
		}

		rsp.Result = append(rsp.Result, &pb.Appstore{
			Id:          appInfo.Id,
			Name:        appInfo.Name,
			CompanyName: appInfo.CompanyName,
			Url:         appInfo.Url,
			Category:    appInfo.Category,
			Logo:        appInfo.Logo,
			Platform:    appInfo.Platform,
			Developer:   appInfo.Developer,
			UpdateTime:  updateTimeStr, // 使用处理过的时间字符串
		})
	}
}

func GetAppstore(ctx context.Context, companyName string, rsp *pb.DigitalAppstoreResponse, isCompany bool) error {
	var allApps []*apps.Apps
	products := []string{"i-phonei-pad-apps", "i-phone-apps", "i-pad-apps", "mac-apps", "watch-apps", "apple-tv-apps"}
	// 获取企业开发者链接
	appstoreUrl, err := getCompanyAppstoreUrl(ctx, companyName)
	if err != nil {
		log.Warnf("GetAppstore: %s", err.Error())
	} else {
		log.WithContextInfof(ctx, "Company: %s , AppstoreUrl: %s", companyName, appstoreUrl)
		// 获取 Apps
		var wg sync.WaitGroup
		wg.Add(6)
		for _, product := range products {
			pt := product
			go func() {
				if appList, appGetErr := GetApps(ctx, companyName, appstoreUrl, pt); appGetErr == nil && len(appList) != 0 {
					allApps = append(allApps, appList...)
				}
				wg.Done()
			}()
		}
		wg.Wait()
	}
	// 如果是公司则抓取零零信安的iOSApp
	if isCompany {
		zeroApps, err := zerozone.FetchZeroZoneIOSApps(ctx, companyName, true)
		if err != nil {
			log.Errorf("FetchZeroZoneIOSApps: %s", err.Error())
		}
		// 去重
		appMap := make(map[string]struct{}, len(allApps))
		for _, app := range allApps {
			appMap[app.Name] = struct{}{}
		}
		for i, app := range zeroApps {
			if _, ok := appMap[app.Name]; !ok {
				allApps = append(allApps, &zeroApps[i])
			}
		}
	}
	formatResponse(allApps, rsp)
	// 更新查询记录
	apps.NewHistoryModel(mysql.GetInstance()).UpdateHistory(companyName)
	return nil
}

// 查找AppStore开发者页面
func getQueryBody(ctx context.Context, searchKeyword string) (string, error) {
	rsp, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    fmt.Sprintf("https://www.google.com/search?q=%s", url.QueryEscape(searchKeyword)),
		Method: crawlerPb.MethodChromeGet,
		Vpn:    1,
	})
	if err != nil {
		return "", err
	}
	// 解析body
	body := crawlerPb.DecodeBy(rsp.Body)
	doc, err := htmlquery.Parse(strings.NewReader(body))
	if err != nil {
		return "", err
	}
	// 查找appstore
	node := htmlquery.FindOne(doc, "/html/body/div[2]/div/div/div/div/div[1]/a")
	if node == nil {
		node = htmlquery.FindOne(doc, "//*[@id=\"main\"]/div[4]/div/div[1]/a")
	}
	if node == nil {
		log.Infof(fmt.Sprintf("APPStore Query:%s\n >>>>>\n\n\n %s \n\n\n\n <<<<<<", searchKeyword, body))
		return "", errors.New("未找到AppStore的ID地址")
	}
	return htmlquery.SelectAttr(node, "href"), nil
}

// 根据企业名称 获取AppStore开发者页面
func getCompanyAppstoreUrl(ctx context.Context, companyName string) (string, error) {
	var appstoreUrl string
	if appid := apps.NewAppStoreIdModel(mysql.GetInstance()).FindByCompanyName(companyName); appid != nil {
		return fmt.Sprintf("https://apps.apple.com/cn/developer/%s/%s?l=en&see-all=i-phone-apps", companyName, appid.StoreId), nil
	}
	appstoreUrl, err := getQueryBody(ctx, fmt.Sprintf(`"%s" "开发的App" site:(apps.apple.com)`, companyName))
	if err != nil {
		appstoreUrl, _ = getQueryBody(ctx, fmt.Sprintf(`"%sApps on the App Store"  site:(apps.apple.com)`, companyName))
	}
	if appstoreUrl == "" {
		return appstoreUrl, errors.New("未找到Appstore地址")
	} else {
		// 去除无效链接信息
		appstoreUrl = strings.Replace(appstoreUrl, "/url?q=", "", -1)
		if strings.Contains(appstoreUrl, "&") {
			appstoreUrl = strings.Split(appstoreUrl, "&")[0]
		}
		// 提取appid 入库
		if appId := getUrlId(appstoreUrl); appId != "" {
			apps.NewAppStoreIdModel(mysql.GetInstance()).Create(companyName, appId)
		}
	}
	return appstoreUrl, nil
}

// 提取URL中的开发者ID
func getUrlId(url string) string {
	return regexp.MustCompile(`id\d+`).FindString(url)
}
