package digital

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/coreService/handler/zerozone"
	"strings"
	"time"

	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/apps"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

type androidApp struct{}

// 腾讯应用宝应用市场

func AndroidTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := apps.NewHistoryModel().First(mysql.WithColumnValue("`id`", taskId))
	if err != nil {
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Progress = info.Progress
	rsp.Keywords = []string{info.Keyword}
	return nil
}

func AndroidTaskResult(taskId uint64, rsp *pb.DigitalAppstoreResponse) error {
	err := AndroidTaskInfo(taskId, &pb.GitHubTaskInfoResponse{})
	if err != nil {
		return err
	}

	list, err := apps.NewAppsModel().ListAllByHistory(taskId)
	if err != nil {
		return err
	}
	formatResponse(list, rsp)
	return nil
}

func AndroidTaskFetchApps(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keyword := utils.ListFirstNonZero(req.Keyword)
	if keyword == "" {
		return errors.New("查询关键字不可为空")
	}

	history, err := apps.NewHistoryModel().First(mysql.WithColumnValue("`keyword`", keyword))
	newTask := false
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		newTask = true
	case err != nil:
		return err
	}

	history.Keyword = keyword
	if newTask || req.Force == force || cacheIsExpired(history.UpdatedAt) || history.Progress == 0 {
		history.Progress = 1.00
		err = apps.NewHistoryModel().Upsert(&history)
		if err != nil {
			return err
		}

		ta := androidApp{}
		go ta.asyncFetchAndMergeAndroidApps(ctx, &history, newTask, req.IsCompany)
	}

	rsp.TaskId = history.Id
	return nil
}

func (ta androidApp) asyncFetchAndMergeAndroidApps(ctx context.Context, item *apps.History, newKeyword bool, isCompany bool) {
	err := ta.fetchAndStoreAndroidApps(ctx, item, newKeyword, isCompany)
	if err != nil {
		log.WithContextErrorf(ctx, "[Android-App] %v", err)
	}

	item.Progress = 100.00
	err = apps.NewHistoryModel().Upsert(item)
	if err != nil {
		log.WithContextErrorf(ctx, "[Android-App] save history failure: %v", err)
	}
}

func (ta androidApp) fetchAndStoreAndroidApps(ctx context.Context, item *apps.History, newKeyword bool, isCompany bool) error {
	fetchList, err := ta.fetchTencentApps(ctx, item.Keyword, true)
	var zeroList []apps.Apps
	if isCompany {
		zeroList, err = zerozone.FetchZeroZoneApps(ctx, item.Keyword, true)
	}
	// 根据名称去重，融合应用宝和零零信安数据
	var nameMap = make(map[string]struct{}, len(fetchList))
	for i := range fetchList {
		nameMap[fetchList[i].Name] = struct{}{}
	}
	for i := range zeroList {
		if _, ok := nameMap[zeroList[i].Name]; ok {
			continue
		}
		fetchList = append(fetchList, zeroList[i])
	}

	if len(fetchList) == 0 {
		return err
	}

	appOp := apps.NewAppsModel()
	var companies = make([]string, 0, len(fetchList))
	for i := range fetchList {
		companies = append(companies, fetchList[i].CompanyName)
	}
	appsList, err := appOp.ListAll(
		mysql.WithColumnValue("`platform`", apps.PlatformAndroid),
		mysql.WithValuesIn("`company_name`", utils.ListDistinctNonZero(companies)))
	if err != nil {
		return err
	}

	for i := range fetchList {
		for j := range appsList {
			if fetchList[i].CompanyName == appsList[j].CompanyName && fetchList[i].Name == appsList[j].Name {
				fetchList[i].Id = appsList[j].Id
				fetchList[i].CreatedAt = appsList[j].CreatedAt
				break
			}
		}
	}
	err = appOp.AppsUpsert(fetchList)
	if err != nil {
		return err
	}

	// 获取任务当前的app关联信息
	var historyList []*apps.Relation
	if !newKeyword {
		historyList, err = appOp.RelationList(mysql.WithColumnValue("`history_id`", item.Id))
		if err != nil {
			return err
		}
	}

	// 建立新的关联关系
	appIdMap := make(map[uint64]struct{}, len(historyList))
	for i := range historyList {
		appIdMap[historyList[i].AppId] = struct{}{}
	}
	var insertRelation []apps.Relation
	for i := range fetchList {
		if _, ok := appIdMap[fetchList[i].Id]; !ok {
			insertRelation = append(insertRelation, apps.Relation{HistoryId: item.Id, AppId: fetchList[i].Id})
		}
	}
	err = appOp.RelationUpsert(insertRelation)
	return err
}

func (androidApp) yybReqBody(page, size int, keyword string) string {
	return fmt.Sprintf(`{
    "head": {
        "cmd": "dynamicard_yybhome",
        "authInfo": {
            "businessId": "AuthName"
        },
        "deviceInfo": {
            "platform": 2
        },
        "userInfo": {
            "guid": "3620280c-051b-4c35-b37b-18c843af7eb9"
        },
        "expSceneIds": "",
        "hostAppInfo": {
            "scene": "search_result"
        }
    },
    "body": {
        "bid": "yybhome",
        "offset": %d,
        "size": %d,
        "preview": false,
        "listS": {
            "region": {
                "repStr": [
                    "CN"
                ]
            },
            "keyword": {
                "repStr": ["%s"]
            }
        }
    }
}`, page, size, keyword)
}

func (androidApp) tencentYYBUrl() string {
	return "https://yybadaccess.3g.qq.com/v2/dynamicard_yybhome"
}

type yybRspBody struct {
	Data struct {
		Components []struct {
			CardId string `json:"cardId"`
			Data   struct {
				Name     string       `json:"name"`
				ItemData []yybAppInfo `json:"itemData"`
			} `json:"data"`
		} `json:"components"`
	} `json:"data"`
}

type yybAppInfo struct {
	PkgName       string `json:"pkg_name"`       // 包签名
	AppId         string `json:"app_id"`         // appID
	Name          string `json:"name"`           // 应用名称
	Icon          string `json:"icon"`           // icon地址
	Operator      string `json:"operator"`       // company
	Developer     string `json:"developer"`      // 开发者
	VersionName   string `json:"version_name"`   // 版本
	AverageRating string `json:"average_rating"` // 评分
	Tags          string `json:"tags"`           // 应用标签
	TagsAlias     string `json:"tag_alias"`      // 应用标签(中文)
	EditorIntro   string `json:"editor_intro"`   // 简介
	DownloadUrl   string `json:"download_url"`   // 下载地址
}

func (ta androidApp) fetchTencentApps(ctx context.Context, keyword string, isFilter bool) ([]apps.Apps, error) {
	param := &crawlerPb.GetRequest{
		Method: crawlerPb.MethodCurlPost,
		Url:    ta.tencentYYBUrl(),
		Body:   ta.yybReqBody(0, 150, keyword),
	}
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return nil, err
	}

	body := crawlerPb.DecodeBy(crawlerRes.Body)
	var result yybRspBody
	_ = json.Unmarshal([]byte(body), &result)
	if len(result.Data.Components) == 0 {
		return nil, nil
	}

	l := result.Data.Components[0].Data.ItemData
	appItems := make([]apps.Apps, 0, len(l))
	for i := range l {
		if isFilter {
			isContains := utils.ListStrContains([]string{l[i].Name, l[i].Operator}, keyword)
			if !isContains {
				continue
			}
		}

		bs, _ := json.Marshal(l[i])
		appItems = append(appItems, apps.Apps{
			Name:          l[i].Name,
			Logo:          l[i].Icon,
			Url:           l[i].DownloadUrl,
			Category:      strings.Join(utils.ListDistinctNonZero(strings.Split(l[i].Tags, ",")), ","),
			CompanyName:   l[i].Operator,
			Platform:      apps.PlatformAndroid,
			Developer:     l[i].Developer,
			Product:       "yyb",
			YybOriginInfo: string(bs),
			Area:          "",
			IsOnline:      1,
			UpdateTime:    time.Now(),
		})
	}
	return appItems, nil
}
