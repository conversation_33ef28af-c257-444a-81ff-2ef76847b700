package digital

import (
	"context"
	"errors"
	"micro-service/coreService/handler/zerozone"
	"time"

	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	mysql2 "micro-service/middleware/mysql"
	"micro-service/middleware/mysql/zero_zone_miniapp"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

func MiniappTaskInfo(id uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := zero_zone_miniapp.NewHistoryModel().First(mysql2.WithColumnValue("`id`", id))
	if err != nil {
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = []string{info.Keyword}
	rsp.Progress = info.Progress
	return nil
}

func MiniappTaskResult(req *pb.GitHubCodeRequest, rsp *pb.MiniappAssets) error {
	l, _, err := zero_zone_miniapp.NewOfficialAccountModel().
		ListAccounts(0, 0, mysql2.WithColumnValue("`mini_app_history_id`", req.TaskId))
	if len(l) == 0 {
		return err
	}

	//		uint64 id = 1;
	//		string name = 2;
	//		string wechat_id = 3;
	//		string app_id = 4;
	//		string company_name = 5;
	//		string introduction = 6;
	//		string icp = 7;
	//		string iconUrl = 8;

	for i := range l {
		updateTimeStr := "" // 默认值
		if !l[i].UpdateTime.IsZero() {
			// 如果 UpdateTime 不是零值，则进行格式化
			updateTimeStr = l[i].UpdateTime.Format(time.DateTime)
		}

		rsp.Items = append(rsp.Items, &pb.MiniappAssetsMiniapp{
			Id:           l[i].Id,
			Name:         l[i].Name,
			WechatId:     l[i].WechatId,
			AppId:        l[i].AppId,
			CompanyName:  l[i].CompanyName,
			PlatName:     l[i].Platform,
			Icp:          l[i].Icp,
			IconUrl:      l[i].IconUrl,
			Introduction: l[i].Introduction,
			UpdateTime:   updateTimeStr, // 使用处理过的时间字符串
		})
	}
	return nil
}

func MiniappTaskUpsert(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keyword := utils.ListFirstNonZero(req.Keyword)
	if keyword == "" {
		return errors.New("查询关键字不能为空")
	}

	historyDb := zero_zone_miniapp.NewHistoryModel()
	item, err := historyDb.First(mysql2.WithColumnValue("`keyword`", keyword))
	isAdd := false
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		isAdd = true
	case err != nil:
		return err
	}

	// case 1: keyword not exist
	if isAdd {
		item.Keyword = keyword
		item.Progress = 1
		err = historyDb.Create(&item)
		if err != nil {
			return err
		}
		// async get task result
		go asyncFetchDigitalMiniappAssets(ctx, item.Id, item.Keyword)
		rsp.TaskId = item.Id
		return nil
	}

	// case 2: keyword exist, but force update or keyword last updated_at > cached day or old data
	expired := time.Now().After(item.UpdatedAt.AddDate(0, 0, int(cfg.LoadCommon().OfficialAccountCacheDay)))
	if req.Force == force || expired || item.Progress == 0 {
		err = historyDb.UpdateAny(item.Id, map[string]any{"progress": 1})
		if err != nil {
			return err
		}
		// async get task result
		go asyncFetchDigitalMiniappAssets(ctx, item.Id, item.Keyword)
	}
	rsp.TaskId = item.Id
	return nil
}

func asyncFetchDigitalMiniappAssets(ctx context.Context, taskId uint64, keyword string) {
	accounts, err := fetchMiniappAssets(ctx, keyword)
	if err != nil {
		log.Errorf("[Core] DigitalAssets: FetchDigitalAssets keyword->%s failed: %v", keyword, err)
	}

	db := zero_zone_miniapp.NewOfficialAccountModel()
	if err = db.Upsert(accounts); err != nil {
		log.Errorf("[Core] DigitalAssets: Upsert keyword->%s records failed: %v", keyword, err)
	}

	relation, _, err := db.ListRelation(0, 0, mysql2.WithColumnValue("`mini_app_history_id`", taskId))
	if err != nil {
		log.Errorf("[Core] DigitalAssets: Get keyword->%s relation failed: %v", keyword, err)
	}
	if len(accounts) > 0 && err == nil {
		var insertList = make([]zero_zone_miniapp.Relation, 0, len(accounts))
		for i := range accounts {
			exist := false
			for j := range relation {
				if accounts[i].Id == relation[j].OfficialAccountId {
					exist = true
					break
				}
			}
			if !exist {
				insertList = append(insertList, zero_zone_miniapp.Relation{
					OfficialAccountHistoryId: taskId,
					OfficialAccountId:        accounts[i].Id,
				})
			}
		}
		if err = db.SaveRelation(insertList...); err != nil {
			log.Errorf("[Core] DigitalAssets: Create keyword->%s relation failed: %v", keyword, err)
		}
	}
	err = zero_zone_miniapp.NewHistoryModel().UpdateAny(taskId, map[string]any{"progress": 100})
	if err != nil {
		log.Errorf("[Core] DigitalAssets: Update keyword->%s progress failed: %v", keyword, err)
	}
}

func fetchMiniappAssets(ctx context.Context, keyword string) ([]*zero_zone_miniapp.OfficialAccount, error) {
	// 00信安源
	zeroList, err := fetchMiniappByZero(ctx, keyword)
	if err != nil {
		log.WithContextErrorf(ctx, "[Digital Assets] fetch digital assets of Miniapp from tianyancha failed: %v", err)
	}
	result := make([]*zero_zone_miniapp.OfficialAccount, 0, len(zeroList))
	for i := range zeroList {
		result = append(result, &zeroList[i])
	}
	return result, nil
}

// 00信安-微信小程序
func fetchMiniappByZero(ctx context.Context, keyword string) ([]zero_zone_miniapp.OfficialAccount, error) {
	l, err := zerozone.MiniAppAccount(ctx, keyword)
	if err != nil {
		return nil, err
	}

	var accounts = make([]zero_zone_miniapp.OfficialAccount, 0, len(l))
	for i := range l {
		accounts = append(accounts, zero_zone_miniapp.OfficialAccount{
			Name:         l[i].Name,
			AppId:        l[i].AppId,
			WechatId:     l[i].WechatId,
			CompanyName:  l[i].CompanyName,
			Platform:     zero_zone_miniapp.PlatformZeroZone,
			Icp:          l[i].Icp,
			Introduction: l[i].Introduction,
			IconUrl:      l[i].IconUrl,
			UpdateTime:   l[i].UpdateTime,
		})
	}
	return accounts, nil
}
