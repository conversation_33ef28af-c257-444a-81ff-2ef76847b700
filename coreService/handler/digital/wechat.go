package digital

import (
	"context"
	"errors"
	"micro-service/coreService/handler/zerozone"
	"strconv"
	"time"

	"gorm.io/gorm"

	"micro-service/coreService/handler/tianyancha"
	pb "micro-service/coreService/proto"
	mysql2 "micro-service/middleware/mysql"
	"micro-service/middleware/mysql/official_account"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

func WechatTaskInfo(id uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := official_account.NewHistoryModel().First(mysql2.WithColumnValue("`id`", id))
	if err != nil {
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = []string{info.Keyword}
	rsp.Progress = info.Progress
	return nil
}

func WechatTaskResult(req *pb.GitHubCodeRequest, rsp *pb.DigitalWechatAccounts) error {
	l, _, err := official_account.NewOfficialAccountModel().
		ListAccounts(0, 0, mysql2.WithColumnValue("`official_account_history_id`", req.TaskId))
	if len(l) == 0 {
		return err
	}

	for i := range l {
		updateTimeStr := "" // 默认值
		if !l[i].UpdateTime.IsZero() {
			// 如果 UpdateTime 不是零值，则进行格式化
			updateTimeStr = l[i].UpdateTime.Format(time.DateTime)
		}
		rsp.Items = append(rsp.Items, &pb.DigitalWechatAccountsAccount{
			Id:          l[i].Id,
			Name:        l[i].Name,
			Account:     l[i].Account,
			QrCode:      l[i].QRCode,
			CompanyName: l[i].CompanyName,
			Platform:    l[i].Platform,
			IsOnline:    strconv.Itoa(l[i].IsOnline),
			Describe:    l[i].Describe,
			UpdateTime:  updateTimeStr, // 使用处理过的时间字符串
		})
	}
	return nil
}

func WechatTaskUpsert(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keyword := utils.ListFirstNonZero(req.Keyword)
	if keyword == "" {
		return errors.New("查询关键字不能为空")
	}

	historyDb := official_account.NewHistoryModel()
	item, err := historyDb.First(mysql2.WithColumnValue("`keyword`", keyword))
	isAdd := false
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		isAdd = true
	case err != nil:
		return err
	}

	// case 1: keyword not exist
	if isAdd {
		item.Keyword = keyword
		item.Progress = 1
		err = historyDb.Create(&item)
		if err != nil {
			return err
		}
		// async get task result
		go asyncFetchDigitalWechatAssets(ctx, item.Id, item.Keyword)
		rsp.TaskId = item.Id
		return nil
	}

	// case 2: keyword exist, but force update or keyword last updated_at > cached day or old data
	expired := time.Now().After(item.UpdatedAt.AddDate(0, 0, int(cfg.LoadCommon().OfficialAccountCacheDay)))
	if req.Force == force || expired || item.Progress == 0 {
		err = historyDb.UpdateAny(item.Id, map[string]any{"progress": 1})
		if err != nil {
			return err
		}
		// async get task result
		go asyncFetchDigitalWechatAssets(ctx, item.Id, item.Keyword)
	}
	rsp.TaskId = item.Id
	return nil
}

func asyncFetchDigitalWechatAssets(ctx context.Context, taskId uint64, keyword string) {
	accounts, err := fetchDigitalAssets(ctx, keyword)
	if err != nil {
		log.Errorf("[Core] DigitalAssets: FetchDigitalAssets keyword->%s failed: %v", keyword, err)
	}

	db := official_account.NewOfficialAccountModel()
	if err = db.Upsert(accounts); err != nil {
		log.Errorf("[Core] DigitalAssets: Upsert keyword->%s records failed: %v", keyword, err)
	}

	relation, _, err := db.ListRelation(0, 0, mysql2.WithColumnValue("`official_account_history_id`", taskId))
	if err != nil {
		log.Errorf("[Core] DigitalAssets: Get keyword->%s relation failed: %v", keyword, err)
	}
	if len(accounts) > 0 && err == nil {
		var insertList = make([]official_account.Relation, 0, len(accounts))
		for i := range accounts {
			exist := false
			for j := range relation {
				if accounts[i].Id == relation[j].OfficialAccountId {
					exist = true
					break
				}
			}
			if !exist {
				insertList = append(insertList, official_account.Relation{
					OfficialAccountHistoryId: taskId,
					OfficialAccountId:        accounts[i].Id,
				})
			}
		}
		if err = db.SaveRelation(insertList...); err != nil {
			log.Errorf("[Core] DigitalAssets: Create keyword->%s relation failed: %v", keyword, err)
		}
	}
	err = official_account.NewHistoryModel().UpdateAny(taskId, map[string]any{"progress": 100})
	if err != nil {
		log.Errorf("[Core] DigitalAssets: Update keyword->%s progress failed: %v", keyword, err)
	}
}

func fetchDigitalAssets(ctx context.Context, keyword string) ([]*official_account.OfficialAccount, error) {
	// 00信安源
	zeroList, err := fetchWeChatByZero(ctx, keyword)

	// 天眼查源
	tianyanchaList, err := fetchWeChatByTianyancha(ctx, keyword)
	if err != nil {
		log.WithContextErrorf(ctx, "[Digital Assets] fetch digital assets of WeChat from tianyancha failed: %v", err)
	}

	result := make([]*official_account.OfficialAccount, 0, len(tianyanchaList)+len(zeroList))

	for i := range zeroList {
		result = append(result, &zeroList[i])
	}

	for i := range tianyanchaList {
		exist := false
		for j := range result {
			if tianyanchaList[i].Account == result[j].Account {
				exist = true
				break
			}
		}
		if !exist {
			result = append(result, &tianyanchaList[i])
		}
	}

	return result, nil
}

// 天眼查-微信公众号
func fetchWeChatByTianyancha(ctx context.Context, keyword string) ([]official_account.OfficialAccount, error) {
	l, err := tianyancha.WeChatAccount(ctx, keyword)
	if err != nil {
		return nil, err
	}

	var accounts = make([]official_account.OfficialAccount, 0, len(l))
	for i := range l {
		accounts = append(accounts, official_account.OfficialAccount{
			Name:        l[i].Title,
			Account:     l[i].PublicNum,
			QRCode:      l[i].CodeImg,
			CompanyName: keyword,
			Platform:    official_account.PlatformTianyancha,
			IsOnline:    official_account.Online,
			Describe:    l[i].Recommend,
			UpdateTime:  time.Now(),
		})
	}
	return accounts, nil
}

// 00信安-微信公众号
func fetchWeChatByZero(ctx context.Context, keyword string) ([]official_account.OfficialAccount, error) {
	l, err := zerozone.WeChatAccount(ctx, keyword)
	if err != nil {
		return nil, err
	}

	var accounts = make([]official_account.OfficialAccount, 0, len(l))
	for i := range l {
		accounts = append(accounts, official_account.OfficialAccount{
			Name:        l[i].Title,
			Account:     l[i].PublicNum,
			QRCode:      l[i].CodeImg,
			CompanyName: keyword,
			Platform:    official_account.PlatformZeroZone,
			IsOnline:    official_account.Online,
			Describe:    l[i].Recommend,
			UpdateTime:  l[i].UpdateTime,
		})
	}
	return accounts, nil
}
