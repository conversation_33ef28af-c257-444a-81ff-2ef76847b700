package digital

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/antchfx/htmlquery"
	"github.com/sourcegraph/conc/pool"
	"golang.org/x/net/html"

	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/middleware/mysql/official_account"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

//nolint:unused,gocritic
func fetchWeChatByBaiduSearch(ctx context.Context, keyword string) ([]official_account.OfficialAccount, error) {
	if keyword == "" {
		return nil, errors.New("keyword is empty")
	}

	var index = 1
	var allUrls = make([]string, 0, 100)
	for index <= 10 {
		urls, err := parseBaiduSearchResult(ctx, keyword, index)
		if err != nil || len(urls) == 0 {
			if err != nil {
				log.Error(err)
			}
			break
		}
		time.Sleep(2 * time.Second)
		allUrls = append(allUrls, urls...)
		index++
	}

	allUrls = utils.ListDistinctNonZero(allUrls)
	if len(allUrls) == 0 {
		return nil, nil
	}

	ch := make(chan official_account.OfficialAccount, len(allUrls)/50)
	go func() {
		p := pool.New().WithMaxGoroutines(utils.Page(len(allUrls), 20))
		for _, v := range allUrls {
			wechatUrl := v
			p.Go(func() {
				item, err := parseBaiduWechatInfo(ctx, wechatUrl)
				if err != nil {
					log.Error(err)
					return
				}
				ch <- item
			})
		}
		p.Wait()
		close(ch)
	}()

	var wechatAccounts = make([]official_account.OfficialAccount, 0, len(allUrls))
	for x := range ch {
		wechatAccounts = append(wechatAccounts, x)
	}
	wechatAccounts = distinctBaiduWechat(keyword, wechatAccounts)
	return wechatAccounts, nil
}

//nolint:unused,gocritic
func parseBaiduSearchResult(ctx context.Context, keyword string, page int) ([]string, error) {
	crawlerRes, err := crawlerPb.GetProtoClient().Get(ctx, &crawlerPb.GetRequest{
		Url:    baiduSearchFormat(keyword, page),
		Method: crawlerPb.MethodCurlGet,
		Vpn:    0,
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return nil, err
	}

	body := crawlerPb.DecodeBy(crawlerRes.Body)
	top, err := htmlquery.Parse(strings.NewReader(body))
	if err != nil {
		return nil, err
	}

	timeout := htmlquery.FindOne(top, "//div/div[@class='timeout-title']")
	if timeout != nil {
		return nil, fmt.Errorf("[Baidu] Search keyword: %s for Wechat account, page: %d: %s", keyword, page, innerText(timeout))
	}

	items := htmlquery.Find(top, `//div[@id="content_left"]/div[@class='result c-container xpath-log new-pmd']`)
	urlList := make([]string, 0, len(items))
	for _, v := range items {
		mu := htmlquery.SelectAttr(v, "mu")
		urlList = append(urlList, mu)
	}
	return urlList, nil
}

//nolint:unused,gocritic
func parseBaiduWechatInfo(ctx context.Context, s string) (info official_account.OfficialAccount, err error) {
	crawlerRes, err := crawlerPb.GetProtoClient().Get(
		ctx,
		&crawlerPb.GetRequest{Url: s, Method: crawlerPb.MethodChromeGet},
		utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return
	}

	top, err := htmlquery.Parse(strings.NewReader(crawlerPb.DecodeBy(crawlerRes.Body)))
	if err != nil {
		return
	}

	result := htmlquery.FindOne(top, `//div[contains(@class, "profile_inner")]`)
	if result == nil {
		return info, fmt.Errorf("request %s, but not found wechat account", s)
	}

	nickname := htmlquery.FindOne(result, "//strong[@class='profile_nickname']")
	info.Name = innerText(nickname)
	metaList := htmlquery.Find(result, "//p[@class='profile_meta']")
	for i, v := range metaList {
		x := htmlquery.FindOne(v, "//span[@class='profile_meta_value']")
		switch i {
		case 0:
			info.Account = innerText(x)
		case 1:
			info.Describe = innerText(x)
		}
	}
	info.QRCode = joinQRCode(info.Account)
	info.Platform = official_account.PlatformBaidu
	info.IsOnline = official_account.Online
	info.UpdateTime = time.Now()
	return info, nil
}

//nolint:unused,gocritic
func innerText(node *html.Node) string {
	if node == nil {
		return ""
	}
	return htmlquery.InnerText(node)
}

//nolint:unused,gocritic
func distinctBaiduWechat(keyword string, l []official_account.OfficialAccount) []official_account.OfficialAccount {
	var m = make(map[string]*official_account.OfficialAccount, len(l))
	for i := range l {
		if strings.Contains(l[i].Name, keyword) {
			m[l[i].Account] = &l[i]
		}
	}
	var result = make([]official_account.OfficialAccount, 0, len(m))
	for _, v := range m {
		result = append(result, *v)
	}
	return result
}
