package digital

import (
	"context"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"testing"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
)

func initCfg() {
	_ = godotenv.Load("../../../.env")

	cfg.InitLoadCfg()
	mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

func Test_GetAppstore(t *testing.T) {
	initCfg()

	rsp := &pb.DigitalAppstoreResponse{}
	err := GetAppstore(context.Background(), "中国农业银行股份有限公司", rsp, true)
	assert.Nil(t, err)
}
