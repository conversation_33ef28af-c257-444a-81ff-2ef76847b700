package digital

import (
	"context"
	"fmt"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"

	crawlerPb "micro-service/crawlerService/proto"
	"micro-service/pkg/utils"
)

func Test_GetBaidu(t *testing.T) {
	rsp, err := crawlerPb.GetProtoClient().Get(context.TODO(), &crawlerPb.GetRequest{
		Url:    baiduSearchFormat("广发银行", 2),
		Method: crawlerPb.MethodChromeGet,
	}, utils.SetRpcTimeoutOpt(30))

	assert.Nil(t, err)
	fmt.Println(crawlerPb.DecodeBy(rsp.Body))
}

func Test_BaiduSearchWechat(t *testing.T) {
	_, err := parseBaiduWechatInfo(context.TODO(),
		"http://mp.weixin.qq.com/s?__biz=MzA5MjgyMzUyOQ==&amp;mid=2247524911&amp;idx=3&amp;sn=71fd911a8d3e57661da2796dce3ab2d9&amp;chksm=906568a9a712e1bf3fe8db1a1e8fa8eb7af675c1ebd4eeefe45d90fb5ccc3748725488a090e8#rd",
	)
	assert.Nil(t, err)
}

func Test_BaiduSearch(t *testing.T) {
	urls, err := parseBaiduSearchResult(context.TODO(), "银行", 1)
	assert.Nil(t, err)
	wg := sync.WaitGroup{}
	for _, v := range urls {
		wg.Add(1)
		go func(s string) {
			info, _ := parseBaiduWechatInfo(context.TODO(), s)
			fmt.Println(info)
		}(v)
	}
	wg.Wait()
}

func Test_baiduSearchWechat(t *testing.T) {
	l, err := fetchWeChatByBaiduSearch(context.TODO(), "广发银行")
	assert.Nil(t, err)
	for _, v := range l {
		fmt.Println(v)
	}
}
