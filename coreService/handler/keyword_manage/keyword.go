package keyword_manage

import (
	"fmt"
	"path"
	"path/filepath"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql/apps"
	"micro-service/middleware/mysql/mini_app"
	"micro-service/middleware/mysql/official_account"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
)

func KeywordDataSync(req *pb.KeywordManageDataSyncRequest, rsp *pb.KeywordManageDataSyncResponse) error {
	name := req.GetName()
	var err error

	// data from apps
	rsp.App, err = appsSync(name)
	if err != nil {
		return fmt.Errorf("get app data failed: %w", err)
	}

	// data from WeChat (微信)
	rsp.Wechat, err = wechatSync(name)
	if err != nil {
		return fmt.Errorf("get wechat data failed: %w", err)
	}

	// data from mini-app (小程序)
	rsp.Xiaochengxu, err = miniAppSync(name)
	if err != nil {
		return fmt.Errorf("get xiaochengxu data failed: %w", err)
	}

	return nil
}

func wechatSync(name string) ([]*pb.KeywordManageDataSyncWechat, error) {
	list, err := official_account.NewOfficialAccountModel().ListAll(official_account.WithKeyword(name))
	if err != nil {
		return nil, err
	}

	var data = make([]*pb.KeywordManageDataSyncWechat, 0, len(list))
	for _, item := range list {
		data = append(data, &pb.KeywordManageDataSyncWechat{
			Id:          int64(item.Id),
			CreatedAt:   item.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   item.UpdatedAt.Format(utils.DateTimeLayout),
			Account:     item.Account,
			Name:        item.Name,
			Qrcode:      item.QRCode,
			CompanyName: item.CompanyName,
			Platform:    item.Platform,
			IsOnline:    int64(item.IsOnline),
			Describe:    item.Describe,
		})
	}
	return data, nil
}

func appsSync(name string) ([]*pb.KeywordManageDataSyncApp, error) {
	list, err := apps.NewAppsModel().ListAll(apps.WithKeyword(name, "`name`", "`company_name`"))
	if err != nil {
		return nil, err
	}

	var data = make([]*pb.KeywordManageDataSyncApp, 0, len(list))
	for _, item := range list {
		data = append(data, &pb.KeywordManageDataSyncApp{
			Id:          int64(item.Id),
			CreatedAt:   item.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   item.UpdatedAt.Format(utils.DateTimeLayout),
			Name:        item.Name,
			Logo:        item.Logo,
			Category:    item.Category,
			CompanyName: item.CompanyName,
			Platform:    item.Platform,
			Product:     item.Product,
			Url:         item.Url,
			Area:        item.Area,
			Developer:   item.Developer,
			IsOnline:    int64(item.IsOnline),
		})
	}
	return data, nil
}

func miniAppSync(name string) ([]*pb.KeywordManageDataSyncMiniAPP, error) {
	list, err := mini_app.NewMiniApp().ListAll(mini_app.WithKeyword(name))
	if err != nil {
		return nil, err
	}

	var data = make([]*pb.KeywordManageDataSyncMiniAPP, 0, len(list))
	for _, item := range list {
		data = append(data, &pb.KeywordManageDataSyncMiniAPP{
			Id:          int64(item.Id),
			CreatedAt:   item.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   item.UpdatedAt.Format(utils.DateTimeLayout),
			Name:        item.Name,
			CompanyName: item.CompanyName,
			Logo:        encryptFilePath(item.Logo),    // 地址
			Erweima:     encryptFilePath(item.Erweima), // 地址
			Platform:    int64(item.Platform),
			OriginId:    item.OriginId,
			Account:     item.Account,
			IsOnline:    int64(item.IsOnline),
			Description: item.Description,
		})
	}
	return data, nil
}

func encryptFilePath(filePath string) string {
	if filePath == "" {
		return ""
	}
	name, _ := utils.GetFileName(filePath)
	fp, _ := utils.DownloadFileEncrypt(filePath, name, "", false)
	return filepath.Join(storage.GetDownloadPrefix(), fp) + path.Ext(filePath)
}
