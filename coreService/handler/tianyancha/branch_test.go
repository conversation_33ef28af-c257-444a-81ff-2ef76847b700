package tianyancha

import (
	"context"
	"fmt"
	"github.com/joho/godotenv"
	"micro-service/pkg/log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	pb "micro-service/coreService/proto"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
)

func initCfg(initRedis, initMysql bool) {
	cfg.InitLoadCfg()
	if initRedis {
		redis.GetInstance(cfg.LoadRedis())
	}
	if initMysql {
		mysql.GetInstance(cfg.LoadMysql())
	}

}

func Test_Branch(t *testing.T) {
	godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	var rsp = &pb.QCCBranchResponse{}
	err := Branch(context.TODO(), &pb.QCCBranchRequest{
		Keyword:  "阿里巴巴（中国）有限公司",
		ClientId: "",
	}, rsp)
	assert.Nil(t, err)
	time.Sleep(time.Second)
	fmt.Println(rsp)
}

func Test_BranchOrigin(t *testing.T) {
	godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	data, err := BranchOrigin(context.TODO(), &pb.QCCBranchRequest{
		Keyword:  "北京百度网讯科技有限公司",
		ClientId: "",
	})
	assert.Nil(t, err)
	time.Sleep(time.Second)
	fmt.Println(data)
}
