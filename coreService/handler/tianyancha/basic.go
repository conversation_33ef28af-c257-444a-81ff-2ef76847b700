package tianyancha

import (
	"errors"
	"net/url"
	"time"

	"github.com/go-resty/resty/v2"

	"micro-service/pkg/cfg"
)

const cachePrefix = "foradar_cache:tianyancha:"

type RespBase struct {
	Reason    string `json:"reason"`
	ErrorCode int    `json:"error_code"`
}

const (
	ok     = 0      // 查询成功
	noData = 300000 // 查询无数据
)

func checkCode(code int, reason string) error {
	// 无数据
	if code == noData {
		return nil
	}
	if code != ok {
		return errors.New(reason)
	}
	return nil
}

type base struct {
	rootApi   string
	token     string
	cacheDays uint64
}

func newBase(from ...int) *base {
	config := cfg.LoadTianyancha()
	var b *base
	if from != nil {
		b = &base{
			rootApi: config.APIUrl,
			token:   config.Token,
		}
		if config.Token == "" {
			// 搜索的专用token
			b.token = "a4c49776-9de3-4905-a713-1f70a58fea3a"
		}
	} else {
		b = &base{
			rootApi: config.APIUrl,
			token:   config.NewToken,
		}
		if config.NewToken == "" {
			// 包年调用的token
			b.token = "3fcaa3a9-dfbe-4f3e-baa4-36a8bd28bb1a"
		}
	}
	if config.APIUrl == "" {
		b.rootApi = "https://open.api.tianyancha.com"
	}
	b.cacheDays = config.CacheDay
	return b
}

func (b *base) reqBuilderWithGET(query string, reqParams map[string]string) (*resty.Response, error) {
	client := resty.New().SetTimeout(time.Minute)
	reqUrl, _ := url.JoinPath(b.rootApi, query)
	resp, err := client.R().EnableTrace().
		SetQueryParams(reqParams).
		SetHeader("Authorization", b.token).
		SetHeader("Content-Type", "application/json").
		Get(reqUrl)
	return resp, err
}
