package tianyancha

import (
	"context"
	"fmt"
	"github.com/joho/godotenv"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	pb "micro-service/coreService/proto"
)

func Test_CompanyInfo(t *testing.T) {
	godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	req := &pb.QCCNameSearchRequest{
		Force:  false,
		Search: "甘肃省水务投资集团有限公司",
	}
	rsp := &pb.QCCGetBasicDetailsByNameResponse{}
	err := CompanyInfo(context.TODO(), req, rsp)
	assert.Nil(t, err)
	time.Sleep(time.Second)
	fmt.Println(rsp)
}

func Test_CompanyInfoBySearch(t *testing.T) {
	godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	var rsp = &pb.QCCBasicDetailSearchResponse{}
	err := CompanyInfoBySearch(context.TODO(), &pb.QCCBasicDetailSearchRequest{
		Keyword: "北京华顺信安信息技术有限公司",
		Force:   true,
	}, rsp)
	assert.Nil(t, err)
	time.Sleep(time.Second)
	fmt.Println(rsp)
}
