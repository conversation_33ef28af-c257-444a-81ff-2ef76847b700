package tianyancha

import (
	"context"
	"github.com/joho/godotenv"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	pb "micro-service/coreService/proto"
)

func Test_WeiboSearch(t *testing.T) {
	godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	req := pb.QCCNameSearchRequest{Force: true, Search: "北京百度网讯科技有限公司"}
	rsp := &pb.TYCWeiboAccountsResponse{}
	err := WeiboSearch(context.TODO(), &req, rsp)
	assert.Nil(t, err)
	time.Sleep(3 * time.Second)

	//req.Force = true
	//err = WeiboSearch(context.TODO(), &req, rsp)
	//assert.Nil(t, err)
	// time.Sleep(time.Second)
}
