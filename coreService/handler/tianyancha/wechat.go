package tianyancha

import (
	"context"
	"encoding/json"
	"strconv"

	"micro-service/pkg/errx"
	"micro-service/pkg/log"
)

const (
	// api docs: https://open.tianyancha.com/open/834
	publicWeChatApi         = "/services/open/ipr/publicWeChat/2.0"
	publicWeChatCachePrefix = cachePrefix + "wechat:"
)

func WeChatAccount(ctx context.Context, search string) ([]WeChatItem, error) {
	if search == "" {
		return nil, errx.ErrSearchCantEmpty
	}

	log.WithContextInfof(ctx, "[tianyancha] get WeChat accounts, keyword: %s", search)
	var err error
	var data WeChatResp
	var key = publicWeChatCachePrefix + search
	if !getCache(ctx, key, &data) {
		log.WithContextInfof(ctx, "[tianyancha] get key: %s cache is empty", key)
		data, err = getWeChatByApi(ctx, search)
		if err != nil {
			return nil, err
		}
	}
	return data.Result.Items, nil
}

type WeChatResp struct {
	RespBase
	Result WeChatResult `json:"result"`
}

type WeChatResult struct {
	Total int          `json:"total"`
	Items []WeChatItem `json:"items,omitempty"`
}

type WeChatItem struct {
	CodeImg     string `json:"codeImg"`     // 二维码
	PublicNum   string `json:"publicNum"`   // 公众号
	Title       string `json:"title"`       // 公众号名称
	TitleImgURL string `json:"titleImgURL"` // 封面图片
	Recommend   string `json:"recommend"`   // 描述
}

func getWeChatByApi(ctx context.Context, search string) (WeChatResp, error) {
	rsp, err := _getWeChatByApi(ctx, search)
	return rsp, err
}

// 调用天眼查API获取微信公众号信息
func _getWeChatByApi(ctx context.Context, search string) (WeChatResp, error) {
	var data WeChatResp
	params := map[string]string{"keyword": search, "pageSize": "20"}
	for page := 1; page <= 5; page++ {
		log.WithContextInfof(ctx, "[tianyancha] query api: %s, keyword: %s, page: %d", publicWeChatApi, search, page)
		params["pageNum"] = strconv.Itoa(page)
		_data, err := getWeChatByRecycle(params)
		costLog(ctx, _data.Result.Total > 0, "query api: %s, keyword: %s, page: %d", publicWeChatApi, search, page)
		if err != nil {
			log.WithContextErrorf(ctx, "[tianyancha] query api: %s, keyword: %s, page: %d failed: %v", publicWeChatApi, page, search, err)
			break
		}
		data.Result.Total = _data.Result.Total
		data.Result.Items = append(data.Result.Items, _data.Result.Items...)
		if !wechatFetchNextPage(page, _data.Result.Total) {
			break
		}
	}

	// set cache
	go setWeChatCache(ctx, search, data)

	return data, nil
}

// 判断是否可以继续获取下一页
func wechatFetchNextPage(page, total int) bool {
	if total == 0 {
		return false
	}
	return total > page*20
}

func getWeChatByRecycle(params map[string]string) (WeChatResp, error) {
	req := newBase()
	resp, err := req.reqBuilderWithGET(publicWeChatApi, params)
	if err != nil {
		return WeChatResp{}, err
	}

	var data WeChatResp
	err = json.Unmarshal(resp.Body(), &data)
	if err != nil {
		return WeChatResp{}, err
	}
	// 检查错误码
	if errCheck := checkCode(data.ErrorCode, data.Reason); errCheck != nil {
		return WeChatResp{}, errCheck
	}
	return data, nil
}

func setWeChatCache(ctx context.Context, search string, data any) {
	key := publicWeChatCachePrefix + search
	log.Infof("[tianyacha] set wechat cache, key: %s, data: %v", key, data)
	bs, _ := json.Marshal(data)
	if errSet := setCache(ctx, key, string(bs)); errSet != nil {
		log.WithContextErrorf(ctx, "[tianyancha] set keyword: %s wechat cache failed: %v", search, errSet)
	}
}
