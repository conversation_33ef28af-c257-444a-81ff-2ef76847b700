package tianyancha

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"strings"

	"github.com/spf13/cast"
	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/weibo_accounts"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
)

const (
	// api docs: https://open.tianyancha.com/open/1026
	weiboApi         = "/services/open/m/weibo/2.0"
	weiboCachePrefix = cachePrefix + "weibo:"
)

type WeiboResp struct {
	RespBase
	Result WeiboResult `json:"result"`
}

type WeiboResult struct {
	Total int         `json:"total"`
	Items []WeiboItem `json:"items,omitempty"`
}

type WeiboItem struct {
	Ico  string   `json:"ico"`            // 图标
	Name string   `json:"name"`           // 微博名称
	Href string   `json:"href"`           // url
	Info string   `json:"info"`           // 简介
	Tags []string `json:"tags,omitempty"` // 行业类别
}

// WeiboSearch 可以通过公司名称或ID获取企业微博的有关信息，包括企业微博名称、行业类别、简介等字段的详细信息
func WeiboSearch(ctx context.Context, req *pb.QCCNameSearchRequest, rsp *pb.TYCWeiboAccountsResponse) error {
	if req.Search == "" {
		return errx.ErrSearchCantEmpty
	}

	var err error
	var data WeiboResp
	if req.Force {
		data, err = getWeiboByApi(ctx, req)
	} else if !getWeiboResultCache(ctx, req.Search, &data) {
		data, err = getWeiboByApi(ctx, req)
	}
	if err != nil {
		return err
	}

	for _, v := range data.Result.Items {
		rsp.Items = append(rsp.Items, &pb.TYCWeiboAccountsResponseItem{
			Ico:  v.Ico,
			Name: v.Name,
			Href: v.Href,
			Info: v.Info,
			Tags: v.Tags,
		})
	}
	return nil
}

func _weiboByApiPage(params map[string]string) (WeiboResp, error) {
	req := newBase()
	resp, err := req.reqBuilderWithGET(weiboApi, params)
	if err != nil {
		return WeiboResp{}, err
	}

	var data WeiboResp
	err = json.Unmarshal(resp.Body(), &data)
	if err != nil {
		return WeiboResp{}, err
	}
	// 检查错误码
	if errCheck := checkCode(data.ErrorCode, data.Reason); errCheck != nil {
		return WeiboResp{}, errCheck
	}
	return data, nil
}

func getWeiboByApi(ctx context.Context, req *pb.QCCNameSearchRequest) (WeiboResp, error) {
	rsp, err := _getWeiboByApi(ctx, req)
	return rsp, err
}

func _getWeiboByApi(ctx context.Context, req *pb.QCCNameSearchRequest) (WeiboResp, error) {
	var data WeiboResp
	params := map[string]string{"keyword": req.Search, "pageSize": "20"}
	for page := 1; page <= 5; page++ {
		log.WithContextInfof(ctx, "[tianyancha] query api: %s, page: %d, keyword: %s, param: %s", weiboApi, page, req.Search, any2Str(req))
		params["pageNum"] = strconv.Itoa(page)
		_data, err := _weiboByApiPage(params)
		costLog(ctx, _data.Result.Total > 0, "query api: %s, page: %d, keyword: %s, param: %s", weiboApi, page, req.Search, any2Str(req))
		if err != nil {
			log.WithContextErrorf(ctx, "[tianyancha] query api: %s, page: %d, keyword: %s failed: %v", weiboApi, page, req.Search, err)
			break
		}
		data.Reason = _data.Reason
		data.ErrorCode = _data.ErrorCode
		data.Result.Total = _data.Result.Total
		data.Result.Items = append(data.Result.Items, _data.Result.Items...)
		if !wechatFetchNextPage(page, _data.Result.Total) {
			break
		}
	}

	// set cache: redis + mysql
	go setWeiboCache(ctx, req.Search, &data)

	return data, nil
}

func setWeiboCache(ctx context.Context, search string, data *WeiboResp) {
	// set redis cache
	go setWeiboRedisCache(ctx, search, data)

	// write mysql db
	go weiboResultWriteDb(ctx, search, data)
}

func getWeiboResultCache(ctx context.Context, search string, data *WeiboResp) bool {
	// 先查Redis缓存
	key := weiboCachePrefix + search
	if getCache(ctx, key, data) {
		return true
	}

	// 查MySQL数据
	db := weibo_accounts.NewWeiboModel()
	historyItem, err := db.HistoryFirst(mysql.WithColumnValue("`keyword`", search))
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.WithContextErrorf(ctx, "[tianyancha] query weibo history in db failed: %v, keyword: %s", err, search)
		}
		return false
	}

	if historyItem.Status != "0" && historyItem.Status != "300000" || isExpired(historyItem.UpdatedAt) {
		return false
	}

	list, err := db.AccountList(mysql.WithColumnValue("`keyword`", search))
	if err != nil {
		log.WithContextErrorf(ctx, "[tianyancha] query weibo accounts in db failed: %v, keyword: %s", err, search)
		return false
	}

	data.Reason = historyItem.Message
	data.ErrorCode = cast.ToInt(historyItem.Status)
	for _, v := range list {
		data.Result.Items = append(data.Result.Items, WeiboItem{
			Ico:  v.Ico,
			Name: v.Name,
			Href: v.Href,
			Info: v.Info,
			Tags: strings.Split(v.Tags, ";"),
		})
	}
	return true
}

func setWeiboRedisCache(ctx context.Context, search string, data *WeiboResp) {
	key := weiboCachePrefix + search
	log.Infof("[tianyacha] set weibo cache, key: %s, data: %v", key, data)
	bs, _ := json.Marshal(*data)
	if errSet := setCache(ctx, key, string(bs)); errSet != nil {
		log.WithContextErrorf(ctx, "[tianyancha] set keyword: %s weibo cache failed: %v", search, errSet)
	}
}

func weiboResultWriteDb(ctx context.Context, search string, rsp *WeiboResp) {
	err := _weiboResultWriteDb(search, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[tianyancha] query api: %s, keyword: %s, write db failed: %v", weiboApi, search, err)
	}
}

func _weiboResultWriteDb(search string, rsp *WeiboResp) error {
	db := weibo_accounts.NewWeiboModel()
	historyItem, err := db.HistoryFirst(mysql.WithColumnValue("`keyword`", search))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
	case err != nil:
		return err
	}
	historyItem.Keyword = search
	historyItem.Status = strconv.Itoa(rsp.ErrorCode)
	historyItem.Message = rsp.Reason
	if errSave := db.HistorySave(&historyItem); errSave != nil {
		return errSave
	}

	if len(rsp.Result.Items) == 0 {
		return nil
	}

	list, err := db.AccountList(mysql.WithColumnValue("`keyword`", search))
	if err != nil {
		return err
	}

	for _, v := range rsp.Result.Items {
		exists := false
		for i := range list {
			if v.Href == list[i].Href {
				continue
			}
			list[i].Name = v.Name
			list[i].Ico = v.Ico
			list[i].Info = v.Info
			list[i].Tags = strings.Join(v.Tags, ";")
			exists = true
			break
		}
		if !exists {
			list = append(list, &weibo_accounts.Account{
				Keyword: search,
				Ico:     v.Ico,
				Name:    v.Name,
				Href:    v.Href,
				Info:    v.Info,
				Tags:    strings.Join(v.Tags, ";"),
			})
		}
	}
	err = db.AccountsSave(list...)
	if err != nil {
		_ = db.HistoryDeleteById(historyItem.Id)
		return err
	}
	return nil
}
