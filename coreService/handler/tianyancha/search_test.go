package tianyancha

import (
	"context"
	"fmt"
	"github.com/joho/godotenv"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/pkg/log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	pb "micro-service/coreService/proto"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
)

func Init() {
	godotenv.Load("./../../../.env")

	cfg.InitLoadCfg()
	_ = es.GetInstance(cfg.LoadElastic())
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	log.Init()
}

func Test_SearchByApi(t *testing.T) {
	Init()
	data, err := searchNameByApi(context.TODO(), &pb.QCCNameSearchRequest{Search: "腾讯"})
	assert.Nil(t, err)
	for _, v := range data.Result.Items {
		fmt.Println(v)
	}
}

func Test_Search(t *testing.T) {
	Init()

	var rsp = &pb.QCCNameSearchResponse{}
	err := Search(context.TODO(), &pb.QCCNameSearchRequest{Search: "上海招商"}, rsp)
	time.Sleep(time.Second)
	assert.Nil(t, err)
	for _, v := range rsp.Data {
		fmt.Println(v)
	}
}
