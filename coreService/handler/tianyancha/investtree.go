package tianyancha

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/coreService/handler/fofatools"
	"micro-service/pkg/cfg"
	"strconv"
	"strings"

	"github.com/spf13/cast"
	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company_equity"
	"micro-service/middleware/mysql/company_record"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
)

// api docs: https://open.tianyancha.com/open/455
// 可以通过公司ID获取企业上下游对外投资关系，包含直接与间接投资企业等
const (
	investTreeApi            = "/services/v3/open/investtree"
	investTreeCacheKeyPrefix = cachePrefix + "invest_tree:"
)

type InvestTreeResp struct {
	RespBase
	Result      string           `json:"result"`
	ResultItems []InvestTreeItem `json:"resultItems,omitempty"`
}

type InvestTreeItem struct {
	Id         int              `json:"id"`         // 公司或人id
	RegStatus  string           `json:"regStatus"`  // 企业状态
	CreditCode string           `json:"creditCode"` // 统一社会信用代码
	Name       string           `json:"name"`       // 公司或人名
	Lable      string           `json:"lable"`      // 对象类型 Company-公司, Human-人，Other-其他
	Open       bool             `json:"open"`       // true-可延伸 false-不可延伸
	Percent    string           `json:"percent"`    // 占比
	Pid        string           `json:"pid"`        // 自然人pid
	Children   []InvestTreeItem `json:"children"`   // 子对象
}

// InvestTree 股权穿透
// 搜索关键字（公司名称、公司id、注册号或社会统一信用代码）
func InvestTree(ctx context.Context, req *pb.QCCInvestmentThroughRequest, rsp *pb.QCCInvestmentThroughResponse) error {
	search := req.Search
	if search == "" {
		return errx.ErrSearchCantEmpty
	}

	var err error
	var data InvestTreeResp
	// 强制刷新 直接调用API
	if req.Force == 1 {
		data, err = getInvestTreeByApi(ctx, req)
	} else if !getInvestTreeCache(ctx, search, &data) { // 没有读取到缓存
		data, err = getInvestTreeByApi(ctx, req)
	}
	if err != nil {
		return err
	}
	bs, err := json.Marshal(data.ResultItems)
	if err != nil {
		return err
	}
	bs = bytes.ReplaceAll(bs, []byte(`"children"`), []byte(`"children_list"`))
	bs = bytes.ReplaceAll(bs, []byte(`"regStatus"`), []byte(`"reg_status"`))
	err = json.Unmarshal(bs, &rsp.List)
	for i := range rsp.GetList() {
		rsp.List[i].Percent = convertPercent(rsp.List[i].Percent)
		modifyInvestTreePercent(rsp.List[i].ChildrenList) // 修改percent为百分比
	}
	return err
}

func getInvestTreeByApi(ctx context.Context, req *pb.QCCInvestmentThroughRequest) (InvestTreeResp, error) {
	rsp, err := _getInvestTreeByApi(ctx, req)
	costLog(ctx, len(rsp.ResultItems) > 0, "query api: %s, keyword: %s, param: %s", investTreeApi, req.Search, any2Str(req))
	return rsp, err
}

func _getInvestTreeByApi(ctx context.Context, req *pb.QCCInvestmentThroughRequest) (InvestTreeResp, error) {
	fofa := cfg.LoadCommon().FofaMcp
	var data InvestTreeResp
	if fofa {
		log.Infof("[InvestTree] use fofa invest tree mcp, keyword: %s, param: %s", req.Search, any2Str(req))
		relation, err := fofatools.QueryCompanyRelation(ctx, req.Search, 1, 0, false)
		if err != nil {
			log.Infof("[InvestTree] QueryCompanyRelation err: %v", err)
			return InvestTreeResp{}, err
		}
		data.ResultItems = []InvestTreeItem{{
			Id:         cast.ToInt(relation.Root.Id),
			RegStatus:  relation.Root.RegStatus,
			CreditCode: relation.Root.CreditCode,
			Name:       relation.Root.Name,
			Lable:      relation.Root.Label,
			Open:       relation.Root.Open,
			Percent:    relation.Root.Percent,
			Pid:        relation.Root.Pid,
			Children:   convertFofaMcpData(relation.Down),
		}}
	} else {
		log.WithContextInfof(ctx, "[tianyancha] query api: %s, keyword: %s, param: %s", investTreeApi, req.Search, any2Str(req))
		c := newBase()
		resp, err := c.reqBuilderWithGET(investTreeApi, map[string]string{
			"flag":    "4",    // 层次，最大4
			"dir":     "down", // up/down
			"keyword": req.Search,
		})
		if err != nil {
			return InvestTreeResp{}, err
		}

		err = json.Unmarshal(resp.Body(), &data)
		if err != nil {
			return InvestTreeResp{}, err
		}
		// 检查错误码
		if errCheck := checkCode(data.ErrorCode, data.Reason); errCheck != nil {
			return InvestTreeResp{}, errCheck
		}

		data.Result = strings.ReplaceAll(data.Result, "\\", "")
		if data.Result != "" {
			err = json.Unmarshal([]byte(data.Result), &data.ResultItems)
			if err != nil {
				return InvestTreeResp{}, err
			}
		}
	}
	// set result cache
	go setInvestTreeCache(ctx, req.Search, data)

	// write mysql db
	go func() {
		if errDb := investTreeWriteDb(ctx, req.Search, data.ResultItems); errDb != nil {
			log.WithContextErrorf(ctx, "[tianyancha] run investTreeWriteDb failed: %v", errDb)
		}
	}()
	return data, nil
}

func convertFofaMcpData(ori []fofatools.CompanyInfo) []InvestTreeItem {
	res := make([]InvestTreeItem, 0, len(ori))
	for _, item := range ori {
		var cur InvestTreeItem
		if len(item.Children) != 0 {
			cur.Children = convertFofaMcpData(item.Children)
		}
		cur.Id = cast.ToInt(item.Id)
		cur.RegStatus = item.RegStatus
		cur.CreditCode = item.CreditCode
		cur.Name = item.Name
		cur.Lable = item.Label
		cur.Open = item.Open
		cur.Percent = item.Percent
		cur.Pid = item.Pid
		res = append(res, cur)
	}
	return res
}

func setInvestTreeCache(ctx context.Context, search string, data any) {
	log.Infof("[tianyancha] set cache for invest_tree keyword: %s, data: %v", search, data)
	key := investTreeCacheKeyPrefix + search
	bs, _ := json.Marshal(data)
	if errSet := setCache(ctx, key, string(bs)); errSet != nil {
		log.WithContextErrorf(ctx, "[tianyancha] set keyword: %s invest_tree cache failed: %v", search, errSet)
	}
}

func investTreeWriteDb(ctx context.Context, search string, data []InvestTreeItem) error {
	recorder := company_record.NewCompanyRecorder()
	record, err := recorder.First(mysql.WithColumnValue("name", search))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		record.Name = search
	case err != nil:
		return err
	}

	if errSave := recorder.Save(&record); errSave != nil {
		return errSave
	}

	if len(data) == 0 {
		return nil
	}

	dataFirst := data[0]
	equityId, err := investTreeWriteEquity(dataFirst.Name, 0, 0, dataFirst.RegStatus)
	if err != nil {
		return err
	}

	record.EquityId = equityId
	if errSave := recorder.Save(&record); errSave != nil {
		return errSave
	}

	err = investTreeWriteChildNodes(ctx, equityId, &dataFirst)
	return err
}

func investTreeWriteChildNodes(ctx context.Context, parentId uint64, node *InvestTreeItem) error {
	if len(node.Children) == 0 {
		return nil
	}
	if parentId == 0 {
		pid, err := investTreeWriteEquity(node.Name, 0, 0, node.RegStatus)
		if err != nil {
			return err
		}
		parentId = pid
	}

	for i := range node.Children {
		percent := cast.ToFloat64(node.Children[i].Percent) * 100
		_, err := investTreeWriteEquity(node.Children[i].Name, parentId, percent, node.Children[i].RegStatus)
		if err != nil {
			log.WithContextErrorf(ctx, "[tianyancha] run func:investTreeWriteEquity failed, name: %s, parentId: %d, %v", node.Children[i].Name, parentId, err)
			continue
		}
		err = investTreeWriteChildNodes(ctx, 0, &node.Children[i])
		if err != nil {
			log.WithContextErrorf(ctx, "[tianyancha] run func:investTreeWriteChildNodes failed: %+v", err)
		}
	}
	return nil
}

func investTreeWriteEquity(name string, parentId uint64, percent float64, regStatus string) (uint64, error) {
	if regStatus == "" {
		regStatus = "正常"
	}
	db := company_equity.NewCompanyEquityModel()
	equity, err := db.First(
		mysql.WithColumnValue("`name`", name), mysql.WithColumnValue("`parent_id`", parentId))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		equity.Name = name
		equity.RegStatus = regStatus
		equity.ParentId = parentId
		equity.Percent = percent
		equity.ShouldCapi = "-"
	case err != nil:
		return 0, err
	default:
		equity.Percent = percent
	}

	var errSave error
	if parentId == 0 {
		if equity.Id == 0 {
			errSave = db.Save(&equity)
		}
		return equity.Id, errSave
	}
	errSave = db.Save(&equity)
	return equity.Id, errSave
}

func modifyInvestTreePercent(list []*pb.QCCInvestmentThroughResponseResultUnit) {
	for i := range list {
		list[i].Percent = convertPercent(list[i].Percent)
	}
}

func convertPercent(s string) string {
	if s != "" {
		f, _ := strconv.ParseFloat(s, 64)
		return fmt.Sprintf("%.2f", 100*f) + "%"
	}
	return "0%"
}

func getInvestTreeCache(ctx context.Context, search string, data *InvestTreeResp) bool {
	key := investTreeCacheKeyPrefix + search
	if getCache(ctx, key, data) {
		return true
	}

	info, err := company_record.NewCompanyRecorder().First(mysql.WithColumnValue("`name`", search))
	if err != nil {
		return false
	}
	if isExpired(info.UpdatedAt) {
		return false
	}
	if info.EquityId == 0 {
		return true
	}

	var dataFirst InvestTreeItem
	err = investTreeFindChildNodes(ctx, info.EquityId, &dataFirst)
	if err != nil {
		return false
	}
	data.Reason = "ok"
	data.ResultItems = append(data.ResultItems, dataFirst)
	return true
}

func investTreeFindChildNodes(ctx context.Context, parentId uint64, item *InvestTreeItem) error {
	db := company_equity.NewCompanyEquityModel()
	l, err := db.ListAll(mysql.WithColumnValue("`parent_id`", parentId))
	if len(l) == 0 {
		return err
	}

	for i := range l {
		x := &InvestTreeItem{
			Name:      l[i].Name,
			RegStatus: l[i].RegStatus,
			Percent:   strconv.FormatFloat(l[i].Percent/100, 'f', -1, 64),
			Children:  make([]InvestTreeItem, 0),
		}
		err = investTreeFindChildNodes(ctx, l[i].Id, x)
		if err != nil {
			log.WithContextErrorf(ctx, "[tianyancha] run func:investTreeFindChildNodes failed, parentId: %d, %v", parentId, err)
			continue
		}
		item.Children = append(item.Children, *x)
	}
	return nil
}
