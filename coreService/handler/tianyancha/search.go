package tianyancha

import (
	"context"
	"encoding/json"
	"github.com/jinzhu/copier"
	"micro-service/coreService/handler/fofatools"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
)

const (
	// api docs: https://open.tianyancha.com/open/816
	searchApi            = "/services/open/search/2.0"
	searchCacheKeyPrefix = cachePrefix + "search:"
)

type SearchResp struct {
	Result SearchResult `json:"result"`
	RespBase
}

type SearchResult struct {
	Total int          `json:"total"`
	Items []searchItem `json:"items,omitempty"`
}

type searchItem struct {
	Name            string `json:"name"`
	Id              int    `json:"id"`
	OrgNumber       string `json:"orgNumber"`
	Base            string `json:"base"`
	RegStatus       string `json:"regStatus"`
	RegCapital      string `json:"regCapital"`
	CompanyType     int    `json:"companyType"`
	MatchType       string `json:"matchType"`
	Type            int    `json:"type"`
	LegalPersonName string `json:"legalPersonName"`
	RegNumber       string `json:"regNumber"`
	CreditCode      string `json:"creditCode"`
	EstablishTime   string `json:"estiblishTime"`
}

func Search(ctx context.Context, req *pb.QCCNameSearchRequest, rsp *pb.QCCNameSearchResponse) error {
	search := req.Search
	if search == "" {
		return errx.ErrSearchCantEmpty
	}

	var err error
	var data SearchResp
	if req.Force {
		data, err = searchNameByApi(ctx, req)
	} else if !getCache(ctx, searchCacheKeyPrefix+search, &data) {
		data, err = searchNameByApi(ctx, req)
	}
	if err != nil {
		return err
	}

	rsp.VerifyResult = int64(data.Result.Total)
	for i := range data.Result.Items {
		if data.Result.Items[i].MatchType != "公司名称匹配" {
			continue
		}
		rsp.Data = append(rsp.Data, &pb.QCCNameSearchResponseNameSearchUnit{
			Name:      data.Result.Items[i].Name,
			HitReason: data.Result.Items[i].MatchType,
		})
	}
	return err
}

func searchNameByApi(ctx context.Context, req *pb.QCCNameSearchRequest) (SearchResp, error) {
	rsp, err := _searchNameByApi(ctx, req)
	costLog(ctx, rsp.Result.Total > 0, "query api: %s, keyword: %s, param: %s", searchApi, req.Search, any2Str(req))
	return rsp, err
}

func _searchNameByApi(ctx context.Context, req *pb.QCCNameSearchRequest) (SearchResp, error) {
	fofa := cfg.LoadCommon().FofaMcp
	var data SearchResp
	if fofa {
		log.WithContextInfof(ctx, "[CompanySearch] query fofa mcp: %s, search: %s, param: %s", branchApi, req.Search, any2Str(req))
		raw, err := fofatools.QueryCompanySearch(ctx, req.Search, 1, 20)
		if err != nil {
			log.WithContextErrorf(ctx, "[CompanySearch] query fofa mcp failed, search: %s, error: %v", req.Search, err)
			return SearchResp{}, err
		}
		err = copier.Copy(&data.Result, &raw)
		if err != nil {
			log.WithContextErrorf(ctx, "[CompanySearch] copy fofa mcp result failed, keyword: %s, error: %v", req.Search, err)
			return SearchResp{}, err
		}
	} else {
		log.WithContextInfof(ctx, "[tianyancha] query api: %s, keyword: %s, param: %s", searchApi, req.Search, any2Str(req))
		c := newBase(1)
		resp, err := c.reqBuilderWithGET(searchApi,
			map[string]string{"word": req.Search, "pageNum": "1", "pageSize": "20"},
		)
		if err != nil {
			return SearchResp{}, err
		}

		err = json.Unmarshal(resp.Body(), &data)
		if err != nil {
			return SearchResp{}, err
		}

		// 检查错误码
		if errCheck := checkCode(data.ErrorCode, data.Reason); errCheck != nil {
			return SearchResp{}, errCheck
		}
	}
	// set cache
	go setSearchCache(ctx, req.Search, data)

	return data, nil
}

func setSearchCache(ctx context.Context, search string, data any) {
	log.Infof("[tianyacha] set cache for search api keyword: %s, data: %v", search, data)
	key := searchCacheKeyPrefix + search
	bs, _ := json.Marshal(data)
	if errSet := setCache(ctx, key, string(bs)); errSet != nil {
		log.WithContextErrorf(ctx, "[Tianyancha] set keyword: %s search api cache failed: %v", search, errSet)
	}
}
