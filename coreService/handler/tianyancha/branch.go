package tianyancha

import (
	"context"
	"encoding/json"
	"github.com/jinzhu/copier"
	"micro-service/coreService/handler/fofatools"
	"micro-service/pkg/cfg"
	"strconv"

	pb "micro-service/coreService/proto"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
)

func Branch(ctx context.Context, req *pb.QCCBranchRequest, rsp *pb.QCCBranchResponse) error {
	data, err := BranchOrigin(ctx, req)
	if err != nil {
		return err
	}

	for i := range data.Result.Items {
		rsp.List = append(rsp.List, &pb.QCCBranchResponseBranchInfo{
			KeyNo:  strconv.Itoa(data.Result.Items[i].Id),
			Name:   data.Result.Items[i].Name,
			Status: data.Result.Items[i].RegStatus,
			OperInfo: &pb.QCCBranchResponseOper{
				KeyNo: "",
				Name:  data.Result.Items[i].LegalPersonName,
			},
		})
	}
	rsp.Total = int64(data.Result.Total)
	return nil
}

func BranchOrigin(ctx context.Context, req *pb.QCCBranchRequest) (BranchResp, error) {
	search := req.Keyword
	if search == "" {
		return BranchResp{}, errx.ErrSearchCantEmpty
	}

	var err error
	var data BranchResp
	key := branchCachePrefix + search
	if getCache(ctx, key, &data) {
		return data, nil
	}

	// query tianyancha api
	data, err = getBranchByApi(ctx, req)
	costLog(ctx, data.Result.Total > 0, "query api: %s, keyowrd: %s, param: %s", branchApi, req.Keyword, any2Str(req))

	return data, err
}

const (
	// api docs: https://open.tianyancha.com/open/824
	branchApi         = "/services/open/ic/branch/2.0"
	branchCachePrefix = cachePrefix + "branch:"
)

type BranchResp struct {
	RespBase
	Result BranchResult `json:"result"`
}

type BranchResult struct {
	Total int          `json:"total"`
	Items []BranchItem `json:"items,omitempty"`
}

type BranchItem struct {
	Id              int    `json:"id"`
	RegStatus       string `json:"regStatus"`       // 企业状态
	EstablishTime   int    `json:"estiblishTime"`   // 开业时间
	RegCapital      string `json:"regCapital"`      // 注册资金
	Name            string `json:"name"`            // 公司名
	Logo            string `json:"logo"`            // logo
	Alias           string `json:"alias"`           // 简称
	Category        string `json:"category"`        // 行业code
	PersonType      int    `json:"personType"`      // 法人类型 1-人 2-公司
	LegalPersonName string `json:"legalPersonName"` // 法人
	Base            string `json:"base"`            // 省份简称
}

func getBranchByApi(ctx context.Context, req *pb.QCCBranchRequest) (BranchResp, error) {
	fofa := cfg.LoadCommon().FofaMcp
	var data BranchResp
	if fofa {
		log.WithContextInfof(ctx, "[GetBranch] query fofa mcp: %s, keyword: %s, param: %s", branchApi, req.Keyword, any2Str(req))
		raw, err := fofatools.QueryCompanyBranch(ctx, req.Keyword, 1, 20)
		if err != nil {
			log.WithContextErrorf(ctx, "[GetBranch] query fofa mcp failed, keyword: %s, error: %v", req.Keyword, err)
			return BranchResp{}, err
		}
		err = copier.Copy(&data.Result, &raw)
		if err != nil {
			log.WithContextErrorf(ctx, "[GetBranch] copy fofa mcp result failed, keyword: %s, error: %v", req.Keyword, err)
			return BranchResp{}, err
		}
	} else {
		log.WithContextInfof(ctx, "[tianyancha] query api: %s, keyword: %s, param: %s", branchApi, req.Keyword, any2Str(req))
		c := newBase()
		resp, err := c.reqBuilderWithGET(branchApi,
			map[string]string{"keyword": req.Keyword, "pageNum": "1", "pageSize": "20"},
		)
		if err != nil {
			return BranchResp{}, err
		}
		err = json.Unmarshal(resp.Body(), &data)
		if err != nil {
			return BranchResp{}, err
		}
		// 检查错误码
		if errCheck := checkCode(data.ErrorCode, data.Reason); errCheck != nil {
			return BranchResp{}, errCheck
		}
	}
	// set cache
	go setBranchCache(ctx, req.String(), data)

	return data, nil
}

func setBranchCache(ctx context.Context, search string, data BranchResp) {
	key := branchCachePrefix + search
	log.Infof("[tianyacha] set branch cache, key: %s, data: %v", key, data)
	bs, _ := json.Marshal(data)
	if errSet := setCache(ctx, key, string(bs)); errSet != nil {
		log.WithContextErrorf(ctx, "[tianyancha] set keyword: %s branch cache failed: %v", search, errSet)
	}
}
