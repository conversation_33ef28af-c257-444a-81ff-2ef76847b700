package tianyancha

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"micro-service/pkg/log"
	"testing"
	"time"

	pb "micro-service/coreService/proto"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"

	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
)

func Test_InvestTreeByApi(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	data, err := getInvestTreeByApi(context.TODO(), &pb.QCCInvestmentThroughRequest{Search: "山西银行股份有限公司"})
	assert.Nil(t, err)
	time.Sleep(500 * time.Millisecond)
	fmt.Println("result length:", len(data.ResultItems))
	// for _, v := range list {
	//	fmt.Printf("%+v", v)
	// }
}

func Test_InvestTree(t *testing.T) {
	godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	rsp := &pb.QCCInvestmentThroughResponse{}
	var req = &pb.QCCInvestmentThroughRequest{Search: "上海招商明华船务有限公司", Force: 1}
	err := InvestTree(context.TODO(), req, rsp) // 上海招商明华船务有限公司 北京华顺信安科技有限公司
	time.Sleep(3 * time.Second)
	assert.Nil(t, err)
	for _, v := range rsp.List {
		fmt.Println("=====")
		fmt.Println(v)
	}
}

func Test_FindInvestTreeChildNodes(t *testing.T) {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	data := &InvestTreeItem{}
	err := investTreeFindChildNodes(context.TODO(), 162, data)
	assert.Nil(t, err)
	fmt.Printf("%+v", data)
}

func TestGetInvestTreeByApi(t *testing.T) {
	_ = godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())

	// 调用 _getInvestTreeByApi 方法
	req := &pb.QCCInvestmentThroughRequest{
		Search: "中国银行股份有限公司",
		Force:  1, // 强制刷新
	}

	result, err := _getInvestTreeByApi(context.Background(), req)
	if err != nil {
		fmt.Printf("[Error] _getInvestTreeByApi failed: %v\n", err)
		return
	}

	// 将结果转换为 JSON 格式
	jsonData, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		fmt.Printf("[Error] JSON marshal failed: %v\n", err)
		return
	}

	// 生成文件名（包含时间戳）
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("invest_tree_result_%s.json", timestamp)

	// 将 JSON 数据写入文件
	err = ioutil.WriteFile(filename, jsonData, 0644)
	if err != nil {
		fmt.Printf("[Error] Failed to write file %s: %v\n", filename, err)
		return
	}

	fmt.Printf("[Info] _getInvestTreeByApi result saved to file: %s\n", filename)
	fmt.Printf("[Info] Total result items: %d\n", len(result.ResultItems))
	if len(result.ResultItems) > 0 {
		fmt.Printf("[Info] Root company: %s\n", result.ResultItems[0].Name)
		fmt.Printf("[Info] Root company children: %d\n", len(result.ResultItems[0].Children))
	}
	fmt.Printf("[Info] Response reason: %s\n", result.Reason)
}
