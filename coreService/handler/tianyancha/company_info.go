package tianyancha

import (
	"context"
	"encoding/json"
	"errors"
	"micro-service/coreService/handler/fofatools"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"gorm.io/gorm"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/qcc"
	"micro-service/pkg/cfg"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

const (
	// api docs: https://open.tianyancha.com/open/1116
	companyInfoApi         = "/services/open/ic/baseinfo/normal"
	companyInfoCachePrefix = cachePrefix + "company_info:"
)

func CompanyInfoBySearch(ctx context.Context, req *pb.QCCBasicDetailSearchRequest, rsp *pb.QCCBasicDetailSearchResponse) error {
	search := req.Keyword
	if search == "" {
		return errx.ErrSearchCantEmpty
	}

	var err error
	var data CompanyInfoResp
	if req.Force {
		data, err = getCompanyInfoByApi(ctx, req)
	} else if !getCache(ctx, companyInfoCachePrefix+search, &data) {
		data, err = getCompanyInfoByApi(ctx, req)
	}
	if err != nil {
		return err
	}
	x := data.Result
	if x.Id == 0 {
		return nil
	}

	rsp.Total, rsp.PerPage, rsp.CurrentPage = 1, 1, 1
	rsp.Message = data.Reason
	rsp.Items = append(rsp.Items, &pb.QCCBasicDetailSearchInfo{
		KeyNo:      strconv.Itoa(x.Id),
		Name:       x.Name,
		CreditCode: x.CreditCode,
		StartDate:  formatTime(x.EstablishTime),
		OperName:   x.LegalPersonName,
		Status:     x.RegStatus,
		No:         x.RegNumber,
		Address:    x.RegLocation,
	})
	return nil
}

// CompanyInfo 企业基本信息
func CompanyInfo(ctx context.Context, req *pb.QCCNameSearchRequest, rsp *pb.QCCGetBasicDetailsByNameResponse) error {
	if req.Search == "" {
		return errx.ErrSearchCantEmpty
	}

	var err error
	var data CompanyInfoResp
	var apiReq = &pb.QCCBasicDetailSearchRequest{Keyword: req.Search, ClientId: req.ClientId}
	if req.Force {
		data, err = getCompanyInfoByApi(ctx, apiReq)
	} else if !getCompanyInfoCache(ctx, req.Search, &data) {
		data, err = getCompanyInfoByApi(ctx, apiReq)
	}
	if err != nil {
		return err
	}

	result := data.Result
	rsp.Status = utils.If(data.ErrorCode == 0, "200", "400")
	rsp.Message = data.Reason
	rsp.OrderNumber = ""
	rsp.Result = &pb.QCCBasicDetail{
		KeyNo:        strconv.Itoa(result.Id), // 主键ID
		Name:         result.Name,
		No:           result.RegNumber,
		BelongOrg:    result.RegInstitute,
		OperId:       "",
		OperName:     result.LegalPersonName,
		StartDate:    formatTime(result.EstablishTime),
		EndDate:      "",
		Status:       result.RegStatus,
		Province:     result.Base,
		UpdatedDate:  formatTime(result.UpdateTimes),
		CreditCode:   result.CreditCode,
		RegistCapi:   result.RegCapital,
		EconKind:     "",
		Address:      result.RegLocation,
		Scope:        result.BusinessScope,
		TermStart:    formatTime(result.FromTime),
		TeamEnd:      formatTime(result.ToTime),
		CheckDate:    formatTime(result.ApprovedTime),
		OrgNo:        result.OrgNumber,
		IsOnStock:    utils.If(result.BondNum != "", "1", "0"),
		StockNumber:  result.BondNum,
		StockType:    result.BondType,
		OriginalName: nil,
		ImageUrl:     "",
		EntType:      "",
		RecCap:       result.ActualCapital,
		RevokeInfo: &pb.QCCRevokeInfo{
			CancelDate:   formatTime(result.CancelDate),
			CancelReason: result.CancelReason,
			RevokeDate:   formatTime(result.RevokeDate),
			RevokeReason: result.RevokeReason,
		},
		Area: &pb.QCCArea{
			Province: result.Base,
			City:     result.City,
			County:   result.District,
		},
		AreaCode: "", // 行政区划代码
	}
	return nil
}

type CompanyInfoResp struct {
	Result CompanyInfoResult `json:"result"`
	RespBase
}

type CompanyInfoIndustryAll struct {
	Category       string `json:"category"`       // 国民经济行业分类门类
	CategoryBig    string `json:"categoryBig"`    // 国民经济行业分类大类
	CategorySmall  string `json:"categorySmall"`  // 国民经济行业分类小类
	CategoryMiddle string `json:"categoryMiddle"` // 国民经济行业分类中类
}

type CompanyInfoResult struct {
	Id                    int                    `json:"id"`                    // 主键ID
	Name                  string                 `json:"name"`                  // 企业名
	HistoryNames          string                 `json:"historyNames"`          // 曾用名
	RegStatus             string                 `json:"regStatus"`             // 企业状态
	RegCapital            string                 `json:"regCapital"`            // 注册资本
	StaffNumRange         string                 `json:"staffNumRange"`         // 人员规模
	BondNum               string                 `json:"bondNum"`               // 股票号
	HistoryNameList       []string               `json:"historyNameList"`       // 曾用名
	Industry              string                 `json:"industry"`              // 行业
	BondName              string                 `json:"bondName"`              // 股票名
	BondType              string                 `json:"bondType"`              // 股票类型
	IsMicroEnt            int                    `json:"isMicroEnt"`            // 是否是小微企业 0不是 1是
	CancelDate            int                    `json:"cancelDate"`            // 注销日期
	RevokeDate            int                    `json:"revokeDate"`            // 吊销日期
	Type                  int                    `json:"type"`                  // 法人类型，1 人 2 公司
	UpdateTimes           int                    `json:"updateTimes"`           // 更新时间
	ApprovedTime          int                    `json:"approvedTime"`          // 核准时间
	FromTime              int                    `json:"fromTime"`              // 经营开始时间
	ToTime                int                    `json:"toTime"`                // 经营结束时间
	EstablishTime         int                    `json:"estiblishTime"`         // 成立日期
	PercentileScore       int                    `json:"percentileScore"`       // 企业评分
	CompForm              int                    `json:"compForm"`              // 组成形式，1-个人经营、2-家庭经营
	SocialStaffNum        int                    `json:"socialStaffNum"`        // 参保人数
	LegalPersonName       string                 `json:"legalPersonName"`       // 法人
	RevokeReason          string                 `json:"revokeReason"`          // 吊销原因
	RegNumber             string                 `json:"regNumber"`             // 注册号
	CreditCode            string                 `json:"creditCode"`            // 统一社会信用代码
	Property3             string                 `json:"property3"`             // 英文名
	UsedBondName          string                 `json:"usedBondName"`          // 股票曾用名
	Alias                 string                 `json:"alias"`                 // 简称
	CompanyOrgType        string                 `json:"companyOrgType"`        // 企业类型
	CancelReason          string                 `json:"cancelReason"`          // 注销原因
	OrgNumber             string                 `json:"orgNumber"`             // 组织机构代码
	ActualCapital         string                 `json:"actualCapital"`         // 实收注册资金
	RegInstitute          string                 `json:"regInstitute"`          // 登记机关
	BusinessScope         string                 `json:"businessScope"`         // 经营范围
	TaxNumber             string                 `json:"taxNumber"`             // 纳税人识别号
	RegLocation           string                 `json:"regLocation"`           // 注册地址
	RegCapitalCurrency    string                 `json:"regCapitalCurrency"`    // 注册资本币种 人民币 美元 欧元 等
	ActualCapitalCurrency string                 `json:"actualCapitalCurrency"` // 实收注册资本币种 人民币 美元 欧元 等
	Tags                  string                 `json:"tags"`                  // 企业标签
	Base                  string                 `json:"base"`                  // 省份简称
	City                  string                 `json:"city"`                  // 市
	District              string                 `json:"district"`              // 区
	IndustryAll           CompanyInfoIndustryAll `json:"industryAll"`           // 国民经济行业分类
}

func getCompanyInfoByApi(ctx context.Context, req *pb.QCCBasicDetailSearchRequest) (CompanyInfoResp, error) {
	rsp, err := _getCompanyInfoByApi(ctx, req)
	costLog(ctx, rsp.Result.Id != 0, "query api: %s, keyword: %s, param: %s", companyInfoApi, req.Keyword, any2Str(req))

	return rsp, err
}

func _getCompanyInfoByApi(ctx context.Context, req *pb.QCCBasicDetailSearchRequest) (CompanyInfoResp, error) {
	fofa := cfg.LoadCommon().FofaMcp
	var data CompanyInfoResp
	if fofa {
		log.WithContextInfof(ctx, "[CompanyInfo] query fofa mcp: %s, keyword: %s, param: %s", branchApi, req.Keyword, any2Str(req))
		raw, err := fofatools.QueryCompanyInfo(ctx, req.Keyword)
		if err != nil {
			log.WithContextErrorf(ctx, "[CompanyInfo] query fofa mcp failed, keyword: %s, error: %v", req.Keyword, err)
			return CompanyInfoResp{}, err
		}
		err = copier.Copy(&data.Result, &raw)
		if err != nil {
			log.WithContextErrorf(ctx, "[CompanyInfo] copy struct failed, keyword: %s, error: %v", req.Keyword, err)
			return CompanyInfoResp{}, err
		}
	} else {
		log.WithContextInfof(ctx, "[tianyancha] query api: %s, keyword: %s, param: %s", companyInfoApi, req.Keyword, any2Str(req))
		c := newBase()
		resp, err := c.reqBuilderWithGET(companyInfoApi, map[string]string{"keyword": req.Keyword})
		if err != nil {
			return CompanyInfoResp{}, err
		}

		err = json.Unmarshal(resp.Body(), &data)
		if err != nil {
			return CompanyInfoResp{}, err
		}

		// 检查错误码
		err = checkCode(data.ErrorCode, data.Reason)
		if err != nil {
			return CompanyInfoResp{}, err
		}
	}

	// set cache
	go setCompanyInfoCache(ctx, req.Keyword, data)

	// write mysql
	go companyInfoWriteDb(ctx, req.Keyword, &data)

	return data, nil
}

func companyInfoWriteDb(ctx context.Context, search string, data *CompanyInfoResp) {
	updateHistory := &pb.QCCGetBasicDetailsByNameResponse{
		Status:  utils.If(data.ErrorCode == 0, "200", "400"),
		Message: data.Reason,
	}
	his := qcc.NewQCCBasicDetailHistoryModel().UpdateHistory(search, updateHistory)
	if his == nil {
		log.WithContextErrorf(ctx, "[tianyancha] Update history failed, got item nil")
		return
	}

	detail, err := qcc.NewQCCBasicDetailModel().Search(search)
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		detail = &qcc.QCCBasicDetail{}
	case err != nil:
		log.WithContextErrorf(ctx, "[tianyancha] get company info by keyword: %s failed: %v", search, err)
		return
	}

	result := data.Result
	if result.Id == 0 {
		return
	}
	x := &qcc.QCCBasicDetail{
		KeyNo:       strconv.Itoa(result.Id), // 主键ID
		Name:        result.Name,
		No:          result.RegNumber,
		BelongOrg:   result.RegInstitute,
		OperId:      "",
		OperName:    result.LegalPersonName,
		StartDate:   formatTime(result.EstablishTime),
		EndDate:     "",
		Status:      result.RegStatus,
		Province:    strings.ToUpper(result.Base),
		UpdatedDate: formatTime(result.UpdateTimes),
		CreditCode:  result.CreditCode,
		RegistCapi:  result.RegCapital,
		EconKind:    "",
		Address:     result.RegLocation,
		Scope:       result.BusinessScope,
		TermStart:   formatTime(result.FromTime),
		TeamEnd:     formatTime(result.ToTime),
		CheckDate:   formatTime(result.ApprovedTime),
		OrgNo:       result.OrgNumber,
		IsOnStock:   utils.If(result.BondNum != "", "1", "0"),
		StockNumber: result.BondNum,
		StockType:   result.BondType,
		ImageUrl:    "",
		EntType:     "",
		RecCap:      result.ActualCapital,
		AreaCode:    "", // 行政区划代码
	}
	revokeInfo := pb.QCCRevokeInfo{
		CancelDate:   formatTime(result.CancelDate),
		CancelReason: result.CancelReason,
		RevokeDate:   formatTime(result.RevokeDate),
		RevokeReason: result.RevokeReason,
	}
	revokeBytes, _ := json.Marshal(&revokeInfo)
	x.RevokeInfo = string(revokeBytes)
	area := pb.QCCArea{Province: provincesMap[x.Province], City: result.City, County: result.District}
	areaBytes, _ := json.Marshal(&area)
	x.Area = string(areaBytes)
	// 曾用名
	var historyNames = make([]*pb.QCCOriginalName, 0, len(data.Result.HistoryNameList))
	for _, v := range data.Result.HistoryNameList {
		historyNames = append(historyNames, &pb.QCCOriginalName{Name: v})
	}
	historyNamesBytes, _ := json.Marshal(&historyNames)
	x.OriginalName = string(historyNamesBytes)
	x.Id = detail.Id
	x.CreatedAt = detail.CreatedAt
	err = qcc.NewQCCBasicDetailModel().Save(x)
	if err != nil {
		log.WithContextErrorf(ctx, "[tianyancha] save company info failed, keyword: %s, %v", search, err)
		_ = qcc.NewQCCBasicDetailHistoryModel().DeleteById(his.Id)
	}
}

func setCompanyInfoCache(ctx context.Context, search string, data any) {
	key := companyInfoCachePrefix + search
	log.Infof("[tianyacha] set company_info cache, key: %s, data: %v", key, data)
	bs, _ := json.Marshal(data)
	if errSet := setCache(ctx, key, string(bs)); errSet != nil {
		log.WithContextErrorf(ctx, "[tianyancha] set keyword: %s company_info cache failed: %v", search, errSet)
	}
}

// get cache: 1. redis, 2. mysql
func getCompanyInfoCache(ctx context.Context, search string, data *CompanyInfoResp) bool {
	// 查redis缓存
	key := companyInfoCachePrefix + search
	if getCache(ctx, key, data) {
		log.WithContextInfof(ctx, "[tianyancha] api: %s, key: %s, got data in redis cache", companyInfoApi, search)
		return true
	}

	// 查数据库
	his, err := qcc.NewQCCBasicDetailHistoryModel().First(mysql.WithColumnValue("`keyword`", search))
	if err != nil {
		return false
	}
	if his.Status != "200" || his.UpdatedAt.Add(time.Duration(cfg.LoadQcc().BasicDetailCacheDay)).Before(time.Now()) {
		return false
	}
	detail, err := qcc.NewQCCBasicDetailModel().Search(search)
	if err != nil {
		return false
	}
	data.ErrorCode = 0
	data.Reason = his.Message
	data.Result = CompanyInfoResult{
		Id:                    cast.ToInt(detail.KeyNo),
		Name:                  detail.Name,
		HistoryNames:          strings.Join(companyHistoryNames(detail.OriginalName), ";"),
		RegStatus:             detail.Status,
		RegCapital:            detail.RegistCapi,
		StaffNumRange:         "",
		BondNum:               detail.StockNumber,
		HistoryNameList:       companyHistoryNames(detail.OriginalName),
		Industry:              "",
		BondName:              "",
		BondType:              detail.StockType,
		IsMicroEnt:            0,
		Type:                  0,
		UpdateTimes:           parseTime(detail.UpdatedDate),
		ApprovedTime:          parseTime(detail.CheckDate),
		FromTime:              parseTime(detail.TermStart),
		ToTime:                parseTime(detail.TeamEnd),
		EstablishTime:         parseTime(detail.StartDate),
		PercentileScore:       0,
		CompForm:              0,
		SocialStaffNum:        0,
		LegalPersonName:       detail.OperName,
		RegNumber:             detail.No,
		CreditCode:            detail.CreditCode,
		Property3:             "",
		UsedBondName:          "",
		Alias:                 "",
		CompanyOrgType:        "",
		OrgNumber:             detail.OrgNo,
		ActualCapital:         detail.RecCap,
		RegInstitute:          detail.BelongOrg,
		BusinessScope:         detail.Scope,
		TaxNumber:             "",
		RegLocation:           detail.Address,
		RegCapitalCurrency:    "",
		ActualCapitalCurrency: "",
		Tags:                  "",
		Base:                  detail.Province,
	}
	revoke := companyRevokeUnmarshal(detail.RevokeInfo)
	data.Result.CancelDate = parseTime(revoke.CancelDate)
	data.Result.RevokeDate = parseTime(revoke.RevokeDate)
	data.Result.CancelReason = revoke.CancelReason
	data.Result.RevokeReason = revoke.RevokeReason
	area := companyAreaUnmarshal(detail.Area)
	data.Result.City = area.City
	data.Result.District = area.County

	return true
}

func parseTime(s string) int {
	t, _ := utils.ParseTime(s)
	return int(t.UnixMilli())
}

func formatTime(t int) string {
	if t == 0 {
		return ""
	}
	return utils.UnixMilliToTime(t).Format(utils.DateTimeLayout)
}

func companyRevokeUnmarshal(s string) *pb.QCCRevokeInfo {
	var revoke = pb.QCCRevokeInfo{}
	_ = json.Unmarshal([]byte(s), &revoke)
	return &revoke
}

func companyHistoryNames(s string) []string {
	type x struct {
		Name string `json:"name"`
	}
	var l []x
	bs, _ := json.Marshal(s)
	_ = json.Unmarshal(bs, &l)
	var names = make([]string, 0, len(l))
	for _, v := range l {
		names = append(names, v.Name)
	}
	return names
}

func companyAreaUnmarshal(s string) *pb.QCCArea {
	var area = pb.QCCArea{}
	_ = json.Unmarshal([]byte(s), &area)
	return &area
}

var provincesMap = map[string]string{
	"BJ": "北京市",
	"SH": "上海市",
	"TJ": "天津市",
	"CQ": "重庆市",
	"HE": "河北省",
	"LN": "辽宁省",
	"JL": "吉林省",
	"HL": "黑龙江省",
	"JS": "江苏省",
	"ZJ": "浙江省",
	"AH": "安徽省",
	"FJ": "福建省",
	"JX": "江西省",
	"SD": "山东省",
	"HA": "河南省",
	"HB": "湖北省",
	"HN": "湖南省",
	"GD": "广东省",
	"HI": "海南省",
	"SC": "四川省",
	"GZ": "贵州省",
	"YN": "云南省",
	"XZ": "西藏自治区",
	"SN": "陕西省",
	"GS": "甘肃省",
	"QH": "青海省",
	"NX": "宁夏回族自治区",
	"XJ": "新疆维吾尔自治区",
	"GX": "广西壮族自治区",
	"HK": "香港特别行政区",
	"MO": "澳门特别行政区",
	"TW": "台湾省",
}
