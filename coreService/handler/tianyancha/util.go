package tianyancha

import (
	"context"
	"fmt"
	"time"

	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

func isExpired(t time.Time) bool {
	ttl := cfg.LoadTianyancha().CacheDay
	return time.Now().After(t.Add(time.Duration(ttl) * utils.Day))
}

// tianyancha log keyword
// 0. log level: INFO
// 1. call api: "[tianyancha] query api:"
// 2. api cost: "[tianyancha] COST: query api:"
// 3. api may not cost: "[tianyancha] NOT-COST: query api:"

func costLog(ctx context.Context, cost bool, format string, args ...any) {
	title := utils.If(cost, "[tianyancha] COST:", "[tianyancha] NOT-COST:")
	p := fmt.Sprintf(format, args...)
	log.WithContextInfof(ctx, fmt.Sprintf("%s %s", title, p))
}

func any2Str(x any) string {
	return utils.AnyToStr(x)
}
