package tianyancha

import (
	"context"
	"errors"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
	"time"

	"micro-service/middleware/redis"
	"micro-service/pkg/log"
)

func getCache(ctx context.Context, key string, data any) bool {
	var got = true
	switch err := redis.Get(ctx, key, data); {
	case errors.Is(err, redis.Nil):
		got = false
	case err != nil:
		log.WithContextErrorf(ctx, "[tianyancha] get cache key-> %s result failed: %v", key, err)
		got = false
	}

	log.WithContextInfof(ctx, "[tianyancha] search cache for key-> %s in redis, got cache data: %v", key, got)
	return got
}

func setCache(ctx context.Context, key string, data any) error {
	ttl := time.Duration(cfg.LoadTianyancha().CacheDay) * utils.Day
	return redis.Set(ctx, key, data, ttl)
}
