package hunter

import (
	"context"
	"fmt"
	"testing"

	microZap "github.com/go-micro/plugins/v4/logger/zap"
	"github.com/hashicorp/go-hclog"
	"github.com/joho/godotenv"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"

	pb "micro-service/coreService/proto"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

func initCfg() {
	_ = godotenv.Load("../../../.env")
	cfg.InitLoadCfg()

	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

func Test_FofaQueryA(t *testing.T) {
	initCfg()
	cfg.InitLoadCfg()

	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	rsp := pb.FofaQueryResponse{}

	err := HunterQuery(context.TODO(), &rsp, &pb.HunterQueryRequest{Search: `domain.suffix="avicnet.cn"`, Page: 1, PageSize: 1, StartTime: "2025-03-02", EndTime: "2025-03-03"})
	if err != nil {
		panic(err)
	}
	for i := range rsp.Sdata {
		println(fmt.Sprintf("%+v", rsp.Sdata[i]))
	}
	return
}
