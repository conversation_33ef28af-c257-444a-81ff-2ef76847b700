package hunter

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"github.com/spf13/cast"
	"net/http"
	"strconv"
	"strings"

	"github.com/idoubi/goz"

	corePb "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

const HttpUrlByHunter = "https://hunter.qianxin.com"

type (
	hunterResponse struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			Total int `json:"total"`
			Time  int `json:"time"`
			Arr   []struct {
				WebTitle     string `json:"web_title"`
				IP           string `json:"ip"`
				Port         int    `json:"port"`
				BaseProtocol string `json:"base_protocol"`
				Protocol     string `json:"protocol"`
				Domain       string `json:"domain"`
				Component    []struct {
					Name    string `json:"name"`
					Version string `json:"version"`
				} `json:"component"`
				URL            string `json:"url"`
				OS             string `json:"os"`
				Country        string `json:"country"`
				Province       string `json:"province"`
				City           string `json:"city"`
				UpdatedAt      string `json:"updated_at"`
				StatusCode     int    `json:"status_code"`
				Number         string `json:"number"`
				Company        string `json:"company"`
				IsWeb          string `json:"is_web"`
				IsRisk         string `json:"is_risk"`
				IsRiskProtocol string `json:"is_risk_protocol"`
				AsOrg          string `json:"as_org"`
				ISP            string `json:"isp"`
				Banner         string `json:"banner"`
				Header         string `json:"header"`
			} `json:"arr"`
			ConsumeQuota string `json:"consume_quota"`
			RestQuota    string `json:"rest_quota"`
		} `json:"data"`
	}
)

// CurlRequest CURL 请求
func CurlRequest(method, url string, opts ...goz.Options) (goz.ResponseBody, error) {
	cli := goz.NewClient()

	resp, err := cli.Request(method, url, opts...)
	if err != nil {
		return nil, err
	}
	content, err := resp.GetBody()
	if err != nil {
		return nil, err
	}
	return content, nil
}

func getHunterConfig(_ context.Context) cfg.Hunter {
	hunterConfig := cfg.LoadHunter()
	// 获取fofa配置
	return hunterConfig
}

// HunterQuery Hunter Search
func HunterQuery(ctx context.Context, rsp *corePb.FofaQueryResponse, param *corePb.HunterQueryRequest) error {
	log.WithContextInfof(ctx, "Received Core.HunterQuery request: %s", utils.AnyToStr(param))
	// 获取hunter配置
	hunterConfig := getHunterConfig(ctx)
	hunterUrl := HttpUrlByHunter + "/openApi/search"

	// 构建查询参数
	values := make([]string, 0)
	var apiKey = hunterConfig.ApiKey
	if apiKey == "" {
		apiKey = "031448d6f1898e892ff1a928f00afe891468f2dcb51ad159addd8d7431941011"
	}
	if param.Page < 1 {
		param.Page = 1
	}
	values = append(values, "api-key="+apiKey)
	values = append(values, "search="+base64.URLEncoding.EncodeToString([]byte(param.Search)))
	if param.PortFilter {
		values = append(values, "port_filter="+strconv.FormatBool(param.PortFilter))
	}
	if param.StatusCode != 0 {
		values = append(values, "status_code="+cast.ToString(param.StatusCode))
	}
	values = append(values, "page="+strconv.Itoa(int(param.Page)))
	values = append(values, "page_size="+strconv.Itoa(int(param.PageSize)))
	if param.StartTime != "" {
		values = append(values, "start_time="+param.StartTime)
	}
	if param.EndTime != "" {
		values = append(values, "end_time="+param.EndTime)
	}

	// 将所有参数拼接到URL中
	hunterUrl = hunterUrl + "?" + strings.Join(values, "&")
	log.WithContextInfof(ctx, "Received Core.HunterQuery Request URL: %s", hunterUrl)

	response, err := CurlRequest(http.MethodGet, hunterUrl, goz.Options{})
	if err != nil {
		log.WithContextErrorf(ctx, "请求hunter报错了第一处 Core.HunterQuery request: %v   报错信息，%s", param, err.Error())
		return err
	}

	var data hunterResponse
	err = json.Unmarshal([]byte(response.GetContents()), &data)
	if err != nil {
		log.WithContextErrorf(ctx, "请求hunter报错了第二处 Core.HunterQuery request: %v   报错信息，%s", param, err.Error())
		return err
	}

	if data.Code != 200 {
		log.WithContextErrorf(ctx, "hunter接口返回错误码: %d, 错误信息: %s", data.Code, data.Message)
		rsp.Error = true
		return nil
	}

	for _, item := range data.Data.Arr {
		port := strconv.Itoa(item.Port)
		var product []string
		if len(item.Component) > 0 {
			for _, component := range item.Component {
				if component.Name != "" {
					product = append(product, component.Name)
				}
			}
		}
		productStr := strings.Join(product, ",") // 使用逗号将所有的 product 连接起来
		var icp string
		if item.Number != "" {
			icp = item.Number
		}
		var url string
		if item.URL != "" {
			url = item.URL
		}
		var domain string
		if item.Domain != "" {
			domain = item.Domain
		}
		var isp string
		if item.ISP != "" {
			isp = item.ISP
		}
		var banner string
		if item.Banner != "" {
			banner = item.Banner
		}
		var title string
		if item.WebTitle != "" {
			title = item.WebTitle
		}
		asset := &corePb.Asset{
			Ip:             &item.IP,
			Port:           &port,
			Protocol:       &item.Protocol,
			BaseProtocol:   &item.BaseProtocol,
			Domain:         &domain,
			Title:          &title,
			Banner:         &banner,
			Isp:            &isp,
			Link:           &url,
			Host:           &domain,
			Lastupdatetime: &item.UpdatedAt,
			Product:        &productStr,
			Icp:            &icp,
		}
		rsp.Sdata = append(rsp.Sdata, asset)
	}
	rsp.Error = false
	size := uint32(data.Data.Total)
	rsp.Size = &size
	page := uint32(param.Page)
	rsp.Page = &page
	log.WithContextInfof(ctx, "hunter的数据拿到了啊 Core.HunterQuery request: %v", param)
	return nil
}
