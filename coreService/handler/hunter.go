package handler

import (
	"context"
	"go-micro.dev/v4/errors"
	"micro-service/coreService/handler/hunter"
	"micro-service/coreService/handler/quake"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/log"
)

func (e *Core) HunterQuery(ctx context.Context, req *pb.HunterQueryRequest, rsp *pb.FofaQueryResponse) error {
	log.WithContextInfof(ctx, "Received Core.HunterQuery request: %v", req)
	if req.Search == "" {
		return errors.New(pb.ServiceName, "Search不能为空!", 400)
	}
	return hunter.HunterQuery(ctx, rsp, req)
}

func (e *Core) QuakeQuery(ctx context.Context, req *pb.HunterQueryRequest, rsp *pb.FofaQueryResponse) error {
	log.WithContextInfof(ctx, "Received Core.HunterQuery request: %v", req)
	if req.Search == "" {
		return errors.New(pb.ServiceName, "Search不能为空!", 400)
	}
	return quake.QuakeQuery(ctx, rsp, req)
}
