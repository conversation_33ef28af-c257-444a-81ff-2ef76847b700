package handler

import (
	"context"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql/category"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"net"
	"strings"
	"sync"
	"time"
)

const WhoisRedactedForPrivacy = "REDACTED FOR PRIVACY"

func reformatInformationDate(s, layout string) string {
	if x, err := time.Parse(layout, s); err == nil {
		return x.Format("2006.01.02")
	}
	return ""
}

// InformationDomain 域名情报
func (e *Core) InformationDomain(ctx context.Context, req *pb.InformationDomainRequest, rsp *pb.InformationDomainResponse) error {
	rsp.RootDomain = utils.GetTopDomain(req.Domain)
	var wg sync.WaitGroup
	wg.Add(2)
	cacheKey := "information:domain:" + req.Domain
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: Information -> domain:%s,Cache:%s", req.Domain, cacheKey)
		return nil
	}
	// 获取解析IP
	queryIps := make([]string, 0)
	go func() {
		startTimeTwo := time.Now() // 记录协程开始时间
		if ips, err := net.LookupIP(utils.GetSubdomain(req.Domain)); err == nil {
			for i := range ips {
				queryIps = append(queryIps, `ip="`+ips[i].String()+`"`)
				rsp.Ip = append(rsp.Ip, ips[i].String())
			}
		}
		endTimeTwo := time.Now() // 记录协程结束时间
		log.WithContextInfof(ctx, "parseIp-InDomain-costtime，毫秒: %v, 请求的参数: %v", endTimeTwo.Sub(startTimeTwo).Milliseconds(), req.Domain)
	}()
	// 获取ICP备案信息
	go func() {
		startTimeOne := time.Now() // 记录协程开始时间
		icpRsp, IcpErr := pb.GetProtoCoreClient().Domain(ctx, &pb.IcpDomainRequest{Domain: rsp.RootDomain}, utils.SetRpcTimeoutOpt(5))
		if IcpErr == nil {
			if icpRsp.Info != nil {
				rsp.IcpCompany = icpRsp.Info.CompanyName
				rsp.IcpNumber = icpRsp.Info.Icp
				rsp.IcpType = icpRsp.Info.CompanyType
				rsp.IcpAuditTime = reformatInformationDate(icpRsp.Info.AuditTime, "2006-01-02 15:04:05 -0700 MST")
			}
		}

		endTimeOne := time.Now() // 记录协程结束时间
		log.WithContextInfof(ctx, "icp-InDomain-costtime，毫秒: %v, 请求的参数: %v", endTimeOne.Sub(startTimeOne).Milliseconds(), req.Domain)
	}()
	// 获取Whois信息
	go func() {
		defer wg.Done()
		startTimeThree := time.Now() // 记录协程开始时间
		whoisRsp, whoisErr := pb.GetProtoCoreClient().WhoisBasic(ctx, &pb.WhoisDomainBasicRequest{Domain: rsp.RootDomain}, utils.SetRpcTimeoutOpt(5))
		if whoisErr == nil {
			rsp.RegistryOrg = whoisRsp.SponsoringRegistrar
			rsp.RegistryCompany = whoisRsp.RegistrantOrg
			rsp.RegistryTime = reformatInformationDate(whoisRsp.RegistrationDate, "2006-01-02 15:04:05 -0700 MST")
			if WhoisRedactedForPrivacy != whoisRsp.RegistrantName {
				rsp.ContactName = whoisRsp.RegistrantName
			}
			if WhoisRedactedForPrivacy != whoisRsp.RegistrantMobile {
				rsp.ContactMobile = whoisRsp.RegistrantMobile
			}
		}
		endTimeThree := time.Now() // 记录协程结束时间
		log.WithContextInfof(ctx, "whois-InDomain-costtime，毫秒: %v, 请求的参数: %v", endTimeThree.Sub(startTimeThree).Milliseconds(), req.Domain)
	}()
	// 获取子域名
	go func() {
		startTimeFour := time.Now() // 记录协程开始时间
		defer wg.Done()
		queryStr := `host="` + utils.GetSubdomain(req.Domain) + `" && domain="` + rsp.RootDomain + `"`
		fofaSubdomainRsp, err := pb.GetProtoCoreClient().FofaQuery(ctx, &pb.FofaQueryRequest{Qbase64: queryStr, Page: 1, Size: 20, Field: []string{"host", "title", "protocol", "product", "product_category"}}, utils.SetRpcTimeoutOpt(5))
		if err == nil {
			for i := range fofaSubdomainRsp.Sdata {
				// 子域名
				subdomain := utils.GetSubdomain(*fofaSubdomainRsp.Sdata[i].Host)
				if !utils.ListContains(rsp.Subdomain, subdomain) && subdomain != rsp.RootDomain {
					rsp.Subdomain = append(rsp.Subdomain, subdomain)
				}
				subdomainTitles := strings.Split("test、dev、develop", "、")
				for x := range subdomainTitles {
					if strings.Contains(subdomain, subdomainTitles[x]) && !utils.ListContains(rsp.Risk, "测试子域名暴露") {
						rsp.Risk = append(rsp.Risk, "测试子域名暴露")
					}
				}
				// 风险信息
				assetsTilte := *fofaSubdomainRsp.Sdata[i].Title
				// 登录入口暴露
				titleKeywords := strings.Split("login、webadmin、登录、管理中心、admin、后台、管理平台、管理系统、工作平台、协同管理、登陆、系统、Manager、信息发布、Sign、logout、logon、Management、Opening...、防护、login、登录、Sign in、SignIn、ログイン、登錄、로그인、Log、вход、Anmelden、Panel、限制、管理、管控、manage", "、")
				for x := range titleKeywords {
					if strings.Contains(assetsTilte, titleKeywords[x]) {
						if !utils.ListContains(rsp.Risk, "登录入口暴露") {
							rsp.Risk = append(rsp.Risk, "登录入口暴露")
						}
					}
				}
				// 内部业务应用暴露
				insideRuleKeywords := strings.Split("企业网盘、FTP、EMAIL、ERP、人力管理、打印机、打卡机、考勤管理、人脸、摄像头、camera", "、")
				for x := range insideRuleKeywords {
					if strings.Contains(assetsTilte, insideRuleKeywords[x]) {
						if !utils.ListContains(rsp.Risk, "内部业务应用暴露") {
							rsp.Risk = append(rsp.Risk, "内部业务应用暴露")
						}
					}
				}
				// 非法互联网资源共享
				illegalRuleKeywords := strings.Split("京东无线宝、迅雷云、网心云、博纳云、玩客云、赚钱宝", "、")
				for x := range illegalRuleKeywords {
					if strings.Contains(assetsTilte, illegalRuleKeywords[x]) {
						if !utils.ListContains(rsp.Risk, "非法互联网资源共享") {
							rsp.Risk = append(rsp.Risk, "非法互联网资源共享")
						}
					}
				}
				// 高危资产互联网暴露
				heightRiskRuleKeywords := strings.Split("switch、administrator、router、交换机、路由器、路由管理、Local、service、outlook、邮箱、Camera", "、")
				for x := range heightRiskRuleKeywords {
					if strings.Contains(assetsTilte, heightRiskRuleKeywords[x]) {
						if !utils.ListContains(rsp.Risk, "高危资产互联网暴露") {
							rsp.Risk = append(rsp.Risk, "高危资产互联网暴露")
						}
					}
				}
				// 风险协议端口
				assetsProtocol := *fofaSubdomainRsp.Sdata[i].Protocol
				riskProtocols := strings.Split("ftp,ssh,telnet,smtp,dns,tftp,jboss,pop3,nfs,smb,imap,snmp,ldap,smb,linux rexec,rsync,openvpn,lotus,sql server,ispmanager,oracle,pptp,nfs,cpanel,zookeeper,zebra,squid,mysql,kangle,windows rdp,svn,glassfish,sybase/db2,postgresql,vnc,couchdb,redis,weblogic,kloxo,ajenti,zabbix,jenkins,plesk,websphere,elasticsearch,memcached,mongodb,hadoop", ",")
				for x := range riskProtocols {
					if strings.Contains(assetsProtocol, riskProtocols[x]) {
						if !utils.ListContains(rsp.Risk, "风险协议端口") {
							rsp.Risk = append(rsp.Risk, "风险协议端口")
						}
					}
				}
				// 数据库暴露
				assetsProduct := *fofaSubdomainRsp.Sdata[i].Product
				assetsProductCat := *fofaSubdomainRsp.Sdata[i].ProductCategory
				if (strings.Contains(assetsProduct, "数据库系统") || strings.Contains(assetsProductCat, "数据库系统")) && !utils.ListContains(rsp.Risk, "数据库暴露") {
					rsp.Risk = append(rsp.Risk, "数据库暴露")
				}
			}
		}
		endTimeFour := time.Now() // 记录协程结束时间
		log.WithContextInfof(ctx, "子域名-InDomain-costtime，毫秒: %v, 请求的参数: %v", endTimeFour.Sub(startTimeFour).Milliseconds(), req.Domain)
	}()
	// 获取域名相关信息
	go func() {
		startTimeFive := time.Now() // 记录协程开始时间
		queryStr := ""
		if len(queryIps) == 0 {
			queryStr = `domain="` + rsp.RootDomain + `" && host="` + utils.GetSubdomain(req.Domain) + `"`
		} else {
			queryStr = "(" + strings.Join(queryIps, "||") + `) && domain="` + rsp.RootDomain + `" && host="` + utils.GetSubdomain(req.Domain) + `"`
		}
		fofaRsp, err := pb.GetProtoCoreClient().FofaQuery(ctx, &pb.FofaQueryRequest{Qbase64: queryStr, Page: 1, Size: 20, Field: []string{"host", "title", "cert", "icon"}}, utils.SetRpcTimeoutOpt(5))
		if err == nil {
			for i := range fofaRsp.Sdata {
				asset := fofaRsp.Sdata[i]
				// 子域名
				subdomain := utils.GetSubdomain(*asset.Host)
				// ICON
				if rsp.Icon == "" || (subdomain == rsp.RootDomain && *asset.Icon != "") {
					rsp.Icon = *asset.Icon
				}
				// 业务系统名称
				if rsp.BusinessName == "" || (subdomain == rsp.RootDomain && *asset.Title != "" && !utils.CheckChinese(rsp.BusinessName) && utils.CheckChinese(*asset.Title)) {
					rsp.BusinessName = *asset.Title
				}
				// 证书
				if rsp.Cert == "" || (subdomain == rsp.RootDomain && *asset.Cert != "") {
					rsp.Cert = strings.Join(utils.GetCert(*asset.Cert, false), " ")
				}
			}
		}
		// 获取证书IP信息
		if rsp.Cert != "" {
			oStr, cnStr := utils.PluckCert(rsp.Cert)
			certQueryArr := make([]string, 0)
			if oStr != "" {
				certQueryArr = append(certQueryArr, `cert="`+oStr+`"`)
			}
			if cnStr != "" {
				certQueryArr = append(certQueryArr, `cert="`+cnStr+`"`)
			}
			fofaCertRsp, certErr := pb.GetProtoCoreClient().FofaQuery(ctx, &pb.FofaQueryRequest{Qbase64: strings.Join(certQueryArr, " && "), Page: 1, Size: 10, Field: []string{"ip", "host"}}, utils.SetRpcTimeoutOpt(10))
			if certErr == nil {
				for i := range fofaCertRsp.Sdata {
					if !utils.ListContains(rsp.CertIp, *fofaCertRsp.Sdata[i].Ip) {
						rsp.CertIp = append(rsp.CertIp, *fofaCertRsp.Sdata[i].Ip)
					}
				}
			}
			// 添加自身IP
			for i := range rsp.Ip {
				if !utils.ListContains(rsp.CertIp, rsp.Ip[i]) {
					rsp.CertIp = append(rsp.CertIp, rsp.Ip[i])
				}
			}
		}
		endTimeFive := time.Now() // 记录协程结束时间
		log.WithContextInfof(ctx, "域名-InDomain-costtime，毫秒: %v, 请求的参数: %v", endTimeFive.Sub(startTimeFive).Milliseconds(), req.Domain)
	}()
	// 获取组件相关信息
	go func() {
		startTimeSix := time.Now() // 记录协程开始时间
		if rsp.Ip != nil {
			fofaHostRsp, err := pb.GetProtoCoreClient().FofaHost(ctx, &pb.FofaHostRequest{Host: rsp.Ip[0], HasDetail: true})
			if err == nil {
				for i := range fofaHostRsp.Ports {
					for x := range fofaHostRsp.Ports[i].Products {
						product := fofaHostRsp.Ports[i].Products[x]
						firstCategory, secondCategory := category.NewSecondCategoriesModel().GetFirstAndSecondCategoryNameByRuleName(product.Product)
						if !utils.ListContains(rsp.CategoryFirst, firstCategory) {
							rsp.CategoryFirst = append(rsp.CategoryFirst, firstCategory)
						}
						if !utils.ListContains(rsp.CategorySecond, secondCategory) {
							rsp.CategorySecond = append(rsp.CategorySecond, secondCategory)
						}
						if !utils.ListContains(rsp.CategoryCompany, product.Company) {
							rsp.CategoryCompany = append(rsp.CategoryCompany, product.Company)
						}
					}
				}
			}
		}
		endTimeSix := time.Now() // 记录协程结束时间
		log.WithContextInfof(ctx, "rules-InDomain-costtime，毫秒: %v, 请求的参数: %v", endTimeSix.Sub(startTimeSix).Milliseconds(), req.Domain)
	}()
	wg.Wait()
	redis.SetCache(cacheKey, 60*time.Minute, rsp)
	return nil
}

// InformationIp Ip情报
func (e *Core) InformationIp(ctx context.Context, req *pb.InformationIpRequest, rsp *pb.InformationIpResponse) error {
	var wg sync.WaitGroup
	var hashData = 0
	wg.Add(3)
	cacheKey := "information:ip:" + req.Ip
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "[Core]: Information -> ip:%s,Cache:%s", req.Ip, cacheKey)
		return nil
	}
	domainChan := make(chan string, 1) // 创建一个字符串类型的通道
	// 获取子域名
	go func() {
		defer wg.Done()
		startTime := time.Now() // 记录协程开始时间
		fofaSubdomainRsp := pb.FofaQueryResponse{}
		queryStr := `ip="` + req.Ip + `"`
		if err := e.FofaQuery(ctx, &pb.FofaQueryRequest{Qbase64: queryStr, Page: 1, Size: 10, Field: []string{"host", "domain", "city", "as_organization", "title", "protocol", "product", "product_category"}}, &fofaSubdomainRsp); err == nil {
			for i := range fofaSubdomainRsp.Sdata {
				hashData = 1
				// fmt.Println(fmt.Sprintf("%+v", *fofaSubdomainRsp.Sdata[i]))
				// 子域名
				subdomain := utils.GetSubdomain(*fofaSubdomainRsp.Sdata[i].Host)
				if !utils.ListContains(rsp.Hosts, subdomain) {
					if len(subdomain) > 0 {
						vaildIp := net.ParseIP(subdomain)
						if vaildIp == nil {
							// fmt.Println("InformationIp-Hosts:" + subdomain)
							rsp.Hosts = append(rsp.Hosts, subdomain)
						}
					}
				}
				// 主域名
				domain := utils.GetSubdomain(*fofaSubdomainRsp.Sdata[i].Domain)
				if !utils.ListContains(rsp.Hosts, domain) {
					if len(domain) > 0 {
						// fmt.Println("InformationIp-domain:" + domain)
						rsp.Hosts = append(rsp.Hosts, domain)
					}
				}
				if !utils.ListContains(rsp.RootDomain, domain) {
					if (len(domain) > 0) && (len(rsp.RootDomain) == 0) {
						// fmt.Println("InformationIp-RootDomain:" + domain)
						// 将查到的域名放到通道里面
						rsp.RootDomain = append(rsp.RootDomain, domain)
						domainChan <- domain
						close(domainChan)
					}
				}
				// 地理位置
				city := *fofaSubdomainRsp.Sdata[i].City
				if len(city) > 0 && rsp.City == "" {
					// fmt.Println("city:" + city)
					rsp.City = city
				}
				// 所属厂商
				asOrganization := *fofaSubdomainRsp.Sdata[i].AsOrganization
				if len(asOrganization) > 0 && rsp.AsOrganization == "" {
					// fmt.Println("InformationIp-asOrganization:" + asOrganization)
					rsp.AsOrganization = asOrganization
				}
				subdomainTitles := strings.Split("test、dev、develop", "、")
				for x := range subdomainTitles {
					if strings.Contains(subdomain, subdomainTitles[x]) && !utils.ListContains(rsp.Risk, "测试子域名暴露") {
						rsp.Risk = append(rsp.Risk, "测试子域名暴露")
					}
				}
				// 风险信息
				assetsTilte := *fofaSubdomainRsp.Sdata[i].Title
				// 登录入口暴露
				titleKeywords := strings.Split("login、webadmin、登录、管理中心、admin、后台、管理平台、管理系统、工作平台、协同管理、登陆、系统、Manager、信息发布、Sign、logout、logon、Management、Opening...、防护、login、登录、Sign in、SignIn、ログイン、登錄、로그인、Log、вход、Anmelden、Panel、限制、管理、管控、manage", "、")
				for x := range titleKeywords {
					if strings.Contains(assetsTilte, titleKeywords[x]) {
						if !utils.ListContains(rsp.Risk, "登录入口暴露") {
							rsp.Risk = append(rsp.Risk, "登录入口暴露")
						}
					}
				}
				// 内部业务应用暴露
				insideRuleKeywords := strings.Split("企业网盘、FTP、EMAIL、ERP、人力管理、打印机、打卡机、考勤管理、人脸、摄像头、camera", "、")
				for x := range insideRuleKeywords {
					if strings.Contains(assetsTilte, insideRuleKeywords[x]) {
						if !utils.ListContains(rsp.Risk, "内部业务应用暴露") {
							rsp.Risk = append(rsp.Risk, "内部业务应用暴露")
						}
					}
				}
				// 非法互联网资源共享
				illegalRuleKeywords := strings.Split("京东无线宝、迅雷云、网心云、博纳云、玩客云、赚钱宝", "、")
				for x := range illegalRuleKeywords {
					if strings.Contains(assetsTilte, illegalRuleKeywords[x]) {
						if !utils.ListContains(rsp.Risk, "非法互联网资源共享") {
							rsp.Risk = append(rsp.Risk, "非法互联网资源共享")
						}
					}
				}
				// 高危资产互联网暴露
				heightRiskRuleKeywords := strings.Split("switch、administrator、router、交换机、路由器、路由管理、Local、service、outlook、邮箱、Camera", "、")
				for x := range heightRiskRuleKeywords {
					if strings.Contains(assetsTilte, heightRiskRuleKeywords[x]) {
						if !utils.ListContains(rsp.Risk, "高危资产互联网暴露") {
							rsp.Risk = append(rsp.Risk, "高危资产互联网暴露")
						}
					}
				}
				// 风险协议端口
				assetsProtocol := *fofaSubdomainRsp.Sdata[i].Protocol
				riskProtocols := strings.Split("ftp,ssh,telnet,smtp,dns,tftp,jboss,pop3,nfs,smb,imap,snmp,ldap,smb,linux rexec,rsync,openvpn,lotus,sql server,ispmanager,oracle,pptp,nfs,cpanel,zookeeper,zebra,squid,mysql,kangle,windows rdp,svn,glassfish,sybase/db2,postgresql,vnc,couchdb,redis,weblogic,kloxo,ajenti,zabbix,jenkins,plesk,websphere,elasticsearch,memcached,mongodb,hadoop", ",")
				for x := range riskProtocols {
					if strings.Contains(assetsProtocol, riskProtocols[x]) {
						if !utils.ListContains(rsp.Risk, "风险协议端口") {
							rsp.Risk = append(rsp.Risk, "风险协议端口")
						}
					}
				}
				// 数据库暴露
				assetsProduct := *fofaSubdomainRsp.Sdata[i].Product
				assetsProductCat := *fofaSubdomainRsp.Sdata[i].ProductCategory
				if (strings.Contains(assetsProduct, "数据库系统") || strings.Contains(assetsProductCat, "数据库系统")) && !utils.ListContains(rsp.Risk, "数据库暴露") {
					rsp.Risk = append(rsp.Risk, "数据库暴露")
				}
			}
		}
		endTime := time.Now() // 记录协程结束时间
		log.WithContextInfof(ctx, "fofa请求资产接口-InformationIp-协程花费时间，毫秒: %v, 请求的参数: %v", endTime.Sub(startTime).Milliseconds(), req.Ip)
	}()
	// 获取解析主机
	go func() {
		defer wg.Done()
		startTimeTwo := time.Now() // 记录协程开始时间
		// 创建一个带有1秒超时的context
		ctxTimeout, cancel := context.WithTimeout(ctx, time.Second)
		defer cancel() // 在函数执行完毕后取消context

		// 在协程内部使用ctxTimeout作为上下文
		ip := net.ParseIP(req.Ip)
		rsp.Ip = req.Ip

		select {
		case <-ctxTimeout.Done():
			log.WithContextInfof(ctx, "获取解析主机超时: %v, 请求的参数: %v", ctxTimeout.Err(), ip)
			return
		default:
			// 使用net.DialTimeout进行连接并设置超时时间
			conn, err := net.DialTimeout("tcp", ip.String()+":80", time.Second*2)
			if err != nil {
				log.WithContextInfof(ctx, "获取解析主机失败: %v, 请求的参数: %v", err, ip)
				return
			}
			defer conn.Close()

			// 使用已连接的conn进行主机名解析
			names, err := net.LookupAddr(ip.String())
			if err == nil {
				for _, name := range names {
					hashData = 1
					name = strings.TrimRight(name, ".")
					// fmt.Println("InformationIp-host_name:" + name)
					rsp.HostName = append(rsp.HostName, name)
				}
			} else {
				log.WithContextInfof(ctx, "获取解析主机失败: %v, 请求的参数: %v", err, ip)
				return
			}
			endTimeTwo := time.Now() // 记录协程结束时间
			log.WithContextInfof(ctx, "获取解析主机-InformationIp-协程花费时间，毫秒: %v, 请求的参数: %v", endTimeTwo.Sub(startTimeTwo).Milliseconds(), ip)
		}
	}()

	// 获取ICP备案信息
	go func() {
		// 等待第一个 goroutine 发送数据
		defer wg.Done()
		startTimeThree := time.Now() // 记录协程开始时间
		select {
		case result := <-domainChan:
			// 处理从通道 ch 中读取到的数据
			// 对接收到的数据进行处理
			topDomain := utils.GetTopDomain(result)
			icpRsp, IcpErr := pb.GetProtoCoreClient().Domain(ctx, &pb.IcpDomainRequest{Domain: topDomain}, utils.SetRpcTimeoutOpt(3))
			if IcpErr == nil {
				if icpRsp.Info != nil {
					hashData = 1
					// fmt.Println("InformationIp-icp_name:" + icpRsp.Info.CompanyName)
					rsp.IcpName = append(rsp.IcpName, icpRsp.Info.CompanyName)
				}
			}
		case <-time.After(time.Second):
			// 超时处理逻辑
			log.Info("通道处理icp查询备案超时1秒，不取了")
		}
		endTimeThree := time.Now() // 记录协程结束时间
		log.WithContextInfof(ctx, "获取ICP备案信息-InformationIp-协程花费时间，毫秒: %v, 请求的参数: %v", endTimeThree.Sub(startTimeThree).Milliseconds(), req.Ip)
	}()
	// 获取组件相关信息
	go func() {
		startTimeFour := time.Now() // 记录协程开始时间
		fofaHostRsp := pb.FofaHostResponse{}
		if err := e.FofaHost(ctx, &pb.FofaHostRequest{Host: req.Ip, HasDetail: true}, &fofaHostRsp); err == nil {
			for i := range fofaHostRsp.Ports {
				var rules []string
				var portRules = new(pb.RulePort)
				portRules.Port = fofaHostRsp.Ports[i].Port
				for x := range fofaHostRsp.Ports[i].Products {
					product := fofaHostRsp.Ports[i].Products[x]
					rules = append(rules, product.Product)
					portRules.Rules = rules
					firstCategory, secondCategory := category.NewSecondCategoriesModel().GetFirstAndSecondCategoryNameByRuleName(product.Product)
					if !utils.ListContains(rsp.CategoryFirst, firstCategory) {
						hashData = 1
						rsp.CategoryFirst = append(rsp.CategoryFirst, firstCategory)
					}
					if !utils.ListContains(rsp.CategorySecond, secondCategory) {
						hashData = 1
						rsp.CategorySecond = append(rsp.CategorySecond, secondCategory)
					}
					if !utils.ListContains(rsp.CategoryCompany, product.Company) {
						hashData = 1
						rsp.CategoryCompany = append(rsp.CategoryCompany, product.Company)
					}
				}
				if len(portRules.Rules) > 0 {
					hashData = 1
					rsp.RulePort = append(rsp.RulePort, portRules)
				}
			}
		}
		endTimeFour := time.Now() // 记录协程结束时间
		log.WithContextInfof(ctx, "fofa获取组件信息-InformationIp-协程花费时间，毫秒: %v, 请求的参数: %v", endTimeFour.Sub(startTimeFour).Milliseconds(), req.Ip)
	}()
	wg.Wait()
	if hashData == 1 {
		redis.SetCache(cacheKey, 24*7*60*time.Minute, rsp)
	} else {
		redis.SetCache(cacheKey, 60*time.Minute, rsp)
	}
	return nil
}
