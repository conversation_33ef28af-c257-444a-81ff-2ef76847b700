package fofatools

import (
	"context"
	"encoding/json"
	"github.com/mark3labs/mcp-go/mcp"
)

const (
	RelationToolName = "mcp_company_relation"
)

type RelationQueryRequest struct {
	IncludeHuman bool    `json:"include_human" doc:"是否包含human标签关系" jsonschema:"description=是否包含human标签关系，默认false"`
	Keyword      string  `json:"keyword" doc:"公司名称" jsonschema:"required,description=公司名称"`
	MaxPercent   float64 `json:"max_percent" doc:"股权比例上限" jsonschema:"default=1.0,description=股权比例上限，如0.5表示50%，默认1.0"`
	MinPercent   float64 `json:"min_percent" doc:"股权比例下限" jsonschema:"default=0.5,description=股权比例下限，如0.1表示10%，默认0.5"`
}

type RelationQueryResult struct {
	Total int           `json:"total"`
	Size  int           `json:"size"`
	Root  CompanyInfo   `json:"root"`
	Up    []CompanyInfo `json:"up"`
	Down  []CompanyInfo `json:"down"`
}

type CompanyInfo struct {
	Id         string        `json:"id"`
	RegStatus  string        `json:"reg_status"`
	CreditCode string        `json:"credit_code"`
	Name       string        `json:"name"`
	Label      string        `json:"label"`
	Open       bool          `json:"open"`
	Percent    string        `json:"percent"`
	Children   []CompanyInfo `json:"children,omitempty"`
	Pid        string        `json:"pid"`
}

func QueryCompanyRelation(ctx context.Context, keyword string, max float64, min float64, includeHuman bool) (*RelationQueryResult, error) {
	q := &RelationQueryRequest{
		IncludeHuman: includeHuman,
		Keyword:      keyword,
		MaxPercent:   max,
		MinPercent:   min,
	}
	return mcpRelationQuery(ctx, q)
}

func mcpRelationQuery(ctx context.Context, q *RelationQueryRequest) (*RelationQueryResult, error) {
	result, err := callFofaMcpTool(ctx, RelationToolName, map[string]any{
		"include_human": q.IncludeHuman,
		"keyword":       q.Keyword,
		"max_percent":   q.MaxPercent,
		"min_percent":   q.MinPercent,
	})
	if err != nil {
		return nil, err
	}

	var icpResult RelationQueryResult
	err = json.Unmarshal([]byte(result.Content[0].(mcp.TextContent).Text), &icpResult)
	if err != nil {
		return nil, err
	}

	return &icpResult, nil
}
