package fofatools

import (
	"context"
	"fmt"
	"github.com/joho/godotenv"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

func TestMcpICPQuery(t *testing.T) {
	godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
	mysql.GetInstance(cfg.LoadMysql())

	info := ICPQueryRequest{
		QueryType:   "domain",
		Value:       "ayxs.com",
		Size:        20,
		Full:        false,
		InclueHuman: false,
	}
	query, err := mcpICPQuery(context.Background(), &info)
	if err != nil {
		return
	}
	for _, item := range query.Records {
		fmt.Printf("%+v\n", item)
	}
}
