package fofatools

import (
	"context"
	"github.com/cloudwego/eino/components/tool"
	toolutils "github.com/cloudwego/eino/components/tool/utils"
	"micro-service/pkg/log"
)

const (
	ICPToolName = "fofa_icp_query"
)

func NewICPTool() (tool.InvokableTool, error) {
	t, err := toolutils.InferTool(ICPToolName,
		"提供ICP备案信息查询功能，可通过域名、IP地址、ICP编号或企业名称查询备案信息。",
		QueryICP)
	if err != nil {
		log.Errorf("Failed to create ICP tool: %v", err)
		return nil, err
	}
	return t, nil
}

type ICPQueryType string

const (
	ICPQueryTypeDomain ICPQueryType = "domain"
	ICPQueryTypeIP     ICPQueryType = "ip"
	ICPQueryTypeICP    ICPQueryType = "icp"
	ICPQueryTypeOrg    ICPQueryType = "org"
)

type ICPQueryRequest struct {
	QueryType   ICPQueryType `json:"query_type" doc:"查询类型" jsonschema:"enum=domain,enum=ip,enum=icp,enum=org,required,description=查询类型：domain（域名）、ip（IP地址）、icp（备案号）或org（企业名称）"`
	Value       string       `json:"value" doc:"查询值" jsonschema:"required,description=查询值，根据query_type提供相应的域名、IP地址、备案号或企业名称"`
	Size        int          `json:"size,omitempty" doc:"查询数量" jsonschema:"default=10,description=返回结果数量，范围1-10000，默认20"`
	Full        bool         `json:"full,omitempty" doc:"是否返回所有相关记录" jsonschema:"default=10,description=是否返回所有相关记录。当query_type为domain时，true=返回所有相关域名记录，false=仅返回精确匹配的记录。默认false"`
	InclueHuman bool         `json:"include_human,omitempty" doc:"是否返回个人备案数据" jsonschema:"description=是否返回个人备案数据。true=返回所有记录，false=仅返回企业的记录。默认false"`
}

type ICPRecord struct {
	Domain    string `json:"domain" doc:"域名"` // Domain在IP存在的情况下为空，要注意
	IP        string `json:"ip" doc:"IP"`
	ICP       string `json:"icp" doc:"ICP备案号"`
	Org       string `json:"org" doc:"企业名称"`
	OrgNature string `json:"org_nature" doc:"企业类型"`
}

type ICPQueryResult struct {
	Records []*ICPRecord `json:"records"`
}

func QueryICP(ctx context.Context, q *ICPQueryRequest) (*ICPQueryResult, error) {
	return mcpICPQuery(ctx, q)
}
