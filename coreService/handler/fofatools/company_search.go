package fofatools

import (
	"context"
	"encoding/json"
	"github.com/mark3labs/mcp-go/mcp"
)

type CompanySearchQueryRequest struct {
	Word     string `json:"word" doc:"搜索关键字" jsonschema:"required,description=搜索关键字，可以是企业名称、企业ID、注册号或统一社会信用代码"`
	PageNum  int    `json:"page_num" doc:"当前页数" jsonschema:"default=1.0,description=当前页数，默认第1页"`
	PageSize int    `json:"page_size" doc:"每页条数" jsonschema:"default=0.5,description=每页条数，默认20，最大20"`
}

type CompanySearchQueryResponse struct {
	Total int                 `json:"total"`
	Size  int                 `json:"size"`
	Page  int                 `json:"page"`
	Items []CompanySearchItem `json:"items"`
}
type CompanySearchItem struct {
	Id              int64  `json:"id"`
	Name            string `json:"name"`
	RegStatus       string `json:"reg_status"`
	EstablishTime   string `json:"estiblish_time"`
	RegCapital      string `json:"reg_capital"`
	CompanyType     int    `json:"company_type"`
	MatchType       string `json:"match_type"`
	Type            int    `json:"type"`
	LegalPersonName string `json:"legal_person_name"`
	RegNumber       string `json:"reg_number"`
	CreditCode      string `json:"credit_code"`
	OrgNumber       string `json:"org_number"`
	Base            string `json:"base"`
}

const CompanySearchToolName = "mcp_company_search"

func QueryCompanySearch(ctx context.Context, keyword string, page, perPage int) (*CompanySearchQueryResponse, error) {
	q := &CompanySearchQueryRequest{
		Word:     keyword,
		PageNum:  page,
		PageSize: perPage,
	}
	return mcpCompanySearchQuery(ctx, q)
}

func mcpCompanySearchQuery(ctx context.Context, q *CompanySearchQueryRequest) (*CompanySearchQueryResponse, error) {
	result, err := callFofaMcpTool(ctx, CompanySearchToolName, map[string]any{
		"word":      q.Word,
		"page_num":  q.PageNum,
		"page_size": q.PageSize,
	})
	if err != nil {
		return nil, err
	}

	var icpResult CompanySearchQueryResponse
	err = json.Unmarshal([]byte(result.Content[0].(mcp.TextContent).Text), &icpResult)
	if err != nil {
		return nil, err
	}

	return &icpResult, nil
}
