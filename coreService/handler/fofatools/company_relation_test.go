package fofatools

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/joho/godotenv"
	"io/ioutil"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
	"time"
)

func TestQueryCompanyRelation(t *testing.T) {
	_ = godotenv.Load("../../../.env")
	cfg.InitLoadCfg()
	log.Init()
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())

	relation, err := QueryCompanyRelation(context.Background(), "中国银行股份有限公司", 1.0, 0.0000001, false)
	if err != nil {
		fmt.Println("[Error] QueryCompanyRelation:", err)
		return
	}

	// 将结果转换为 JSON 格式
	jsonData, err := json.MarshalIndent(relation, "", "  ")
	if err != nil {
		fmt.Printf("[Error] JSON marshal failed: %v\n", err)
		return
	}

	// 生成文件名（包含时间戳）
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("company_relation_result_%s.json", timestamp)

	// 将 JSON 数据写入文件
	err = ioutil.WriteFile(filename, jsonData, 0644)
	if err != nil {
		fmt.Printf("[Error] Failed to write file %s: %v\n", filename, err)
		return
	}

	fmt.Printf("[Info] QueryCompanyRelation result saved to file: %s\n", filename)
	fmt.Printf("[Info] Total companies found: %d\n", relation.Total)
	fmt.Printf("[Info] Root company: %s\n", relation.Root.Name)
	fmt.Printf("[Info] Upstream companies: %d\n", len(relation.Up))
	fmt.Printf("[Info] Downstream companies: %d\n", len(relation.Down))
}
