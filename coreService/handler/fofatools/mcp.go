package fofatools

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"math/rand"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"
	"time"
)

//var (
//	globalFofaMcpClient *client.Client
//)

func getFofaConfig() cfg.Fofa {
	fofaList := cfg.LoadFofa()
	if len(fofaList) == 0 {
		fofaKeyStr, _ := utils.LaravelDecrypt("eyJpdiI6ImxOdnY4SUd1U1hKbi9kbGk0RGk5TlE9PSIsIm1hYyI6IjI4YjA1Y2FmMDI3Y2M3YTEzMzUwOWRkNWE0OWEwMjQyZmFiZjVhYmMwZjA0MDA5NmM2YWVjYzQ4NTRkOWFiNjMiLCJ2YWx1ZSI6Ik05Q3QzQnFiNHd3NlhsTW41bFJCT2xkSm0wRVFOZmRNZ3h3cjNBSGdSMDVoLzVOa2RtSStEbGphSThtTEtvK3BKY2l5b0RMR0FmWGdZcW00VEh5NVVHZ3cyTUh4eW8yR0o5YmRNalJDNVRRNzltSnIvZERkVHdxY3dRVTkralJHNG5pSFMyUElOalN0eUFCc2tKNlNQdm9xdkpnUmdBaUwvdEFuU1lvYVZBc1lQZ1FIRlh6RlAvR0dsRHdwVFE0WVNrdGRPV3J1YXdwYlNKR1Fqei94eHprMU83Ykh1cmVZZTJkbGszKzFBRnc9In0")
		fofaKeyStr = strings.Replace(strings.Replace(strings.Replace(fofaKeyStr, `"{`, "{", -1), `}"`, "}", -1), `\"`, `"`, -1)
		cfgFofa := cfg.Fofa{}
		_ = json.Unmarshal([]byte(fofaKeyStr), &cfgFofa)
		return cfgFofa
	}
	// 获取fofa配置
	return fofaList[rand.Intn(len(fofaList))]
}

func getFofaMcpClient() (*client.Client, error) {
	//if globalFofaMcpClient != nil && !refresh {
	//	return globalFofaMcpClient, nil
	//}
	//if refresh {
	//	fmt.Println("close old client ，start new client...")
	//	_ = globalFofaMcpClient.Close()
	//}
	fofaConfig := getFofaConfig()

	fofaKey := fofaConfig.FofaKey
	if fofaKey == "" {
		fofaKey = "bf96e714021457f6808b2aed62a208aa"
	}

	log.Infof("[MCP Tool] Initializing client...")

	c, err := client.NewSSEMCPClient("https://mcp.fofa.info/sse?key=" + fofaKey)
	if err != nil {
		log.Errorf("[MCP Tool] Failed to create SSE client: %v", err)
		return nil, err
	}

	err = c.Start(context.Background())
	if err != nil {
		log.Errorf("[MCP Tool] Failed to start SSE client: %v", err)
		return nil, err
	}

	// Initialize the client
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "cybersec-client",
		Version: "1.0.0",
	}

	initResult, err := c.Initialize(context.Background(), initRequest)
	if err != nil {
		return nil, err
	}
	log.Infof(
		"[MCP Tool] Initialized with server: %s_%s",
		initResult.ServerInfo.Name,
		initResult.ServerInfo.Version,
	)

	//globalFofaMcpClient = c
	return c, nil
}

// callFofaMcpTool 调用mcp的tool call，原始请求处理
func callFofaMcpTool(_ context.Context, name string, params map[string]any) (*mcp.CallToolResult, error) {
	listTmpRequest := mcp.CallToolRequest{}
	listTmpRequest.Params.Name = name
	listTmpRequest.Params.Arguments = params
	log.Infof("[MCP Tool] Calling tool: %s with params: %v", name, params)

	var result *mcp.CallToolResult
	var err error
	var c *client.Client

	for i := 0; i < 3; i++ {
		c, err = getFofaMcpClient()
		if err != nil {
			log.Errorf("[MCP Tool] Error getting mcp client: %v\n", err)
			return nil, err
		}
		defer c.Close()
		result, err = c.CallTool(context.Background(), listTmpRequest)
		if err != nil {
			log.Warnf("[MCP Tool] Retry %d - Error calling mcp tool: %v\n", i+1, err)
			time.Sleep(2 * time.Second)
			continue
		}
		break
	}
	if err != nil {
		log.Errorf("[MCP Tool] Failed calling mcp tool with retry 3 times: %v\n", err)
		return nil, errors.New("call mcp tool failed")
	}

	log.Infof("[MCP Tool] Success calling tool: %s with result: %v", name, result)
	return result, err
}

func mcpICPQuery(ctx context.Context, q *ICPQueryRequest) (*ICPQueryResult, error) {
	if q.Size == 0 {
		q.Size = 300
	}
	result, err := callFofaMcpTool(ctx, "mcp_icp_query", map[string]any{
		"query_type":    q.QueryType,
		"value":         q.Value,
		"size":          q.Size,
		"full":          true,
		"include_human": q.InclueHuman,
	})
	if err != nil {
		return nil, err
	}

	var icpResult ICPQueryResult
	err = json.Unmarshal([]byte(result.Content[0].(mcp.TextContent).Text), &icpResult)
	if err != nil {
		return nil, err
	}

	return &icpResult, nil
}
