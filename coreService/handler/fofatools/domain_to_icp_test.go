package fofatools

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/pkg/cfg"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestQueryICP(t *testing.T) {
	// 初始化配置
	cfg.InitLoadCfg()

	result, err := QueryICP(context.Background(), &ICPQueryRequest{
		QueryType:   ICPQueryTypeOrg,
		Value:       "刘磊",
		Size:        10,
		InclueHuman: true,
	})
	for _, record := range result.Records {
		fmt.Printf("%+v\n", record)
	}
	assert.NoError(t, err)
	assert.True(t, len(result.Records) > 0)

}

func TestNewFofaICPTool(t *testing.T) {
	// 初始化配置
	cfg.InitLoadCfg()
	icpTool, err := NewICPTool()
	if err != nil {
		t.Fatalf("Failed to create ICP tool: %v", err)
	}
	r, err := icpTool.InvokableRun(context.Background(), `{"value":"mi.com","query_type":"domain"}`)
	assert.NoError(t, err)
	var icpResult ICPQueryResult
	err = json.Unmarshal([]byte(r), &icpResult)
	assert.NoError(t, err)
	assert.True(t, len(icpResult.Records) > 0)
}
