package fofatools

import (
	"context"
	"fmt"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

func TestQueryCompanySearch(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()

	relation, err := QueryCompanySearch(context.Background(), "山西银行股份有限公司", 1, 10)
	if err != nil {
		fmt.Println("[Error] QueryCompanySearch:", err)
		return
	}
	fmt.Printf("[Info] QueryCompanySearch result: %#v\n", relation)
}
