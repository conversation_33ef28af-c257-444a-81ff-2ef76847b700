package fofatools

import (
	"context"
	"encoding/json"
	"github.com/mark3labs/mcp-go/mcp"
)

type CompanyInfoQueryRequest struct {
	Keyword string `json:"keyword" doc:"搜索关键字" jsonschema:"required,description=搜索关键字，可以是企业名称、企业ID、注册号或统一社会信用代码"`
}

type CompanyInfoQueryResponse struct {
	Id                    int    `json:"id"`
	Name                  string `json:"name"`
	CreditCode            string `json:"credit_code"`
	OrgNumber             string `json:"org_number"`
	RegNumber             string `json:"reg_number"`
	TaxNumber             string `json:"tax_number"`
	RegStatus             string `json:"reg_status"`
	RegCapital            string `json:"reg_capital"`
	RegCapitalCurrency    string `json:"reg_capital_currency"`
	ActualCapital         string `json:"actual_capital"`
	ActualCapitalCurrency string `json:"actual_capital_currency"`
	EstablishTime         int    `json:"estiblish_time"`
	FromTime              int    `json:"from_time"`
	ToTime                int    `json:"to_time"`
	LegalPersonName       string `json:"legal_person_name"`
	RegLocation           string `json:"reg_location"`
	RegInstitute          string `json:"reg_institute"`
	BusinessScope         string `json:"business_scope"`
	CompanyOrgType        string `json:"company_org_type"`
	CompForm              int    `json:"comp_form"`
	Industry              string `json:"industry"`
	IndustryAll           struct {
		Category       string `json:"category"`
		CategoryBig    string `json:"category_big"`
		CategoryMiddle string `json:"category_middle"`
		CategorySmall  string `json:"category_small"`
	} `json:"industry_all"`
	City            string   `json:"city"`
	District        string   `json:"district"`
	Base            string   `json:"base"`
	StaffNumRange   string   `json:"staff_num_range"`
	SocialStaffNum  int      `json:"social_staff_num"`
	BondNum         string   `json:"bond_num"`
	BondName        string   `json:"bond_name"`
	BondType        string   `json:"bond_type"`
	UsedBondName    string   `json:"used_bond_name"`
	Alias           string   `json:"alias"`
	HistoryNames    string   `json:"history_names"`
	HistoryNameList []string `json:"history_name_list"`
	Tags            string   `json:"tags"`
	PercentileScore int      `json:"percentile_score"`
	Type            int      `json:"type"`
	IsMicroEnt      int      `json:"is_micro_ent"`
	Property3       string   `json:"property3"`
	UpdateTimes     int      `json:"update_times"`
	ApprovedTime    int      `json:"approved_time"`
	CancelDate      int      `json:"cancel_date"`
	CancelReason    string   `json:"cancel_reason"`
	RevokeDate      int      `json:"revoke_date"`
	RevokeReason    string   `json:"revoke_reason"`
}

const CompanyInfoToolName = "mcp_company_info"

func QueryCompanyInfo(ctx context.Context, keyword string) (*CompanyInfoQueryResponse, error) {
	q := &CompanyInfoQueryRequest{
		Keyword: keyword,
	}
	return mcpCompanyInfoQuery(ctx, q)
}

func mcpCompanyInfoQuery(ctx context.Context, q *CompanyInfoQueryRequest) (*CompanyInfoQueryResponse, error) {
	result, err := callFofaMcpTool(ctx, CompanyInfoToolName, map[string]any{
		"keyword": q.Keyword,
	})
	if err != nil {
		return nil, err
	}

	var icpResult CompanyInfoQueryResponse
	err = json.Unmarshal([]byte(result.Content[0].(mcp.TextContent).Text), &icpResult)
	if err != nil {
		return nil, err
	}

	return &icpResult, nil
}
