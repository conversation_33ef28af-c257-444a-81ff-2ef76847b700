package fofatools

import (
	"context"
	"fmt"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

func TestQueryCompanyBranch(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()

	relation, err := QueryCompanyBranch(context.Background(), "山西银行股份有限公司", 1, 20)
	if err != nil {
		fmt.Println("[Error] QueryCompanyBranch:", err)
		return
	}
	fmt.Printf("[Info] QueryCompanyBranch result: %#v\n", relation)
}
