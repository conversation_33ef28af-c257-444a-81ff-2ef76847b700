package fofatools

import (
	"context"
	"encoding/json"
	"github.com/mark3labs/mcp-go/mcp"
)

type BranchQueryRequest struct {
	Keyword  string `json:"keyword" doc:"搜索关键字" jsonschema:"required,description=搜索关键字，可以是企业名称、企业ID、注册号或统一社会信用代码"`
	PageNum  int    `json:"page_num" doc:"当前页数" jsonschema:"default=1.0,description=当前页数，默认第1页"`
	PageSize int    `json:"page_size" doc:"每页条数" jsonschema:"default=0.5,description=每页条数，默认20，最大20"`
}

type BranchQueryResult struct {
	Total int           `json:"total"`
	Size  int           `json:"size"`
	Page  int           `json:"page"`
	Items []BranchItems `json:"items"`
}
type BranchItems struct {
	Id              int    `json:"id"`
	Name            string `json:"name"`
	RegStatus       string `json:"reg_status"`
	EstablishTime   int    `json:"estiblish_time"`
	RegCapital      string `json:"reg_capital"`
	Logo            string `json:"logo"`
	Alias           string `json:"alias"`
	Category        string `json:"category"`
	PersonType      int    `json:"person_type"`
	LegalPersonName string `json:"legal_person_name"`
	Base            string `json:"base"`
}

const BranchToolName = "mcp_company_branch"

func QueryCompanyBranch(ctx context.Context, keyword string, page, perPage int) (*BranchQueryResult, error) {
	q := &BranchQueryRequest{
		Keyword:  keyword,
		PageNum:  page,
		PageSize: perPage,
	}
	return mcpBranchQuery(ctx, q)
}

func mcpBranchQuery(ctx context.Context, q *BranchQueryRequest) (*BranchQueryResult, error) {
	result, err := callFofaMcpTool(ctx, BranchToolName, map[string]any{
		"keyword":   q.Keyword,
		"page_num":  q.PageNum,
		"page_size": q.PageSize,
	})
	if err != nil {
		return nil, err
	}

	var icpResult BranchQueryResult
	err = json.Unmarshal([]byte(result.Content[0].(mcp.TextContent).Text), &icpResult)
	if err != nil {
		return nil, err
	}

	return &icpResult, nil
}
