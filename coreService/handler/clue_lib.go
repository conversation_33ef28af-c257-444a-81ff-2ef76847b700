package handler

import (
	"context"
	"errors"
	"fmt"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/company_clues"
	"micro-service/middleware/mysql/company_equity"
	"micro-service/middleware/mysql/user"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/errx"
	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"path/filepath"
	"strconv"
	"time"

	"go-micro.dev/v4/logger"
	"gorm.io/gorm"
)

func hasCompanyList(userId, operateCompanyId int64) ([]string, int64, error) {
	newUid, _, err := user.NewUserModel().GetOpTargetID(userId, operateCompanyId)
	if err != nil {
		return nil, userId, err
	}
	companyInfo, err := company.NewCompanyModel().FindById(0, uint64(newUid))
	if err != nil {
		return nil, newUid, err
	}
	var sonNameArr []string
	if companyInfo.Name != "" && companyInfo.Name != "null" {
		sonNameArr = append(sonNameArr, companyInfo.Name)
		items, _ := company_equity.NewCompanyEquityModel().ListAll(mysql.WithWhere("name = ?", companyInfo.Name))
		var ceIds []int64
		for _, v := range items {
			ceIds = append(ceIds, int64(v.Id))
		}
		if len(ceIds) > 0 {
			parentItems, _ := company_equity.NewCompanyEquityModel().
				ListAll(mysql.WithWhere("parent_id in (?)", ceIds), mysql.WithOrder("percent DESC"))
			var filterMap = make(map[string]struct{})
			filterMap[companyInfo.Name] = struct{}{}
			for _, v := range parentItems {
				if v.Name == "" || v.Name == "null" {
					continue
				}
				if _, ok := filterMap[v.Name]; ok {
					continue
				}
				filterMap[v.Name] = struct{}{}
				sonNameArr = append(sonNameArr, v.Name)
			}
		}
	}
	return sonNameArr, newUid, nil
}

func (e *Core) ClueCompanyDropList(ctx context.Context, req *pb.ClueCompanyDropListRequest, rsp *pb.ClueCompanyDropListResponse) error {
	sonNameArr, _, err := hasCompanyList(req.UserId, req.OperateCompanyId)
	if err != nil {
		log.WithContextWarnf(ctx, "ClueCompanyDropList hasCompanyList failed, uid:%s,companyId:%d,err:%v\n", req.UserId, req.OperateCompanyId, err)
		rsp.Data = nil
		return nil
	}
	rsp.Data = sonNameArr
	return nil
}

func (e *Core) ClueCompanyClueList(ctx context.Context, req *pb.ClueCompanyClueListRequest, rsp *pb.ClueCompanyClueListResponse) error {
	// 校验搜索的企业名称是否属于该公司控股的公司
	sonNameArr, newUid, err := hasCompanyList(req.UserId, req.OperateCompanyId)
	if err != nil {
		log.WithContextWarnf(ctx, "ClueCompanyClueList hasCompanyList failed, uid:%s,companyId:%d,err:%v\n", req.UserId, req.OperateCompanyId, err)
		rsp.Data = nil
		return nil
	}

	if !utils.Contains(sonNameArr, req.CompanyName) {
		return errors.New("您输入的企业暂无权限查看，请重新输入")
	}
	records, err := company_clues.NewCompanyCluesModel().ListAll(mysql.WithWhere("user_id = ?", newUid), mysql.WithWhere("clue_company_name = ?", req.CompanyName))
	if err != nil {
		log.WithContextWarnf(ctx, "ClueCompanyClueList company_clues failed, uid:%s,companyId:%d,err:%v\n", req.UserId, req.OperateCompanyId, err)
		rsp.Data = nil
		return nil
	}

	for _, v := range records {
		rsp.Data = append(rsp.Data, &pb.ClueCompanyClueListResponse_ClueInfo{
			Id:              int64(v.Id),
			UserId:          v.UserId,
			CompanyId:       v.CompanyId,
			Content:         v.Content,
			ClueCompanyName: v.ClueCompanyName,
			Hash:            v.Hash,
			PunycodeDomain:  v.PunycodeDomain,
			Type:            int64(v.Type),
			CreatedAt:       v.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:       v.UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return nil
}

func (e *Core) ClueCompanyListExport(ctx context.Context, req *pb.ClueCompanyListExportRequest, rsp *pb.ClueCompanyListExportResponse) error {
	newUid, opCompanyID, err := user.NewUserModel().GetOpTargetID(req.UserId, req.OperateCompanyId)
	log.Debug("ClueCompanyListExportHandle newUid", newUid, opCompanyID)
	if err != nil {
		return err
	}
	var records []*company_clues.CompanyClues
	if len(req.Ids) > 0 {
		//导出指定
		records, err = company_clues.NewCompanyCluesModel().ListAll(mysql.WithSelect("type,content,created_at"),
			mysql.WithWhere("user_id = ?", newUid),
			mysql.WithWhere("id in (?) ", req.Ids))

	} else {
		if req.CompanyName == "" {
			return errx.ErrFrontEndParam
		}
		// 导出全部
		records, err = company_clues.NewCompanyCluesModel().ListAll(mysql.WithSelect("type,content,created_at"),
			mysql.WithWhere("user_id = ?", newUid),
			mysql.WithWhere("clue_company_name = ? ", req.CompanyName))
	}
	if err != nil {
		return err
	}
	if len(records) == 0 {
		return errx.ErrFrontEndNoData
	}

	url, err := clueSaveAsExcel(req.UserId, records)
	if err != nil {
		return err
	}
	rsp.Url = url
	return nil
}

// https://10.10.10.189/api/v1/clue_company/list/export?ids%5B0%5D=266&ids%5B1%5D=268&ids%5B2%5D=265&operate_company_id=-1&company_name=

/*
	{
	    "code": 0,
	    "message": "\u6210\u529f",
	    "data": {
	        "url": "\/api\/v1\/files\/eyJpdiI6ImI3U1AwMTR4Q0M5b044Q3dMMURjclE9PSIsInZhbHVlIjoiM0V4YXJDQUJLWnBQWjlzb01vcHRUVWhOVDhyYXZUSy9oZGFXSDdja2puVVlrNGxxWVBiZW54TUQySUxvUDlvbWM5QlFPRFRuSVJtUGxUV1FjSjU3VWtKUGgvakdMZ010VllnVHQ0UCtUOFk3ZVJQZERpZzZvK1FxcjFLb0g4bGtqc3BPQTVEZ3NtVWhPOGxGaGhhWkg0QjcxMjhvdElyaXFmK2dCVUR2UGNnQ0I0c1JtdGZHOGUvcG9BdnBUTjRsaGNaMXdia1hyVkFsS1A3NUM4SC95dz09IiwibWFjIjoiOGUyNzY4ZmIxZDhjNGIyMjNlNTc3OWE4Zjk5ZWQzOThhYWZhYWJhZTQ5YTA5ZjVmZjRmZWZmNzBiODUwODVkOCIsInRhZyI6IiJ9.xlsx"
	    }
	}
*/
func clueSaveAsExcel(uid int64, records []*company_clues.CompanyClues) (string, error) {
	excelHeader := []string{"类型", "线索", "添加时间"}
	order := excel.WriteByOrder{
		SheetName: "sheet1",
		Data: [][]string{
			excelHeader,
		},
	}
	for _, v := range records {
		if v.Type == company_clues.CompanyCluesTypeLOGO {
			v.Content = cfg.LoadAPP().Url + storage.GenDownloadUrl(v.Content, filepath.Base(v.Content))
		}
		t, ok := company_clues.CompanyCluesTypeMap[v.Type]
		if !ok {
			t = "内容"
		}
		order.Data = append(order.Data, []string{
			t,
			v.Content,
			v.CreatedAt.Format(utils.DateTimeLayout),
		})

	}
	// app/public/{$user_id}/
	fileName := "公共线索库-" + time.Now().Format(utils.DateTimeIntegerLayout)
	relFileName := utils.Md5Hash(strconv.Itoa(int(time.Now().Unix())) + fileName)
	relFileNameWithExt := relFileName + ".xlsx"
	excelPath := filepath.Join(storage.GetPublicStoragePath(), strconv.Itoa(int(uid)), "/")
	abFilePath := filepath.Join(storage.GetRootPath(), excelPath, relFileNameWithExt)
	if utils.IsTestEnv() {
		abFilePath = filepath.Join("./", relFileNameWithExt)
	}
	err := excel.WriteSheetsByOrder(abFilePath, &order)
	if err != nil {
		return "", err
	}
	logger.Infof("clueSaveAsExcel fileName:%s, relFileNameWithExt:%s,abFilePath:%s\n", relFileName, relFileNameWithExt, abFilePath)
	return storage.GenDownloadUrl(filepath.Join(excelPath, relFileNameWithExt), relFileName), nil
}

const (
	SceneTypeChushi   = 0
	ScentypeKuozhan   = 1
	SecentTypeXunhuan = 2
)

var seceneTypeToStringMap = map[int64]string{
	0: "yuanshi",
	1: "kuozhan",
	2: "xunhuan",
}

func genClueCompanySceneMap() map[int64]map[int]int64 {
	var clueSceneMap = make(map[int64]map[int]int64, 0)
	clueSceneMap[SceneTypeChushi] = map[int]int64{
		company_clues.CompanyCluesTypeDomain:    0,
		company_clues.CompanyCluesTypeCert:      0,
		company_clues.CompanyCluesTypeICP:       0,
		company_clues.CompanyCluesTypeLOGO:      0,
		company_clues.CompanyCluesTypeKeyword:   0,
		company_clues.CompanyCluesTypeSubdomain: 0,
		company_clues.CompanyCluesTypeIP:        0,
		company_clues.CompanyCluesTypeFID:       0,
	}
	clueSceneMap[ScentypeKuozhan] = map[int]int64{
		company_clues.CompanyCluesTypeDomain:    0,
		company_clues.CompanyCluesTypeCert:      0,
		company_clues.CompanyCluesTypeICP:       0,
		company_clues.CompanyCluesTypeLOGO:      0,
		company_clues.CompanyCluesTypeKeyword:   0,
		company_clues.CompanyCluesTypeSubdomain: 0,
		company_clues.CompanyCluesTypeIP:        0,
		company_clues.CompanyCluesTypeFID:       0,
	}
	clueSceneMap[SecentTypeXunhuan] = map[int]int64{
		company_clues.CompanyCluesTypeDomain:    0,
		company_clues.CompanyCluesTypeCert:      0,
		company_clues.CompanyCluesTypeICP:       0,
		company_clues.CompanyCluesTypeLOGO:      0,
		company_clues.CompanyCluesTypeKeyword:   0,
		company_clues.CompanyCluesTypeSubdomain: 0,
		company_clues.CompanyCluesTypeIP:        0,
		company_clues.CompanyCluesTypeFID:       0,
	}
	return clueSceneMap
}
func (e *Core) ClueLibCompanyClueOverview(ctx context.Context, req *pb.ClueLibCompanyClueOverviewRequest, rsp *pb.ClueLibCompanyClueOverviewResponse) error {
	// 校验能否查看，看是否为其子企业，做完公司从属关系后替换
	companyInfo, err := company.NewCompanyModel().First(mysql.WithId(req.OperateCompanyId))
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}
	if err != nil {
		return err
	}
	// 这里的own_id，母公司的和子公司的 是相同的吗 // todo wufj
	if companyInfo.OwnerId != uint64(req.UserId) {
		return errors.New("您没有查看此企业信息的权限")
	}

	//获取已确认线索数据,todo wufj 做企业线索库时确认
	clueList, err := clues.NewCluer().ListAll(
		mysql.WithWhere("user_id = ?", req.UserId),
		mysql.WithWhere("clue_company_name", companyInfo.Name),
		mysql.WithWhere("status = 1"),
		mysql.WithWhere("is_deleted = 0"),
		mysql.WithWhere("is_supply_chain = 0"),
	)
	if err != nil {
		return err
	}

	clueSceneMap := genClueCompanySceneMap()

	rsp.CompanyName = companyInfo.Name
	for _, v := range clueList {
		fmt.Println(v.ClueCompanyName)
		switch v.Source {
		case clues.ClueSourceManual, clues.ClueSourceSafeAdd, clues.ClueSourceICP, clues.ClueSourceImport:
			t := clueSceneMap[SceneTypeChushi]
			t[v.Type]++
		case clues.ClueSourceExpAdd:
			t := clueSceneMap[SceneTypeChushi]
			t[v.Type]++
		case clues.ClueSourceRecommend:
			t := clueSceneMap[SceneTypeChushi]
			t[v.Type]++

		}
	}

	for k, v := range clueSceneMap {
		var allCount int64
		var details = make([]*pb.ClueLibCompanyClueOverviewResponse_SceneDetails, 0)
		for t, count := range v {
			allCount += count
			details = append(details, &pb.ClueLibCompanyClueOverviewResponse_SceneDetails{
				Type:  int64(t),
				Label: company_clues.CompanyCluesTypeMap[int8(t)],
				Count: int64(count),
			})
		}
		lastCount := getLastMonthCompanyCluesAllCount(ctx, seceneTypeToStringMap[k], allCount)
		t := pb.ClueLibCompanyClueOverviewResponse_ClueInfo{
			SceneType:     k,
			SceneAllCount: allCount,
			SceneAllMom:   utils.MonthOnMonth(allCount, lastCount),
			SceneDetails:  details,
		}
		rsp.ClueInfo = append(rsp.ClueInfo, &t)
	}
	return nil
}

const (
	CompanyCluesCountKey = "foradar:core:companyCluesCount"
)

func getLastMonthCompanyCluesAllCount(ctx context.Context, scene string, nowCount int64) int64 {
	month := time.Now().Format(utils.DateMonthLayout)
	lastMonth := time.Now().AddDate(0, -1, 0).Format(utils.DateMonthLayout)
	nowMapkey := scene + ":" + month
	lastMapKey := scene + ":" + lastMonth

	lastData, err := redis.GetClient().HGet(ctx, CompanyCluesCountKey, lastMapKey).Int64()
	if err == redis.Nil || lastData <= 0 {
		log.Warn("redis hget Failed ", CompanyCluesCountKey, lastMapKey, err)
		return nowCount
	}

	nowData, err := redis.GetClient().HGet(ctx, CompanyCluesCountKey, nowMapkey).Result()
	if err == redis.Nil || nowData == "" {
		err = redis.GetClient().HSet(ctx, CompanyCluesCountKey, nowMapkey, nowCount).Err()
		if err != nil {
			log.WithContextErrorf(ctx, "getLastMonthCompanyCluesAllCount save to redis failed,nowMapkey:%s, %v\n", nowMapkey, err)
		}
	}
	return nowCount
}
