package main

import (
	"time"

	microZap "github.com/go-micro/plugins/v4/logger/zap"
	ocplugin "github.com/go-micro/plugins/v4/wrapper/trace/opentracing"
	"github.com/hashicorp/go-hclog"
	"github.com/opentracing/opentracing-go"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"
	"micro-service/coreService/brokers"
	"micro-service/coreService/handler"
	pb "micro-service/coreService/proto"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/traces"
	"micro-service/pkg/utils"
)

func onInit() {
	cfg.InitLoadCfg()

	// 初始化日志
	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// Mysql & Redis & elastic
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	_ = es.GetInstance(cfg.LoadElastic())
}

func main() {
	onInit()
	// jaeger 链路追踪
	if jaegerTrace, io, err := traces.GetJaegerTracer(pb.ServiceName, cfg.LoadCommon().JaegerAdder); err != nil {
		log.Errorf("Jaeger: %s Error:", cfg.LoadCommon().JaegerAdder, err.Error())
		panic(err)
	} else {
		defer io.Close()
		opentracing.SetGlobalTracer(jaegerTrace)
		log.Infof("Jaeger: %s Connection successful", cfg.LoadCommon().JaegerAdder)
	}
	consulReg := cfg.GetInstance().GetConsulReg()
	// Create service
	srv := micro.NewService(
		micro.Name(pb.ServiceName),
		micro.Version(pb.ServiceVersion),
		micro.RegisterTTL(5*time.Second),      // TTL指定从上一次心跳间隔起，超过这个时间服务会被服务发现移除
		micro.RegisterInterval(2*time.Second), // 让服务在指定时间内重新注册，保持TTL获取的注册时间有效
		micro.Logger(logger.DefaultLogger),
		micro.Address(utils.GetListenAddress(cfg.LoadCommon().Network)),
		micro.Broker(brokers.NewWhoisBroker()),
		micro.WrapHandler(ocplugin.NewHandlerWrapper(opentracing.GlobalTracer())),
		micro.WrapClient(ocplugin.NewClientWrapper(opentracing.GlobalTracer())),
	)

	srv.Init(
		micro.Registry(consulReg),
	)
	regSrv := srv.Server()

	// Register handler
	if err := pb.RegisterCoreHandler(regSrv, new(handler.Core)); err != nil {
		panic(err)
	}
	// Register Whois Subscriber
	if err := brokers.RegisterWhoisSubscriber(regSrv); err != nil {
		panic(err)
	}
	// Run service
	if err := srv.Run(); err != nil {
		panic(err)
	}
}
