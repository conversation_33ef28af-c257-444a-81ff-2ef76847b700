package core

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

type apiRsp struct {
	Server  string `json:"server"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

type authData struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

func tokenFmt(token authData) string {
	return fmt.Sprintf("%s |%s", token.TokenType, token.AccessToken)
}

func getToken() (string, error) {
	c := cfg.LoadCommon().Client
	if token, err := getTokenCache(); err == nil {
		return tokenFmt(token), nil
	}

	u, _ := url.JoinPath(c.<PERSON><PERSON>, "/api/v1/auth/token")
	param := fmt.Sprintf(`{"client_id": %q, "client_secret": %q}`, c.<PERSON>d, c.Secret)
	authReq, _ := http.NewRequest(http.MethodPost, u, strings.NewReader(param))
	authClient := http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:    100,
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true}, //nolint:gosec,gocritic
		},
	}
	authRsp, err := authClient.Do(authReq)
	if err != nil {
		return "", err
	}
	defer authRsp.Body.Close()

	body, err := io.ReadAll(authRsp.Body)
	if err != nil {
		return "", err
	}

	var tk apiRsp
	err = json.Unmarshal(body, &tk)
	if err != nil {
		return "", err
	}
	if tk.Code != 0 {
		return "", errors.New(tk.Message)
	}

	var ad authData
	bs, _ := json.Marshal(tk.Data)
	err = json.Unmarshal(bs, &ad)
	if ad.AccessToken == "" {
		return "", fmt.Errorf("json Unmarshl: Empty token: %w", err)
	}

	go setTokenCache(ad)

	return tokenFmt(ad), nil
}

type option struct {
	Token   string
	Timeout time.Duration
	Header  map[string]string
}

type f func(*option)

func WithTimeout(t time.Duration) f {
	return func(o *option) {
		o.Timeout = t
	}
}

func WithHeader(h map[string]string) f {
	return func(o *option) {
		o.Header = h
	}
}

func tokenKey() string {
	clientId := cfg.LoadCommon().Client.ClientId
	return fmt.Sprintf("local-client:SAAS-token:%s", clientId)
}

func setTokenCache(token authData) {
	key := tokenKey()
	exp := token.ExpiresIn - 60
	bs, _ := json.Marshal(token)
	err := redis.Set(context.TODO(), key, string(bs), time.Duration(exp)*time.Second)
	if err != nil {
		log.Warnf("[Local Client] Get SAAS cache token failure: %v", err)
	}
}

func getTokenCache() (authData, error) {
	key := tokenKey()
	var token authData
	err := redis.Get(context.TODO(), key, &token)
	return token, err
}

func checkOption(opts ...f) (*option, error) {
	opt := &option{}
	for _, o := range opts {
		o(opt)
	}

	var err error
	token, err := getToken()
	if err != nil {
		return nil, err
	}
	opt.Token = token

	if len(opt.Header) == 0 {
		opt.Header = map[string]string{
			"Authorization": opt.Token,
			"Connection":    "keep-alive",
			"Content-Type":  "application/json",
		}
	} else {
		opt.Header["Authorization"] = opt.Token
		if v, ok := opt.Header["Connection"]; !ok || v == "" {
			opt.Header["Connection"] = "keep-alive"
		}
		if v, ok := opt.Header["Content-Type"]; !ok || v == "" {
			opt.Header["Content-Type"] = "application/json"
		}
	}
	if opt.Timeout == 0 {
		opt.Timeout = 30 * time.Second
	}
	return opt, nil
}

func HttpClient(method, path string, param, rsp any) error {
	return NewHttpClient(method, path, param, rsp)
}

func NewHttpClient(method, path string, param, rsp any, opts ...f) error {
	c := cfg.LoadCommon().Client
	if c.ClientId == "" || c.Secret == "" {
		return errors.New("缺失客户端ID或密钥信息")
	}

	opt, err := checkOption(opts...)
	if err != nil {
		return err
	}

	reqUrl, _ := url.JoinPath(c.ApiPath, path)
	log.Infof("[Local Client] Call Foradar SAAS, request url->%s, method: %s, param: %v", reqUrl, method, param)
	bs, _ := json.Marshal(param)
	req, err := http.NewRequest(method, reqUrl, bytes.NewReader(bs))
	if err != nil {
		return err
	}

	for k, v := range opt.Header {
		req.Header.Set(k, v)
	}
	clientReq := &http.Client{
		Timeout: opt.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:    100,
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true}, //nolint:gosec,gocritic
		},
	}
	doResp, err := clientReq.Do(req)
	if err != nil {
		return err
	}
	defer doResp.Body.Close()

	doBody, err := io.ReadAll(doResp.Body)
	if err != nil {
		return err
	}

	var doData apiRsp
	if errUnmarshal := json.Unmarshal(doBody, &doData); errUnmarshal != nil {
		return errUnmarshal
	}
	if doData.Code != 0 {
		return fmt.Errorf("code: %d, msg: %s", doData.Code, doData.Message)
	}
	bs, _ = json.Marshal(doData.Data)
	err = json.Unmarshal(bs, rsp)
	return err
}
