package core

import (
	"fmt"
	"net/http"
	"testing"

	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
)

func Test_HttpClient(t *testing.T) {
	cfg.LoadCommon()
	redis.GetInstance(cfg.LoadRedis())

	queryReq := &FofaQueryRequest{
		Qbase64: fmt.Sprintf(`domain=%s`, "baidu.com"),
		Field:   []string{"port", "host"},
		Size:    uint32(100),
	}
	for i := 1; i <= 10; i++ {
		queryReq.Page = uint32(i)
		queryRsp := &FofaQueryResponse{}
		_ = HttpClient(http.MethodPost, "/api/v1/fofa/query", queryReq, queryRsp)
	}
}
