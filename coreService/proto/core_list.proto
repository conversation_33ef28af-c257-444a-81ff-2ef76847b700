syntax = "proto3";

package core;

option go_package = "./proto;core";


message ClueCompanyDropListRequest {
    int64 user_id               = 1;
    int64 operate_company_id    = 2;
}

message ClueCompanyDropListResponse {
    repeated string data = 1;
}

message ClueCompanyClueListRequest {
    int64 operate_company_id    = 1;
    string company_name         = 2;
    int64 user_id               = 3;
    int64 page                  = 4;
    int64 per_page              = 5;
}

message ClueCompanyClueListResponse {
    repeated ClueInfo data =1; 
    
    message ClueInfo {
        int64 id                    = 1;
        int64 user_id               = 2;
        int64 company_id            = 3;
        string content              = 4;
        string clue_company_name    = 5;
        int64 hash                  = 6;
        string punycode_domain      = 7; // 中文域名的国际域名
        int64 type                  = 8; // 线索类型 0：根域；1：证书；2：ICP；3：ICON；4：关键词 5：子域名 6:iP，10：FID
        string created_at           = 9;
        string updated_at           = 10;
    }
}

message ClueCompanyListExportRequest{
    int64 operate_company_id    = 1;
    string company_name         = 2;
    repeated int64 ids          = 3;
    int64 user_id               = 4;
}

message ClueCompanyListExportResponse {
    string url      = 1;
}
message ClueLibCompanyClueOverviewRequest {
    int64 operate_company_id    = 1;
    int64 user_id               = 2;
}

message ClueLibCompanyClueOverviewResponse {
    message Scene_details {
        int64 type      = 1;
        string label    = 2;
        int64 count     = 3;
    }

    message Clue_info {
        int64 scene_type                        = 1; // 0初始线索，1拓展线索，2循环线索
        int64 scene_all_count                   = 2; // 线索总数
        double scene_all_mom                    = 3; // 月环比
        repeated Scene_details scene_details    = 4;
    }

    string company_name = 1;
    repeated Clue_info clue_info = 2;
}