package brokers

import (
	"context"
	"database/sql"
	"github.com/go-micro/plugins/v4/broker/rabbitmq"
	"go-micro.dev/v4"
	"go-micro.dev/v4/broker"
	"go-micro.dev/v4/server"
	"micro-service/coreService/handler/whois"
	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	whoisModel "micro-service/middleware/mysql/whois"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

func NewWhoisBroker() broker.Broker {
	mqConfig := cfg.LoadRabbitMq()
	// 创建 RabbitMQ Broker
	return rabbitmq.NewBroker(
		broker.Addrs(cfg.GetRabbitMqAddr(mqConfig)),           // RabbitMQ访问地址，含VHost
		rabbitmq.ExchangeName(pb.MqExchangeCore),              // 交换机的名称
		rabbitmq.DurableExchange(),                            // 消息在Exchange中时会进行持久化处理
		rabbitmq.PrefetchCount(mqConfig.PrefetchCount),        // 使用配置的预取数量，提高并发性能
	)
}

func RegisterWhoisSubscriber(regSrv server.Server) error {
	// 初始化订阅上下文：这里不是必需的，订阅会有默认值
	subOpts := broker.NewSubscribeOptions(
		rabbitmq.DurableQueue(),   // 队列持久化，消费者断开连接后，消息仍然保存到队列中
		rabbitmq.RequeueOnError(), // 消息处理函数返回error时，消息再次入队列
		rabbitmq.AckOnSuccess(),   // 消息处理函数没有error返回时，go-micro发送Ack给RabbitMQ
	)
	// 注册Subscriber Whois
	if err := micro.RegisterSubscriber(
		pb.RoutingKeyWhois,
		regSrv,
		HandleWhois,
		server.SubscriberContext(subOpts.Context),
		server.SubscriberQueue(pb.QueueWhois),
	); err != nil {
		return err
	}
	return nil
}

func HandleWhois(ctx context.Context, msg interface{}) error {
	switch v := msg.(type) {
	case map[string]interface{}:
		info := v
		whoisInfo := whois.GetWhoisInfoByDomain(ctx, info["domain"].(string))
		if whoisInfo == nil {
			_ = whoisModel.NewWhoisModel().Create(&whoisModel.Whois{
				Domain:           info["domain"].(string),
				RegistrantName:   &sql.NullString{String: info["register"].(string), Valid: true},
				RegistrantEmail:  &sql.NullString{String: info["email"].(string), Valid: true},
				RegistrantMobile: &sql.NullString{String: info["mobile"].(string), Valid: true},
			})
		} else {
			_ = whoisModel.NewWhoisModel().UpdateBy(&whoisModel.Whois{
				Domain:           info["domain"].(string),
				RegistrantName:   &sql.NullString{String: info["register"].(string), Valid: true},
				RegistrantEmail:  &sql.NullString{String: info["email"].(string), Valid: true},
				RegistrantMobile: &sql.NullString{String: info["mobile"].(string), Valid: true},
			}, mysql.WithWhere("id", whoisInfo.Id), mysql.WithWhere("dns = ''"))
		}
	default:
		log.WithContextInfof(ctx, "[Subscriber.Whois]:  异常数据:%v", msg)
	}
	return nil
}
