package fofaee_service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"io"
	"micro-service/pkg/utils"

	"time"

	"github.com/olivere/elastic"
	es "micro-service/middleware/elastic"

	"micro-service/pkg/log"
)

const fofaeeServiceIndexName = "fofaee_service"

type FofaeeServiceModel interface {
	FindByIpPort(l ...es.IpPort) ([]FofaeeService, error)
	FindByIpPortWithUserIdAndRuleTags(ip string, userId uint64) ([]FofaeeService, error)
	Create(ctx context.Context, userId uint64, l []*FofaeeService) (successIds, failedIds []string, err error)
	Upsert(ctx context.Context, userId uint64, l []*FofaeeService) (successIds, failedIds []string, err error)
	Updates(ctx context.Context, userId uint64, list ...*FofaeeService) error
}

type Rule struct {
	Category         string `json:"category"`
	CnCategory       string `json:"cn_category"`
	CnCompany        string `json:"cn_company"`
	CnParentCategory string `json:"cn_parent_category"`
	CnProduct        string `json:"cn_product"`
	Company          string `json:"company"`
	Level            string `json:"level"`
	ParentCategory   string `json:"parent_category"`
	Product          string `json:"product"`
	RuleID           string `json:"rule_id"`
	SoftHard         string `json:"softhard"`
}

type Geo struct {
	CityName      string  `json:"city_name"`
	ContinentCode string  `json:"continent_code"`
	CountryCode2  string  `json:"country_code2"`
	CountryCode3  string  `json:"country_code3"`
	CountryName   string  `json:"country_name"`
	DmaCode       any     `json:"dma_code"` // type: int
	Latitude      float64 `json:"latitude"`
	Location      struct {
		Lat float64 `json:"lat"`
		Lon float64 `json:"lon"`
	} `json:"location"`
	Longitude      float64 `json:"longitude"`
	PostalCode     string  `json:"postal_code"`
	RealRegionName string  `json:"real_region_name"`
	RegionName     string  `json:"region_name"`
	Timezone       string  `json:"timezone"`
}

// FofaeeService
type FofaeeService struct {
	Appserver []string `json:"appserver"`
	Asn       struct {
		AsNumber       int    `json:"as_number"`
		AsOrganization string `json:"as_organization"`
	} `json:"asn"`
	BanLen       int    `json:"ban_len"`
	Banner       string `json:"banner"`
	BaseProtocol string `json:"base_protocol"`
	Cert         string `json:"cert"` // type: string
	Certs        struct {
		CertDate  string   `json:"cert_date"`
		IsValid   bool     `json:"is_valid"`
		ValidType string   `json:"valid_type"`
		IssuerCN  string   `json:"issuer_cn"`
		IssuerCNs []string `json:"issuer_cns"`
		IssuerOrg []string `json:"issuer_org"`
		NotAfter  any      `json:"not_after"`
		NotBefore any      `json:"not_before"`
		SigAlth   string   `json:"sig_alth"`
		Sn        string   `json:"sn"`
		SubjectCN string   `json:"subject_cn"`
		V         string   `json:"v"`
	} `json:"certs"`
	Dbs struct {
		Count   int `json:"Count"`
		DbSize  int `json:"DbSize"`
		Records int `json:"Records"`
	} `json:"dbs"`
	Geoip        Geo      `json:"geoip"`
	Gid          string   `json:"gid"`           // ?
	HoneypotName string   `json:"honeypot_name"` // ?
	Hostnames    []string `json:"hostnames"`
	IP           string   `json:"ip"`
	Ipcnet       string   `json:"ipcnet"`
	IsHoneypot   bool     `json:"is_honeypot"`
	IsIpv6       bool     `json:"is_ipv6"`
	Jarm         struct {
		Group string `json:"group"`
		Hash  string `json:"hash"`
	} `json:"jarm"` // type?
	Language       string         `json:"language"`
	Mac            string         `json:"mac"`
	NetbiosName    string         `json:"netbios_name"`
	Middleware     string         `json:"middleware"`
	Os             []string       `json:"os"`
	Port           int            `json:"port"`
	PortState      string         `json:"port_state"`
	Product        []string       `json:"product"`
	Protocol       string         `json:"protocol"`
	Structinfo     string         `json:"structinfo"`
	Subbody        string         `json:"subbody"`
	Tags           []string       `json:"tags"`
	UserTags       string         `json:"user_tags"` // type?
	RuleTags       []Rule         `json:"rule_tags"`
	Time           time.Time      `json:"time"`
	V              int            `json:"v"`
	Version        []string       `json:"version"`
	UpdatedAt      string         `json:"updated_at"`     // format: YYYY-MM-dd HH:mm:ss
	Lastchecktime  string         `json:"lastchecktime"`  // format: YYYY-MM-dd HH:mm:ss
	Lastupdatetime string         `json:"lastupdatetime"` // format: YYYY-MM-dd HH:mm:ss
	Extra          map[string]any `json:"extra"`          // 扩展字段，包含user_id等信息
}

func (m *FofaeeService) IndexName() string {
	return fofaeeServiceIndexName
}

type defaultFofaeeServiceModel struct {
	*elastic.Client
	Type      string
	indexName string
}

func GenId(userId int, ip string, port int) string {
	return fmt.Sprintf("%d_%s:%d", userId, ip, port)
}

func NewFofeeServiceModel(clients ...*elastic.Client) FofaeeServiceModel {
	return &defaultFofaeeServiceModel{
		Client:    es.GetEsClient(clients...),
		Type:      "service",
		indexName: fofaeeServiceIndexName,
	}
}

func (d *defaultFofaeeServiceModel) FindByIpPort(l ...es.IpPort) ([]FofaeeService, error) {
	if len(l) == 0 {
		return nil, nil
	}

	query := elastic.NewBoolQuery()
	should := make([]elastic.Query, 0, len(l))
	for i := range l {
		must := elastic.NewBoolQuery()
		must.Must(elastic.NewTermQuery("ip.ip_raw", l[i].Ip))
		if l[i].PortInt > 0 {
			must = must.Must(elastic.NewTermQuery("port", l[i].PortInt))
		}
		should = append(should, must)
	}

	query.Should(should...)
	s, _ := query.Source()
	esstr, _ := json.Marshal(s)
	fmt.Println("es query:", string(esstr))

	const keepAlive = "5m"
	do, err := d.Client.Scroll().Index(d.indexName).Query(query).Scroll(keepAlive).Size(3000).Do(context.Background())
	if errors.Is(err, io.EOF) {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	listData := make([]FofaeeService, 0, do.Hits.TotalHits)
	listData = append(listData, marshalHandle(do.Hits.Hits)...)

	scrollId := do.ScrollId
	searchResult := new(elastic.SearchResult)
	for {
		searchResult, err = d.Client.Scroll(keepAlive).ScrollId(scrollId).Do(context.Background())
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) <= 0 {
			break
		}

		listData = append(listData, marshalHandle(searchResult.Hits.Hits)...)
	}

	return listData, err
}

func (d *defaultFofaeeServiceModel) Updates(ctx context.Context, userId uint64, list ...*FofaeeService) error {
	req := d.Client.Bulk()
	for i := range list {
		id := GenId(cast.ToInt(userId), list[i].IP, list[i].Port)
		if list[i].UpdatedAt == "" {
			list[i].UpdatedAt = utils.CurrentTime()
		}
		doc := elastic.NewBulkUpdateRequest().Index(d.indexName).Type(d.Type).Id(id).Doc(list[i])
		req.Add(doc)
	}

	if req.NumberOfActions() <= 0 {
		return nil
	}
	do, err := req.Do(ctx)
	if err != nil {
		return err
	}

	if len(do.Failed()) > 0 {
		for _, v := range do.Failed() {
			log.Warnf("es index_name: %s, doc id: %s update failed: %s\n", v.Index, v.Id, v.Error.Reason)
		}
	}
	return nil
}

func (d *defaultFofaeeServiceModel) Create(ctx context.Context, userId uint64, l []*FofaeeService) (successIds, failedIds []string, err error) {
	req := d.Client.Bulk().Index(d.indexName)
	for i := range l {
		l[i].UpdatedAt = utils.CurrentTime()
		id := GenId(cast.ToInt(userId), cast.ToString(l[i].IP), l[i].Port)
		doc := elastic.NewBulkIndexRequest().Type(d.Type).Id(id).Doc(l[i])
		req.Add(doc)
	}

	if req.NumberOfActions() <= 0 {
		return nil, nil, nil
	}

	resp, err := req.Refresh("true").Do(ctx)
	if err != nil {
		return nil, nil, err
	}

	for _, failed := range resp.Failed() {
		failedIds = append(failedIds, failed.Id)
		s, _ := json.Marshal(failed.Error)
		log.Errorf("es index: %s insert failed, doc id: %s, err: %s\n", d.Index, failed.Id, failed, string(s))
	}
	for _, v := range resp.Succeeded() {
		successIds = append(successIds, v.Id)
	}

	return successIds, failedIds, nil
}

func (d *defaultFofaeeServiceModel) Upsert(ctx context.Context, userId uint64, l []*FofaeeService) (successIds, failedIds []string, err error) {
	bulkReq := d.Client.Bulk()
	for i := range l {
		id := GenId(cast.ToInt(userId), cast.ToString(l[i].IP), l[i].Port)
		l[i].UpdatedAt = utils.CurrentTime()
		doc := elastic.NewBulkUpdateRequest().Index(d.indexName).Type(d.Type).Id(id).Doc(l[i]).DocAsUpsert(true)
		bulkReq.Add(doc)
	}

	if bulkReq.NumberOfActions() <= 0 {
		return nil, nil, nil
	}

	resp, err := bulkReq.Refresh("true").Do(ctx)
	if err != nil {
		return nil, nil, err
	}

	for _, failed := range resp.Failed() {
		failedIds = append(failedIds, failed.Id)
		s, _ := json.Marshal(failed.Error)
		log.Errorf("es index: %s upsert failed, doc id: %s, err: %s\n", d.Index, failed.Id, failed, string(s))
	}
	for _, v := range resp.Succeeded() {
		successIds = append(successIds, v.Id)
	}

	return successIds, failedIds, nil
}

func marshalHandle(hits []*elastic.SearchHit) []FofaeeService {
	var list = make([]FofaeeService, 0, len(hits))
	for _, item := range hits {
		if item == nil || item.Source == nil {
			continue
		}
		b, err := item.Source.MarshalJSON()
		if err != nil {
			continue
		}

		var record FofaeeService
		if err = json.Unmarshal(b, &record); err != nil {
			log.Errorf("ES index name: %s, json.Unmarshal: %+v\n", fofaeeServiceIndexName, err)
			continue
		}
		list = append(list, record)
	}
	return list
}

// FindByIpPortWithUserIdAndRuleTags 根据IP、用户ID查询服务资产，且必须有rule_tags
// 对应PHP: AssetService::query()->where('ip', $res['ip'])->where('extra.user_id',$user_id)->whereNotNull('rule_tags')->get(['rule_tags','port'])->toArray()
func (d *defaultFofaeeServiceModel) FindByIpPortWithUserIdAndRuleTags(ip string, userId uint64) ([]FofaeeService, error) {
	if ip == "" || userId == 0 {
		return nil, nil
	}

	query := elastic.NewBoolQuery()

	// 添加IP条件 - 使用正确的字段名
	query.Must(elastic.NewTermQuery("ip", ip))

	// 添加用户ID条件 - 对应PHP的where('extra.user_id',$user_id)
	query.Must(elastic.NewMatchQuery("extra.user_id", cast.ToString(userId)))

	// 添加rule_tags不为空的条件 - 对应PHP的whereNotNull('rule_tags')
	query.Must(elastic.NewExistsQuery("rule_tags"))

	s, _ := query.Source()
	esstr, _ := json.Marshal(s)
	fmt.Println("fofaee_service es query:", string(esstr))

	const keepAlive = "5m"
	// 只返回rule_tags和port字段 - 对应PHP的get(['rule_tags','port'])
	do, err := d.Client.Scroll().
		Index(d.indexName).
		Query(query).
		Scroll(keepAlive).
		Size(10000).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("rule_tags", "port")).
		Do(context.Background())
	if errors.Is(err, io.EOF) {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	listData := make([]FofaeeService, 0, do.Hits.TotalHits)
	listData = append(listData, marshalHandle(do.Hits.Hits)...)

	scrollId := do.ScrollId
	searchResult := new(elastic.SearchResult)
	for {
		searchResult, err = d.Client.Scroll(keepAlive).ScrollId(scrollId).Do(context.Background())
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) <= 0 {
			break
		}

		listData = append(listData, marshalHandle(searchResult.Hits.Hits)...)
	}

	return listData, err
}
