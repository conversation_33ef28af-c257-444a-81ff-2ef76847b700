package elastic

import (
	"context"
	"fmt"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"strings"
	"testing"
	"time"

	"reflect"

	microZap "github.com/go-micro/plugins/v4/logger/zap"
	"github.com/hashicorp/go-hclog"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"
)

// TestDocument 测试用的文档结构
type TestDocument struct {
	Id        string     `json:"id"`
	Name      string     `json:"name"`
	Age       int        `json:"age"`
	Status    int        `json:"status"`
	Tags      []string   `json:"tags"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

// IndexName 实现索引名称方法
func (d *TestDocument) IndexName() string {
	return "test_documents"
}

// TypeName 实现类型名称方法
func (d *TestDocument) TypeName() string {
	return "_doc"
}

// AllByParamsTestDocument 用于 AllByParams 测试的文档结构
type AllByParamsTestDocument struct {
	TestDocument
}

func (d *AllByParamsTestDocument) IndexName() string {
	return "test_documents_all_by_params"
}

// ListByParamsTestDocument 用于 ListSearchAfterByParams 测试的文档结构
type ListByParamsTestDocument struct {
	TestDocument
}

func (d *ListByParamsTestDocument) IndexName() string {
	return "test_documents_list_by_params"
}

// GetByIdTestDocument 用于 GetById 测试的文档结构
type GetByIdTestDocument struct {
	TestDocument
}

func (d *GetByIdTestDocument) IndexName() string {
	return "test_documents_get_by_id"
}

// FirstTestDocument 用于 First 测试的文档结构
type FirstTestDocument struct {
	TestDocument
}

func (d *FirstTestDocument) IndexName() string {
	return "test_documents_first"
}

// GetCountTestDocument 用于 GetCount 测试的文档结构
type GetCountTestDocument struct {
	TestDocument
}

func (d *GetCountTestDocument) IndexName() string {
	return "test_documents_get_count"
}

// DeleteTestDocument 用于 Delete 测试的文档结构
type DeleteTestDocument struct {
	TestDocument
}

func (d *DeleteTestDocument) IndexName() string {
	return "test_documents_delete"
}

// InsertOrUpdateTestDocument 用于 InsertOrUpdate 测试的文档结构
type InsertOrUpdateTestDocument struct {
	TestDocument
}

func (d *InsertOrUpdateTestDocument) IndexName() string {
	return "test_documents_insert_or_update"
}

type TestComplexQueryDocument struct {
	TestDocument
}

func (d *TestComplexQueryDocument) IndexName() string {
	return "test_complex_query"
}

func init() {
	cfg.InitLoadCfg()
	// 初始化日志
	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// Mysql&Redis
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	_ = es.GetInstance(cfg.LoadElastic())
}

// 准备测试数据
func prepareTestData[T any](t *testing.T) []T {
	// 创建索引
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	exists, err := GetEsClient().IndexExists(indexName).Do(context.TODO())
	assert.NoError(t, err)

	if exists {
		// 删除已存在的索引
		_, err = GetEsClient().DeleteIndex(indexName).Do(context.TODO())
		assert.NoError(t, err)
	}

	// 创建兼容ES 6.X的索引映射
	mapping := `{
		"settings": {
			"number_of_shards": 1,
			"number_of_replicas": 0
		},
		"mappings": {
			"_doc": {
				"properties": {
					"id": { 
						"type": "keyword"
					},
					"name": { 
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword"
							}
						}
					},
					"age": { 
						"type": "integer"
					},
					"status": { 
						"type": "integer"
					},
					"tags": { 
						"type": "keyword"
					},
					"created_at": { 
						"type": "date"
					},
					"updated_at": { 
						"type": "date"
					}
				}
			}
		}
	}`
	_, err = GetEsClient().CreateIndex(indexName).Body(mapping).Do(context.TODO())
	assert.NoError(t, err)

	// 等待索引创建完成
	time.Sleep(2 * time.Second)

	// 创建测试数据
	now := time.Now()

	// 使用反射创建测试数据
	testData := make([]T, 3)
	for i := 0; i < 3; i++ {
		// 创建新的 T 类型实例
		doc := new(T)
		docValue := reflect.ValueOf(doc).Elem()

		// 设置基本字段
		docValue.FieldByName("Id").SetString(fmt.Sprintf("%d", i+1))
		docValue.FieldByName("Name").SetString([]string{
			"test document 1",
			"test document 2",
			"another test",
		}[i])
		docValue.FieldByName("Age").SetInt([]int64{20, 25, 15}[i])
		docValue.FieldByName("Status").SetInt([]int64{1, 1, 0}[i])
		docValue.FieldByName("Tags").Set(reflect.ValueOf([][]string{
			{"test", "document"},
			{"test", "example"},
			{"another"},
		}[i]))
		docValue.FieldByName("CreatedAt").Set(reflect.ValueOf(&now))
		docValue.FieldByName("UpdatedAt").Set(reflect.ValueOf(&now))

		testData[i] = *doc
	}

	// 插入测试数据
	successCount, errCount, lastErrorInfo := InsertOrUpdateWithTime(indexName, "_doc", testData, 500)
	assert.NoError(t, err)
	assert.Equal(t, 3, successCount)
	assert.Equal(t, 0, errCount)
	assert.Equal(t, "", lastErrorInfo)

	// 强制刷新索引
	_, err = GetEsClient().Refresh(indexName).Do(context.TODO())
	assert.NoError(t, err)

	// 等待数据可查询
	time.Sleep(3 * time.Second)

	// 验证数据是否正确插入
	totalCount, err := GetCount(indexName, elastic.NewBoolQuery())
	assert.NoError(t, err)
	assert.Equal(t, int64(3), totalCount, "应该插入3条测试数据")

	return testData
}

// 清理测试数据
func cleanupTestData[T any](t *testing.T) {
	indexName := reflect.ValueOf(new(T)).MethodByName("IndexName").Call([]reflect.Value{})[0].String()
	_, err := GetEsClient().DeleteIndex(indexName).Do(context.TODO())
	assert.NoError(t, err)
}

func TestAllByParams(t *testing.T) {
	// 准备测试数据
	prepareTestData[AllByParamsTestDocument](t)
	defer cleanupTestData[AllByParamsTestDocument](t)

	// 测试用例1：简单条件查询
	t.Run("Simple Query", func(t *testing.T) {
		params := [][]interface{}{
			{"status", 1},
		}
		results, err := AllByParams[AllByParamsTestDocument](10, params, nil)
		assert.NoError(t, err)
		assert.Len(t, results, 2) // 应该返回两条记录
	})

	// 测试用例2：范围查询
	t.Run("Range Query", func(t *testing.T) {
		params := [][]interface{}{
			{"age", ">=", 20},
		}
		results, err := AllByParams[AllByParamsTestDocument](10, params, nil)
		assert.NoError(t, err)
		assert.Len(t, results, 2) // 应该返回两条记录：李四(20)和王五(25)
	})

	// 测试用例3：复杂查询
	t.Run("Complex Query", func(t *testing.T) {
		params := [][]interface{}{
			{"MUST", [][]interface{}{
				{"status", 1},
				{"SHOULD", [][]interface{}{
					{"age", ">=", 20},
					{"tags", "tag1"},
				}},
			}},
		}
		results, err := AllByParams[AllByParamsTestDocument](10, params, nil)
		assert.NoError(t, err)
		assert.Len(t, results, 2) // 应该返回两条记录
	})
}

func TestListSearchAfterByParams(t *testing.T) {
	// 准备测试数据
	prepareTestData[ListByParamsTestDocument](t)
	defer cleanupTestData[ListByParamsTestDocument](t)

	// 设置排序
	sorts := []elastic.Sorter{
		elastic.NewFieldSort("age").Desc(),
		elastic.NewFieldSort("id").Desc(),
	}

	// 测试用例1：分页查询
	t.Run("Pagination", func(t *testing.T) {
		params := [][]interface{}{
			{"status", 1},
		}
		total, results, nextSortValues, err := ListSearchAfterByParams[ListByParamsTestDocument](2, params, sorts, nil)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, results, 2)
		assert.NotEmpty(t, nextSortValues)

		// 测试下一页
		total, results, _, err = ListSearchAfterByParams[ListByParamsTestDocument](2, params, sorts, nextSortValues)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, results, 0) // 没有更多数据
	})

	// 测试用例2：带字段选择的查询
	t.Run("Field Selection", func(t *testing.T) {
		params := [][]interface{}{
			{"status", 1},
		}
		fields := []string{"id", "name", "age"}
		total, results, _, err := ListSearchAfterByParams[ListByParamsTestDocument](10, params, sorts, nil, fields...)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, results, 2)
	})
}

func TestListByParams(t *testing.T) {
	// 准备测试数据
	prepareTestData[ListByParamsTestDocument](t)
	defer cleanupTestData[ListByParamsTestDocument](t)

	// 设置排序
	sorts := []elastic.Sorter{
		elastic.NewFieldSort("age").Desc(),
		elastic.NewFieldSort("id").Desc(),
	}

	// 测试用例1：分页查询
	t.Run("Pagination", func(t *testing.T) {
		params := [][]interface{}{
			{"status", 1},
		}
		total, results, err := ListByParams[ListByParamsTestDocument](1, 2, params, sorts)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, results, 2)

		// 测试下一页
		total, results, err = ListByParams[ListByParamsTestDocument](2, 2, params, sorts)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, results, 0) // 没有更多数据
	})

	// 测试用例2：带字段选择的查询
	t.Run("Field Selection", func(t *testing.T) {
		params := [][]interface{}{
			{"status", 1},
		}
		fields := []string{"id", "name", "age"}
		total, results, err := ListByParams[ListByParamsTestDocument](1, 10, params, sorts, fields...)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, results, 2)
	})
}

func TestGetById(t *testing.T) {
	// 准备测试数据
	testData := prepareTestData[GetByIdTestDocument](t)
	defer cleanupTestData[GetByIdTestDocument](t)

	// 测试用例1：获取存在的文档
	t.Run("Existing Document", func(t *testing.T) {
		doc, err := GetById[GetByIdTestDocument](testData[0].Id)
		assert.NoError(t, err)
		assert.NotNil(t, doc)
		assert.Equal(t, testData[0].Name, doc.Name)
	})

	// 测试用例2：获取不存在的文档
	t.Run("Non-existing Document", func(t *testing.T) {
		doc, err := GetById[GetByIdTestDocument]("non-existing-id")
		assert.Error(t, err)
		assert.Nil(t, doc)
	})
}

func TestFirst(t *testing.T) {
	// 准备测试数据
	prepareTestData[FirstTestDocument](t)
	defer cleanupTestData[FirstTestDocument](t)

	// 测试用例1：简单查询
	t.Run("Simple Query", func(t *testing.T) {
		query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("status", 1))
		sorts := []elastic.Sorter{elastic.NewFieldSort("age").Desc()}
		doc, err := First[FirstTestDocument](query, sorts)
		assert.NoError(t, err)
		assert.NotNil(t, doc)
		assert.Equal(t, 1, doc.Status)
	})

	// 测试用例2：不存在的记录
	t.Run("Non-existing Record", func(t *testing.T) {
		query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("status", 999))
		doc, err := First[FirstTestDocument](query, nil)
		assert.NoError(t, err)
		assert.Nil(t, doc)
	})
}

func TestGetCount(t *testing.T) {
	// 准备测试数据
	testData := prepareTestData[GetCountTestDocument](t)
	defer cleanupTestData[GetCountTestDocument](t)

	// 测试用例1：统计总数
	t.Run("Total Count", func(t *testing.T) {
		query := elastic.NewBoolQuery()
		count, err := GetCount("test_documents_get_count", query)
		assert.NoError(t, err)
		assert.Equal(t, int64(len(testData)), count)
	})

	// 测试用例2：条件统计
	t.Run("Conditional Count", func(t *testing.T) {
		query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("status", 1))
		count, err := GetCount("test_documents_get_count", query)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), count)
	})
}

func TestDelete(t *testing.T) {
	// 准备测试数据
	prepareTestData[DeleteTestDocument](t)
	defer cleanupTestData[DeleteTestDocument](t)

	// 测试用例1：删除符合条件的文档
	t.Run("Delete by Query", func(t *testing.T) {
		query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("status", 1))
		err := Delete[DeleteTestDocument](query)
		assert.NoError(t, err)

		// 验证删除结果
		count, err := GetCount("test_documents_delete", elastic.NewBoolQuery())
		assert.NoError(t, err)
		assert.Equal(t, int64(1), count) // 只剩一条记录
	})
}

func TestInsertOrUpdate(t *testing.T) {
	// 准备测试数据
	var now = time.Now()
	testData := []InsertOrUpdateTestDocument{
		{
			TestDocument: TestDocument{
				Id:        "1",
				Name:      "张三",
				Age:       18,
				Status:    1,
				Tags:      []string{"tag1"},
				CreatedAt: &now,
				UpdatedAt: &now,
			},
		},
	}
	defer cleanupTestData[InsertOrUpdateTestDocument](t)

	// 测试用例1：插入新文档
	t.Run("Insert New Document", func(t *testing.T) {
		successCount, errCount, lastErrorInfo := InsertOrUpdateWithTime("test_documents_insert_or_update", "_doc", testData, 500)
		assert.Equal(t, 0, errCount)
		assert.Equal(t, 1, successCount)
		assert.Equal(t, "", lastErrorInfo)

		// 验证插入结果
		doc, err := GetById[InsertOrUpdateTestDocument](testData[0].Id)
		assert.NoError(t, err)
		assert.NotNil(t, doc)
		assert.Equal(t, testData[0].Name, doc.Name)
	})

	// 测试用例2：更新已存在的文档
	t.Run("Update Existing Document", func(t *testing.T) {
		testData[0].Name = "张三(已更新)"
		successCount, errCount, lastErrorInfo := InsertOrUpdateWithTime("test_documents_insert_or_update", "_doc", testData, 500)
		assert.Equal(t, 0, errCount)
		assert.Equal(t, 1, successCount)
		assert.Equal(t, "", lastErrorInfo)

		// 验证更新结果
		doc, err := GetById[InsertOrUpdateTestDocument](testData[0].Id)
		assert.NoError(t, err)
		assert.NotNil(t, doc)
		assert.Equal(t, "张三(已更新)", doc.Name)
	})
}

func TestComplexQuery(t *testing.T) {
	// 准备测试数据
	_ = prepareTestData[TestComplexQueryDocument](t)
	defer cleanupTestData[TestComplexQueryDocument](t)

	// 测试 MUST_NOT 条件
	t.Run("Test MUST_NOT condition", func(t *testing.T) {
		params := [][]interface{}{
			{"MUST_NOT", [][]interface{}{
				{"age", "<", 18},
				{"status", 0},
			}},
		}
		results, err := AllByParams[TestComplexQueryDocument](10, params, nil)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.Greater(t, len(results), 0)
		for _, doc := range results {
			assert.GreaterOrEqual(t, doc.Age, 18)
			assert.NotEqual(t, doc.Status, 0)
		}
	})

	// 测试带参数的 match 查询
	t.Run("Test match query with parameters", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "match", "test", map[string]interface{}{
				"operator":             "or",
				"minimum_should_match": "1",
				"boost":                2.0,
			}},
		}
		results, err := AllByParams[TestComplexQueryDocument](10, params, nil)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.Greater(t, len(results), 0)
		for _, doc := range results {
			name := strings.ToLower(doc.Name)
			assert.True(t, strings.Contains(name, "test"))
		}
	})

	// 测试带参数的 match_phrase 查询
	t.Run("Test match_phrase query with parameters", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "match_phrase", "test document", map[string]interface{}{
				"slop":  2,
				"boost": 1.5,
			}},
		}
		results, err := AllByParams[TestComplexQueryDocument](10, params, nil)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.Greater(t, len(results), 0)
		for _, doc := range results {
			assert.Contains(t, strings.ToLower(doc.Name), "test")
		}
	})

	// 测试其他查询类型
	t.Run("Test other query types", func(t *testing.T) {
		// 测试 prefix 查询
		prefixParams := [][]interface{}{
			{"name.keyword", "prefix", "test"},
		}
		prefixResults, err := AllByParams[TestComplexQueryDocument](10, prefixParams, nil)
		assert.NoError(t, err)
		assert.NotNil(t, prefixResults)
		assert.Greater(t, len(prefixResults), 0)

		// 测试 regexp 查询
		regexpParams := [][]interface{}{
			{"name.keyword", "regexp", "test.*"},
		}
		regexpResults, err := AllByParams[TestComplexQueryDocument](10, regexpParams, nil)
		assert.NoError(t, err)
		assert.NotNil(t, regexpResults)
		assert.Greater(t, len(regexpResults), 0)

		// 测试 exists 查询 - 查询name字段存在的文档
		existsParams := [][]interface{}{
			{"name", "exists"},
		}
		existsResults, err := AllByParams[TestComplexQueryDocument](10, existsParams, nil)
		assert.NoError(t, err)
		assert.NotNil(t, existsResults)
		assert.Greater(t, len(existsResults), 0)
		// 验证所有结果都有name字段
		for _, doc := range existsResults {
			assert.NotEmpty(t, doc.Name)
		}
	})
}

func TestQueryTypes(t *testing.T) {
	// 准备测试数据
	_ = prepareTestData[TestComplexQueryDocument](t)
	defer cleanupTestData[TestComplexQueryDocument](t)

	// 测试 match 查询（大写）
	t.Run("Test MATCH query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "MATCH", "test"},
		}
		results, err := AllByParams[TestComplexQueryDocument](10, params, nil)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.Greater(t, len(results), 0)
		for _, doc := range results {
			assert.Contains(t, strings.ToLower(doc.Name), "test")
		}
	})

	// 测试 match_phrase 查询（大写）
	t.Run("Test MATCH_PHRASE query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "MATCH_PHRASE", "test document"},
		}
		results, err := AllByParams[TestComplexQueryDocument](10, params, nil)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.Greater(t, len(results), 0)
		for _, doc := range results {
			assert.Contains(t, strings.ToLower(doc.Name), "test")
		}
	})

	// 测试 like 查询（大写）
	t.Run("Test LIKE query", func(t *testing.T) {
		params := [][]interface{}{
			{"name.keyword", "LIKE", "test*"},
		}
		results, err := AllByParams[TestComplexQueryDocument](10, params, nil)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.Greater(t, len(results), 0)
		for _, doc := range results {
			assert.True(t, strings.HasPrefix(strings.ToLower(doc.Name), "test"))
		}
	})

	// 测试 exists 查询（大写）
	t.Run("Test EXISTS query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "EXISTS"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试默认 term 查询
	t.Run("Test default term query", func(t *testing.T) {
		params := [][]interface{}{
			{"status", 1}, // 使用默认的 term 查询
		}
		results, err := AllByParams[TestComplexQueryDocument](10, params, nil)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.Greater(t, len(results), 0)
		for _, doc := range results {
			assert.Equal(t, 1, doc.Status)
		}
	})

	// 测试无效操作符（应该使用默认的 term 查询）
	t.Run("Test invalid operator", func(t *testing.T) {
		params := [][]interface{}{
			{"status", "invalid_operator", 1},
		}
		results, err := AllByParams[TestComplexQueryDocument](10, params, nil)
		assert.NoError(t, err)
		assert.NotNil(t, results)
		assert.Greater(t, len(results), 0)
	})
}

func TestParseQueryDirectly(t *testing.T) {
	// 直接测试 ParseQuery 函数的所有分支，无需依赖 Elasticsearch 环境

	// 测试空参数
	t.Run("Empty params", func(t *testing.T) {
		params := [][]interface{}{}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试长度为0的参数
	t.Run("Zero length param", func(t *testing.T) {
		params := [][]interface{}{
			{}, // 空参数应该被跳过
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 MUST 条件
	t.Run("MUST condition", func(t *testing.T) {
		params := [][]interface{}{
			{"MUST", [][]interface{}{
				{"status", 1},
				{"age", ">=", 18},
			}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 SHOULD 条件
	t.Run("SHOULD condition", func(t *testing.T) {
		params := [][]interface{}{
			{"SHOULD", [][]interface{}{
				{"status", 1},
				{"vip", true},
			}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 MUST_NOT 条件
	t.Run("MUST_NOT condition", func(t *testing.T) {
		params := [][]interface{}{
			{"MUST_NOT", [][]interface{}{
				{"age", "<", 18},
				{"status", 0},
			}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 match 查询（小写）
	t.Run("match query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "match", "test"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 MATCH 查询（大写）
	t.Run("MATCH query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "MATCH", "test"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 match_phrase 查询（小写）
	t.Run("match_phrase query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "match_phrase", "test document"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 MATCH_PHRASE 查询（大写）
	t.Run("MATCH_PHRASE query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "MATCH_PHRASE", "test document"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 like 查询（小写）
	t.Run("like query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "like", "test*"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 LIKE 查询（大写）
	t.Run("LIKE query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "LIKE", "test*"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 prefix 查询（小写）
	t.Run("prefix query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "prefix", "test"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 PREFIX 查询（大写）
	t.Run("PREFIX query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "PREFIX", "test"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 regexp 查询（小写）
	t.Run("regexp query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "regexp", "test.*"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 REGEXP 查询（大写）
	t.Run("REGEXP query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "REGEXP", "test.*"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 exists 查询（小写）
	t.Run("exists query", func(t *testing.T) {
		params := [][]interface{}{
			{"tags", "exists"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 EXISTS 查询（大写）
	t.Run("EXISTS query", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "exists"},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 in 查询（小写）
	t.Run("in query", func(t *testing.T) {
		params := [][]interface{}{
			{"status", "in", []interface{}{1, 2, 3}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试 IN 查询（大写）
	t.Run("IN query", func(t *testing.T) {
		params := [][]interface{}{
			{"status", "IN", []interface{}{1, 2, 3}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试范围查询
	t.Run("Range queries", func(t *testing.T) {
		testCases := []struct {
			name string
			op   string
		}{
			{"Greater than", ">"},
			{"Greater than or equal", ">="},
			{"Less than", "<"},
			{"Less than or equal", "<="},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				params := [][]interface{}{
					{"age", tc.op, 18},
				}
				query := ParseQuery(params)
				assert.NotNil(t, query)
			})
		}
	})

	// 测试默认 term 查询（无操作符，3个参数但操作符无效）
	t.Run("Default term query with invalid operator", func(t *testing.T) {
		params := [][]interface{}{
			{"status", "invalid_operator", 1},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试简单条件（2个参数）
	t.Run("Simple condition", func(t *testing.T) {
		params := [][]interface{}{
			{"status", 1},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试数组值的简单条件
	t.Run("Simple condition with array value", func(t *testing.T) {
		params := [][]interface{}{
			{"status", []interface{}{1, 2, 3}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试带参数的 match 查询
	t.Run("Match query with parameters", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "match", "test", map[string]interface{}{
				"operator":             "and",
				"minimum_should_match": 2,
				"fuzziness":            "AUTO",
				"boost":                2.0,
			}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试带参数的 MATCH 查询（大写）
	t.Run("MATCH query with parameters", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "MATCH", "test", map[string]interface{}{
				"operator":             "or",
				"minimum_should_match": 1,
				"boost":                1.5,
			}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试带参数的 match_phrase 查询
	t.Run("match_phrase query with parameters", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "match_phrase", "test document", map[string]interface{}{
				"slop":  2,
				"boost": 1.5,
			}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试带参数的 MATCH_PHRASE 查询（大写）
	t.Run("MATCH_PHRASE query with parameters", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "MATCH_PHRASE", "test document", map[string]interface{}{
				"slop":  1,
				"boost": 2.0,
			}},
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试非字符串字段名
	t.Run("Non-string field name", func(t *testing.T) {
		params := [][]interface{}{
			{123, "value"}, // 非字符串字段名，应该被跳过
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试非字符串操作符
	t.Run("Non-string operator", func(t *testing.T) {
		params := [][]interface{}{
			{"field", 123, "value"}, // 非字符串操作符，应该被跳过
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试无效的逻辑操作符参数类型
	t.Run("Invalid logical operator param type", func(t *testing.T) {
		params := [][]interface{}{
			{"MUST", "invalid"}, // 非 [][]interface{} 类型，应该被跳过
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试非字符串的逻辑操作符
	t.Run("Non-string logical operator", func(t *testing.T) {
		params := [][]interface{}{
			{123, [][]interface{}{{"status", 1}}}, // 非字符串操作符，应该被跳过
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试无效的 match 参数类型
	t.Run("Invalid match parameters type", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "match", "test", "invalid"}, // 非 map[string]interface{} 类型
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试非字符串的 match 值
	t.Run("Non-string match value", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "match", 123}, // 非字符串值
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试非字符串的 like 值
	t.Run("Non-string like value", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "like", 123}, // 非字符串值
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})

	// 测试非数组的 in 值
	t.Run("Non-array in value", func(t *testing.T) {
		params := [][]interface{}{
			{"status", "in", "not_array"}, // 非数组值
		}
		query := ParseQuery(params)
		assert.NotNil(t, query)
	})
}

func TestCountByParams(t *testing.T) {
	// 准备测试数据
	prepareTestData[AllByParamsTestDocument](t)
	defer cleanupTestData[AllByParamsTestDocument](t)

	// 测试用例1：简单条件统计
	t.Run("Simple Count", func(t *testing.T) {
		params := [][]interface{}{
			{"status", 1},
		}
		count, err := CountByParams[AllByParamsTestDocument](params)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), count)
	})

	// 测试用例2：范围查询统计
	t.Run("Range Count", func(t *testing.T) {
		params := [][]interface{}{
			{"age", ">=", 20},
		}
		count, err := CountByParams[AllByParamsTestDocument](params)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), count)
	})

	// 测试用例3：复杂查询统计
	t.Run("Complex Count", func(t *testing.T) {
		params := [][]interface{}{
			{"MUST", [][]interface{}{
				{"status", 1},
				{"age", ">=", 18},
			}},
		}
		count, err := CountByParams[AllByParamsTestDocument](params)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), count)
	})

	// 测试用例4：无匹配结果的统计
	t.Run("No Match Count", func(t *testing.T) {
		params := [][]interface{}{
			{"status", 999},
		}
		count, err := CountByParams[AllByParamsTestDocument](params)
		assert.NoError(t, err)
		assert.Equal(t, int64(0), count)
	})

	// 测试用例5：全部数据统计
	t.Run("All Data Count", func(t *testing.T) {
		params := [][]interface{}{} // 空条件，统计所有数据
		count, err := CountByParams[AllByParamsTestDocument](params)
		assert.NoError(t, err)
		assert.Equal(t, int64(3), count)
	})

	// 测试用例6：EXISTS查询统计
	t.Run("EXISTS Count", func(t *testing.T) {
		params := [][]interface{}{
			{"name", "exists"},
		}
		count, err := CountByParams[AllByParamsTestDocument](params)
		assert.NoError(t, err)
		assert.Equal(t, int64(3), count) // 所有文档都有name字段
	})

	// 测试用例7：MUST_NOT查询统计
	t.Run("MUST_NOT Count", func(t *testing.T) {
		params := [][]interface{}{
			{"MUST_NOT", [][]interface{}{
				{"age", "<", 18},
			}},
		}
		count, err := CountByParams[AllByParamsTestDocument](params)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), count) // 排除年龄小于18的文档
	})
}

func TestUpdateMany(t *testing.T) {
	prepareTestData[TestDocument](t)
	defer cleanupTestData[TestDocument](t)

	m := map[string]map[string]any{
		"1": {
			"name": "test-update-1",
		},
		"2": {
			"name": "test-update-2",
		},
	}
	td := &TestDocument{}
	err := UpdateMany[TestDocument](context.Background(), td.IndexName(), td.TypeName(), m)
	assert.NoError(t, err)

	// 等待3秒，让ES更新数据
	time.Sleep(3 * time.Second)

	q := elastic.NewIdsQuery().Ids("2", "1")
	docs, err := All[TestDocument](10, q, nil, "id", "name")
	assert.NoError(t, err)
	assert.Equal(t, 2, len(docs))
	for _, doc := range docs {
		if doc.Id == "1" {
			assert.Equal(t, "test-update-1", doc.Name)
		} else if doc.Id == "2" {
			assert.Equal(t, "test-update-2", doc.Name)
		}
	}
}

func TestGetObjectID(t *testing.T) {
	td := &TestDocument{
		Id: "123",
	}
	id := reflect.ValueOf(td).Elem().FieldByName("Id").String()
	assert.Equal(t, "123", id)

	td2 := TestDocument{
		Id: "123",
	}
	id2 := reflect.ValueOf(td2).FieldByName("Id").String()
	assert.Equal(t, "123", id2)

	td3 := TestDocument{}
	id3 := reflect.ValueOf(td3).FieldByName("Id").String()
	assert.Equal(t, "", id3)
}
