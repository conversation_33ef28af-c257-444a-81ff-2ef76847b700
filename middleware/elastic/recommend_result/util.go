package recommend_result

import (
	"time"

	"micro-service/pkg/log"

	"github.com/olivere/elastic"
	"golang.org/x/exp/constraints"
)

// FindCondition 查询条件
type FindCondition struct {
	UserId          int    // value > 0 valid
	TaskId          int    // value > 0 valid
	FakeTaskId      int    // value > 0 valid
	GroupId         int    // value > 0 valid
	Type            int    // value >= 0 valid
	FakeType        int    // value > 0 valid
	Status          int    // value >= 0 valid
	OnlineState     int    // value >= 0 valid
	AssetFrom       int    // value >= 0 valid
	ReasonType      int    // value >= 0 valid
	Flag            string // value not empty valid
	Title           string
	Ip              string
	IpFuzzy         string
	SubdomainFuzzy  string
	Keyword         string
	UrlFuzzy        string
	Ports           []int
	Icon            []int // icon_hash
	Ids             []string
	FakeCompany     []string
	FakeIcpCompany  []string
	Protocol        []string
	Domains         []string
	Subdomains      []string
	SourceUpdatedAt [2]string // Format: YYYY-MM-dd HH:mm:ss
	UpdatedAt       [2]string // same to SourceUpdatedAt
	Source          time.Time
	FakeDeep        bool
}

// NewFindCondition 创建查询条件
func NewFindCondition() *FindCondition {
	return &FindCondition{
		Type:        -1,
		Status:      -1,
		OnlineState: -1,
		AssetFrom:   -1,
		ReasonType:  -1,
		Ids:         make([]string, 0),
		Ports:       make([]int, 0),
		Icon:        make([]int, 0),
		FakeCompany: make([]string, 0),
		Protocol:    make([]string, 0),
		Domains:     make([]string, 0),
	}
}

func BuildQueryByCondition(query *elastic.BoolQuery, param *FindCondition) {
	// 打印查询条件详情
	log.Debugf("构建ES查询条件: UserId=%d, Flag=%s, Status=%d, Keyword=%s, Ip=%s, Domains=%v, Protocol=%v, Ports=%v",
		param.UserId, param.Flag, param.Status, param.Keyword, param.Ip, param.Domains, param.Protocol, param.Ports)

	if len(param.Ids) > 0 {
		query.Must(elastic.NewIdsQuery().Ids(param.Ids...))
	}
	if param.UserId > 0 {
		query.Must(elastic.NewTermQuery("user_id", param.UserId))
	}
	if param.TaskId > 0 {
		query.Must(elastic.NewTermQuery("task_id", param.TaskId))
	}
	if param.FakeTaskId > 0 {
		query.Must(elastic.NewTermQuery("fake_task_id", param.FakeTaskId))
	}
	if param.GroupId > 0 {
		query.Must(elastic.NewTermQuery("group_id", param.GroupId))
	}
	if param.Type >= 0 {
		query.Must(elastic.NewTermQuery("type", param.Type))
	}
	if param.FakeType > 0 {
		query.Must(elastic.NewTermQuery("fake_type", param.FakeType))
	}
	if param.Status >= 0 {
		query.Must(elastic.NewTermQuery("status", param.Status))
	}
	if param.AssetFrom >= 0 {
		query.Must(elastic.NewTermQuery("assets_from", param.AssetFrom))
	}
	if param.ReasonType >= 0 {
		query.Must(elastic.NewTermQuery("reason.type", param.ReasonType))
	}
	if param.Flag != "" {
		query.Must(elastic.NewTermQuery("flag", param.Flag))
	}
	if param.OnlineState >= 0 {
		query.Must(elastic.NewTermQuery("online_state", param.OnlineState > 0))
	}
	if param.Ip != "" {
		query.Must(elastic.NewTermQuery("ip.ip_raw", param.Ip))
	}
	if len(param.Ports) > 0 {
		query.Must(elastic.NewTermsQuery("port", convertAny(param.Ports)...))
	}
	if len(param.Icon) > 0 {
		query.Must(elastic.NewTermsQuery("logo.hash", convertAny(param.Icon)...))
	}
	if len(param.FakeCompany) > 0 {
		query.Must(elastic.NewTermsQuery("fake_company.keyword", convertAny(param.FakeCompany)...))
	}
	if len(param.FakeIcpCompany) > 0 {
		query.Must(elastic.NewTermsQuery("fake_icp_company.keyword", convertAny(param.FakeIcpCompany)...))
	}

	// 检查 Protocol 数组，确保它不是空的或只包含空字符串
	hasValidProtocol := false
	for _, p := range param.Protocol {
		if p != "" {
			hasValidProtocol = true
			break
		}
	}
	if hasValidProtocol {
		query.Must(elastic.NewTermsQuery("protocol", convertAny(param.Protocol)...))
	}

	// 检查 Domains 数组，确保它不是空的或只包含空字符串
	hasValidDomain := false
	for _, d := range param.Domains {
		if d != "" {
			hasValidDomain = true
			break
		}
	}
	if hasValidDomain {
		query.Must(elastic.NewTermsQuery("domain", convertAny(param.Domains)...))
	}

	if len(param.Subdomains) > 0 {
		query.Must(elastic.NewTermsQuery("subdomain", convertAny(param.Subdomains)...))
	}
	if param.SourceUpdatedAt[0] != "" && param.SourceUpdatedAt[1] != "" {
		query.Must(elastic.NewRangeQuery("source_updated_at").Gte(param.SourceUpdatedAt[0]).Lte(param.SourceUpdatedAt[1]))
	}
	if param.UpdatedAt[0] != "" && param.UpdatedAt[1] != "" {
		query.Must(elastic.NewRangeQuery("updated_at").Gte(param.UpdatedAt[0]).Lte(param.UpdatedAt[1]))
	}
	if param.IpFuzzy != "" {
		query.Must(elastic.NewWildcardQuery("ip.ip_raw", fuzzyValue(param.IpFuzzy)))
	}
	if param.Title != "" {
		query.Must(elastic.NewWildcardQuery("title", fuzzyValue(param.Title)))
	}
	if param.UrlFuzzy != "" {
		query.Must(elastic.NewWildcardQuery("url", fuzzyValue(param.UrlFuzzy)))
	}
	if param.UrlFuzzy != "" {
		query.Must(elastic.NewWildcardQuery("url", fuzzyValue(param.UrlFuzzy)))
	}
	if param.FakeDeep {
		query.MustNot(elastic.NewTermQuery("fake_deep", true))
	}
}

func FakeAssetKeywordQuery(query *elastic.BoolQuery, keyword string) {
	fuzzyQuery := elastic.NewBoolQuery()

	keyword = fuzzyValue(keyword)
	fuzzyQuery.Should(elastic.NewWildcardQuery("ip.ip_raw", keyword))
	fuzzyQuery.Should(elastic.NewWildcardQuery("subdomain.keyword", keyword))
	fuzzyQuery.Should(elastic.NewWildcardQuery("fake_company.keyword", keyword))
	fuzzyQuery.Should(elastic.NewWildcardQuery("fake_icp_company.keyword", keyword))
	fuzzyQuery.Should(elastic.NewWildcardQuery("url", keyword))
	fuzzyQuery.Should(elastic.NewWildcardQuery("title", keyword))

	query.Must(fuzzyQuery)
}

func convertAny[T constraints.Ordered](l []T) []any {
	res := make([]any, 0)
	for _, v := range l {
		res = append(res, v)
	}
	return res
}

func fuzzyValue(value string) string {
	return "*" + value + "*"
}
