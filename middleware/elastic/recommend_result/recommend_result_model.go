package recommend_result

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strconv"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"

	es "micro-service/middleware/elastic"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

// 云端资产推荐/钓鱼仿冒资产数据
const (
	indexName = "foradar_recommend_result"
	indexType = "result"
)

const (
	Offline = 0 // 离线
	Online  = 1 // 在线

	TypeFakeTaskFake   = 1 // 钓鱼仿冒
	TypeFakeTaskDomain = 2 // 域名混淆
	TypeFakeTaskOther  = 3 // 其他

	AssetFromDefault = 0 // 资产来源: 默认
	AssetFromImport  = 1 // 资产来源: 第三方资产

	AssetsLevelA = 1 // A级资产
	AssetsLevelB = 2 // B级资产
	AssetsLevelC = 3 // C级资产
	AssetsLevelD = 4 // D级资产

	// 资产可信度级别
	HIGH     = 1
	MIDDLE   = 2
	LOW      = 3
	THREATEN = 4
	//status 0 默认状态 1 已核对 2 不需要核对 当前自动化没用了
	STATUS_DEFAULT       = 0
	STATUS_AUDITED       = 1
	STATUS_NO_NEED_AUDIT = 2
)

// 资产来源 1/FOFA的search/all接口返回的 2/IP138 3/chaziyu 4/hunter 5/线索库域名实时解析 6/FOFA纯解析域名数据 7/扫描任务中域名实时解析出来的，而且不在线索库中的域名 8/列表端口扫描的时候，某个端口上扫出来新的协议数据，类似基础扫描类型
// 9/单位测绘任务下的全端口扫描  10/单位测绘下的常用端口扫描 11/ oneforall
const ASSETS_SOURCE_FOFA = 1
const ASSETS_SOURCE_IP138 = 2
const ASSETS_SOURCE_CHAZIYU = 3
const ASSETS_SOURCE_HUNTER = 4
const ASSETS_SOURCE_DOMAIN_CLUE = 5
const ASSETS_SOURCE_FOFA_PARSE_DOMAIN = 6
const ASSETS_SOURCE_SCAN_ASSETS_DOMAIN = 7
const ASSETS_SOURCE_NEW_PROROCOL = 8
const ASSETS_DETECT_SCAN_FULL_PORT = 9
const ASSETS_DETECT_SCAN_NORMAL_PORT = 10
const ONEFORALL = 11
const VirusTotalQuery = 12
const CensysAPIQuery = 13
const CertSpotterQuery = 14
const CrtshQuery = 15
const GoogleQuery = 16
const AnubisQuery = 17
const BinaryEdgeAPIQuery = 18
const CeBaiduQuery = 19
const ChinazQuery = 20
const ChinazAPIQuery = 21
const CirclAPIQuery = 22
const CloudFlareAPIQuery = 23
const DNSdbAPIQuery = 24
const DNSDumpsterQuery = 25
const HackerTargetQuery = 26
const IP138Query = 27
const IPv4InfoAPIQuery = 28
const QianXunQuery = 29
const GithubAPISearch = 30
const OtherSource = 31
const Altdns = 32
const SecurityTrailsAPIQuery = 33
const RapidDNSQuery = 34
const SiteDossierQuery = 35
const DNSCHECKER = 36

type RecommendResult struct {
	Fid              string            `json:"fid"`
	UserId           int               `json:"user_id"`
	CompanyId        int               `json:"company_id"`
	GroupId          int               `json:"group_id"`
	TaskId           int               `json:"task_id"`      // 资产扫描id
	FakeTaskId       int               `json:"fake_task_id"` // 钓鱼仿冒任务id
	Status           int               `json:"status"`
	Audit            int               `json:"audit"`
	AssetsFrom       int               `json:"assets_from"` // 是否第三方资产: 0否 1是
	FakeType         int               `json:"fake_type"`   // 仿冒数据类型 1:仿冒任务 2:域名混淆 3:其他
	Type             int               `json:"type"`
	Level            int               `json:"level"`
	ThreatenType     int               `json:"threaten_type"`      // 威胁类型
	ThreatenTypeName string            `json:"threaten_type_name"` // 威胁类型名称
	Id               string            `json:"id"`                 // doc id
	Ip               string            `json:"ip"`
	Port             any               `json:"port"` // int, eg: cast.ToInt(port) to parse port value
	GroupName        string            `json:"group_name"`
	Url              string            `json:"url"`
	Screenshot       string            `json:"screenshot"` // 仿冒: url在线则截图
	Protocol         string            `json:"protocol"`
	BaseProtocol     string            `json:"base_protocol"`
	Title            string            `json:"title"`
	Domain           string            `json:"domain"`    // 根域名
	Subdomain        string            `json:"subdomain"` // 子域名
	Cert             string            `json:"cert"`      // 提取的CN O信息
	Icp              string            `json:"icp"`
	FakeIcpCompany   string            `json:"fake_icp_company"` // 钓鱼仿冒域名ICP备案企业
	FakeCompany      string            `json:"fake_company"`     // 仿冒目标
	Server           string            `json:"server"`
	Flag             string            `json:"flag"` // eq es index: recommend record flag value
	CloudName        string            `json:"cloud_name"`
	ISP              string            `json:"isp"`
	CertRaw          string            `json:"cert_raw"` // 证书内容
	Logo             Logo              `json:"logo"`
	Reason           []RecommendReason `json:"reason"`
	CertsValid       bool              `json:"certs_valid"`    // 证书是否有效
	IsIPv6           bool              `json:"is_ipv6"`        // 是否ipv6 true是 false
	IsFakeAssets     bool              `json:"is_fake_assets"` // 是否仿冒资产
	OpenParse        bool              `json:"open_parse"`
	IsCDN            bool              `json:"is_cdn"`            // 是否CDN
	OnlineState      bool              `json:"online_state"`      // 在线状态: true 在线 false 不在线
	SourceUpdatedAt  string            `json:"source_updated_at"` // fofa lastupdatetime, same to CreatedAt
	CreatedAt        string            `json:"created_at"`        // time.Time to string with 2006-01-02 15:04:05 layout
	UpdatedAt        string            `json:"updated_at"`        // same to CreatedAt
	FakeDeep         bool              `json:"fake_deep"`         // 深度发现,仿冒资产
	FakeAssetFrom    string            `json:"fake_asset_from"`   // 仿冒资产来源 fofa hunter quake
	// 添加新字段
	AssetsSource          int      `json:"assets_source"`           // 资产来源
	AllAssetsSource       []int    `json:"all_assets_source"`       // 所有命中的数据源
	OneforallSource       string   `json:"oneforall_source"`        // Oneforall来源
	Cname                 string   `json:"cname"`                   // CNAME记录
	AllCompanyName        []string `json:"all_company_name"`        // 所有公司名称
	ClueCompanyName       []string `json:"clue_company_name"`       // 线索公司名称
	Banner                string   `json:"banner"`                  // Banner信息
	Product               string   `json:"product"`                 // 产品名称
	IpStatus              int      `json:"ip_status"`               // IP状态
	AssetsConfidenceLevel int      `json:"assets_confidence_level"` // 资产可信度级别
}

// Logo 推荐结果Logo结构
type Logo struct {
	Hash    any    `json:"hash"`
	Content string `json:"content"` // logo stote path
}

func NewLogo(hash, content string) Logo {
	hashInt, _ := strconv.Atoi(hash)
	return Logo{Hash: hashInt, Content: content}
}

type RecommendReason struct {
	Id              int    `json:"id"`
	Type            int    `json:"type"`
	Content         string `json:"content"`
	GroupId         int    `json:"group_id"`
	ClueCompanyName string `json:"clue_company_name"`
	Source          int    `json:"source"`
}

func (r *RecommendResult) IndexName() string {
	return indexName
}
func GetDocId(ip, protocol, port, subdomain, flag string, fakeType int) string {
	id := utils.Md5sHash(ip+protocol+port+subdomain+flag, false)
	if fakeType > 0 {
		id = id + strconv.Itoa(fakeType)
	}
	return id
}

type (
	RecommendResultModel interface {
		Create(assets []*RecommendResult) error
		UpdatesAny([]*UpdateAnyParam) error
		FindByID(id string) (*RecommendResult, error)
		FindByIPAndPort(userId int, param []es.IpPort, status int) ([]RecommendResult, error)
		FindByCondition(*FindCondition) ([]*RecommendResult, error)
		FindByPageCondition(page, size int, param *FindCondition) ([]*RecommendResult, int64, error)
		AssetsAgg(*FindCondition) (AggResult, error)
		CountByCondition(*FindCondition) (int64, error)
	}

	defaultRecommendResultModel struct{ *elastic.Client }
)

func IndexName() string {
	return indexName
}

func TypeName() string {
	return indexType
}

func NewRecommendResultModel(conn ...*elastic.Client) RecommendResultModel {
	return &defaultRecommendResultModel{Client: es.GetEsClient(conn...)}
}

func (d *defaultRecommendResultModel) Create(assets []*RecommendResult) error {
	if len(assets) == 0 {
		return nil
	}

	req := d.Client.Bulk().Index(indexName)
	for i := range assets {
		if assets[i].Id == "" {
			port := cast.ToString(assets[i].Port)
			assets[i].Id = GetDocId(assets[i].Ip, assets[i].Protocol, port, assets[i].Subdomain, assets[i].Flag, assets[i].FakeType)
		}
		if assets[i].CreatedAt == "" {
			assets[i].CreatedAt = time.Now().Format(utils.DateTimeLayout)
		}
		assets[i].UpdatedAt = time.Now().Format(utils.DateTimeLayout)

		doc := elastic.NewBulkIndexRequest().Type(indexType).Id(assets[i].Id).Doc(assets[i])
		req.Add(doc)
	}

	resp, err := req.Refresh("true").Do(context.Background())
	if err != nil {
		return err
	}

	failedIds := make([]string, 0, len(resp.Failed()))
	for _, failed := range resp.Failed() {
		failedIds = append(failedIds, failed.Id)
		s, _ := json.Marshal(failed.Error)
		log.Errorf("es index: %s insert failed, doc id: %s, err: %s\n", indexName, failed.Id, failed, string(s))
	}

	return nil
}

type UpdateAnyParam struct {
	Id   string
	Data map[string]any
}

func (d *defaultRecommendResultModel) UpdatesAny(data []*UpdateAnyParam) error {
	if len(data) == 0 {
		return nil
	}

	req := d.Client.Bulk().Index(indexName)
	for i := range data {
		if data[i].Id == "" || len(data[i].Data) == 0 {
			continue
		}
		data[i].Data["updated_at"] = time.Now().Format(utils.DateTimeLayout)
		doc := elastic.NewBulkUpdateRequest().Type(indexType).Id(data[i].Id).Doc(data[i].Data)
		req.Add(doc)
	}
	if req.NumberOfActions() <= 0 {
		return nil
	}

	do, err := req.Do(context.Background())
	if err != nil {
		return err
	}

	if len(do.Failed()) > 0 {
		// var failedIds []string
		for _, v := range do.Failed() {
			fmt.Println(v.Error.Reason)
			log.Errorf("es index: %s update failed, doc id: %s, err: %v\n", indexName, v.Id, v.Error)
		}
	}

	return nil
}

func (d *defaultRecommendResultModel) FindByID(id string) (*RecommendResult, error) {
	get, err := d.Client.Get().Type(indexType).Index(indexName).Id(id).Do(context.Background())
	if err != nil {
		return nil, err
	}

	source, err := get.Source.MarshalJSON()
	if err != nil {
		return nil, err
	}

	var data *RecommendResult
	err = json.Unmarshal(source, &data)
	return data, err
}

func (d *defaultRecommendResultModel) FindByIPAndPort(userId int, list []es.IpPort, status int) ([]RecommendResult, error) {
	query := elastic.NewBoolQuery()
	should := make([]elastic.Query, 0, len(list))
	for _, v := range list {
		must := elastic.NewBoolQuery()
		must.Must(elastic.NewTermQuery("user_id", userId))
		if v.Ip != "" {
			must.Must(elastic.NewTermQuery("ip", v.Ip))
		}
		if v.PortInt > 0 {
			must.Must(elastic.NewTermQuery("port", v.PortInt))
		}
		if status != -1 {
			must.Must(elastic.NewTermQuery("status", status))
		}
		should = append(should, must)
	}

	if len(should) > 0 {
		query.Should(should...)
	} else {
		query.Must(elastic.NewTermQuery("user_id", userId))
		if status != -1 {
			query.Must(elastic.NewTermQuery("status", status))
		}
	}
	s, _ := query.Source()
	src, _ := json.Marshal(s)
	fmt.Println("recommend result es query:", string(src))

	const keepAlive = "5m"
	do, err := d.Client.Scroll().Index(indexName).Query(query).Scroll(keepAlive).Size(3000).Do(context.Background())
	if errors.Is(err, io.EOF) {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	listData := make([]RecommendResult, 0, do.Hits.TotalHits)
	listData = append(listData, searchHitParseFunc(do.Hits.Hits)...)

	scrollId := do.ScrollId
	searchResult := new(elastic.SearchResult)
	for {
		searchResult, err = d.Client.Scroll(keepAlive).ScrollId(scrollId).Do(context.Background())
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) <= 0 {
			break
		}

		listData = append(listData, searchHitParseFunc(searchResult.Hits.Hits)...)
	}

	return listData, err
}

func searchHitParseFunc(hits []*elastic.SearchHit) []RecommendResult {
	var list = make([]RecommendResult, 0, len(hits))
	for _, item := range hits {
		b, err := item.Source.MarshalJSON()
		if err != nil {
			continue
		}

		var record RecommendResult
		if err = json.Unmarshal(b, &record); err != nil {
			continue
		}
		list = append(list, record)
	}

	return list
}

func listConvPointer(assets []RecommendResult) []*RecommendResult {
	var list = make([]*RecommendResult, 0, len(assets))
	for i := range assets {
		list = append(list, &assets[i])
	}
	return list
}

func (d *defaultRecommendResultModel) FindByCondition(param *FindCondition) ([]*RecommendResult, error) {
	query := elastic.NewBoolQuery()
	BuildQueryByCondition(query, param)
	if param.Keyword != "" {
		FakeAssetKeywordQuery(query, param.Keyword)
	}

	s, _ := query.Source()
	src, _ := json.Marshal(s)
	log.Infof("es index: %s query by condition src: %s, params: %+v\n", indexName, string(src), param)

	searchSource := elastic.NewSearchSource().Query(query).Size(3000).Sort("id", true)
	do, err := d.Client.Search().Index(indexName).Type(indexType).
		SearchSource(searchSource).Do(context.Background())
	if err != nil {
		return nil, err
	}

	if len(do.Hits.Hits) == 0 {
		return nil, nil
	}
	list := make([]*RecommendResult, 0, do.Hits.TotalHits)
	list = append(list, listConvPointer(searchHitParseFunc(do.Hits.Hits))...)

	lastValue := do.Hits.Hits[len(do.Hits.Hits)-1].Sort
	for {
		searchAfter := elastic.NewSearchSource().Query(query).Size(3000).SearchAfter(lastValue...).Sort("id", true)
		do, err = d.Client.Search().Index().Index(indexName).Type(indexType).SearchSource(searchAfter).Do(context.Background())
		if err != nil || len(do.Hits.Hits) == 0 {
			break
		}
		list = append(list, listConvPointer(searchHitParseFunc(do.Hits.Hits))...)
		lastValue = do.Hits.Hits[len(do.Hits.Hits)-1].Sort
	}
	return list, err
}

func (d *defaultRecommendResultModel) FindByPageCondition(page, size int, param *FindCondition) ([]*RecommendResult, int64, error) {
	query := elastic.NewBoolQuery()
	BuildQueryByCondition(query, param)
	if param.Keyword != "" {
		FakeAssetKeywordQuery(query, param.Keyword)
	}

	// 获取查询源并打印
	src, err := query.Source()
	if err != nil {
		log.Errorf("Error getting source from query: %v", err)
	} else {
		data, err := json.MarshalIndent(src, "", "  ")
		if err != nil {
			log.Errorf("Error marshaling query to JSON: %v", err)
		} else {
			log.Debugf("ES查询语句 (index: %s): %s", indexName, string(data))
		}
	}

	search := d.Client.Search(indexName).Type(indexType).Query(query).
		Sort("id", true). // 使用与全量查询相同的ID排序，确保数据不丢失
		From(es.GetFrom(page, size)).Size(es.GetSize(size))

	do, err := search.Do(context.Background())
	if err != nil {
		log.Errorf("ES查询执行失败: %v", err)
		return nil, 0, err
	}

	var total = do.Hits.TotalHits
	list := listConvPointer(searchHitParseFunc(do.Hits.Hits))

	// 打印查询结果
	log.Debugf("ES查询结果 (index: %s): 总数=%d, 返回数=%d", indexName, total, len(list))

	return list, total, nil
}

func (d *defaultRecommendResultModel) CountByCondition(param *FindCondition) (int64, error) {
	query := elastic.NewBoolQuery()
	BuildQueryByCondition(query, param)

	count, err := d.Client.Count(indexName).Type(indexType).Query(query).Do(context.Background())
	return count, err
}

type AggResult struct {
	FakeCompany []string
	Domain      []string
	Subdomain   []string
	Port        []string
	Protocol    []string
	IcpCompany  []string
	Icons       []Logo
}

func (d *defaultRecommendResultModel) AssetsAgg(param *FindCondition) (AggResult, error) {
	query := elastic.NewBoolQuery()
	BuildQueryByCondition(query, param)

	aggQuery := d.Client.Search().Index(indexName).Type(indexType).Query(query)
	aggQuery.Aggregation("fake_company", elastic.NewTermsAggregation().Field("fake_company.keyword"))
	aggQuery.Aggregation("domain", elastic.NewTermsAggregation().Field("domain"))
	aggQuery.Aggregation("subdomain", elastic.NewTermsAggregation().Field("subdomain"))
	aggQuery.Aggregation("port", elastic.NewTermsAggregation().Field("port"))
	aggQuery.Aggregation("protocol", elastic.NewTermsAggregation().Field("protocol"))
	aggQuery.Aggregation("fake_icp_company", elastic.NewTermsAggregation().Field("fake_icp_company.keyword"))
	aggQuery.Aggregation("logo", elastic.NewTermsAggregation().Field("logo.hash").
		SubAggregation("content", elastic.NewTermsAggregation().Field("logo.content.keyword"))).Size(1000)

	do, err := aggQuery.Do(context.Background())
	if err != nil {
		return AggResult{}, err
	}

	singleAgg := new(elastic.AggregationBucketKeyItems)
	gd := make(map[string][]string, 0)
	var logoAgg = make([]Logo, 0)
	for aggName, raw := range do.Aggregations {
		if err := json.Unmarshal(*raw, singleAgg); err != nil {
			continue
		}
		buckets := singleAgg.Buckets
		list := make([]string, 0, len(buckets))
		for i := range buckets {
			if aggName != "logo" {
				list = append(list, cast.ToString(buckets[i].Key))
				continue
			}
			content, _ := buckets[i].Aggregations.Terms("content")
			if content != nil {
				for _, v := range content.Buckets {
					logoAgg = append(logoAgg, Logo{
						Hash:    cast.ToInt(buckets[i].Key),
						Content: cast.ToString(v.Key),
					})
				}
			}
		}
		gd[aggName] = list
	}

	aggResult := AggResult{
		FakeCompany: gd["fake_company"],
		Domain:      gd["domain"],
		Subdomain:   gd["subdomain"],
		Port:        gd["port"],
		Protocol:    gd["protocol"],
		IcpCompany:  gd["fake_icp_company"],
		Icons:       logoAgg,
	}
	return aggResult, nil
}

func DistinctAssets(l []RecommendResult) []RecommendResult {
	var list = make([]RecommendResult, 0, len(l)/2)
	m := make(map[string]struct{}, len(l)/2)

	key := ""
	for i := range l {
		key = l[i].Ip + "/" + cast.ToString(l[i].Port) + "/" + l[i].Protocol + "/" + l[i].Subdomain
		if _, ok := m[key]; ok {
			continue
		}
		m[key] = struct{}{}
		list = append(list, l[i])
	}
	return list
}
