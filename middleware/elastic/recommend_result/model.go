package recommend_result

import (
	"context"
	"encoding/json"

	"github.com/olivere/elastic"

	es "micro-service/middleware/elastic"
	"micro-service/pkg/log"
)

type Model struct {
	client *elastic.Client
}

func NewModel(conn ...*elastic.Client) *Model {
	return &Model{client: es.GetEsClient(conn...)}
}

// SearchIPs 根据查询条件获取唯一的IP列表
func (m *Model) SearchIPs(ctx context.Context, query elastic.Query, limit int) ([]string, error) {
	// 创建聚合查询，按IP分组
	agg := elastic.NewTermsAggregation().Field("ip.keyword").Size(10000)

	// 执行查询
	searchResult, err := m.client.Search().
		Index(indexName).
		Type(indexType).
		Query(query).
		Aggregation("ips", agg).
		Size(0).
		Do(ctx)

	if err != nil {
		log.WithContextErrorf(ctx, "SearchIPs 查询失败: %v", err)
		return nil, err
	}

	// 解析聚合结果
	ipAgg, found := searchResult.Aggregations.Terms("ips")
	if !found {
		return []string{}, nil
	}

	// 提取IP列表
	ips := make([]string, 0, len(ipAgg.Buckets))
	for _, bucket := range ipAgg.Buckets {
		ip, ok := bucket.Key.(string)
		if ok {
			ips = append(ips, ip)
			if limit > 0 && len(ips) >= limit {
				break
			}
		}
	}

	return ips, nil
}

// SearchGroupByIP 根据IP分组查询结果
func (m *Model) SearchGroupByIP(ctx context.Context, query elastic.Query) (map[string][]map[string]interface{}, error) {
	// 执行查询，获取所有匹配的文档
	searchResult, err := m.client.Search().
		Index(indexName).
		Type(indexType).
		Query(query).
		Sort("source_updated_at", false). // 按更新时间降序排序
		Size(10000).                      // 设置较大的size以获取所有结果
		Do(ctx)

	if err != nil {
		log.WithContextErrorf(ctx, "SearchGroupByIP 查询失败: %v", err)
		return nil, err
	}

	// 按IP分组
	groupedResults := make(map[string][]map[string]interface{})

	for _, hit := range searchResult.Hits.Hits {
		var result map[string]interface{}
		err := json.Unmarshal(*hit.Source, &result)
		if err != nil {
			log.WithContextErrorf(ctx, "解析搜索结果失败: %v", err)
			continue
		}

		// 添加ID字段
		result["id"] = hit.Id

		// 获取IP
		ip, ok := result["ip"].(string)
		if !ok {
			continue
		}

		// 添加到分组中
		groupedResults[ip] = append(groupedResults[ip], result)
	}

	return groupedResults, nil
}
