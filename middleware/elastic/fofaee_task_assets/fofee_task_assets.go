package fofaee_task_assets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"micro-service/pkg/utils"

	"github.com/spf13/cast"

	es "micro-service/middleware/elastic"

	"github.com/olivere/elastic"

	"micro-service/pkg/log"
)

const fofaeeTaskAssetsIndexName = "fofaee_task_assets"

type FofaeeTaskAssetsModel interface {
	FindByIpPort(l ...es.IpPort) ([]FofaeeTaskAssets, error)
	FindByTaskId(taskId uint64, fields ...string) ([]*FofaeeTaskAssets, error)
	FindByUserIdAndId(ctx context.Context, userId uint64, assetId string) (*FofaeeTaskAssets, error)
	Create(ctx context.Context, userId uint64, l []*FofaeeTaskAssets) (successIds, failedIds []string, err error)
	Upsert(ctx context.Context, userId uint64, l []*FofaeeTaskAssets) (successIds, failedIds []string, err error)
	Updates(ctx context.Context, userId uint64, list ...*FofaeeTaskAssets) error
	DeleteByTaskIds(ctx context.Context, taskIds []uint64) error
}

type PortListCertInfo struct {
	IssuerCn     string   `json:"issuer_cn"`
	SubjectOrg   []string `json:"subject_org"`
	IsMatch      bool     `json:"is_match"`
	SubjectCns   any      `json:"subject_cns"`
	CertDate     string   `json:"cert_date"`
	NotBefore    any      `json:"not_before"`
	IssuerCns    []string `json:"issuer_cns"`
	CertNum      uint     `json:"cert_num"`
	SigAlth      string   `json:"sig_alth"`
	NotAfter     any      `json:"not_after"`
	SubjectCn    string   `json:"subject_cn"`
	IsExpired    bool     `json:"is_expired"`
	IssuerOrg    []string `json:"issuer_org"`
	SubjectNames []string `json:"subject_names"`
	V            string   `json:"v"`
	ValidType    string   `json:"valid_type"`
	Domain       string   `json:"domain"`
	IsValid      bool     `json:"isValid"`
	Sn           string   `json:"sn"`
}

type PortListInfo struct {
	Protocol     string           `json:"protocol"`
	IsHoneypot   bool             `json:"is_honeypot"`
	Port         interface{}      `json:"port"`
	Banner       string           `json:"banner"`
	HoneypotName string           `json:"honeypot_name"`
	Certs        PortListCertInfo `json:"certs"`
}

type TitleListInfo struct {
	Charset      string      `json:"Charset"`
	IsHoneypot   bool        `json:"is_honeypot"`
	Port         interface{} `json:"port"`
	Host         string      `json:"host"`
	IsFraud      bool        `json:"is_fraud"`
	FraudName    string      `json:"fraud_name"`
	Title        string      `json:"title"`
	HoneypotName string      `json:"honeypot_name"`
}

type Rule struct {
	Category         string `json:"category"`
	CnCategory       string `json:"cn_category"`
	CnCompany        string `json:"cn_company"`
	CnParentCategory string `json:"cn_parent_category"`
	CnProduct        string `json:"cn_product"`
	Company          string `json:"company"`
	Level            string `json:"level"`
	ParentCategory   string `json:"parent_category"`
	Product          string `json:"product"`
	RuleID           string `json:"rule_id"`
	SoftHard         string `json:"softhard"`
}

// FofaeeTaskAssets
type FofaeeTaskAssets struct {
	UserId         uint64          `json:"user_id"`
	TaskId         interface{}     `json:"task_id"`
	Protocols      []string        `json:"protocols"`
	State          uint64          `json:"state"`
	OnlineState    uint64          `json:"online_state"`
	IsPhpFill      uint64          `json:"is_php_fill"`
	HostList       []string        `json:"-"`
	TitleList      []TitleListInfo `json:"title_list"`
	Ports          []interface{}   `json:"ports"`
	IsIpv6         bool            `json:"is_ipv6"`
	Rules          []Rule          `json:"rules"`
	Ip             string          `json:"ip"`
	Hosts          []string        `json:"hosts"`
	CompanyId      uint64          `json:"company_id"`
	PortList       []PortListInfo  `json:"port_list"`
	PortSize       uint            `json:"port_size"`
	Lastupdatetime string          `json:"lastupdatetime"` // format: YYYY-MM-dd HH:mm:ss
	Createtime     string          `json:"createtime"`     // format: YYYY-MM-dd HH:mm:ss
	UpdatedAt      string          `json:"updated_at"`     // format: YYYY-MM-dd HH:mm:ss
	Id             string          `json:"id"`
}

type defaultFofaeeTaskAssetsModel struct {
	*elastic.Client
	Type      string
	indexName string
}

func (f *FofaeeTaskAssets) IndexName() string {
	return fofaeeTaskAssetsIndexName
}

func (f *FofaeeTaskAssets) TypeName() string {
	return "ips"
}

func GenId(userId int, ip string) string {
	return fmt.Sprintf("%d_%s", userId, ip)
}

func NewFofaeeTaskAssetsModel(clients ...*elastic.Client) FofaeeTaskAssetsModel {
	return &defaultFofaeeTaskAssetsModel{
		Client:    es.GetEsClient(clients...),
		Type:      "ips",
		indexName: fofaeeTaskAssetsIndexName,
	}
}

func (d *defaultFofaeeTaskAssetsModel) FindByIpPort(l ...es.IpPort) ([]FofaeeTaskAssets, error) {
	if len(l) == 0 {
		return nil, nil
	}

	query := elastic.NewBoolQuery()
	should := make([]elastic.Query, 0, len(l))
	for i := range l {
		must := elastic.NewBoolQuery()
		must.Must(elastic.NewTermQuery("ip.ip_raw", l[i].Ip))
		if l[i].PortInt > 0 {
			must = must.Must(elastic.NewTermQuery("port", l[i].PortInt))
		}
		should = append(should, must)
	}

	query.Should(should...)
	s, _ := query.Source()
	esstr, _ := json.Marshal(s)
	fmt.Println("es query:", string(esstr))

	const keepAlive = "5m"
	do, err := d.Client.Scroll().Index(d.indexName).Query(query).Scroll(keepAlive).Size(3000).Do(context.Background())
	if errors.Is(err, io.EOF) {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}

	listData := make([]FofaeeTaskAssets, 0, do.Hits.TotalHits)
	listData = append(listData, marshalHandle(do.Hits.Hits)...)

	scrollId := do.ScrollId
	searchResult := new(elastic.SearchResult)
	for {
		searchResult, err = d.Client.Scroll(keepAlive).ScrollId(scrollId).Do(context.Background())
		if errors.Is(err, io.EOF) {
			err = nil
			break
		}
		if err != nil || len(searchResult.Hits.Hits) <= 0 {
			break
		}

		listData = append(listData, marshalHandle(searchResult.Hits.Hits)...)
	}

	return listData, err
}

func (d *defaultFofaeeTaskAssetsModel) FindByTaskId(taskId uint64, fields ...string) ([]*FofaeeTaskAssets, error) {
	query := elastic.NewBoolQuery()

	// 直接使用字符串精确匹配
	query.Must(elastic.NewTermQuery("task_id", fmt.Sprintf("%d", taskId)))

	// 使用scroll方式获取所有数据
	scroll := d.Client.Scroll(d.indexName).Query(query).Size(1000)
	if len(fields) > 0 {
		scroll = scroll.FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...))
	}

	var allAssets []*FofaeeTaskAssets

	for {
		searchResult, err := scroll.Do(context.Background())
		if err != nil {
			if err == io.EOF {
				break // 没有更多数据
			}
			fmt.Printf("fofaee_task_assets FindByTaskId scroll error: %v\n", err)
			return nil, err
		}

		if len(searchResult.Hits.Hits) == 0 {
			break
		}

		// 解析这一批数据
		batchAssets := ParseHitsValue[FofaeeTaskAssets](searchResult.Hits.Hits)
		allAssets = append(allAssets, batchAssets...)
	}

	return allAssets, nil
}

func (d *defaultFofaeeTaskAssetsModel) Updates(ctx context.Context, userId uint64, list ...*FofaeeTaskAssets) error {
	req := d.Client.Bulk()
	for i := range list {
		id := GenId(cast.ToInt(userId), list[i].Ip)
		if list[i].UpdatedAt == "" {
			list[i].UpdatedAt = utils.CurrentTime()
		}
		doc := elastic.NewBulkUpdateRequest().Index(d.indexName).Type(d.Type).Id(id).Doc(list[i])
		req.Add(doc)
	}

	if req.NumberOfActions() <= 0 {
		return nil
	}
	do, err := req.Do(ctx)
	if err != nil {
		return err
	}

	if len(do.Failed()) > 0 {
		for _, v := range do.Failed() {
			log.Warnf("es index_name: %s, doc id: %s update failed: %s\n", v.Index, v.Id, v.Error.Reason)
		}
	}
	return nil
}

func (d *defaultFofaeeTaskAssetsModel) Create(ctx context.Context, userId uint64, l []*FofaeeTaskAssets) (successIds, failedIds []string, err error) {
	req := d.Client.Bulk().Index(d.indexName)
	for i := range l {
		l[i].UpdatedAt = utils.CurrentTime()
		id := GenId(cast.ToInt(userId), cast.ToString(l[i].Ip))
		doc := elastic.NewBulkIndexRequest().Type(d.Type).Id(id).Doc(l[i])
		req.Add(doc)
	}

	if req.NumberOfActions() <= 0 {
		return nil, nil, nil
	}

	resp, err := req.Refresh("true").Do(ctx)
	if err != nil {
		return nil, nil, err
	}

	for _, failed := range resp.Failed() {
		failedIds = append(failedIds, failed.Id)
		s, _ := json.Marshal(failed.Error)
		log.Errorf("es index: %s insert failed, doc id: %s, err: %s\n", d.Index, failed.Id, failed, string(s))
	}
	for _, v := range resp.Succeeded() {
		successIds = append(successIds, v.Id)
	}

	return successIds, failedIds, nil
}

func (d *defaultFofaeeTaskAssetsModel) Upsert(ctx context.Context, userId uint64, l []*FofaeeTaskAssets) (successIds, failedIds []string, err error) {
	bulkReq := d.Client.Bulk()
	for i := range l {
		id := GenId(cast.ToInt(userId), cast.ToString(l[i].Ip))
		l[i].UpdatedAt = utils.CurrentTime()
		doc := elastic.NewBulkUpdateRequest().Index(d.indexName).Type(d.Type).Id(id).Doc(l[i]).DocAsUpsert(true)
		bulkReq.Add(doc)
	}

	if bulkReq.NumberOfActions() <= 0 {
		return nil, nil, nil
	}

	resp, err := bulkReq.Refresh("true").Do(ctx)
	if err != nil {
		return nil, nil, err
	}

	for _, failed := range resp.Failed() {
		failedIds = append(failedIds, failed.Id)
		s, _ := json.Marshal(failed.Error)
		log.Errorf("es index: %s upsert failed, doc id: %s, err: %s\n", d.Index, failed.Id, failed, string(s))
	}
	for _, v := range resp.Succeeded() {
		successIds = append(successIds, v.Id)
	}

	return successIds, failedIds, nil
}

func marshalHandle(hits []*elastic.SearchHit) []FofaeeTaskAssets {
	var list = make([]FofaeeTaskAssets, 0, len(hits))
	for i, item := range hits {
		if item == nil || item.Source == nil {
			log.Errorf("【ES解析调试】第%d条记录为空", i)
			continue
		}
		b, err := item.Source.MarshalJSON()
		if err != nil {
			log.Errorf("【ES解析调试】第%d条记录源数据序列化失败: %v, 文档ID: %s", i, err, item.Id)
			continue
		}

		var record FofaeeTaskAssets
		if err = json.Unmarshal(b, &record); err != nil {
			jsonStr := string(b)
			if len(jsonStr) > 200 {
				jsonStr = jsonStr[:200] + "..."
			}
			log.Errorf("【ES解析调试】第%d条记录解析失败: %v, 文档ID: %s, 索引: %s, 原始JSON: %s",
				i, err, item.Id, item.Index, jsonStr)
			continue
		}
		list = append(list, record)
	}
	log.Infof("【ES解析调试】总记录数: %d, 成功解析数: %d", len(hits), len(list))
	return list
}

func ParseHitsValue[T any](l []*elastic.SearchHit) []*T {
	var list = make([]*T, 0, len(l))
	for i := range l {
		if l[i] == nil || l[i].Source == nil {
			fmt.Printf("【ES解析调试】第%d条记录为空\n", i)
			continue
		}
		var record T
		err := json.Unmarshal(*l[i].Source, &record)
		if err != nil {
			fmt.Printf("【ES解析调试】第%d条记录解析失败: %v, 原始数据: %s\n", i, err, string(*l[i].Source))
			continue
		}
		list = append(list, &record)
	}
	fmt.Printf("【ES解析调试】总记录数: %d, 成功解析数: %d\n", len(l), len(list))
	return list
}

func (d *defaultFofaeeTaskAssetsModel) DeleteByTaskIds(ctx context.Context, taskIds []uint64) error {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermsQuery("task_id", taskIds))
	_, err := d.Client.DeleteByQuery(d.indexName).Size(500).Type(d.Type).Query(query).Do(ctx)
	return err
}

// FindByUserIdAndId 根据用户ID和资产ID安全查询任务资产，防止越权访问
func (d *defaultFofaeeTaskAssetsModel) FindByUserIdAndId(ctx context.Context, userId uint64, assetId string) (*FofaeeTaskAssets, error) {
	if userId == 0 || assetId == "" {
		return nil, fmt.Errorf("用户ID和资产ID不能为空")
	}

	// 构建查询条件：必须同时匹配用户ID和文档ID
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))

	// 使用文档ID查询
	getResult, err := d.Client.Get().
		Index(d.indexName).
		Type(d.Type).
		Id(assetId).
		Do(ctx)

	if err != nil {
		if elastic.IsNotFound(err) {
			log.Infof("任务资产不存在，ID: %s", assetId)
			return nil, nil
		}
		log.Errorf("查询任务资产失败: %v", err)
		return nil, err
	}

	if !getResult.Found {
		log.Infof("任务资产不存在，ID: %s", assetId)
		return nil, nil
	}

	// 解析文档
	var asset FofaeeTaskAssets
	if err := json.Unmarshal(*getResult.Source, &asset); err != nil {
		log.Errorf("解析任务资产数据失败: %v", err)
		return nil, err
	}

	// 验证用户ID是否匹配，防止越权访问
	if asset.UserId != userId {
		log.Warnf("用户ID不匹配，拒绝访问。请求用户ID: %d, 资产用户ID: %d, 资产ID: %s",
			userId, asset.UserId, assetId)
		return nil, nil // 返回nil而不是错误，避免泄露资产存在信息
	}

	log.Infof("成功查询到任务资产，用户ID: %d, 资产ID: %s, IP: %s",
		userId, assetId, asset.Ip)

	return &asset, nil
}
