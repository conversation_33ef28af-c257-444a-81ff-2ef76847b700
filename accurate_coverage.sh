#!/bin/bash

# 更准确的覆盖率计算脚本 - 只测试指定的文件
#
# 使用方法:
#   ./accurate_coverage.sh                    # 使用默认并行参数
#   ./accurate_coverage.sh -parallel 10 -p 4 # 手动指定并行参数
#   ./accurate_coverage.sh --help             # 显示帮助信息

# 解析命令行参数
CUSTOM_PARALLEL=""
CUSTOM_PROCESS=""
DEBUG_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -parallel)
            CUSTOM_PARALLEL="$2"
            shift 2
            ;;
        -p)
            CUSTOM_PROCESS="$2"
            shift 2
            ;;
        --debug)
            DEBUG_MODE=true
            shift
            ;;
        --help|-h)
            echo "使用方法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  -parallel N    设置并行测试数量 (默认: 自动检测)"
            echo "  -p N          设置并行进程数量 (默认: 自动检测)"
            echo "  --debug       启用调试模式，显示详细的测试命令"
            echo "  --help, -h    显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                    # 使用自动检测的并行参数"
            echo "  $0 -parallel 20 -p 4 # 手动指定并行参数"
            echo "  $0 --debug           # 启用调试模式"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

echo -e "\e[44m========== 准确的指定文件覆盖率统计 ==========\e[0m"

# 指定要测试的Go文件列表
TARGET_FILES=(
    # # middleware/elastic files
    # "middleware/elastic/fofaee_service/fofaee_service.go"
    # "middleware/elastic/fofaee_subdomain/fofee_subdomain.go"
    # "middleware/elastic/fofaee_task_assets/fofee_task_assets.go"
    # "middleware/elastic/foradar_assets/foradar_assets.go"
    # "middleware/elastic/recommend_result/model.go"
    # "middleware/elastic/recommend_result/recommend_result_model.go"
    # "middleware/elastic/recommend_result/util.go"
    
    # # middleware/mysql files
    # "middleware/mysql/black_forbidden_domains/domains.go"
    # "middleware/mysql/black_keyword_system/keyword.go"
    # "middleware/mysql/black_keyword_system/where.go"
    # "middleware/mysql/black_keyword_type/where.go"
    # "middleware/mysql/clue_black_keyword/clue_black_keyword.go"
    # "middleware/mysql/clue_task/clue_task.go"
    # "middleware/mysql/clues/clues.go"
    # "middleware/mysql/company/company.go"
    # "middleware/mysql/cron_task_pocs/cron_task_pocs.go"
    # "middleware/mysql/detect_assets_tasks/detect_assets_tasks.go"
    # "middleware/mysql/domain_assets/domain_assets.go"
    # "middleware/mysql/domain_historys/domain_historys_model.go"
    # "middleware/mysql/dsl.go"
    # "middleware/mysql/foradar_report/foradar_report.go"
    # "middleware/mysql/forbid/ip.go"
    # "middleware/mysql/geo_areas/geo_areas.go"
    # "middleware/mysql/ip_history/ip_history.go"
    # "middleware/mysql/organization_company_clues/organization_company_clues.go"
    # "middleware/mysql/organization_discover_task/organization_discover_task.go"
    # "middleware/mysql/port_group/define_port.go"
    # "middleware/mysql/port_group/port.go"
    # "middleware/mysql/port_group/port_group.go"
    # "middleware/mysql/report_template/report_template.go"
    # "middleware/mysql/scan_crontab_tasks/scan_crontab_tasks.go"
    # "middleware/mysql/scan_crontab_tasks_ips/scan_crontab_tasks_ips.go"
    # "middleware/mysql/scan_crontab_tasks_ports/scan_crontab_tasks_ports.go"
    # "middleware/mysql/scan_task/model.go"
    # "middleware/mysql/scan_task_domains/scan_task_domains.go"
    # "middleware/mysql/sensitive_data/sensitive_data.go"
    # "middleware/mysql/sensitive_keyword/sensitive_keyword.go"
    # "middleware/mysql/task/task_host.go"
    # "middleware/mysql/task/task_model.go"
    # "middleware/mysql/task/task_port.go"
    # "middleware/mysql/util.go"
    # "middleware/mysql/website_message/message.go"
    # "middleware/mysql/website_message/notification_policy.go"
    
    # # pkg files - 只包含你指定的2个文件
    # "webService/asyncq/handlers/icp_query_job.go"
    # "webService/asyncq/handlers/laravel_job_helper.go"
    # "webService/asyncq/job_factory.go"
    # "webService/asyncq/laravel_job.go"
    # "webService/asyncq/php_serializer.go"
    # "pkg/clues/clue.go"
    # "pkg/network/ip.go"
    # "pkg/network/network.go"
    # "pkg/network/range.go"
    # "pkg/utils/file.go"
    # "pkg/utils/ip.go"
    # "pkg/utils/ipv6.go"
    # "pkg/utils/list.go"
    # "pkg/utils/operator.go"
    # "pkg/utils/string.go"
    # "pkg/utils/utils.go"

    # pkg/asyncq files
    "webService/asyncq/handlers/detect_direct_operate_job.go"
    "webService/asyncq/handlers/detect_golang_clues_job.go"
    "webService/asyncq/handlers/expand_clues_job.go"
    "webService/asyncq/handlers/extract_asset_clues_job.go"
    "webService/asyncq/handlers/recommend_asset_handler.go"
    "webService/asyncq/handlers/scan_asset_job.go"
    "webService/asyncq/handlers/oneforall_job.go"
    "webService/asyncq/handlers/update_assets_level.go"
    "webService/asyncq/handlers/update_assets_level_code.go"
    "webService/asyncq/handlers/table_assets_domains_sync_job.go"
    "webService/asyncq/handlers/statistics_login_assets_job.go"
    "webService/asyncq/handlers/update_ip_and_port_asset_company_name.go"
    "webService/asyncq/handlers/update_ip_and_port_offline_state_job.go"
    "webService/asyncq/handlers/update_ip_and_port_online_state_job.go"
    "webService/asyncq/handlers/scan_foradar_asset_handler.go"
    "webService/asyncq/handlers/create_detect_task_job.go"
    

    # pkg files
    "pkg/fofa/fofa.go"
    "pkg/fofa/hunter.go"
    "pkg/github/oneforall.go"

    
    # # webService/handler files
    # "webService/handler/cloud_recommend/delete_recommend_result.go"
    # "webService/handler/cloud_recommend/group_ip_results.go"
    # "webService/handler/cloud_recommend/results.go"
    # "webService/handler/cloud_recommend/update_confidence_level.go"
    # "webService/handler/detect_assets/assets_result.go"
    # "webService/handler/detect_assets/company.go"
    # "webService/handler/detect_assets/detect_assets.go"
    # "webService/handler/detect_assets/es_data.go"
    # "webService/handler/detect_assets/import_clues.go"
    # "webService/handler/detect_assets/ip_detail.go"
    # "webService/handler/detect_assets/report.go"
    # "webService/handler/detect_assets/task_relate.go"
    # "webService/handler/detect_assets/utils.go"
    # "webService/handler/domain_assets/domain_assets.go"
    # "webService/handler/forbid/ip.go"
    # "webService/handler/ignore_assets/ignore_assets.go"
    # "webService/handler/ignore_assets/ignore_passets.go"
    # "webService/handler/ip_asset/company_name.go"
    # "webService/handler/port_group/port_group.go"
    # "webService/handler/scan_crontab/scan_crontab.go"
    # "webService/handler/scan_task/create_task.go"
    # "webService/handler/scan_task/scan_task.go"
    # "webService/handler/scan_task/task_detail.go"
    # "webService/handler/scan_task/task_result.go"
    # "webService/handler/scan_task/task_result_analyse.go"
    # "webService/handler/scan_task/task_result_condition.go"
    # "webService/handler/scan_task/task_result_export.go"
    # "webService/handler/table_assets/assets_import.go"
    # "webService/handler/table_assets/statistics.go"
    # "webService/handler/threaten_assets/threaten_assets.go"
    # "webService/handler/threaten_assets/threaten_passets.go"
    # "webService/handler/unsure_assets/passets.go"
    # "webService/handler/unsure_assets/unsure_assets.go"
    
    # # webService/handler/asset_account files (除了system_detect目录)
    # "webService/handler/asset_account/account.go"
    # "webService/handler/asset_account/book_data.go"
    # "webService/handler/asset_account/ip.go"
    # "webService/handler/asset_account/ip_passets.go"
    # "webService/handler/asset_account/util.go"
    # "webService/handler/asset_account/cert_assets/cert_assets.go"
    # "webService/handler/asset_account/domain_assets/domain_assets.go"
    # "webService/handler/asset_account/domain_assets/utils.go"
    # "webService/handler/asset_account/login_assets/login_assets.go"
)

# 统计变量
TOTAL_FILES=0
TESTED_PACKAGES=0
FAILED_PACKAGES=0
SKIPPED_FILES=0
TOTAL_CODE_LINES=0
COVERED_CODE_LINES=0

# 记录失败的文件
FAILED_TEST_PACKAGES=()
RETRY_FAILED_FILES=()

# 输出文件
REPORT_FILE="coverage_visual_report_$(date +%Y%m%d_%H%M%S).txt"
VISUAL_OUTPUT=""

# 记录开始时间
START_TIME=$(date +%s)
START_TIME_READABLE=$(date '+%Y-%m-%d %H:%M:%S')

echo -e "\e[1;36m>>> 开始准确计算指定文件覆盖率...\e[0m"
echo -e "开始时间: $START_TIME_READABLE"
echo -e "目标文件总数: ${#TARGET_FILES[@]}"

# 检测CI/CD环境
if [[ -n "$CI" || -n "$GITLAB_CI" || -n "$JENKINS_URL" ]]; then
    echo -e "\e[1;33m>>> 检测到CI/CD环境，启用兼容模式\e[0m"
    CI_MODE=true
    # 在CI/CD环境中增加超时时间
    DEFAULT_TIMEOUT=60
else
    echo -e "\e[1;33m>>> 本地开发环境\e[0m"
    CI_MODE=false
    DEFAULT_TIMEOUT=30
fi

# 输出环境信息
echo "Go版本: $(go version)"
echo "工作目录: $(pwd)"
echo "CI模式: $CI_MODE"

# 初始化输出文件
cat > "$REPORT_FILE" << EOF
========================================
单元测试覆盖率可视化报告
生成时间: $(date '+%Y-%m-%d %H:%M:%S')
========================================

目标文件总数: ${#TARGET_FILES[@]}

EOF

function get_package_path() {
    local file_path="$1"
    local dir_path=$(dirname "$file_path")

    # 特殊处理：在CI/CD环境中，对pkg/utils包使用相对路径
    if [[ "$CI_MODE" == true && "$dir_path" == "pkg/utils" ]]; then
        echo "./pkg/utils"
    else
        echo "micro-service/$dir_path"
    fi
}

function get_alternative_package_path() {
    local file_path="$1"
    local dir_path=$(dirname "$file_path")

    # 在CI/CD环境中，尝试不同的路径格式
    if [[ "$CI_MODE" == true ]]; then
        # 返回相对路径格式
        echo "./$dir_path"
    else
        echo "micro-service/$dir_path"
    fi
}

function optimize_parallel_settings() {
    # 如果用户手动指定了参数，使用用户指定的值
    if [[ -n "$CUSTOM_PARALLEL" && -n "$CUSTOM_PROCESS" ]]; then
        PARALLEL_COUNT="$CUSTOM_PARALLEL"
        PROCESS_COUNT="$CUSTOM_PROCESS"
        echo "使用用户指定的并行参数: -parallel $PARALLEL_COUNT -p $PROCESS_COUNT"
        return
    fi

    # 获取CPU核心数
    local cpu_cores
    if command -v nproc >/dev/null 2>&1; then
        cpu_cores=$(nproc)
    elif [[ -f /proc/cpuinfo ]]; then
        cpu_cores=$(grep -c ^processor /proc/cpuinfo)
    elif command -v sysctl >/dev/null 2>&1; then
        cpu_cores=$(sysctl -n hw.ncpu 2>/dev/null || echo "4")
    else
        cpu_cores=4
    fi

    # 根据CPU核心数和环境调整并行参数
    if [[ "$CI_MODE" == true ]]; then
        # CI/CD环境：保守设置，避免资源竞争
        PARALLEL_COUNT=$((cpu_cores * 2))
        PROCESS_COUNT=$((cpu_cores / 2))
        [[ $PROCESS_COUNT -lt 1 ]] && PROCESS_COUNT=1
        [[ $PROCESS_COUNT -gt 4 ]] && PROCESS_COUNT=4
        [[ $PARALLEL_COUNT -gt 8 ]] && PARALLEL_COUNT=8
    else
        # 本地环境：更激进的并行设置
        PARALLEL_COUNT=$((cpu_cores * 4))
        PROCESS_COUNT=$cpu_cores
        [[ $PROCESS_COUNT -gt 8 ]] && PROCESS_COUNT=8
        [[ $PARALLEL_COUNT -gt 32 ]] && PARALLEL_COUNT=32
    fi

    echo "检测到CPU核心数: $cpu_cores"
    echo "自动优化并行参数: -parallel $PARALLEL_COUNT -p $PROCESS_COUNT"
}

# 优化并行参数
optimize_parallel_settings

function get_test_file() {
    local go_file="$1"
    local base_name=$(basename "$go_file" .go)
    local dir_path=$(dirname "$go_file")
    echo "${dir_path}/${base_name}_test.go"
}

function count_code_lines() {
    local go_file="$1"
    # 统计有效代码行数（排除空行和注释行）
    grep -v '^\s*$' "$go_file" | grep -v '^\s*//' | grep -v '^\s*/\*' | grep -v '^\s*\*' | wc -l
}

function generate_progress_bar() {
    local percentage="$1"
    local bar_length=20
    local filled_length=$(awk "BEGIN {printf \"%.0f\", $percentage * $bar_length / 100}")
    local empty_length=$((bar_length - filled_length))

    local bar=""
    local color=""

    # 根据覆盖率选择颜色
    if (( $(echo "$percentage >= 80" | bc -l) )); then
        color="\e[42m"  # 绿色背景
    elif (( $(echo "$percentage >= 60" | bc -l) )); then
        color="\e[43m"  # 黄色背景
    elif (( $(echo "$percentage >= 40" | bc -l) )); then
        color="\e[45m"  # 紫色背景
    else
        color="\e[41m"  # 红色背景
    fi

    # 生成填充部分
    for ((i=0; i<filled_length; i++)); do
        bar="${bar}${color} \e[0m"
    done

    # 生成空白部分
    for ((i=0; i<empty_length; i++)); do
        bar="${bar}\e[47m \e[0m"
    done

    echo "[$bar]"
}

function generate_simple_progress_bar() {
    local percentage="$1"
    local bar_length=20
    local filled_length=$(awk "BEGIN {printf \"%.0f\", $percentage * $bar_length / 100}")
    local empty_length=$((bar_length - filled_length))

    local bar=""

    # 生成填充部分 (使用█字符)
    for ((i=0; i<filled_length; i++)); do
        bar="${bar}█"
    done

    # 生成空白部分 (使用░字符)
    for ((i=0; i<empty_length; i++)); do
        bar="${bar}░"
    done

    # 根据覆盖率添加标识
    local status=""
    if (( $(echo "$percentage >= 80" | bc -l) )); then
        status="🟢"  # 绿色圆点
    elif (( $(echo "$percentage >= 60" | bc -l) )); then
        status="🟡"  # 黄色圆点
    elif (( $(echo "$percentage >= 40" | bc -l) )); then
        status="🟠"  # 橙色圆点
    else
        status="🔴"  # 红色圆点
    fi

    echo "$status [$bar]"
}

# 第一步：统计所有文件的代码行数
echo -e "\n\e[1;33m>>> 第一步：统计所有文件代码行数...\e[0m"
echo "=== 代码行数统计 ===" >> "$REPORT_FILE"

for file in "${TARGET_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo -e "\e[31m[SKIP] 文件不存在: $file\e[0m"
        echo "[SKIP] 文件不存在: $file" >> "$REPORT_FILE"
        ((SKIPPED_FILES++))
        continue
    fi

    # 统计该文件的代码行数
    file_lines=$(count_code_lines "$file")
    TOTAL_CODE_LINES=$((TOTAL_CODE_LINES + file_lines))
    echo "  $file: $file_lines 行"
    echo "$file: $file_lines 行" >> "$REPORT_FILE"
done

echo -e "\e[1;36m总代码行数: $TOTAL_CODE_LINES 行\e[0m"
echo -e "\n总代码行数: $TOTAL_CODE_LINES 行\n" >> "$REPORT_FILE"

# 第二步：逐个文件测试并计算覆盖率
echo -e "\n\e[1;33m>>> 第二步：逐个文件测试并计算覆盖率...\e[0m"
echo "=== 覆盖率测试结果 ===" >> "$REPORT_FILE"

for file in "${TARGET_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        continue
    fi
    
    test_file=$(get_test_file "$file")
    if [[ ! -f "$test_file" ]]; then
        echo -e "\e[33m[SKIP] 测试文件不存在: $test_file\e[0m"
        echo "[SKIP] 测试文件不存在: $test_file" >> "$REPORT_FILE"
        continue
    fi
    
    package_path=$(get_package_path "$file")
    file_lines=$(count_code_lines "$file")
    
    echo -n "测试文件 $file ($file_lines 行) ... "
    
    # 使用coverprofile获取详细覆盖率信息
    temp_coverage="/tmp/coverage_$(basename "$file" .go).out"

    # 先尝试运行测试，将输出重定向到文本文件
    # 在CI/CD环境中使用更长的超时时间，并添加并行参数
    timeout_duration="${DEFAULT_TIMEOUT}s"

    # 创建测试输出文件
    test_output_file="/tmp/test_output_$(basename "$file" .go)_$(date +%s).log"

    # 调试模式：显示完整的测试命令
    if [[ "$DEBUG_MODE" == true ]]; then
        echo "    [DEBUG] 执行命令: go test \"$package_path\" -covermode=set -coverprofile=\"$temp_coverage\" -timeout \"$timeout_duration\" -parallel \"$PARALLEL_COUNT\" -p \"$PROCESS_COUNT\""
        echo "    [DEBUG] 测试输出将保存到: $test_output_file"
    fi

    # 运行测试并将所有输出重定向到文件
    go test "$package_path" -covermode=set -coverprofile="$temp_coverage" -timeout "$timeout_duration" -parallel "$PARALLEL_COUNT" -p "$PROCESS_COUNT" > "$test_output_file" 2>&1
    test_result=$?

    # 读取测试输出（用于后续的缓存检查等）
    test_output=$(cat "$test_output_file" 2>/dev/null || echo "")

    # 如果在CI/CD环境中测试失败或覆盖率文件为空，尝试备用路径
    if [[ "$CI_MODE" == true && ($test_result -ne 0 || ! -s "$temp_coverage") ]]; then
        echo -n "[重试备用路径] "
        alternative_package_path=$(get_alternative_package_path "$file")
        temp_coverage_alt="${temp_coverage}.alt"
        alt_output_file="/tmp/alt_test_output_$(basename "$file" .go)_$(date +%s).log"

        # 运行备用路径测试并重定向输出
        go test "$alternative_package_path" -covermode=set -coverprofile="$temp_coverage_alt" -timeout "$timeout_duration" -parallel "$PARALLEL_COUNT" -p "$PROCESS_COUNT" > "$alt_output_file" 2>&1
        test_result_alt=$?

        # 如果备用路径成功且有覆盖率数据，使用备用结果
        if [[ $test_result_alt -eq 0 && -s "$temp_coverage_alt" ]]; then
            test_result=$test_result_alt
            mv "$temp_coverage_alt" "$temp_coverage"
            # 更新测试输出文件
            mv "$alt_output_file" "$test_output_file"
            test_output=$(cat "$test_output_file" 2>/dev/null || echo "")
            echo -n "[备用路径成功] "
        else
            rm -f "$temp_coverage_alt" "$alt_output_file"
        fi
    fi

    # 特殊处理：对于job类文件，优先检查覆盖率文件
    is_job_file=false
    if [[ "$file" == *"job.go" ]] || [[ "$file" == *"handler.go" ]] || [[ "$package_path" == *"handlers"* ]] || [[ "$package_path" == *"asyncq"* ]]; then
        is_job_file=true
    fi

    # 改进判断逻辑：
    # 1. 如果测试成功，直接认为成功
    # 2. 如果测试失败但有覆盖率数据，也认为成功（处理CI/CD环境差异）
    # 3. 特别处理pkg/utils等包，即使测试失败但有覆盖率数据
    # 4. 处理测试被跳过或缓存的情况
    has_coverage_data=false
    if [[ -f "$temp_coverage" && -s "$temp_coverage" && $(grep -c "$(basename "$file")" "$temp_coverage" 2>/dev/null) -gt 0 ]]; then
        has_coverage_data=true
    fi

    if [[ $test_result -eq 0 ]] || [[ "$has_coverage_data" == true ]]; then
        if [[ -f "$temp_coverage" && -s "$temp_coverage" ]]; then
            # 从覆盖率文件中提取该文件的覆盖信息
            if grep -q "$(basename "$file")" "$temp_coverage" 2>/dev/null; then
                # 使用go tool cover计算该特定文件的覆盖率
                # 创建一个只包含目标文件的临时覆盖率文件
                temp_single_file="/tmp/single_file_coverage.out"
                head -1 "$temp_coverage" > "$temp_single_file"  # 复制mode行
                grep "$(basename "$file")" "$temp_coverage" >> "$temp_single_file"

                if [[ -s "$temp_single_file" ]]; then
                    # 计算单个文件的覆盖率
                    single_file_coverage=$(go tool cover -func="$temp_single_file" 2>/dev/null | tail -1)

                    if [[ "$single_file_coverage" =~ ([0-9]+\.[0-9]+)% ]]; then
                        file_coverage_percent="${BASH_REMATCH[1]}"
                    else
                        file_coverage_percent="0.0"
                    fi

                    rm -f "$temp_single_file"

                    if [[ -n "$file_coverage_percent" && "$file_coverage_percent" =~ ^[0-9]+\.?[0-9]*$ ]]; then
                        file_covered_lines=$(awk "BEGIN {printf \"%.0f\", $file_lines * $file_coverage_percent / 100}")
                        COVERED_CODE_LINES=$((COVERED_CODE_LINES + file_covered_lines))

                        # 生成可视化进度条
                        progress_bar=$(generate_progress_bar "$file_coverage_percent")
                        simple_bar=$(generate_simple_progress_bar "$file_coverage_percent")

                        echo -e "\e[32m${file_coverage_percent}% ${progress_bar} (${file_covered_lines}/${file_lines}行)\e[0m"
                        echo "    ${simple_bar} ${file_coverage_percent}% (${file_covered_lines}/${file_lines}行)"

                        # 写入文件
                        echo "${simple_bar} ${file_coverage_percent}% (${file_covered_lines}/${file_lines}行) - $file" >> "$REPORT_FILE"
                        ((TESTED_PACKAGES++))
                    else
                        # 如果无法提取文件级覆盖率，使用包级覆盖率
                        coverage_line=$(go tool cover -func="$temp_coverage" 2>/dev/null | tail -1)
                        if [[ "$coverage_line" =~ ([0-9]+\.[0-9]+)% ]]; then
                            coverage_percent="${BASH_REMATCH[1]}"
                            file_covered_lines=$(awk "BEGIN {printf \"%.0f\", $file_lines * $coverage_percent / 100}")
                            COVERED_CODE_LINES=$((COVERED_CODE_LINES + file_covered_lines))

                            # 生成可视化进度条
                            progress_bar=$(generate_progress_bar "$coverage_percent")
                            simple_bar=$(generate_simple_progress_bar "$coverage_percent")

                            echo -e "\e[33m${coverage_percent}% ${progress_bar} (${file_covered_lines}/${file_lines}行) [包级别]\e[0m"
                            echo "    ${simple_bar} ${coverage_percent}% (${file_covered_lines}/${file_lines}行) [包级别]"

                            # 写入文件
                            echo "${simple_bar} ${coverage_percent}% (${file_covered_lines}/${file_lines}行) [包级别] - $file" >> "$REPORT_FILE"
                            ((TESTED_PACKAGES++))
                        else
                            echo -e "\e[33m无法解析覆盖率\e[0m"
                            echo "[WARN] 无法解析覆盖率 - $file" >> "$REPORT_FILE"
                        fi
                    fi
                else
                    echo -e "\e[33m文件未在覆盖率报告中\e[0m"
                    echo "[WARN] 文件未在覆盖率报告中 - $file" >> "$REPORT_FILE"
                fi
            else
                echo -e "\e[33m文件未被覆盖\e[0m"
                echo "[WARN] 文件未被覆盖 - $file" >> "$REPORT_FILE"
            fi
        else
            echo -e "\e[33m无覆盖率数据\e[0m"
            echo "[WARN] 无覆盖率数据 - $file" >> "$REPORT_FILE"
        fi
        
        # 清理临时文件
        [[ -f "$temp_coverage" ]] && rm -f "$temp_coverage"
    else
        # 在CI/CD环境中，即使测试失败也检查是否有覆盖率数据
        if [[ "$has_coverage_data" == true ]]; then
            if [[ "$CI_MODE" == true ]]; then
                echo -e "\e[33m CI/CD环境：测试失败但有覆盖率数据\e[0m"
            else
                echo -e "\e[33m包测试失败但目标文件有覆盖率数据\e[0m"
            fi

            # 使用go tool cover计算该特定文件的覆盖率
            temp_single_file="/tmp/single_file_coverage.out"
            head -1 "$temp_coverage" > "$temp_single_file"
            grep "$(basename "$file")" "$temp_coverage" >> "$temp_single_file"

            if [[ -s "$temp_single_file" ]]; then
                single_file_coverage=$(go tool cover -func="$temp_single_file" 2>/dev/null | tail -1)

                if [[ "$single_file_coverage" =~ ([0-9]+\.[0-9]+)% ]]; then
                    file_coverage_percent="${BASH_REMATCH[1]}"
                else
                    file_coverage_percent="0.0"
                fi

                rm -f "$temp_single_file"

                if [[ -n "$file_coverage_percent" && "$file_coverage_percent" =~ ^[0-9]+\.?[0-9]*$ ]]; then
                    file_covered_lines=$(awk "BEGIN {printf \"%.0f\", $file_lines * $file_coverage_percent / 100}")
                    COVERED_CODE_LINES=$((COVERED_CODE_LINES + file_covered_lines))

                    # 生成可视化进度条
                    progress_bar=$(generate_progress_bar "$file_coverage_percent")
                    simple_bar=$(generate_simple_progress_bar "$file_coverage_percent")

                    if [[ "$CI_MODE" == true ]]; then
                        echo -e "\e[32m${file_coverage_percent}% ${progress_bar} (${file_covered_lines}/${file_lines}行) [CI/CD]\e[0m"
                        echo "    ${simple_bar} ${file_coverage_percent}% (${file_covered_lines}/${file_lines}行) [CI/CD]"
                        echo "${simple_bar} ${file_coverage_percent}% (${file_covered_lines}/${file_lines}行) [CI/CD] - $file" >> "$REPORT_FILE"
                    else
                        echo -e "\e[32m${file_coverage_percent}% ${progress_bar} (${file_covered_lines}/${file_lines}行) [覆盖率恢复]\e[0m"
                        echo "    ${simple_bar} ${file_coverage_percent}% (${file_covered_lines}/${file_lines}行) [覆盖率恢复]"
                        echo "${simple_bar} ${file_coverage_percent}% (${file_covered_lines}/${file_lines}行) [覆盖率恢复] - $file" >> "$REPORT_FILE"
                    fi
                    ((TESTED_PACKAGES++))
                else
                    echo -e "\e[31m测试失败\e[0m"
                    # 从保存的测试输出文件中读取前50行错误信息
                    error_output=$(head -50 "$test_output_file" 2>/dev/null || echo "无法读取错误信息")
                    echo -e "\e[33m    错误: $error_output\e[0m"
                    echo "[FAIL] 测试失败 - $file" >> "$REPORT_FILE"
                    echo "    错误: $error_output" >> "$REPORT_FILE"

                    # 记录失败的文件用于重试
                    RETRY_FAILED_FILES+=("$file")
                    FAILED_TEST_PACKAGES+=("$package_path")
                    ((FAILED_PACKAGES++))
                fi
            else
                echo -e "\e[31m测试失败\e[0m"
                # 从保存的测试输出文件中读取前50行错误信息
                error_output=$(head -50 "$test_output_file" 2>/dev/null || echo "无法读取错误信息")
                echo -e "\e[33m    错误: $error_output\e[0m"
                echo "[FAIL] 测试失败 - $file" >> "$REPORT_FILE"
                echo "    错误: $error_output" >> "$REPORT_FILE"

                # 记录失败的文件用于重试
                RETRY_FAILED_FILES+=("$file")
                FAILED_TEST_PACKAGES+=("$package_path")
                ((FAILED_PACKAGES++))
            fi
        else
            echo -e "\e[31m测试失败\e[0m"
            # 从保存的测试输出文件中读取前50行错误信息
            error_output=$(head -50 "$test_output_file" 2>/dev/null || echo "无法读取错误信息")
            echo -e "\e[33m    错误: $error_output\e[0m"
            echo "[FAIL] 测试失败 - $file" >> "$REPORT_FILE"
            echo "    错误: $error_output" >> "$REPORT_FILE"

            # 记录失败的文件用于重试
            RETRY_FAILED_FILES+=("$file")
            FAILED_TEST_PACKAGES+=("$package_path")
            ((FAILED_PACKAGES++))
        fi
    fi

    # 清理当前文件的测试输出文件
    # [[ -f "$test_output_file" ]] && rm -f "$test_output_file"

    ((TOTAL_FILES++))
done

# 第三步：重试失败的测试文件
if [[ ${#RETRY_FAILED_FILES[@]} -gt 0 ]]; then
    echo -e "\n\e[1;33m>>> 第三步：重试失败的测试文件...\e[0m"
    echo "=== 重试失败的测试 ===" >> "$REPORT_FILE"
    echo -e "重试 ${#RETRY_FAILED_FILES[@]} 个失败的文件"

    RETRY_SUCCESS_COUNT=0
    FINAL_FAILED_FILES=()

    for file in "${RETRY_FAILED_FILES[@]}"; do
        package_path=$(get_package_path "$file")
        file_lines=$(count_code_lines "$file")

        echo -n "重试测试文件 $file ($file_lines 行) ... "

        # 使用coverprofile获取详细覆盖率信息
        temp_coverage="/tmp/retry_coverage_$(basename "$file" .go).out"

        # 重试测试，增加超时时间和并行参数，并重定向输出
        # 在重试时也使用相同的路径逻辑
        retry_package_path=$(get_package_path "$file")
        retry_output_file="/tmp/retry_test_output_$(basename "$file" .go)_$(date +%s).log"

        # 运行重试测试并将输出重定向到文件
        go test "$retry_package_path" -covermode=set -coverprofile="$temp_coverage" -timeout 60s -parallel "$PARALLEL_COUNT" -p "$PROCESS_COUNT" > "$retry_output_file" 2>&1
        test_result=$?

        # 读取测试输出
        test_output=$(cat "$retry_output_file" 2>/dev/null || echo "")

        if [[ $test_result -eq 0 ]]; then
            if [[ -f "$temp_coverage" && -s "$temp_coverage" ]]; then
                # 从覆盖率文件中提取该文件的覆盖信息
                if grep -q "$(basename "$file")" "$temp_coverage" 2>/dev/null; then
                    # 使用go tool cover计算该特定文件的覆盖率
                    temp_single_file="/tmp/retry_single_file_coverage.out"
                    head -1 "$temp_coverage" > "$temp_single_file"
                    grep "$(basename "$file")" "$temp_coverage" >> "$temp_single_file"

                    if [[ -s "$temp_single_file" ]]; then
                        single_file_coverage=$(go tool cover -func="$temp_single_file" 2>/dev/null | tail -1)

                        if [[ "$single_file_coverage" =~ ([0-9]+\.[0-9]+)% ]]; then
                            file_coverage_percent="${BASH_REMATCH[1]}"
                        else
                            file_coverage_percent="0.0"
                        fi

                        rm -f "$temp_single_file"

                        if [[ -n "$file_coverage_percent" && "$file_coverage_percent" =~ ^[0-9]+\.?[0-9]*$ ]]; then
                            file_covered_lines=$(awk "BEGIN {printf \"%.0f\", $file_lines * $file_coverage_percent / 100}")
                            COVERED_CODE_LINES=$((COVERED_CODE_LINES + file_covered_lines))

                            # 生成可视化进度条
                            progress_bar=$(generate_progress_bar "$file_coverage_percent")
                            simple_bar=$(generate_simple_progress_bar "$file_coverage_percent")

                            echo -e "\e[32m重试成功 ${file_coverage_percent}% ${progress_bar} (${file_covered_lines}/${file_lines}行)\e[0m"
                            echo "    ${simple_bar} ${file_coverage_percent}% (${file_covered_lines}/${file_lines}行) [重试成功]"

                            # 写入文件
                            echo "${simple_bar} ${file_coverage_percent}% (${file_covered_lines}/${file_lines}行) [重试成功] - $file" >> "$REPORT_FILE"

                            ((TESTED_PACKAGES++))
                            ((RETRY_SUCCESS_COUNT++))
                            ((FAILED_PACKAGES--))  # 减少失败计数

                            # 从失败列表中移除
                            UPDATED_FAILED_PACKAGES=()
                            for failed_pkg in "${FAILED_TEST_PACKAGES[@]}"; do
                                if [[ "$failed_pkg" != "$package_path" ]]; then
                                    UPDATED_FAILED_PACKAGES+=("$failed_pkg")
                                fi
                            done
                            FAILED_TEST_PACKAGES=("${UPDATED_FAILED_PACKAGES[@]}")
                        else
                            echo -e "\e[31m重试失败：无法解析覆盖率\e[0m"
                            FINAL_FAILED_FILES+=("$file")
                        fi
                    else
                        echo -e "\e[31m重试失败：无覆盖率数据\e[0m"
                        FINAL_FAILED_FILES+=("$file")
                    fi
                else
                    echo -e "\e[31m重试失败：文件未在覆盖率报告中\e[0m"
                    FINAL_FAILED_FILES+=("$file")
                fi
            else
                echo -e "\e[31m重试失败：无覆盖率文件\e[0m"
                FINAL_FAILED_FILES+=("$file")
            fi

            # 清理临时文件
            [[ -f "$temp_coverage" ]] && rm -f "$temp_coverage"
        else
            echo -e "\e[31m重试失败\e[0m"
            # 从保存的重试输出文件中读取前50行错误信息
            retry_error_output=$(head -50 "$retry_output_file" 2>/dev/null || echo "无法读取重试错误信息")
            echo -e "\e[33m    重试错误: $retry_error_output\e[0m"
            echo "[RETRY FAIL] 重试失败 - $file" >> "$REPORT_FILE"
            echo "    重试错误: $retry_error_output" >> "$REPORT_FILE"
            FINAL_FAILED_FILES+=("$file")
        fi

        # 清理重试的测试输出文件
        [[ -f "$retry_output_file" ]] && rm -f "$retry_output_file"
    done

    echo -e "\n\e[1;36m>>> 重试结果汇总:\e[0m"
    echo -e "重试文件数: ${#RETRY_FAILED_FILES[@]}"
    echo -e "\e[32m重试成功: $RETRY_SUCCESS_COUNT\e[0m"
    echo -e "\e[31m最终失败: ${#FINAL_FAILED_FILES[@]}\e[0m"

    # 更新失败文件列表
    RETRY_FAILED_FILES=("${FINAL_FAILED_FILES[@]}")
fi

# 计算真实的整体覆盖率
REAL_COVERAGE=0
if [[ $TOTAL_CODE_LINES -gt 0 ]]; then
    REAL_COVERAGE=$(awk "BEGIN {printf \"%.2f\", $COVERED_CODE_LINES * 100 / $TOTAL_CODE_LINES}")
fi

echo -e "\n\e[1;33m>>> 最终统计汇总:\e[0m"
echo -e "目标文件总数: ${#TARGET_FILES[@]}"
echo -e "有效测试文件: $TOTAL_FILES"
echo -e "\e[32m成功测试文件: $TESTED_PACKAGES\e[0m"
echo -e "\e[31m失败测试文件: $FAILED_PACKAGES\e[0m"
echo -e "\e[33m跳过文件: $SKIPPED_FILES\e[0m"

echo -e "\n\e[1;36m>>> 代码行数统计:\e[0m"
echo -e "总代码行数: $TOTAL_CODE_LINES 行"
echo -e "已覆盖行数: $COVERED_CODE_LINES 行"
echo -e "未覆盖行数: $((TOTAL_CODE_LINES - COVERED_CODE_LINES)) 行"

echo -e "\n\e[1;32m>>> 真实整体覆盖率: ${REAL_COVERAGE}%\e[0m"

# 计算测试文件覆盖率
if [[ ${#TARGET_FILES[@]} -gt 0 ]]; then
    TEST_COVERAGE_RATE=$(awk "BEGIN {printf \"%.1f\", $TOTAL_FILES * 100 / ${#TARGET_FILES[@]}}")
    echo -e "\e[1;35m测试文件覆盖率: ${TEST_COVERAGE_RATE}%\e[0m"
fi

# 输出CI/CD兼容的覆盖率格式
echo ""
echo "=========================================="
echo "CI/CD Coverage Report"
echo "=========================================="
echo "total: (statements) ${REAL_COVERAGE}%"
echo "files: ${#ALL_FILES[@]} total, $TOTAL_FILES with tests"
echo "packages: ${#PACKAGES_ARRAY[@]} total, $TESTED_PACKAGES tested successfully"
echo "failed: ${#RETRY_FAILED_FILES[@]} files still failing after retry"
echo "duration: ${TOTAL_DURATION}s (${FORMATTED_DURATION})"
echo "=========================================="

# 列出失败的文件
if [[ ${#RETRY_FAILED_FILES[@]} -gt 0 ]]; then
    echo -e "\n\e[1;31m>>> 重试后仍然失败的文件 (${#RETRY_FAILED_FILES[@]}个):\e[0m"
    for failed_file in "${RETRY_FAILED_FILES[@]}"; do
        echo -e "\e[31m  $failed_file\e[0m"
    done
else
    echo -e "\n\e[32m>>> 所有测试文件都成功了！\e[0m"
fi

# 计算总耗时（在写入报告文件之前）
END_TIME=$(date +%s)
END_TIME_READABLE=$(date '+%Y-%m-%d %H:%M:%S')
TOTAL_DURATION=$((END_TIME - START_TIME))

# 格式化耗时显示
format_duration() {
    local duration=$1
    local hours=$((duration / 3600))
    local minutes=$(((duration % 3600) / 60))
    local seconds=$((duration % 60))

    if [[ $hours -gt 0 ]]; then
        echo "${hours}小时${minutes}分钟${seconds}秒"
    elif [[ $minutes -gt 0 ]]; then
        echo "${minutes}分钟${seconds}秒"
    else
        echo "${seconds}秒"
    fi
}

FORMATTED_DURATION=$(format_duration $TOTAL_DURATION)

# 生成完整的统计报告到文件
cat >> "$REPORT_FILE" << EOF

=== 最终统计汇总 ===
目标文件总数: ${#TARGET_FILES[@]}
有效测试文件: $TOTAL_FILES
成功测试文件: $TESTED_PACKAGES
失败测试文件: $FAILED_PACKAGES
跳过文件: $SKIPPED_FILES

=== 代码行数统计 ===
总代码行数: $TOTAL_CODE_LINES 行
已覆盖行数: $COVERED_CODE_LINES 行
未覆盖行数: $((TOTAL_CODE_LINES - COVERED_CODE_LINES)) 行

=== 覆盖率统计 ===
真实整体覆盖率: ${REAL_COVERAGE}%
测试文件覆盖率: ${TEST_COVERAGE_RATE}%

=== 执行耗时统计 ===
开始时间: $START_TIME_READABLE
结束时间: $END_TIME_READABLE
总耗时: $FORMATTED_DURATION (${TOTAL_DURATION}秒)

EOF

# 添加失败的文件列表
if [[ ${#RETRY_FAILED_FILES[@]} -gt 0 ]]; then
    echo "=== 重试后仍然失败的文件列表 ===" >> "$REPORT_FILE"
    for failed_file in "${RETRY_FAILED_FILES[@]}"; do
        echo "$failed_file" >> "$REPORT_FILE"
    done
    echo "" >> "$REPORT_FILE"
fi

echo "=== 覆盖率等级说明 ===" >> "$REPORT_FILE"
echo "🟢 绿色圆点: 80%+ (优秀)" >> "$REPORT_FILE"
echo "🟡 黄色圆点: 60-79% (良好)" >> "$REPORT_FILE"
echo "🟠 橙色圆点: 40-59% (一般)" >> "$REPORT_FILE"
echo "🔴 红色圆点: <40% (需要改进)" >> "$REPORT_FILE"
echo "█ = 已覆盖代码, ░ = 未覆盖代码" >> "$REPORT_FILE"

echo -e "\n\e[1;36m>>> 准确覆盖率计算完成!\e[0m"
echo -e "\e[1;32m>>> 执行耗时统计:\e[0m"
echo -e "  开始时间: $START_TIME_READABLE"
echo -e "  结束时间: $END_TIME_READABLE"
echo -e "  总耗时: \e[1;33m$FORMATTED_DURATION\e[0m"
echo -e "\e[1;33m>>> 可视化报告已保存到: $REPORT_FILE\e[0m"

# 清理临时测试输出文件
# echo -e "\e[1;36m>>> 清理临时文件...\e[0m"
# rm -f /tmp/test_output_*.log /tmp/retry_test_output_*.log /tmp/alt_test_output_*.log 2>/dev/null
# echo -e "\e[32m>>> 临时文件清理完成\e[0m"
