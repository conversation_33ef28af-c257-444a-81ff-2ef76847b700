package handlers

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"

	asynq "micro-service/pkg/queue_helper"
)

func TestPushDomainIcpJobPayload(t *testing.T) {
	// 测试载荷序列化和反序列化
	payload := PushDomainIcpJobPayload{
		UserID:     123,
		DomainList: []string{"example.com", "test.com"},
	}

	// 序列化
	data, err := json.Marshal(payload)
	assert.NoError(t, err)

	// 反序列化
	var decoded PushDomainIcpJobPayload
	err = json.Unmarshal(data, &decoded)
	assert.NoError(t, err)

	// 验证数据
	assert.Equal(t, payload.UserID, decoded.UserID)
	assert.Equal(t, payload.DomainList, decoded.DomainList)
}

func TestPushDomainIcpJob_EmptyDomainList(t *testing.T) {
	// 测试空域名列表的情况
	payload := PushDomainIcpJobPayload{
		UserID:     123,
		DomainList: []string{},
	}

	data, err := json.Marshal(payload)
	assert.NoError(t, err)

	task := &asynq.Task{
		Type:    asynq.PushDomainIcpJob,
		Payload: string(data),
	}

	// 调用处理函数
	err = PushDomainIcpJob(context.Background(), task)
	assert.NoError(t, err) // 空列表应该正常返回，不报错
}

func TestPushDomainIcpJob_InvalidPayload(t *testing.T) {
	// 测试无效载荷的情况
	task := &asynq.Task{
		Type:    asynq.PushDomainIcpJob,
		Payload: "invalid json",
	}

	// 调用处理函数
	err := PushDomainIcpJob(context.Background(), task)
	assert.Error(t, err) // 无效载荷应该返回错误
	assert.Contains(t, err.Error(), "解析任务载荷失败")
}
