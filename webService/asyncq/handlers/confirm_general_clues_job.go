package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
)

// ConfirmGeneralCluesJob 确认通用线索
// 消息体:
//
//	{
//		"user_id": 1,
//		"group_id": 1
//	}
func ConfirmGeneralCluesJob(ctx context.Context, task *asyncq.Task) error {
	userId, groupId, err := parsePayloadForConfirmGeneralCluesJob([]byte(task.Payload))
	if err != nil {
		return err
	}
	query := []mysql.HandleFunc{
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		mysql.WithColumnValue("group_id", groupId),
		mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
		mysql.WithColumnNotValue("type", clues.TYPE_IP),
		mysql.WithColumnNotValue("type", clues.TYPE_SUBDOMAIN),
		mysql.WithColumnNotNull("clue_company_name"),
	}
	cluesData, err := clues.NewCluer().ListAll(query...)
	if err != nil {
		log.Error("ConfirmGeneralCluesJob", "获取线索数据失败", map[string]interface{}{
			"query": query,
		})
		return err
	}
	for _, c := range cluesData {
		generalCluesModel := general_clues.NewGeneralCluesModel()
		var generalClues *general_clues.GeneralClues
		if c.Type == clues.TYPE_LOGO {
			t, err := generalCluesModel.First(mysql.WithColumnValue("hash", c.Hash))
			if err != nil {
				log.Error("ConfirmGeneralCluesJob", "获取通用线索数据失败", map[string]interface{}{
					"query": query,
				})
				continue
			}
			generalClues = t
		} else {
			t, err := generalCluesModel.First(mysql.WithColumnValue("content", c.Content))
			if err != nil {
				log.Error("ConfirmGeneralCluesJob", "获取通用线索数据失败", map[string]interface{}{
					"query": query,
				})
				continue
			}
			generalClues = t
		}
		if generalClues != nil {
			if generalClues.Confirmed == 1 {
				continue
			} else {
				// 确认线索
				err = generalCluesModel.UpdateAny(map[string]any{
					"confirmed":    1,
					"company_name": c.ClueCompanyName,
				}, mysql.WithColumnValue("id", generalClues.Id))
				if err != nil {
					log.Error("ConfirmGeneralCluesJob", "更新通用线索数据失败", map[string]interface{}{
						"query": query,
					})
					continue
				}
			}
		} else {
			// 通用线索不存在，创建通用线索
			generalClues = &general_clues.GeneralClues{
				Content:     c.Content,
				CompanyName: c.ClueCompanyName,
				Type:        uint8(c.Type),
				Hash:        int64(c.Hash),
				Source:      "INPUT_PHP",
				Confirmed:   1,
			}
			switch c.Type {
			case clues.TYPE_DOMAIN:
				generalClues.Type = 5
			case clues.TYPE_SUBDOMAIN:
				generalClues.Type = 7
			}
			// 创建通用线索
			err = generalCluesModel.Create(generalClues)
			if err != nil {
				log.Error("ConfirmGeneralCluesJob", "创建通用线索数据失败", map[string]interface{}{
					"query": query,
				})
				continue
			}
		}
	}
	return nil
}

// parsePayload 解析任务参数
func parsePayloadForConfirmGeneralCluesJob(payload []byte) (userId int, groupId int, err error) {
	// task.Payload 是json字符串，需要解析成map
	var payloadMap map[string]interface{}
	err = json.Unmarshal(payload, &payloadMap)
	if err != nil {
		log.Error("ConfirmGeneralCluesJob", "解析任务参数失败", map[string]interface{}{
			"payload": string(payload),
		})
		return 0, 0, err
	}

	userIdVal, ok := payloadMap["user_id"]
	if !ok {
		return 0, 0, errors.New("解析user_id失败")
	}
	userId = utils.SafeInt(userIdVal)

	groupIdVal, ok := payloadMap["group_id"]
	if !ok {
		return 0, 0, errors.New("解析group_id失败")
	}
	groupId = utils.SafeInt(groupIdVal)

	return userId, groupId, nil
}
