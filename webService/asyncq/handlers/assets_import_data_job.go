package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/middleware/mysql/domain_assets"
	"regexp"
	"runtime/debug"
	"slices"
	"strconv"
	"strings"
	"time"

	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"

	es_utils "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/ip_history"

	"github.com/olivere/elastic"
)

const (
	WsImportAssetsType    = "assets_import_data"
	WsImportAssetsTypeEnd = "assets_import_data_end"
)

// 资产标签映射
var assetsTagMap = map[string]int{
	"用户-扫描": 0,
	"安服-扫描": 1,
	"用户-推荐": 2,
	"安服-推荐": 3,
	"安服-导入": 4,
}

// 资产类型映射
var assetsTypeMap = map[string]int{
	"已知资产IP": 0,
	"云端推荐":   1,
}

// 状态字符串到数字的映射
var statusStringToIntMap = map[string]int{
	"account_assets": fofaee_assets.STATUS_UPLOAD,   // 台账资产
	"unsure_assets":  fofaee_assets.STATUS_DEFAULT,  // 疑似资产
	"threaten_asset": fofaee_assets.STATUS_THREATEN, // 威胁资产
}

// AssetsImportProcessor 资产导入处理器
type AssetsImportProcessor struct {
	UserId       uint64
	Status       string
	CompanyId    uint64
	AssetsData   map[string][]map[string]interface{} // 修改为正确的格式：IP -> assets array
	GroupId      uint64
	DomainAssets []string

	// 模型实例
	ipAssetModel   fofaee_assets.FofaeeAssetsModel
	portAssetModel foradar_assets.ForadarAssetModel
	clueModel      clues.Cluer
}

// NewAssetsImportProcessor 创建资产导入处理器
func NewAssetsImportProcessor(payload *asyncq.AssetsImportDataJobPayload) *AssetsImportProcessor {
	// 获取用户的GroupId
	clueModel := clues.NewCluer()
	var groupId uint64
	if clue, err := clueModel.First(mysql.WithColumnValue("user_id", payload.UserId), mysql.WithOrder("id ASC")); err == nil {
		groupId = clue.GroupId
	}

	// 直接使用payload.AssetsData，不需要复杂的格式转换
	// payload.AssetsData 格式: map[string][]map[string]interface{} (IP -> assets array)
	// 这个格式已经符合处理需求

	return &AssetsImportProcessor{
		UserId:         payload.UserId,
		Status:         payload.Status,
		CompanyId:      payload.CompanyId,
		AssetsData:     payload.AssetsData, // 直接使用原始数据格式
		GroupId:        groupId,
		DomainAssets:   make([]string, 0),
		ipAssetModel:   fofaee_assets.NewFofaeeAssetsModel(),
		portAssetModel: foradar_assets.NewForadarAssetModel(),
		clueModel:      clueModel,
	}
}

// AssetsImportDataJob 处理资产数据导入任务
func AssetsImportDataJob(ctx context.Context, task *asyncq.Task) error {
	var payload asyncq.AssetsImportDataJobPayload
	if err := json.Unmarshal([]byte(task.Payload), &payload); err != nil {
		log.Error("AssetsImportDataJobHandler", "解析任务参数失败", map[string]interface{}{
			"payload": string(task.Payload),
			"error":   err.Error(),
		})
		return err
	}

	log.Info("AssetsImportDataJobHandler", "开始导入资产", map[string]interface{}{
		"user_id": payload.UserId,
		"count":   len(payload.AssetsData),
	})

	// 创建处理器并执行
	processor := NewAssetsImportProcessor(&payload)
	return processor.Handle(ctx)
}

// Handle 执行资产导入主逻辑
func (p *AssetsImportProcessor) Handle(ctx context.Context) error {
	defer func() {
		if r := recover(); r != nil {
			log.Error("AssetsImportDataJob", "资产导入发生panic", map[string]interface{}{
				"user_id": p.UserId,
				"error":   r,
				"stack":   string(debug.Stack()),
			})
		}
	}()

	log.Info("AssetsImportDataJobHandler", "开始导入", map[string]interface{}{
		"user_id":      p.UserId,
		"company_id":   p.CompanyId,
		"status":       p.Status,
		"assets_count": len(p.AssetsData),
	})

	// 准备批量插入的数据
	var insertIpAssets []fofaee_assets.FofaeeAssets
	var insertIpPortAssets []foradar_assets.ForadarAsset
	var ipHistoryArray []*ip_history.IpHistory

	// 处理进度计算
	i := 0

	statusList := []int{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}

	// 遍历处理每个IP的资产数据
	for ip, assets := range p.AssetsData {

		if len(assets) == 0 {
			log.Warn("AssetsImportDataJob", "IP没有资产数据，跳过", map[string]interface{}{
				"ip": ip,
			})
			continue
		}

		// 将assets数组转换为map格式，以兼容现有的IP维度处理逻辑
		assetMap := make(map[string]interface{})
		for i, asset := range assets {
			assetMap[fmt.Sprintf("asset_%d", i)] = asset
		}
		// 将状态字符串转换为对应的数字状态
		assetStatus, exists := statusStringToIntMap[p.Status]
		if !exists {
			log.Warn("AssetsImportDataJob", "未知的状态类型，使用默认状态", map[string]interface{}{
				"status": p.Status,
			})
			assetStatus = fofaee_assets.STATUS_DEFAULT
		}
		ip = strings.ToLower(utils.CompleteIPV6(ip))

		// IP维度处理（每个IP只处理一次）
		id := fmt.Sprintf("%d_%s", p.UserId, ip)

		log.Debug("AssetsImportDataJob", "处理IP资产", map[string]interface{}{
			"original_ip":  ip,
			"user_id":      p.UserId,
			"generated_id": id,
			"assets_count": len(assets),
		})

		// 查询已存在的IP资产
		oldIpAssets := p.ipAssetModel.FindFullById(ctx, id)
		// 处理资产状态逻辑
		if oldIpAssets != nil {
			oldStatus := utils.SafeInt(oldIpAssets.Status)
			newStatus := statusStringToIntMap[p.Status] // 使用映射获取正确的状态值

			log.Debug("AssetsImportDataJob", "资产状态检查", map[string]interface{}{
				"ip":         ip,
				"old_status": oldStatus,
				"new_status": newStatus,
				"status_str": p.Status,
			})

			if oldStatus != newStatus {
				// 只有当新旧状态都是台账资产状态时，才保持旧状态
				if slices.Contains(statusList, oldStatus) && slices.Contains(statusList, newStatus) {
					assetStatus = oldStatus
					log.Debug("AssetsImportDataJob", "保持旧的台账资产状态", map[string]interface{}{
						"ip":          ip,
						"keep_status": oldStatus,
					})
				} else {
					// 对于非台账资产或状态不匹配的情况，继续处理但使用新状态
					assetStatus = newStatus
					log.Debug("AssetsImportDataJob", "使用新状态", map[string]interface{}{
						"ip":         ip,
						"new_status": newStatus,
					})
				}
			}
		}

		// 处理IP维度数据（传入整个assetMap）
		ipAsset, isUpdate, err := p.doneIpAssetData(id, ip, assetMap, oldIpAssets, assetStatus)
		if err != nil {
			log.Error("AssetsImportDataJob", "处理IP资产数据失败", map[string]interface{}{
				"error": err.Error(),
				"ip":    ip,
				"id":    id,
			})
			continue
		}

		if ipAsset != nil {
			// 确保ID正确设置
			if ipAsset.Id == "" {
				ipAsset.Id = id
			}

			log.Debug("AssetsImportDataJob", "处理IP资产", map[string]interface{}{
				"ip":        ip,
				"id":        ipAsset.Id,
				"user_id":   ipAsset.UserId,
				"is_update": isUpdate,
			})

			if isUpdate && oldIpAssets != nil {
				// 更新现有数据 - 使用upsert操作避免document missing错误
				if err := p.updateOrCreateIpAsset(ctx, ipAsset); err != nil {
					log.Error("AssetsImportDataJob", "更新IP资产失败", map[string]interface{}{
						"error": err.Error(),
						"ip":    ip,
						"id":    ipAsset.Id,
					})
				}
			} else {
				// 新增数据
				insertIpAssets = append(insertIpAssets, *ipAsset)
			}

			// 创建IP历史记录
			history := p.createHistory(ipAsset, ip)
			ipHistoryArray = append(ipHistoryArray, history)
		}

		// 处理IP端口维度数据
		query := elastic.NewBoolQuery().Must(elastic.NewTermQuery("ip", ip), elastic.NewTermQuery("user_id", p.UserId))

		log.Debug("AssetsImportDataJob", "查询IP端口资产", map[string]interface{}{
			"ip":      ip,
			"user_id": p.UserId,
		})

		// 查询IP端口资产
		oldIpPortAssets, err := es_utils.All[foradar_assets.ForadarAsset](500, query, nil, "id")
		if err != nil {
			log.Error("AssetsImportDataJob", "查询IP端口资产失败", map[string]interface{}{
				"error":   err.Error(),
				"ip":      ip,
				"user_id": p.UserId,
			})
		}

		log.Debug("AssetsImportDataJob", "查询到的现有IP端口资产", map[string]interface{}{
			"ip":                    ip,
			"existing_assets_count": len(oldIpPortAssets),
			"input_assets_count":    len(assets),
		})

		// 处理这个IP下的所有资产（传入整个assets数组）

		ipPortAssets, isInsert, err := p.doneIpPortAssetData(ip, assets, oldIpPortAssets, assetStatus)
		if err != nil {
			log.Error("AssetsImportDataJob", "处理IP端口资产数据失败", map[string]interface{}{
				"error": err.Error(),
				"ip":    ip,
			})
			continue
		}

		if isInsert && len(ipPortAssets) > 0 {
			insertIpPortAssets = append(insertIpPortAssets, ipPortAssets...)
			log.Debug("AssetsImportDataJob", "添加到批量插入列表", map[string]interface{}{
				"ip":                        ip,
				"added_count":               len(ipPortAssets),
				"total_insert_assets_count": len(insertIpPortAssets),
			})
		}

		i++
	}

	// 批量插入IP资产
	if len(insertIpAssets) > 0 {
		successCount, errCount, lastErrorInfo := es_utils.InsertOrUpdate(fofaee_assets.FofaeeAssetsIndex, fofaee_assets.FofaeeAssetsType, insertIpAssets, false, 500)
		log.Debugf("[AssetsImportDataJob]批量插入IP资产, successCount: %d, errCount: %d, lastErrorInfo: %s", successCount, errCount, lastErrorInfo)
		if errCount > 0 {
			log.Error("AssetsImportDataJob", "批量插入IP资产失败", map[string]interface{}{
				"error": lastErrorInfo,
			})
		}
	}

	// 批量插入IP端口资产
	if len(insertIpPortAssets) > 0 {
		log.Info("AssetsImportDataJob", "开始批量插入IP端口资产", map[string]interface{}{
			"total_count": len(insertIpPortAssets),
			"index_name":  foradar_assets.IndexName,
			"type_name":   foradar_assets.TypeName,
		})

		// 检查ID重复问题 - 详细分析
		idMap := make(map[string]int)
		idToAssets := make(map[string][]foradar_assets.ForadarAsset) // 记录每个ID对应的资产
		duplicateIds := make([]string, 0)

		for _, asset := range insertIpPortAssets {
			if count, exists := idMap[asset.ID]; exists {
				idMap[asset.ID] = count + 1
				if count == 1 { // 第一次发现重复
					duplicateIds = append(duplicateIds, asset.ID)
				}
				idToAssets[asset.ID] = append(idToAssets[asset.ID], asset)
			} else {
				idMap[asset.ID] = 1
				idToAssets[asset.ID] = []foradar_assets.ForadarAsset{asset}
			}

		}

		if len(duplicateIds) > 0 {
			log.Debugf("[AssetsImportDataJob]发现重复的资产ID: %d个重复, 总数: %d, 唯一数: %d", len(duplicateIds), len(insertIpPortAssets), len(idMap))
		}

		successCount, errCount, lastErrorInfo := es_utils.InsertOrUpdate(foradar_assets.IndexName, foradar_assets.TypeName, insertIpPortAssets, false, 500)

		log.Info("AssetsImportDataJob", "批量插入IP端口资产完成", map[string]interface{}{
			"total_count":   len(insertIpPortAssets),
			"success_count": successCount,
			"error_count":   errCount,
			"last_error":    lastErrorInfo,
		})

		if errCount > 0 {
			log.Error("AssetsImportDataJob", "批量插入IP端口资产失败", map[string]interface{}{
				"error":         lastErrorInfo,
				"success_count": successCount,
				"error_count":   errCount,
				"total_count":   len(insertIpPortAssets),
			})
		} else {
			log.Info("AssetsImportDataJob", "所有IP端口资产插入成功", map[string]interface{}{
				"success_count": successCount,
				"total_count":   len(insertIpPortAssets),
			})

		}
	} else {
		log.Info("AssetsImportDataJob", "没有IP端口资产需要插入", map[string]interface{}{
			"user_id": p.UserId,
		})
	}

	// 插入IP历史记录
	if len(ipHistoryArray) > 0 {
		if err := ip_history.NewModel().BatchCreate(ipHistoryArray); err != nil {
			log.Error("AssetsImportDataJob", "插入IP历史记录失败", map[string]interface{}{
				"error": err.Error(),
			})
		}
	}

	// 同步域名资产
	status, exists := statusStringToIntMap[p.Status]
	if !exists {
		status = fofaee_assets.STATUS_DEFAULT
	}
	if slices.Contains(statusList, status) {
		p.syncDomainAssets()
	}

	log.Info("AssetsImportDataJobHandler", "导入完成", map[string]interface{}{
		"user_id": p.UserId,
	})

	return nil
}

func (p *AssetsImportProcessor) createHistory(ipAsset *fofaee_assets.FofaeeAssets, ip string) *ip_history.IpHistory {
	historyData := ipAsset
	historyData.HostList = nil // 删除部分数据
	historyData.Hosts = nil
	// 序列化
	historyDataJson, err := json.Marshal(historyData)
	if err != nil {
		log.Error("AssetsImportDataJob", "序列化IP资产数据失败", map[string]interface{}{
			"error": err.Error(),
			"ip":    ip,
		})
	}

	history := &ip_history.IpHistory{
		Ip:        ip,
		Data:      string(historyDataJson),
		CompanyId: int64(p.CompanyId),
		UserId:    p.UserId,
		CreatedAt: time.Now(),
	}
	return history
}

// doneIpAssetData 处理IP维度数据
func (p *AssetsImportProcessor) doneIpAssetData(id, ip string, assets map[string]interface{}, oldAsset *fofaee_assets.FofaeeAssets, setStatus int) (*fofaee_assets.FofaeeAssets, bool, error) {
	assetsArray := make([]map[string]interface{}, 0)
	for _, assetData := range assets {
		if assetMap, ok := assetData.(map[string]interface{}); ok {
			assetsArray = append(assetsArray, assetMap)
		}
	}

	if len(assetsArray) == 0 {
		return nil, false, nil
	}

	isUpdate := false
	var ipAssets *fofaee_assets.FofaeeAssets

	if oldAsset != nil {
		// 更新现有资产
		isUpdate = true
		ipAssets = p.updateIpAsset(oldAsset, assetsArray, setStatus)
	} else {
		// 创建新资产
		ipAssets = p.buildIpAsset(assetsArray, setStatus, id, ip)
	}

	return ipAssets, isUpdate, nil
}

// updateOrCreateIpAsset 更新或创建IP资产（使用upsert操作）
func (p *AssetsImportProcessor) updateOrCreateIpAsset(ctx context.Context, ipAsset *fofaee_assets.FofaeeAssets) error {
	// 确保ID正确设置
	if ipAsset.Id == "" {
		ipAsset.Id = fmt.Sprintf("%d_%s", p.UserId, ipAsset.Ip)
	}

	// 确保UserId正确设置
	if ipAsset.UserId == 0 {
		ipAsset.UserId = int(p.UserId)
	}

	// 确保时间字段设置
	if ipAsset.UpdatedAt == "" {
		ipAsset.UpdatedAt = utils.CurrentTime()
	}

	log.Debug("AssetsImportDataJob", "执行upsert操作", map[string]interface{}{
		"id":      ipAsset.Id,
		"ip":      ipAsset.Ip,
		"user_id": ipAsset.UserId,
	})

	// 使用 es_utils.InsertOrUpdate 进行批量插入，这会自动处理创建和更新
	successCount, errCount, lastErrorInfo := es_utils.InsertOrUpdate(
		fofaee_assets.FofaeeAssetsIndex,
		fofaee_assets.FofaeeAssetsType,
		[]*fofaee_assets.FofaeeAssets{ipAsset},
		false, // supplementTime=false，我们已经设置了时间
		1,     // batchSize=1
	)

	if errCount > 0 {
		return fmt.Errorf("upsert失败: %s", lastErrorInfo)
	}

	log.Debug("AssetsImportDataJob", "upsert成功", map[string]interface{}{
		"id":            ipAsset.Id,
		"success_count": successCount,
	})

	return nil
}

// buildIpAsset 构建IP资产数据
func (p *AssetsImportProcessor) buildIpAsset(assetsArray []map[string]interface{}, setStatus int, id string, ip string) *fofaee_assets.FofaeeAssets {
	assetFirst := assetsArray[0]
	assetsState := 0
	if state, ok := assetFirst["资产状态"]; ok {
		switch state {
		case "在线":
			assetsState = 1
		case "离线":
			assetsState = 0
		}
	}

	assetTag := 4
	if tag, ok := assetFirst["资产标签"]; ok {
		if tagVal, exists := assetsTagMap[fmt.Sprintf("%v", tag)]; exists {
			assetTag = tagVal
		}
	}

	// 处理时间
	timeStr := time.Now().Format("2006-01-02 15:04:05")
	if updateTime, ok := assetFirst["更新时间"]; ok && updateTime != nil {
		if parsed, err := time.Parse("2006-01-02 15:04:05", fmt.Sprintf("%v", updateTime)); err == nil {
			timeStr = parsed.Format("2006-01-02 15:04:05")
		}
	}

	// 构建端口列表
	portList := make([]fofaee_assets.FofaeeAssetPort, 0)
	hosts := make([]string, 0)
	ruleTags := make([]fofaee_assets.RuleTag, 0)
	cloudNameList := make([]string, 0)
	companyNameList := make([]string, 0)
	allDomain := make([]string, 0)
	allTitle := make([]string, 0)
	for _, asset := range assetsArray {
		url := utils.SafeString(asset["URL"])
		domain := utils.SafeString(asset["域名"])
		if domain == "" {
			domain = url
		}
		topDomain := utils.GetTopDomain(domain)
		topSubdomain := utils.GetSubdomain(url)
		portData := fofaee_assets.FofaeeAssetPort{
			Port:           utils.SafeInt(asset["*端口"]),
			Url:            url,
			Protocol:       utils.SafeString(asset["*协议"]),
			Title:          utils.SafeString(asset["网站标题"]),
			Domain:         topDomain,
			Subdomain:      topSubdomain,
			HttpStatusCode: asset["状态码"],
		}
		portList = append(portList, portData)

		allDomain = append(allDomain, topDomain)
		allTitle = append(allTitle, utils.SafeString(asset["网站标题"]))

		p.DomainAssets = append(p.DomainAssets, topDomain)
		p.DomainAssets = append(p.DomainAssets, topSubdomain)
		// 提取hosts
		if host := topSubdomain; host != "" {
			if !slices.Contains(hosts, host) {
				hosts = append(hosts, host)
			}
		}
		// 提取规则标签
		if components := utils.SafeString(asset["组件信息"]); components != "" {
			for _, component := range strings.Split(components, ",") {
				component = strings.TrimSpace(component)
				if component != "" {
					ruleTag := fofaee_assets.RuleTag{
						CnProduct:        component,
						RuleId:           "",
						Product:          "",
						CnCategory:       "",
						Level:            "",
						ParentCategory:   "",
						Softhard:         "",
						Company:          "",
						CnParentCategory: "",
						Category:         "",
						CnCompany:        "",
					}
					ruleTags = append(ruleTags, ruleTag)
				}
			}
		}
		// 提取云厂商
		cloudNameList = append(cloudNameList, extractInfoFromAsset(asset, "云厂商")...)
		// 提取企业名称
		companyNameList = append(companyNameList, extractInfoFromAsset(asset, "企业名称")...)
	}
	cloudNameList = utils.ListDistinct(cloudNameList)
	// 转换为any类型
	cloudNameAnyList := make([]any, 0)
	for _, cloudName := range cloudNameList {
		cloudNameAnyList = append(cloudNameAnyList, cloudName)
	}
	companyNameList = utils.ListDistinct(companyNameList)
	// 转换为any类型
	companyNameAnyList := make([]any, 0)
	for _, companyName := range companyNameList {
		companyNameAnyList = append(companyNameAnyList, companyName)
	}
	// 去重
	allDomain = utils.ListDistinct(allDomain)
	allTitle = utils.ListDistinct(allTitle)

	// 处理推荐原因
	detectType, reasons := p.buildReason(assetFirst, assetsArray)

	// 确定资产类型
	assetType := detectType
	if setStatus == 0 {
		assetType = fofaee_assets.TYPE_RECOMMEND
	}

	// 按照PHP逻辑：host_list保存所有端口数据（不去重），port_list根据port去重
	hostList := portList // host_list保存所有数据，不去重

	// 对port_list根据Port字段去重
	portMap := make(map[int]fofaee_assets.FofaeeAssetPort)
	for _, port := range portList {
		portMap[utils.SafeInt(port.Port)] = port
	}
	portListUnique := make([]fofaee_assets.FofaeeAssetPort, 0, len(portMap))
	for _, port := range portMap {
		portListUnique = append(portListUnique, port)
	}

	// 构建IP资产数据
	ipAssets := &fofaee_assets.FofaeeAssets{
		Id:          id,
		Hosts:       hosts,
		HostList:    hostList,       // 所有端口数据，不去重
		PortList:    portListUnique, // 根据port去重的数据
		CreatedAt:   timeStr,
		UpdatedAt:   timeStr,
		Ip:          ip,
		IsIpv6:      strings.Contains(ip, ":"),
		UserId:      int(p.UserId),
		Type:        assetType,
		CloudName:   cloudNameAnyList,
		OnlineState: assetsState,
		Status:      setStatus,
		Geo: fofaee_assets.Geo{
			Country:  "",
			City:     "",
			Province: utils.SafeString(assetFirst["地理位置"]),
			Asn:      "",
			Lat:      "",
			Lon:      "",
			AsName:   "",
		},
		ClueCompanyName: companyNameAnyList,
		Tags:            []int{assetTag},
		PortSize:        len(assetsArray),
		RuleTags:        ruleTags,
		AllDomain:       allDomain,
		AllTitle:        allTitle,
	}

	// 处理威胁类型
	if setStatus == fofaee_assets.STATUS_THREATEN {
		if threatType := utils.SafeString(assetFirst["威胁类型"]); threatType != "" {
			ipAssets.ThreatenType = threatToInt(threatType)
		}
	}

	// 添加推荐原因
	if len(reasons) > 0 {
		for _, reason := range reasons {
			ipAssets.ReasonArr = append(ipAssets.ReasonArr, reason)
		}
	}
	return ipAssets
}

// buildReason 构建推荐原因
func (p *AssetsImportProcessor) buildReason(assetFirst map[string]interface{}, assetsArray []map[string]interface{}) (int, []map[string]interface{}) {
	detectType := assetsTypeMap["已知资产IP"]
	var reasons []map[string]interface{}
	if detectWay := utils.SafeString(assetFirst["探测方式"]); strings.Contains(detectWay, "推荐") {
		detectType = assetsTypeMap["云端推荐"]
		reasonStrs := make([]string, 0)
		for _, asset := range assetsArray {
			if way := utils.SafeString(asset["探测方式"]); way != "" {
				reasonStrs = append(reasonStrs, way)
			}
		}
		reasons = p.parseReason(reasonStrs, utils.SafeString(assetFirst["企业名称"]))
	}
	return detectType, reasons
}

// updateIpAsset 更新IP资产数据
func (p *AssetsImportProcessor) updateIpAsset(oldAsset *fofaee_assets.FofaeeAssets, assetsArray []map[string]interface{}, setStatus int) *fofaee_assets.FofaeeAssets {
	ipAssets := oldAsset

	// 处理端口列表更新逻辑
	existingPorts := make(map[string]bool)
	for _, port := range oldAsset.PortList {
		existingPorts[fmt.Sprintf("%v", port.Port)] = true
	}

	// 添加新端口
	for _, asset := range assetsArray {
		port := fmt.Sprintf("%v", asset["*端口"])
		if existingPorts[port] {
			log.Info("AssetsImportDataJob", "跳出这条数据", map[string]interface{}{
				"ports":  existingPorts,
				"要导入的端口": port,
			})
			continue
		}
		url := utils.SafeString(asset["URL"])
		domain := utils.SafeString(asset["域名"])
		if domain == "" {
			domain = url
		}
		topDomain := utils.GetTopDomain(domain)
		topSubdomain := utils.GetSubdomain(url)

		p.DomainAssets = append(p.DomainAssets, topDomain)
		p.DomainAssets = append(p.DomainAssets, topSubdomain)

		newPortData := fofaee_assets.FofaeeAssetPort{
			Port:           utils.SafeInt(port),
			Url:            url,
			Protocol:       utils.SafeString(asset["*协议"]),
			Title:          utils.SafeString(asset["网站标题"]),
			Domain:         topDomain,
			Subdomain:      topSubdomain,
			HttpStatusCode: utils.SafeString(asset["状态码"]),
		}
		// 更新端口列表
		ipAssets.PortList = append(ipAssets.PortList, newPortData)
		ipAssets.PortSize = ipAssets.PortSize + 1

		// 更新hosts
		host := topSubdomain
		if host != "" && !slices.Contains(ipAssets.Hosts, host) {
			ipAssets.Hosts = append(ipAssets.Hosts, host)
		}
		ipAssets.HostList = append(ipAssets.HostList, newPortData)

		// 更新云厂商
		if cloudName := utils.SafeString(asset["云厂商"]); cloudName != "" {
			cloudNameList := make([]string, 0)
			for _, cloudName := range ipAssets.CloudName {
				cloudNameList = append(cloudNameList, utils.SafeString(cloudName))
			}
			if !slices.Contains(cloudNameList, cloudName) {
				ipAssets.CloudName = append(ipAssets.CloudName, cloudName)
			}
		}

		// 更新企业名称
		if companyName := utils.SafeString(asset["企业名称"]); companyName != "" {
			companyNameList := make([]string, 0)
			for _, companyName := range ipAssets.ClueCompanyName {
				companyNameList = append(companyNameList, utils.SafeString(companyName))
			}
			if !slices.Contains(companyNameList, companyName) {
				ipAssets.ClueCompanyName = []any{companyName}
			}
		}

		if setStatus == fofaee_assets.STATUS_THREATEN {
			if threatType := utils.SafeString(asset["威胁类型"]); threatType != "" {
				ipAssets.ThreatenType = threatToInt(threatType)
			}
		}
	}

	// 按照PHP逻辑：基于HostList对PortList进行去重
	// HostList保存所有数据（已经在上面append了），PortList根据port去重
	portMap := make(map[int]fofaee_assets.FofaeeAssetPort)
	for _, port := range ipAssets.HostList {
		portMap[utils.SafeInt(port.Port)] = port
	}
	ipAssets.PortList = make([]fofaee_assets.FofaeeAssetPort, 0, len(portMap))
	for _, port := range portMap {
		ipAssets.PortList = append(ipAssets.PortList, port)
	}

	ipAssets.PortSize = len(ipAssets.PortList)

	// 更新聚合字段
	domainMap := make(map[string]struct{})
	titleMap := make(map[string]struct{})
	for _, host := range ipAssets.HostList {
		domainMap[utils.SafeString(host.Domain)] = struct{}{}
		titleMap[utils.SafeString(host.Title)] = struct{}{}
	}
	allDomain := make([]string, 0, len(domainMap))
	for domain := range domainMap {
		allDomain = append(allDomain, domain)
	}
	allTitle := make([]string, 0, len(titleMap))
	for title := range titleMap {
		allTitle = append(allTitle, title)
	}
	ipAssets.AllDomain = allDomain
	ipAssets.AllTitle = allTitle
	return ipAssets
}

// doneIpPortAssetData 处理IP端口维度数据
func (p *AssetsImportProcessor) doneIpPortAssetData(ip string, assets []map[string]interface{}, oldPortAssets []*foradar_assets.ForadarAsset, setStatus int) ([]foradar_assets.ForadarAsset, bool, error) {
	// 直接使用传入的assets数组
	assetsArray := assets

	log.Debug("AssetsImportDataJob", "开始处理IP端口资产数据", map[string]interface{}{
		"ip":                    ip,
		"input_assets_count":    len(assetsArray),
		"existing_assets_count": len(oldPortAssets),
		"set_status":            setStatus,
	})

	if len(assetsArray) == 0 {
		log.Debug("AssetsImportDataJob", "没有资产数据需要处理", map[string]interface{}{
			"ip": ip,
		})
		return nil, false, nil
	}

	isInsert := false
	var portAssets []foradar_assets.ForadarAsset

	// 提取现有资产ID
	existingIds := make(map[string]bool)
	for _, asset := range oldPortAssets {
		existingIds[asset.ID] = true
	}

	log.Debug("AssetsImportDataJob", "现有资产ID列表", map[string]interface{}{
		"ip":           ip,
		"existing_ids": existingIds,
	})

	for i, asset := range assetsArray {
		url := utils.SafeString(asset["URL"])
		port := utils.SafeString(asset["*端口"])
		protocol := utils.SafeString(asset["*协议"])
		id := generatePortAssetId(ip, asset, url, p.UserId)

		log.Debugf("AssetsImportDataJob", "处理单个端口资产", map[string]interface{}{
			"ip":         ip,
			"index":      i,
			"port":       port,
			"protocol":   protocol,
			"url":        url,
			"id":         id,
			"exists":     existingIds[id],
			"asset_data": asset,
		})

		if !existingIds[id] {
			isInsert = true

			log.Debug("AssetsImportDataJob", "创建新的端口资产", map[string]interface{}{
				"ip":       ip,
				"id":       id,
				"port":     port,
				"protocol": protocol,
			})

			// 构建端口资产数据
			assetsState := 0
			if state, ok := asset["资产状态"]; ok {
				switch state {
				case "在线":
					assetsState = 1
				case "离线":
					assetsState = 0
				}
			}

			assetTag := 4
			if tag, ok := asset["资产标签"]; ok {
				if tagVal, exists := assetsTagMap[fmt.Sprintf("%v", tag)]; exists {
					assetTag = tagVal
				}
			}

			// 处理时间
			timeStr := time.Now().Format("2006-01-02 15:04:05")
			if updateTime, ok := asset["更新时间"]; ok && updateTime != nil {
				if parsed, err := time.Parse("2006-01-02 15:04:05", fmt.Sprintf("%v", updateTime)); err == nil {
					timeStr = parsed.Format("2006-01-02 15:04:05")
				}
			}

			companyName := utils.SafeString(asset["企业名称"])
			detectType := assetsTypeMap["已知资产IP"]
			var reasons []foradar_assets.AssetReason

			if detectWay := utils.SafeString(asset["探测方式"]); strings.Contains(detectWay, "推荐") {
				detectType = assetsTypeMap["云端推荐"]
				reasonsMap := p.parseReason([]string{detectWay}, companyName)
				// 转换为foradar_assets.AssetReason类型
				for _, reason := range reasonsMap {
					reasons = append(reasons, foradar_assets.AssetReason{
						GroupID:         int(p.GroupId),
						ID:              utils.SafeInt(reason["id"]),
						Source:          utils.SafeInt(reason["source"]),
						Type:            utils.SafeInt(reason["type"]),
						Content:         utils.SafeString(reason["content"]),
						ClueCompanyName: utils.SafeString(reason["clue_company_name"]),
					})
				}
			}

			// 确定资产类型
			assetType := detectType
			if setStatus == 0 {
				assetType = fofaee_assets.TYPE_RECOMMEND
			}

			// 构建规则标签
			var ruleTags []foradar_assets.RuleTag
			if components := utils.SafeString(asset["组件信息"]); components != "" {
				for _, component := range strings.Split(components, ",") {
					component = strings.TrimSpace(component)
					if component != "" {
						ruleTag := foradar_assets.RuleTag{
							CnProduct:        component,
							RuleID:           "",
							Product:          "",
							CnCategory:       "",
							Level:            "",
							ParentCategory:   "",
							Softhard:         "",
							Company:          "",
							CnParentCategory: "",
							Category:         "",
							CnCompany:        "",
						}
						ruleTags = append(ruleTags, ruleTag)
					}
				}
			}

			// domain
			domain := ""
			domain = utils.GetTopDomain(utils.SafeString(asset["域名"]))

			// companyName
			clueCompanyName := []string{}
			if companyName != "" {
				clueCompanyName = []string{companyName}
			}

			portAsset := foradar_assets.ForadarAsset{
				ID:              id,
				Protocol:        utils.SafeString(asset["*协议"]),
				Port:            utils.SafeInt(asset["*端口"]),
				Ip:              strings.ToLower(utils.CompleteIPV6(ip)),
				HTTPStatusCode:  utils.SafeInt(asset["状态码"]),
				IsIpv6:          strings.Contains(ip, ":"),
				UserID:          int(p.UserId),
				Type:            assetType,
				OnlineState:     assetsState,
				Status:          setStatus,
				CompanyID:       int(p.CompanyId),
				Url:             url,
				Title:           utils.SafeString(asset["网站标题"]),
				Domain:          domain,
				Tags:            []int{assetTag},
				ClueCompanyName: clueCompanyName,
				Subdomain:       utils.GetSubdomain(url),
				Geo: foradar_assets.AssetGeo{
					Country:  "",
					City:     "",
					Province: utils.SafeString(asset["地理位置"]),
					Asn:      "",
					Lat:      "",
					Lon:      "",
					AsName:   "",
				},
				RuleTags:  ruleTags,
				CreatedAt: timeStr,
				UpdatedAt: timeStr,
			}

			// 添加域名到同步列表
			p.DomainAssets = append(p.DomainAssets, portAsset.Domain)
			p.DomainAssets = append(p.DomainAssets, portAsset.Subdomain)

			// 处理威胁类型
			if setStatus == fofaee_assets.STATUS_THREATEN {
				if threatType := utils.SafeString(asset["威胁类型"]); threatType != "" {
					portAsset.ThreatenType = threatToInt(threatType)
				}
			}

			// 添加推荐原因
			if len(reasons) > 0 {
				portAsset.Reason = reasons
			}
			portAssets = append(portAssets, portAsset)

			log.Debug("AssetsImportDataJob", "成功创建端口资产", map[string]interface{}{
				"ip":         ip,
				"id":         portAsset.ID,
				"port":       portAsset.Port,
				"protocol":   portAsset.Protocol,
				"user_id":    portAsset.UserID,
				"company_id": portAsset.CompanyID,
				"url":        portAsset.Url,
				"domain":     portAsset.Domain,
			})
		} else {
			log.Debug("AssetsImportDataJob", "跳过已存在的端口资产", map[string]interface{}{
				"ip":       ip,
				"id":       id,
				"port":     port,
				"protocol": protocol,
			})
		}
	}

	log.Debug("AssetsImportDataJob", "完成IP端口资产数据处理", map[string]interface{}{
		"ip":                 ip,
		"total_processed":    len(assetsArray),
		"new_assets_created": len(portAssets),
		"is_insert":          isInsert,
	})

	return portAssets, isInsert, nil
}

// parseReason 解析推荐原因
func (p *AssetsImportProcessor) parseReason(reasonStrs []string, excelCompanyName string) []map[string]interface{} {
	var reasons []map[string]interface{}

	typeDecMap := map[string]int{
		"根域":   clues.TYPE_DOMAIN,
		"证书":   clues.TYPE_CERT,
		"ICP":  clues.TYPE_ICP,
		"ICON": clues.TYPE_LOGO,
		"关键词":  clues.TYPE_KEYWORD,
		"子域名":  clues.TYPE_SUBDOMAIN,
	}
	// 正则匹配获取数组
	typeRegex := regexp.MustCompile(`根据.*的((根域|证书|ICP|ICON|关键词|子域名)+)`)
	contentRegex := regexp.MustCompile(`的(根域|证书|ICP|ICON|关键词|子域名)+(.+)推荐`)
	companyRegex := regexp.MustCompile(`根据(.*)的((根域|证书|ICP|ICON|关键词|子域名)+)`)

	for _, reasonStr := range reasonStrs {
		reasonArray := strings.Split(reasonStr, ";")
		for _, item := range reasonArray {
			typeMatches := typeRegex.FindStringSubmatch(item)
			contentMatches := contentRegex.FindStringSubmatch(item)
			companyMatches := companyRegex.FindStringSubmatch(item)

			if len(typeMatches) < 2 || len(contentMatches) < 3 {
				continue
			}

			clueType := typeMatches[1]
			content := contentMatches[2]
			companyName := excelCompanyName

			if len(companyMatches) >= 2 && companyMatches[1] != "" {
				if companyMatches[1] == "-" {
					companyName = excelCompanyName
				} else {
					companyName = companyMatches[1]
				}
			}

			if typeVal, exists := typeDecMap[strings.ToUpper(clueType)]; exists {
				// 尝试查找线索
				existingClue, err := p.clueModel.First(
					mysql.WithColumnValue("type", typeVal),
					mysql.WithColumnValue("group_id", p.GroupId),
					mysql.WithColumnValue("user_id", p.UserId),
					mysql.WithColumnValue("content", content),
					mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
				)

				var clue *clues.Clue

				// 如果可以找到线索
				if err == nil && existingClue.Id > 0 {
					clue = &existingClue
					// 如果线索状态不是已通过，则更新为已通过
					if existingClue.Status != clues.CLUE_PASS_STATUS {
						if err := p.clueModel.UpdateAny(map[string]any{"status": clues.CLUE_PASS_STATUS}, mysql.WithColumnValue("id", existingClue.Id)); err != nil {
							log.Error("AssetsImportDataJob", "更新线索状态失败", map[string]interface{}{
								"error": err,
							})
						}
						clue.Status = clues.CLUE_PASS_STATUS
					}
				} else {
					// 线索不存在，创建新线索 - 对应PHP的firstOrCreate逻辑
					newClue := &clues.Clue{
						Type:            typeVal,
						GroupId:         p.GroupId,
						UserId:          p.UserId,
						Content:         content,
						IsDeleted:       clues.NOT_DELETE,
						ClueCompanyName: companyName,
						CompanyId:       p.CompanyId,
						Source:          clues.SOURCE_IMPORT,
						Status:          clues.CLUE_PASS_STATUS,
					}

					if err := p.clueModel.Create(newClue); err != nil {
						log.Error("AssetsImportDataJob", "创建线索失败", map[string]interface{}{
							"error": err,
							"clue":  newClue,
						})
						continue
					}
					clue = newClue
				}

				// 构建返回的线索信息
				reason := map[string]interface{}{
					"id":                clue.Id,
					"group_id":          p.GroupId,
					"source":            clues.SOURCE_IMPORT,
					"type":              typeVal,
					"content":           content,
					"clue_company_name": companyName,
				}
				reasons = append(reasons, reason)
			}
		}
	}

	return reasons
}
func threatToInt(threatType string) int {
	// 威胁类型转换为整数
	threatMap := map[string]int{
		"钓鱼仿冒":  fofaee_assets.ThreatenTypeDY,
		"ICP盗用": fofaee_assets.ThreatenTypeFmICP,
		"黄赌毒网站": fofaee_assets.ThreatenTypeHDD,
		"域名混淆":  fofaee_assets.ThreatenTypeDomain,
		"其他":    fofaee_assets.ThreatenTypeOther,
	}
	if val, exists := threatMap[threatType]; exists {
		return val
	}
	return fofaee_assets.ThreatenTypeOther
}

func generatePortAssetId(ip string, asset map[string]interface{}, url string, userId uint64) string {
	port := fmt.Sprintf("%v", asset["*端口"])
	protocol := fmt.Sprintf("%v", asset["*协议"])
	subdomain := utils.GetSubdomain(url)
	userIdStr := strconv.FormatUint(userId, 10)
	completeIP := strings.ToLower(utils.CompleteIPV6(ip))

	// 完全保持与PHP一致的逻辑: md5(strtolower(completeIPV6($ip)) . $asset['*端口'] . $asset['*协议'] . getSubdomain($url) . $userId)
	s := completeIP + port + protocol + subdomain + userIdStr
	id := utils.Md5Hash(s)

	// 添加详细调试日志来分析重复ID问题 - 只记录有问题的情况
	shouldLog := false

	// 检查是否是容易产生重复的组合
	if (ip == "************" && port == "443" && protocol == "https") ||
		(ip == "************" && port == "80" && protocol == "http") ||
		(ip == "************" && port == "443" && protocol == "https") ||
		(ip == "************" && port == "80" && protocol == "http") ||
		(ip == "************" && port == "443" && protocol == "https") ||
		(ip == "************" && port == "80" && protocol == "http") {
		shouldLog = true
	}

	if shouldLog {
		log.Warn("AssetsImportDataJob", "生成端口资产ID详情", map[string]interface{}{
			"ip":           ip,
			"complete_ip":  completeIP,
			"port":         port,
			"protocol":     protocol,
			"url":          url,
			"subdomain":    subdomain,
			"user_id":      userId,
			"concat_str":   s,
			"generated_id": id,
		})
	}

	return id
}

func (p *AssetsImportProcessor) syncDomainAssets() {
	// 去重域名列表
	uniqueDomains := utils.ListDistinctNonZero(p.DomainAssets)

	if len(uniqueDomains) > 0 {
		log.Info("AssetsImportDataJob", "当前导入资产需要同步到域名资产", map[string]interface{}{
			"domains": uniqueDomains,
		})

		// 下发域名资产同步的任务
		// 对应PHP: TableAssetsDoaminsSync::dispatch($user_id,null,DomainAssets::DOMAIN_IMPORT,null,null,$domainList);
		log.Infof("AssetsImportDataJob: 准备下发PHP域名同步任务 - user_id=%d, domain_count=%d, domains=%v",
			p.UserId, len(uniqueDomains), uniqueDomains)
		err := asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
			p.UserId,                    // user_id
			nil,                         // task_id (null)
			domain_assets.DOMAIN_IMPORT, // from (DomainAssets::DOMAIN_IMPORT = 4)
			nil,                         // groupId (null)
			nil,                         // domain_task_id (null)
			uniqueDomains,               // import_domains ($domainList)
			nil,                         // flag (null)
			nil,                         // detect_task_id (null)
			nil,                         // organization_discover_task_id (null)
		)
		if err != nil {
			log.Errorf("AssetsImportDataJob: 下发PHP域名同步任务失败 - user_id=%d, error=%v", p.UserId, err)
		} else {
			log.Infof("AssetsImportDataJob: 下发PHP域名同步任务成功 - user_id=%d, domain_count=%d", p.UserId, len(uniqueDomains))
		}
	}
}

// extractInfoFromAsset 提取资产信息
func extractInfoFromAsset(asset map[string]interface{}, key string) []string {
	// 用于返回结果
	infoList := make([]string, 0)

	if info := utils.SafeString(asset[key]); info != "" {
		if !slices.Contains(infoList, info) {
			infoList = append(infoList, info)
		}
	}

	return infoList
}
