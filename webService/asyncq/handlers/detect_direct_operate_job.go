package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/clues_groups"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/mysql/sensitive_keyword"
	"micro-service/pkg/cfg"
	"micro-service/pkg/localization"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"
	"strconv"
	"time"

	es "micro-service/middleware/elastic"
	asyncq "micro-service/pkg/queue_helper"
	pb "micro-service/webService/proto"
)

func DetectDirectOperateJobHandler(ctx context.Context, task *asyncq.Task) error {
	payload, err := parseDetectDirectOperateJobPayload([]byte(task.Payload))
	if err != nil {
		return err
	}
	if payload.TaskName != "cloudRecommend" && payload.TaskName != "detectAssetsEvaluate" && payload.TaskName != "scanAssets" {
		log.Errorf("不支持的任务名称: %s", payload.TaskName)
		return errors.New("不支持的任务名称")
	}

	// 获取测绘任务
	detectAssetTaskModel := detect_assets_tasks.NewModel()
	detectAssetTask, err := detectAssetTaskModel.First(mysql.WithId(payload.DetectTaskId), mysql.WithUserID(payload.UserId))
	if err != nil {
		log.Errorf("获取测绘任务失败: %v", err)
		return err
	}
	if detectAssetTask == nil {
		log.Errorf("测绘任务不存在: %d", payload.DetectTaskId)
		return errors.New("测绘任务不存在")
	}

	switch payload.TaskName {
	case "cloudRecommend":
		log.Infof("[步骤流转][DetectDirectOperateJobHandler] 开始下发云端推荐任务,任务id:%d", payload.DetectTaskId)
		// 更新测绘任务的线索数量
		allClueList, err := updateDetectTask(payload, detectAssetTask, detectAssetTaskModel)
		if err != nil {
			log.Errorf("更新测绘任务的线索数量失败: %v", err)
			return err
		}
		// 如果测绘任务是自动泄漏资产
		err = dispatchSensitiveKeywordJob(detectAssetTask, allClueList, payload)
		if err != nil {
			log.Errorf("下发敏感词任务失败: %v", err)
			return err
		}
		// 发送websocket消息
		websocket_message.PublishSuccess(int64(payload.UserId), "detect_assets_tip2_all", map[string]interface{}{
			"task_id":  payload.DetectTaskId,
			"progress": 100.00,
			"user_id":  payload.UserId,
		})
		// 下发资产推荐任务
		err = cloudRecommend(detectAssetTask)
		if err != nil {
			log.Errorf("下发资产推荐任务失败: %v", err)
			return err
		}
		return nil
	case "detectAssetsEvaluate":
		log.Infof("[步骤流转][DetectDirectOperateJobHandler] 开始下发资产评估任务,任务id:%d", payload.DetectTaskId)
		err = evaluate(detectAssetTask, payload.UserId)
		if err != nil {
			log.Errorf("资产评估失败: %v", err)
			return err
		}
		return nil
	case "scanAssets":
		log.Infof("[步骤流转][DetectDirectOperateJobHandler] 开始下发扫描资产任务,任务id:%d", payload.DetectTaskId)
		// 查找scan_tasks表中status为0的记录
		scanTaskModel := scan_task.NewScanTasksModel()
		scanTaskIds, err := scanTaskModel.FindIds(mysql.WithColumnValue("detect_assets_tasks_id", detectAssetTask.ID))
		if err != nil {
			log.Errorf("查找扫描任务失败: %v", err)
			return err
		}
		// 将scanTaskIds写入测绘任务的ScanTaskIDs字段
		detectAssetTask.ScanTaskIDs = scanTaskIds
		// 更新测绘任务的扫描任务ID
		err = scanAssets(detectAssetTask, payload.UserId)
		if err != nil {
			log.Errorf("扫描资产失败: %v", err)
			return err
		}
		return nil
	default:
		log.Errorf("不支持的任务名称: %s", payload.TaskName)
		return errors.New("不支持的任务名称")
	}
}

// 扫描资产
func scanAssets(detectAssetTask *detect_assets_tasks.DetectAssetsTask, userId uint64) error {
	var err error
	rsp := &pb.ScanRecommendResponse{}
	req := &pb.ScanRecommendRequest{
		UserId:           userId,
		Id:               uint64(detectAssetTask.ID),
		OperateCompanyId: int64(detectAssetTask.CompanyId),
	}
	// 本地化调用
	if cfg.IsLocalClient() {
		err = localization.NewCaller().ScanRecommend(req, rsp)
		if err != nil {
			log.Errorf("扫描资产失败: %v", err)
			return err
		}
	} else {
		rsp, err = pb.GetProtoClient().ScanRecommend(context.Background(), req, microx.SetTimeout(30, 29)...)
		if err != nil {
			log.Errorf("扫描资产失败: %v", err)
			return err
		}
	}
	if rsp.WarnMessage != "" {
		log.Errorf("扫描资产失败: %s", rsp.WarnMessage)
		return errors.New(rsp.WarnMessage)
	}
	// 下发扫描资产任务
	// for _, taskID := range detectAssetTask.ScanTaskIDs {
	// 	err = asyncq.Enqueue(context.Background(), asyncq.ScanAssetJob, &TaskIdPayload{
	// 		TaskId: uint64(taskID),
	// 		UserId: userId,
	// 	})
	// 	if err != nil {
	// 		log.Errorf("下发扫描资产任务(%d)失败: %v", taskID, err)
	// 		return err
	// 	}
	// }
	return nil
}

// 资产评估
func evaluate(detectAssetTask *detect_assets_tasks.DetectAssetsTask, userId uint64) error {
	err := detect_assets_tasks.NewModel().UpdateAny(map[string]interface{}{
		"step":        detect_assets_tasks.StepFour,
		"step_detail": detect_assets_tasks.StepFourPredict,
		"step_status": detect_assets_tasks.StepStatusDone,
	}, mysql.WithId(detectAssetTask.ID))
	if err != nil {
		log.Errorf("更新任务失败: %v", err)
		return err
	}
	// 下发资产评估任务
	err = asyncq.Enqueue(context.Background(), asyncq.UpdateAssetsLevel, &asyncq.UpdateAssetsLevelTaskPayload{
		GroupID:      detectAssetTask.GroupId,
		Flag:         detectAssetTask.ExpendFlags,
		DetectTaskId: fmt.Sprintf("%d", detectAssetTask.ID),
		UserID:       userId,
	})
	if err != nil {
		log.Errorf("下发资产评估任务失败: %v", err)
		return err
	}
	return nil
}

// 云端推荐
func cloudRecommend(detectAssetTask *detect_assets_tasks.DetectAssetsTask) error {
	// 获取任务的return_json
	rj := make(map[string]interface{})
	err := json.Unmarshal([]byte(detectAssetTask.ReturnJson), &rj)
	if err != nil {
		log.Errorf("解析线索总表失败: %v", err)
		return err
	}
	// 查询相关线索
	clueModel := clues.NewCluer()
	allClueList, err := clueModel.ListAll(
		mysql.WithUserID(detectAssetTask.UserId),
		mysql.WithColumnValue("group_id", detectAssetTask.GroupId),
		mysql.WithColumnValue("status", clues.CLUE_DEFAULT_STATUS),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
	)
	allClueIdList := make([]uint64, 0)
	for _, clue := range allClueList {
		allClueIdList = append(allClueIdList, clue.Id)
	}
	// 把线索id写入任务的return_json中
	rj[fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandAll)] = allClueIdList
	newReturnJson, err := json.Marshal(rj)
	if err != nil {
		log.Errorf("收集线索失败: %v", err)
		return err
	}
	// 更新任务return_json
	detectAssetTask.ReturnJson = string(newReturnJson)
	// 更新任务step_detail
	detectAssetTask.StepDetail = detect_assets_tasks.StepTwoFormAll
	// 更新任务step_status
	detectAssetTask.StepStatus = detect_assets_tasks.StepStatusDone
	// 更新任务
	detectAssetTaskModel := detect_assets_tasks.NewModel()
	err = detectAssetTaskModel.UpdateAny(map[string]interface{}{
		"return_json": string(newReturnJson),
		"step_detail": detect_assets_tasks.StepTwoFormAll,
		"step_status": detect_assets_tasks.StepStatusDone,
	}, mysql.WithId(detectAssetTask.ID))
	if err != nil {
		log.Errorf("更新任务失败: %v", err)
		return err
	}

	// todo 复制场景线索内容

	// 更新线索状态
	err = clueModel.UpdateAny(map[string]interface{}{
		"status": clues.CLUE_PASS_STATUS,
	}, mysql.WithColumnValue("group_id", detectAssetTask.GroupId), mysql.WithColumnValue("status", clues.CLUE_DEFAULT_STATUS))
	if err != nil {
		log.Errorf("更新线索状态失败: %v", err)
		return err
	}
	// 更新线索组状态
	clueGroupModel := clues_groups.NewCluesGrouper()
	err = clueGroupModel.UpdateAny(map[string]interface{}{
		"is_show": clues_groups.Show,
	}, mysql.WithId(detectAssetTask.GroupId))
	if err != nil {
		log.Errorf("更新线索组状态失败: %v", err)
		return err
	}

	if detectAssetTask.CompanyId > 0 {
		// 检查是否达到推荐次数限制
		companyModel := company.NewCompanyModel()
		companyInfo, err := companyModel.First(mysql.WithId(detectAssetTask.CompanyId))
		if err != nil || companyInfo.ID == 0 {
			log.Errorf("获取企业失败: %v", err)
			return err
		}
		limitCount, err := company.CheckLimitById(int64(detectAssetTask.CompanyId), company.LIMIT_TYPE_CLOUD_REC, 1)
		if err != nil {
			log.Errorf("检查推荐次数限制失败: %v", err)
			return err
		}
		if limitCount < 0 {
			log.Errorf("推荐次数达到最大限制: %d", limitCount)
			return errors.New("推荐次数达到最大限制")
		}
		// 更新任务
		detectAssetTaskModel.UpdateAny(map[string]interface{}{
			"is_intellect_failed": detect_assets_tasks.ModeSmartPause,
		}, mysql.WithId(detectAssetTask.ID))
		// 发送websocket消息
		websocket_message.PublishSuccess(int64(detectAssetTask.UserId), "detect_direct_operate", map[string]interface{}{
			"step":        detectAssetTask.Step,
			"step_detail": detectAssetTask.StepDetail,
			"step_status": detectAssetTask.StepStatus,
			"user_id":     detectAssetTask.UserId,
			"error_msg":   "推荐次数达到最大限制",
		})
		// 更新企业推荐次数
		err = companyInfo.UpdateLimit(company.LIMIT_TYPE_CLOUD_REC, 1, true)
		if err != nil {
			log.Errorf("更新企业推荐次数失败: %v", err)
			return err
		}
	}
	// 获取已确认的线索
	allPassClueList, err := clueModel.ListAll(mysql.WithColumnValue("group_id", detectAssetTask.GroupId), mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS), mysql.WithColumnValue("is_deleted", clues.NOT_DELETE), mysql.WithValuesIn("type", []int{clues.TYPE_IP, clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN, clues.TYPE_ICP, clues.TYPE_CERT, clues.TYPE_LOGO}))
	if err != nil {
		log.Errorf("获取线索失败: %v", err)
		return err
	}
	allPassClueIdList := make([]uint64, 0)
	for _, clue := range allPassClueList {
		allPassClueIdList = append(allPassClueIdList, clue.Id)
	}
	// 生成任务
	taskName := fmt.Sprintf("资产测绘智能模式推荐任务_%s", time.Now().Format("2006-01-02 15:04:05"))
	flag := utils.Md5Hash(fmt.Sprintf("%d_%s_%v", detectAssetTask.UserId, taskName, allPassClueIdList))
	// 将生成的所有flag写到测绘任务表的expend_flags字段，并且标记当前任务的step为第三步
	err = detectAssetTaskModel.UpdateAny(map[string]interface{}{
		"expend_flags": flag,
		"step":         detect_assets_tasks.StepThree,
		"step_detail":  detect_assets_tasks.StepThreeRecommendAll,
		"step_status":  detect_assets_tasks.StepStatusDone,
	}, mysql.WithId(detectAssetTask.ID))
	if err != nil {
		log.Errorf("更新任务失败: %v", err)
		return err
	}
	// todo 异步调用生成数据泄露监控的数据

	// 添加推荐记录
	err = addRecommendRecord(detectAssetTask.UserId, detectAssetTask.CompanyId, detectAssetTask.GroupId, taskName, flag, allPassClueIdList, allPassClueList, uint64(detectAssetTask.ID), detectAssetTask.UserId)
	if err != nil {
		log.Errorf("添加推荐记录失败: %v", err)
		return err
	}

	// 下发推荐任务
	err = asyncq.Enqueue(context.Background(), asyncq.RecommendAssetJob, &asyncq.RecommendAssetJobPayload{
		RecommendRecordId: flag,
		UserId:            detectAssetTask.UserId,
		DetectTaskId:      uint64(detectAssetTask.ID),
	})
	if err != nil {
		log.Errorf("下发推荐任务失败: %v", err)
		return err
	}
	return nil
}

// addRecommendRecord 添加推荐记录
// 对应PHP中的addRecord方法
func addRecommendRecord(userId, companyId, groupId uint64, taskName, flag string, clueIds []uint64, clueArr []*clues.Clue, expendId, operatorId uint64) error {
	// 检查线索是否为空
	if len(clueArr) == 0 {
		return fmt.Errorf("线索不能为空!")
	}

	// 获取分组名称
	var groupName string
	if groupId > 0 {
		group, err := clues_groups.NewCluesGrouper().First(
			mysql.WithColumnValue("id", groupId),
		)
		if err == nil {
			groupName = group.Name
		}
	}

	// 按类型分组线索
	clueTypes := make(map[int][]map[string]interface{})
	for _, clue := range clueArr {
		item := map[string]interface{}{
			"id":                clue.Id,
			"type":              clue.Type,
			"content":           clue.Content,
			"group_id":          clue.GroupId,
			"clue_company_name": clue.ClueCompanyName,
			"source":            clue.Source,
			"punycode_domain":   clue.PunycodeDomain,
		}

		// 处理LOGO类型线索
		if clue.Type == clues.TYPE_LOGO && clue.Content != "" {
			item["content"] = storage.GenDownloadUrl(clue.Content, fmt.Sprintf("%d", clue.Hash))
		}

		if _, ok := clueTypes[clue.Type]; !ok {
			clueTypes[clue.Type] = []map[string]interface{}{}
		}
		clueTypes[clue.Type] = append(clueTypes[clue.Type], item)
	}

	// 判断是否只有子域名类型线索推荐资产
	typeArr := make([]int, 0)
	for _, clue := range clueArr {
		found := false
		for _, t := range typeArr {
			if t == clue.Type {
				found = true
				break
			}
		}
		if !found {
			typeArr = append(typeArr, clue.Type)
		}
	}
	isOnlySubdomain := 0
	if len(typeArr) == 1 && typeArr[0] == clues.TYPE_SUBDOMAIN {
		log.Infof("[RecommendAssets] 只有子域名推荐资产, expendId=%d, id=%s", expendId, flag)
		isOnlySubdomain = 1
	}

	// 检查记录是否已存在
	existingRecord, err := recommend_record.NewRecommendRecordModel().FindByID(flag)
	if err == nil && existingRecord.Id != "" {
		// 记录已存在，不需要创建
		return nil
	}

	// 构建推荐记录数据
	recordMap := map[string]interface{}{
		"id":                     flag,
		"flag":                   flag,
		"task_name":              taskName,
		"cron_id":                nil,
		"group_id":               int(groupId),
		"group_name":             groupName,
		"count":                  0,
		"user_id":                int(userId),
		"company_id":             int(companyId),
		"progress":               0,
		"status":                 0, // StatusDefault
		"confirm":                0, // ConfirmDefault
		"op_id":                  int(operatorId),
		"detect_assets_tasks_id": int(expendId),
		"clue_id":                clueIds,
		"is_only_subdomain":      isOnlySubdomain,
		"created_at":             time.Now().Format(utils.DateTimeLayout),
		"updated_at":             time.Now().Format(utils.DateTimeLayout),
	}

	if subdomainClues, ok := clueTypes[clues.TYPE_SUBDOMAIN]; ok {
		recordMap["subdomain"] = subdomainClues
	}
	if domainClues, ok := clueTypes[clues.TYPE_DOMAIN]; ok {
		recordMap["domain"] = domainClues
	}
	if certClues, ok := clueTypes[clues.TYPE_CERT]; ok {
		recordMap["cert"] = certClues
	}
	if icpClues, ok := clueTypes[clues.TYPE_ICP]; ok {
		recordMap["icp"] = icpClues
	}
	if keywordClues, ok := clueTypes[clues.TYPE_KEYWORD]; ok {
		recordMap["title"] = keywordClues
	}
	if logoClues, ok := clueTypes[clues.TYPE_LOGO]; ok {
		recordMap["logo"] = logoClues
	}

	// 创建推荐记录
	_, err = es.GetEsClient().Index().Index("foradar_recommend_record").Type("record").
		Id(flag).BodyJson(recordMap).Do(context.Background())
	if err != nil {
		return err
	}

	return nil
}

// 下发敏感词任务
func dispatchSensitiveKeywordJob(detectAssetTask *detect_assets_tasks.DetectAssetsTask, allClueList []*clues.Clue, payload *asyncq.DetectDirectOperateJobPayload) error {
	if detectAssetTask.IsAutoLeakAsset == 1 {
		sensitiveKeywordList := make([]*sensitive_keyword.SensitiveKeyword, 0)
		// 查询相关线索
		for _, clue := range allClueList {
			if clue.Type == clues.TYPE_IP || clue.Type == clues.TYPE_DOMAIN || clue.Type == clues.TYPE_SUBDOMAIN {
				sensitiveKeywordList = append(sensitiveKeywordList, &sensitive_keyword.SensitiveKeyword{
					Name:         clue.Content,
					Type:         sensitive_keyword.LEAK_DATA_TYPE,
					UserId:       payload.UserId,
					DetectTaskId: payload.DetectTaskId,
					CompanyId:    detectAssetTask.CompanyId,
					CreatedAt:    time.Now(),
				})
			}
		}
		if len(sensitiveKeywordList) > 0 {
			// 插入敏感关键词
			sensitiveKeywordModel := sensitive_keyword.NewModel()
			err := sensitiveKeywordModel.CreateBatch(sensitiveKeywordList)
			if err != nil {
				log.Errorf("插入敏感关键词失败: %v", err)
				return err
			}
			// 下发任务
			for _, sensitiveKeyword := range sensitiveKeywordList {
				switch sensitiveKeyword.Type {
				case sensitive_keyword.ALL_TYPE:
					DispatchLaravelCreateSensitiveKeywordJob(payload.UserId, sensitiveKeyword.Name, true, true, strconv.FormatUint(payload.DetectTaskId, 10))
				case sensitive_keyword.NEW_DATA_TYPE:
					DispatchLaravelCreateSensitiveKeywordJob(payload.UserId, sensitiveKeyword.Name, false, true, strconv.FormatUint(payload.DetectTaskId, 10))
				default:
					DispatchLaravelCreateSensitiveKeywordJob(payload.UserId, sensitiveKeyword.Name, true, false, strconv.FormatUint(payload.DetectTaskId, 10))
				}
			}
		}
	}
	return nil
}

// 更新测绘任务的线索数量
func updateDetectTask(payload *asyncq.DetectDirectOperateJobPayload, detectAssetTask *detect_assets_tasks.DetectAssetsTask, detectAssetTaskModel detect_assets_tasks.DetectAssetsTaskModel) ([]*clues.Clue, error) {
	// 查询相关线索
	clueModel := clues.NewCluer()
	allClueList, err := clueModel.ListAll(
		mysql.WithUserID(payload.UserId),
		mysql.WithColumnValue("group_id", detectAssetTask.GroupId),
		mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
	)
	if err != nil {
		log.Errorf("查询相关线索失败: %v", err)
		return nil, err
	}
	clueNum := 0
	for _, clue := range allClueList {
		if clue.Type == clues.TYPE_LOGO || clue.Type == clues.TYPE_IP || clue.Type == clues.TYPE_DOMAIN || clue.Type == clues.TYPE_ICP || clue.Type == clues.TYPE_CERT || clue.Type == clues.TYPE_SUBDOMAIN {
			clueNum += 1
		}
	}
	// 更新测绘任务的线索数量
	err = detectAssetTaskModel.UpdateAny(map[string]interface{}{
		"clues_count": clueNum,
	}, mysql.WithId(payload.DetectTaskId))
	if err != nil {
		log.Errorf("更新测绘任务的线索数量失败: %v", err)
		return nil, err
	}
	return allClueList, nil
}

// parseDetectDirectOperateJobPayload 解析直接操作任务参数
func parseDetectDirectOperateJobPayload(data []byte) (*asyncq.DetectDirectOperateJobPayload, error) {
	payload := &asyncq.DetectDirectOperateJobPayload{}
	err := json.Unmarshal(data, &payload)
	if err != nil {
		log.Errorf("解析直接操作任务参数失败: %v", err)
		return payload, err
	}
	if payload.TaskName == "" {
		log.Errorf("任务名称不能为空")
		return payload, errors.New("任务名称不能为空")
	}
	if payload.DetectTaskId <= 0 {
		log.Errorf("测绘任务ID不能为0")
		return payload, errors.New("测绘任务ID不能为0")
	}
	if payload.UserId <= 0 {
		log.Errorf("用户ID不能为0")
		return payload, errors.New("用户ID不能为0")
	}
	return payload, nil
}
