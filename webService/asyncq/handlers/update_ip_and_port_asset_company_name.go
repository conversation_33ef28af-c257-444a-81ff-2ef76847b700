package handlers

import (
	"context"
	"encoding/json"
	"fmt"

	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"

	es_utils "micro-service/middleware/elastic"

	"github.com/olivere/elastic"
)

// UpdateIpAndPortAssetCompanyNameJob 更新IP端口资产公司名称
// 消息体:
//
//	{
//		"user_id": 1,      // 用户ID，必填
//		"task_id": 1,      // 任务ID，必填
//	}
func UpdateIpAndPortAssetCompanyNameJob(ctx context.Context, t *asyncq.Task) error {
	payloadInfo, err := parsePayloadForUpdateIpAndPortAssetCompanyName([]byte(t.Payload))
	if err != nil {
		return err
	}

	log.Info("UpdateIpAndPortAssetsCompantyNameJob", "更新ip端口数据维度的企业名称开始", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"task_id": payloadInfo.TaskId,
	})

	// 查询ForadarAssets数据 - 对应PHP的ForadarAssets::query()->where('task_id',$this->task_id)->where('user_id',$this->user_id)->get()
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("task_id", payloadInfo.TaskId))
	query.Must(elastic.NewTermQuery("user_id", payloadInfo.UserId))

	assets, err := es_utils.All[foradar_assets.ForadarAsset](500, query, nil, "id", "ip", "clue_company_name")
	if err != nil {
		log.Errorf("UpdateIpAndPortAssetsCompantyNameJob 查询ForadarAssets失败: %v", err)
		return err
	}

	if len(assets) == 0 {
		log.Info("UpdateIpAndPortAssetsCompantyNameJob", "没有找到需要更新的资产数据", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
		})
		return nil
	}

	// 按IP分组 - 对应PHP的->groupBy('ip')->toArray()
	ipGroupedAssets := make(map[string][]*foradar_assets.ForadarAsset)
	for _, asset := range assets {
		if asset.Ip == "" {
			continue
		}
		ipGroupedAssets[asset.Ip] = append(ipGroupedAssets[asset.Ip], asset)
	}

	// 处理每个IP的公司名称更新 - 对应PHP的foreach ($list as $key=>$value)
	for ip, ipAssets := range ipGroupedAssets {
		// 提取公司名称 - 对应PHP的collect($value)->pluck('clue_company_name')->filter()->collapse()->unique()->values()->toArray()
		companyNames := extractCompanyNamesFromAssets(ipAssets)

		if len(companyNames) == 0 {
			continue
		}

		finalCompanyNames := companyNames

		// 如果有多个公司名称，查询IP维度的数据 - 对应PHP的count($company) > 1逻辑
		if len(companyNames) > 1 {
			log.Warn("UpdateIpAndPortAssetsCompantyNameJob", "这个ip上有2个公司名啊", map[string]interface{}{
				"clue_company_name": companyNames,
				"ip":                ip,
				"user_id":           payloadInfo.UserId,
			})

			// 查询IP维度的数据 - 对应PHP的IpAssets::query()->where('user_id',$this->user_id)->where('ip',$key)->first()
			ipAsset, err := queryIpAssetByUserIdAndIp(ctx, payloadInfo.UserId, ip)
			if err == nil && ipAsset != nil && len(ipAsset.ClueCompanyName) > 0 {
				ipCompanyNames := extractCompanyNamesFromIpAsset(ipAsset)
				if len(ipCompanyNames) > 0 {
					finalCompanyNames = ipCompanyNames
				}
			}
		}

		// 更新ForadarAssets - 对应PHP的ForadarAssets::query()->where('user_id',$this->user_id)->where('ip',$key)->update(['clue_company_name'=>$company])
		if len(finalCompanyNames) > 0 {
			log.Info("UpdateIpAndPortAssetsCompantyNameJob", "更新了数据", map[string]interface{}{
				"clue_company_name": finalCompanyNames,
				"ip":                ip,
				"user_id":           payloadInfo.UserId,
			})

			err := updateForadarAssetsCompanyName(ctx, payloadInfo.UserId, ip, finalCompanyNames)
			if err != nil {
				log.Errorf("UpdateIpAndPortAssetsCompantyNameJob 更新IP[%s]的公司名称失败: %v", ip, err)
				// 继续处理其他IP，不中断整个任务
				continue
			}
		}
	}

	log.Info("UpdateIpAndPortAssetsCompantyNameJob", "更新ip端口数据维度的企业名称结束", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"task_id": payloadInfo.TaskId,
	})

	return nil
}

// parsePayloadForUpdateIpAndPortAssetCompanyName 解析任务参数
func parsePayloadForUpdateIpAndPortAssetCompanyName(payload []byte) (*asyncq.UpdateIpAndPortAssetCompanyNameJobPayload, error) {
	var payloadInfo asyncq.UpdateIpAndPortAssetCompanyNameJobPayload
	if err := json.Unmarshal(payload, &payloadInfo); err != nil {
		return nil, fmt.Errorf("解析任务参数失败: %v", err)
	}

	// 参数验证
	if payloadInfo.UserId == 0 {
		return nil, fmt.Errorf("用户ID不能为空")
	}
	if payloadInfo.TaskId == 0 {
		return nil, fmt.Errorf("任务ID不能为空")
	}

	return &payloadInfo, nil
}

// extractCompanyNamesFromAssets 从资产列表中提取公司名称 - 对应PHP的collect($value)->pluck('clue_company_name')->filter()->collapse()->unique()->values()->toArray()
func extractCompanyNamesFromAssets(assets []*foradar_assets.ForadarAsset) []string {
	var allCompanyNames []string

	// 收集所有公司名称
	for _, asset := range assets {
		if asset.ClueCompanyName == nil {
			continue
		}

		// 处理ClueCompanyName字段（可能是string或[]interface{}）
		companyNames := extractStringArrayFromInterface(asset.ClueCompanyName)
		allCompanyNames = append(allCompanyNames, companyNames...)
	}

	// 去重并过滤空值 - 对应PHP的filter()->unique()->values()
	return utils.ListDistinctNonZero(allCompanyNames)
}

// extractStringArrayFromInterface 从interface{}中提取字符串数组
func extractStringArrayFromInterface(data interface{}) []string {
	if data == nil {
		return nil
	}

	var result []string

	switch v := data.(type) {
	case string:
		if v != "" {
			result = append(result, v)
		}
	case []interface{}:
		for _, item := range v {
			if str, ok := item.(string); ok && str != "" {
				result = append(result, str)
			}
		}
	case []string:
		for _, str := range v {
			if str != "" {
				result = append(result, str)
			}
		}
	}

	return result
}

// queryIpAssetByUserIdAndIp 查询IP维度的资产信息 - 对应PHP的IpAssets::query()->where('user_id',$this->user_id)->where('ip',$key)->first()
func queryIpAssetByUserIdAndIp(ctx context.Context, userId uint64, ip string) (*fofaee_assets.FofaeeAssets, error) {
	// 生成fofaee_assets的ID（格式：userId_ip）
	id := fofaee_assets.GenId(int(userId), ip)

	// 查询IP维度的资产信息
	ipAsset, err := es_utils.GetById[fofaee_assets.FofaeeAssets](id)
	if err != nil {
		return nil, err
	}

	return ipAsset, nil
}

// extractCompanyNamesFromIpAsset 从IP资产中提取公司名称 - 对应PHP的$info['clue_company_name'] ?? []
func extractCompanyNamesFromIpAsset(ipAsset *fofaee_assets.FofaeeAssets) []string {
	if ipAsset == nil || len(ipAsset.ClueCompanyName) == 0 {
		return nil
	}

	var companyNames []string
	for _, name := range ipAsset.ClueCompanyName {
		if nameStr, ok := name.(string); ok && nameStr != "" {
			companyNames = append(companyNames, nameStr)
		}
	}

	return companyNames
}

// updateForadarAssetsCompanyName 更新ForadarAssets中指定IP的公司名称 - 对应PHP的ForadarAssets::query()->where('user_id',$this->user_id)->where('ip',$key)->update(['clue_company_name'=>$company])
func updateForadarAssetsCompanyName(ctx context.Context, userId uint64, ip string, companyNames []string) error {
	// 构建更新查询条件
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermQuery("ip.keyword", ip))

	// 构建更新数据
	updateData := map[string]interface{}{
		"clue_company_name": companyNames,
	}

	// 执行批量更新
	model := foradar_assets.NewForadarAssetModel()
	err := model.UpdateByQuery(ctx, query, updateData)
	if err != nil {
		return fmt.Errorf("UpdateByQuery失败: %v", err)
	}

	return nil
}
