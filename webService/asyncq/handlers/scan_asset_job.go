package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/network"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"
	"net"
	"os"
	"slices"
	"strings"
	"sync"
	"time"

	es_utils "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/forbid"
	"micro-service/middleware/mysql/ip_history"
	"micro-service/middleware/mysql/port_group"
	"micro-service/middleware/mysql/scan_task"
	task_model "micro-service/middleware/mysql/task"
	"micro-service/scanService/handler/microkernel"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// ScanAssetJobHandler 处理扫描资产任务的主要函数
func ScanAssetJobHandler(ctx context.Context, task *asyncq.Task) error {
	log.Infof("[步骤流转][ScanAssetJobHandler] 开始处理扫描资产任务 payload: %s", string(task.Payload))
	// 解析任务ID
	taskIdPayload := asyncq.TaskIdPayload{}
	err := json.Unmarshal([]byte(task.Payload), &taskIdPayload)
	if err != nil {
		log.Error("ScanAssetJobHandler", "解析任务ID失败", map[string]interface{}{
			"payload": string(task.Payload),
			"error":   err.Error(),
		})
		return err
	}

	log.Infof("[步骤流转][ScanAssetJobHandler] 处理消息,任务id:%d", taskIdPayload.TaskId)

	// 根据hostname加分布式锁，如果锁失败，则把消息重新入队
	hostname, err := os.Hostname()
	if err != nil {
		log.Error("ScanAssetJobHandler", "获取hostname失败", map[string]interface{}{
			"error": err.Error(),
		})
		return err
	}
	lockkey := cache.GetLockKey("scan_asset_job", hostname)
	lock := redis.Lock(lockkey, 24*time.Hour)
	if !lock {
		log.Infof("[步骤流转][ScanAssetJobHandler] 获取锁失败，消息重新入队,任务id:%d", taskIdPayload.TaskId)
		err = asyncq.Enqueue(context.Background(), asyncq.ScanAssetJob, taskIdPayload)
		if err != nil {
			log.Errorf("ScanAssetJobHandler, 消息重新入队失败,error:%v", err)
		}
		return nil
	}
	taskID := taskIdPayload.TaskId

	defer func() {
		// 释放锁
		redis.UnLock(lockkey)
		// 异常恢复处理
		if r := recover(); r != nil {
			log.Error("ScanAssetJobHandler", "任务执行异常", map[string]interface{}{
				"task_id": taskID,
				"error":   fmt.Sprintf("%v", r),
			})
			handleTaskError(taskID, fmt.Errorf("任务执行异常: %v", r))
		}
	}()

	taskModel := scan_task.NewScanTasksModel()
	// 查找任务
	scanTask, err := taskModel.FindByID(taskID)
	if err != nil || scanTask == nil {
		log.Warn("ScanAssetJobHandler", fmt.Sprintf(" 任务不存在,task_id:%d", taskID), "")
		return nil
	}

	// 检查任务状态和进度
	if err := checkTaskStatus(scanTask); err != nil {
		return err
	}

	// 检查禁扫时间
	if isForbidTime(scanTask) {
		return nil
	}

	// 检查IPv6扫描配置
	if !cfg.LoadCommon().MonitorAssetScanIpv6 && scanTask.IpType == port_group.IpTypev6 {
		// 任务重新排队（这里简化处理，实际需要实现消息重入队列机制）
		log.Info("ScanAssetJobHandler", "IPv6扫描已禁用，任务重新排队", fmt.Sprintf("TaskId:%d", taskID))
		taskModel.UpdateByMap([]mysql.HandleFunc{mysql.WithColumnValue("id", taskID)}, map[string]interface{}{
			"status":        scan_task.StatusPause,
			"forbid_status": 1,
		})
		return fmt.Errorf("IPv6扫描已禁用")
	}

	// 检查任务是否已被其他节点消费
	count, err := taskModel.Count(mysql.WithColumnValue("id", taskID), mysql.WithColumnNotValue("status", scan_task.StatusPause), mysql.WithColumnNotNull("node"), mysql.WithColumnNotValue("node", ""))
	if err != nil {
		log.Error("ScanAssetJobHandler", "检查任务是否已被其他节点消费失败", map[string]interface{}{
			"task_id": taskID,
			"error":   err.Error(),
		})
	}
	if count > 0 {
		log.Info("ScanAssetJobHandler", fmt.Sprintf("TaskId:%d 任务已被消费,消息重新投送!", taskID), "")
		return nil
	}

	// 记录任务状态和进度
	log.Warn("ScanAssetJobHandler", fmt.Sprintf("TaskId:%d", taskID),
		fmt.Sprintf("状态:%d,进度：%f", scanTask.Status, scanTask.Progress))

	// 处理暂停任务的继续执行
	if scanTask.Status == scan_task.StatusPause {
		err = handlePausedTask(scanTask)
		if err != nil {
			log.Error("ScanAssetJobHandler", "处理暂停任务的继续执行失败", map[string]interface{}{
				"task_id": scanTask.ID,
				"error":   err.Error(),
			})
		}
		return err
	} else {
		// 处理新任务
		err = handleNewTask(scanTask)
		if err != nil {
			log.Errorf("[步骤流转][ScanAssetJobHandler] 扫描任务下发失败,task_id:%d,error:%v", scanTask.ID, err)
		} else {
			log.Infof("[步骤流转][ScanAssetJobHandler] 成功下发扫描任务,task_id:%d", scanTask.ID)
		}
		return err
	}
}

// checkTaskStatus 检查任务状态
func checkTaskStatus(task *scan_task.ScanTasks) error {
	// 任务已完成
	if task.Status == scan_task.StatusFinished {
		log.Info("ScanAssetJobHandler", fmt.Sprintf("TaskId:%d", task.ID), "任务已完成")
		return nil
	}

	// 任务进行中但进度已达到100%，标记为完成
	if task.Status == scan_task.StatusDoing && task.Progress == 100 {
		taskModel := scan_task.NewScanTasksModel()
		err := taskModel.UpdateByMap([]mysql.HandleFunc{mysql.WithColumnValue("id", task.ID)}, map[string]interface{}{
			"status": scan_task.StatusFinished,
		})
		if err != nil {
			log.Error("ScanAssetJobHandler", "更新任务状态失败", map[string]interface{}{
				"task_id": task.ID,
				"error":   err.Error(),
			})
		}
		log.Info("ScanAssetJobHandler", fmt.Sprintf("TaskId:%d", task.ID), "已经完成的任务进到running")
		return nil
	}

	return nil
}

// isForbidTime 检查是否在禁扫时间
// true:在禁扫时间,false:不在禁扫时间
func isForbidTime(task *scan_task.ScanTasks) bool {
	forbidTimeModel := forbid.NewForbidTimeModel()
	if forbidTimeModel.IsForbidTime(task.UserId) {
		taskId := uint64(task.ID)
		// 暂停正在扫描的任务
		if task.Step == scan_task.StepScanning && task.Status == scan_task.StatusDoing {
			microkernel.GetInstance().Pause(taskId)
		}

		// 更新任务状态为暂停
		updates := map[string]interface{}{
			"status":        scan_task.StatusPause,
			"puase_at":      time.Now(),
			"forbid_status": 1,
		}
		taskRepo := scan_task.NewScanTasksModel()
		err := taskRepo.UpdateByMap([]mysql.HandleFunc{mysql.WithColumnValue("id", task.ID)}, updates)
		if err != nil {
			log.Error("ScanAssetJobHandler", "更新任务状态失败", map[string]interface{}{
				"task_id": task.ID,
				"error":   err.Error(),
			})
		}

		log.Info("ScanAssetJobHandler", fmt.Sprintf("TaskId:%d", task.ID), "禁扫时间暂停扫描")

		// 重新获取任务信息用于WebSocket通知
		updatedTask, _ := taskRepo.FindByID(taskId)
		if updatedTask != nil {
			// 发送WebSocket通知
			wsData := map[string]interface{}{
				"task_id":  updatedTask.ID,
				"status":   scan_task.StatusPause,
				"step":     updatedTask.Step,
				"progress": updatedTask.Progress,
				"target":   nil,
				"type":     0,
				"user_id":  updatedTask.UserId,
				"start_at": updatedTask.StartAt,
			}
			websocket_message.PublishSuccess(int64(updatedTask.UserId), "scan_task_progress", wsData)
		}

		return true
	}
	return false
}

// handlePausedTask 处理暂停任务的继续执行
func handlePausedTask(task *scan_task.ScanTasks) error {
	log.Info("ScanAssetJobHandler", fmt.Sprintf("TaskId:%d", task.ID), "暂停的任务继续扫描")

	// 更新任务状态为运行中
	updates := map[string]interface{}{
		"status": scan_task.StatusDoing,
		"node":   cfg.LoadCommon().Node,
	}
	taskRepo := scan_task.NewScanTasksModel()
	err := taskRepo.UpdateByMap([]mysql.HandleFunc{mysql.WithColumnValue("id", task.ID)}, updates)
	if err != nil {
		log.Error("ScanAssetJobHandler", "更新任务状态失败", map[string]interface{}{
			"task_id": task.ID,
			"error":   err.Error(),
		})
		return err
	}

	// 如果任务在扫描步骤，重新添加扫描任务
	if task.Step == scan_task.StepScanning {
		err = addScanTask(task)
		if err != nil {
			log.Error("ScanAssetJobHandler", "重新添加扫描任务失败", map[string]interface{}{
				"task_id": task.ID,
				"error":   err.Error(),
			})
		}
		if task.StartAt.IsZero() {
			err1 := taskRepo.UpdateByMap([]mysql.HandleFunc{mysql.WithColumnValue("id", task.ID)}, map[string]interface{}{
				"start_at": time.Now(),
			})
			if err1 != nil {
				log.Error("ScanAssetJobHandler", "更新任务开始时间失败", map[string]interface{}{
					"task_id": task.ID,
					"error":   err1.Error(),
				})
			}
		}
		return err
	}

	return nil
}

// handleNewTask 处理新任务
func handleNewTask(task *scan_task.ScanTasks) error {
	// 更新任务状态为运行中并设置开始时间
	updates := map[string]interface{}{
		"status": scan_task.StatusDoing,
		"node":   cfg.LoadCommon().Node,
	}
	taskRepo := scan_task.NewScanTasksModel()
	err := taskRepo.UpdateByMap([]mysql.HandleFunc{mysql.WithColumnValue("id", task.ID)}, updates)
	if err != nil {
		log.Error("ScanAssetJobHandler", "更新任务状态失败", map[string]interface{}{
			"task_id": task.ID,
			"error":   err.Error(),
		})
		return err
	}

	// 添加扫描任务
	err = addScanTask(task)
	if err != nil {
		log.Error("ScanAssetJobHandler", "添加扫描任务失败", map[string]interface{}{
			"task_id": task.ID,
			"error":   err.Error(),
		})
		return err
	}
	if task.StartAt == nil || task.StartAt.IsZero() {
		err1 := taskRepo.UpdateByMap([]mysql.HandleFunc{mysql.WithColumnValue("id", task.ID)}, map[string]interface{}{
			"start_at": time.Now(),
		})
		if err1 != nil {
			log.Error("ScanAssetJobHandler", "更新任务开始时间失败", map[string]interface{}{
				"task_id": task.ID,
				"error":   err1.Error(),
			})
		}
	}
	// 死循环阻塞查询状态
	for {
		select {
		case <-time.After(time.Hour * 24):
			log.Info("ScanAssetJobHandler", fmt.Sprintf("TaskId:%d", task.ID), "任务正在运行中")
			return nil
		default:
			t, err := taskRepo.FindByID(uint64(task.ID))
			if err != nil {
				log.Error("ScanAssetJobHandler", "查询任务失败", map[string]interface{}{
					"task_id": task.ID,
					"error":   err.Error(),
				})
				return err
			}
			if t != nil && t.Status != scan_task.StatusDoing {
				log.Info("ScanAssetJobHandler", fmt.Sprintf("TaskId:%d", task.ID), "任务已经完成")
				return nil
			}
			if t != nil && t.Status == scan_task.StatusDoing && int(t.Progress) >= 100 {
				log.Info("ScanAssetJobHandler", fmt.Sprintf("TaskId:%d", task.ID), "任务进度100，状态不是完成，更新成完成")
				err := taskRepo.UpdateByMap([]mysql.HandleFunc{mysql.WithColumnValue("id", task.ID)}, map[string]interface{}{
					"status": scan_task.StatusFinished,
				})
				if err != nil {
					log.Error("ScanAssetJobHandler", "更新任务状态失败", map[string]interface{}{
						"task_id": task.ID,
						"error":   err.Error(),
					})
				}
				return nil
			}
			log.Infof("ScanAssetJobHandler,任务正在运行中,task_id:%d,progress:%f", task.ID, t.Progress)
			time.Sleep(15 * time.Second)
		}
	}
}

// addScanTask 添加扫描任务
func addScanTask(task *scan_task.ScanTasks) error {
	if task.Step == scan_task.StepSyncData {
		log.Infof("ScanAssetJobHandler,任务正在同步数据,不添加扫描任务,task_id:%d", task.ID)
		return nil
	}

	errs := make([]error, 0)
	// 转换任务信息
	taskInfo := convertTaskToTaskInfo(task)

	wg := sync.WaitGroup{}
	// 处理host信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 处理host信息
		hostInfo, err := processHost(task)
		if err != nil {
			errs = append(errs, err)
			return
		}
		taskInfo.Hostinfos = hostInfo

		// 列表端口扫描
		if task.PortRange == scan_task.PORT_RANGE_PROBE {
			// 获取最优端口IP组合
			portGroups, err := getOptimalPortIps(task, hostInfo, task.UserId)
			if err != nil {
				errs = append(errs, err)
				return
			}
			taskInfo.PortGroup = portGroups

			// 如果最优端口IP组合为空，直接标记该任务完成
			if len(portGroups) == 0 {
				log.WithContextInfof(context.Background(), "ScanAssetJobHandler portGroups empty: %d", task.ID)
				updateTaskStatus(task)
			}
		}
	}()

	// 获取扫描目标IP
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 获取扫描目标IP
		targetIPs, err := getScanTargetIpsForCloud(task)
		if err != nil {
			errs = append(errs, err)
			return
		}
		// 云端推荐资产/ 本地扫描
		if task.AssetType == scan_task.TASK_ASSET_CLOUD || task.AssetType == scan_task.TASK_ASSET_MANUAL {
			if len(targetIPs) > 0 {
				filtersIPs, err := getScanTargetIpsForManual(task, targetIPs)
				if err != nil {
					errs = append(errs, err)
					return
				}
				if len(filtersIPs) > 0 {
					taskInfo.IpList = strings.Join(filtersIPs, ",")
				}
			}
		}

		// 非列表端口扫描
		if task.PortRange != scan_task.PORT_RANGE_PROBE {
			// 如果扫描目标为空，直接标记该任务完成
			if taskInfo.IpList == "" {
				log.WithContextInfof(context.Background(), "ScanAssetJobHandler targetIPs empty: %d", task.ID)
				updateTaskStatus(task)
			}
		}
	}()

	// 获取扫描端口
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 获取扫描端口
		scanPorts, err := getScanPorts(task)
		if err != nil {
			errs = append(errs, err)
			return
		}
		if len(scanPorts) > 0 {
			taskInfo.Ports = strings.Join(scanPorts, ",")
		}
	}()

	// 获取黑名单IP
	wg.Add(1)
	go func() {
		defer wg.Done()
		if task.IpType == scan_task.IP_TYPE_V6 {
			taskInfo.Blacklist = ""
		} else {
			// 获取黑名单IP
			blackIPs, err := getBlackIps(task)
			if err != nil {
				errs = append(errs, err)
				return
			}
			if len(blackIPs) > 0 {
				taskInfo.Blacklist = strings.Join(blackIPs, ",")
			}
		}
	}()

	wg.Wait()

	if len(errs) > 0 {
		return fmt.Errorf("获取扫描任务信息失败: %v", errs)
	}

	log.Infof("ScanAssetJobHandler,添加扫描任务参数信息,task_id:%d,taskInfo:%+v", task.ID, taskInfo)
	// 调用扫描服务添加任务
	return microkernel.GetInstance().AddTask(taskInfo)
}

// convertTaskToTaskInfo 转换任务信息
func convertTaskToTaskInfo(task *scan_task.ScanTasks) *microkernel.TaskInfo {
	taskType := "quick"
	// 如果是ipv6，则使用common模式,深度扫描
	if task.IpType == scan_task.IP_TYPE_V6 {
		taskType = "common"
	}
	protocolConcurrency := 1
	if task.ProtocolConcurrency != "" {
		protocolConcurrency = utils.SafeInt(task.ProtocolConcurrency)
	} else if task.Bandwidth != "" {
		bandwidth := utils.SafeInt(task.Bandwidth)
		if bandwidth > 0 {
			protocolConcurrency = bandwidth / 8
		}
	}
	if protocolConcurrency < 1 {
		protocolConcurrency = 1
	}

	taskInfo := &microkernel.TaskInfo{
		TaskId:              cast.ToString(task.ID),
		TaskType:            taskType,
		Bandwidth:           task.Bandwidth,
		GrabConcurrent:      uint64(protocolConcurrency),
		PingScan:            task.PingSwitch == 1,
		IsIpv6:              task.IpType == scan_task.IP_TYPE_V6,
		DeepGetOs:           false,
		DeepGetMac:          false,
		UnknownProtocolIndb: true,
		Extra:               map[string]interface{}{"user_id": cast.ToString(task.UserId)},
	}

	return taskInfo
}

// updateTaskStatus 更新任务状态
func updateTaskStatus(task *scan_task.ScanTasks) error {
	err := scan_task.NewModel().UpdateByMap(map[string]interface{}{
		"status":      scan_task.StatusFinished,
		"end_at":      time.Now(),
		"step":        scan_task.StepFinished,
		"progress":    100.00,
		"use_seconds": 1,
		"asset_num":   0,
		"rule_num":    0,
	}, mysql.WithColumnValue("id", task.ID))
	if err != nil {
		log.Errorf("ScanAssetJobHandler,更新任务状态失败,task_id:%d,error:%v", task.ID, err)
		return err
	}
	return nil
}

// getOptimalPortIps 获取最优端口IP组合
func getOptimalPortIps(task *scan_task.ScanTasks, newHost []string, userId uint64) ([]*microkernel.PortIPGroup, error) {
	// 获取任务基本信息
	taskInfo, err := getTaskBasicInfo(task, userId)
	if err != nil {
		return nil, fmt.Errorf("获取任务基本信息失败: %w", err)
	}

	// 获取确认的域名线索
	confirmedDomains, err := getConfirmedDomains(taskInfo)
	if err != nil {
		return nil, fmt.Errorf("获取确认域名失败: %w", err)
	}

	// 解析域名为IP并插入探测数据
	err = resolveDomainAndInsertProbeData(taskInfo, newHost, confirmedDomains)
	if err != nil {
		log.Error("getOptimalPortIps", "解析域名失败", map[string]interface{}{
			"task_id": task.ID,
			"error":   err.Error(),
		})
	}

	// 构建最优端口IP组合
	return buildOptimalPortIpGroups(uint64(task.ID))
}

// TaskBasicInfo 任务基本信息
type TaskBasicInfo struct {
	TaskID              uint64
	UserID              uint64
	IPType              uint8
	ForbiddenIPs        []string
	SubdomainClues      []string
	DetectAssetsTasksID uint64
}

// getTaskBasicInfo 获取任务基本信息
func getTaskBasicInfo(task *scan_task.ScanTasks, userID uint64) (*TaskBasicInfo, error) {
	taskInfo := &TaskBasicInfo{
		TaskID: uint64(task.ID),
		UserID: userID,
	}

	taskInfo.IPType = uint8(task.IpType)
	taskInfo.DetectAssetsTasksID = uint64(task.DetectAssetsTasksId)

	// 获取禁扫IP
	forbiddenIPs, err := getForbiddenIPs(userID)
	if err != nil {
		return nil, fmt.Errorf("获取禁扫IP失败: %w", err)
	}
	taskInfo.ForbiddenIPs = forbiddenIPs

	// 获取子域名线索
	subdomainClues, err := getSubdomainClues(userID)
	if err != nil {
		return nil, fmt.Errorf("获取子域名线索失败: %w", err)
	}
	taskInfo.SubdomainClues = subdomainClues

	return taskInfo, nil
}

// getForbiddenIPs 获取禁扫IP列表
func getForbiddenIPs(userID uint64) ([]string, error) {
	forbidIpModel := forbid.NewForbidIpsModel()
	forbidIps, err := forbidIpModel.FindByUserID(userID)
	if err != nil {
		return nil, err
	}

	result := make([]string, 0, len(forbidIps))
	for _, forbidIp := range forbidIps {
		if forbidIp.IpSegment != "" {
			result = append(result, forbidIp.IpSegment)
		}
	}

	return result, nil
}

// getSubdomainClues 获取子域名线索
func getSubdomainClues(userID uint64) ([]string, error) {
	cluesModel := clues.NewCluer()

	// 查询用户的子域名线索
	cluesContentList, err := cluesModel.ListAllContent(
		mysql.WithColumnValue("user_id", userID),
		mysql.WithColumnValue("type", clues.TYPE_SUBDOMAIN),
		mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
		mysql.WithColumnValue("is_deleted", clues.DeletedFalse),
	)
	if err != nil {
		return nil, err
	}

	return utils.ListDistinctNonZero(cluesContentList), nil
}

// getConfirmedDomains 获取确认的域名线索
func getConfirmedDomains(taskInfo *TaskBasicInfo) ([]string, error) {
	if taskInfo.DetectAssetsTasksID == 0 {
		return []string{"1"}, nil // 默认值，表示无限制
	}

	// 获取推荐资产任务信息
	detectTaskInfo, err := detect_assets_tasks.NewModel().First(mysql.WithId(taskInfo.DetectAssetsTasksID))
	if err != nil {
		return nil, err
	}
	if detectTaskInfo == nil {
		return []string{"1"}, nil
	}

	// 如果没有扩展标识，返回默认值
	if detectTaskInfo.ExpendFlags == "" {
		return []string{"1"}, nil
	}

	// 查询推荐记录获取线索ID
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", taskInfo.UserID))
	query.Must(elastic.NewTermQuery("flag", detectTaskInfo.ExpendFlags))

	records, err := es_utils.All[recommend_record.RecommendRecord](100, query, nil, "clue_id")
	if err != nil {
		return nil, err
	}

	if len(records) == 0 {
		return []string{"1"}, nil
	}

	// 提取线索ID
	clueIds := make([]uint64, 0)
	for _, record := range records {
		for _, clueId := range record.ClueId {
			if clueId > 0 {
				clueIds = append(clueIds, uint64(clueId))
			}
		}
	}

	if len(clueIds) == 0 {
		return []string{"1"}, nil
	}

	// 根据线索ID查询确认的域名
	cluesModel := clues.NewCluer()
	cluesContentList, err := cluesModel.ListAllContent(
		mysql.WithColumnValue("user_id", taskInfo.UserID),
		mysql.WithValuesIn("id", clueIds),
		mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
		mysql.WithColumnValue("is_deleted", clues.DeletedFalse),
		mysql.WithColumnValue("type", clues.TYPE_DOMAIN),
	)
	if err != nil {
		return nil, err
	}

	if len(cluesContentList) == 0 {
		return []string{"1"}, nil
	}

	return cluesContentList, nil
}

// resolveDomainAndInsertProbeData 解析域名并插入探测数据
func resolveDomainAndInsertProbeData(taskInfo *TaskBasicInfo, newHost []string, confirmedDomains []string) error {
	if len(newHost) == 0 {
		return nil
	}

	// 解析域名为IP
	resolvedIPs, err := resolveDomainToIPs(taskInfo, newHost, confirmedDomains)
	if err != nil {
		return err
	}

	if len(resolvedIPs) == 0 {
		log.Info("resolveDomainAndInsertProbeData", "域名解析结果为空", map[string]interface{}{
			"task_id": taskInfo.TaskID,
			"domains": newHost,
		})
		return nil
	}

	// 插入探测数据
	return insertTaskProbeData(taskInfo.TaskID, resolvedIPs)
}

// resolveDomainToIPs 解析域名为IP
func resolveDomainToIPs(taskInfo *TaskBasicInfo, newHost []string, confirmedDomains []string) ([]string, error) {
	resolvedIPs := make([]string, 0)
	forbiddenIPSet := make(map[string]bool)

	// 构建禁扫IP集合
	for _, ip := range taskInfo.ForbiddenIPs {
		forbiddenIPSet[ip] = true
	}

	for _, domain := range newHost {
		// 去掉端口号
		domain = strings.Split(domain, ":")[0]

		// 验证域名是否在确认域名列表中
		if !isDomainAllowed(domain, confirmedDomains) {
			continue
		}

		// 根据IP类型解析域名
		var domainIPs []string
		var err error

		if taskInfo.IPType == scan_task.IP_TYPE_V4 {
			domainIPs, err = utils.GetDNSARecords(domain)
		} else {
			domainIPs, err = utils.GetDNSAAAARecords(domain)
		}

		if err != nil {
			log.Warn("resolveDomainToIPs", "域名解析失败", map[string]interface{}{
				"domain": domain,
				"error":  err.Error(),
			})
			continue
		}

		// 处理解析结果
		for _, ip := range domainIPs {
			if !isValidIP(ip, taskInfo.IPType) {
				continue
			}

			// 检查是否为禁扫IP
			if forbiddenIPSet[ip] {
				log.Info("resolveDomainToIPs", "跳过禁扫IP", map[string]interface{}{
					"domain": domain,
					"ip":     ip,
				})
				continue
			}

			// 检查IPv4映射的IPv6地址
			if taskInfo.IPType == scan_task.IP_TYPE_V6 && network.IsIPv4MappedIPv6(ip) {
				log.Info("resolveDomainToIPs", "跳过IPv4映射的IPv6地址", map[string]interface{}{
					"domain": domain,
					"ip":     ip,
				})
				continue
			}

			// 缓存IP与域名的对应关系
			cacheIPDomainRelation(taskInfo.TaskID, ip, domain, taskInfo.SubdomainClues)

			resolvedIPs = append(resolvedIPs, ip)
		}
	}

	// 去重
	return utils.ListDistinctNonZero(resolvedIPs), nil
}

// isDomainAllowed 检查域名是否被允许
func isDomainAllowed(domain string, confirmedDomains []string) bool {
	if len(confirmedDomains) == 1 && confirmedDomains[0] == "1" {
		return true // 无限制
	}

	topDomain := utils.GetTopDomain(domain)
	if topDomain == "" {
		return false
	}

	for _, confirmedDomain := range confirmedDomains {
		if topDomain == confirmedDomain {
			return true
		}
	}

	return false
}

// isValidIP 检查IP是否有效
func isValidIP(ip string, ipType uint8) bool {
	if net.ParseIP(ip) == nil {
		return false
	}

	if ipType == scan_task.IP_TYPE_V4 {
		return utils.IsIPv4(ip)
	} else {
		return utils.IsIPv6(ip)
	}
}

// cacheIPDomainRelation 缓存IP与域名的对应关系
func cacheIPDomainRelation(taskID uint64, ip, domain string, subdomainClues []string) {
	// 检查是否为子域名线索
	isSubdomain := false
	for _, clue := range subdomainClues {
		if clue == domain {
			isSubdomain = true
			break
		}
	}

	var cacheKey string
	var domainToCache string

	if isSubdomain {
		domainToCache = domain
	} else {
		domainToCache = utils.GetTopDomain(domain)
	}

	// IPv6地址使用MD5哈希作为键
	if network.IsIPv6(ip) {
		cacheKey = cache.GetCacheKey("ip_to_clue_domain", fmt.Sprintf("%d:%s", taskID, utils.Md5Hash(ip)))
		if !isSubdomain {
			subdomainCacheKey := cache.GetCacheKey("ip_to_clue_subdomain", fmt.Sprintf("%d:%s", taskID, utils.Md5Hash(ip)))
			log.Info("cacheIPDomainRelation", "设置子域名缓存", map[string]interface{}{
				"task_id":            taskID,
				"ip":                 ip,
				"subdomain_cache_key": subdomainCacheKey,
				"domain":             domain,
			})
			redis.GetClient().Set(context.Background(), subdomainCacheKey, domain, 90*24*time.Hour)
		}
	} else {
		cacheKey = cache.GetCacheKey("ip_to_clue_domain", fmt.Sprintf("%d:%s", taskID, ip))
		if !isSubdomain {
			subdomainCacheKey := cache.GetCacheKey("ip_to_clue_subdomain", fmt.Sprintf("%d:%s", taskID, ip))
			log.Info("cacheIPDomainRelation", "设置子域名缓存", map[string]interface{}{
				"task_id":            taskID,
				"ip":                 ip,
				"subdomain_cache_key": subdomainCacheKey,
				"domain":             domain,
			})
			redis.GetClient().Set(context.Background(), subdomainCacheKey, domain, 90*24*time.Hour)
		}
	}

	redis.GetClient().Set(context.Background(), cacheKey, domainToCache, 30*24*time.Hour)

	log.Info("cacheIPDomainRelation", "缓存IP域名关系", map[string]interface{}{
		"task_id":      taskID,
		"ip":           ip,
		"domain":       domainToCache,
		"is_subdomain": isSubdomain,
		"cache_key":    cacheKey,
	})
}

// insertTaskProbeData 插入任务探测数据
func insertTaskProbeData(taskID uint64, ips []string) error {
	if len(ips) == 0 {
		return nil
	}

	db := mysql.GetDbClient()

	// 构建插入数据
	now := time.Now()
	probeData := make([]map[string]interface{}, 0, len(ips)*2)

	for _, ip := range ips {
		// 完善IPv6地址格式
		formattedIP := ip
		if network.IsIPv6(ip) {
			formattedIP = utils.CompleteIPv6(ip)
		}

		// 添加80端口探测
		probeData = append(probeData, map[string]interface{}{
			"task_id":       taskID,
			"ip":            formattedIP,
			"port":          80,
			"base_protocol": "tcp",
			"created_at":    now,
		})

		// 添加443端口探测
		probeData = append(probeData, map[string]interface{}{
			"task_id":       taskID,
			"ip":            formattedIP,
			"port":          443,
			"base_protocol": "tcp",
			"created_at":    now,
		})
	}

	// 分批插入数据
	batchSize := 50
	for i := 0; i < len(probeData); i += batchSize {
		end := i + batchSize
		if end > len(probeData) {
			end = len(probeData)
		}

		batch := probeData[i:end]
		err := db.Table("task_probe_infos").Create(batch).Error
		if err != nil {
			return fmt.Errorf("插入探测数据失败: %w", err)
		}
	}

	log.Info("insertTaskProbeData", "插入探测数据成功", map[string]interface{}{
		"task_id":    taskID,
		"ip_count":   len(ips),
		"data_count": len(probeData),
	})

	return nil
}

// buildOptimalPortIpGroups 构建最优端口IP组合
func buildOptimalPortIpGroups(taskID uint64) ([]*microkernel.PortIPGroup, error) {
	db := mysql.GetDbClient()

	// 查询任务的探测信息
	var probeInfos []struct {
		IP           string `gorm:"column:ip"`
		Port         int    `gorm:"column:port"`
		BaseProtocol string `gorm:"column:base_protocol"`
	}

	err := db.Table("task_probe_infos").
		Where("task_id = ?", taskID).
		Find(&probeInfos).Error
	if err != nil {
		return nil, fmt.Errorf("查询探测信息失败: %w", err)
	}

	// 按IP分组端口
	ipPortsMap := make(map[string][]string)
	udpIPPortsMap := make(map[string][]string)

	for _, info := range probeInfos {
		if info.BaseProtocol == "udp" {
			udpPort := fmt.Sprintf("U:%d", info.Port)
			if !slices.Contains(udpIPPortsMap[info.IP], udpPort) {
				udpIPPortsMap[info.IP] = append(udpIPPortsMap[info.IP], udpPort)
			}
		} else {
			port := cast.ToString(info.Port)
			if !slices.Contains(ipPortsMap[info.IP], port) {
				ipPortsMap[info.IP] = append(ipPortsMap[info.IP], port)
			}
		}
	}

	// 对每个IP的端口进行排序
	for ip := range ipPortsMap {
		slices.Sort(ipPortsMap[ip])
	}
	for ip := range udpIPPortsMap {
		slices.Sort(udpIPPortsMap[ip])
	}

	// 按端口组合分组IP
	portGroupMap := make(map[string][]string)
	udpPortGroupMap := make(map[string][]string)

	for ip, ports := range ipPortsMap {
		portsKey := strings.Join(ports, ",")
		portGroupMap[portsKey] = append(portGroupMap[portsKey], ip)
	}

	for ip, ports := range udpIPPortsMap {
		portsKey := strings.Join(ports, ",")
		udpPortGroupMap[portsKey] = append(udpPortGroupMap[portsKey], ip)
	}

	// 构建结果
	result := make([]*microkernel.PortIPGroup, 0, len(portGroupMap)+len(udpPortGroupMap))

	for ports, ips := range portGroupMap {
		result = append(result, &microkernel.PortIPGroup{
			Ports:  ports,
			IPList: ips,
		})
	}

	for ports, ips := range udpPortGroupMap {
		result = append(result, &microkernel.PortIPGroup{
			Ports:  ports,
			IPList: ips,
		})
	}

	log.Info("buildOptimalPortIpGroups", "构建最优端口IP组合成功", map[string]interface{}{
		"task_id":     taskID,
		"group_count": len(result),
		"probe_count": len(probeInfos),
	})

	return result, nil
}

// extract 提取域名
// 例如：http://www.baidu.com:80/index.html 提取 www.baidu.com
func extract(s string) string {
	if s == "" {
		return ""
	}
	// 查找 "://"
	startIdx := strings.Index(s, "://")
	if startIdx != -1 {
		startIdx += len("://") // 从 "://" 之后开始
	} else {
		startIdx = 0 // 不存在 "://", 从头开始
	}

	// 从 startIdx 位置开始查找 ":"
	rest := s[startIdx:]
	endIdx := strings.Index(rest, ":")
	if endIdx == -1 {
		return rest // 没有冒号，取到末尾
	}

	return rest[:endIdx] // 截取到冒号前
}

// processHost 处理任务主机
func processHost(task *scan_task.ScanTasks) ([]string, error) {
	// 获取任务主机
	hosts, err := task_model.NewTaskHostsModel().ListAll([]string{"urls"}, mysql.WithColumnValue("task_id", task.ID))
	if err != nil {
		log.Errorf("processHost,获取任务主机失败,任务id:%d,error:%v", task.ID, err)
		return nil, err
	}
	// 去掉host里面的链接为ip格式的链接地址，只给微内核下发地址为域名格式的
	newHost := make([]string, 0, len(hosts))
	for _, host := range hosts {
		// 解析urls字符串为数组
		urls := make([]string, 0)
		err := json.Unmarshal([]byte(host.Urls), &urls)
		if err != nil {
			log.Errorf("processHost,解析任务主机失败,任务id:%d,error:%v", task.ID, err)
			continue
		}
		// 遍历urls数组，提取域名
		for _, url := range urls {
			// 提取域名
			hostStr := extract(url)
			if hostStr != "" {
				// 判断是否为ip
				if net.ParseIP(hostStr) == nil {
					newHost = append(newHost, hostStr)
				}
			}
		}
	}
	// 去重
	newHost = utils.ListDistinctNonZero(newHost)
	// 判断当前的 hostArr里面是否有泛解析域名，如果存在的话，泛解析域名只需要下发一个就行
	newHostArr := make(map[string]struct{}, 0)
	result := make([]string, 0)
	for _, host := range newHost {
		topDomain := utils.GetTopDomain(host)
		cacheValue := getParseDomain(host)
		if cacheValue == "" {
			newHostArr[host] = struct{}{}
			result = append(result, host)
		} else {
			if _, ok := newHostArr[topDomain]; !ok {
				newHostArr[topDomain] = struct{}{}
				result = append(result, topDomain)
			}
		}
	}
	return result, nil
}

// getParseDomain 获取解析域名
func getParseDomain(host string) string {
	cacheKey := cache.GetCacheKey("is_parse_domain", host)
	cacheValue, err := redis.GetClient().Get(context.Background(), cacheKey).Result()
	if err != nil {
		return ""
	}
	return cacheValue
}

// getScanTargetIpsForCloud 获取云端推荐资产的扫描目标IP
func getScanTargetIpsForCloud(task *scan_task.ScanTasks) ([]string, error) {
	if task.PortRange == 0 {
		// 获取任务指定的IP列表
		ips, err := task_model.NewTaskIpsModel().FindByQuerys(mysql.WithColumnValue("task_id", task.ID))
		if err != nil {
			return nil, err
		}
		if len(ips) > 0 {
			var result []string
			for _, ip := range ips {
				result = append(result, ip.Ip.String)
			}
			return result, nil
		}

		// IP为空时，使用用户已认领的所有IP
		query := fofaee_assets.NewFindCondition()
		query.UserId = task.UserId
		query.Status = []int{fofaee_assets.StatusConfirmAsset}
		// 设置超时时间,避免查询时间过长
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		ipAssets, _, err := fofaee_assets.NewFofaeeAssetsModel().FindByCondition(ctx, query, 0, 0, "ip")
		if err != nil {
			return nil, err
		}
		if len(ipAssets) > 0 {
			var result []string
			for _, ip := range ipAssets {
				result = append(result, ip.Ip)
			}
			return result, nil
		}
		return []string{}, nil
	}

	return []string{}, nil
}

// getScanTargetIpsForManual 获取手动添加的扫描目标IP
func getScanTargetIpsForManual(task *scan_task.ScanTasks, ipList []string) ([]string, error) {
	allIps := make([]string, 0)
	if task.IpType == scan_task.IP_TYPE_V4 {
		for _, ip := range ipList {
			if strings.Contains(ip, ":") {
				continue
			} else if strings.Contains(ip, "*") {
				networks, err := parseWildcardRange(ip)
				if err != nil {
					continue
				}
				allIps = append(allIps, networks...)
			} else {
				if strings.Contains(ip, "-") {
					ip = fullIpRange(ip)
				}
				// 将IP范围转换为网络段
				networks, err := rangeToNetworks(ip)
				if err != nil {
					continue
				}
				allIps = append(allIps, networks...)
			}
		}
	} else {
		for _, ip := range ipList {
			if strings.Contains(ip, "-") {
				continue
			}
			// 判断IP是否为ipv6
			if network.IsIPv6(ip) && !network.IsIPv4MappedIPv6(ip) {
				allIps = append(allIps, ip)
			}
		}
	}

	return allIps, nil
}

// fullIpRange 完善IP范围格式，处理不完整的IP范围
// 例如: "***********-50" -> "***********-************"
func fullIpRange(ipRange string) string {
	if strings.Contains(ipRange, "-") {
		parts := strings.Split(ipRange, "-")
		if len(parts) == 2 {
			startIp := parts[0]
			endIp := parts[1]

			// 检查结束IP是否为完整IP格式
			if net.ParseIP(endIp) == nil {
				// 如果不是完整IP，则补全为完整IP
				lastDotIndex := strings.LastIndex(startIp, ".")
				if lastDotIndex != -1 {
					prefix := startIp[:lastDotIndex+1]
					endIp = prefix + endIp
					ipRange = startIp + "-" + endIp
				}
			}
		}
	}
	return ipRange
}

// ParseWildcardRange 解析包含通配符的IP范围
// 输入格式: "192.168.1-50"
// 输出: CIDR网络段列表
func parseWildcardRange(ipRange string) ([]string, error) {
	if !strings.Contains(ipRange, "-") {
		return nil, fmt.Errorf("invalid wildcard range format: %s", ipRange)
	}

	// 分割IP范围
	parts := strings.Split(ipRange, "-")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid range format, expected one '-': %s", ipRange)
	}

	startPart := parts[0] // "192.168.1"
	endNumber := parts[1] // "50"

	// 构建起始IP: "192.168.1*.0"
	startIP := startPart + ".0"

	// 解析前缀部分
	beforeIPParts := strings.Split(startPart, ".")
	if len(beforeIPParts) < 3 {
		return nil, fmt.Errorf("invalid IP format: %s", startPart)
	}

	// 提取通配符前的数字部分
	wildcardPart := beforeIPParts[2]                    // "1"
	baseNumber := strings.TrimSuffix(wildcardPart, "*") // "1"

	// 构建结束IP: "***************"
	endIP := fmt.Sprintf("%s.%s.%s%s.255",
		beforeIPParts[0],
		beforeIPParts[1],
		baseNumber,
		endNumber)

	// 构建完整范围
	fullRange := fmt.Sprintf("%s-%s", startIP, endIP)

	// 转换为网络段
	networks, err := rangeToNetworks(fullRange)
	if err != nil {
		return nil, fmt.Errorf("failed to convert range to networks: %w", err)
	}

	return networks, nil
}

// rangeToNetworks 将IP范围、CIDR或单个IP转换为CIDR网络段列表
// 支持的输入格式：
// - IP范围: "***********-***********00"
// - CIDR: "***********/24"
// - 单个IP: "***********"
func rangeToNetworks(input string) ([]string, error) {
	input = strings.TrimSpace(input)
	if input == "" {
		return nil, fmt.Errorf("empty input")
	}

	// 检查是否为CIDR格式
	if strings.Contains(input, "/") {
		// 验证CIDR格式
		_, _, err := net.ParseCIDR(input)
		if err != nil {
			return nil, fmt.Errorf("invalid CIDR format: %s, error: %w", input, err)
		}
		// CIDR格式直接返回
		return []string{input}, nil
	}

	// 检查是否为IP范围格式
	if strings.Contains(input, "-") {
		return processIPRange(input)
	}

	// 处理单个IP地址
	ip := net.ParseIP(input)
	if ip == nil {
		return nil, fmt.Errorf("invalid IP address: %s", input)
	}

	// 根据IP类型确定CIDR前缀
	if ip.To4() != nil {
		// IPv4单个地址转换为/32
		return []string{fmt.Sprintf("%s/32", ip.String())}, nil
	} else {
		// IPv6单个地址转换为/128
		return []string{fmt.Sprintf("%s/128", ip.String())}, nil
	}
}

// processIPRange 处理IP范围格式的输入，使用标准CIDR合并算法
func processIPRange(ipRange string) ([]string, error) {
	parts := strings.Split(ipRange, "-")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid range format: %s", ipRange)
	}

	startIP := net.ParseIP(strings.TrimSpace(parts[0]))
	endIP := net.ParseIP(strings.TrimSpace(parts[1]))

	if startIP == nil {
		return nil, fmt.Errorf("invalid start IP: %s", parts[0])
	}
	if endIP == nil {
		return nil, fmt.Errorf("invalid end IP: %s", parts[1])
	}

	startIP = startIP.To4()
	endIP = endIP.To4()
	if startIP == nil || endIP == nil {
		return nil, fmt.Errorf("only IPv4 addresses are supported in range format")
	}

	startUint := network.IPToUint32(startIP)
	endUint := network.IPToUint32(endIP)
	if startUint > endUint {
		return nil, fmt.Errorf("start IP %s is greater than end IP %s", startIP.String(), endIP.String())
	}

	// 使用标准CIDR合并算法
	networks := []string{}
	current := startUint

	for current <= endUint {
		// 找到当前位置能使用的最大前缀长度
		prefixLen := findMaxPrefixLength(current, endUint)

		// 计算网络大小
		networkSize := uint32(1) << (32 - prefixLen)

		// 生成CIDR
		cidr := fmt.Sprintf("%s/%d", network.Uint32ToIP(current), prefixLen)
		networks = append(networks, cidr)

		// 移动到下一个网络
		current += networkSize
	}

	return networks, nil
}

// findMaxPrefixLength 找到当前位置能使用的最大前缀长度
func findMaxPrefixLength(start, end uint32) int {
	// 从最大的网络段开始尝试（前缀长度最小）
	for prefixLen := 0; prefixLen <= 32; prefixLen++ {
		networkSize := uint32(1) << (32 - prefixLen)

		// 检查当前起始地址是否对齐到该网络大小
		if start&(networkSize-1) != 0 {
			continue // 不对齐，尝试更小的网络
		}

		// 检查网络是否完全在范围内
		if start+networkSize-1 <= end {
			return prefixLen
		}
	}

	// 默认返回/32（单个IP）
	return 32
}

// getScanPorts 获取扫描端口信息
func getScanPorts(task *scan_task.ScanTasks) ([]string, error) {
	taskId := uint64(task.ID)
	// 判断是否是自定义端口
	if task.IsDefinePort == 1 {
		return getDefinePortScanParam(taskId)
	}

	// 获取任务端口配置
	taskPortsRepo := task_model.NewTaskPortsModel()
	taskPorts, err := taskPortsRepo.FindByTaskID(taskId)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Infof("ScanAssetJobHandler,没有找到任务端口配置,task_id:%d", taskId)
			return []string{}, nil
		}
		log.Error("ScanAssetJobHandler", "获取任务端口配置失败", map[string]interface{}{
			"task_id": taskId,
			"error":   err.Error(),
		})
		return []string{}, err
	}

	var isAllPort bool
	if taskPorts != nil && taskPorts.PortsType == "App\\Models\\MySql\\PortGroup" {
		groupId := uint64(taskPorts.PortsId)
		portGroupModel := port_group.NewPortGroupModel()
		portGroup, err := portGroupModel.FindById(groupId)
		if err != nil {
			log.Error("ScanAssetJobHandler", "获取端口分组失败", map[string]interface{}{
				"task_id": taskId,
				"error":   err.Error(),
			})
		}
		if portGroup != nil {
			isAllPort = portGroup.Name == "0-65535"
		}
	}

	var ports []string
	if isAllPort {
		// 全量端口扫描
		ports = strings.Split(port_group.ScanAllPorts, ",")
	} else {
		allPortIds, _, err := scan_task.NewScanTasksModel().GetAllPorts(taskId)
		if err != nil {
			log.Error("ScanAssetJobHandler", "获取任务端口配置失败", map[string]interface{}{
				"task_id": taskId,
				"error":   err.Error(),
			})
			return nil, err
		}
		portParams, err := getPortScanParam(allPortIds)
		if err != nil {
			return nil, err
		}
		if len(portParams) == 0 {
			// 使用默认端口
			ports = strings.Split(port_group.ScanAllPorts, ",")
		} else {
			ports = portParams
		}
	}

	return ports, nil
}

// getBlackIps 获取禁扫IP信息
func getBlackIps(task *scan_task.ScanTasks) ([]string, error) {
	forbidIps, err := forbid.NewForbidIpsModel().FindByUserID(task.UserId)
	if err != nil {
		return nil, err
	}

	var result []string
	for _, forbidIp := range forbidIps {
		// 检查是否为有效的IPv4地址
		if net.ParseIP(forbidIp.IpSegment) != nil {
			result = append(result, forbidIp.IpSegment)
		}
		// 注意：原PHP代码中有IP范围解析的逻辑被注释掉，这里也省略了复杂的IP范围解析
	}

	return result, nil
}

// getPortScanParam 获取端口扫描参数
func getPortScanParam(portIDs []uint64) ([]string, error) {
	// 如果端口ID为空，则返回空数组
	if len(portIDs) == 0 {
		return []string{}, nil
	}
	portModel := port_group.NewPortModel()
	// 检查端口数量限制
	countPort := portModel.CountByIds(portIDs, port_group.StatusDefault)
	if countPort == 0 {
		return []string{}, nil
	} else if countPort > 10000 {
		log.Warn("getPortScanParam", "端口数量超出限制", fmt.Sprintf("count: %d", countPort))
		return []string{}, nil
	}

	// 获取端口及其协议信息
	ports, err := portModel.FindWithProtocolsByIDs(portIDs)
	if err != nil {
		return nil, err
	}

	if len(ports) == 0 {
		return []string{}, nil
	}

	var tcp, tcpCustom, udp, udpCustom []string

	// 遍历端口及其协议信息
	for _, port := range ports {
		// 遍历端口协议信息
		for _, protocol := range port.Protocols {
			portStr := fmt.Sprintf("%d", port.Port)
			// TCP协议
			if protocol.Type == port_group.PortProtocolTypeTCP {
				// 系统预置,不指定协议,格式：端口
				if port.Source == port_group.PortSourceDefault {
					tcp = append(tcp, portStr)
				} else {
					// 用户添加,指定协议,格式：T:端口:协议
					tcpCustom = append(tcpCustom, fmt.Sprintf("T:%s:%s", portStr, protocol.Protocol))
				}
			} else {
				// UDP协议
				if port.Source == port_group.PortSourceDefault {
					// 系统预置,不指定协议,格式：U:端口
					udp = append(udp, fmt.Sprintf("U:%s", portStr))
				} else {
					// 用户添加,指定协议,格式：U:端口:协议
					udpCustom = append(udpCustom, fmt.Sprintf("U:%s:%s", portStr, protocol.Protocol))
				}
			}
		}
	}

	// 合并所有端口配置并去重
	allPorts := append(tcp, tcpCustom...)
	allPorts = append(allPorts, udp...)
	allPorts = append(allPorts, udpCustom...)

	return utils.ListDistinctNonZero(allPorts), nil
}

// getDefinePortScanParam 获取自定义端口扫描参数
func getDefinePortScanParam(taskID uint64) ([]string, error) {
	definePortModel := port_group.NewDefinePortModel()
	// 检查自定义端口数量限制
	countPort := definePortModel.CountByTaskId(taskID)
	if countPort > 100 {
		log.Warn("getDefinePortScanParam", "自定义端口超出数量限制",
			fmt.Sprintf("task_id: %d, count: %d", taskID, countPort))
		return []string{}, nil
	}

	// 获取自定义端口及其协议信息
	ports, err := definePortModel.FindWithProtocolsByTaskId(taskID)
	if err != nil {
		return nil, err
	}

	if len(ports) == 0 {
		return []string{}, nil
	}

	var tcp, tcpCustom, udp, udpCustom []string
	portModel := port_group.NewPortModel()

	// 遍历自定义端口及其协议信息
	for _, port := range ports {
		// 根据PortProtocolId获取协议信息
		protocol, err := portModel.FindProtocolById(port.PortProtocolId)
		if err != nil {
			log.Warn("getDefinePortScanParam", "获取协议信息失败",
				fmt.Sprintf("port_protocol_id: %d, error: %v", port.PortProtocolId, err))
			continue
		}

		portStr := fmt.Sprintf("%d", port.Port)
		// TCP协议
		if protocol.Type == port_group.PortProtocolTypeTCP {
			// 格式：T:端口:协议
			tcpCustom = append(tcpCustom, fmt.Sprintf("T:%s:%s", portStr, protocol.Protocol))
		} else {
			// 格式：U:端口:协议
			udpCustom = append(udpCustom, fmt.Sprintf("U:%s:%s", portStr, protocol.Protocol))
		}
	}

	// 合并所有端口配置并去重
	allPorts := append(tcp, tcpCustom...)
	allPorts = append(allPorts, udp...)
	allPorts = append(allPorts, udpCustom...)

	return utils.ListDistinctNonZero(allPorts), nil
}

// ping 检测网络连通性
func ping(ip string, port int) bool {
	address := net.JoinHostPort(ip, fmt.Sprintf("%d", port))
	conn, err := net.DialTimeout("tcp", address, time.Second)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// clearFailedAssets 删除扫描失败的数据
func clearFailedAssets(taskID uint64) {
	// 删除IP+端口资产数据
	foradar_assets.NewForadarAssetModel().DeleteByTaskId(context.Background(), taskID)

	// 删除IP维度数据
	assets, err := fofaee_assets.NewFofaeeAssetsModel().FindUserIdsAndIpByTaskId(context.Background(), taskID)
	if err != nil {
		log.Error("clearFailedAssets", "删除IP维度数据失败", map[string]interface{}{
			"task_id": taskID,
			"error":   err.Error(),
		})
	}
	userAndIPs := make([]map[uint64]string, 0, len(assets))
	for _, asset := range assets {
		userAndIPs = append(userAndIPs, map[uint64]string{uint64(asset.UserId): asset.Ip})
	}
	err = ip_history.NewModel().DeleteByUserAndIP(userAndIPs)
	if err != nil {
		log.Error("clearFailedAssets", "删除历史数据失败", map[string]interface{}{
			"task_id": taskID,
			"error":   err.Error(),
		})
	}

	// 删除资产数据
	err = fofaee_assets.NewFofaeeAssetsModel().DeleteByTaskId(context.Background(), taskID)
	if err != nil {
		log.Error("clearFailedAssets", "删除资产数据失败", map[string]interface{}{
			"task_id": taskID,
			"error":   err.Error(),
		})
	}

	log.Info("任务中断,清除任务数据:", "", fmt.Sprintf("task_id: %d", taskID))
}

// handleTaskError 处理任务错误
func handleTaskError(taskID uint64, err error) {
	log.Error("ScanAssetJobHandler", "任务异常", map[string]interface{}{
		"task_id": taskID,
		"error":   err.Error(),
	})

	// 获取环境信息
	// env := cfg.LoadCommon().Env
	node := cfg.LoadCommon().Node
	// envMessage := fmt.Sprintf("%s环境，Node:%s，", env, node)

	// 发送钉钉告警消息
	// dingtalkClient, err := dingtalk.GetDingTalkClient(dingtalk.AccountApplyRobot)
	// message := fmt.Sprintf("%s微内核下发扫描任务失败了,资产扫描任务id:%d,报错信息：%s", envMessage, taskID, err.Error())
	// if sendErr := dingtalkClient.SendMsg(message); sendErr != nil {
	// 	// 钉钉发送失败时的简化消息
	// 	simpleMessage := fmt.Sprintf("微内核下发扫描任务失败了，钉钉发消息失败，资产扫描任务id:%d,报错信息：%s", taskID, err.Error())
	// 	if err != nil {
	// 		log.Error("handleTaskError", "获取钉钉客户端失败", map[string]interface{}{
	// 			"task_id": taskID,
	// 			"error":   err.Error(),
	// 		})
	// 	}
	// 	dingtalkClient.SendMsg(simpleMessage)
	// }

	// 停止扫描任务
	if ping("127.0.0.1", 61234) {
		microkernel.GetInstance().Stop(taskID)
	}

	// 更新任务状态为失败
	updates := map[string]interface{}{
		"status": scan_task.StatusFailed,
		"node":   node,
	}
	err = scan_task.NewScanTasksModel().UpdateByMap([]mysql.HandleFunc{mysql.WithColumnValue("id", taskID)}, updates)
	if err != nil {
		log.Error("ScanAssetJobHandler", "更新任务状态失败", map[string]interface{}{
			"task_id": taskID,
			"error":   err.Error(),
		})
	}

	// 清理失败的资产数据
	clearFailedAssets(taskID)
}
