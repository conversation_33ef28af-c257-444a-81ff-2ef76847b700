package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/spf13/cast"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/detect_task_report_result"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/webService/handler/detect_asset_task"
	pb "micro-service/webService/proto"
	"time"
)

// CreateDetectTaskJobHandler 创建测绘任务
// 新建企业用户时，可以选择自动创建一个对应的测绘任务
//
// 消息体:
//
//	{
//		"user_id": 1, // 新创建用户的ID
//	 }
func CreateDetectTaskJobHandler(ctx context.Context, task *asyncq.Task) error {
	payloadInfo, err := parsePayloadForCreateDetectTask([]byte(task.Payload))
	if err != nil {
		return err
	}

	// 判断用户是否需要创建测绘任务
	userInfo, needCreate, err := isNeedCreateDetectTask(payloadInfo.UserId)
	if err != nil {
		log.Errorf("CreateDetectTaskJobHandler: 获取用户信息失败 - user_id=%d, error=%v", payloadInfo.UserId, err)
		return err
	}
	if !needCreate {
		log.Infof("CreateDetectTaskJobHandler: 用户不需要创建测绘任务 - user_id=%d", payloadInfo.UserId)
		return nil
	}

	// 创建测绘任务
	detectTaskId, groupId, err := createDetectTask(ctx, userInfo)
	if err != nil {
		log.Errorf("CreateDetectTaskJobHandler: 创建测绘任务失败 - user_id=%d, error=%v", payloadInfo.UserId, err)
		return err
	}
	log.Infof("CreateDetectTaskJobHandler: 创建测绘任务成功 - user_id=%d, task_id=%d, group_id=%d", payloadInfo.UserId, detectTaskId, groupId)

	// 更新用户信息
	updateData := map[string]interface{}{
		"first_detect_task_id": detectTaskId,
	}
	err = updateUserInfo(userInfo, updateData)
	if err != nil {
		log.Errorf("CreateDetectTaskJobHandler: 更新用户信息失败 - user_id=%d,updateData=%v, error=%v", payloadInfo.UserId, updateData, err)
		return err
	}
	log.Infof("CreateDetectTaskJobHandler: 更新用户信息成功 - user_id=%d,updateData=%v", payloadInfo.UserId, updateData)

	// 分发线索获取任务
	err = dispatchCluesJob(ctx, userInfo, detectTaskId, groupId)
	if err != nil {
		log.Errorf("CreateDetectTaskJobHandler: 分发线索获取任务失败 - user_id=%d, error=%v", payloadInfo.UserId, err)
		return err
	}
	log.Infof("CreateDetectTaskJobHandler: 分发线索获取任务成功 - user_id=%d, task_id=%d, group_id=%d", payloadInfo.UserId, detectTaskId, groupId)

	// 更新用户信息
	updateData = map[string]interface{}{
		"detect_tasks_step": user.DetectTasksStepDoing,
	}
	err = updateUserInfo(userInfo, updateData)
	if err != nil {
		log.Errorf("CreateDetectTaskJobHandler: 更新用户信息失败 - user_id=%d,updateData=%v, error=%v", payloadInfo.UserId, updateData, err)
		return err
	}
	log.Infof("CreateDetectTaskJobHandler: 更新用户信息成功 - user_id=%d,updateData=%v", payloadInfo.UserId, updateData)
	return nil
}

// updateUserInfo 更新用户信息
func updateUserInfo(userInfo *user.User, updateData map[string]interface{}) error {
	userModel := user.NewUserModel()
	err := userModel.UpdateAny(userInfo.Id, updateData)
	if err != nil {
		return err
	}
	return nil
}

// dispatchCluesJob 分发线索获取任务
func dispatchCluesJob(ctx context.Context, userInfo *user.User, detectTaskId uint64, groupId uint64) error {
	// 从detect_assets_tasks表取return_json字段的STEP_ONE_COMPANY_LIST值
	detectTaskModel := detect_assets_tasks.NewModel()
	taskInfo, err := detectTaskModel.First(detect_assets_tasks.WithID(detectTaskId))
	if err != nil {
		return err
	}

	// 解析ReturnJson字段获取公司列表
	var companyList []string
	if taskInfo != nil && taskInfo.ReturnJson != "" {
		var returnJson map[string]interface{}
		if err := json.Unmarshal([]byte(taskInfo.ReturnJson), &returnJson); err == nil {
			// 获取STEP_ONE_COMPANY_LIST对应的值
			if v, ok := returnJson[cast.ToString(detect_assets_tasks.StepOneCompanyList)]; ok {
				if arr, ok := v.([]interface{}); ok {
					for _, co := range arr {
						if name, ok := co.(string); ok {
							companyList = append(companyList, name)
						}
					}
				}
			}
		}
	}

	// 如果没有获取到公司列表，使用默认值
	if len(companyList) == 0 {
		companyList = []string{userInfo.TargetCompanyName}
	}

	// 分发 DetectGolangCluesJob
	payload := asyncq.NewDetectGolangCluesJobPayload(
		companyList,
		userInfo.Id,
		0,
		detectTaskId,
		groupId,
		true,
		nil,
		nil,
	)
	if userInfo.CompanyId != nil && userInfo.CompanyId.Valid {
		payload.CompanyId = uint64(userInfo.CompanyId.Int64)
	}
	err = asyncq.Enqueue(ctx, asyncq.DetectGolangCluesJob, payload)
	if err != nil {
		log.Errorf("dispatchCluesJob: 分发线索获取任务失败 - user_id=%d, task_id=%d, group_id=%d, error=%v", userInfo.Id, detectTaskId, groupId, err)
		return err
	} else {
		log.Infof("dispatchCluesJob: 分发线索获取任务成功 - user_id=%d, task_id=%d, group_id=%d", userInfo.Id, detectTaskId, groupId)
	}

	// 插入 DetectAssetsTaskReport
	now := time.Now()
	taskReport := &detect_task_report_result.Result{
		UserID:       userInfo.Id,
		DetectTaskID: detectTaskId,
		CreatedAt:    now,
	}
	// 处理 CompanyID 的类型转换
	if userInfo.CompanyId != nil && userInfo.CompanyId.Valid {
		taskReport.CompanyID = uint64(userInfo.CompanyId.Int64)
	} else {
		taskReport.CompanyID = 0 // 默认值为0
	}
	_ = detect_task_report_result.NewCall().Create(taskReport)
	return nil
}

// createDetectTask 创建测绘任务
func createDetectTask(ctx context.Context, userInfo *user.User) (detectTaskId uint64, groupId uint64, err error) {
	param := buildDetectTaskParam(userInfo)
	rsp := &pb.DetectAssetTaskCreateResponse{}
	err = detect_asset_task.CreateTask(ctx, param, rsp)
	if err != nil {
		return 0, 0, err
	}
	return rsp.DetectTaskId, rsp.GroupId, nil
}

// parseOtherMappingCompanies 解析其他测绘企业名称字段
// 该字段在数据库中存储的是JSON格式的字符串数组，如: ["杭州快门网络技术有限公司"]
// 或者是空字符串
func parseOtherMappingCompanies(otherMappingCompanies string) []string {
	// 如果是空字符串，返回空切片
	if otherMappingCompanies == "" {
		return []string{}
	}

	// 尝试解析JSON格式
	var companies []string
	err := json.Unmarshal([]byte(otherMappingCompanies), &companies)
	if err != nil {
		// 如果JSON解析失败，记录日志并返回空切片
		log.Errorf("parseOtherMappingCompanies: JSON解析失败 - data=%s, error=%v", otherMappingCompanies, err)
		return []string{}
	}

	return companies
}

// buildDetectTaskParam 构建创建测绘任务的参数
func buildDetectTaskParam(userInfo *user.User) *pb.DetectAssetTaskCreateRequest {
	p := &pb.DetectAssetTaskCreateRequest{
		DetectMode:           detect_assets_tasks.ModeAuto, // 自动模式(基于智能模式，全部自动)
		UserId:               userInfo.Id,
		SafeUserId:           1, // 只有admin可以新建用户并创建自动测绘任务
		CompanyId:            0,
		Name:                 userInfo.TargetCompanyName,
		Percent:              uint32(userInfo.Percent),
		CompanyList:          []string{},
		OtherCompanyList:     parseOtherMappingCompanies(userInfo.OtherMappingCompanies),
		IsNeedHunter:         uint64(0),
		IsNeedDnschecker:     uint64(0),
		IsAutoExpendIp:       uint64(0),
		NoNeedControyCompany: "",
		Bandwidth:            1000,
	}
	if userInfo.CompanyId != nil && userInfo.CompanyId.Valid {
		p.CompanyId = uint64(userInfo.CompanyId.Int64)
	}
	//打印p参数值
	log.Infof("buildDetectTaskParam: p=%v user_id=%d", p, userInfo.Id)
	return p
}

// isNeedCreateDetectTask 判断用户是否需要创建测绘任务
func isNeedCreateDetectTask(userId uint64) (userInfo *user.User, needCreate bool, err error) {
	// 获取用户信息
	userModel := user.NewUserModel()
	user, err := userModel.FindById(userId)
	if err != nil {
		return nil, false, err
	}
	if user == nil {
		return nil, false, errors.New("用户不存在")
	}
	// 判断用户是否需要创建测绘任务
	if user.NeedAssetMapping != 1 {
		return nil, false, nil
	}
	if user.TargetCompanyName == "" {
		return nil, false, errors.New("目标企业名称不能为空")
	}
	return user, true, nil
}

// parsePayloadForCreateDetectTask 解析创建测绘任务的参数
func parsePayloadForCreateDetectTask(payload []byte) (*asyncq.CreateDetectTaskJobPayload, error) {
	var payloadInfo asyncq.CreateDetectTaskJobPayload
	err := json.Unmarshal(payload, &payloadInfo)
	if err != nil {
		return nil, err
	}
	if payloadInfo.UserId == 0 {
		return nil, errors.New("user_id is required")
	}
	return &payloadInfo, nil
}
