package handlers

import (
	"context"
	"encoding/json"
	"errors"

	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
)

// RecommendAssetJobHandler 处理推荐资产任务的主要函数
// 消息体:
//
//	{
//		"task_id": 1 // 任务ID，必填
//	}
func RecommendAssetJobHandler(ctx context.Context, task *asyncq.Task) error {
	log.Infof("[步骤流转][RecommendAssetJobHandler] 开始处理推荐资产任务,任务id:%s", string(task.Payload))
	// 解析任务ID
	payload, err := parseRecommendAssetJobPayload([]byte(task.Payload))
	if err != nil {
		log.Error("[RecommendAssetJobHandler]", "解析任务ID失败", map[string]interface{}{
			"payload": string(task.Payload),
			"error":   err.Error(),
		})
		return err
	}
	// 查询测绘任务
	detectAssetTask, err := detect_assets_tasks.NewModel().First(mysql.WithId(payload.DetectTaskId))
	if err != nil {
		log.Error("[RecommendAssetJobHandler]", "查询测绘任务失败", map[string]interface{}{
			"task_id": payload.DetectTaskId,
			"error":   err.Error(),
		})
	}
	log.Infof("[RecommendAssetJobHandler] 处理消息,任务id:%d", payload.DetectTaskId)
	recommendRecordModel := recommend_record.NewRecommendRecordModel()
	// 查询推荐资产任务
	recommendRecord, err := recommendRecordModel.FindByID(payload.RecommendRecordId)
	if err != nil {
		log.Error("[RecommendAssetJobHandler]", "查询推荐资产任务失败", map[string]interface{}{
			"task_id": payload.DetectTaskId,
			"error":   err.Error(),
		})
		return err
	}
	if recommendRecord == nil {
		log.Error("[RecommendAssetJobHandler]", "推荐资产任务不存在", map[string]interface{}{
			"recommend_record_id": payload.RecommendRecordId,
		})
		return errors.New("推荐资产任务不存在")
	}

	// 此处不需要更新推荐资产任务状态，后续的资产推荐任务会更新推荐资产任务状态

	// 下发资产推荐任务
	err = dispatchRecommendAssetJob(recommendRecord)
	if err != nil {
		log.Error("[RecommendAssetJobHandler]", "下发资产推荐任务失败", map[string]interface{}{
			"task_id": recommendRecord.Id,
			"error":   err.Error(),
		})
	}
	// 如果智能模式，则调用可信度评估任务
	if detectAssetTask.IsIntellectMode == 1 {
		log.Infof("[步骤流转][RecommendAssetJobHandler] 智能模式,开始下发可信度评估任务,任务id:%d", recommendRecord.Id)
		// 下发可信度评估任务
		err = asyncq.Enqueue(ctx, asyncq.DetectDirectOperateJob, &asyncq.DetectDirectOperateJobPayload{
			UserId:       payload.UserId,
			DetectTaskId: uint64(recommendRecord.DetectAssetsTasksId),
			TaskName:     "detectAssetsEvaluate",
		})
		if err != nil {
			log.Error("[RecommendAssetJobHandler]", "下发可信度评估任务失败", map[string]interface{}{
				"task_id": recommendRecord.Id,
				"error":   err.Error(),
			})
		}
	}
	return nil
}

// 下发资产推荐任务
func dispatchRecommendAssetJob(recommendRecord *recommend_record.RecommendRecord) error {
	log.Infof("[步骤流转][RecommendAssetJobHandler] 开始处理资产推荐具体流程,任务id:%d", recommendRecord.Id)
	handler := NewRecommendAssetHandler(
		recommendRecord.TaskName,
		recommendRecord.Id,
		recommendRecord.DetectAssetsTasksId,
		utils.SafeInt(recommendRecord.UserId),
		recommendRecord.ClueId,
		utils.SafeInt(recommendRecord.CronId),
		0,
	)

	// 单位资产测绘的资产推荐任务
	if recommendRecord.DetectAssetsTasksId != 0 {
		// 查询线索
		clues, err := clues.NewCluer().ListAll(mysql.WithColumnValue("group_id", recommendRecord.GroupId), mysql.WithColumnValue("is_deleted", clues.NOT_DELETE))
		if err != nil {
			log.Error("[RecommendAssetJobHandler]", "查询线索失败", map[string]interface{}{
				"task_id": recommendRecord.Id,
				"error":   err.Error(),
			})
		}
		// 线索id列表
		clueIds := make([]int, 0)
		for _, clue := range clues {
			clueIds = append(clueIds, int(clue.Id))
		}
		handler.config.ClueArr = clueIds
		handler.config.OpId = 0
	}

	err := handler.Start()
	if err != nil {
		log.Error("[RecommendAssetJobHandler]", "处理推荐资产任务失败", map[string]interface{}{
			"task_id": recommendRecord.Id,
			"error":   err.Error(),
		})
	}
	log.Infof("[步骤流转][RecommendAssetJobHandler] 处理推荐资产具体流程完成,任务id:%d", recommendRecord.Id)
	return nil
}

// parseRecommendAssetJobPayload 解析推荐资产任务参数
func parseRecommendAssetJobPayload(msg []byte) (*asyncq.RecommendAssetJobPayload, error) {
	var payload asyncq.RecommendAssetJobPayload
	err := json.Unmarshal(msg, &payload)
	if err != nil {
		return nil, err
	}
	if payload.RecommendRecordId == "" {
		return nil, errors.New("推荐资产记录ID不能为空")
	}
	if payload.UserId == 0 {
		return nil, errors.New("用户ID不能为空")
	}
	if payload.DetectTaskId == 0 {
		return nil, errors.New("测绘任务ID不能为空")
	}
	return &payload, nil
}
