package handlers

import (
	"context"
	testcommon "micro-service/initialize/common_test"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"micro-service/initialize/es"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/mysql/task"
	"micro-service/pkg/utils"
)

func FilterDomainAssetInit() {
	// 设置测试环境
	es.SetTestEnv(true)

	// 初始化 mock ES 服务器和客户端
	mockServer := es.NewMockServer()
	defer func() {
		// 注册基本的ES操作响应
		mockServer.Register(".*/_update_by_query.*", map[string]interface{}{
			"took":              1,
			"timed_out":         false,
			"total":             1,
			"updated":           1,
			"deleted":           0,
			"failures":          []interface{}{},
			"version_conflicts": 0,
		})
	}()
	testcommon.SetTestEnv(true)
	// 创建Mock服务
	mockClient := mockServer.NewElasticMockClient()
	testcommon.SetElasticClient(mockClient)
}

// createTestTaskAsset 创建测试用的TaskAsset对象
func createTestTaskAsset(ip string, ports []interface{}) *fofaee_task_assets.FofaeeTaskAssets {
	return &fofaee_task_assets.FofaeeTaskAssets{
		UserId:         1,
		TaskId:         "1",
		Protocols:      []string{"http", "https"},
		State:          1,
		OnlineState:    1,
		IsPhpFill:      0,
		HostList:       []string{},
		TitleList:      []fofaee_task_assets.TitleListInfo{},
		Ports:          ports,
		IsIpv6:         false,
		Rules:          []fofaee_task_assets.Rule{},
		Ip:             ip,
		Hosts:          []string{},
		CompanyId:      1,
		PortList:       []fofaee_task_assets.PortListInfo{},
		PortSize:       uint(len(ports)),
		Lastupdatetime: "2023-01-01 00:00:00",
		Createtime:     "2023-01-01 00:00:00",
		UpdatedAt:      "2023-01-01 00:00:00",
		Id:             "1_" + ip,
	}
}

// 创建测试用的 ForadarAsset 数据
func createTestForadarAsset(port interface{}, protocol, subdomain, domain, url string, httpStatusCode interface{}, onlineState int, updatedAt time.Time) *foradar_assets.ForadarAsset {
	return &foradar_assets.ForadarAsset{
		Ip:              "***********",
		Port:            port,
		Protocol:        protocol,
		Subdomain:       subdomain,
		Domain:          domain,
		Url:             url,
		HTTPStatusCode:  httpStatusCode,
		OnlineState:     onlineState,
		SourceUpdatedAt: updatedAt.Format("2006-01-02 15:04:05"),
	}
}

// TestFilterDomainAsset_EmptyInput 测试空输入
func TestFilterDomainAsset_EmptyInput(t *testing.T) {
	FilterDomainAssetInit()
	s := &ScanForadarAsset{}
	ctx := context.Background()

	tAsset := createTestTaskAsset("***********", []interface{}{})
	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, []*foradar_assets.ForadarAsset{}, tAsset)

	assert.Empty(t, hostUniqueHash, "hostUniqueHash should be empty")
	assert.Empty(t, hostList, "hostList should be empty")
	assert.Empty(t, portLists, "portLists should be empty")
}

// TestFilterDomainAsset_SingleAsset 测试单个资产
func TestFilterDomainAsset_SingleAsset(t *testing.T) {
	FilterDomainAssetInit()
	s := &ScanForadarAsset{}
	ctx := context.Background()

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, time.Now()),
	}

	tAsset := createTestTaskAsset("***********", []interface{}{80})
	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 1, "should have 1 hash")
	assert.Len(t, hostList, 1, "should have 1 host")
	assert.Len(t, portLists, 1, "should have 1 port list")

	// 验证hostList内容
	host := hostList[0]
	assert.Equal(t, 80, host["port"])
	assert.Equal(t, "http", host["protocol"])
	assert.Equal(t, "www.example.com", host["subdomain"])
	assert.Equal(t, "example.com", host["domain"])
	assert.Equal(t, 200, host["http_status_code"])
	assert.Equal(t, 1, host["online_state"])

	// 验证portLists内容
	portList := portLists[0]
	assert.Equal(t, 80, portList["port"])
	assert.Equal(t, "www.example.com", portList["subdomain"])
}

// TestFilterDomainAsset_MultiplePortsSameDomain 测试同域名多端口
func TestFilterDomainAsset_MultiplePortsSameDomain(t *testing.T) {
	FilterDomainAssetInit()
	s := &ScanForadarAsset{}
	ctx := context.Background()

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(443, "https", "www.example.com", "example.com", "https://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(8080, "http", "admin.example.com", "example.com", "http://admin.example.com", 200, 1, time.Now()),
	}

	tAsset := createTestTaskAsset("***********", []interface{}{80, 443, 8080})
	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 3, "should have 3 hashes")
	assert.Len(t, hostList, 3, "should have 3 hosts")
	assert.Len(t, portLists, 3, "should have 3 port lists")

	// 验证端口排序
	ports := make([]int, len(portLists))
	for i, portList := range portLists {
		ports[i] = utils.SafeInt(portList["port"])
	}
	assert.Equal(t, []int{80, 443, 8080}, ports, "ports should be sorted")
}

// TestFilterDomainAsset_SamePortMultipleSubdomains 测试同端口多子域名
func TestFilterDomainAsset_SamePortMultipleSubdomains(t *testing.T) {
	FilterDomainAssetInit()
	s := &ScanForadarAsset{}
	ctx := context.Background()

	now := time.Now()
	older := now.Add(-1 * time.Hour)

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, older),
		createTestForadarAsset(80, "http", "api.example.com", "example.com", "http://api.example.com", 200, 1, now),
		createTestForadarAsset(80, "http", "blog.example.com", "example.com", "http://blog.example.com", 404, 1, now.Add(-30*time.Minute)),
	}

	tAsset := createTestTaskAsset("***********", []interface{}{80})
	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 3, "should have 3 hashes")
	assert.Len(t, hostList, 3, "should have 3 hosts")
	assert.Len(t, portLists, 1, "should have 1 port list after filtering")
}

// TestFilterDomainAsset_AllPortScan_OnlineIP 测试全端口扫描 - IP在线
func TestFilterDomainAsset_AllPortScan_OnlineIP(t *testing.T) {
	FilterDomainAssetInit()
	s := &ScanForadarAsset{
		isAllPort: true, // 设置为全端口扫描
		Task: scan_task.ScanTasks{
			UserId: 1,
		},
	}
	s.Task.ID = 1
	ctx := context.Background()

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(443, "https", "www.example.com", "example.com", "https://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(8080, "http", "admin.example.com", "example.com", "http://admin.example.com", 200, 1, time.Now()),
	}

	// IP在线，且扫描到了端口80和443，没有扫描到8080
	tAsset := createTestTaskAsset("***********", []interface{}{80, 443})
	tAsset.OnlineState = 1

	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 3, "should have 3 hashes")
	assert.Len(t, hostList, 3, "should have 3 hosts")
	assert.Len(t, portLists, 3, "should have 3 port lists")

	// 验证端口状态
	for i, host := range hostList {
		port := utils.SafeInt(host["port"])
		switch port {
		case 80, 443:
			assert.Equal(t, 1, host["is_open"], "ports 80 and 443 should be online")
			assert.Equal(t, 1, host["online_state"], "ports 80 and 443 should be online")
		case 8080:
			assert.Equal(t, 0, host["is_open"], "port 8080 should be offline")
			assert.Equal(t, 0, host["online_state"], "port 8080 should be offline")
			assert.Equal(t, 0, host["http_status_code"], "port 8080 http_status_code should be 0")
		}
		t.Logf("Host %d: port=%d, is_open=%v, online_state=%v", i, port, host["is_open"], host["online_state"])
	}
}

// TestFilterDomainAsset_AllPortScan_OfflineIP 测试全端口扫描 - IP离线
func TestFilterDomainAsset_AllPortScan_OfflineIP(t *testing.T) {
	FilterDomainAssetInit()
	s := &ScanForadarAsset{
		isAllPort: true, // 设置为全端口扫描
		Task: scan_task.ScanTasks{
			UserId: 1,
		},
	}
	s.Task.ID = 1
	ctx := context.Background()

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(443, "https", "www.example.com", "example.com", "https://www.example.com", 200, 1, time.Now()),
	}

	// IP离线，没有扫描到任何端口
	tAsset := createTestTaskAsset("***********", []interface{}{})
	tAsset.OnlineState = 0

	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 2, "should have 2 hashes")
	assert.Len(t, hostList, 2, "should have 2 hosts")
	assert.Len(t, portLists, 2, "should have 2 port lists")

	// 验证所有端口都被设置为离线
	for i, host := range hostList {
		port := utils.SafeInt(host["port"])
		assert.Equal(t, 0, host["is_open"], "all ports should be offline")
		assert.Equal(t, 0, host["online_state"], "all ports should be offline")
		assert.Equal(t, 0, host["http_status_code"], "all ports http_status_code should be 0")
		t.Logf("Host %d: port=%d, is_open=%v, online_state=%v", i, port, host["is_open"], host["online_state"])
	}
}

// TestFilterDomainAsset_ListPortScan_OnlineIP 测试列表端口扫描 - IP在线
func TestFilterDomainAsset_ListPortScan_OnlineIP(t *testing.T) {
	FilterDomainAssetInit()
	// 创建测试用的probe信息
	taskProbeInfos := map[string][]task.TaskProbeInfo{
		"***********": {
			{Port: 80, BaseProtocol: "tcp"},
			{Port: 443, BaseProtocol: "tcp"},
			{Port: 8080, BaseProtocol: "tcp"},
		},
	}

	s := &ScanForadarAsset{
		isAllPort: false, // 非全端口扫描
		Task: scan_task.ScanTasks{
			UserId:    1,
			PortRange: 1, // 设置为列表端口扫描
		},
		portInfoArr: []interface{}{
			[]int{80, 443}, // taskPortArr[0] - 基础端口
			nil,            // taskPortArr[1] - 不使用
			taskProbeInfos, // taskPortArr[2] - 列表端口扫描信息
		},
	}
	s.Task.ID = 1
	ctx := context.Background()

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(443, "https", "www.example.com", "example.com", "https://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(8080, "http", "admin.example.com", "example.com", "http://admin.example.com", 200, 1, time.Now()),
	}

	// IP在线，扫描到了端口80，没有扫描到443和8080
	tAsset := createTestTaskAsset("***********", []interface{}{80})
	tAsset.OnlineState = 1

	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 3, "should have 3 hashes")
	assert.Len(t, hostList, 3, "should have 3 hosts")
	assert.Len(t, portLists, 3, "should have 3 port lists")

	// 验证端口状态
	for i, host := range hostList {
		port := utils.SafeInt(host["port"])
		switch port {
		case 80:
			assert.Equal(t, 1, host["is_open"], "port 80 should be online")
			assert.Equal(t, 1, host["online_state"], "port 80 should be online")
		case 443, 8080:
			assert.Equal(t, 0, host["is_open"], "ports 443 and 8080 should be offline")
			assert.Equal(t, 0, host["online_state"], "ports 443 and 8080 should be offline")
			assert.Equal(t, 0, host["http_status_code"], "ports 443 and 8080 http_status_code should be 0")
		}
		t.Logf("Host %d: port=%d, is_open=%v, online_state=%v", i, port, host["is_open"], host["online_state"])
	}
}

// TestFilterDomainAsset_ListPortScan_OfflineIP 测试列表端口扫描 - IP离线
func TestFilterDomainAsset_ListPortScan_OfflineIP(t *testing.T) {
	FilterDomainAssetInit()
	// 创建测试用的probe信息
	taskProbeInfos := map[string][]task.TaskProbeInfo{
		"***********": {
			{Port: 80, BaseProtocol: "tcp"},
			{Port: 443, BaseProtocol: "tcp"},
			{Port: 8080, BaseProtocol: "tcp"},
		},
	}

	s := &ScanForadarAsset{
		isAllPort: false, // 非全端口扫描
		Task: scan_task.ScanTasks{
			UserId:    1,
			PortRange: 1, // 设置为列表端口扫描
		},
		portInfoArr: []interface{}{
			[]int{80, 443}, // taskPortArr[0] - 基础端口
			nil,            // taskPortArr[1] - 不使用
			taskProbeInfos, // taskPortArr[2] - 列表端口扫描信息
		},
	}
	s.Task.ID = 1
	ctx := context.Background()

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(443, "https", "www.example.com", "example.com", "https://www.example.com", 200, 1, time.Now()),
	}

	// IP离线，没有扫描到任何端口
	tAsset := createTestTaskAsset("***********", []interface{}{})
	tAsset.OnlineState = 0

	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 2, "should have 2 hashes")
	assert.Len(t, hostList, 2, "should have 2 hosts")
	assert.Len(t, portLists, 2, "should have 2 port lists")

	// 验证在列表中的端口被设置为离线
	for i, host := range hostList {
		port := utils.SafeInt(host["port"])
		assert.Equal(t, 0, host["is_open"], "all listed ports should be offline")
		assert.Equal(t, 0, host["online_state"], "all listed ports should be offline")
		assert.Equal(t, 0, host["http_status_code"], "all listed ports http_status_code should be 0")
		t.Logf("Host %d: port=%d, is_open=%v, online_state=%v", i, port, host["is_open"], host["online_state"])
	}
}

// TestFilterDomainAsset_CommonPortScan_OnlineIP 测试常用端口扫描 - IP在线
func TestFilterDomainAsset_CommonPortScan_OnlineIP(t *testing.T) {
	FilterDomainAssetInit()
	s := &ScanForadarAsset{
		isAllPort: false, // 非全端口扫描
		Task: scan_task.ScanTasks{
			UserId:    1,
			PortRange: 0, // 设置为常用端口扫描
		},
		portInfoArr: []interface{}{
			[]int{80, 443, 8080}, // taskPortArr[0] - 常用端口
		},
	}
	s.Task.ID = 1
	ctx := context.Background()

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(443, "https", "www.example.com", "example.com", "https://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(8080, "http", "admin.example.com", "example.com", "http://admin.example.com", 200, 1, time.Now()),
	}

	// IP在线，只扫描到了端口80和443，没有扫描到8080
	tAsset := createTestTaskAsset("***********", []interface{}{80, 443})
	tAsset.OnlineState = 1

	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 3, "should have 3 hashes")
	assert.Len(t, hostList, 3, "should have 3 hosts")
	assert.Len(t, portLists, 3, "should have 3 port lists")

	// 验证端口状态
	for i, host := range hostList {
		port := utils.SafeInt(host["port"])
		switch port {
		case 80, 443:
			assert.Equal(t, 1, host["is_open"], "ports 80 and 443 should be online")
			assert.Equal(t, 1, host["online_state"], "ports 80 and 443 should be online")
		case 8080:
			assert.Equal(t, 0, host["is_open"], "port 8080 should be offline")
			assert.Equal(t, 0, host["online_state"], "port 8080 should be offline")
			assert.Equal(t, 0, host["http_status_code"], "port 8080 http_status_code should be 0")
		}
		t.Logf("Host %d: port=%d, is_open=%v, online_state=%v", i, port, host["is_open"], host["online_state"])
	}
}

// TestFilterDomainAsset_CommonPortScan_OfflineIP 测试常用端口扫描 - IP离线
func TestFilterDomainAsset_CommonPortScan_OfflineIP(t *testing.T) {
	FilterDomainAssetInit()
	s := &ScanForadarAsset{
		isAllPort: false, // 非全端口扫描
		Task: scan_task.ScanTasks{
			UserId:    1,
			PortRange: 0, // 设置为常用端口扫描
		},
		portInfoArr: []interface{}{
			[]int{80, 443, 8080}, // taskPortArr[0] - 常用端口
		},
	}
	s.Task.ID = 1
	ctx := context.Background()

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(443, "https", "www.example.com", "example.com", "https://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(8080, "http", "admin.example.com", "example.com", "http://admin.example.com", 200, 1, time.Now()),
	}

	// IP离线，没有扫描到任何端口
	tAsset := createTestTaskAsset("***********", []interface{}{})
	tAsset.OnlineState = 0

	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 3, "should have 3 hashes")
	assert.Len(t, hostList, 3, "should have 3 hosts")
	assert.Len(t, portLists, 3, "should have 3 port lists")

	// 验证在常用端口列表中的端口被设置为离线
	for i, host := range hostList {
		port := utils.SafeInt(host["port"])
		assert.Equal(t, 0, host["is_open"], "all common ports should be offline")
		assert.Equal(t, 0, host["online_state"], "all common ports should be offline")
		assert.Equal(t, 0, host["http_status_code"], "all common ports http_status_code should be 0")
		t.Logf("Host %d: port=%d, is_open=%v, online_state=%v", i, port, host["is_open"], host["online_state"])
	}
}

// TestFilterDomainAsset_OffPortAndOnlinePortArrays 测试离线端口和在线端口数组
func TestFilterDomainAsset_OffPortAndOnlinePortArrays(t *testing.T) {
	FilterDomainAssetInit()
	s := &ScanForadarAsset{
		isAllPort: true, // 全端口扫描，更容易触发offPortArr和onlinePortArr
		Task: scan_task.ScanTasks{
			UserId: 1,
		},
	}
	s.Task.ID = 1
	ctx := context.Background()

	assets := []*foradar_assets.ForadarAsset{
		createTestForadarAsset(80, "http", "www.example.com", "example.com", "http://www.example.com", 200, 1, time.Now()),
		createTestForadarAsset(443, "https", "api.example.com", "example.com", "https://api.example.com", 200, 1, time.Now()),
		createTestForadarAsset(8080, "http", "admin.example.com", "example.com", "http://admin.example.com", 200, 1, time.Now()),
		createTestForadarAsset(3306, "mysql", "db.example.com", "example.com", "mysql://db.example.com", 0, 1, time.Now()),
	}

	// IP在线，只扫描到了端口80和443，没有扫描到8080和3306
	tAsset := createTestTaskAsset("***********", []interface{}{80, 443})
	tAsset.OnlineState = 1

	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, assets, tAsset)

	assert.Len(t, hostUniqueHash, 4, "should have 4 hashes")
	assert.Len(t, hostList, 4, "should have 4 hosts")
	assert.Len(t, portLists, 4, "should have 4 port lists")

	// 统计在线和离线端口数量
	onlineCount := 0
	offlineCount := 0
	for i, host := range hostList {
		port := utils.SafeInt(host["port"])
		isOpen := utils.SafeInt(host["is_open"])
		onlineState := utils.SafeInt(host["online_state"])

		switch port {
		case 80, 443:
			assert.Equal(t, 1, isOpen, "ports 80 and 443 should be online")
			assert.Equal(t, 1, onlineState, "ports 80 and 443 should be online")
			onlineCount++
		case 8080, 3306:
			assert.Equal(t, 0, isOpen, "ports 8080 and 3306 should be offline")
			assert.Equal(t, 0, onlineState, "ports 8080 and 3306 should be offline")
			assert.Equal(t, 0, host["http_status_code"], "offline ports http_status_code should be 0")
			offlineCount++
		}
		t.Logf("Host %d: port=%d, is_open=%v, online_state=%v", i, port, isOpen, onlineState)
	}

	assert.Equal(t, 2, onlineCount, "should have 2 online ports")
	assert.Equal(t, 2, offlineCount, "should have 2 offline ports")
}
