package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/ip_history"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"sync"
)

type DeleteTaskAssetsJobPayload struct {
	TaskIds    []string `json:"task_ids"`
	UserId     int      `json:"user_id"`
	RunningIds []uint64 `json:"running_ids"`
}

// DeleteTaskAssets 删除任务资产
// 消息体:
//
//	{
//		"task_ids": "1,2,3" // 任务ID，可选
//		"user_id": 1 // 用户ID，必填
//		"running_ids": "1,2,3" // 运行ID，可选
//	}
func DeleteTaskAssetsJob(ctx context.Context, task *asyncq.Task) error {
	payloadInfo, err := parsePayloadForDeleteTaskAssets([]byte(task.Payload))
	if err != nil {
		return err
	}
	if len(payloadInfo.TaskIds) == 0 {
		log.Warn("DeleteTaskAssetsJob", "任务ID为空", map[string]interface{}{
			"payload":  string(task.Payload),
			"task_ids": payloadInfo.TaskIds,
		})
		return nil
	}
	fofaTaskAssetModel := fofaee_task_assets.NewFofaeeTaskAssetsModel()
	// 删除资产数据
	if len(payloadInfo.RunningIds) > 0 {
		foradarAssetModel := foradar_assets.NewForadarAssetModel()
		fofaAssetModel := fofaee_assets.NewFofaeeAssetsModel()
		ipHistoryModel := ip_history.NewModel()

		// 创建WaitGroup和channels用于并行处理
		var wg sync.WaitGroup
		foradarChan := make(chan uint64, len(payloadInfo.RunningIds))
		fofaChan := make(chan uint64, len(payloadInfo.RunningIds))

		// Goroutine 1: 处理foradar资产删除
		wg.Add(1)
		go func() {
			defer wg.Done()
			for runningId := range foradarChan {
				err := foradarAssetModel.DeleteByTaskId(ctx, runningId)
				if err != nil {
					log.Error("DeleteTaskAssetsJob", "删除任务资产失败", map[string]interface{}{
						"running_id": runningId,
						"error":      err.Error(),
					})
				}
			}
		}()

		// Goroutine 2: 处理fofa相关操作
		wg.Add(1)
		go func() {
			defer wg.Done()
			for runningId := range fofaChan {
				// 查询FofaAssets中的ip信息
				fofaAssets, err := fofaAssetModel.FindUserIdsAndIpByTaskId(ctx, runningId)
				if err != nil {
					log.Error("DeleteTaskAssetsJob", "查询FofaAssets中的ip信息失败", map[string]interface{}{
						"running_id": runningId,
						"error":      err.Error(),
					})
					continue
				}
				userAndIPs := make([]map[uint64]string, 0, len(fofaAssets))
				for _, asset := range fofaAssets {
					userAndIPs = append(userAndIPs, map[uint64]string{uint64(asset.UserId): asset.Ip})
				}
				// 删除IP历史数据
				err = ipHistoryModel.DeleteByUserAndIP(userAndIPs)
				if err != nil {
					log.Error("DeleteTaskAssetsJob", "删除历史数据失败", map[string]interface{}{
						"running_id": runningId,
						"error":      err.Error(),
					})
					continue
				}
				// 删除FofaAssets中的数据
				err = fofaAssetModel.DeleteByTaskId(ctx, runningId)
				if err != nil {
					log.Error("DeleteTaskAssetsJob", "删除FofaAssets中的数据失败", map[string]interface{}{
						"running_id": runningId,
						"error":      err.Error(),
					})
				}
			}
		}()

		// 分发runningIds到各个channel
		for _, runningId := range payloadInfo.RunningIds {
			foradarChan <- runningId
			fofaChan <- runningId
		}

		// 关闭channels
		close(foradarChan)
		close(fofaChan)

		// 等待所有goroutine完成
		wg.Wait()
	}
	// 查询扫描任务
	scanTaskModel := scan_task.NewScanTasksModel()
	scanTaskIds, err := scanTaskModel.FindIds(mysql.WithColumnValue("user_id", payloadInfo.UserId), mysql.WithValuesIn("id", payloadInfo.TaskIds))
	if err != nil {
		log.Error("DeleteTaskAssetsJob", "查询任务失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"error":   err.Error(),
		})
	}
	// 删除FofaTaskAssets中的数据
	if len(scanTaskIds) > 0 {
		err = fofaTaskAssetModel.DeleteByTaskIds(ctx, scanTaskIds)
		if err != nil {
			log.Error("DeleteTaskAssetsJob", "删除FofaTaskAssets中的数据失败", map[string]interface{}{
				"task_ids": scanTaskIds,
				"error":    err.Error(),
			})
		}
	}

	return nil
}

func parsePayloadForDeleteTaskAssets(payload []byte) (payloadInfo *DeleteTaskAssetsJobPayload, err error) {
	err = json.Unmarshal(payload, &payloadInfo)
	if err != nil {
		log.Error("DeleteTaskAssetsJob", "解析payload失败", map[string]interface{}{
			"payload": string(payload),
			"error":   err.Error(),
		})
		return payloadInfo, err
	}

	if payloadInfo.UserId == 0 {
		log.Error("DeleteTaskAssetsJob", "解析用户ID失败", map[string]interface{}{
			"payload": string(payload),
		})
		return payloadInfo, errors.New("用户ID不能为空")
	}

	return payloadInfo, nil
}
