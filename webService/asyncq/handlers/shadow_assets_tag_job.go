package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/initialize/es"
	asyncq "micro-service/pkg/queue_helper"

	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

// ShadowAssetsTagJob 标记影子资产标签
// 消息体:
//
//	{
//		"user_id": 1,        // 用户ID，必填
//		"task_id": 1,        // 任务ID，必填
//		"detect_task_id": 1  // 测绘任务ID，必填
//	}
func ShadowAssetsTagJob(ctx context.Context, t *asyncq.Task) error {
	payloadInfo, err := parsePayloadForShadowAssetsTag([]byte(t.Payload))
	if err != nil {
		return err
	}

	log.Info("ShadowAssetsTag", "标记影子资产标签", map[string]interface{}{
		"user_id":        payloadInfo.UserId,
		"task_id":        payloadInfo.TaskId,
		"detect_task_id": payloadInfo.DetectTaskId,
	})

	// 查询已认领的IP资产
	assetsList, err := queryClaimedAssets(ctx, payloadInfo.UserId, payloadInfo.TaskId)
	if err != nil {
		log.Error("ShadowAssetsTag", "查询已认领资产失败", map[string]interface{}{
			"user_id":        payloadInfo.UserId,
			"task_id":        payloadInfo.TaskId,
			"detect_task_id": payloadInfo.DetectTaskId,
			"error":          err.Error(),
		})
		// 即使查询失败也要更新任务状态
		updateTaskFinishStatus(payloadInfo.DetectTaskId)
		return err
	}

	// 添加调试日志：显示查询到的资产数量和详细信息
	log.Info("ShadowAssetsTag", "查询已认领资产结果", map[string]interface{}{
		"user_id":        payloadInfo.UserId,
		"task_id":        payloadInfo.TaskId,
		"detect_task_id": payloadInfo.DetectTaskId,
		"assets_count":   len(assetsList),
	})

	// 如果查询到资产，记录前几个资产的详细信息用于调试
	if len(assetsList) > 0 {
		debugAssets := make([]map[string]interface{}, 0)
		maxDebugCount := 5 // 最多显示5个资产的详细信息
		if len(assetsList) < maxDebugCount {
			maxDebugCount = len(assetsList)
		}
		for i := 0; i < maxDebugCount; i++ {
			asset := assetsList[i]
			debugAssets = append(debugAssets, map[string]interface{}{
				"id":      asset.Id,
				"ip":      asset.Ip,
				"task_id": asset.TaskId,
				"status":  asset.Status,
			})
		}
		log.Info("ShadowAssetsTag", "查询到的资产详细信息(前5个)", map[string]interface{}{
			"user_id":        payloadInfo.UserId,
			"task_id":        payloadInfo.TaskId,
			"detect_task_id": payloadInfo.DetectTaskId,
			"debug_assets":   debugAssets,
		})
	}

	if len(assetsList) == 0 {
		log.Info("ShadowAssetsTag", "未发现已认领状态的资产，开始调试查询", map[string]interface{}{
			"user_id":        payloadInfo.UserId,
			"task_id":        payloadInfo.TaskId,
			"detect_task_id": payloadInfo.DetectTaskId,
		})

		// 调试：去掉状态条件再查询一次，看看是否因为状态不对导致查询不到数据
		debugAssets, debugErr := queryAssetsWithoutStatus(ctx, payloadInfo.UserId, payloadInfo.TaskId)
		if debugErr != nil {
			log.Error("ShadowAssetsTag", "调试查询失败", map[string]interface{}{
				"user_id":        payloadInfo.UserId,
				"task_id":        payloadInfo.TaskId,
				"detect_task_id": payloadInfo.DetectTaskId,
				"error":          debugErr.Error(),
			})
		} else {
			log.Info("ShadowAssetsTag", "调试查询结果（无状态条件）", map[string]interface{}{
				"user_id":        payloadInfo.UserId,
				"task_id":        payloadInfo.TaskId,
				"detect_task_id": payloadInfo.DetectTaskId,
				"total_assets":   len(debugAssets),
				"message":        "如果这里有数据，说明是状态条件导致查询不到数据",
			})

			// 如果调试查询有数据，进一步分析状态分布
			if len(debugAssets) > 0 {
				statusCount := make(map[int]int)
				for _, asset := range debugAssets {
					if status, ok := asset.Status.(int); ok {
						statusCount[status]++
					} else if statusFloat, ok := asset.Status.(float64); ok {
						statusCount[int(statusFloat)]++
					} else {
						log.Warn("ShadowAssetsTag", "无法解析资产状态", map[string]interface{}{
							"asset_id": asset.Id,
							"status":   asset.Status,
							"type":     fmt.Sprintf("%T", asset.Status),
						})
					}
				}
				log.Info("ShadowAssetsTag", "资产状态分布统计", map[string]interface{}{
					"user_id":         payloadInfo.UserId,
					"task_id":         payloadInfo.TaskId,
					"detect_task_id":  payloadInfo.DetectTaskId,
					"status_count":    statusCount,
					"expected_status": fofaee_assets.STATUS_CLAIMED,
				})
			}
		}

		log.Info("ShadowAssetsTag", "标记影子资产标签完成,未发现需要标记的资产", map[string]interface{}{
			"user_id":        payloadInfo.UserId,
			"task_id":        payloadInfo.TaskId,
			"detect_task_id": payloadInfo.DetectTaskId,
		})
		updateTaskFinishStatus(payloadInfo.DetectTaskId)
		return nil
	}

	// 获取测绘任务信息
	detectTask, err := queryDetectTask(payloadInfo.DetectTaskId)
	if err != nil {
		log.Error("ShadowAssetsTag", "查询测绘任务失败", map[string]interface{}{
			"user_id":        payloadInfo.UserId,
			"task_id":        payloadInfo.TaskId,
			"detect_task_id": payloadInfo.DetectTaskId,
			"error":          err.Error(),
		})
		updateTaskFinishStatus(payloadInfo.DetectTaskId)
		return err
	}

	if detectTask == nil {
		log.Info("ShadowAssetsTag", "标记影子资产标签完成,测绘任务不存在了", map[string]interface{}{
			"user_id":        payloadInfo.UserId,
			"task_id":        payloadInfo.TaskId,
			"detect_task_id": payloadInfo.DetectTaskId,
		})
		updateTaskFinishStatus(payloadInfo.DetectTaskId)
		return nil
	}

	// 添加调试日志：显示测绘任务的详细信息
	log.Info("ShadowAssetsTag", "查询到测绘任务信息", map[string]interface{}{
		"user_id":         payloadInfo.UserId,
		"task_id":         payloadInfo.TaskId,
		"detect_task_id":  payloadInfo.DetectTaskId,
		"task_name":       detectTask.Name,
		"task_user_id":    detectTask.UserId,
		"import_sure_ips": detectTask.ImportSureIps,
	})

	// 处理影子资产标记
	err = processShadowAssets(ctx, assetsList, detectTask, payloadInfo)
	if err != nil {
		log.Error("ShadowAssetsTag", "处理影子资产标记失败", map[string]interface{}{
			"user_id":        payloadInfo.UserId,
			"task_id":        payloadInfo.TaskId,
			"detect_task_id": payloadInfo.DetectTaskId,
			"error":          err.Error(),
		})
	}

	// 更新任务完成状态
	updateTaskFinishStatus(payloadInfo.DetectTaskId)
	return nil
}

// queryClaimedAssets 查询已认领的IP资产
func queryClaimedAssets(ctx context.Context, userId, taskId int) ([]*fofaee_assets.FofaeeAssets, error) {
	model := fofaee_assets.NewFofaeeAssetsModel()

	condition := &fofaee_assets.FindCondition{
		UserId: uint64(userId),
		TaskId: taskId, // 直接在查询条件中添加 task_id
		Status: []int{fofaee_assets.STATUS_CLAIMED},
	}

	// 添加调试日志：显示查询条件
	log.Info("ShadowAssetsTag", "查询已认领资产的条件", map[string]interface{}{
		"user_id": userId,
		"task_id": taskId,
		"status":  fofaee_assets.STATUS_CLAIMED,
	})

	// 先刷新ES索引确保数据可见
	_, refreshErr := es.GetInstance().Refresh(fofaee_assets.FofaeeAssetsIndex).Do(ctx)
	if refreshErr != nil {
		log.Warn("ShadowAssetsTag", "ES索引刷新失败", map[string]interface{}{
			"user_id": userId,
			"task_id": taskId,
			"error":   refreshErr.Error(),
		})
	} else {
		log.Info("ShadowAssetsTag", "ES索引刷新成功", map[string]interface{}{
			"user_id": userId,
			"task_id": taskId,
			"index":   fofaee_assets.FofaeeAssetsIndex,
		})
	}

	// 查询所有匹配的资产
	assets, total, err := model.FindByCondition(ctx, condition, 1, 30000)
	if err != nil {
		log.Error("ShadowAssetsTag", "ES查询失败", map[string]interface{}{
			"user_id": userId,
			"task_id": taskId,
			"error":   err.Error(),
		})
		return nil, fmt.Errorf("查询FofaeeAssets失败: %v", err)
	}

	// 添加调试日志：显示查询结果
	log.Info("ShadowAssetsTag", "ES查询结果", map[string]interface{}{
		"user_id":      userId,
		"task_id":      taskId,
		"total_count":  total,
		"assets_count": len(assets),
	})

	return assets, nil
}

// queryAssetsWithoutStatus 查询资产（不带状态条件，用于调试）
func queryAssetsWithoutStatus(ctx context.Context, userId, taskId int) ([]*fofaee_assets.FofaeeAssets, error) {
	model := fofaee_assets.NewFofaeeAssetsModel()

	condition := &fofaee_assets.FindCondition{
		UserId: uint64(userId),
		TaskId: taskId, // 只使用 user_id 和 task_id 条件，不使用 status 条件
	}

	// 添加调试日志：显示查询条件
	log.Info("ShadowAssetsTag", "调试查询条件（无状态限制）", map[string]interface{}{
		"user_id":   userId,
		"task_id":   taskId,
		"condition": condition,
	})

	// 先刷新ES索引确保数据可见
	_, refreshErr := es.GetInstance().Refresh(fofaee_assets.FofaeeAssetsIndex).Do(ctx)
	if refreshErr != nil {
		log.Warn("ShadowAssetsTag", "调试查询ES索引刷新失败", map[string]interface{}{
			"user_id": userId,
			"task_id": taskId,
			"error":   refreshErr.Error(),
		})
	}

	// 查询所有匹配的资产（不限制状态）
	assets, total, err := model.FindByCondition(ctx, condition, 1, 30000)
	if err != nil {
		log.Error("ShadowAssetsTag", "调试查询ES失败", map[string]interface{}{
			"user_id": userId,
			"task_id": taskId,
			"error":   err.Error(),
		})
		return nil, fmt.Errorf("调试查询FofaeeAssets失败: %v", err)
	}

	// 添加调试日志：显示查询结果
	log.Info("ShadowAssetsTag", "调试查询ES结果", map[string]interface{}{
		"user_id":      userId,
		"task_id":      taskId,
		"total_count":  total,
		"assets_count": len(assets),
	})

	return assets, nil
}

// queryDetectTask 查询测绘任务信息
func queryDetectTask(detectTaskId int) (*detect_assets_tasks.DetectAssetsTask, error) {
	model := detect_assets_tasks.NewModel()

	task, err := model.First(
		mysql.WithColumnValue("id", detectTaskId),
	)
	if err != nil {
		return nil, fmt.Errorf("查询DetectAssetsTask失败: %v", err)
	}

	return task, nil
}

// processShadowAssets 处理影子资产标记
func processShadowAssets(ctx context.Context, assetsList []*fofaee_assets.FofaeeAssets, detectTask *detect_assets_tasks.DetectAssetsTask, payload *asyncq.ShadowAssetsTagJobPayload) error {
	// 解析确定IP列表
	sureIPs, err := parseSureIPs(detectTask.ImportSureIps)
	if err != nil {
		log.Error("ShadowAssetsTag", "解析ImportSureIps失败", map[string]interface{}{
			"user_id":         payload.UserId,
			"task_id":         payload.TaskId,
			"detect_task_id":  payload.DetectTaskId,
			"import_sure_ips": detectTask.ImportSureIps,
			"error":           err.Error(),
		})
		return fmt.Errorf("解析ImportSureIps失败: %v", err)
	}

	// 添加调试日志：显示解析出的确定IP列表
	log.Info("ShadowAssetsTag", "解析确定IP列表", map[string]interface{}{
		"user_id":        payload.UserId,
		"task_id":        payload.TaskId,
		"detect_task_id": payload.DetectTaskId,
		"sure_ips_count": len(sureIPs),
		"sure_ips":       sureIPs,
	})

	// 标准化确定IP列表
	normalizedSureIPs := normalizeSureIPs(sureIPs)

	// 添加调试日志：显示标准化后的IP列表
	log.Info("ShadowAssetsTag", "标准化确定IP列表", map[string]interface{}{
		"user_id":              payload.UserId,
		"task_id":              payload.TaskId,
		"detect_task_id":       payload.DetectTaskId,
		"normalized_ips_count": len(normalizedSureIPs),
		"normalized_sure_ips":  normalizedSureIPs,
	})

	// 创建IP查找映射，提高查找效率
	sureIPMap := make(map[string]bool, len(normalizedSureIPs))
	for _, ip := range normalizedSureIPs {
		sureIPMap[ip] = true
	}

	// 处理资产标记

	// 需要更新的ID列表
	needUpdateIdList := make([]string, 0)
	shadowAssets := make([]map[string]interface{}, 0) // 用于调试的影子资产列表

	for _, asset := range assetsList {
		normalizedIP := normalizeIP(asset.Ip)

		// 如果IP不在确定IP列表中，标记为影子资产
		if !sureIPMap[normalizedIP] {
			needUpdateIdList = append(needUpdateIdList, asset.Id)
			shadowAssets = append(shadowAssets, map[string]interface{}{
				"id":            asset.Id,
				"ip":            asset.Ip,
				"normalized_ip": normalizedIP,
				"task_id":       asset.TaskId,
			})
		}
	}

	// 添加调试日志：显示影子资产详情
	log.Info("ShadowAssetsTag", "发现的影子资产详情", map[string]interface{}{
		"user_id":        payload.UserId,
		"task_id":        payload.TaskId,
		"detect_task_id": payload.DetectTaskId,
		"shadow_count":   len(needUpdateIdList),
		"shadow_assets":  shadowAssets,
	})
	// 去重
	needUpdateIdList = utils.ListDistinctNonZero(needUpdateIdList)
	if len(needUpdateIdList) > 0 {
		// 批量更新
		err := elastic.UpdateByIds[fofaee_assets.FofaeeAssets](ctx, fofaee_assets.FofaeeAssetsIndex, fofaee_assets.FofaeeAssetsType, needUpdateIdList, map[string]any{
			"is_shadow": 1,
		})
		if err != nil {
			return fmt.Errorf("更新is_shadow失败: %v", err)
		}
	}

	log.Info("ShadowAssetsTag", "标记影子资产标签完成,发现了需要标记的资产", map[string]interface{}{
		"user_id":        payload.UserId,
		"task_id":        payload.TaskId,
		"detect_task_id": payload.DetectTaskId,
		"更新的条数":          len(needUpdateIdList),
	})

	return nil
}

// parseSureIPs 解析确定IP列表JSON字符串
func parseSureIPs(importSureIps string) ([]string, error) {
	if importSureIps == "" {
		return []string{}, nil
	}

	var sureIPs []string
	err := json.Unmarshal([]byte(importSureIps), &sureIPs)
	if err != nil {
		return nil, fmt.Errorf("解析ImportSureIps JSON失败: %v", err)
	}

	return sureIPs, nil
}

// normalizeSureIPs 标准化确定IP列表
func normalizeSureIPs(sureIPs []string) []string {
	if len(sureIPs) == 0 {
		return []string{}
	}

	normalizedIPs := make([]string, 0, len(sureIPs))
	for _, ip := range sureIPs {
		normalizedIP := normalizeIP(ip)
		if normalizedIP != "" {
			normalizedIPs = append(normalizedIPs, normalizedIP)
		}
	}

	return normalizedIPs
}

// normalizeIP 标准化IP地址（对应PHP的expandIPv6函数）
func normalizeIP(ip string) string {
	if ip == "" {
		return ip
	}

	// 对于IPv6地址，使用IPExpanded进行标准化
	if utils.IsIPv6(ip) {
		return utils.IPExpanded(ip)
	}

	// 对于IPv4地址，直接返回
	return ip
}

// updateTaskFinishStatus 更新任务完成状态
func updateTaskFinishStatus(detectTaskId int) {
	model := detect_assets_tasks.NewModel()

	updateData := map[string]interface{}{
		"is_finish_shadow_assets": 1, // 对应PHP的DetectAssetsTask::SHADOW_YES
	}

	err := model.UpdateAny(updateData, mysql.WithColumnValue("id", detectTaskId))
	if err != nil {
		log.Error("ShadowAssetsTag", "更新is_finish_shadow_assets报错了", map[string]interface{}{
			"detect_task_id": detectTaskId,
			"error":          err.Error(),
		})
	}
}

// parsePayloadForShadowAssetsTag 解析payload
func parsePayloadForShadowAssetsTag(payload []byte) (payloadInfo *asyncq.ShadowAssetsTagJobPayload, err error) {
	err = json.Unmarshal(payload, &payloadInfo)
	if err != nil {
		log.Error("ShadowAssetsTag", "解析payload失败", map[string]interface{}{
			"payload": string(payload),
			"error":   err.Error(),
		})
		return nil, err
	}

	if payloadInfo.UserId == 0 {
		log.Error("ShadowAssetsTag", "用户ID不能为空", map[string]interface{}{
			"payload": string(payload),
		})
		return nil, errors.New("用户ID不能为空")
	}

	if payloadInfo.TaskId == 0 {
		log.Error("ShadowAssetsTag", "任务ID不能为空", map[string]interface{}{
			"payload": string(payload),
		})
		return nil, errors.New("任务ID不能为空")
	}

	if payloadInfo.DetectTaskId == 0 {
		log.Error("ShadowAssetsTag", "测绘任务ID不能为空", map[string]interface{}{
			"payload": string(payload),
		})
		return nil, errors.New("测绘任务ID不能为空")
	}

	return payloadInfo, nil
}
