package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	redisInit "micro-service/initialize/redis"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clue_black_keyword"
	"micro-service/middleware/mysql/clue_task"
	task_ids "micro-service/middleware/mysql/clue_task_ids"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/company_equity"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/phishing_fake_task"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/cfg"
	"micro-service/pkg/icp"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"sync"

	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// 全局去重变量，防止重复插入相同内容的线索
var (
	globalClueDedupeMap   = make(map[string]bool)
	globalClueDedupeMutex sync.RWMutex
)

// ExpandCluesJobHandler 处理扩展线索任务
func ExpandCluesJobHandler(ctx context.Context, task *asyncq.Task) error {
	// 解析任务参数
	payload := &asyncq.ExpandCluesJobPayload{}
	if err := json.Unmarshal([]byte(task.Payload), payload); err != nil {
		log.Errorf("[ExpandCluesJob] 解析任务参数失败: %v", err)
		return err
	}

	log.Infof("[ExpandCluesJob] 开始线索扩展任务: user_id=%d, clue_task_id=%d, detect_task_id=%d, is_intellect_mode=%d, is_fake_clue_expend=%d, clue_type=%d",
		payload.UserId, payload.ClueTaskId, payload.DetectTaskId, payload.IsIntellectMode, payload.IsFakeClueExpend, payload.ClueType)

	// 获取用户信息
	userInfo, err := user.NewUserModel().FindById(payload.UserId)
	if err != nil {
		log.Errorf("[ExpandCluesJob] 获取用户信息失败: %v", err)
		return err
	}

	// 获取任务信息
	clueTaskModel := clue_task.NewClueTasker()
	clueTask, err := clueTaskModel.First(
		mysql.WithColumnValue("id", payload.ClueTaskId),
		mysql.WithColumnValue("user_id", payload.UserId),
	)
	if err != nil {
		log.Errorf("[ExpandCluesJob] 获取线索任务信息失败: %v", err)
		return err
	}

	// 更新任务状态为执行中
	clueTask.Status = clue_task.RunningStatus
	if payload.DetectTaskId > 0 {
		clueTask.DetectAssetsTasksId = payload.DetectTaskId
	}
	err = clueTaskModel.Update(clueTask)
	if err != nil {
		log.Errorf("[ExpandCluesJob] 更新线索任务状态失败: %v", err)
		return err
	}

	// 获取线索分组ID
	groupId := clueTask.GroupId
	log.Infof("[ExpandCluesJob] 获取线索分组ID: %d", groupId)

	// 获取线索任务ID列表
	db := mysql.GetDbClient()
	var clueTaskIdsList []struct {
		ID          uint64 `gorm:"column:id"`
		ClueTasksID uint64 `gorm:"column:clue_tasks_id"`
		ClueID      uint64 `gorm:"column:clue_id"`
		Status      int    `gorm:"column:status"`
	}
	err = db.Table("clue_tasks_ids").
		Where("clue_tasks_id = ? AND status = ?", payload.ClueTaskId, task_ids.StatusDefaultIds).
		Find(&clueTaskIdsList).Error
	if err != nil {
		log.Errorf("[ExpandCluesJob] 获取线索任务ID列表失败: %v", err)
		return err
	}

	// 获取测绘任务信息
	var detectTaskInfo map[string]interface{}
	var detectType int
	var detectTask *detect_assets_tasks.DetectAssetsTask
	if payload.DetectTaskId > 0 {
		if payload.IsFakeClueExpend > 0 {
			// 仿冒测绘流程
			fakeDetectTask, err := phishing_fake_task.NewTasker().First(
				mysql.WithColumnValue("id", payload.DetectTaskId),
			)
			if err != nil {
				log.Errorf("[ExpandCluesJob] 获取仿冒测绘任务信息失败: %v", err)
				return err
			}
			detectTaskInfo = map[string]interface{}{
				"id":          fakeDetectTask.ID,
				"name":        fakeDetectTask.Name,
				"group_id":    fakeDetectTask.GroupId,
				"detect_type": 2, // 设置为深度模式
				"step_detail": fakeDetectTask.StepDetail,
			}
			detectType = 2 // 深度模式
		} else {
			// 正常测绘流程
			detectTask, err = detect_assets_tasks.NewModel().First(
				detect_assets_tasks.WithID(payload.DetectTaskId),
			)
			if err != nil {
				log.Errorf("[ExpandCluesJob] 获取测绘任务信息失败: %v", err)
				return err
			}
			if detectTask == nil {
				log.Errorf("[ExpandCluesJob] 获取测绘任务信息失败: %v", err)
				return err
			}
			detectTaskInfo = map[string]interface{}{
				"id":          detectTask.ID,
				"name":        detectTask.Name,
				"group_id":    detectTask.GroupId,
				"detect_type": detectTask.DetectType,
				"step_detail": detectTask.StepDetail,
			}
			detectType = detectTask.DetectType
		}
	}

	// 确定扩展类型
	expandType := payload.ClueType
	expandTypeName := ""
	if detectType == detect_assets_tasks.DetectTypeDepth {
		// 深度模式，根据步骤确定扩展类型
		stepDetail := cast.ToInt(detectTaskInfo["step_detail"])
		switch stepDetail {
		case detect_assets_tasks.StepTwoExpandDomain:
			expandType = clues.TYPE_DOMAIN
			expandTypeName = "domain"
		case detect_assets_tasks.StepTwoExpandICP:
			expandType = clues.TYPE_ICP
			expandTypeName = "icp"
		case detect_assets_tasks.StepTwoExpandIP:
			expandType = clues.TYPE_IP
			expandTypeName = "ip"
		case detect_assets_tasks.StepTwoExpandCert:
			expandType = clues.TYPE_CERT
			expandTypeName = "cert"
		}
	}

	totalCount := uint64(len(clueTaskIdsList))
	doneCount := uint64(0)

	log.Infof("[ExpandCluesJob] 改为串行处理线索扩展，避免并发冲突和第三方服务压力: 总数量=%d", totalCount)

	// 串行处理线索扩展
	for i, item := range clueTaskIdsList {
		log.Infof("[ExpandCluesJob] 开始处理第%d/%d个线索: clue_id=%d, clue_task_ids_id=%d",
			i+1, totalCount, item.ClueID, item.ID)

		// 检查任务是否还存在
		_, err := clueTaskModel.First(
			mysql.WithColumnValue("id", payload.ClueTaskId),
			mysql.WithColumnValue("user_id", payload.UserId),
		)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Warnf("[ExpandCluesJob] 线索任务已被删除: clue_task_id=%d", payload.ClueTaskId)
				return nil
			}
			log.Errorf("[ExpandCluesJob] 检查线索任务是否存在失败: %v", err)
			return err
		}

		// 串行处理线索扩展
		err = handleExpandClue(ctx, userInfo, item.ClueID, item.ID, payload.DetectTaskId, expandType, payload.IsFakeClueExpend > 0)
		if err != nil {
			log.Errorf("[ExpandCluesJob] 处理线索扩展失败: clue_id=%d, error=%v", item.ClueID, err)
		} else {
			log.Infof("[ExpandCluesJob] 线索扩展处理成功: clue_id=%d", item.ClueID)
		}

		// 更新线索任务ID状态为成功
		err = db.Table("clue_tasks_ids").
			Where("id = ?", item.ID).
			Update("status", task_ids.StatusSuccessIds).Error
		if err != nil {
			log.Errorf("[ExpandCluesJob] 更新线索任务ID状态失败: clue_task_ids_id=%d, error=%v", item.ID, err)
		}

		// 发送进度
		doneCount++
		err = sendProgressForExpandCluesJob(ctx, payload.UserId, detectTask, expandTypeName, doneCount, totalCount, 0, 0, false)
		if err != nil {
			log.Errorf("[ExpandCluesJob] 发送进度失败: %v", err)
		}

		log.Infof("[ExpandCluesJob] 第%d/%d个线索处理完成: clue_id=%d, 当前进度=%.1f%%",
			i+1, totalCount, item.ClueID, float64(doneCount)/float64(totalCount)*100)
	}

	log.Infof("[ExpandCluesJob] 传进来的扩展线索已经完成-ID: %d", payload.ClueTaskId)

	// 更新线索任务状态为完成
	clueTask.Status = clue_task.FinishStatus
	clueTask.Progress = 100
	err = clueTaskModel.Update(clueTask)
	if err != nil {
		log.Errorf("[ExpandCluesJob] 更新线索任务状态为完成失败: %v", err)
	}

	// 极速模式下处理不同类型的线索扩展
	if detectType == detect_assets_tasks.DetectTypeSpeed {
		// 极速模式下，根据当前扩展类型决定后续要扩展的线索类型
		// 定义完整的扩展链顺序：IP -> 域名 -> ICP -> 证书
		type expandItem struct {
			Type     int
			TypeName int32
		}

		expandChain := []expandItem{
			{clues.TYPE_IP, clues.TYPE_IP},
			{clues.TYPE_DOMAIN, clues.TYPE_DOMAIN},
			{clues.TYPE_ICP, clues.TYPE_ICP},
			{clues.TYPE_CERT, clues.TYPE_CERT},
		}

		// 找到当前扩展类型在链中的位置
		currentIndex := -1
		for i, item := range expandChain {
			if item.TypeName == expandType {
				currentIndex = i
				break
			}
		}

		// 如果找到当前类型，从下一个类型开始扩展
		if currentIndex >= 0 && currentIndex < len(expandChain)-1 {
			// 处理剩余的所有类型
			for i := currentIndex + 1; i < len(expandChain); i++ {
				nextType := expandChain[i]
				log.Infof("[ExpandCluesJob] 极速模式，开始扩展%d线索: detect_task_id=%d", nextType.TypeName, payload.DetectTaskId)
				err = handleSpeedExpand(ctx, userInfo, detectTaskInfo, nextType.Type, payload.DetectTaskId, nextType.TypeName)
				if err != nil {
					log.Errorf("[ExpandCluesJob] 扩展%d线索失败: %v", nextType.TypeName, err)
				} else {
					log.Infof("[ExpandCluesJob] 扩展%d线索完成: detect_task_id=%d", nextType.TypeName, payload.DetectTaskId)
				}
			}
		}

		// 处理完所有扩展后，最后更新测绘任务状态
		if payload.IsFakeClueExpend > 0 {
			log.Infof("[ExpandCluesJob] 更新仿冒测绘任务状态为完成: detect_task_id=%d", payload.DetectTaskId)
			err = phishing_fake_task.NewTasker().UpdateAny(payload.DetectTaskId, map[string]any{
				"step_status": detect_assets_tasks.StepStatusDone,
			})
		} else {
			log.Infof("[ExpandCluesJob] 更新测绘任务状态为完成: detect_task_id=%d", payload.DetectTaskId)
			err = detect_assets_tasks.NewModel().UpdateAny(
				map[string]interface{}{
					"step_status":   detect_assets_tasks.StepStatusDone,
					"clue_progress": 100,
				},
				detect_assets_tasks.WithID(payload.DetectTaskId),
			)
		}
		// 智能模式，调用下一个异步任务
		if detectTask.IsIntellectMode > 0 {
			log.Infof("[ExpandCluesJob] 智能模式，调用云推荐任务: detect_task_id=%d", payload.DetectTaskId)
			// 下发云推荐任务
			err = asyncq.Enqueue(ctx, asyncq.DetectDirectOperateJob, &asyncq.DetectDirectOperateJobPayload{
				UserId:       payload.UserId,
				DetectTaskId: payload.DetectTaskId,
				TaskName:     "cloudRecommend",
			})
			if err != nil {
				log.Errorf("[ExpandCluesJob] 下发云推荐任务失败: %v", err)
			}
		}
		if err != nil {
			log.Errorf("[ExpandCluesJob] 更新测绘任务状态失败: %v", err)
		}
	} else if detectType == detect_assets_tasks.DetectTypeDepth {
		// 深度模式下更新任务状态
		if payload.IsFakeClueExpend > 0 {
			log.Infof("[ExpandCluesJob] 更新仿冒测绘任务状态为完成: detect_task_id=%d", payload.DetectTaskId)
			err = phishing_fake_task.NewTasker().UpdateAny(payload.DetectTaskId, map[string]any{
				"step_status": detect_assets_tasks.StepStatusDone,
			})
		} else {
			log.Infof("[ExpandCluesJob] 更新测绘任务状态为完成: detect_task_id=%d", payload.DetectTaskId)
			err = detect_assets_tasks.NewModel().UpdateAny(
				map[string]interface{}{
					"step_status":   detect_assets_tasks.StepStatusDone,
					"clue_progress": 100,
				},
				detect_assets_tasks.WithID(payload.DetectTaskId),
			)
		}
		if err != nil {
			log.Errorf("[ExpandCluesJob] 更新测绘任务状态失败: %v", err)
		}
	}

	// 发送完成状态
	err = sendProgressForExpandCluesJob(ctx, payload.UserId, detectTask, expandTypeName, totalCount, totalCount, 0, 0, true)
	if err != nil {
		log.Errorf("[ExpandCluesJob] 发送完成状态失败: %v", err)
	}

	log.Infof("[ExpandCluesJob] 线索扩展任务完成: clue_task_id=%d", payload.ClueTaskId)
	return nil
}

// handleExpandClue 处理线索扩展，对应PHP中的dealExpandClue函数
func handleExpandClue(ctx context.Context, userInfo *user.User, clueId uint64, clueTaskIdsId uint64, detectTaskId uint64, expandType int32, isFakeClueExpend bool) error {
	// 检查用户信息是否为空
	if userInfo == nil {
		log.Errorf("[handleExpandClue] 用户信息为空")
		return errors.New("用户信息为空")
	}

	log.Infof("[handleExpandClue] 处理线索扩展: user_id=%d, clue_id=%d, clue_task_ids_id=%d, detect_task_id=%d, expand_type=%d, is_fake_clue_expend=%v",
		userInfo.Id, clueId, clueTaskIdsId, detectTaskId, expandType, isFakeClueExpend)

	if isFakeClueExpend {
		// 仿冒测绘流程，调用FakeExpandCluesGolangJob
		log.Infof("[handleExpandClue] 仿冒测绘下发扩展任务: clue_id=%d, clue_task_ids_id=%d", clueId, clueTaskIdsId)
		if err := processFakeExpandClue(ctx, userInfo, clueId, clueTaskIdsId, detectTaskId, expandType); err != nil {
			log.Errorf("[handleExpandClue] 处理仿冒线索扩展失败: %v", err)
			return err
		}
	} else {
		// 正常测绘流程，调用ExpandCluesGolangJob
		log.Infof("[handleExpandClue] 正常测绘下发扩展任务: clue_id=%d, clue_task_ids_id=%d", clueId, clueTaskIdsId)
		if err := processExpandClue(ctx, userInfo, clueId, clueTaskIdsId, detectTaskId, expandType); err != nil {
			log.Errorf("[handleExpandClue] 处理正常线索扩展失败: %v", err)
			return err
		}
	}

	return nil
}

// processExpandClue 处理正常线索扩展，对应PHP中的ExpandCluesGolangJob
func processExpandClue(ctx context.Context, userInfo *user.User, clueId uint64, clueTaskIdsId uint64, detectTaskId uint64, expandType int32) error {
	// 检查用户信息是否为空
	if userInfo == nil {
		log.Errorf("[processExpandClue] 用户信息为空")
		return errors.New("用户信息为空")
	}

	log.Infof("[processExpandClue] 开始处理线索扩展: user_id=%d, clue_id=%d, clue_task_ids_id=%d, detect_task_id=%d, expand_type=%d",
		userInfo.Id, clueId, clueTaskIdsId, detectTaskId, expandType)

	// 1. 获取线索信息
	clueModel := clues.NewCluer()
	clueInfo, err := clueModel.First(mysql.WithColumnValue("id", clueId))
	if err != nil {
		log.Errorf("[processExpandClue] 获取线索信息失败: %v", err)
		return err
	}

	// 2. 获取用户信息（PHP中是通过User::query()->with('company')->find($userId)获取）
	// 在Go中，我们简化处理，只获取必要的信息
	db := mysql.GetDbClient()
	userId := userInfo.Id

	// 3. 获取任务信息
	var clueTaskIdsInfo task_ids.ClueTaskIds
	err = db.Table("clue_tasks_ids").Where("id = ?", clueTaskIdsId).First(&clueTaskIdsInfo).Error
	if err != nil {
		log.Errorf("[processExpandClue] 获取任务信息失败: %v", err)
		return err
	}

	// 4. 设置任务状态为执行中
	err = db.Table("clue_tasks_ids").
		Where("id = ?", clueTaskIdsId).
		Update("status", task_ids.StatusRunningIds).Error
	if err != nil {
		log.Errorf("[processExpandClue] 更新任务状态失败: %v", err)
	}

	// 5. 检查缓存中是否存在任务运行标记
	// 在PHP中是通过Cache::add('clue_running'.$info->user_id.'_'.$this->child_task_model->clue_tasks_id, 1, 60 * 60 * 24)
	// 现在改为串行处理，恢复原来的缓存键格式
	cacheKey := fmt.Sprintf("clue_running%d_%d", userId, clueTaskIdsInfo.ClueTasksId)
	log.Infof("[processExpandClue] 设置任务缓存标记: %s", cacheKey)

	// 获取Redis客户端
	redisClient := redisInit.GetInstance(cfg.LoadRedis())

	// 检查缓存是否存在
	exists, err := redisClient.Exists(ctx, cacheKey).Result()
	if err != nil {
		log.Warnf("[processExpandClue] 检查缓存是否存在失败: %v", err)
	} else if exists > 0 {
		log.Infof("[processExpandClue] 任务正在处理中，跳过: %s", cacheKey)
		return nil
	}

	// 设置缓存，有效期24小时
	err = redisClient.Set(ctx, cacheKey, 1, 24*time.Hour).Err()
	if err != nil {
		log.Warnf("[processExpandClue] 设置缓存失败: %v", err)
	}

	// 6. 获取测绘任务信息（如果有）
	var detectTaskInfo map[string]interface{}
	if detectTaskId > 0 {
		detectTask, err := detect_assets_tasks.NewModel().First(
			mysql.WithColumnValue("id", detectTaskId),
		)
		if err != nil {
			log.Errorf("[processFakeExpandClue] 获取仿冒测绘任务信息失败: %v", err)
		} else if detectTask == nil {
			log.Errorf("[processFakeExpandClue] 获取仿冒测绘任务信息失败: %v", err)
		} else {
			detectTaskInfo = map[string]interface{}{
				"id":          detectTask.ID,
				"name":        detectTask.Name,
				"group_id":    detectTask.GroupId,
				"detect_type": detectTask.DetectType,
				"step_detail": detectTask.StepDetail,
				"status":      detectTask.Status,
				"step_status": detectTask.StepStatus,
				"return_json": detectTask.ReturnJson,
			}
		}
	}

	// 6. 根据线索类型处理不同的扩展
	switch clueInfo.Type {
	case clues.TYPE_ICP:
		// 调用API扩展ICP线索
		result, err := icp.ExpandByIcp(ctx, clueInfo.Content, false, userId)
		if err != nil {
			log.Errorf("[processExpandClue] 调用API扩展ICP线索失败: %v", err)
			break
		}
		if result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err != nil {
				log.Errorf("[processExpandClue] 轮询获取扩展结果失败: %v", err)
				break
			}
			if clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "icp", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processExpandClue] 处理ICP扩展结果数据失败: %v", err)
				}
			}
		}
	case clues.TYPE_CERT:
		// 解析证书中的O和CN
		o, cn := extractCertInfo(clueInfo.Content)
		if cn != "" {
			cnValue := fmt.Sprintf("CN=\"%s\"", cn)
			result, err := icp.ExpandByCert(ctx, cnValue, false, userId)
			if err == nil && result != nil && result.TaskID != "" {
				clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
				if err == nil && clueResult != nil && clueResult.Process == 100 {
					err = splitClueData(ctx, clueResult.Items, "cert_cn", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
					if err != nil {
						log.Errorf("[processExpandClue] 处理CN扩展结果数据失败: %v", err)
					}
				}
			}
		}
		if o != "" {
			result, err := icp.ExpandByCert(ctx, o, false, userId)
			if err == nil && result != nil && result.TaskID != "" {
				clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
				if err == nil && clueResult != nil && clueResult.Process == 100 {
					err = splitClueData(ctx, clueResult.Items, "cert_o", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
					if err != nil {
						log.Errorf("[processExpandClue] 处理O扩展结果数据失败: %v", err)
					}
				}
			}
		}
	case clues.TYPE_LOGO:
		// 直接调用API扩展Logo线索，传递hash和公司名称
		result, err := icp.ExpandByIconWithHash(ctx, "", cast.ToInt64(clueInfo.Hash), clueInfo.ClueCompanyName, false, userId)
		if err == nil && result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err == nil && clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "logo", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processExpandClue] 处理Logo扩展结果数据失败: %v", err)
				}
			}
		}
	case clues.TYPE_DOMAIN:
		log.Infof("[processExpandClue] 开始处理域名扩展: clue_id=%d, domain=%s, user_id=%d",
			clueId, clueInfo.Content, userId)

		result, err := icp.ExpandByDomain(ctx, clueInfo.Content, false, userId)
		if err != nil {
			log.Errorf("[processExpandClue] 调用ExpandByDomain失败: clue_id=%d, domain=%s, error=%v",
				clueId, clueInfo.Content, err)
			break
		}

		if result == nil {
			log.Errorf("[processExpandClue] ExpandByDomain返回结果为空: clue_id=%d, domain=%s",
				clueId, clueInfo.Content)
			break
		}

		if result.TaskID == "" {
			log.Errorf("[processExpandClue] ExpandByDomain返回的TaskID为空: clue_id=%d, domain=%s",
				clueId, clueInfo.Content)
			break
		}

		log.Infof("[processExpandClue] ExpandByDomain调用成功: clue_id=%d, domain=%s, task_id=%s",
			clueId, clueInfo.Content, result.TaskID)

		clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
		if err != nil {
			log.Errorf("[processExpandClue] 等待域名扩展结果失败: clue_id=%d, domain=%s, task_id=%s, error=%v",
				clueId, clueInfo.Content, result.TaskID, err)
			break
		}

		if clueResult == nil {
			log.Errorf("[processExpandClue] 域名扩展结果为空: clue_id=%d, domain=%s, task_id=%s",
				clueId, clueInfo.Content, result.TaskID)
			break
		}

		log.Infof("[processExpandClue] 域名扩展结果获取成功: clue_id=%d, domain=%s, task_id=%s, process=%d, items_count=%d",
			clueId, clueInfo.Content, result.TaskID, clueResult.Process, len(clueResult.Items))

		if clueResult.Process == 100 {
			err = splitClueData(ctx, clueResult.Items, "domain", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
			if err != nil {
				log.Errorf("[processExpandClue] 处理域名扩展结果数据失败: clue_id=%d, domain=%s, error=%v",
					clueId, clueInfo.Content, err)
			} else {
				log.Infof("[processExpandClue] 域名扩展结果处理完成: clue_id=%d, domain=%s, items_count=%d",
					clueId, clueInfo.Content, len(clueResult.Items))
			}
		} else {
			log.Warnf("[processExpandClue] 域名扩展未完成: clue_id=%d, domain=%s, process=%d",
				clueId, clueInfo.Content, clueResult.Process)
		}
	case clues.TYPE_KEYWORD:
		result, err := icp.ExpandByKeyword(ctx, clueInfo.Content, false, userId)
		if err == nil && result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err == nil && clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "keyword", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processExpandClue] 处理关键词扩展结果数据失败: %v", err)
				}
			}
		}
	case clues.TYPE_SUBDOMAIN:
		result, err := icp.ExpandBySubDomain(ctx, clueInfo.Content, false, userId)
		if err == nil && result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err == nil && clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "subdomain", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processExpandClue] 处理子域名扩展结果数据失败: %v", err)
				}
			}
		}
	case clues.TYPE_IP:
		result, err := icp.ExpandByIp(ctx, clueInfo.Content, false, userId)
		if err == nil && result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err == nil && clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "ip", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processExpandClue] 处理IP扩展结果数据失败: %v", err)
				}
			}
		}
	default:
		log.Warnf("[processExpandClue] 未知的线索类型: %d", clueInfo.Type)
	}

	if err != nil {
		log.Errorf("[processExpandClue] 扩展线索失败: %v", err)

		// 删除缓存
		if redisClient := redisInit.GetInstance(cfg.LoadRedis()); redisClient != nil {
			if delErr := redisClient.Del(ctx, cacheKey).Err(); delErr != nil {
				log.Warnf("[processExpandClue] 删除缓存失败: %v", delErr)
			} else {
				log.Infof("[processExpandClue] 删除缓存成功: %s", cacheKey)
			}
		}

		return err
	}

	// 7. 更新线索状态为已扩展
	err = db.Table("clues").
		Where("id = ?", clueId).
		Update("is_expand", clues.HAS_EXPAND).Error
	if err != nil {
		log.Errorf("[processExpandClue] 更新线索扩展状态失败: %v", err)
	}

	// 8. 发送WebSocket通知进度
	// 在PHP中是通过WsServer::sendClientSuccess发送，这里我们简化为日志
	log.Infof("[processExpandClue] 发送进度通知: user_id=%d, task_id=%d", userId, clueTaskIdsInfo.ClueTasksId)

	// 9. 删除缓存，无论任务成功还是失败
	cacheKey = fmt.Sprintf("clue_running%d_%d", userId, clueTaskIdsInfo.ClueTasksId)
	if redisClient := redisInit.GetInstance(cfg.LoadRedis()); redisClient != nil {
		if err := redisClient.Del(ctx, cacheKey).Err(); err != nil {
			log.Warnf("[processExpandClue] 删除缓存失败: %v", err)
		} else {
			log.Infof("[processExpandClue] 删除缓存成功: %s", cacheKey)
		}
	}

	log.Infof("[processExpandClue] 处理线索扩展完成: user_id=%d, clue_id=%d", userId, clueId)
	return nil
}

// processFakeExpandClue 处理仿冒线索扩展，对应PHP中的FakeExpandCluesGolangJob
func processFakeExpandClue(ctx context.Context, userInfo *user.User, clueId uint64, clueTaskIdsId uint64, detectTaskId uint64, expandType int32) error {
	// 检查用户信息是否为空
	if userInfo == nil {
		log.Errorf("[processFakeExpandClue] 用户信息为空")
		return errors.New("用户信息为空")
	}

	log.Infof("[processFakeExpandClue] 开始处理仿冒线索扩展: user_id=%d, clue_id=%d, clue_task_ids_id=%d, detect_task_id=%d, expand_type=%d",
		userInfo.Id, clueId, clueTaskIdsId, detectTaskId, expandType)

	// 1. 获取线索信息
	clueModel := clues.NewCluer()
	clueInfo, err := clueModel.First(mysql.WithColumnValue("id", clueId))
	if err != nil {
		log.Errorf("[processFakeExpandClue] 获取线索信息失败: %v", err)
		return err
	}

	db := mysql.GetDbClient()
	// 2. 获取用户信息
	userId := userInfo.Id

	// 3. 获取任务信息
	var clueTaskIdsInfo task_ids.ClueTaskIds
	err = db.Table("clue_tasks_ids").Where("id = ?", clueTaskIdsId).First(&clueTaskIdsInfo).Error
	if err != nil {
		log.Errorf("[processFakeExpandClue] 获取任务信息失败: %v", err)
		return err
	}

	// 4. 设置任务状态为执行中
	err = db.Table("clue_tasks_ids").
		Where("id = ?", clueTaskIdsId).
		Update("status", task_ids.StatusRunningIds).Error
	if err != nil {
		log.Errorf("[processFakeExpandClue] 更新任务状态失败: %v", err)
	}

	// 5. 缓存任务运行标记
	cacheKey := fmt.Sprintf("clue_running%d_%d", userId, clueTaskIdsInfo.ClueTasksId)
	log.Infof("[processFakeExpandClue] 设置任务缓存标记: %s", cacheKey)

	// 获取Redis客户端
	redisClient := redisInit.GetInstance(cfg.LoadRedis())

	// 检查缓存是否存在
	exists, err := redisClient.Exists(ctx, cacheKey).Result()
	if err != nil {
		log.Warnf("[processFakeExpandClue] 检查缓存是否存在失败: %v", err)
	} else if exists > 0 {
		log.Infof("[processFakeExpandClue] 任务正在处理中，跳过: %s", cacheKey)
		return nil
	}

	// 设置缓存，有效期24小时
	err = redisClient.Set(ctx, cacheKey, 1, 24*time.Hour).Err()
	if err != nil {
		log.Warnf("[processFakeExpandClue] 设置缓存失败: %v", err)
	}

	// 6. 获取测绘任务信息（如果有）
	var detectTaskInfo map[string]interface{}
	if detectTaskId > 0 {
		fakeDetectTask, err := phishing_fake_task.NewTasker().First(
			mysql.WithColumnValue("id", detectTaskId),
		)
		if err != nil {
			log.Errorf("[processFakeExpandClue] 获取仿冒测绘任务信息失败: %v", err)
		} else {
			detectTaskInfo = map[string]interface{}{
				"id":          fakeDetectTask.ID,
				"name":        fakeDetectTask.Name,
				"group_id":    fakeDetectTask.GroupId,
				"detect_type": fakeDetectTask.DetectType,
				"step_detail": fakeDetectTask.StepDetail,
				"return_json": fakeDetectTask.ReturnJson,
			}
		}
	}

	// 7. 根据线索类型处理不同的扩展
	var expandErr error
	switch clueInfo.Type {
	case clues.TYPE_ICP:
		// 调用expandIcpClue函数并将detectTaskInfo传递给splitClueData
		result, err := icp.ExpandByIcp(ctx, clueInfo.Content, false, userId)
		if err != nil {
			log.Errorf("[processFakeExpandClue] 调用API扩展ICP线索失败: %v", err)
			expandErr = err
			break
		}
		if result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err != nil {
				log.Errorf("[processFakeExpandClue] 轮询获取扩展结果失败: %v", err)
				expandErr = err
				break
			}
			if clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "icp", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processFakeExpandClue] 处理ICP扩展结果数据失败: %v", err)
					expandErr = err
				}
			}
		}
	case clues.TYPE_CERT:
		// 调用expandCertClue函数并将detectTaskInfo传递给splitClueData
		o, cn := extractCertInfo(clueInfo.Content)
		var certErr error
		if cn != "" {
			cnValue := fmt.Sprintf("CN=\"%s\"", cn)
			result, err := icp.ExpandByCert(ctx, cnValue, false, userId)
			if err != nil {
				log.Errorf("[processFakeExpandClue] 调用API扩展证书CN失败: %v", err)
				certErr = err
			} else if result != nil && result.TaskID != "" {
				clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
				if err != nil {
					log.Errorf("[processFakeExpandClue] 轮询获取CN扩展结果失败: %v", err)
					certErr = err
				} else if clueResult != nil && clueResult.Process == 100 {
					err = splitClueData(ctx, clueResult.Items, "cert_cn", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
					if err != nil {
						log.Errorf("[processFakeExpandClue] 处理CN扩展结果数据失败: %v", err)
						certErr = err
					}
				}
			}
		}
		if o != "" && certErr == nil {
			result, err := icp.ExpandByCert(ctx, o, false, userId)
			if err != nil {
				log.Errorf("[processFakeExpandClue] 调用API扩展证书O失败: %v", err)
				expandErr = err
			} else if result != nil && result.TaskID != "" {
				clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
				if err != nil {
					log.Errorf("[processFakeExpandClue] 轮询获取O扩展结果失败: %v", err)
					expandErr = err
				} else if clueResult != nil && clueResult.Process == 100 {
					err = splitClueData(ctx, clueResult.Items, "cert_o", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
					if err != nil {
						log.Errorf("[processFakeExpandClue] 处理O扩展结果数据失败: %v", err)
						expandErr = err
					}
				}
			}
		}
		if certErr != nil {
			expandErr = certErr
		}
	case clues.TYPE_LOGO:
		// 直接调用API扩展Logo线索，传递hash和公司名称
		result, err := icp.ExpandByIconWithHash(ctx, "", cast.ToInt64(clueInfo.Hash), clueInfo.ClueCompanyName, false, userId)
		if err != nil {
			log.Errorf("[processFakeExpandClue] 调用API扩展Logo失败: %v", err)
			expandErr = err
			break
		}
		if result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err != nil {
				log.Errorf("[processFakeExpandClue] 轮询获取Logo扩展结果失败: %v", err)
				expandErr = err
				break
			}
			if clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "logo", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processFakeExpandClue] 处理Logo扩展结果数据失败: %v", err)
					expandErr = err
				}
			}
		}
	case clues.TYPE_DOMAIN:
		result, err := icp.ExpandByDomain(ctx, clueInfo.Content, false, userId)
		if err != nil {
			log.Errorf("[processFakeExpandClue] 调用API扩展域名失败: %v", err)
			expandErr = err
			break
		}
		if result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err != nil {
				log.Errorf("[processFakeExpandClue] 轮询获取域名扩展结果失败: %v", err)
				expandErr = err
				break
			}
			if clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "domain", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processFakeExpandClue] 处理域名扩展结果数据失败: %v", err)
					expandErr = err
				}
			}
		}
	case clues.TYPE_KEYWORD:
		result, err := icp.ExpandByKeyword(ctx, clueInfo.Content, false, userId)
		if err != nil {
			log.Errorf("[processFakeExpandClue] 调用API扩展关键词失败: %v", err)
			expandErr = err
			break
		}
		if result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err != nil {
				log.Errorf("[processFakeExpandClue] 轮询获取关键词扩展结果失败: %v", err)
				expandErr = err
				break
			}
			if clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "keyword", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processFakeExpandClue] 处理关键词扩展结果数据失败: %v", err)
					expandErr = err
				}
			}
		}
	case clues.TYPE_SUBDOMAIN:
		result, err := icp.ExpandBySubDomain(ctx, clueInfo.Content, false, userId)
		if err != nil {
			log.Errorf("[processFakeExpandClue] 调用API扩展子域名失败: %v", err)
			expandErr = err
			break
		}
		if result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err != nil {
				log.Errorf("[processFakeExpandClue] 轮询获取子域名扩展结果失败: %v", err)
				expandErr = err
				break
			}
			if clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "subdomain", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processFakeExpandClue] 处理子域名扩展结果数据失败: %v", err)
					expandErr = err
				}
			}
		}
	case clues.TYPE_IP:
		result, err := icp.ExpandByIp(ctx, clueInfo.Content, false, userId)
		if err != nil {
			log.Errorf("[processFakeExpandClue] 调用API扩展IP失败: %v", err)
			expandErr = err
			break
		}
		if result != nil && result.TaskID != "" {
			clueResult, err := icp.WaitForExpandClueResult(ctx, result.TaskID, 10*time.Minute, 8*time.Second)
			if err != nil {
				log.Errorf("[processFakeExpandClue] 轮询获取IP扩展结果失败: %v", err)
				expandErr = err
				break
			}
			if clueResult != nil && clueResult.Process == 100 {
				err = splitClueData(ctx, clueResult.Items, "ip", userInfo, clueTaskIdsId, clueTaskIdsInfo.ClueTasksId, detectTaskInfo)
				if err != nil {
					log.Errorf("[processFakeExpandClue] 处理IP扩展结果数据失败: %v", err)
					expandErr = err
				}
			}
		}
	default:
		log.Warnf("[processFakeExpandClue] 未知的线索类型: %d", clueInfo.Type)
	}

	if expandErr != nil {
		log.Errorf("[processFakeExpandClue] 扩展线索失败: %v", expandErr)

		// 删除缓存
		if redisClient := redisInit.GetInstance(cfg.LoadRedis()); redisClient != nil {
			if delErr := redisClient.Del(ctx, cacheKey).Err(); delErr != nil {
				log.Warnf("[processFakeExpandClue] 删除缓存失败: %v", delErr)
			} else {
				log.Infof("[processFakeExpandClue] 删除缓存成功: %s", cacheKey)
			}
		}

		return expandErr
	}

	// 8. 更新线索状态为已扩展
	err = db.Table("clues").
		Where("id = ?", clueId).
		Update("is_expand", clues.HAS_EXPAND).Error
	if err != nil {
		log.Errorf("[processFakeExpandClue] 更新线索扩展状态失败: %v", err)
	}

	// 9. 发送WebSocket通知进度
	// 获取已完成的任务数量和总任务数量，用于计算进度
	var hasDoneCount, cloudTaskCount int64
	err = db.Table("clue_tasks_ids").
		Where("clue_tasks_id = ? AND status >= ?", clueTaskIdsInfo.ClueTasksId, task_ids.StatusSuccessIds).
		Count(&hasDoneCount).Error
	if err != nil {
		log.Errorf("[processFakeExpandClue] 获取已完成任务数量失败: %v", err)
	}

	err = db.Table("clue_tasks_ids").
		Where("clue_tasks_id = ?", clueTaskIdsInfo.ClueTasksId).
		Count(&cloudTaskCount).Error
	if err != nil {
		log.Errorf("[processFakeExpandClue] 获取总任务数量失败: %v", err)
	}

	// 计算进度
	var progress float64
	if cloudTaskCount > 0 {
		progress = float64(hasDoneCount) * 100 / float64(cloudTaskCount)
	} else {
		progress = 100
	}

	// 更新主任务进度
	err = db.Table("clue_tasks").
		Where("id = ?", clueTaskIdsInfo.ClueTasksId).
		Update("progress", progress).Error
	if err != nil {
		log.Errorf("[processFakeExpandClue] 更新主任务进度失败: %v", err)
	}

	// 如果所有子任务都已完成，更新主任务状态为完成
	if hasDoneCount == cloudTaskCount {
		err = db.Table("clue_tasks").
			Where("id = ?", clueTaskIdsInfo.ClueTasksId).
			Update("status", clue_task.FinishStatus).Error
		if err != nil {
			log.Errorf("[processFakeExpandClue] 更新主任务状态为完成失败: %v", err)
		}
	}

	log.Infof("[processFakeExpandClue] 发送进度通知: user_id=%d, task_id=%d, progress=%.2f%%", userId, clueTaskIdsInfo.ClueTasksId, progress)

	// 10. 更新仿冒测绘任务状态（如果需要）
	if detectTaskId > 0 {
		// 使用常量值204代替phishing_fake_task.STEP_TWO_EXPAND_ALL
		const STEP_TWO_EXPAND_ALL = 204
		err = phishing_fake_task.NewTasker().UpdateAny(detectTaskId, map[string]any{
			"step_detail": STEP_TWO_EXPAND_ALL,
		})
		if err != nil {
			log.Errorf("[processFakeExpandClue] 更新仿冒测绘任务状态失败: %v", err)
		}

		// 更新仿冒测绘任务的线索进度
		err = phishing_fake_task.NewTasker().UpdateAny(detectTaskId, map[string]any{
			"clue_progress": progress,
		})
		if err != nil {
			log.Errorf("[processFakeExpandClue] 更新仿冒测绘任务线索进度失败: %v", err)
		}
	}

	// 11. 删除缓存，无论任务成功还是失败
	cacheKey = fmt.Sprintf("clue_running%d_%d", userId, clueTaskIdsInfo.ClueTasksId)
	if redisClient := redisInit.GetInstance(cfg.LoadRedis()); redisClient != nil {
		if err := redisClient.Del(ctx, cacheKey).Err(); err != nil {
			log.Warnf("[processFakeExpandClue] 删除缓存失败: %v", err)
		} else {
			log.Infof("[processFakeExpandClue] 删除缓存成功: %s", cacheKey)
		}
	}

	log.Infof("[processFakeExpandClue] 处理仿冒线索扩展完成: user_id=%d, clue_id=%d", userId, clueId)
	return nil
}

// getConfirmClues 获取确认的线索ID列表
func getConfirmClues(userId uint64, clueType int, detectTaskInfo map[string]interface{}) ([]uint64, error) {
	// 获取测绘任务信息
	var detectTask detect_assets_tasks.DetectAssetsTask
	err := mysql.GetDbClient().
		Where("id = ?", detectTaskInfo["id"]).
		First(&detectTask).Error
	if err != nil {
		return nil, fmt.Errorf("获取测绘任务信息失败: %v", err)
	}

	// 解析返回JSON
	var returnJsonMap map[string]interface{}
	if err := json.Unmarshal([]byte(detectTask.ReturnJson), &returnJsonMap); err != nil {
		return nil, fmt.Errorf("解析返回JSON失败: %v", err)
	}

	// 获取场景ID
	var sceneIds []uint64
	sceneGroupSet, ok := returnJsonMap["scene_group_set"].(map[string]interface{})
	if !ok {
		sceneGroupSet = make(map[string]interface{})
	}
	isUseInitClues := cast.ToInt(sceneGroupSet["expand_init_clues"])
	if isUseInitClues == 0 {
		if sceneIdsInterface, ok := returnJsonMap["scene_ids"].([]interface{}); ok {
			for _, id := range sceneIdsInterface {
				sceneIds = append(sceneIds, cast.ToUint64(id))
			}
		}
	}

	// 获取历史所选控股公司
	var oldCompanyList []string
	stepOneCompanyListKey := "step_one_company_list"
	if oldCompanyListInterface, ok := returnJsonMap[stepOneCompanyListKey].([]interface{}); ok {
		for _, company := range oldCompanyListInterface {
			oldCompanyList = append(oldCompanyList, cast.ToString(company))
		}
	}

	// 获取测绘任务确认的企业列表
	var confirmCompanyList []string
	if detectTask.ConfirmCompanyList != "" {
		if err := json.Unmarshal([]byte(detectTask.ConfirmCompanyList), &confirmCompanyList); err != nil {
			log.Errorf("[getConfirmClues] 解析测绘任务确认企业列表失败: %v", err)
			confirmCompanyList = []string{}
		}
	}

	// 获取分组ID
	groupId := detectTask.GroupId

	// 直接获取指定类型的线索
	ids := getClueIdsByType(userId, groupId, clueType, sceneIds, detectTask.Name, oldCompanyList, confirmCompanyList)
	log.Infof("[getConfirmClues] 获取指定类型线索: user_id=%d, clue_type=%d, count=%d", userId, clueType, len(ids))

	return ids, nil
}

// getClueIdsByType 根据类型获取线索ID列表
func getClueIdsByType(userId uint64, groupId uint64, clueType int, sceneIds []uint64, detectTaskName string, oldCompanyList []string, confirmCompanyList []string) []uint64 {
	log.Infof("[getClueIdsByType] 开始查询线索: user_id=%d, group_id=%d, clue_type=%d, scene_ids=%v, detect_task_name=%s, old_company_list=%v, confirm_company_list=%v",
		userId, groupId, clueType, sceneIds, detectTaskName, oldCompanyList, confirmCompanyList)

	clueModel := clues.NewCluer()
	var conditions []mysql.HandleFunc
	conditions = append(conditions,
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		mysql.WithColumnValue("group_id", groupId),
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO), // 供应链线索不进行扩展
	)

	// 状态条件
	statusList := []int{clues.CLUE_PASS_STATUS, clues.CLUE_DEFAULT_STATUS}
	conditions = append(conditions, mysql.WithWhere("status IN ?", statusList))

	// 类型条件
	if clueType == clues.TYPE_DOMAIN {
		typeList := []int{clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN}
		conditions = append(conditions, mysql.WithWhere("type IN ?", typeList))
	} else {
		conditions = append(conditions, mysql.WithColumnValue("type", clueType))
	}

	clueList, err := clueModel.ListAll(conditions...)
	if err != nil {
		log.Errorf("[getClueIdsByType] 查询线索失败: %v", err)
		return nil
	}

	log.Infof("[getClueIdsByType] 查询到原始线索总数: %d", len(clueList))

	// 打印所有查询到的原始线索信息
	for i, clue := range clueList {
		log.Infof("[getClueIdsByType] 原始线索[%d/%d]: id=%d, type=%d, content=%s, company_name=%s, status=%d, is_expand=%d, source=%d",
			i+1, len(clueList), clue.Id, clue.Type, clue.Content, clue.ClueCompanyName, clue.Status, clue.IsExpand, clue.Source)
	}

	// 过滤线索
	var ids []uint64
	var filteredCount int
	for i, clue := range clueList {
		// 过滤场景ID
		if utils.ContainsUint64(sceneIds, uint64(clue.Id)) {
			log.Infof("[getClueIdsByType] 线索[%d/%d]被过滤-场景ID匹配: id=%d, content=%s, company_name=%s, scene_ids=%v",
				i+1, len(clueList), clue.Id, clue.Content, clue.ClueCompanyName, sceneIds)
			filteredCount++
			continue
		}

		companyName := clue.ClueCompanyName
		if companyName == "" {
			log.Infof("[getClueIdsByType] 线索[%d/%d]被过滤-公司名称为空: id=%d, content=%s",
				i+1, len(clueList), clue.Id, clue.Content)
			filteredCount++
			continue
		}

		// 主企业名称直接通过
		if utils.StringsEqualFold(companyName, detectTaskName) {
			log.Infof("[getClueIdsByType] 线索[%d/%d]通过-主企业名称匹配: id=%d, content=%s, company_name=%s, detect_task_name=%s",
				i+1, len(clueList), clue.Id, clue.Content, companyName, detectTaskName)
			ids = append(ids, uint64(clue.Id))
			continue
		}

		// 检查是否在确认企业列表或历史选择中
		inConfirmList := utils.ContainsString(confirmCompanyList, companyName)
		inOldList := utils.ContainsString(oldCompanyList, companyName)
		if inConfirmList || inOldList {
			log.Infof("[getClueIdsByType] 线索[%d/%d]通过-在确认企业列表或历史选择中: id=%d, content=%s, company_name=%s, in_confirm_list=%v, in_old_company_list=%v",
				i+1, len(clueList), clue.Id, clue.Content, companyName, inConfirmList, inOldList)
			ids = append(ids, uint64(clue.Id))
		} else {
			log.Infof("[getClueIdsByType] 线索[%d/%d]被过滤-不在确认企业列表且不在历史选择中: id=%d, content=%s, company_name=%s, in_confirm_list=%v, in_old_company_list=%v",
				i+1, len(clueList), clue.Id, clue.Content, companyName, inConfirmList, inOldList)
			filteredCount++
		}
	}

	log.Infof("[getClueIdsByType] 线索过滤完成: 原始总数=%d, 被过滤数量=%d, 最终通过数量=%d, 通过的线索IDs=%v",
		len(clueList), filteredCount, len(ids), ids)

	return ids
}

// getEquityParentId 获取股权父ID
func getEquityParentId(name string) int64 {
	parent, err := company_equity.NewCompanyEquityModel().First(
		mysql.WithColumnValue("parent_id", 0),
		mysql.WithColumnValue("name", name),
	)
	if err != nil {
		return 0
	}
	return int64(parent.Id)
}

// getEquityChildList 获取股权子列表
func getEquityChildList(parentId int64) []struct {
	Name    string
	Percent float64
} {
	children, err := company_equity.NewCompanyEquityModel().ListAll(
		mysql.WithColumnValue("parent_id", parentId),
	)
	if err != nil {
		return nil
	}
	result := make([]struct {
		Name    string
		Percent float64
	}, 0, len(children))
	for _, c := range children {
		result = append(result, struct {
			Name    string
			Percent float64
		}{Name: c.Name, Percent: c.Percent})
	}
	return result
}

// 常量定义
const (
	// EXPAND_CLUES_LIMIT 限定扩展线索数量
	EXPAND_CLUES_LIMIT = 10000
)

// splitClueData 处理扩展结果数据，对应PHP中的splitClueData函数
func splitClueData(ctx context.Context, data []interface{}, tag string, userInfo *user.User, clueTaskIdsId uint64, clueTasksId uint64, detectTaskInfo map[string]interface{}) error {
	if len(data) == 0 {
		log.Warnf("[splitClueData] 扩展结果数据为空")
		return nil
	}
	if userInfo == nil {
		log.Errorf("[splitClueData] 用户信息为空")
		return errors.New("用户信息为空")
	}
	userId := userInfo.Id

	log.Infof("[splitClueData] 开始处理扩展结果数据: tag=%s, data_count=%d", tag, len(data))

	// 打印扩展结果的详细数据
	for i, item := range data {
		itemBytes, err := json.Marshal(item)
		if err != nil {
			log.Warnf("[splitClueData] 序列化扩展结果数据失败 index=%d: %v", i, err)
		} else {
			log.Infof("[splitClueData] 扩展结果数据[%d]: %s", i, string(itemBytes))
		}
		// 只打印前10条，避免日志过多
		if i >= 9 {
			log.Infof("[splitClueData] 扩展结果数据过多，只显示前10条，总共%d条", len(data))
			break
		}
	}

	// 添加去重集合，用于在数据组装阶段去重
	// 注意：去重不考虑parent_id，相同内容只保留一个
	type ClueKey struct {
		Type    int
		Content string
		Hash    int
		GroupId uint64
		UserId  uint64
	}
	clueDedupeMap := make(map[ClueKey]bool)

	log.Infof("[splitClueData] 初始化去重集合完成 - 策略：相同内容只保留一个，不考虑parent_id")

	// 获取任务信息
	var clueTaskIdsInfo task_ids.ClueTaskIds
	err := mysql.GetDbClient().Table("clue_tasks_ids").Where("id = ?", clueTaskIdsId).First(&clueTaskIdsInfo).Error
	if err != nil {
		log.Errorf("[splitClueData] 获取任务信息失败: %v", err)
		return err
	}

	// 这里我们简化处理，直接检查数据库
	var count int64
	err = mysql.GetDbClient().Table("clue_tasks").Where("id = ? AND user_id = ?", clueTasksId, userId).Count(&count).Error
	if err != nil {
		log.Errorf("[splitClueData] 检查任务是否存在失败: %v", err)
		return err
	}
	if count == 0 {
		log.Warnf("[splitClueData] 任务已被删除: clue_tasks_id=%d", clueTasksId)
		return nil
	}

	// 获取父线索ID
	var parentClue clues.Clue
	err = mysql.GetDbClient().Table("clues").
		Where("id = (SELECT clue_id FROM clue_tasks_ids WHERE id = ?)", clueTaskIdsId).
		First(&parentClue).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Errorf("[splitClueData] 获取父线索信息失败: %v", err)
		return err
	}
	db := mysql.GetDbClient()
	// 获取用户企业ID
	userCompanyId := uint64(0)
	if userInfo.CompanyId != nil && userInfo.CompanyId.Valid {
		userCompanyId = uint64(userInfo.CompanyId.Int64)
	}

	// 获取分组ID
	var groupId uint64
	err = db.Table("clue_tasks").Where("id = ?", clueTasksId).Select("group_id").Row().Scan(&groupId)
	if err != nil {
		log.Errorf("[splitClueData] 获取分组ID失败: %v", err)
		return err
	}

	// 获取父级线索ID
	var parentClueId uint64
	err = db.Table("clues").
		Joins("JOIN clue_tasks_ids ON clues.id = clue_tasks_ids.clue_id").
		Where("clue_tasks_ids.id = ?", clueTaskIdsId).
		Select("clues.id").Row().Scan(&parentClueId)
	if err != nil {
		log.Errorf("[splitClueData] 获取父级线索ID失败: %v", err)
		return err
	}

	// 判断是否为企业起源任务
	isCompanyOriginTask := false

	// 获取测绘任务名称，用于判断线索状态
	var detectTaskName string
	var detectType int
	var oldCompanyList []string
	var equityReflectList map[string]float64

	if detectTaskInfo != nil {
		detectTaskName = cast.ToString(detectTaskInfo["name"])
		detectType = cast.ToInt(detectTaskInfo["detect_type"])

		// 获取历史所选控股公司
		if confirmCompanyListInterface, ok := detectTaskInfo["confirm_company_list"].([]interface{}); ok {
			for _, company := range confirmCompanyListInterface {
				oldCompanyList = append(oldCompanyList, cast.ToString(company))
			}
		} else if returnJsonStr, ok := detectTaskInfo["return_json"].(string); ok && returnJsonStr != "" {
			var returnJsonMap map[string]interface{}
			if err := json.Unmarshal([]byte(returnJsonStr), &returnJsonMap); err == nil {
				if stepOneCompanyListInterface, ok := returnJsonMap["100"].([]interface{}); ok {
					for _, company := range stepOneCompanyListInterface {
						oldCompanyList = append(oldCompanyList, cast.ToString(company))
					}
				}
			}
		}

		// 如果从detectTaskInfo中获取不到，尝试直接从数据库获取
		if len(oldCompanyList) == 0 && detectTaskInfo["id"] != nil {
			detectTaskId := cast.ToUint64(detectTaskInfo["id"])
			var detectTask detect_assets_tasks.DetectAssetsTask
			err := db.Where("id = ?", detectTaskId).First(&detectTask).Error
			if err == nil && detectTask.ReturnJson != "" {
				var returnJsonMap map[string]interface{}
				if err := json.Unmarshal([]byte(detectTask.ReturnJson), &returnJsonMap); err == nil {
					if stepOneCompanyListInterface, ok := returnJsonMap["100"].([]interface{}); ok {
						for _, company := range stepOneCompanyListInterface {
							oldCompanyList = append(oldCompanyList, cast.ToString(company))
						}
					}
				}

				// 尝试从confirm_company_list字段获取
				if detectTask.ConfirmCompanyList != "" {
					var confirmCompanyList []string
					if err := json.Unmarshal([]byte(detectTask.ConfirmCompanyList), &confirmCompanyList); err == nil {
						oldCompanyList = append(oldCompanyList, confirmCompanyList...)
					}
				}
			}
		}

		log.Infof("[splitClueData] 获取到的企业列表: %v", oldCompanyList)

		// 获取股权关系
		equityReflectList = make(map[string]float64)
		if detectTaskName != "" {
			if equityParentId := getEquityParentId(detectTaskName); equityParentId > 0 {
				equityChildList := getEquityChildList(equityParentId)
				for _, child := range equityChildList {
					equityReflectList[child.Name] = child.Percent
				}
			}
		}
	}

	// 处理每条数据
	log.Infof("[splitClueData] 开始逐条处理扩展结果数据，总数量: %d", len(data))
	for i, item := range data {
		log.Infof("[splitClueData] 处理第%d条数据开始", i+1)
		// 每处理5条数据，检查一次任务是否被删除
		if i%5 == 0 {
			var count int64
			err = mysql.GetDbClient().Table("clue_tasks").Where("id = ? AND user_id = ?", clueTasksId, userId).Count(&count).Error
			if err != nil {
				log.Errorf("[splitClueData] 检查任务是否存在失败: %v", err)
				return err
			}
			if count == 0 {
				log.Warnf("[splitClueData] 任务已被删除: clue_tasks_id=%d", clueTasksId)
				return nil
			}
		}

		// 将item转换为map
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			log.Infof("[splitClueData] 第%d条数据不是map格式，尝试转换: %v", i+1, item)
			// 尝试将结构体转为JSON，再转为map
			jsonBytes, err := json.Marshal(item)
			if err != nil {
				log.Warnf("[splitClueData] 第%d条数据格式不正确: %v", i+1, item)
				continue
			}

			var convertedMap map[string]interface{}
			if err := json.Unmarshal(jsonBytes, &convertedMap); err != nil {
				log.Warnf("[splitClueData] 第%d条数据格式不正确，无法转换: %v", i+1, item)
				continue
			}

			itemMap = convertedMap
			log.Infof("[splitClueData] 第%d条数据转换成功: %v", i+1, itemMap)
		}

		// 获取类型
		clueType, ok := itemMap["type"]
		if !ok {
			log.Warnf("[splitClueData] 第%d条数据缺少type字段: %v", i+1, itemMap)
			continue
		}

		clueTypeInt := cast.ToInt(clueType)
		content, _ := itemMap["content"].(string)
		companyName, _ := itemMap["company_name"].(string)

		log.Infof("[splitClueData] 第%d条数据解析完成 - type:%d, content:%s, company_name:%s",
			i+1, clueTypeInt, content, companyName)

		// 根据类型处理不同的线索
		log.Infof("[splitClueData] 第%d条数据开始处理，类型: %d", i+1, clueTypeInt)
		switch clueTypeInt {
		case 5: // golang返回的5是主域名
			log.Infof("[splitClueData] 第%d条数据 - 处理主域名类型: content=%s, company_name=%s", i+1, content, companyName)
			err = writeClue(ctx, tag, map[string]interface{}{
				"type":              clues.TYPE_DOMAIN,
				"content":           content,
				"clue_company_name": companyName,
			}, userId, clueTaskIdsId, clueTasksId, parentClueId, groupId, userCompanyId, isCompanyOriginTask)

			if err != nil {
				log.Errorf("[splitClueData] 第%d条数据 - writeClue失败: %v", i+1, err)
			} else {
				log.Infof("[splitClueData] 第%d条数据 - writeClue成功", i+1)
			}

			// 写入clues表
			if err == nil {
				// 检查是否需要写入clues表
				needWriteToClues := true

				// 极速模式下过滤线索
				if detectTaskInfo != nil && detectType == detect_assets_tasks.DetectTypeSpeed {
					if companyName == "" {
						log.Infof("[splitClueData] 无企业名称被过滤掉了")
						needWriteToClues = false
					} else if !utils.StringsEqualFold(companyName, detectTaskName) {
						// 检查股权关系
						equityPercent := equityReflectList[companyName]
						if equityPercent > 0 {
							// 股权小于50%且不在所选企业列表中，过滤掉
							if equityPercent < 50 && !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 股权小于50%%并且企业名称不在所选企业的线索被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						} else {
							// 没有股权关系且不在所选企业列表中，过滤掉
							if !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 有企业名称(无股权||不在所选企业中)被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						}
					}
				}

				if needWriteToClues {
					log.Infof("[splitClueData] 第%d条数据 - 需要写入clues表", i+1)
					// 创建去重键
					clueKey := ClueKey{
						Type:    clues.TYPE_DOMAIN,
						Content: content,
						Hash:    0, // 域名类型不使用hash
						GroupId: groupId,
						UserId:  userId,
					}

					// 检查是否已经处理过相同的线索
					if clueDedupeMap[clueKey] {
						log.Infof("[splitClueData] 第%d条数据 - 跳过重复的域名线索 - content:%s, groupId:%d, userId:%d",
							i+1, content, groupId, userId)
						// 跳过后续处理，不创建线索
					} else {
						log.Infof("[splitClueData] 第%d条数据 - 创建新的域名线索 - content:%s, company_name:%s",
							i+1, content, companyName)
						// 标记为已处理
						clueDedupeMap[clueKey] = true

						clueModel := clues.NewCluer()
						// 设置线索状态为默认状态
						clueStatus := clues.CLUE_DEFAULT_STATUS

						newClue := &clues.Clue{
							UserId:          userId,
							CompanyId:       userCompanyId,
							ClueCompanyName: companyName,
							Content:         content,
							GroupId:         groupId,
							Type:            clues.TYPE_DOMAIN,
							Status:          clueStatus,
							ParentId:        parentClueId,
							Source:          clues.SOURCE_EXPAND,
							IsExpand:        clues.NOT_EXPAND,
							IsDeleted:       clues.NOT_DELETE,
						}
						err = clueModel.UpdateOrCreate(newClue)
						if err != nil {
							log.Errorf("[splitClueData] 第%d条数据 - 写入clues表失败: %v", i+1, err)
						} else {
							log.Infof("[splitClueData] 第%d条数据 - 写入clues表成功，线索ID: %d", i+1, newClue.Id)
						}
					}
				} else {
					log.Infof("[splitClueData] 第%d条数据 - 不需要写入clues表（被过滤）", i+1)
				}
			}
		case 7: // golang返回的7是子域名
			log.Infof("[splitClueData] 第%d条数据 - 处理子域名类型: content=%s, company_name=%s", i+1, content, companyName)
			err = writeClue(ctx, tag, map[string]interface{}{
				"type":              clues.TYPE_SUBDOMAIN,
				"content":           content,
				"clue_company_name": companyName,
			}, userId, clueTaskIdsId, clueTasksId, parentClueId, groupId, userCompanyId, isCompanyOriginTask)

			if err != nil {
				log.Errorf("[splitClueData] 第%d条数据 - writeClue失败: %v", i+1, err)
			} else {
				log.Infof("[splitClueData] 第%d条数据 - writeClue成功", i+1)
			}

			// 写入clues表
			if err == nil {
				// 检查是否需要写入clues表
				needWriteToClues := true

				// 极速模式下过滤线索
				if detectTaskInfo != nil && detectType == detect_assets_tasks.DetectTypeSpeed {
					if companyName == "" {
						log.Infof("[splitClueData] 无企业名称被过滤掉了")
						needWriteToClues = false
					} else if !utils.StringsEqualFold(companyName, detectTaskName) {
						// 检查股权关系
						equityPercent := equityReflectList[companyName]
						if equityPercent > 0 {
							// 股权小于50%且不在所选企业列表中，过滤掉
							if equityPercent < 50 && !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 股权小于50%%并且企业名称不在所选企业的线索被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						} else {
							// 没有股权关系且不在所选企业列表中，过滤掉
							if !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 有企业名称(无股权||不在所选企业中)被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						}
					}
				}

				if needWriteToClues {
					// 创建去重键
					clueKey := ClueKey{
						Type:    clues.TYPE_SUBDOMAIN,
						Content: content,
						Hash:    0, // 子域名类型不使用hash
						GroupId: groupId,
						UserId:  userId,
					}

					// 检查是否已经处理过相同的线索
					if clueDedupeMap[clueKey] {
						log.Infof("[splitClueData] 跳过重复的子域名线索 - content:%s, groupId:%d, userId:%d",
							content, groupId, userId)
						continue
					}

					// 标记为已处理
					clueDedupeMap[clueKey] = true

					clueModel := clues.NewCluer()
					// 设置线索状态为默认状态
					clueStatus := clues.CLUE_DEFAULT_STATUS

					newClue := &clues.Clue{
						UserId:          userId,
						CompanyId:       userCompanyId,
						ClueCompanyName: companyName,
						Content:         content,
						GroupId:         groupId,
						Type:            clues.TYPE_SUBDOMAIN,
						Status:          clueStatus,
						ParentId:        parentClueId,
						Source:          clues.SOURCE_EXPAND,
						IsExpand:        clues.NOT_EXPAND,
						IsDeleted:       clues.NOT_DELETE,
					}
					err = clueModel.UpdateOrCreate(newClue)
					if err != nil {
						log.Errorf("[splitClueData] 写入clues表失败: %v", err)
					}
				}
			}
		case clues.TYPE_ICP:
			log.Infof("[splitClueData] 第%d条数据 - 处理ICP类型: content=%s, company_name=%s", i+1, content, companyName)
			err = writeClue(ctx, tag, map[string]interface{}{
				"type":              clues.TYPE_ICP,
				"content":           content,
				"clue_company_name": companyName,
			}, userId, clueTaskIdsId, clueTasksId, parentClueId, groupId, userCompanyId, isCompanyOriginTask)

			if err != nil {
				log.Errorf("[splitClueData] 第%d条数据 - writeClue失败: %v", i+1, err)
			} else {
				log.Infof("[splitClueData] 第%d条数据 - writeClue成功", i+1)
			}

			// 写入clues表
			if err == nil {
				// 检查是否需要写入clues表
				needWriteToClues := true

				// 极速模式下过滤线索
				if detectTaskInfo != nil && detectType == detect_assets_tasks.DetectTypeSpeed {
					if companyName == "" {
						log.Infof("[splitClueData] 无企业名称被过滤掉了")
						needWriteToClues = false
					} else if !utils.StringsEqualFold(companyName, detectTaskName) {
						// 检查股权关系
						equityPercent := equityReflectList[companyName]
						if equityPercent > 0 {
							// 股权小于50%且不在所选企业列表中，过滤掉
							if equityPercent < 50 && !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 股权小于50%%并且企业名称不在所选企业的线索被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						} else {
							// 没有股权关系且不在所选企业列表中，过滤掉
							if !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 有企业名称(无股权||不在所选企业中)被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						}
					}
				}

				if needWriteToClues {
					// 创建去重键
					clueKey := ClueKey{
						Type:    clues.TYPE_ICP,
						Content: content,
						Hash:    0, // ICP类型不使用hash
						GroupId: groupId,
						UserId:  userId,
					}

					// 检查是否已经处理过相同的线索
					if clueDedupeMap[clueKey] {
						log.Infof("[splitClueData] 跳过重复的ICP线索 - content:%s, groupId:%d, userId:%d",
							content, groupId, userId)
						continue
					}

					// 标记为已处理
					clueDedupeMap[clueKey] = true

					clueModel := clues.NewCluer()
					// 设置线索状态为默认状态
					clueStatus := clues.CLUE_DEFAULT_STATUS

					newClue := &clues.Clue{
						UserId:          userId,
						CompanyId:       userCompanyId,
						ClueCompanyName: companyName,
						Content:         content,
						GroupId:         groupId,
						Type:            clues.TYPE_ICP,
						Status:          clueStatus,
						ParentId:        parentClueId,
						Source:          clues.SOURCE_EXPAND,
						IsExpand:        clues.NOT_EXPAND,
						IsDeleted:       clues.NOT_DELETE,
					}
					err = clueModel.UpdateOrCreate(newClue)
					if err != nil {
						log.Errorf("[splitClueData] 写入clues表失败: %v", err)
					}
				}
			}
		case clues.TYPE_LOGO:
			// 判断是否为极速模式
			if !isCompanyOriginTask {
				if detectTaskInfo != nil && cast.ToInt(detectTaskInfo["detect_type"]) == detect_assets_tasks.DetectTypeSpeed {
					log.Infof("[splitClueData] 极速模式不要icon: detect_type=%d", cast.ToInt(detectTaskInfo["detect_type"]))
					continue
				} else {
					log.Infof("[splitClueData] icon类型线索写入: detect_type=%d", cast.ToInt(detectTaskInfo["detect_type"]))

					// 添加详细的hash值追踪日志
					hashRaw := itemMap["hash"]
					log.Infof("[splitClueData] 原始hash值: %v (类型: %T)", hashRaw, hashRaw)

					hash, ok := itemMap["hash"].(string)
					if !ok {
						// 尝试其他类型转换
						if hashInt, ok := itemMap["hash"].(int); ok {
							hash = cast.ToString(hashInt)
							log.Infof("[splitClueData] hash从int转换为string: %s", hash)
						} else if hashFloat, ok := itemMap["hash"].(float64); ok {
							// 对于float64类型，需要正确处理科学计数法
							hashInt64 := int64(hashFloat)
							hash = cast.ToString(hashInt64)
							log.Infof("[splitClueData] hash从float64转换为string: 原始值=%e, 转换后=%s", hashFloat, hash)
						} else {
							hash = cast.ToString(hashRaw)
							log.Infof("[splitClueData] hash使用cast.ToString转换: %s", hash)
						}
					} else {
						log.Infof("[splitClueData] hash字符串值: %s", hash)
					}

					err = writeClue(ctx, tag, map[string]interface{}{
						"type":              clues.TYPE_LOGO,
						"content":           content,
						"hash":              hash,
						"clue_company_name": companyName,
					}, userId, clueTaskIdsId, clueTasksId, parentClueId, groupId, userCompanyId, isCompanyOriginTask)

					// 写入clues表
					if err == nil {
						// 检查是否需要写入clues表
						needWriteToClues := true

						// 极速模式下过滤线索
						if detectTaskInfo != nil && detectType == detect_assets_tasks.DetectTypeSpeed {
							if companyName == "" {
								log.Infof("[splitClueData] 无企业名称被过滤掉了")
								needWriteToClues = false
							} else if !utils.StringsEqualFold(companyName, detectTaskName) {
								// 检查股权关系
								equityPercent := equityReflectList[companyName]
								if equityPercent > 0 {
									// 股权小于50%且不在所选企业列表中，过滤掉
									if equityPercent < 50 && !isCompanyInList(companyName, oldCompanyList) {
										log.Infof("[splitClueData] 股权小于50%%并且企业名称不在所选企业的线索被过滤掉: %s", companyName)
										needWriteToClues = false
									}
								} else {
									// 没有股权关系且不在所选企业列表中，过滤掉
									if !isCompanyInList(companyName, oldCompanyList) {
										log.Infof("[splitClueData] 有企业名称(无股权||不在所选企业中)被过滤掉: %s", companyName)
										needWriteToClues = false
									}
								}
							}
						}

						if needWriteToClues {
							// 创建去重键（LOGO类型使用hash）
							clueKey := ClueKey{
								Type:    clues.TYPE_LOGO,
								Content: "", // LOGO类型不使用content
								Hash:    cast.ToInt(hash),
								GroupId: groupId,
								UserId:  userId,
							}

							// 检查是否已经处理过相同的线索
							if clueDedupeMap[clueKey] {
								log.Infof("[splitClueData] 跳过重复的LOGO线索 - hash:%s, groupId:%d, userId:%d",
									hash, groupId, userId)
								continue
							}

							// 标记为已处理
							clueDedupeMap[clueKey] = true

							clueModel := clues.NewCluer()
							// 设置线索状态为默认状态
							clueStatus := clues.CLUE_DEFAULT_STATUS

							// 添加hash转换日志
							hashInt := cast.ToInt(hash)
							log.Infof("[splitClueData] 写入clues表 - hash字符串: %s, 转换后的int: %d", hash, hashInt)

							newClue := &clues.Clue{
								UserId:          userId,
								CompanyId:       userCompanyId,
								ClueCompanyName: companyName,
								Content:         content,
								GroupId:         groupId,
								Type:            clues.TYPE_LOGO,
								Status:          clueStatus,
								ParentId:        parentClueId,
								Source:          clues.SOURCE_EXPAND,
								IsExpand:        clues.NOT_EXPAND,
								IsDeleted:       clues.NOT_DELETE,
								Hash:            hashInt,
							}
							err = clueModel.UpdateOrCreate(newClue)
							if err != nil {
								log.Errorf("[splitClueData] 写入clues表失败: %v", err)
							}
						}
					}
				}
			} else {
				log.Infof("[splitClueData] 不提取icon线索-2: user_id=%d", userId)
			}
		case clues.TYPE_KEYWORD:
			err = writeClue(ctx, tag, map[string]interface{}{
				"type":              clues.TYPE_KEYWORD,
				"content":           content,
				"clue_company_name": companyName,
			}, userId, clueTaskIdsId, clueTasksId, parentClueId, groupId, userCompanyId, isCompanyOriginTask)

			// 写入clues表
			if err == nil {
				// 检查是否需要写入clues表
				needWriteToClues := true

				// 极速模式下过滤线索
				if detectTaskInfo != nil && detectType == detect_assets_tasks.DetectTypeSpeed {
					if companyName == "" {
						log.Infof("[splitClueData] 无企业名称被过滤掉了")
						needWriteToClues = false
					} else if !utils.StringsEqualFold(companyName, detectTaskName) {
						// 检查股权关系
						equityPercent := equityReflectList[companyName]
						if equityPercent > 0 {
							// 股权小于50%且不在所选企业列表中，过滤掉
							if equityPercent < 50 && !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 股权小于50%%并且企业名称不在所选企业的线索被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						} else {
							// 没有股权关系且不在所选企业列表中，过滤掉
							if !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 有企业名称(无股权||不在所选企业中)被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						}
					}
				}

				if needWriteToClues {
					// 简单粗暴的全局去重检查
					globalKey := fmt.Sprintf("type_%d_content_%s_group_%d_user_%d", clues.TYPE_KEYWORD, content, groupId, userId)

					// 使用读锁检查是否已存在
					globalClueDedupeMutex.RLock()
					exists := globalClueDedupeMap[globalKey]
					globalClueDedupeMutex.RUnlock()

					if exists {
						log.Infof("[splitClueData] 全局去重：跳过重复的关键词线索 - content:%s, groupId:%d, userId:%d",
							content, groupId, userId)
					} else {
						// 使用写锁设置标记
						globalClueDedupeMutex.Lock()
						globalClueDedupeMap[globalKey] = true
						globalClueDedupeMutex.Unlock()

						log.Infof("[splitClueData] 全局去重：首次处理关键词线索 - content:%s, groupId:%d, userId:%d",
							content, groupId, userId)

						// 创建去重键
						clueKey := ClueKey{
							Type:    clues.TYPE_KEYWORD,
							Content: content,
							Hash:    0, // 关键词类型不使用hash
							GroupId: groupId,
							UserId:  userId,
						}

						// 检查是否已经处理过相同的线索
						if clueDedupeMap[clueKey] {
							log.Infof("[splitClueData] 跳过重复的关键词线索 - content:%s, groupId:%d, userId:%d",
								content, groupId, userId)
							// 跳过后续处理，不创建线索
						} else {
							// 标记为已处理
							clueDedupeMap[clueKey] = true

							log.Infof("[splitClueData] 准备写入关键词线索到clues表 - content:%s, groupId:%d, userId:%d, parentId:%d",
								content, groupId, userId, parentClueId)

							clueModel := clues.NewCluer()
							// 设置线索状态为默认状态
							clueStatus := clues.CLUE_DEFAULT_STATUS

							newClue := &clues.Clue{
								UserId:          userId,
								CompanyId:       userCompanyId,
								ClueCompanyName: companyName,
								Content:         content,
								GroupId:         groupId,
								Type:            clues.TYPE_KEYWORD,
								Status:          clueStatus,
								ParentId:        parentClueId,
								Source:          clues.SOURCE_EXPAND,
								IsExpand:        clues.NOT_EXPAND,
								IsDeleted:       clues.NOT_DELETE,
							}

							log.Infof("[splitClueData] 开始调用UpdateOrCreate - type:%d, content:%s, groupId:%d, userId:%d, parentId:%d",
								newClue.Type, newClue.Content, newClue.GroupId, newClue.UserId, newClue.ParentId)

							err = clueModel.UpdateOrCreate(newClue)
							if err != nil {
								log.Errorf("[splitClueData] 写入clues表失败: %v", err)
							} else {
								log.Infof("[splitClueData] 写入clues表成功 - newClueId:%d", newClue.Id)
							}
						}
					}
				}
			}
		case clues.TYPE_IP:
			err = writeClue(ctx, tag, map[string]interface{}{
				"type":              clues.TYPE_IP,
				"content":           content,
				"clue_company_name": companyName,
			}, userId, clueTaskIdsId, clueTasksId, parentClueId, groupId, userCompanyId, isCompanyOriginTask)

			// 写入clues表
			if err == nil {
				// 检查是否需要写入clues表
				needWriteToClues := true

				// 极速模式下过滤线索
				if detectTaskInfo != nil && detectType == detect_assets_tasks.DetectTypeSpeed {
					if companyName == "" {
						log.Infof("[splitClueData] 无企业名称被过滤掉了")
						needWriteToClues = false
					} else if !utils.StringsEqualFold(companyName, detectTaskName) {
						// 检查股权关系
						equityPercent := equityReflectList[companyName]
						if equityPercent > 0 {
							// 股权小于50%且不在所选企业列表中，过滤掉
							if equityPercent < 50 && !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 股权小于50%%并且企业名称不在所选企业的线索被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						} else {
							// 没有股权关系且不在所选企业列表中，过滤掉
							if !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 有企业名称(无股权||不在所选企业中)被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						}
					}
				}

				if needWriteToClues {
					clueModel := clues.NewCluer()
					// 设置线索状态为默认状态
					clueStatus := clues.CLUE_DEFAULT_STATUS

					newClue := &clues.Clue{
						UserId:          userId,
						CompanyId:       userCompanyId,
						ClueCompanyName: companyName,
						Content:         content,
						GroupId:         groupId,
						Type:            clues.TYPE_IP,
						Status:          clueStatus,
						ParentId:        parentClueId,
						Source:          clues.SOURCE_EXPAND,
						IsExpand:        clues.NOT_EXPAND,
						IsDeleted:       clues.NOT_DELETE,
					}
					err = clueModel.UpdateOrCreate(newClue)
					if err != nil {
						log.Errorf("[splitClueData] 写入clues表失败: %v", err)
					}
				}
			}
		case clues.TYPE_CERT:
			err = writeClue(ctx, tag, map[string]interface{}{
				"type":              clues.TYPE_CERT,
				"content":           content,
				"clue_company_name": companyName,
			}, userId, clueTaskIdsId, clueTasksId, parentClueId, groupId, userCompanyId, isCompanyOriginTask)

			// 写入clues表
			if err == nil {
				// 检查是否需要写入clues表
				needWriteToClues := true

				// 极速模式下过滤线索
				if detectTaskInfo != nil && detectType == detect_assets_tasks.DetectTypeSpeed {
					if companyName == "" {
						log.Infof("[splitClueData] 无企业名称被过滤掉了")
						needWriteToClues = false
					} else if !utils.StringsEqualFold(companyName, detectTaskName) {
						// 检查股权关系
						equityPercent := equityReflectList[companyName]
						if equityPercent > 0 {
							// 股权小于50%且不在所选企业列表中，过滤掉
							if equityPercent < 50 && !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 股权小于50%%并且企业名称不在所选企业的线索被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						} else {
							// 没有股权关系且不在所选企业列表中，过滤掉
							if !isCompanyInList(companyName, oldCompanyList) {
								log.Infof("[splitClueData] 有企业名称(无股权||不在所选企业中)被过滤掉: %s", companyName)
								needWriteToClues = false
							}
						}
					}
				}

				if needWriteToClues {
					clueModel := clues.NewCluer()
					// 设置线索状态为默认状态
					clueStatus := clues.CLUE_DEFAULT_STATUS

					newClue := &clues.Clue{
						UserId:          userId,
						CompanyId:       userCompanyId,
						ClueCompanyName: companyName,
						Content:         content,
						GroupId:         groupId,
						Type:            clues.TYPE_CERT,
						Status:          clueStatus,
						ParentId:        parentClueId,
						Source:          clues.SOURCE_EXPAND,
						IsExpand:        clues.NOT_EXPAND,
						IsDeleted:       clues.NOT_DELETE,
					}
					err = clueModel.UpdateOrCreate(newClue)
					if err != nil {
						log.Errorf("[splitClueData] 写入clues表失败: %v", err)
					}
				}
			}
		default:
			log.Warnf("[splitClueData] 第%d条数据 - 未知的线索类型: %d, content=%s, company_name=%s",
				i+1, clueTypeInt, content, companyName)
			continue
		}

		if err != nil {
			log.Errorf("[splitClueData] 第%d条数据 - 写入线索失败: %v", i+1, err)
		} else {
			log.Infof("[splitClueData] 第%d条数据 - 处理完成", i+1)
		}
	}

	log.Infof("[splitClueData] 所有数据处理完成，共处理%d条数据", len(data))

	// 统计处理结果
	processedCount := 0
	skippedCount := 0
	errorCount := 0

	// 这里我们简化统计，实际统计需要在处理循环中累加
	processedCount = len(data) // 假设所有数据都被处理了

	log.Infof("[splitClueData] 处理扩展结果数据完成: tag=%s, 总数据量=%d, 已处理=%d, 跳过=%d, 错误=%d",
		tag, len(data), processedCount, skippedCount, errorCount)
	log.Infof("[splitClueData] 去重集合大小: %d", len(clueDedupeMap))

	// 打印去重集合的内容（只显示前10个）
	keyCount := 0
	for key := range clueDedupeMap {
		if keyCount < 10 {
			log.Infof("[splitClueData] 去重键[%d]: Type=%d, Content=%s, GroupId=%d, UserId=%d",
				keyCount, key.Type, key.Content, key.GroupId, key.UserId)
			keyCount++
		} else {
			log.Infof("[splitClueData] 去重键过多，只显示前10个，总共%d个", len(clueDedupeMap))
			break
		}
	}

	return nil
}

// writeClue 写入线索，对应PHP中的writeClue函数
func writeClue(ctx context.Context, tag string, clue map[string]interface{}, userId uint64, clueTaskIdsId uint64, clueTasksId uint64, parentId uint64, groupId uint64, companyId uint64, isCompanyOriginTask bool) error {
	// 忽略的图标hash值列表
	icoIgs := []int{-1252041730, -*********, 1278323681, 1198047028, -*********, 1611729805, -*********, 1768726119, -1015107330, -*********, 2123863676, 81586312, -1275226814}

	content, _ := clue["content"].(string)
	hashStr, _ := clue["hash"].(string)
	clueType := cast.ToInt(clue["type"])

	// 添加详细的hash值日志
	log.Infof("[writeClue] 接收到的参数 - tag:%s, type:%d, content:%s, hashStr:%s", tag, clueType, content, hashStr)

	// 检查内容是否为空
	if content == "" && hashStr == "" {
		return nil
	}

	// 检查是否在黑名单中
	if checkClueInBlackList(clue) {
		log.Infof("[writeClue] 线索在黑名单中: tag=%s, type=%d, content=%s", tag, clueType, content)
		return nil
	}

	db := mysql.GetDbClient()

	// 检查线索是否已存在
	var count int64
	if clueType == clues.TYPE_LOGO {
		log.Infof("[writeClue] 处理logo线索: tag=%s", tag)
		hash := cast.ToInt(hashStr)
		err := db.Table("clue_records").
			Where("clue_tasks_id = ? AND clue_tasks_ids_id = ? AND user_id = ? AND type = ? AND hash = ?",
				clueTasksId, clueTaskIdsId, userId, clueType, hash).
			Count(&count).Error
		if err != nil {
			log.Errorf("[writeClue] 检查logo线索是否存在失败: %v", err)
			return err
		}
	} else {
		err := db.Table("clue_records").
			Where("clue_tasks_id = ? AND clue_tasks_ids_id = ? AND user_id = ? AND type = ? AND content = ?",
				clueTasksId, clueTaskIdsId, userId, clueType, content).
			Count(&count).Error
		if err != nil {
			log.Errorf("[writeClue] 检查线索是否存在失败: %v", err)
			return err
		}
	}

	// 如果线索已存在，则不再处理
	if count > 0 {
		log.Infof("[writeClue] 线索已存在: tag=%s, type=%d, content=%s", tag, clueType, content)
		return nil
	}

	// 处理LOGO类型线索
	if clueType == clues.TYPE_LOGO {
		hash := cast.ToInt(hashStr)
		log.Infof("[writeClue] LOGO类型线索处理 - tag:%s, hashStr:%s, 转换后hash:%d", tag, hashStr, hash)

		// 检查是否在忽略列表中
		for _, ignoreHash := range icoIgs {
			if hash == ignoreHash {
				log.Infof("[writeClue] 忽略指定ICO线索: tag=%s, hash=%d", tag, hash)
				return nil
			}
		}

		log.Infof("[writeClue] LOGO线索未在忽略列表中，继续处理 - tag:%s, hash:%d", tag, hash)

		// 处理图片URL
		if cfg.IsLocalClient() {
			// 本地化部署
			judgeContentTypeNum := len(strings.Split(content, "pp/public"))
			if judgeContentTypeNum < 2 {
				log.Infof("[writeClue] 取的golang总库的icon地址: %s", content)
				// 本地化处理
				downUrl := cfg.LoadCommon().Client.ApiPath + content
				base64Content, err := utils.DownloadAndEncodeBase64(downUrl)
				if err != nil {
					log.Errorf("[writeClue] 图片转base64失败: %v, clue_hash=%d", err, hash)
				}

				if base64Content != "" {
					// 保存base64图片
					content = utils.SaveBase64Ico(base64Content, userId)
					log.Infof("[writeClue] 保存base64图片成功: %s", content)
				} else {
					// 生成下载URL
					content = cfg.LoadCommon().Client.ApiPath + utils.GenDownloadUrl(content, filepath.Base(content), false)
					log.Infof("[writeClue] 生成下载URL: %s", content)
				}
			} else {
				// PHP自己爬取的icon
				log.Infof("[writeClue] php根据域名爬取icon数据: %s", content)
			}
		} else {
			// SAAS部署
			judgeContentTypeNum := len(strings.Split(content, "pp/public"))
			if judgeContentTypeNum < 2 {
				// 路径是加密的路径，转为加密前的地址
				content = utils.ParseDownloadUrl(content, false)
				log.Infof("[writeClue] 解析加密路径: %s", content)
			}
		}
		clue["content"] = content
	}

	// 获取Redis客户端，用于ICP查询
	redisClient := redisInit.GetInstance(cfg.LoadRedis())
	icpQuery := icp.NewICPQuery(redisClient)

	// 补充企业名称
	clueCompanyName, _ := clue["clue_company_name"].(string)
	if clueType == clues.TYPE_DOMAIN && clueCompanyName == "" {
		// 通过域名查询企业名称
		result, err := icpQuery.QueryDomain(ctx, content, false, false, false, userId)
		if err == nil && result != nil && result.Info != nil {
			if companyName, ok := result.Info["company_name"].(string); ok && companyName != "" {
				clueCompanyName = companyName
				clue["clue_company_name"] = companyName
			}
		}
	}
	if clueType == clues.TYPE_ICP && clueCompanyName == "" {
		// 通过ICP查询企业名称
		result, err := icpQuery.QueryICP(ctx, content, false, false, userId)
		if err == nil && result != nil && result.Info != nil {
			if companyName, ok := result.Info["company_name"].(string); ok && companyName != "" {
				clueCompanyName = companyName
				clue["clue_company_name"] = companyName
			}
		}
	}
	if clueType == clues.TYPE_CERT && clueCompanyName == "" {
		// 通过证书查询企业名称
		o, cn := extractCertInfo(content)
		if checkContainsChinese(o) {
			clueCompanyName = o
			clue["clue_company_name"] = o
		} else if cn != "" {
			// 通过域名查询企业名称
			topDomain := icp.GetTopDomain(cn)
			if topDomain != "" {
				result, err := icpQuery.QueryDomain(ctx, topDomain, false, false, false, userId)
				if err == nil && result != nil && result.Info != nil {
					if companyName, ok := result.Info["company_name"].(string); ok && companyName != "" {
						clueCompanyName = companyName
						clue["clue_company_name"] = companyName
					}
				}
			}
		}
	}

	// 创建线索记录
	clueRecord := map[string]interface{}{
		"clue_tasks_id":     clueTasksId,
		"clue_tasks_ids_id": clueTaskIdsId,
		"user_id":           userId,
		"company_id":        companyId,
		"status":            clues.CLUE_DEFAULT_STATUS,
		"parent_id":         parentId,
		"type":              clueType,
		"content":           content,
		"clue_company_name": clueCompanyName,
		"created_at":        time.Now(),
		"updated_at":        time.Now(),
	}

	// 如果是证书类型，需要拆分处理
	if clueType == clues.TYPE_CERT {
		// 拆分证书
		certs := splitCertString(content)
		for _, certItem := range certs {
			// 检查是否已存在
			var certCount int64
			err := db.Table("clue_records").
				Where("clue_tasks_id = ? AND clue_tasks_ids_id = ? AND user_id = ? AND type = ? AND content = ?",
					clueTasksId, clueTaskIdsId, userId, clueType, certItem).
				Count(&certCount).Error
			if err != nil {
				log.Errorf("[writeClue] 检查证书线索是否存在失败: %v", err)
				continue
			}

			if certCount == 0 {
				// 创建新记录
				certRecord := map[string]interface{}{
					"clue_tasks_id":     clueTasksId,
					"clue_tasks_ids_id": clueTaskIdsId,
					"user_id":           userId,
					"company_id":        companyId,
					"status":            clues.CLUE_DEFAULT_STATUS,
					"parent_id":         parentId,
					"type":              clueType,
					"content":           certItem,
					"clue_company_name": clueCompanyName,
					"created_at":        time.Now(),
					"updated_at":        time.Now(),
				}

				err := db.Table("clue_records").Create(certRecord).Error
				if err != nil {
					log.Errorf("[writeClue] 创建证书线索记录失败: %v", err)
				}
			}
		}
	} else {
		// 添加hash字段（如果有）
		if hashStr != "" {
			hashInt := cast.ToInt(hashStr)
			clueRecord["hash"] = hashInt
			log.Infof("[writeClue] 添加hash字段到clue_records - hashStr:%s, hashInt:%d", hashStr, hashInt)
		}

		// 创建记录
		log.Infof("[writeClue] 准备创建clue_records记录: %+v", clueRecord)
		err := db.Table("clue_records").Create(clueRecord).Error
		if err != nil {
			log.Errorf("[writeClue] 创建线索记录失败: %v", err)
			return err
		}
		log.Infof("[writeClue] 创建clue_records记录成功")
	}

	log.Infof("[writeClue] 写入线索成功: tag=%s, type=%d, content=%s", tag, clueType, content)
	return nil
}

// checkClueInBlackList 检查线索是否在黑名单中
func checkClueInBlackList(item map[string]interface{}) bool {
	clueType := cast.ToInt(item["type"])

	// 获取缓存key
	cacheKey := fmt.Sprintf("expand_clues_black_words_list_%d", clueType)

	// 从缓存获取黑名单列表
	var blackList []interface{}
	redisClient := redisInit.GetInstance(cfg.LoadRedis())
	cacheData, err := redisClient.Get(context.Background(), cacheKey).Result()
	if err == nil && cacheData != "" {
		if err := json.Unmarshal([]byte(cacheData), &blackList); err != nil {
			log.Error("[checkClueInBlackList] 解析缓存数据失败", err)
		}
	}

	if err != nil || len(blackList) == 0 {
		// 缓存未命中，从数据库获取
		blackKeywordModel := clue_black_keyword.NewClueBlackKeyworder()
		query := blackKeywordModel.Search(map[string]interface{}{
			"status": "1",
			"type":   strconv.Itoa(clueType),
		})

		var blackKeywords []clue_black_keyword.ClueBlackKeyword
		if err := query.Find(&blackKeywords).Error; err != nil {
			log.Error("[checkClueInBlackList] 获取黑名单列表失败", err)
			return false
		}

		// 根据类型获取不同的字段
		if clueType == clues.TYPE_LOGO {
			// 使用map去重
			hashMap := make(map[int]struct{})
			for _, keyword := range blackKeywords {
				hashMap[keyword.Hash] = struct{}{}
			}
			// 转换为切片
			blackList = make([]interface{}, 0, len(hashMap))
			for hash := range hashMap {
				blackList = append(blackList, hash)
			}
		} else {
			// 使用map去重
			nameMap := make(map[string]struct{})
			for _, keyword := range blackKeywords {
				nameMap[keyword.Name] = struct{}{}
			}
			// 转换为切片
			blackList = make([]interface{}, 0, len(nameMap))
			for name := range nameMap {
				blackList = append(blackList, name)
			}
		}

		// 设置缓存，有效期2小时
		if cacheData, err := json.Marshal(blackList); err == nil {
			if err := redisClient.Set(context.Background(), cacheKey, string(cacheData), 2*time.Hour).Err(); err != nil {
				log.Error("[checkClueInBlackList] 设置缓存失败", err)
			}
		}
	}

	log.Info("[checkClueInBlackList] 黑名单列表", map[string]interface{}{
		"black_list": blackList,
		"clue":       item,
	})

	// 检查是否在黑名单中
	if clueType == clues.TYPE_LOGO {
		hash := cast.ToInt(item["hash"])
		for _, value := range blackList {
			if blackHash, ok := value.(int); ok && hash == blackHash {
				log.Info("[checkClueInBlackList] 黑名单匹配", map[string]interface{}{
					"hash":    hash,
					"compare": blackHash,
					"clue":    item,
				})
				return true
			}
		}
	} else {
		content, _ := item["content"].(string)
		content = strings.ToLower(strings.TrimSpace(content))
		for _, value := range blackList {
			if blackName, ok := value.(string); ok {
				blackName = strings.ToLower(strings.TrimSpace(blackName))
				if content == blackName {
					log.Info("[checkClueInBlackList] 黑名单匹配", map[string]interface{}{
						"content": content,
						"compare": blackName,
						"clue":    item,
					})
					return true
				}
			}
		}
	}

	return false
}

// extractCertInfo 从证书字符串中提取 O 和 CN 字段
func extractCertInfo(str string) (string, string) {
	cnStr := ""
	oStr := ""
	if str == "" {
		return oStr, cnStr
	}

	// 匹配 CN= cn= O= o=  CN =  O = 等
	cnRegex := regexp.MustCompile(`(?i)\bCN\s*=`)
	onRegex := regexp.MustCompile(`(?i)\bO\s*=`)
	if cnRegex.MatchString(str) || onRegex.MatchString(str) {
		cnRegex = regexp.MustCompile(`(?i)\bCN\s*=\s*"?([^",]+)"?`)
		oRegex := regexp.MustCompile(`(?i)\bO\s*=\s*"?([^",]+)"?`)
		if cnMatches := cnRegex.FindStringSubmatch(str); len(cnMatches) > 1 {
			cnStr = cnMatches[1]
		}
		if oMatches := oRegex.FindStringSubmatch(str); len(oMatches) > 1 {
			oStr = oMatches[1]
		}
	} else {
		// 使用 isDomainValid 方法验证域名
		if isDomainValid(str) {
			cnStr = str
		} else {
			oStr = str
		}
	}
	return oStr, cnStr
}

// isDomainValid 检查域名是否有效
func isDomainValid(domain string) bool {
	if domain == "" {
		return false
	}

	// 移除可能的协议前缀
	domain = strings.TrimPrefix(domain, "http://")
	domain = strings.TrimPrefix(domain, "https://")

	// 移除路径和查询参数
	if idx := strings.Index(domain, "/"); idx != -1 {
		domain = domain[:idx]
	}
	if idx := strings.Index(domain, "?"); idx != -1 {
		domain = domain[:idx]
	}

	// 检查域名长度
	if len(domain) > 255 {
		return false
	}

	// 检查域名格式，支持更多特殊字符
	domainRegex := regexp.MustCompile(`^([a-zA-Z0-9]([a-zA-Z0-9\-_\.]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$`)
	return domainRegex.MatchString(domain)
}

// checkContainsChinese 检查字符串是否包含中文
func checkContainsChinese(s string) bool {
	if s == "" {
		return false
	}
	// 使用正则表达式匹配中文字符
	matched, _ := regexp.MatchString("[\u4e00-\u9fa5]", s)
	return matched
}

// splitCertString 拆分证书字符串
func splitCertString(cert string) []string {
	if cert == "" {
		return nil
	}

	// 不再提取内容，直接返回原始证书字符串
	return []string{cert}
}

// handleSpeedExpand 处理极速模式下的扩展
func handleSpeedExpand(ctx context.Context, userInfo *user.User, detectTaskInfo map[string]interface{}, clueType int, detectTaskId uint64, expandType int32) error {
	if userInfo == nil {
		log.Errorf("[handleSpeedExpand] 用户信息为空")
		return errors.New("用户信息为空")
	}
	userId := userInfo.Id
	// 获取线索ID列表
	clueIds, err := getConfirmClues(userId, clueType, detectTaskInfo)
	if err != nil {
		return fmt.Errorf("获取确认线索失败: %v", err)
	}

	if len(clueIds) == 0 {
		// 线索为空，更新进度并直接返回
		log.Infof("[ExpandCluesJob] %s类型线索为空，跳过处理: detect_task_id=%d", expandType, detectTaskId)

		// 更新进度（只在特定类型时更新）
		switch expandType {
		case clues.TYPE_DOMAIN:
			log.Infof("[ExpandCluesJob] 域名线索为空，设置进度为50%%: detect_task_id=%d", detectTaskId)
			err = detect_assets_tasks.NewModel().UpdateAny(
				map[string]interface{}{
					"clue_progress": 50,
				},
				detect_assets_tasks.WithID(detectTaskId),
			)
		case clues.TYPE_ICP:
			log.Infof("[ExpandCluesJob] ICP线索为空，设置进度为75%%: detect_task_id=%d", detectTaskId)
			err = detect_assets_tasks.NewModel().UpdateAny(
				map[string]interface{}{
					"clue_progress": 75,
				},
				detect_assets_tasks.WithID(detectTaskId),
			)
		}
		if err != nil {
			log.Errorf("[ExpandCluesJob] 更新任务进度失败: %v", err)
		}
		return nil
	}

	// 创建线索任务
	isIntellectMode := cast.ToInt32(detectTaskInfo["is_intellect_mode"])
	expandTypeName := getExpandTypeName(int(expandType))
	taskName := ""
	if isIntellectMode > 0 {
		taskName = fmt.Sprintf("资产测绘-智能模式-线索扩充-%s-%s", expandTypeName, time.Now().Format("2006-01-02-15-04-05"))
	} else {
		taskName = fmt.Sprintf("资产测绘-极速模式-线索扩充-%s-%s", expandTypeName, time.Now().Format("2006-01-02-15-04-05"))
	}

	clueTaskModel := clue_task.NewClueTasker()
	newClueTask := &clue_task.ClueTask{
		UserId:              userId,
		GroupId:             cast.ToUint64(detectTaskInfo["group_id"]),
		CompanyId:           cast.ToUint64(detectTaskInfo["company_id"]),
		Name:                taskName,
		Status:              clue_task.RunningStatus,
		Node:                "golang-node",
		DetectAssetsTasksId: detectTaskId,
		Progress:            0,
		IsShow:              clue_task.ShowHidden,
	}

	// 如果当前登录的用户id不等于userId，设置安服用户ID
	if safeUserId, ok := detectTaskInfo["safe_user_id"]; ok && safeUserId != nil {
		newClueTask.SafeUserId = cast.ToUint64(safeUserId)
	}

	err = clueTaskModel.Create(newClueTask)
	if err != nil {
		return fmt.Errorf("创建线索任务失败: %v", err)
	}

	// 创建线索任务ID关联
	db := mysql.GetDbClient()
	now := time.Now()
	for _, clueId := range clueIds {
		clueTaskIds := struct {
			ClueTasksID uint64    `gorm:"column:clue_tasks_id"`
			ClueID      uint64    `gorm:"column:clue_id"`
			Status      int       `gorm:"column:status"`
			CreatedAt   time.Time `gorm:"column:created_at"`
		}{
			ClueTasksID: newClueTask.Id,
			ClueID:      clueId,
			Status:      0, // 默认状态
			CreatedAt:   now,
		}

		err = db.Table("clue_tasks_ids").Create(&clueTaskIds).Error
		if err != nil {
			log.Errorf("[ExpandCluesJob] 创建线索任务ID关联失败: clue_id=%d, error=%v", clueId, err)
		}
	}

	// 处理线索扩展
	clueTaskIdsList := make([]struct {
		ID          uint64 `gorm:"column:id"`
		ClueTasksID uint64 `gorm:"column:clue_tasks_id"`
		ClueID      uint64 `gorm:"column:clue_id"`
		Status      int    `gorm:"column:status"`
	}, 0, len(clueIds))
	err = db.Table("clue_tasks_ids").
		Where("clue_tasks_id = ? AND status = ?", newClueTask.Id, task_ids.StatusDefaultIds).
		Find(&clueTaskIdsList).Error
	if err != nil {
		log.Errorf("[ExpandCluesJob] 获取线索任务ID列表失败: %v", err)
		return err
	}

	for _, item := range clueTaskIdsList {
		// 处理线索扩展
		isFakeClueExpend := cast.ToBool(detectTaskInfo["is_fake_clue_expend"])
		err = handleExpandClue(ctx, userInfo, item.ClueID, item.ID, detectTaskId, expandType, isFakeClueExpend)
		if err != nil {
			log.Errorf("[ExpandCluesJob] 处理线索扩展失败: %v", err)
		}

		// 更新线索任务ID状态为成功
		err = db.Table("clue_tasks_ids").
			Where("id = ?", item.ID).
			Update("status", task_ids.StatusSuccessIds).Error
		if err != nil {
			log.Errorf("[ExpandCluesJob] 更新线索任务ID状态失败: %v", err)
		}
	}

	// 更新线索任务状态为完成
	err = db.Table("clue_tasks").
		Where("id = ?", newClueTask.Id).
		Updates(map[string]interface{}{
			"progress": 100,
			"status":   clue_task.FinishStatus,
		}).Error
	if err != nil {
		log.Errorf("[ExpandCluesJob] 更新线索任务状态为完成失败: %v", err)
	} else {
		log.Infof("[ExpandCluesJob] 更新线索任务状态为完成: clue_task_id=%d, expand_type=%d", newClueTask.Id, expandType)
	}

	return nil
}

// isCompanyInList 检查企业名称是否在所选企业列表中
func isCompanyInList(companyName string, oldCompanyList []string) bool {
	log.Infof("[isCompanyInList] 检查企业名称是否在列表中: companyName=%s, oldCompanyList=%v", companyName, oldCompanyList)
	for _, oldCompany := range oldCompanyList {
		if utils.StringsEqualFold(companyName, oldCompany) {
			log.Infof("[isCompanyInList] 企业名称匹配成功: companyName=%s, oldCompany=%s", companyName, oldCompany)
			return true
		}
	}
	log.Infof("[isCompanyInList] 企业名称不在列表中: companyName=%s", companyName)
	return false
}

func sendProgressForExpandCluesJob(ctx context.Context, userId uint64, task *detect_assets_tasks.DetectAssetsTask, expandType string, doneCount uint64, totalCount uint64, progress float64, status int, isFinish bool) error {
	// 计算进度：如果总数为0则进度为100，否则按比例计算
	var finalProgress float64
	if totalCount == 0 {
		finalProgress = 100
	} else {
		// 使用高精度计算：(doneCount + progress) * 100 / totalCount
		finalProgress = (float64(doneCount) + progress) * 100 / float64(totalCount)
		// 保留2位小数
		finalProgress = math.Round(finalProgress*100) / 100
	}

	// 设置状态：如果status为0，则根据totalCount判断状态；否则使用传入的status
	var setStatus int
	if status == 0 {
		if totalCount == 0 {
			setStatus = clue_task.FinishStatus
		} else {
			setStatus = clue_task.RunningStatus
		}
	} else {
		setStatus = status
	}
	// 将进度值四舍五入到2位小数
	finalProgress = math.Round(finalProgress*100) / 100
	// 如果进度为100，则设置为99
	if !isFinish && finalProgress == 100 {
		log.Infof("[sendProgressForExpandCluesJob] 进度为100,但是isFinish为false，设置为99")
		finalProgress = 99
	}

	if task != nil {
		if task.DetectType == detect_assets_tasks.DetectTypeSpeed {
			switch expandType {
			case "domain":
				log.Infof("[sendProgressForExpandCluesJob] 域名的进度: %f", finalProgress)
				task.ClueProgress = 100.0/4 + finalProgress/4
			case "icp":
				log.Infof("[sendProgressForExpandCluesJob] icp的进度: %f", finalProgress)
				task.ClueProgress = 200.0/4 + finalProgress/4
			case "ip":
				log.Infof("[sendProgressForExpandCluesJob] ip的进度: %f", finalProgress)
				task.ClueProgress = finalProgress / 4
			default:
				log.Infof("[sendProgressForExpandCluesJob] 证书的进度: %f", finalProgress)
				task.ClueProgress = 300.0/4 + finalProgress/4
			}

			err := websocket_message.PublishSuccess(int64(userId), "detect_assets_tip2_all", map[string]any{
				"progress": finalProgress,
				"status":   setStatus,
				"task_id":  task.ID,
			})
			if err != nil {
				log.Errorf("[sendProgressForExpandCluesJob] 发送进度失败: %v", err)
			}
		} else {
			cmd := fmt.Sprintf("detect_assets_tip2_%s", expandType)
			err := websocket_message.PublishSuccess(int64(userId), cmd, map[string]any{
				"progress": finalProgress,
				"status":   setStatus,
				"task_id":  task.ID,
			})
			if err != nil {
				log.Errorf("[sendProgressForExpandCluesJob] 发送进度失败: %v", err)
			}
		}
	}

	return nil
}

// getExpandTypeName 将扩展类型转换为易读的名称
func getExpandTypeName(expandType int) string {
	switch expandType {
	case clues.TYPE_DOMAIN:
		return "domain"
	case clues.TYPE_CERT:
		return "cert"
	case clues.TYPE_ICP:
		return "icp"
	case clues.TYPE_LOGO:
		return "logo"
	case clues.TYPE_KEYWORD:
		return "keyword"
	case clues.TYPE_SUBDOMAIN:
		return "subdomain"
	case clues.TYPE_IP:
		return "ip"
	case clues.TYPE_FID:
		return "fid"
	default:
		return fmt.Sprintf("type_%d", expandType)
	}
}
