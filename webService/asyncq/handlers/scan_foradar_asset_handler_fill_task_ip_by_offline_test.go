package handlers

import (
	"database/sql"

	testcommon "micro-service/initialize/common_test"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/mysql/task"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
)

// setupFillTaskIpByOffLineTestEnvironment 初始化测试环境
func setupFillTaskIpByOffLineTestEnvironment(fn ...func(esServer *testcommon.MockServer)) {
	cfg.InitLoadCfg()
	log.Init()

	// 启用测试环境
	testcommon.SetTestEnv(true)

	// 初始化Mock服务 - 使用testcommon的MockServer
	mock := testcommon.NewMockEsServer()

	// 设置测试环境
	es.SetTestEnv(true)
	mysql.SetTestEnv(true)
	redis.SetTestEnv(true)
	testcommon.SetTestEnv(true) // 确保testcommon也知道这是测试环境

	for _, f := range fn {
		f(mock)
	}

	// 设置mock客户端到testcommon，这样GetEsClient()就能获取到正确的客户端
	testcommon.SetElasticClient(mock.NewElasticClient())

	// 初始化数据库
	es.GetInstance(cfg.LoadElastic())
	mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

// setupESMockForTest 为测试设置ES Mock
func setupESMockForTest() *es.MockServer {
	esServer := es.NewMockServer()

	// 注册refresh操作的Handler
	esServer.Register("/fofaee_task_assets/_refresh", map[string]interface{}{
		"acknowledged": true,
		"_shards": map[string]interface{}{
			"total":      1,
			"successful": 1,
			"failed":     0,
		},
	})

	// 设置Mock ES客户端
	mockClient := esServer.NewElasticMockClient()
	es.SetElasticMockClient(mockClient)

	return esServer
}

// createTestScanForadarAsset 创建测试用的ScanForadarAsset实例
func createTestScanForadarAsset(taskID uint64, flag string, portRange int) *ScanForadarAsset {
	task := scan_task.ScanTasks{
		Flag:      flag,
		PortRange: portRange,
		UserId:    1001,
		CompanyId: 2001,
	}
	task.ID = uint(taskID) // 直接设置ID字段

	return &ScanForadarAsset{
		Task: task,
	}
}

// createTestScanForadarAssetWithDetectTask 创建带DetectTask的测试实例
func createTestScanForadarAssetWithDetectTask(taskID uint64, flag string, portRange int) *ScanForadarAsset {
	detectTask := detect_assets_tasks.DetectAssetsTask{
		UserId: 1001,
	}
	detectTask.ID = 3001 // 直接设置ID字段

	task := scan_task.ScanTasks{
		Flag:      flag,
		PortRange: portRange,
		UserId:    1001,
		CompanyId: 2001,
	}
	task.ID = uint(taskID) // 直接设置ID字段

	return &ScanForadarAsset{
		Task:       task,
		DetectTask: &detectTask,
	}
}

// mockTaskProbeInfos 创建TaskProbeInfo的Mock数据
func mockTaskProbeInfos() []task.TaskProbeInfo {
	return []task.TaskProbeInfo{
		{
			Id:           1,
			TaskId:       1001,
			Ip:           sql.NullString{String: "***********", Valid: true},
			Port:         80,
			BaseProtocol: "http",
		},
		{
			Id:           2,
			TaskId:       1001,
			Ip:           sql.NullString{String: "***********", Valid: true},
			Port:         443,
			BaseProtocol: "https",
		},
		{
			Id:           3,
			TaskId:       1001,
			Ip:           sql.NullString{String: "***********", Valid: true}, // 重复IP，测试去重
			Port:         8080,
			BaseProtocol: "http",
		},
	}
}

// mockTaskIps 创建TaskIps的Mock数据
func mockTaskIps() []task.TaskIps {
	return []task.TaskIps{
		{
			Id:     1,
			TaskId: 1001,
			Ip:     sql.NullString{String: "********", Valid: true},
		},
		{
			Id:     2,
			TaskId: 1001,
			Ip:     sql.NullString{String: "********", Valid: true},
		},
		{
			Id:     3,
			TaskId: 1001,
			Ip:     sql.NullString{String: "2001:db8::1", Valid: true}, // IPv6
		},
	}
}

// mockRecommendResults 创建RecommendResult的Mock数据
func mockRecommendResults() []recommend_result.RecommendResult {
	return []recommend_result.RecommendResult{
		{
			Id:       "rec1",
			Ip:       "***********",
			Port:     "80",
			Protocol: "http",
			Title:    "Test Site 1",
			Url:      "http://***********",
			Flag:     "test_flag",
		},
		{
			Id:       "rec2",
			Ip:       "***********",
			Port:     "443",
			Protocol: "https",
			Title:    "Test Site 2",
			Url:      "https://***********",
			Flag:     "test_flag",
		},
		{
			Id:       "rec3",
			Ip:       "***********", // 新IP，不在任务IP中
			Port:     "80",
			Protocol: "http",
			Title:    "Test Site 3",
			Url:      "http://***********",
			Flag:     "test_flag",
		},
	}
}

// mockExistingTaskAssets 创建已存在的FofaeeTaskAssets Mock数据
func mockExistingTaskAssets() []fofaee_task_assets.FofaeeTaskAssets {
	return []fofaee_task_assets.FofaeeTaskAssets{
		{
			Id:     "1001_***********",
			TaskId: 1001,
			Ip:     "***********",
			UserId: 1001,
		},
	}
}
