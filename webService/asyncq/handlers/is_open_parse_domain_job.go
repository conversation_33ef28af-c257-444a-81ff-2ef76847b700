package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"micro-service/pkg/domain"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
)

type IsOpenParseDomainJobPayload struct {
	UserId    int    `json:"user_id"`    // 用户ID
	SubDomain string `json:"sub_domain"` // 子域名,必填
	AssetsId  int    `json:"assets_id"`  // 资产ID
}

// IsOpenParseDomainJob 判断域名是否开启解析
// 消息体:
//
//	 {
//			"user_id": 1, // 用户ID
//			"sub_domain": "baidu.com", // 子域名，必填
//			"assets_id": 1 // 资产ID
//		}
func IsOpenParseDomainJob(ctx context.Context, t *asyncq.Task) error {
	payloadInfo, err := parsePayloadForIsOpenParseDomainJob([]byte(t.Payload))
	if err != nil {
		return err
	}
	topDomain := utils.GetTopDomain(payloadInfo.SubDomain)
	if topDomain == "" {
		return errors.New("top_domain is required")
	}

	isParse := domain.IsWildcardDomain(payloadInfo.SubDomain)
	log.Info("IsOpenParseDomainJob", "检测域名解析状态成功", map[string]interface{}{
		"sub_domain": payloadInfo.SubDomain,
		"assets_id":  payloadInfo.AssetsId,
		"is_parse":   isParse,
	})
	return nil
}

func parsePayloadForIsOpenParseDomainJob(payload []byte) (payloadInfo *IsOpenParseDomainJobPayload, err error) {
	err = json.Unmarshal(payload, &payloadInfo)
	if err != nil {
		log.Error("IsOpenParseDomainJob", "解析消息体失败", map[string]interface{}{
			"payload": string(payload),
			"error":   err.Error(),
		})
		return nil, err
	}
	if payloadInfo.SubDomain == "" {
		log.Error("IsOpenParseDomainJob", "子域名为空", map[string]interface{}{
			"payload": string(payload),
		})
		return nil, errors.New("sub_domain is required")
	}
	return payloadInfo, nil
}
