package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	testcommon "micro-service/initialize/common_test"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	m "micro-service/middleware/mysql"
	"micro-service/middleware/mysql/scan_task"
	redisMiddleware "micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/cfg"
	asyncq "micro-service/pkg/queue_helper"
	"testing"
	"time"
)

func TestScanCreateDetectHandler(t *testing.T) {
	// 启用测试环境，这样mysql.GetDbClient()会使用mock数据库
	mysql.ForceTest(false)
	es.ForceTest(false)
	redis.ForceTest(false)
	testcommon.SetTestEnv(false)
	cfg.LoadAPP()
	mysql.GetInstance(cfg.LoadMysql())
	es.GetInstance(cfg.LoadElastic())
	redis.GetInstance(cfg.LoadRedis())

	// 构造固定参数，user_id 为 886
	payload := map[string]interface{}{
		"user_id": 886,
	}

	// 将参数转换为JSON字符串
	payloadBytes, err := json.Marshal(payload)
	assert.NoError(t, err)

	// 调用 CreateDetectTaskJobHandler
	err = CreateDetectTaskJobHandler(context.Background(), &asyncq.Task{
		Payload: string(payloadBytes),
	})

	// 打印结果
	if err != nil {
		fmt.Printf("CreateDetectTaskJobHandler 执行失败: %v\n", err)
	} else {
		fmt.Printf("CreateDetectTaskJobHandler 执行成功，user_id: %d\n", 886)
	}

	// 这里不使用 assert.NoError，因为可能会有业务逻辑错误（比如用户不存在等）
	// 主要目的是调试，所以只打印结果
}

func TestScanForadarAssetsHandler(t *testing.T) {
	// 启用测试环境，这样mysql.GetDbClient()会使用mock数据库
	mysql.ForceTest(false)
	es.ForceTest(false)
	redis.ForceTest(false)
	testcommon.SetTestEnv(false)
	cfg.LoadAPP()
	mysql.GetInstance(cfg.LoadMysql())
	es.GetInstance(cfg.LoadElastic())
	redis.GetInstance(cfg.LoadRedis())
	st, err := m.NewDSL[scan_task.ScanTasks]().FindByParams([][]interface{}{
		//{"status", "=", 2},
		//{"step", "=", 4},
		//{"scan_type", "=", 1},
		//{"flag", "!=", ""},
		{"id", "=", 24672},
	})
	assert.NoError(t, err)
	_, err = m.NewDSL[scan_task.ScanTasks]().UpdateByID(uint64(st.ID), map[string]interface{}{
		"status": 1,
	})
	assert.NoError(t, err)
	err = ScanForadarAssetHandler(context.Background(), &asyncq.Task{
		Payload: string([]byte(fmt.Sprintf(`{"task_id": %d}`, st.ID))),
	})
	assert.NoError(t, err)
}

func TestGetCacheValue(t *testing.T) {
	t.Skip()
	// 启用真实环境连接
	mysql.ForceTest(false)
	es.ForceTest(false)
	redis.ForceTest(false)
	testcommon.SetTestEnv(false)
	cfg.LoadAPP()
	redis.GetInstance(cfg.LoadRedis())

	// 测试获取缓存值
	cacheKey := "cache:ip_to_clue_domain:24672:**************"

	// 方法1: 使用 GetCache 方法
	var cacheValue1 string
	result1 := redisMiddleware.GetCache(cacheKey, &cacheValue1)
	fmt.Printf("方法1 - GetCache结果: success=%t, value='%s'\n", result1, cacheValue1)

	// 方法2: 直接使用Redis原生Get方法
	cacheValue2, err := redisMiddleware.GetClient().Get(context.Background(), cacheKey).Result()
	fmt.Printf("方法2 - 原生Get结果: error=%v, value='%s'\n", err, cacheValue2)

	// 方法3: 使用cache.GetCacheKey生成key再测试
	generatedKey := cache.GetCacheKey("ip_to_clue_domain", "24672:**************")
	fmt.Printf("生成的缓存键: '%s'\n", generatedKey)

	var cacheValue3 string
	result3 := redisMiddleware.GetCache(generatedKey, &cacheValue3)
	fmt.Printf("方法3 - 使用生成key的GetCache结果: success=%t, value='%s'\n", result3, cacheValue3)

	// 方法4: 直接使用生成key的原生Get方法
	cacheValue4, err4 := redisMiddleware.GetClient().Get(context.Background(), generatedKey).Result()
	fmt.Printf("方法4 - 使用生成key的原生Get结果: error=%v, value='%s'\n", err4, cacheValue4)

	// 测试数字类型缓存
	fmt.Println("\n=== 测试数字类型缓存 ===")

	// 设置一个数字缓存
	testNumKey := "test_num_cache"
	testNum := 123
	redisMiddleware.SetCache(testNumKey, 60*time.Second, testNum)

	// 方法1: 使用GetCache获取数字
	var retrievedNum1 int
	result5 := redisMiddleware.GetCache(testNumKey, &retrievedNum1)
	fmt.Printf("数字缓存 - GetCache结果: success=%t, value=%d\n", result5, retrievedNum1)

	// 方法2: 使用原生Get获取数字缓存的原始值
	rawValue, err5 := redisMiddleware.GetClient().Get(context.Background(), testNumKey).Result()
	fmt.Printf("数字缓存 - 原生Get结果: error=%v, raw_value='%s'\n", err5, rawValue)

	// 测试实际的爬虫计数缓存
	fmt.Println("\n=== 测试实际爬虫计数缓存 ===")
	crawlerCacheKey := "crawler_num:24667:***************"

	// 方法1: 使用GetCache获取爬虫计数
	var crawlerIpNum1 int
	result6 := redisMiddleware.GetCache(crawlerCacheKey, &crawlerIpNum1)
	fmt.Printf("爬虫计数缓存 - GetCache结果: success=%t, value=%d\n", result6, crawlerIpNum1)

	// 方法2: 使用原生Get获取爬虫计数缓存的原始值
	crawlerRawValue, err6 := redisMiddleware.GetClient().Get(context.Background(), crawlerCacheKey).Result()
	fmt.Printf("爬虫计数缓存 - 原生Get结果: error=%v, raw_value='%s'\n", err6, crawlerRawValue)

	// 验证字符串缓存的差异
	fmt.Println("\n=== 验证字符串缓存的差异 ===")

	// 方式1: 通过Go的SetCache存储字符串（JSON格式）
	testStrKey1 := "test_string_json"
	testStr := "cr-power.com"
	redisMiddleware.SetCache(testStrKey1, 60*time.Second, testStr)

	var retrievedStr1 string
	result7 := redisMiddleware.GetCache(testStrKey1, &retrievedStr1)
	rawStr1, _ := redisMiddleware.GetClient().Get(context.Background(), testStrKey1).Result()
	fmt.Printf("JSON格式字符串 - GetCache: success=%t, value='%s', raw='%s'\n", result7, retrievedStr1, rawStr1)

	// 方式2: 直接存储纯字符串（模拟PHP存储方式）
	testStrKey2 := "test_string_raw"
	redisMiddleware.GetClient().Set(context.Background(), testStrKey2, "cr-power.com", 60*time.Second)

	var retrievedStr2 string
	result8 := redisMiddleware.GetCache(testStrKey2, &retrievedStr2)
	rawStr2, _ := redisMiddleware.GetClient().Get(context.Background(), testStrKey2).Result()
	fmt.Printf("纯字符串格式 - GetCache: success=%t, value='%s', raw='%s'\n", result8, retrievedStr2, rawStr2)
}
