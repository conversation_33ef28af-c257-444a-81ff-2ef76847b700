package handlers

import (
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
)

// 简化的Laravel任务处理函数

// DispatchLaravelCountClueAssetTotalJob 发送CountClueAssetTotalJob任务到Laravel队列
// 此任务没有参数
func DispatchLaravelCountClueAssetTotalJob() error {
	// 使用任务工厂发送任务
	err := asyncq.CountClueAssetTotalJob.Dispatch()
	if err != nil {
		log.Errorf("[DispatchLaravelCountClueAssetTotalJob] 发送任务失败: %v", err)
		return err
	}

	log.Infof("[DispatchLaravelCountClueAssetTotalJob] 成功发送任务")
	return nil
}

// DispatchLaravelCacheTableIpsConditionJob 触发Laravel的CacheTableIpsCondition
func DispatchLaravelCacheTableIpsConditionJob(userId uint64) error {
	// 使用任务工厂发送任务
	err := asyncq.CacheTableIpsConditionJob.Dispatch(userId)
	if err != nil {
		log.Errorf("[DispatchLaravelCacheTableIpsConditionJob] 发送任务失败: %v", err)
		return err
	}

	log.Infof("[DispatchLaravelCacheTableIpsConditionJob] 成功发送任务: userId=%d", userId)
	return nil
}

// DispatchLaravelCreateSensitiveKeywordJob 触发Laravel的CreateSensitiveKeywordJob
func DispatchLaravelCreateSensitiveKeywordJob(userId uint64, keyword string, isFullMatch bool, isIgnoreCase bool, detectTaskId string) error {
	// 使用任务工厂发送任务
	err := asyncq.CreateSensitiveKeywordJob.Dispatch(userId, keyword, isFullMatch, isIgnoreCase, detectTaskId)
	if err != nil {
		log.Errorf("[DispatchLaravelCreateSensitiveKeywordJob] 发送任务失败: %v", err)
		return err
	}

	log.Infof("[DispatchLaravelCreateSensitiveKeywordJob] 成功发送任务: userId=%d, keyword=%s", userId, keyword)
	return nil
}

// DispatchLaravelTableAssetsDomainsSyncJob 触发Laravel的TableAssetsDoaminsSync
func DispatchLaravelTableAssetsDomainsSyncJob(userId uint64, taskId interface{}, taskType string, domains interface{}, count uint64) error {
	// 使用任务工厂发送任务
	err := asyncq.TableAssetsDoaminsSyncJob.Dispatch(userId, taskId, taskType, domains, count)
	if err != nil {
		log.Errorf("[DispatchLaravelTableAssetsDomainsSyncJob] 发送任务失败: %v", err)
		return err
	}

	log.Infof("[DispatchLaravelTableAssetsDomainsSyncJob] 成功发送任务: userId=%d, taskType=%s, count=%d", userId, taskType, count)
	return nil
}

// DispatchScanForadarAssetJob 触发Laravel的ScanForadarAssetJob并指定队列
// 示例：类似PHP中的 dispatch(new ScanForadarAssetJob($task_id))->onQueue('bmh_asset_store')
func DispatchScanForadarAssetJob(taskId uint64) error {
	// 使用预定义的任务工厂，并通过OnQueue方法指定队列名称
	err := asyncq.ScanForadarAssetJob.OnQueue("bmh_asset_store").Dispatch(taskId)
	if err != nil {
		log.Errorf("[DispatchScanForadarAssetJob] 发送任务失败: %v", err)
		return err
	}

	log.Infof("[DispatchScanForadarAssetJob] 成功发送任务到队列 bmh_asset_store: taskId=%d", taskId)
	return nil
}
