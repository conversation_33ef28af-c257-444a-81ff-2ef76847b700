package handlers

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestParsePayloadForConfirmGeneralCluesJob(t *testing.T) {
	// 正常解析成功的测试用例
	t.Run("SuccessCase", func(t *testing.T) {
		payload := []byte(`{"user_id":123,"group_id":456}`)
		userId, groupId, err := parsePayloadForConfirmGeneralCluesJob(payload)
		assert.NoError(t, err)
		assert.Equal(t, 123, userId)
		assert.Equal(t, 456, groupId)
	})

	// JSON 格式错误的测试用例
	t.Run("InvalidJSON", func(t *testing.T) {
		payload := []byte(`{user_id:123,group_id:456}`)
		_, _, err := parsePayloadForConfirmGeneralCluesJob(payload)
		assert.Error(t, err)
	})

	// 缺少 user_id 的测试用例
	t.Run("MissingUserId", func(t *testing.T) {
		payload := []byte(`{"group_id":456}`)
		_, _, err := parsePayloadForConfirmGeneralCluesJob(payload)
		assert.EqualError(t, err, "解析user_id失败")
	})

	// 缺少 group_id 的测试用例
	t.Run("MissingGroupId", func(t *testing.T) {
		payload := []byte(`{"user_id":123}`)
		_, _, err := parsePayloadForConfirmGeneralCluesJob(payload)
		assert.EqualError(t, err, "解析group_id失败")
	})

	// user_id 类型不正确的测试用例（例如字符串）
	t.Run("UserIdTypeIncorrect", func(t *testing.T) {
		payload := []byte(`{"user_id":"abc","group_id":456}`)
		userMap := make(map[string]interface{})
		_ = json.Unmarshal(payload, &userMap)
		userMap["user_id"] = "abc"
		newPayload, _ := json.Marshal(userMap)
		userId, _, err := parsePayloadForConfirmGeneralCluesJob(newPayload)
		assert.NoError(t, err)
		assert.Equal(t, 0, userId) // 因为无法转换，SafeInt 返回 0
	})

	// group_id 类型不正确的测试用例（例如字符串）
	t.Run("GroupIdTypeIncorrect", func(t *testing.T) {
		payload := []byte(`{"user_id":123,"group_id":"def"}`)
		groupMap := make(map[string]interface{})
		_ = json.Unmarshal(payload, &groupMap)
		groupMap["group_id"] = "def"
		newPayload, _ := json.Marshal(groupMap)
		_, groupId, err := parsePayloadForConfirmGeneralCluesJob(newPayload)
		assert.NoError(t, err)
		assert.Equal(t, 0, groupId) // 因为无法转换，SafeInt 返回 0
	})

	// user_id 为 nil 的测试用例
	t.Run("UserIdNil", func(t *testing.T) {
		payload := []byte(`{"user_id":null,"group_id":456}`)
		userMap := make(map[string]interface{})
		_ = json.Unmarshal(payload, &userMap)
		userMap["user_id"] = nil
		newPayload, _ := json.Marshal(userMap)
		userId, _, err := parsePayloadForConfirmGeneralCluesJob(newPayload)
		assert.NoError(t, err)
		assert.Equal(t, 0, userId) // SafeInt 处理 nil 返回 0
	})

	// group_id 为 nil 的测试用例
	t.Run("GroupIdNil", func(t *testing.T) {
		payload := []byte(`{"user_id":123,"group_id":null}`)
		groupMap := make(map[string]interface{})
		_ = json.Unmarshal(payload, &groupMap)
		groupMap["group_id"] = nil
		newPayload, _ := json.Marshal(groupMap)
		_, groupId, err := parsePayloadForConfirmGeneralCluesJob(newPayload)
		assert.NoError(t, err)
		assert.Equal(t, 0, groupId) // SafeInt 处理 nil 返回 0
	})

	// user_id 为布尔类型的测试用例
	t.Run("UserIdBoolean", func(t *testing.T) {
		payload := []byte(`{"user_id":true,"group_id":456}`)
		userMap := make(map[string]interface{})
		_ = json.Unmarshal(payload, &userMap)
		userMap["user_id"] = true
		newPayload, _ := json.Marshal(userMap)
		userId, _, err := parsePayloadForConfirmGeneralCluesJob(newPayload)
		assert.NoError(t, err)
		assert.Equal(t, 1, userId) // SafeInt 处理 true 返回 1
	})

	// group_id 为布尔类型的测试用例
	t.Run("GroupIdBoolean", func(t *testing.T) {
		payload := []byte(`{"user_id":123,"group_id":false}`)
		groupMap := make(map[string]interface{})
		_ = json.Unmarshal(payload, &groupMap)
		groupMap["group_id"] = false
		newPayload, _ := json.Marshal(groupMap)
		_, groupId, err := parsePayloadForConfirmGeneralCluesJob(newPayload)
		assert.NoError(t, err)
		assert.Equal(t, 0, groupId) // SafeInt 处理 false 返回 0
	})

	// user_id 为浮点数的测试用例
	t.Run("UserIdFloat", func(t *testing.T) {
		payload := []byte(`{"user_id":123.45,"group_id":456}`)
		userMap := make(map[string]interface{})
		_ = json.Unmarshal(payload, &userMap)
		userMap["user_id"] = 123.45
		newPayload, _ := json.Marshal(userMap)
		userId, _, err := parsePayloadForConfirmGeneralCluesJob(newPayload)
		assert.NoError(t, err)
		assert.Equal(t, 123, userId) // SafeInt 处理浮点数返回整数部分
	})

	// group_id 为浮点数的测试用例
	t.Run("GroupIdFloat", func(t *testing.T) {
		payload := []byte(`{"user_id":123,"group_id":456.78}`)
		groupMap := make(map[string]interface{})
		_ = json.Unmarshal(payload, &groupMap)
		groupMap["group_id"] = 456.78
		newPayload, _ := json.Marshal(groupMap)
		_, groupId, err := parsePayloadForConfirmGeneralCluesJob(newPayload)
		assert.NoError(t, err)
		assert.Equal(t, 456, groupId) // SafeInt 处理浮点数返回整数部分
	})
}
