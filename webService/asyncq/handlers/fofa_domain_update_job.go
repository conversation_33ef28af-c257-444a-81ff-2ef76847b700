package handlers

import (
	"context"
	"encoding/json"
	"errors"
	core "micro-service/coreService/proto"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/domain_assets"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	asyncq "micro-service/pkg/queue_helper"
)

const PAGE_SIZE = 800

type FofaDomainUpdateJobPayload struct {
	Type   int `json:"type"`
	UserId int `json:"user_id"`
}

// FofaDomainUpdateJob 更新Fofa域名
// 消息体:
//
//	 {
//			"type": 1, // 1: DomainAssets, 2: IpAssets
//			"user_id": 1 // 用户ID，必填
//		}
func FofaDomainUpdateJob(ctx context.Context, t *asyncq.Task) error {
	if cfg.IsLocalClient() {
		log.Info("FofaDomainUpdateJob", "本地客户端，不执行", map[string]interface{}{
			"payload": string(t.Payload),
		})
		return nil
	}
	payloadInfo, err := parsePayloadForFofaDomainUpdate([]byte(t.Payload))
	if err != nil {
		return err
	}

	switch payloadInfo.Type {
	case 1:
		return processDomainAssets(ctx, payloadInfo.UserId)
	case 2:
		return processFofaeeAssets(ctx, payloadInfo.UserId)
	}

	return nil
}

// processDomainAssets 分页处理域名资产数据
func processDomainAssets(ctx context.Context, userId int) error {
	domainAssetsModel := domain_assets.NewModel()
	page := 1
	batchNumber := 1

	for {
		domainAssets, _, err := domainAssetsModel.List([]string{"domain"}, page, PAGE_SIZE, mysql.WithColumnValue("user_id", userId))
		if err != nil {
			log.Error("FofaDomainUpdateJob", "查询域名资产失败", map[string]interface{}{
				"user_id": userId,
				"page":    page,
				"error":   err.Error(),
			})
			return err
		}

		// 如果没有数据了，退出循环
		if len(domainAssets) == 0 {
			log.Info("FofaDomainUpdateJob", "域名资产数据处理完成", map[string]interface{}{
				"user_id":     userId,
				"total_batch": batchNumber - 1,
			})
			break
		}

		domains := make([]string, 0, len(domainAssets))
		for _, domainAsset := range domainAssets {
			domains = append(domains, domainAsset.Domain)
		}

		// 创建域名更新任务
		err = createDomainUpdateTask(ctx, domains, userId, batchNumber, "domain_assets")
		if err != nil {
			log.Error("FofaDomainUpdateJob", "创建域名更新任务失败", map[string]interface{}{
				"user_id": userId,
				"batch":   batchNumber,
				"count":   len(domains),
				"error":   err.Error(),
			})
			return err
		}

		// 如果返回的数据少于PAGE_SIZE，说明是最后一页
		if len(domainAssets) < PAGE_SIZE {
			log.Info("FofaDomainUpdateJob", "域名资产数据处理完成", map[string]interface{}{
				"user_id":     userId,
				"total_batch": batchNumber,
			})
			break
		}

		page++
		batchNumber++
	}

	return nil
}

// processFofaeeAssets 分页处理Fofa资产数据
func processFofaeeAssets(ctx context.Context, userId int) error {
	fofaeeAssetsModel := fofaee_assets.NewFofaeeAssetsModel()
	page := 1
	batchNumber := 1

	for {
		fofaeeAssets, _, err := fofaeeAssetsModel.FindByCondition(ctx, &fofaee_assets.FindCondition{
			UserId: uint64(userId),
			Status: []int{1, 4},
		}, page, PAGE_SIZE, "ip")
		if err != nil {
			log.Error("FofaDomainUpdateJob", "查询Fofa资产失败", map[string]interface{}{
				"user_id": userId,
				"page":    page,
				"error":   err.Error(),
			})
			return err
		}

		// 如果没有数据了，退出循环
		if len(fofaeeAssets) == 0 {
			log.Info("FofaDomainUpdateJob", "Fofa资产数据处理完成", map[string]interface{}{
				"user_id":     userId,
				"total_batch": batchNumber - 1,
			})
			break
		}

		domains := make([]string, 0, len(fofaeeAssets))
		for _, fofaeeAsset := range fofaeeAssets {
			domains = append(domains, fofaeeAsset.Ip)
		}

		// 创建域名更新任务
		err = createDomainUpdateTask(ctx, domains, userId, batchNumber, "fofaee_assets")
		if err != nil {
			log.Error("FofaDomainUpdateJob", "创建域名更新任务失败", map[string]interface{}{
				"user_id": userId,
				"batch":   batchNumber,
				"count":   len(domains),
				"error":   err.Error(),
			})
			return err
		}

		// 如果返回的数据少于PAGE_SIZE，说明是最后一页
		if len(fofaeeAssets) < PAGE_SIZE {
			log.Info("FofaDomainUpdateJob", "Fofa资产数据处理完成", map[string]interface{}{
				"user_id":     userId,
				"total_batch": batchNumber,
			})
			break
		}

		page++
		batchNumber++
	}

	return nil
}

// createDomainUpdateTask 创建域名更新任务
func createDomainUpdateTask(ctx context.Context, domains []string, userId, batchNumber int, dataSource string) error {
	rsp, err := core.GetProtoCoreClient().CreateDomainUpdateTask(ctx, &core.FofaDomainTaskRequest{
		Domains: domains,
	}, microx.SetTimeout(30, 29)...)
	if err != nil {
		return err
	}

	log.Info("FofaDomainUpdateJob", "创建域名更新任务成功", map[string]interface{}{
		"user_id":     userId,
		"batch":       batchNumber,
		"data_source": dataSource,
		"count":       len(domains),
		"rsp":         rsp,
	})

	return nil
}

// 解析payload
func parsePayloadForFofaDomainUpdate(payload []byte) (payloadInfo *FofaDomainUpdateJobPayload, err error) {
	err = json.Unmarshal(payload, &payloadInfo)
	if err != nil {
		log.Error("FofaDomainUpdateJob", "解析payload失败", map[string]interface{}{
			"payload": string(payload),
			"error":   err.Error(),
		})
		return nil, err
	}
	if payloadInfo.UserId == 0 {
		log.Error("FofaDomainUpdateJob", "用户ID不能为空", map[string]interface{}{
			"payload": string(payload),
		})
		return nil, errors.New("用户ID不能为空")
	}
	return payloadInfo, nil
}
