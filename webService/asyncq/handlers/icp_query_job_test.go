package handlers

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asynq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"

	"github.com/stretchr/testify/assert"
)

// 初始化测试环境
func init() {
	cfg.InitLoadCfg()
	log.Init()
}

func TestICPQueryJobPayload(t *testing.T) {
	t.Run("json marshal and unmarshal", func(t *testing.T) {
		payload := asynq.ICPQueryJobPayload{
			ClueID:      12345,
			QueryType:   "domain",
			QueryValue:  "example.com",
			UserId:      1,
			GroupId:     2,
			IsSubdomain: false,
		}

		// 测试序列化
		data, err := json.Marshal(payload)
		assert.NoError(t, err)
		assert.NotEmpty(t, data)

		// 测试反序列化
		var decoded asynq.ICPQueryJobPayload
		err = json.Unmarshal(data, &decoded)
		assert.NoError(t, err)
		assert.Equal(t, payload.ClueID, decoded.ClueID)
		assert.Equal(t, payload.QueryType, decoded.QueryType)
		assert.Equal(t, payload.QueryValue, decoded.QueryValue)
		assert.Equal(t, payload.UserId, decoded.UserId)
		assert.Equal(t, payload.GroupId, decoded.GroupId)
		assert.Equal(t, payload.IsSubdomain, decoded.IsSubdomain)
	})

	t.Run("empty payload", func(t *testing.T) {
		payload := asynq.ICPQueryJobPayload{}
		data, err := json.Marshal(payload)
		assert.NoError(t, err)

		var decoded asynq.ICPQueryJobPayload
		err = json.Unmarshal(data, &decoded)
		assert.NoError(t, err)
		assert.Equal(t, uint64(0), decoded.ClueID)
		assert.Equal(t, "", decoded.QueryType)
		assert.Equal(t, "", decoded.QueryValue)
		assert.Equal(t, uint64(0), decoded.UserId)
		assert.Equal(t, uint64(0), decoded.GroupId)
		assert.Equal(t, false, decoded.IsSubdomain)
	})

	t.Run("subdomain payload", func(t *testing.T) {
		payload := asynq.ICPQueryJobPayload{
			ClueID:      67890,
			QueryType:   "subdomain",
			QueryValue:  "sub.example.com",
			UserId:      3,
			GroupId:     4,
			IsSubdomain: true,
		}

		data, err := json.Marshal(payload)
		assert.NoError(t, err)

		var decoded asynq.ICPQueryJobPayload
		err = json.Unmarshal(data, &decoded)
		assert.NoError(t, err)
		assert.Equal(t, payload.ClueID, decoded.ClueID)
		assert.Equal(t, "subdomain", decoded.QueryType)
		assert.Equal(t, "sub.example.com", decoded.QueryValue)
		assert.Equal(t, true, decoded.IsSubdomain)
	})
}

func TestICPQueryJob_InvalidPayload(t *testing.T) {
	t.Run("invalid json", func(t *testing.T) {
		ctx := context.Background()
		invalidJSON := []byte(`{"invalid": json}`)

		err := ICPQueryJob(ctx, invalidJSON)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid character")
	})

	t.Run("empty payload", func(t *testing.T) {
		ctx := context.Background()
		emptyJSON := []byte(`{}`)

		// 空payload应该能正常解析，但会因为查询类型不支持而返回错误
		err := ICPQueryJob(ctx, emptyJSON)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "不支持的查询类型")
	})

	t.Run("unsupported query type", func(t *testing.T) {
		ctx := context.Background()
		payload := asynq.ICPQueryJobPayload{
			ClueID:     1,
			QueryType:  "unsupported",
			QueryValue: "test",
			UserId:     1,
		}

		data, err := json.Marshal(payload)
		assert.NoError(t, err)

		err = ICPQueryJob(ctx, data)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "不支持的查询类型: unsupported")
	})
}

func TestICPQueryJob_ContextCancellation(t *testing.T) {
	t.Run("context timeout", func(t *testing.T) {
		// 创建一个很短的超时上下文
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		payload := asynq.ICPQueryJobPayload{
			ClueID:     1,
			QueryType:  "domain",
			QueryValue: "example.com",
			UserId:     1,
		}

		data, err := json.Marshal(payload)
		assert.NoError(t, err)

		// 等待上下文超时
		time.Sleep(2 * time.Millisecond)

		// 即使上下文超时，函数也应该正常返回（因为ICP查询失败不返回错误）
		err = ICPQueryJob(ctx, data)
		assert.NoError(t, err) // ICP查询失败不返回错误
	})

	t.Run("context cancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())

		payload := asynq.ICPQueryJobPayload{
			ClueID:     1,
			QueryType:  "domain",
			QueryValue: "example.com",
			UserId:     1,
		}

		data, err := json.Marshal(payload)
		assert.NoError(t, err)

		// 立即取消上下文
		cancel()

		// 即使上下文被取消，函数也应该正常返回
		err = ICPQueryJob(ctx, data)
		assert.NoError(t, err) // ICP查询失败不返回错误
	})
}

func TestUtilityFunctions(t *testing.T) {
	t.Run("GetTopDomain function", func(t *testing.T) {
		// 测试utils.GetTopDomain函数的行为
		testCases := []struct {
			input    string
			expected string
		}{
			{"sub.example.com", "example.com"},
			{"www.test.org", "test.org"},
			{"example.com", "example.com"},
			{"", ""},
			{"localhost", "localhost"},
		}

		for _, tc := range testCases {
			result := utils.GetTopDomain(tc.input)
			assert.Equal(t, tc.expected, result, "GetTopDomain(%s) should return %s", tc.input, tc.expected)
		}
	})
}

func TestICPQueryJob_ErrorHandling(t *testing.T) {
	t.Run("malformed json", func(t *testing.T) {
		ctx := context.Background()
		malformedJSON := []byte(`{"clue_id": "not_a_number"}`)

		err := ICPQueryJob(ctx, malformedJSON)
		assert.Error(t, err)
	})

	t.Run("missing required fields", func(t *testing.T) {
		ctx := context.Background()
		incompletePayload := map[string]interface{}{
			"clue_id": 1,
			// 缺少query_type
		}

		data, err := json.Marshal(incompletePayload)
		assert.NoError(t, err)

		err = ICPQueryJob(ctx, data)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "不支持的查询类型")
	})

	t.Run("nil payload", func(t *testing.T) {
		ctx := context.Background()

		err := ICPQueryJob(ctx, nil)
		assert.Error(t, err)
	})

	t.Run("empty byte slice", func(t *testing.T) {
		ctx := context.Background()

		err := ICPQueryJob(ctx, []byte{})
		assert.Error(t, err)
	})
}
