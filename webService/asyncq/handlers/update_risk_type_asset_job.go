package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/engine_rules"
	"micro-service/middleware/mysql/risks"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"

	es_utils "micro-service/middleware/elastic"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
)

// 常量定义
const (
	RISK_YES = 1 // 风险检查完成状态
	RISK_NO  = 2 // 风险检查未完成状态
)

// UpdateRiskTypeAssetJob 更新风险类型资产任务
// 消息体:
//
//	{
//	  "user_id": 1,        // 用户ID，必填
//	  "task_id": 1,        // 任务ID，必填，可以是单个数字或数组
//	  "company_id": 1,     // 公司ID，必填
//	  "detect_task_id": 1  // 测绘任务ID，可选
//	}
func UpdateRiskTypeAssetJob(ctx context.Context, t *asyncq.Task) error {
	payloadInfo, err := parsePayloadForUpdateRiskTypeAsset([]byte(t.Payload))
	if err != nil {
		return err
	}

	log.Info("UpdateRiskTypeAssetJob", "更新风险类型资产开始", map[string]interface{}{
		"user_id":        payloadInfo.UserId,
		"task_id":        payloadInfo.TaskId,
		"company_id":     payloadInfo.CompanyId,
		"detect_task_id": payloadInfo.DetectTaskId,
	})

	// 对应PHP的forSleep(10)
	time.Sleep(10 * time.Second)

	// 转换TaskId为[]uint64格式
	taskIds, err := normalizeTaskIds(payloadInfo.TaskId)
	if err != nil {
		return fmt.Errorf("任务ID格式错误: %v", err)
	}

	// 查询包含风险类型的资产
	assets, err := queryAssetsWithRiskType(ctx, payloadInfo.UserId, taskIds)
	if err != nil {
		log.Errorf("UpdateRiskTypeAssetJob 查询资产失败: %v", err)
		return err
	}

	// 如果没有风险资产，记录日志并处理测绘任务完成状态
	if len(assets) == 0 {
		log.Info("UpdateRiskTypeAssetJob", "当前用户没有风险事件，结束了,计算风险事件资产结束",
			map[string]interface{}{
				"user_id": payloadInfo.UserId,
				"task_id": payloadInfo.TaskId,
			})

		// 检查测绘任务完成状态
		if payloadInfo.DetectTaskId != nil {
			err := checkIsFinishedDetectTask(ctx, *payloadInfo.DetectTaskId, []uint64{}, payloadInfo.UserId, taskIds)
			if err != nil {
				log.Errorf("UpdateRiskTypeAssetJob 检查测绘任务状态失败: %v", err)
			}
		}
		return nil
	}

	// 获取所有风险类型用于测绘任务检查
	allRiskTypes := extractAllRiskTypes(assets)

	// 处理风险类型统计和更新
	err = processRiskTypeAssets(ctx, payloadInfo, allRiskTypes)
	if err != nil {
		log.Errorf("UpdateRiskTypeAssetJob 处理风险类型资产失败: %v", err)
		return err
	}

	// 清理无匹配资产的风险事件
	err = cleanupUnmatchedRiskEvents(ctx, payloadInfo.UserId)
	if err != nil {
		log.Errorf("UpdateRiskTypeAssetJob 清理无匹配风险事件失败: %v", err)
		// 不返回错误，继续执行
	}

	// 检查测绘任务完成状态
	if payloadInfo.DetectTaskId != nil {
		err := checkIsFinishedDetectTask(ctx, *payloadInfo.DetectTaskId, allRiskTypes, payloadInfo.UserId, taskIds)
		if err != nil {
			log.Errorf("UpdateRiskTypeAssetJob 检查测绘任务状态失败: %v", err)
		}
	}

	log.Info("UpdateRiskTypeAssetJob", "正常匹配计算风险事件资产结束",
		map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
		})

	return nil
}

// parsePayloadForUpdateRiskTypeAsset 解析任务参数
func parsePayloadForUpdateRiskTypeAsset(payload []byte) (*asyncq.UpdateRiskTypeAssetJobPayload, error) {
	var payloadInfo asyncq.UpdateRiskTypeAssetJobPayload
	if err := json.Unmarshal(payload, &payloadInfo); err != nil {
		return nil, fmt.Errorf("解析任务参数失败: %v", err)
	}

	// 参数验证
	if payloadInfo.UserId == 0 {
		return nil, fmt.Errorf("用户ID不能为空")
	}
	if payloadInfo.TaskId == nil {
		return nil, fmt.Errorf("任务ID不能为空")
	}
	if payloadInfo.CompanyId == 0 {
		return nil, fmt.Errorf("公司ID不能为空")
	}

	return &payloadInfo, nil
}

// normalizeTaskIds 将TaskId标准化为[]uint64格式
func normalizeTaskIds(taskId interface{}) ([]uint64, error) {
	if taskId == nil {
		return nil, fmt.Errorf("任务ID不能为空")
	}

	switch v := taskId.(type) {
	case float64:
		return []uint64{uint64(v)}, nil
	case int:
		return []uint64{uint64(v)}, nil
	case uint64:
		return []uint64{v}, nil
	case []interface{}:
		var taskIds []uint64
		for _, id := range v {
			taskIds = append(taskIds, cast.ToUint64(id))
		}
		return taskIds, nil
	case []uint64:
		return v, nil
	default:
		return nil, fmt.Errorf("不支持的任务ID类型: %T", taskId)
	}
}

// queryAssetsWithRiskType 查询包含风险类型的资产
func queryAssetsWithRiskType(ctx context.Context, userId uint64, taskIds []uint64) ([]*fofaee_assets.FofaeeAssets, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermsQuery("status", 1, 4)) // STATUS_CLAIMED, STATUS_UPLOAD
	query.Must(elastic.NewExistsQuery("risk_type"))

	// 如果有任务ID，添加任务ID筛选
	if len(taskIds) > 0 {
		if len(taskIds) == 1 {
			query.Must(elastic.NewTermQuery("task_id", taskIds[0]))
		} else {
			// 转换为interface{}切片用于TermsQuery
			taskIdInterfaces := make([]interface{}, len(taskIds))
			for i, id := range taskIds {
				taskIdInterfaces[i] = id
			}
			query.Must(elastic.NewTermsQuery("task_id", taskIdInterfaces...))
		}
	}

	// 只查询需要的字段
	assets, err := es_utils.All[fofaee_assets.FofaeeAssets](500, query, nil, "ip", "id", "risk_type")
	if err != nil {
		return nil, fmt.Errorf("查询包含风险类型的资产失败: %v", err)
	}

	return assets, nil
}

// processRiskTypeAssets 处理风险类型资产的统计和更新
func processRiskTypeAssets(ctx context.Context, payload *asyncq.UpdateRiskTypeAssetJobPayload, allRiskTypes []uint64) error {
	now := time.Now()

	// 提取并统计所有风险类型

	if len(allRiskTypes) == 0 {
		return nil
	}

	// 更新规则关系
	relationModel := engine_rules.NewRuleRelation()
	for _, ruleId := range allRiskTypes {
		err := updateOrCreateRuleRelation(ctx, relationModel, ruleId, payload.UserId, payload.CompanyId, now)
		if err != nil {
			log.Errorf("UpdateRiskTypeAssetJob 更新规则关系失败 ruleId=%d: %v", ruleId, err)
			continue
		}
	}

	// 记录风险事件 - 对应PHP的if($this->task_id)逻辑
	if payload.TaskId != nil {
		var existRelationIp []interface{}
		cacheKey := cache.GetCacheKey("scan_risk_ip", fmt.Sprintf("%v", payload.TaskId))
		err := redis.Get(ctx, cacheKey, &existRelationIp)

		// 对应PHP的if(is_array($realated_ip))判断
		if err == nil && len(existRelationIp) > 0 {
			riskIpNum := len(existRelationIp)

			// 获取风险数量 - 对应PHP的Cache::get('scan_risk_type_num:'.$this->task_id)
			var redisRiskNum interface{}
			riskNumCacheKey := cache.GetCacheKey("scan_risk_type_num", fmt.Sprintf("%v", payload.TaskId))
			redis.Get(ctx, riskNumCacheKey, &redisRiskNum)

			contentData := map[string]interface{}{
				"content":    fmt.Sprintf("发现%v例安全告警事件，涉及%d个IP资产", redisRiskNum, riskIpNum),
				"realted_ip": existRelationIp,
			}
			content, err := json.Marshal(contentData)
			if err != nil {
				log.Errorf("UpdateRiskTypeAssetJob 记录风险事件失败: %v", err)
				return err
			}

			// 对应PHP的recordRiskEvent函数调用
			riskEventModel := risks.NewModel()
			err = riskEventModel.Create(&risks.Risks{
				UserID:    int64(payload.UserId),
				CompanyID: int64(payload.CompanyId),
				Type:      risks.TYPE_ASSET_RISK,
				Content:   string(content),
			})
			if err != nil {
				log.Errorf("UpdateRiskTypeAssetJob 记录风险事件失败: %v", err)
				return err
			}

			log.Info("UpdateRiskTypeAssetJob", "记录风险事件成功", map[string]interface{}{
				"user_id":        payload.UserId,
				"task_id":        payload.TaskId,
				"company_id":     payload.CompanyId,
				"risk_ip_num":    riskIpNum,
				"redis_risk_num": redisRiskNum,
				"content":        string(content),
			})
		}
	}

	return nil
}

// updateOrCreateRuleRelation 更新或创建规则关系
func updateOrCreateRuleRelation(ctx context.Context, model engine_rules.RuleRelationModel, ruleId, userId, companyId uint64, now time.Time) error {
	// 检查是否已存在
	existing, err := model.First(ruleId, userId)
	if err != nil && err.Error() != "record not found" {
		return fmt.Errorf("查询规则关系失败: %v", err)
	}

	if existing.Id > 0 {
		// 更新现有记录
		existing.Status = 0 // STATUS_DEFAULT
		existing.LastestFoundTime = &now
		existing.Node = cfg.LoadCommon().Node

		return model.Update(existing)
	} else {
		// 创建新记录
		newRelation := engine_rules.RuleRelation{
			RuleId:           ruleId,
			UserId:           userId,
			Status:           0, // STATUS_DEFAULT
			CompanyId:        companyId,
			LastestFoundTime: &now,
			Node:             cfg.LoadCommon().Node,
			Enable:           1,
		}

		return model.Create(newRelation)
	}
}

// cleanupUnmatchedRiskEvents 清理无匹配资产的风险事件
func cleanupUnmatchedRiskEvents(ctx context.Context, userId uint64) error {
	now := time.Now()
	relationModel := engine_rules.NewRuleRelation()

	// 查询所有非删除状态且最近发现时间不是当前时间的记录
	outdatedRelations, err := relationModel.List(
		engine_rules.WithUser(userId),
		mysql.WithWhere("status != ?", 3), // STATUS_DELETE 状态值
		mysql.WithWhere("lastest_found_time != ?", now.Format("2006-01-02 15:04:05")),
	)
	if err != nil {
		return fmt.Errorf("查询过期规则关系失败: %v", err)
	}

	// 检查每个规则是否还有匹配的资产
	for _, relation := range outdatedRelations {
		hasAssets, err := checkRuleHasAssets(ctx, relation.RuleId, userId)
		if err != nil {
			log.Errorf("检查规则资产失败 ruleId=%d: %v", relation.RuleId, err)
			continue
		}

		// 修复逻辑错误：如果没有匹配的资产，才标记为删除 - 对应PHP的if(empty($info))
		if !hasAssets {
			// 标记为删除
			relation.Status = 3 // STATUS_DELETE
			relation.Node = cfg.LoadCommon().Node

			err := relationModel.Update(relation)
			if err != nil {
				log.Errorf("删除无匹配资产的规则关系失败 id=%d: %v", relation.Id, err)
			} else {
				log.Info("UpdateRiskTypeAssetJob", "删除了一个事件，因为没匹配到资产",
					map[string]interface{}{
						"user_id": userId,
						"事件id":    relation.Id,
					})
			}
		}
	}

	return nil
}

// checkRuleHasAssets 检查规则是否还有匹配的资产
func checkRuleHasAssets(ctx context.Context, ruleId, userId uint64) (bool, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermQuery("risk_type", ruleId))

	count, err := es_utils.CountByParams[fofaee_assets.FofaeeAssets]([][]interface{}{
		{"user_id", userId},
		{"risk_type", ruleId},
	})
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// extractAllRiskTypes 提取所有风险类型
func extractAllRiskTypes(assets []*fofaee_assets.FofaeeAssets) []uint64 {
	riskTypeSet := make(map[uint64]bool)
	var allRiskTypes []uint64

	for _, asset := range assets {
		if asset.RiskType != nil {
			for _, riskType := range asset.RiskType {
				riskTypeId := cast.ToUint64(riskType)
				if riskTypeId > 0 && !riskTypeSet[riskTypeId] {
					riskTypeSet[riskTypeId] = true
					allRiskTypes = append(allRiskTypes, riskTypeId)
				}
			}
		}
	}

	return allRiskTypes
}

// checkIsFinishedDetectTask 检查测绘任务是否完成
func checkIsFinishedDetectTask(ctx context.Context, detectTaskId uint64, riskTypes []uint64, userId uint64, taskIds []uint64) error {
	if detectTaskId == 0 {
		return nil
	}

	// 1. 将命中的风险事件类型写到缓存
	err := cacheRiskTypes(ctx, detectTaskId, riskTypes)
	if err != nil {
		log.Errorf("缓存风险类型失败: %v", err)
		return err
	}

	// 2. 查询关联的扫描任务
	scanTaskModel := scan_task.NewScanTasksModel()
	relatedTasks, err := scanTaskModel.FindIds(
		mysql.WithWhere("detect_assets_tasks_id = ?", detectTaskId),
	)
	if err != nil {
		log.Errorf("查询关联扫描任务失败: %v", err)
		return err
	}

	if len(relatedTasks) == 0 || len(relatedTasks) == 1 {
		// 没有关联任务或者只有一个任务，直接更新状态
		return updateDetectTaskFinishStatus(ctx, detectTaskId)
	}

	// 多个任务的情况，需要检查其他任务是否完成
	return checkMultipleTasksCompletion(ctx, detectTaskId, relatedTasks, taskIds, userId)
}

// cacheRiskTypes 缓存风险类型到Redis
func cacheRiskTypes(ctx context.Context, detectTaskId uint64, riskTypes []uint64) error {
	cacheKey := cache.GetCacheKey("detect_risk_type", fmt.Sprintf("%d", detectTaskId))

	// 获取现有的风险类型
	var existingRiskTypes []uint64
	err := redis.Get(ctx, cacheKey, &existingRiskTypes)
	if err != nil && err != redis.Nil {
		log.Errorf("获取现有风险类型缓存失败: %v", err)
	}

	// 合并风险类型（去重）
	riskTypeSet := make(map[uint64]bool)
	for _, rt := range existingRiskTypes {
		riskTypeSet[rt] = true
	}
	for _, rt := range riskTypes {
		riskTypeSet[rt] = true
	}

	// 转换为切片
	var mergedRiskTypes []uint64
	for rt := range riskTypeSet {
		mergedRiskTypes = append(mergedRiskTypes, rt)
	}

	// 写入缓存，不设置过期时间
	return redis.Set(ctx, cacheKey, mergedRiskTypes, 0)
}

// updateDetectTaskFinishStatus 更新测绘任务完成状态
func updateDetectTaskFinishStatus(ctx context.Context, detectTaskId uint64) error {
	detectTaskModel := detect_assets_tasks.NewModel()
	updateData := map[string]interface{}{
		"is_finish_check_risk": RISK_YES,
	}

	return detectTaskModel.UpdateAny(updateData,
		mysql.WithWhere("id = ?", detectTaskId),
	)
}

// checkMultipleTasksCompletion 检查多个任务的完成状态
func checkMultipleTasksCompletion(ctx context.Context, detectTaskId uint64, relatedTasks []uint64, currentTaskIds []uint64, userId uint64) error {
	relatedTasksMap := make(map[uint64]struct{})
	for _, taskId := range relatedTasks {
		relatedTasksMap[taskId] = struct{}{}
	}
	currentTaskIdsMap := make(map[uint64]struct{})
	for _, taskId := range currentTaskIds {
		currentTaskIdsMap[taskId] = struct{}{}
	}

	// 遍历所有相关任务，检查是否都完成了风险检查
	for taskId := range relatedTasksMap {
		// 跳过当前任务
		if _, ok := currentTaskIdsMap[taskId]; ok {
			continue
		}

		// 检查其他任务是否完成
		cacheKey := cache.GetCacheKey("is_finish_check_risk", fmt.Sprintf("%d", taskId))
		var isFinished bool
		err := redis.Get(ctx, cacheKey, &isFinished)
		if err == redis.Nil || !isFinished {
			// 有任务还没完成，设置当前任务为完成状态并返回
			for _, currentId := range currentTaskIds {
				currentCacheKey := cache.GetCacheKey("is_finish_check_risk", fmt.Sprintf("%d", currentId))
				redis.Set(ctx, currentCacheKey, true, 0)
			}

			log.Info("UpdateRiskTypeAssetJob", "checkIsFinishDetectTask-当前的的扫描任务没有全部完成风险事件告警识别",
				map[string]interface{}{
					"user_id":        userId,
					"task_id":        currentTaskIds,
					"detect_task_id": detectTaskId,
				})
			return nil
		}
	}

	// 所有任务都完成了，更新状态
	for _, currentId := range currentTaskIds {
		currentCacheKey := cache.GetCacheKey("is_finish_check_risk", fmt.Sprintf("%d", currentId))
		redis.Set(ctx, currentCacheKey, true, 0)
	}

	return updateDetectTaskFinishStatus(ctx, detectTaskId)
}
