package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"micro-service/pkg/github"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"strings"
)

// OneForAllJob 执行oneforall任务
// 消息体:
//
//	{
//		"domain": "example.com", // 域名，必填
//		"title": "example", // 标题，可选
//		"user_id": 1 // 用户ID，可选
//	}
func OneForAllJob(ctx context.Context, task *asyncq.Task) error {
	payload, err := parseOneForAllPayload(task)
	if err != nil {
		log.Error("OneForAllJob", "解析任务参数失败", map[string]interface{}{
			"payload": string(task.Payload),
		})
		return err
	}

	log.Info("OneForAllJob", "开始执行任务", map[string]interface{}{
		"domain":  payload.Domain,
		"title":   payload.Title,
		"user_id": payload.UserId,
	})
	// 异步执行,结果会写入缓存，不需要等待结果
	go github.OneForAllQuery(ctx, payload.Domain, payload.Title, false)

	return nil
}

func parseOneForAllPayload(data *asyncq.Task) (*asyncq.OneForAllJobPayload, error) {
	var payload asyncq.OneForAllJobPayload
	if err := json.Unmarshal([]byte(data.Payload), &payload); err != nil {
		return nil, err
	}
	// 检查域名是否为空或只包含空白字符
	if payload.Domain == "" || len(strings.TrimSpace(payload.Domain)) == 0 {
		return nil, errors.New("[OneForAllJob] domain解析为空")
	}
	return &payload, nil
}
