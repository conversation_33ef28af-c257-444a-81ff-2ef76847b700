package handlers

import (
	"errors"
	asynq "micro-service/pkg/queue_helper"
	"strings"
	"testing"
)

func TestParseRecommendAssetJobPayload(t *testing.T) {
	tests := []struct {
		name        string
		input       []byte
		expected    *asynq.RecommendAssetJobPayload
		expectedErr error
	}{
		//{
		//	name:  "有效载荷",
		//	input: []byte(`{"recommendRecordId":"rec123","userId":1001,"detectTaskId":2001}`),
		//	expected: &RecommendAssetJobPayload{
		//		RecommendRecordId: "rec123",
		//		UserId:            1001,
		//		DetectTaskId:      2001,
		//	},
		//	expectedErr: nil,
		//},
		{
			name:        "JSON解析失败",
			input:       []byte(`{invalid json}`),
			expected:    nil,
			expectedErr: errors.New("invalid character 'i' looking for beginning of object key string"),
		},
		{
			name:        "缺少RecommendRecordId",
			input:       []byte(`{"userId":1001,"detectTaskId":2001}`),
			expected:    nil,
			expectedErr: errors.New("推荐资产记录ID不能为空"),
		},
		{
			name:        "空RecommendRecordId",
			input:       []byte(`{"recommendRecordId":"","userId":1001,"detectTaskId":2001}`),
			expected:    nil,
			expectedErr: errors.New("推荐资产记录ID不能为空"),
		},
		//{
		//	name:        "缺少UserId",
		//	input:       []byte(`{"recommendRecordId":"rec123","detectTaskId":2001}`),
		//	expected:    nil,
		//	expectedErr: errors.New("用户ID不能为空"),
		//},
		//{
		//	name:        "UserId为0",
		//	input:       []byte(`{"recommendRecordId":"rec123","userId":0,"detectTaskId":2001}`),
		//	expected:    nil,
		//	expectedErr: errors.New("用户ID不能为空"),
		//},
		//{
		//	name:        "缺少DetectTaskId",
		//	input:       []byte(`{"recommendRecordId":"rec123","userId":1001}`),
		//	expected:    nil,
		//	expectedErr: errors.New("测绘任务ID不能为空"),
		//},
		//{
		//	name:        "DetectTaskId为0",
		//	input:       []byte(`{"recommendRecordId":"rec123","userId":1001,"detectTaskId":0}`),
		//	expected:    nil,
		//	expectedErr: errors.New("测绘任务ID不能为空"),
		//},
		{
			name:        "所有字段都缺失",
			input:       []byte(`{}`),
			expected:    nil,
			expectedErr: errors.New("推荐资产记录ID不能为空"),
		},
		//{
		//	name:  "额外字段",
		//	input: []byte(`{"recommendRecordId":"rec123","userId":1001,"detectTaskId":2001,"extraField":"value"}`),
		//	expected: &RecommendAssetJobPayload{
		//		RecommendRecordId: "rec123",
		//		UserId:            1001,
		//		DetectTaskId:      2001,
		//	},
		//	expectedErr: nil,
		//},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parseRecommendAssetJobPayload(tt.input)

			// 验证错误 - 使用包含匹配而不是完全相等
			if tt.expectedErr != nil {
				if err == nil {
					t.Fatalf("预期错误: %v, 但未返回错误", tt.expectedErr)
				}
				// 检查错误消息是否包含预期字符串
				if !strings.Contains(err.Error(), tt.expectedErr.Error()) {
					t.Fatalf("预期错误包含: %v, 实际错误: %v", tt.expectedErr, err)
				}
			} else {
				if err != nil {
					t.Fatalf("预期无错误, 实际错误: %v", err)
				}
			}

			// 验证结果
			if tt.expected == nil {
				if result != nil {
					t.Fatalf("预期结果为 nil, 实际为 %+v", result)
				}
			} else {
				if result == nil {
					t.Fatal("预期结果不为 nil, 实际为 nil")
				}
				// 更健壮的比较
				if tt.expected.RecommendRecordId != "" && result.RecommendRecordId != tt.expected.RecommendRecordId {
					t.Errorf("RecommendRecordId 不匹配: 预期 %s, 实际 %s",
						tt.expected.RecommendRecordId, result.RecommendRecordId)
				}
				if tt.expected.UserId != 0 && result.UserId != tt.expected.UserId {
					t.Errorf("UserId 不匹配: 预期 %d, 实际 %d",
						tt.expected.UserId, result.UserId)
				}
				if tt.expected.DetectTaskId != 0 && result.DetectTaskId != tt.expected.DetectTaskId {
					t.Errorf("DetectTaskId 不匹配: 预期 %d, 实际 %d",
						tt.expected.DetectTaskId, result.DetectTaskId)
				}
			}
		})
	}
}
