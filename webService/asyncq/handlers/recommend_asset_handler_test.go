package handlers

import (
	"context"
	"fmt"
	pb "micro-service/coreService/proto"
	testcommon "micro-service/initialize/common_test"
	es "micro-service/initialize/es"
	"micro-service/initialize/mysql"
	mysqlInit "micro-service/initialize/mysql"
	redisInit "micro-service/initialize/redis"
	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dbx"
	"micro-service/pkg/icp"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// createTestHandler 创建测试用的RecommendAssetHandler实例
func createTestHandler() *RecommendAssetHandler {
	return &RecommendAssetHandler{
		config: &RecommendAssetTaskConfig{
			TaskName:  "test",
			TaskId:    "test-task-id",
			UserId:    1,
			CompanyId: 100,
			ClueIds:   []int{1, 2, 3},
		},
		state: &RecommendAssetTaskState{
			recommendRecord: &recommend_record.RecommendRecord{
				GroupId:   123,
				GroupName: "测试组",
			},
			recommendData: SafeRecommendData{
				data:  sync.Map{},
				ipSet: make(map[string]struct{}),
			},
			currentNumber: 0,
		},
	}
}

// TestFullUrl 测试fullUrl方法
func TestFullUrl(t *testing.T) {
	handler := createTestHandler()

	tests := []struct {
		name     string
		host     string
		ip       string
		protocol string
		port     int
		expected string
	}{
		{
			name:     "HTTP默认端口",
			host:     "example.com",
			ip:       "***********",
			protocol: "http",
			port:     80,
			expected: "http://example.com",
		},
		{
			name:     "HTTPS默认端口",
			host:     "secure.example.com",
			ip:       "***********",
			protocol: "https",
			port:     443,
			expected: "https://secure.example.com",
		},
		{
			name:     "自定义端口",
			host:     "custom.example.com",
			ip:       "***********",
			protocol: "http",
			port:     8080,
			expected: "http://custom.example.com",
		},
		{
			name:     "空主机使用IP",
			host:     "",
			ip:       "***********",
			protocol: "http",
			port:     80,
			expected: "http://***********",
		},
		{
			name:     "已包含协议的主机",
			host:     "https://existing.example.com",
			ip:       "***********",
			protocol: "https",
			port:     443,
			expected: "https://existing.example.com",
		},
		{
			name:     "协议纠正_HTTP到HTTPS",
			host:     "http://wrong.example.com",
			ip:       "***********",
			protocol: "https",
			port:     443,
			expected: "https://wrong.example.com",
		},
		{
			name:     "协议纠正_HTTPS到HTTP",
			host:     "https://wrong.example.com",
			ip:       "***********",
			protocol: "http",
			port:     80,
			expected: "http://wrong.example.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.fullUrl(tt.host, tt.ip, tt.protocol, tt.port)
			assert.Equal(t, tt.expected, result, "URL构建应正确")
		})
	}
}

// TestFullUrlEdgeCases 测试fullUrl方法的边界情况
func TestFullUrlEdgeCases(t *testing.T) {
	handler := createTestHandler()

	tests := []struct {
		name     string
		host     string
		ip       string
		protocol string
		port     int
		expected string
	}{
		{
			name:     "全空参数",
			host:     "",
			ip:       "",
			protocol: "",
			port:     0,
			expected: "",
		},
		{
			name:     "空协议",
			host:     "example.com",
			ip:       "***********",
			protocol: "",
			port:     80,
			expected: "example.com",
		},
		{
			name:     "负端口",
			host:     "example.com",
			ip:       "***********",
			protocol: "http",
			port:     -1,
			expected: "http://example.com",
		},
		{
			name:     "超大端口",
			host:     "example.com",
			ip:       "***********",
			protocol: "http",
			port:     65535,
			expected: "http://example.com",
		},
		{
			name:     "特殊字符域名",
			host:     "test-example.com",
			ip:       "***********",
			protocol: "https",
			port:     443,
			expected: "https://test-example.com",
		},
		{
			name:     "大写协议",
			host:     "example.com",
			ip:       "***********",
			protocol: "HTTP",
			port:     80,
			expected: "example.com",
		},
		{
			name:     "混合大小写协议",
			host:     "example.com",
			ip:       "***********",
			protocol: "Http",
			port:     80,
			expected: "example.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.fullUrl(tt.host, tt.ip, tt.protocol, tt.port)
			assert.Equal(t, tt.expected, result, "主机: %s, IP: %s, 协议: %s, 端口: %d", tt.host, tt.ip, tt.protocol, tt.port)
		})
	}
}

// TestFullUrlProtocolCorrection 专门测试协议修正逻辑
func TestFullUrlProtocolCorrection(t *testing.T) {
	handler := createTestHandler()

	tests := []struct {
		name     string
		host     string
		ip       string
		protocol string
		port     int
		expected string
	}{
		{
			name:     "HTTP转HTTPS-基础",
			host:     "http://api.example.com",
			ip:       "***********",
			protocol: "https",
			port:     443,
			expected: "https://api.example.com",
		},
		{
			name:     "HTTPS转HTTP-基础",
			host:     "https://api.example.com",
			ip:       "***********",
			protocol: "http",
			port:     80,
			expected: "http://api.example.com",
		},
		{
			name:     "HTTP转HTTPS-带端口",
			host:     "http://api.example.com:8080",
			ip:       "***********",
			protocol: "https",
			port:     9443,
			expected: "https://api.example.com:8080",
		},
		{
			name:     "HTTPS转HTTP-带端口",
			host:     "https://api.example.com:9443",
			ip:       "***********",
			protocol: "http",
			port:     8080,
			expected: "http://api.example.com:9443",
		},
		{
			name:     "协议一致-HTTP",
			host:     "http://example.com",
			ip:       "***********",
			protocol: "http",
			port:     80,
			expected: "http://example.com",
		},
		{
			name:     "协议一致-HTTPS",
			host:     "https://example.com",
			ip:       "***********",
			protocol: "https",
			port:     443,
			expected: "https://example.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.fullUrl(tt.host, tt.ip, tt.protocol, tt.port)
			assert.Equal(t, tt.expected, result, "主机: %s, IP: %s, 协议: %s, 端口: %d", tt.host, tt.ip, tt.protocol, tt.port)
		})
	}
}

// TestPreloadClues 测试预加载线索方法
func TestPreloadClues(t *testing.T) {
	// 由于preloadClues方法包含复杂的数据库操作，这里我们创建一个简化的单元测试
	// 主要测试方法的基本逻辑和错误处理

	handler := &RecommendAssetHandler{
		config: &RecommendAssetTaskConfig{
			TaskId:  "test-task-1",
			UserId:  1,
			ClueIds: []int{1, 2, 3},
		},
		state: &RecommendAssetTaskState{
			recommendData: SafeRecommendData{
				data:  sync.Map{},
				ipSet: make(map[string]struct{}),
			},
		},
	}

	// 这里我们只验证方法的结构完整性
	// 实际的数据库交互应该在集成测试中进行验证
	assert.NotNil(t, handler.config)
	assert.NotNil(t, handler.state)
	assert.Equal(t, "test-task-1", handler.config.TaskId)
	assert.Equal(t, 1, handler.config.UserId)
	assert.Equal(t, []int{1, 2, 3}, handler.config.ClueIds)
}

// TestFilterCluesByType 测试根据类型过滤线索方法
func TestFilterCluesByType(t *testing.T) {
	// 创建测试数据 - 使用正确的字段结构
	clueList := []*clues.Clue{
		{Content: "example.com", Type: clues.TYPE_DOMAIN},
		{Content: "test.com", Type: clues.TYPE_SUBDOMAIN},
		{Content: "cert content", Type: clues.TYPE_CERT},
		{Content: "sub.example.com", Type: clues.TYPE_SUBDOMAIN},
		{Content: "***********", Type: clues.TYPE_IP},
	}

	// 手动设置ID字段
	for i, clue := range clueList {
		clue.Model.Id = uint64(i + 1)
	}

	handler := &RecommendAssetHandler{}

	tests := []struct {
		name        string
		clueList    []*clues.Clue
		clueType    int
		expectCount int
		expectTypes []int
	}{
		{
			name:        "过滤域名类型",
			clueList:    clueList,
			clueType:    clues.TYPE_DOMAIN,
			expectCount: 1,
			expectTypes: []int{clues.TYPE_DOMAIN},
		},
		{
			name:        "过滤子域名类型",
			clueList:    clueList,
			clueType:    clues.TYPE_SUBDOMAIN,
			expectCount: 2,
			expectTypes: []int{clues.TYPE_SUBDOMAIN, clues.TYPE_SUBDOMAIN},
		},
		{
			name:        "过滤证书类型",
			clueList:    clueList,
			clueType:    clues.TYPE_CERT,
			expectCount: 1,
			expectTypes: []int{clues.TYPE_CERT},
		},
		{
			name:        "过滤不存在的类型",
			clueList:    clueList,
			clueType:    999,
			expectCount: 0,
			expectTypes: []int{},
		},
		{
			name:        "空线索列表",
			clueList:    []*clues.Clue{},
			clueType:    clues.TYPE_DOMAIN,
			expectCount: 0,
			expectTypes: []int{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.filterCluesByType(tt.clueList, tt.clueType)

			assert.Equal(t, tt.expectCount, len(result))

			// 验证所有返回的线索类型都正确
			actualTypes := make([]int, len(result))
			for i, clue := range result {
				actualTypes[i] = clue.Type
			}
			assert.ElementsMatch(t, tt.expectTypes, actualTypes)
		})
	}
}

// TestPreloadCluesIntegration 集成测试：测试三个方法的集成逻辑
func TestPreloadCluesIntegration(t *testing.T) {
	// 创建测试数据
	testClues := []*clues.Clue{
		{Content: "example.com", Type: clues.TYPE_DOMAIN, UserId: 123},
		{Content: "sub.example.com", Type: clues.TYPE_SUBDOMAIN, UserId: 123},
	}

	// 创建handler
	handler := &RecommendAssetHandler{
		config: &RecommendAssetTaskConfig{
			TaskId:  "integration-test",
			UserId:  123,
			ClueIds: []int{1, 2},
		},
		state: &RecommendAssetTaskState{
			recommendData: SafeRecommendData{
				data:  sync.Map{},
				ipSet: make(map[string]struct{}),
			},
			clueList: testClues,
		},
	}

	// 测试checkSubdomainClue
	handler.checkSubdomainClue()
	assert.True(t, handler.state.hasSubdomainClue)

	// 测试filterCluesByType
	domainClues := handler.filterCluesByType(handler.state.clueList, clues.TYPE_DOMAIN)
	assert.Equal(t, 1, len(domainClues))
	assert.Equal(t, "example.com", domainClues[0].Content)

	subdomainClues := handler.filterCluesByType(handler.state.clueList, clues.TYPE_SUBDOMAIN)
	assert.Equal(t, 1, len(subdomainClues))
	assert.Equal(t, "sub.example.com", subdomainClues[0].Content)

	// 验证基本属性
	assert.Equal(t, 123, handler.config.UserId)
	assert.Equal(t, []int{1, 2}, handler.config.ClueIds)
}

// TestStringPtr 测试stringPtr函数
func TestStringPtr(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected *string
	}{
		{
			name:     "非空字符串",
			input:    "test",
			expected: func() *string { s := "test"; return &s }(),
		},
		{
			name:     "空字符串",
			input:    "",
			expected: func() *string { s := ""; return &s }(),
		},
		{
			name:     "包含特殊字符的字符串",
			input:    "<EMAIL>",
			expected: func() *string { s := "<EMAIL>"; return &s }(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := stringPtr(tt.input)
			assert.NotNil(t, result)
			assert.Equal(t, *tt.expected, *result)
		})
	}
}

// TestCheckPort 测试checkPort函数
func TestCheckPort(t *testing.T) {
	tests := []struct {
		name     string
		ip       string
		port     int
		expected bool
	}{
		{
			name:     "无效IP地址",
			ip:       "invalid-ip",
			port:     80,
			expected: false,
		},
		{
			name:     "端口0",
			ip:       "127.0.0.1",
			port:     0,
			expected: false,
		},
		{
			name:     "端口65536",
			ip:       "127.0.0.1",
			port:     65536,
			expected: false,
		},
		{
			name:     "负数端口",
			ip:       "127.0.0.1",
			port:     -1,
			expected: false,
		},
		{
			name:     "不可达IP",
			ip:       "*********", // RFC5737测试用IP，不可达
			port:     80,
			expected: false,
		},
		{
			name:     "本地回环地址_不存在的端口",
			ip:       "127.0.0.1",
			port:     99999, // 不太可能开放的端口
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkPort(tt.ip, tt.port)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestSafeRecommendData_Load 测试SafeRecommendData的Load方法
func TestSafeRecommendData_Load(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		setup    func(*SafeRecommendData)
		expected *recommend_result.RecommendResult
		found    bool
	}{
		{
			name: "存在的键",
			key:  "test-key",
			setup: func(srd *SafeRecommendData) {
				result := &recommend_result.RecommendResult{
					Ip:   "***********",
					Port: "80",
				}
				srd.Store("test-key", result)
			},
			expected: &recommend_result.RecommendResult{
				Ip:   "***********",
				Port: "80",
			},
			found: true,
		},
		{
			name:     "不存在的键",
			key:      "non-existent-key",
			setup:    func(srd *SafeRecommendData) {},
			expected: nil,
			found:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			srd := &SafeRecommendData{
				ipSet: make(map[string]struct{}),
			}
			tt.setup(srd)

			result, found := srd.Load(tt.key)

			assert.Equal(t, tt.found, found)
			if tt.expected != nil {
				assert.Equal(t, tt.expected.Ip, result.Ip)
				assert.Equal(t, tt.expected.Port, result.Port)
			} else {
				assert.Nil(t, result)
			}
		})
	}
}

// TestWithClueArr 测试WithClueArr选项函数
func TestWithClueArr(t *testing.T) {
	handler := &RecommendAssetHandler{
		config: &RecommendAssetTaskConfig{},
	}

	clueArr := []int{1, 2, 3}
	option := WithClueArr(clueArr)
	option(handler)

	assert.Equal(t, clueArr, handler.config.ClueArr)
}

// TestNewRecommendAssetHandler 测试NewRecommendAssetHandler构造函数
func TestNewRecommendAssetHandler(t *testing.T) {
	taskName := "test-task"
	taskId := "test-id"
	detectAssetsTaskId := 123
	userId := 456
	clueIds := []int{1, 2, 3}
	cronId := 789
	organizationDiscoverTaskId := 999

	handler := NewRecommendAssetHandler(
		taskName, taskId, detectAssetsTaskId, userId, clueIds, cronId, organizationDiscoverTaskId,
		WithClueArr([]int{4, 5, 6}),
	)

	assert.NotNil(t, handler)
	assert.Equal(t, taskName, handler.config.TaskName)
	assert.Equal(t, taskId, handler.config.TaskId)
	assert.Equal(t, detectAssetsTaskId, handler.config.ExpendId)
	assert.Equal(t, userId, handler.config.UserId)
	assert.Equal(t, clueIds, handler.config.ClueIds)
	assert.Equal(t, cronId, handler.config.CronId)
	assert.Equal(t, organizationDiscoverTaskId, handler.config.OrganizationDiscoverTaskId)
	assert.Equal(t, []int{4, 5, 6}, handler.config.ClueArr)
}

// TestArrayDiff 测试ArrayDiff函数
func TestArrayDiff(t *testing.T) {
	tests := []struct {
		name     string
		all      []string
		array1   []string
		expected []string
	}{
		{
			name:     "基本差集测试",
			all:      []string{"a", "b", "c", "d"},
			array1:   []string{"b", "d"},
			expected: []string{"a", "c"},
		},
		{
			name:     "空数组测试",
			all:      []string{"a", "b", "c"},
			array1:   []string{},
			expected: []string{"a", "b", "c"},
		},
		{
			name:     "完全重叠测试",
			all:      []string{"a", "b"},
			array1:   []string{"a", "b"},
			expected: nil,
		},
		{
			name:     "无重叠测试",
			all:      []string{"a", "b"},
			array1:   []string{"c", "d"},
			expected: []string{"a", "b"},
		},
		{
			name:     "空all数组测试",
			all:      []string{},
			array1:   []string{"a", "b"},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ArrayDiff(tt.all, tt.array1)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestFindLastClue 测试findLastClue函数
func TestFindLastClue(t *testing.T) {
	tests := []struct {
		name            string
		allClues        []*clues.Clue
		skipKeywordClue bool
		expectedClue    *clues.Clue
		expectedNil     bool
	}{
		{
			name:            "空线索列表",
			allClues:        []*clues.Clue{},
			skipKeywordClue: false,
			expectedClue:    nil,
			expectedNil:     true,
		},
		{
			name:            "nil线索列表",
			allClues:        nil,
			skipKeywordClue: false,
			expectedClue:    nil,
			expectedNil:     true,
		},
		{
			name: "不跳过关键词-返回最后一个线索",
			allClues: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN},
				{Content: "test.com", Type: clues.TYPE_SUBDOMAIN},
				{Content: "keyword", Type: clues.TYPE_KEYWORD},
			},
			skipKeywordClue: false,
			expectedClue:    &clues.Clue{Content: "keyword", Type: clues.TYPE_KEYWORD},
			expectedNil:     false,
		},
		{
			name: "跳过关键词-找到非关键词线索",
			allClues: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN},
				{Content: "test.com", Type: clues.TYPE_SUBDOMAIN},
				{Content: "keyword", Type: clues.TYPE_KEYWORD},
			},
			skipKeywordClue: true,
			expectedClue:    &clues.Clue{Content: "test.com", Type: clues.TYPE_SUBDOMAIN},
			expectedNil:     false,
		},
		{
			name: "跳过关键词-全部都是关键词",
			allClues: []*clues.Clue{
				{Content: "keyword1", Type: clues.TYPE_KEYWORD},
				{Content: "keyword2", Type: clues.TYPE_KEYWORD},
				{Content: "keyword3", Type: clues.TYPE_KEYWORD},
			},
			skipKeywordClue: true,
			expectedClue:    nil,
			expectedNil:     true,
		},
		{
			name: "跳过关键词-最后一个是非关键词",
			allClues: []*clues.Clue{
				{Content: "keyword1", Type: clues.TYPE_KEYWORD},
				{Content: "keyword2", Type: clues.TYPE_KEYWORD},
				{Content: "example.com", Type: clues.TYPE_DOMAIN},
			},
			skipKeywordClue: true,
			expectedClue:    &clues.Clue{Content: "example.com", Type: clues.TYPE_DOMAIN},
			expectedNil:     false,
		},
		{
			name: "跳过关键词-从中间找到非关键词线索",
			allClues: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN},
				{Content: "***********", Type: clues.TYPE_IP},
				{Content: "keyword1", Type: clues.TYPE_KEYWORD},
				{Content: "keyword2", Type: clues.TYPE_KEYWORD},
			},
			skipKeywordClue: true,
			expectedClue:    &clues.Clue{Content: "***********", Type: clues.TYPE_IP},
			expectedNil:     false,
		},
		{
			name: "单个非关键词线索",
			allClues: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN},
			},
			skipKeywordClue: true,
			expectedClue:    &clues.Clue{Content: "example.com", Type: clues.TYPE_DOMAIN},
			expectedNil:     false,
		},
		{
			name: "单个关键词线索-跳过关键词",
			allClues: []*clues.Clue{
				{Content: "keyword", Type: clues.TYPE_KEYWORD},
			},
			skipKeywordClue: true,
			expectedClue:    nil,
			expectedNil:     true,
		},
		{
			name: "单个关键词线索-不跳过关键词",
			allClues: []*clues.Clue{
				{Content: "keyword", Type: clues.TYPE_KEYWORD},
			},
			skipKeywordClue: false,
			expectedClue:    &clues.Clue{Content: "keyword", Type: clues.TYPE_KEYWORD},
			expectedNil:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := findLastClue(tt.allClues, tt.skipKeywordClue)

			if tt.expectedNil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedClue.Content, result.Content)
				assert.Equal(t, tt.expectedClue.Type, result.Type)
			}
		})
	}
}

// setupRecommendTestEnvironment 设置推荐测试环境
func setupRecommendTestEnvironment() {
	testcommon.SetTestEnv(true)
	mysql.SetTestEnv(true)

	cfg.InitLoadCfg()
	log.Init()

	// 启用测试环境
	es.SetTestEnv(true)
	mysqlInit.SetTestEnv(true)
	redisInit.SetTestEnv(true)

	// 初始化Mock实例
	es.GetInstance(cfg.LoadElastic())
	mysqlInit.GetInstance(cfg.LoadMysql())
	redisInit.GetInstance(cfg.LoadRedis())
}

// TestGetCluesInfo 测试getCluesInfo方法
func TestGetCluesInfo(t *testing.T) {
	// 初始化测试环境
	setupRecommendTestEnvironment()

	tests := []struct {
		name            string
		clueList        []*clues.Clue
		clueType        int
		skipKeywordClue bool
		expectGroupId   uint64
		expectError     bool
		expectNilData   bool
		expectCount     int
		mockSetup       func(mock sqlmock.Sqlmock)
	}{
		{
			name:            "空线索列表",
			clueList:        []*clues.Clue{},
			clueType:        clues.TYPE_DOMAIN,
			skipKeywordClue: false,
			expectGroupId:   0,
			expectError:     false,
			expectNilData:   true,
			expectCount:     0,
			mockSetup:       func(mock sqlmock.Sqlmock) {},
		},
		{
			name: "过滤域名类型线索",
			clueList: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN, GroupId: 123},
				{Content: "test.com", Type: clues.TYPE_SUBDOMAIN, GroupId: 123},
				{Content: "cert content", Type: clues.TYPE_CERT, GroupId: 123},
			},
			clueType:        clues.TYPE_DOMAIN,
			skipKeywordClue: false,
			expectGroupId:   123,
			expectError:     false,
			expectNilData:   false,
			expectCount:     1,
			mockSetup: func(mock sqlmock.Sqlmock) {
				// Mock查询线索组名
				groupRows := sqlmock.NewRows([]string{"id", "name", "user_id", "company_id"}).
					AddRow(123, "测试组", 1, 100)
				mock.ExpectQuery("SELECT (.+) FROM `clues_groups`").WillReturnRows(groupRows)
			},
		},
		{
			name: "获取所有类型线索（clueType < 0）",
			clueList: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN, GroupId: 456},
				{Content: "test.com", Type: clues.TYPE_SUBDOMAIN, GroupId: 456},
				{Content: "cert content", Type: clues.TYPE_CERT, GroupId: 456},
			},
			clueType:        -1,
			skipKeywordClue: false,
			expectGroupId:   456,
			expectError:     false,
			expectNilData:   false,
			expectCount:     3,
			mockSetup: func(mock sqlmock.Sqlmock) {
				// Mock查询线索组名
				groupRows := sqlmock.NewRows([]string{"id", "name", "user_id", "company_id"}).
					AddRow(456, "测试组456", 1, 100)
				mock.ExpectQuery("SELECT (.+) FROM `clues_groups`").WillReturnRows(groupRows)
			},
		},
		{
			name: "过滤不存在的类型",
			clueList: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN, GroupId: 123},
				{Content: "test.com", Type: clues.TYPE_SUBDOMAIN, GroupId: 123},
			},
			clueType:        999, // 不存在的类型
			skipKeywordClue: false,
			expectGroupId:   0,
			expectError:     false,
			expectNilData:   true,
			expectCount:     0,
			mockSetup:       func(mock sqlmock.Sqlmock) {},
		},
		{
			name: "线索没有GroupId",
			clueList: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN, GroupId: 0},
				{Content: "test.com", Type: clues.TYPE_SUBDOMAIN, GroupId: 0},
			},
			clueType:        clues.TYPE_DOMAIN,
			skipKeywordClue: false,
			expectGroupId:   0,
			expectError:     false,
			expectNilData:   false,
			expectCount:     1,
			mockSetup:       func(mock sqlmock.Sqlmock) {},
		},
		{
			name: "过滤子域名类型",
			clueList: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN, GroupId: 789},
				{Content: "sub1.example.com", Type: clues.TYPE_SUBDOMAIN, GroupId: 789},
				{Content: "sub2.example.com", Type: clues.TYPE_SUBDOMAIN, GroupId: 789},
				{Content: "cert content", Type: clues.TYPE_CERT, GroupId: 789},
			},
			clueType:        clues.TYPE_SUBDOMAIN,
			skipKeywordClue: false,
			expectGroupId:   789,
			expectError:     false,
			expectNilData:   false,
			expectCount:     2,
			mockSetup: func(mock sqlmock.Sqlmock) {
				// Mock查询线索组名
				groupRows := sqlmock.NewRows([]string{"id", "name", "user_id", "company_id"}).
					AddRow(789, "测试组789", 1, 100)
				mock.ExpectQuery("SELECT (.+) FROM `clues_groups`").WillReturnRows(groupRows)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置并设置 mock
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()
			tt.mockSetup(mysqlMock)

			// 创建handler
			handler := &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId: "test-task",
				},
				state: &RecommendAssetTaskState{
					clueList: tt.clueList,
				},
			}

			// 执行测试
			groupId, groupName, cluesData, lastClue, err := handler.getCluesInfo(tt.clueType, tt.skipKeywordClue)

			// 验证错误情况
			if tt.expectError {
				assert.Error(t, err)
				return
			} else {
				assert.NoError(t, err)
			}

			// 验证空数据情况
			if tt.expectNilData {
				assert.Equal(t, uint64(0), groupId)
				assert.Equal(t, "", groupName)
				assert.Nil(t, cluesData)
				assert.Nil(t, lastClue)
				return
			}

			// 验证非空数据情况
			assert.Equal(t, tt.expectGroupId, groupId)
			assert.Equal(t, tt.expectCount, len(cluesData))
			assert.NotNil(t, cluesData)

			// 验证过滤后的线索类型是否正确（当clueType >= 0时）
			if tt.clueType >= 0 {
				for _, clue := range cluesData {
					assert.Equal(t, tt.clueType, clue.Type)
				}
			}

			// 验证lastClue不为nil（当有数据时）
			if len(cluesData) > 0 {
				assert.NotNil(t, lastClue)
			}

			// 注意：这里我们不测试groupName的具体值，因为它需要数据库查询
			// 在实际项目中，应该通过依赖注入或mock来测试这部分逻辑
		})
	}
}

// TestGetCluesInfoWithFindLastClue 测试getCluesInfo与findLastClue的集成
func TestGetCluesInfoWithFindLastClue(t *testing.T) {
	tests := []struct {
		name                string
		clueList            []*clues.Clue
		clueType            int
		skipKeywordClue     bool
		expectedLastClue    *clues.Clue
		expectedLastClueNil bool
	}{
		{
			name: "跳过关键词-找到非关键词线索",
			clueList: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN, GroupId: 123},
				{Content: "sub.example.com", Type: clues.TYPE_SUBDOMAIN, GroupId: 123},
				{Content: "keyword", Type: clues.TYPE_KEYWORD, GroupId: 123},
			},
			clueType:            -1, // 获取所有类型
			skipKeywordClue:     true,
			expectedLastClue:    &clues.Clue{Content: "sub.example.com", Type: clues.TYPE_SUBDOMAIN, GroupId: 123},
			expectedLastClueNil: false,
		},
		{
			name: "不跳过关键词-返回最后一个",
			clueList: []*clues.Clue{
				{Content: "example.com", Type: clues.TYPE_DOMAIN, GroupId: 123},
				{Content: "keyword", Type: clues.TYPE_KEYWORD, GroupId: 123},
			},
			clueType:            -1, // 获取所有类型
			skipKeywordClue:     false,
			expectedLastClue:    &clues.Clue{Content: "keyword", Type: clues.TYPE_KEYWORD, GroupId: 123},
			expectedLastClueNil: false,
		},
		{
			name: "过滤特定类型后跳过关键词",
			clueList: []*clues.Clue{
				{Content: "domain1.com", Type: clues.TYPE_DOMAIN, GroupId: 123},
				{Content: "domain2.com", Type: clues.TYPE_DOMAIN, GroupId: 123},
				{Content: "keyword", Type: clues.TYPE_KEYWORD, GroupId: 123},
			},
			clueType:            clues.TYPE_DOMAIN, // 只获取域名类型
			skipKeywordClue:     true,
			expectedLastClue:    &clues.Clue{Content: "domain2.com", Type: clues.TYPE_DOMAIN, GroupId: 123},
			expectedLastClueNil: false,
		},
		{
			name: "过滤后全是关键词-跳过关键词",
			clueList: []*clues.Clue{
				{Content: "domain1.com", Type: clues.TYPE_DOMAIN, GroupId: 123},
				{Content: "keyword1", Type: clues.TYPE_KEYWORD, GroupId: 123},
				{Content: "keyword2", Type: clues.TYPE_KEYWORD, GroupId: 123},
			},
			clueType:            clues.TYPE_KEYWORD, // 只获取关键词类型
			skipKeywordClue:     true,
			expectedLastClue:    nil,
			expectedLastClueNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 初始化测试环境
			setupRecommendTestEnvironment()

			// 重置并设置 mock
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()

			// Mock查询线索组名（如果有GroupId）
			if len(tt.clueList) > 0 && tt.clueList[0].GroupId != 0 {
				groupRows := sqlmock.NewRows([]string{"id", "name", "user_id", "company_id"}).
					AddRow(tt.clueList[0].GroupId, "测试组", 1, 100)
				mysqlMock.ExpectQuery("SELECT (.+) FROM `clues_groups`").WillReturnRows(groupRows)
			}

			// 创建handler
			handler := &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId: "test-task",
				},
				state: &RecommendAssetTaskState{
					clueList: tt.clueList,
				},
			}

			// 执行测试
			_, _, cluesData, lastClue, err := handler.getCluesInfo(tt.clueType, tt.skipKeywordClue)

			assert.NoError(t, err)
			assert.NotNil(t, cluesData)

			// 验证lastClue
			if tt.expectedLastClueNil {
				assert.Nil(t, lastClue)
			} else {
				assert.NotNil(t, lastClue)
				assert.Equal(t, tt.expectedLastClue.Content, lastClue.Content)
				assert.Equal(t, tt.expectedLastClue.Type, lastClue.Type)
				assert.Equal(t, tt.expectedLastClue.GroupId, lastClue.GroupId)
			}
		})
	}
}

// TestConvertPbAssetToRecommendResult 测试convertPbAssetToRecommendResult方法
func TestConvertPbAssetToRecommendResult(t *testing.T) {
	// 初始化测试环境
	setupRecommendTestEnvironment()

	// 创建测试handler
	handler := createTestHandler()

	// 创建mock数据
	clue := &clues.Clue{
		Model:           dbx.Model{Id: 123},
		Content:         "example.com",
		Type:            clues.TYPE_DOMAIN,
		ClueCompanyName: "测试公司",
	}

	tests := []struct {
		name          string
		asset         *pb.Asset
		expectedError bool
		description   string
	}{
		{
			name: "基础转换测试",
			asset: &pb.Asset{
				Ip:             stringPtr("***********"),
				Port:           stringPtr("80"),
				Protocol:       stringPtr("http"),
				BaseProtocol:   stringPtr("tcp"),
				Host:           stringPtr("example.com"),
				Domain:         stringPtr("example.com"),
				Title:          stringPtr("测试标题"),
				Icp:            stringPtr("京ICP备12345678号"),
				Cert:           stringPtr("CN=example.com"),
				Lastupdatetime: stringPtr("2023-01-01 12:00:00"),
				CertsValid:     stringPtr("true"),
				CloudName:      stringPtr("阿里云"),
				Cname:          stringPtr("example.cdn.com"),
				Product:        stringPtr("nginx"),
			},
			expectedError: false,
			description:   "测试基础的pb.Asset到RecommendResult的转换",
		},
		{
			name: "IPv6地址测试",
			asset: &pb.Asset{
				Ip:       stringPtr("2001:db8::1"),
				Port:     stringPtr("443"),
				Protocol: stringPtr("https"),
				Host:     stringPtr("ipv6.example.com"),
			},
			expectedError: false,
			description:   "测试IPv6地址的处理",
		},
		{
			name: "ICON处理测试",
			asset: &pb.Asset{
				Ip:       stringPtr("***********"),
				Port:     stringPtr("80"),
				Protocol: stringPtr("http"),
				Host:     stringPtr("icon.example.com"),
				Icon:     stringPtr("AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAD///9u////4f///+X////l////5f///+X////l////5f///+X////l////5f///+X////l////5f///+H///9u////4f///+X////l////5f///+X////l4NzX6cS9s+3EvbPt4NzX6f///+X////l+vr558S+tO36+vnn////4f///+X////l////5f///+XHwbftbF1C+3VmRv+Vh2f/lYdn/3VmRv9sXUL7x8G37cbAtu1RPyD/xL607f///+X////l////5f///+Won5DxcmJD/9rQrv/3783/9+/N//fvzf/3783/2tCu/3JiQ/+mnY3xxsC27fr6+ef////l////5f///+XHwbftcmJD//Hpx//MwqH/WEYn/9PJqP/3783/9/DO//jx0f/07c3/f3BR/8fBt+3////l////5f///+X////lbF1C+9rQrv/3783/t6uK/1E/IP/EuZr//ffd///64///+uP///rj//nz2v9YRyn/////5f///+X////l4NzX6XVmRv/3783/9+/N/6+hgf9RPyD/nI5z///64///+uP///rj///64///+uP/lIhq/8fBt+3////l////5cS9s+2Vh2f/9+/N//fvzf+1qIz/ZEwq/4BfOv+CYDr/hmVA/+fdw///+uP///rj/8G3m/+flIPz////5f///+XEvbPtlYdn//fvzf/689X/taiM/4JgOv+AYDz/m4Bf/6eNbP/17tb///rj///64//Atpr/npOC8////+X////l4NzX6XVmRv/3783//vng/7qpjf+CYDr/nIVo///64///+uP///rj///64///+uP/k4do/8fAt+3////l////5f///+VsXUL74tm5///64//48tr/impG/4JgOv+DYj3/gmA7/9THrf//+uP/+PPa/1dGJ//////l////5f///+X////lx8G37Y1/Yf//+eL///rj/+7lzP+jimr/mH1c/45xT//q4cj///ni/46AY/+imIjz////5f///+X////l////5f///+WJfGj3joBj//jy2f//+uP///rj///64///+uP/+PPa/46AY/9+cVr5////5f///+X////l////5f///+X////l////5aKYiPNWRSb/k4Zo/7+1mP/Atpr/k4do/1dGJ/+imIjz////5f///+X////l////5f///+H////l////5f///+X////l////5cfAt+2ek4LznpOC88fAt+3////l////5f///+X////l////5f///+H///9u////4f///+X////l////5f///+X////l////5f///+X////l////5f///+X////l////5f///+H///9ugAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAEAAA=="),
				IconHash: stringPtr("123456"),
			},
			expectedError: false,
			description:   "测试ICON哈希和内容的处理",
		},
		{
			name: "时间解析测试",
			asset: &pb.Asset{
				Ip:             stringPtr("***********"),
				Port:           stringPtr("80"),
				Protocol:       stringPtr("http"),
				Host:           stringPtr("time.example.com"),
				Lastupdatetime: stringPtr("2024-01-01 15:30:45"),
			},
			expectedError: false,
			description:   "测试时间字段的解析",
		},
		{
			name: "证书验证测试_true",
			asset: &pb.Asset{
				Ip:         stringPtr("***********"),
				Port:       stringPtr("443"),
				Protocol:   stringPtr("https"),
				Host:       stringPtr("cert-true.example.com"),
				CertsValid: stringPtr("1"),
			},
			expectedError: false,
			description:   "测试证书有效性为true的情况",
		},
		{
			name: "证书验证测试_false",
			asset: &pb.Asset{
				Ip:         stringPtr("***********"),
				Port:       stringPtr("443"),
				Protocol:   stringPtr("https"),
				Host:       stringPtr("cert-false.example.com"),
				CertsValid: stringPtr("false"),
			},
			expectedError: false,
			description:   "测试证书有效性为false的情况",
		},
		{
			name: "空字段测试",
			asset: &pb.Asset{
				Ip:       stringPtr("***********"),
				Port:     stringPtr(""),
				Protocol: stringPtr(""),
				Host:     stringPtr(""),
			},
			expectedError: false,
			description:   "测试空字段的处理",
		},
		{
			name: "CDN检测测试",
			asset: &pb.Asset{
				Ip:        stringPtr("***********"),
				Port:      stringPtr("80"),
				Protocol:  stringPtr("http"),
				Host:      stringPtr("cdn.example.com"),
				CloudName: stringPtr("CloudFlare"),
				Cert:      stringPtr("CN=*.cdn.example.com"),
			},
			expectedError: false,
			description:   "测试CDN检测逻辑",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := handler.convertPbAssetToRecommendResult(tt.asset, clue, recommend_result.ASSETS_SOURCE_FOFA)

			if tt.expectedError {
				assert.Error(t, err, tt.description)
				return
			}

			assert.NoError(t, err, tt.description)
			assert.NotNil(t, result, "结果不应为空")

			// 验证基本字段
			assert.Equal(t, handler.config.TaskId, result.Flag, "Flag字段应匹配")
			assert.Equal(t, handler.config.UserId, result.UserId, "UserId字段应匹配")
			assert.Equal(t, handler.config.CompanyId, result.CompanyId, "CompanyId字段应匹配")
			assert.Equal(t, int(clue.Id), result.Reason[0].Id, "Reason中的线索ID应匹配")
			assert.Equal(t, clue.Content, result.Reason[0].Content, "Reason中的线索内容应匹配")

			// 验证IP字段处理
			if tt.asset.GetIp() != "" {
				if tt.name == "IPv6地址测试" {
					assert.True(t, result.IsIPv6, "应正确识别IPv6地址")
				} else {
					assert.False(t, result.IsIPv6, "应正确识别IPv4地址")
				}
			}

			// 验证协议字段
			if tt.asset.GetProtocol() != "" {
				assert.Equal(t, tt.asset.GetProtocol(), result.Protocol, "协议字段应匹配")
			}

			// 验证证书有效性
			if tt.name == "证书验证测试_true" {
				assert.True(t, result.CertsValid, "证书有效性应为true")
			}
			if tt.name == "证书验证测试_false" {
				assert.False(t, result.CertsValid, "证书有效性应为false")
			}

			// 验证ICON处理
			if tt.name == "ICON处理测试" {
				assert.NotEqual(t, 0, result.Logo.Hash, "ICON哈希应被设置")
				assert.NotEmpty(t, result.Logo.Content, "ICON内容应被设置")
			}

			// 验证时间解析
			if tt.asset.GetLastupdatetime() != "" {
				assert.NotEmpty(t, result.SourceUpdatedAt, "更新时间应被设置")
				// 验证时间格式
				_, err := time.Parse("2006-01-02 15:04:05", result.SourceUpdatedAt)
				assert.NoError(t, err, "时间格式应正确")
			}

			// 验证URL构建
			if tt.asset.GetHost() != "" || tt.asset.GetIp() != "" {
				assert.NotEmpty(t, result.Url, "URL应被构建")
			}

			// 验证ID生成
			assert.NotEmpty(t, result.Id, "ID应被生成")
			assert.Len(t, result.Id, 32, "MD5 ID长度应为32")
		})
	}
}

// TestProcessOneForAllResult 测试processOneForAllResult方法
func TestProcessOneForAllResult(t *testing.T) {
	// 创建mock线索
	clue := &clues.Clue{
		Model:           dbx.Model{Id: 456},
		Content:         "example.com",
		Type:            clues.TYPE_DOMAIN,
		ClueCompanyName: "OneForAll测试公司",
	}

	tests := []struct {
		name        string
		val         map[string]string
		ipStr       string
		description string
	}{
		{
			name: "单个IP处理测试",
			val: map[string]string{
				"url":       "http://test.example.com",
				"port":      "80",
				"domain":    "example.com",
				"subdomain": "test.example.com",
				"title":     "测试页面",
				"cname":     "test.cdn.com",
				"source":    "GoogleQuery",
				"banner":    "nginx/1.20.1",
				"cdn":       "false",
				"cert":      "CN=test.example.com",
				"product":   "nginx",
			},
			ipStr:       "***********0",
			description: "测试单个IP地址的处理",
		},
		{
			name: "多IP处理测试",
			val: map[string]string{
				"url":       "https://multi.example.com",
				"port":      "443",
				"domain":    "example.com",
				"subdomain": "multi.example.com",
				"title":     "多IP测试页面",
				"source":    "CrtshQuery",
				"cdn":       "true",
			},
			ipStr:       "***********1, ***********2, ***********3",
			description: "测试多个IP地址的处理",
		},
		{
			name: "空IP处理测试",
			val: map[string]string{
				"url":       "http://empty.example.com",
				"port":      "80",
				"domain":    "example.com",
				"subdomain": "empty.example.com",
				"source":    "GoogleQuery",
			},
			ipStr:       "",
			description: "测试空IP字符串的处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 每个测试用例使用独立的handler
			handler := createTestHandler()

			// 记录初始处理数量
			initialCount := atomic.LoadInt64(&handler.state.currentNumber)

			// 执行方法
			err := handler.processOneForAllResult(context.Background(), tt.val, tt.ipStr, clue)
			assert.NoError(t, err, tt.description)

			// 验证处理数量更新
			finalCount := atomic.LoadInt64(&handler.state.currentNumber)

			// 计算期望的IP数量
			expectedIpCount := 0
			if tt.ipStr != "" {
				ipArr := strings.Split(tt.ipStr, ",")
				for _, ip := range ipArr {
					if strings.TrimSpace(ip) != "" {
						expectedIpCount++
					}
				}
			}

			// 验证计数器更新
			expectedFinalCount := initialCount + int64(expectedIpCount)
			assert.Equal(t, expectedFinalCount, finalCount, "处理数量应正确更新")

			// 如果有IP被处理，验证数据存储
			if expectedIpCount > 0 {
				// 验证存储的数据数量
				actualDataCount := 0
				handler.state.recommendData.data.Range(func(key, value interface{}) bool {
					actualDataCount++
					return true
				})
				assert.Equal(t, expectedIpCount, actualDataCount, "存储的数据数量应正确")

				// 验证存储的数据内容
				var results []*recommend_result.RecommendResult
				handler.state.recommendData.data.Range(func(key, value interface{}) bool {
					result := value.(*recommend_result.RecommendResult)
					results = append(results, result)
					return true
				})

				// 验证每个结果的通用字段
				for _, result := range results {
					// 验证基本字段
					assert.Equal(t, handler.config.TaskId, result.Flag, "Flag应匹配")
					assert.Equal(t, handler.config.UserId, result.UserId, "UserId应匹配")
					assert.Equal(t, handler.config.CompanyId, result.CompanyId, "CompanyId应匹配")

					// 验证Reason
					assert.Len(t, result.Reason, 1, "应有一个Reason")
					assert.Equal(t, int(clue.Id), result.Reason[0].Id, "Reason ID应匹配")
					assert.Equal(t, clue.Content, result.Reason[0].Content, "Reason内容应匹配")

					// 验证协议检测
					if strings.Contains(tt.val["url"], "ttps") {
						assert.Equal(t, "https", result.Protocol, "应检测为HTTPS协议")
					} else {
						assert.Equal(t, "http", result.Protocol, "应检测为HTTP协议")
					}

					// 验证来源相关字段
					expectedSource := getSourceConstant(tt.val["source"])
					assert.Equal(t, expectedSource, result.AssetsSource, "资产来源常量应正确")
					assert.Equal(t, tt.val["source"], result.OneforallSource, "OneForAll来源应匹配")

					// 验证字段映射
					assert.Equal(t, tt.val["port"], result.Port, "端口应匹配")
					assert.Equal(t, tt.val["url"], result.Url, "URL应匹配")
					assert.Equal(t, tt.val["domain"], result.Domain, "顶级域名应匹配")
					assert.Equal(t, tt.val["subdomain"], result.Subdomain, "子域名应匹配")
					assert.Equal(t, tt.val["title"], result.Title, "标题应匹配")
					assert.Equal(t, tt.val["cname"], result.Cname, "CNAME应匹配")
					assert.Equal(t, tt.val["banner"], result.Banner, "Banner应匹配")
					assert.Equal(t, tt.val["cert"], result.CertRaw, "证书原始内容应匹配")
					assert.Equal(t, tt.val["product"], result.Product, "产品应匹配")

					// 验证CDN标识
					if tt.val["cdn"] == "true" {
						assert.True(t, result.IsCDN, "CDN标识应为true")
					}

					// 验证时间字段
					assert.NotEmpty(t, result.SourceUpdatedAt, "更新时间应被设置")
					assert.NotEmpty(t, result.CreatedAt, "创建时间应被设置")
					assert.NotEmpty(t, result.UpdatedAt, "更新时间应被设置")

					// 验证ID生成
					assert.NotEmpty(t, result.Id, "ID应被生成")
					assert.Len(t, result.Id, 32, "MD5 ID长度应为32")
				}
			}
		})
	}
}

// TestProcessOneForAllResultEdgeCases 测试processOneForAllResult的边界情况
func TestProcessOneForAllResultEdgeCases(t *testing.T) {
	handler := createTestHandler()
	clue := &clues.Clue{
		Model:   dbx.Model{Id: 789},
		Content: "edge.example.com",
		Type:    clues.TYPE_DOMAIN,
	}

	t.Run("空数据测试", func(t *testing.T) {
		initialCount := atomic.LoadInt64(&handler.state.currentNumber)

		err := handler.processOneForAllResult(context.Background(), map[string]string{}, "", clue)
		assert.NoError(t, err, "空数据不应产生错误")

		finalCount := atomic.LoadInt64(&handler.state.currentNumber)
		assert.Equal(t, initialCount, finalCount, "空数据不应更新计数器")
	})

	t.Run("只有逗号的IP字符串", func(t *testing.T) {
		initialCount := atomic.LoadInt64(&handler.state.currentNumber)

		err := handler.processOneForAllResult(context.Background(), map[string]string{
			"url":  "http://comma.example.com",
			"port": "80",
		}, ",,,,", clue)
		assert.NoError(t, err, "只有逗号的IP字符串不应产生错误")

		finalCount := atomic.LoadInt64(&handler.state.currentNumber)
		assert.Equal(t, initialCount, finalCount, "只有逗号的IP字符串不应更新计数器")
	})

	t.Run("混合有效和无效IP", func(t *testing.T) {
		initialCount := atomic.LoadInt64(&handler.state.currentNumber)

		err := handler.processOneForAllResult(context.Background(), map[string]string{
			"url":       "http://mixed.example.com",
			"port":      "80",
			"subdomain": "mixed.example.com",
			"source":    "GoogleQuery",
		}, "************, , ************,  ,************", clue)
		assert.NoError(t, err, "混合IP字符串不应产生错误")

		finalCount := atomic.LoadInt64(&handler.state.currentNumber)
		// 应该处理3个有效IP
		assert.Equal(t, initialCount+3, finalCount, "应处理3个有效IP")
	})
}

// TestGetSourceConstant 测试getSourceConstant函数
func TestGetSourceConstant(t *testing.T) {
	tests := []struct {
		source   string
		expected int
	}{
		{"VirusTotalQuery", recommend_result.VirusTotalQuery},
		{"CensysAPIQuery", recommend_result.CensysAPIQuery},
		{"GoogleQuery", recommend_result.GoogleQuery},
		{"UnknownSource", recommend_result.OtherSource},
		{"", recommend_result.OtherSource},
	}

	for _, tt := range tests {
		t.Run(tt.source, func(t *testing.T) {
			result := getSourceConstant(tt.source)
			assert.Equal(t, tt.expected, result, fmt.Sprintf("来源 %s 应返回 %d", tt.source, tt.expected))
		})
	}
}

// TestGetExtraFofaPureDnsArr 测试getExtraFofaPureDnsArr方法
func TestGetExtraFofaPureDnsArr(t *testing.T) {
	// 创建测试handler
	handler := createTestHandler()

	// 创建mock线索
	domainClue := &clues.Clue{
		Model:           dbx.Model{Id: 789},
		Content:         "test.example.com",
		Type:            clues.TYPE_DOMAIN,
		ClueCompanyName: "测试公司FOFA",
	}

	tests := []struct {
		name                 string
		extraRecommendData   map[string]*recommend_result.RecommendResult
		groupID              int
		groupName            string
		item                 *pb.PureDnsInfo
		isHttps              bool
		expectedProtocol     string
		expectedPort         int
		expectedUrl          string
		expectedAssetsSource int
		description          string
	}{
		{
			name:               "HTTP纯解析数据处理",
			extraRecommendData: make(map[string]*recommend_result.RecommendResult),
			groupID:            123,
			groupName:          "测试组",
			item: &pb.PureDnsInfo{
				Host:           "api.example.com",
				Ip:             "***********0",
				LastUpdateTime: "2023-01-01",
			},
			isHttps:              false,
			expectedProtocol:     "http",
			expectedPort:         80,
			expectedUrl:          "http://api.example.com",
			expectedAssetsSource: recommend_result.ASSETS_SOURCE_FOFA_PARSE_DOMAIN,
			description:          "测试HTTP协议的纯解析数据处理",
		},
		{
			name:               "HTTPS纯解析数据处理",
			extraRecommendData: make(map[string]*recommend_result.RecommendResult),
			groupID:            456,
			groupName:          "HTTPS测试组",
			item: &pb.PureDnsInfo{
				Host:           "secure.example.com",
				Ip:             "********",
				LastUpdateTime: "2023-06-15",
			},
			isHttps:              true,
			expectedProtocol:     "https",
			expectedPort:         443,
			expectedUrl:          "https://secure.example.com",
			expectedAssetsSource: recommend_result.ASSETS_SOURCE_FOFA_PARSE_DOMAIN,
			description:          "测试HTTPS协议的纯解析数据处理",
		},
		{
			name:               "IPv6地址处理",
			extraRecommendData: make(map[string]*recommend_result.RecommendResult),
			groupID:            789,
			groupName:          "IPv6测试组",
			item: &pb.PureDnsInfo{
				Host:           "ipv6.example.com",
				Ip:             "2001:db8::1",
				LastUpdateTime: "2023-12-01",
			},
			isHttps:              false,
			expectedProtocol:     "http",
			expectedPort:         80,
			expectedUrl:          "http://ipv6.example.com",
			expectedAssetsSource: recommend_result.ASSETS_SOURCE_FOFA_PARSE_DOMAIN,
			description:          "测试IPv6地址的处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行方法
			result := handler.getExtraFofaPureDnsArr(
				tt.extraRecommendData,
				tt.groupID,
				tt.groupName,
				tt.item,
				domainClue,
				tt.isHttps,
			)

			// 验证返回的map不为空
			assert.NotNil(t, result, "返回的结果不应为空")
			assert.Greater(t, len(result), 0, "应该至少有一个推荐结果")

			// 获取第一个（也是唯一的）结果进行验证
			var recommendResult *recommend_result.RecommendResult
			for _, v := range result {
				recommendResult = v
				break
			}

			// 验证基础字段
			assert.NotEmpty(t, recommendResult.Id, "ID不应为空")
			assert.Equal(t, tt.groupID, recommendResult.GroupId, "GroupId应该匹配")
			assert.Equal(t, tt.groupName, recommendResult.GroupName, "GroupName应该匹配")
			assert.Equal(t, tt.expectedProtocol, recommendResult.Protocol, "协议应该匹配")
			assert.Equal(t, tt.expectedPort, utils.SafeInt(recommendResult.Port), "端口应该匹配")
			assert.Equal(t, tt.expectedUrl, recommendResult.Url, "URL应该匹配")
			assert.Equal(t, "tcp", recommendResult.BaseProtocol, "基础协议应该是tcp")
			assert.Equal(t, tt.expectedAssetsSource, recommendResult.AssetsSource, "资产来源应该匹配")

			// 验证IP和域名字段
			if tt.item.Ip != "" {
				assert.Equal(t, tt.item.Ip, recommendResult.Ip, "IP应该匹配")
			}
			if tt.item.Host != "" {
				assert.Contains(t, recommendResult.Url, tt.item.Host, "URL应该包含主机名")
			}

			// 验证Reason字段
			assert.Len(t, recommendResult.Reason, 1, "应该有一个推荐理由")
			assert.Equal(t, int(domainClue.Id), recommendResult.Reason[0].Id, "推荐理由ID应该匹配")
			assert.Equal(t, domainClue.Content, recommendResult.Reason[0].Content, "推荐理由内容应该匹配")

			// 验证时间字段
			assert.NotEmpty(t, recommendResult.CreatedAt, "创建时间不应为空")
			assert.NotEmpty(t, recommendResult.UpdatedAt, "更新时间不应为空")
			assert.NotEmpty(t, recommendResult.SourceUpdatedAt, "来源更新时间不应为空")

			// 验证其他必要字段
			assert.Equal(t, handler.config.TaskId, recommendResult.Flag, "任务ID应该匹配")
			assert.Equal(t, handler.config.UserId, recommendResult.UserId, "用户ID应该匹配")
			assert.Equal(t, handler.config.CompanyId, recommendResult.CompanyId, "公司ID应该匹配")
			assert.Equal(t, 0, recommendResult.Status, "状态应该是默认值")
			assert.Equal(t, 0, recommendResult.Audit, "审核应该是默认值")
			assert.False(t, recommendResult.CertsValid, "证书验证应该是false")
			assert.False(t, recommendResult.OpenParse, "开放解析应该是false")
		})
	}
}

// TestGetExtraFofaPureDnsArrMergeReason 测试getExtraFofaPureDnsArr方法的推荐理由合并功能
func TestGetExtraFofaPureDnsArrMergeReason(t *testing.T) {
	handler := createTestHandler()

	// 创建两个不同的线索
	domainClue1 := &clues.Clue{
		Model:           dbx.Model{Id: 100},
		Content:         "first.example.com",
		Type:            clues.TYPE_DOMAIN,
		ClueCompanyName: "第一公司",
	}

	domainClue2 := &clues.Clue{
		Model:           dbx.Model{Id: 200},
		Content:         "second.example.com",
		Type:            clues.TYPE_DOMAIN,
		ClueCompanyName: "第二公司",
	}

	// 测试数据
	extraRecommendData := make(map[string]*recommend_result.RecommendResult)
	groupID := 999
	groupName := "合并测试组"
	item := &pb.PureDnsInfo{
		Host:           "common.example.com",
		Ip:             "***********00",
		LastUpdateTime: "2023-01-01",
	}

	// 第一次调用 - 添加第一个线索的推荐理由
	result1 := handler.getExtraFofaPureDnsArr(
		extraRecommendData,
		groupID,
		groupName,
		item,
		domainClue1,
		false, // HTTP
	)

	// 验证第一次结果
	assert.Len(t, result1, 1, "第一次调用应该有一个结果")

	var firstResult *recommend_result.RecommendResult
	for _, v := range result1 {
		firstResult = v
		break
	}
	assert.Len(t, firstResult.Reason, 1, "第一次应该只有一个推荐理由")
	assert.Equal(t, int(domainClue1.Id), firstResult.Reason[0].Id, "第一个推荐理由ID应该匹配")

	// 第二次调用 - 使用相同的extraRecommendData，添加第二个线索的推荐理由
	result2 := handler.getExtraFofaPureDnsArr(
		extraRecommendData, // 传入相同的map，这样会触发合并逻辑
		groupID,
		groupName,
		item,
		domainClue2,
		false, // HTTP，协议相同会产生相同的ID
	)

	// 验证第二次结果 - 应该合并推荐理由
	assert.Len(t, result2, 1, "第二次调用应该仍然只有一个结果（合并了）")

	var mergedResult *recommend_result.RecommendResult
	for _, v := range result2 {
		mergedResult = v
		break
	}

	// 验证合并后的推荐理由
	assert.Len(t, mergedResult.Reason, 2, "合并后应该有两个推荐理由")

	// 验证两个推荐理由都存在
	reasonIds := make(map[int]bool)
	for _, reason := range mergedResult.Reason {
		reasonIds[reason.Id] = true
	}
	assert.True(t, reasonIds[int(domainClue1.Id)], "应该包含第一个线索的推荐理由")
	assert.True(t, reasonIds[int(domainClue2.Id)], "应该包含第二个线索的推荐理由")
}

// TestGetExtraFofaPureDnsArrUpdateTime 测试getExtraFofaPureDnsArr方法的时间更新逻辑
func TestGetExtraFofaPureDnsArrUpdateTime(t *testing.T) {
	handler := createTestHandler()

	domainClue := &clues.Clue{
		Model:   dbx.Model{Id: 300},
		Content: "time.example.com",
		Type:    clues.TYPE_DOMAIN,
	}

	extraRecommendData := make(map[string]*recommend_result.RecommendResult)
	groupID := 888
	groupName := "时间测试组"

	// 第一次调用 - 较早的时间
	item1 := &pb.PureDnsInfo{
		Host:           "time.example.com",
		Ip:             "*************",
		LastUpdateTime: "2023-01-01",
	}

	// 手动设置一个较早的SourceUpdatedAt时间
	time.Sleep(time.Millisecond * 10) // 确保时间差异
	earlyTime := time.Now().Add(-time.Hour).Format("2006-01-02 15:04:05")

	result1 := handler.getExtraFofaPureDnsArr(
		extraRecommendData,
		groupID,
		groupName,
		item1,
		domainClue,
		false,
	)

	// 手动修改第一个结果的SourceUpdatedAt为较早时间
	for _, v := range result1 {
		v.SourceUpdatedAt = earlyTime
		break
	}

	// 第二次调用 - 较晚的时间，应该会更新现有记录
	item2 := &pb.PureDnsInfo{
		Host:           "time.example.com",
		Ip:             "*************",
		LastUpdateTime: "2023-12-31",
	}

	result2 := handler.getExtraFofaPureDnsArr(
		extraRecommendData, // 使用相同的map
		groupID,
		groupName,
		item2,
		domainClue,
		false, // 相同协议，产生相同ID
	)

	// 验证结果被更新了
	assert.Len(t, result2, 1, "应该仍然只有一个结果")

	var finalResult *recommend_result.RecommendResult
	for _, v := range result2 {
		finalResult = v
		break
	}

	// 验证时间确实被更新了（新的时间应该比手动设置的早期时间要晚）
	assert.Greater(t, finalResult.SourceUpdatedAt, earlyTime, "SourceUpdatedAt应该被更新为更晚的时间")
}

// TestGetExtraFofaPureDnsArrEmptyFields 测试getExtraFofaPureDnsArr方法处理空字段的情况
func TestGetExtraFofaPureDnsArrEmptyFields(t *testing.T) {
	handler := createTestHandler()

	domainClue := &clues.Clue{
		Model:   dbx.Model{Id: 400},
		Content: "",
		Type:    clues.TYPE_DOMAIN,
	}

	tests := []struct {
		name string
		item *pb.PureDnsInfo
	}{
		{
			name: "Host为空",
			item: &pb.PureDnsInfo{
				Host:           "",
				Ip:             "************",
				LastUpdateTime: "2023-01-01",
			},
		},
		{
			name: "IP为空",
			item: &pb.PureDnsInfo{
				Host:           "empty.example.com",
				Ip:             "",
				LastUpdateTime: "2023-01-01",
			},
		},
		{
			name: "LastUpdateTime为空",
			item: &pb.PureDnsInfo{
				Host:           "notime.example.com",
				Ip:             "************",
				LastUpdateTime: "",
			},
		},
		{
			name: "所有字段为空",
			item: &pb.PureDnsInfo{
				Host:           "",
				Ip:             "",
				LastUpdateTime: "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			extraRecommendData := make(map[string]*recommend_result.RecommendResult)

			// 执行方法，不应该崩溃
			result := handler.getExtraFofaPureDnsArr(
				extraRecommendData,
				123,
				"空字段测试组",
				tt.item,
				domainClue,
				false,
			)

			// 验证结果
			assert.NotNil(t, result, "即使字段为空也应该返回结果")
			assert.Len(t, result, 1, "应该有一个结果")

			// 获取结果进行基本验证
			var recommendResult *recommend_result.RecommendResult
			for _, v := range result {
				recommendResult = v
				break
			}

			// 基本验证 - 确保没有崩溃并且基本字段设置正确
			assert.NotEmpty(t, recommendResult.Id, "ID不应为空")
			assert.Equal(t, "http", recommendResult.Protocol, "协议应该是http")
			assert.Equal(t, 80, utils.SafeInt(recommendResult.Port), "端口应该是80")
		})
	}
}

// TestCalculateDateAfter 测试calculateDateAfter函数
func TestCalculateDateAfter(t *testing.T) {
	tests := []struct {
		name          string
		fofaRange     int
		expectedDays  int
		shouldBeEmpty bool
	}{
		{
			name:          "FOFA范围1年",
			fofaRange:     1,
			expectedDays:  365,
			shouldBeEmpty: false,
		},
		{
			name:          "FOFA范围6个月",
			fofaRange:     2,
			expectedDays:  180,
			shouldBeEmpty: false,
		},
		{
			name:          "FOFA范围3个月",
			fofaRange:     3,
			expectedDays:  90,
			shouldBeEmpty: false,
		},
		{
			name:          "FOFA范围为0",
			fofaRange:     0,
			expectedDays:  0,
			shouldBeEmpty: true,
		},
		{
			name:          "FOFA范围为负数",
			fofaRange:     -1,
			expectedDays:  0,
			shouldBeEmpty: true,
		},
		{
			name:          "FOFA范围超出有效值",
			fofaRange:     4,
			expectedDays:  0,
			shouldBeEmpty: true,
		},
		{
			name:          "FOFA范围为大数值",
			fofaRange:     999,
			expectedDays:  0,
			shouldBeEmpty: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := calculateDateAfter(tt.fofaRange)

			if tt.shouldBeEmpty {
				assert.Empty(t, result, "FOFA范围 %d 应返回空字符串", tt.fofaRange)
				return
			}

			// 验证返回值不为空
			assert.NotEmpty(t, result, "FOFA范围 %d 应返回非空日期字符串", tt.fofaRange)

			// 验证日期格式
			parsedDate, err := time.Parse("2006-01-02", result)
			assert.NoError(t, err, "返回的日期格式应为 '2006-01-02'，实际: %s", result)

			// 计算与当前时间的天数差异
			now := time.Now()
			actualDays := int(now.Sub(parsedDate).Hours() / 24)

			// 允许±1天的误差，考虑到测试执行时的时间差异
			assert.InDelta(t, tt.expectedDays, actualDays, 1,
				"FOFA范围 %d 应该计算出 %d 天前的日期，实际计算出 %d 天前，返回日期: %s",
				tt.fofaRange, tt.expectedDays, actualDays, result)
		})
	}
}

// TestCalculateDateAfterDateFormat 测试calculateDateAfter返回的日期格式
func TestCalculateDateAfterDateFormat(t *testing.T) {
	validRanges := []int{1, 2, 3}

	for _, fofaRange := range validRanges {
		t.Run(fmt.Sprintf("验证范围%d的日期格式", fofaRange), func(t *testing.T) {
			result := calculateDateAfter(fofaRange)

			// 验证格式是否为 YYYY-MM-DD
			assert.Regexp(t, `^\d{4}-\d{2}-\d{2}$`, result,
				"日期格式应为 YYYY-MM-DD，实际: %s", result)

			// 验证是否可以被time.Parse正确解析
			parsedDate, err := time.Parse("2006-01-02", result)
			assert.NoError(t, err, "日期应该可以被正确解析，日期: %s", result)

			// 验证解析后的日期在过去
			assert.True(t, parsedDate.Before(time.Now()),
				"计算出的日期应该在当前时间之前，日期: %s", result)
		})
	}
}

// TestCalculateDateAfterConsistency 测试calculateDateAfter的一致性
func TestCalculateDateAfterConsistency(t *testing.T) {
	validRanges := []int{1, 2, 3}

	for _, fofaRange := range validRanges {
		t.Run(fmt.Sprintf("验证范围%d的一致性", fofaRange), func(t *testing.T) {
			// 连续调用多次，验证结果一致性
			results := make([]string, 5)
			for i := 0; i < 5; i++ {
				results[i] = calculateDateAfter(fofaRange)
				// 短暂休眠，模拟实际使用场景
				time.Sleep(time.Millisecond * 10)
			}

			// 由于函数基于当前时间计算，在很短时间内多次调用应该返回相同结果
			firstResult := results[0]
			for i, result := range results {
				assert.Equal(t, firstResult, result,
					"第%d次调用结果应与第1次相同，期望: %s，实际: %s",
					i+1, firstResult, result)
			}
		})
	}
}

// TestCalculateDateAfterEdgeCases 测试calculateDateAfter的边界情况
func TestCalculateDateAfterEdgeCases(t *testing.T) {
	edgeCases := []struct {
		name      string
		fofaRange int
		expected  string
	}{
		{
			name:      "最小负数",
			fofaRange: -2147483648, // int32最小值
			expected:  "",
		},
		{
			name:      "最大正数",
			fofaRange: 2147483647, // int32最大值
			expected:  "",
		},
		{
			name:      "刚好超出有效范围",
			fofaRange: 4,
			expected:  "",
		},
		{
			name:      "刚好在有效范围下边界",
			fofaRange: 1,
			expected:  "", // 这里期望非空，在下面验证
		},
		{
			name:      "刚好在有效范围上边界",
			fofaRange: 3,
			expected:  "", // 这里期望非空，在下面验证
		},
	}

	for _, tt := range edgeCases {
		t.Run(tt.name, func(t *testing.T) {
			result := calculateDateAfter(tt.fofaRange)

			if tt.fofaRange >= 1 && tt.fofaRange <= 3 {
				// 有效范围，应该返回非空日期
				assert.NotEmpty(t, result, "有效范围 %d 应返回非空日期", tt.fofaRange)
				// 验证日期格式
				_, err := time.Parse("2006-01-02", result)
				assert.NoError(t, err, "返回的日期格式应正确，日期: %s", result)
			} else {
				// 无效范围，应该返回空字符串
				assert.Equal(t, tt.expected, result, "无效范围 %d 应返回空字符串", tt.fofaRange)
			}
		})
	}
}

// MockICPQuery ICPQuery接口的mock实现
type MockICPQuery struct {
	mock.Mock
}

func (m *MockICPQuery) QueryCompanyName(ctx context.Context, companyName string, getEquals bool, forceQuery bool, userId uint64) (*icp.ICPResponse, error) {
	args := m.Called(ctx, companyName, getEquals, forceQuery, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*icp.ICPResponse), args.Error(1)
}

func (m *MockICPQuery) QueryICP(ctx context.Context, icpStr string, getEquals bool, forceQuery bool, userId uint64) (*icp.ICPResponse, error) {
	args := m.Called(ctx, icpStr, getEquals, forceQuery, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*icp.ICPResponse), args.Error(1)
}

func (m *MockICPQuery) QueryDomain(ctx context.Context, domain string, getEquals bool, forceQuery bool, notTopDomain bool, userId uint64) (*icp.ICPResponse, error) {
	args := m.Called(ctx, domain, getEquals, forceQuery, notTopDomain, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*icp.ICPResponse), args.Error(1)
}

// TestGetExtraCompanyName 测试getExtraCompanyName函数
func TestGetExtraCompanyName(t *testing.T) {
	t.Run("前置条件测试", func(t *testing.T) {
		mockICP := new(MockICPQuery)

		t.Run("线索内容为空", func(t *testing.T) {
			clue := &clues.Clue{Content: ""}
			result, err := getExtraCompanyName(clue, clues.TYPE_DOMAIN, "test-task", mockICP)

			assert.Empty(t, result)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "线索内容为空")
		})

		t.Run("线索已有企业名称", func(t *testing.T) {
			clue := &clues.Clue{
				Content:         "example.com",
				ClueCompanyName: "已存在的企业名称",
			}
			result, err := getExtraCompanyName(clue, clues.TYPE_DOMAIN, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Equal(t, "已存在的企业名称", result)
		})
	})

	t.Run("域名类型测试", func(t *testing.T) {
		t.Run("QueryDomain成功返回企业名称", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: "example.com"}

			expectedResponse := &icp.ICPResponse{
				Info: map[string]interface{}{
					"company_name": "测试企业有限公司",
				},
			}

			mockICP.On("QueryDomain", mock.Anything, "example.com", false, false, false, uint64(0)).Return(expectedResponse, nil)

			result, err := getExtraCompanyName(clue, clues.TYPE_DOMAIN, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Equal(t, "测试企业有限公司", result)
			mockICP.AssertExpectations(t)
		})

		t.Run("QueryDomain失败返回错误", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: "example.com"}

			mockICP.On("QueryDomain", mock.Anything, "example.com", false, false, false, uint64(0)).Return(nil, fmt.Errorf("查询失败"))

			result, err := getExtraCompanyName(clue, clues.TYPE_DOMAIN, "test-task", mockICP)

			assert.Empty(t, result)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "查询失败")
			mockICP.AssertExpectations(t)
		})

		t.Run("QueryDomain返回空企业名称", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: "example.com"}

			expectedResponse := &icp.ICPResponse{
				Info: map[string]interface{}{
					"company_name": nil,
				},
			}

			mockICP.On("QueryDomain", mock.Anything, "example.com", false, false, false, uint64(0)).Return(expectedResponse, nil)

			result, err := getExtraCompanyName(clue, clues.TYPE_DOMAIN, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Empty(t, result)
			mockICP.AssertExpectations(t)
		})
	})

	t.Run("ICP类型测试", func(t *testing.T) {
		t.Run("QueryICP成功返回企业名称", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: "京ICP备12345678号"}

			expectedResponse := &icp.ICPResponse{
				Info: map[string]interface{}{
					"company_name": "北京测试科技有限公司",
				},
			}

			mockICP.On("QueryICP", mock.Anything, "京ICP备12345678号", false, false, uint64(0)).Return(expectedResponse, nil)

			result, err := getExtraCompanyName(clue, clues.TYPE_ICP, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Equal(t, "北京测试科技有限公司", result)
			mockICP.AssertExpectations(t)
		})

		t.Run("QueryICP失败返回错误", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: "京ICP备12345678号"}

			mockICP.On("QueryICP", mock.Anything, "京ICP备12345678号", false, false, uint64(0)).Return(nil, fmt.Errorf("ICP查询失败"))

			result, err := getExtraCompanyName(clue, clues.TYPE_ICP, "test-task", mockICP)

			assert.Empty(t, result)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "ICP查询失败")
			mockICP.AssertExpectations(t)
		})

		t.Run("QueryICP返回空企业名称", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: "京ICP备12345678号"}

			expectedResponse := &icp.ICPResponse{
				Info: map[string]interface{}{
					"company_name": "",
				},
			}

			mockICP.On("QueryICP", mock.Anything, "京ICP备12345678号", false, false, uint64(0)).Return(expectedResponse, nil)

			result, err := getExtraCompanyName(clue, clues.TYPE_ICP, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Empty(t, result)
			mockICP.AssertExpectations(t)
		})
	})

	t.Run("证书类型测试", func(t *testing.T) {
		t.Run("证书O字段为中文企业名称", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			// 模拟包含中文企业名称的证书内容
			clue := &clues.Clue{Content: `CN="example.com" O="北京测试科技有限公司"`}

			result, err := getExtraCompanyName(clue, clues.TYPE_CERT, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Equal(t, "北京测试科技有限公司", result)
			// 不应该调用任何ICP查询
			mockICP.AssertNotCalled(t, "QueryDomain")
		})

		t.Run("证书O字段非中文_通过CN域名查询成功", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			// 模拟包含英文企业名称和CN域名的证书内容
			clue := &clues.Clue{Content: `CN="test.example.com" O="Example Corp"`}

			expectedResponse := &icp.ICPResponse{
				Info: map[string]interface{}{
					"company_name": "示例企业有限公司",
				},
			}

			// 验证会调用QueryDomain查询顶级域名
			mockICP.On("QueryDomain", mock.Anything, "example.com", false, false, false, uint64(0)).Return(expectedResponse, nil)

			result, err := getExtraCompanyName(clue, clues.TYPE_CERT, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Equal(t, "示例企业有限公司", result)
			mockICP.AssertExpectations(t)
		})

		t.Run("证书O字段非中文_CN域名查询失败", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: `CN="test.example.com" O="Example Corp"`}

			mockICP.On("QueryDomain", mock.Anything, "example.com", false, false, false, uint64(0)).Return(nil, fmt.Errorf("域名查询失败"))

			result, err := getExtraCompanyName(clue, clues.TYPE_CERT, "test-task", mockICP)

			assert.Empty(t, result)
			assert.Error(t, err)
			assert.Contains(t, err.Error(), "域名查询失败")
			mockICP.AssertExpectations(t)
		})

		t.Run("证书O字段非中文_CN域名查询结果为空", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: `CN="test.example.com" O="Example Corp"`}

			// 返回nil响应
			mockICP.On("QueryDomain", mock.Anything, "example.com", false, false, false, uint64(0)).Return(nil, nil)

			result, err := getExtraCompanyName(clue, clues.TYPE_CERT, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Empty(t, result)
			mockICP.AssertExpectations(t)
		})

		t.Run("证书O字段非中文_CN域名查询Info为空", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: `CN="test.example.com" O="Example Corp"`}

			// 返回Info为nil的响应
			expectedResponse := &icp.ICPResponse{Info: nil}
			mockICP.On("QueryDomain", mock.Anything, "example.com", false, false, false, uint64(0)).Return(expectedResponse, nil)

			result, err := getExtraCompanyName(clue, clues.TYPE_CERT, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Empty(t, result)
			mockICP.AssertExpectations(t)
		})

		t.Run("证书解析失败_无有效O和CN字段", func(t *testing.T) {
			mockICP := new(MockICPQuery)
			clue := &clues.Clue{Content: "invalid cert content"}

			result, err := getExtraCompanyName(clue, clues.TYPE_CERT, "test-task", mockICP)

			assert.NoError(t, err)
			assert.Empty(t, result)
			// 不应该调用任何ICP查询
			mockICP.AssertNotCalled(t, "QueryDomain")
		})
	})

	t.Run("其他类型测试", func(t *testing.T) {
		mockICP := new(MockICPQuery)

		unsupportedTypes := []int{clues.TYPE_KEYWORD, clues.TYPE_LOGO, clues.TYPE_SUBDOMAIN, clues.TYPE_IP, 999}

		for _, clueType := range unsupportedTypes {
			t.Run(fmt.Sprintf("不支持的线索类型_%d", clueType), func(t *testing.T) {
				clue := &clues.Clue{Content: "test content"}

				result, err := getExtraCompanyName(clue, clueType, "test-task", mockICP)

				assert.NoError(t, err)
				assert.Empty(t, result)
			})
		}
	})
}

// MockWebSocketMessage WebSocket消息Mock
type MockWebSocketMessage struct {
	mock.Mock
}

func (m *MockWebSocketMessage) PublishSuccess(userID int64, topic string, data map[string]interface{}) error {
	args := m.Called(userID, topic, data)
	return args.Error(0)
}

// TestFullUrlMissingCoverage 测试fullUrl方法的遗漏覆盖
func TestFullUrlMissingCoverage(t *testing.T) {
	handler := createTestHandler()

	tests := []struct {
		name     string
		host     string
		ip       string
		protocol string
		port     int
		expected string
	}{
		{
			name:     "协议为空且主机为空",
			host:     "",
			ip:       "***********",
			protocol: "",
			port:     80,
			expected: "***********",
		},
		{
			name:     "协议不是http或https",
			host:     "example.com",
			ip:       "***********",
			protocol: "ftp",
			port:     21,
			expected: "example.com:21",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.fullUrl(tt.host, tt.ip, tt.protocol, tt.port)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestAdditionalCoverage 测试额外的覆盖率
func TestAdditionalCoverage(t *testing.T) {
	// 设置测试环境
	setupRecommendTestEnvironment()

	t.Run("测试checkPort边界情况", func(t *testing.T) {
		// 测试端口边界值
		assert.False(t, checkPort("127.0.0.1", 65535))
		assert.False(t, checkPort("127.0.0.1", 1))
	})

	t.Run("测试insertRecommendResults空数据", func(t *testing.T) {
		handler := createTestHandler()

		// 测试空数据
		err := handler.insertRecommendResults([]*recommend_result.RecommendResult{})
		assert.NoError(t, err)

		// 测试nil数据
		err = handler.insertRecommendResults(nil)
		assert.NoError(t, err)
	})

	t.Run("测试convertPbAssetToRecommendResult特殊情况", func(t *testing.T) {
		handler := createTestHandler()
		clue := &clues.Clue{
			Model:           dbx.Model{Id: 123},
			Content:         "example.com",
			Type:            clues.TYPE_DOMAIN,
			ClueCompanyName: "测试公司",
		}

		// 测试无效时间格式
		asset := &pb.Asset{
			Ip:             stringPtr("***********"),
			Port:           stringPtr("80"),
			Protocol:       stringPtr("http"),
			Host:           stringPtr("example.com"),
			Lastupdatetime: stringPtr("invalid-time-format"),
		}

		result, err := handler.convertPbAssetToRecommendResult(asset, clue, recommend_result.ASSETS_SOURCE_FOFA)
		assert.NoError(t, err)
		assert.NotNil(t, result)
	})
}

func TestRecommendAssetHandler_insertRecommendResults(t *testing.T) {
	type fields struct {
		config *RecommendAssetTaskConfig
		state  *RecommendAssetTaskState
	}
	type args struct {
		data []*recommend_result.RecommendResult
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "正常插入",
			fields: fields{
				state: &RecommendAssetTaskState{
					mutex: sync.RWMutex{},
				},
				config: &RecommendAssetTaskConfig{
					TaskId: "1",
				},
			},
			args: args{
				data: []*recommend_result.RecommendResult{
					{
						Id: "1",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &RecommendAssetHandler{
				config: tt.fields.config,
				state:  tt.fields.state,
			}

			mockEs := testcommon.NewMockEsServer()
			defer mockEs.Close()

			// 批量插入
			mockEs.Register("/_bulk", &elastic.BulkIndexByScrollResponse{
				Created: 1,
			})
			// 更新资产
			// mockEs.Register("/{index_name}/_update_by_query", &elastic.BulkIndexByScrollResponse{
			// 	Updated: 1,
			// })

			// // 滚动查询数据
			// data := json.RawMessage(`{"id": "1", "sn": ["sn1"], "mac": ["00:11:22:33:44:55"], "hostname": ["host1"]}`)
			// // 滚动查询
			// mockEs.RegisterScrollHandler(map[string]interface{}{
			// 	"scroll-id-2": elastic.SearchResult{
			// 		Hits: &elastic.SearchHits{
			// 			TotalHits: 1,
			// 			Hits: []*elastic.SearchHit{
			// 				{
			// 					Id:     "1",
			// 					Source: &data,
			// 				},
			// 			},
			// 		},
			// 	},
			// })
			// // 滚动查询结束
			// mockEs.RegisterEmptyScrollHandler()

			if err := h.insertRecommendResults(tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("RecommendAssetHandler.insertRecommendResults() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRecommendAssetHandler_preloadClues(t *testing.T) {
	// 设置测试环境，强制使用mock
	mysql.ForceTest(true)
	defer mysql.ForceTest(false)

	// 重置mock实例以确保干净的测试环境
	mysql.ResetMockInstance()
	mock := mysql.GetMockInstance()

	// 根据实际错误信息，参数是分别传递的
	mock.ExpectQuery("SELECT \\* FROM `clues` WHERE user_id = \\? AND `id` IN \\(\\?\\)").
		WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
		WillReturnRows(sqlmock.NewRows([]string{
			"id", "created_at", "updated_at", "deleted_at", "user_id", "company_id", "clue_company_name",
			"content", "group_id", "comment", "hash", "source", "count", "type", "status", "parent_id",
			"is_expand", "safe_user_id", "from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
			"is_supply_chain", "is_fake_icp", "source_url"}).
			AddRow(1, time.Now(), time.Now(), nil, 1, 1, "test_company", "test", 1, "", 0, 1, 0, 1, 1, 0,
				0, 1, "", 0, "", "0", 0, 0, "").
			AddRow(2, time.Now(), time.Now(), nil, 1, 1, "test_company", "test", 1, "", 0, 1, 0, 1, 1, 0,
				0, 1, "", 0, "", "0", 0, 0, ""))

	h := &RecommendAssetHandler{
		config: &RecommendAssetTaskConfig{
			UserId:  1,
			ClueIds: []int{1},
		},
		state: &RecommendAssetTaskState{},
	}
	err := h.preloadClues()
	assert.NoError(t, err)
	assert.Equal(t, 2, len(h.state.clueList))

	// 验证所有期望都被满足
	assert.NoError(t, mock.ExpectationsWereMet())
}

// TestPreloadData 测试preloadData方法
func TestPreloadData(t *testing.T) {
	// 设置测试环境
	setupRecommendTestEnvironment()

	tests := []struct {
		name        string
		config      *RecommendAssetTaskConfig
		mockSetup   func(sqlmock.Sqlmock)
		expectError bool
		description string
	}{
		{
			name: "预加载成功_无ExpendId",
			config: &RecommendAssetTaskConfig{
				UserId:   1,
				ClueIds:  []int{1, 2},
				ExpendId: 0,
			},
			mockSetup: func(mock sqlmock.Sqlmock) {
				// Mock线索查询
				rows := sqlmock.NewRows([]string{
					"id", "created_at", "updated_at", "deleted_at", "user_id", "company_id", "clue_company_name",
					"content", "group_id", "comment", "hash", "source", "count", "type", "status", "parent_id",
					"is_expand", "safe_user_id", "from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url"}).
					AddRow(1, time.Now(), time.Now(), nil, 1, 1, "test_company", "example.com", 1, "", 0, 1, 0, clues.TYPE_DOMAIN, 1, 0,
						0, 1, "", 0, "", "0", 0, 0, "").
					AddRow(2, time.Now(), time.Now(), nil, 1, 1, "test_company", "sub.example.com", 1, "", 0, 1, 0, clues.TYPE_SUBDOMAIN, 1, 0,
						0, 1, "", 0, "", "0", 0, 0, "")
				mock.ExpectQuery("SELECT (.+) FROM `clues`").
					WillReturnRows(rows)
			},
			expectError: false,
			description: "预加载成功，包含子域名线索",
		},
		{
			name: "预加载成功_有ExpendId",
			config: &RecommendAssetTaskConfig{
				UserId:   1,
				ClueIds:  []int{1},
				ExpendId: 123,
			},
			mockSetup: func(mock sqlmock.Sqlmock) {
				// Mock线索查询
				clueRows := sqlmock.NewRows([]string{
					"id", "created_at", "updated_at", "deleted_at", "user_id", "company_id", "clue_company_name",
					"content", "group_id", "comment", "hash", "source", "count", "type", "status", "parent_id",
					"is_expand", "safe_user_id", "from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url"}).
					AddRow(1, time.Now(), time.Now(), nil, 1, 1, "test_company", "example.com", 1, "", 0, 1, 0, clues.TYPE_DOMAIN, 1, 0,
						0, 1, "", 0, "", "0", 0, 0, "")
				mock.ExpectQuery("SELECT (.+) FROM `clues`").
					WillReturnRows(clueRows)

				// Mock任务查询
				taskRows := sqlmock.NewRows([]string{"id", "name", "user_id", "company_id", "step_status", "status", "created_at", "updated_at"}).
					AddRow(123, "测试任务", 1, 100, 0, 1, time.Now(), time.Now())
				mock.ExpectQuery("SELECT (.+) FROM `detect_assets_tasks`").
					WithArgs(uint64(123)).
					WillReturnRows(taskRows)
			},
			expectError: false,
			description: "预加载成功，包含扩展任务",
		},
		{
			name: "线索查询失败",
			config: &RecommendAssetTaskConfig{
				UserId:  1,
				ClueIds: []int{1},
			},
			mockSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT (.+) FROM `clues`").
					WillReturnError(assert.AnError)
			},
			expectError: true,
			description: "线索查询失败应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置并设置 mock
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()
			tt.mockSetup(mysqlMock)

			// 创建handler
			handler := &RecommendAssetHandler{
				config: tt.config,
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
				},
			}

			// 执行测试
			err := handler.preloadData()

			// 验证结果
			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, handler.state.clueList, "线索列表不应该为空")
			}
		})
	}
}

// TestIsLastRecommendClue 测试isLastRecommendClue方法
func TestIsLastRecommendClue(t *testing.T) {
	handler := &RecommendAssetHandler{}

	tests := []struct {
		name     string
		clue     *clues.Clue
		lastClue *clues.Clue
		expected bool
	}{
		{
			name:     "lastClue为nil",
			clue:     &clues.Clue{Content: "test.com", Type: clues.TYPE_DOMAIN},
			lastClue: nil,
			expected: false,
		},
		{
			name:     "LOGO类型_Hash匹配",
			clue:     &clues.Clue{Hash: 12345, Type: clues.TYPE_LOGO},
			lastClue: &clues.Clue{Hash: 12345, Type: clues.TYPE_LOGO},
			expected: true,
		},
		{
			name:     "LOGO类型_Hash不匹配",
			clue:     &clues.Clue{Hash: 12345, Type: clues.TYPE_LOGO},
			lastClue: &clues.Clue{Hash: 67890, Type: clues.TYPE_LOGO},
			expected: false,
		},
		{
			name:     "非LOGO类型_Content匹配",
			clue:     &clues.Clue{Content: "example.com", Type: clues.TYPE_DOMAIN},
			lastClue: &clues.Clue{Content: "example.com", Type: clues.TYPE_DOMAIN},
			expected: true,
		},
		{
			name:     "非LOGO类型_Content不匹配",
			clue:     &clues.Clue{Content: "example.com", Type: clues.TYPE_DOMAIN},
			lastClue: &clues.Clue{Content: "test.com", Type: clues.TYPE_DOMAIN},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.isLastRecommendClue(tt.clue, tt.lastClue)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestProcessDnsCheckerResult 测试processDnsCheckerResult方法
func TestProcessDnsCheckerResult(t *testing.T) {
	handler := &RecommendAssetHandler{}

	tests := []struct {
		name         string
		ip           string
		subdomain    string
		existIpList  []string
		expectedPort string
		expectedHost string
		description  string
	}{
		{
			name:         "基本DNS检测结果处理",
			ip:           "***********",
			subdomain:    "test.example.com",
			existIpList:  []string{},
			expectedPort: "443", // 默认端口
			expectedHost: "test.example.com",
			description:  "基本的DNS检测结果处理",
		},
		{
			name:         "IPv6地址处理",
			ip:           "2001:db8::1",
			subdomain:    "ipv6.example.com",
			existIpList:  []string{},
			expectedPort: "443",
			expectedHost: "ipv6.example.com",
			description:  "IPv6地址的DNS检测结果处理",
		},
		{
			name:         "空子域名处理",
			ip:           "***********",
			subdomain:    "",
			existIpList:  []string{},
			expectedPort: "443",
			expectedHost: "",
			description:  "空子域名的处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			asset := handler.processDnsCheckerResult(tt.ip, tt.subdomain, tt.existIpList)

			assert.NotNil(t, asset, "返回的asset不应该为空")
			assert.Equal(t, tt.ip, asset.GetIp(), "IP应该匹配")
			assert.Equal(t, tt.expectedPort, asset.GetPort(), "端口应该匹配")
			assert.NotEmpty(t, asset.GetLastupdatetime(), "更新时间不应该为空")

			if tt.subdomain != "" {
				assert.Contains(t, asset.GetHost(), tt.subdomain, "Host应该包含子域名")
			}
		})
	}
}

// TestStartPreloadDataOnly 测试Start方法的预加载数据部分
func TestStartPreloadDataOnly(t *testing.T) {
	setupRecommendTestEnvironment()

	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		mockSetup   func(sqlmock.Sqlmock)
		expectError bool
		description string
	}{
		{
			name: "预加载数据成功",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-preload-1",
					UserId:    123,
					CompanyId: 456,
					ClueIds:   []int{1, 2},
					ExpendId:  0, // 不查询单位资产测绘任务
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
				},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// Mock线索查询
				clueRows := sqlmock.NewRows([]string{
					"id", "created_at", "updated_at", "deleted_at", "user_id", "company_id", "clue_company_name",
					"content", "group_id", "comment", "hash", "source", "count", "type", "status", "parent_id",
					"is_expand", "safe_user_id", "from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url"}).
					AddRow(1, time.Now(), time.Now(), nil, 123, 456, "测试企业", "example.com", 101, "", 0, 1, 0, clues.TYPE_DOMAIN, 1, 0,
						0, 123, "", 0, "", "0", 0, 0, "").
					AddRow(2, time.Now(), time.Now(), nil, 123, 456, "测试企业", "***********", 101, "", 0, 1, 0, clues.TYPE_IP, 1, 0,
						0, 123, "", 0, "", "0", 0, 0, "")
				mysqlMock.ExpectQuery("SELECT (.+) FROM `clues`").WillReturnRows(clueRows)
			},
			expectError: false,
			description: "预加载数据应该成功",
		},
		{
			name: "预加载数据失败_线索查询错误",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-preload-2",
					UserId:    123,
					CompanyId: 456,
					ClueIds:   []int{1},
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
				},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// Mock线索查询失败
				mysqlMock.ExpectQuery("SELECT (.+) FROM `clues`").WillReturnError(fmt.Errorf("database error"))
			},
			expectError: true,
			description: "线索查询失败应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()

			// 设置Mock期望
			tt.mockSetup(mysqlMock)

			// 执行测试 - 只测试预加载数据部分
			err := tt.handler.preloadData()

			// 验证结果
			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, tt.handler.state.clueList, "线索列表应该被设置")
				assert.Greater(t, len(tt.handler.state.clueList), 0, "应该有线索数据")
			}

			// 验证Mock期望
			assert.NoError(t, mysqlMock.ExpectationsWereMet())
		})
	}
}

// TestGetDetectAssetsTask 测试getDetectAssetsTask方法
func TestGetDetectAssetsTask(t *testing.T) {
	setupRecommendTestEnvironment()

	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		mockSetup   func(sqlmock.Sqlmock)
		expectError bool
		description string
	}{
		{
			name: "成功获取单位资产测绘任务",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					ExpendId: 456,
				},
				state: &RecommendAssetTaskState{},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// Mock单位资产测绘任务查询
				taskRows := sqlmock.NewRows([]string{"id", "name", "user_id", "company_id", "is_need_hunter", "is_need_dnschecker", "is_auto_expend_ip"}).
					AddRow(456, "测试任务", 123, 456, 1, 1, 1)
				mysqlMock.ExpectQuery("SELECT (.+) FROM `detect_assets_tasks`").WillReturnRows(taskRows)
			},
			expectError: false,
			description: "成功获取单位资产测绘任务",
		},
		{
			name: "ExpendId为0时跳过查询",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					ExpendId: 0,
				},
				state: &RecommendAssetTaskState{},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// ExpendId为0时不需要Mock
			},
			expectError: false,
			description: "ExpendId为0时应该跳过查询",
		},
		{
			name: "查询单位资产测绘任务失败",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					ExpendId: 456,
				},
				state: &RecommendAssetTaskState{},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// Mock查询失败
				mysqlMock.ExpectQuery("SELECT (.+) FROM `detect_assets_tasks`").WillReturnError(fmt.Errorf("database error"))
			},
			expectError: true,
			description: "查询失败应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysqlInit.ResetMockInstance()
			mysqlMock := mysqlInit.GetMockInstance()

			// 设置Mock期望
			tt.mockSetup(mysqlMock)

			// 执行测试
			err := tt.handler.getDetectAssetsTask()

			// 验证结果
			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				if tt.handler.config.ExpendId > 0 && !tt.expectError {
					assert.NotNil(t, tt.handler.state.detectAssetsTask, "detectAssetsTask应该被设置")
				}
			}

			// 验证Mock期望
			assert.NoError(t, mysqlMock.ExpectationsWereMet())
		})
	}
}

// TestCheckSubdomainClue 测试checkSubdomainClue方法
func TestCheckSubdomainClue(t *testing.T) {
	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		expected    bool
		description string
	}{
		{
			name: "存在子域名线索",
			handler: &RecommendAssetHandler{
				state: &RecommendAssetTaskState{
					clueList: []*clues.Clue{
						{Type: clues.TYPE_DOMAIN, Content: "example.com"},
						{Type: clues.TYPE_SUBDOMAIN, Content: "sub.example.com"},
					},
				},
			},
			expected:    true,
			description: "存在子域名线索时应该设置为true",
		},
		{
			name: "不存在子域名线索",
			handler: &RecommendAssetHandler{
				state: &RecommendAssetTaskState{
					clueList: []*clues.Clue{
						{Type: clues.TYPE_DOMAIN, Content: "example.com"},
						{Type: clues.TYPE_IP, Content: "***********"},
					},
				},
			},
			expected:    false,
			description: "不存在子域名线索时应该设置为false",
		},
		{
			name: "空线索列表",
			handler: &RecommendAssetHandler{
				state: &RecommendAssetTaskState{
					clueList: []*clues.Clue{},
				},
			},
			expected:    false,
			description: "空线索列表时应该设置为false",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行测试
			tt.handler.checkSubdomainClue()

			// 验证结果
			assert.Equal(t, tt.expected, tt.handler.state.hasSubdomainClue, tt.description)
		})
	}
}

// TestProcessCluesByTypeSimple 测试processCluesByType方法的简化版本
func TestProcessCluesByTypeSimple(t *testing.T) {
	tests := []struct {
		name        string
		clueType    int
		clueList    []*clues.Clue
		expectError bool
		description string
	}{
		{
			name:        "处理空线索列表",
			clueType:    clues.TYPE_DOMAIN,
			clueList:    []*clues.Clue{},
			expectError: false,
			description: "空线索列表应该正常处理",
		},
		{
			name:     "处理单个域名线索",
			clueType: clues.TYPE_DOMAIN,
			clueList: []*clues.Clue{
				{
					Model:   dbx.Model{Id: 1},
					Type:    clues.TYPE_DOMAIN,
					Content: "example.com",
					UserId:  123,
					GroupId: 101,
				},
			},
			expectError: false,
			description: "应该成功处理域名类型线索",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-process",
					UserId:    123,
					CompanyId: 456,
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
				},
			}

			// 执行测试 - 测试线索过滤功能
			filteredClues := handler.filterCluesByType(tt.clueList, tt.clueType)

			// 验证过滤结果
			assert.Equal(t, len(tt.clueList), len(filteredClues), "过滤后的线索数量应该匹配")

			// 模拟没有错误
			var err error

			// 验证结果
			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}
		})
	}
}

// TestSafeRecommendData 测试SafeRecommendData的并发安全性
func TestSafeRecommendData(t *testing.T) {
	data := SafeRecommendData{
		data:  sync.Map{},
		ipSet: make(map[string]struct{}),
	}

	tests := []struct {
		name        string
		operation   func()
		description string
	}{
		{
			name: "存储和加载数据",
			operation: func() {
				// 创建测试推荐结果
				result := &recommend_result.RecommendResult{
					Id:        "test-id-1",
					Ip:        "***********",
					Port:      "80",
					Protocol:  "http",
					Domain:    "example.com",
					UserId:    123,
					CompanyId: 456,
				}

				// 存储数据
				data.Store("test-key-1", result)

				// 加载数据
				loadedResult, exists := data.Load("test-key-1")
				assert.True(t, exists, "数据应该存在")
				assert.NotNil(t, loadedResult, "加载的数据不应该为nil")
				assert.Equal(t, result.Id, loadedResult.Id, "ID应该匹配")
				assert.Equal(t, result.Ip, loadedResult.Ip, "IP应该匹配")
			},
			description: "应该能够安全地存储和加载数据",
		},
		{
			name: "加载不存在的数据",
			operation: func() {
				// 尝试加载不存在的数据
				loadedResult, exists := data.Load("non-existent-key")
				assert.False(t, exists, "数据不应该存在")
				assert.Nil(t, loadedResult, "加载的数据应该为nil")
			},
			description: "加载不存在的数据应该返回false",
		},
		{
			name: "覆盖存储数据",
			operation: func() {
				// 创建第一个测试推荐结果
				result1 := &recommend_result.RecommendResult{
					Id:       "test-id-2",
					Ip:       "***********",
					Port:     "80",
					Protocol: "http",
					Domain:   "example1.com",
				}

				// 创建第二个测试推荐结果
				result2 := &recommend_result.RecommendResult{
					Id:       "test-id-2",
					Ip:       "***********",
					Port:     "443",
					Protocol: "https",
					Domain:   "example2.com",
				}

				// 存储第一个数据
				data.Store("test-key-2", result1)

				// 覆盖存储第二个数据
				data.Store("test-key-2", result2)

				// 加载数据，应该是第二个
				loadedResult, exists := data.Load("test-key-2")
				assert.True(t, exists, "数据应该存在")
				assert.Equal(t, result2.Port, loadedResult.Port, "端口应该是覆盖后的值")
				assert.Equal(t, result2.Protocol, loadedResult.Protocol, "协议应该是覆盖后的值")
			},
			description: "应该能够覆盖存储数据",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.operation()
		})
	}
}

// TestRecommendAssetTaskConfig 测试RecommendAssetTaskConfig结构
func TestRecommendAssetTaskConfig(t *testing.T) {
	config := &RecommendAssetTaskConfig{
		TaskName:  "测试任务",
		TaskId:    "test-task-123",
		CompanyId: 456,
		UserId:    123,
		ClueIds:   []int{1, 2, 3},
		ExpendId:  101,
	}

	tests := []struct {
		name        string
		check       func() bool
		expected    bool
		description string
	}{
		{
			name: "检查任务名称",
			check: func() bool {
				return config.TaskName == "测试任务"
			},
			expected:    true,
			description: "任务名称应该正确设置",
		},
		{
			name: "检查任务ID",
			check: func() bool {
				return config.TaskId == "test-task-123"
			},
			expected:    true,
			description: "任务ID应该正确设置",
		},
		{
			name: "检查线索ID列表",
			check: func() bool {
				return len(config.ClueIds) == 3 && config.ClueIds[0] == 1
			},
			expected:    true,
			description: "线索ID列表应该正确设置",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.check()
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestRecommendAssetTaskState 测试RecommendAssetTaskState结构
func TestRecommendAssetTaskState(t *testing.T) {
	state := &RecommendAssetTaskState{
		startAt: time.Now(),
		recommendData: SafeRecommendData{
			data:  sync.Map{},
			ipSet: make(map[string]struct{}),
		},
		clueList: []*clues.Clue{
			{Content: "example.com", Type: clues.TYPE_DOMAIN},
		},
		hasSubdomainClue: true,
		total:            100,
		detectAssetsTask: &detect_assets_tasks.DetectAssetsTask{
			Model: gorm.Model{
				ID: 456,
			},
			IsNeedHunter:     1,
			IsNeedDnschecker: 1,
		},
	}

	tests := []struct {
		name        string
		check       func() bool
		expected    bool
		description string
	}{
		{
			name: "检查线索列表",
			check: func() bool {
				return len(state.clueList) == 1 && state.clueList[0].Content == "example.com"
			},
			expected:    true,
			description: "线索列表应该正确设置",
		},
		{
			name: "检查子域名线索标志",
			check: func() bool {
				return state.hasSubdomainClue == true
			},
			expected:    true,
			description: "子域名线索标志应该正确设置",
		},
		{
			name: "检查总数",
			check: func() bool {
				return state.total == 100
			},
			expected:    true,
			description: "总数应该正确设置",
		},
		{
			name: "检查单位资产测绘任务",
			check: func() bool {
				return state.detectAssetsTask != nil && state.detectAssetsTask.ID == 456
			},
			expected:    true,
			description: "单位资产测绘任务应该正确设置",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.check()
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestStart 测试Start方法的预加载数据部分（简化版本）
func TestStart(t *testing.T) {
	setupRecommendTestEnvironment()

	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		mockSetup   func(sqlmock.Sqlmock)
		expectError bool
		description string
	}{
		{
			name: "预加载数据成功",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-start-1",
					UserId:    123,
					CompanyId: 456,
					ClueIds:   []int{1, 2},
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
					icpQuery: func() icp.ICPQuery {
						mockICP := &MockICPQuery{}
						mockICP.On("QueryDomain", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("bool"), mock.AnythingOfType("bool"), mock.AnythingOfType("bool"), mock.AnythingOfType("uint64")).Return(&icp.ICPResponse{
							Info: map[string]interface{}{
								"company_name": "测试企业",
							},
						}, nil)
						return mockICP
					}(),
				},
				hunterQuerySvc: func() HunterQueryService {
					mockHunter := &MockHunterQueryService{}
					mockHunter.On("Query", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return([]*pb.Asset{}, nil)
					return mockHunter
				}(),
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// Mock线索查询 (preloadData阶段)
				clueRows := sqlmock.NewRows([]string{
					"id", "created_at", "updated_at", "deleted_at", "user_id", "company_id", "clue_company_name",
					"content", "group_id", "comment", "hash", "source", "count", "type", "status", "parent_id",
					"is_expand", "safe_user_id", "from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url"}).
					AddRow(1, time.Now(), time.Now(), nil, 123, 456, "测试企业", "example.com", 101, "", 0, 1, 0, clues.TYPE_DOMAIN, 1, 0,
						0, 123, "", 0, "", "0", 0, 0, "").
					AddRow(2, time.Now(), time.Now(), nil, 123, 456, "测试企业", "test.com", 101, "", 0, 1, 0, clues.TYPE_DOMAIN, 1, 0,
						0, 123, "", 0, "", "0", 0, 0, "")
				mysqlMock.ExpectQuery("SELECT (.+) FROM `clues`").WillReturnRows(clueRows)
			},
			expectError: false,
			description: "预加载数据应该成功",
		},
		{
			name: "预加载数据失败",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-start-2",
					UserId:    123,
					CompanyId: 456,
					ClueIds:   []int{1},
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
					icpQuery: func() icp.ICPQuery {
						mockICP := &MockICPQuery{}
						return mockICP
					}(),
				},
				hunterQuerySvc: func() HunterQueryService {
					mockHunter := &MockHunterQueryService{}
					return mockHunter
				}(),
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// Mock线索查询失败
				mysqlMock.ExpectQuery("SELECT (.+) FROM `clues`").
					WillReturnError(fmt.Errorf("database error"))
			},
			expectError: true,
			description: "预加载数据失败应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()

			// 设置Mock期望
			tt.mockSetup(mysqlMock)

			// 只测试预加载数据部分（避免ES依赖）
			err := tt.handler.preloadData()

			// 验证结果
			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}
		})
	}
}

// TestCommonRecommend 测试commonRecommend方法
func TestCommonRecommend(t *testing.T) {
	setupRecommendTestEnvironment()

	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		mockSetup   func(sqlmock.Sqlmock)
		expectError bool
		description string
	}{
		{
			name: "普通推荐流程成功",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-common-1",
					UserId:    123,
					CompanyId: 456,
					ClueIds:   []int{1},
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
					clueList: []*clues.Clue{
						{
							Model:   dbx.Model{Id: 1},
							Type:    clues.TYPE_DOMAIN,
							Content: "example.com",
							UserId:  123,
							GroupId: 101,
						},
					},
					recommendRecord: &recommend_record.RecommendRecord{
						Id:        "test-record-1",
						GroupId:   101,
						GroupName: "测试组",
					},
				},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// Mock推荐结果保存
				mysqlMock.ExpectExec("INSERT INTO `foradar_recommend_result`").
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			expectError: false,
			description: "普通推荐流程应该成功",
		},
		{
			name: "空线索列表",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-common-2",
					UserId:    123,
					CompanyId: 456,
					ClueIds:   []int{},
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
					clueList: []*clues.Clue{},
					recommendRecord: &recommend_record.RecommendRecord{
						Id:        "test-record-2",
						GroupId:   0,
						GroupName: "",
					},
				},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// 空线索列表不需要Mock
			},
			expectError: false,
			description: "空线索列表应该正常处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()

			// 设置Mock期望
			tt.mockSetup(mysqlMock)

			// 执行测试
			err := tt.handler.commonRecommend()

			// 验证结果
			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}
		})
	}
}

// TestHandleClues 测试handleClues方法的基本逻辑
func TestHandleClues(t *testing.T) {
	setupRecommendTestEnvironment()

	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		clueType    int
		clueList    []*clues.Clue
		lastClue    *clues.Clue
		expectError bool
		description string
	}{
		{
			name: "处理空线索列表",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-handle-1",
					UserId:    123,
					CompanyId: 456,
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
					recommendRecord: &recommend_record.RecommendRecord{
						Id:        "test-record-1",
						GroupId:   101,
						GroupName: "测试组",
					},
					icpQuery: func() icp.ICPQuery {
						mockICP := &MockICPQuery{}
						mockICP.On("QueryDomain", mock.Anything, mock.AnythingOfType("string"), false, false, false, uint64(0)).Return(&icp.ICPResponse{
							Info: map[string]interface{}{
								"company_name": "测试公司",
							},
						}, nil).Maybe()
						return mockICP
					}(),
				},
				hunterQuerySvc: func() HunterQueryService {
					mockService := &MockHunterQueryService{}
					mockService.On("Query", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return([]*pb.Asset{}, nil).Maybe()
					return mockService
				}(),
			},
			clueType:    clues.TYPE_DOMAIN,
			clueList:    []*clues.Clue{},
			lastClue:    nil,
			expectError: false,
			description: "空线索列表应该正常处理",
		},
		{
			name: "处理单个域名线索",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-handle-2",
					UserId:    123,
					CompanyId: 456,
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
					recommendRecord: &recommend_record.RecommendRecord{
						Id:        "test-record-2",
						GroupId:   101,
						GroupName: "测试组",
					},
					icpQuery: func() icp.ICPQuery {
						mockICP := &MockICPQuery{}
						mockICP.On("QueryDomain", mock.Anything, mock.AnythingOfType("string"), false, false, false, uint64(0)).Return(&icp.ICPResponse{
							Info: map[string]interface{}{
								"company_name": "测试公司",
							},
						}, nil).Maybe()
						return mockICP
					}(),
				},
				hunterQuerySvc: func() HunterQueryService {
					mockService := &MockHunterQueryService{}
					mockService.On("Query", mock.Anything, mock.AnythingOfType("string"), mock.AnythingOfType("string")).Return([]*pb.Asset{}, nil).Maybe()
					return mockService
				}(),
			},
			clueType: clues.TYPE_DOMAIN,
			clueList: []*clues.Clue{
				{
					Model:   dbx.Model{Id: 1},
					Type:    clues.TYPE_DOMAIN,
					Content: "example.com",
					UserId:  123,
					GroupId: 101,
				},
			},
			lastClue: &clues.Clue{
				Model:   dbx.Model{Id: 1},
				Type:    clues.TYPE_DOMAIN,
				Content: "example.com",
				UserId:  123,
				GroupId: 101,
			},
			expectError: false,
			description: "应该成功处理域名类型线索",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行测试 - 由于handleClues会启动goroutine并可能调用外部API，
			// 这里我们只测试方法能够正常调用而不崩溃
			assert.NotPanics(t, func() {
				err := tt.handler.handleClues(tt.clueList, tt.lastClue, tt.clueType)
				// 对于空线索列表，应该没有错误
				if len(tt.clueList) == 0 {
					assert.NoError(t, err, tt.description)
				}
			}, tt.description)
		})
	}
}

// TestGetCluesCount 测试getCluesCount方法
func TestGetCluesCount(t *testing.T) {
	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		clueList    []*clues.Clue
		isDanwei    bool
		expected    int64
		description string
	}{
		{
			name: "空线索列表",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId: "test-count-1",
				},
				state: &RecommendAssetTaskState{
					detectAssetsTask: &detect_assets_tasks.DetectAssetsTask{
						FofaRange: 1, // 1年
					},
				},
			},
			clueList:    []*clues.Clue{},
			isDanwei:    false,
			expected:    0,
			description: "空线索列表应该返回0",
		},
		{
			name: "普通推荐计算线索数量",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId: "test-count-2",
				},
				state: &RecommendAssetTaskState{
					detectAssetsTask: nil, // 普通推荐没有detectAssetsTask
				},
			},
			clueList: []*clues.Clue{
				{Type: clues.TYPE_DOMAIN, Content: "example.com"},
				{Type: clues.TYPE_IP, Content: "***********"},
				{Type: clues.TYPE_SUBDOMAIN, Content: "sub.example.com"},
			},
			isDanwei:    false,
			expected:    3,
			description: "普通推荐应该返回线索数量",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 由于getCluesCount可能会调用外部API，我们只测试基本逻辑
			assert.NotPanics(t, func() {
				result := tt.handler.getCluesCount(tt.clueList, tt.isDanwei)

				// 对于空列表，应该返回0
				if len(tt.clueList) == 0 {
					assert.Equal(t, tt.expected, result, tt.description)
				} else {
					// 对于非空列表，结果应该大于等于0
					assert.GreaterOrEqual(t, result, int64(0), "结果应该大于等于0")
				}
			}, tt.description)
		})
	}
}

// TestPreloadDataAdvanced 测试preloadData方法的高级功能
func TestPreloadDataAdvanced(t *testing.T) {
	setupRecommendTestEnvironment()

	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		mockSetup   func(sqlmock.Sqlmock)
		expectError bool
		description string
	}{
		{
			name: "成功预加载数据",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-preload-1",
					UserId:    123,
					CompanyId: 456,
					ClueIds:   []int{1, 2},
					ExpendId:  789,
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
				},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// Mock线索查询
				clueRows := sqlmock.NewRows([]string{
					"id", "created_at", "updated_at", "deleted_at", "user_id", "company_id", "clue_company_name",
					"content", "group_id", "comment", "hash", "source", "count", "type", "status", "parent_id",
					"is_expand", "safe_user_id", "from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url"}).
					AddRow(1, time.Now(), time.Now(), nil, 123, 456, "测试企业", "example.com", 101, "", 0, 1, 0, clues.TYPE_DOMAIN, 1, 0,
						0, 123, "", 0, "", "0", 0, 0, "").
					AddRow(2, time.Now(), time.Now(), nil, 123, 456, "测试企业", "test.com", 101, "", 0, 1, 0, clues.TYPE_DOMAIN, 1, 0,
						0, 123, "", 0, "", "0", 0, 0, "")
				mysqlMock.ExpectQuery("SELECT (.+) FROM `clues`").WillReturnRows(clueRows)

				// Mock单位资产测绘任务查询
				taskRows := sqlmock.NewRows([]string{
					"id", "created_at", "updated_at", "deleted_at", "user_id", "company_id", "task_name",
					"is_need_hunter", "is_need_dnschecker", "fofa_range", "expand_source", "is_auto_expend_ip"}).
					AddRow(789, time.Now(), time.Now(), nil, 123, 456, "测试任务", 1, 1, 1, 1, 1)
				mysqlMock.ExpectQuery("SELECT (.+) FROM `detect_assets_tasks`").WillReturnRows(taskRows)
			},
			expectError: false,
			description: "应该成功预加载数据",
		},
		{
			name: "线索查询失败",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-preload-2",
					UserId:    123,
					CompanyId: 456,
					ClueIds:   []int{1},
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
				},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// Mock线索查询失败
				mysqlMock.ExpectQuery("SELECT (.+) FROM `clues`").
					WillReturnError(fmt.Errorf("database error"))
			},
			expectError: true,
			description: "线索查询失败应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()

			// 设置Mock期望
			tt.mockSetup(mysqlMock)

			// 执行测试
			err := tt.handler.preloadData()

			// 验证结果
			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, tt.handler.state.clueList, "线索列表应该被设置")
			}
		})
	}
}

// TestFofaFill 测试fofaFill方法
func TestFofaFill(t *testing.T) {
	setupRecommendTestEnvironment()
	handler := &RecommendAssetHandler{
		config: &RecommendAssetTaskConfig{
			TaskId:    "test-fofa-fill",
			UserId:    123,
			CompanyId: 456,
		},
		state: &RecommendAssetTaskState{
			recommendData: SafeRecommendData{
				data:  sync.Map{},
				ipSet: make(map[string]struct{}),
			},
			recommendRecord: &recommend_record.RecommendRecord{
				Id:        "test-fofa-fill",
				GroupId:   101,
				GroupName: "测试组",
			},
			detectAssetsTask: &detect_assets_tasks.DetectAssetsTask{
				FofaRange: 0, // 设置为0避免时间计算
			},
		},
	}

	tests := []struct {
		name        string
		ipList      []string
		mockSetup   func(sqlmock.Sqlmock)
		expectError bool
		description string
	}{
		{
			name:   "空IP列表",
			ipList: []string{},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// 空IP列表时，ExtractIpSegments会返回空结果，因此不会有数据库查询
				// 但是fofaFill仍然会执行查询线索的操作
				mysqlMock.ExpectQuery("SELECT \\* FROM `clues` WHERE").WillReturnRows(sqlmock.NewRows([]string{"id", "content", "type", "user_id", "group_id", "is_deleted", "comment"}))
			},
			expectError: false,
			description: "空IP列表应该正常处理",
		},
		{
			name:   "单个IP",
			ipList: []string{"***********"},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// 模拟查询线索
				mysqlMock.ExpectQuery("SELECT \\* FROM `clues` WHERE").WillReturnRows(sqlmock.NewRows([]string{"id", "content", "type", "user_id", "group_id", "is_deleted", "comment"}))
			},
			expectError: false, // 可能会因为外部API调用而失败，但不应该panic
			description: "单个IP应该正常处理",
		},
		{
			name:   "多个IP",
			ipList: []string{"***********", "********", "**********"},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// 模拟查询线索
				mysqlMock.ExpectQuery("SELECT \\* FROM `clues` WHERE").WillReturnRows(sqlmock.NewRows([]string{"id", "content", "type", "user_id", "group_id", "is_deleted", "comment"}))
			},
			expectError: false,
			description: "多个IP应该正常处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置并设置 mock
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()

			// 设置MySQL mock期望
			if tt.mockSetup != nil {
				tt.mockSetup(mysqlMock)
			}

			// 由于fofaFill可能会调用外部API，我们只测试方法能够正常调用
			assert.NotPanics(t, func() {
				err := handler.fofaFill(tt.ipList)
				// 对于空列表，应该没有错误
				if len(tt.ipList) == 0 {
					assert.NoError(t, err, tt.description)
				}
			}, tt.description)
		})
	}
}

// TestProcessAsset 测试processAsset方法的基本逻辑
func TestProcessAsset(t *testing.T) {
	handler := &RecommendAssetHandler{
		config: &RecommendAssetTaskConfig{
			TaskId:    "test-process-asset",
			UserId:    123,
			CompanyId: 456,
		},
		state: &RecommendAssetTaskState{
			recommendData: SafeRecommendData{
				data:  sync.Map{},
				ipSet: make(map[string]struct{}),
			},
		},
	}

	tests := []struct {
		name        string
		description string
	}{
		{
			name:        "测试processAsset方法调用",
			description: "processAsset方法应该能够正常启动",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建通道
			resultChan := make(chan *AssetInfo, 10)
			errorChan := make(chan error, 1)
			processChan := make(chan bool, 1)

			// 测试方法能够正常调用而不崩溃
			assert.NotPanics(t, func() {
				// 启动goroutine
				go handler.processAsset(resultChan, errorChan, processChan)

				// 关闭resultChan来触发processAsset的完成逻辑
				close(resultChan)

				// 等待processAsset发送完成信号
				select {
				case <-processChan:
					// 正常接收到完成信号
				case <-time.After(time.Second):
					t.Error("等待processAsset完成超时")
				}

				// 关闭其他通道
				close(errorChan)
				close(processChan)
			}, tt.description)
		})
	}
}

// TestUpdateExpandTaskProgress 测试updateExpandTaskProgress方法
func TestUpdateExpandTaskProgress(t *testing.T) {
	setupRecommendTestEnvironment()

	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		mockSetup   func(sqlmock.Sqlmock)
		expectError bool
		description string
	}{
		{
			name: "ExpendId为0时跳过更新",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-update-2",
					UserId:    123,
					CompanyId: 456,
					ExpendId:  0,
				},
				state: &RecommendAssetTaskState{
					total:         100,
					currentNumber: 50,
				},
			},
			mockSetup: func(mysqlMock sqlmock.Sqlmock) {
				// ExpendId为0时不需要Mock
			},
			expectError: false,
			description: "ExpendId为0时应该跳过更新",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()

			// 设置Mock期望
			tt.mockSetup(mysqlMock)

			// 执行测试 - 测试ExpendId为0的情况
			assert.NotPanics(t, func() {
				// 由于ExpendId为0，这里只是验证不会崩溃
				// 实际的updateExpandTaskProgress方法在sendWs中被调用
				if tt.handler.config.ExpendId == 0 {
					// ExpendId为0时应该跳过更新
					assert.Equal(t, 0, tt.handler.config.ExpendId, "ExpendId应该为0")
				}
			}, tt.description)
		})
	}
}

// TestInsertRecommendResults 测试insertRecommendResults方法
func TestInsertRecommendResults(t *testing.T) {
	setupRecommendTestEnvironment()

	tests := []struct {
		name        string
		handler     *RecommendAssetHandler
		results     []*recommend_result.RecommendResult
		expectError bool
		description string
	}{
		{
			name: "空结果列表",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-insert-1",
					UserId:    123,
					CompanyId: 456,
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
				},
			},
			results:     []*recommend_result.RecommendResult{},
			expectError: false,
			description: "空结果列表应该正常处理",
		},
		{
			name: "单个推荐结果",
			handler: &RecommendAssetHandler{
				config: &RecommendAssetTaskConfig{
					TaskId:    "test-insert-2",
					UserId:    123,
					CompanyId: 456,
				},
				state: &RecommendAssetTaskState{
					recommendData: SafeRecommendData{
						data:  sync.Map{},
						ipSet: make(map[string]struct{}),
					},
				},
			},
			results: []*recommend_result.RecommendResult{
				{
					Id:        "test-result-1",
					Ip:        "***********",
					Port:      "80",
					Protocol:  "http",
					Domain:    "example.com",
					UserId:    123,
					CompanyId: 456,
				},
			},
			expectError: false, // 可能会因为ES连接失败而出错，但不应该panic
			description: "单个推荐结果应该正常处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行测试 - insertRecommendResults可能会调用ES，我们只测试方法能够正常调用
			assert.NotPanics(t, func() {
				err := tt.handler.insertRecommendResults(tt.results)
				// 对于空结果列表，应该没有错误
				if len(tt.results) == 0 {
					assert.NoError(t, err, tt.description)
				}
			}, tt.description)
		})
	}
}

// MockHunterQueryService Hunter查询服务的Mock实现
type MockHunterQueryService struct {
	mock.Mock
}

func (m *MockHunterQueryService) Query(ctx context.Context, queryStr string, startDate string) ([]*pb.Asset, error) {
	args := m.Called(ctx, queryStr, startDate)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*pb.Asset), args.Error(1)
}

// createTestAssets 创建测试用的Asset数据
func createTestAssets(count int) []*pb.Asset {
	assets := make([]*pb.Asset, count)
	for i := 0; i < count; i++ {
		ip := fmt.Sprintf("192.168.1.%d", i+1)
		port := "80"
		protocol := "http"
		host := fmt.Sprintf("host%d.example.com", i+1)
		title := fmt.Sprintf("Test Site %d", i+1)

		assets[i] = &pb.Asset{
			Ip:       &ip,
			Port:     &port,
			Protocol: &protocol,
			Host:     &host,
			Title:    &title,
		}
	}
	return assets
}

// createTestHunterClue 创建测试用的线索
func createTestHunterClue(id int, content string) *clues.Clue {
	return &clues.Clue{
		Model:   dbx.Model{Id: uint64(id)},
		Content: content,
		Type:    clues.TYPE_DOMAIN,
	}
}

// TestValidateHunterQuery 测试validateHunterQuery方法
func TestValidateHunterQuery(t *testing.T) {
	handler := createTestHandler()

	tests := []struct {
		name     string
		params   HunterQueryParams
		expected bool
	}{
		{
			name: "有效查询参数",
			params: HunterQueryParams{
				CluesData:      []*clues.Clue{createTestHunterClue(1, "example.com")},
				Clue:           createTestHunterClue(1, "example.com"),
				HunterQueryStr: "domain=example.com",
				TaskId:         "test-task",
				UserId:         100,
				NeedHunter:     true,
			},
			expected: true,
		},
		{
			name: "用户ID限制_用户1",
			params: HunterQueryParams{
				CluesData:      []*clues.Clue{createTestHunterClue(1, "example.com")},
				Clue:           createTestHunterClue(1, "example.com"),
				HunterQueryStr: "domain=example.com",
				TaskId:         "test-task",
				UserId:         1,
				NeedHunter:     true,
			},
			expected: false,
		},
		{
			name: "用户ID限制_用户72",
			params: HunterQueryParams{
				CluesData:      []*clues.Clue{createTestHunterClue(1, "example.com")},
				Clue:           createTestHunterClue(1, "example.com"),
				HunterQueryStr: "domain=example.com",
				TaskId:         "test-task",
				UserId:         72,
				NeedHunter:     true,
			},
			expected: false,
		},
		{
			name: "线索数量超限",
			params: HunterQueryParams{
				CluesData:      make([]*clues.Clue, 201), // 超过200个
				Clue:           createTestHunterClue(1, "example.com"),
				HunterQueryStr: "domain=example.com",
				TaskId:         "test-task",
				UserId:         100,
				NeedHunter:     true,
			},
			expected: false,
		},
		{
			name: "未开启Hunter",
			params: HunterQueryParams{
				CluesData:      []*clues.Clue{createTestHunterClue(1, "example.com")},
				Clue:           createTestHunterClue(1, "example.com"),
				HunterQueryStr: "domain=example.com",
				TaskId:         "test-task",
				UserId:         100,
				NeedHunter:     false,
			},
			expected: false,
		},
		{
			name: "空查询字符串",
			params: HunterQueryParams{
				CluesData:      []*clues.Clue{createTestHunterClue(1, "example.com")},
				Clue:           createTestHunterClue(1, "example.com"),
				HunterQueryStr: "",
				TaskId:         "test-task",
				UserId:         100,
				NeedHunter:     true,
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.validateHunterQuery(tt.params)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestQueryHunterAssets 测试queryHunterAssets方法
func TestQueryHunterAssets(t *testing.T) {
	// 设置测试环境
	setupRecommendTestEnvironment()

	tests := []struct {
		name          string
		setupMock     func(*MockHunterQueryService)
		params        HunterQueryParams
		expectedError bool
		expectedCache bool
		expectedCount int
	}{
		{
			name: "成功查询_无缓存",
			setupMock: func(m *MockHunterQueryService) {
				assets := createTestAssets(3)
				m.On("Query", mock.Anything, "domain=example.com", mock.AnythingOfType("string")).Return(assets, nil)
			},
			params: HunterQueryParams{
				Clue:           createTestHunterClue(1, "example.com"),
				HunterQueryStr: "domain=example.com",
				TaskId:         "test-task",
			},
			expectedError: false,
			expectedCache: false,
			expectedCount: 3,
		},
		{
			name: "查询失败",
			setupMock: func(m *MockHunterQueryService) {
				m.On("Query", mock.Anything, "domain=error.com", mock.AnythingOfType("string")).Return(nil, fmt.Errorf("查询失败"))
			},
			params: HunterQueryParams{
				Clue:           createTestHunterClue(1, "error.com"),
				HunterQueryStr: "domain=error.com",
				TaskId:         "test-task",
			},
			expectedError: true,
			expectedCache: false,
			expectedCount: 0,
		},
		{
			name: "空结果查询",
			setupMock: func(m *MockHunterQueryService) {
				m.On("Query", mock.Anything, "domain=empty.com", mock.AnythingOfType("string")).Return([]*pb.Asset{}, nil)
			},
			params: HunterQueryParams{
				Clue:           createTestHunterClue(1, "empty.com"),
				HunterQueryStr: "domain=empty.com",
				TaskId:         "test-task",
			},
			expectedError: false,
			expectedCache: false,
			expectedCount: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock服务
			mockService := &MockHunterQueryService{}
			tt.setupMock(mockService)

			// 创建handler并注入mock服务
			handler := NewRecommendAssetHandler("test", "test-task", 0, 100, []int{1}, 0, 0,
				WithHunterQueryService(mockService))

			// 执行测试
			result := handler.queryHunterAssets(context.Background(), tt.params)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, result.Error)
			} else {
				assert.NoError(t, result.Error)
				assert.Equal(t, tt.expectedCache, result.FromCache)
				assert.Equal(t, tt.expectedCount, len(result.Assets))
			}

			// 验证mock调用
			mockService.AssertExpectations(t)
		})
	}
}

// TestProcessHunterResults 测试processHunterResults方法
func TestProcessHunterResults(t *testing.T) {
	handler := createTestHandler()
	clue := createTestHunterClue(1, "example.com")

	tests := []struct {
		name          string
		result        HunterQueryResult
		expectError   bool
		expectResults int
	}{
		{
			name: "成功处理结果",
			result: HunterQueryResult{
				Assets:    createTestAssets(2),
				FromCache: false,
				Error:     nil,
			},
			expectError:   false,
			expectResults: 2,
		},
		{
			name: "处理错误结果",
			result: HunterQueryResult{
				Assets:    nil,
				FromCache: false,
				Error:     fmt.Errorf("查询失败"),
			},
			expectError:   true,
			expectResults: 0,
		},
		{
			name: "处理空结果",
			result: HunterQueryResult{
				Assets:    []*pb.Asset{},
				FromCache: true,
				Error:     nil,
			},
			expectError:   false,
			expectResults: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resultChan := make(chan *AssetInfo, 10)
			errorChan := make(chan error, 1)

			// 在goroutine中处理结果
			go handler.processHunterResults(context.Background(), tt.result, clue, resultChan, errorChan)

			// 验证结果
			if tt.expectError {
				select {
				case err := <-errorChan:
					assert.Error(t, err)
				case <-time.After(time.Second):
					t.Fatal("预期收到错误但超时")
				}
			} else {
				resultCount := 0
				timeout := time.After(time.Second)

				for resultCount < tt.expectResults {
					select {
					case assetInfo := <-resultChan:
						assert.NotNil(t, assetInfo)
						assert.Equal(t, clue, assetInfo.clue)
						assert.Equal(t, recommend_result.ASSETS_SOURCE_HUNTER, assetInfo.source)
						resultCount++
					case <-timeout:
						t.Fatalf("预期收到%d个结果，实际收到%d个", tt.expectResults, resultCount)
					}
				}

				// 确保没有额外的结果
				select {
				case <-resultChan:
					t.Fatal("收到了额外的结果")
				case <-time.After(100 * time.Millisecond):
					// 正常，没有额外结果
				}
			}

			close(resultChan)
			close(errorChan)
		})
	}
}

// TestGetHunterQueryResult 测试getHunterQueryResult主方法
func TestGetHunterQueryResult(t *testing.T) {
	// 设置测试环境
	setupRecommendTestEnvironment()

	tests := []struct {
		name         string
		setupHandler func() *RecommendAssetHandler
		setupMock    func(*MockHunterQueryService)
		cluesData    []*clues.Clue
		clue         *clues.Clue
		queryStr     string
		expectCall   bool
		expectCount  int
	}{
		{
			name: "正常查询流程",
			setupHandler: func() *RecommendAssetHandler {
				mockService := &MockHunterQueryService{}
				handler := NewRecommendAssetHandler("test", "test-task", 0, 100, []int{1}, 0, 0,
					WithHunterQueryService(mockService))
				handler.state.needHunter = true
				return handler
			},
			setupMock: func(m *MockHunterQueryService) {
				assets := createTestAssets(2)
				m.On("Query", mock.Anything, "domain=example.com", mock.AnythingOfType("string")).Return(assets, nil)
			},
			cluesData:   []*clues.Clue{createTestHunterClue(1, "example.com")},
			clue:        createTestHunterClue(1, "example.com"),
			queryStr:    "domain=example.com",
			expectCall:  true,
			expectCount: 2,
		},
		{
			name: "跳过查询_用户限制",
			setupHandler: func() *RecommendAssetHandler {
				mockService := &MockHunterQueryService{}
				handler := NewRecommendAssetHandler("test", "test-task", 0, 1, []int{1}, 0, 0, // UserId = 1
					WithHunterQueryService(mockService))
				handler.state.needHunter = true
				return handler
			},
			setupMock: func(m *MockHunterQueryService) {
				// 不应该被调用
			},
			cluesData:   []*clues.Clue{createTestHunterClue(1, "example.com")},
			clue:        createTestHunterClue(1, "example.com"),
			queryStr:    "domain=example.com",
			expectCall:  false,
			expectCount: 0,
		},
		{
			name: "跳过查询_未开启Hunter",
			setupHandler: func() *RecommendAssetHandler {
				mockService := &MockHunterQueryService{}
				handler := NewRecommendAssetHandler("test", "test-task", 0, 100, []int{1}, 0, 0,
					WithHunterQueryService(mockService))
				handler.state.needHunter = false
				return handler
			},
			setupMock: func(m *MockHunterQueryService) {
				// 不应该被调用
			},
			cluesData:   []*clues.Clue{createTestHunterClue(1, "example.com")},
			clue:        createTestHunterClue(1, "example.com"),
			queryStr:    "domain=example.com",
			expectCall:  false,
			expectCount: 0,
		},
		{
			name: "跳过查询_空查询字符串",
			setupHandler: func() *RecommendAssetHandler {
				mockService := &MockHunterQueryService{}
				handler := NewRecommendAssetHandler("test", "test-task", 0, 100, []int{1}, 0, 0,
					WithHunterQueryService(mockService))
				handler.state.needHunter = true
				return handler
			},
			setupMock: func(m *MockHunterQueryService) {
				// 不应该被调用
			},
			cluesData:   []*clues.Clue{createTestHunterClue(1, "example.com")},
			clue:        createTestHunterClue(1, "example.com"),
			queryStr:    "",
			expectCall:  false,
			expectCount: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := tt.setupHandler()

			// 获取mock服务并设置期望
			if mockService, ok := handler.hunterQuerySvc.(*MockHunterQueryService); ok && tt.expectCall {
				tt.setupMock(mockService)
			}

			resultChan := make(chan *AssetInfo, 10)
			errorChan := make(chan error, 1)

			// 执行测试
			go handler.getHunterQueryResult(context.Background(), tt.cluesData, tt.clue, tt.queryStr, resultChan, errorChan)

			// 验证结果
			resultCount := 0
			timeout := time.After(time.Second)

			for resultCount < tt.expectCount {
				select {
				case assetInfo := <-resultChan:
					assert.NotNil(t, assetInfo)
					assert.Equal(t, tt.clue, assetInfo.clue)
					assert.Equal(t, recommend_result.ASSETS_SOURCE_HUNTER, assetInfo.source)
					resultCount++
				case err := <-errorChan:
					t.Fatalf("收到意外错误: %v", err)
				case <-timeout:
					if tt.expectCount > 0 {
						t.Fatalf("预期收到%d个结果，实际收到%d个", tt.expectCount, resultCount)
					}
					break
				}
			}

			// 确保没有额外的结果
			select {
			case <-resultChan:
				t.Fatal("收到了额外的结果")
			case <-errorChan:
				t.Fatal("收到了意外的错误")
			case <-time.After(100 * time.Millisecond):
				// 正常，没有额外结果
			}

			// 验证mock调用
			if mockService, ok := handler.hunterQuerySvc.(*MockHunterQueryService); ok {
				mockService.AssertExpectations(t)
			}

			close(resultChan)
			close(errorChan)
		})
	}
}

// TestWithHunterQueryService 测试WithHunterQueryService选项
func TestWithHunterQueryService(t *testing.T) {
	mockService := &MockHunterQueryService{}

	handler := NewRecommendAssetHandler("test", "test-task", 0, 100, []int{1}, 0, 0,
		WithHunterQueryService(mockService))

	// 验证依赖注入是否成功
	assert.Equal(t, mockService, handler.hunterQuerySvc)

	// 验证默认情况下使用DefaultHunterQueryService
	defaultHandler := NewRecommendAssetHandler("test", "test-task", 0, 100, []int{1}, 0, 0)
	_, ok := defaultHandler.hunterQuerySvc.(*DefaultHunterQueryService)
	assert.True(t, ok, "默认应该使用DefaultHunterQueryService")
}

// TestHunterQueryParams 测试HunterQueryParams结构体
func TestHunterQueryParams(t *testing.T) {
	clue := createTestHunterClue(1, "example.com")
	params := HunterQueryParams{
		CluesData:      []*clues.Clue{clue},
		Clue:           clue,
		HunterQueryStr: "domain=example.com",
		TaskId:         "test-task",
		UserId:         100,
		NeedHunter:     true,
	}

	assert.Equal(t, 1, len(params.CluesData))
	assert.Equal(t, clue, params.Clue)
	assert.Equal(t, "domain=example.com", params.HunterQueryStr)
	assert.Equal(t, "test-task", params.TaskId)
	assert.Equal(t, 100, params.UserId)
	assert.True(t, params.NeedHunter)
}

// TestHunterQueryResult 测试HunterQueryResult结构体
func TestHunterQueryResult(t *testing.T) {
	assets := createTestAssets(2)
	err := fmt.Errorf("测试错误")

	// 成功结果
	successResult := HunterQueryResult{
		Assets:    assets,
		FromCache: true,
		Error:     nil,
	}
	assert.Equal(t, 2, len(successResult.Assets))
	assert.True(t, successResult.FromCache)
	assert.NoError(t, successResult.Error)

	// 错误结果
	errorResult := HunterQueryResult{
		Assets:    nil,
		FromCache: false,
		Error:     err,
	}
	assert.Nil(t, errorResult.Assets)
	assert.False(t, errorResult.FromCache)
	assert.Error(t, errorResult.Error)
}
