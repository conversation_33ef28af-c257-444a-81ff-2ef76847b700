package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"micro-service/pkg/cfg"
	asyncq "micro-service/pkg/queue_helper"
	"net"
	"strings"
	"time"

	"github.com/spf13/cast"

	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/port_group"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/mysql/scan_task_domains"
	"micro-service/middleware/mysql/standing_ips"
	"micro-service/middleware/mysql/task"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

// StandingAssetsScanJob 台账资产扫描任务
func StandingAssetsScanJob(ctx context.Context, t *asyncq.Task) error {
	log.Info("StandingAssetsScanJob", "开始处理台账资产扫描任务", map[string]interface{}{
		"task_type": t.Type,
		"payload":   string(t.Payload),
	})

	// 解析参数
	payloadInfo, err := parsePayloadForStandingAssetsScan([]byte(t.Payload))
	if err != nil {
		log.Errorf("StandingAssetsScanJob 解析参数失败: %v", err)
		return err
	}

	log.Info("StandingAssetsScanJob", "参数解析成功", map[string]interface{}{
		"user_id":      payloadInfo.UserId,
		"company_id":   payloadInfo.CompanyId,
		"ips_count":    len(payloadInfo.Ips),
		"domain_count": len(payloadInfo.DomainArr),
	})

	// IP类型验证和过滤
	validIps, err := filterAndValidateIps(payloadInfo.Ips, payloadInfo.ScanParams.TaskParam.IpType)
	if err != nil {
		log.Errorf("StandingAssetsScanJob IP过滤验证失败: %v", err)
		return err
	}

	if len(validIps) == 0 {
		return fmt.Errorf("没有需要扫描的IP数据")
	}

	// 检查IP数量限制
	if len(validIps) > 1000 {
		return fmt.Errorf("超出资产扫描限制数量%d个", len(validIps)-1000)
	}

	// 创建扫描任务
	taskId, err := createScanTask(payloadInfo)
	if err != nil {
		log.Errorf("StandingAssetsScanJob 创建扫描任务失败: %v", err)
		return err
	}

	log.Info("StandingAssetsScanJob", "扫描任务创建成功", map[string]interface{}{
		"task_id":   taskId,
		"user_id":   payloadInfo.UserId,
		"ips_count": len(validIps),
	})

	// 创建任务IP
	err = createTaskIps(taskId, validIps)
	if err != nil {
		log.Errorf("StandingAssetsScanJob 创建任务IP失败: %v", err)
		return err
	}

	// 处理端口配置
	if payloadInfo.ScanParams.IsDefinePort == 1 {
		// 自定义端口
		err = createDefinePorts(taskId, payloadInfo.UserId, payloadInfo.ScanParams.DefinePorts, payloadInfo.ScanParams.DefinePortProtocols)
		if err != nil {
			log.Errorf("StandingAssetsScanJob 创建自定义端口失败: %v", err)
			return err
		}
	} else {
		// 端口组
		err = createTaskPorts(taskId, payloadInfo.ScanParams.TaskParam.PortGroupIds)
		if err != nil {
			log.Errorf("StandingAssetsScanJob 创建任务端口失败: %v", err)
			return err
		}
	}

	// 处理域名配置
	if len(payloadInfo.DomainArr) > 0 {
		err = processDomainConfiguration(taskId, payloadInfo.UserId, payloadInfo.CompanyId, validIps, payloadInfo.DomainArr)
		if err != nil {
			log.Errorf("StandingAssetsScanJob 处理域名配置失败: %v", err)
			// 不返回错误，继续执行
		}
	}

	// 发布扫描任务
	err = publishScanTask(ctx, taskId, payloadInfo.UserId)
	if err != nil {
		log.Errorf("StandingAssetsScanJob 发布扫描任务失败: %v", err)
		return err
	}

	log.Info("StandingAssetsScanJob", "台账资产扫描任务处理完成", map[string]interface{}{
		"task_id":   taskId,
		"user_id":   payloadInfo.UserId,
		"ips_count": len(validIps),
	})

	return nil
}

// parsePayloadForStandingAssetsScan 解析任务参数
func parsePayloadForStandingAssetsScan(payload []byte) (*asyncq.StandingAssetsScanJobPayload, error) {
	var payloadInfo asyncq.StandingAssetsScanJobPayload
	if err := json.Unmarshal(payload, &payloadInfo); err != nil {
		return nil, fmt.Errorf("解析payload失败: %w", err)
	}

	// 验证必填字段
	if payloadInfo.UserId == 0 {
		return nil, fmt.Errorf("user_id不能为空")
	}

	if len(payloadInfo.Ips) == 0 {
		return nil, fmt.Errorf("ips不能为空")
	}

	// 设置默认值和范围验证 - 对应PHP的参数验证逻辑
	if payloadInfo.ScanParams.TaskParam.Name == "" {
		payloadInfo.ScanParams.TaskParam.Name = fmt.Sprintf("资产台账-扫描任务(%s)", time.Now().Format("2006-01-02 15:04:05"))
	}
	// 限制名称长度 - 对应PHP的mb_substr($name,0,60)
	if len(payloadInfo.ScanParams.TaskParam.Name) > 60 {
		payloadInfo.ScanParams.TaskParam.Name = payloadInfo.ScanParams.TaskParam.Name[:60]
	}

	// 带宽验证 - 对应PHP的bandwidth验证逻辑
	if payloadInfo.ScanParams.TaskParam.Bandwidth < 1 {
		payloadInfo.ScanParams.TaskParam.Bandwidth = 300
	}
	if payloadInfo.ScanParams.TaskParam.Bandwidth > 5000 {
		payloadInfo.ScanParams.TaskParam.Bandwidth = 5000
	}

	// 协议并发验证 - 对应PHP的protocol_concurrency验证逻辑
	if payloadInfo.ScanParams.TaskParam.ProtocolConcurrency < 1 {
		payloadInfo.ScanParams.TaskParam.ProtocolConcurrency = 2
	}
	if payloadInfo.ScanParams.TaskParam.ProtocolConcurrency > 100 {
		payloadInfo.ScanParams.TaskParam.ProtocolConcurrency = 20
	}

	// 扫描类型验证 - 对应PHP的scanType验证逻辑
	if payloadInfo.ScanParams.TaskParam.ScanType != 1 && payloadInfo.ScanParams.TaskParam.ScanType != 2 {
		payloadInfo.ScanParams.TaskParam.ScanType = scan_task.SCAN_TYPE_SPEED
	}

	// IP类型验证 - 对应PHP的ipType验证逻辑
	if payloadInfo.ScanParams.TaskParam.IpType != 1 && payloadInfo.ScanParams.TaskParam.IpType != 2 {
		payloadInfo.ScanParams.TaskParam.IpType = scan_task.IP_TYPE_V4
	}

	// Ping开关验证 - 对应PHP的pingSwitch验证逻辑
	if payloadInfo.ScanParams.TaskParam.PingSwitch > 1 {
		payloadInfo.ScanParams.TaskParam.PingSwitch = 1
	}

	// Web Logo开关验证 - 对应PHP的web_logo_switch验证逻辑
	if payloadInfo.ScanParams.TaskParam.WebLogoSwitch > 1 {
		payloadInfo.ScanParams.TaskParam.WebLogoSwitch = 1
	}

	// 自定义端口验证 - 对应PHP的is_define_port验证逻辑
	if payloadInfo.ScanParams.IsDefinePort > 1 {
		payloadInfo.ScanParams.IsDefinePort = 1
	}

	// 任务来源验证 - 对应PHP的task_from验证逻辑
	if payloadInfo.ScanParams.TaskFrom > 100 {
		payloadInfo.ScanParams.TaskFrom = 1
	}

	return &payloadInfo, nil
}

// filterAndValidateIps 过滤和验证IP - 对应PHP的IP过滤逻辑
// 注意：PHP原代码处理的是关联数组 map[string]string，其中键可能是'subdomain'
// Go版本处理的是字符串数组 []string，所以逻辑略有调整
func filterAndValidateIps(ips []string, ipType int) ([]string, error) {
	var validIps []string

	for _, ip := range ips {
		// 跳过空值 - 对应PHP的empty($checkIp)检查
		if ip == "" {
			continue
		}

		// 根据IP类型过滤 - 对应PHP的IP类型过滤逻辑
		if ipType == scan_task.IP_TYPE_V4 {
			// IPv4任务，跳过IPv6地址 - 对应PHP的filter_var($checkIp, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)
			if net.ParseIP(ip) != nil && net.ParseIP(ip).To4() == nil {
				continue
			}
		} else {
			// IPv6任务，跳过IPv4地址 - 对应PHP的filter_var($checkIp, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)
			if net.ParseIP(ip) != nil && net.ParseIP(ip).To4() != nil {
				continue
			}
		}

		// 最终验证IP格式 - 对应PHP的!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 || FILTER_FLAG_IPV6)
		if net.ParseIP(ip) == nil {
			continue
		}

		validIps = append(validIps, ip)
	}

	return validIps, nil
}

// createScanTask 创建扫描任务
func createScanTask(payload *asyncq.StandingAssetsScanJobPayload) (uint64, error) {
	scanTaskModel := scan_task.NewScanTasksModel()
	// 获取最大order值
	maxOrder, err := scanTaskModel.GetMaxOrder()
	if err != nil {
		log.Errorf("获取最大order值失败: %v", err)
		maxOrder = 0
	}

	companyId := cast.ToInt64(payload.CompanyId)

	taskData := scan_task.ScanTasks{
		UserId:              payload.UserId,
		CompanyId:           int(companyId),
		Name:                payload.ScanParams.TaskParam.Name,
		Bandwidth:           cast.ToString(payload.ScanParams.TaskParam.Bandwidth),
		ProtocolConcurrency: cast.ToString(payload.ScanParams.TaskParam.ProtocolConcurrency),
		PingSwitch:          payload.ScanParams.TaskParam.PingSwitch,
		WebLogoSwitch:       payload.ScanParams.TaskParam.WebLogoSwitch,
		ScanType:            payload.ScanParams.TaskParam.ScanType,
		TaskType:            scan_task.TASK_TYPE_ASSET,
		AssetType:           scan_task.TASK_ASSET_MANUAL,
		IpType:              payload.ScanParams.TaskParam.IpType,
		ScanRange:           0,
		Order:               maxOrder + 1,
		IsDefinePort:        payload.ScanParams.IsDefinePort,
		TaskFrom:            payload.ScanParams.TaskFrom,
		Status:              scan_task.StatusWaiting,
		Step:                scan_task.StepScanning,
		Progress:            0.0,
	}

	if payload.OpUserId != nil {
		taskData.OpId = int(*payload.OpUserId)
	}

	// 使用Select明确指定scan_range字段，确保0值能被正确保存
	db := mysql.GetDbClient()
	err = db.Select("*").Create(&taskData).Error
	if err != nil {
		return 0, fmt.Errorf("创建扫描任务失败: %w", err)
	}

	return uint64(taskData.ID), nil
}

// createTaskIps 创建任务IP
func createTaskIps(taskId uint64, ips []string) error {
	if len(ips) == 0 {
		return nil
	}

	now := time.Now()

	var taskIps []*task.TaskIps
	for _, ip := range ips {
		taskIps = append(taskIps, &task.TaskIps{
			TaskId:    taskId,
			Ip:        sql.NullString{String: ip, Valid: true},
			CreatedAt: sql.NullTime{Time: now, Valid: true},
			UpdatedAt: sql.NullTime{Time: now, Valid: true},
		})
	}

	// 批量插入
	return task.NewTaskIpsModel().CreateBatch(taskIps)
}

// createDefinePorts 创建自定义端口
func createDefinePorts(taskId, userId uint64, ports, protocols []uint64) error {
	if len(ports) == 0 || len(protocols) == 0 {
		return nil
	}

	// 去重端口和协议 - 对应PHP的array_unique($allParams['define_ports'])和array_unique($allParams['define_port_protocols'])
	uniquePorts := utils.ListDistinct(ports)
	uniqueProtocols := utils.ListDistinct(protocols)

	var definePorts []*port_group.DefinePort
	for _, port := range uniquePorts {
		for _, protocol := range uniqueProtocols {
			definePorts = append(definePorts, &port_group.DefinePort{
				TaskId:         taskId,
				UserId:         userId,
				Port:           int(port),
				PortProtocolId: protocol,
			})
		}
	}

	// 批量插入
	return port_group.NewDefinePortModel().CreateBatch(definePorts)
}

// createTaskPorts 创建任务端口
func createTaskPorts(taskId uint64, portGroupIds uint64) error {
	now := time.Now()
	return task.NewTaskPortsModel().Create(&task.TaskPorts{
		TaskId:    taskId,
		PortsType: "App\\Models\\MySql\\PortGroup", // 对应PHP的PortGroup::class
		PortsId:   portGroupIds,
		CreatedAt: sql.NullTime{Time: now, Valid: true},
		UpdatedAt: sql.NullTime{Time: now, Valid: true},
	})
}

// processDomainConfiguration 处理域名配置
func processDomainConfiguration(taskId, userId uint64, companyId string, validIps, domainArr []string) error {
	// 创建TaskHosts
	if len(domainArr) > 0 {
		// 获取台账IP对应的域名
		urls, err := getStandingIpDomains(validIps, userId)
		if err != nil {
			log.Errorf("获取台账IP域名失败: %v", err)
		} else if len(urls) > 0 {
			err = createTaskHosts(taskId, urls)
			if err != nil {
				return fmt.Errorf("创建台账IP任务主机失败: %w", err)
			}
		}

		// 创建ScanTaskDomains记录
		err = createScanTaskDomains(taskId, userId, companyId, domainArr)
		if err != nil {
			return fmt.Errorf("创建扫描任务域名失败: %w", err)
		}
	}

	return nil
}

// createTaskHosts 创建任务主机 - 对应PHP的createHosts方法
func createTaskHosts(taskId uint64, urls []string) error {
	if len(urls) == 0 {
		return nil
	}

	// 分块处理，每50个一组 - 对应PHP的array_chunk($hosts, 50)
	chunkSize := 50
	for i := 0; i < len(urls); i += chunkSize {
		end := i + chunkSize
		if end > len(urls) {
			end = len(urls)
		}

		chunk := urls[i:end]

		// 为每个分块创建一个TaskHost记录，urls字段存储整个分块
		taskHost := &task.TaskHost{
			TaskId: taskId,
			Urls:   utils.AnyToStr(chunk), // 将分块转换为JSON格式存储
		}

		err := task.NewTaskHostsModel().Create(taskHost)
		if err != nil {
			return err
		}
	}

	return nil
}

// getStandingIpDomains 获取台账IP对应的域名
func getStandingIpDomains(ips []string, userId uint64) ([]string, error) {
	if len(ips) == 0 {
		return nil, nil
	}

	var domains []string
	standingIpsModel := standing_ips.NewStandingIpsModel()
	standingIps, err := standingIpsModel.GetByQuerys(
		mysql.WithValuesIn("ip", ips),
		mysql.WithUserID(userId),
		mysql.WithColumnNotNull("domain"),
		mysql.WithColumnNotValue("domain", ""),
	)
	if err != nil {
		return nil, err
	}

	for _, standingIp := range standingIps {
		domains = append(domains, standingIp.Domain)
	}
	domains = utils.ListDistinctNonZero(domains)

	return domains, nil
}

// createScanTaskDomains 创建扫描任务域名
func createScanTaskDomains(taskId, userId uint64, companyId string, domainArr []string) error {
	if len(domainArr) == 0 {
		return nil
	}

	domains := strings.Join(domainArr, ",")

	scanTaskDomain := scan_task_domains.ScanTaskDomains{
		UserId:    userId,
		CompanyId: companyId,
		Domains:   domains,
		TaskId:    taskId,
	}

	scanTaskDomainModel := scan_task_domains.NewScanTaskDomainsModel()
	err := scanTaskDomainModel.Create(&scanTaskDomain)
	if err != nil {
		return err
	}

	log.Info("StandingAssetsScanJob", "扫描任务写入域名成功", map[string]interface{}{
		"user_id": userId,
		"task_id": taskId,
		"domains": domains,
	})

	return nil
}

// publishScanTask 下发扫描任务到MQ - 对应PHP的ScanAssetMqJob::publish
func publishScanTask(ctx context.Context, taskId, userId uint64) error {
	// 获取用户优先级 - 对应PHP的User::query()->where('id',$user_id)->first()
	userInfo, err := mysql.NewDSL[user.User]().FindByParams([][]interface{}{
		{"id", "=", userId},
	})
	if err != nil {
		log.Errorf("[StandingAssetsScanJob] 获取用户优先级失败: %v", err)
		return err
	}

	priority := 0
	if userInfo.Id > 0 {
		priority = int(userInfo.Priority)
	}

	// 如果配置开启了调用golang的job，就调用golang的job，否则调用php的job
	if cfg.ExecGolangJob() {
		// 下发扫描任务到MQ - 对应PHP的ScanAssetMqJob::publish
		err = asyncq.Enqueue(ctx, asyncq.ScanAssetJob, &asyncq.TaskIdPayload{
			TaskId: taskId,
			UserId: userId,
		})
	} else {
		//调用php的job --DispatchGolangJobToPhpJob
		err = asyncq.DispatchGolangJobToPhpJob.Dispatch(taskId)
	}

	if err != nil {
		return fmt.Errorf("下发扫描任务到MQ失败: %v", err)
	}

	log.Info("StandingAssetsScanJob", "成功下发扫描任务到MQ", map[string]interface{}{
		"task_id":  taskId,
		"priority": priority,
	})

	return nil
}
