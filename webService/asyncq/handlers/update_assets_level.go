package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/black_forbidden_domains"
	"micro-service/middleware/mysql/black_keyword_type"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/company"
	tasks "micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/domain_task"
	"micro-service/middleware/mysql/url_api_task"
	"micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/cfg"
	clueUtil "micro-service/pkg/clues"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"
	scanPb "micro-service/scanService/proto"
	"strconv"
	"strings"
	"time"

	es2 "github.com/olivere/elastic"

	"github.com/spf13/cast"
)

type UpdateAssetsLevelPayload struct {
	asyncq.UpdateAssetsLevelTaskPayload

	detectTaskInfo *tasks.DetectAssetsTask
}

type UrlApiDomainData struct {
	Master []string `json:"master"`
	Second []string `json:"second"`
	Three  []string `json:"three"`
}

// HandleUpdateAssetsLevel 资产评估主处理函数
func HandleUpdateAssetsLevel(ctx context.Context, t *asyncq.Task) error {
	log.Infof("[步骤流转][HandleUpdateAssetsLevel] 开始处理资产评估任务,任务id:%s", string(t.Payload))

	payload, clueList, err := preloadData([]byte(t.Payload))
	if err != nil {
		log.Errorf("单位资产测绘-资产可信度评估: 预加载数据失败: %v", err)
		return err
	}
	log.Infof("更新资产level开始 - user_id: %d, group_id: %d, detect_task_info: %+v", payload.UserID, payload.GroupID, payload.detectTaskInfo)

	// 添加针对特定IP的跟踪日志
	// targetIPs := []string{}
	// log.Infof("【IP跟踪】开始资产可信度评估任务 - user_id: %d, group_id: %d, flag: %s, 目标IPs: %v", payload.UserID, payload.GroupID, payload.Flag, targetIPs)

	// 锁

	// 获取wsType
	wsType := "update_assets_level"
	if payload.detectTaskInfo.ExpandSource == 1 {
		wsType = "update_assets_level_cloud"
	}

	// 下发域名爆破任务
	err = handleDomainTask(ctx, payload.UserID, payload.detectTaskInfo)
	if err != nil {
		log.Errorf("单位资产测绘-下发域名爆破任务报错了: %v", err)
	}

	// 记录线索数据统计
	log.Infof("【IP跟踪】获取到线索数据 - user_id: %d, group_id: %d, 线索总数: %d", payload.UserID, payload.GroupID, len(clueList))

	// 第一阶段：可信度判断
	var total int
	size := 100
	var totalPage int
	condition := recommend_result.NewFindCondition()
	condition.Flag = payload.Flag
	condition.UserId = int(payload.UserID)

	// 查询资产总数，该总数全局可用，不需要每次都查询
	count, err := recommend_result.NewRecommendResultModel().CountByCondition(condition)
	if err != nil {
		log.Errorf("单位资产测绘-分片获取推荐资产数据进行可信度判断: 获取推荐资产数据失败: %v", err)
	}
	total = int(count)
	log.Infof("单位资产测绘-推荐资产数据总数: %d", total)
	// 计算分页总数（向上取整）
	totalPage = (total + size - 1) / size
	log.Infof("单位资产测绘-推荐资产数据每页大小: %d, 分页总数: %d", size, totalPage)

	// 添加查询条件日志
	log.Infof("【IP跟踪】第一阶段查询条件 - flag: %s, user_id: %d", payload.Flag, payload.UserID)

	// // 先直接查询目标IPs是否存在
	// for _, targetIP := range targetIPs {
	// 	targetCondition := recommend_result.NewFindCondition()
	// 	targetCondition.Flag = payload.Flag
	// 	targetCondition.UserId = int(payload.UserID)
	// 	targetCondition.Ip = targetIP
	// 	targetAssets, err := recommend_result.NewRecommendResultModel().FindByCondition(targetCondition)
	// 	if err != nil {
	// 		log.Errorf("【IP跟踪】直接查询目标IP %s 失败: %v", targetIP, err)
	// 	} else {
	// 		log.Infof("【IP跟踪】直接查询目标IP %s 结果 - 找到资产数: %d", targetIP, len(targetAssets))
	// 		for i, asset := range targetAssets {
	// 			log.Debugf("【IP跟踪】目标IP %s 资产[%d] - asset_id: %s, ip: %s, url: %s, subdomain: %s, status: %d",
	// 				targetIP, i, asset.Id, asset.Ip, asset.Url, asset.Subdomain, asset.Status)
	// 		}
	// 	}
	// }

	// // 再查询不带IP条件的，看看是否有其他问题
	// generalCondition := recommend_result.NewFindCondition()
	// generalCondition.Flag = payload.Flag
	// generalCondition.UserId = int(payload.UserID)
	// generalAssets, err := recommend_result.NewRecommendResultModel().FindByCondition(generalCondition)
	// if err != nil {
	// 	log.Errorf("【IP跟踪】查询所有资产失败: %v", err)
	// } else {
	// 	log.Infof("【IP跟踪】查询所有资产结果 - 总资产数: %d", len(generalAssets))

	// 	// 检查是否包含目标IPs
	// 	for _, targetIP := range targetIPs {
	// 		targetFound := false
	// 		targetIndex := -1
	// 		for i, asset := range generalAssets {
	// 			if asset.Ip == targetIP {
	// 				targetFound = true
	// 				targetIndex = i
	// 				log.Debugf("【IP跟踪】在所有资产中找到目标IP %s - 索引: %d, asset_id: %s, url: %s, status: %d",
	// 					targetIP, i, asset.Id, asset.Url, asset.Status)
	// 				break
	// 			}
	// 		}
	// 		if !targetFound {
	// 			log.Warnf("【IP跟踪】在所有资产中未找到目标IP %s - flag: %s, user_id: %d", targetIP, payload.Flag, payload.UserID)
	// 		} else {
	// 			// 计算目标IP在分页中应该出现的页数
	// 			pageNum := (targetIndex / size) + 1
	// 			log.Debugf("【IP跟踪】目标IP %s 在全量数据中的索引: %d, 应该在第 %d 页出现 (每页%d条)",
	// 				targetIP, targetIndex, pageNum, size)
	// 		}
	// 	}
	// }

	// 进度缓存key
	progressCacheKey := cache.GetCacheKey("UpdateAssetsLevelProgress", fmt.Sprintf("%d_%d", payload.GroupID, payload.UserID))

	// // 先查询一次看看总数和是否包含目标IP
	// log.Infof("【IP跟踪】开始分页查询第1页 - flag: %s, user_id: %d, size: %d", condition.Flag, condition.UserId, size)
	// firstPage, firstAll, err := recommend_result.NewRecommendResultModel().FindByPageCondition(1, size, condition)
	// if err != nil {
	// 	log.Errorf("单位资产测绘-分片获取推荐资产数据进行可信度判断: 获取推荐资产数据失败: %v", err)
	// } else {
	// 	log.Infof("【IP跟踪】第一阶段总资产数: %d", firstAll)

	// 	// 检查第一页是否包含目标IP
	// 	hasTargetIP := false
	// 	for _, item := range firstPage {
	// 		for _, targetIP := range targetIPs {
	// 			if item.Ip == targetIP {
	// 				hasTargetIP = true
	// 				log.Debugf("【IP跟踪】在第一页找到目标IP - asset_id: %s, ip: %s, online_state: %v, updated_at: %s",
	// 					item.Id, item.Ip, item.OnlineState, item.UpdatedAt)
	// 				break
	// 			}
	// 		}
	// 		if hasTargetIP {
	// 			break
	// 		}
	// 	}
	// 	log.Infof("【IP跟踪】第一页是否包含目标IP: %v, 第一页资产数: %d", hasTargetIP, len(firstPage))

	// 	// 打印第一页前5个资产的排序字段，用于分析排序逻辑
	// 	log.Infof("【IP跟踪】第一页前5个资产的排序字段:")
	// 	for i, item := range firstPage {
	// 		if i >= 5 {
	// 			break
	// 		}
	// 		log.Debugf("【IP跟踪】资产[%d] - ip: %s, online_state: %v, updated_at: %s",
	// 			i, item.Ip, item.OnlineState, item.UpdatedAt)
	// 	}

	// 	// 如果第一页没有，我们需要查询所有数据来确认目标IP是否存在
	// 	if !hasTargetIP && firstAll > 0 {
	// 		log.Infof("【IP跟踪】开始全量检查是否包含目标IP...")
	// 		allAssets, err := recommend_result.NewRecommendResultModel().FindByCondition(condition)
	// 		if err != nil {
	// 			log.Errorf("【IP跟踪】全量查询失败: %v", err)
	// 		} else {
	// 			targetFound := false
	// 			targetIndex := -1
	// 			for i, asset := range allAssets {
	// 				for _, targetIP := range targetIPs {
	// 					if asset.Ip == targetIP {
	// 						targetFound = true
	// 						targetIndex = i
	// 						log.Debugf("【IP跟踪】在全量数据中找到目标IP - 索引: %d, asset_id: %s, ip: %s, url: %s, online_state: %v, updated_at: %s",
	// 							i, asset.Id, asset.Ip, asset.Url, asset.OnlineState, asset.UpdatedAt)
	// 						break
	// 					}
	// 				}
	// 				if targetFound {
	// 					break
	// 				}
	// 			}
	// 			if !targetFound {
	// 				log.Warnf("【IP跟踪】在全量数据中未找到目标IP - 总资产数: %d, flag: %s, user_id: %d",
	// 					len(allAssets), payload.Flag, payload.UserID)
	// 			} else {
	// 				// 计算目标IP在分页中应该出现的页数
	// 				pageNum := (targetIndex / size) + 1
	// 				log.Infof("【IP跟踪】目标IP在全量数据中的索引: %d, 应该在第 %d 页出现 (每页%d条)",
	// 					targetIndex, pageNum, size)
	// 			}
	// 		}
	// 	}
	// }

	for i := 1; i <= totalPage; i++ {
		// 在每次分页查询前，确认查询条件
		log.Infof("【IP跟踪】第%d页查询条件 - flag: %s, user_id: %d", i, condition.Flag, condition.UserId)

		pageData, _, err := recommend_result.NewRecommendResultModel().FindByPageCondition(i, size, condition)
		if err != nil {
			log.Errorf("单位资产测绘-分片获取推荐资产数据进行可信度判断: 获取推荐资产数据失败: %v", err)
			continue
		}
		current := 0
		log.Infof("单位资产测绘-分片获取推荐资产数据进行可信度判断 - 第一阶段 - page: %d, total: %d, 当前页资产数: %d", i, total, len(pageData))

		// 检查当前页是否包含目标IPs
		// hasTargetInPage := false
		// foundTargets := []string{}
		// for _, item := range page {
		// 	for _, targetIP := range targetIPs {
		// 		if item.Ip == targetIP {
		// 			hasTargetInPage = true
		// 			foundTargets = append(foundTargets, targetIP)
		// 		}
		// 	}
		// }
		// if hasTargetInPage {
		// 	log.Infof("【IP跟踪】第%d页包含目标IPs: %v", i, foundTargets)
		// }

		for j, item := range pageData {
			current++

			// // 针对特定IPs的详细跟踪
			// for _, targetIP := range targetIPs {
			// 	if item.Ip == targetIP {
			// 		log.Infof("【IP跟踪】第一阶段发现目标IP - asset_id: %s, ip: %s, url: %s, subdomain: %s, page: %d, index: %d",
			// 			item.Id, item.Ip, item.Url, item.Subdomain, i, j)
			// 		break
			// 	}
			// }

			err = clueUtil.ClassifyAssetsLevel(item, clueList, payload.UserID, j, total)
			if err != nil {
				log.Errorf("单位资产测绘-分片获取推荐资产数据进行可信度判断 - 第一阶段 - 资产分类 err: %v", err)
				// for _, targetIP := range targetIPs {
				// 	if item.Ip == targetIP {
				// 		log.Errorf("【IP跟踪】第一阶段目标IP处理失败 - ip: %s, error: %v", targetIP, err)
				// 		break
				// 	}
				// }
			} else {
				// // 成功处理后，检查是否为目标IP
				// for _, targetIP := range targetIPs {
				// 	if item.Ip == targetIP {
				// 		log.Infof("【IP跟踪】第一阶段目标IP处理成功 - ip: %s", targetIP)
				// 		break
				// 	}
				// }
			}
			if current%20 == 0 {
				progress := float64(current) / float64(total*2)
				// 确保进度不超过0.499
				if progress >= 0.5 {
					progress = 0.499
				}
				// 获取缓存中的进度
				var cacheProgress float64
				redis.GetCache(progressCacheKey, &cacheProgress)
				if round(progress*100, 2) < cacheProgress {
					progress = cacheProgress + 0.02
					if progress >= 0.5 {
						progress = 0.499
					}
				}
				// 更新进度
				err = tasks.NewModel().UpdateAny(map[string]interface{}{
					"update_assets_level_progress": progress,
				}, mysql.WithWhere("id = ? AND user_id = ?", payload.DetectTaskId, payload.UserID))

				if err != nil {
					log.Errorf("单位资产测绘-分片获取推荐资产数据进行可信度判断 - 第一阶段 - 更新进度失败: %v", err)
					return fmt.Errorf("更新进度失败: %v", err)
				}
				// 写入缓存
				redis.SetCache(progressCacheKey, time.Hour, round(progress*100, 2))

				// 发送webSocket消息推送
				err = websocket_message.PublishSuccess(int64(payload.UserID), wsType, map[string]interface{}{
					"flag":     payload.Flag,
					"user_id":  payload.UserID,
					"progress": round(progress*100, 2),
					"status":   1,
					"task_id":  payload.DetectTaskId,
				})
				if err != nil {
					log.Errorf("单位资产测绘-分片获取推荐资产数据进行可信度判断 - 第一阶段 - 发送webSocket消息推送失败: %v", err)
				}
			}
		}
	}

	// 发送中间进度
	err = websocket_message.PublishSuccess(int64(payload.UserID), wsType, map[string]interface{}{
		"flag":     payload.Flag,
		"user_id":  payload.UserID,
		"progress": 50,
		"status":   1,
		"task_id":  payload.DetectTaskId,
	})
	if err != nil {
		log.Errorf("单位资产测绘-分片获取推荐资产数据进行可信度判断 - 发送中间进度webSocket消息推送失败: %v", err)
	}

	// 第二阶段：更新资产可信度级别
	log.Infof("【IP跟踪】第二阶段开始 - 查询条件相同，flag: %s, user_id: %d", payload.Flag, payload.UserID)

	for i := 1; i <= totalPage; i++ {
		pageData, _, err := recommend_result.NewRecommendResultModel().FindByPageCondition(i, size, condition)
		if err != nil {
			log.Errorf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别: 获取推荐资产数据失败: %v", err)
			continue
		}
		current := 0
		log.Infof("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 第二阶段 - page: %d, total: %d", i, total)
		for _, item := range pageData {
			current++

			// // 针对特定IPs的详细跟踪
			// for _, targetIP := range targetIPs {
			// 	if item.Ip == targetIP {
			// 		log.Infof("【IP跟踪】第二阶段发现目标IP - asset_id: %s, ip: %s, url: %s, subdomain: %s, page: %d",
			// 			item.Id, item.Ip, item.Url, item.Subdomain, i)
			// 		break
			// 	}
			// }

			if item.Subdomain != "" {
				// 调用异步job
				payload := NewUpdateAssetsLevelCodePayload(payload.UserID, item.Id, item.Subdomain, item.Url, item.Protocol)
				go func() {
					err = UpdateAssetsLevelCodeHandler(ctx, &asyncq.Task{
						Type:    asyncq.UpdateAssetsLevelCode,
						Payload: utils.AnyToStr(payload),
					})
					if err != nil {
						log.Errorf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 第二阶段 - 异步任务入队失败: %v", err)
					}
				}()
				//err = asyncq.Enqueue(ctx, asyncq.UpdateAssetsLevelCode, payload)
				//if err != nil {
				//	log.Errorf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 第二阶段 - 异步任务入队失败: %v", err)
				//}
			}
			top := getTopLevel(item, payload.UserID)
			if top != nil {
				// // 针对特定IPs的详细跟踪
				// for _, targetIP := range targetIPs {
				// 	if item.Ip == targetIP {
				// 		log.Infof("【IP跟踪】第二阶段目标IP获取到可信度 - ip: %s, level: %d, is_fake: %v, reason: %s",
				// 			targetIP, top.Level, top.IsFake, top.Reason)
				// 		break
				// 	}
				// }

				updates := map[string]interface{}{}
				updates["level"] = top.Level
				updates["is_fake_assets"] = top.IsFake
				if top.Level == clueUtil.AssetsLevelD && top.IsFake {
					updates["threaten_type"] = top.FakeType
				}
				updates["level_reason"] = top.Reason
				if top.Level == clueUtil.AssetsLevelA {
					updates["assets_confidence_level"] = recommend_result.HIGH
				}
				if top.Level == clueUtil.AssetsLevelB || top.Level == clueUtil.AssetsLevelC || top.Level == clueUtil.AssetsLevelD {
					updates["assets_confidence_level"] = recommend_result.MIDDLE
				}
				if top.Level == clueUtil.AssetsLevelD && top.IsFake {
					updates["assets_confidence_level"] = recommend_result.LOW
					black, err := black_keyword_type.NewTypeModel().First(mysql.WithWhere("id = ?", top.FakeType))
					if err != nil {
						log.Errorf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 第二阶段 - 获取威胁类型失败: %v", err)
					} else {
						updates["threaten_type_name"] = black.Name
					}
				}
				log.Debugf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 第二阶段 - flag: %s, assets_id: %d", payload.Flag, payload.DetectTaskId)
				param := recommend_result.UpdateAnyParam{
					Id:   item.Id,
					Data: updates,
				}
				err = recommend_result.NewRecommendResultModel().UpdatesAny([]*recommend_result.UpdateAnyParam{&param})
				if err != nil {
					log.Errorf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 第二阶段 - 更新资产可信度级别失败: %v", err)
					// for _, targetIP := range targetIPs {
					// 	if item.Ip == targetIP {
					// 		log.Errorf("【IP跟踪】第二阶段目标IP更新失败 - ip: %s, error: %v", targetIP, err)
					// 		break
					// 	}
					// }
				} else {
					// for _, targetIP := range targetIPs {
					// 	if item.Ip == targetIP {
					// 		log.Infof("【IP跟踪】第二阶段目标IP更新成功 - ip: %s, updates: %+v", targetIP, updates)
					// 		break
					// 	}
					// }
				}
			} else {
				log.Warnf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 第二阶段 - 当前资产取不到可信度: %+v", item)
				// for _, targetIP := range targetIPs {
				// 	if item.Ip == targetIP {
				// 		log.Warnf("【IP跟踪】第二阶段目标IP无法获取可信度 - ip: %s, asset_id: %s", targetIP, item.Id)
				// 		break
				// 	}
				// }
			}

			if current%9 == 0 {
				progress := float64(current)/float64(total*2) + 0.5
				// 确保进度不超过0.499
				if progress >= 1 {
					progress = 0.999
				}
				// 获取缓存中的进度
				var cacheProgress float64
				redis.GetCache(progressCacheKey, &cacheProgress)
				if round(progress*100, 2) < cacheProgress {
					progress = cacheProgress + 0.02
					if progress >= 0.5 {
						progress = 0.499
					}
				}
				// 更新进度
				err = tasks.NewModel().UpdateAny(map[string]interface{}{
					"update_assets_level_progress": progress,
				}, mysql.WithWhere("id = ? AND user_id = ?", payload.DetectTaskId, payload.UserID))

				if err != nil {
					log.Errorf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 第二阶段 - 更新进度失败: %v", err)
					return fmt.Errorf("更新进度失败: %v", err)
				}
				// 写入缓存
				redis.SetCache(progressCacheKey, time.Hour, round(progress*100, 2))

				// 发送webSocket消息推送
				err = websocket_message.PublishSuccess(int64(payload.UserID), wsType, map[string]interface{}{
					"flag":     payload.Flag,
					"user_id":  payload.UserID,
					"progress": round(progress*100, 2),
					"status":   1,
					"task_id":  payload.DetectTaskId,
				})
				if err != nil {
					log.Errorf("单位资产测绘-分片获取推荐资产数据进行可信度判断 - 第二阶段 - 发送webSocket消息推送失败: %v", err)
				}
			}
		}
	}

	// 标记任务完成
	err = tasks.NewModel().UpdateAny(map[string]interface{}{
		"step_status":                  tasks.StepStatusDone,
		"update_assets_level_progress": 100,
	}, mysql.WithWhere("id = ? AND user_id = ?", payload.DetectTaskId, payload.UserID))
	if err != nil {
		log.Errorf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 第二阶段 - 更新任务状态失败: %v", err)
		return fmt.Errorf("更新任务状态失败: %v", err)
	}

	// 发送完成消息
	err = websocket_message.PublishSuccess(int64(payload.UserID), wsType, map[string]interface{}{
		"flag":     payload.Flag,
		"user_id":  payload.UserID,
		"progress": 100,
		"status":   2,
		"task_id":  payload.DetectTaskId,
	})
	if err != nil {
		log.Errorf("单位资产测绘-分片获取推荐资产数据进行更新可信度级别 - 发送完成消息失败: %v", err)
	}

	// 检查是否需要下发智能模式扫描任务
	if payload.detectTaskInfo.IsIntellectMode == 1 {
		log.Infof("[步骤流转][HandleUpdateAssetsLevel] 智能模式下发扫描任务 - user_id: %d, flag: %s, group_id: %d", payload.UserID, payload.Flag, payload.GroupID)
		// 下发扫描入库任务
		err = asyncq.Enqueue(ctx, asyncq.DetectDirectOperateJob, &asyncq.DetectDirectOperateJobPayload{
			UserId:       uint64(payload.UserID),
			DetectTaskId: uint64(payload.detectTaskInfo.ID),
			TaskName:     "scanAssets",
		})
		if err != nil {
			log.Errorf("[UpdateAssetsLevel] 下发扫描入库任务失败: %v", err)
		}
	}

	log.Infof("更新资产level完成 - user_id: %d, flag: %s, group_id: %d", payload.UserID, payload.Flag, payload.GroupID)

	// // 添加针对特定IPs的最终跟踪日志
	// log.Infof("【IP跟踪】资产可信度评估任务完成 - user_id: %d, group_id: %d, flag: %s, 目标IPs: %v",
	// 	payload.UserID, payload.GroupID, payload.Flag, targetIPs)

	return nil
}

// preloadData 预加载数据
func preloadData(payloadData []byte) (*UpdateAssetsLevelPayload, []*clues.Clue, error) {
	payload := &UpdateAssetsLevelPayload{}
	// 解析任务数据
	if err := json.Unmarshal(payloadData, &payload); err != nil {
		return nil, nil, fmt.Errorf("解析任务数据失败: %v", err)
	}
	if payload.DetectTaskId == "" {
		return nil, nil, fmt.Errorf("任务id不能为0")
	}
	// 获取扫描任务信息
	detectTask, err := tasks.NewModel().First(mysql.WithWhere("id = ?", payload.DetectTaskId))
	if err != nil {
		return payload, nil, fmt.Errorf("获取任务信息失败: %v", err)
	}
	payload.detectTaskInfo = detectTask

	// 获取线索数据
	clueList, err := clues.NewCluer().ListAll(
		mysql.WithWhere("user_id", payload.UserID),
		mysql.WithWhere("group_id", payload.GroupID),
		mysql.WithWhere("is_deleted", clues.NOT_DELETE),
		mysql.WithWhere("is_supply_chain", clues.SUPPLY_CHAIN_NO),
		mysql.WithWhere("status", clues.CLUE_PASS_STATUS),
	)
	if err != nil {
		log.Errorf("单位资产测绘-资产可信度评估: 获取线索数据失败: %v", err)
		return payload, nil, fmt.Errorf("获取线索数据失败")
	}
	return payload, clueList, nil
}

func shouldSkipDomainTask(info *tasks.DetectAssetsTask) bool {
	if info.IsAutoDomainBurst == 0 && info.IsAutoURLApi == 0 {
		return true
	}
	return false
}

func getClueDomains(userID uint64, groupID uint64) ([]string, []string, error) {
	// 获取主域名列表
	domainsContent, err := clues.NewCluer().ListAll(
		mysql.WithWhere("user_id", userID),
		mysql.WithWhere("group_id", groupID),
		mysql.WithWhere("status", clues.CLUE_PASS_STATUS),
		mysql.WithWhere("is_deleted", clues.NOT_DELETE),
		mysql.WithValuesIn("type", []int{clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN}),
	)
	if err != nil {
		return nil, nil, fmt.Errorf("获取主域名列表失败: %v", err)
	}
	topDomains := make([]string, 0)
	subDomains := make([]string, 0)
	for _, i := range domainsContent {
		if i.Type == clues.TYPE_DOMAIN {
			topDomains = append(topDomains, i.Content)
		} else {
			subDomains = append(subDomains, i.Content)
		}
	}
	return topDomains, subDomains, nil
}

// 处理域名任务
func handleDomainTask(ctx context.Context, userID uint64, info *tasks.DetectAssetsTask) error {
	if shouldSkipDomainTask(info) {
		log.Infof("当前任务不需要进行域名爆破任务 - user_id: %d", userID)
		return nil
	}

	topDomains, subDomains, err := getClueDomains(userID, info.GroupId)
	if err != nil {
		log.Errorf("单位资产测绘-资产可信度评估: 获取主域名列表失败: %v", err)
		return fmt.Errorf("获取主域名列表失败")
	}

	// 获取所有子域名
	domains, err := getSubDomainsFromRecommendResult(info, userID, topDomains)
	if err != nil {
		return fmt.Errorf("获取推荐资产数据失败: %v", err)
	}
	// 过滤黑名单域名
	domains, err = filterDomains(ctx, domains)
	if err != nil {
		return fmt.Errorf("过滤黑名单域名失败: %v", err)
	}

	// 合并域名列表
	allDomains := append(domains, domains...)
	allDomains = append(allDomains, subDomains...)
	allDomains = utils.ListDistinctNonZero(allDomains)
	if len(allDomains) == 0 {
		log.Warnf("单位资产测绘-资产可信度评估: 域名参数过滤已经为空")
		return nil
	}

	if info.IsAutoDomainBurst == 1 {
		// 分块处理域名
		domainChunks := chunkDomains(allDomains, 500)
		for _, chunk := range domainChunks {
			// 创建域名爆破任务
			err := createDomainTask(chunk, userID, info)
			if err != nil {
				log.Errorf("单位资产测绘-资产可信度评估: 创建域名爆破任务失败: %v", err)
				return fmt.Errorf("创建域名爆破任务失败")
			}
			bandwidth, err := strconv.Atoi(info.Bandwidth)
			if err != nil {
				bandwidth = 1000
			}
			param := scanPb.ParamsRequest{
				DomainList:   chunk,
				Level:        1,
				Modify:       0,
				Bandwidth:    uint32(bandwidth),
				CallbackUrl:  cfg.GetWebUrl() + "/api/v1/domain_task/finish",
				SkipCallback: false,
			}
			result, err := scanPb.GetProtoClient().DomainBurst(ctx, &param)
			if err != nil {
				log.Errorf("单位资产测绘-资产可信度评估: 域名爆破任务分发失败: %v", err)
				return fmt.Errorf("域名爆破任务分发失败")
			}
			log.Infof("单位资产测绘-资产可信度评估: 调用golang域名爆破任务下发成功, param: %+v", &param)
			if result.GolangTaskId == 0 {
				log.Warnf("单位资产测绘-资产可信度评估: 域名爆破任务分发返回的GolangTaskId为0")
				continue
			}

			// 获取刚创建的域名任务ID（需要从数据库查询最新创建的记录）
			domainTaskRecord, err := updateDomainTask(userID, uint64(info.ID), result.GolangTaskId)
			if err != nil {
				log.Errorf("单位资产测绘-资产可信度评估: 更新golang_task_id失败: %v", err)
				continue
			} else {
				log.Infof("domainTaskCreate", "调用golang的域名爆破任务结束-单位测绘自动触发", map[string]interface{}{
					"php_task_id":    domainTaskRecord.Id,
					"golang_task_id": result.GolangTaskId,
				})
			}
		}
	}

	// 处理搜索引擎任务
	log.Infof("单位资产测绘-资产可信度评估: 检查URL-API任务标志 - IsAutoURLApi=%d", info.IsAutoURLApi)
	if cast.ToInt(info.IsAutoURLApi) == 1 {
		log.Infof("单位资产测绘-资产可信度评估: 开始处理URL-API搜索引擎任务")
		masterDomain := make([]string, 0, 100)
		secondDomain := make([]string, 0, 100)
		thirdDomain := make([]string, 0, 100)
		log.Infof("单位资产测绘-资产可信度评估: 开始处理域名分类 - 总域名数量=%d, 域名列表=%v", len(allDomains), allDomains)
		for _, domain := range allDomains {
			clueDomain := clueUtil.GetTopDomain(domain)
			if len(masterDomain) < 101 {
				masterDomain = append(masterDomain, clueDomain)
			}
		}
		// 处理域名
		masterDomain, secondDomain, thirdDomain = processAllDomains(allDomains, masterDomain, secondDomain, thirdDomain)
		log.Infof("测绘主体企业的-搜索引擎的数据 main_company_domain => %v, detect_task_id  =>  %d,  mainCompanyName =>  %s", masterDomain, info.ID, info.Name)

		// 去重并统计
		uniqueMaster := utils.ListDistinctNonZero(masterDomain)
		uniqueSecond := utils.ListDistinctNonZero(secondDomain)
		uniqueThird := utils.ListDistinctNonZero(thirdDomain)
		log.Infof("单位资产测绘-资产可信度评估: 域名分类统计 - 主域名数量=%d, 二级域名数量=%d, 三级域名数量=%d",
			len(uniqueMaster), len(uniqueSecond), len(uniqueThird))
		log.Infof("单位资产测绘-资产可信度评估: 主域名列表=%v", uniqueMaster)
		log.Infof("单位资产测绘-资产可信度评估: 二级域名列表=%v", uniqueSecond)
		log.Infof("单位资产测绘-资产可信度评估: 三级域名列表=%v", uniqueThird)

		var data = UrlApiDomainData{
			Master: uniqueMaster,
			Second: uniqueSecond,
			Three:  uniqueThird,
		}
		var companyId uint64 = 0
		comp, err := company.NewCompanyModel().First(mysql.WithWhere("owner_id", userID))
		if err != nil {
			log.Warnf("单位资产测绘-资产可信度评估: 未找到企业信息，使用默认CompanyId=0 - user_id=%d, error=%v", userID, err)
			companyId = 0
		} else {
			companyId = comp.ID
			log.Infof("单位资产测绘-资产可信度评估: 获取企业信息成功 - company_id=%d, user_id=%d", comp.ID, userID)
		}
		raw, err := json.Marshal(data)
		if err != nil {
			log.Errorf("单位资产测绘-资产可信度评估: 序列化域名数据失败: %v", err)
			return fmt.Errorf("序列化域名数据失败")
		}
		log.Infof("单位资产测绘-资产可信度评估: 域名数据序列化成功 - JSON长度=%d, 内容=%s", len(raw), string(raw))
		task := url_api_task.SearchEngineUrlApiTask{
			UserId:       userID,
			CompanyId:    companyId,
			OpId:         utils.If(info.SafeUserId == 0, userID, info.SafeUserId),
			Name:         "单位测绘URL-API资产发现任务-" + time.Now().Format(time.DateTime),
			DetectTaskId: uint64(info.ID),
			DomainList:   string(raw),
		}
		log.Infof("单位资产测绘-资产可信度评估: 准备创建URL-API任务 - user_id=%d, company_id=%d, detect_task_id=%d, name=%s",
			userID, companyId, info.ID, task.Name)

		err = url_api_task.NewUrlApiTaskModel().Create(&task)
		if err != nil {
			log.Errorf("单位资产测绘-资产可信度评估: 创建URL-API任务失败: %v", err)
			return fmt.Errorf("创建URL-API任务失败")
		}
		log.Infof("单位资产测绘-资产可信度评估: 创建URL-API任务成功 - task_id=%d", task.Id)

		// 过滤掉空字符串
		filteredMaster := utils.ListNonZero(masterDomain)
		filteredSecond := utils.ListNonZero(secondDomain)
		filteredThird := utils.ListNonZero(thirdDomain)

		log.Infof("单位资产测绘-资产可信度评估: 过滤空字符串后的域名统计 - 主域名数量=%d, 二级域名数量=%d, 三级域名数量=%d",
			len(filteredMaster), len(filteredSecond), len(filteredThird))
		log.Infof("单位资产测绘-资产可信度评估: 过滤后主域名列表=%v", filteredMaster)
		log.Infof("单位资产测绘-资产可信度评估: 过滤后二级域名列表=%v", filteredSecond)
		log.Infof("单位资产测绘-资产可信度评估: 过滤后三级域名列表=%v", filteredThird)

		param := scanPb.DomainSearchTaskCreateRequest{
			UserId:     userID,
			SafeUserId: info.SafeUserId,
			CompanyId:  info.CompanyId,
			Domain: &scanPb.DomainSearchTaskCreateRequestParam{
				Master: filteredMaster,
				Second: filteredSecond,
				Three:  filteredThird,
			},
		}
		log.Infof("单位资产测绘-资产可信度评估: 准备调用DomainSearchTaskCreate - user_id=%d, safe_user_id=%d, company_id=%d, php_task_id=%d",
			userID, info.SafeUserId, info.CompanyId, task.Id)
		log.Infof("domainSearchEngineTask", "domainSearchEngineTask-单位测绘自动触发", map[string]interface{}{
			"php_id":    task.Id,
			"taskParam": &param,
		})

		create, err := scanPb.GetProtoClient().DomainSearchTaskCreate(ctx, &param)
		if err != nil {
			log.Errorf("单位资产测绘-资产可信度评估: 调用DomainSearchTaskCreate失败: %v", err)
			return fmt.Errorf("创建URL-API任务失败")
		}
		log.Infof("单位资产测绘-资产可信度评估: DomainSearchTaskCreate调用成功 - golang_task_id=%d", create.TaskId)

		if create.TaskId > 0 {
			log.Infof("单位资产测绘-资产可信度评估: 获得有效的golang_task_id=%d，开始更新数据库", create.TaskId)
			// 更新golang_task_id
			err = url_api_task.NewUrlApiTaskModel().UpdateAny(
				map[string]interface{}{
					"golang_task_id": create.TaskId,
				},
				mysql.WithWhere("id = ?", task.Id),
				mysql.WithWhere("user_id = ?", userID),
			)
			if err != nil {
				log.Errorf("单位资产测绘-资产可信度评估: 更新golang_task_id失败: %v", err)
			} else {
				log.Infof("单位资产测绘-资产可信度评估: 更新golang_task_id成功 - php_task_id=%d, golang_task_id=%d", task.Id, create.TaskId)
				log.Infof("domainSearchEngineTask", "调用golang的域名搜索引擎发现任务-单位测绘自动触发", map[string]interface{}{
					"php_task_id":    task.Id,
					"golang_task_id": create.TaskId,
				})
			}

			// 标记php域名任务开始
			err = url_api_task.NewUrlApiTaskModel().UpdateAny(
				map[string]interface{}{
					"status":   url_api_task.StatusRunning,
					"progress": 1.0,
				},
				mysql.WithWhere("id = ?", task.Id),
				mysql.WithWhere("user_id = ?", userID),
			)
			if err != nil {
				log.Errorf("单位资产测绘-资产可信度评估: 更新任务状态失败: %v", err)
			}

			// 异步程序去监控处理结果
			if cfg.ExecGolangJob() {
				// 使用Go版本的异步任务监控
				err = asyncq.UrlApiGetResultPhpJob.Dispatch(userID, create.TaskId, uint64(info.ID), task.Id)
				if err != nil {
					log.Errorf("单位资产测绘-资产可信度评估: 下发PHP异步监控任务失败: %v", err)
				} else {
					log.Infof("单位资产测绘-资产可信度评估: 下发PHP异步监控任务成功 - user_id=%d, golang_task_id=%d, php_task_id=%d",
						userID, create.TaskId, task.Id)
				}
			} else {
				// 使用PHP版本的异步任务监控
				err = asyncq.UrlApiGetResultPhpJob.Dispatch(userID, create.TaskId, uint64(info.ID), task.Id)
				if err != nil {
					log.Errorf("单位资产测绘-资产可信度评估: 下发PHP异步监控任务失败: %v", err)
				} else {
					log.Infof("单位资产测绘-资产可信度评估: 下发PHP异步监控任务成功 - user_id=%d, golang_task_id=%d, php_task_id=%d",
						userID, create.TaskId, task.Id)
				}
			}
		} else {
			log.Warnf("单位资产测绘-资产可信度评估: DomainSearchTaskCreate返回的TaskId为0或无效 - response_task_id=%d", create.TaskId)
			log.Warnf("domainSearchEngineTask", "下发url-api任务报错了", map[string]interface{}{
				"response": create,
				"php_id":   task.Id,
			})
		}
	} else {
		log.Infof("单位资产测绘-资产可信度评估: IsAutoURLApi不等于1，跳过URL-API任务处理")
	}
	return nil
}

// updateDomainTask 更新域名任务记录
func updateDomainTask(userID uint64, detectTaskId uint64, taskId uint64) (domain_task.DomainTask, error) {
	model := domain_task.NewDomainTaskModel()
	// 查询域名任务记录
	domainTaskRecord, err := model.First(
		mysql.WithWhere("user_id = ?", strconv.FormatUint(userID, 10)),
		mysql.WithWhere("detect_task_id = ?", detectTaskId),
		mysql.WithOrder("id DESC"),
	)
	if err != nil {
		log.Errorf("单位资产测绘-资产可信度评估: 查询域名任务记录失败: %v", err)
		return domain_task.DomainTask{

			// 更新golang_task_id - 对应PHP: DomainTasks::query()->where('id', $id)->where('user_id',$this->user_id)->update(['golang_task_id' => $response['golang_task_id']]);
		}, nil
	}

	err = model.UpdateAny(
		domainTaskRecord.Id,
		map[string]interface{}{
			"golang_task_id": taskId,
			"status":         domain_task.StatusDoing,
			"progress":       1.0,
		},
	)
	return domainTaskRecord, err
}

// createDomainTask 创建域名爆破任务
func createDomainTask(chunk []string, userID uint64, info *tasks.DetectAssetsTask) error {
	// 将域名列表转换为JSON字符串 - 对应PHP: 'domain_list' => json_encode($domainList)
	domainListJSON, err := json.Marshal(chunk)
	if err != nil {
		log.Errorf("单位资产测绘-资产可信度评估: 域名列表JSON序列化失败: %v", err)
		return nil
	}

	domainListStr := string(domainListJSON)
	log.Infof("单位资产测绘-资产可信度评估: 域名列表JSON格式: %s", domainListStr)

	domainTask := &domain_task.DomainTask{
		UserId:       strconv.FormatUint(userID, 10),
		OpId:         utils.If(info.SafeUserId == 0, userID, info.SafeUserId),
		Name:         "单位测绘域名爆破任务-" + time.Now().Format(time.DateTime),
		DomainList:   &sql.NullString{String: domainListStr, Valid: true},
		Level:        1,
		Bandwidth:    info.Bandwidth,
		DetectTaskId: uint64(info.ID),
	}
	err = domain_task.NewDomainTaskModel().Create(domainTask)
	return err
}

// 获取推荐资产数据
func getSubDomainsFromRecommendResult(info *tasks.DetectAssetsTask, userID uint64, topDomains []string) ([]string, error) {
	condition := recommend_result.NewFindCondition()
	condition.Flag = info.ExpendFlags
	condition.UserId = int(userID)
	condition.Domains = topDomains
	query := es2.NewBoolQuery()
	recommend_result.BuildQueryByCondition(query, condition)
	if condition.Keyword != "" {
		recommend_result.FakeAssetKeywordQuery(query, condition.Keyword)
	}
	result, err := elastic.All[recommend_result.RecommendResult](100, query, nil)
	if err != nil {
		log.Errorf("单位资产测绘-资产可信度评估: 获取推荐资产数据失败: %v", err)
		return nil, fmt.Errorf("获取推荐资产数据失败")
	}
	var domains []string
	for _, v := range result {
		if v.Subdomain != "" {
			domains = append(domains, v.Subdomain)
		}
	}
	// 组装线索的主域名到 domains
	if len(topDomains) > 0 {
		for _, v := range topDomains {
			domains = append(domains, v)
		}
	}
	return domains, nil
}

func processAllDomains(allDomains []string, masterDomain []string, secondDomain []string, thirdDomain []string) ([]string, []string, []string) {
	subdomainNum := 0
	for _, do := range allDomains {
		topDo := clueUtil.GetTopDomain(do)
		// 处理主域名
		if topDo == do && do != "" {
			if len(masterDomain) < 101 {
				masterDomain = append(masterDomain, do)
			}
			continue
		}
		if subdomainNum >= 2900 {
			break
		}
		// 处理二级域名
		parts := strings.SplitN(do, "."+topDo, 2)
		if len(parts) < 2 {
			continue
		}
		topDoNewNum := len(strings.Split(parts[0], "."))
		if topDoNewNum == 1 {
			secondDomain = append(secondDomain, do)
			subdomainNum++
			continue
		}
		thirdDomain = append(thirdDomain, do)
		subdomainNum++
	}
	return masterDomain, secondDomain, thirdDomain
}

func filterDomains(_ context.Context, domains []string) ([]string, error) {
	// 获取黑名单域名列表
	blackDomains, err := black_forbidden_domains.NewBlackDomainsModel().ListAll()
	if err != nil {
		return nil, fmt.Errorf("获取黑名单域名失败: %v", err)
	}
	// 过滤黑名单域名
	for i := 0; i < len(domains); i++ {
		l := domains[i]
		lowerL := strings.ToLower(l)
		// 检查域名是否在黑名单中
		if len(blackDomains) > 0 {
			skip := false
			for _, bd := range blackDomains {
				if lowerL == strings.ToLower(bd) {
					skip = true
					break
				}
			}
			if skip {
				domains = append(domains[:i], domains[i+1:]...)
				i--
				continue
			}
		}
		// 校验域名格式是否包含过多通配符(*)
		if strings.Count(l, "*") > 1 {
			domains = append(domains[:i], domains[i+1:]...)
			i--
			continue
		}
		// 校验域名格式是否正确
		if !utils.IsDomain(l) {
			domains = append(domains[:i], domains[i+1:]...)
			i--
		}
	}
	return domains, nil
}

func chunkDomains(domains []string, size int) [][]string {
	if size <= 0 {
		return nil
	}
	var chunks [][]string
	for i := 0; i < len(domains); i += size {
		end := i + size
		if end > len(domains) {
			end = len(domains)
		}
		chunks = append(chunks, domains[i:end])
	}
	return chunks
}

func round(val float64, precision int) float64 {
	ratio := math.Pow(10, float64(precision))
	return math.Round(val*ratio) / ratio
}

func getTopLevel(asset *recommend_result.RecommendResult, userID uint64) *clueUtil.LevelReason {
	targetIPs := []string{"*************", "*************"}
	isTargetIP := false
	for _, targetIP := range targetIPs {
		if asset.Ip == targetIP {
			isTargetIP = true
			break
		}
	}

	var levelArr []clueUtil.LevelItem
	cacheKey := cache.GetCacheKey("classifyAssetsLevel", fmt.Sprintf("%d_%s_level", userID, asset.Ip))

	if isTargetIP {
		log.Debugf("【IP跟踪】getTopLevel开始 - ip: %s, cache_key: %s", asset.Ip, cacheKey)
	}

	if !redis.GetCache(cacheKey, &levelArr) {
		log.Errorf("Fail getTopLevel cache - assets %+v, user_id %d", asset, userID)
		if isTargetIP {
			log.Errorf("【IP跟踪】getTopLevel缓存获取失败 - ip: %s, cache_key: %s", asset.Ip, cacheKey)
		}
		return nil
	}

	if isTargetIP {
		log.Debugf("【IP跟踪】getTopLevel缓存获取成功 - ip: %s, 级别数: %d", asset.Ip, len(levelArr))
		for i, level := range levelArr {
			log.Debugf("【IP跟踪】缓存级别[%d] - level: %d, is_fake: %v, reason: %s",
				i, level.IP.Level, level.IP.Val.IsFake, level.IP.Val.Reason)
		}
	}
	if len(levelArr) > 0 {
		// 找出最高的level
		topLevel := 5
		allLevels := make(map[int]bool)
		for _, l := range levelArr {
			topLevel = min(l.IP.Level, topLevel)
			allLevels[l.IP.Level] = true
		}
		// 检查是否存在D但不存A的情况
		_, hasD := allLevels[clueUtil.AssetsLevelD]
		_, hasA := allLevels[clueUtil.AssetsLevelA]
		if hasD && !hasA {
			// 检查D是否被标记为仿冒
			for _, l := range levelArr {
				if l.IP.Level == clueUtil.AssetsLevelD && l.IP.Val.IsFake {
					topLevel = clueUtil.AssetsLevelD
					break
				}
			}
		}
		// 取跟topScore值匹配的数据
		for _, l := range levelArr {
			if l.IP.Level == topLevel {
				if isTargetIP {
					log.Debugf("【IP跟踪】getTopLevel找到匹配级别 - ip: %s, top_level: %d, is_fake: %v, reason: %s",
						asset.Ip, topLevel, l.IP.Val.IsFake, l.IP.Val.Reason)
				}

				if topLevel == clueUtil.AssetsLevelD {
					log.Infof("当前资产的可信度好几个等级，存在仿冒的标识，标记为仿冒资产 - getTopLevel", map[string]any{
						"asset_id": asset.Id,
						"user_id":  userID,
					})
				} else {
					log.Infof("当前资产(ip:%s,asset_id:%s)的可信度好几个等级，取最高的等级,level:%d,user_id:%d", asset.Ip, asset.Id, topLevel, userID)
				}
				return &l.IP.Val
			}
		}
	}
	log.Errorf("Fail getTopLevel - assets %+v, user_id %d", asset, userID)
	if isTargetIP {
		log.Errorf("【IP跟踪】getTopLevel最终失败 - ip: %s, 无法找到匹配的级别", asset.Ip)
	}
	return nil
}
