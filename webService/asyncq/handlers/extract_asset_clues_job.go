package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clue_black_keyword"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/cache"
	"micro-service/pkg/icp"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"

	redis_init "micro-service/initialize/redis"
	redis "micro-service/middleware/redis"

	goRedis "github.com/go-redis/redis/v8"

	"github.com/olivere/elastic"
)

// ExtractAssetCluesProcessor 线索提取处理器
type ExtractAssetCluesProcessor struct {
	userID              uint64
	param               map[string]interface{}
	detectAssetsTasksID uint64
	detectAssetsGroupID uint64
	startTime           time.Time
	allClues            []*clues.Clue
	clueModel           clues.Cluer
	assetModel          foradar_assets.ForadarAssetModel
	icpQuery            icp.ICPQuery
	redis               goRedis.Client
}

// ClueExtractResult 线索提取结果
type ClueExtractResult struct {
	Success bool
	Error   error
	Type    string
}

// NewExtractAssetCluesProcessor 创建线索提取处理器
func NewExtractAssetCluesProcessor(payload *asyncq.ExtractAssetCluesJobPayload) *ExtractAssetCluesProcessor {
	// 获取Redis实例，如果为nil则使用默认值
	redisInstance := redis_init.GetInstance()
	var redisClient goRedis.Client
	if redisInstance != nil {
		redisClient = *redisInstance
	}

	return &ExtractAssetCluesProcessor{
		userID:              payload.UserID,
		param:               payload.Param,
		detectAssetsTasksID: payload.DetectAssetsTasksID,
		detectAssetsGroupID: payload.DetectAssetsGroupID,
		startTime:           time.Now(),
		clueModel:           clues.NewCluer(),
		assetModel:          foradar_assets.NewForadarAssetModel(),
		icpQuery:            icp.NewICPQuery(redisInstance),
		redis:               redisClient,
	}
}

// ExtractAssetCluesJob 提取资产线索任务处理器
func ExtractAssetCluesJob(ctx context.Context, task *asyncq.Task) error {
	// 解析任务参数
	payload := &asyncq.ExtractAssetCluesJobPayload{}
	if err := json.Unmarshal([]byte(task.Payload), payload); err != nil {
		log.Errorf("[ExtractAssetCluesJob] 解析任务参数失败: %v", err)
		return err
	}

	log.Infof("[ExtractAssetCluesJob] 开始提取资产线索任务: user_id=%d, detect_tasks_id=%d, detect_group_id=%d",
		payload.UserID, payload.DetectAssetsTasksID, payload.DetectAssetsGroupID)

	// 创建处理器并执行
	processor := NewExtractAssetCluesProcessor(payload)
	return processor.Handle(ctx)
}

// Handle 执行线索提取主逻辑
func (p *ExtractAssetCluesProcessor) Handle(ctx context.Context) error {
	log.Infof("[ExtractAssetCluesJob] 开始处理，用户ID: %d, 搜索条件: %+v", p.userID, p.param)

	cacheKey := cache.GetLockKey("extract_clue", utils.Md5Hash(p.param))

	// 检查Redis实例是否可用，如果不可用则跳过锁定（主要用于测试环境）
	if redisInstance := redis_init.GetInstance(); redisInstance != nil {
		ok := redis.Lock(cacheKey, 10*time.Second)
		if !ok {
			log.Error("ExtractAssetCluesJob", "设置Redis锁失败", map[string]interface{}{
				"user_id": p.userID,
				"param":   p.param,
			})
			return errors.New("已有进程在提取线索")
		}
		defer redis.UnLock(cacheKey)
	}

	if taskId, ok := p.param["task_id"]; ok && taskId != nil {
		cacheKey := cache.GetCacheKey("extract_clue", utils.SafeString(taskId))
		if _, err := p.redis.Get(ctx, cacheKey).Result(); err == nil {
			log.Error("ExtractAssetCluesJob", "重复任务下发", map[string]interface{}{
				"user_id": p.userID,
				"param":   p.param,
			})
			return errors.New("重复任务下发")
		}
		p.redis.Set(ctx, cacheKey, 1, 24*3600*time.Second)
	}
	log.Infof("[ExtractAssetCluesJob] 开始提取已认领资产线索!提取线索搜索条件：%+v", p.param)
	// 获取现有线索
	if err := p.loadExistingClues(ctx); err != nil {
		log.Errorf("[ExtractAssetCluesJob] 获取现有线索失败: %v", err)
		return err
	}

	// 获取用户企业ID
	companyID, err := p.getUserCompanyID(ctx)
	if err != nil {
		log.Errorf("[ExtractAssetCluesJob] 获取用户企业ID失败: %v", err)
		return err
	}

	// 搜索并处理资产
	if err := p.processAssets(ctx, companyID); err != nil {
		log.Errorf("[ExtractAssetCluesJob] 处理资产失败: %v", err)
		return err
	}

	// 发送完成通知
	if err := p.sendCompletionNotification(ctx); err != nil {
		log.Errorf("[ExtractAssetCluesJob] 发送完成通知失败: %v", err)
	}

	log.Infof("[ExtractAssetCluesJob] 提取已认领资产线索已经完成，用户ID: %d", p.userID)
	return nil
}

// loadExistingClues 加载现有线索
func (p *ExtractAssetCluesProcessor) loadExistingClues(ctx context.Context) error {
	existingClues, err := p.clueModel.ListAll(
		mysql.WithColumnValue("user_id", p.userID),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
	)
	if err != nil {
		return fmt.Errorf("查询现有线索失败: %w", err)
	}

	p.allClues = existingClues

	log.Infof("[ExtractAssetCluesJob] 加载现有线索数量: %d", len(p.allClues))
	return nil
}

// getUserCompanyID 获取用户企业ID
func (p *ExtractAssetCluesProcessor) getUserCompanyID(ctx context.Context) (uint64, error) {
	userModel := user.NewUserModel()
	userInfo, err := userModel.FindById(p.userID)
	if err != nil {
		return 0, fmt.Errorf("查询用户信息失败: %w", err)
	}
	if userInfo.CompanyId != nil && userInfo.CompanyId.Valid {
		return uint64(userInfo.CompanyId.Int64), nil
	}
	return 0, nil
}

// processAssets 处理资产提取线索
func (p *ExtractAssetCluesProcessor) processAssets(ctx context.Context, companyID uint64) error {
	// 构建搜索查询
	query, err := p.buildSearchQuery()
	if err != nil {
		return fmt.Errorf("构建搜索查询失败: %w", err)
	}

	// 执行搜索并处理
	assets, err := p.assetModel.ListAll(ctx, query, "subdomain", "domain", "icp", "logo", "cert", "url", "ip", "reason", "clue_company_name")
	if err != nil {
		return fmt.Errorf("搜索资产失败: %w", err)
	}

	log.Infof("[ExtractAssetCluesJob] 搜索到资产数量: %d", len(assets))

	// 处理每个资产
	for _, asset := range assets {
		if err := p.extractCluesFromAsset(ctx, &asset, companyID); err != nil {
			log.Errorf("[ExtractAssetCluesJob] 处理资产失败: %v", err)
		}
	}

	return nil
}

// buildSearchQuery 构建Elasticsearch搜索查询
func (p *ExtractAssetCluesProcessor) buildSearchQuery() (*elastic.BoolQuery, error) {
	query := elastic.NewBoolQuery()

	// 基础用户条件
	query.Must(elastic.NewTermQuery("user_id", p.userID))

	// 处理搜索参数

	// 处理其他可能的搜索条件
	for key, value := range p.param {
		if value == nil {
			continue
		}

		switch key {
		case "task_id":
			if taskId := utils.SafeInt(value); taskId > 0 {
				query.Must(elastic.NewTermQuery("task_id", taskId))
			}
		case "ip":
			if ip, ok := value.(string); ok && ip != "" {
				query.Must(elastic.NewTermQuery("ip", ip))
			}
		case "domain":
			if domain, ok := value.(string); ok && domain != "" {
				query.Must(elastic.NewMatchQuery("domain", domain))
			}
		case "subdomain":
			if subdomain, ok := value.(string); ok && subdomain != "" {
				query.Must(elastic.NewMatchQuery("subdomain", subdomain))
			}
		case "url":
			if url, ok := value.(string); ok && url != "" {
				query.Must(elastic.NewMatchQuery("url", url))
			}
		case "status":
			if status, ok := value.([]interface{}); ok && len(status) > 0 {
				query.Must(elastic.NewTermsQuery("status", status...))
			}
		}
	}

	return query, nil
}

// extractCluesFromAsset 从单个资产提取线索
func (p *ExtractAssetCluesProcessor) extractCluesFromAsset(ctx context.Context, asset *foradar_assets.ForadarAsset, companyID uint64) error {
	for clueType, typeName := range clues.TYPE_LABELS {
		log.Infof("[ExtractAssetCluesJob] 开始提取%s线索", typeName)
		if err := p.extractClueByType(ctx, asset, clueType, companyID); err != nil {
			log.Errorf("[ExtractAssetCluesJob] 提取%s线索失败: %v", typeName, err)
		}
	}

	return nil
}

// extractClueByType 根据类型提取线索
func (p *ExtractAssetCluesProcessor) extractClueByType(ctx context.Context, asset *foradar_assets.ForadarAsset, clueType int, companyID uint64) error {
	switch clueType {
	case clues.TYPE_FID:
		return p.extractFid(ctx, asset, companyID)
	case clues.TYPE_DOMAIN:
		return p.extractDomain(ctx, asset, companyID)
	case clues.TYPE_CERT:
		return p.extractCert(ctx, asset, companyID)
	case clues.TYPE_ICP:
		return p.extractIcp(ctx, asset, companyID)
	case clues.TYPE_LOGO:
		return p.extractIcon(ctx, asset, companyID)
	case clues.TYPE_KEYWORD:
		return p.extractTitle(ctx, asset, companyID)
	case clues.TYPE_SUBDOMAIN:
		return p.extractSubdomain(ctx, asset, companyID)
	case clues.TYPE_IP:
		return p.extractIp(ctx, asset, companyID)
	default:
		return nil
	}
}

// extractFid FID线索提取（占位实现）
func (p *ExtractAssetCluesProcessor) extractFid(ctx context.Context, asset *foradar_assets.ForadarAsset, companyID uint64) error {
	// 对应PHP中的extractFid方法，返回空数组
	return nil
}

// extractDomain 提取域名线索
func (p *ExtractAssetCluesProcessor) extractDomain(ctx context.Context, asset *foradar_assets.ForadarAsset, companyID uint64) error {
	domains := make([]string, 0)

	// 提取URL中的域名
	if asset.Url != "" {
		if topDomain := utils.GetTopDomain(asset.Url); topDomain != "" {
			domains = append(domains, topDomain)
		}
	}

	// 提取domain字段中的域名
	if asset.Domain != "" {
		if topDomain := utils.GetTopDomain(asset.Domain); topDomain != "" {
			domains = append(domains, topDomain)
		}
	}

	// 去重
	domains = utils.UniqueStringSlice(domains)

	// 处理每个域名
	for _, domain := range domains {
		if domain == "" {
			continue
		}

		// 查询ICP信息
		icpResp, err := p.icpQuery.QueryDomain(ctx, domain, false, false, false, p.userID)
		if err != nil {
			log.Errorf("[ExtractAssetCluesJob] 查询域名%s的ICP信息失败: %v", domain, err)
		}

		var companyName string
		if icpResp == nil || icpResp.Info == nil {
			log.Errorf("[ExtractAssetCluesJob] 查询域名%s的ICP信息失败: %v", domain, err)
			continue
		}
		icpInfo := icpResp.Info

		log.Infof("[ExtractAssetCluesJob] 提取域名，获取域名的备案信息: domain=%s, icp=%+v", domain, icpResp.Info)

		if val, ok := icpInfo["company_name"]; !ok || val == nil {
			log.Infof("[ExtractAssetCluesJob] 备案没查到企业名称: domain=%s", domain)
		} else {
			// 检查企业名称是否为数组类型
			switch v := val.(type) {
			case string:
				companyName = v
			case []interface{}:
				log.Warnf("[ExtractAssetCluesJob] 备案查到的企业名称是数组: domain=%s, company_name=%+v", domain, val)
				companyName = ""
			}
		}

		// 写入域名线索
		domainClue := &clues.Clue{
			UserId:          p.userID,
			CompanyId:       companyID,
			Source:          clues.SOURCE_RECOMMEND,
			Type:            clues.TYPE_DOMAIN,
			Content:         domain,
			Count:           1,
			Status:          clues.CLUE_DEFAULT_STATUS,
			ClueCompanyName: companyName,
		}

		if err := p.writeClue(ctx, domainClue, asset.Reason, asset.Ip); err != nil {
			log.Errorf("[ExtractAssetCluesJob] 写入域名线索失败: %v", err)
		}

		// 如果查到ICP，写入ICP线索
		if icpInfo != nil {
			if icpNo, ok := icpInfo["icp"].(string); ok && icpNo != "" {
				icpClue := &clues.Clue{
					UserId:          p.userID,
					CompanyId:       companyID,
					Source:          clues.SOURCE_RECOMMEND,
					Type:            clues.TYPE_ICP,
					Content:         icpNo,
					Count:           1,
					Status:          clues.CLUE_DEFAULT_STATUS,
					ClueCompanyName: companyName,
				}

				if err := p.writeClue(ctx, icpClue, asset.Reason, asset.Ip); err != nil {
					log.Errorf("[ExtractAssetCluesJob] 写入ICP线索失败: %v", err)
				}
			}

			// 将企业名称作为关键词线索
			companyType, _ := icpInfo["company_type"].(string)
			if companyName != "" && companyType == "企业" {
				log.Infof("[ExtractAssetCluesJob] 根域名拿到的企业名称是: %s", companyName)

				keywordClue := &clues.Clue{
					UserId:          p.userID,
					CompanyId:       companyID,
					Source:          clues.SOURCE_RECOMMEND,
					Type:            clues.TYPE_KEYWORD,
					Content:         companyName,
					Count:           1,
					Status:          clues.CLUE_DEFAULT_STATUS,
					ClueCompanyName: companyName,
				}

				if err := p.writeClue(ctx, keywordClue, asset.Reason, asset.Ip); err != nil {
					log.Errorf("[ExtractAssetCluesJob] 写入关键词线索失败: %v", err)
				}
			}
		}
	}

	return nil
}

// extractCert 提取证书线索
func (p *ExtractAssetCluesProcessor) extractCert(ctx context.Context, asset *foradar_assets.ForadarAsset, companyID uint64) error {
	var cert string

	// 首先尝试从raw字段获取证书信息
	if asset.Cert.Raw != "" {
		cert = clues.GetCert(asset.Cert.Raw, true)
	}

	// 如果raw为空，尝试从subject_key字段获取
	if cert == "" && asset.Cert.SubjectKey != "" {
		cert = asset.Cert.SubjectKey
	}

	log.Infof("[ExtractAssetCluesJob] 提取证书: %s", cert)

	if cert == "" {
		return nil
	}

	// 解析证书获取O和CN
	o, cn := clues.PluckCert(cert)
	companyName := o

	// 如果O不是中文或为空，尝试通过CN查询ICP获取企业名称
	if !utils.CheckIsChinese(o) || o == "" {
		if cn != "" {
			topDomain := utils.GetTopDomain(cn)
			if topDomain != "" {
				icpResp, err := p.icpQuery.QueryDomain(ctx, topDomain, false, false, false, p.userID)
				if err != nil {
					log.Errorf("[ExtractAssetCluesJob] 通过CN查询ICP失败: %v", err)
				} else if icpResp != nil && icpResp.Info != nil {
					if icpCompanyName, ok := icpResp.Info["company_name"].(string); ok && icpCompanyName != "" {
						companyName = icpCompanyName
					}
				}
			}
			log.Infof("[ExtractAssetCluesJob] 补充证书企业名称-1,%s: [%s, %s]", cert, cn, companyName)
		} else {
			log.Infof("[ExtractAssetCluesJob] 补充证书企业名称-2,%s: [CN为空!]", cert)
		}
	} else {
		log.Infof("[ExtractAssetCluesJob] 补充证书企业名称-3,%s [企业名称为空,或者不是中文]: [%s, %t]", cert, companyName, !utils.CheckIsChinese(o))
	}

	// 拆分证书字符串
	certItems := clues.SplitCert(cert)
	certItems = utils.ListDistinctNonZero(certItems)

	// 为每个证书项创建线索
	for _, certItem := range certItems {
		// 创建证书线索
		certClue := &clues.Clue{
			UserId:          p.userID,
			CompanyId:       companyID,
			Source:          clues.SOURCE_RECOMMEND,
			Type:            clues.TYPE_CERT,
			Content:         certItem,
			Count:           1,
			Status:          clues.CLUE_DEFAULT_STATUS,
			ClueCompanyName: companyName,
		}

		if err := p.writeClue(ctx, certClue, asset.Reason, asset.Ip); err != nil {
			log.Errorf("[ExtractAssetCluesJob] 写入证书线索失败: %v", err)
		}
	}

	return nil
}

// extractIcp 提取ICP线索
func (p *ExtractAssetCluesProcessor) extractIcp(ctx context.Context, asset *foradar_assets.ForadarAsset, companyID uint64) error {
	icpNo := asset.Icp.No

	log.Infof("[ExtractAssetCluesJob] 提取ICP: %s", icpNo)

	if icpNo != "" {
		icpClue := &clues.Clue{
			UserId:          p.userID,
			CompanyId:       companyID,
			Source:          clues.SOURCE_RECOMMEND,
			Type:            clues.TYPE_ICP,
			Content:         icpNo,
			Count:           1,
			Status:          clues.CLUE_DEFAULT_STATUS,
			ClueCompanyName: asset.Icp.CompanyName,
		}

		if err := p.writeClue(ctx, icpClue, asset.Reason, asset.Ip); err != nil {
			log.Errorf("[ExtractAssetCluesJob] 写入ICP线索失败: %v", err)
		}
	}

	// 提取备案企业名称作为关键词
	companyName := asset.Icp.CompanyName
	companyType := asset.Icp.Type

	log.Infof("[ExtractAssetCluesJob] 提取备案企业名称: %s", companyName)

	if companyName != "" && companyType == "企业" {
		keywordClue := &clues.Clue{
			UserId:          p.userID,
			CompanyId:       companyID,
			Source:          clues.SOURCE_RECOMMEND,
			Type:            clues.TYPE_KEYWORD,
			Content:         companyName,
			Count:           1,
			Status:          clues.CLUE_DEFAULT_STATUS,
			ClueCompanyName: companyName,
		}

		if err := p.writeClue(ctx, keywordClue, asset.Reason, asset.Ip); err != nil {
			log.Errorf("[ExtractAssetCluesJob] 写入关键词线索失败: %v", err)
		}
	}

	return nil
}

// extractIcon 提取Logo线索
func (p *ExtractAssetCluesProcessor) extractIcon(ctx context.Context, asset *foradar_assets.ForadarAsset, companyID uint64) error {
	var logoHash int

	// 处理Logo hash，可能是int或string类型
	switch h := asset.Logo.Hash.(type) {
	case int:
		logoHash = h
	case float64:
		logoHash = int(h)
	case string:
		if parsedHash, err := strconv.Atoi(h); err == nil {
			logoHash = parsedHash
		}
	default:
		return nil
	}

	log.Infof("[ExtractAssetCluesJob] 提取ICON: %d", logoHash)

	if logoHash == 0 {
		return nil
	}

	// 过滤默认图标hash
	defaultIcons := []int{-1252041730, -*********, 1278323681, 1198047028, -*********, 1611729805, -*********, 1768726119, -1015107330, -*********, 2123863676, 81586312, -1275226814}
	for _, defaultIcon := range defaultIcons {
		if logoHash == defaultIcon {
			return nil // 跳过默认图标
		}
	}

	content := asset.Logo.Content

	logoClue := &clues.Clue{
		UserId:    p.userID,
		CompanyId: companyID,
		Source:    clues.SOURCE_RECOMMEND,
		Type:      clues.TYPE_LOGO,
		Content:   content,
		Hash:      logoHash,
		Count:     1,
		Status:    clues.CLUE_DEFAULT_STATUS,
	}

	if err := p.writeClue(ctx, logoClue, asset.Reason, asset.Ip); err != nil {
		log.Errorf("[ExtractAssetCluesJob] 写入Logo线索失败: %v", err)
	}

	return nil
}

// extractTitle 提取Title线索（占位实现）
func (p *ExtractAssetCluesProcessor) extractTitle(ctx context.Context, asset *foradar_assets.ForadarAsset, companyID uint64) error {
	// 对应PHP中的extractTitle方法，返回空
	return nil
}

// extractSubdomain 提取子域名线索（占位实现）
func (p *ExtractAssetCluesProcessor) extractSubdomain(ctx context.Context, asset *foradar_assets.ForadarAsset, companyID uint64) error {
	// 对应PHP中的extractSubdomain方法，返回空数组
	return nil
}

// extractIp 提取IP线索（占位实现）
func (p *ExtractAssetCluesProcessor) extractIp(ctx context.Context, asset *foradar_assets.ForadarAsset, companyID uint64) error {
	// 对应PHP中的extractIp方法，返回空数组
	return nil
}

// writeClue 写入线索
func (p *ExtractAssetCluesProcessor) writeClue(ctx context.Context, clue *clues.Clue, reasons []foradar_assets.AssetReason, fromIP string) error {
	log.Infof("[ExtractAssetCluesJob] count: %d", len(p.allClues))

	// 线索黑名单检查
	if p.clueIsBlackWords(clue) {
		log.Warnf("[ExtractAssetCluesJob] 关键词存在于线索黑名单,跳过: %+v", clue)
		return nil
	}

	// 获取分组ID数组
	var groupIdArr []uint64
	// 如果指定了检测资产组ID，使用该ID
	if p.detectAssetsGroupID > 0 {
		groupIdArr = []uint64{p.detectAssetsGroupID}
	} else {
		for _, reason := range reasons {
			groupIdArr = append(groupIdArr, uint64(reason.GroupID))
		}
		groupIdArr = utils.ListDistinct(groupIdArr)
	}

	// 单位测绘的线索提取
	if len(groupIdArr) > 0 && clue.Type >= 0 && clue.Content != "" {
		for _, groupID := range groupIdArr {
			clue.GroupId = groupID
			if fromIP != "" {
				clue.FromIp = fromIP
			}

			if len(reasons) == 0 {
				if err := p.clueExtracted(ctx, clue, groupID); err != nil {
					log.Errorf("[ExtractAssetCluesJob] clueExtracted失败: %v", err)
				}
				continue
			}

			for _, reason := range reasons {
				clue.ParentId = uint64(reason.ID)
				if err := p.clueExtracted(ctx, clue, groupID); err != nil {
					log.Errorf("[ExtractAssetCluesJob] clueExtracted失败: %v", err)
				}
			}
		}
	} else {
		log.Warnf("[ExtractAssetCluesJob] 单位测绘-缺失必要字段,跳过: %+v", clue)
	}

	return nil
}

// clueExtracted 执行线索提取写入
func (p *ExtractAssetCluesProcessor) clueExtracted(ctx context.Context, clue *clues.Clue, groupID uint64) error {
	if p.canExists(clue, groupID) {
		log.Infof("[ExtractAssetCluesJob] 单位测绘-线索已存在,跳过: %+v", clue)
		return nil
	}

	// 补充ICP企业名称
	if clue.Type == clues.TYPE_ICP && clue.ClueCompanyName == "" {
		icpResp, err := p.icpQuery.QueryICP(ctx, clue.Content, false, false, p.userID)
		if err != nil {
			log.Errorf("[ExtractAssetCluesJob] 查询ICP企业名称失败: %v", err)
		} else if icpResp != nil && icpResp.Info != nil {
			if companyName, ok := icpResp.Info["company_name"].(string); ok {
				clue.ClueCompanyName = companyName
			}
		}
		log.Infof("[ExtractAssetCluesJob] 补充ICP企业名称,%s: %s", clue.Content, clue.ClueCompanyName)
	}

	// 补充证书企业名称
	if clue.Type == clues.TYPE_CERT {
		o, cn := clues.PluckCert(clue.Content)
		if clue.ClueCompanyName == "" || !utils.CheckIsChinese(o) || o == "" {
			if cn != "" {
				topDomain := utils.GetTopDomain(cn)
				if topDomain != "" {
					icpResp, err := p.icpQuery.QueryDomain(ctx, topDomain, false, false, false, p.userID)
					if err != nil {
						log.Errorf("[ExtractAssetCluesJob] 通过证书CN查询域名ICP失败: %v", err)
					} else if icpResp != nil && icpResp.Info != nil {
						if companyName, ok := icpResp.Info["company_name"].(string); ok {
							clue.ClueCompanyName = companyName
						}
					}
				}
				log.Infof("[ExtractAssetCluesJob] 补充证书企业名称,%s: [%s, %s]", clue.Content, cn, clue.ClueCompanyName)
			} else {
				log.Infof("[ExtractAssetCluesJob] 补充证书企业名称,%s: CN为空!", clue.Content)
			}
		} else {
			log.Infof("[ExtractAssetCluesJob] 补充证书企业名称,%s [企业名称为空,或者不是中文]: [%s, %t]", clue.Content, clue.ClueCompanyName, !utils.CheckIsChinese(o))
		}
	}

	// 设置默认值
	if clue.IsFromCheckTable == "" {
		clue.IsFromCheckTable = "0"
	}

	// 处理Logo类型线索
	if clue.Type == clues.TYPE_LOGO {
		// 检查父级ID是否与自身ID相同
		existingClue, err := p.clueModel.First(
			mysql.WithColumnValue("type", clue.Type),
			mysql.WithColumnValue("hash", clue.Hash),
			mysql.WithColumnValue("user_id", p.userID),
			mysql.WithColumnValue("group_id", groupID),
			mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		)
		if err == nil && existingClue.Id > 0 {
			if clue.ParentId == existingClue.Id {
				log.Warnf("[ExtractAssetCluesJob] 发现线索父id等于id----1: %+v", clue)
				clue.ParentId = 0
			}
		}

		// 执行 UpdateOrCreate
		if err := p.clueModel.UpdateOrCreate(clue); err != nil {
			return fmt.Errorf("更新或创建Logo线索失败: %w", err)
		}
	} else {
		// 处理中文域名
		if clue.Type == clues.TYPE_DOMAIN {
			if utils.CheckIdnDomain(clue.Content) {
				clue.PunycodeDomain = utils.GetIdnDomain(clue.Content, false)
				clue.Content = utils.GetIdnDomain(clue.Content, true)
			}
		}

		// 处理证书类型线索
		if clue.Type == clues.TYPE_CERT {
			certItems := clues.SplitCert(clue.Content)
			for _, certItem := range certItems {
				clue.Content = certItem

				// 检查父级ID是否与自身ID相同
				existingClue, err := p.clueModel.First(
					mysql.WithColumnValue("type", clue.Type),
					mysql.WithColumnValue("content", certItem),
					mysql.WithColumnValue("user_id", p.userID),
					mysql.WithColumnValue("group_id", groupID),
					mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
				)
				if err == nil && existingClue.Id > 0 {
					if clue.ParentId == existingClue.Id {
						log.Warnf("[ExtractAssetCluesJob] 发现线索父id等于id----2: %+v", clue)
						clue.ParentId = 0
					}
				}

				// 执行 UpdateOrCreate
				if err := p.clueModel.UpdateOrCreate(clue); err != nil {
					log.Errorf("[ExtractAssetCluesJob] 更新或创建证书线索失败: %v", err)
				}
			}
		} else {
			// 其他类型线索
			existingClue, err := p.clueModel.First(
				mysql.WithColumnValue("type", clue.Type),
				mysql.WithColumnValue("content", clue.Content),
				mysql.WithColumnValue("user_id", p.userID),
				mysql.WithColumnValue("group_id", groupID),
				mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
			)
			if err == nil && existingClue.Id > 0 {
				if clue.ParentId == existingClue.Id {
					log.Warnf("[ExtractAssetCluesJob] 发现线索父id等于id----3: %+v", clue)
					clue.ParentId = 0
				}
			}

			// 执行 UpdateOrCreate
			if err := p.clueModel.UpdateOrCreate(clue); err != nil {
				return fmt.Errorf("更新或创建线索失败: %w", err)
			}
		}
	}

	// 添加到本地缓存
	clue.IsDeleted = clues.NOT_DELETE
	p.allClues = append(p.allClues, clue)

	log.Infof("[ExtractAssetCluesJob] 单位测绘-自动提取线索OK!，并且写入一个父级id: %+v", clue)
	return nil
}

// canExists 检查线索是否已存在
func (p *ExtractAssetCluesProcessor) canExists(clue *clues.Clue, groupID uint64) bool {
	for _, existingClue := range p.allClues {
		if existingClue.GroupId != groupID || existingClue.UserId != clue.UserId {
			continue
		}

		if clue.Type == clues.TYPE_LOGO {
			if existingClue.Type == clue.Type && existingClue.Hash == clue.Hash {
				return true
			}
		} else {
			if existingClue.Type == clue.Type && existingClue.Content == clue.Content {
				return true
			}
		}
	}
	return false
}

// clueIsBlackWords 检查线索是否在黑名单中
func (p *ExtractAssetCluesProcessor) clueIsBlackWords(clue *clues.Clue) bool {
	typeKeyWords := make([]string, 0)
	// 优先从缓存中检查
	cacheKey := cache.GetCacheKey("clue_black_words", fmt.Sprintf("%d", clue.Type))
	blackWords, err := p.redis.Get(context.Background(), cacheKey).Result()
	if err == nil && blackWords != "" {
		typeKeyWords = strings.Split(blackWords, ",")
	} else {
		// 查询线索黑名单关键词表
		blackKeywordModel := clue_black_keyword.NewClueBlackKeyworder()

		// 简化实现，检查内容是否在黑名单中
		blackKeywords, err := blackKeywordModel.ListAll([]string{"hash", "name"},
			mysql.WithColumnValue("status", clue_black_keyword.STATS_ENABLE),
			mysql.WithColumnValue("type", clue.Type),
		)
		if err != nil {
			log.Errorf("[ExtractAssetCluesJob] 检查线索黑名单失败: %v", err)
			return false
		}
		for _, bk := range blackKeywords {
			if clue.Type == clues.TYPE_LOGO {
				typeKeyWords = append(typeKeyWords, strconv.Itoa(bk.Hash))
			} else {
				typeKeyWords = append(typeKeyWords, bk.Name)
			}
		}
		p.redis.Set(context.Background(), cacheKey, strings.Join(typeKeyWords, ","), 30*time.Minute)
	}

	// 检查线索是否在黑名单中
	currentKeyWord := ""
	if clue.Type == clues.TYPE_LOGO {
		currentKeyWord = strconv.Itoa(clue.Hash)
	} else {
		currentKeyWord = clue.Content
	}
	for _, typeKeyWord := range typeKeyWords {
		if typeKeyWord == currentKeyWord {
			return true
		}
	}
	return false
}

// sendCompletionNotification 发送完成通知
func (p *ExtractAssetCluesProcessor) sendCompletionNotification(ctx context.Context) error {
	// 在这推送消息，表示已经提取完成
	// 发送websocket消息推送
	if p.detectAssetsTasksID > 0 {
		detectTaskModel := detect_assets_tasks.NewModel()
		detectTask, err := detectTaskModel.First(
			detect_assets_tasks.WithID(p.detectAssetsTasksID),
		)
		if err != nil {
			log.Errorf("[ExtractAssetCluesJob] 获取检测任务信息失败: %v", err)
			return err
		}

		if detectTask != nil {
			// 统计新增线索数量
			addCount, err := p.clueModel.Count(
				mysql.WithColumnValue("group_id", detectTask.GroupId),
				mysql.WithColumnValue("user_id", p.userID),
				mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
				mysql.WithColumnValue("status", clues.CLUE_DEFAULT_STATUS),
				mysql.WithColumnNotValue("type", clues.TYPE_SUBDOMAIN),
				mysql.WithWhere("created_at >= ?", p.startTime.Format("2006-01-02 15:04:05")),
			)
			if err != nil {
				log.Errorf("[ExtractAssetCluesJob] 统计新增线索数量失败: %v", err)
			}

			// 发送WebSocket通知
			wsData := map[string]interface{}{
				"detect_assets_tasks_id": p.detectAssetsTasksID,
				"user_id":                p.userID,
				"status":                 2,
				"progress":               100,
				"group_id":               detectTask.GroupId,
				"num":                    addCount,
			}

			if err := websocket_message.PublishSuccess(int64(p.userID), "extract_asset_clues_job", wsData); err != nil {
				log.Errorf("[ExtractAssetCluesJob] 发送WebSocket消息失败: %v", err)
			}
		}
	}

	return nil
}
