package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"strings"

	es_util "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/mysql/task"
	"micro-service/pkg/log"
	"micro-service/pkg/network"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"

	"github.com/olivere/elastic"
)

// TagAssetsJob 标记资产tags
// 消息体:
//
//	{
//		"user_id": 1,    // 用户ID，必填
//		"task_id": 1     // 任务ID，必填
//	}
func TagAssetsJob(ctx context.Context, t *asyncq.Task) error {
	payloadInfo, err := parsePayloadForTagAssets([]byte(t.Payload))
	if err != nil {
		return err
	}

	log.Info("TagAssetsJob", "标记tag任务开始", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"task_id": payloadInfo.TaskId,
	})

	// 查询符合条件的资产
	assets, err := queryAssetsForTagging(payloadInfo.UserId, payloadInfo.TaskId)
	if err != nil {
		log.Error("TagAssetsJob", "查询资产失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	if len(assets) == 0 {
		log.Info("TagAssetsJob", "标记tag完成,未发现需要标记的资产", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
		})
		return nil
	}

	log.Info("TagAssetsJob", "查询到需要标记的资产", map[string]interface{}{
		"user_id":     payloadInfo.UserId,
		"task_id":     payloadInfo.TaskId,
		"asset_count": len(assets),
	})

	// 查询任务信息判断扫描类型
	scanTaskModel := scan_task.NewScanTasksModel()
	taskInfo, err := scanTaskModel.FindByIdAndUserId(payloadInfo.TaskId, payloadInfo.UserId)
	if err != nil {
		log.Error("TagAssetsJob", "查询任务信息失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	if taskInfo == nil {
		log.Error("TagAssetsJob", "任务不存在", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
		})
		return fmt.Errorf("任务不存在")
	}

	// 根据任务信息确定标签类型
	var tags []int
	if taskInfo.OpId != int(taskInfo.UserId) {
		// 安服扫描
		tags = []int{fofaee_assets.SAFE_SCAN} // SAFE_SCAN
	} else {
		// 客户端扫描
		tags = []int{fofaee_assets.CLIENT_SCAN} // CLIENT_SCAN
	}

	// 查询任务IP列表
	taskIpModel := task.NewTaskIpsModel()
	taskIps, err := taskIpModel.FindByQuerys(mysql.WithWhere("task_id = ?", payloadInfo.TaskId))
	if err != nil {
		log.Error("TagAssetsJob", "查询任务IP失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	if len(taskIps) == 0 {
		log.Info("TagAssetsJob", "任务没有配置IP列表", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
		})
		return nil
	}
	// 处理特殊结构的IP，拆分IP - 对应PHP的特殊IP格式处理逻辑
	var needUpdateIp []string
	allTaskIp := make([]string, 0, len(taskIps))
	for _, taskIp := range taskIps {
		if taskIp.Ip.Valid {
			allTaskIp = append(allTaskIp, taskIp.Ip.String)
		}
	}

	if len(allTaskIp) > 0 {
		// 提取所有资产的IP列表
		var assetIpList []string
		for _, asset := range assets {
			assetIpList = append(assetIpList, asset.Ip)
		}

		// 对每个资产IP进行检查
		for _, ip := range assetIpList {
			for _, checkIp := range allTaskIp {
				if processSpecialIPFormat(ip, checkIp) {
					needUpdateIp = append(needUpdateIp, ip)
					break
				}
			}
		}
	}

	// 去重
	needUpdateIp = utils.ListDistinctNonZero(needUpdateIp)

	// 根据需要更新的IP列表筛选资产
	var needUpdateAssets []*fofaee_assets.FofaeeAssets
	needUpdateIpMap := make(map[string]bool)
	for _, ip := range needUpdateIp {
		needUpdateIpMap[ip] = true
	}

	for _, asset := range assets {
		if needUpdateIpMap[asset.Ip] {
			needUpdateAssets = append(needUpdateAssets, asset)
		}
	}

	if len(needUpdateAssets) == 0 {
		log.Info("TagAssetsJob", "没有资产需要标记", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
		})
		return nil
	}

	// 批量更新资产tags
	updateCount, err := updateAssetTags(needUpdateAssets, tags)
	if err != nil {
		log.Error("TagAssetsJob", "更新资产标签失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	log.Info("TagAssetsJob", "标记tag完成,发现了需要标记的资产", map[string]interface{}{
		"user_id":      payloadInfo.UserId,
		"task_id":      payloadInfo.TaskId,
		"update_count": updateCount,
	})

	return nil
}

// queryAssetsForTagging 查询需要标记的资产,返回ip列表
func queryAssetsForTagging(userId, taskId uint64) ([]*fofaee_assets.FofaeeAssets, error) {
	// 构建查询条件
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermQuery("task_id", taskId))
	query.Must(elastic.NewTermQuery("status", fofaee_assets.StatusConfirmAsset))

	// tags不等于CLIENT_SCAN和SAFE_SCAN
	query.MustNot(elastic.NewTermQuery("tags", fofaee_assets.CLIENT_SCAN))
	query.MustNot(elastic.NewTermQuery("tags", fofaee_assets.SAFE_SCAN))

	// online_state为0或null
	onlineQuery := elastic.NewBoolQuery()
	onlineQuery.Should(elastic.NewTermQuery("online_state", fofaee_assets.StatusOnline))
	onlineQuery.Should(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("online_state")))
	query.Must(onlineQuery)

	// 查询所有匹配的资产
	assets, err := es_util.All[fofaee_assets.FofaeeAssets](200, query, nil, "ip")
	if err != nil {
		return nil, fmt.Errorf("查询资产失败: %w", err)
	}

	return assets, nil
}

// updateAssetTags 批量更新资产标签
func updateAssetTags(assets []*fofaee_assets.FofaeeAssets, newTags []int) (int, error) {
	// key:id,value:map[string]any
	updateData := make(map[string]map[string]any)

	for _, asset := range assets {
		// 合并现有标签和新标签
		var finalTags []int
		if len(asset.Tags) > 0 {
			// 去重合并
			tagMap := make(map[int]bool)
			for _, tag := range asset.Tags {
				tagMap[tag] = true
			}
			for _, tag := range newTags {
				tagMap[tag] = true
			}

			for tag := range tagMap {
				finalTags = append(finalTags, tag)
			}
		} else {
			finalTags = newTags
		}

		updateData[asset.Id] = map[string]any{
			"tags": finalTags,
		}
	}

	// 批量更新
	if len(updateData) > 0 {
		err := es_util.UpdateMany[fofaee_assets.FofaeeAssets](context.Background(), fofaee_assets.FofaeeAssetsIndex, fofaee_assets.FofaeeAssetsType, updateData)
		if err != nil {
			return 0, fmt.Errorf("批量更新资产失败: %w", err)
		}
	}

	return len(updateData), nil
}

// parsePayloadForTagAssets 解析任务参数
func parsePayloadForTagAssets(payload []byte) (*asyncq.TagAssetsJobPayload, error) {
	var payloadInfo asyncq.TagAssetsJobPayload
	if err := json.Unmarshal(payload, &payloadInfo); err != nil {
		return nil, fmt.Errorf("解析任务参数失败: %w", err)
	}

	if payloadInfo.UserId == 0 {
		return nil, fmt.Errorf("用户ID不能为空")
	}

	if payloadInfo.TaskId == 0 {
		return nil, fmt.Errorf("任务ID不能为空")
	}

	return &payloadInfo, nil
}

// processSpecialIPFormat 处理特殊IP格式 - 对应PHP的特殊IP格式处理逻辑
func processSpecialIPFormat(ip, checkIp string) bool {
	// 检查特殊格式的IP
	if strings.Contains(checkIp, "-") || strings.Contains(checkIp, "/") || strings.Contains(checkIp, "*") {
		// 校验所有ipv4的格式是否正确
		if strings.Contains(checkIp, "/") {
			// **********/24 - CIDR格式
			return isIPInCIDRRange(ip, checkIp)
		} else if strings.Contains(checkIp, "*") {
			// 192.168.1-10.*（代表***********-************共10个网段）
			return isIPInWildcardNetworkRange(ip, checkIp)
		} else {
			// 处理IP范围格式，如 **********-100
			return isIPInHyphenRange(ip, checkIp)
		}
	} else {
		// 单个IP直接比较
		return ip == checkIp
	}
}

// isIPInCIDRRange 检查IP是否在CIDR范围内 - 对应PHP的Range::parse($checkIp)->contains(new \IPTools\IP($ip))
func isIPInCIDRRange(ip, cidr string) bool {
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return false
	}
	return ipnet.Contains(net.ParseIP(ip))
}

// isIPInWildcardNetworkRange 检查IP是否在通配符网段范围内 - 对应PHP的192.168.1-10.*处理逻辑
func isIPInWildcardNetworkRange(ip, wildcardRange string) bool {
	// 192.168.1-10.*（代表***********-************共10个网段）
	ipExArr := strings.Split(wildcardRange, "-")
	if len(ipExArr) != 2 {
		return false
	}

	newIp := ipExArr[0] + ".0"
	beforeIpArr := strings.Split(ipExArr[0], ".")
	if len(beforeIpArr) < 3 {
		return false
	}

	// 构建结束IP：**************
	endPart := strings.Replace(ipExArr[1], "*", "", -1)
	beforeIp := beforeIpArr[0] + "." + beforeIpArr[1] + "." + endPart + "255"
	newIpRange := newIp + "-" + beforeIp

	// 使用network包解析IP范围
	ipRange, err := network.ParseRange(newIpRange)
	if err != nil {
		return false
	}

	// 获取网络列表
	networks, err := ipRange.GetNetworks()
	if err != nil {
		return false
	}

	// 检查IP是否在任何一个网络中
	for _, networkCIDR := range networks {
		if isIPInCIDRRange(ip, networkCIDR) {
			return true
		}
	}

	return false
}

// isIPInHyphenRange 检查IP是否在连字符范围内 - 对应PHP的fullIpRange($checkIp)处理逻辑
func isIPInHyphenRange(ip, ipRange string) bool {
	// 使用network包的FullIPRange函数处理IP范围
	fullRange := network.FullIPRange(ipRange)

	// 解析完整的IP范围
	rangeObj, err := network.ParseRange(fullRange)
	if err != nil {
		return false
	}

	// 检查IP是否在范围内
	targetIP := net.ParseIP(ip)
	if targetIP == nil {
		return false
	}

	return rangeObj.Contains(targetIP)
}
