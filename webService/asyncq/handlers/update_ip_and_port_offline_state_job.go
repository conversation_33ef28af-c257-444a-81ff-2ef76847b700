package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/initialize/es"
	asyncq "micro-service/pkg/queue_helper"
	"strings"

	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/port_group"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/mysql/task"
	"micro-service/pkg/log"
	"micro-service/pkg/network"
	"micro-service/pkg/utils"

	"github.com/olivere/elastic"

	olivere "github.com/olivere/elastic"
)

// filterPortListForLog 过滤端口列表，去掉body、cert等大字段，只保留关键信息用于日志
func filterPortListForLog(portList []fofaee_assets.FofaeeAssetPort) []map[string]interface{} {
	filtered := make([]map[string]interface{}, len(portList))
	for i, port := range portList {
		filtered[i] = map[string]interface{}{
			"port":             port.Port,
			"protocol":         port.Protocol,
			"is_open":          port.IsOpen,
			"online_state":     port.OnlineState,
			"http_status_code": port.HttpStatusCode,
			"domain":           port.Domain,
			"url":              port.Url,
			// 去掉 body、cert、header 等大字段
		}
	}
	return filtered
}

// TaskScanPortInfo 任务扫描端口信息
type TaskScanPortInfo struct {
	Ports      []uint64               `json:"ports"`       // 端口列表
	IsAllPort  bool                   `json:"is_all_port"` // 是否全端口扫描
	ProbePorts map[string]interface{} `json:"probe_ports"` // 探活端口信息
}

// UpdateIpAndPortOfflineStateJob 更新IP和端口离线状态任务
//
// 消息体:
//
//	{
//		"user_id": 1,     // 用户ID，必填
//		"task_id": 123,   // 任务ID，必填
//		"task": {}        // 任务对象，可选
//	}
//
// 功能说明:
// 处理之前IP在线，然后扫描完成以后，有些IP已经离线了，
// 但是本地扫描的任务不会补充结果到task_assets数据，
// 导致IP不会把原来的在线状态改为离线状态。
func UpdateIpAndPortOfflineStateJob(ctx context.Context, t *asyncq.Task) error {
	payloadInfo, err := parsePayloadForUpdateIpAndPortOfflineState([]byte(t.Payload))
	if err != nil {
		return err
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "处理之前ip在线，然后扫描完成以后，有些ip已经离线了，但是本地扫描的任务不会补充结果到task_assets数据，导致ip不会把原来的在线状态改为离线状态-开始", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"task_id": payloadInfo.TaskId,
	})

	// 如果没有传递Task对象，从数据库查询
	if payloadInfo.Task == nil {
		scanTaskModel := scan_task.NewScanTasksModel()
		taskInfo, err := scanTaskModel.FindByID(payloadInfo.TaskId)
		if err != nil {
			log.Error("UpdateIpAndPortOfflineStateJob", "查询任务信息失败", map[string]interface{}{
				"task_id": payloadInfo.TaskId,
				"error":   err.Error(),
			})
			return err
		}
		payloadInfo.Task = taskInfo
	}

	// 获取任务结果中的IP列表
	taskResultIps, err := getTaskResultIps(payloadInfo.TaskId)
	if err != nil {
		log.Error("UpdateIpAndPortOfflineStateJob", "获取任务结果IP列表失败", map[string]interface{}{
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "获取任务结果IP列表", map[string]interface{}{
		"task_id":          payloadInfo.TaskId,
		"result_ips_count": len(taskResultIps),
		"result_ips":       taskResultIps,
	})

	// 获取扫描目标IP列表
	taskTargetIps, err := getScanTargetIpsFromTaskIps(payloadInfo.Task)
	if err != nil {
		log.Error("UpdateIpAndPortOfflineStateJob", "获取扫描目标IP列表失败", map[string]interface{}{
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "获取扫描目标IP列表", map[string]interface{}{
		"task_id":          payloadInfo.TaskId,
		"target_ips_count": len(taskTargetIps),
		"target_ips":       taskTargetIps,
	})

	// 计算离线的IP（在目标中但不在结果中）
	offlineIps := calculateOfflineIps(taskTargetIps, taskResultIps)

	log.Info("UpdateIpAndPortOfflineStateJob", "IP对比分析结果", map[string]interface{}{
		"task_id":           payloadInfo.TaskId,
		"target_ips_count":  len(taskTargetIps),
		"result_ips_count":  len(taskResultIps),
		"offline_ips_count": len(offlineIps),
		"offline_ips":       offlineIps,
	})

	if len(offlineIps) == 0 {
		log.Info("UpdateIpAndPortOfflineStateJob", "没有离线的IP需要处理", map[string]interface{}{
			"task_id": payloadInfo.TaskId,
		})
		return nil
	}

	// 获取任务扫描端口信息
	portInfo, err := getTaskScanPortInfo(payloadInfo.Task)
	if err != nil {
		log.Error("UpdateIpAndPortOfflineStateJob", "获取任务扫描端口信息失败", map[string]interface{}{
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	// 打印portInfo详细信息
	log.Info("UpdateIpAndPortOfflineStateJob", "获取到任务扫描端口信息", map[string]interface{}{
		"task_id":     payloadInfo.TaskId,
		"user_id":     payloadInfo.UserId,
		"is_all_port": portInfo.IsAllPort,
		"ports_count": len(portInfo.Ports),
		"ports":       portInfo.Ports,
		"probe_ports": portInfo.ProbePorts,
	})

	// 处理离线IP
	log.Info("UpdateIpAndPortOfflineStateJob", "开始处理离线IP列表", map[string]interface{}{
		"task_id":       payloadInfo.TaskId,
		"user_id":       payloadInfo.UserId,
		"offline_ips":   offlineIps,
		"offline_count": len(offlineIps),
		"port_info":     portInfo,
	})

	err = processOfflineIps(ctx, payloadInfo.UserId, offlineIps, portInfo)
	if err != nil {
		log.Error("UpdateIpAndPortOfflineStateJob", "处理离线IP失败", map[string]interface{}{
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "处理之前ip在线，然后扫描完成以后，有些ip已经离线了，但是本地扫描的任务不会补充结果到task_assets数据，导致ip不会把原来的在线状态改为离线状态-结束", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"task_id": payloadInfo.TaskId,
	})

	return nil
}

// getTaskResultIps 获取任务结果中的IP列表
func getTaskResultIps(taskId uint64) ([]string, error) {
	log.Info("UpdateIpAndPortOfflineStateJob", "开始查询任务结果IP", map[string]interface{}{
		"task_id": taskId,
		"index":   "fofaee_task_assets",
	})

	assets, err := fofaee_task_assets.NewFofaeeTaskAssetsModel().FindByTaskId(taskId, "ip", "task_id", "user_id")
	if err != nil {
		return nil, err
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "查询任务结果详情", map[string]interface{}{
		"task_id":      taskId,
		"assets_count": len(assets),
	})

	// 验证返回的数据是否正确
	var validAssets []*fofaee_task_assets.FofaeeTaskAssets
	for i, asset := range assets {
		log.Info("UpdateIpAndPortOfflineStateJob", "任务结果资产详情", map[string]interface{}{
			"index":   i,
			"task_id": asset.TaskId,
			"ip":      asset.Ip,
			"user_id": asset.UserId,
		})

		// 只保留task_id匹配的数据
		if asset.TaskId == fmt.Sprintf("%d", taskId) {
			validAssets = append(validAssets, asset)
		}
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "过滤后的有效资产", map[string]interface{}{
		"task_id":        taskId,
		"original_count": len(assets),
		"valid_count":    len(validAssets),
	})

	// 使用过滤后的有效数据
	assets = validAssets

	ipMap := make(map[string]struct{})
	var ips []string
	for _, asset := range assets {
		if asset.Ip != "" {
			if _, ok := ipMap[asset.Ip]; !ok {
				ips = append(ips, asset.Ip)
				ipMap[asset.Ip] = struct{}{}
			}
		}
	}

	return ips, nil
}

// getScanTargetIpsFromTaskIps 获取扫描目标IP列表
func getScanTargetIpsFromTaskIps(taskInfo *scan_task.ScanTasks) ([]string, error) {
	// 检查是否有port_range，如果有则返回空（不处理端口范围任务）
	if taskInfo.PortRange != 0 {
		return []string{}, nil
	}

	// 先从TaskIps表查找
	taskIpModel := task.NewTaskIpsModel()
	handlers := []mysql.HandleFunc{
		mysql.WithColumnValue("task_id", taskInfo.ID),
	}
	taskIps, err := taskIpModel.FindByQuerys(handlers...)
	if err == nil && len(taskIps) > 0 {
		var ipList []string
		for _, taskIp := range taskIps {
			if taskIp.Ip.Valid {
				ipList = append(ipList, taskIp.Ip.String)
			}
		}
		return expandIpList(ipList, taskInfo.IpType), nil
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "getScanTargetIpsFromTaskIps-当前任务在TaskIps表找不到扫描目标，取台账所有ip扫描", map[string]interface{}{
		"task_id": taskInfo.ID,
	})

	// 如果是单位测绘类型的任务，返回空
	if (taskInfo.DetectAssetsTasksId > 0) ||
		(taskInfo.OrganizationDiscoverTaskId != nil && *taskInfo.OrganizationDiscoverTaskId > 0) {
		log.Info("UpdateIpAndPortOfflineStateJob", "getScanTargetIpsFromTaskIps-当前任务在TaskIps表找不到扫描目标，但是是单位测绘类型的任务，ip直接返回空", map[string]interface{}{
			"task_id": taskInfo.ID,
		})
		return []string{}, nil
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "getScanTargetIpsFromTaskIps-当前任务在TaskIps表找不到扫描目标，正常的资产扫描任务，取台账的ip", map[string]interface{}{
		"task_id": taskInfo.ID,
	})

	// 从资产台账获取IP
	q := &fofaee_assets.FindCondition{
		UserId: taskInfo.UserId,
		Status: []int{fofaee_assets.StatusConfirmAsset},
	}

	// 根据IP类型过滤
	if taskInfo.IpType == scan_task.IP_TYPE_V6 { // IPv6
		q.IpType = 2
	} else { // IPv4
		q.IpType = 1
	}

	assets, _, err := fofaee_assets.NewFofaeeAssetsModel().FindByCondition(context.Background(), q, 0, 0, "ip")
	if err != nil {
		return nil, err
	}

	var ips []string
	for _, asset := range assets {
		// 根据任务类型过滤IP类型 - 对应参考文件scan_asset_callback.go的逻辑
		if taskInfo.IpType == scan_task.IP_TYPE_V6 {
			// IPv6：包含冒号的IP
			if strings.Contains(asset.Ip, ":") {
				ips = append(ips, asset.Ip)
			}
		} else {
			// IPv4：不包含冒号的IP
			if !strings.Contains(asset.Ip, ":") {
				ips = append(ips, asset.Ip)
			}
		}
	}

	return expandIpList(ips, taskInfo.IpType), nil
}

// expandIpList 扩展IP列表（处理CIDR、IP范围等）
// 对应PHP: spliteIp($getIps,$this->task->ip_type)
func expandIpList(ips []string, ipType int) []string {
	// 使用network包的SplitIP函数，它实现了与PHP spliteIp相同的逻辑
	isIPv6 := ipType == scan_task.IP_TYPE_V6
	splitIps := network.SplitIP(ips, isIPv6)

	var result []string

	if isIPv6 {
		// IPv6处理 - 对应PHP: completeIPV6($showip)
		for _, ip := range splitIps {
			ipv6 := utils.CompleteIPV6(ip)
			result = append(result, ipv6)
		}
	} else {
		// IPv4处理 - 对应PHP: Network::parse($showip) foreach ($network as $ip)
		for _, ip := range splitIps {
			if strings.Contains(ip, "/") {
				// CIDR格式，展开为单个IP
				expandedIps, err := network.ParseIPRange(ip)
				if err != nil {
					log.Error("UpdateIpAndPortOfflineStateJob", "展开CIDR失败", map[string]interface{}{
						"ip":    ip,
						"error": err.Error(),
					})
					result = append(result, ip)
					continue
				}
				// 限制最大数量，避免内存溢出
				if len(expandedIps) > 1000 {
					expandedIps = expandedIps[:1000]
				}
				result = append(result, expandedIps...)
			} else if strings.Contains(ip, "-") {
				// IP范围格式，展开为单个IP
				expandedIps, err := network.ParseIPRange(ip)
				if err != nil {
					log.Error("UpdateIpAndPortOfflineStateJob", "展开IP范围失败", map[string]interface{}{
						"ip":    ip,
						"error": err.Error(),
					})
					result = append(result, ip)
					continue
				}
				// 限制最大数量，避免内存溢出
				if len(expandedIps) > 1000 {
					expandedIps = expandedIps[:1000]
				}
				result = append(result, expandedIps...)
			} else {
				// 单个IP
				result = append(result, ip)
			}
		}
	}

	return utils.ListDistinct(result)
}

// calculateOfflineIps 计算离线的IP
func calculateOfflineIps(targetIps, resultIps []string) []string {
	resultMap := make(map[string]struct{})
	for _, ip := range resultIps {
		resultMap[ip] = struct{}{}
	}

	//var offlineIps []string
	offlineIps := make([]string, 0, len(targetIps))
	for _, ip := range targetIps {
		if _, ok := resultMap[ip]; !ok {
			offlineIps = append(offlineIps, ip)
		}
	}

	return offlineIps
}

// getTaskScanPortInfo 获取任务扫描端口信息
func getTaskScanPortInfo(taskInfo *scan_task.ScanTasks) (*TaskScanPortInfo, error) {
	portInfo := &TaskScanPortInfo{
		Ports:     []uint64{},
		IsAllPort: false,
	}

	taskId := uint64(taskInfo.ID)

	// 区分是否自定义端口扫描
	if taskInfo.IsDefinePort == 1 {
		// 获取自定义端口
		definePortModel := port_group.NewDefinePortModel()
		definePorts, err := definePortModel.FindWithProtocolsByTaskId(taskId)
		if err != nil {
			log.Error("UpdateIpAndPortOfflineStateJob", "获取自定义端口配置失败", map[string]interface{}{
				"task_id": taskId,
				"error":   err.Error(),
			})
			return portInfo, nil
		}

		var taskPorts []uint64
		for _, definePort := range definePorts {
			taskPorts = append(taskPorts, uint64(definePort.Port))
		}

		// 去重
		taskPorts = utils.ListDistinct(taskPorts)

		// 如果是0-65535个自定义端口扫描，那么扫描类型必须是mascan，nmap不支持
		// 根据PHP代码中的常量 \App\Models\MySql\Task::ALL_PORT_DEFINE_PORT
		// 这里检查是否是全端口自定义扫描的特殊值
		if len(taskPorts) == 1 && taskPorts[0] == scan_task.ALL_PORT_DEFINE_PORT {
			// 全端口自定义扫描
			portInfo.IsAllPort = true
			return portInfo, nil
		}

		portInfo.Ports = taskPorts
		return portInfo, nil
	} else {
		// 使用GetAllPorts获取端口
		_, taskPortsUint64, err := scan_task.NewScanTasksModel().GetAllPorts(taskId)
		if err != nil {
			log.Error("UpdateIpAndPortOfflineStateJob", "获取任务端口配置失败", map[string]interface{}{
				"task_id": taskId,
				"error":   err.Error(),
			})
			return portInfo, nil
		}

		var taskPorts []uint64
		if len(taskPortsUint64) > 0 {
			taskPorts = taskPortsUint64

			// 判断当前是否存在禁用的端口
			// 检查是否有禁用的端口，通过查询数据库来判断
			portModel := port_group.NewPortModel()
			hasForbiddenPortCount := portModel.CountByUserIds(uint64(taskInfo.UserId), port_group.StatusDisable)
			if hasForbiddenPortCount > 0 {
				// 扫描端口里面去掉禁用的端口
				taskPortsRepo := task.NewTaskPortsModel()
				taskPortInfo, err := taskPortsRepo.FindByTaskID(taskId)
				if err == nil && taskPortInfo != nil {
					portGroupModel := port_group.NewPortGroupModel()
					// 获取端口分组中的所有端口ID
					var allRealPortIds []uint64
					portGroup, err := portGroupModel.FindById(taskPortInfo.PortsId)
					if err == nil && portGroup != nil {
						for _, port := range portGroup.Ports {
							allRealPortIds = append(allRealPortIds, port.Id)
						}
					}
					if err == nil && len(allRealPortIds) > 0 {
						// 获取状态为默认的端口
						var allRealPortsStruct []*port_group.Port
						allRealPortsStruct, err = portModel.GetByIds(allRealPortIds, port_group.StatusDefault)
						if err == nil && len(allRealPortsStruct) > 0 {
							var allRealPorts []uint64
							for _, port := range allRealPortsStruct {
								allRealPorts = append(allRealPorts, uint64(port.Port))
							}
							log.Info("UpdateIpAndPortOfflineStateJob", "getTaskScanPortInfo-当前用户存在禁用端口,直接按照真正扫描的端口作为判断依据", map[string]interface{}{
								"user_id": taskInfo.UserId,
								"task_id": taskId,
							})
							taskPorts = allRealPorts
						}
					}
				}
			} else {
				log.Info("UpdateIpAndPortOfflineStateJob", "getTaskScanPortInfo-当前用户不存在禁用端口，取默认的端口分组的端口数据", map[string]interface{}{
					"user_id": taskInfo.UserId,
					"task_id": taskId,
				})
			}
		}

		// 检查是否为全端口扫描
		isAllPort := false
		if len(taskPorts) == 0 {
			taskPortsRepo := task.NewTaskPortsModel()
			taskPortInfo, err := taskPortsRepo.FindByTaskID(taskId)
			if err == nil && taskPortInfo != nil {
				portGroupModel := port_group.NewPortGroupModel()
				portGroup, err := portGroupModel.FindById(taskPortInfo.PortsId)
				if err == nil && portGroup != nil {
					// 检查是否为全端口扫描，根据名称判断
					isAllPort = portGroup.Name == "0-65535"
				}
				if err != nil {
					log.Error("UpdateIpAndPortOfflineStateJob", "检查是否全端口扫描失败", map[string]interface{}{
						"task_id":  taskId,
						"group_id": taskPortInfo.PortsId,
						"error":    err.Error(),
					})
				}
			}
		}

		portInfo.Ports = taskPorts
		portInfo.IsAllPort = isAllPort
		return portInfo, nil
	}
}

// processOfflineIps 处理离线IP
func processOfflineIps(ctx context.Context, userId int64, offlineIps []string, portInfo *TaskScanPortInfo) error {
	log.Info("UpdateIpAndPortOfflineStateJob", "processOfflineIps开始处理", map[string]interface{}{
		"user_id":       userId,
		"offline_ips":   offlineIps,
		"offline_count": len(offlineIps),
		"is_all_port":   portInfo.IsAllPort,
		"scan_ports":    portInfo.Ports,
		"ports_count":   len(portInfo.Ports),
		"probe_ports":   portInfo.ProbePorts,
	})

	// 分块处理（每次50个）
	chunkSize := 50
	for i := 0; i < len(offlineIps); i += chunkSize {
		end := i + chunkSize
		if end > len(offlineIps) {
			end = len(offlineIps)
		}

		chunk := offlineIps[i:end]
		log.Info("UpdateIpAndPortOfflineStateJob", "处理离线IP分块", map[string]interface{}{
			"user_id":     userId,
			"chunk_index": i/chunkSize + 1,
			"chunk_size":  len(chunk),
			"chunk_ips":   chunk,
		})

		err := processOfflineIpChunk(ctx, userId, chunk, portInfo)
		if err != nil {
			log.Error("UpdateIpAndPortOfflineStateJob", "处理离线IP分块失败", map[string]interface{}{
				"user_id": userId,
				"chunk":   chunk,
				"error":   err.Error(),
			})
			// 继续处理下一块，不中断整个流程
		}
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "processOfflineIps处理完成", map[string]interface{}{
		"user_id":      userId,
		"total_ips":    len(offlineIps),
		"total_chunks": (len(offlineIps) + chunkSize - 1) / chunkSize,
	})

	return nil
}

// processOfflineIpChunk 处理离线IP分块
func processOfflineIpChunk(ctx context.Context, userId int64, ips []string, portInfo *TaskScanPortInfo) error {
	log.Info("UpdateIpAndPortOfflineStateJob", "开始查询离线IP对应的资产", map[string]interface{}{
		"user_id":   userId,
		"query_ips": ips,
		"ips_count": len(ips),
	})

	// 构建查询条件：user_id = userId AND ip IN (ips)
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))

	// 构建IP的terms查询（相当于SQL的IN查询）
	ipTerms := make([]interface{}, len(ips))
	for i, ip := range ips {
		ipTerms[i] = ip
	}
	query.Must(elastic.NewTermsQuery("ip.keyword", ipTerms...))

	// 执行查询，获取指定字段
	searchResult, err := es.GetInstance().Search().
		Index("fofaee_assets").
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("port_list", "host_list", "online_state", "id", "ip", "user_id")).
		Size(1000). // 设置合理的大小
		Do(ctx)

	if err != nil {
		log.Error("UpdateIpAndPortOfflineStateJob", "查询离线IP对应的资产失败", map[string]interface{}{
			"user_id": userId,
			"ips":     ips,
			"error":   err.Error(),
		})
		return err
	}

	// 解析查询结果
	var assets []*fofaee_assets.FofaeeAssets
	for _, hit := range searchResult.Hits.Hits {
		if hit.Source == nil {
			continue
		}

		var asset fofaee_assets.FofaeeAssets
		if err := json.Unmarshal(*hit.Source, &asset); err != nil {
			log.Warn("UpdateIpAndPortOfflineStateJob", "解析资产数据失败", map[string]interface{}{
				"hit_id": hit.Id,
				"error":  err.Error(),
			})
			continue
		}

		// 确保使用正确的文档ID
		asset.Id = hit.Id

		log.Info("UpdateIpAndPortOfflineStateJob", "解析到资产数据", map[string]interface{}{
			"hit_id":          hit.Id,
			"asset_id":        asset.Id,
			"asset_ip":        asset.Ip,
			"asset_user_id":   asset.UserId,
			"port_list_count": len(asset.PortList),
			"host_list_count": len(asset.HostList),
		})

		assets = append(assets, &asset)
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "查询到离线IP对应的资产", map[string]interface{}{
		"user_id":      userId,
		"query_ips":    ips,
		"found_assets": len(assets),
	})

	// 更新资产状态
	for _, asset := range assets {
		ip := asset.Ip
		updated := false

		log.Info("UpdateIpAndPortOfflineStateJob", "开始处理资产离线状态更新", map[string]interface{}{
			"asset_id":             asset.Id,
			"ip":                   ip,
			"user_id":              userId,
			"is_all_port":          portInfo.IsAllPort,
			"scan_ports":           portInfo.Ports,
			"current_online_state": asset.OnlineState,
			"port_list_count":      len(asset.PortList),
			"host_list_count":      len(asset.HostList),
		})

		if portInfo.IsAllPort {
			// 全端口扫描：所有端口都设为离线
			updated = updateAllPortsOffline(asset)
		} else {
			// 指定端口扫描：只有扫描的端口设为离线
			updated = updateSpecificPortsOffline(asset, portInfo.Ports)
		}

		if updated {
			// 更新fofaee_assets
			err := updateFofaeeAsset(ctx, asset)
			if err != nil {
				log.Error("UpdateIpAndPortOfflineStateJob", "更新fofaee_assets失败", map[string]interface{}{
					"asset_id": asset.Id,
					"ip":       ip,
					"error":    err.Error(),
				})
			}

			// 更新foradar_assets
			err = updateForadarAssets(ctx, userId, ip, portInfo)
			if err != nil {
				log.Error("UpdateIpAndPortOfflineStateJob", "更新foradar_assets失败", map[string]interface{}{
					"user_id": userId,
					"ip":      ip,
					"error":   err.Error(),
				})
			}

			log.Info("UpdateIpAndPortOfflineStateJob", "完成IP资产离线状态更新", map[string]interface{}{
				"asset_id":           asset.Id,
				"user_id":            userId,
				"ip":                 ip,
				"is_all_port":        portInfo.IsAllPort,
				"scan_ports":         portInfo.Ports,
				"final_online_state": asset.OnlineState,
			})
		} else {
			log.Info("UpdateIpAndPortOfflineStateJob", "资产无需更新-所有相关端口已经是离线状态", map[string]interface{}{
				"asset_id":    asset.Id,
				"user_id":     userId,
				"ip":          ip,
				"is_all_port": portInfo.IsAllPort,
				"scan_ports":  portInfo.Ports,
			})
		}
	}

	return nil
}

// updateAllPortsOffline 将所有端口设为离线
func updateAllPortsOffline(asset *fofaee_assets.FofaeeAssets) bool {
	log.Info("UpdateIpAndPortOfflineStateJob", "updateAllPortsOffline开始处理", map[string]interface{}{
		"asset_id":            asset.Id,
		"ip":                  asset.Ip,
		"port_list_before":    filterPortListForLog(asset.PortList),
		"host_list_before":    filterPortListForLog(asset.HostList),
		"online_state_before": asset.OnlineState,
	})

	updated := false
	var changedPorts []map[string]interface{}

	// 处理port_list
	if len(asset.PortList) > 0 {
		for i := range asset.PortList {
			portInfo := &asset.PortList[i] // 使用指针来修改原始数据
			if portInfo.IsOpen != 0 || portInfo.OnlineState != 0 {
				// 记录变化前的状态
				oldIsOpen := utils.SafeInt(portInfo.IsOpen)
				oldOnlineState := utils.SafeInt(portInfo.OnlineState)
				oldHttpStatusCode := utils.SafeInt(portInfo.HttpStatusCode)
				port := utils.SafeInt(portInfo.Port)

				// 更新状态
				portInfo.IsOpen = 0
				portInfo.HttpStatusCode = 0
				portInfo.OnlineState = 0
				updated = true

				// 记录变化详情
				changedPorts = append(changedPorts, map[string]interface{}{
					"list_type":            "port_list",
					"port":                 port,
					"protocol":             portInfo.Protocol,
					"old_is_open":          oldIsOpen,
					"new_is_open":          0,
					"old_online_state":     oldOnlineState,
					"new_online_state":     0,
					"old_http_status_code": oldHttpStatusCode,
					"new_http_status_code": 0,
				})
			}
		}
	}

	// 处理host_list
	if len(asset.HostList) > 0 {
		for i := range asset.HostList {
			hostInfo := &asset.HostList[i] // 使用指针来修改原始数据
			if hostInfo.IsOpen != 0 || hostInfo.OnlineState != 0 {
				// 记录变化前的状态
				oldIsOpen := utils.SafeInt(hostInfo.IsOpen)
				oldOnlineState := utils.SafeInt(hostInfo.OnlineState)
				oldHttpStatusCode := utils.SafeInt(hostInfo.HttpStatusCode)
				port := utils.SafeInt(hostInfo.Port)

				// 更新状态
				hostInfo.IsOpen = 0
				hostInfo.HttpStatusCode = 0
				hostInfo.OnlineState = 0
				updated = true

				// 记录变化详情
				changedPorts = append(changedPorts, map[string]interface{}{
					"list_type":            "host_list",
					"port":                 port,
					"protocol":             hostInfo.Protocol,
					"old_is_open":          oldIsOpen,
					"new_is_open":          0,
					"old_online_state":     oldOnlineState,
					"new_online_state":     0,
					"old_http_status_code": oldHttpStatusCode,
					"new_http_status_code": 0,
				})
			}
		}
	}

	if updated {
		oldAssetOnlineState := asset.OnlineState
		asset.OnlineState = 0

		log.Info("UpdateIpAndPortOfflineStateJob", "全端口扫描-更新资产所有端口为离线状态", map[string]interface{}{
			"asset_id":               asset.Id,
			"ip":                     asset.Ip,
			"user_id":                asset.UserId,
			"old_asset_online_state": oldAssetOnlineState,
			"new_asset_online_state": 0,
			"changed_ports_count":    len(changedPorts),
			"changed_ports":          changedPorts,
		})
	}

	return updated
}

// updateSpecificPortsOffline 将指定端口设为离线
func updateSpecificPortsOffline(asset *fofaee_assets.FofaeeAssets, scanPorts []uint64) bool {
	log.Info("UpdateIpAndPortOfflineStateJob", "updateSpecificPortsOffline开始处理", map[string]interface{}{
		"asset_id":            asset.Id,
		"ip":                  asset.Ip,
		"scan_ports":          scanPorts,
		"port_list_before":    filterPortListForLog(asset.PortList),
		"host_list_before":    filterPortListForLog(asset.HostList),
		"online_state_before": asset.OnlineState,
	})

	if len(scanPorts) == 0 {
		return false
	}

	// 创建端口映射
	portMap := make(map[uint64]bool)
	for _, port := range scanPorts {
		portMap[port] = true
	}

	updated := false
	maxOnlineState := 0
	var changedPorts []map[string]interface{}

	// 处理port_list
	for i := range asset.PortList {
		portInfo := &asset.PortList[i] // 使用指针来修改原始数据
		if portInfo.Port != 0 {
			port := uint64(utils.SafeInt(portInfo.Port))

			if portMap[port] {
				if portInfo.IsOpen != 0 || portInfo.OnlineState != 0 {
					// 记录变化前的状态
					oldIsOpen := utils.SafeInt(portInfo.IsOpen)
					oldOnlineState := utils.SafeInt(portInfo.OnlineState)
					oldHttpStatusCode := utils.SafeInt(portInfo.HttpStatusCode)

					// 更新状态
					portInfo.IsOpen = 0
					portInfo.HttpStatusCode = 0
					portInfo.OnlineState = 0
					updated = true

					// 记录变化详情
					changedPorts = append(changedPorts, map[string]interface{}{
						"list_type":            "port_list",
						"port":                 port,
						"protocol":             portInfo.Protocol,
						"old_is_open":          oldIsOpen,
						"new_is_open":          0,
						"old_online_state":     oldOnlineState,
						"new_online_state":     0,
						"old_http_status_code": oldHttpStatusCode,
						"new_http_status_code": 0,
					})
				}
			}

			// 计算最大在线状态
			if utils.SafeInt(portInfo.OnlineState) > maxOnlineState {
				maxOnlineState = utils.SafeInt(portInfo.OnlineState)
			}
		}
	}

	// 处理host_list
	for i := range asset.HostList {
		hostInfo := &asset.HostList[i] // 使用指针来修改原始数据
		if hostInfo.Port != 0 {
			port := uint64(utils.SafeInt(hostInfo.Port))

			if portMap[port] {
				if hostInfo.IsOpen != 0 || hostInfo.OnlineState != 0 {
					// 记录变化前的状态
					oldIsOpen := utils.SafeInt(hostInfo.IsOpen)
					oldOnlineState := utils.SafeInt(hostInfo.OnlineState)
					oldHttpStatusCode := utils.SafeInt(hostInfo.HttpStatusCode)

					// 更新状态
					hostInfo.IsOpen = 0
					hostInfo.HttpStatusCode = 0
					hostInfo.OnlineState = 0
					updated = true

					// 记录变化详情
					changedPorts = append(changedPorts, map[string]interface{}{
						"list_type":            "host_list",
						"port":                 port,
						"protocol":             hostInfo.Protocol,
						"old_is_open":          oldIsOpen,
						"new_is_open":          0,
						"old_online_state":     oldOnlineState,
						"new_online_state":     0,
						"old_http_status_code": oldHttpStatusCode,
						"new_http_status_code": 0,
					})
				}
			}

			// 计算最大在线状态（对应PHP逻辑）
			if utils.SafeInt(hostInfo.OnlineState) > maxOnlineState {
				maxOnlineState = utils.SafeInt(hostInfo.OnlineState)
			}
		}
	}

	if updated {
		oldAssetOnlineState := asset.OnlineState
		asset.OnlineState = maxOnlineState

		log.Info("UpdateIpAndPortOfflineStateJob", "指定端口扫描-更新资产指定端口为离线状态", map[string]interface{}{
			"asset_id":               asset.Id,
			"ip":                     asset.Ip,
			"user_id":                asset.UserId,
			"scan_ports":             scanPorts,
			"old_asset_online_state": oldAssetOnlineState,
			"new_asset_online_state": maxOnlineState,
			"changed_ports_count":    len(changedPorts),
			"changed_ports":          changedPorts,
		})
	}

	return updated
}

// updateFofaeeAsset 更新fofaee_assets记录
func updateFofaeeAsset(ctx context.Context, asset *fofaee_assets.FofaeeAssets) error {
	log.Info("UpdateIpAndPortOfflineStateJob", "开始更新fofaee_assets记录", map[string]interface{}{
		"asset_id":        asset.Id,
		"ip":              asset.Ip,
		"user_id":         asset.UserId,
		"online_state":    asset.OnlineState,
		"port_list_count": len(asset.PortList),
		"host_list_count": len(asset.HostList),
	})

	// 只更新需要修改的字段，不更新created_at等时间字段
	updateData := map[string]interface{}{
		"port_list":    asset.PortList,
		"host_list":    asset.HostList,
		"online_state": asset.OnlineState,
		"updated_at":   utils.CurrentTime(), // 只更新updated_at
	}

	// 先检查文档是否存在
	exists, err := es.GetInstance().Exists().
		Index("fofaee_assets").
		Type("ips").
		Id(asset.Id).
		Do(ctx)

	if err != nil {
		log.Error("UpdateIpAndPortOfflineStateJob", "检查fofaee_assets文档是否存在失败", map[string]interface{}{
			"asset_id": asset.Id,
			"ip":       asset.Ip,
			"user_id":  asset.UserId,
			"error":    err.Error(),
		})
		return err
	}

	if !exists {
		log.Warn("UpdateIpAndPortOfflineStateJob", "fofaee_assets文档不存在，跳过更新", map[string]interface{}{
			"asset_id": asset.Id,
			"ip":       asset.Ip,
			"user_id":  asset.UserId,
		})
		return nil // 文档不存在，跳过更新，不报错
	}

	// 使用Elasticsearch客户端直接更新指定字段
	_, err = es.GetInstance().Update().
		Index("fofaee_assets").
		Type("ips"). // 添加缺失的type参数
		Id(asset.Id).
		Doc(updateData).
		Do(ctx)

	if err != nil {
		log.Error("UpdateIpAndPortOfflineStateJob", "更新fofaee_assets记录失败", map[string]interface{}{
			"asset_id":        asset.Id,
			"ip":              asset.Ip,
			"user_id":         asset.UserId,
			"online_state":    updateData["online_state"],
			"port_list_count": len(asset.PortList),
			"host_list_count": len(asset.HostList),
			"error":           err.Error(),
		})
		return err
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "成功更新fofaee_assets记录", map[string]interface{}{
		"asset_id":        asset.Id,
		"ip":              asset.Ip,
		"user_id":         asset.UserId,
		"online_state":    updateData["online_state"],
		"port_list_count": len(asset.PortList),
		"host_list_count": len(asset.HostList),
	})

	return nil
}

// updateForadarAssets 更新foradar_assets记录
func updateForadarAssets(ctx context.Context, userId int64, ip string, portInfo *TaskScanPortInfo) error {
	log.Info("UpdateIpAndPortOfflineStateJob", "开始更新foradar_assets记录", map[string]interface{}{
		"user_id":     userId,
		"ip":          ip,
		"is_all_port": portInfo.IsAllPort,
		"scan_ports":  portInfo.Ports,
	})

	foradarAssetModel := foradar_assets.NewForadarAssetModel()

	query := olivere.NewBoolQuery()
	query.Must(olivere.NewTermQuery("user_id", userId))
	query.Must(olivere.NewTermQuery("ip", ip))

	updateData := map[string]interface{}{
		"online_state":     0,
		"http_status_code": 0,
	}

	var queryDescription string
	// 如果是指定端口扫描，只更新特定端口
	if !portInfo.IsAllPort && len(portInfo.Ports) > 0 {
		portQuery := olivere.NewBoolQuery()
		portQuery.Must(query)
		portQuery.Must(olivere.NewTermsQuery("port", uint64SliceToInterfaceSlice(portInfo.Ports)...))
		query = portQuery
		queryDescription = fmt.Sprintf("指定端口扫描，更新端口: %v", portInfo.Ports)
	} else {
		queryDescription = "全端口扫描，更新所有端口"
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "开始更新foradar_assets记录", map[string]interface{}{
		"user_id":           userId,
		"ip":                ip,
		"is_all_port":       portInfo.IsAllPort,
		"scan_ports":        portInfo.Ports,
		"query_description": queryDescription,
		"update_data":       updateData,
	})

	err := foradarAssetModel.UpdateByQuery(ctx, query, updateData)
	if err != nil {
		log.Error("UpdateIpAndPortOfflineStateJob", "更新foradar_assets记录失败", map[string]interface{}{
			"user_id":           userId,
			"ip":                ip,
			"is_all_port":       portInfo.IsAllPort,
			"scan_ports":        portInfo.Ports,
			"query_description": queryDescription,
			"error":             err.Error(),
		})
		return err
	}

	log.Info("UpdateIpAndPortOfflineStateJob", "成功更新foradar_assets记录", map[string]interface{}{
		"user_id":           userId,
		"ip":                ip,
		"is_all_port":       portInfo.IsAllPort,
		"scan_ports":        portInfo.Ports,
		"query_description": queryDescription,
	})

	return nil
}

func uint64SliceToInterfaceSlice(slice []uint64) []interface{} {
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}
	return result
}

// parsePayloadForUpdateIpAndPortOfflineState 解析任务载荷
func parsePayloadForUpdateIpAndPortOfflineState(payload []byte) (*asyncq.UpdateIpAndPortOfflineStateJobPayload, error) {
	var payloadInfo asyncq.UpdateIpAndPortOfflineStateJobPayload
	err := json.Unmarshal(payload, &payloadInfo)
	if err != nil {
		log.Error("UpdateIpAndPortOfflineStateJob", "解析payload失败", map[string]interface{}{
			"payload": string(payload),
			"error":   err.Error(),
		})
		return nil, err
	}

	// 验证必填字段
	if payloadInfo.UserId == 0 {
		return nil, fmt.Errorf("用户ID不能为空")
	}
	if payloadInfo.TaskId == 0 {
		return nil, fmt.Errorf("任务ID不能为空")
	}

	return &payloadInfo, nil
}
