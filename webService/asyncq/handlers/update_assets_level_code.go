package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/pkg/log"
	"micro-service/pkg/network"
	asyncq "micro-service/pkg/queue_helper"

	"micro-service/pkg/dns"
)

func NewUpdateAssetsLevelCodePayload(userID uint64, assetsID string, subDomain, url, protocol string) *asyncq.UpdateAssetsLevelCodePayload {
	return &asyncq.UpdateAssetsLevelCodePayload{
		UserID:    userID,
		SubDomain: subDomain,
		Url:       url,
		Protocol:  protocol,
		AssetId:   assetsID,
	}
}

// UpdateAssetsLevelCodeHandler 处理更新资产等级代码的任务
func UpdateAssetsLevelCodeHandler(ctx context.Context, t *asyncq.Task) error {
	var task asyncq.UpdateAssetsLevelCodePayload
	if err := json.Unmarshal([]byte(t.Payload), &task); err != nil {
		return fmt.Errorf("解析任务数据失败: %v", err)
	}
	log.Infof("更新资产level code开始 - user_id: %d, sub_domain: %s, protocol: %s", task.UserID, task.SubDomain, task.Protocol)
	subDomain := task.SubDomain
	if subDomain != "" {
		getAAAARecords(subDomain)
		getARecords(subDomain)
	}
	network.GetUrlStatusCode(task.Url, task.AssetId)
	return nil
}

// getAAAARecords 解析域名的IPv6地址
func getAAAARecords(domain string) []string {
	return dns.GetAAAARecords(domain, "*********:53")
}

// getARecords 解析域名的IPv4地址
func getARecords(domain string) ([]string, string) {
	return dns.GetARecords(domain, "*********:53"), ""
}
