package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/pkg/icp"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"time"

	asyncq "micro-service/pkg/queue_helper"
)

// ICPQueryJob 异步ICP查询任务处理器
func ICPQueryJob(ctx context.Context, payload []byte) error {
	log.Info("ICPQueryJob", "开始处理异步ICP查询任务", nil)

	var p asyncq.ICPQueryJobPayload
	if err := json.Unmarshal(payload, &p); err != nil {
		log.Error("ICPQueryJob", "解析任务载荷失败", err)
		return err
	}

	log.Info("ICPQueryJob", "任务参数", map[string]interface{}{
		"clue_id":      p.Clue<PERSON>,
		"query_type":   p.QueryType,
		"query_value":  p.QueryValue,
		"user_id":      p.UserId,
		"group_id":     p.GroupId,
		"is_subdomain": p.IsSubdomain,
	})

	// 创建ICP查询实例
	icpQuery := icp.NewICPQuery(redis.GetInstance())

	// 设置较长的超时时间，避免超时
	queryCtx, cancel := context.WithTimeout(ctx, 3*time.Minute)
	defer cancel()

	var companyName string
	var err error

	// 根据查询类型执行不同的查询
	switch p.QueryType {
	case "domain":
		result, queryErr := icpQuery.QueryDomain(queryCtx, p.QueryValue, false, false, false, p.UserId)
		if queryErr == nil && result != nil && result.Info != nil {
			if name, ok := result.Info["company_name"].(string); ok && name != "" {
				companyName = name
			}
		}
		err = queryErr

	case "icp":
		result, queryErr := icpQuery.QueryICP(queryCtx, p.QueryValue, false, false, p.UserId)
		if queryErr == nil && result != nil && result.Info != nil {
			if name, ok := result.Info["company_name"].(string); ok && name != "" {
				companyName = name
			}
		}
		err = queryErr

	case "subdomain":
		// 先尝试查询子域名
		result, queryErr := icpQuery.QueryDomain(queryCtx, p.QueryValue, false, false, true, p.UserId)
		if queryErr == nil && result != nil && result.Info != nil {
			if name, ok := result.Info["company_name"].(string); ok && name != "" {
				companyName = name
			}
		}

		// 如果子域名查不到，尝试查询顶级域名
		if companyName == "" {
			topDomain := utils.GetTopDomain(p.QueryValue)
			if topDomain != "" && topDomain != p.QueryValue {
				result, queryErr := icpQuery.QueryDomain(queryCtx, topDomain, false, false, false, p.UserId)
				if queryErr == nil && result != nil && result.Info != nil {
					if name, ok := result.Info["company_name"].(string); ok && name != "" {
						companyName = name
					}
				}
			}
		}
		err = queryErr

	default:
		log.Error("ICPQueryJob", "不支持的查询类型", map[string]interface{}{
			"query_type": p.QueryType,
		})
		return fmt.Errorf("不支持的查询类型: %s", p.QueryType)
	}

	// 记录查询结果
	if err != nil {
		log.Warn("ICPQueryJob", "ICP查询失败，但不影响整体流程", map[string]interface{}{
			"clue_id":     p.ClueID,
			"query_type":  p.QueryType,
			"query_value": p.QueryValue,
			"error":       err.Error(),
		})
		// ICP查询失败不返回错误，避免任务重试
		return nil
	}

	// 如果查询到公司名称，更新线索记录
	if companyName != "" {
		log.Info("ICPQueryJob", "查询到公司名称，更新线索记录", map[string]interface{}{
			"clue_id":      p.ClueID,
			"company_name": companyName,
		})

		err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", p.ClueID).
			Update("clue_company_name", companyName).Error

		if err != nil {
			log.Error("ICPQueryJob", "更新线索公司名称失败", map[string]interface{}{
				"clue_id": p.ClueID,
				"error":   err.Error(),
			})
			return err
		}

		log.Info("ICPQueryJob", "成功更新线索公司名称", map[string]interface{}{
			"clue_id":      p.ClueID,
			"company_name": companyName,
		})
	} else {
		log.Info("ICPQueryJob", "未查询到公司名称", map[string]interface{}{
			"clue_id":     p.ClueID,
			"query_type":  p.QueryType,
			"query_value": p.QueryValue,
		})
	}

	log.Info("ICPQueryJob", "异步ICP查询任务处理完成", map[string]interface{}{
		"clue_id":      p.ClueID,
		"company_name": companyName,
	})

	return nil
}

// ICPQueryJobHandler 异步ICP查询任务处理器包装函数
func ICPQueryJobHandler(ctx context.Context, task *asyncq.Task) error {
	return ICPQueryJob(ctx, []byte(task.Payload))
}
