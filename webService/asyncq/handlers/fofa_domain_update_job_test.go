package handlers

import (
	"encoding/json"
	"errors"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestParsePayloadForFofaDomainUpdate(t *testing.T) {
	tests := []struct {
		name    string
		payload []byte
		want    *FofaDomainUpdateJobPayload
		wantErr error
	}{
		{
			name:    "正常解析",
			payload: []byte(`{"type": 1, "user_id": 1}`),
			want: &FofaDomainUpdateJobPayload{
				Type:   1,
				UserId: 1,
			},
			wantErr: nil,
		},
		{
			name:    "解析失败",
			payload: []byte(`{"type": 1, "user_id": 0`),
			want:    nil,
			wantErr: &json.SyntaxError{},
		},
		{
			name:    "用户ID为空",
			payload: []byte(`{"type": 1, "user_id": 0}`),
			want:    nil,
			wantErr: errors.New("用户ID不能为空"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parsePayloadForFofaDomainUpdate(tt.payload)

			if tt.wantErr != nil {
				assert.Error(t, err)
				if _, ok := tt.wantErr.(*json.SyntaxError); ok {
					assert.IsType(t, tt.wantErr, err)
				} else {
					assert.EqualError(t, err, tt.wantErr.Error())
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}
