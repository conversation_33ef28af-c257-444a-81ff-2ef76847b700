package handlers

import (
	"context"
	"encoding/json"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asynq "micro-service/pkg/queue_helper"
)

// 初始化测试环境
func init() {
	cfg.InitLoadCfg()
	log.Init()
}

// MockGithubOneForAllQuery 模拟github.OneForAllQuery函数
type MockGithubOneForAllQuery struct {
	mock.Mock
}

func (m *MockGithubOneForAllQuery) OneForAllQuery(ctx context.Context, domain string, title string, bruteforce bool) ([]map[string]string, error) {
	args := m.Called(ctx, domain, title, bruteforce)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]map[string]string), args.Error(1)
}

// createTestOneForAllTask 创建测试任务
func createTestOneForAllTask(payload *asynq.OneForAllJobPayload) *asynq.Task {
	data, _ := json.Marshal(payload)
	return &asynq.Task{
		Type:    "oneforall_job",
		Payload: string(data),
	}
}

// createValidOneForAllPayload 创建有效的OneForAll任务载荷
func createValidOneForAllPayload() *asynq.OneForAllJobPayload {
	return &asynq.OneForAllJobPayload{
		Domain: "example.com",
		Title:  "测试标题",
		UserId: "123",
	}
}

// createInvalidOneForAllPayload 创建无效的OneForAll任务载荷
func createInvalidOneForAllPayload() *asynq.OneForAllJobPayload {
	return &asynq.OneForAllJobPayload{
		Domain: "", // 空域名，应该导致错误
		Title:  "测试标题",
		UserId: "123",
	}
}

// TestParseOneForAllPayload 测试parseOneForAllPayload函数
func TestParseOneForAllPayload(t *testing.T) {
	tests := []struct {
		name        string
		payload     interface{}
		expectError bool
		description string
	}{
		{
			name:        "有效的JSON载荷",
			payload:     createValidOneForAllPayload(),
			expectError: false,
			description: "有效的JSON应该成功解析",
		},
		{
			name:        "域名为空的载荷",
			payload:     createInvalidOneForAllPayload(),
			expectError: true,
			description: "空域名应该返回错误",
		},
		{
			name: "无效的JSON格式",
			payload: map[string]interface{}{
				"invalid": "data",
			},
			expectError: true,
			description: "缺少domain字段应该返回错误",
		},
		{
			name: "domain字段为空字符串",
			payload: map[string]interface{}{
				"domain":  "",
				"title":   "test",
				"user_id": "123",
			},
			expectError: true,
			description: "空字符串domain应该返回错误",
		},
		{
			name: "只有domain字段",
			payload: map[string]interface{}{
				"domain": "onlydomain.com",
			},
			expectError: false,
			description: "只有domain字段应该成功解析",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 序列化测试数据
			data, err := json.Marshal(tt.payload)
			assert.NoError(t, err, "测试数据序列化不应该失败")

			// 创建任务
			task := &asynq.Task{
				Type:    "oneforall_job",
				Payload: string(data),
			}

			// 测试解析函数
			result, err := parseOneForAllPayload(task)

			if tt.expectError {
				assert.Error(t, err, tt.description)
				assert.Nil(t, result, "错误情况下结果应该为nil")
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, result, "成功情况下结果不应该为nil")
				assert.NotEmpty(t, result.Domain, "解析后的domain不应该为空")
			}
		})
	}
}

// TestParseOneForAllPayload_InvalidJSON 测试无效JSON的解析
func TestParseOneForAllPayload_InvalidJSON(t *testing.T) {
	// 创建包含无效JSON的任务
	task := &asynq.Task{
		Type:    "oneforall_job",
		Payload: `{"domain": "test.com", "invalid_json"}`,
	}

	result, err := parseOneForAllPayload(task)
	assert.Error(t, err, "无效JSON应该返回错误")
	assert.Nil(t, result, "无效JSON解析结果应该为nil")
}

// TestParseOneForAllPayload_EmptyPayload 测试空载荷
func TestParseOneForAllPayload_EmptyPayload(t *testing.T) {
	task := &asynq.Task{
		Type:    "oneforall_job",
		Payload: "",
	}

	result, err := parseOneForAllPayload(task)
	assert.Error(t, err, "空载荷应该返回错误")
	assert.Nil(t, result, "空载荷解析结果应该为nil")
}

// TestOneForAllJobPayload_Serialization 测试OneForAllJobPayload序列化
func TestOneForAllJobPayload_Serialization(t *testing.T) {
	original := &asynq.OneForAllJobPayload{
		Domain: "serialization-test.com",
		Title:  "序列化测试",
		UserId: "789",
	}

	// 序列化
	data, err := json.Marshal(original)
	assert.NoError(t, err, "序列化不应该失败")

	// 反序列化
	var decoded asynq.OneForAllJobPayload
	err = json.Unmarshal(data, &decoded)
	assert.NoError(t, err, "反序列化不应该失败")

	// 验证数据一致性
	assert.Equal(t, original.Domain, decoded.Domain, "Domain字段应该一致")
	assert.Equal(t, original.Title, decoded.Title, "Title字段应该一致")
	assert.Equal(t, original.UserId, decoded.UserId, "UserId字段应该一致")
}

// TestParseOneForAllPayload_DataTypes 测试不同数据类型
func TestParseOneForAllPayload_DataTypes(t *testing.T) {
	tests := []struct {
		name        string
		payload     map[string]interface{}
		expectError bool
		description string
	}{
		{
			name: "字符串类型的user_id",
			payload: map[string]interface{}{
				"domain":  "string-userid.com",
				"title":   "字符串用户ID",
				"user_id": "123",
			},
			expectError: false,
			description: "字符串类型的user_id应该正常解析",
		},
		{
			name: "数字类型的user_id",
			payload: map[string]interface{}{
				"domain":  "numeric-userid.com",
				"title":   "数字用户ID",
				"user_id": 123,
			},
			expectError: true,
			description: "数字类型的user_id应该导致JSON解析错误",
		},
		{
			name: "布尔类型的title",
			payload: map[string]interface{}{
				"domain":  "boolean-title.com",
				"title":   true,
				"user_id": "123",
			},
			expectError: true,
			description: "布尔类型的title应该导致JSON解析错误",
		},
		{
			name: "null值的title",
			payload: map[string]interface{}{
				"domain":  "null-title.com",
				"title":   nil,
				"user_id": "123",
			},
			expectError: false,
			description: "null值的title应该正常处理",
		},
		{
			name: "数组类型的domain",
			payload: map[string]interface{}{
				"domain":  []string{"array-domain.com"},
				"title":   "数组域名",
				"user_id": "123",
			},
			expectError: true,
			description: "数组类型的domain应该导致解析错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data, err := json.Marshal(tt.payload)
			assert.NoError(t, err, "测试数据序列化不应该失败")

			task := &asynq.Task{
				Type:    "oneforall_job",
				Payload: string(data),
			}

			result, err := parseOneForAllPayload(task)

			if tt.expectError {
				assert.Error(t, err, tt.description)
				assert.Nil(t, result, "错误情况下结果应该为nil")
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, result, "成功情况下结果不应该为nil")
			}
		})
	}
}

// TestOneForAllJobPayload_FieldValidation 测试字段验证
func TestOneForAllJobPayload_FieldValidation(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		title    string
		userId   string
		isValid  bool
		errorMsg string
	}{
		{
			name:    "有效的标准域名",
			domain:  "example.com",
			title:   "标准测试",
			userId:  "123",
			isValid: true,
		},
		{
			name:    "有效的子域名",
			domain:  "sub.example.com",
			title:   "子域名测试",
			userId:  "456",
			isValid: true,
		},
		{
			name:     "空域名",
			domain:   "",
			title:    "空域名测试",
			userId:   "789",
			isValid:  false,
			errorMsg: "domain解析为空",
		},
		{
			name:    "只有空格的域名",
			domain:  "   ",
			title:   "空格域名测试",
			userId:  "101",
			isValid: false,
		},
		{
			name:    "极长域名",
			domain:  "a" + strings.Repeat("very-long-subdomain.", 10) + "example.com",
			title:   "极长域名测试",
			userId:  "102",
			isValid: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			payload := &asynq.OneForAllJobPayload{
				Domain: tt.domain,
				Title:  tt.title,
				UserId: tt.userId,
			}

			task := createTestOneForAllTask(payload)
			result, err := parseOneForAllPayload(task)

			if tt.isValid {
				assert.NoError(t, err, "有效载荷不应该返回错误")
				assert.NotNil(t, result, "有效载荷应该返回结果")
				assert.Equal(t, tt.domain, result.Domain, "域名应该匹配")
			} else {
				assert.Error(t, err, "无效载荷应该返回错误")
				assert.Nil(t, result, "无效载荷不应该返回结果")
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg, "错误信息应该包含预期内容")
				}
			}
		})
	}
}
