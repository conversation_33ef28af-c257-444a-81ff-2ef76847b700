package handlers

import (
	"errors"
	"fmt"
	"reflect"
	"strings"
	"testing"

	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql/clues"
	"micro-service/pkg/utils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestExtractInfoFromAsset(t *testing.T) {
	type args struct {
		asset map[string]interface{}
		key   string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "正常提取",
			args: args{
				asset: map[string]interface{}{
					"key": "value",
				},
				key: "key",
			},
			want: []string{"value"},
		},
		{
			name: "空值提取",
			args: args{
				asset: map[string]interface{}{
					"key": "",
				},
				key: "key",
			},
			want: []string{},
		},
		{
			name: "key不存在",
			args: args{
				asset: map[string]interface{}{
					"key": "value",
				},
				key: "key2",
			},
			want: []string{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := extractInfoFromAsset(tt.args.asset, tt.args.key); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AssetsImportProcessor.extractInfoFromAsset() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generatePortAssetId(t *testing.T) {
	type args struct {
		ip     string
		asset  map[string]interface{}
		url    string
		userId uint64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "正常生成",
			args: args{
				ip: "***********",
				asset: map[string]interface{}{
					"*端口": "8080",
					"*协议": "http",
				},
				url:    "http://***********:8080",
				userId: 1,
			},
			// want: ""***********8080http1"",
			want: "ff18ce4438c65950c8ce689a5307c973",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generatePortAssetId(tt.args.ip, tt.args.asset, tt.args.url, tt.args.userId); got != tt.want {
				t.Errorf("generatePortAssetId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_threatToInt(t *testing.T) {
	type args struct {
		threatType string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			name: "正常转换",
			args: args{
				threatType: "其他",
			},
			want: 0,
		},
		{
			name: "正常转换",
			args: args{
				threatType: "钓鱼仿冒",
			},
			want: 1,
		},
		{
			name: "正常转换",
			args: args{
				threatType: "ICP盗用",
			},
			want: 3,
		},
		{
			name: "正常转换",
			args: args{
				threatType: "黄赌毒网站",
			},
			want: 2,
		},
		{
			name: "正常转换",
			args: args{
				threatType: "域名混淆",
			},
			want: 4,
		},
		{
			name: "正常转换",
			args: args{
				threatType: "其他不存在",
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := threatToInt(tt.args.threatType); got != tt.want {
				t.Errorf("threatToInt() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestAssetsImportProcessor_parseReason 测试 parseReason 方法
func TestAssetsImportProcessor_parseReason(t *testing.T) {
	tests := []struct {
		name             string
		reasonStrs       []string
		excelCompanyName string
		setupMock        func(*clues.MockCluer)
		expectedReasons  int
		expectedContent  string
		description      string
	}{
		{
			name:             "正常情况_根域类型_找到线索",
			reasonStrs:       []string{"根据测试公司的根域example.com推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				clue := clues.Clue{
					Status: clues.CLUE_DEFAULT_STATUS, // 待确认状态
				}
				clue.Id = 1
				m.On("First", mock.Anything).Return(clue, nil)
				m.On("UpdateAny", mock.Anything, mock.Anything).Return(nil)
			},
			expectedReasons: 1,
			expectedContent: "example.com",
			description:     "测试正常解析根域类型推荐原因，线索状态更新",
		},
		{
			name:             "正常情况_证书类型_线索已通过",
			reasonStrs:       []string{"根据-的证书*.example.com推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				clue := clues.Clue{
					Status: clues.CLUE_PASS_STATUS, // 已通过状态
				}
				clue.Id = 2
				m.On("First", mock.Anything).Return(clue, nil)
				// 不需要更新状态
			},
			expectedReasons: 1,
			expectedContent: "*.example.com",
			description:     "测试证书类型，线索已是通过状态，不需要更新",
		},
		{
			name:             "正常情况_ICP类型",
			reasonStrs:       []string{"根据腾讯公司的ICP腾讯科技推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				clue := clues.Clue{
					Status: clues.CLUE_DEFAULT_STATUS,
				}
				clue.Id = 3
				m.On("First", mock.Anything).Return(clue, nil)
				m.On("UpdateAny", mock.Anything, mock.Anything).Return(nil)
			},
			expectedReasons: 1,
			expectedContent: "腾讯科技",
			description:     "测试ICP类型推荐原因解析",
		},
		{
			name:             "正常情况_ICON类型",
			reasonStrs:       []string{"根据百度的ICONbaidu_logo推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				clue := clues.Clue{
					Status: clues.CLUE_DEFAULT_STATUS,
				}
				clue.Id = 4
				m.On("First", mock.Anything).Return(clue, nil)
				m.On("UpdateAny", mock.Anything, mock.Anything).Return(nil)
			},
			expectedReasons: 1,
			expectedContent: "baidu_logo",
			description:     "测试ICON类型推荐原因解析",
		},
		{
			name:             "正常情况_关键词类型",
			reasonStrs:       []string{"根据阿里巴巴的关键词阿里云推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				clue := clues.Clue{
					Status: clues.CLUE_DEFAULT_STATUS,
				}
				clue.Id = 5
				m.On("First", mock.Anything).Return(clue, nil)
				m.On("UpdateAny", mock.Anything, mock.Anything).Return(nil)
			},
			expectedReasons: 1,
			expectedContent: "阿里云",
			description:     "测试关键词类型推荐原因解析",
		},
		{
			name:             "正常情况_子域名类型",
			reasonStrs:       []string{"根据京东的子域名www.jd.com推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				clue := clues.Clue{
					Status: clues.CLUE_DEFAULT_STATUS,
				}
				clue.Id = 6
				m.On("First", mock.Anything).Return(clue, nil)
				m.On("UpdateAny", mock.Anything, mock.Anything).Return(nil)
			},
			expectedReasons: 1,
			expectedContent: "www.jd.com",
			description:     "测试子域名类型推荐原因解析",
		},
		{
			name:             "多个推荐原因_分号分隔",
			reasonStrs:       []string{"根据测试公司的根域example.com推荐;根据测试公司的证书*.example.com推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				// 第一个线索
				clue1 := clues.Clue{
					Status: clues.CLUE_DEFAULT_STATUS,
				}
				clue1.Id = 7
				// 第二个线索
				clue2 := clues.Clue{
					Status: clues.CLUE_DEFAULT_STATUS,
				}
				clue2.Id = 8
				m.On("First", mock.Anything).Return(clue1, nil).Once()
				m.On("First", mock.Anything).Return(clue2, nil).Once()
				m.On("UpdateAny", mock.Anything, mock.Anything).Return(nil).Times(2)
			},
			expectedReasons: 2,
			description:     "测试分号分隔的多个推荐原因",
		},
		{
			name:             "未找到线索",
			reasonStrs:       []string{"根据不存在公司的根域notfound.com推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				m.On("First", mock.Anything).Return(clues.Clue{}, errors.New("记录不存在"))
				// 当线索不存在时，会尝试创建新线索
				m.On("Create", mock.AnythingOfType("*clues.Clue")).Return(nil).Run(func(args mock.Arguments) {
					clue := args.Get(0).(*clues.Clue)
					clue.Id = 1 // 模拟数据库分配的ID
				})
			},
			expectedReasons: 1, // 创建新线索后应该返回1个推荐原因
			description:     "测试线索不存在时创建新线索的情况",
		},
		{
			name:             "线索ID为0",
			reasonStrs:       []string{"根据测试公司的根域example.com推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				clue := clues.Clue{
					Status: clues.CLUE_DEFAULT_STATUS,
				}
				clue.Id = 0 // ID为0
				m.On("First", mock.Anything).Return(clue, nil)
				// 当线索ID为0时，也会尝试创建新线索
				m.On("Create", mock.AnythingOfType("*clues.Clue")).Return(nil).Run(func(args mock.Arguments) {
					clue := args.Get(0).(*clues.Clue)
					clue.Id = 1 // 模拟数据库分配的ID
				})
			},
			expectedReasons: 1, // 创建新线索后应该返回1个推荐原因
			description:     "测试线索ID为0时创建新线索的情况",
		},
		{
			name:             "无效的线索类型",
			reasonStrs:       []string{"根据测试公司的无效类型invalid_content推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				// 不会调用数据库查询
			},
			expectedReasons: 0,
			description:     "测试无效线索类型",
		},
		{
			name:             "空的推荐原因",
			reasonStrs:       []string{},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				// 不会调用数据库查询
			},
			expectedReasons: 0,
			description:     "测试空的推荐原因数组",
		},
		{
			name:             "格式不匹配的推荐原因",
			reasonStrs:       []string{"这是一个格式错误的推荐原因"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				// 不会调用数据库查询
			},
			expectedReasons: 0,
			description:     "测试格式不匹配的推荐原因",
		},
		{
			name:             "状态更新失败",
			reasonStrs:       []string{"根据测试公司的根域example.com推荐"},
			excelCompanyName: "默认公司",
			setupMock: func(m *clues.MockCluer) {
				clue := clues.Clue{
					Status: clues.CLUE_DEFAULT_STATUS,
				}
				clue.Id = 9
				m.On("First", mock.Anything).Return(clue, nil)
				m.On("UpdateAny", mock.Anything, mock.Anything).Return(errors.New("更新失败"))
			},
			expectedReasons: 1, // 即使更新失败，仍会返回结果
			description:     "测试线索状态更新失败的情况",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock
			mockCluer := new(clues.MockCluer)
			tt.setupMock(mockCluer)

			// 创建处理器实例
			processor := &AssetsImportProcessor{
				UserId:    1,
				GroupId:   1,
				clueModel: mockCluer,
			}

			// 执行测试
			result := processor.parseReason(tt.reasonStrs, tt.excelCompanyName)

			// 验证结果
			assert.Equal(t, tt.expectedReasons, len(result), fmt.Sprintf("测试失败: %s", tt.description))

			if tt.expectedReasons > 0 && tt.expectedContent != "" {
				assert.Equal(t, tt.expectedContent, result[0]["content"], "解析的内容不匹配")
				assert.Equal(t, processor.GroupId, result[0]["group_id"], "group_id不匹配")
				assert.Equal(t, clues.SOURCE_IMPORT, result[0]["source"], "source不匹配")
			}

			// 验证mock调用
			mockCluer.AssertExpectations(t)
		})
	}
}

// TestAssetsImportProcessor_parseReason_正则表达式 专门测试正则表达式匹配
func TestAssetsImportProcessor_parseReason_正则表达式(t *testing.T) {
	mockCluer := new(clues.MockCluer)

	// 返回一个ID为0的线索，这会触发创建新线索的逻辑
	mockCluer.On("First", mock.Anything).Return(clues.Clue{}, nil)
	// 添加Create方法的mock
	mockCluer.On("Create", mock.AnythingOfType("*clues.Clue")).Return(nil).Run(func(args mock.Arguments) {
		clue := args.Get(0).(*clues.Clue)
		clue.Id = 1 // 模拟数据库分配的ID
	})

	tests := []struct {
		name            string
		reasonStr       string
		expectMatch     bool
		expectedType    string
		expectedContent string
		expectedCompany string
		description     string
	}{
		{
			name:            "标准格式_根域",
			reasonStr:       "根据腾讯公司的根域tencent.com推荐",
			expectMatch:     true,
			expectedType:    "根域",
			expectedContent: "tencent.com",
			expectedCompany: "腾讯公司",
			description:     "测试标准格式的根域匹配",
		},
		{
			name:            "标准格式_证书",
			reasonStr:       "根据阿里巴巴的证书*.alibaba.com推荐",
			expectMatch:     true,
			expectedType:    "证书",
			expectedContent: "*.alibaba.com",
			expectedCompany: "阿里巴巴",
			description:     "测试标准格式的证书匹配",
		},
		{
			name:            "公司名为短横线",
			reasonStr:       "根据-的根域example.com推荐",
			expectMatch:     true,
			expectedType:    "根域",
			expectedContent: "example.com",
			expectedCompany: "-",
			description:     "测试公司名为短横线的情况",
		},
		{
			name:            "复杂内容_带特殊字符",
			reasonStr:       "根据测试公司的ICP京ICP备12345678号推荐",
			expectMatch:     true,
			expectedType:    "ICP",
			expectedContent: "京ICP备12345678号",
			expectedCompany: "测试公司",
			description:     "测试带特殊字符的内容",
		},
		{
			name:        "不匹配_缺少推荐",
			reasonStr:   "根据测试公司的根域example.com",
			expectMatch: false,
			description: "测试缺少'推荐'关键字的情况",
		},
		{
			name:        "不匹配_无类型",
			reasonStr:   "根据测试公司的example.com推荐",
			expectMatch: false,
			description: "测试缺少线索类型的情况",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor := &AssetsImportProcessor{
				UserId:    1,
				GroupId:   1,
				clueModel: mockCluer,
			}

			result := processor.parseReason([]string{tt.reasonStr}, "默认公司")

			if tt.expectMatch {
				// 对于期望匹配的情况，会创建新线索，所以结果应该为1
				assert.Equal(t, 1, len(result), fmt.Sprintf("测试失败: %s", tt.description))
			} else {
				assert.Equal(t, 0, len(result), fmt.Sprintf("测试失败: %s", tt.description))
			}
		})
	}
}

// TestAssetsImportProcessor_parseReason_类型映射 测试类型映射功能
func TestAssetsImportProcessor_parseReason_类型映射(t *testing.T) {
	mockCluer := new(clues.MockCluer)

	// 设置mock返回成功的线索查找
	clue := clues.Clue{
		Status: clues.CLUE_DEFAULT_STATUS,
	}
	clue.Id = 1
	mockCluer.On("First", mock.Anything).Return(clue, nil)
	mockCluer.On("UpdateAny", mock.Anything, mock.Anything).Return(nil)

	processor := &AssetsImportProcessor{
		UserId:    1,
		GroupId:   1,
		clueModel: mockCluer,
	}

	typeTests := map[string]int{
		"根域":   clues.TYPE_DOMAIN,
		"证书":   clues.TYPE_CERT,
		"ICP":  clues.TYPE_ICP,
		"ICON": clues.TYPE_LOGO,
		"关键词":  clues.TYPE_KEYWORD,
		"子域名":  clues.TYPE_SUBDOMAIN,
	}

	for typeName, expectedTypeVal := range typeTests {
		t.Run(fmt.Sprintf("类型映射_%s", typeName), func(t *testing.T) {
			reasonStr := fmt.Sprintf("根据测试公司的%stest_content推荐", typeName)
			result := processor.parseReason([]string{reasonStr}, "默认公司")

			assert.Equal(t, 1, len(result), fmt.Sprintf("类型%s应该返回一个结果", typeName))
			if len(result) > 0 {
				assert.Equal(t, expectedTypeVal, result[0]["type"], fmt.Sprintf("类型%s的映射值不正确", typeName))
			}
		})
	}
}

// TestAssetsImportProcessor_doneIpPortAssetData 测试 doneIpPortAssetData 方法
func TestAssetsImportProcessor_doneIpPortAssetData(t *testing.T) {
	tests := []struct {
		name           string
		ip             string
		assets         []map[string]interface{}
		oldPortAssets  []*foradar_assets.ForadarAsset
		setStatus      int
		setupProcessor func(*AssetsImportProcessor)
		expectedAssets int
		expectedInsert bool
		expectedError  bool
		description    string
	}{
		{
			name:          "正常情况_单个端口资产_新建",
			ip:            "***********",
			assets:        createTestAssetData("80", "http", "example.com", "在线", "用户-扫描"),
			oldPortAssets: []*foradar_assets.ForadarAsset{},
			setStatus:     1,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
			},
			expectedAssets: 1,
			expectedInsert: true,
			expectedError:  false,
			description:    "测试正常创建单个端口资产",
		},
		{
			name:   "去重逻辑_已存在的资产ID",
			ip:     "***********",
			assets: createTestAssetData("80", "http", "example.com", "在线", "用户-扫描"),
			oldPortAssets: []*foradar_assets.ForadarAsset{
				{
					ID: generateTestAssetId("***********", "80", "http", "example.com", 123),
				},
			},
			setStatus: 1,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
			},
			expectedAssets: 0,
			expectedInsert: false,
			expectedError:  false,
			description:    "测试资产ID已存在时的去重逻辑",
		},
		{
			name:           "边界情况_空的assets数据",
			ip:             "***********",
			assets:         []map[string]interface{}{},
			oldPortAssets:  []*foradar_assets.ForadarAsset{},
			setStatus:      1,
			setupProcessor: func(p *AssetsImportProcessor) {},
			expectedAssets: 0,
			expectedInsert: false,
			expectedError:  false,
			description:    "测试空的assets数据",
		},
		{
			name:          "资产状态映射_离线状态",
			ip:            "***********",
			assets:        createTestAssetData("443", "https", "secure.com", "离线", "安服-扫描"),
			oldPortAssets: []*foradar_assets.ForadarAsset{},
			setStatus:     1,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
			},
			expectedAssets: 1,
			expectedInsert: true,
			expectedError:  false,
			description:    "测试离线状态的资产映射",
		},
		{
			name:          "资产标签映射_多种标签类型",
			ip:            "***********",
			assets:        createTestAssetData("22", "ssh", "admin.com", "在线", "安服-推荐"),
			oldPortAssets: []*foradar_assets.ForadarAsset{},
			setStatus:     1,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
			},
			expectedAssets: 1,
			expectedInsert: true,
			expectedError:  false,
			description:    "测试不同标签类型的映射",
		},
		{
			name:          "时间解析_有效时间格式",
			ip:            "***********",
			assets:        createTestAssetDataWithTime("8080", "http", "app.com", "在线", "用户-推荐", "2023-12-01 10:30:00"),
			oldPortAssets: []*foradar_assets.ForadarAsset{},
			setStatus:     1,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
			},
			expectedAssets: 1,
			expectedInsert: true,
			expectedError:  false,
			description:    "测试有效时间格式的解析",
		},
		{
			name:          "推荐原因处理_探测方式包含推荐",
			ip:            "***********",
			assets:        createTestAssetDataWithDetection("3306", "mysql", "db.com", "在线", "用户-推荐", "根据测试公司的根域example.com推荐"),
			oldPortAssets: []*foradar_assets.ForadarAsset{},
			setStatus:     1,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
				// Mock parseReason返回值
				mockCluer := new(clues.MockCluer)
				clue := clues.Clue{Status: clues.CLUE_PASS_STATUS}
				clue.Id = 1
				mockCluer.On("First", mock.Anything).Return(clue, nil)
				p.clueModel = mockCluer
			},
			expectedAssets: 1,
			expectedInsert: true,
			expectedError:  false,
			description:    "测试探测方式包含推荐的处理",
		},
		{
			name:          "威胁类型处理_威胁状态",
			ip:            "***********",
			assets:        createTestAssetDataWithThreat("8888", "http", "threat.com", "在线", "用户-扫描", "钓鱼仿冒"),
			oldPortAssets: []*foradar_assets.ForadarAsset{},
			setStatus:     fofaee_assets.STATUS_THREATEN,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
			},
			expectedAssets: 1,
			expectedInsert: true,
			expectedError:  false,
			description:    "测试威胁类型的处理",
		},
		{
			name:          "组件信息解析_规则标签",
			ip:            "***********",
			assets:        createTestAssetDataWithComponents("80", "http", "web.com", "在线", "用户-扫描", "Apache,MySQL,PHP"),
			oldPortAssets: []*foradar_assets.ForadarAsset{},
			setStatus:     1,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
			},
			expectedAssets: 1,
			expectedInsert: true,
			expectedError:  false,
			description:    "测试组件信息的解析和规则标签构建",
		},
		{
			name:          "多个端口资产_批量处理",
			ip:            "***********",
			assets:        createMultiplePortAssets(),
			oldPortAssets: []*foradar_assets.ForadarAsset{},
			setStatus:     1,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
			},
			expectedAssets: 3,
			expectedInsert: true,
			expectedError:  false,
			description:    "测试多个端口资产的批量处理",
		},
		{
			name: "类型转换错误_无效资产数据",
			ip:   "***********",
			assets: []map[string]interface{}{
				{
					"invalid_asset": "not_a_map",
				},
			},
			oldPortAssets: []*foradar_assets.ForadarAsset{},
			setStatus:     1,
			setupProcessor: func(p *AssetsImportProcessor) {
				p.UserId = 123
				p.GroupId = 456
				p.CompanyId = 789
			},
			expectedAssets: 1, // 即使数据无效，也会创建资产记录
			expectedInsert: true,
			expectedError:  false,
			description:    "测试无效的资产数据类型转换",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建处理器实例
			processor := &AssetsImportProcessor{}
			tt.setupProcessor(processor)

			// 执行测试
			result, isInsert, err := processor.doneIpPortAssetData(tt.ip, tt.assets, tt.oldPortAssets, tt.setStatus)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err, fmt.Sprintf("测试失败: %s", tt.description))
			} else {
				assert.NoError(t, err, fmt.Sprintf("测试失败: %s", tt.description))
			}

			assert.Equal(t, tt.expectedAssets, len(result), fmt.Sprintf("资产数量不匹配: %s", tt.description))
			assert.Equal(t, tt.expectedInsert, isInsert, fmt.Sprintf("插入标志不匹配: %s", tt.description))

			// 验证资产详细信息
			if len(result) > 0 {
				asset := result[0]
				assert.Equal(t, strings.ToLower(utils.CompleteIPV6(tt.ip)), asset.Ip, "IP地址不匹配")
				assert.Equal(t, int(processor.UserId), asset.UserID, "用户ID不匹配")
				assert.Equal(t, int(processor.CompanyId), asset.CompanyID, "企业ID不匹配")
				assert.Equal(t, tt.setStatus, asset.Status, "状态不匹配")
			}
		})
	}
}

// generateTestAssetId 生成测试用的资产ID
func generateTestAssetId(ip, port, protocol, domain string, userId uint64) string {
	url := fmt.Sprintf("%s://%s:%s", protocol, domain, port)
	asset := map[string]interface{}{
		"*端口": port,
		"*协议": protocol,
	}
	return generatePortAssetId(ip, asset, url, userId)
}

// createTestAssetData 创建测试用的资产数据
func createTestAssetData(port, protocol, domain, status, tag string) []map[string]interface{} {
	return []map[string]interface{}{
		{
			"*端口":  port,
			"*协议":  protocol,
			"域名":   domain,
			"URL":  fmt.Sprintf("%s://%s:%s", protocol, domain, port),
			"资产状态": status,
			"资产标签": tag,
			"网站标题": "Test Title",
			"状态码":  200,
			"地理位置": "北京",
			"企业名称": "测试公司",
		},
	}
}

// createTestAssetDataWithTime 创建包含时间的测试资产数据
func createTestAssetDataWithTime(port, protocol, domain, status, tag, updateTime string) []map[string]interface{} {
	data := createTestAssetData(port, protocol, domain, status, tag)
	if assetMap := data[0]; assetMap != nil {
		assetMap["更新时间"] = updateTime
	}
	return data
}

// createTestAssetDataWithDetection 创建包含探测方式的测试资产数据
func createTestAssetDataWithDetection(port, protocol, domain, status, tag, detection string) []map[string]interface{} {
	data := createTestAssetData(port, protocol, domain, status, tag)
	if assetMap := data[0]; assetMap != nil {
		assetMap["探测方式"] = detection
	}
	return data
}

// createTestAssetDataWithThreat 创建包含威胁类型的测试资产数据
func createTestAssetDataWithThreat(port, protocol, domain, status, tag, threatType string) []map[string]interface{} {
	data := createTestAssetData(port, protocol, domain, status, tag)
	if assetMap := data[0]; assetMap != nil {
		assetMap["威胁类型"] = threatType
	}
	return data
}

// createTestAssetDataWithComponents 创建包含组件信息的测试资产数据
func createTestAssetDataWithComponents(port, protocol, domain, status, tag, components string) []map[string]interface{} {
	data := createTestAssetData(port, protocol, domain, status, tag)
	if assetMap := data[0]; assetMap != nil {
		assetMap["组件信息"] = components
	}
	return data
}

// createMultiplePortAssets 创建多个端口的测试资产数据
func createMultiplePortAssets() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"*端口":  "80",
			"*协议":  "http",
			"域名":   "web.com",
			"URL":  "http://web.com:80",
			"资产状态": "在线",
			"资产标签": "用户-扫描",
			"网站标题": "Web Server",
			"状态码":  200,
		},
		{
			"*端口":  "443",
			"*协议":  "https",
			"域名":   "secure.com",
			"URL":  "https://secure.com:443",
			"资产状态": "在线",
			"资产标签": "用户-扫描",
			"网站标题": "Secure Server",
			"状态码":  200,
		},
		{
			"*端口":  "22",
			"*协议":  "ssh",
			"域名":   "admin.com",
			"URL":  "ssh://admin.com:22",
			"资产状态": "在线",
			"资产标签": "用户-扫描",
			"网站标题": "SSH Server",
			"状态码":  0,
		},
	}
}

// TestAssetsImportProcessor_doneIpPortAssetData_资产状态映射 专门测试资产状态映射
func TestAssetsImportProcessor_doneIpPortAssetData_资产状态映射(t *testing.T) {
	tests := []struct {
		name          string
		assetStatus   string
		expectedState int
		description   string
	}{
		{
			name:          "在线状态",
			assetStatus:   "在线",
			expectedState: 1,
			description:   "测试在线状态映射",
		},
		{
			name:          "离线状态",
			assetStatus:   "离线",
			expectedState: 0,
			description:   "测试离线状态映射",
		},
		{
			name:          "未知状态",
			assetStatus:   "未知",
			expectedState: 0,
			description:   "测试未知状态默认为离线",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor := &AssetsImportProcessor{
				UserId:    123,
				GroupId:   456,
				CompanyId: 789,
			}

			assets := createTestAssetData("80", "http", "test.com", tt.assetStatus, "用户-扫描")
			result, isInsert, err := processor.doneIpPortAssetData("***********", assets, []*foradar_assets.ForadarAsset{}, 1)

			assert.NoError(t, err, fmt.Sprintf("测试失败: %s", tt.description))
			assert.True(t, isInsert, "应该插入新资产")
			assert.Equal(t, 1, len(result), "应该返回一个资产")
			assert.Equal(t, tt.expectedState, result[0].OnlineState, fmt.Sprintf("在线状态不匹配: %s", tt.description))
		})
	}
}

// TestAssetsImportProcessor_doneIpPortAssetData_资产标签映射 专门测试资产标签映射
func TestAssetsImportProcessor_doneIpPortAssetData_资产标签映射(t *testing.T) {
	tests := []struct {
		name        string
		assetTag    string
		expectedTag int
		description string
	}{
		{
			name:        "用户扫描",
			assetTag:    "用户-扫描",
			expectedTag: 0,
			description: "测试用户扫描标签映射",
		},
		{
			name:        "安服扫描",
			assetTag:    "安服-扫描",
			expectedTag: 1,
			description: "测试安服扫描标签映射",
		},
		{
			name:        "用户推荐",
			assetTag:    "用户-推荐",
			expectedTag: 2,
			description: "测试用户推荐标签映射",
		},
		{
			name:        "安服推荐",
			assetTag:    "安服-推荐",
			expectedTag: 3,
			description: "测试安服推荐标签映射",
		},
		{
			name:        "安服导入",
			assetTag:    "安服-导入",
			expectedTag: 4,
			description: "测试安服导入标签映射",
		},
		{
			name:        "未知标签",
			assetTag:    "未知标签",
			expectedTag: 4, // 默认值
			description: "测试未知标签默认值",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor := &AssetsImportProcessor{
				UserId:    123,
				GroupId:   456,
				CompanyId: 789,
			}

			assets := createTestAssetData("80", "http", "test.com", "在线", tt.assetTag)
			result, isInsert, err := processor.doneIpPortAssetData("***********", assets, []*foradar_assets.ForadarAsset{}, 1)

			assert.NoError(t, err, fmt.Sprintf("测试失败: %s", tt.description))
			assert.True(t, isInsert, "应该插入新资产")
			assert.Equal(t, 1, len(result), "应该返回一个资产")
			assert.Equal(t, []int{tt.expectedTag}, result[0].Tags, fmt.Sprintf("标签不匹配: %s", tt.description))
		})
	}
}
