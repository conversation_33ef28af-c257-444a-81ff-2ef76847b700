package handlers

import (
	"encoding/json"
	asynq "micro-service/pkg/queue_helper"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParsePayloadForTagAssets(t *testing.T) {
	// 测试正常的payload
	payload := asynq.TagAssetsJobPayload{
		UserId: 123,
		TaskId: 456,
	}

	data, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Marshal error: %v", err)
	}

	result, err := parsePayloadForTagAssets(data)
	if err != nil {
		t.Fatalf("Parse error: %v", err)
	}

	if result.UserId != 123 {
		t.<PERSON><PERSON><PERSON>("Expected UserId 123, got %d", result.UserId)
	}

	if result.TaskId != 456 {
		t.<PERSON><PERSON>("Expected TaskId 456, got %d", result.TaskId)
	}
}

func TestParsePayloadForTagAssets_InvalidData(t *testing.T) {
	// 测试无效的payload
	_, err := parsePayloadForTagAssets([]byte("invalid json"))
	if err == nil {
		t.Error("Expected error for invalid JSON")
	}
}

func TestParsePayloadForTagAssets_MissingUserId(t *testing.T) {
	// 测试缺少UserId的payload
	payload := asynq.TagAssetsJobPayload{
		TaskId: 456,
	}

	data, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Marshal error: %v", err)
	}

	_, err = parsePayloadForTagAssets(data)
	if err == nil {
		t.Error("Expected error for missing UserId")
	}
}

func TestParsePayloadForTagAssets_MissingTaskId(t *testing.T) {
	// 测试缺少TaskId的payload
	payload := asynq.TagAssetsJobPayload{
		UserId: 123,
	}

	data, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Marshal error: %v", err)
	}

	_, err = parsePayloadForTagAssets(data)
	if err == nil {
		t.Error("Expected error for missing TaskId")
	}
}

// TestProcessSpecialIPFormat 测试特殊IP格式处理
func TestProcessSpecialIPFormat(t *testing.T) {
	tests := []struct {
		name     string
		ip       string
		checkIp  string
		expected bool
	}{
		// 单个IP测试
		{
			name:     "单个IP匹配",
			ip:       "***********",
			checkIp:  "***********",
			expected: true,
		},
		{
			name:     "单个IP不匹配",
			ip:       "***********",
			checkIp:  "***********",
			expected: false,
		},
		// CIDR格式测试
		{
			name:     "CIDR格式匹配",
			ip:       "***********0",
			checkIp:  "***********/24",
			expected: true,
		},
		{
			name:     "CIDR格式不匹配",
			ip:       "************",
			checkIp:  "***********/24",
			expected: false,
		},
		{
			name:     "CIDR格式/32匹配",
			ip:       "**********",
			checkIp:  "**********/32",
			expected: true,
		},
		// IP范围测试（连字符格式）
		{
			name:     "IP范围匹配-简写格式",
			ip:       "***********",
			checkIp:  "**********-100",
			expected: true,
		},
		{
			name:     "IP范围不匹配-简写格式",
			ip:       "**********50",
			checkIp:  "**********-100",
			expected: false,
		},
		{
			name:     "IP范围匹配-完整格式",
			ip:       "************",
			checkIp:  "***********-***********00",
			expected: true,
		},
		{
			name:     "IP范围不匹配-完整格式",
			ip:       "***********50",
			checkIp:  "***********-***********00",
			expected: false,
		},
		// 通配符网段测试
		{
			name:     "通配符网段匹配",
			ip:       "*************",
			checkIp:  "192.168.1-10.*",
			expected: true,
		},
		{
			name:     "通配符网段不匹配",
			ip:       "**************",
			checkIp:  "192.168.1-10.*",
			expected: false,
		},
		{
			name:     "通配符网段边界测试-起始",
			ip:       "***********",
			checkIp:  "192.168.1-10.*",
			expected: true,
		},
		{
			name:     "通配符网段边界测试-结束",
			ip:       "**************",
			checkIp:  "192.168.1-10.*",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processSpecialIPFormat(tt.ip, tt.checkIp)
			assert.Equal(t, tt.expected, result, "IP: %s, CheckIP: %s", tt.ip, tt.checkIp)
		})
	}
}
