package handlers

import (
	"encoding/json"
	"testing"
)

func TestParsePayloadForDeleteTaskAssets(t *testing.T) {
	t.Run("SuccessCase", func(t *testing.T) {
		payload := &DeleteTaskAssetsJobPayload{
			TaskIds:    []string{"task1", "task2"},
			UserId:     123,
			RunningIds: []uint64{1, 2, 3},
		}
		data, _ := json.Marshal(payload)
		result, err := parsePayloadForDeleteTaskAssets(data)
		if err != nil {
			t.<PERSON><PERSON><PERSON>("Expected no error, but got: %v", err)
		}
		if result == nil || result.UserId != payload.UserId ||
			len(result.TaskIds) != len(payload.TaskIds) || len(result.RunningIds) != len(payload.RunningIds) {
			t.<PERSON>rrorf("Expected payload values not matched")
		}
	})

	t.Run("InvalidJSON", func(t *testing.T) {
		invalidJSON := []byte(`{ "task_ids": ["task1", "task2"], "user_id": 123`)
		_, err := parsePayloadForDeleteTaskAssets(invalidJSON)
		if err == nil {
			t.<PERSON>("Expected an error for invalid JSON, but got none")
		}
	})

	t.Run("MissingUserId", func(t *testing.T) {
		payload := &DeleteTaskAssetsJobPayload{
			TaskIds:    []string{"task1", "task2"},
			RunningIds: []uint64{1, 2, 3},
		}
		data, _ := json.Marshal(payload)
		_, err := parsePayloadForDeleteTaskAssets(data)
		if err == nil || err.Error() != "用户ID不能为空" {
			t.Errorf("Expected error '用户ID不能为空', but got: %v", err)
		}
	})

	t.Run("UserIdZero", func(t *testing.T) {
		payload := &DeleteTaskAssetsJobPayload{
			UserId:     0,
			TaskIds:    []string{"task1", "task2"},
			RunningIds: []uint64{1, 2, 3},
		}
		data, _ := json.Marshal(payload)
		_, err := parsePayloadForDeleteTaskAssets(data)
		if err == nil || err.Error() != "用户ID不能为空" {
			t.Errorf("Expected error '用户ID不能为空', but got: %v", err)
		}
	})

	t.Run("EmptyPayload", func(t *testing.T) {
		_, err := parsePayloadForDeleteTaskAssets([]byte{})
		if err == nil {
			t.Errorf("Expected an error for empty payload, but got none")
		}
	})

	t.Run("NullPayload", func(t *testing.T) {
		_, err := parsePayloadForDeleteTaskAssets(nil)
		if err == nil {
			t.Errorf("Expected an error for nil payload, but got none")
		}
	})
}
