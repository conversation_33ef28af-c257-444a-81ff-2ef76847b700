package handlers

import (
	"github.com/stretchr/testify/assert"
	"micro-service/middleware/mysql/scan_task"
	"testing"
)

func TestFilterAndValidateIps(t *testing.T) {
	type args struct {
		ips    []string
		ipType int
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr error
	}{
		{
			name: "包含IPv4、IPv6和特殊标记",
			args: args{
				ips:    []string{"***********", "fe80::1", "subdomain", "invalid-ip"},
				ipType: scan_task.IP_TYPE_V4,
			},
			want: []string{"***********"},
		},
		{
			name: "纯IPv4列表",
			args: args{
				ips:    []string{"***********", "********"},
				ipType: scan_task.IP_TYPE_V4,
			},
			want: []string{"***********", "********"},
		},
		{
			name: "纯IPv6列表",
			args: args{
				ips:    []string{"fe80::1", "2001:db8::1"},
				ipType: scan_task.IP_TYPE_V6,
			},
			want: []string{"fe80::1", "2001:db8::1"},
		},
		{
			name: "空列表",
			args: args{
				ips:    []string{},
				ipType: scan_task.IP_TYPE_V4,
			},
			want: nil,
		},
		{
			name: "IPv4任务中包含IPv6地址",
			args: args{
				ips:    []string{"***********", "fe80::1"},
				ipType: scan_task.IP_TYPE_V4,
			},
			want: []string{"***********"},
		},
		{
			name: "IPv6任务中包含IPv4地址",
			args: args{
				ips:    []string{"fe80::1", "***********"},
				ipType: scan_task.IP_TYPE_V6,
			},
			want: []string{"fe80::1"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := filterAndValidateIps(tt.args.ips, tt.args.ipType)
			assert.NoError(t, err)
			assert.Equal(t, tt.want, got)
		})
	}
}
