package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/middleware/mysql/scan_pocs"
	"micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/websocket_message"

	es_utils "micro-service/middleware/elastic"

	"github.com/google/uuid"
	"github.com/olivere/elastic"
)

// UpdateRiskIpCountJob 更新风险IP数量任务
func UpdateRiskIpCountJob(ctx context.Context, task *asyncq.Task) error {
	// 解析任务参数
	var payload asyncq.UpdateRiskIpCountJobPayload
	if err := json.Unmarshal([]byte(task.Payload), &payload); err != nil {
		log.Errorf("UpdateRiskIpCountJob 解析任务参数失败: %v", err)
		return err
	}

	// 检查是否是beta节点
	if cfg.LoadAPP().Beta {
		log.Infof("UpdateRiskIpCountJob beta节点不更新风险ip数量!，当前节点是: %s", cfg.LoadAPP().Beta)
		return nil
	}

	// 检查Redis锁
	lockKey := cache.GetLockKey("risk_ip", fmt.Sprintf("%d", payload.UserId))
	if isTaskLocked(lockKey) {
		log.Infof("UpdateRiskIpCountJob 最近1小时内更新过风险资产，不再更新了: %d", payload.UserId)
		return nil
	}

	// 设置分布式锁
	riskTaskId := uuid.New().String()
	if err := setTaskLock(payload.UserId, riskTaskId, lockKey); err != nil {
		log.Errorf("UpdateRiskIpCountJob 设置分布式锁失败: %v", err)
		return err
	}
	defer clearTaskLock(payload.UserId, riskTaskId)

	startTime := time.Now()
	log.Info("UpdateRiskIpCountJob", "更新风险ip数量-开始", map[string]interface{}{
		"user_id": payload.UserId,
		"time":    startTime.Format("2006-01-02 15:04:05"),
		"node":    cfg.LoadCommon().Node,
	})

	// 执行风险IP统计更新
	if err := processRiskIpUpdate(ctx, payload.UserId); err != nil {
		log.Errorf("UpdateRiskIpCountJob 更新风险ip数量失败: %v", err)
		return err
	}

	// 设置完成缓存
	if err := setFinishCache(payload.UserId); err != nil {
		log.Warnf("UpdateRiskIpCountJob 设置完成缓存失败: %v", err)
	}

	// 发送WebSocket消息
	if err := sendWebSocketMessage(payload.UserId); err != nil {
		log.Warnf("UpdateRiskIpCountJob 发送WebSocket消息失败: %v", err)
	}

	costTime := time.Since(startTime)
	log.Info("UpdateRiskIpCountJob", "更新风险ip数量-完成", map[string]interface{}{
		"user_id": payload.UserId,
		"time":    time.Now().Format("2006-01-02 15:04:05"),
		"cost":    costTime,
	})

	return nil
}

// isTaskLocked 检查任务是否被锁定
func isTaskLocked(lockKey string) bool {
	var val string
	err := redis.Get(context.Background(), lockKey, &val)
	if err != nil {
		return false
	}
	return val != ""
}

// setTaskLock 设置任务锁
func setTaskLock(userId uint64, riskTaskId string, lockKey string) error {
	// 设置主锁（10分钟）
	if ok := redis.Lock(lockKey, 10*time.Minute); !ok {
		return fmt.Errorf("设置主锁失败")
	}

	// 设置任务锁（2小时50分钟）
	taskKey := cache.GetLockKey("risk_ip_task", fmt.Sprintf("%d", userId))
	taskValue := fmt.Sprintf("%d-%s", userId, riskTaskId)
	ok := redis.Lock(taskKey, 170*time.Minute, taskValue)
	if !ok {
		return fmt.Errorf("设置任务锁失败")
	}
	return nil
}

// clearTaskLock 清除任务锁
func clearTaskLock(userId uint64, riskTaskId string) {
	taskKey := cache.GetLockKey("risk_ip_task", fmt.Sprintf("%d", userId))
	taskValue := fmt.Sprintf("%d-%s", userId, riskTaskId)
	redis.UnLock(taskKey, taskValue)
}

// setFinishCache 设置完成缓存
func setFinishCache(userId uint64) error {
	key := fmt.Sprintf("finshRiskipCount:user_id:%d", userId)
	return redis.Set(context.Background(), key, time.Now().Format("2006-01-02 15:04:05"), 365*24*time.Hour)
}

// sendWebSocketMessage 发送WebSocket消息
func sendWebSocketMessage(userId uint64) error {
	return websocket_message.PublishSuccess(int64(userId), "finish_risk_ip_count", map[string]interface{}{
		"progress": 100,
		"user_id":  userId,
	})
}

// processRiskIpUpdate 处理风险IP统计更新
func processRiskIpUpdate(ctx context.Context, userId uint64) error {
	// 检查是否有资产数据
	hasAssets, err := checkUserAssets(ctx, userId)
	if err != nil {
		return fmt.Errorf("检查用户资产失败: %v", err)
	}
	if !hasAssets {
		// 没有资产数据，将所有风险数量设为0
		return resetAllRiskCounts(ctx, userId)
	}

	// 查询用户的POC列表
	pocModel := scan_pocs.NewScanPocModel()
	pocList, err := pocModel.FindUserAssociatedPocs(userId)
	if err != nil {
		return fmt.Errorf("查询用户POC列表失败: %v", err)
	}
	// 获取台账IP列表
	tableIps, err := getTableIps(ctx, userId)
	if err != nil {
		return fmt.Errorf("获取台账IP列表失败: %v", err)
	}
	log.Infof("UpdateRiskIpCountJob 没有全部删除资产数据，用户ID: %d", userId)

	// 分批处理POC
	return processPocBatches(ctx, pocList, userId, tableIps)
}

// checkUserAssets 检查用户是否有资产数据
func checkUserAssets(ctx context.Context, userId uint64) (bool, error) {
	// 查询ForadarAssets
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermsQuery("status", 1, 2)) // STATUS_CLAIMED=1, STATUS_UPLOAD=2

	count, err := es_utils.GetCount(foradar_assets.IndexName, query)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// getTableIps 获取台账IP列表
func getTableIps(ctx context.Context, userId uint64) ([]string, error) {
	// 查询fofaee_assets获取IP列表
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermsQuery("status", 1, 2)) // STATUS_CLAIMED=1, STATUS_UPLOAD=2

	assets, err := es_utils.All[fofaee_assets.FofaeeAssets](500, query, nil, "ip")
	if err != nil {
		return nil, err
	}

	ips := make([]string, 0, len(assets))
	for _, asset := range assets {
		ips = append(ips, asset.Ip)
	}

	return ips, nil
}

// resetAllRiskCounts 重置所有风险数量为0
func resetAllRiskCounts(ctx context.Context, userId uint64) error {
	// 更新intelligence_user_hot_poc表，将所有风险数量设为0
	userHotPocModel := intelligence.NewUserHotPoc()
	updateData := map[string]interface{}{
		"risk_num": 0,
	}

	q := []mysql.HandleFunc{
		mysql.WithWhere("risk_num > ?", 0),
		mysql.WithWhere("user_id = ?", userId),
	}
	err := userHotPocModel.UpdateAny(updateData, q...)
	if err != nil {
		return fmt.Errorf("重置风险数量失败: %v", err)
	}

	log.Infof("UpdateRiskIpCountJob 更新风险ip数量完成!风险数量设置为0，用户ID: %d", userId)
	return nil
}

// processPocBatches 分批处理POC
func processPocBatches(ctx context.Context, pocList []*scan_pocs.ScanPoc, userId uint64, tableIps []string) error {
	batchSize := 200

	for i := 0; i < len(pocList); i += batchSize {
		end := i + batchSize
		if end > len(pocList) {
			end = len(pocList)
		}

		batch := pocList[i:end]

		if err := processPocBatch(ctx, batch, userId, tableIps); err != nil {
			log.Warnf("UpdateRiskIpCountJob 处理POC批次失败: %v", err)
			// 继续处理下一批，不中断整个任务
		}

		log.Infof("UpdateRiskIpCountJob 更新完一批漏洞的风险资产数量，用户ID: %d", userId)
	}

	return nil
}

// processPocBatch 处理单个POC批次
func processPocBatch(ctx context.Context, pocs []*scan_pocs.ScanPoc, userId uint64, tableIps []string) error {
	var updates []*intelligence.UserHotPoc

	for _, poc := range pocs {
		riskNum, err := countRiskIp(ctx, poc, userId, tableIps)
		if err != nil {
			log.Warnf("UpdateRiskIpCountJob 计算POC[%s]风险IP数量失败: %v", poc.Filename, err)
			riskNum = 0 // 出错时设为0
		}

		if riskNum > 0 {
			log.Infof("UpdateRiskIpCountJob 计算出风险数量: %d, POC: %s", riskNum, poc.Filename)
		}

		updates = append(updates, &intelligence.UserHotPoc{
			UserId:    userId,
			PocId:     poc.ID,
			RiskCount: riskNum,
		})
	}

	if len(updates) > 0 {
		userHotPocModel := intelligence.NewUserHotPoc()
		return userHotPocModel.Save(updates)
	}

	return nil
}

// countRiskIp 计算风险IP数量
func countRiskIp(ctx context.Context, poc *scan_pocs.ScanPoc, userId uint64, tableIps []string) (int, error) {
	// 使用缓存
	cacheKey := cache.GetCacheKey("count_risk_ip", fmt.Sprintf("%s_%d", poc.Filename, userId))

	var cachedResult int
	if redis.GetCache(cacheKey, &cachedResult) {
		return cachedResult, nil
	}

	queryStr := ""
	// LOG4J，直接返回简单计数
	if poc.Filename == scan_pocs.LOG4J2_FILENAME {
		// todo 写死的查询语句，但是看起来不像fofa的语句
		queryStr = ""
	} else {
		// todo 通过FOFA parse方法获取查询语句
		queryStr = ""
	}
	log.Debugf("UpdateRiskIpCountJob 查询语句: %s", queryStr)

	// 对于其他POC，暂时返回简单的资产计数
	count := 0
	// todo 调用FOFA的searchFofaQueryByAsset方法查询数量

	// 缓存结果（1分钟）
	redis.SetCache(cacheKey, 1*time.Minute, count)

	return count, nil
}
