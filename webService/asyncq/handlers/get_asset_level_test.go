package handlers

import (
	testcommon "micro-service/initialize/common_test"
	"testing"

	"micro-service/initialize/es"

	"github.com/DATA-DOG/go-sqlmock"
	mysqlDriver "gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// setupMockDB 创建mock数据库连接
func setupMockDB(t *testing.T) (*gorm.DB, sqlmock.Sqlmock) {
	// 设置测试环境标志，让ES使用mock
	es.SetTestEnv(true)
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("Failed to create mock: %v", err)
	}

	// Mock GORM 初始化时的查询
	mock.ExpectQuery("SELECT VERSION\\(\\)").WillReturnRows(sqlmock.NewRows([]string{"VERSION()"}).AddRow("8.0.0"))

	// 创建GORM实例
	gormDB, err := gorm.Open(mysqlDriver.New(mysqlDriver.Config{
		Conn:                      db,
		SkipInitializeWithVersion: false,
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to create gorm instance: %v", err)
	}

	return gormDB, mock
}

// mockRecommendRecordES mock推荐记录ES查询
func mockRecommendRecordES() {
	testcommon.SetTestEnv(true)
	// 获取mock ES客户端
	mockServer := es.NewMockServer()

	// Mock推荐记录查询 - 返回空结果模拟404
	mockServer.Register("/foradar_recommend_record/record/*", map[string]interface{}{
		"found": false,
	})
	testcommon.SetElasticClient(mockServer.NewElasticMockClient())
	// 设置mock客户端
	es.SetElasticMockClient(mockServer.NewElasticMockClient())
}
