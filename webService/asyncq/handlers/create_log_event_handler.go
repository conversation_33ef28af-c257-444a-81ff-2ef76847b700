package handlers

import (
	"context"
	"encoding/json"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/middleware/mysql/user"
	asyncq "micro-service/pkg/queue_helper"
	"time"
)

func CreateLogEventHandler(ctx context.Context, task *asyncq.Task) error {
	payload := &asyncq.CreateLogEventPayload{}
	if err := json.Unmarshal([]byte(task.Payload), payload); err != nil {
		return err
	}
	userModel := user.NewUserModel()
	userInfo, err := userModel.FindById(payload.UserId)
	if err != nil {
		return err
	}
	_, err = mysql.NewDSL[operate_logs.OperateLogs]().Create(operate_logs.OperateLogs{
		UserId:    payload.UserId,
		Content:   payload.Content,
		Ip:        payload.Ip,
		CompanyId: payload.CompanyId,
		Model:     payload.ModelType,
		Type:      payload.Type,
		Username:  userInfo.Name.String,
		Role:      int(userInfo.Role),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	})

	return err
}
