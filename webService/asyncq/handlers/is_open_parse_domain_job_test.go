package handlers

import (
	"context"
	"errors"
	"fmt"
	"micro-service/initialize/es"
	sqlFac "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asynq "micro-service/pkg/queue_helper"
	"net"
	"strconv"
	"strings"
	"testing"

	"github.com/alicebob/miniredis/v2"
	microZap "github.com/go-micro/plugins/v4/logger/zap"
	"github.com/hashicorp/go-hclog"
	"github.com/stretchr/testify/require"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"
)

func init() {
	cfg.InitLoadCfg()
	// 初始化日志
	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// Mysql&Redis&ES
	_ = sqlFac.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	_ = es.GetInstance(cfg.LoadElastic())

	// Setup mock redis
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	defer s.Close()
	num, err := strconv.ParseUint(s.Port(), 10, 16)
	if err != nil {
		fmt.Println("转换失败：", err)
		panic(err)
	}

	redis.GetInstance(cfg.Redis{
		Address: strings.Split(s.Addr(), ":")[0],
		Port:    uint16(num),
	})
}

func TestIsOpenParseDomain_CacheMissParseSuccess(t *testing.T) {

	// 启动一个本地TCP服务器用于测试
	listener, err := net.Listen("tcp", ":0")
	require.NoError(t, err)
	defer listener.Close()

	port := listener.Addr().(*net.TCPAddr).Port
	_ = fmt.Sprintf("localhost:%d", port) // 保留端口信息用于未来扩展

	// 启动服务器
	go func() {
		for {
			conn, err := listener.Accept()
			if err != nil {
				return
			}
			conn.Close()
		}
	}()
}

func TestParsePayloadForIsOpenParseDomainJob(t *testing.T) {
	testCases := []struct {
		name        string
		payload     []byte
		expectedErr error
	}{
		{
			name:        "Valid payload",
			payload:     []byte(`{"user_id":1,"sub_domain":"test.example.com","assets_id":10}`),
			expectedErr: nil,
		},
		{
			name:        "Invalid JSON",
			payload:     []byte(`{"user_id":1,"sub_domain":"test.example.com"`),
			expectedErr: errors.New("unexpected end of JSON input"),
		},
		{
			name:        "Missing sub_domain",
			payload:     []byte(`{"user_id":1,"assets_id":10}`),
			expectedErr: errors.New("sub_domain is required"),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			payloadInfo, err := parsePayloadForIsOpenParseDomainJob(tc.payload)
			if tc.expectedErr != nil {
				if err == nil {
					t.Errorf("Expected error, got nil")
				}
				if err.Error() != tc.expectedErr.Error() {
					t.Errorf("Expected error %v, got %v", tc.expectedErr, err)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
				if payloadInfo == nil {
					t.Error("Expected payloadInfo to be non-nil")
				}
			}
		})
	}
}

func TestIsOpenParseDomainJob(t *testing.T) {
	testCases := []struct {
		name    string
		payload []byte
		err     error
	}{
		{
			name:    "Valid task",
			payload: []byte(`{"user_id":1,"sub_domain":"test.example.com","assets_id":10}`),
			err:     nil,
		},
		{
			name:    "Invalid task payload",
			payload: []byte(`{"user_id":1,"assets_id":10}`),
			err:     errors.New("sub_domain is required"),
		},
		{
			name:    "Empty sub domain",
			payload: []byte(`{"user_id":1,"sub_domain":"","assets_id":10}`),
			err:     errors.New("sub_domain is required"),
		},
		{
			name:    "GetTopDomain returns empty",
			payload: []byte(`{"user_id":1,"sub_domain":"invalid","assets_id":10}`),
			err:     nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			task := &asynq.Task{
				Payload: string(tc.payload),
			}
			err := IsOpenParseDomainJob(context.Background(), task)
			if tc.err != nil {
				if err == nil {
					t.Errorf("Expected error, got nil")
				}
				if tc.err.Error() != err.Error() {
					t.Errorf("Expected error %v, got %v", tc.err, err)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}
