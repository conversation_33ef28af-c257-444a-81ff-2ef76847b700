package handlers

import (
	"fmt"
	"reflect"
	"testing"
)

func TestExtractCommonTitles(t *testing.T) {
	tests := []struct {
		name     string
		threats  []map[string]interface{}
		expected []string
	}{
		{
			name: "正常提取标题",
			threats: []map[string]interface{}{
				{"common_title": "威胁1", "other_field": "value1"},
				{"common_title": "威胁2", "other_field": "value2"},
				{"common_title": "威胁3", "other_field": "value3"},
			},
			expected: []string{"威胁1", "威胁2", "威胁3"},
		},
		{
			name: "包含重复标题",
			threats: []map[string]interface{}{
				{"common_title": "威胁1", "other_field": "value1"},
				{"common_title": "威胁1", "other_field": "value2"},
				{"common_title": "威胁2", "other_field": "value3"},
			},
			expected: []string{"威胁1", "威胁2"},
		},

		{
			name: "某些威胁没有common_title字段",
			threats: []map[string]interface{}{
				{"common_title": "威胁1", "other_field": "value1"},
				{"other_field": "value2"},
				{"common_title": "威胁3", "other_field": "value3"},
			},
			expected: []string{"威胁1", "威胁3"},
		},
		{
			name: "某些威胁的common_title不是字符串",
			threats: []map[string]interface{}{
				{"common_title": "威胁1", "other_field": "value1"},
				{"common_title": 123, "other_field": "value2"},
				{"common_title": "威胁3", "other_field": "value3"},
			},
			expected: []string{"威胁1", "威胁3"},
		},
		{
			name: "某些威胁的common_title为空字符串",
			threats: []map[string]interface{}{
				{"common_title": "威胁1", "other_field": "value1"},
				{"common_title": "", "other_field": "value2"},
				{"common_title": "威胁3", "other_field": "value3"},
			},
			expected: []string{"威胁1", "威胁3"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := extractCommonTitles(tt.threats)
			if !reflect.DeepEqual(got, tt.expected) {
				t.Errorf("extractCommonTitles() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestFindNewThreats(t *testing.T) {
	tests := []struct {
		name     string
		current  []string
		existing []string
		want     []string
	}{

		// 3. 现有威胁为空，当前威胁有值
		{
			name:     "existing_empty",
			current:  []string{"threat1", "threat2"},
			existing: []string{},
			want:     []string{"threat1", "threat2"},
		},

		//  部分匹配，有新威胁
		{
			name:     "some_new_threats",
			current:  []string{"threat1", "threat2", "threat3"},
			existing: []string{"threat1", "threat3"},
			want:     []string{"threat2"},
		},

		//  完全没有匹配，所有都是新威胁
		{
			name:     "all_new_threats",
			current:  []string{"threatA", "threatB"},
			existing: []string{"threatX", "threatY"},
			want:     []string{"threatA", "threatB"},
		},

		//  当前威胁中有重复项
		{
			name:     "duplicates_in_current",
			current:  []string{"threat1", "threat1", "threat2"},
			existing: []string{"threat1"},
			want:     []string{"threat2"},
		},

		// 现有威胁中有重复项
		{
			name:     "duplicates_in_existing",
			current:  []string{"threat1", "threat2"},
			existing: []string{"threat1", "threat1", "threat1"},
			want:     []string{"threat2"},
		},

		//  大小写敏感测试
		{
			name:     "case_sensitivity",
			current:  []string{"Threat1", "threat2"},
			existing: []string{"threat1", "THREAT2"},
			want:     []string{"Threat1", "threat2"},
		},

		//  顺序保持不变
		{
			name:     "order_preserved",
			current:  []string{"threatC", "threatA", "threatB"},
			existing: []string{"threatA"},
			want:     []string{"threatC", "threatB"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := findNewThreats(tt.current, tt.existing)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("findNewThreats() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParsePayloadForRecordIpHistoryJob(t *testing.T) {
	tests := []struct {
		name        string
		payload     string
		wantPayload *RecordIpHistoryJobPayload
		wantErr     error
	}{
		//  完全有效的payload
		{
			name: "valid_payload",
			payload: `{
				"user_id": 123,
				"company_id": 456,
				"ip": "***********",
				"data": {"key1": "value1", "key2": 2},
				"threat_data": [{"threat": "malware"}, {"severity": "high"}],
				"data_type": 1,
				"is_check": true
			}`,
			wantPayload: &RecordIpHistoryJobPayload{
				UserId:    123,
				CompanyId: 456,
				Ip:        "***********",
				Data:      map[string]interface{}{"key1": "value1", "key2": float64(2)},
				ThreatData: []map[string]interface{}{
					{"threat": "malware"},
					{"severity": "high"},
				},
				DataType: 1,
				IsCheck:  true,
			},
			wantErr: nil,
		},

		//  最小有效payload
		{
			name: "minimal_valid_payload",
			payload: `{
				"user_id": 1,
				"company_id": 2,
				"ip": "127.0.0.1"
			}`,
			wantPayload: &RecordIpHistoryJobPayload{
				UserId:    1,
				CompanyId: 2,
				Ip:        "127.0.0.1",
			},
			wantErr: nil,
		},

		//  缺少user_id
		{
			name: "missing_user_id",
			payload: `{
				"company_id": 456,
				"ip": "***********"
			}`,
			wantErr: fmt.Errorf("用户ID不能为空"),
		},

		//  user_id为0
		{
			name: "zero_user_id",
			payload: `{
				"user_id": 0,
				"company_id": 456,
				"ip": "***********"
			}`,
			wantErr: fmt.Errorf("用户ID不能为空"),
		},

		//  缺少ip
		{
			name: "missing_ip",
			payload: `{
				"user_id": 123,
				"company_id": 456
			}`,
			wantErr: fmt.Errorf("IP地址不能为空"),
		},

		//  ip为空字符串
		{
			name: "empty_ip",
			payload: `{
				"user_id": 123,
				"company_id": 456,
				"ip": ""
			}`,
			wantErr: fmt.Errorf("IP地址不能为空"),
		},

		//  ip为空格
		{
			name: "whitespace_ip",
			payload: `{
				"user_id": 123,
				"company_id": 456,
				"ip": "   "
			}`,
			wantErr: fmt.Errorf("IP地址不能为空"),
		},

		//  可选字段为空
		{
			name: "optional_fields_empty",
			payload: `{
				"user_id": 123,
				"company_id": 456,
				"ip": "*******",
				"data": {},
				"threat_data": [],
				"data_type": 0,
				"is_check": false
			}`,
			wantPayload: &RecordIpHistoryJobPayload{
				UserId:     123,
				CompanyId:  456,
				Ip:         "*******",
				Data:       map[string]interface{}{},
				ThreatData: []map[string]interface{}{},
				DataType:   0,
				IsCheck:    false,
			},
			wantErr: nil,
		},

		//  包含额外字段
		{
			name: "extra_fields",
			payload: `{
				"user_id": 123,
				"company_id": 456,
				"ip": "********",
				"extra_field": "should be ignored"
			}`,
			wantPayload: &RecordIpHistoryJobPayload{
				UserId:    123,
				CompanyId: 456,
				Ip:        "********",
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parsePayloadForRecordIpHistoryJob([]byte(tt.payload))

			// 检查错误
			if tt.wantErr != nil {
				if err == nil {
					t.Fatalf("期望错误 %v, 但未返回错误,name:%s", tt.wantErr, tt.name)
				}
				if err.Error() != tt.wantErr.Error() {
					t.Fatalf("期望错误 %v, 实际错误 %v,name:%s", tt.wantErr, err, tt.name)
				}
				return
			}

			if err != nil {
				t.Fatalf("不期望的错误: %v", err)
			}

			// 检查payload内容
			if got.UserId != tt.wantPayload.UserId {
				t.Errorf("UserId 不匹配: 期望 %d, 实际 %d", tt.wantPayload.UserId, got.UserId)
			}

			if got.CompanyId != tt.wantPayload.CompanyId {
				t.Errorf("CompanyId 不匹配: 期望 %d, 实际 %d", tt.wantPayload.CompanyId, got.CompanyId)
			}

			if got.Ip != tt.wantPayload.Ip {
				t.Errorf("Ip 不匹配: 期望 %s, 实际 %s", tt.wantPayload.Ip, got.Ip)
			}

			// 检查可选字段
			if tt.wantPayload.Data != nil {
				if len(got.Data) != len(tt.wantPayload.Data) {
					t.Errorf("Data 长度不匹配: 期望 %d, 实际 %d", len(tt.wantPayload.Data), len(got.Data))
				}
				// 更深入的检查可以在这里添加
			}

			if tt.wantPayload.ThreatData != nil {
				if len(got.ThreatData) != len(tt.wantPayload.ThreatData) {
					t.Errorf("ThreatData 长度不匹配: 期望 %d, 实际 %d", len(tt.wantPayload.ThreatData), len(got.ThreatData))
				}
			}

			if got.DataType != tt.wantPayload.DataType {
				t.Errorf("DataType 不匹配: 期望 %d, 实际 %d", tt.wantPayload.DataType, got.DataType)
			}

			if got.IsCheck != tt.wantPayload.IsCheck {
				t.Errorf("IsCheck 不匹配: 期望 %t, 实际 %t", tt.wantPayload.IsCheck, got.IsCheck)
			}
		})
	}
}
