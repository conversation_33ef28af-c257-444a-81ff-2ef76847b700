package handlers

import (
	"context"
	"micro-service/initialize/es"
	mysqlInit "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"

	asynq "micro-service/pkg/queue_helper"

	"github.com/stretchr/testify/assert"
)

// setupUpdateAssetsLevelCodeTestEnvironment 初始化测试环境
func setupUpdateAssetsLevelCodeTestEnvironment() {
	cfg.InitLoadCfg()
	log.Init()

	// 启用测试环境
	es.SetTestEnv(true)
	mysqlInit.SetTestEnv(true)
	redis.SetTestEnv(true)

	// 初始化Mock实例
	es.GetInstance(cfg.LoadElastic())
	mysqlInit.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

// TestNewUpdateAssetsLevelCodePayload_Extended 测试创建UpdateAssetsLevelCodePayload的扩展情况
func TestNewUpdateAssetsLevelCodePayload_Extended(t *testing.T) {
	// 设置测试环境
	setupUpdateAssetsLevelCodeTestEnvironment()
	tests := []struct {
		name      string
		userID    uint64
		assetsID  string
		subDomain string
		url       string
		protocol  string
		expected  *asynq.UpdateAssetsLevelCodePayload
	}{
		{
			name:      "大用户ID",
			userID:    18446744073709551615, // uint64最大值
			assetsID:  "asset-max",
			subDomain: "max.example.com",
			url:       "https://max.example.com",
			protocol:  "https",
			expected: &asynq.UpdateAssetsLevelCodePayload{
				UserID:    18446744073709551615,
				SubDomain: "max.example.com",
				Url:       "https://max.example.com",
				Protocol:  "https",
				AssetId:   "asset-max",
			},
		},
		{
			name:      "特殊字符",
			userID:    123,
			assetsID:  "asset-special-123!@#",
			subDomain: "test-special.example.com",
			url:       "https://test-special.example.com/path?param=value",
			protocol:  "https",
			expected: &asynq.UpdateAssetsLevelCodePayload{
				UserID:    123,
				SubDomain: "test-special.example.com",
				Url:       "https://test-special.example.com/path?param=value",
				Protocol:  "https",
				AssetId:   "asset-special-123!@#",
			},
		},
		{
			name:      "HTTP协议",
			userID:    456,
			assetsID:  "asset-http",
			subDomain: "http.example.com",
			url:       "http://http.example.com",
			protocol:  "http",
			expected: &asynq.UpdateAssetsLevelCodePayload{
				UserID:    456,
				SubDomain: "http.example.com",
				Url:       "http://http.example.com",
				Protocol:  "http",
				AssetId:   "asset-http",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewUpdateAssetsLevelCodePayload(tt.userID, tt.assetsID, tt.subDomain, tt.url, tt.protocol)

			assert.Equal(t, tt.expected.UserID, result.UserID)
			assert.Equal(t, tt.expected.AssetId, result.AssetId)
			assert.Equal(t, tt.expected.SubDomain, result.SubDomain)
			assert.Equal(t, tt.expected.Url, result.Url)
			assert.Equal(t, tt.expected.Protocol, result.Protocol)
		})
	}
}

// TestUpdateAssetsLevelCodeHandler_PayloadValidation 测试载荷验证
func TestUpdateAssetsLevelCodeHandler_PayloadValidation(t *testing.T) {
	// 设置测试环境
	setupUpdateAssetsLevelCodeTestEnvironment()
	tests := []struct {
		name        string
		payload     []byte
		expectError bool
		errorMsg    string
	}{
		{
			name:        "空载荷",
			payload:     []byte(``),
			expectError: true,
			errorMsg:    "解析任务数据失败",
		},
		{
			name:        "null载荷",
			payload:     []byte(`null`),
			expectError: false, // null可以被解析为空结构体
		},
		{
			name:        "空对象载荷",
			payload:     []byte(`{}`),
			expectError: false,
		},
		{
			name:        "格式错误的JSON",
			payload:     []byte(`{"user_id": "not_a_number"}`),
			expectError: true,
			errorMsg:    "解析任务数据失败",
		},
		{
			name: "包含额外字段的载荷",
			payload: []byte(`{
				"user_id": 123,
				"sub_domain": "test.example.com",
				"url": "https://test.example.com",
				"protocol": "https",
				"asset_id": "asset-123",
				"extra_field": "should_be_ignored"
			}`),
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			task := &asynq.Task{
				Type:    "update_assets_level_code",
				Payload: string(tt.payload),
			}

			err := UpdateAssetsLevelCodeHandler(context.Background(), task)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
