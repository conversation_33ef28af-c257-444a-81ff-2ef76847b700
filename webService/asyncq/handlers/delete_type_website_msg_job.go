package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/website_message"
	"micro-service/pkg/email"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
)

type DeleteTypeWebsiteMsgJobPayload struct {
	UserId  int64  `json:"user_id"`  // 用户ID
	Type    uint64 `json:"type"`     // 资产类型
	Num     int    `json:"num"`      // 删除数量
	AppType uint64 `json:"app_type"` // 数字资产类型（仅当Type为APP_ASSETS时使用）
}

type AssetTypeInfo struct {
	Name    string // 资产类型名称
	Subtype uint64 // 子类型
}

// DeleteTypeWebsiteMsgJob 删除类型站内信任务
// 消息体:
//
//	{
//		"user_id": 1,    // 用户ID，必填
//		"type": 1,       // 资产类型，必填 (1:IP资产 2:登录入口 3:域名资产 4:证书资产 5:业务系统 6:URL(API)资产 7:疑似资产 8:数字资产)
//		"num": 5,        // 删除数量，必填
//		"app_type": 1    // 数字资产类型，可选 (1:微信公众号 2:小程序 3:APP)
//	}
func DeleteTypeWebsiteMsgJob(ctx context.Context, t *asyncq.Task) error {
	payloadInfo, err := parsePayloadForDeleteTypeWebsiteMsg([]byte(t.Payload))
	if err != nil {
		return err
	}

	log.Info("DeleteTypeWebsiteMsgJob", "站内信记录删除类型站内信-开始", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"type":    payloadInfo.Type,
		"num":     payloadInfo.Num,
	})

	// 检查用户通知设置
	notifyConfig, err := checkUserNotificationSettings(payloadInfo.UserId)
	if err != nil {
		log.Error("DeleteTypeWebsiteMsgJob", "检查用户通知设置失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"error":   err.Error(),
		})
		return err
	}

	if notifyConfig == nil {
		log.Info("DeleteTypeWebsiteMsgJob", "当前没有用户设置站内信通知，不需要进行检测变化-结束", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"date":    time.Now().Format("2006-01-02 15:04:05"),
		})
		return nil
	}

	// 数量为0不记录
	if payloadInfo.Num < 1 {
		log.Info("DeleteTypeWebsiteMsgJob", "删除的数量为0-结束", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"date":    time.Now().Format("2006-01-02 15:04:05"),
		})
		return nil
	}

	// 解析资产变化配置
	var assetChangeArr []uint64
	if notifyConfig.AssetChangeJson != "" {
		err = json.Unmarshal([]byte(notifyConfig.AssetChangeJson), &assetChangeArr)
		if err != nil {
			log.Error("DeleteTypeWebsiteMsgJob", "解析资产变化配置失败", map[string]interface{}{
				"user_id": payloadInfo.UserId,
				"error":   err.Error(),
			})
			assetChangeArr = []uint64{}
		}
	}

	if len(assetChangeArr) == 0 {
		log.Info("DeleteTypeWebsiteMsgJob", "当前用户设置了站内信，但是没设置资产模块", map[string]interface{}{
			"user_id": payloadInfo.UserId,
		})
		return nil
	}

	// 检查邮件设置
	email := notifyConfig.Email
	if notifyConfig.IsEmail == website_message.NO_EMAIL_MSG {
		email = ""
	}

	log.Info("DeleteTypeWebsiteMsgJob", "站内信记录删除类型站内信-执行中", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"type":    payloadInfo.Type,
		"num":     payloadInfo.Num,
		"email":   email,
	})

	// 处理资产变化
	err = processAssetChange(payloadInfo, assetChangeArr, email)
	if err != nil {
		log.Error("DeleteTypeWebsiteMsgJob", "处理资产变化失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"type":    payloadInfo.Type,
			"error":   err.Error(),
		})
		return err
	}

	log.Info("DeleteTypeWebsiteMsgJob", "站内信记录删除类型站内信-结束", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"type":    payloadInfo.Type,
		"num":     payloadInfo.Num,
		"email":   email,
	})

	return nil
}

// checkUserNotificationSettings 检查用户通知设置
func checkUserNotificationSettings(userId int64) (*website_message.Notify, error) {
	websiteModel := website_message.NewWebsiteMessageModel()
	handlers := []mysql.HandleFunc{
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("is_website_msg", website_message.YES_WEBSITE_MSG),
	}

	notify, err := websiteModel.NotificationList(handlers...)
	if err != nil {
		// 如果记录不存在，返回nil，不算错误
		if err.Error() == "record not found" {
			return nil, nil
		}
		return nil, err
	}

	return &notify, nil
}

// processAssetChange 处理资产变化
func processAssetChange(payload *DeleteTypeWebsiteMsgJobPayload, assetChangeArr []uint64, email string) error {
	// 检查当前资产类型是否在用户配置中
	if !slices.Contains(assetChangeArr, payload.Type) {
		return nil
	}

	// 获取资产类型信息
	assetInfo := getAssetTypeInfo(payload.Type, payload.AppType)
	if assetInfo == nil {
		log.Info("DeleteTypeWebsiteMsgJob", "不支持的资产类型", map[string]interface{}{
			"user_id": payload.UserId,
			"type":    payload.Type,
		})
		return nil
	}

	// 获取公司ID
	companyModel := company.NewCompanyModel()
	companyInfo, err := companyModel.FindById(0, uint64(payload.UserId))
	if err != nil {
		return fmt.Errorf("查询公司信息失败: %v", err)
	}

	// 创建站内信记录
	message := &website_message.Message{
		UserId:                uint64(payload.UserId),
		CompanyId:             companyInfo.ID,
		MsgType:               website_message.ASSETS_MODEL,
		MsgSubtype:            assetInfo.Subtype,
		IsRead:                0,
		MsgStatus:             website_message.DELETE_TYPE,
		AffectAssetId:         "",
		LastTimeAffectAssetId: "",
		ThisTimeAffectAssetId: "",
		MsgContent:            fmt.Sprintf("【资产变化】- 删除%d个%s", payload.Num, assetInfo.Name),
	}

	// 如果是数字资产，需要设置app_type和is_read
	if payload.Type == website_message.APP_ASSETS {
		message.AppType = payload.AppType
		message.IsRead = 2 // 数字资产的is_read设置为2
	}

	// 手动设置时间
	now := time.Now()
	message.CreatedAt = now
	message.UpdatedAt = now

	// 保存站内信记录
	websiteModel := website_message.NewWebsiteMessageModel()
	err = websiteModel.CreateMessage(message)
	if err != nil {
		return fmt.Errorf("创建站内信记录失败: %v", err)
	}

	// 发送邮件通知
	if email != "" {
		err = sendNotificationEmail(email, message.MsgContent)
		if err != nil {
			log.Error("DeleteTypeWebsiteMsgJob", "发送邮件失败", map[string]interface{}{
				"email": email,
				"error": err.Error(),
			})
			// 邮件发送失败不影响主流程
		}
	}

	log.Info("DeleteTypeWebsiteMsgJob", fmt.Sprintf("%s数据删除记录", assetInfo.Name), map[string]interface{}{
		"user_id": payload.UserId,
		"type":    payload.Type,
		"num":     payload.Num,
	})

	return nil
}

// getAssetTypeInfo 获取资产类型信息
func getAssetTypeInfo(assetType, appType uint64) *AssetTypeInfo {
	switch assetType {
	case website_message.TABLE_IP:
		return &AssetTypeInfo{Name: "IP资产", Subtype: website_message.TABLE_IP}
	case website_message.LOGIN_PAGE:
		return &AssetTypeInfo{Name: "登录入口", Subtype: website_message.LOGIN_PAGE}
	case website_message.DOMAIN_ASSETS:
		return &AssetTypeInfo{Name: "域名资产", Subtype: website_message.DOMAIN_ASSETS}
	case website_message.CERT_ASSETS:
		return &AssetTypeInfo{Name: "证书资产", Subtype: website_message.CERT_ASSETS}
	case website_message.BUSSINESS:
		return &AssetTypeInfo{Name: "业务系统", Subtype: website_message.BUSSINESS}
	case website_message.URL_API:
		return &AssetTypeInfo{Name: "URL-API资产", Subtype: website_message.URL_API}
	case website_message.UNSURE_IP:
		return &AssetTypeInfo{Name: "疑似资产", Subtype: website_message.UNSURE_IP}
	case website_message.APP_ASSETS:
		appName := getAppTypeName(appType)
		return &AssetTypeInfo{Name: fmt.Sprintf("%s类型的数字资产", appName), Subtype: website_message.APP_ASSETS}
	default:
		return nil
	}
}

// getAppTypeName 获取数字资产类型名称
func getAppTypeName(appType uint64) string {
	switch appType {
	case 1: // SensitiveModel::WECHAT
		return "微信公众号"
	case 2: // SensitiveModel::XIAOCHENGXU
		return "小程序"
	case 3: // SensitiveModel::APP
		return "APP"
	default:
		return "数字资产"
	}
}

// sendNotificationEmail 发送通知邮件
func sendNotificationEmail(emailTo, content string) error {
	subject := "资产变化邮件通知"
	err := email.Send(subject, content, []string{emailTo})
	if err != nil {
		log.Error("DeleteTypeWebsiteMsgJob", "邮件发送失败", map[string]interface{}{
			"email":   emailTo,
			"subject": subject,
			"content": content,
			"error":   err.Error(),
		})
		return err
	} else {
		log.Info("DeleteTypeWebsiteMsgJob", "邮件发送", map[string]interface{}{
			"emailTo": emailTo,
			"subject": subject,
			"content": content,
		})
	}
	return nil
}

func parsePayloadForDeleteTypeWebsiteMsg(payload []byte) (payloadInfo *DeleteTypeWebsiteMsgJobPayload, err error) {
	err = json.Unmarshal(payload, &payloadInfo)
	if err != nil {
		log.Error("DeleteTypeWebsiteMsgJob", "解析payload失败", map[string]interface{}{
			"payload": string(payload),
			"error":   err.Error(),
		})
		return nil, err
	}

	// 验证必填字段
	if payloadInfo.UserId == 0 {
		return nil, fmt.Errorf("用户ID不能为空")
	}
	if payloadInfo.Type == 0 {
		return nil, fmt.Errorf("资产类型不能为空")
	}

	return payloadInfo, nil
}
