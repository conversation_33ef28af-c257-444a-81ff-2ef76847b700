package handlers

import (
	"testing"

	"micro-service/middleware/elastic/fofaee_subdomain"
	"micro-service/middleware/elastic/foradar_assets"

	"github.com/stretchr/testify/assert"
)

// TestRuleTagsProcessing 测试 rule_tags 处理逻辑
func TestRuleTagsProcessing(t *testing.T) {
	t.Run("test rule_tags conversion from interface{} to foradar_assets.RuleTag", func(t *testing.T) {
		// 模拟 scanAsset["rule_tags"] 的数据结构
		ruleTagsData := []interface{}{
			map[string]interface{}{
				"rule_id":            "rule-001",
				"cn_product":         "测试产品",
				"product":            "test-product",
				"cn_category":        "测试分类",
				"category":           "test-category",
				"level":              "high",
				"parent_category":    "test-parent",
				"cn_parent_category": "测试父分类",
				"softhard":           "soft",
				"company":            "Test Company",
				"cn_company":         "测试公司",
			},
			map[string]interface{}{
				"rule_id":            "rule-002",
				"cn_product":         "测试产品2",
				"product":            "test-product-2",
				"cn_category":        "测试分类2",
				"category":           "test-category-2",
				"level":              "medium",
				"parent_category":    "test-parent-2",
				"cn_parent_category": "测试父分类2",
				"softhard":           "hard",
				"company":            "Test Company 2",
				"cn_company":         "测试公司2",
			},
		}

		// 模拟处理逻辑
		ruleTagsArray := make([]foradar_assets.RuleTag, 0, len(ruleTagsData))
		for _, tag := range ruleTagsData {
			if tagMap, ok := tag.(map[string]interface{}); ok {
				ruleTag := foradar_assets.RuleTag{
					CnProduct:        safeStringValue(tagMap["cn_product"]),
					RuleID:           safeStringValue(tagMap["rule_id"]),
					Product:          safeStringValue(tagMap["product"]),
					CnCategory:       safeStringValue(tagMap["cn_category"]),
					Level:            safeStringValue(tagMap["level"]),
					ParentCategory:   safeStringValue(tagMap["parent_category"]),
					Softhard:         safeStringValue(tagMap["softhard"]),
					Company:          safeStringValue(tagMap["company"]),
					CnParentCategory: safeStringValue(tagMap["cn_parent_category"]),
					Category:         safeStringValue(tagMap["category"]),
					CnCompany:        safeStringValue(tagMap["cn_company"]),
				}
				ruleTagsArray = append(ruleTagsArray, ruleTag)
			}
		}

		// 验证转换结果
		assert.Len(t, ruleTagsArray, 2, "应该转换出2个 RuleTag")

		// 验证第一个 RuleTag
		assert.Equal(t, "rule-001", ruleTagsArray[0].RuleID)
		assert.Equal(t, "测试产品", ruleTagsArray[0].CnProduct)
		assert.Equal(t, "test-product", ruleTagsArray[0].Product)
		assert.Equal(t, "high", ruleTagsArray[0].Level)

		// 验证第二个 RuleTag
		assert.Equal(t, "rule-002", ruleTagsArray[1].RuleID)
		assert.Equal(t, "测试产品2", ruleTagsArray[1].CnProduct)
		assert.Equal(t, "test-product-2", ruleTagsArray[1].Product)
		assert.Equal(t, "medium", ruleTagsArray[1].Level)
	})

	t.Run("test fofaee_subdomain.Rule to map conversion", func(t *testing.T) {
		// 模拟 fofaee_subdomain.Rule 数据
		subdomainRules := []fofaee_subdomain.Rule{
			{
				RuleId:           "rule-001",
				CnProduct:        "测试产品",
				Product:          "test-product",
				CnCategory:       "测试分类",
				Category:         "test-category",
				Level:            "high",
				ParentCategory:   "test-parent",
				CnParentCategory: "测试父分类",
				SoftHard:         "soft",
				Company:          "Test Company",
				CnCompany:        "测试公司",
			},
		}

		// 模拟转换逻辑
		var allRuleTags []interface{}
		for _, rule := range subdomainRules {
			allRuleTags = append(allRuleTags, map[string]interface{}{
				"rule_id":            rule.RuleId,
				"cn_product":         rule.CnProduct,
				"product":            rule.Product,
				"category":           rule.Category,
				"level":              rule.Level,
				"softhard":           rule.SoftHard,
				"cn_category":        rule.CnCategory,
				"company":            rule.Company,
				"cn_company":         rule.CnCompany,
				"parent_category":    rule.ParentCategory,
				"cn_parent_category": rule.CnParentCategory,
			})
		}

		// 验证转换结果
		assert.Len(t, allRuleTags, 1, "应该转换出1个 rule tag map")

		tagMap := allRuleTags[0].(map[string]interface{})
		assert.Equal(t, "rule-001", tagMap["rule_id"])
		assert.Equal(t, "测试产品", tagMap["cn_product"])
		assert.Equal(t, "test-product", tagMap["product"])
		assert.Equal(t, "soft", tagMap["softhard"])
	})
}

// TestFieldNameConsistency 测试字段名一致性
func TestFieldNameConsistency(t *testing.T) {
	t.Run("verify field name mapping", func(t *testing.T) {
		// 验证字段名映射的一致性

		// fofaee_subdomain.Rule 使用的字段名
		subdomainRule := fofaee_subdomain.Rule{
			RuleId:   "test-rule", // 注意：这里是 RuleId
			SoftHard: "soft",      // 注意：这里是 SoftHard
		}

		// foradar_assets.RuleTag 使用的字段名
		foradarRuleTag := foradar_assets.RuleTag{
			RuleID:   "test-rule", // 注意：这里是 RuleID
			Softhard: "soft",      // 注意：这里是 Softhard
		}

		// 验证字段值
		assert.Equal(t, subdomainRule.RuleId, foradarRuleTag.RuleID)
		assert.Equal(t, subdomainRule.SoftHard, foradarRuleTag.Softhard)
	})
}

// TestRuleTagsTypeDetection 测试不同类型的 rule_tags 检测
func TestRuleTagsTypeDetection(t *testing.T) {
	t.Run("test []interface{} type detection", func(t *testing.T) {
		// 模拟 []interface{} 类型的 rule_tags
		ruleTagsInterface := []interface{}{
			map[string]interface{}{
				"rule_id":    "rule-001",
				"product":    "test-product",
				"cn_product": "测试产品",
			},
		}

		// 测试类型检测
		var ruleTagsRaw interface{} = ruleTagsInterface
		if ruleTags, ok := ruleTagsRaw.([]interface{}); ok {
			assert.Len(t, ruleTags, 1)
			assert.IsType(t, map[string]interface{}{}, ruleTags[0])
		} else {
			t.Error("应该能够转换为 []interface{}")
		}
	})

	t.Run("test []fofaee_subdomain.Rule type detection", func(t *testing.T) {
		// 模拟 []fofaee_subdomain.Rule 类型的 rule_tags
		ruleTagsSubdomain := []fofaee_subdomain.Rule{
			{
				RuleId:    "rule-001",
				Product:   "test-product",
				CnProduct: "测试产品",
			},
		}

		// 测试类型检测
		var ruleTagsRaw interface{} = ruleTagsSubdomain
		if ruleTags, ok := ruleTagsRaw.([]fofaee_subdomain.Rule); ok {
			assert.Len(t, ruleTags, 1)
			assert.Equal(t, "rule-001", ruleTags[0].RuleId)
		} else {
			t.Error("应该能够转换为 []fofaee_subdomain.Rule")
		}
	})

	t.Run("test []foradar_assets.RuleTag type detection", func(t *testing.T) {
		// 模拟 []foradar_assets.RuleTag 类型的 rule_tags
		ruleTagsForadar := []foradar_assets.RuleTag{
			{
				RuleID:    "rule-001",
				Product:   "test-product",
				CnProduct: "测试产品",
			},
		}

		// 测试类型检测
		var ruleTagsRaw interface{} = ruleTagsForadar
		if ruleTags, ok := ruleTagsRaw.([]foradar_assets.RuleTag); ok {
			assert.Len(t, ruleTags, 1)
			assert.Equal(t, "rule-001", ruleTags[0].RuleID)
		} else {
			t.Error("应该能够转换为 []foradar_assets.RuleTag")
		}
	})
}
