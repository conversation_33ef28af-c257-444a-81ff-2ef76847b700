package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-redis/redis/v8"
	"github.com/go-redis/redismock/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	testcommon "micro-service/initialize/common_test"
	es "micro-service/initialize/es"
	mysqlInit "micro-service/initialize/mysql"
	redisInit "micro-service/initialize/redis"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql/detect_assets_tasks"
	tasks "micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/pkg/cfg"
	clueUtil "micro-service/pkg/clues"
	"micro-service/pkg/log"
	asynq "micro-service/pkg/queue_helper"
)

// setupUpdateAssetsLevelTestEnvironment 初始化测试环境
func setupUpdateAssetsLevelTestEnvironment() {
	testcommon.SetTestEnv(true)

	cfg.InitLoadCfg()
	log.Init()

	// 启用测试环境
	es.SetTestEnv(true)
	mysqlInit.SetTestEnv(true)
	redisInit.SetTestEnv(true)

	// 初始化Mock实例
	redisInit.GetInstance(cfg.LoadRedis())
	es.GetInstance(cfg.LoadElastic())
	mysqlInit.GetInstance(cfg.LoadMysql())
}

func TestChunkDomains(t *testing.T) {
	tests := []struct {
		name     string
		domains  []string
		size     int
		expected [][]string
	}{
		{
			name:     "正常分块",
			domains:  []string{"a.com", "b.com", "c.com", "d.com", "e.com"},
			size:     2,
			expected: [][]string{{"a.com", "b.com"}, {"c.com", "d.com"}, {"e.com"}},
		},
		{
			name:     "分块大小等于切片长度",
			domains:  []string{"a.com", "b.com", "c.com"},
			size:     3,
			expected: [][]string{{"a.com", "b.com", "c.com"}},
		},
		{
			name:     "分块大小大于切片长度",
			domains:  []string{"a.com", "b.com"},
			size:     5,
			expected: [][]string{{"a.com", "b.com"}},
		},
		{
			name:     "分块大小为0",
			domains:  []string{"a.com", "b.com", "c.com"},
			size:     0,
			expected: nil,
		},
		{
			name:     "分块大小为负数",
			domains:  []string{"a.com", "b.com", "c.com"},
			size:     -1,
			expected: nil,
		},
		{
			name:     "空切片",
			domains:  []string{},
			size:     2,
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := chunkDomains(tt.domains, tt.size)
			assert.Equal(t, tt.expected, got)
		})
	}
}

func TestRound(t *testing.T) {
	tests := []struct {
		name      string
		val       float64
		precision int
		expected  float64
	}{
		{
			name:      "正常四舍五入",
			val:       3.14159,
			precision: 2,
			expected:  3.14,
		},
		{
			name:      "负数",
			val:       -3.14159,
			precision: 2,
			expected:  -3.14,
		},
		{
			name:      "精度为0",
			val:       3.14159,
			precision: 0,
			expected:  3.0,
		},
		{
			name:      "高精度",
			val:       3.1415926535,
			precision: 4,
			expected:  3.1416,
		},
		{
			name:      "精度为负数",
			val:       3.14159,
			precision: -1,
			expected:  0.0,
		},
		{
			name:      "整数",
			val:       5.0,
			precision: 2,
			expected:  5.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := round(tt.val, tt.precision)
			assert.InDelta(t, tt.expected, got, 0.0001)
		})
	}
}

// TestNewUpdateAssetsLevelTask 测试创建UpdateAssetsLevelTask
func TestNewUpdateAssetsLevelTask(t *testing.T) {
	tests := []struct {
		name         string
		userID       uint64
		operatorID   uint64
		groupID      uint64
		flag         string
		detectTaskId string
		expected     *UpdateAssetsLevelPayload
	}{
		{
			name:         "正常创建任务",
			userID:       123,
			operatorID:   456,
			groupID:      789,
			flag:         "test-flag",
			detectTaskId: "test-task-id",
			expected: &UpdateAssetsLevelPayload{
				UpdateAssetsLevelTaskPayload: asynq.UpdateAssetsLevelTaskPayload{
					UserID:       123,
					OperatorID:   456,
					GroupID:      789,
					Flag:         "test-flag",
					DetectTaskId: "test-task-id",
				},
			},
		},
		{
			name:         "空字符串参数",
			userID:       0,
			operatorID:   0,
			groupID:      0,
			flag:         "",
			detectTaskId: "",
			expected: &UpdateAssetsLevelPayload{
				UpdateAssetsLevelTaskPayload: asynq.UpdateAssetsLevelTaskPayload{
					UserID:       0,
					OperatorID:   0,
					GroupID:      0,
					Flag:         "",
					DetectTaskId: "",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := asynq.NewUpdateAssetsLevelTask(tt.userID, tt.operatorID, tt.groupID, tt.flag, tt.detectTaskId)

			assert.Equal(t, tt.expected.UserID, result.UserID)
			assert.Equal(t, tt.expected.OperatorID, result.OperatorID)
			assert.Equal(t, tt.expected.GroupID, result.GroupID)
			assert.Equal(t, tt.expected.Flag, result.Flag)
			assert.Equal(t, tt.expected.DetectTaskId, result.DetectTaskId)
			assert.Nil(t, result.DetectTaskId)
		})
	}
}

// TestFilterDomains 测试filterDomains函数
func TestFilterDomains(t *testing.T) {
	tests := []struct {
		name            string
		domains         []string
		blackDomains    []string
		expectedCount   int
		expectedDomains []string
		expectError     bool
	}{
		{
			name:            "正常过滤",
			domains:         []string{"example.com", "test.com", "bad.com", "good.org"},
			blackDomains:    []string{"bad.com"},
			expectedCount:   3,
			expectedDomains: []string{"example.com", "test.com", "good.org"},
			expectError:     false,
		},
		{
			name:            "过滤通配符域名",
			domains:         []string{"example.com", "*.test.com", "**bad.com", "good.org"},
			blackDomains:    []string{},
			expectedCount:   3,
			expectedDomains: []string{"example.com", "*.test.com", "good.org"},
			expectError:     false,
		},
		{
			name:            "过滤无效域名",
			domains:         []string{"example.com", "invalid-domain", "test.com", "not_a_domain"},
			blackDomains:    []string{},
			expectedCount:   2,
			expectedDomains: []string{"example.com", "test.com"},
			expectError:     false,
		},
		{
			name:            "空域名列表",
			domains:         []string{},
			blackDomains:    []string{"bad.com"},
			expectedCount:   0,
			expectedDomains: []string{},
			expectError:     false,
		},
		{
			name:            "大小写不敏感黑名单",
			domains:         []string{"Example.com", "TEST.COM", "good.org"},
			blackDomains:    []string{"example.com", "test.com"},
			expectedCount:   1,
			expectedDomains: []string{"good.org"},
			expectError:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 由于filterDomains函数直接调用black_forbidden_domains.NewBlackDomainsModel()
			// 我们需要通过集成测试的方式来测试，或者重构代码使其可测试
			// 这里我们测试函数的基本逻辑

			// 创建测试域名列表
			testDomains := make([]string, len(tt.domains))
			copy(testDomains, tt.domains)

			// 手动模拟过滤逻辑进行验证
			var filtered []string
			for _, domain := range testDomains {
				// 检查黑名单
				isBlacklisted := false
				for _, blackDomain := range tt.blackDomains {
					if strings.EqualFold(domain, blackDomain) {
						isBlacklisted = true
						break
					}
				}
				if isBlacklisted {
					continue
				}

				// 检查通配符
				if strings.Count(domain, "*") > 1 {
					continue
				}

				// 检查域名格式（简化版）
				if strings.Contains(domain, ".") && !strings.Contains(domain, "_") {
					filtered = append(filtered, domain)
				}
			}

			assert.Equal(t, tt.expectedCount, len(filtered))
		})
	}
}

// MockDetectAssetsTaskModel Mock检测任务模型
type MockDetectAssetsTaskModel struct {
	mock.Mock
}

func (m *MockDetectAssetsTaskModel) First(opts ...interface{}) (*detect_assets_tasks.DetectAssetsTask, error) {
	args := m.Called(opts)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*detect_assets_tasks.DetectAssetsTask), args.Error(1)
}

func (m *MockDetectAssetsTaskModel) UpdateAny(data map[string]interface{}, opts ...interface{}) error {
	args := m.Called(data, opts)
	return args.Error(0)
}

// MockRecommendResultModel Mock推荐结果模型
type MockRecommendResultModel struct {
	mock.Mock
}

func (m *MockRecommendResultModel) FindByPageCondition(page, size int, condition *recommend_result.FindCondition) ([]*recommend_result.RecommendResult, int64, error) {
	args := m.Called(page, size, condition)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*recommend_result.RecommendResult), args.Get(1).(int64), args.Error(2)
}

func (m *MockRecommendResultModel) FindByCondition(condition *recommend_result.FindCondition) ([]*recommend_result.RecommendResult, error) {
	args := m.Called(condition)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*recommend_result.RecommendResult), args.Error(1)
}

func (m *MockRecommendResultModel) UpdatesAny(params []*recommend_result.UpdateAnyParam) error {
	args := m.Called(params)
	return args.Error(0)
}

// TestHandleUpdateAssetsLevelPayloadValidation 测试HandleUpdateAssetsLevel的payload验证
func TestHandleUpdateAssetsLevelPayloadValidation(t *testing.T) {
	tests := []struct {
		name        string
		payload     []byte
		expectError bool
		errorMsg    string
	}{
		{
			name:        "无效JSON",
			payload:     []byte(`{invalid json`),
			expectError: true,
			errorMsg:    "解析任务数据失败",
		},
		{
			name: "空任务ID",
			payload: func() []byte {
				p := UpdateAssetsLevelPayload{
					UpdateAssetsLevelTaskPayload: asynq.UpdateAssetsLevelTaskPayload{
						UserID:       123,
						OperatorID:   456,
						GroupID:      789,
						Flag:         "test-flag",
						DetectTaskId: "",
					},
				}
				data, _ := json.Marshal(p)
				return data
			}(),
			expectError: true,
			errorMsg:    "任务id不能为0",
		},
		{
			name: "有效payload",
			payload: func() []byte {
				p := UpdateAssetsLevelPayload{
					UpdateAssetsLevelTaskPayload: asynq.UpdateAssetsLevelTaskPayload{
						UserID:       123,
						OperatorID:   456,
						GroupID:      789,
						Flag:         "test-flag",
						DetectTaskId: "valid-task-id",
					},
				}
				data, _ := json.Marshal(p)
				return data
			}(),
			expectError: false,
			errorMsg:    "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock任务
			task := &asynq.Task{
				Type:    "update_assets_level",
				Payload: string(tt.payload),
			}

			// 由于HandleUpdateAssetsLevel函数依赖很多外部服务，
			// 这里我们只测试payload解析部分的逻辑
			var payload UpdateAssetsLevelPayload
			err := json.Unmarshal([]byte(task.Payload), &payload)

			if tt.expectError && tt.errorMsg == "解析任务数据失败" {
				assert.Error(t, err)
				return
			}

			if tt.expectError && tt.errorMsg == "任务id不能为0" {
				assert.NoError(t, err) // JSON解析应该成功
				assert.Empty(t, payload.DetectTaskId)
				return
			}

			if !tt.expectError {
				assert.NoError(t, err)
				assert.NotEmpty(t, payload.DetectTaskId)
				assert.Equal(t, uint64(123), payload.UserID)
				assert.Equal(t, uint64(456), payload.OperatorID)
				assert.Equal(t, uint64(789), payload.GroupID)
				assert.Equal(t, "test-flag", payload.Flag)
			}
		})
	}
}

// TestGetDomainFromAsset 测试从资产中提取域名的功能
func TestGetDomainFromAsset(t *testing.T) {
	tests := []struct {
		name     string
		asset    *recommend_result.RecommendResult
		expected string
	}{
		{
			name: "从URL提取域名",
			asset: &recommend_result.RecommendResult{
				Url: "https://example.com/path",
			},
			expected: "example.com",
		},
		{
			name: "从Subdomain提取域名",
			asset: &recommend_result.RecommendResult{
				Subdomain: "test.example.com",
			},
			expected: "test.example.com",
		},
		{
			name: "从Title提取域名",
			asset: &recommend_result.RecommendResult{
				Title: "Welcome to subdomain.example.org",
			},
			expected: "subdomain.example.org",
		},
		{
			name: "无有效域名",
			asset: &recommend_result.RecommendResult{
				Title: "No domain here",
				Url:   "invalid-url",
			},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 这里我们模拟域名提取逻辑
			var domain string

			// 从URL提取
			if tt.asset.Url != "" && strings.Contains(tt.asset.Url, "://") {
				parts := strings.Split(tt.asset.Url, "://")
				if len(parts) > 1 {
					hostPart := strings.Split(parts[1], "/")[0]
					if strings.Contains(hostPart, ".") {
						domain = hostPart
					}
				}
			}

			// 从Subdomain提取
			if domain == "" && tt.asset.Subdomain != "" && strings.Contains(tt.asset.Subdomain, ".") {
				domain = tt.asset.Subdomain
			}

			// 从Title提取（简化版）
			if domain == "" && tt.asset.Title != "" {
				words := strings.Fields(tt.asset.Title)
				for _, word := range words {
					if strings.Contains(word, ".") && strings.Count(word, ".") >= 1 {
						// 简单的域名格式检查
						if len(strings.Split(word, ".")) >= 2 {
							domain = word
							break
						}
					}
				}
			}

			assert.Equal(t, tt.expected, domain)
		})
	}
}

// TestValidateDomainFormat 测试域名格式验证
func TestValidateDomainFormat(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		expected bool
	}{
		{
			name:     "有效域名",
			domain:   "example.com",
			expected: true,
		},
		{
			name:     "有效子域名",
			domain:   "sub.example.com",
			expected: true,
		},
		{
			name:     "有效通配符域名",
			domain:   "*.example.com",
			expected: true,
		},
		{
			name:     "无效域名-无点",
			domain:   "localhost",
			expected: false,
		},
		{
			name:     "无效域名-包含下划线",
			domain:   "test_domain.com",
			expected: false,
		},
		{
			name:     "无效域名-多个通配符",
			domain:   "**.example.com",
			expected: false,
		},
		{
			name:     "空域名",
			domain:   "",
			expected: false,
		},
		{
			name:     "IP地址",
			domain:   "***********",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟域名验证逻辑
			isValid := false

			if tt.domain != "" {
				// 检查是否包含点
				if strings.Contains(tt.domain, ".") {
					// 检查通配符数量
					if strings.Count(tt.domain, "*") <= 1 {
						// 检查是否包含下划线
						if !strings.Contains(tt.domain, "_") {
							// 简单的IP地址检查
							parts := strings.Split(tt.domain, ".")
							isIP := true
							for _, part := range parts {
								if part == "*" {
									isIP = false
									break
								}
								if len(part) == 0 || len(part) > 3 {
									isIP = false
									break
								}
								for _, char := range part {
									if char < '0' || char > '9' {
										isIP = false
										break
									}
								}
								if isIP {
									break
								}
							}
							if !isIP {
								isValid = true
							}
						}
					}
				}
			}

			assert.Equal(t, tt.expected, isValid)
		})
	}
}

// TestGetTopLevel 测试getTopLevel方法
func TestGetTopLevel(t *testing.T) {
	// 初始化测试环境
	setupUpdateAssetsLevelTestEnvironment()

	tests := []struct {
		name        string
		asset       *recommend_result.RecommendResult
		userID      uint64
		cacheSetup  func(redismock.ClientMock)
		expected    *clueUtil.LevelReason
		description string
	}{
		{
			name: "缓存命中-返回A级别",
			asset: &recommend_result.RecommendResult{
				Id: "asset-123",
				Ip: "***********",
			},
			userID: 123,
			cacheSetup: func(rdm redismock.ClientMock) {
				levelArr := []clueUtil.LevelItem{
					{
						IP: clueUtil.LevelValue{
							Level: clueUtil.AssetsLevelA,
							Val: clueUtil.LevelReason{
								Level:    clueUtil.AssetsLevelA,
								IsFake:   false,
								Reason:   "高可信度资产",
								FakeType: 0,
							},
						},
					},
				}
				cacheJSON, _ := json.Marshal(levelArr)
				rdm.ExpectGet("cache:classifyAssetsLevel:123_***********_level").SetVal(string(cacheJSON))
			},
			expected: &clueUtil.LevelReason{
				Level:    clueUtil.AssetsLevelA,
				IsFake:   false,
				Reason:   "高可信度资产",
				FakeType: 0,
			},
			description: "缓存命中时应返回A级别的可信度",
		},
		{
			name: "缓存命中-返回D级别仿冒资产",
			asset: &recommend_result.RecommendResult{
				Id: "asset-456",
				Ip: "***********",
			},
			userID: 456,
			cacheSetup: func(rdm redismock.ClientMock) {
				levelArr := []clueUtil.LevelItem{
					{
						IP: clueUtil.LevelValue{
							Level: clueUtil.AssetsLevelD,
							Val: clueUtil.LevelReason{
								Level:    clueUtil.AssetsLevelD,
								IsFake:   true,
								Reason:   "仿冒资产",
								FakeType: 1,
							},
						},
					},
				}
				cacheJSON, _ := json.Marshal(levelArr)
				rdm.ExpectGet("cache:classifyAssetsLevel:456_***********_level").SetVal(string(cacheJSON))
			},
			expected: &clueUtil.LevelReason{
				Level:    clueUtil.AssetsLevelD,
				IsFake:   true,
				Reason:   "仿冒资产",
				FakeType: 1,
			},
			description: "缓存命中时应返回D级别的仿冒资产",
		},
		{
			name: "缓存命中-多级别选择最高级别",
			asset: &recommend_result.RecommendResult{
				Id: "asset-789",
				Ip: "***********",
			},
			userID: 789,
			cacheSetup: func(rdm redismock.ClientMock) {
				levelArr := []clueUtil.LevelItem{
					{
						IP: clueUtil.LevelValue{
							Level: clueUtil.AssetsLevelC,
							Val: clueUtil.LevelReason{
								Level:    clueUtil.AssetsLevelC,
								IsFake:   false,
								Reason:   "中等可信度",
								FakeType: 0,
							},
						},
					},
					{
						IP: clueUtil.LevelValue{
							Level: clueUtil.AssetsLevelA,
							Val: clueUtil.LevelReason{
								Level:    clueUtil.AssetsLevelA,
								IsFake:   false,
								Reason:   "高可信度资产",
								FakeType: 0,
							},
						},
					},
					{
						IP: clueUtil.LevelValue{
							Level: clueUtil.AssetsLevelB,
							Val: clueUtil.LevelReason{
								Level:    clueUtil.AssetsLevelB,
								IsFake:   false,
								Reason:   "较高可信度",
								FakeType: 0,
							},
						},
					},
				}
				cacheJSON, _ := json.Marshal(levelArr)
				rdm.ExpectGet("cache:classifyAssetsLevel:789_***********_level").SetVal(string(cacheJSON))
			},
			expected: &clueUtil.LevelReason{
				Level:    clueUtil.AssetsLevelA,
				IsFake:   false,
				Reason:   "高可信度资产",
				FakeType: 0,
			},
			description: "多级别时应选择最高级别(A级)",
		},
		{
			name: "缓存命中-存在D但不存在A时选择仿冒D",
			asset: &recommend_result.RecommendResult{
				Id: "asset-101",
				Ip: "***********",
			},
			userID: 101,
			cacheSetup: func(rdm redismock.ClientMock) {
				levelArr := []clueUtil.LevelItem{
					{
						IP: clueUtil.LevelValue{
							Level: clueUtil.AssetsLevelC,
							Val: clueUtil.LevelReason{
								Level:    clueUtil.AssetsLevelC,
								IsFake:   false,
								Reason:   "中等可信度",
								FakeType: 0,
							},
						},
					},
					{
						IP: clueUtil.LevelValue{
							Level: clueUtil.AssetsLevelD,
							Val: clueUtil.LevelReason{
								Level:    clueUtil.AssetsLevelD,
								IsFake:   true,
								Reason:   "仿冒资产",
								FakeType: 2,
							},
						},
					},
				}
				cacheJSON, _ := json.Marshal(levelArr)
				rdm.ExpectGet("cache:classifyAssetsLevel:101_***********_level").SetVal(string(cacheJSON))
			},
			expected: &clueUtil.LevelReason{
				Level:    clueUtil.AssetsLevelD,
				IsFake:   true,
				Reason:   "仿冒资产",
				FakeType: 2,
			},
			description: "存在D级别且为仿冒时，应优先选择仿冒D级别",
		},
		{
			name: "缓存未命中-返回nil",
			asset: &recommend_result.RecommendResult{
				Id: "asset-404",
				Ip: "***********",
			},
			userID: 404,
			cacheSetup: func(rdm redismock.ClientMock) {
				rdm.ExpectGet("cache:classifyAssetsLevel:404_***********_level").SetErr(redis.Nil)
			},
			expected:    nil,
			description: "缓存未命中时应返回nil",
		},
		{
			name: "缓存为空数组-返回nil",
			asset: &recommend_result.RecommendResult{
				Id: "asset-empty",
				Ip: "***********",
			},
			userID: 999,
			cacheSetup: func(rdm redismock.ClientMock) {
				levelArr := []clueUtil.LevelItem{}
				cacheJSON, _ := json.Marshal(levelArr)
				rdm.ExpectGet("cache:classifyAssetsLevel:999_***********_level").SetVal(string(cacheJSON))
			},
			expected:    nil,
			description: "缓存为空数组时应返回nil",
		},
		{
			name: "目标IP跟踪测试-*************",
			asset: &recommend_result.RecommendResult{
				Id: "asset-target1",
				Ip: "*************",
			},
			userID: 888,
			cacheSetup: func(rdm redismock.ClientMock) {
				levelArr := []clueUtil.LevelItem{
					{
						IP: clueUtil.LevelValue{
							Level: clueUtil.AssetsLevelB,
							Val: clueUtil.LevelReason{
								Level:    clueUtil.AssetsLevelB,
								IsFake:   false,
								Reason:   "目标IP测试",
								FakeType: 0,
							},
						},
					},
				}
				cacheJSON, _ := json.Marshal(levelArr)
				rdm.ExpectGet("cache:classifyAssetsLevel:888_*************_level").SetVal(string(cacheJSON))
			},
			expected: &clueUtil.LevelReason{
				Level:    clueUtil.AssetsLevelB,
				IsFake:   false,
				Reason:   "目标IP测试",
				FakeType: 0,
			},
			description: "目标IP应正常处理并记录详细日志",
		},
		{
			name: "目标IP跟踪测试-*************",
			asset: &recommend_result.RecommendResult{
				Id: "asset-target2",
				Ip: "*************",
			},
			userID: 777,
			cacheSetup: func(rdm redismock.ClientMock) {
				levelArr := []clueUtil.LevelItem{
					{
						IP: clueUtil.LevelValue{
							Level: clueUtil.AssetsLevelC,
							Val: clueUtil.LevelReason{
								Level:    clueUtil.AssetsLevelC,
								IsFake:   false,
								Reason:   "目标IP测试2",
								FakeType: 0,
							},
						},
					},
				}
				cacheJSON, _ := json.Marshal(levelArr)
				rdm.ExpectGet("cache:classifyAssetsLevel:777_*************_level").SetVal(string(cacheJSON))
			},
			expected: &clueUtil.LevelReason{
				Level:    clueUtil.AssetsLevelC,
				IsFake:   false,
				Reason:   "目标IP测试2",
				FakeType: 0,
			},
			description: "第二个目标IP应正常处理并记录详细日志",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 强制使用测试环境并重新初始化Redis Mock
			redisInit.ForceTest(true)
			redisMock := redisInit.GetMockInstance()

			// 设置Redis Mock期望
			if tt.cacheSetup != nil {
				tt.cacheSetup(redisMock)
			}

			// 调用被测试的方法
			result := getTopLevel(tt.asset, tt.userID)

			// 验证结果
			if tt.expected == nil {
				assert.Nil(t, result, tt.description)
			} else {
				assert.NotNil(t, result, tt.description)
				assert.Equal(t, tt.expected.Level, result.Level, "Level should match")
				assert.Equal(t, tt.expected.IsFake, result.IsFake, "IsFake should match")
				assert.Equal(t, tt.expected.Reason, result.Reason, "Reason should match")
				assert.Equal(t, tt.expected.FakeType, result.FakeType, "FakeType should match")
			}

			// 验证所有Mock期望都被满足
			assert.NoError(t, redisMock.ExpectationsWereMet())
		})
	}
}

// TestNewUpdateAssetsLevelCodePayload 测试创建UpdateAssetsLevelCodePayload
func TestNewUpdateAssetsLevelCodePayload(t *testing.T) {
	tests := []struct {
		name      string
		userID    uint64
		assetsID  string
		subDomain string
		url       string
		protocol  string
		expected  *asynq.UpdateAssetsLevelCodePayload
	}{
		{
			name:      "正常创建",
			userID:    123,
			assetsID:  "asset-123",
			subDomain: "test.example.com",
			url:       "https://test.example.com",
			protocol:  "https",
			expected: &asynq.UpdateAssetsLevelCodePayload{
				UserID:    123,
				SubDomain: "test.example.com",
				Url:       "https://test.example.com",
				Protocol:  "https",
				AssetId:   "asset-123",
			},
		},
		{
			name:      "空值创建",
			userID:    0,
			assetsID:  "",
			subDomain: "",
			url:       "",
			protocol:  "",
			expected: &asynq.UpdateAssetsLevelCodePayload{
				UserID:    0,
				SubDomain: "",
				Url:       "",
				Protocol:  "",
				AssetId:   "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewUpdateAssetsLevelCodePayload(tt.userID, tt.assetsID, tt.subDomain, tt.url, tt.protocol)

			assert.Equal(t, tt.expected.UserID, result.UserID)
			assert.Equal(t, tt.expected.AssetId, result.AssetId)
			assert.Equal(t, tt.expected.SubDomain, result.SubDomain)
			assert.Equal(t, tt.expected.Url, result.Url)
			assert.Equal(t, tt.expected.Protocol, result.Protocol)
		})
	}
}

// TestUpdateAssetsLevelCodeHandlerPayloadValidation 测试UpdateAssetsLevelCodeHandler的payload验证
func TestUpdateAssetsLevelCodeHandlerPayloadValidation(t *testing.T) {
	tests := []struct {
		name        string
		payload     []byte
		expectError bool
		errorMsg    string
	}{
		{
			name:        "无效JSON",
			payload:     []byte(`{invalid json`),
			expectError: true,
			errorMsg:    "解析任务数据失败",
		},
		{
			name: "有效payload",
			payload: func() []byte {
				p := asynq.UpdateAssetsLevelCodePayload{
					UserID:    123,
					SubDomain: "test.example.com",
					Url:       "https://test.example.com",
					Protocol:  "https",
					AssetId:   "asset-123",
				}
				data, _ := json.Marshal(p)
				return data
			}(),
			expectError: false,
			errorMsg:    "",
		},
		{
			name: "空SubDomain",
			payload: func() []byte {
				p := asynq.UpdateAssetsLevelCodePayload{
					UserID:    123,
					SubDomain: "",
					Url:       "https://test.example.com",
					Protocol:  "https",
					AssetId:   "asset-123",
				}
				data, _ := json.Marshal(p)
				return data
			}(),
			expectError: false,
			errorMsg:    "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock任务
			task := &asynq.Task{
				Type:    "update_assets_level_code",
				Payload: string(tt.payload),
			}

			// 由于UpdateAssetsLevelCodeHandler函数依赖外部服务，
			// 这里我们只测试payload解析部分的逻辑
			var payload asynq.UpdateAssetsLevelCodePayload
			err := json.Unmarshal([]byte(task.Payload), &payload)

			if tt.expectError && tt.errorMsg == "解析任务数据失败" {
				assert.Error(t, err)
				return
			}

			if !tt.expectError {
				assert.NoError(t, err)
				assert.Equal(t, uint64(123), payload.UserID)
				assert.Equal(t, "asset-123", payload.AssetId)
			}
		})
	}
}

// TestFilterDomainsWithMock 测试 filterDomains 方法 - 使用真实的数据库Mock
func TestFilterDomainsWithMock(t *testing.T) {
	// 初始化测试环境
	cfg.InitLoadCfg()
	log.Init()

	// 设置测试环境并获取Mock实例
	mysqlInit.SetTestEnv(true)
	mysqlInit.GetInstance(cfg.LoadMysql())

	tests := []struct {
		name           string
		inputDomains   []string
		blackDomains   []string
		dbSetup        func(sqlmock.Sqlmock)
		expectedResult []string
		expectedError  bool
		description    string
	}{
		{
			name:         "正常过滤黑名单域名",
			inputDomains: []string{"example.com", "blacklisted.com", "test.org", "evil.com"},
			blackDomains: []string{"blacklisted.com", "evil.com"},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"}).
					AddRow("blacklisted.com").
					AddRow("evil.com")
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedResult: []string{"example.com", "test.org"},
			expectedError:  false,
			description:    "应该过滤掉黑名单中的域名",
		},
		{
			name:         "过滤多个通配符域名",
			inputDomains: []string{"example.com", "**.test.com", "***.evil.org", "*.good.com"},
			blackDomains: []string{},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"})
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedResult: []string{"example.com", "*.good.com"},
			expectedError:  false,
			description:    "应该过滤掉包含多个通配符的域名",
		},
		{
			name:         "过滤无效域名格式",
			inputDomains: []string{"example.com", "invalid-domain", "test..com", "valid.org", "not_a_domain"},
			blackDomains: []string{},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"})
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedResult: []string{"example.com", "valid.org"},
			expectedError:  false,
			description:    "应该过滤掉无效格式的域名",
		},
		{
			name:         "空域名列表",
			inputDomains: []string{},
			blackDomains: []string{},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"})
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedResult: []string{},
			expectedError:  false,
			description:    "空域名列表应该返回空结果",
		},
		{
			name:         "黑名单大小写不敏感",
			inputDomains: []string{"Example.COM", "TEST.org", "blacklisted.COM"},
			blackDomains: []string{"example.com", "blacklisted.com"},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"}).
					AddRow("example.com").
					AddRow("blacklisted.com")
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedResult: []string{"TEST.org"},
			expectedError:  false,
			description:    "黑名单过滤应该不区分大小写",
		},
		{
			name:         "数据库查询失败",
			inputDomains: []string{"example.com", "test.org"},
			blackDomains: []string{},
			dbSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnError(errors.New("database connection failed"))
			},
			expectedResult: nil,
			expectedError:  true,
			description:    "数据库查询失败应该返回错误",
		},
		{
			name: "混合场景测试",
			inputDomains: []string{
				"valid.com",     // 有效域名
				"blacklist.com", // 黑名单域名
				"**.wild.com",   // 多通配符
				"invalid..com",  // 无效格式
				"*.good.org",    // 单通配符（有效）
				"BLACKLIST.COM", // 大写黑名单
			},
			blackDomains: []string{"blacklist.com"},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"}).
					AddRow("blacklist.com")
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedResult: []string{"valid.com", "*.good.org"},
			expectedError:  false,
			description:    "混合场景应该正确过滤各种类型的域名",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysqlInit.ResetMockInstance()
			mysqlMock := mysqlInit.GetMockInstance()

			// 设置Mock期望
			if tt.dbSetup != nil {
				tt.dbSetup(mysqlMock)
			}

			// 执行测试
			result, err := filterDomains(context.Background(), tt.inputDomains)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err, tt.description)
				assert.Nil(t, result, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				assert.Equal(t, tt.expectedResult, result, tt.description)
			}

			// 验证Mock期望
			if !tt.expectedError && tt.dbSetup != nil {
				assert.NoError(t, mysqlMock.ExpectationsWereMet())
			}
		})
	}
}

// TestShouldSkipDomainTask 测试shouldSkipDomainTask函数
func TestShouldSkipDomainTask(t *testing.T) {
	tests := []struct {
		name              string
		isAutoDomainBurst int
		isAutoURLApi      int
		expected          bool
		description       string
	}{
		{
			name:              "两个开关都关闭-应该跳过",
			isAutoDomainBurst: 0,
			isAutoURLApi:      0,
			expected:          true,
			description:       "当域名爆破和URL-API都关闭时，应该跳过域名任务",
		},
		{
			name:              "只开启域名爆破-不应该跳过",
			isAutoDomainBurst: 1,
			isAutoURLApi:      0,
			expected:          false,
			description:       "当域名爆破开启时，不应该跳过域名任务",
		},
		{
			name:              "只开启URL-API-不应该跳过",
			isAutoDomainBurst: 0,
			isAutoURLApi:      1,
			expected:          false,
			description:       "当URL-API开启时，不应该跳过域名任务",
		},
		{
			name:              "两个开关都开启-不应该跳过",
			isAutoDomainBurst: 1,
			isAutoURLApi:      1,
			expected:          false,
			description:       "当域名爆破和URL-API都开启时，不应该跳过域名任务",
		},
		{
			name:              "异常值测试-负数",
			isAutoDomainBurst: -1,
			isAutoURLApi:      -1,
			expected:          false,
			description:       "负数值不等于0，所以不应该跳过",
		},
		{
			name:              "异常值测试-大于1的值",
			isAutoDomainBurst: 2,
			isAutoURLApi:      0,
			expected:          false,
			description:       "大于1的值应该被视为开启状态",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试用的DetectAssetsTask
			task := &detect_assets_tasks.DetectAssetsTask{
				IsAutoDomainBurst: tt.isAutoDomainBurst,
				IsAutoURLApi:      tt.isAutoURLApi,
			}

			// 调用被测试的函数
			result := shouldSkipDomainTask(task)

			// 验证结果
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestFilterDomainsAdvanced 测试filterDomains函数的高级场景
func TestFilterDomainsAdvanced(t *testing.T) {
	// 初始化测试环境
	cfg.InitLoadCfg()
	log.Init()

	// 设置测试环境并获取Mock实例
	mysqlInit.SetTestEnv(true)
	mysqlInit.GetInstance(cfg.LoadMysql())

	tests := []struct {
		name           string
		inputDomains   []string
		dbSetup        func(sqlmock.Sqlmock)
		expectedResult []string
		expectedError  bool
		description    string
	}{
		{
			name: "边界值测试-极长域名",
			inputDomains: []string{
				"example.com",
				"very-long-subdomain-name-that-might-exceed-normal-limits.example.com",
				"a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.example.com",
			},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"})
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedResult: []string{
				"example.com",
				"very-long-subdomain-name-that-might-exceed-normal-limits.example.com",
				"a.b.c.d.e.f.g.h.i.j.k.l.m.n.o.p.q.r.s.t.u.v.w.x.y.z.example.com",
			},
			expectedError: false,
			description:   "应该能处理极长的域名",
		},
		{
			name: "特殊字符测试",
			inputDomains: []string{
				"example.com",
				"test-domain.com", // 连字符
				"test_domain.com", // 下划线（应该被过滤）
				"test.domain.com", // 多级域名
				"test..com",       // 双点（无效）
				"test-.com",       // 以连字符结尾（无效）
				"-test.com",       // 以连字符开头（无效）
			},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"})
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedResult: []string{
				"example.com",
				"test-domain.com",
				"test.domain.com",
			},
			expectedError: false,
			description:   "应该正确过滤包含特殊字符的域名",
		},
		{
			name: "通配符边界测试",
			inputDomains: []string{
				"*.example.com",     // 单通配符（有效）
				"**.example.com",    // 双通配符（无效）
				"***.example.com",   // 三通配符（无效）
				"sub.*.example.com", // 中间通配符（无效）
				"*.*.example.com",   // 多个通配符（无效）
			},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"})
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedResult: []string{
				"*.example.com",
			},
			expectedError: false,
			description:   "应该只允许单个前缀通配符",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysqlInit.ResetMockInstance()
			mysqlMock := mysqlInit.GetMockInstance()

			// 设置Mock期望
			if tt.dbSetup != nil {
				tt.dbSetup(mysqlMock)
			}

			// 执行测试
			result, err := filterDomains(context.Background(), tt.inputDomains)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err, tt.description)
				assert.Nil(t, result, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				assert.Equal(t, len(tt.expectedResult), len(result), tt.description)
				// 验证结果包含预期的域名（顺序可能不同）
				for _, expectedDomain := range tt.expectedResult {
					assert.Contains(t, result, expectedDomain,
						fmt.Sprintf("结果应该包含域名: %s", expectedDomain))
				}
			}

			// 验证Mock期望
			if !tt.expectedError && tt.dbSetup != nil {
				assert.NoError(t, mysqlMock.ExpectationsWereMet())
			}
		})
	}
}

// TestFilterDomainsErrorHandling 测试filterDomains函数的错误处理
func TestFilterDomainsErrorHandling(t *testing.T) {
	// 初始化测试环境
	cfg.InitLoadCfg()
	log.Init()

	// 设置测试环境并获取Mock实例
	mysqlInit.SetTestEnv(true)
	mysqlInit.GetInstance(cfg.LoadMysql())

	tests := []struct {
		name          string
		inputDomains  []string
		dbSetup       func(sqlmock.Sqlmock)
		expectedError bool
		errorContains string
		description   string
	}{
		{
			name:         "数据库连接超时",
			inputDomains: []string{"example.com", "test.org"},
			dbSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnError(errors.New("connection timeout"))
			},
			expectedError: true,
			errorContains: "获取黑名单域名失败",
			description:   "数据库连接超时应该返回相应错误",
		},
		{
			name:         "数据库查询语法错误",
			inputDomains: []string{"example.com"},
			dbSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnError(errors.New("syntax error"))
			},
			expectedError: true,
			errorContains: "获取黑名单域名失败",
			description:   "SQL语法错误应该返回相应错误",
		},
		{
			name:         "空输入处理",
			inputDomains: []string{},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"domain"})
				mock.ExpectQuery("SELECT (.+) FROM `black_forbidden_domains`").
					WillReturnRows(rows)
			},
			expectedError: false,
			errorContains: "",
			description:   "空输入应该正常处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysqlInit.ResetMockInstance()
			mysqlMock := mysqlInit.GetMockInstance()

			// 设置Mock期望
			if tt.dbSetup != nil {
				tt.dbSetup(mysqlMock)
			}

			// 执行测试
			result, err := filterDomains(context.Background(), tt.inputDomains)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err, tt.description)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains, tt.description)
				}
				assert.Nil(t, result, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, result, tt.description)
			}

			// 验证Mock期望
			if tt.dbSetup != nil {
				assert.NoError(t, mysqlMock.ExpectationsWereMet())
			}
		})
	}
}

// TestUpdateAssetsLevelPreloadData 测试preloadData方法
func TestUpdateAssetsLevelPreloadData(t *testing.T) {
	// 初始化测试环境
	cfg.InitLoadCfg()
	log.Init()

	// 设置测试环境并获取Mock实例
	mysqlInit.SetTestEnv(true)
	mysqlInit.GetInstance(cfg.LoadMysql())

	tests := []struct {
		name           string
		payloadData    []byte
		dbSetup        func(sqlmock.Sqlmock)
		expectedError  bool
		errorContains  string
		expectedResult *UpdateAssetsLevelPayload
		expectedClues  int
		description    string
	}{
		{
			name:          "无效JSON数据",
			payloadData:   []byte(`{invalid json`),
			dbSetup:       nil,
			expectedError: true,
			errorContains: "解析任务数据失败",
			description:   "无效JSON应该返回解析错误",
		},
		{
			name: "空任务ID",
			payloadData: func() []byte {
				payload := UpdateAssetsLevelPayload{
					UpdateAssetsLevelTaskPayload: asynq.UpdateAssetsLevelTaskPayload{
						UserID:       123,
						OperatorID:   456,
						GroupID:      789,
						Flag:         "test-flag",
						DetectTaskId: "",
					},
				}
				data, _ := json.Marshal(payload)
				return data
			}(),
			dbSetup:       nil,
			expectedError: true,
			errorContains: "任务id不能为0",
			description:   "空任务ID应该返回错误",
		},
		{
			name: "成功场景",
			payloadData: func() []byte {
				payload := UpdateAssetsLevelPayload{
					UpdateAssetsLevelTaskPayload: asynq.UpdateAssetsLevelTaskPayload{
						UserID:       123,
						OperatorID:   456,
						GroupID:      789,
						Flag:         "test-flag",
						DetectTaskId: "valid-task-id",
					},
				}
				data, _ := json.Marshal(payload)
				return data
			}(),
			dbSetup: func(mock sqlmock.Sqlmock) {
				// Mock 任务查询成功
				taskRows := sqlmock.NewRows([]string{
					"id", "user_id", "company_id", "safe_user_id",
					"name", "step", "step_detail", "detect_type", "group_id", "step_status", "status",
					"expend_flags", "expand_source", "is_auto_domain_burst", "is_auto_url_api",
					"is_intellect_mode", "bandwidth", "company_id",
				}).AddRow(
					1, 123, 100, 0,
					"测试任务", 1, 100, 1, 789, 0, 0,
					"test-flag", 0, 1, 1, 0, "1000", 100,
				)
				mock.ExpectQuery("SELECT (.+) FROM `detect_assets_tasks`").
					WithArgs("valid-task-id", 1).
					WillReturnRows(taskRows)

				// Mock 线索查询成功
				clueRows := sqlmock.NewRows([]string{
					"id", "user_id", "company_id",
					"clue_company_name", "content", "group_id", "comment", "hash", "source",
					"count", "type", "status", "parent_id", "is_expand", "safe_user_id",
					"from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url",
				}).
					AddRow(1, 123, 100,
						"测试企业", "example.com", 789, "", 0, 0,
						0, 0, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "").
					AddRow(2, 123, 100,
						"测试企业", "test.com", 789, "", 0, 0,
						0, 0, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "")
				mock.ExpectQuery("SELECT (.+) FROM `clues`").
					WithArgs(123, 789, 0, 0, 1).
					WillReturnRows(clueRows)
			},
			expectedError: false,
			expectedResult: &UpdateAssetsLevelPayload{
				UpdateAssetsLevelTaskPayload: asynq.UpdateAssetsLevelTaskPayload{
					UserID:       123,
					OperatorID:   456,
					GroupID:      789,
					Flag:         "test-flag",
					DetectTaskId: "valid-task-id",
				},
			},
			expectedClues: 2,
			description:   "成功场景应该返回正确的payload和线索数据",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysqlInit.ResetMockInstance()
			mysqlMock := mysqlInit.GetMockInstance()

			// 设置Mock期望
			if tt.dbSetup != nil {
				tt.dbSetup(mysqlMock)
			}

			// 执行测试
			payload, clueList, err := preloadData(tt.payloadData)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err, tt.description)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains, tt.description)
				}
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, payload, tt.description)
				assert.NotNil(t, clueList, tt.description)

				// 验证payload内容
				if tt.expectedResult != nil {
					assert.Equal(t, tt.expectedResult.UserID, payload.UserID, "UserID should match")
					assert.Equal(t, tt.expectedResult.OperatorID, payload.OperatorID, "OperatorID should match")
					assert.Equal(t, tt.expectedResult.GroupID, payload.GroupID, "GroupID should match")
					assert.Equal(t, tt.expectedResult.Flag, payload.Flag, "Flag should match")
					assert.Equal(t, tt.expectedResult.DetectTaskId, payload.DetectTaskId, "DetectTaskId should match")
					assert.NotNil(t, payload.detectTaskInfo, "detectTaskInfo should not be nil")
				}

				// 验证线索数量
				if tt.expectedClues > 0 {
					assert.Equal(t, tt.expectedClues, len(clueList), "Clue count should match")
				}
			}

			// 验证Mock期望
			if tt.dbSetup != nil {
				assert.NoError(t, mysqlMock.ExpectationsWereMet())
			}
		})
	}
}

// TestGetClueDomains 测试getClueDomains方法
func TestGetClueDomains(t *testing.T) {
	// 初始化测试环境
	cfg.InitLoadCfg()
	log.Init()

	// 设置测试环境并获取Mock实例
	mysqlInit.SetTestEnv(true)
	mysqlInit.GetInstance(cfg.LoadMysql())

	tests := []struct {
		name               string
		userID             uint64
		groupID            uint64
		dbSetup            func(sqlmock.Sqlmock)
		expectedTopDomains []string
		expectedSubDomains []string
		expectedError      bool
		errorContains      string
		description        string
	}{
		{
			name:    "数据库查询失败",
			userID:  123,
			groupID: 789,
			dbSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT (.+) FROM `clues`").
					WithArgs(123, 789, 1, 0, 0, 5).
					WillReturnError(errors.New("database connection failed"))
			},
			expectedTopDomains: nil,
			expectedSubDomains: nil,
			expectedError:      true,
			errorContains:      "获取主域名列表失败",
			description:        "数据库查询失败应该返回错误",
		},
		{
			name:    "空结果",
			userID:  123,
			groupID: 789,
			dbSetup: func(mock sqlmock.Sqlmock) {
				clueRows := sqlmock.NewRows([]string{
					"id", "created_at", "updated_at", "deleted_at", "user_id", "company_id",
					"clue_company_name", "content", "group_id", "comment", "hash", "source",
					"count", "type", "status", "parent_id", "is_expand", "safe_user_id",
					"from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url",
				})
				mock.ExpectQuery("SELECT (.+) FROM `clues`").
					WithArgs(123, 789, 1, 0, 0, 5).
					WillReturnRows(clueRows)
			},
			expectedTopDomains: []string{},
			expectedSubDomains: []string{},
			expectedError:      false,
			description:        "空结果应该返回空的切片",
		},
		{
			name:    "只有主域名",
			userID:  123,
			groupID: 789,
			dbSetup: func(mock sqlmock.Sqlmock) {
				clueRows := sqlmock.NewRows([]string{
					"id", "user_id", "company_id",
					"clue_company_name", "content", "group_id", "comment", "hash", "source",
					"count", "type", "status", "parent_id", "is_expand", "safe_user_id",
					"from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url",
				}).
					AddRow(1, 123, 100,
						"测试企业", "example.com", 789, "", 0, 0,
						0, 0, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "").
					AddRow(2, 123, 100,
						"测试企业", "test.org", 789, "", 0, 0,
						0, 0, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "")
				mock.ExpectQuery("SELECT (.+) FROM `clues`").
					WithArgs(123, 789, 1, 0, 0, 5).
					WillReturnRows(clueRows)
			},
			expectedTopDomains: []string{"example.com", "test.org"},
			expectedSubDomains: []string{},
			expectedError:      false,
			description:        "只有主域名时应该正确分类",
		},
		{
			name:    "只有子域名",
			userID:  123,
			groupID: 789,
			dbSetup: func(mock sqlmock.Sqlmock) {
				clueRows := sqlmock.NewRows([]string{
					"id", "user_id", "company_id",
					"clue_company_name", "content", "group_id", "comment", "hash", "source",
					"count", "type", "status", "parent_id", "is_expand", "safe_user_id",
					"from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url",
				}).
					AddRow(1, 123, 100,
						"测试企业", "sub.example.com", 789, "", 0, 0,
						0, 5, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "").
					AddRow(2, 123, 100,
						"测试企业", "api.test.org", 789, "", 0, 0,
						0, 5, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "")
				mock.ExpectQuery("SELECT (.+) FROM `clues`").
					WithArgs(123, 789, 1, 0, 0, 5).
					WillReturnRows(clueRows)
			},
			expectedTopDomains: []string{},
			expectedSubDomains: []string{"sub.example.com", "api.test.org"},
			expectedError:      false,
			description:        "只有子域名时应该正确分类",
		},
		{
			name:    "混合域名类型",
			userID:  123,
			groupID: 789,
			dbSetup: func(mock sqlmock.Sqlmock) {
				clueRows := sqlmock.NewRows([]string{
					"id", "user_id", "company_id",
					"clue_company_name", "content", "group_id", "comment", "hash", "source",
					"count", "type", "status", "parent_id", "is_expand", "safe_user_id",
					"from_ip", "is_deleted", "punycode_domain", "is_from_check_table",
					"is_supply_chain", "is_fake_icp", "source_url",
				}).
					AddRow(1, 123, 100,
						"测试企业", "example.com", 789, "", 0, 0,
						0, 0, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "").
					AddRow(2, 123, 100,
						"测试企业", "sub.example.com", 789, "", 0, 0,
						0, 5, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "").
					AddRow(3, 123, 100,
						"测试企业", "test.org", 789, "", 0, 0,
						0, 0, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "").
					AddRow(4, 123, 100,
						"测试企业", "api.test.org", 789, "", 0, 0,
						0, 5, 1, 0, 0, 0,
						"", 0, "", "0", 0, 0, "")
				mock.ExpectQuery("SELECT (.+) FROM `clues`").
					WithArgs(123, 789, 1, 0, 0, 5).
					WillReturnRows(clueRows)
			},
			expectedTopDomains: []string{"example.com", "test.org"},
			expectedSubDomains: []string{"sub.example.com", "api.test.org"},
			expectedError:      false,
			description:        "混合域名类型应该正确分类",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysqlInit.ResetMockInstance()
			mysqlMock := mysqlInit.GetMockInstance()

			// 设置Mock期望
			if tt.dbSetup != nil {
				tt.dbSetup(mysqlMock)
			}

			// 执行测试
			topDomains, subDomains, err := getClueDomains(tt.userID, tt.groupID)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err, tt.description)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains, tt.description)
				}
				assert.Nil(t, topDomains, tt.description)
				assert.Nil(t, subDomains, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, topDomains, tt.description)
				assert.NotNil(t, subDomains, tt.description)

				// 验证主域名
				assert.Equal(t, len(tt.expectedTopDomains), len(topDomains), "Top domains count should match")
				for i, expectedDomain := range tt.expectedTopDomains {
					assert.Equal(t, expectedDomain, topDomains[i], "Top domain should match")
				}

				// 验证子域名
				assert.Equal(t, len(tt.expectedSubDomains), len(subDomains), "Sub domains count should match")
				for i, expectedDomain := range tt.expectedSubDomains {
					assert.Equal(t, expectedDomain, subDomains[i], "Sub domain should match")
				}
			}

			// 验证Mock期望
			if tt.dbSetup != nil {
				assert.NoError(t, mysqlMock.ExpectationsWereMet())
			}
		})
	}
}

// TestProcessAllDomains 测试processAllDomains方法
func TestProcessAllDomains(t *testing.T) {
	tests := []struct {
		name                 string
		allDomains           []string
		inputMasterDomain    []string
		inputSecondDomain    []string
		inputThirdDomain     []string
		expectedMasterDomain []string
		expectedSecondDomain []string
		expectedThirdDomain  []string
		description          string
	}{
		{
			name:                 "空输入",
			allDomains:           []string{},
			inputMasterDomain:    []string{},
			inputSecondDomain:    []string{},
			inputThirdDomain:     []string{},
			expectedMasterDomain: []string{},
			expectedSecondDomain: []string{},
			expectedThirdDomain:  []string{},
			description:          "空输入应该返回空结果",
		},
		{
			name:                 "只有主域名",
			allDomains:           []string{"example.com", "test.org", "demo.net"},
			inputMasterDomain:    []string{},
			inputSecondDomain:    []string{},
			inputThirdDomain:     []string{},
			expectedMasterDomain: []string{"example.com", "test.org", "demo.net"},
			expectedSecondDomain: []string{},
			expectedThirdDomain:  []string{},
			description:          "只有主域名时应该正确分类",
		},
		{
			name:                 "只有二级域名",
			allDomains:           []string{"sub.example.com", "api.test.org", "www.demo.net"},
			inputMasterDomain:    []string{},
			inputSecondDomain:    []string{},
			inputThirdDomain:     []string{},
			expectedMasterDomain: []string{},
			expectedSecondDomain: []string{"sub.example.com", "api.test.org", "www.demo.net"},
			expectedThirdDomain:  []string{},
			description:          "只有二级域名时应该正确分类",
		},
		{
			name:                 "只有三级域名",
			allDomains:           []string{"app.sub.example.com", "api.dev.test.org"},
			inputMasterDomain:    []string{},
			inputSecondDomain:    []string{},
			inputThirdDomain:     []string{},
			expectedMasterDomain: []string{},
			expectedSecondDomain: []string{},
			expectedThirdDomain:  []string{"app.sub.example.com", "api.dev.test.org"},
			description:          "只有三级域名时应该正确分类",
		},
		{
			name: "混合域名类型",
			allDomains: []string{
				"example.com",         // 主域名
				"sub.example.com",     // 二级域名
				"app.sub.example.com", // 三级域名
				"test.org",            // 主域名
				"api.test.org",        // 二级域名
				"v1.api.test.org",     // 三级域名
			},
			inputMasterDomain:    []string{},
			inputSecondDomain:    []string{},
			inputThirdDomain:     []string{},
			expectedMasterDomain: []string{"example.com", "test.org"},
			expectedSecondDomain: []string{"sub.example.com", "api.test.org"},
			expectedThirdDomain:  []string{"app.sub.example.com", "v1.api.test.org"},
			description:          "混合域名类型应该正确分类",
		},
		{
			name:                 "已有输入数据",
			allDomains:           []string{"new.example.com", "demo.net"},
			inputMasterDomain:    []string{"existing.com"},
			inputSecondDomain:    []string{"old.test.org"},
			inputThirdDomain:     []string{"deep.old.test.org"},
			expectedMasterDomain: []string{"existing.com", "demo.net"},
			expectedSecondDomain: []string{"old.test.org", "new.example.com"},
			expectedThirdDomain:  []string{"deep.old.test.org"},
			description:          "应该在已有数据基础上追加新数据",
		},
		{
			name: "主域名数量限制测试",
			allDomains: func() []string {
				domains := make([]string, 105)
				for i := 0; i < 105; i++ {
					domains[i] = fmt.Sprintf("domain%d.com", i)
				}
				return domains
			}(),
			inputMasterDomain: []string{},
			inputSecondDomain: []string{},
			inputThirdDomain:  []string{},
			expectedMasterDomain: func() []string {
				domains := make([]string, 101)
				for i := 0; i < 101; i++ {
					domains[i] = fmt.Sprintf("domain%d.com", i)
				}
				return domains
			}(),
			expectedSecondDomain: []string{},
			expectedThirdDomain:  []string{},
			description:          "主域名数量应该限制在101个以内",
		},
		{
			name: "子域名数量限制测试",
			allDomains: func() []string {
				domains := make([]string, 3000)
				for i := 0; i < 3000; i++ {
					domains[i] = fmt.Sprintf("sub%d.example.com", i)
				}
				return domains
			}(),
			inputMasterDomain:    []string{},
			inputSecondDomain:    []string{},
			inputThirdDomain:     []string{},
			expectedMasterDomain: []string{},
			expectedSecondDomain: func() []string {
				domains := make([]string, 2900)
				for i := 0; i < 2900; i++ {
					domains[i] = fmt.Sprintf("sub%d.example.com", i)
				}
				return domains
			}(),
			expectedThirdDomain: []string{},
			description:         "子域名数量应该限制在2900个以内",
		},
		{
			name:                 "包含空字符串",
			allDomains:           []string{"example.com", "", "sub.test.org", ""},
			inputMasterDomain:    []string{},
			inputSecondDomain:    []string{},
			inputThirdDomain:     []string{},
			expectedMasterDomain: []string{"example.com"},
			expectedSecondDomain: []string{"sub.test.org"},
			expectedThirdDomain:  []string{},
			description:          "应该忽略空字符串",
		},
		{
			name:                 "无效域名格式",
			allDomains:           []string{"example.com", "invalid", "sub.test.org"},
			inputMasterDomain:    []string{},
			inputSecondDomain:    []string{},
			inputThirdDomain:     []string{},
			expectedMasterDomain: []string{"example.com"},
			expectedSecondDomain: []string{"sub.test.org"},
			expectedThirdDomain:  []string{},
			description:          "应该处理无效域名格式",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行测试
			resultMaster, resultSecond, resultThird := processAllDomains(
				tt.allDomains,
				tt.inputMasterDomain,
				tt.inputSecondDomain,
				tt.inputThirdDomain,
			)

			// 验证主域名结果
			assert.Equal(t, len(tt.expectedMasterDomain), len(resultMaster),
				"Master domain count should match for %s", tt.description)
			for i, expected := range tt.expectedMasterDomain {
				if i < len(resultMaster) {
					assert.Equal(t, expected, resultMaster[i],
						"Master domain at index %d should match for %s", i, tt.description)
				}
			}

			// 验证二级域名结果
			assert.Equal(t, len(tt.expectedSecondDomain), len(resultSecond),
				"Second domain count should match for %s", tt.description)
			for i, expected := range tt.expectedSecondDomain {
				if i < len(resultSecond) {
					assert.Equal(t, expected, resultSecond[i],
						"Second domain at index %d should match for %s", i, tt.description)
				}
			}

			// 验证三级域名结果
			assert.Equal(t, len(tt.expectedThirdDomain), len(resultThird),
				"Third domain count should match for %s", tt.description)
			for i, expected := range tt.expectedThirdDomain {
				if i < len(resultThird) {
					assert.Equal(t, expected, resultThird[i],
						"Third domain at index %d should match for %s", i, tt.description)
				}
			}
		})
	}
}

func TestGetSubDomainsFromRecommendResult(t *testing.T) {
	setupUpdateAssetsLevelTestEnvironment()

	tests := []struct {
		name            string
		info            *tasks.DetectAssetsTask
		userID          uint64
		topDomains      []string
		expectedDomains []string
		expectedError   error
		setupMockEs     func(mockEs *testcommon.MockServer)
	}{
		{
			name: "正常情况",
			info: &tasks.DetectAssetsTask{
				Model: gorm.Model{ID: 1},
			},
			userID:          123,
			topDomains:      []string{"example.com", "test.org"},
			expectedDomains: []string{"example.com", "test.org"},
			expectedError:   nil,
			setupMockEs: func(mockEs *testcommon.MockServer) {
				asset1Source := json.RawMessage(`{
					"fid": "",
					"user_id": 624,
					"company_id": 0,
					"group_id": 8179,
					"task_id": 0,
					"fake_task_id": 0,
					"status": 0,
					"audit": 0,
					"assets_from": 0,
					"fake_type": 0,
					"type": 0,
					"level": 0,
					"threaten_type": 0,
					"threaten_type_name": "",
					"id": "asset1",
					"ip": "***********",
					"port": 80,
					"group_name": "资产测绘-北京华顺信安科技有限公司2025-07-01 20:56:50",
					"url": "",
					"screenshot": "",
					"protocol": "http",
					"base_protocol": "tcp",
					"title": "301 Moved Permanently",
					"domain": "huashunxinan.net",
					"subdomain": "",
					"cert": "",
					"icp": "",
					"fake_icp_company": "",
					"fake_company": "",
					"server": "",
					"flag": "0f1126ddaa0105854adde6324b5b7678",
					"cloud_name": "",
					"isp": "",
					"cert_raw": "",
					"logo": {
						"hash": 0,
						"content": ""
					},
					"reason": [
						{
							"id": 350981,
							"type": 0,
							"content": "huashunxinan.net",
							"group_id": 0,
							"clue_company_name": "",
							"source": 0
						}
					],
					"certs_valid": false,
					"is_ipv6": false,
					"is_fake_assets": false,
					"open_parse": false,
					"is_cdn": false,
					"online_state": false,
					"source_updated_at": "2023-03-06 08:00:00",
					"created_at": "2025-07-01 21:06:26",
					"updated_at": "2025-07-01 21:06:26",
					"fake_deep": false,
					"fake_asset_from": "",
					"assets_source": 1,
					"oneforall_source": "",
					"cname": "",
					"all_company_name": null,
					"clue_company_name": [
						"北京华顺信安科技有限公司"
					],
					"banner": "",
					"product": "",
					"ip_status": 0,
					"assets_confidence_level": 0
				}`)
				mockEs.RegisterSearchAfterHandler("/foradar_recommend_result/_search", map[string]*json.RawMessage{
					"asset1": &asset1Source,
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			esServer := testcommon.NewMockEsServer()
			tt.setupMockEs(esServer)
			defer esServer.Close()

			domains, err := getSubDomainsFromRecommendResult(tt.info, tt.userID, tt.topDomains)
			assert.Equal(t, tt.expectedDomains, domains)
			assert.Equal(t, tt.expectedError, err)
		})
	}
}

func TestCreateDomainTask(t *testing.T) {
	setupUpdateAssetsLevelTestEnvironment()

	tests := []struct {
		name          string
		chunk         []string
		userID        uint64
		info          *tasks.DetectAssetsTask
		expectedError error
		setupMockDb   func(mockDb sqlmock.Sqlmock)
	}{
		{
			name:   "正常情况",
			chunk:  []string{"example.com", "test.org"},
			userID: 123,
			info: &tasks.DetectAssetsTask{
				Model:     gorm.Model{ID: 1},
				Bandwidth: "100",
			},
			setupMockDb: func(mockDb sqlmock.Sqlmock) {
				mockDb.ExpectBegin()
				mockDb.ExpectExec("INSERT INTO `domain_tasks`").
					WillReturnResult(sqlmock.NewResult(1, 1))
				mockDb.ExpectCommit()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDb := mysqlInit.GetMockInstance()
			tt.setupMockDb(mockDb)

			err := createDomainTask(tt.chunk, tt.userID, tt.info)
			assert.Equal(t, tt.expectedError, err)
			assert.NoError(t, mockDb.ExpectationsWereMet())
		})
	}
}

func TestUpdateDomainTask(t *testing.T) {
	setupUpdateAssetsLevelTestEnvironment()

	tests := []struct {
		name          string
		userID        uint64
		detectTaskId  uint64
		taskId        uint64
		expectedError error
		setupMockDb   func(mockDb sqlmock.Sqlmock)
	}{
		{
			name:          "正常情况",
			userID:        123,
			detectTaskId:  1,
			taskId:        1,
			expectedError: nil,
			setupMockDb: func(mockDb sqlmock.Sqlmock) {
				mockDb.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `domain_tasks` WHERE user_id = ? AND detect_task_id = ? ORDER BY id DESC,`domain_tasks`.`id` LIMIT ?")).
					WillReturnRows(sqlmock.NewRows([]string{"id", "detect_task_id", "task_id"}).AddRow(1, 1, 1))
				mockDb.ExpectBegin()
				mockDb.ExpectExec("UPDATE `domain_tasks`").
					WillReturnResult(sqlmock.NewResult(1, 1))
				mockDb.ExpectCommit()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDb := mysqlInit.GetMockInstance()
			tt.setupMockDb(mockDb)

			domainTaskRecord, err := updateDomainTask(tt.userID, tt.detectTaskId, tt.taskId)
			assert.Equal(t, tt.expectedError, err)
			assert.NotNil(t, domainTaskRecord)
		})
	}
}
