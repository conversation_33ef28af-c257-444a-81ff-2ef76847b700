package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"micro-service/initialize/es"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"

	"github.com/olivere/elastic"
)

// UpdateIpAndPortOnlineStateJob 更新IP端口在线状态
// 消息体:
//
//	{
//		"user_id": 1,    // 用户ID，必填
//		"task_id": 1     // 任务ID，可选
//	}
func UpdateIpAndPortOnlineStateJob(ctx context.Context, t *asyncq.Task) error {
	payloadInfo, err := parsePayloadForUpdateIpAndPortOnlineState([]byte(t.Payload))
	if err != nil {
		return err
	}

	log.Info("UpdateIpAndPortOnlineState", "同步ip端口维度数据的online_state-开始", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"task_id": payloadInfo.TaskId,
	})

	// 第一部分：同步IP端口维度数据的online_state
	err = syncIpPortOnlineState(ctx, payloadInfo.UserId)
	if err != nil {
		log.Error("UpdateIpAndPortOnlineState", "更新ip端口维度的online_state报错了", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"error":   err.Error(),
		})
	}

	// 第二部分：处理IP维度数据和IP端口维度数据online_state不一致问题
	err = syncIpDimensionOnlineState(ctx, payloadInfo.UserId, payloadInfo.TaskId)
	if err != nil {
		log.Error("UpdateIpAndPortOnlineState", "处理ip维度数据和ip端口纬度数据online_state不一致问题报错了", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
	}

	return nil
}

// syncIpPortOnlineState 同步IP端口维度数据的online_state
func syncIpPortOnlineState(ctx context.Context, userId int) error {
	// 查询ForadarAssets数据
	foradarModel := foradar_assets.NewForadarAssetModel()
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))

	assets, err := foradarModel.ListAll(ctx, query, "ip", "port", "online_state")
	if err != nil {
		return fmt.Errorf("查询ForadarAssets失败: %v", err)
	}

	if len(assets) == 0 {
		log.Info("UpdateIpAndPortOnlineState", "同步ip端口维度数据的online_state-完成", map[string]interface{}{
			"user_id": userId,
		})
		return nil
	}

	// 按IP分组数据 - 对应PHP: $data[$v['ip']][] = $v->toArray();
	dataByIp := make(map[string][]foradar_assets.ForadarAsset)
	// 按IP-Port组合分组 - 对应PHP: $new[$vv['ip'].'-'.$vv['port']][] = $vv;
	dataByIpPort := make(map[string][]foradar_assets.ForadarAsset)
	for _, asset := range assets {
		dataByIp[asset.Ip] = append(dataByIp[asset.Ip], asset)
		key := fmt.Sprintf("%s-%d", asset.Ip, asset.Port)
		dataByIpPort[key] = append(dataByIpPort[key], asset)
	}

	num := 0
	for ipPortKey, assets := range dataByIpPort {
		if len(assets) <= 1 {
			continue
		}

		// 检查online_state是否有多种状态
		onlineStates := make(map[int]bool)
		for _, asset := range assets {
			state := utils.SafeInt(asset.OnlineState)
			onlineStates[state] = true
		}

		if len(onlineStates) <= 1 {
			continue
		}

		// 需要统一端口在线状态
		parts := strings.Split(ipPortKey, "-")
		if len(parts) != 2 {
			continue
		}
		ip := parts[0]
		port := parts[1]

		log.Info("UpdateIpAndPortOnlineState", "当前ip的同一个端口存在2种资产状态，需要统一为一种状态", map[string]interface{}{
			"user_id": userId,
			"ip":      ip,
			"port":    port,
		})

		// 获取最新更新时间的在线状态
		state, err := getLatestOnlineState(ctx, foradarModel, userId, ip, port)
		if err != nil {
			log.Error("UpdateIpAndPortOnlineState", "获取最新在线状态失败", map[string]interface{}{
				"user_id": userId,
				"ip":      ip,
				"port":    port,
				"error":   err.Error(),
			})
			continue
		}

		// 记录当前状态用于对比
		var currentStates []map[string]interface{}
		for _, asset := range assets {
			currentStates = append(currentStates, map[string]interface{}{
				"asset_id":         asset.ID,
				"old_online_state": utils.SafeInt(asset.OnlineState),
			})
		}

		// 更新数据
		log.Info("UpdateIpAndPortOnlineState", "开始更新ip端口维度的在线状态", map[string]interface{}{
			"ip":             ip,
			"port":           port,
			"user_id":        userId,
			"target_state":   state,
			"assets_count":   len(assets),
			"current_states": currentStates,
		})

		err = updateForadarAssetsOnlineState(ctx, foradarModel, userId, ip, port, state)
		if err != nil {
			log.Error("UpdateIpAndPortOnlineState", "更新ForadarAssets在线状态失败", map[string]interface{}{
				"user_id": userId,
				"ip":      ip,
				"port":    port,
				"error":   err.Error(),
			})
			continue
		}

		log.Info("UpdateIpAndPortOnlineState", "完成更新ip端口维度的在线状态", map[string]interface{}{
			"ip":           ip,
			"port":         port,
			"user_id":      userId,
			"new_state":    state,
			"assets_count": len(assets),
		})
		num++
	}

	log.Info("UpdateIpAndPortOnlineState", "同步ip端口维度数据的online_state-完成", map[string]interface{}{
		"user_id": userId,
		"更新次数":    num,
	})
	return nil
}

// syncIpDimensionOnlineState 处理IP维度数据和IP端口维度数据online_state不一致问题
func syncIpDimensionOnlineState(ctx context.Context, userId, taskId int) error {
	log.Info("UpdateIpAndPortOnlineState", "开始处理ip维度数据和ip端口纬度数据online_state不一致问题", map[string]interface{}{
		"user_id": userId,
		"task_id": taskId,
	})

	// 查询IP维度数据(FofaeeAssets对应PHP的IpAssets)
	fofaeeModel := fofaee_assets.NewFofaeeAssetsModel()

	// 使用FindByCondition查询IP维度数据
	condition := &fofaee_assets.FindCondition{
		UserId: uint64(userId),
	}

	ipAssets, _, err := fofaeeModel.FindByCondition(ctx, condition, 1, 10000, "ip", "online_state", "task_id")
	if err != nil {
		return fmt.Errorf("查询FofaeeAssets失败: %v", err)
	}

	// 如果指定了taskId，需要过滤结果
	var filteredAssets []*fofaee_assets.FofaeeAssets
	if taskId > 0 {
		for _, asset := range ipAssets {
			for _, tid := range asset.TaskId {
				if tid == taskId {
					filteredAssets = append(filteredAssets, asset)
					break
				}
			}
		}
		ipAssets = filteredAssets
	}

	// 构建IP -> online_state映射
	ipData := make(map[string]int)
	for _, asset := range ipAssets {
		ipData[asset.Ip] = asset.OnlineState
	}

	// 查询IP端口维度数据
	foradarModel := foradar_assets.NewForadarAssetModel()
	portQuery := elastic.NewBoolQuery()
	portQuery.Must(elastic.NewTermQuery("user_id", userId))
	if taskId > 0 {
		portQuery.Must(elastic.NewTermQuery("task_id", taskId))
	}

	portAssets, err := foradarModel.ListAll(ctx, portQuery)
	if err != nil {
		return fmt.Errorf("查询ForadarAssets失败: %v", err)
	}

	// 按IP分组端口数据的online_state - 对应PHP: $new2[$v2['ip']][] = $v2['online_state'];
	ipPortStates := make(map[string][]int)
	for _, asset := range portAssets {
		// asset.OnlineState是any类型，需要类型断言
		if state, ok := asset.OnlineState.(int); ok {
			ipPortStates[asset.Ip] = append(ipPortStates[asset.Ip], state)
		} else if state, ok := asset.OnlineState.(float64); ok {
			ipPortStates[asset.Ip] = append(ipPortStates[asset.Ip], int(state))
		}
	}

	updateNum := 0
	for ip, states := range ipPortStates {
		// 计算该IP的最高在线状态
		var thisOnlineState int
		if len(states) == 0 {
			thisOnlineState = 0
		} else {
			thisOnlineState = maxInt(states)
		}

		// 检查是否需要更新IP维度数据
		if currentState, exists := ipData[ip]; exists && currentState != thisOnlineState {
			log.Info("UpdateIpAndPortOnlineState", "检测到IP维度数据状态不一致", map[string]interface{}{
				"ip":                    ip,
				"user_id":               userId,
				"current_ip_state":      currentState,
				"calculated_port_state": thisOnlineState,
				"port_states":           states,
			})

			// 如果当前状态是离线，需要检查是否存在在线的端口
			if thisOnlineState == 0 {
				hasOnlinePort, err := checkHasOnlinePort(ctx, foradarModel, userId, ip)
				if err != nil {
					log.Error("UpdateIpAndPortOnlineState", "检查IP是否有在线端口失败", map[string]interface{}{
						"user_id": userId,
						"ip":      ip,
						"error":   err.Error(),
					})
					continue
				}
				if hasOnlinePort {
					log.Info("UpdateIpAndPortOnlineState", "跳过更新-当前ip端口维度数据存在在线的数据，不更新ip维度的资产在线离线状态", map[string]interface{}{
						"ip":                    ip,
						"user_id":               userId,
						"current_ip_state":      currentState,
						"calculated_port_state": thisOnlineState,
						"has_online_port":       hasOnlinePort,
					})
					continue
				}
			}

			log.Info("UpdateIpAndPortOnlineState", "开始更新ip资产维度的在线状态", map[string]interface{}{
				"ip":          ip,
				"user_id":     userId,
				"old_state":   currentState,
				"new_state":   thisOnlineState,
				"port_states": states,
			})

			// 更新IP维度数据的状态
			err = updateFofaeeAssetsOnlineState(ctx, fofaeeModel, userId, taskId, ip, thisOnlineState)
			if err != nil {
				log.Error("UpdateIpAndPortOnlineState", "更新FofaeeAssets在线状态失败", map[string]interface{}{
					"user_id": userId,
					"ip":      ip,
					"error":   err.Error(),
				})
				continue
			}

			log.Info("UpdateIpAndPortOnlineState", "完成更新ip资产维度的在线状态", map[string]interface{}{
				"ip":        ip,
				"user_id":   userId,
				"old_state": currentState,
				"new_state": thisOnlineState,
			})
			updateNum++
		}
	}

	log.Info("UpdateIpAndPortOnlineState", "处理ip维度数据和ip端口纬度数据online_state不一致问题-完成", map[string]interface{}{
		"user_id": userId,
		"task_id": taskId,
		"更新次数":    updateNum,
	})
	return nil
}

// getLatestOnlineState 获取IP端口最新的在线状态
func getLatestOnlineState(ctx context.Context, model foradar_assets.ForadarAssetModel, userId int, ip, port string) (int, error) {
	log.Info("UpdateIpAndPortOnlineState", "开始获取IP端口最新在线状态", map[string]interface{}{
		"user_id": userId,
		"ip":      ip,
		"port":    port,
	})

	// 首先尝试从ForadarAssets获取最新状态
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermQuery("ip.keyword", ip))

	// 端口需要转换为数值类型
	portInt, err := strconv.Atoi(port)
	if err != nil {
		return 0, fmt.Errorf("端口转换失败: %v", err)
	}
	query.Must(elastic.NewTermQuery("port", portInt))

	// 按updated_at降序排序，获取最新的一条
	_, assets, err := model.List(ctx, query, 1, 1)
	if err != nil {
		log.Error("UpdateIpAndPortOnlineState", "查询ForadarAssets失败", map[string]interface{}{
			"user_id": userId,
			"ip":      ip,
			"port":    port,
			"error":   err.Error(),
		})
		return 0, err
	}

	if len(assets) > 0 {
		// assets[0].OnlineState是any类型，需要类型断言
		var state int
		if s, ok := assets[0].OnlineState.(int); ok {
			state = s
		} else if s, ok := assets[0].OnlineState.(float64); ok {
			state = int(s)
		}

		log.Info("UpdateIpAndPortOnlineState", "从ForadarAssets获取到最新状态", map[string]interface{}{
			"user_id":      userId,
			"ip":           ip,
			"port":         port,
			"asset_id":     assets[0].ID,
			"online_state": state,
			"updated_at":   assets[0].UpdatedAt,
		})
		return state, nil
	}

	log.Info("UpdateIpAndPortOnlineState", "ForadarAssets中未找到数据，尝试查询TaskAssets", map[string]interface{}{
		"user_id": userId,
		"ip":      ip,
		"port":    port,
	})

	// 如果ForadarAssets中没有数据，查询TaskAssets
	// 对应PHP: $taskIpInfo = TaskAsset::query()->where('user_id',$this->user_id)->where('ip',$ip)->where('port_list.port',$port)->where('is_php_fill','!=',1)->where('updated_at','>',$date)->first();
	state, err := queryTaskAssetOnlineState(ctx, userId, ip, portInt)
	if err != nil {
		log.Error("UpdateIpAndPortOnlineState", "查询TaskAsset失败", map[string]interface{}{
			"user_id": userId,
			"ip":      ip,
			"port":    port,
			"error":   err.Error(),
		})
		return 0, nil // 默认离线
	}

	log.Info("UpdateIpAndPortOnlineState", "从TaskAssets获取到状态", map[string]interface{}{
		"user_id":      userId,
		"ip":           ip,
		"port":         port,
		"online_state": state,
	})

	return state, nil
}

// updateForadarAssetsOnlineState 更新ForadarAssets的在线状态
func updateForadarAssetsOnlineState(ctx context.Context, model foradar_assets.ForadarAssetModel, userId int, ip, port string, state int) error {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermQuery("ip.keyword", ip))

	// 端口需要转换为数值类型
	portInt, err := strconv.Atoi(port)
	if err != nil {
		return fmt.Errorf("端口转换失败: %v", err)
	}
	query.Must(elastic.NewTermQuery("port", portInt))

	updateData := map[string]any{
		"online_state": state,
	}

	log.Info("UpdateIpAndPortOnlineState", "开始更新ForadarAssets记录", map[string]interface{}{
		"user_id":     userId,
		"ip":          ip,
		"port":        portInt,
		"new_state":   state,
		"update_data": updateData,
	})

	err = model.UpdateByQuery(ctx, query, updateData)
	if err != nil {
		log.Error("UpdateIpAndPortOnlineState", "更新ForadarAssets记录失败", map[string]interface{}{
			"user_id": userId,
			"ip":      ip,
			"port":    portInt,
			"state":   state,
			"error":   err.Error(),
		})
		return err
	}

	log.Info("UpdateIpAndPortOnlineState", "成功更新ForadarAssets记录", map[string]interface{}{
		"user_id":   userId,
		"ip":        ip,
		"port":      portInt,
		"new_state": state,
	})

	return nil
}

// updateFofaeeAssetsOnlineState 更新FofaeeAssets的在线状态
func updateFofaeeAssetsOnlineState(ctx context.Context, model fofaee_assets.FofaeeAssetsModel, userId, taskId int, ip string, state int) error {
	// FofaeeAssets更新需要构建对应的对象
	assetId := fofaee_assets.GenId(userId, ip)
	updateAsset := &fofaee_assets.FofaeeAssets{
		Id:          assetId,
		UserId:      userId,
		Ip:          ip,
		OnlineState: state,
	}

	if taskId > 0 {
		updateAsset.TaskId = []int{taskId}
	}

	log.Info("UpdateIpAndPortOnlineState", "开始更新FofaeeAssets记录", map[string]interface{}{
		"asset_id":  assetId,
		"user_id":   userId,
		"task_id":   taskId,
		"ip":        ip,
		"new_state": state,
	})

	err := model.Updates(ctx, updateAsset)
	if err != nil {
		log.Error("UpdateIpAndPortOnlineState", "更新FofaeeAssets记录失败", map[string]interface{}{
			"asset_id": assetId,
			"user_id":  userId,
			"task_id":  taskId,
			"ip":       ip,
			"state":    state,
			"error":    err.Error(),
		})
		return err
	}

	log.Info("UpdateIpAndPortOnlineState", "成功更新FofaeeAssets记录", map[string]interface{}{
		"asset_id":  assetId,
		"user_id":   userId,
		"task_id":   taskId,
		"ip":        ip,
		"new_state": state,
	})

	return nil
}

// checkHasOnlinePort 检查IP是否有在线的端口
func checkHasOnlinePort(ctx context.Context, model foradar_assets.ForadarAssetModel, userId int, ip string) (bool, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermQuery("ip.keyword", ip))
	query.Must(elastic.NewTermQuery("online_state", 1))

	count, err := model.Count(ctx, query)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// maxInt 返回整数切片中的最大值
func maxInt(slice []int) int {
	if len(slice) == 0 {
		return 0
	}
	max := slice[0]
	for _, v := range slice[1:] {
		if v > max {
			max = v
		}
	}
	return max
}

// queryTaskAssetOnlineState 查询TaskAsset获取在线状态
// 对应PHP: TaskAsset::query()->where('user_id',$this->user_id)->where('ip',$ip)->where('port_list.port',$port)->where('is_php_fill','!=',1)->where('updated_at','>',$date)->first();
func queryTaskAssetOnlineState(ctx context.Context, userId int, ip string, port int) (int, error) {
	// 计算48小时前的时间 - 对应PHP: $date = Carbon::now()->subHours(48)->format('Y-m-d H:i:s');
	date48HoursAgo := time.Now().Add(-48 * time.Hour).Format("2006-01-02 15:04:05")

	// 构建查询条件
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermQuery("ip.keyword", ip))
	query.Must(elastic.NewRangeQuery("updated_at").Gt(date48HoursAgo))

	// is_php_fill != 1 的条件
	query.MustNot(elastic.NewTermQuery("is_php_fill", 1))

	// 查询port_list.port字段，需要使用nested查询
	nestedQuery := elastic.NewNestedQuery("port_list", elastic.NewTermQuery("port_list.port", port))
	query.Must(nestedQuery)

	// 执行查询，获取第一条记录
	esClient := es.GetInstance()
	searchResult, err := esClient.Search().
		Index("fofaee_task_assets").
		Type("ips").
		Query(query).
		Size(1).
		Do(ctx)

	if err != nil {
		return 0, err
	}

	// 如果找到记录，返回在线状态1；否则返回离线状态0
	if searchResult.TotalHits() > 0 {
		return 1, nil
	}

	return 0, nil
}

// 解析payload
func parsePayloadForUpdateIpAndPortOnlineState(payload []byte) (payloadInfo *asyncq.UpdateIpAndPortOnlineStateJobPayload, err error) {
	err = json.Unmarshal(payload, &payloadInfo)
	if err != nil {
		log.Error("UpdateIpAndPortOnlineState", "解析payload失败", map[string]interface{}{
			"payload": string(payload),
			"error":   err.Error(),
		})
		return nil, err
	}
	if payloadInfo.UserId == 0 {
		log.Error("UpdateIpAndPortOnlineState", "用户ID不能为空", map[string]interface{}{
			"payload": string(payload),
		})
		return nil, errors.New("用户ID不能为空")
	}
	return payloadInfo, nil
}
