package handlers

import (
	"context"
	"database/sql"
	"errors"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	mysqlInit "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

// 测试初始化
func setupCreateDetectTaskTestEnvironment() {
	cfg.InitLoadCfg()
	log.Init()

	es.SetTestEnv(true)
	mysqlInit.SetTestEnv(true)
	redis.SetTestEnv(true)

	es.GetInstance(cfg.LoadElastic())
	mysqlInit.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

// 辅助函数
func createTestCreateDetectTaskUser() *user.User {
	return &user.User{
		Id:                    1,
		Name:                  &sql.NullString{String: "test_user", Valid: true},
		CompanyId:             &sql.NullInt64{Int64: 100, Valid: true},
		TargetCompanyName:     "Test Company",
		Percent:               60,
		NeedAssetMapping:      1,
		OtherMappingCompanies: "Company1,Company2,Company3",
		DetectTasksStep:       user.DetectTasksStepInit,
	}
}

// MockDetectAssetTaskHandler 模拟detect_asset_task.CreateTask方法
type MockDetectAssetTaskHandler struct {
	mock.Mock
}

func (m *MockDetectAssetTaskHandler) CreateTask(ctx context.Context, req *pb.DetectAssetTaskCreateRequest, rsp *pb.DetectAssetTaskCreateResponse) error {
	args := m.Called(ctx, req, rsp)
	if args.Error(0) == nil && rsp != nil {
		// 设置响应值
		rsp.DetectTaskId = 123
		rsp.GroupId = 456
	}
	return args.Error(0)
}

// Test parsePayloadForCreateDetectTask
func TestParsePayloadForCreateDetectTask(t *testing.T) {
	tests := []struct {
		name       string
		payload    string
		expectErr  bool
		expectedId uint64
	}{
		{
			name:       "有效的payload",
			payload:    `{"user_id": 123}`,
			expectErr:  false,
			expectedId: 123,
		},
		{
			name:      "无效的JSON",
			payload:   `{"user_id": invalid}`,
			expectErr: true,
		},
		{
			name:      "缺少user_id",
			payload:   `{}`,
			expectErr: true,
		},
		{
			name:      "user_id为0",
			payload:   `{"user_id": 0}`,
			expectErr: true,
		},
		{
			name:      "空payload",
			payload:   ``,
			expectErr: true,
		},
		{
			name:      "user_id为字符串数字",
			payload:   `{"user_id": "123"}`,
			expectErr: true, // JSON解析会失败，因为类型不匹配
		},
		{
			name:      "负数user_id",
			payload:   `{"user_id": -1}`,
			expectErr: true, // 会被转换为大整数，但不符合业务逻辑
		},
		{
			name:       "最大uint64",
			payload:    `{"user_id": 18446744073709551615}`,
			expectErr:  false,
			expectedId: 18446744073709551615,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parsePayloadForCreateDetectTask([]byte(tt.payload))

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedId, result.UserId)
			}
		})
	}
}

// Test isNeedCreateDetectTask
func TestIsNeedCreateDetectTask(t *testing.T) {
	setupCreateDetectTaskTestEnvironment()

	tests := []struct {
		name         string
		userId       uint64
		mockSetup    func(mock sqlmock.Sqlmock)
		expectErr    bool
		expectedUser bool
	}{
		{
			name:   "用户需要创建测绘任务",
			userId: 1,
			mockSetup: func(mock sqlmock.Sqlmock) {
				userRows := sqlmock.NewRows([]string{
					"id", "name", "company_id", "target_company_name",
					"percent", "need_asset_mapping", "other_mapping_companies", "detect_tasks_step",
				}).AddRow(
					1, "test_user", 100, "Test Company",
					60, 1, "Company1,Company2", user.DetectTasksStepInit,
				)
				mock.ExpectQuery("SELECT (.+) FROM `users`").
					WithArgs(1, user.UserStatusEnable, 1).
					WillReturnRows(userRows)
			},
			expectErr:    false,
			expectedUser: true,
		},
		{
			name:   "用户不需要创建测绘任务",
			userId: 2,
			mockSetup: func(mock sqlmock.Sqlmock) {
				userRows := sqlmock.NewRows([]string{
					"id", "name", "company_id", "target_company_name",
					"percent", "need_asset_mapping", "other_mapping_companies", "detect_tasks_step",
				}).AddRow(
					2, "test_user2", 200, "Test Company2",
					60, 0, "Company1,Company2", user.DetectTasksStepInit,
				)
				mock.ExpectQuery("SELECT (.+) FROM `users`").
					WithArgs(2, user.UserStatusEnable, 1).
					WillReturnRows(userRows)
			},
			expectErr:    false,
			expectedUser: false,
		},
		{
			name:   "用户不存在",
			userId: 999,
			mockSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT (.+) FROM `users`").
					WithArgs(999, user.UserStatusEnable, 1).
					WillReturnError(gorm.ErrRecordNotFound)
			},
			expectErr:    true,
			expectedUser: false,
		},
		{
			name:   "目标企业名称为空",
			userId: 3,
			mockSetup: func(mock sqlmock.Sqlmock) {
				userRows := sqlmock.NewRows([]string{
					"id", "name", "company_id", "target_company_name",
					"percent", "need_asset_mapping", "other_mapping_companies", "detect_tasks_step",
				}).AddRow(
					3, "test_user3", 300, "",
					60, 1, "Company1,Company2", user.DetectTasksStepInit,
				)
				mock.ExpectQuery("SELECT (.+) FROM `users`").
					WithArgs(3, user.UserStatusEnable, 1).
					WillReturnRows(userRows)
			},
			expectErr:    true,
			expectedUser: false,
		},
		{
			name:   "数据库查询错误",
			userId: 4,
			mockSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery("SELECT (.+) FROM `users`").
					WithArgs(4, user.UserStatusEnable, 1).
					WillReturnError(errors.New("database connection error"))
			},
			expectErr:    true,
			expectedUser: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()

			tt.mockSetup(mysqlMock)

			userInfo, needCreate, err := isNeedCreateDetectTask(tt.userId)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedUser, needCreate)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedUser, needCreate)
				if needCreate {
					assert.NotNil(t, userInfo)
					assert.Equal(t, tt.userId, userInfo.Id)
				}
			}

			assert.NoError(t, mysqlMock.ExpectationsWereMet())
		})
	}
}

// Test buildDetectTaskParam
func TestBuildDetectTaskParam(t *testing.T) {
	tests := []struct {
		name     string
		userInfo *user.User
		validate func(*testing.T, *pb.DetectAssetTaskCreateRequest)
	}{
		{
			name: "正常用户信息",
			userInfo: &user.User{
				Id:                    1,
				CompanyId:             &sql.NullInt64{Int64: 100, Valid: true},
				TargetCompanyName:     "Test Company",
				Percent:               60,
				OtherMappingCompanies: "Company1,Company2,Company3",
			},
			validate: func(t *testing.T, param *pb.DetectAssetTaskCreateRequest) {
				assert.Equal(t, int(detect_assets_tasks.ModeAuto), int(param.DetectMode))
				assert.Equal(t, uint64(1), param.UserId)
				assert.Equal(t, uint64(1), param.SafeUserId)
				assert.Equal(t, uint64(100), param.CompanyId)
				assert.Equal(t, "Test Company", param.Name)
				assert.Equal(t, uint32(60), param.Percent)
				assert.Equal(t, []string{"Test Company"}, param.CompanyList)
				assert.Equal(t, []string{"Company1", "Company2", "Company3"}, param.OtherCompanyList)
				assert.Equal(t, uint64(0), param.IsNeedHunter)
				assert.Equal(t, uint64(0), param.IsNeedDnschecker)
				assert.Equal(t, uint64(0), param.IsAutoExpendIp)
				assert.Equal(t, "1", param.NoNeedControyCompany)
				assert.Equal(t, uint32(1000), param.Bandwidth)
			},
		},
		{
			name: "CompanyId为空",
			userInfo: &user.User{
				Id:                    2,
				CompanyId:             nil,
				TargetCompanyName:     "Test Company 2",
				Percent:               0,
				OtherMappingCompanies: "",
			},
			validate: func(t *testing.T, param *pb.DetectAssetTaskCreateRequest) {
				assert.Equal(t, uint64(0), param.CompanyId)
				assert.Equal(t, "", param.NoNeedControyCompany)
				assert.Equal(t, []string{""}, param.OtherCompanyList)
			},
		},
		{
			name: "CompanyId无效",
			userInfo: &user.User{
				Id:                    3,
				CompanyId:             &sql.NullInt64{Int64: 0, Valid: false},
				TargetCompanyName:     "Test Company 3",
				Percent:               100,
				OtherMappingCompanies: "SingleCompany",
			},
			validate: func(t *testing.T, param *pb.DetectAssetTaskCreateRequest) {
				assert.Equal(t, uint64(0), param.CompanyId)
				assert.Equal(t, "1", param.NoNeedControyCompany)
				assert.Equal(t, []string{"SingleCompany"}, param.OtherCompanyList)
			},
		},
		{
			name: "最小边界情况",
			userInfo: &user.User{
				Id:                    0,
				CompanyId:             &sql.NullInt64{Int64: -1, Valid: true},
				TargetCompanyName:     "",
				Percent:               -10,
				OtherMappingCompanies: ",,,",
			},
			validate: func(t *testing.T, param *pb.DetectAssetTaskCreateRequest) {
				assert.Equal(t, uint64(0), param.UserId)
				assert.Equal(t, uint64(18446744073709551615), param.CompanyId) // -1转为uint64
				assert.Equal(t, "", param.Name)
				assert.Equal(t, uint32(4294967286), param.Percent) // -10转为uint32
				assert.Equal(t, []string{""}, param.CompanyList)
				assert.Equal(t, []string{"", "", "", ""}, param.OtherCompanyList)
				assert.Equal(t, "", param.NoNeedControyCompany)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			param := buildDetectTaskParam(tt.userInfo)

			assert.NotNil(t, param)
			tt.validate(t, param)
		})
	}
}

// Test updateUserInfo
func TestUpdateUserInfo(t *testing.T) {
	setupCreateDetectTaskTestEnvironment()

	tests := []struct {
		name       string
		userInfo   *user.User
		updateData map[string]interface{}
		mockSetup  func(mock sqlmock.Sqlmock)
		expectErr  bool
	}{
		{
			name:     "更新成功",
			userInfo: createTestCreateDetectTaskUser(),
			updateData: map[string]interface{}{
				"first_detect_task_id": uint64(123),
			},
			mockSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE `users`").
					WithArgs(123, 1).
					WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()
			},
			expectErr: false,
		},
		{
			name:     "更新失败",
			userInfo: createTestCreateDetectTaskUser(),
			updateData: map[string]interface{}{
				"detect_tasks_step": user.DetectTasksStepDoing,
			},
			mockSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE `users`").
					WithArgs(user.DetectTasksStepDoing, 1).
					WillReturnError(errors.New("update failed"))
				mock.ExpectRollback()
			},
			expectErr: true,
		},
		{
			name:       "空更新数据",
			userInfo:   createTestCreateDetectTaskUser(),
			updateData: map[string]interface{}{},
			mockSetup: func(mock sqlmock.Sqlmock) {
				// 空更新数据时，gorm不会执行UPDATE SQL，只会开始并提交事务
				mock.ExpectBegin()
				mock.ExpectCommit()
			},
			expectErr: false,
		},
		{
			name: "nil用户信息",
			userInfo: &user.User{
				Id: 0, // 无效ID
			},
			updateData: map[string]interface{}{
				"name": "updated_name",
			},
			mockSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE `users`").
					WithArgs("updated_name", 0).
					WillReturnError(errors.New("user not found"))
				mock.ExpectRollback()
			},
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mysql.ResetMockInstance()
			mysqlMock := mysql.GetMockInstance()

			tt.mockSetup(mysqlMock)

			err := updateUserInfo(tt.userInfo, tt.updateData)

			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.NoError(t, mysqlMock.ExpectationsWereMet())
		})
	}
}

// Test dispatchCluesJob - 简化测试，主要测试参数构建逻辑
func TestDispatchCluesJob(t *testing.T) {
	setupCreateDetectTaskTestEnvironment()

	tests := []struct {
		name         string
		userInfo     *user.User
		detectTaskId uint64
		groupId      uint64
		expectErr    bool
		checkPayload func(*testing.T, *user.User, uint64, uint64)
	}{
		{
			name:         "正常用户信息",
			userInfo:     createTestCreateDetectTaskUser(),
			detectTaskId: 123,
			groupId:      456,
			expectErr:    false,
			checkPayload: func(t *testing.T, userInfo *user.User, detectTaskId, groupId uint64) {
				assert.Equal(t, "Test Company", userInfo.TargetCompanyName)
				assert.NotNil(t, userInfo.CompanyId)
				assert.True(t, userInfo.CompanyId.Valid)
				assert.Equal(t, int64(100), userInfo.CompanyId.Int64)
			},
		},
		{
			name: "用户无CompanyId",
			userInfo: &user.User{
				Id:                1,
				CompanyId:         nil,
				TargetCompanyName: "Test Company",
			},
			detectTaskId: 123,
			groupId:      456,
			expectErr:    false,
			checkPayload: func(t *testing.T, userInfo *user.User, detectTaskId, groupId uint64) {
				assert.Nil(t, userInfo.CompanyId)
			},
		},
		{
			name: "用户CompanyId无效",
			userInfo: &user.User{
				Id:                1,
				CompanyId:         &sql.NullInt64{Int64: 100, Valid: false},
				TargetCompanyName: "Test Company",
			},
			detectTaskId: 123,
			groupId:      456,
			expectErr:    false,
			checkPayload: func(t *testing.T, userInfo *user.User, detectTaskId, groupId uint64) {
				assert.False(t, userInfo.CompanyId.Valid)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 验证参数构建逻辑（不实际发送到队列）
			tt.checkPayload(t, tt.userInfo, tt.detectTaskId, tt.groupId)

			// 测试函数调用（可能会因为Redis连接失败而返回错误，这是正常的）
			err := dispatchCluesJob(context.Background(), tt.userInfo, tt.detectTaskId, tt.groupId)

			// 在测试环境中，由于Redis可能不可用，我们不强制要求成功
			// 主要确保函数不会panic
			t.Logf("dispatchCluesJob result: %v", err)
		})
	}
}

// Test createDetectTask
func TestCreateDetectTask(t *testing.T) {
	tests := []struct {
		name             string
		userInfo         *user.User
		mockHandlerSetup func(*MockDetectAssetTaskHandler)
		expectErr        bool
		expectedTaskId   uint64
		expectedGroupId  uint64
	}{
		{
			name:     "创建任务成功",
			userInfo: createTestCreateDetectTaskUser(),
			mockHandlerSetup: func(m *MockDetectAssetTaskHandler) {
				m.On("CreateTask", mock.Anything, mock.Anything, mock.Anything).
					Return(nil).
					Run(func(args mock.Arguments) {
						// 设置响应
						rsp := args.Get(2).(*pb.DetectAssetTaskCreateResponse)
						rsp.DetectTaskId = 123
						rsp.GroupId = 456
					})
			},
			expectErr:       false,
			expectedTaskId:  123,
			expectedGroupId: 456,
		},
		{
			name:     "创建任务失败",
			userInfo: createTestCreateDetectTaskUser(),
			mockHandlerSetup: func(m *MockDetectAssetTaskHandler) {
				m.On("CreateTask", mock.Anything, mock.Anything, mock.Anything).
					Return(errors.New("create task failed"))
			},
			expectErr:       true,
			expectedTaskId:  0,
			expectedGroupId: 0,
		},
		{
			name: "用户信息不完整",
			userInfo: &user.User{
				Id:                1,
				TargetCompanyName: "",
			},
			mockHandlerSetup: func(m *MockDetectAssetTaskHandler) {
				m.On("CreateTask", mock.Anything, mock.Anything, mock.Anything).
					Return(nil).
					Run(func(args mock.Arguments) {
						rsp := args.Get(2).(*pb.DetectAssetTaskCreateResponse)
						rsp.DetectTaskId = 123
						rsp.GroupId = 456
					})
			},
			expectErr:       false,
			expectedTaskId:  123,
			expectedGroupId: 456,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 注意：这里没有直接测试createDetectTask函数，因为它依赖外部服务
			// 实际测试时需要依赖注入或接口抽象来mock detect_asset_task.CreateTask
			// 这里仅作为示例展示测试思路

			mockHandler := &MockDetectAssetTaskHandler{}
			tt.mockHandlerSetup(mockHandler)

			// 模拟createDetectTask的行为
			param := buildDetectTaskParam(tt.userInfo)
			rsp := &pb.DetectAssetTaskCreateResponse{}
			err := mockHandler.CreateTask(context.Background(), param, rsp)

			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedTaskId, rsp.DetectTaskId)
				assert.Equal(t, tt.expectedGroupId, rsp.GroupId)
			}

			mockHandler.AssertExpectations(t)
		})
	}
}

// 边界情况和错误处理的额外测试

// TestParsePayloadForCreateDetectTaskEdgeCases 测试解析payload的边界情况
func TestParsePayloadForCreateDetectTaskEdgeCases(t *testing.T) {
	tests := []struct {
		name      string
		payload   []byte
		expectErr bool
	}{
		{
			name:      "nil payload",
			payload:   nil,
			expectErr: true,
		},
		{
			name:      "大payload",
			payload:   []byte(`{"user_id": 123, "extra_data": "` + strings.Repeat("a", 10000) + `"}`),
			expectErr: false,
		},
		{
			name:      "包含特殊字符",
			payload:   []byte(`{"user_id": 123, "note": "测试\n\t\r"}`),
			expectErr: false,
		},
		{
			name:      "嵌套JSON",
			payload:   []byte(`{"user_id": 123, "meta": {"key": "value"}}`),
			expectErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parsePayloadForCreateDetectTask(tt.payload)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, uint64(123), result.UserId)
			}
		})
	}
}

// TestBuildDetectTaskParamEdgeCases 测试构建参数的边界情况
func TestBuildDetectTaskParamEdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		userInfo *user.User
	}{
		{
			name: "极长字符串",
			userInfo: &user.User{
				Id:                    1,
				TargetCompanyName:     string(make([]byte, 1000)),
				OtherMappingCompanies: string(make([]byte, 5000)),
				Percent:               999999,
			},
		},
		{
			name: "包含特殊字符",
			userInfo: &user.User{
				Id:                    1,
				TargetCompanyName:     "公司名称\n\t\r\"'",
				OtherMappingCompanies: "Company1\n,Company2\t,Company3\r",
			},
		},
		{
			name: "极值测试",
			userInfo: &user.User{
				Id:      ^uint64(0), // 最大uint64
				Percent: ^int(0),    // 最大int
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试函数不会panic
			assert.NotPanics(t, func() {
				param := buildDetectTaskParam(tt.userInfo)
				assert.NotNil(t, param)
			})
		})
	}
}

// TestIsNeedCreateDetectTaskConcurrency 测试并发安全性
func TestIsNeedCreateDetectTaskConcurrency(t *testing.T) {
	setupCreateDetectTaskTestEnvironment()

	t.Run("并发调用", func(t *testing.T) {
		mysql.ResetMockInstance()
		mysqlMock := mysql.GetMockInstance()

		// 设置多次相同的mock期望
		for i := 0; i < 10; i++ {
			userRows := sqlmock.NewRows([]string{
				"id", "name", "company_id", "target_company_name",
				"percent", "need_asset_mapping", "other_mapping_companies", "detect_tasks_step",
			}).AddRow(
				1, "test_user", 100, "Test Company",
				60, 1, "Company1,Company2", user.DetectTasksStepInit,
			)
			mysqlMock.ExpectQuery("SELECT (.+) FROM `users`").
				WithArgs(1, user.UserStatusEnable, 1).
				WillReturnRows(userRows)
		}

		// 并发执行
		errChan := make(chan error, 10)
		for i := 0; i < 10; i++ {
			go func() {
				_, _, err := isNeedCreateDetectTask(1)
				errChan <- err
			}()
		}

		// 收集结果
		for i := 0; i < 10; i++ {
			err := <-errChan
			assert.NoError(t, err)
		}
	})
}

// TestUpdateUserInfoTransactionHandling 测试事务处理
func TestUpdateUserInfoTransactionHandling(t *testing.T) {
	setupCreateDetectTaskTestEnvironment()

	t.Run("事务回滚", func(t *testing.T) {
		mysql.ResetMockInstance()
		mysqlMock := mysql.GetMockInstance()

		mysqlMock.ExpectBegin()
		mysqlMock.ExpectExec("UPDATE `users`").
			WillReturnError(errors.New("constraint violation"))
		mysqlMock.ExpectRollback()

		userInfo := createTestCreateDetectTaskUser()
		updateData := map[string]interface{}{
			"invalid_field": "invalid_value",
		}

		err := updateUserInfo(userInfo, updateData)
		assert.Error(t, err)
		assert.NoError(t, mysqlMock.ExpectationsWereMet())
	})
}
