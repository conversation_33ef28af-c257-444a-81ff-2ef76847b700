package handlers

import (
	"strings"
	"testing"
)

func TestParsePayloadForDeleteTypeWebsiteMsg(t *testing.T) {
	tests := []struct {
		name        string
		payload     string
		wantPayload *DeleteTypeWebsiteMsgJobPayload
		wantErr     string
	}{
		{
			name:    "正常解析所有字段",
			payload: `{"user_id":1001,"type":2,"num":5,"app_type":3}`,
			wantPayload: &DeleteTypeWebsiteMsgJobPayload{
				UserId:  1001,
				Type:    2,
				Num:     5,
				AppType: 3,
			},
			wantErr: "",
		},
		{
			name:    "仅必填字段",
			payload: `{"user_id":1002,"type":3}`,
			wantPayload: &DeleteTypeWebsiteMsgJobPayload{
				UserId:  1002,
				Type:    3,
				Num:     0,
				AppType: 0,
			},
			wantErr: "",
		},
		{
			name:        "缺少user_id",
			payload:     `{"type":2,"num":5}`,
			wantPayload: nil,
			wantErr:     "用户ID不能为空",
		},
		{
			name:        "user_id为0",
			payload:     `{"user_id":0,"type":2}`,
			wantPayload: nil,
			wantErr:     "用户ID不能为空",
		},
		{
			name:        "缺少type",
			payload:     `{"user_id":1001,"num":5}`,
			wantPayload: nil,
			wantErr:     "资产类型不能为空",
		},
		{
			name:        "type为0",
			payload:     `{"user_id":1001,"type":0}`,
			wantPayload: nil,
			wantErr:     "资产类型不能为空",
		},
		{
			name:        "user_id和type都缺失",
			payload:     `{"num":5,"app_type":3}`,
			wantPayload: nil,
			wantErr:     "用户ID不能为空",
		},
		{
			name:        "无效类型",
			payload:     `{"user_id":{},"type":[]}`,
			wantPayload: nil,
			wantErr:     "json: cannot unmarshal object into Go struct field DeleteTypeWebsiteMsgJobPayload.user_id of type int64",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parsePayloadForDeleteTypeWebsiteMsg([]byte(tt.payload))

			// 错误处理检查
			if tt.wantErr != "" {
				if err == nil {
					t.Fatal("预期错误，但未返回错误")
				}
				if !strings.Contains(err.Error(), tt.wantErr) {
					t.Errorf("期望错误: %q, 实际错误: %q", tt.wantErr, err.Error())
				}
				return
			}

			if err != nil {
				t.Fatalf("预期无错误，但得到: %v", err)
			}

			// 结构体内容检查
			if got == nil {
				t.Fatal("返回的payloadInfo为nil")
			}

			if got.UserId != tt.wantPayload.UserId {
				t.Errorf("UserId 不匹配，期望: %d, 实际: %d", tt.wantPayload.UserId, got.UserId)
			}
			if got.Type != tt.wantPayload.Type {
				t.Errorf("Type 不匹配，期望: %d, 实际: %d", tt.wantPayload.Type, got.Type)
			}
			if got.Num != tt.wantPayload.Num {
				t.Errorf("Num 不匹配，期望: %d, 实际: %d", tt.wantPayload.Num, got.Num)
			}
			if got.AppType != tt.wantPayload.AppType {
				t.Errorf("AppType 不匹配，期望: %d, 实际: %d", tt.wantPayload.AppType, got.AppType)
			}
		})
	}
}
