package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	redisInit "micro-service/initialize/redis"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clue_black_keyword"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/domain_assets"
	"micro-service/pkg/cfg"
	clueUtil "micro-service/pkg/clues"
	"micro-service/pkg/icp"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"

	asyncq "micro-service/pkg/queue_helper"
	"micro-service/webService/handler/detect_assets"
	pb "micro-service/webService/proto"

	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"gorm.io/gorm"
)

const (
	StepStatusDone = 1
)

// DetectGolangCluesJobPayload 已在 types.go 中定义

func DetectGolangCluesEventHandler(ctx context.Context, task *asyncq.Task) error {
	log.Info("DetectGolangCluesJob", "开始执行-1", map[string]interface{}{
		"task_type": task.Type,
		"payload":   string(task.Payload),
	})
	payload := &asyncq.DetectGolangCluesJobPayload{}
	if err := json.Unmarshal([]byte(task.Payload), payload); err != nil {
		log.Error("DetectGolangCluesJob", "解析任务数据失败", err)
		return err
	}

	log.Info("DetectGolangCluesJob", "开始执行-2", map[string]interface{}{
		"clueCompanyArray": payload.ClueCompanyArray,
		"user_id":          payload.UserId,
		"task_id":          payload.TaskId,
		"preIds":           payload.PreIds,
	})

	// 获取任务信息
	taskModel := detect_assets_tasks.NewModel()
	taskInfo, err := taskModel.First(detect_assets_tasks.WithID(payload.TaskId))
	if err != nil {
		log.Error("DetectGolangCluesJob", "获取任务信息失败", err)
		return err
	}

	if taskInfo == nil || taskInfo.ID == 0 {
		log.Warn("DetectGolangCluesJob", "任务不存在", map[string]interface{}{
			"task_id": payload.TaskId,
		})
		// 发送进度
		err := sendProgressForDetectCluesJob(ctx, payload.UserId, nil, 0, 0, 0, 100, "")
		if err != nil {
			log.Error("DetectGolangCluesJob", "发送进度失败", err)
		}
		return nil
	}

	originCompanyName := taskInfo.Name
	log.Info("资产测绘调研表线索", "测绘的主企业名称:"+originCompanyName)

	// 初始化ICP查询实例
	redisClient := redisInit.GetInstance(cfg.LoadRedis())
	if redisClient == nil {
		log.Error("DetectGolangCluesJob", "获取Redis实例失败", nil)
		return fmt.Errorf("获取Redis实例失败")
	}
	icpQuery := icp.NewICPQuery(redisClient)

	// 处理预处理的线索
	newClueCompanyName := make([]string, 0)
	clueModel := clues.NewCluer()

	for _, clueId := range payload.PreIds {
		clue, err := clueModel.First(WithID(clueId))
		if err != nil {
			log.Error("DetectGolangCluesJob", "获取线索信息失败", err, map[string]interface{}{
				"clue_id": clueId,
			})
			continue
		}
		if clue.Id > 0 {
			// 获取额外企业名称
			if clue.Type == clues.TYPE_DOMAIN && clue.ClueCompanyName == "" {
				// 通过域名查询企业名称
				result, err := icpQuery.QueryDomain(ctx, clue.Content, true, false, false, payload.UserId)
				if err != nil {
					log.Error("DetectGolangCluesJob", "域名查询失败", err, map[string]interface{}{
						"domain": clue.Content,
					})
					continue
				}
				if result != nil && result.Info != nil {
					if companyName, ok := result.Info["company_name"].(string); ok && companyName != "" {
						clue.ClueCompanyName = companyName
					} else {
						log.Warn("DetectGolangCluesJob", "域名查询结果中未找到企业名称", map[string]interface{}{
							"domain": clue.Content,
							"result": result.Info,
						})
					}
				} else {
					log.Warn("DetectGolangCluesJob", "域名查询结果为空", map[string]interface{}{
						"domain": clue.Content,
					})
				}
			}
			if clue.Type == clues.TYPE_ICP && clue.ClueCompanyName == "" {
				// 通过ICP查询企业名称
				result, err := icpQuery.QueryICP(ctx, clue.Content, true, false, payload.UserId)
				if err != nil {
					log.Error("DetectGolangCluesJob", "ICP查询失败", err, map[string]interface{}{
						"icp": clue.Content,
					})
					continue
				}
				if result != nil && result.Info != nil {
					if companyName, ok := result.Info["company_name"].(string); ok && companyName != "" {
						clue.ClueCompanyName = companyName
					} else {
						log.Warn("DetectGolangCluesJob", "ICP查询结果中未找到企业名称", map[string]interface{}{
							"icp":    clue.Content,
							"result": result.Info,
						})
					}
				} else {
					log.Warn("DetectGolangCluesJob", "ICP查询结果为空", map[string]interface{}{
						"icp": clue.Content,
					})
				}
			}
			if clue.Type == clues.TYPE_CERT {
				o, cn := pluckCert(clue.Content, false)

				if clue.ClueCompanyName == "" || !utils.CheckIsChinese(o) || o == "" {
					if cn != "" {
						// 通过域名查询企业名称
						result, err := icpQuery.QueryDomain(ctx, clueUtil.GetTopDomain(cn), false, false, false, payload.UserId)
						if err != nil {
							log.Error("DetectGolangCluesJob", "证书域名查询失败", err, map[string]interface{}{
								"domain": cn,
							})
							continue
						}

						if result != nil && result.Info != nil {
							if companyName, ok := result.Info["company_name"].(string); ok && companyName != "" {
								clue.ClueCompanyName = companyName
							} else {
								clue.ClueCompanyName = o
							}
						} else {
							clue.ClueCompanyName = o
						}

						log.Info("DetectGolangCluesJob", "补充证书企业名称，取的cn域名对应的企业名称", map[string]interface{}{
							"cn":           cn,
							"company_name": clue.ClueCompanyName,
							"cert_content": clue.Content,
						})
					} else {
						log.Info("DetectGolangCluesJob", "补充证书企业名称-cn不存在，取默认测绘主体企业名称", map[string]interface{}{
							"cert_content": clue.Content,
							"error":        "CN为空!",
						})
					}
				} else {
					if o != "" {
						clue.ClueCompanyName = o
						log.Info("DetectGolangCluesJob", "补充证书企业名称-取的o里面的企业名称", map[string]interface{}{
							"cert_content": clue.Content,
							"company_name": clue.ClueCompanyName,
							"is_chinese":   utils.CheckIsChinese(o),
						})
					} else {
						log.Info("DetectGolangCluesJob", "补充证书企业名称-o里面没值，取测绘主体的企业名称", map[string]interface{}{
							"cert_content": clue.Content,
							"company_name": clue.ClueCompanyName,
							"is_chinese":   utils.CheckIsChinese(o),
						})
					}
				}
			}
			// 如果获取不到名称，使用任务名称
			if clue.ClueCompanyName == "" {
				log.Info("DetectGolangCluesJob", "使用任务名称作为企业名称", map[string]interface{}{
					"task_name": taskInfo.Name,
				})
				clue.ClueCompanyName = taskInfo.Name
			}
			newClueCompanyName = append(newClueCompanyName, clue.ClueCompanyName)
			clue.Status = clues.CLUE_PASS_STATUS
			if err := clueModel.Update(clue); err != nil {
				log.Error("DetectGolangCluesJob", "更新线索信息失败", err, map[string]interface{}{
					"clue_id": clue.Id,
				})
				continue
			}
		}
	}

	// 合并企业名称列表
	deteCompanyName := make([]string, 0)
	if len(newClueCompanyName) > 0 {
		// 去重合并
		companyMap := make(map[string]struct{})
		for _, name := range append(payload.ClueCompanyArray, newClueCompanyName...) {
			if _, ok := companyMap[name]; !ok {
				companyMap[name] = struct{}{}
				deteCompanyName = append(deteCompanyName, name)
			}
		}
	} else {
		deteCompanyName = payload.ClueCompanyArray
	}
	// 3. 调用企业线索扩展
	force := payload.IsForce
	// 计算总数
	totalCount := uint64(len(payload.ClueCompanyArray))
	// 完成计数
	doneCount := atomic.Uint64{}

	// 发送进度
	err = sendProgressForDetectCluesJob(ctx, payload.UserId, taskInfo, 0, totalCount, 0, 0, "")
	if err != nil {
		log.Error("DetectGolangCluesJob", "发送进度失败", err)
	}

	failedCompanyArr := make([]string, 0)
	failedCompanyChan := make(chan string, len(payload.ClueCompanyArray))
	wg := sync.WaitGroup{}
	semChan := make(chan struct{}, 10) // 限制并发数,最大并发数为10
	// 处理每个企业
	for i, companyName := range payload.ClueCompanyArray {
		wg.Add(1)
		go func(i int, companyName string) {
			defer func() {
				<-semChan
				wg.Done()
			}()
			semChan <- struct{}{} // 限制并发数

			log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 开始并发处理企业", map[string]interface{}{
				"index":        i,
				"company_name": companyName,
				"user_id":      payload.UserId,
				"group_id":     payload.GroupId,
			})
			// 1. 调用微信数据获取任务
			wechatResult, err := icp.SearchWechatTask(ctx, []string{companyName}, false, payload.UserId)
			if err != nil {
				log.Error("DetectGolangCluesJob", "微信公众号数据获取任务失败", err, map[string]interface{}{
					"company_name": companyName,
				})
				failedCompanyChan <- companyName
				return
			}
			if wechatResult == nil || wechatResult.Code != 0 {
				log.Warn("DetectGolangCluesJob", "微信公众号数据获取任务返回异常", map[string]interface{}{
					"company_name": companyName,
					"result":       wechatResult,
				})
			}

			// 2. 调用微信小程序数据获取任务
			miniappResult, err := icp.SearchMiniappTask(ctx, []string{companyName}, false, payload.UserId)
			if err != nil {
				log.Error("DetectGolangCluesJob", "微信小程序数据获取任务失败", err, map[string]interface{}{
					"company_name": companyName,
				})
				failedCompanyChan <- companyName
				return
			}
			if miniappResult == nil || miniappResult.Code != 0 {
				log.Warn("DetectGolangCluesJob", "微信小程序数据获取任务返回异常", map[string]interface{}{
					"company_name": companyName,
					"result":       miniappResult,
				})
			}

			// 尝试获取企业线索扩展任务（最多重试一次）
			var clueResult *icp.CompanyClueResponse
			var taskId string
			for attempt := 0; attempt < 2; attempt++ {
				// 重试间隔
				if attempt > 0 {
					time.Sleep(10 * time.Second)
				}

				// 尝试获取企业线索扩展结果
				clueResult, err = icp.ExpandCompanyClue(ctx, companyName, force, payload.UserId)
				if err != nil {
					if attempt == 0 {
						log.Error("DetectGolangCluesJob", "企业线索扩展失败", err, map[string]interface{}{
							"company_name": companyName,
						})
					} else {
						log.Error("DetectGolangCluesJob", "企业线索扩展重试失败", err, map[string]interface{}{
							"company_name": companyName,
						})
					}

					if attempt == 1 { // 最后一次尝试失败
						failedCompanyChan <- companyName
						return
					}
					continue
				}

				taskId = clueResult.TaskID
				if taskId != "" {
					break // 成功获取到任务ID，跳出循环
				}
			}

			// 两次尝试都没有获取到有效的任务ID
			if taskId == "" {
				log.Error("DetectGolangCluesJob", "企业线索扩展任务ID为空", nil, map[string]interface{}{
					"company_name": companyName,
					"task_id":      payload.TaskId,
				})
				return
			}

			log.Info("DetectGolangCluesJob", "下发任务成功", map[string]interface{}{
				"company_name": companyName,
				"task_id":      taskId,
			})

			// 等待结果
			result, err := icp.WaitForExpandClueResult(ctx, taskId, 10*time.Minute, 5*time.Second)
			if err != nil {
				log.Error("DetectGolangCluesJob", "等待企业线索扩展结果失败", err, map[string]interface{}{
					"company_name": companyName,
					"task_id":      taskId,
				})
				return
			}

			if result != nil && result.Process == 100 {
				log.Info("DetectGolangCluesJob", "获取线索成功", map[string]interface{}{
					"company_name": companyName,
					"task_id":      taskId,
					"items":        result.Items,
				})

				// 处理返回的线索数据
				log.Info("DetectGolangCluesJob", "开始处理线索数据", map[string]interface{}{
					"company_name":        companyName,
					"origin_company_name": originCompanyName,
					"task_id":             taskId,
					"items_count":         len(result.Items),
					"user_id":             payload.UserId,
					"group_id":            payload.GroupId,
				})

				if err := handleClueItems(ctx, result.Items, companyName, originCompanyName, payload.UserId, payload.GroupId, payload.CompanyId, payload.SceneIds); err != nil {
					log.Error("DetectGolangCluesJob", "处理线索数据失败", err, map[string]interface{}{
						"company_name":        companyName,
						"origin_company_name": originCompanyName,
						"task_id":             taskId,
					})
				} else {
					log.Info("DetectGolangCluesJob", "线索数据处理完成", map[string]interface{}{
						"company_name":        companyName,
						"origin_company_name": originCompanyName,
						"task_id":             taskId,
					})
				}
			} else {
				log.Error("DetectGolangCluesJob", "获取线索失败", nil, map[string]interface{}{
					"company_name": companyName,
					"task_id":      taskId,
				})
			}

			log.Info("处理企业", companyName)

			// 发送进度
			doneCount.Add(1)
			err = sendProgressForDetectCluesJob(ctx, payload.UserId, taskInfo, doneCount.Load(), totalCount, 0, 0, companyName)
			if err != nil {
				log.Error("DetectGolangCluesJob", "发送进度失败", err)
			}
		}(i, companyName)
	}
	wg.Wait()
	// 关闭通道
	close(failedCompanyChan)
	// 遍历通道，获取所有失败的企业名称
	for companyName := range failedCompanyChan {
		failedCompanyArr = append(failedCompanyArr, companyName)
	}
	// 更新任务状态
	updateData := map[string]interface{}{
		"step_status": StepStatusDone,
	}

	if originCompanyName != "" {
		updateData["name"] = originCompanyName
	}

	if len(deteCompanyName) > 0 {
		companyJson, err := json.Marshal(deteCompanyName)
		if err != nil {
			log.Error("DetectGolangCluesJob", "序列化企业名称列表失败", err)
			return err
		}
		updateData["confirm_company_list"] = string(companyJson)
	}

	if len(failedCompanyArr) > 0 {
		failedJson, err := json.Marshal(failedCompanyArr)
		if err != nil {
			log.Error("DetectGolangCluesJob", "序列化失败企业列表失败", err)
			return err
		}
		updateData["failed_company_json"] = string(failedJson)
	}

	_, err = mysql.NewDSL[detect_assets_tasks.DetectAssetsTask]().UpdateByID(
		payload.TaskId,
		updateData,
	)
	if err != nil {
		log.Error("DetectGolangCluesJob", "更新任务状态失败", err)
		return err
	}

	// 同步域名资产 - 统一调用PHP的域名同步job
	// 对应PHP: TableAssetsDoaminsSync::dispatch($this->userId, null, DomainAssets::ASSETS_RECOMMEND, $this->groupId,null,null,null,$deteTaskInfo->id);
	log.Infof("DetectGolangCluesJob: 准备下发PHP域名同步任务 - user_id=%d, group_id=%d, task_id=%d",
		payload.UserId, payload.GroupId, payload.TaskId)

	err = asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
		payload.UserId,                 // user_id ($this->userId)
		nil,                            // task_id (null)
		domain_assets.ASSETS_RECOMMEND, // from (DomainAssets::ASSETS_RECOMMEND = 2)
		payload.GroupId,                // groupId ($this->groupId)
		nil,                            // domain_task_id (null)
		nil,                            // import_domains (null)
		nil,                            // flag (null)
		payload.TaskId,                 // detect_task_id ($deteTaskInfo->id)
		nil,                            // organization_discover_task_id (null)
	)
	if err != nil {
		log.Errorf("DetectGolangCluesJob: 下发PHP域名同步任务失败 - user_id=%d, task_id=%d, error=%v",
			payload.UserId, payload.TaskId, err)
	} else {
		log.Infof("DetectGolangCluesJob: 下发PHP域名同步任务成功 - user_id=%d, task_id=%d, company_count=%d",
			payload.UserId, payload.TaskId, len(payload.ClueCompanyArray))
	}

	if taskInfo.DetectMode == detect_assets_tasks.ModeAuto {
		// 自动模式下，自动下发线索扩展任务
		param := &pb.ExpandCluesRequest{
			UserId:           payload.UserId,
			DetectTaskId:     payload.TaskId,
			OperateCompanyId: payload.CompanyId,
			DetectType:       int32(taskInfo.DetectType),
			ClientIp:         "",
		}
		response := &pb.ExpandCluesResponse{}
		err := detect_assets.ExpandClues(ctx, param, response)
		if err != nil {
			log.Error("DetectGolangCluesJob", "下发线索扩展任务失败", err)
		}
		log.Info("DetectGolangCluesJob", "下发线索扩展任务成功", map[string]interface{}{
			"param":    param,
			"response": response,
		})
	}

	// 	发送完成状态
	err = sendProgressForDetectCluesJob(ctx, payload.UserId, taskInfo, 0, 0, detect_assets_tasks.StatusFinished, 100, "")
	if err != nil {
		log.Error("DetectGolangCluesJob", "发送完成状态失败", err)
	}

	log.Info("资产测绘企业备案信息查询", "备案查询完成:"+string(mustMarshal(payload.ClueCompanyArray)))
	return nil
}

func mustMarshal(v interface{}) []byte {
	b, _ := json.Marshal(v)
	return b
}

// WithID 添加ID查询条件
func WithID(id uint64) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where("id = ?", id)
	}
}

var sceneIsLock sync.Mutex

// handleClueItems 处理线索数据
func handleClueItems(ctx context.Context, items []interface{}, companyName string, originCompanyName string, userId uint64, groupId uint64, companyId uint64, sceneIds []uint64) error {
	allIconClue := make([]map[string]interface{}, 0)

	// 在函数开始就定义 finalCompanyName，默认使用传入的 companyName
	finalCompanyName := companyName

	log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 开始处理线索数据", map[string]interface{}{
		"items_count":  len(items),
		"company_name": companyName,
		"user_id":      userId,
		"group_id":     groupId,
	})

	for _, item := range items {
		// 打印原始数据类型
		log.Info("DetectGolangCluesJob", "原始数据类型", map[string]interface{}{
			"type":  fmt.Sprintf("%T", item),
			"value": item,
		})

		// 尝试将item转换为map[string]interface{}
		var itemMap map[string]interface{}
		switch v := item.(type) {
		case map[string]interface{}:
			itemMap = v
		case string:
			// 如果是字符串，尝试解析JSON
			if err := json.Unmarshal([]byte(v), &itemMap); err != nil {
				log.Warn("DetectGolangCluesJob", "JSON解析失败", map[string]interface{}{
					"item":  v,
					"error": err.Error(),
				})
				continue
			}
		default:
			// 尝试将其他类型转换为JSON字符串再解析
			jsonBytes, err := json.Marshal(v)
			if err != nil {
				log.Warn("DetectGolangCluesJob", "JSON序列化失败", map[string]interface{}{
					"item":  v,
					"error": err.Error(),
				})
				continue
			}
			if err := json.Unmarshal(jsonBytes, &itemMap); err != nil {
				log.Warn("DetectGolangCluesJob", "JSON解析失败", map[string]interface{}{
					"item":  string(jsonBytes),
					"error": err.Error(),
				})
				continue
			}
		}

		if itemMap == nil {
			log.Warn("DetectGolangCluesJob", "线索数据为空", map[string]interface{}{
				"item": item,
			})
			continue
		}

		log.Info("DetectGolangCluesJob", "处理单条线索", map[string]interface{}{
			"item": itemMap,
		})

		// 检查企业名称是否变化
		itemCompanyName, _ := itemMap["company_name"].(string)
		content, _ := itemMap["content"].(string)

		log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 企业名称检查", map[string]interface{}{
			"content":             content,
			"origin_company_name": originCompanyName,
			"param_company_name":  companyName,
			"item_company_name":   itemCompanyName,
			"names_equal":         originCompanyName == companyName,
			"item_name_not_empty": itemCompanyName != "",
			"names_different":     originCompanyName != itemCompanyName,
		})

		if originCompanyName == companyName {
			if itemCompanyName != "" && originCompanyName != itemCompanyName && utils.StringsEqualFold(originCompanyName, itemCompanyName) {
				log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 企业名称发生变化", map[string]interface{}{
					"content":             content,
					"origin_company_name": originCompanyName,
					"new_company_name":    itemCompanyName,
				})
				originCompanyName = itemCompanyName
			}
		}

		// 更新最终使用的企业名称
		if itemCompanyName != "" {
			finalCompanyName = itemCompanyName
		}

		log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 最终企业名称确定", map[string]interface{}{
			"content":            content,
			"final_company_name": finalCompanyName,
			"item_company_name":  itemCompanyName,
			"param_company_name": companyName,
		})

		// 获取线索类型
		var typeValue float64
		switch v := itemMap["type"].(type) {
		case float64:
			typeValue = v
		case int:
			typeValue = float64(v)
		case string:
			if f, err := strconv.ParseFloat(v, 64); err == nil {
				typeValue = f
			} else {
				log.Warn("DetectGolangCluesJob", "类型转换失败", map[string]interface{}{
					"type": v,
				})
				continue
			}
		default:
			log.Warn("DetectGolangCluesJob", "未知的类型值", map[string]interface{}{
				"type": v,
			})
			continue
		}

		clueType := int(typeValue)

		log.Info("DetectGolangCluesJob", "线索类型转换", map[string]interface{}{
			"original_type":  typeValue,
			"converted_type": clueType,
		})

		// 转换类型
		switch clueType {
		case 5:
			clueType = clues.TYPE_DOMAIN
		case 7:
			clueType = clues.TYPE_SUBDOMAIN
		case 8:
			// 关联词类型线索转换
			clueType = clues.TYPE_KEYWORD
		}

		log.Info("DetectGolangCluesJob", "转换后的线索类型", map[string]interface{}{
			"final_type": clueType,
		})

		// 检查黑名单
		if isClueInBlackList(itemMap) {
			log.Info("DetectGolangCluesJob", "线索存在于黑名单", map[string]interface{}{
				"user_id": userId,
				"item":    itemMap,
			})
			continue
		}

		// content 已经在上面获取过了

		log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 准备创建线索", map[string]interface{}{
			"type":         clueType,
			"company_name": finalCompanyName,
			"content":      content,
			"user_id":      userId,
			"group_id":     groupId,
		})

		switch clueType {
		case clues.TYPE_DOMAIN:
			// 处理域名类型
			if content == "" {
				log.Warn("DetectGolangCluesJob", "域名内容为空", map[string]interface{}{
					"company": finalCompanyName,
				})
				continue
			}

			domain := utils.GetTopDomain(content)
			if domain == "" {
				log.Warn("DetectGolangCluesJob", "获取主域名为空", map[string]interface{}{
					"domain":  content,
					"company": finalCompanyName,
				})
				continue
			}

			log.Info("DetectGolangCluesJob", "处理域名线索", map[string]interface{}{
				"original_domain":    content,
				"top_domain":         domain,
				"final_company_name": finalCompanyName,
			})

			// 创建或更新线索
			clue, err := createOrUpdateClue(clues.TYPE_DOMAIN, domain, finalCompanyName, userId, groupId, companyId)
			if err != nil {
				log.Error("DetectGolangCluesJob", "更新域名线索失败", err, map[string]interface{}{
					"clue":         clue,
					"domain":       domain,
					"company_name": finalCompanyName,
				})
				continue
			}

			log.Info("DetectGolangCluesJob", "域名线索入库成功", map[string]interface{}{
				"clue_id":      clue.Id,
				"domain":       domain,
				"company_name": finalCompanyName,
			})

			// 移除已处理的场景ID
			if len(sceneIds) > 0 {
				sceneIsLock.Lock()
				for i, id := range sceneIds {
					if id == clue.Id {
						sceneIds = append(sceneIds[:i], sceneIds[i+1:]...)
						break
					}
				}
				sceneIsLock.Unlock()
			}

			// 查询OneForAll
			//err = asyncq.Enqueue(ctx, asyncq.OneForAllJob, &OneForAllJobPayload{
			//	Domain: domain,
			//	Title:  "",
			//	UserId: strconv.FormatUint(userId, 10),
			//})
			//if err != nil {
			//	log.Error("DetectGolangCluesJob", "下发OneForAll任务失败", err, map[string]interface{}{
			//		"domain": domain,
			//	})
			//}
		case clues.TYPE_ICP:
			// 处理ICP类型
			if content == "" {
				log.Warn("DetectGolangCluesJob", "ICP内容为空", map[string]interface{}{
					"company": finalCompanyName,
				})
				continue
			}

			log.Info("DetectGolangCluesJob", "处理ICP线索", map[string]interface{}{
				"content":            content,
				"final_company_name": finalCompanyName,
			})

			clue, err := createOrUpdateClue(clues.TYPE_ICP, content, finalCompanyName, userId, groupId, companyId)
			if err != nil {
				log.Error("DetectGolangCluesJob", "更新ICP线索失败", err, map[string]interface{}{
					"clue":         clue,
					"content":      content,
					"company_name": finalCompanyName,
				})
				continue
			}

			log.Info("DetectGolangCluesJob", "ICP线索入库成功", map[string]interface{}{
				"clue_id":      clue.Id,
				"content":      content,
				"company_name": finalCompanyName,
			})

			// 移除已处理的场景ID
			if len(sceneIds) > 0 {
				sceneIsLock.Lock()
				for i, id := range sceneIds {
					if id == clue.Id {
						sceneIds = append(sceneIds[:i], sceneIds[i+1:]...)
						break
					}
				}
				sceneIsLock.Unlock()
			}

		case clues.TYPE_CERT:
			// 处理证书类型
			if content == "" {
				log.Warn("DetectGolangCluesJob", "证书内容为空", map[string]interface{}{
					"company": finalCompanyName,
				})
				continue
			}

			log.Info("DetectGolangCluesJob", "处理证书线索", map[string]interface{}{
				"content":            content,
				"final_company_name": finalCompanyName,
			})

			clue, err := createOrUpdateClue(clues.TYPE_CERT, content, finalCompanyName, userId, groupId, companyId)
			if err != nil {
				log.Error("DetectGolangCluesJob", "更新证书线索失败", err, map[string]interface{}{
					"clue":         clue,
					"content":      content,
					"company_name": finalCompanyName,
				})
				continue
			}

			log.Info("DetectGolangCluesJob", "证书线索入库成功", map[string]interface{}{
				"clue_id":      clue.Id,
				"content":      content,
				"company_name": finalCompanyName,
			})

			// 移除已处理的场景ID
			if len(sceneIds) > 0 {
				sceneIsLock.Lock()
				for i, id := range sceneIds {
					if id == clue.Id {
						sceneIds = append(sceneIds[:i], sceneIds[i+1:]...)
						break
					}
				}
				sceneIsLock.Unlock()
			}

		case clues.TYPE_LOGO:
			// 收集LOGO类型的线索，稍后统一处理
			log.Info("DetectGolangCluesJob", "收集LOGO线索", map[string]interface{}{
				"item": itemMap,
			})
			allIconClue = append(allIconClue, itemMap)
		}
	}

	// 处理LOGO类型的线索
	if len(allIconClue) > 0 {
		log.Info("DetectGolangCluesJob", "开始处理LOGO线索", map[string]interface{}{
			"logo_count": len(allIconClue),
		})
		for _, icon := range allIconClue {
			if err := handleLogoClue(ctx, clues.NewCluer(), icon, finalCompanyName, userId, groupId, companyId); err != nil {
				log.Error("DetectGolangCluesJob", "处理LOGO线索失败", err, map[string]interface{}{
					"icon":         icon,
					"company_name": finalCompanyName,
				})
			}
		}
	}

	log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 线索处理完成", map[string]interface{}{
		"company_name":       companyName,
		"final_company_name": finalCompanyName,
		"user_id":            userId,
		"group_id":           groupId,
		"processed_items":    len(items),
		"logo_items":         len(allIconClue),
	})

	return nil
}

// createOrUpdateClue 创建或更新线索
func createOrUpdateClue(clueType int, content string, companyName string, userId uint64, groupId uint64, companyId uint64) (*clues.Clue, error) {
	// 专门的重复线索追踪日志标识
	duplicateTraceKey := fmt.Sprintf("DUPLICATE_CLUE_TRACE type:%d content:%s user:%d group:%d", clueType, content, userId, groupId)

	log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 开始创建线索", map[string]interface{}{
		"trace_key":    duplicateTraceKey,
		"type":         clueType,
		"content":      content,
		"company_name": companyName,
		"user_id":      userId,
		"group_id":     groupId,
		"company_id":   companyId,
	})

	clue := &clues.Clue{
		Type:            clueType,
		GroupId:         groupId,
		UserId:          userId,
		Content:         content,
		IsDeleted:       clues.NOT_DELETE,
		ClueCompanyName: companyName,
		CompanyId:       companyId,
		Source:          clues.SOURCE_ICP,
		Status:          clues.CLUE_PASS_STATUS,
	}

	clueModel := clues.NewCluer()

	// 在调用 UpdateOrCreate 前先查询是否已存在
	existingClue, err := clueModel.First(
		mysql.WithColumnValue("type", clueType),
		mysql.WithColumnValue("content", content),
		mysql.WithColumnValue("group_id", groupId),
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
	)

	if err == nil && existingClue.Id > 0 {
		log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 线索已存在，跳过创建", map[string]interface{}{
			"trace_key":             duplicateTraceKey,
			"existing_id":           existingClue.Id,
			"existing_company_name": existingClue.ClueCompanyName,
			"new_company_name":      companyName,
			"existing_status":       existingClue.Status,
		})
		return &existingClue, nil
	}

	log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 线索不存在，开始创建", map[string]interface{}{
		"trace_key":   duplicateTraceKey,
		"query_error": err,
	})

	// 使用 UpdateOrCreate 方法，避免并发问题
	err = clueModel.UpdateOrCreate(clue)
	if err != nil {
		log.Error("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE UpdateOrCreate失败", err, map[string]interface{}{
			"trace_key": duplicateTraceKey,
			"clue":      clue,
		})
		return clue, err
	}

	log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 线索创建完成", map[string]interface{}{
		"trace_key":    duplicateTraceKey,
		"clue_id":      clue.Id,
		"company_name": clue.ClueCompanyName,
		"status":       clue.Status,
	})

	// 如果需要确保状态为 CLUE_PASS_STATUS，可以再次更新
	if clue.Id > 0 && clue.Status != clues.CLUE_PASS_STATUS {
		log.Info("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 更新线索状态", map[string]interface{}{
			"trace_key":  duplicateTraceKey,
			"clue_id":    clue.Id,
			"old_status": clue.Status,
			"new_status": clues.CLUE_PASS_STATUS,
		})

		err = clueModel.UpdateAny(map[string]any{
			"status": clues.CLUE_PASS_STATUS,
		}, mysql.WithColumnValue("id", clue.Id))
		if err != nil {
			log.Error("DetectGolangCluesJob", "DUPLICATE_CLUE_TRACE 更新状态失败", err, map[string]interface{}{
				"trace_key": duplicateTraceKey,
			})
			return clue, err
		}
	}

	return clue, nil
}

// isClueInBlackList 检查线索是否在黑名单中
func isClueInBlackList(item map[string]interface{}) bool {
	clueType, _ := item["type"].(float64)
	clueTypeInt := int(clueType)

	// 获取缓存key
	cacheKey := fmt.Sprintf("expand_clues_black_words_list_%d", clueTypeInt)

	// 从缓存获取黑名单列表
	var blackList []interface{}
	redisClient := redisInit.GetInstance(cfg.LoadRedis())
	cacheData, err := redisClient.Get(context.Background(), cacheKey).Result()
	if err == nil && cacheData != "" {
		if err := json.Unmarshal([]byte(cacheData), &blackList); err != nil {
			log.Error("isClueInBlackList", "解析缓存数据失败", err)
		}
	}

	if err != nil || len(blackList) == 0 {
		// 缓存未命中，从数据库获取
		blackKeywordModel := clue_black_keyword.NewClueBlackKeyworder()
		query := blackKeywordModel.Search(map[string]interface{}{
			"status": "1",
			"type":   strconv.Itoa(clueTypeInt),
		})

		var blackKeywords []clue_black_keyword.ClueBlackKeyword
		if err := query.Find(&blackKeywords).Error; err != nil {
			log.Error("isClueInBlackList", "获取黑名单列表失败", err)
			return false
		}

		// 根据类型获取不同的字段
		if clueTypeInt == clues.TYPE_LOGO {
			// 使用map去重
			hashMap := make(map[int]struct{})
			for _, keyword := range blackKeywords {
				hashMap[keyword.Hash] = struct{}{}
			}
			// 转换为切片
			blackList = make([]interface{}, 0, len(hashMap))
			for hash := range hashMap {
				blackList = append(blackList, hash)
			}
		} else {
			// 使用map去重
			nameMap := make(map[string]struct{})
			for _, keyword := range blackKeywords {
				nameMap[keyword.Name] = struct{}{}
			}
			// 转换为切片
			blackList = make([]interface{}, 0, len(nameMap))
			for name := range nameMap {
				blackList = append(blackList, name)
			}
		}

		// 设置缓存，有效期2小时
		if cacheData, err := json.Marshal(blackList); err == nil {
			if err := redisClient.Set(context.Background(), cacheKey, string(cacheData), 2*time.Hour).Err(); err != nil {
				log.Error("isClueInBlackList", "设置缓存失败", err)
			}
		}
	}

	log.Info("isClueInBlackList", "黑名单列表", map[string]interface{}{
		"black_list": blackList,
		"clue":       item,
	})

	// 检查是否在黑名单中
	if clueTypeInt == clues.TYPE_LOGO {
		hash, _ := item["hash"].(float64)
		hashInt := int(hash)
		for _, value := range blackList {
			if blackHash, ok := value.(int); ok && hashInt == blackHash {
				log.Info("isClueInBlackList", "黑名单匹配", map[string]interface{}{
					"hash":    hashInt,
					"compare": blackHash,
					"clue":    item,
				})
				return true
			}
		}
	} else {
		content, _ := item["content"].(string)
		content = strings.ToLower(strings.TrimSpace(content))
		for _, value := range blackList {
			if blackName, ok := value.(string); ok {
				blackName = strings.ToLower(strings.TrimSpace(blackName))
				if content == blackName {
					log.Info("isClueInBlackList", "黑名单匹配", map[string]interface{}{
						"content": content,
						"compare": blackName,
						"clue":    item,
					})
					return true
				}
			}
		}
	}

	return false
}

// isValidDomain 检查域名是否有效
func isValidDomain(domain string) bool {
	if domain == "" {
		return false
	}

	// 移除可能的协议前缀
	domain = strings.TrimPrefix(domain, "http://")
	domain = strings.TrimPrefix(domain, "https://")

	// 移除路径和查询参数
	if idx := strings.Index(domain, "/"); idx != -1 {
		domain = domain[:idx]
	}
	if idx := strings.Index(domain, "?"); idx != -1 {
		domain = domain[:idx]
	}

	// 检查域名长度
	if len(domain) > 255 {
		return false
	}

	// 检查域名格式，支持更多特殊字符
	domainRegex := regexp.MustCompile(`^([a-zA-Z0-9]([a-zA-Z0-9\-_\.]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$`)
	return domainRegex.MatchString(domain)
}

// pluckCert 从证书字符串中提取 CN 和 O 字段
func pluckCert(str string, onlyDomain bool) (string, string) {
	cnStr := ""
	oStr := ""
	if str == "" {
		return oStr, cnStr
	}

	// 匹配 CN= cn= O= o=  CN =  O = 等
	cnRegex := regexp.MustCompile(`(?i)\bCN\s*=`)
	onRegex := regexp.MustCompile(`(?i)\bO\s*=`)
	if cnRegex.MatchString(str) || onRegex.MatchString(str) {
		cnRegex = regexp.MustCompile(`(?i)\bCN\s*=\s*"?([^",]+)"?`)
		oRegex := regexp.MustCompile(`(?i)\bO\s*=\s*"?([^",]+)"?`)
		if cnMatches := cnRegex.FindStringSubmatch(str); len(cnMatches) > 1 {
			cnStr = cnMatches[1]
		}
		if oMatches := oRegex.FindStringSubmatch(str); len(oMatches) > 1 {
			oStr = oMatches[1]
		}
	} else {
		// 使用 isValidDomain 方法验证域名
		if isValidDomain(str) {
			cnStr = str
		} else {
			oStr = str
		}
	}

	return oStr, cnStr
}

// getTopDomain 获取顶级域名
//func getTopDomain(domain string) string {
//	parts := strings.Split(domain, ".")
//	if len(parts) <= 2 {
//		return domain
//	}
//	return strings.Join(parts[len(parts)-2:], ".")
//}

// handleLogoClue 处理 LOGO 类型的线索
func handleLogoClue(ctx context.Context, clueModel clues.Cluer, icon map[string]interface{}, companyName string, userId uint64, groupId uint64, companyId uint64) error {
	log.Info("DetectGolangCluesJob", "golang接口返回的icon数据", icon)

	source, _ := icon["source"].(string)
	//clueModel := clues.NewCluer()

	// hash 字段类型转换
	var hashInt int
	switch v := icon["hash"].(type) {
	case float64:
		hashInt = int(v)
	case int:
		hashInt = v
	case string:
		hashInt, _ = strconv.Atoi(v)
	}

	//是否存在LOGO线索
	existingClue, err := clueModel.First(
		func(tx *gorm.DB) {
			tx.Where("user_id = ? AND type = ? AND group_id = ? AND is_deleted = ? AND hash = ?",
				userId, clues.TYPE_LOGO, groupId, clues.NOT_DELETE, hashInt)
		},
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if existingClue.Id > 0 {
		existingClue.Status = clues.CLUE_DEFAULT_STATUS
		existingClue.UpdatedAt = time.Now()
		return clueModel.Update(existingClue)
	}

	// 不存在LOGO线索 查询父级线索
	parentClue, err := clueModel.First(
		func(tx *gorm.DB) {
			tx.Where("user_id = ? AND group_id = ? AND is_deleted = ? AND content = ?",
				userId, groupId, clues.NOT_DELETE, clueUtil.GetTopDomain(source))
		},
	)
	if err != nil {
		log.Warn("DetectGolangCluesJob", "备案爬取icon找不到父级线索id", map[string]interface{}{
			"source":   source,
			"group_id": groupId,
		})
	}

	parentId := uint64(0)
	if parentClue.Id > 0 {
		parentId = parentClue.Id
	}

	content := ""
	if cfg.IsLocalClient() {
		iconContent, _ := icon["content"].(string)
		if !strings.Contains(iconContent, "app/public") {
			log.Info("DetectGolangCluesJob", "取的golang总库的icon地址", map[string]interface{}{
				"clue": icon,
			})
			downUrl := cfg.GetWebUrl() + iconContent
			base64Content, err := utils.DownloadAndEncodeBase64(downUrl)
			if err != nil {
				log.Error("DetectGolangCluesJob", "图片转base64失败", err, map[string]interface{}{
					"clue_hash": icon["hash"],
				})
			}
			if base64Content != "" {
				content = utils.SaveBase64Ico(base64Content, userId)
				log.Info("DetectGolangCluesJob", "保存base64图片", map[string]interface{}{
					"content": content,
				})
			} else {
				content = cfg.GetWebUrl() + utils.GenDownloadUrl(iconContent, filepath.Base(iconContent), false)
				log.Info("DetectGolangCluesJob", "生成下载URL", map[string]interface{}{
					"content": content,
				})
			}
		} else {
			log.Info("DetectGolangCluesJob", "php根据域名爬取icon数据", map[string]interface{}{
				"clue": icon,
			})
			return nil
		}
	} else {
		iconContent, _ := icon["content"].(string)
		if !strings.Contains(iconContent, "app/public") {
			content = utils.ParseDownloadUrl(iconContent, false)
		}
	}

	iconClue := &clues.Clue{
		ClueCompanyName: companyName,
		Content:         content,
		CompanyId:       companyId,
		Source:          clues.SOURCE_ICP,
		Status:          clues.CLUE_DEFAULT_STATUS,
		UserId:          userId,
		Type:            clues.TYPE_LOGO,
		GroupId:         groupId,
		Hash:            hashInt,
		ParentId:        parentId,
	}

	return clueModel.Create(iconClue)
}

func sendProgressForDetectCluesJob(ctx context.Context, userId uint64, task *detect_assets_tasks.DetectAssetsTask, doneCount uint64, totalCount uint64, status int, progress float64, companyName string) error {
	// 计算进度百分比
	var calculatedProgress float64
	if totalCount == 0 {
		calculatedProgress = 100
	} else {
		// 使用浮点数计算: (doneCount + progress) * 100 / totalCount
		calculatedProgress = (float64(doneCount) + progress) * 100 / float64(totalCount)
		// 保留2位小数
		calculatedProgress = math.Round(calculatedProgress*100) / 100
	}

	// 如果状态为0且进度为100%，则设置为99%
	if status == 0 && calculatedProgress == 100 {
		calculatedProgress = 99
	}

	// 进度最小值为3%
	if calculatedProgress < 3 {
		calculatedProgress = 3
	}

	if totalCount == 0 {
		status = detect_assets_tasks.StatusFinished
	} else {
		status = detect_assets_tasks.StatusDefault
	}

	taskId := uint(0)
	if task != nil {
		taskId = task.ID
	}
	// 更新传入的progress参数
	progress = calculatedProgress
	err := websocket_message.PublishSuccess(int64(userId), "detect_assets_tip1_process", map[string]any{
		"progress":      progress,
		"status":        status,
		"task_id":       taskId,
		"user_id":       userId,
		"company_name":  companyName,
		"total_company": totalCount,
	})
	if err != nil {
		log.Error("sendProgressForDetectCluesJob", "发送进度失败", err)
	}
	if task != nil {
		task.ClueProgress = progress
		err := detect_assets_tasks.NewModel().UpdateAny(map[string]any{"clue_progress": progress}, detect_assets_tasks.WithID(uint64(task.ID)))
		if err != nil {
			log.Error("sendProgressForDetectCluesJob", "更新任务进度失败", err)
		}
	}
	return nil
}
