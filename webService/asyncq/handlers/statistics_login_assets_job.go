package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/login_page_assets"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"

	es_utils "micro-service/middleware/elastic"

	"github.com/olivere/elastic"
)

// StatisticsLoginAssetsJob 统计登录入口数据
// 消息体:
//
//	{
//		"user_id": 1,    // 用户ID，必填
//		"task_id": 1     // 任务ID，必填
//	}
func StatisticsLoginAssetsJob(ctx context.Context, t *asyncq.Task) error {
	payloadInfo, err := parsePayloadForStatisticsLoginAssets([]byte(t.Payload))
	if err != nil {
		return err
	}

	log.Info("StatisticsLoginAssetsJob", "统计登录入口数据开始", map[string]interface{}{
		"user_id": payloadInfo.UserId,
		"task_id": payloadInfo.TaskId,
	})

	// 查询公司信息
	companyModel := company.NewCompanyModel()
	companyInfo, err := companyModel.FindById(0, payloadInfo.UserId)
	if err != nil {
		log.Warn("StatisticsLoginAssetsJob", "查询公司信息失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"error":   err.Error(),
		})
	}

	// 查询登录页面资产，最多重试5次
	var assets []*foradar_assets.ForadarAsset
	for retry := 1; retry <= 6; retry++ {
		assets, err = queryLoginAssets(ctx, payloadInfo.UserId, payloadInfo.TaskId)
		if err != nil {
			log.Error("StatisticsLoginAssetsJob", "查询登录页面资产失败", map[string]interface{}{
				"user_id": payloadInfo.UserId,
				"task_id": payloadInfo.TaskId,
				"retry":   retry,
				"error":   err.Error(),
			})
			return err
		}

		if len(assets) > 0 {
			log.Info("StatisticsLoginAssetsJob", "查询到登录页面资产", map[string]interface{}{
				"user_id": payloadInfo.UserId,
				"task_id": payloadInfo.TaskId,
				"count":   len(assets),
				"retry":   retry,
			})
			break
		}

		if retry < 6 {
			log.Info("StatisticsLoginAssetsJob", fmt.Sprintf("第%d次没查到登录入口的资产，等到10秒再进行查询", retry), map[string]interface{}{
				"user_id": payloadInfo.UserId,
				"task_id": payloadInfo.TaskId,
			})
			time.Sleep(10 * time.Second)
		}
	}

	// 查询测绘任务ID
	var detectTaskId int
	scanTaskModel := scan_task.NewScanTasksModel()
	scanTaskInfo, err := scanTaskModel.FindByID(payloadInfo.TaskId)
	if err != nil {
		log.Warn("StatisticsLoginAssetsJob", "查询任务信息失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
			"error":   err.Error(),
		})
	} else if scanTaskInfo != nil {
		detectTaskId = scanTaskInfo.DetectAssetsTasksId
	}

	if len(assets) > 0 {
		log.Info("StatisticsLoginAssetsJob", "查询到了有登录入口的资产", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
		})

		// 处理资产数据
		err = processLoginAssets(payloadInfo.UserId, assets, companyInfo, detectTaskId)
		if err != nil {
			log.Error("StatisticsLoginAssetsJob", "处理登录资产数据失败", map[string]interface{}{
				"user_id": payloadInfo.UserId,
				"task_id": payloadInfo.TaskId,
				"error":   err.Error(),
			})
			return err
		}

		log.Info("StatisticsLoginAssetsJob", fmt.Sprintf("统计登录入口数据结束，总共写入%d个登录页面数据", len(assets)), map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
		})
	} else {
		log.Info("StatisticsLoginAssetsJob", "统计登录入口数据结束-资产数据为空，统计结果为空", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"task_id": payloadInfo.TaskId,
		})
	}

	return nil
}

// queryLoginAssets 查询登录页面资产
func queryLoginAssets(ctx context.Context, userId, taskId uint64) ([]*foradar_assets.ForadarAsset, error) {
	// 构建查询条件
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	query.Must(elastic.NewTermQuery("task_id", taskId))
	query.Must(elastic.NewTermQuery("is_login_page", 1))
	query.Must(elastic.NewTermsQuery("status", foradar_assets.StatusUploadAsset, foradar_assets.StatusConfirmAsset))

	// 查询资产
	assets, err := es_utils.All[foradar_assets.ForadarAsset](200, query, nil, "id", "ip", "title", "port", "url", "screenshot")
	if err != nil {
		return nil, fmt.Errorf("查询ForadarAssets失败: %v", err)
	}

	return assets, nil
}

// processLoginAssets 处理登录资产数据
func processLoginAssets(userId uint64, assets []*foradar_assets.ForadarAsset, companyInfo company.Company, detectTaskId int) error {
	loginPageModel := login_page_assets.NewModel()

	// 准备数据
	var loginAssets []*login_page_assets.LoginPageAssets
	uniqueKeys := make(map[string]bool)
	node := cfg.LoadCommon().Node

	for _, asset := range assets {
		// 格式化URL
		url := formatUrl(asset.Url, asset.Port)
		uniqueKey := fmt.Sprintf("%d-%s", userId, url)
		// 避免重复
		if uniqueKeys[uniqueKey] {
			continue
		}
		uniqueKeys[uniqueKey] = true

		// 构建登录页面资产对象
		loginAsset := &login_page_assets.LoginPageAssets{
			UserId:    userId,
			CompanyId: int64(companyInfo.ID),
			Title:     utils.SafeString(asset.Title),
			Ip:        asset.Ip,
			Port:      strconv.Itoa(utils.SafeInt(asset.Port)),
			Url:       url,
			ImgUrl:    asset.Screenshot,
			Status:    0, // 待审核
			Node:      node,
			UniqueKey: uniqueKey,
		}

		// 如果有测绘任务ID，设置detect_task_id
		if detectTaskId > 0 {
			detectTaskIdStr := fmt.Sprintf("[%d]", detectTaskId)
			loginAsset.DetectTaskId = detectTaskIdStr
		}

		// 设置时间
		now := time.Now()
		loginAsset.CreatedAt = now
		loginAsset.UpdatedAt = now

		loginAssets = append(loginAssets, loginAsset)
	}

	// 批量写入数据
	if len(loginAssets) > 0 {
		// 批量写入数据,UpsertByKey方法会
		err := loginPageModel.UpsertByKey(userId, loginAssets)
		if err != nil {
			return fmt.Errorf("写入登录页面资产失败: %v", err)
		}
	}

	return nil
}

// formatUrl 格式化URL
func formatUrl(url string, port interface{}) string {
	if url == "" {
		return ""
	}

	// 检查URL是否已经包含协议
	httpCount := strings.Count(url, "http://")
	httpsCount := strings.Count(url, "https://")

	if httpCount == 0 && httpsCount == 0 {
		// 根据端口判断协议
		portInt := utils.SafeInt(port)
		if portInt == 443 {
			return "https://" + url
		} else {
			return "http://" + url
		}
	}

	return url
}

// parsePayloadForStatisticsLoginAssets 解析任务参数
func parsePayloadForStatisticsLoginAssets(payload []byte) (*asyncq.StatisticsLoginAssetsJobPayload, error) {
	var payloadInfo asyncq.StatisticsLoginAssetsJobPayload
	if err := json.Unmarshal(payload, &payloadInfo); err != nil {
		return nil, fmt.Errorf("解析任务参数失败: %v", err)
	}

	if payloadInfo.UserId == 0 {
		return nil, fmt.Errorf("用户ID不能为空")
	}

	if payloadInfo.TaskId == 0 {
		return nil, fmt.Errorf("任务ID不能为空")
	}

	return &payloadInfo, nil
}
