package handlers

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"html"
	"io"
	core "micro-service/coreService/proto"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/domain_assets"
	"micro-service/middleware/mysql/domain_historys"
	"micro-service/middleware/mysql/domain_task"
	"micro-service/middleware/mysql/domain_task_result"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/middleware/mysql/organization_discover_task"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/pkg/cfg"
	"micro-service/pkg/chinaz"
	domain_utils "micro-service/pkg/domain"
	"micro-service/pkg/log"
	"micro-service/pkg/network"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"
	"net"
	"net/http"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	es_utils "micro-service/middleware/elastic"

	"github.com/olivere/elastic"
	"gorm.io/gorm"
)

type AaaRecords struct {
	Ip      []string `json:"ip"`
	DnsA    []string `json:"dns_a"`
	DnsAaaa []string `json:"dns_aaaa"`
	Cname   []string `json:"cname"`
}

// TableAssetsDomainsSyncEventHandler 台账同步域名任务处理
func TableAssetsDomainsSyncEventHandler(ctx context.Context, t *asyncq.Task) error {
	var p asyncq.TableAssetsDomainsSyncJobPayload
	if err := json.Unmarshal([]byte(t.Payload), &p); err != nil {
		log.Errorf("TableAssetsDomainsSyncJob: 反序列化payload失败: %v", err)
		return err
	}

	log.Infof("TableAssetsDomainsSyncJob: 开始处理任务, payload: %+v", p)

	if p.DetectTaskID > 0 {
		err := getDetectTaskInfo(&p)
		if err != nil {
			return err
		}
	}
	domainAll, err := fetchDomains(&p)
	if err != nil {
		log.Errorf("TableAssetsDomainsSyncJob: 获取域名失败: %v", err)
		return err
	}

	if err := processDomains(ctx, &p, domainAll); err != nil {
		log.Errorf("TableAssetsDomainsSyncJob: 处理域名失败: %v", err)
		return err
	}

	log.Infof("TableAssetsDomainsSyncJob: 任务处理完成, 用户ID: %d, 处理域名数量: %d", p.UserID, len(domainAll))
	return nil
}

// getDetectTaskInfo 获取检测资产任务信息
func getDetectTaskInfo(p *asyncq.TableAssetsDomainsSyncJobPayload) error {
	detectTask, err := detect_assets_tasks.NewModel().First(detect_assets_tasks.WithID(p.DetectTaskID))
	if err != nil {
		log.Errorf("TableAssetsDomainsSyncJob: 未找到检测资产任务: %v", err)
		return err
	}
	if detectTask == nil {
		log.Errorf("TableAssetsDomainsSyncJob: 未找到检测资产任务: ID=%d", p.DetectTaskID)
		return fmt.Errorf("未找到检测资产任务: ID=%d", p.DetectTaskID)
	}
	if detectTask.ConfirmCompanyList != "" {
		var confirmCompanyList []string
		if err := json.Unmarshal([]byte(detectTask.ConfirmCompanyList), &confirmCompanyList); err != nil {
			log.Errorf("TableAssetsDomainsSyncJob: 反序列化检测资产任务确认公司列表失败: %v", err)
			return err
		}
		clearCompanyList := make([]string, 0, len(confirmCompanyList))
		for _, company := range confirmCompanyList {
			company = strings.ToLower(strings.ReplaceAll(company, " ", ""))
			company = strings.ReplaceAll(company, "(", "")
			company = strings.ReplaceAll(company, ")", "")
			company = strings.ReplaceAll(company, "（", "")
			company = strings.ReplaceAll(company, "）", "")
			clearCompanyList = append(clearCompanyList, company)
		}
		p.DetectTaskCompanyName = clearCompanyList
	}
	return nil
}

func processDomains(ctx context.Context, p *asyncq.TableAssetsDomainsSyncJobPayload, domains []string) error {
	if len(domains) == 0 {
		return nil
	}

	log.Infof("TableAssetsDomainsSyncJob: 处理域名, 用户ID: %d, 域名数量: %d", p.UserID, len(domains))

	for i, domain := range domains {
		if domain == "" {
			continue
		}

		// 检查域名是否合法
		if !utils.IsValidDomain(domain) {
			log.Warnf("TableAssetsDomainsSyncJob: 域名不合法, 域名: %s", domain)
			continue
		}

		// 判断是否是gov域名
		isGovDomain := false
		if strings.Contains(domain, ".gov.cn") {
			isGovDomain = true
		}
		topDomain := utils.GetTopDomain(domain)

		asset := &domain_assets.DomainAssets{}
		// 非本地化用户
		if !cfg.IsLocalClient() {
			if isGovDomain {
				// 使用新的方法处理政府域名的公司信息获取
				if !processGovDomainCompanyInfo(ctx, asset, domain, p.UserID) {
					log.Warnf("TableAssetsDomainsSyncJob: 无法获取域名公司信息, 域名: %s", domain)
				}
			} else {
				// 使用新的方法处理非政府域名的公司信息获取
				if !processNormalDomainCompanyInfo(ctx, asset, domain) {
					log.Warnf("TableAssetsDomainsSyncJob: 无法获取域名公司信息, 域名: %s", domain)
				}
			}
		} else {
			searchDomain := domain
			if !isGovDomain {
				searchDomain = topDomain
			}
			info, err := tryGetCompanyInfoFromCoreService(ctx, searchDomain)
			if err == nil && info != nil {
				setAssetCompanyInfo(asset, info)
			}
		}

		// 如果还没有获取到公司信息，尝试从GeneralClues获取
		if asset.CompanyName == "" {
			if info, err := tryGetCompanyInfoFromGeneralClues(ctx, topDomain); err == nil && info != nil {
				setAssetCompanyInfo(asset, info)
			}

			if info, err := tryGetCompanyInfoFromCoreService(ctx, topDomain); err == nil && info != nil {
				setAssetCompanyInfo(asset, info)
			}
		}
		if asset.CompanyName != "" {
			asset.CompanyName = strings.ToLower(strings.ReplaceAll(asset.CompanyName, " ", ""))
			asset.CompanyName = strings.ReplaceAll(asset.CompanyName, "(", "")
			asset.CompanyName = strings.ReplaceAll(asset.CompanyName, ")", "")
			asset.CompanyName = strings.ReplaceAll(asset.CompanyName, "（", "")
			asset.CompanyName = strings.ReplaceAll(asset.CompanyName, "）", "")
		}

		companyInfo, err := company.NewCompanyModel().First(mysql.WithColumnValue("owner_id", p.UserID))
		if err != nil {
			log.Errorf("TableAssetsDomainsSyncJob: 获取公司信息失败: %v", err)
			return err
		}
		if companyInfo.ID > 0 {
			asset.CompanyId = companyInfo.ID
		}
		// 获取AAA记录
		asset.Domain = domain
		asset.TopDomain = topDomain
		asset.FDomain = utils.GetFullDomain(domain)
		asset.Depth = func() int {
			parts := strings.Split(domain, ".")
			return len(parts) - 1
		}()
		asset.Type = func() int {
			if domain != topDomain {
				return domain_assets.YES_ANALYAIS_DOMAIN
			}
			return domain_assets.NO_ANALYAIS_DOMAIN
		}()
		asset.UserId = p.UserID

		// 获取域名的解析数据
		aaaRecord := getDomainAandAAAandCname(domain)
		if aaaRecord != nil {
			asset.Status = domain_assets.CAN_ANALYAIS // 可以解析
			asset.DnsA = strings.Join(aaaRecord.DnsA, ",")
			asset.DnsAaaa = strings.Join(aaaRecord.DnsAaaa, ",")
			asset.Cname = strings.Join(aaaRecord.Cname, ",")
			p.Ips = aaaRecord.Ip
			// 获取网站信息
			asset.StatusCode = ""
			asset.Title = ""
			websiteInfo := getWebsiteInfo(domain)
			if websiteInfo != nil {
				asset.StatusCode = strconv.Itoa(websiteInfo.StatusCode)
				asset.Title = websiteInfo.Title
				// 如果状态码是302、301、307、308，则获取https的网站信息
				if websiteInfo.StatusCode == 302 || websiteInfo.StatusCode == 301 || websiteInfo.StatusCode == 307 || websiteInfo.StatusCode == 308 {
					websiteInfo = getWebsiteInfo("https://" + domain)
					if websiteInfo != nil {
						asset.StatusCode = strconv.Itoa(websiteInfo.StatusCode)
						asset.Title = websiteInfo.Title
					}
				}
			}
		} else {
			asset.Status = domain_assets.NO_ANALYAIS // 不能解析
			asset.StatusCode = ""
			asset.Title = ""
		}

		// 判断域名是不是泛解析
		isOpenParse := domain_utils.IsWildcardDomain(topDomain)
		if isOpenParse {
			asset.OpenParse = 1
		} else {
			asset.OpenParse = 0 // 不是泛解析
		}

		source := ""
		switch p.From {
		case domain_assets.TABLE_DOMAIN:
			source = domain_assets.SourceScan
		case domain_assets.ASSETS_RECOMMEND:
			source = domain_assets.SourceRecommend
		case domain_assets.DOMAIN_TASK:
			source = domain_assets.SourceEnum
		case domain_assets.DOMAIN_IMPORT:
			source = domain_assets.SourceImport
		case domain_assets.DOMAIN_SCAN:
			source = domain_assets.SourceVerify
		}
		asset.Source = source

		// 更新域名资产
		upsertDomainAsset(ctx, p, asset, aaaRecord)
		// 发送进度
		progress := (float64(i+1)/float64(len(domains)))*50 + 50
		sendProgress(ctx, p, progress)
	}
	// 发送完成进度
	sendProgress(ctx, p, 100)
	return nil
}

// getDomainAandAAAandCname 获取域名的解析数据
func getDomainAandAAAandCname(domain string) *AaaRecords {
	data := &AaaRecords{}

	// 获取A记录
	aRecords, err := net.LookupIP(domain)
	if err == nil {
		for _, ip := range aRecords {
			if ip.To4() != nil {
				// IPv4地址
				ipv4 := ip.String()
				data.Ip = append(data.Ip, ipv4)
				data.DnsA = append(data.DnsA, ipv4)
			}
		}
	}

	// 获取AAAA记录
	aaaaRecords, err := net.LookupIP(domain)
	if err == nil {
		for _, ip := range aaaaRecords {
			if ip.To4() == nil && ip.To16() != nil {
				// IPv6地址
				ipv6 := ip.String()
				// 判断是否是IPv4映射的IPv6，如果是则过滤掉
				if !network.IsIPv4MappedIPv6(ipv6) {
					data.Ip = append(data.Ip, ipv6)
					data.DnsAaaa = append(data.DnsAaaa, ipv6)
				}
			}
		}
	}

	// 获取CNAME记录
	cnameRecord, err := net.LookupCNAME(domain)
	if err == nil && cnameRecord != "" && cnameRecord != domain+"." {
		// 移除末尾的点号
		target := strings.TrimSuffix(cnameRecord, ".")
		data.Cname = append(data.Cname, target)
	}

	return data
}

// WebsiteInfo 网站信息结构体
type WebsiteInfo struct {
	StatusCode int    `json:"status_code"`
	Title      string `json:"title"`
}

// getWebsiteInfo 获取网站信息，返回状态码和标题
func getWebsiteInfo(url string) *WebsiteInfo {
	log.Infof("getWebsiteInfo: 开始获取网站信息, url: %s", url)

	var statusCode int
	var htmlContent string
	var title string

	log.Infof("getWebsiteInfo: 创建HTTP客户端, url: %s", url)

	// 创建HTTP客户端，配置超时和禁用SSL验证
	client := &http.Client{
		Timeout: 3 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Errorf("getWebsiteInfo: 创建请求失败, url: %s, error: %v", url, err)
		return &WebsiteInfo{StatusCode: 0, Title: ""}
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36")

	log.Infof("getWebsiteInfo: 发送HTTP请求, url: %s", url)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		log.Warnf("getWebsiteInfo: HTTP请求失败, url: %s, error: %v", url, err)
		return &WebsiteInfo{StatusCode: 0, Title: ""}
	}
	defer resp.Body.Close()

	log.Infof("getWebsiteInfo: 收到HTTP响应, url: %s", url)

	// 获取状态码
	statusCode = resp.StatusCode

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warnf("getWebsiteInfo: 读取响应内容失败, url: %s, error: %v", url, err)
		return &WebsiteInfo{StatusCode: statusCode, Title: ""}
	}

	htmlContent = string(body)

	// 解析HTML内容并提取标题
	if htmlContent != "" {
		title = extractTitleFromHTML(htmlContent, url)
	}

	// 处理标题，移除等号并限制长度
	title = strings.ReplaceAll(title, "=", "")
	if len(title) > 200 {
		// 安全地截取字符串，避免截断UTF-8字符
		runes := []rune(title)
		if len(runes) > 200 {
			title = string(runes[:200])
		}
	}

	log.Infof("getWebsiteInfo: 获取网站信息完成, url: %s, title: %s, status_code: %d", url, title, statusCode)

	return &WebsiteInfo{
		StatusCode: statusCode,
		Title:      title,
	}
}

// extractTitleFromHTML 从HTML内容中提取标题
func extractTitleFromHTML(htmlContent, url string) string {
	var title string

	defer func() {
		if r := recover(); r != nil {
			log.Warnf("extractTitleFromHTML: 解析HTML时发生panic, url: %s, error: %v", url, r)
			title = htmlContent
			if len(title) > 200 {
				runes := []rune(title)
				if len(runes) > 200 {
					title = string(runes[:200])
				}
			}
		}
	}()

	// 使用正则表达式提取title标签内容
	titleRegex := regexp.MustCompile(`(?i)<title[^>]*>([^<]*)</title>`)
	matches := titleRegex.FindStringSubmatch(htmlContent)

	if len(matches) > 1 {
		title = strings.TrimSpace(matches[1])
		// 解码HTML实体
		title = html.UnescapeString(title)
	} else {
		// 如果没有找到title标签，使用HTML内容作为备选
		if len(htmlContent) > 0 {
			title = htmlContent
		}
		log.Warnf("extractTitleFromHTML: 未找到title标签, url: %s", url)
	}

	return title
}

func getIcp(ctx context.Context, domain string) (string, error) {
	token := cfg.LoadChinaz().ICPToken
	icpResp, err := chinaz.NewDefaultClient(token).Domain(ctx, &chinaz.ICPRequest{
		Domain: domain,
	})
	if err != nil {
		return "", err
	}
	return icpResp.Result.SiteLicense, nil
}

func extractSecondDomain(domain string) string {
	parts := strings.Split(domain, ".")
	count := len(parts)

	if count >= 3 {
		// 取最后4个部分拼接
		start := count - 4
		if start < 0 {
			start = 0
		}
		result := strings.Join(parts[start:], ".")
		return result
	}

	return ""
}

// CompanyInfo 公司信息结构体
type CompanyInfo struct {
	CompanyName string
	Icp         string
}

// tryGetCompanyInfoFromGeneralClues 尝试从GeneralClues获取公司信息
func tryGetCompanyInfoFromGeneralClues(ctx context.Context, domain string) (*CompanyInfo, error) {
	// 从GeneralClues获取公司信息
	generalClue, err := general_clues.NewGeneralCluesModel().First(mysql.WithColumnValue("content", domain))
	if err != nil {
		return nil, err
	}

	if generalClue.CompanyName == "" {
		return nil, nil
	}

	icp, err := getIcp(ctx, domain)
	if err != nil {
		return nil, err
	}

	return &CompanyInfo{
		CompanyName: generalClue.CompanyName,
		Icp:         icp,
	}, nil
}

// tryGetCompanyInfoFromClues 尝试从Clues获取公司信息
func tryGetCompanyInfoFromClues(ctx context.Context, domain string, userID uint64) (*CompanyInfo, error) {
	clue, err := clues.NewCluer().First(
		mysql.WithColumnValue("content", domain),
		mysql.WithColumnValue("user_id", userID),
		mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
	)
	if err != nil {
		return nil, err
	}

	if clue.ClueCompanyName == "" {
		return nil, nil
	}

	icp, err := getIcp(ctx, domain)
	if err != nil {
		return nil, err
	}

	return &CompanyInfo{
		CompanyName: clue.ClueCompanyName,
		Icp:         icp,
	}, nil
}

// tryGetCompanyInfoFromCoreService 尝试从核心服务获取公司信息
func tryGetCompanyInfoFromCoreService(ctx context.Context, domain string) (*CompanyInfo, error) {
	icp, err := core.GetProtoCoreClient().Domain(ctx, &core.IcpDomainRequest{
		Domain: domain,
	})
	if err != nil {
		return nil, err
	}

	return &CompanyInfo{
		CompanyName: icp.Info.CompanyName,
		Icp:         icp.Info.Icp,
	}, nil
}

// setAssetCompanyInfo 设置资产的公司信息
func setAssetCompanyInfo(asset *domain_assets.DomainAssets, info *CompanyInfo) {
	if info != nil {
		asset.CompanyName = info.CompanyName
		asset.Icp = info.Icp
	}
}

// processGovDomainCompanyInfo 处理政府域名的公司信息获取
func processGovDomainCompanyInfo(ctx context.Context, asset *domain_assets.DomainAssets, domain string, userID uint64) bool {
	topDomain := utils.GetTopDomain(domain)

	// 判断是主域名还是子域名，如果是主域名，则直接获取公司信息
	if topDomain == domain {
		// 主域名处理逻辑
		if info, err := tryGetCompanyInfoFromGeneralClues(ctx, domain); err == nil && info != nil {
			setAssetCompanyInfo(asset, info)
			return true
		}

		if info, err := tryGetCompanyInfoFromClues(ctx, topDomain, userID); err == nil && info != nil {
			setAssetCompanyInfo(asset, info)
			return true
		}

		if info, err := tryGetCompanyInfoFromCoreService(ctx, domain); err == nil && info != nil {
			setAssetCompanyInfo(asset, info)
			return true
		}
	} else {
		// 子域名处理逻辑
		if strings.Count(topDomain, ".") == 3 {
			if info, err := tryGetCompanyInfoFromClues(ctx, topDomain, userID); err == nil && info != nil {
				setAssetCompanyInfo(asset, info)
				return true
			}

			if info, err := tryGetCompanyInfoFromCoreService(ctx, topDomain); err == nil && info != nil {
				setAssetCompanyInfo(asset, info)
				return true
			}
		} else {
			secondDomain := extractSecondDomain(domain)
			if secondDomain != "" {
				if info, err := tryGetCompanyInfoFromClues(ctx, secondDomain, userID); err == nil && info != nil {
					setAssetCompanyInfo(asset, info)
					return true
				}

				if info, err := tryGetCompanyInfoFromCoreService(ctx, secondDomain); err == nil && info != nil {
					setAssetCompanyInfo(asset, info)
					return true
				}
			}
		}
	}

	return false
}

// processNormalDomainCompanyInfo 处理非政府域名的公司信息获取
func processNormalDomainCompanyInfo(ctx context.Context, asset *domain_assets.DomainAssets, domain string) bool {
	topDomain := utils.GetTopDomain(domain)
	if info, err := tryGetCompanyInfoFromGeneralClues(ctx, topDomain); err == nil && info != nil {
		setAssetCompanyInfo(asset, info)
		return true
	}

	if info, err := tryGetCompanyInfoFromCoreService(ctx, topDomain); err == nil && info != nil {
		setAssetCompanyInfo(asset, info)
		return true
	}

	return false
}

func fetchDomains(p *asyncq.TableAssetsDomainsSyncJobPayload) ([]string, error) {
	//台账的域名的icp备案不是在单位测绘的企业名称里面的域名
	domainAll := make([]string, 0)
	switch p.From {
	// 台账
	case domain_assets.TABLE_DOMAIN:
		log.Infof("TableAssetsDomainsSyncJob: 从台账获取域名, 任务ID: %+v", p.TaskID)
		// 台账来的
		isNoScan := false

		query := elastic.NewBoolQuery().
			Must(
				elastic.NewTermQuery("user_id", p.UserID),
				elastic.NewTermsQuery("status", fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD),
			)
		if len(p.TaskID) > 1 {
			query.Must(elastic.NewTermsQuery("task_id", p.TaskID))

		} else {
			if p.OrganizationDiscoverTaskID > 0 {
				isNoScan = true
				// PHP 使用的条件是 task_id，保持一致
				query.Must(elastic.NewTermQuery("org_detect_assets_tasks_id", p.TaskID))
			} else {
				query.Must(elastic.NewTermQuery("task_id", p.TaskID))
			}
		}
		// 获取资产
		assets, err := es_utils.All[fofaee_assets.FofaeeAssets](500, query, nil, "id", "host_list", "subdomain", "domain")
		if err != nil {
			log.Errorf("TableAssetsDomainsSyncJob: 从 ES 获取资产失败: %v", err)
			return nil, err
		}

		if len(assets) == 0 {
			log.Infof("TableAssetsDomainsSyncJob: 台账域名数据同步结束-域名结构为空，统计结果为空, 任务ID: %+v", p.TaskID)
			return nil, nil
		}

		for _, asset := range assets {
			if asset.HostList != nil {
				for _, host := range asset.HostList {
					domainAll = append(domainAll, host.Domain)
					domainAll = append(domainAll, host.Subdomain)
				}
			}
		}
		if !isNoScan {
			domainAll, err = getAllDomain(p, domainAll)
			if err != nil {
				return nil, err
			}
		} else {
			orgTask, err := organization_discover_task.NewModel().First(organization_discover_task.WithID(p.OrganizationDiscoverTaskID), organization_discover_task.WithUserID(p.UserID))
			if err != nil || orgTask.ID == 0 {
				log.Warnf("TableAssetsDomainsSyncJob: 未找到组织架构任务或任务不存在. task_id: %d", p.OrganizationDiscoverTaskID)
				return nil, nil // PHP 返回 false, 停止任务.
			}
			if orgTask.GroupId > 0 {
				clueDomainArr, err := clues.NewCluer().ListAllContent(
					clues.WithGroupIDAndTypes(orgTask.GroupId, []int{clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN}),
					mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
					mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
				)
				if err != nil {
					log.Errorf("TableAssetsDomainsSyncJob: 获取线索域名失败: %v", err)
					return nil, err
				}
				if len(clueDomainArr) > 0 {
					bakDomainAll := make([]string, 0)
					for _, domain := range domainAll {
						topDomain := utils.GetTopDomain(domain)
						// 主域名直接判断是否在线索库里面
						if domain == topDomain {
							if slices.Contains(clueDomainArr, domain) {
								bakDomainAll = append(bakDomainAll, domain)
							}
						} else {
							// 子域名，根据主域名是否在线索库里面
							if slices.Contains(clueDomainArr, domain) || slices.Contains(clueDomainArr, topDomain) {
								bakDomainAll = append(bakDomainAll, domain)
							} else {
								// 循环线索
								for _, clueDomain := range clueDomainArr {
									if strings.HasSuffix(domain, clueDomain) || strings.HasSuffix(clueDomain, domain) {
										bakDomainAll = append(bakDomainAll, domain)
										break
									}
								}
							}
						}
					}
					domainAll = bakDomainAll
				} else {
					log.Infof("TableAssetsDomainsSyncJob: 台账域名同步的时候去找组织架构测绘任务的线索库，但是找不到已经确认的域名类型的线索，不进行域名过滤")
					return nil, nil
				}
			} else {
				log.Infof("TableAssetsDomainsSyncJob: 当前台账域名提取的组织架构测绘任务没找到线索分组数据，不进行域名过滤")
				return nil, nil
			}
		}
		if len(domainAll) == 0 {
			log.Infof("TableAssetsDomainsSyncJob: 台账域名数据同步结束-域名结构为空，台账资产里面的域名没有符合条件的域名")
			return nil, nil
		}
	case domain_assets.ASSETS_RECOMMEND:
		log.Infof("TableAssetsDomainsSyncJob: 从 ASSETS_RECOMMEND 获取域名, 用户ID: %d", p.UserID)

		if p.Flag != "" {
			// 推荐资产域名数据同步-存在flag
			topDomains := make([]string, 0)
			assets, err := domain_assets.NewModel().ListAll([]string{"top_domain"},
				mysql.WithColumnValue("user_id", p.UserID),
				mysql.WithColumnValue("flag", p.Flag),
			)
			if err != nil {
				log.Errorf("TableAssetsDomainsSyncJob: 从 domain_assets 获取失败: %v", err)
				return nil, err
			}
			for _, asset := range assets {
				topDomains = append(topDomains, asset.TopDomain)
			}
			if len(topDomains) > 0 {
				query := elastic.NewBoolQuery().
					Must(
						elastic.NewTermQuery("user_id", p.UserID),
						elastic.NewTermsQuery("flag", p.Flag),
						elastic.NewTermsQuery("domain", topDomains),
					)
				results, err := es_utils.All[recommend_result.RecommendResult](500, query, nil, "id", "subdomain")
				if err != nil {
					log.Errorf("TableAssetsDomainsSyncJob: 从 recommend_result 获取失败: %v", err)
					return nil, err
				}
				for _, result := range results {
					domainAll = append(domainAll, result.Subdomain)
				}
				domainAll = utils.ListDistinctNonZero(domainAll)
			}
		} else {
			clueDomainArr, err := clues.NewCluer().ListAllContent(
				clues.WithGroupIDAndTypes(p.GroupID[0], []int{clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN}),
				mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
				mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
			)
			if err != nil {
				log.Errorf("TableAssetsDomainsSyncJob: 获取线索域名失败: %v", err)
				return nil, err
			}
			if len(clueDomainArr) > 0 {
				domainAll = append(domainAll, clueDomainArr...)
				domainAll = utils.ListDistinctNonZero(domainAll)
			}
		}
	// 域名爆破或者验证任务
	case domain_assets.DOMAIN_TASK:
		log.Infof("TableAssetsDomainsSyncJob: 从 DOMAIN_TASK 获取域名, 任务ID: %d", p.DomainTaskID)
		if p.DomainTaskID == 0 {
			log.Infof("TableAssetsDomainsSyncJob: 域名爆破或者验证任务ID为0，不进行域名过滤")
			return nil, nil
		}

		// 获取任务信息
		taskModel := domain_task.NewDomainTaskModel()
		task, err := taskModel.First(domain_task.WithID(p.DomainTaskID))
		if err != nil || task.Id <= 0 {
			log.Errorf("TableAssetsDomainsSyncJob: 未找到域名任务: %v", err)
			return nil, err
		}

		domainTaskResultAll, err := domain_task_result.NewResulter().ListAll([]string{"domain"},
			mysql.WithColumnValue("task_id", p.DomainTaskID),
			mysql.WithColumnValue("user_id", p.UserID),
		)
		if err != nil {
			log.Errorf("TableAssetsDomainsSyncJob: 获取域名任务结果失败: %v", err)
			return nil, err
		}
		for _, result := range domainTaskResultAll {
			domainAll = append(domainAll, result.Domain)
		}
		if task.DomainList.Valid {
			domainList := make([]string, 0)
			if err := json.Unmarshal([]byte(task.DomainList.String), &domainList); err != nil {
				log.Errorf("TableAssetsDomainsSyncJob: 反序列化域名列表失败: %v", err)
				return nil, err
			}
			domainAll = append(domainAll, domainList...)
		}

		if task.Modify == domain_task.BRUST_TYPE {
			// 爆破模式
			log.Infof("TableAssetsDomainsSyncJob: 域名爆破模式")
		} else {
			// 验证模式
			log.Infof("TableAssetsDomainsSyncJob: 域名验证模式")
		}

		if task.DetectTaskId > 0 {
			p.BrustDomainDetectTaskID = task.DetectTaskId
		}
	// 域名导入
	case domain_assets.DOMAIN_IMPORT:
		log.Infof("TableAssetsDomainsSyncJob: 从 DOMAIN_IMPORT 获取域名, 用户ID: %d", p.UserID)
		domainAll = p.ImportDomains
		// 域名扫描
	case domain_assets.DOMAIN_SCAN:
		log.Infof("TableAssetsDomainsSyncJob: 从 DOMAIN_SCAN 获取域名, 用户ID: %d", p.UserID)
		domainAll = p.ImportDomains
	}

	// 去重
	result := utils.ListDistinctNonZero(domainAll)
	return result, nil
}

// getAllDomain 获取扫描任务的域名
func getAllDomain(p *asyncq.TableAssetsDomainsSyncJobPayload, domainAll []string) ([]string, error) {
	scanTask, err := scan_task.NewScanTasksModel().FindByIdAndUserId(p.TaskID[0], p.UserID)
	if err != nil {
		log.Errorf("TableAssetsDomainsSyncJob: 未找到扫描任务: %v", err)
		return nil, err
	}
	if scanTask.DetectAssetsTasksId > 0 {
		log.Infof("TableAssetsDomainsSyncJob: 扫描任务的检测资产任务ID: %d", scanTask.DetectAssetsTasksId)
		detectTask, err := detect_assets_tasks.NewModel().First(detect_assets_tasks.WithID(uint64(scanTask.DetectAssetsTasksId)), detect_assets_tasks.WithUserID(p.UserID))
		if err != nil {
			log.Errorf("TableAssetsDomainsSyncJob: 未找到检测资产任务: %v", err)
			return nil, err
		}
		if detectTask.GroupId > 0 {
			clueDomainArr, err := clues.NewCluer().ListAllContent(
				clues.WithGroupIDAndTypes(detectTask.GroupId, []int{clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN}),
				mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
				mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
			)
			if err != nil {
				log.Errorf("TableAssetsDomainsSyncJob: 获取线索域名失败: %v", err)
				return nil, err
			}
			if len(clueDomainArr) > 0 {
				bakDomainAll := make([]string, 0)
				for _, domain := range domainAll {
					topDomain := utils.GetTopDomain(domain)
					if domain == topDomain {
						if slices.Contains(clueDomainArr, domain) {
							bakDomainAll = append(bakDomainAll, domain)
						}
					} else {
						if slices.Contains(clueDomainArr, domain) || slices.Contains(clueDomainArr, topDomain) {
							bakDomainAll = append(bakDomainAll, domain)
						} else {
							for _, clueDomain := range clueDomainArr {
								if strings.HasSuffix(domain, clueDomain) || strings.HasSuffix(clueDomain, domain) {
									bakDomainAll = append(bakDomainAll, domain)
									break
								}
							}
						}
					}
				}
				domainAll = bakDomainAll
			} else {
				log.Infof("TableAssetsDomainsSyncJob: 未找到线索域名, 不进行域名过滤")
				return nil, nil
			}
		} else {
			log.Infof("TableAssetsDomainsSyncJob: 当前台账域名提取的扫描任务没找到线索分组数据，不进行域名过滤")
			return nil, nil
		}
	} else {
		log.Infof("TableAssetsDomainsSyncJob: 当前台账域名提取的扫描任务没查到对应测绘任务，不进行域名过滤")
		return nil, nil
	}
	return domainAll, nil
}

// upsertDomainAsset 封装域名资产的新增或更新逻辑
func upsertDomainAsset(ctx context.Context, p *asyncq.TableAssetsDomainsSyncJobPayload, asset *domain_assets.DomainAssets, aaaRecord *AaaRecords) error {
	assetModel := domain_assets.NewModel()

	// 检查域名是否已存在
	existingAsset, err := assetModel.First(
		mysql.WithColumnValue("user_id", p.UserID),
		mysql.WithColumnValue("domain", asset.Domain),
	)

	if err != nil && err != gorm.ErrRecordNotFound {
		log.Errorf("upsertDomainAsset: 检查域名是否已存在失败: %v", err)
		return err
	}

	if existingAsset != nil {
		// 更新现有记录
		asset.UpdatedAt = time.Now()
		// 更新 source 字段
		if existingAsset.Source != "" {
			var sourceArr []string
			if err := json.Unmarshal([]byte(existingAsset.Source), &sourceArr); err == nil {
				sourceArr = append(sourceArr, asset.Source)
				sourceArr = utils.ListDistinct(sourceArr)
				asset.Source = utils.AnyToStr(sourceArr)
			}
		} else {
			asset.Source = utils.AnyToStr([]string{asset.Source})
		}

		// 更新 detect_task_id 字段
		if p.DetectTaskID > 0 {
			var detectTaskIds []uint64
			if existingAsset.DetectTaskId != "" {
				if err := json.Unmarshal([]byte(existingAsset.DetectTaskId), &detectTaskIds); err == nil {
					detectTaskIds = append(detectTaskIds, p.DetectTaskID)
					detectTaskIds = utils.ListDistinct(detectTaskIds)
				}
				if len(detectTaskIds) > 0 {
					asset.DetectTaskId = utils.AnyToStr(detectTaskIds)
				}
			} else {
				asset.DetectTaskId = utils.AnyToStr([]uint64{p.DetectTaskID})
			}
		} else if p.BrustDomainDetectTaskID > 0 {
			var detectTaskIds []uint64
			if existingAsset.DetectTaskId != "" {
				if err := json.Unmarshal([]byte(existingAsset.DetectTaskId), &detectTaskIds); err == nil {
					detectTaskIds = append(detectTaskIds, p.BrustDomainDetectTaskID)
					detectTaskIds = utils.ListDistinct(detectTaskIds)
				}
				if len(detectTaskIds) > 0 {
					asset.DetectTaskId = utils.AnyToStr(detectTaskIds)
				}
			} else {
				asset.DetectTaskId = utils.AnyToStr([]uint64{p.BrustDomainDetectTaskID})
			}
		}

		// 更新组织架构相关字段
		if p.OrganizationDiscoverTaskID > 0 {
			var orgDiscoverTaskIds []uint64
			if existingAsset.OrganizationDiscoverTaskId != "" {
				if err := json.Unmarshal([]byte(existingAsset.OrganizationDiscoverTaskId), &orgDiscoverTaskIds); err == nil {
					orgDiscoverTaskIds = append(orgDiscoverTaskIds, p.OrganizationDiscoverTaskID)
					orgDiscoverTaskIds = utils.ListDistinct(orgDiscoverTaskIds)
				}
				if len(orgDiscoverTaskIds) > 0 {
					asset.OrganizationDiscoverTaskId = utils.AnyToStr(orgDiscoverTaskIds)
				}
			} else {
				asset.OrganizationDiscoverTaskId = utils.AnyToStr([]uint64{p.OrganizationDiscoverTaskID})
			}

			// 更新组织ID
			if p.OrganizationID > 0 {
				var orgIds []uint64
				if existingAsset.OrganizationId != "" {
					if err := json.Unmarshal([]byte(existingAsset.OrganizationId), &orgIds); err == nil {
						orgIds = append(orgIds, p.OrganizationID)
						orgIds = utils.ListDistinct(orgIds)
					}
					if len(orgIds) > 0 {
						asset.OrganizationId = utils.AnyToStr(orgIds)
					}
				} else {
					asset.OrganizationId = utils.AnyToStr([]uint64{p.OrganizationID})
				}
			}
		}

		updateData := map[string]any{
			"f_domain":                      asset.FDomain,
			"top_domain":                    asset.TopDomain,
			"depth":                         asset.Depth,
			"type":                          asset.Type,
			"status":                        asset.Status,
			"company_name":                  asset.CompanyName,
			"dns_a":                         asset.DnsA,
			"dns_aaaa":                      asset.DnsAaaa,
			"cname":                         asset.Cname,
			"status_code":                   asset.StatusCode,
			"title":                         asset.Title,
			"icp":                           asset.Icp,
			"open_parse":                    asset.OpenParse,
			"updated_at":                    time.Now(),
			"source":                        asset.Source,
			"detect_task_id":                asset.DetectTaskId,
			"organization_discover_task_id": asset.OrganizationDiscoverTaskId,
			"organization_id":               asset.OrganizationId,
		}
		if err := assetModel.UpdateMap(existingAsset.Id, updateData); err != nil {
			log.Errorf("upsertDomainAsset: 更新域名资产失败: %v", err)
			return err
		}

		log.Infof("upsertDomainAsset: 更新域名资产成功, 域名: %s, 用户ID: %d", asset.Domain, p.UserID)
	} else {
		// 创建新记录
		asset.Source = utils.AnyToStr([]string{asset.Source})

		if p.DetectTaskID > 0 {
			asset.DetectTaskId = utils.AnyToStr([]uint64{p.DetectTaskID})
		} else if p.BrustDomainDetectTaskID > 0 {
			asset.DetectTaskId = utils.AnyToStr([]int64{int64(p.BrustDomainDetectTaskID)})
		}

		if p.OrganizationDiscoverTaskID > 0 {
			asset.OrganizationDiscoverTaskId = utils.AnyToStr([]uint64{p.OrganizationDiscoverTaskID})

			// 设置组织ID
			if p.OrganizationID > 0 {
				asset.OrganizationId = utils.AnyToStr([]uint64{p.OrganizationID})
			}
		}

		if err := assetModel.Create([]*domain_assets.DomainAssets{asset}); err != nil {
			log.Errorf("upsertDomainAsset: 创建域名资产失败: %v", err)
			return err
		}

		log.Infof("upsertDomainAsset: 创建域名资产成功, 域名: %s, 用户ID: %d", asset.Domain, p.UserID)
	}

	// 创建域名历史记录
	if err := createDomainHistory(ctx, p, asset, aaaRecord); err != nil {
		log.Warnf("upsertDomainAsset: 创建域名历史记录失败: %v", err)
		// 历史记录失败不影响主流程
	}

	return nil
}

// createDomainHistory 创建域名历史记录
func createDomainHistory(ctx context.Context, p *asyncq.TableAssetsDomainsSyncJobPayload, asset *domain_assets.DomainAssets, aaaRecord *AaaRecords) error {
	historyModel := domain_historys.NewModel()

	// 准备历史记录数据
	history := &domain_historys.DomainHistory{
		UserId:    p.UserID,
		CompanyId: asset.CompanyId,
		Domain:    asset.Domain,
		Cname:     asset.Cname,
	}

	// 设置IP信息
	if aaaRecord != nil && len(aaaRecord.Ip) > 0 {
		history.Ips = strings.Join(aaaRecord.Ip, ",")
	} else if asset.Status == domain_assets.NO_ANALYAIS {
		history.Ips = "不可解析"
	}

	// 检查是否已存在相同的历史记录，如果不存在则创建
	return historyModel.CreateIfNotExists(history)
}

// sendProgress 发送进度
func sendProgress(ctx context.Context, p *asyncq.TableAssetsDomainsSyncJobPayload, progress float64) error {
	if p.From == domain_assets.DOMAIN_TASK {
		taskModel := domain_task.NewDomainTaskModel()
		task, err := taskModel.First(domain_task.WithID(p.DomainTaskID))
		if err != nil || task.Id <= 0 {
			log.Errorf("TableAssetsDomainsSyncJob: 未找到域名任务: %v", err)
			return err
		}
		if progress > 100 {
			progress = 99.9
		}
		status := task.Status
		if progress == 100 {
			status = domain_task.StatusFinished
		}
		// 发送进度
		websocket_message.PublishSuccess(int64(p.UserID), "domain_tasks", map[string]any{"progress": progress, "status": status})
		// 更新数据库中的任务进度
		if err := taskModel.UpdateAny(task.Id, map[string]any{"progress": progress, "status": status}); err != nil {
			log.Errorf("TableAssetsDomainsSyncJob: 更新域名任务进度失败: %v", err)
			return err
		}
	}
	return nil
}
