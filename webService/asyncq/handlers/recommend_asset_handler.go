package handlers

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/middleware/mysql/domain_assets"
	asyncq "micro-service/pkg/queue_helper"
	"net"
	"slices"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	pb "micro-service/coreService/proto"
	es_utils "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/clues_groups"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/organization_discover_task"
	"micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/cfg"
	clueUtil "micro-service/pkg/clues"
	"micro-service/pkg/dns"
	"micro-service/pkg/domain"
	"micro-service/pkg/fofa"
	"micro-service/pkg/github"
	"micro-service/pkg/icp"
	"micro-service/pkg/log"
	"micro-service/pkg/network"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"
)

// HunterQueryService hunter查询服务接口
type HunterQueryService interface {
	Query(ctx context.Context, queryStr string, startDate string) ([]*pb.Asset, error)
}

// DefaultHunterQueryService 默认hunter查询服务实现
type DefaultHunterQueryService struct{}

func (d *DefaultHunterQueryService) Query(ctx context.Context, queryStr string, startDate string) ([]*pb.Asset, error) {
	return fofa.HunterQuery(ctx, queryStr, startDate)
}

type (
	RecommendAssetTaskConfig struct {
		TaskName                   string `json:"task_name" validate:"required"`
		TaskId                     string `json:"task_id" validate:"required"`
		UserId                     int    `json:"user_id" validate:"required"`
		ClueIds                    []int  `json:"clue_ids" validate:"required"` // 线索id列表
		CronId                     int    `json:"cron_id" validate:"omitempty"`
		OpId                       int    `json:"op_id" validate:"omitempty"`
		ClueArr                    []int  `json:"clue_arr" validate:"omitempty"`
		ExpendId                   int    `json:"expend_id" validate:"omitempty"` // 单位资产测绘任务id
		IsFromRecord               bool   `json:"is_from_record" validate:"omitempty" default:"false"`
		CompanyId                  int    `json:"company_id" validate:"required"`
		AllDomainClueArr           []int  `json:"all_domain_clue_arr" validate:"omitempty"`
		ClueSubdomainArr           []int  `json:"clue_subdomain_arr" validate:"omitempty"`
		OrganizationDiscoverTaskId int    `json:"organization_discover_task_id" validate:"omitempty"` // 组织架构测绘任务的id
	}
	RecommendAssetTaskState struct {
		// 运行时状态
		startAt           time.Time
		total             int64
		currentNumber     int64
		recommendRecord   *recommend_record.RecommendRecord
		recommendData     SafeRecommendData
		detectAssetsTask  *detect_assets_tasks.DetectAssetsTask
		mutex             sync.RWMutex
		hasSubdomainClue  bool
		needHunter        bool
		needDnsCheck      bool
		needAutoExpendIp  bool
		clueList          []*clues.Clue
		icpQuery          icp.ICPQuery
		ipSubdomainLimits sync.Map
	}
	// 资产推荐配置
	RecommendAssetHandler struct {
		config         *RecommendAssetTaskConfig
		state          *RecommendAssetTaskState
		hunterQuerySvc HunterQueryService
	}

	AssetInfo struct {
		asset *pb.Asset
		clue  *clues.Clue
		// 资产来源,参考recommend_result.RecommendResult.AssetsSource
		source int
	}

	SafeRecommendData struct {
		data  sync.Map
		ipSet map[string]struct{}
	}
)

var storeLock sync.Mutex

func (srd *SafeRecommendData) Store(key string, value *recommend_result.RecommendResult) {
	srd.data.Store(key, value)
	storeLock.Lock()
	srd.ipSet[value.Ip] = struct{}{}
	storeLock.Unlock()
}

func (srd *SafeRecommendData) Load(key string) (*recommend_result.RecommendResult, bool) {
	actual, loaded := srd.data.Load(key)
	if !loaded {
		return nil, false
	}
	return actual.(*recommend_result.RecommendResult), loaded
}

// oneforall的数据来源匹配
var sourceMap = map[string]int{
	"VirusTotalQuery":        recommend_result.VirusTotalQuery,
	"CensysAPIQuery":         recommend_result.CensysAPIQuery,
	"CertSpotterQuery":       recommend_result.CertSpotterQuery,
	"CrtshQuery":             recommend_result.CrtshQuery,
	"GoogleQuery":            recommend_result.GoogleQuery,
	"AnubisQuery":            recommend_result.AnubisQuery,
	"BinaryEdgeAPIQuery":     recommend_result.BinaryEdgeAPIQuery,
	"CeBaiduQuery":           recommend_result.CeBaiduQuery,
	"ChinazQuery":            recommend_result.ChinazQuery,
	"ChinazAPIQuery":         recommend_result.ChinazAPIQuery,
	"CirclAPIQuery":          recommend_result.CirclAPIQuery,
	"CloudFlareAPIQuery":     recommend_result.CloudFlareAPIQuery,
	"DNSdbAPIQuery":          recommend_result.DNSdbAPIQuery,
	"DNSDumpsterQuery":       recommend_result.DNSDumpsterQuery,
	"HackerTargetQuery":      recommend_result.HackerTargetQuery,
	"IP138Query":             recommend_result.IP138Query,
	"IPv4InfoAPIQuery":       recommend_result.IPv4InfoAPIQuery,
	"QianXunQuery":           recommend_result.QianXunQuery,
	"GithubAPISearch":        recommend_result.GithubAPISearch,
	"Altdns":                 recommend_result.Altdns,
	"SecurityTrailsAPIQuery": recommend_result.SecurityTrailsAPIQuery,
	"RapidDNSQuery":          recommend_result.RapidDNSQuery,
	"SiteDossierQuery":       recommend_result.SiteDossierQuery,
}

// 推荐资产配置选项
type RecommendAssetHandlerOption func(*RecommendAssetHandler)

func WithClueArr(clueArr []int) RecommendAssetHandlerOption {
	return func(h *RecommendAssetHandler) {
		h.config.ClueArr = clueArr
	}
}

func WithHunterQueryService(service HunterQueryService) RecommendAssetHandlerOption {
	return func(h *RecommendAssetHandler) {
		h.hunterQuerySvc = service
	}
}

func NewRecommendAssetHandler(taskName string, taskId string, detectAssetsTaskId int, userId int, clueIds []int, cronId int, organizationDiscoverTaskId int, opts ...RecommendAssetHandlerOption) *RecommendAssetHandler {
	h := &RecommendAssetHandler{
		config: &RecommendAssetTaskConfig{
			TaskName:                   taskName,
			TaskId:                     taskId,
			ExpendId:                   detectAssetsTaskId,
			UserId:                     userId,
			ClueIds:                    clueIds,
			CronId:                     cronId,
			OrganizationDiscoverTaskId: organizationDiscoverTaskId,
		},
		state: &RecommendAssetTaskState{
			recommendData: SafeRecommendData{
				data:  sync.Map{},
				ipSet: make(map[string]struct{}),
			},
			icpQuery:          icp.NewICPQuery(redis.GetClient()),
			ipSubdomainLimits: sync.Map{},
		},
		// 设置默认依赖
		hunterQuerySvc: &DefaultHunterQueryService{},
	}
	for _, opt := range opts {
		opt(h)
	}

	return h
}

// preloadData 预加载数据
func (h *RecommendAssetHandler) preloadData() error {
	// 预加载线索
	if err := h.preloadClues(); err != nil {
		log.Error("[RecommendAssetHandler]", "预加载线索失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	// 检查是否有子域名线索
	h.checkSubdomainClue()
	// 查询单位资产测绘任务
	h.getDetectAssetsTask()

	return nil
}

// preloadClues 预加载线索
func (h *RecommendAssetHandler) preloadClues() error {
	clueModel := clues.NewCluer()
	clueList, err := clueModel.ListAll(
		mysql.WithWhere("user_id = ?", h.config.UserId),
		mysql.WithValuesIn("id", h.config.ClueIds),
	)
	if err != nil {
		log.Warn("[RecommendAssetHandler]", "预加载线索失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   err.Error(),
		})
		return err
	}
	h.state.clueList = clueList
	return nil
}

// filterCluesByType 根据线索类型过滤线索
func (h *RecommendAssetHandler) filterCluesByType(clueList []*clues.Clue, clueType int) []*clues.Clue {
	filteredClues := make([]*clues.Clue, 0, len(clueList))
	for _, clue := range clueList {
		if clue.Type == clueType {
			filteredClues = append(filteredClues, clue)
		}
	}
	return filteredClues
}

// checkSubdomainClue 检查是否有子域名线索
func (h *RecommendAssetHandler) checkSubdomainClue() {
	clueList := h.state.clueList
	for _, clue := range clueList {
		if clue.Type == clues.TYPE_SUBDOMAIN {
			h.state.hasSubdomainClue = true
			return
		}
	}
	h.state.hasSubdomainClue = false
}

// getDetectAssetsTask 查询单位资产测绘任务
func (h *RecommendAssetHandler) getDetectAssetsTask() error {
	// 初始化单位资产测绘任务
	h.state.detectAssetsTask = nil
	// 如果存在扩展任务，则查找detectAssetsTasks表
	if h.config.ExpendId > 0 {
		// 查询单位资产测绘任务
		detectAssetsTasksModel := detect_assets_tasks.NewModel()
		detectAssetsTask, err := detectAssetsTasksModel.First(mysql.WithId(uint64(h.config.ExpendId)))
		if err != nil {
			return errors.New("查询单位资产测绘任务失败," + err.Error())
		}
		if detectAssetsTask == nil {
			log.Info("RecommendAssetJobHandler", "单位资产测绘任务不存在,不进行资产推荐", map[string]interface{}{
				"task_id": h.config.ExpendId,
			})
			return nil
		}
		h.state.detectAssetsTask = detectAssetsTask
		return nil
	}
	return nil
}

func (h *RecommendAssetHandler) Start() error {
	log.Info("[RecommendAssetHandler]", "开始推荐任务", map[string]interface{}{
		"task_id": h.config.TaskId,
	})

	// 预加载数据
	if err := h.preloadData(); err != nil {
		return err
	}

	recommendRecordModel := recommend_record.NewRecommendRecordModel()
	// 查询推荐资产任务
	recommendRecord, err := recommendRecordModel.FindByID(h.config.TaskId)
	if err != nil {
		log.Error("RecommendAssetJobHandler", "查询推荐资产任务失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   err.Error(),
		})
		return err
	}
	if recommendRecord == nil {
		log.Error("RecommendAssetJobHandler", "推荐资产任务不存在", map[string]interface{}{
			"task_id": h.config.TaskId,
		})
		return errors.New("推荐资产任务不存在")
	}

	// 保存推荐资产任务
	h.state.recommendRecord = recommendRecord

	// 更新推荐资产任务状态为运行中
	err = recommendRecordModel.UpdateAny(h.config.TaskId, map[string]any{
		"status":   recommend_record.StatusDoing,
		"node":     cfg.LoadCommon().Node,
		"start_at": h.state.startAt.Format("2006-01-02 15:04:05"),
	})
	if err != nil {
		log.Error("RecommendAssetJobHandler", "更新推荐资产任务状态失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	// 发送进度
	h.sendWs(5, recommend_record.StatusDoing)

	var processErr error
	// 判断是单位资产推荐任务还是普通推荐任务
	if len(h.config.ClueArr) > 0 {
		log.Info("[RecommendAssetHandler]", "执行单位资产测绘推荐流程", map[string]interface{}{
			"task_id": h.config.TaskId,
		})
		processErr = h.detectRecommend()
	} else {
		log.Info("[RecommendAssetHandler]", "执行普通资产推荐流程", map[string]interface{}{
			"task_id": h.config.TaskId,
		})
		processErr = h.commonRecommend()
	}

	if processErr != nil {
		log.Error("[RecommendAssetHandler]", "推荐任务处理失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   processErr.Error(),
		})
		// 更新任务状态为失败
		_ = recommendRecordModel.UpdateAny(h.config.TaskId, map[string]any{
			"status": recommend_record.StatusFailed,
		})
		return processErr
	}

	// 发送进度
	h.sendWs(95, recommend_record.StatusDoing)

	// 推荐任务完成处理
	return h.recommendComplete()
}

// 单位资产测绘资产推荐流程
func (h *RecommendAssetHandler) detectRecommend() error {
	// 线索类型；0：根域；1：证书；2：ICP；3：LOGO；
	// 关键词线索推荐资产标识
	skipKeywordClue := true
	// 如果存在扩展任务，则查找detectAssetsTasks表
	if h.state.detectAssetsTask != nil {
		// 单位测绘的跳过关键词线索推荐资产，但是云端推荐不跳过
		if h.state.detectAssetsTask.ExpandSource != detect_assets_tasks.ExpandSourceRecommend {
			skipKeywordClue = false
		}
		h.state.needHunter = h.state.detectAssetsTask.IsNeedHunter == 1
		h.state.needDnsCheck = h.state.detectAssetsTask.IsNeedDnschecker == 1
		h.state.needAutoExpendIp = h.state.detectAssetsTask.IsAutoExpendIp == 1
		log.Infof("单位资产测绘任务配置, 是否需要hunter: %v, 是否需要DNS检测: %v, 是否需要自动扩展IP: %v", h.state.needHunter, h.state.needDnsCheck, h.state.needAutoExpendIp)
	}
	for i := 0; i < 7; i++ {
		// 发送进度
		h.sendWs(float64(i+1)/8*90, recommend_record.StatusDoing)

		if skipKeywordClue {
			if i == 4 {
				log.Info("RecommendAssetJobHandler", "单位测绘的跳过关键词线索推荐资产", map[string]interface{}{
					"task_id": h.config.ExpendId,
				})
				continue
			}
		}

		// 获取线索信息
		_, _, cluesData, lastClue, err := h.getCluesInfo(i, skipKeywordClue)
		if err != nil {
			log.Error("RecommendAssetJobHandler", fmt.Sprintf("获取线索信息失败，线索类型：%d", i), map[string]interface{}{
				"task_id":   h.config.TaskId,
				"clue_type": i,
				"error":     err.Error(),
			})
			continue
		}
		if len(cluesData) == 0 {
			log.Info("RecommendAssetJobHandler", fmt.Sprintf("线索类型：%d，没有线索，跳过", i), map[string]interface{}{
				"task_id":   h.config.TaskId,
				"clue_type": i,
			})
			continue
		}

		// 计算当前类型线索的数量，累加到总数
		// 这是FOFA调用，不要使用goroutine，否则容易引发限流
		clueCount := h.getCluesCount(cluesData, true)
		h.state.total += clueCount

		log.Infof("当前线索类型：%d，线索数量：%d，FOFA数量：%d", i, len(cluesData), clueCount)

		// 处理线索
		if err := h.handleClues(cluesData, lastClue, i); err != nil {
			log.Error("RecommendAssetJobHandler", "处理线索失败", map[string]interface{}{
				"task_id":   h.config.TaskId,
				"clue_type": i,
				"error":     err.Error(),
			})
		}
		log.Debug("RecommendAssetJobHandler", "处理线索完成", map[string]interface{}{
			"task_id":   h.config.TaskId,
			"clue_type": i,
		})
	}
	return nil
}

// getCluesInfo 获取线索信息
// clueType 线索类型
// skipKeywordClue 是否跳过关键词线索
// 返回值：
// groupId 组id
// groupName 组名
// cluesData 线索数据
// lastClue 最后一个线索
// error 错误信息
func (h *RecommendAssetHandler) getCluesInfo(clueType int, skipKeywordClue bool) (uint64, string, []*clues.Clue, *clues.Clue, error) {
	allClues := h.state.clueList
	if clueType >= 0 {
		allClues = h.filterCluesByType(allClues, clueType)
	}
	if len(allClues) == 0 {
		return 0, "", nil, nil, nil
	}
	groupId := uint64(0)
	groupName := ""
	if firstClue := allClues[0]; firstClue.GroupId != 0 {
		groupId = firstClue.GroupId
		// 查询组名
		group, err := clues_groups.NewCluesGrouper().First(mysql.WithId(groupId))
		if err != nil {
			// 组名查询失败不影响主流程，记录日志即可
			log.Warn("RecommendAssetJobHandler", "查询线索组名失败", map[string]interface{}{
				"task_id": h.config.TaskId,
				"error":   err.Error(),
			})
			groupName = ""
		} else {
			groupName = group.Name
		}
	}
	// 优化：从已查询的数据中获取最后一个线索，而不是重新查询数据库
	lastClue := findLastClue(allClues, skipKeywordClue)

	return groupId, groupName, allClues, lastClue, nil
}

// findLastClue 从已查询的数据中找到最后一个符合条件的线索
// 优化：避免重新查询数据库，直接在内存中处理
func findLastClue(allClues []*clues.Clue, skipKeywordClue bool) *clues.Clue {
	if len(allClues) == 0 {
		return nil
	}

	if skipKeywordClue {
		// 单位测绘：需要排除关键词类型，从后往前找第一个非关键词类型的线索
		for i := len(allClues) - 1; i >= 0; i-- {
			if allClues[i].Type != clues.TYPE_KEYWORD {
				return allClues[i]
			}
		}
		// 如果所有线索都是关键词类型，返回空
		return nil
	} else {
		// 云端推荐：直接返回最后一个线索
		lastClue := allClues[len(allClues)-1]
		return lastClue
	}
}

// handleClues 处理线索
func (h *RecommendAssetHandler) handleClues(cluesData []*clues.Clue, lastClue *clues.Clue, clueType int) error {
	resultChan := make(chan *AssetInfo, 100)
	errorChan := make(chan error, 1)
	processChan := make(chan bool, 1)
	wg := sync.WaitGroup{}

	// 创建可取消的context
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel() // 确保资源清理

	// 启动goroutine处理资产
	go func() {
		defer func() {
			cancel()
		}()
		err := h.processAsset(resultChan, errorChan, processChan)
		if err != nil {
			log.Error("RecommendAssetJobHandler", "processAsset失败", map[string]interface{}{
				"task_id": h.config.TaskId,
				"error":   err.Error(),
			})
		}
	}()

Loop:
	for _, clueInfo := range cluesData {
		// 检查context是否已取消
		select {
		case <-ctx.Done():
			log.Warn("RecommendAssetJobHandler-handleClues", "processAsset已退出，停止处理后续线索")
			break Loop // 退出循环
		default:
		}

		log.Infof("开始处理线索,线索类型: %d, 线索ID: %d, 线索内容: %s", clueType, clueInfo.Id, clueInfo.Content)
		wg.Add(4)
		// 普通推荐任务，补充企业名称
		if len(h.config.ClueArr) == 0 {
			companyName, err := getExtraCompanyName(clueInfo, clueType, h.config.TaskId, h.state.icpQuery)
			if err != nil {
				log.Error("RecommendAssetJobHandler", "获取额外企业名称失败", map[string]interface{}{
					"task_id":   h.config.TaskId,
					"clue_type": clueType,
					"content":   clueInfo.Content,
					"error":     err.Error(),
				})
			} else {
				clueInfo.ClueCompanyName = companyName
			}
		}

		// 获取OneForAll查询结果
		go func() {
			defer wg.Done()
			if clueInfo.Type == clues.TYPE_DOMAIN {
				if !cfg.IsLocalClient() {
					log.Infof("开始获取OneForAll查询结果,线索ID: %d, 线索内容: %s", clueInfo.Id, clueInfo.Content)
					if err := h.getOneForAllResult(ctx, clueInfo, clueInfo.Content); err != nil {
						log.Error("RecommendAssetJobHandler", "获取OneForAll查询结果失败", map[string]interface{}{
							"task_id":   h.config.TaskId,
							"clue_type": clueType,
							"error":     err.Error(),
						})
					}
				}
			}
		}()

		// 获取DNS检测结果
		go func() {
			defer wg.Done()
			if !cfg.IsLocalClient() {
				// 判断是否是最后一个线索
				isLastClue := h.isLastRecommendClue(clueInfo, lastClue)
				if isLastClue && h.state.needDnsCheck {
					log.Infof("开始获取DNS检测结果,线索ID: %d, 线索内容: %s", clueInfo.Id, clueInfo.Content)
					h.getDnsCheckerQueryResult(ctx, clueInfo, resultChan, errorChan)
					log.Infof("获取DNS检测结果完成,线索ID: %d, 线索内容: %s", clueInfo.Id, clueInfo.Content)
				}
			}
		}()

		// 获取hunter查询结果
		go func() {
			defer wg.Done()
			if !cfg.IsLocalClient() {
				// 获取hunter查询字符串
				var hunterQueryStr string
				if clueInfo.Type == clues.TYPE_DOMAIN || clueInfo.Type == clues.TYPE_SUBDOMAIN || clueInfo.Type == clues.TYPE_IP {
					hunterQueryStr = clueUtil.ParseHunterQueryStr([]*clues.Clue{clueInfo})
				}
				if hunterQueryStr != "" {
					log.Infof("开始获取hunter查询结果,线索ID: %d, 查询字符串: %s", clueInfo.Id, hunterQueryStr)
					h.getHunterQueryResult(ctx, cluesData, clueInfo, hunterQueryStr, resultChan, errorChan)
					log.Infof("获取hunter查询结果完成,线索ID: %d, 查询字符串: %s", clueInfo.Id, hunterQueryStr)
				}
			}
		}()

		// 获取FOFA查询结果
		go func() {
			defer wg.Done()

			// 判断是否是域名类型
			isDomain := clueType == clues.TYPE_DOMAIN || clueType == clues.TYPE_SUBDOMAIN
			// 获取FOFA查询字符串
			queryStr := clueUtil.ParseQueryStr([]*clues.Clue{clueInfo}, false, isDomain)
			log.Debug("RecommendAssetJobHandler", "处理线索", map[string]interface{}{
				"task_id":   h.config.TaskId,
				"clue_type": clueType,
				"query_str": queryStr,
			})
			// 获取FOFA查询结果（使用channel处理）
			// fofa接口限流严重，不使用独立goroutine处理
			log.Infof("开始获取FOFA查询结果,线索ID: %d, 查询字符串: %s", clueInfo.Id, queryStr)
			h.getFofaQueryResult(ctx, clueInfo, queryStr, resultChan, errorChan)
			log.Infof("获取FOFA查询结果完成,线索ID: %d, 查询字符串: %s", clueInfo.Id, queryStr)
		}()

		// 等待当前线索全部处理完成
		wg.Wait()
	}
	log.Infof("等待所有线索处理完成,线索类型: %d, 线索数量: %d", clueType, len(cluesData))
	close(resultChan)
	close(errorChan)
	<-processChan
	close(processChan)
	log.Infof("所有线索处理完成,线索类型: %d, 线索数量: %d", clueType, len(cluesData))
	return nil
}

var (
	reasonMapPool = sync.Pool{
		New: func() any {
			return make(map[int]bool, 10)
		},
	}
)

// canLimitSubdomainCount 判断ip+port的子域名是否超过30个，超过则返回true，否则返回false
// 参考PHP逻辑：只对HTTP/HTTPS协议进行限制，按不同子域名计数
func (h *RecommendAssetHandler) canLimitSubdomainCount(ip string, port string, protocol string, subdomain string) bool {
	// 只对HTTP/HTTPS协议且有子域名的情况进行限制
	if subdomain == "" || (protocol != "http" && protocol != "https") {
		return false
	}

	// 使用IPv6补全函数处理IP，与PHP逻辑保持一致
	completeIP := utils.CompleteIPV6(ip)
	key := fmt.Sprintf("%s-%s", completeIP, port) // 使用MD5前的格式

	if subdomains, ok := h.state.ipSubdomainLimits.Load(key); ok {
		subdomainMap := subdomains.(map[string]bool)
		if len(subdomainMap) >= 30 {
			log.Infof("[RecommendAssetHandler]子域名限制跳过 - IP: %s, Port: %s, Protocol: %s, Subdomain: %s, 当前子域名数量: %d",
				ip, port, protocol, subdomain, len(subdomainMap))
			return true
		} else {
			// 添加新的子域名
			subdomainMap[subdomain] = true
			h.state.ipSubdomainLimits.Store(key, subdomainMap)
			log.Debugf("[RecommendAssetHandler]子域名计数 - IP: %s, Port: %s, Protocol: %s, Subdomain: %s, 当前子域名数量: %d",
				ip, port, protocol, subdomain, len(subdomainMap))
			return false
		}
	} else {
		// 创建新的子域名映射
		subdomainMap := make(map[string]bool)
		subdomainMap[subdomain] = true
		h.state.ipSubdomainLimits.Store(key, subdomainMap)
		log.Debugf("[RecommendAssetHandler]子域名计数初始化 - IP: %s, Port: %s, Protocol: %s, Subdomain: %s, 当前子域名数量: 1",
			ip, port, protocol, subdomain)
		return false
	}
}

// canLimitSubdomainCountSimple 简化版本，用于没有协议和子域名信息的场景
func (h *RecommendAssetHandler) canLimitSubdomainCountSimple(ip string, port string) bool {
	// 对于没有详细信息的场景，使用简单计数，限制30个
	key := fmt.Sprintf("%s:%s", ip, port)
	if count, ok := h.state.ipSubdomainLimits.Load(key); ok {
		if count.(int) >= 30 {
			log.Debugf("[RecommendAssetHandler]简单计数限制跳过 - IP: %s, Port: %s, 当前计数: %d",
				ip, port, count.(int))
			return true
		} else {
			newCount := count.(int) + 1
			h.state.ipSubdomainLimits.Store(key, newCount)
			log.Debugf("[RecommendAssetHandler]简单计数 - IP: %s, Port: %s, 当前计数: %d",
				ip, port, newCount)
			return false
		}
	} else {
		h.state.ipSubdomainLimits.Store(key, 1)
		log.Debugf("[RecommendAssetHandler]简单计数初始化 - IP: %s, Port: %s, 当前计数: 1",
			ip, port)
		return false
	}
}

// processAsset 处理资产数据
func (h *RecommendAssetHandler) processAsset(resultChan chan *AssetInfo, errorChan chan error, processChan chan bool) error {
	// 等待所有数据处理完成
	wg := sync.WaitGroup{}
	lock := sync.Mutex{}
	semChan := make(chan bool, 10)

	// 处理结果
	for {
		select {
		case item, ok := <-resultChan:
			if !ok {
				// 等待所有数据处理完成
				wg.Wait()
				// 通知处理完成
				processChan <- true
				// channel已关闭，处理完成
				return nil
			}
			wg.Add(1)
			semChan <- true
			go func(result *pb.Asset, clue *clues.Clue, source int) {
				defer func() {
					<-semChan
					wg.Done()
				}()
				// 如果ip+port超过30条，则跳过（使用简化版本）
				if h.canLimitSubdomainCountSimple(result.GetIp(), result.GetPort()) {
					return
				}
				// 处理每个资产项
				recommentResult, err := h.convertPbAssetToRecommendResult(result, clue, source)
				if err != nil {
					log.Error("[RecommendAssetHandler]", "处理资产项失败", map[string]interface{}{
						"task_id": h.config.TaskId,
						"ip":      result.GetIp(),
						"error":   err.Error(),
					})
					return
				}
				id := recommentResult.Id

				lock.Lock()
				func(id string, recommentResult *recommend_result.RecommendResult, clue *clues.Clue) {
					defer func() {
						lock.Unlock()
						if r := recover(); r != nil {
							log.Error("[RecommendAssetHandler]", "处理资产项失败", map[string]interface{}{
								"task_id": h.config.TaskId,
								"ip":      result.GetIp(),
								"error":   r,
							})
						}
					}()
					// 处理重复数据
					if existingResult, exists := h.state.recommendData.Load(id); exists {
						// 从池中获取reasonIds，避免频繁创建map
						reasonIds := reasonMapPool.Get().(map[int]bool)
						defer func() {
							for k := range reasonIds {
								delete(reasonIds, k)
							}
							reasonMapPool.Put(reasonIds)
						}()
						// 合并reason
						for _, reason := range existingResult.Reason {
							reasonIds[int(reason.Id)] = true
						}
						if !reasonIds[int(clue.Id)] {
							existingResult.Reason = append(existingResult.Reason, recommend_result.RecommendReason{
								Id:              int(clue.Id),
								Content:         clue.Content,
								GroupId:         int(clue.GroupId),
								Source:          source,
								ClueCompanyName: clue.ClueCompanyName,
								Type:            clue.Type,
							})
						}

						// 合并 AllAssetsSource 字段，确保数据源不重复
						sourceExists := false
						for _, existingSource := range existingResult.AllAssetsSource {
							if existingSource == source {
								sourceExists = true
								break
							}
						}
						if !sourceExists {
							existingResult.AllAssetsSource = append(existingResult.AllAssetsSource, source)
						}

						// 更新数据（如果新数据更新时间更晚）
						if recommentResult.SourceUpdatedAt > existingResult.SourceUpdatedAt {
							// 保留合并后的 reason 和 AllAssetsSource
							recommentResult.Reason = existingResult.Reason
							recommentResult.AllAssetsSource = existingResult.AllAssetsSource
							h.state.recommendData.Store(id, recommentResult)
						} else {
							// 即使不更新主数据，也要保存合并后的 AllAssetsSource
							h.state.recommendData.Store(id, existingResult)
						}
					} else {
						h.state.recommendData.Store(id, recommentResult)
					}
				}(id, recommentResult, clue)

				// 更新当前处理数量
				atomic.AddInt64(&h.state.currentNumber, 1)
			}(item.asset, item.clue, item.source)

		case err := <-errorChan:
			if err != nil {
				log.Errorf("RecommendAssetJobHandler", "查询结果处理失败", map[string]interface{}{
					"task_id": h.config.TaskId,
					"error":   err.Error(),
				})

				// 如果需要失败后快速短路整个流程，就取消以下代码
				// // 等待所有数据处理完成
				// wg.Wait()
				// // 通知处理完成
				// processChan <- true
				// return fmt.Errorf("查询结果处理失败: %v", err)
			}
		case <-time.After(time.Minute * 5): // 整体超时保护
			// 等待所有数据处理完成
			wg.Wait()
			// 通知处理完成
			processChan <- true
			return fmt.Errorf("查询结果处理超时")
		}
	}
}

// 计算dateAfter
func calculateDateAfter(fofaRange int) string {
	if fofaRange > 0 {
		today := time.Now()

		var dateAfter time.Time
		switch fofaRange {
		case 1:
			dateAfter = today.AddDate(0, 0, -365) // 减去365天
		case 2:
			dateAfter = today.AddDate(0, 0, -180) // 减去180天
		case 3:
			dateAfter = today.AddDate(0, 0, -90) // 减去90天
		default:
			return ""
		}

		return dateAfter.Format("2006-01-02")
	}
	return ""
}

// getExtraCompanyName 获取额外企业名称
func getExtraCompanyName(clue *clues.Clue, clueType int, taskId string, icpQuery icp.ICPQuery) (string, error) {
	content := clue.Content
	if clue.Content == "" {
		return "", fmt.Errorf("线索内容为空")
	}

	// 如果企业名称已存在且不为空，直接返回
	if clue.ClueCompanyName != "" {
		return clue.ClueCompanyName, nil
	}

	switch clueType {
	case clues.TYPE_DOMAIN:
		// 查询域名信息
		resp, err := icpQuery.QueryDomain(context.Background(), content, false, false, false, uint64(0))
		if err != nil {
			log.Error("RecommendAssetJobHandler", "处理域名线索失败", map[string]interface{}{
				"task_id":   taskId,
				"clue_type": clueType,
				"error":     err.Error(),
			})
			return "", err
		}
		return utils.SafeString(resp.Info["company_name"]), nil
	case clues.TYPE_ICP:
		// 查询ICP信息
		resp, err := icpQuery.QueryICP(context.Background(), content, false, false, uint64(0))
		if err != nil {
			log.Error("RecommendAssetJobHandler", "处理ICP线索失败", map[string]interface{}{
				"task_id":   taskId,
				"clue_type": clueType,
				"error":     err.Error(),
			})
			return "", err
		}
		return utils.SafeString(resp.Info["company_name"]), nil
	case clues.TYPE_CERT:
		// 解析证书信息
		o, cn := clues.PluckCert(content)

		// 优先使用证书O字段中的中文企业名称
		if o != "" && utils.CheckIsChinese(o) {
			return o, nil
		} else if cn != "" {
			// 通过证书CN域名查询企业名称
			topDomain := clueUtil.GetTopDomain(cn)
			resp, err := icpQuery.QueryDomain(context.Background(), topDomain, false, false, false, uint64(0))
			if err != nil {
				log.Error("RecommendAssetJobHandler", "证书域名查询失败", err, map[string]interface{}{
					"task_id": taskId,
					"domain":  cn,
				})
				return "", err
			} else if resp == nil || resp.Info == nil {
				log.Warn("RecommendAssetJobHandler", "证书域名查询结果为空", map[string]interface{}{
					"task_id": taskId,
					"domain":  cn,
				})
				return "", nil
			} else {
				return utils.SafeString(resp.Info["company_name"]), nil
			}
		} else {
			log.Warn("RecommendAssetJobHandler", "证书解析失败，无有效信息", map[string]interface{}{
				"task_id":   taskId,
				"clue_type": clueType,
				"cert":      content,
				"o":         o,
				"cn":        cn,
			})
			return "", nil
		}
	}

	return "", nil
}

// commonRecommend 普通资产推荐流程
func (h *RecommendAssetHandler) commonRecommend() error {
	log.Infof("开始普通资产推荐流程,任务ID: %s", h.config.TaskId)

	// 获取线索信息
	_, _, cluesData, lastClue, err := h.getCluesInfo(-1, false)
	if err != nil {
		return fmt.Errorf("获取线索信息失败: %v", err)
	}

	// 计算线索推荐总数
	h.state.total = h.getCluesCount(cluesData, false)

	log.Infof("普通资产推荐任务ID: %s, 线索数量: %d", h.config.TaskId, h.state.total)

	h.state.currentNumber = 1
	// 处理线索
	return h.handleClues(cluesData, lastClue, -1)
}

// recommendComplete 推荐任务完成处理
func (h *RecommendAssetHandler) recommendComplete() error {
	log.Info("[RecommendAssetHandler]", "开始推荐任务完成处理", map[string]interface{}{
		"task_id": h.config.TaskId,
	})

	// 批量插入推荐结果到ES
	recommendData := make([]*recommend_result.RecommendResult, 0)
	h.state.recommendData.data.Range(func(key, value any) bool {
		recommendData = append(recommendData, value.(*recommend_result.RecommendResult))
		return true
	})
	if err := h.insertRecommendResults(recommendData); err != nil {
		log.Error("[RecommendAssetHandler]", "插入推荐结果失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	// fofa纯解析
	extraRecommendData, err := h.getFofaPureDnsData()
	if err != nil {
		log.Error("[RecommendAssetHandler]", "获取FOFA纯解析数据失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   err.Error(),
		})
	} else if extraRecommendData != nil {
		if err := h.insertRecommendResults(extraRecommendData); err != nil {
			log.Error("[RecommendAssetHandler]", "插入FOFA纯解析推荐结果失败", map[string]interface{}{
				"task_id": h.config.TaskId,
				"error":   err.Error(),
			})
		}
	}

	if h.state.needAutoExpendIp {
		// 统计推荐结果
		allIp := make([]string, 0)
		allCndIp := make([]string, 0)
		h.state.recommendData.data.Range(func(key, value any) bool {
			rd := value.(*recommend_result.RecommendResult)
			if !rd.IsIPv6 && rd.AssetsSource != recommend_result.DNSCHECKER {
				allIp = append(allIp, rd.Ip)
			}
			if rd.IsCDN && !rd.IsIPv6 {
				allCndIp = append(allCndIp, rd.Ip)
			}
			return true
		})
		allNoCndIp := ArrayDiff(allIp, allCndIp)
		if err := h.fofaFill(allNoCndIp); err != nil {
			log.Error("[RecommendAssetHandler]", "FOFA填充失败", map[string]interface{}{
				"task_id": h.config.TaskId,
				"error":   err.Error(),
			})
		}
	}

	// 更新推荐记录为完成状态
	recommendRecordModel := recommend_record.NewRecommendRecordModel()
	// 计算推荐结果数量（按IP去重）
	resultCount := len(h.state.recommendData.ipSet)
	// for _, v := range extraRecommendData {
	// 	if _, ok := h.state.recommendData.ipSet[v.Ip]; !ok {
	// 		resultCount++
	// 	}
	// }

	err = recommendRecordModel.UpdateAny(h.config.TaskId, map[string]any{
		"status":   recommend_record.StatusFinished,
		"progress": 100.0,
		"count":    resultCount,
		"end_at":   time.Now().Format("2006-01-02 15:04:05"),
		"confirm":  1, // CONFIRM_OK
	})
	if err != nil {
		log.Error("[RecommendAssetHandler]", "更新推荐记录状态失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   err.Error(),
		})
		return err
	}

	// 更新扩展任务进度
	if h.state.detectAssetsTask != nil {
		detectAssetsTaskModel := detect_assets_tasks.NewModel()
		err = detectAssetsTaskModel.UpdateAny(map[string]interface{}{
			"expend_progress": 100,
			"step_status":     detect_assets_tasks.StepStatusDone,
		}, mysql.WithWhere("id = ?", h.state.detectAssetsTask.ID))
		if err != nil {
			log.Error("[RecommendAssetHandler]", "更新扩展任务进度失败", map[string]interface{}{
				"task_id": h.config.TaskId,
				"error":   err.Error(),
			})
		}
	}

	// 更新组织架构测绘任务进度
	if h.config.OrganizationDiscoverTaskId > 0 {
		organizationDiscoverTaskModel := organization_discover_task.NewModel()
		err = organizationDiscoverTaskModel.UpdateAny(map[string]interface{}{
			"step_status": organization_discover_task.StepStatusFinish,
			"progress":    100,
		}, mysql.WithWhere("id = ?", h.config.OrganizationDiscoverTaskId))
		if err != nil {
			log.Error("[RecommendAssetHandler]", "更新组织架构测绘任务进度失败", map[string]interface{}{
				"task_id": h.config.TaskId,
				"error":   err.Error(),
			})
		}
	}

	// 发送WebSocket进度消息
	h.sendWs(100.0, recommend_record.StatusFinished)

	log.Info("[RecommendAssetHandler]", "推荐任务完成", map[string]interface{}{
		"task_id":      h.config.TaskId,
		"total":        h.state.total,
		"result_count": resultCount,
	})

	//同步域名数据
	// 对应PHP: TableAssetsDoaminsSync::dispatch($task->user_id, null, DomainAssets::ASSETS_RECOMMEND, null,null,[],$flag,$detect_assets_tasks_id,$organization_discover_task_id);
	log.Infof("RecommendAssetHandler: 准备下发PHP域名同步任务 - user_id=%d, task_id=%s,flag=%s, detect_task_id=%d, org_task_id=%d",
		h.config.UserId, h.config.TaskId, h.state.recommendRecord.Flag, h.state.detectAssetsTask.ID, h.config.OrganizationDiscoverTaskId)

	err = asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
		uint64(h.config.UserId),             // user_id ($task->user_id)
		nil,                                 // task_id (null)
		domain_assets.ASSETS_RECOMMEND,      // from (DomainAssets::ASSETS_RECOMMEND = 5)
		nil,                                 // groupId (null)
		nil,                                 // domain_task_id (null)
		[]string{},                          // import_domains ([])
		h.state.recommendRecord.Flag,        // flag ($flag)
		uint64(h.state.detectAssetsTask.ID), // detect_task_id ($detect_assets_tasks_id)
		uint64(h.config.OrganizationDiscoverTaskId), // organization_discover_task_id ($organization_discover_task_id)
	)
	if err != nil {
		log.Errorf("RecommendAssetHandler: 下发PHP域名同步任务失败 - user_id=%d, task_id=%s, error=%v",
			h.config.UserId, h.config.TaskId, err)
	} else {
		log.Infof("RecommendAssetHandler: 下发PHP域名同步任务成功 - user_id=%d, task_id=%s",
			h.config.UserId, h.config.TaskId)
	}

	return nil
}

func ArrayDiff(all, array1 []string) []string {
	// 用 map 加速查找
	cndIpMap := make(map[string]struct{})
	for _, ip := range array1 {
		cndIpMap[ip] = struct{}{}
	}

	var diff []string
	for _, ip := range all {
		if _, exists := cndIpMap[ip]; !exists {
			diff = append(diff, ip)
		}
	}

	return diff
}

func (h *RecommendAssetHandler) getDnsCheckerQueryResult(ctx context.Context, clue *clues.Clue, resultChan chan *AssetInfo, errorChan chan error) error {
	// 获取所有子域名
	subdomainList := make([]string, 0)
	existIpList := make([]string, 0)
	h.state.recommendData.data.Range(func(key, value any) bool {
		rd := value.(*recommend_result.RecommendResult)
		if rd.Subdomain != "" {
			subdomainList = append(subdomainList, rd.Subdomain)
		}
		if rd.Ip != "" {
			existIpList = append(existIpList, rd.Ip)
		}
		return true
	})
	// 子域名去重
	subdomainList = utils.ListDistinctNonZero(subdomainList)
	// 获取所有域名线索
	domainClueList := h.filterCluesByType(h.state.clueList, clues.TYPE_DOMAIN)
	// 线索域名
	domainList := make([]string, 0)
	for _, domainClue := range domainClueList {
		domainList = append(domainList, domainClue.Content)
	}
	domainList = utils.ListDistinctNonZero(domainList)
	if len(domainList) == 0 {
		return nil
	}

	// 子域名通道
	dnsCheckerDomainChan := make(chan string, len(subdomainList))
	// 结果处理最大并发控制
	resultSem := make(chan struct{}, 10)
	resultWg := sync.WaitGroup{}
	// 处理DNS检测结果
	go func() {
	domainLoop:
		for dnsCheckerDomain := range dnsCheckerDomainChan {
			// 检查context是否已取消
			select {
			case <-ctx.Done():
				log.Warn("RecommendAssetHandler-getDnsCheckerQueryResult", "processAsset已退出，停止处理后续线索")
				break domainLoop
			default:
			}

			resultWg.Add(1)
			resultSem <- struct{}{}
			go func(dnsCheckerDomain string) {
				defer func() {
					resultWg.Done()
					<-resultSem
				}()
				ips, err := dns.GetDnsCheckerResult(context.Background(), dnsCheckerDomain)
				if err != nil {
					log.Error("[RecommendAssetHandler]", "获取DNS检测结果失败", map[string]interface{}{
						"task_id": h.config.TaskId,
						"error":   err.Error(),
					})
				}
				// 获取DNS检测结果与已存在IP的差集
				diffIpList := ArrayDiff(ips, existIpList)
				for _, ip := range diffIpList {
					// 处理DNS检测结果
					asset := h.processDnsCheckerResult(ip, dnsCheckerDomain, existIpList)
					resultChan <- &AssetInfo{
						asset:  asset,
						clue:   clue,
						source: recommend_result.DNSCHECKER,
					}
				}
			}(dnsCheckerDomain)
		}
	}()

	// 并发处理子域名
	processWg := sync.WaitGroup{}
subdomainLoop:
	for _, subdomain := range subdomainList {
		// 检查context是否已取消
		select {
		case <-ctx.Done():
			log.Warn("RecommendAssetHandler-getDnsCheckerQueryResult", "processAsset已退出，停止处理后续线索")
			break subdomainLoop
		default:
		}

		processWg.Add(1)
		go func(subdomain string) {
			defer processWg.Done()
			// 判断是否是泛解析域名,获取顶级域名
			topDomain := utils.GetTopDomain(subdomain)
			// 生成缓存key
			cacheKey := cache.GetCacheKey("is_parse_domain", topDomain)
			// 获取泛解析域名缓存
			cacheValue, err := redis.GetClient().Get(context.Background(), cacheKey).Result()
			if err != nil {
				log.Error("[RecommendAssetHandler]", "获取泛解析域名缓存失败", map[string]interface{}{
					"task_id": h.config.TaskId,
					"error":   err.Error(),
				})
			}
			isOpenParseDomain := false
			// 如果缓存存在，则直接使用缓存值
			if cacheValue != "" {
				isOpenParseDomain = cacheValue == "1" || cacheValue == "true"
			} else {
				// 如果缓存不存在，则获取泛解析域名
				isOpenParseDomain = domain.IsWildcardDomain(topDomain)
			}
			// 设置泛解析域名缓存
			redis.GetClient().Set(context.Background(), cacheKey, isOpenParseDomain, time.Hour*24)
			// 如果泛解析域名未开启，则直接跳过
			if !isOpenParseDomain {
				if topDomain == subdomain {
					if slices.Contains(domainList, subdomain) {
						dnsCheckerDomainChan <- subdomain
					}
				} else {
					if slices.Contains(domainList, topDomain) || slices.Contains(domainList, subdomain) {
						dnsCheckerDomainChan <- subdomain
					} else {
						// 检查子域名是否与线索域名匹配
						for _, clueDomain := range domainList {
							// 任意一方是另一方的后缀就算匹配成功
							if strings.HasSuffix(subdomain, clueDomain) || strings.HasSuffix(clueDomain, subdomain) {
								dnsCheckerDomainChan <- subdomain
								break
							}
						}
					}
				}
			}
		}(subdomain)
	}

	// 等待所有子域名处理完成
	processWg.Wait()
	// 关闭子域名通道
	close(dnsCheckerDomainChan)
	// 等待所有DNS检测结果处理完成
	resultWg.Wait()
	return nil
}

// stringPtr 返回字符串指针，用于protobuf字段赋值
func stringPtr(s string) *string {
	return &s
}

// checkPort 检查IP端口是否开放
func checkPort(ip string, port int) bool {
	address := net.JoinHostPort(ip, fmt.Sprintf("%d", port))
	conn, err := net.DialTimeout("tcp", address, time.Second*1)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

func (h *RecommendAssetHandler) processDnsCheckerResult(ip string, subdomain string, existIpList []string) *pb.Asset {
	asset := &pb.Asset{
		Ip:             &ip,
		Protocol:       stringPtr("http"),
		BaseProtocol:   stringPtr("tcp"),
		Host:           stringPtr(fmt.Sprintf("https://%s", subdomain)),
		Domain:         stringPtr(utils.GetTopDomain(subdomain)),
		Lastupdatetime: stringPtr(time.Now().Format("2006-01-02 15:04:05")),
	}
	if checkPort(ip, 80) {
		asset.Port = stringPtr("80")
		asset.Protocol = stringPtr("http")
	} else if checkPort(ip, 443) {
		asset.Port = stringPtr("443")
		asset.Protocol = stringPtr("https")
	} else {
		asset.Port = stringPtr("443")
		asset.Protocol = stringPtr("https")
	}
	asset.Host = stringPtr(utils.GetIdnDomain(subdomain, true))
	return asset
}

func (h *RecommendAssetHandler) getFofaPureDnsData() ([]*recommend_result.RecommendResult, error) {
	// 仅处理域名线索
	domainClueList := h.filterCluesByType(h.state.clueList, clues.TYPE_DOMAIN)
	if len(domainClueList) == 0 {
		return nil, nil
	}

	var groupName string
	groupId := h.state.clueList[0].GroupId
	if groupId > 0 {
		clueGroupModel := clues_groups.NewCluesGrouper()
		clueGroup, err := clueGroupModel.First(mysql.WithId(uint64(groupId)))
		if err != nil {
			return nil, fmt.Errorf("获取线索组失败: %v", err)
		}
		groupName = clueGroup.Name
	}

	extraRecommendData := make(map[string]*recommend_result.RecommendResult)
	for _, clueInfo := range domainClueList {
		ipSubdomainLimits := make(map[string]int)

		// 缓存标识
		cacheTrigger := false
		// 缓存数据
		dnsInfoList := make([]*pb.PureDnsInfo, 0)
		// 通过缓存减少FOFA查询次数
		cacheKey := cache.GetCacheKey("fofa_pure_dns_data", utils.Md5Hash(clueInfo.Content))
		cacheInfoList, err := redis.GetClient().Get(context.Background(), cacheKey).Result()
		if err == nil {
			err = json.Unmarshal([]byte(cacheInfoList), &dnsInfoList)
			if err == nil {
				cacheTrigger = true
			}
		}

		if !cacheTrigger {
			// 获取FOFA纯解析数据
			dnsInfoList, _, err = fofa.PureDnsFromFOFA(context.Background(), clueInfo.Content, fofa.PageParams{
				Page: 1,
				Size: 100,
			})
			if err != nil {
				log.Error("[RecommendAssetHandler]", "获取FOFA查询结果失败", map[string]interface{}{
					"task_id": h.config.TaskId,
					"clue_id": clueInfo.Id,
					"error":   err.Error(),
				})
				continue
			}
			// 将结果缓存
			cacheData, err := json.Marshal(dnsInfoList)
			if err == nil {
				redis.GetClient().Set(context.Background(), cacheKey, string(cacheData), time.Hour*12)
			}
		}
		// 处理FOFA纯解析数据
		for _, dnsInfo := range dnsInfoList {
			_, isPrivateIP := utils.IsPrivateIP(dnsInfo.Ip)
			if dnsInfo.Ip == "" || dnsInfo.Host == "" || isPrivateIP {
				continue
			}
			// 限制IP的子域名数量
			if ipSubdomainLimits[dnsInfo.Ip] >= 20 {
				continue
			}
			ipSubdomainLimits[dnsInfo.Ip]++
			extraRecommendData = h.getExtraFofaPureDnsArr(extraRecommendData, int(groupId), groupName, dnsInfo, clueInfo, true)
			extraRecommendData = h.getExtraFofaPureDnsArr(extraRecommendData, int(groupId), groupName, dnsInfo, clueInfo, false)
		}
	}

	extraRecommendDataList := make([]*recommend_result.RecommendResult, 0)
	for _, v := range extraRecommendData {
		extraRecommendDataList = append(extraRecommendDataList, v)
	}
	return extraRecommendDataList, nil
}

func (h *RecommendAssetHandler) fofaFill(allIp []string) error {
	ipSegments, err := utils.ExtractIpSegments(allIp)
	if err != nil {
		return fmt.Errorf("提取IP段信息失败: %v", err)
	}
	// 覆盖率大于等于65%的IP段
	toFillIp := make([]string, 0)
	for _, segment := range ipSegments {
		if segment.Percent >= 65 {
			toFillIp = append(toFillIp, segment.ToFillIp...)
		}
	}

	cluesModel := clues.NewCluer()
	// 创建线索
	for _, ip := range toFillIp {
		q := make([]mysql.HandleFunc, 0)
		q = append(q, mysql.WithWhere("content = ?", fmt.Sprintf("%s/32", ip)))
		q = append(q, mysql.WithWhere("type = ?", clues.TYPE_IP))
		q = append(q, mysql.WithWhere("group_id = ?", h.state.recommendRecord.GroupId))
		q = append(q, mysql.WithWhere("user_id = ?", h.config.UserId))
		q = append(q, mysql.WithWhere("is_deleted = ?", clues.NOT_DELETE))
		c := &clues.Clue{
			Content:          fmt.Sprintf("%s/32", ip),
			Type:             clues.TYPE_IP,
			GroupId:          uint64(h.state.recommendRecord.GroupId),
			UserId:           uint64(h.config.UserId),
			IsDeleted:        clues.NOT_DELETE,
			CompanyId:        uint64(h.config.CompanyId),
			Comment:          fmt.Sprint(h.state.recommendRecord.GroupId),
			Source:           clues.SOURCE_EXPAND,
			Status:           clues.CLUE_PASS_STATUS,
			IsFromCheckTable: fmt.Sprint(clues.NOT_FROM_CHECK_TABLE),
		}
		newCreate, err := cluesModel.CreateIfNotExist(c, q...)
		if err != nil {
			log.Warn("fofaFill", "创建线索失败", map[string]interface{}{
				"task_id": h.config.TaskId,
				"ip":      ip,
				"error":   err.Error(),
			})
			continue
		}
		if newCreate {
			log.Info("fofaFill", "创建线索成功", map[string]interface{}{
				"task_id": h.config.TaskId,
				"ip":      ip,
			})
		}
	}

	// 获取线索
	clueList, err := cluesModel.ListAll(
		mysql.WithWhere("user_id = ?", h.config.UserId),
		mysql.WithWhere("group_id = ?", h.state.recommendRecord.GroupId),
		mysql.WithWhere("type = ?", clues.TYPE_IP),
		mysql.WithWhere("comment = ?", fmt.Sprint(h.state.recommendRecord.GroupId)),
		mysql.WithWhere("is_deleted = ?", clues.NOT_DELETE),
		mysql.WithOrder("id ASC"),
	)
	if err != nil {
		log.Error("fofaFill", "获取线索失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   err.Error(),
		})
		return err
	}
	// 获取FOFA查询结果
	resultList := make([]*recommend_result.RecommendResult, 0)
	for _, c := range clueList {
		queryStr := clueUtil.ParseQueryStr([]*clues.Clue{c}, false, false)
		assets, err := h.onlyFofaResult(queryStr)
		if err != nil {
			log.Error("[RecommendAssetHandler]", "获取FOFA查询结果失败", map[string]interface{}{
				"task_id":   h.config.TaskId,
				"query_str": queryStr,
				"error":     err.Error(),
			})
			continue
		}
		if len(assets) > 0 {
			// 每个IP只获取第一个资产
			asset := assets[0]
			// 处理FOFA资产项,转换为推荐结果
			recommentResult, err := h.convertPbAssetToRecommendResult(asset, c, recommend_result.ASSETS_SOURCE_FOFA)
			if err != nil {
				log.Error("fofaFill", "处理FOFA资产项失败", map[string]interface{}{
					"task_id": h.config.TaskId,
					"clue_id": c.Id,
					"error":   err.Error(),
				})
				continue
			}
			resultList = append(resultList, recommentResult)
		}
	}

	// 批量插入推荐结果
	return h.insertRecommendResults(resultList)
}

// insertRecommendResults 批量插入推荐结果
func (h *RecommendAssetHandler) insertRecommendResults(data []*recommend_result.RecommendResult) error {
	h.state.mutex.RLock()
	defer h.state.mutex.RUnlock()

	if len(data) == 0 {
		log.Info("[RecommendAssetHandler]", "没有推荐结果需要插入", map[string]interface{}{
			"task_id": h.config.TaskId,
		})
		return nil
	}

	// 过滤内网ip
	originalCount := len(data)
	filteredData := make([]*recommend_result.RecommendResult, 0, len(data))
	filteredCount := 0

	for _, item := range data {
		if item.Ip != "" && network.IsPrivateIP(item.Ip) {
			// 跳过内网IP
			filteredCount++
			log.Debug("[RecommendAssetHandler]", "过滤内网IP", map[string]interface{}{
				"task_id": h.config.TaskId,
				"ip":      item.Ip,
				"domain":  item.Domain,
				"port":    item.Port,
			})
			continue
		}
		filteredData = append(filteredData, item)
	}

	log.Info("[RecommendAssetHandler]", "内网IP过滤统计", map[string]interface{}{
		"task_id":        h.config.TaskId,
		"original_count": originalCount,
		"filtered_count": filteredCount,
		"remain_count":   len(filteredData),
	})

	// 如果过滤后没有数据，直接返回
	if len(filteredData) == 0 {
		log.Info("[RecommendAssetHandler]", "过滤内网IP后没有数据需要插入", map[string]interface{}{
			"task_id": h.config.TaskId,
		})
		return nil
	}

	// 分批插入，每批500条
	const batchSize int = 500
	successCount, errCount, lastErrorInfo := es_utils.InsertOrUpdate(recommend_result.IndexName(), recommend_result.TypeName(), filteredData, false, batchSize)
	log.Infof("[RecommendAssetHandler]批量插入推荐结果, successCount: %d, errCount: %d, lastErrorInfo: %s", successCount, errCount, lastErrorInfo)
	if errCount > 0 {
		return fmt.Errorf("批量插入推荐结果失败, errorInfo: %s", lastErrorInfo)
	}

	return nil
}

// getCluesCount 计算线索推荐总数
func (h *RecommendAssetHandler) getCluesCount(clueList []*clues.Clue, isDanweiCehui bool) int64 {
	var dateAfter string
	if isDanweiCehui && h.state.detectAssetsTask != nil {
		// 获取扩展任务的时间范围设置
		if h.state.detectAssetsTask.FofaRange > 0 {
			dateAfter = calculateDateAfter(h.state.detectAssetsTask.FofaRange)
		}
	}

	var count int64 = 0
	for _, clue := range clueList {
		isDomain := clue.Type == clues.TYPE_DOMAIN || clue.Type == clues.TYPE_SUBDOMAIN
		queryStr := clueUtil.ParseQueryStr([]*clues.Clue{clue}, false, isDomain)

		if dateAfter != "" {
			queryStr = fmt.Sprintf("%s && after=%s", queryStr, dateAfter)
		}
		// 缓存查询数量
		clueCount := int64(0)
		// 通过缓存减少FOFA查询次数
		cacheKey := cache.GetCacheKey("fofa_query_count", utils.Md5Hash(queryStr))
		cacheCount, err := redis.GetClient().Get(context.Background(), cacheKey).Int()
		if err == nil {
			clueCount = int64(cacheCount)
			log.Debugf("[RecommendAssetHandler]", "通过缓存获取FOFA线索数量", map[string]interface{}{
				"task_id":    h.config.TaskId,
				"clue_id":    clue.Id,
				"query_str":  queryStr,
				"clue_count": clueCount,
			})
		} else {
			// 获取FOFA查询数量
			clueCount, err = fofa.GetCountFromFOFA(context.Background(), queryStr, true, true)
			if err != nil {
				log.Error("[RecommendAssetHandler]", "通过FOFA获取查询数量失败", map[string]interface{}{
					"task_id":   h.config.TaskId,
					"clue_id":   clue.Id,
					"query_str": queryStr,
					"error":     err.Error(),
				})
				continue
			}
			log.Debugf("[RecommendAssetHandler]", "通过FOFA查询线索数量", map[string]interface{}{
				"task_id":    h.config.TaskId,
				"clue_id":    clue.Id,
				"query_str":  queryStr,
				"clue_count": clueCount,
			})
			// 缓存数量
			redis.GetClient().Set(context.Background(), cacheKey, clueCount, time.Hour*12)
		}

		// 累加查询数量
		count += clueCount
	}

	return count
}

// sendWs 发送WebSocket进度消息
func (h *RecommendAssetHandler) sendWs(progress float64, status int) {
	var resultCount int
	if progress >= 100 {
		// 查询推荐结果中ip去重后计算数量
		allIps, err := es_utils.AllByParams[recommend_result.RecommendResult](1, [][]interface{}{
			{"flag", "=", h.config.TaskId},
		}, nil, "ip")
		if err != nil {
			resultCount = 0
		} else {
			// ip去重后计算数量
			distinctIps := make(map[string]struct{}, 0)
			for _, v := range allIps {
				distinctIps[v.Ip] = struct{}{}
			}
			resultCount = len(distinctIps)
		}
	}
	err := websocket_message.PublishSuccess(int64(h.config.UserId), "recommend_progress", map[string]interface{}{
		"flag":        h.config.TaskId,
		"progress":    progress,
		"status":      status,
		"count":       resultCount,
		"user_id":     h.config.UserId,
		"total":       h.state.total,
		"task_name":   h.config.TaskName,
		"use_seconds": int(time.Since(h.state.startAt).Seconds()),
		"task_id":     h.config.ExpendId,
	})
	if err != nil {
		log.Warn("[RecommendAssetHandler]", "发送WebSocket消息失败", map[string]interface{}{
			"task_id": h.config.TaskId,
			"error":   err.Error(),
		})
	}
	// 更新扩展任务进度
	if h.state.detectAssetsTask != nil {
		updateData := map[string]interface{}{
			"expend_progress": progress,
		}
		if progress == 100 {
			updateData["step_status"] = detect_assets_tasks.StepStatusDone
		} else {
			updateData["step_status"] = detect_assets_tasks.StepStatusDefault
		}
		detectAssetsTaskModel := detect_assets_tasks.NewModel()
		err := detectAssetsTaskModel.UpdateAny(updateData, mysql.WithWhere("id = ?", h.state.detectAssetsTask.ID))
		if err != nil {
			log.Error("[RecommendAssetHandler]", "更新扩展任务进度失败", map[string]interface{}{
				"task_id": h.config.TaskId,
				"error":   err.Error(),
			})
		}
	}
	if h.config.OrganizationDiscoverTaskId > 0 {
		organizationDiscoverTaskModel := organization_discover_task.NewModel()
		err := organizationDiscoverTaskModel.UpdateAny(map[string]interface{}{
			"progress": progress,
		}, mysql.WithWhere("id = ?", h.config.OrganizationDiscoverTaskId))
		if err != nil {
			log.Error("[RecommendAssetHandler]", "更新组织架构测绘任务进度失败", map[string]interface{}{
				"task_id": h.config.TaskId,
				"error":   err.Error(),
			})
		}
	}
}

// isLastRecommendClue 判断是否是最后一个推荐线索
func (h *RecommendAssetHandler) isLastRecommendClue(clue *clues.Clue, lastClue *clues.Clue) bool {
	if lastClue == nil {
		return false
	}

	if clue.Type == clues.TYPE_LOGO {
		return lastClue.Hash == clue.Hash
	} else {
		return lastClue.Content == clue.Content
	}
}

// getFofaQueryResult 获取查询结果（使用channel处理）
func (h *RecommendAssetHandler) getFofaQueryResult(ctx context.Context, clue *clues.Clue, fofaQueryStr string, resultChan chan *AssetInfo, errorChan chan error) {
	log.Debugf("getFofaQueryResult", "开始获取FOFA查询结果,线索Id: %d, 查询字符串: %s", clue.Id, fofaQueryStr)

	var dateAfter string
	// 如果存在扩展任务，则查找detectAssetsTasks表
	if h.state.detectAssetsTask != nil && h.state.detectAssetsTask.FofaRange > 0 {
		dateAfter = calculateDateAfter(h.state.detectAssetsTask.FofaRange)
	}

	// 如果dateAfter不为空，则添加到queryStr中
	if dateAfter != "" {
		fofaQueryStr = fmt.Sprintf("%s && after=%s", fofaQueryStr, dateAfter)
	}

	assets := make([]*pb.Asset, 0)

	// 是否触发缓存标识
	cacheTrigger := false
	// 通过缓存减少FOFA查询次数
	cacheKey := cache.GetCacheKey("fofa_query_result", utils.Md5Hash(fofaQueryStr))
	cacheAssets, err := redis.GetClient().Get(context.Background(), cacheKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(cacheAssets), &assets)
		if err == nil {
			log.Debugf("RecommendAssetJobHandler", "通过缓存获取FOFA查询结果", map[string]interface{}{
				"task_id": h.config.TaskId,
				"clue_id": clue.Id,
			})
			cacheTrigger = true
		}
	}

	if !cacheTrigger {
		// 获取FOFA查询结果
		assets, _, err = fofa.GetAssetsFromFOFA(ctx, fofaQueryStr, fofa.PageParams{
			Page:     1,
			Size:     200,
			MaxPages: 100,
			MaxData:  10000,
		})
		if err != nil {
			errorChan <- err
			return
		}
		// 将结果缓存
		cacheData, err := json.Marshal(assets)
		if err == nil {
			redis.GetClient().Set(context.Background(), cacheKey, string(cacheData), time.Hour*12)
			log.Debugf("RecommendAssetJobHandler", "将FOFA查询结果缓存", map[string]interface{}{
				"task_id": h.config.TaskId,
				"clue_id": clue.Id,
			})
		}
	}

	log.Debugf("获取FOFA查询结果,线索Id: %d, 查询字符串: %s, 结果数量: %d", clue.Id, fofaQueryStr, len(assets))
	// 将结果发送到channel
	for _, asset := range assets {
		// 如果ip+port超过50条，则跳过（使用简化版本）
		if h.canLimitSubdomainCountSimple(asset.GetIp(), asset.GetPort()) {
			continue
		}
		select {
		case <-ctx.Done():
			log.Warn("RecommendAssetHandler-getFofaQueryResult", "processAsset已退出，停止处理后续线索")
			return
		case resultChan <- &AssetInfo{
			asset:  asset,
			clue:   clue,
			source: recommend_result.ASSETS_SOURCE_FOFA,
		}:
		case <-time.After(time.Second * 30): // 超时保护
			log.Warn("[RecommendAssetHandler]", "发送结果到channel超时", map[string]interface{}{
				"task_id": h.config.TaskId,
			})
			return
		}
	}
}

// onlyFofaResult 只获取FOFA查询结果
func (h *RecommendAssetHandler) onlyFofaResult(fofaQueryStr string) ([]*pb.Asset, error) {
	dateAfter := calculateDateAfter(h.state.detectAssetsTask.FofaRange)
	if dateAfter != "" {
		fofaQueryStr = fmt.Sprintf("%s && after=%s", fofaQueryStr, dateAfter)
	}
	assets := make([]*pb.Asset, 0)
	// 是否触发缓存标识
	cacheTrigger := false
	// 通过缓存减少FOFA查询次数
	cacheKey := cache.GetCacheKey("fofa_query_result", utils.Md5Hash(fofaQueryStr))
	cacheAssets, err := redis.GetClient().Get(context.Background(), cacheKey).Result()
	if err == nil {
		err = json.Unmarshal([]byte(cacheAssets), &assets)
		if err == nil {
			log.Debugf("RecommendAssetJobHandler", "通过缓存获取FOFA查询结果", map[string]interface{}{
				"task_id":   h.config.TaskId,
				"query_str": fofaQueryStr,
			})
			cacheTrigger = true
		}
	}
	if !cacheTrigger {
		// 获取FOFA查询结果
		assets, _, err = fofa.GetAssetsFromFOFA(context.Background(), fofaQueryStr, fofa.PageParams{
			Page: 1,
			Size: 200,
		})
		if err != nil {
			return nil, err
		}
		// 将结果缓存
		cacheData, err := json.Marshal(assets)
		if err == nil {
			redis.GetClient().Set(context.Background(), cacheKey, string(cacheData), time.Hour*12)
			log.Debugf("RecommendAssetJobHandler", "将FOFA查询结果缓存", map[string]interface{}{
				"task_id":   h.config.TaskId,
				"query_str": fofaQueryStr,
			})
		}
	}
	return assets, nil
}

// convertPbAssetToRecommendResult 处理单个FOFA资产项
func (h *RecommendAssetHandler) convertPbAssetToRecommendResult(asset *pb.Asset, clue *clues.Clue, source int) (*recommend_result.RecommendResult, error) {
	// 解析时间
	var lastUpdateTime time.Time
	if asset.GetLastupdatetime() != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", asset.GetLastupdatetime()); err == nil {
			lastUpdateTime = t
		}
	}

	// 解析CertsValid
	certsValid := false
	if asset.GetCertsValid() == "true" || asset.GetCertsValid() == "1" {
		certsValid = true
	}

	// 构造完整URL
	fullUrl := h.fullUrl(asset.GetHost(), asset.GetIp(), asset.GetProtocol(), utils.SafeInt(asset.GetPort()))

	// 获取子域名
	subdomain := utils.GetSubdomain(fullUrl)

	// 生成唯一ID
	id := utils.Md5Hash(fmt.Sprintf("%s%s%s%s%s",
		utils.CompleteIPv6(asset.GetIp()), asset.GetProtocol(), asset.GetPort(), subdomain, h.config.TaskId))

	certStr := ""
	cert := utils.GetCert(asset.GetCert(), false)
	certStr = strings.Join(cert, " ")
	// 构造推荐结果
	result := &recommend_result.RecommendResult{
		Id:           id,
		TaskId:       h.config.ExpendId,
		GroupId:      h.state.recommendRecord.GroupId,
		GroupName:    h.state.recommendRecord.GroupName,
		IsIPv6:       utils.IsIPv6(asset.GetIp()),
		Ip:           utils.CompleteIPv6(asset.GetIp()),
		Port:         utils.SafeInt(asset.GetPort()),
		Url:          fullUrl,
		Protocol:     asset.GetProtocol(),
		BaseProtocol: asset.GetBaseProtocol(),
		Title:        asset.GetTitle(),
		Domain:       utils.GetTopDomain(asset.GetDomain()),
		Subdomain:    subdomain,
		Cert:         certStr,
		Icp:          asset.GetIcp(),
		Logo: recommend_result.Logo{
			Hash:    0,
			Content: "",
		},
		SourceUpdatedAt: lastUpdateTime.Format("2006-01-02 15:04:05"),
		Reason: []recommend_result.RecommendReason{
			{
				Id:              int(clue.Id),
				Content:         clue.Content,
				ClueCompanyName: clue.ClueCompanyName,
				GroupId:         h.state.recommendRecord.GroupId,
				Type:            clue.Type,
				Source:          clue.Source,
			},
		},
		Flag:            h.config.TaskId,
		UserId:          h.config.UserId,
		CompanyId:       h.config.CompanyId,
		ClueCompanyName: []string{clue.ClueCompanyName},
		AllCompanyName:  []string{clue.ClueCompanyName},
		CertRaw:         asset.GetCert(),
		Product:         asset.GetProduct(),
		Status:          0, // STATUS_DEFAULT
		Audit:           0, // AUDIT_DEFAULT
		CertsValid:      certsValid,
		CloudName:       asset.GetCloudName(),
		OpenParse:       false,
		AssetsSource:    source,        // 资产来源
		AllAssetsSource: []int{source}, // 初始化所有命中的数据源
		Cname:           asset.GetCname(),
		CreatedAt:       time.Now().Format("2006-01-02 15:04:05"),
		UpdatedAt:       time.Now().Format("2006-01-02 15:04:05"),
	}

	if asset.GetCloudName() != "" {
		result.IsCDN = true
		// 如果检测为CDN，立即设置缓存
		if result.IsCDN {
			h.setCdnCache(asset.GetIp(), true)
		}
	}
	// 处理ICON
	content := asset.GetIcon()
	if asset.GetIconHash() != "" && content != "" {
		iconHash := utils.SafeInt(asset.GetIconHash())
		result.Logo.Hash = iconHash
		// 保存图标内容
		path := storage.SaveIco(content, int64(iconHash), true)
		if path != "" {
			result.Logo.Content = path
		}
	}

	// 检查是否是CDN
	if !result.IsIPv6 {
		result.IsCDN = network.IsCdn(asset.GetIp(), 0)
		// 设置缓存（无论是否为CDN）
		h.setCdnCache(asset.GetIp(), result.IsCDN)
	}
	if !result.IsCDN && asset.GetCname() != "" {
		result.IsCDN = network.IsCdn(asset.GetCname(), 1)
		// 如果检测为CDN，立即设置缓存
		if result.IsCDN {
			h.setCdnCache(asset.GetIp(), true)
		}
	}
	if !result.IsCDN && asset.GetCloudName() != "" {
		result.IsCDN = true
		// 如果检测为CDN，立即设置缓存
		if result.IsCDN {
			h.setCdnCache(asset.GetIp(), true)
		}
	}

	// 检查证书中的CDN标识
	if !result.IsCDN && result.Cert != "" {
		if strings.Contains(strings.ToLower(result.Cert), "cdn") {
			result.IsCDN = true
			// 如果检测为CDN，立即设置缓存
			if result.IsCDN {
				h.setCdnCache(asset.GetIp(), true)
			}
		}
	}

	// 检测到最后一层不是CDN再加上缓存
	if !result.IsCDN {
		h.setCdnCache(asset.GetIp(), false)
	}

	return result, nil
}

// HunterQueryParams hunter查询参数
type HunterQueryParams struct {
	CluesData      []*clues.Clue
	Clue           *clues.Clue
	HunterQueryStr string
	TaskId         string
	UserId         int
	NeedHunter     bool
}

// HunterQueryResult hunter查询结果
type HunterQueryResult struct {
	Assets    []*pb.Asset
	FromCache bool
	Error     error
}

// validateHunterQuery 验证hunter查询条件
func (h *RecommendAssetHandler) validateHunterQuery(params HunterQueryParams) bool {
	// 检查用户限制
	if params.UserId == 1 || params.UserId == 72 || len(params.CluesData) > 200 {
		return false
	}
	// 检查hunter开关和查询字符串
	if !params.NeedHunter || params.HunterQueryStr == "" {
		return false
	}
	return true
}

// queryHunterAssets 查询hunter资产（核心业务逻辑）
func (h *RecommendAssetHandler) queryHunterAssets(ctx context.Context, params HunterQueryParams) HunterQueryResult {
	log.Infof("开始获取hunter查询结果,task_id: %s, 线索ID: %d, 查询字符串: %s", h.config.TaskId, params.Clue.Id, params.HunterQueryStr)

	// 使用缓存减少hunter查询次数
	cacheKey := cache.GetCacheKey("hunter_query_result", utils.Md5Hash(params.HunterQueryStr))

	// 尝试从缓存获取
	if cacheResult, err := redis.GetClient().Get(ctx, cacheKey).Result(); err == nil {
		var cacheAssets []*pb.Asset
		if err := json.Unmarshal([]byte(cacheResult), &cacheAssets); err == nil {
			log.Debugf("从缓存获取hunter查询结果, 线索ID: %d, 结果数量: %d", params.Clue.Id, len(cacheAssets))
			return HunterQueryResult{
				Assets:    cacheAssets,
				FromCache: true,
				Error:     nil,
			}
		}
	}

	// 缓存未命中，执行实际查询
	today := time.Now()
	start := today.AddDate(0, 0, -30).Format(time.DateOnly)

	assets, err := h.hunterQuerySvc.Query(ctx, params.HunterQueryStr, start)
	if err != nil {
		log.Error("[RecommendAssetHandler]", "获取hunter查询结果失败", map[string]interface{}{
			"task_id": params.TaskId,
			"error":   err.Error(),
		})
		return HunterQueryResult{
			Assets:    nil,
			FromCache: false,
			Error:     err,
		}
	}

	// 缓存查询结果
	if cacheData, err := json.Marshal(assets); err == nil {
		if err := redis.GetClient().Set(ctx, cacheKey, string(cacheData), time.Hour*12).Err(); err != nil {
			log.Warnf("缓存hunter查询结果失败: %v", err)
		} else {
			log.Debugf("将hunter查询结果缓存", map[string]interface{}{
				"task_id":   params.TaskId,
				"query_str": params.HunterQueryStr,
			})
		}
	}

	return HunterQueryResult{
		Assets:    assets,
		FromCache: false,
		Error:     nil,
	}
}

// processHunterResults 处理hunter查询结果
func (h *RecommendAssetHandler) processHunterResults(ctx context.Context, result HunterQueryResult, clue *clues.Clue, resultChan chan *AssetInfo, errorChan chan error) {
	if result.Error != nil {
		errorChan <- result.Error
		return
	}

	// 发送资产结果到channel
	for _, asset := range result.Assets {
		select {
		case <-ctx.Done():
			log.Warn("RecommendAssetHandler-processHunterResults", "processAsset已退出，停止处理后续线索")
			return
		case resultChan <- &AssetInfo{
			asset:  asset,
			clue:   clue,
			source: recommend_result.ASSETS_SOURCE_HUNTER,
		}:
		case <-time.After(time.Second * 30): // 超时保护
			log.Warn("[RecommendAssetHandler]", "发送hunter结果到channel超时")
			return
		}
	}
}

// getHunterQueryResult 获取hunter查询结果（重构后的主方法）
func (h *RecommendAssetHandler) getHunterQueryResult(ctx context.Context, cluesData []*clues.Clue, clue *clues.Clue, hunterQuerystr string, resultChan chan *AssetInfo, errorChan chan error) {
	params := HunterQueryParams{
		CluesData:      cluesData,
		Clue:           clue,
		HunterQueryStr: hunterQuerystr,
		TaskId:         h.config.TaskId,
		UserId:         h.config.UserId,
		NeedHunter:     h.state.needHunter,
	}

	// 验证查询条件
	if !h.validateHunterQuery(params) {
		return
	}

	// 执行查询
	result := h.queryHunterAssets(ctx, params)

	// 处理结果
	h.processHunterResults(ctx, result, clue, resultChan, errorChan)
}

// getOneForAllResult 获取一个域名所有资产
func (h *RecommendAssetHandler) getOneForAllResult(ctx context.Context, clue *clues.Clue, domainStr string) error {
	if clue.Type == clues.TYPE_DOMAIN && clue.PunycodeDomain == "" && h.config.CompanyId <= 0 {
		oneForAllData, err := github.OneForAllQuery(ctx, domainStr, "", false)
		if err != nil {
			return err
		}
		// 处理OneForAll返回的数据
		for _, val := range oneForAllData {
			// 检查context是否已取消
			select {
			case <-ctx.Done():
				log.Warn("RecommendAssetHandler-getOneForAllResult", "processAsset已退出，停止处理后续线索")
				return nil
			default:
			}

			ipStr := val["ip"]
			if ipStr == "" {
				continue
			}
			go func(val map[string]string, ipStr string) {
				// 如果ip+port超过30条，则跳过（使用简化版本）
				if h.canLimitSubdomainCountSimple(ipStr, val["port"]) {
					return
				}
				err := h.processOneForAllResult(ctx, val, ipStr, clue)
				if err != nil {
					log.Error("[RecommendAssetHandler]", "处理OneForAll返回的数据失败", map[string]interface{}{
						"task_id": h.config.TaskId,
						"error":   err.Error(),
					})
				}
			}(val, ipStr)
		}
	}

	return nil
}

// processOneForAllResult 处理OneForAll返回的数据
func (h *RecommendAssetHandler) processOneForAllResult(ctx context.Context, val map[string]string, ipStr string, clue *clues.Clue) error {
	// 分割IP地址
	ipArr := strings.Split(ipStr, ",")

	for _, ip := range ipArr {
		// 检查context是否已取消
		select {
		case <-ctx.Done():
			log.Warn("RecommendAssetHandler-processOneForAllResult", "processAsset已退出，停止处理后续线索")
			return nil
		default:
		}

		ip = strings.TrimSpace(ip)
		if ip == "" {
			continue
		}

		// 确定协议
		protocol := "http"
		if strings.Contains(val["url"], "ttps") {
			protocol = "https"
		}

		// 创建资产结果
		// 优先使用domain字段，如果不存在则从subdomain中提取顶级域名
		domain := val["domain"]
		if domain == "" {
			domain = utils.GetTopDomain(val["subdomain"])
		}

		isCdn := val["cdn"] == "true"
		sourceConstant := getSourceConstant(val["source"])
		result := &recommend_result.RecommendResult{
			TaskId:          h.config.ExpendId,
			Ip:              ip,
			Port:            val["port"],
			Url:             val["url"],
			Domain:          domain,
			Subdomain:       val["subdomain"],
			Title:           val["title"],
			Cname:           val["cname"],
			Protocol:        protocol,
			SourceUpdatedAt: time.Now().Format("2006-01-02 15:04:05"),
			OneforallSource: val["source"],
			AssetsSource:    sourceConstant,
			AllAssetsSource: []int{sourceConstant}, // 初始化所有命中的数据源
			Banner:          val["banner"],
			IsCDN:           isCdn,
			Reason: []recommend_result.RecommendReason{
				{
					Id:              int(clue.Id),
					Content:         clue.Content,
					ClueCompanyName: clue.ClueCompanyName,
					GroupId:         h.state.recommendRecord.GroupId,
					Type:            clue.Type,
					Source:          clue.Source,
				},
			},
			Flag:            h.config.TaskId,
			UserId:          h.config.UserId,
			CompanyId:       h.config.CompanyId,
			AllCompanyName:  []string{clue.ClueCompanyName},
			ClueCompanyName: []string{clue.ClueCompanyName},
			CertRaw:         val["cert"],
			Product:         val["product"],
			Status:          0, // STATUS_DEFAULT
			Audit:           0, // AUDIT_DEFAULT
			CreatedAt:       time.Now().Format("2006-01-02 15:04:05"),
			UpdatedAt:       time.Now().Format("2006-01-02 15:04:05"),
		}

		// 生成唯一ID
		id := utils.Md5Hash(fmt.Sprintf("%s%s%s%s%s",
			utils.CompleteIPv6(ip), protocol, val["port"], val["subdomain"], h.config.TaskId))
		result.Id = id

		// 设置缓存（无论是否为CDN）
		h.setCdnCache(ip, isCdn)

		// 处理重复数据，合并 AllAssetsSource
		if existingResult, exists := h.state.recommendData.Load(id); exists {
			// 合并 AllAssetsSource 字段，确保数据源不重复
			sourceExists := false
			for _, existingSource := range existingResult.AllAssetsSource {
				if existingSource == sourceConstant {
					sourceExists = true
					break
				}
			}
			if !sourceExists {
				existingResult.AllAssetsSource = append(existingResult.AllAssetsSource, sourceConstant)
			}

			// 合并reason（如果需要）
			reasonExists := false
			for _, reason := range existingResult.Reason {
				if reason.Id == int(clue.Id) {
					reasonExists = true
					break
				}
			}
			if !reasonExists {
				existingResult.Reason = append(existingResult.Reason, recommend_result.RecommendReason{
					Id:              int(clue.Id),
					Content:         clue.Content,
					ClueCompanyName: clue.ClueCompanyName,
					GroupId:         h.state.recommendRecord.GroupId,
					Type:            clue.Type,
					Source:          clue.Source,
				})
			}

			// 更新数据（如果新数据更新时间更晚）
			if result.SourceUpdatedAt > existingResult.SourceUpdatedAt {
				// 保留合并后的 reason 和 AllAssetsSource
				result.Reason = existingResult.Reason
				result.AllAssetsSource = existingResult.AllAssetsSource
				h.state.recommendData.Store(id, result)
			} else {
				// 即使不更新主数据，也要保存合并后的 AllAssetsSource
				h.state.recommendData.Store(id, existingResult)
			}
		} else {
			// 添加到结果集
			h.state.recommendData.Store(id, result)
			// 更新当前处理数量
			atomic.AddInt64(&h.state.currentNumber, 1)
		}
	}
	return nil
}

// fullUrl 构造完整URL
func (h *RecommendAssetHandler) fullUrl(host string, ip string, protocol string, port int) string {
	if host == "" {
		host = ip
	}
	// 处理协议
	if (protocol == "https" || protocol == "http") &&
		!strings.HasPrefix(host, "https://") && !strings.HasPrefix(host, "http://") {
		host = fmt.Sprintf("%s://%s", protocol, host)
	}

	// 处理端口
	if port != 0 && port != 80 && port != 443 && !strings.Contains(host, ":") {
		host = fmt.Sprintf("%s:%d", host, port)
	}

	// 确保协议正确
	if protocol == "http" && strings.HasPrefix(host, "https://") {
		host = strings.Replace(host, "https://", "http://", 1)
	} else if protocol == "https" && strings.HasPrefix(host, "http://") {
		host = strings.Replace(host, "http://", "https://", 1)
	}

	return host
}

// oneforall的数据来源匹配
func getSourceConstant(sourceString string) int {
	if source, ok := sourceMap[sourceString]; ok {
		return source
	}
	return recommend_result.OtherSource
}

// getExtraFofaPureDnsArr 处理FOFA纯解析数据并构建推荐资产数据
func (h *RecommendAssetHandler) getExtraFofaPureDnsArr(extraRecommendData map[string]*recommend_result.RecommendResult, groupID int, groupName string, item *pb.PureDnsInfo, domainClue *clues.Clue, isHttps bool) map[string]*recommend_result.RecommendResult {
	var protocol string
	var port int32
	var id string

	if isHttps {
		protocol = "https"
		port = 443
		id = fmt.Sprintf("%x", md5.Sum([]byte(utils.CompleteIPv6(item.Ip)+"https443"+item.Host+h.config.TaskId)))
	} else {
		protocol = "http"
		port = 80
		id = fmt.Sprintf("%x", md5.Sum([]byte(utils.CompleteIPv6(item.Ip)+"http80"+item.Host+h.config.TaskId)))
	}

	// 构建推荐资产参数
	params := &recommend_result.RecommendResult{
		Id:           id,
		TaskId:       h.config.ExpendId,
		GroupId:      groupID,
		GroupName:    groupName,
		IsIPv6:       utils.IsIPv6(item.Ip),
		Ip:           utils.CompleteIPv6(item.Ip),
		Port:         port,
		Url:          h.fullUrl(item.Host, item.Ip, protocol, int(port)),
		Protocol:     protocol,
		BaseProtocol: "tcp",
		Title:        "",
		Domain:       utils.GetTopDomain(item.Host),
		Subdomain:    utils.GetSubdomain(item.Host),
		Cert:         "",
		Icp:          "",
		Logo: recommend_result.Logo{
			Hash:    0,
			Content: "",
		},
		SourceUpdatedAt: time.Now().Format("2006-01-02 15:04:05"),
		Reason: []recommend_result.RecommendReason{
			{
				Id:              int(domainClue.Id),
				Content:         domainClue.Content,
				ClueCompanyName: domainClue.ClueCompanyName,
				GroupId:         h.state.recommendRecord.GroupId,
				Type:            domainClue.Type,
				Source:          domainClue.Source,
			},
		},
		Flag:            h.config.TaskId,
		UserId:          h.config.UserId,
		CompanyId:       h.config.CompanyId,
		ClueCompanyName: []string{domainClue.ClueCompanyName},
		AllCompanyName:  []string{domainClue.ClueCompanyName},
		CertRaw:         "",
		Status:          0,
		Audit:           0,
		CertsValid:      false,
		CloudName:       "",
		OpenParse:       false,
		AssetsSource:    recommend_result.ASSETS_SOURCE_FOFA_PARSE_DOMAIN,
		AllAssetsSource: []int{recommend_result.ASSETS_SOURCE_FOFA_PARSE_DOMAIN}, // 初始化所有命中的数据源
		CreatedAt:       time.Now().Format("2006-01-02 15:04:05"),
		UpdatedAt:       time.Now().Format("2006-01-02 15:04:05"),
	}

	// 校验是否是CDN
	if !params.IsIPv6 {
		params.IsCDN = network.IsCdn(item.Ip, 0)
		// 设置缓存（无论是否为CDN）
		h.setCdnCache(item.Ip, params.IsCDN)
	}

	// 校验域名是否是CDN
	if !params.IsCDN {
		cname := params.Cname
		params.IsCDN = network.IsCdn(cname, 1)
		// 如果检测为CDN，立即设置缓存
		if params.IsCDN {
			h.setCdnCache(item.Ip, true)
		}
	}

	// 检测到最后一层不是CDN再加上缓存
	if !params.IsCDN {
		h.setCdnCache(item.Ip, false)
	}
	if existingData, ok := extraRecommendData[params.Id]; ok {
		reasonMap := make(map[int]bool)
		for _, reason := range existingData.Reason {
			reasonMap[reason.Id] = true
		}

		if len(params.Reason) == 0 {
			existingData.Reason = params.Reason
		} else {
			if _, ok := reasonMap[params.Reason[0].Id]; !ok {
				existingData.Reason = append(existingData.Reason, params.Reason[0])
			}
		}

		// 合并 AllAssetsSource 字段，确保数据源不重复
		sourceExists := false
		for _, existingSource := range existingData.AllAssetsSource {
			if existingSource == recommend_result.ASSETS_SOURCE_FOFA_PARSE_DOMAIN {
				sourceExists = true
				break
			}
		}
		if !sourceExists {
			existingData.AllAssetsSource = append(existingData.AllAssetsSource, recommend_result.ASSETS_SOURCE_FOFA_PARSE_DOMAIN)
		}

		if existingData.SourceUpdatedAt < params.SourceUpdatedAt {
			// 保留合并后的 reason 和 AllAssetsSource
			params.Reason = existingData.Reason
			params.AllAssetsSource = existingData.AllAssetsSource
			extraRecommendData[params.Id] = params
		} else {
			// 即使不更新主数据，也要保存合并后的 AllAssetsSource
			extraRecommendData[params.Id] = existingData
		}
	} else {
		extraRecommendData[params.Id] = params
	}
	return extraRecommendData
}

// setCdnCache 设置CDN缓存 - 统一处理CDN缓存设置
func (h *RecommendAssetHandler) setCdnCache(ip string, isCdn bool) {
	if ip != "" {
		cacheKey := "cdn-ip:" + ip
		var cacheData bool
		if !redis.GetCache(cacheKey, &cacheData) {
			// 缓存不存在，直接设置
			redis.SetCache(cacheKey, 30*24*time.Hour, isCdn) // 30天缓存
			log.Debugf("[CDN_DETECT_DEBUG] 设置CDN缓存 - IP: %s, 缓存Key: %s, 缓存值: %v", ip, cacheKey, isCdn)
		} else {
			// 缓存已存在，检查是否需要更新
			if !cacheData && isCdn {
				// 原来是false，现在要设置为true，需要更新
				redis.SetCache(cacheKey, 30*24*time.Hour, isCdn)
				log.Debugf("[CDN_DETECT_DEBUG] 更新CDN缓存 - IP: %s, 缓存Key: %s, 原值: %v, 新值: %v", ip, cacheKey, cacheData, isCdn)
			} else {
				// 其他情况保持原值不变
				log.Debugf("[CDN_DETECT_DEBUG] CDN缓存已存在且无需更新 - IP: %s, 缓存Key: %s, 现有值: %v, 尝试设置值: %v", ip, cacheKey, cacheData, isCdn)
			}
		}
	}
}
