package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/spf13/cast"
	"gorm.io/gorm"

	redisInit "micro-service/initialize/redis"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/domain_assets"
	"micro-service/pkg/cfg"
	"micro-service/pkg/icp"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
)

// PushDomainIcpJobPayload 域名推送备案查询任务载荷
type PushDomainIcpJobPayload struct {
	UserID     uint64   `json:"user_id"`
	DomainList []string `json:"domain_list"`
}

// PushDomainIcpJob 处理域名推送的备案查询任务
// 这个任务专门用于异步处理域名推送时的备案信息查询，避免API层超时
func PushDomainIcpJob(ctx context.Context, t *asyncq.Task) error {
	log.WithContextInfof(ctx, "[PushDomainIcpJob] 开始处理域名备案查询任务")

	var payload PushDomainIcpJobPayload
	if err := json.Unmarshal([]byte(t.Payload), &payload); err != nil {
		log.WithContextErrorf(ctx, "[PushDomainIcpJob] 解析任务载荷失败: %v", err)
		return fmt.Errorf("解析任务载荷失败: %v", err)
	}

	if len(payload.DomainList) == 0 {
		log.WithContextInfof(ctx, "[PushDomainIcpJob] 域名列表为空，跳过处理")
		return nil
	}

	log.WithContextInfof(ctx, "[PushDomainIcpJob] 开始处理用户 %d 的 %d 个域名备案查询", payload.UserID, len(payload.DomainList))

	// 初始化ICP查询客户端
	redisClient := redisInit.GetInstance(cfg.LoadRedis())
	icpQuery := icp.NewICPQuery(redisClient)

	// 处理每个域名的备案查询
	successCount := 0
	failCount := 0

	for _, domain := range payload.DomainList {
		if err := processDomainIcp(ctx, icpQuery, payload.UserID, domain); err != nil {
			log.WithContextErrorf(ctx, "[PushDomainIcpJob] 处理域名 %s 备案查询失败: %v", domain, err)
			failCount++
		} else {
			successCount++
		}
	}

	log.WithContextInfof(ctx, "[PushDomainIcpJob] 域名备案查询任务完成，用户ID: %d, 成功: %d, 失败: %d",
		payload.UserID, successCount, failCount)

	return nil
}

// processDomainIcp 处理单个域名的备案查询
func processDomainIcp(ctx context.Context, icpQuery icp.ICPQuery, userID uint64, domain string) error {
	// 查询数据库中的域名记录
	domainModel := domain_assets.NewModel()
	domainAsset, err := domainModel.First(mysql.WithWhere("user_id = ? AND domain = ?", userID, domain))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.WithContextWarnf(ctx, "[PushDomainIcpJob] 域名 %s 在数据库中不存在，跳过处理", domain)
			return nil
		}
		return fmt.Errorf("查询域名记录失败: %v", err)
	}

	// 如果已经有备案信息，跳过
	if domainAsset.CompanyName != "" {
		log.WithContextInfof(ctx, "[PushDomainIcpJob] 域名 %s 已有备案信息，跳过处理", domain)
		return nil
	}

	// 查询备案信息，设置较长的超时时间
	queryCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)
	defer cancel()

	topDomain := domainAsset.TopDomain
	if topDomain == "" {
		log.WithContextWarnf(ctx, "[PushDomainIcpJob] 域名 %s 的顶级域名为空，跳过备案查询", domain)
		return nil
	}

	log.WithContextInfof(ctx, "[PushDomainIcpJob] 开始查询域名 %s (顶级域名: %s) 的备案信息", domain, topDomain)

	res, err := icpQuery.QueryDomain(queryCtx, topDomain, false, false, false, userID)
	if err != nil {
		log.WithContextErrorf(ctx, "[PushDomainIcpJob] 查询域名 %s 备案信息失败: %v", domain, err)
		// 备案查询失败不影响整体流程，记录错误但继续处理
		return nil
	}

	// 更新域名记录的备案信息
	companyName := ""
	if res != nil && res.Info != nil {
		companyName = cast.ToString(res.Info["company_name"])
	}

	updateData := map[string]interface{}{
		"company_name": companyName,
	}

	if err := domainModel.UpdateMap(domainAsset.Id, updateData); err != nil {
		return fmt.Errorf("更新域名备案信息失败: %v", err)
	}

	log.WithContextInfof(ctx, "[PushDomainIcpJob] 成功更新域名 %s 的备案信息: %s", domain, companyName)
	return nil
}
