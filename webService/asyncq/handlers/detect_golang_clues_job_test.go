package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-redis/redis/v8"
	"github.com/go-redis/redismock/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	es "micro-service/initialize/es"
	mysqlInit "micro-service/initialize/mysql"
	redisInit "micro-service/initialize/redis"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dbx"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
)

// setupTestEnvironment 初始化测试环境
func setupTestEnvironment() {
	cfg.InitLoadCfg()
	log.Init()

	// 启用测试环境
	es.SetTestEnv(true)
	mysqlInit.SetTestEnv(true)
	redisInit.SetTestEnv(true)

	// 初始化Mock实例
	es.GetInstance(cfg.LoadElastic())
	mysqlInit.GetInstance(cfg.LoadMysql())
	redisInit.GetInstance(cfg.LoadRedis())
}

// ICPQueryResult ICP查询结果结构体
type ICPQueryResult struct {
	Info map[string]interface{} `json:"info"`
}

// MockICPQueryInterface ICP查询服务Mock接口
type MockICPQueryInterface struct {
	mock.Mock
}

func (m *MockICPQueryInterface) QueryDomain(ctx context.Context, domain string, needInfo, needHistory, needSubdomain bool, userId uint64) (*ICPQueryResult, error) {
	args := m.Called(ctx, domain, needInfo, needHistory, needSubdomain, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*ICPQueryResult), args.Error(1)
}

func (m *MockICPQueryInterface) QueryICP(ctx context.Context, icpNum string, needInfo, needHistory bool, userId uint64) (*ICPQueryResult, error) {
	args := m.Called(ctx, icpNum, needInfo, needHistory, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*ICPQueryResult), args.Error(1)
}

// MockWebsocketMessage WebSocket消息Mock
type MockWebsocketMessage struct {
	mock.Mock
}

func (m *MockWebsocketMessage) PublishSuccess(userID int64, topic string, data map[string]any) error {
	args := m.Called(userID, topic, data)
	return args.Error(0)
}

// MockMySQLDSL MySQL DSL操作Mock
type MockMySQLDSL struct {
	mock.Mock
}

func (m *MockMySQLDSL) UpdateByID(id uint64, data map[string]interface{}) (int64, error) {
	args := m.Called(id, data)
	return args.Get(0).(int64), args.Error(1)
}

type DetectAssetsTaskModelMock struct {
	mock.Mock
}

func (m *DetectAssetsTaskModelMock) UpdateAny(data map[string]any, opts ...any) error {
	args := m.Called(data, opts)
	return args.Error(0)
}

func TestHandleLogoClue_NewClueCreation(t *testing.T) {
	// 初始化测试环境，包括配置和日志系统
	setupTestEnvironment()
	mockCluer := new(clues.MockCluer)

	icon := map[string]interface{}{
		"source":  "https://example.com/icon.ico",
		"hash":    float64(123456),
		"content": "/test/icon/1.ico",
	}

	userId := uint64(1)
	groupId := uint64(1)
	companyId := uint64(1)
	companyName := "测试"

	mockCluer.On("First", mock.MatchedBy(func(fns []mysql.HandleFunc) bool {
		return len(fns) == 1
	})).Return(clues.Clue{}, gorm.ErrRecordNotFound).Once()

	// 模拟未命中父线索
	mockCluer.On("First", mock.MatchedBy(func(fns []mysql.HandleFunc) bool {
		return len(fns) == 1
	})).Return(clues.Clue{}, gorm.ErrRecordNotFound).Once()

	// 模拟 Create 成功
	mockCluer.On("Create", mock.AnythingOfType("*clues.Clue")).Return(nil).Once()

	err := handleLogoClue(context.Background(), mockCluer, icon, companyName, userId, groupId, companyId)

	assert.NoError(t, err)
	mockCluer.AssertExpectations(t)
}

func TestHandleLogoClue_ExistingClueUpdate(t *testing.T) {
	// 初始化测试环境，包括配置和日志系统
	setupTestEnvironment()
	mockCluer := new(clues.MockCluer)

	icon := map[string]interface{}{
		"source":  "http://example.com/icon.ico",
		"hash":    float64(78910),
		"content": "/some/icon/path.ico",
	}

	userId := uint64(2)
	groupId := uint64(20)
	companyId := uint64(100)
	companyName := "已有线索公司"

	// 第一次 First 调用，返回已有线索（注意这里是 Clue，不是 *Clue）
	mockCluer.On("First", mock.MatchedBy(func(fns []mysql.HandleFunc) bool {
		return true
	})).Return(clues.Clue{
		Model:  dbx.Model{Id: 1},
		UserId: userId,
		Status: 0,
	}, nil).Once()

	//
	mockCluer.On("First", mock.Anything).Return(clues.Clue{}, gorm.ErrRecordNotFound).Maybe()

	// 期望 Update 被调用
	mockCluer.On("Update", mock.AnythingOfType("clues.Clue")).Return(nil).Once()

	err := handleLogoClue(context.Background(), mockCluer, icon, companyName, userId, groupId, companyId)
	assert.NoError(t, err)
	mockCluer.AssertExpectations(t)
}
func TestHandleLogoClue_UpdateExistingClue(t *testing.T) {
	// 初始化测试环境，包括配置和日志系统
	setupTestEnvironment()
	mockCluer := new(clues.MockCluer)

	icon := map[string]interface{}{
		"source":  "http://example.com/icon.ico",
		"hash":    float64(99999),
		"content": "/not/in/app/public/icon1.ico",
	}
	userId := uint64(3)
	groupId := uint64(30)
	companyId := uint64(300)
	companyName := "更新线索公司"

	// 模拟返回已有线索
	mockCluer.On("First", mock.Anything).Return(clues.Clue{
		Model:  dbx.Model{Id: 1001},
		UserId: userId,
		Status: 1,
	}, nil).Once()

	// Update 被调用
	mockCluer.On("Update", mock.AnythingOfType("clues.Clue")).Return(nil).Once()

	// 防止多次 First 报错
	mockCluer.On("First", mock.Anything).Return(clues.Clue{}, gorm.ErrRecordNotFound).Maybe()

	err := handleLogoClue(context.Background(), mockCluer, icon, companyName, userId, groupId, companyId)
	assert.NoError(t, err)
	mockCluer.AssertExpectations(t)
}
func TestHandleLogoClue_HashAsString(t *testing.T) {
	// 初始化测试环境，包括配置和日志系统
	setupTestEnvironment()
	mockCluer := new(clues.MockCluer)

	icon := map[string]interface{}{
		"source":  "http://example.com/icon.ico",
		"hash":    "56789", // string 类型
		"content": "/icon/3.ico",
	}
	userId := uint64(5)
	groupId := uint64(50)
	companyId := uint64(500)
	companyName := "字符串 hash"

	mockCluer.On("First", mock.Anything).Return(clues.Clue{}, gorm.ErrRecordNotFound).Maybe()
	mockCluer.On("Create", mock.AnythingOfType("*clues.Clue")).Return(nil).Once()

	err := handleLogoClue(context.Background(), mockCluer, icon, companyName, userId, groupId, companyId)
	assert.NoError(t, err)
	mockCluer.AssertExpectations(t)
}
func TestHandleLogoClue_FirstQueryError(t *testing.T) {
	// 初始化测试环境，包括配置和日志系统
	setupTestEnvironment()
	mockCluer := new(clues.MockCluer)

	icon := map[string]interface{}{
		"source":  "http://example.com/icon.ico",
		"hash":    float64(88888),
		"content": "/icon/4.ico",
	}
	userId := uint64(6)
	groupId := uint64(60)
	companyId := uint64(600)
	companyName := "查询出错公司"

	mockCluer.On("First", mock.Anything).Return(clues.Clue{}, errors.New("db error")).Once()

	err := handleLogoClue(context.Background(), mockCluer, icon, companyName, userId, groupId, companyId)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
}

func TestSendProgressForDetectCluesJob(t *testing.T) {
	// 初始化测试环境，包括配置和日志系统
	setupTestEnvironment()
	// 准备 mock
	wsMock := new(MockWebsocketMessage)
	modelMock := new(DetectAssetsTaskModelMock)

	ctx := context.Background()

	task := &detect_assets_tasks.DetectAssetsTask{}

	tests := []struct {
		name        string
		done        uint64
		total       uint64
		status      int
		progress    float64
		expectedPct float64
		expectErr   bool
	}{
		{
			name:        "正常处理",
			done:        50,
			total:       100,
			status:      0,
			progress:    0.5,
			expectedPct: 50.5,
		},
		{
			name:        "status=0 进度100%设置为99%",
			done:        100,
			total:       100,
			status:      0,
			progress:    0.0,
			expectedPct: 99,
		},
		{
			name:        "total=0,直接完成",
			done:        0,
			total:       0,
			status:      1,
			progress:    0,
			expectedPct: 100,
		},
		{
			name:        "进度最小值为3%",
			done:        0,
			total:       1000,
			status:      1,
			progress:    0.01,
			expectedPct: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟 PublishSuccess 成功
			wsMock.On("PublishSuccess", int64(1), "detect_assets_tip1_process", mock.Anything).Return(nil).Once()

			// 模拟 UpdateAny 成功
			modelMock.On("UpdateAny", mock.Anything, mock.Anything).Return(nil).Once()

			err := sendProgressForDetectCluesJob(ctx, 1, task, tt.done, tt.total, tt.status, tt.progress, "test-company")
			assert.NoError(t, err)

			assert.Equal(t, tt.expectedPct, task.ClueProgress, 0.01)
		})
	}
}

func TestSendProgress_PublishError(t *testing.T) {
	// 初始化测试环境，包括配置和日志系统
	setupTestEnvironment()
	wsMock := new(MockWebsocketMessage)
	modelMock := new(DetectAssetsTaskModelMock)

	task := &detect_assets_tasks.DetectAssetsTask{}

	wsMock.On("PublishSuccess", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("mock-publish-error")).Once()
	modelMock.On("UpdateAny", mock.Anything, mock.Anything).Return(nil).Once()

	err := sendProgressForDetectCluesJob(context.Background(), 1, task, 10, 20, 0, 0.1, "Test")
	assert.NoError(t, err)
}

func TestIsValidDomain(t *testing.T) {
	tests := []struct {
		name   string
		domain string
		want   bool
	}{
		// 空值和无效输入
		{"空字符串", "", false},
		{"纯空格", "   ", false},
		{"过长域名", strings.Repeat("a", 256), false},
		{"单个字符", "a", false},
		{"只有顶级域", "com", false},

		// 带协议前缀
		{"带http协议", "http://example.com", true},
		{"带https协议", "https://example.com", true},
		{"带ftp协议", "ftp://example.com", false}, // 只处理http/https

		// 带路径和参数
		{"带路径", "example.com/path/to/resource", true},
		{"带查询参数", "example.com?query=param", true},
		{"带路径和参数", "example.com/path?query=param#section", true},

		// 合法域名格式
		{"标准域名", "example.com", true},
		{"三级域名", "sub.example.com", true},
		{"四级域名", "sub.sub.example.com", true},
		{"带数字域名", "123domain.com", true},
		{"数字域名", "360.com", true},
		{"带连字符", "my-domain.com", true},
		{"带下划线", "my_domain.com", true}, // 下划线在域名中允许但非标准
		{"带点号", "my.domain.com", true},
		{"长TLD", "example.technology", true},
		{"短域名", "a.bc", true},
		{"最小长度", "a.co", true},

		// 非法域名格式
		{"非法字符", "exa<mple.com", false},
		{"起始连字符", "-example.com", false},
		{"结束连字符", "example-.com", false},
		{"连续点", "example..com", false},
		{"起始点", ".example.com", false},
		{"结束点", "example.com.", false},
		{"单字符TLD", "example.c", false}, // TLD至少2个字符
		{"空格域名", "exa mple.com", false},
		{"特殊字符", "example!.com", false},
		{"斜杠在域名中", "exa/mple.com", false},
		{"冒号在域名中", "exa:mple.com", false},
		{"协议但无域名", "http://", false},
		{"IP地址", "***********", false},
		{"带端口", "example.com:8080", false},
		{"本地域名", "localhost", false},
		{"邮件地址", "<EMAIL>", false},
		{"URL编码", "example%20.com", false},

		// 边界情况
		{"255字符域名", strings.Repeat("a", 63) + "." + strings.Repeat("b", 63) + "." + strings.Repeat("c", 63) + ".com", true},
		{"256字符域名", strings.Repeat("a", 64) + "." + strings.Repeat("b", 63) + "." + strings.Repeat("c", 63) + ".com", false},
		{"长标签", strings.Repeat("a", 63) + ".com", true},
		{"超长标签", strings.Repeat("a", 64) + ".com", false},
		{"最小TLD", "example.ab", true},
		{"单字符TLD", "example.a", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := isValidDomain(tt.domain)
			if got != tt.want {
				t.Errorf("isValidDomain(%q) = %v, want %v", tt.domain, got, tt.want)
			}
		})
	}
}

func TestPluckCert(t *testing.T) {
	tests := []struct {
		name       string
		certStr    string
		onlyDomain bool
		expectedO  string
		expectedCN string
	}{
		{
			name:       "标准证书格式-包含CN和O",
			certStr:    `CN=example.com, O=测试公司, L=北京, ST=北京, C=CN`,
			onlyDomain: false,
			expectedO:  "测试公司",
			expectedCN: "example.com",
		},
		{
			name:       "只有CN字段",
			certStr:    `CN=test.example.com`,
			onlyDomain: false,
			expectedO:  "",
			expectedCN: "test.example.com",
		},
		{
			name:       "只有O字段",
			certStr:    `O=Another Company, L=上海`,
			onlyDomain: false,
			expectedO:  "Another Company",
			expectedCN: "",
		},
		{
			name:       "纯域名字符串",
			certStr:    "example.com",
			onlyDomain: false,
			expectedO:  "",
			expectedCN: "example.com",
		},
		{
			name:       "无效证书格式",
			certStr:    "invalid cert data",
			onlyDomain: false,
			expectedO:  "invalid cert data",
			expectedCN: "",
		},
		{
			name:       "空字符串",
			certStr:    "",
			onlyDomain: false,
			expectedO:  "",
			expectedCN: "",
		},
		{
			name:       "带引号的字段",
			certStr:    `CN="quoted.example.com", O="带引号的公司"`,
			onlyDomain: false,
			expectedO:  "带引号的公司",
			expectedCN: "quoted.example.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actualO, actualCN := pluckCert(tt.certStr, tt.onlyDomain)
			assert.Equal(t, tt.expectedO, actualO, "O字段不匹配")
			assert.Equal(t, tt.expectedCN, actualCN, "CN字段不匹配")
		})
	}
}

// TestIsClueInBlackList 测试黑名单检查函数
func TestIsClueInBlackList(t *testing.T) {
	setupTestEnvironment()

	tests := []struct {
		name       string
		item       map[string]interface{}
		cacheSetup func(redismock.ClientMock)
		dbSetup    func(sqlmock.Sqlmock)
		expected   bool
	}{
		{
			name: "域名类型-缓存命中-在黑名单",
			item: map[string]interface{}{
				"type":    float64(clues.TYPE_DOMAIN),
				"content": "blacklisted.com",
			},
			cacheSetup: func(rdm redismock.ClientMock) {
				cacheData := []string{"blacklisted.com", "another-black.com"}
				cacheJSON, _ := json.Marshal(cacheData)
				rdm.ExpectGet("expand_clues_black_words_list_0").SetVal(string(cacheJSON))
			},
			expected: true,
		},
		{
			name: "域名类型-缓存命中-不在黑名单",
			item: map[string]interface{}{
				"type":    float64(clues.TYPE_DOMAIN),
				"content": "allowed.com",
			},
			cacheSetup: func(rdm redismock.ClientMock) {
				cacheData := []string{"blacklisted.com", "another-black.com"}
				cacheJSON, _ := json.Marshal(cacheData)
				rdm.ExpectGet("expand_clues_black_words_list_0").SetVal(string(cacheJSON))
			},
			expected: false,
		},
		{
			name: "LOGO类型-Hash匹配",
			item: map[string]interface{}{
				"type": float64(clues.TYPE_LOGO),
				"hash": float64(12345),
			},
			cacheSetup: func(rdm redismock.ClientMock) {
				rdm.ExpectGet("expand_clues_black_words_list_3").SetErr(redis.Nil)
			},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"id", "user_id", "company_id", "operator_id", "name", "status", "hash", "type", "created_at", "updated_at", "deleted_at"}).
					AddRow(1, 1, 1, 1, "", 1, 12345, 3, time.Now(), time.Now(), nil).
					AddRow(2, 1, 1, 1, "", 1, 67890, 3, time.Now(), time.Now(), nil)
				mock.ExpectQuery("SELECT (.+) FROM `clue_black_keyword`").
					WillReturnRows(rows)
			},
			expected: true,
		},
		{
			name: "缓存未命中-从数据库获取",
			item: map[string]interface{}{
				"type":    float64(clues.TYPE_DOMAIN),
				"content": "test.com",
			},
			cacheSetup: func(rdm redismock.ClientMock) {
				rdm.ExpectGet("expand_clues_black_words_list_0").SetErr(redis.Nil)
				// 设置缓存
				rdm.ExpectSet("expand_clues_black_words_list_0", mock.Anything, 2*time.Hour).SetVal("OK")
			},
			dbSetup: func(mock sqlmock.Sqlmock) {
				rows := sqlmock.NewRows([]string{"id", "user_id", "company_id", "operator_id", "name", "status", "hash", "type", "created_at", "updated_at", "deleted_at"}).
					AddRow(1, 1, 1, 1, "blacklisted.com", 1, 0, 0, time.Now(), time.Now(), nil).
					AddRow(2, 1, 1, 1, "test.com", 1, 0, 0, time.Now(), time.Now(), nil)
				mock.ExpectQuery("SELECT (.+) FROM `clue_black_keyword`").
					WillReturnRows(rows)
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysqlInit.ResetMockInstance()

			mysqlMock := mysqlInit.GetMockInstance()
			redisMock := redisInit.GetMockInstance()

			// 设置Mock期望
			if tt.cacheSetup != nil {
				tt.cacheSetup(redisMock)
			}
			if tt.dbSetup != nil {
				tt.dbSetup(mysqlMock)
			}

			result := isClueInBlackList(tt.item)

			assert.Equal(t, tt.expected, result)

			// 验证Mock期望
			assert.NoError(t, mysqlMock.ExpectationsWereMet())
		})
	}
}

// MockICP 提供ICP相关函数的Mock
func MockExpandCompanyClue(companyName string, taskID string, process int, items []interface{}) {
	// 这个函数用于在测试中Mock icp.ExpandCompanyClue的行为
	// 实际实现需要根据具体的Mock框架来做
}

// TestDetectGolangCluesEventHandler 测试主函数
func TestDetectGolangCluesEventHandler(t *testing.T) {
	setupTestEnvironment()

	tests := []struct {
		name          string
		taskPayload   string
		taskSetup     func(sqlmock.Sqlmock)
		redisSetup    func(redismock.ClientMock)
		expectedError bool
		description   string
	}{
		{
			name: "成功解析payload并处理空企业列表",
			taskPayload: `{
				"clue_company_array": [],
				"user_id": 123,
				"company_id": 456,
				"task_id": 789,
				"group_id": 101,
				"is_force": false,
				"pre_ids": [],
				"scene_ids": []
			}`,
			taskSetup: func(mock sqlmock.Sqlmock) {
				// Mock任务查询 - 包含所有GORM查询条件
				rows := sqlmock.NewRows([]string{"id", "name", "user_id", "company_id", "step_status", "status", "clue_progress", "created_at", "updated_at", "deleted_at"}).
					AddRow(789, "测试任务", 123, 456, 0, 1, 0.0, time.Now(), time.Now(), nil)
				mock.ExpectQuery("SELECT (.+) FROM `detect_assets_tasks` WHERE (.+)").
					WithArgs(uint64(789), 1).
					WillReturnRows(rows)

					// Mock所有可能的UPDATE操作（顺序很重要）
				// 1. 开始进度更新（UpdateAny调用 - 事务内）
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE (.+)").
					WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()

				// 2. 结束进度更新（UpdateAny调用 - 事务内）
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE (.+)").
					WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()

				// 3. 最终任务状态更新（DSL UpdateByID - 事务内）
				mock.ExpectBegin()
				mock.ExpectExec("UPDATE (.+)").
					WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()
			},
			expectedError: false,
			description:   "空企业列表应该直接完成，不执行企业线索扩展",
		},
		{
			name:          "payload解析失败",
			taskPayload:   `{invalid json}`,
			expectedError: true,
			description:   "无效JSON格式应该返回解析错误",
		},
		{
			name: "任务不存在",
			taskPayload: `{
				"clue_company_array": ["测试企业"],
				"user_id": 123,
				"task_id": 999,
				"group_id": 101
			}`,
			taskSetup: func(mock sqlmock.Sqlmock) {
				// Mock任务查询返回空结果
				rows := sqlmock.NewRows([]string{"id", "name", "user_id", "company_id", "step_status", "status", "clue_progress", "created_at", "updated_at", "deleted_at"})
				mock.ExpectQuery("SELECT (.+) FROM `detect_assets_tasks` WHERE (.+)").
					WithArgs(uint64(999), 1).
					WillReturnRows(rows)
			},
			expectedError: false,
			description:   "任务不存在时应该发送100%进度并正常结束",
		},
		{
			name: "数据库查询任务失败",
			taskPayload: `{
				"clue_company_array": ["测试企业"],
				"user_id": 123,
				"task_id": 789,
				"group_id": 101
			}`,
			taskSetup: func(mock sqlmock.Sqlmock) {
				// Mock任务查询失败
				mock.ExpectQuery("SELECT (.+) FROM `detect_assets_tasks` WHERE (.+)").
					WithArgs(uint64(789), 1).
					WillReturnError(assert.AnError)
			},
			expectedError: true,
			description:   "数据库查询失败应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置Mock实例
			mysqlInit.ResetMockInstance()

			mysqlMock := mysqlInit.GetMockInstance()
			redisMock := redisInit.GetMockInstance()

			// 设置Mock期望
			if tt.taskSetup != nil {
				tt.taskSetup(mysqlMock)
			}
			if tt.redisSetup != nil {
				tt.redisSetup(redisMock)
			}

			// 创建测试任务
			task := &asyncq.Task{
				Type:    asyncq.DetectGolangCluesJob,
				Payload: tt.taskPayload,
			}

			// 执行函数
			err := DetectGolangCluesEventHandler(context.Background(), task)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}

			// 验证Mock期望（如果没有错误的话）
			if !tt.expectedError && tt.taskSetup != nil {
				assert.NoError(t, mysqlMock.ExpectationsWereMet())
			}
		})
	}
}

// TestHandleClueItems 相关测试 - 使用集成测试方法测试核心逻辑

// createTestClueItem 创建测试线索项
func createTestClueItem(itemType interface{}, content string, companyName string) map[string]interface{} {
	return map[string]interface{}{
		"type":         itemType,
		"content":      content,
		"company_name": companyName,
	}
}

// createTestClueItemWithHash 创建带hash的测试线索项（用于LOGO）
func createTestClueItemWithHash(itemType interface{}, content string, companyName string, hash interface{}) map[string]interface{} {
	return map[string]interface{}{
		"type":         itemType,
		"content":      content,
		"company_name": companyName,
		"hash":         hash,
	}
}

// TestHandleClueItems_DataTypeConversion 测试数据类型转换功能
func TestHandleClueItems_DataTypeConversion(t *testing.T) {
	setupTestEnvironment()

	tests := []struct {
		name        string
		items       []interface{}
		expectError bool
		description string
	}{
		{
			name: "正常map类型处理",
			items: []interface{}{
				map[string]interface{}{
					"type":         float64(5),
					"content":      "example.com",
					"company_name": "测试企业",
				},
			},
			expectError: false,
			description: "map[string]interface{}类型应该正常处理",
		},
		{
			name: "JSON字符串解析",
			items: []interface{}{
				`{"type":5,"content":"example.com","company_name":"测试企业"}`,
			},
			expectError: false,
			description: "JSON字符串应该能正确解析",
		},
		{
			name: "结构体类型转换",
			items: []interface{}{
				struct {
					Type        int    `json:"type"`
					Content     string `json:"content"`
					CompanyName string `json:"company_name"`
				}{
					Type:        5,
					Content:     "example.com",
					CompanyName: "测试企业",
				},
			},
			expectError: false,
			description: "struct类型应该能通过JSON转换处理",
		},
		{
			name: "无效JSON字符串",
			items: []interface{}{
				`{invalid json}`,
			},
			expectError: false, // handleClueItems不会因为单个项目失败而返回错误
			description: "无效JSON应该被跳过，不影响整体执行",
		},
		{
			name:        "空items列表",
			items:       []interface{}{},
			expectError: false,
			description: "空列表应该正常处理",
		},
		{
			name: "nil项目",
			items: []interface{}{
				nil,
			},
			expectError: false,
			description: "nil项目应该被跳过",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := handleClueItems(context.Background(), tt.items, "测试企业", "测试企业", 123, 456, 789, []uint64{})

			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}
		})
	}
}

// TestHandleClueItems_TypeMapping 测试线索类型映射功能
func TestHandleClueItems_TypeMapping(t *testing.T) {
	setupTestEnvironment()

	tests := []struct {
		name        string
		inputType   interface{}
		description string
	}{
		{
			name:        "类型5映射为域名",
			inputType:   5,
			description: "类型5应该映射为TYPE_DOMAIN",
		},
		{
			name:        "类型7映射为子域名",
			inputType:   7,
			description: "类型7应该映射为TYPE_SUBDOMAIN",
		},
		{
			name:        "类型8映射为关键词",
			inputType:   8,
			description: "类型8应该映射为TYPE_KEYWORD",
		},
		{
			name:        "float64类型转换",
			inputType:   float64(5),
			description: "float64类型的5应该映射为TYPE_DOMAIN",
		},
		{
			name:        "字符串类型转换",
			inputType:   "5",
			description: "字符串类型的5应该映射为TYPE_DOMAIN",
		},
		{
			name:        "无效类型字符串",
			inputType:   "invalid",
			description: "无效类型应该被跳过",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testItems := []interface{}{
				createTestClueItem(tt.inputType, "test-content", "测试企业"),
			}

			err := handleClueItems(context.Background(), testItems, "测试企业", "测试企业", 123, 456, 789, []uint64{})
			assert.NoError(t, err, tt.description)
		})
	}
}

// TestHandleClueItems_EmptyContent 测试空内容处理
func TestHandleClueItems_EmptyContent(t *testing.T) {
	setupTestEnvironment()

	tests := []struct {
		name        string
		clueType    interface{}
		content     string
		description string
	}{
		{
			name:        "空内容域名",
			clueType:    5,
			content:     "",
			description: "空内容的域名应该被跳过",
		},
		{
			name:        "空内容ICP",
			clueType:    2,
			content:     "",
			description: "空内容的ICP应该被跳过",
		},
		{
			name:        "空内容证书",
			clueType:    1,
			content:     "",
			description: "空内容的证书应该被跳过",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testItems := []interface{}{
				createTestClueItem(tt.clueType, tt.content, "测试企业"),
			}

			err := handleClueItems(context.Background(), testItems, "测试企业", "测试企业", 123, 456, 789, []uint64{})
			assert.NoError(t, err, tt.description)
		})
	}
}

// TestHandleClueItems_LogoCollection 测试LOGO类型线索收集
func TestHandleClueItems_LogoCollection(t *testing.T) {
	setupTestEnvironment()

	// 准备混合类型的测试数据
	testItems := []interface{}{
		createTestClueItemWithHash(clues.TYPE_LOGO, "/logo/test1.ico", "测试企业", float64(12345)),
		createTestClueItemWithHash(3, "/logo/test2.ico", "测试企业", 67890), // TYPE_LOGO = 3
		createTestClueItem(5, "example.com", "测试企业"),                    // 普通域名线索
	}

	err := handleClueItems(context.Background(), testItems, "测试企业", "测试企业", 123, 456, 789, []uint64{})
	assert.NoError(t, err, "包含LOGO线索的处理应该正常完成")
}

// TestHandleClueItems_SceneIdManagement 测试场景ID管理
func TestHandleClueItems_SceneIdManagement(t *testing.T) {
	setupTestEnvironment()

	sceneIds := []uint64{1, 2, 3, 4, 5}
	testItems := []interface{}{
		createTestClueItem(5, "example.com", "测试企业"),
	}

	err := handleClueItems(context.Background(), testItems, "测试企业", "测试企业", 123, 456, 789, sceneIds)
	assert.NoError(t, err, "场景ID管理应该正常工作")
}

// TestHandleClueItems_CompanyNameHandling 测试企业名称处理
func TestHandleClueItems_CompanyNameHandling(t *testing.T) {
	setupTestEnvironment()

	// 测试企业名称从线索项中提取
	testItems := []interface{}{
		map[string]interface{}{
			"type":         5,
			"content":      "example.com",
			"company_name": "线索中的企业名称",
		},
	}

	err := handleClueItems(context.Background(), testItems, "参数中的企业名称", "原始企业名称", 123, 456, 789, []uint64{})
	assert.NoError(t, err, "企业名称处理应该正常工作")
}
