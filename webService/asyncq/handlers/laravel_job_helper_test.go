package handlers

import (
	"testing"

	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asynq "micro-service/pkg/queue_helper"

	"github.com/stretchr/testify/assert"
)

// 初始化测试环境
func init() {
	cfg.InitLoadCfg()
	log.Init()
}

func TestDispatchLaravelCountClueAssetTotalJob(t *testing.T) {
	t.Run("function exists and callable", func(t *testing.T) {
		// 验证函数存在且可调用，不会panic
		assert.NotPanics(t, func() {
			err := DispatchLaravelCountClueAssetTotalJob()
			// 在测试环境中可能会因为Redis连接失败而返回错误，这是正常的
			_ = err
		})
	})

	t.Run("job factory exists", func(t *testing.T) {
		// 验证任务工厂不为nil
		assert.NotNil(t, asynq.CountClueAssetTotalJob)
		assert.IsType(t, &asynq.LaravelJobFactory{}, asynq.CountClueAssetTotalJob)
	})
}

func TestDispatchLaravelCacheTableIpsConditionJob(t *testing.T) {
	t.Run("function exists and callable", func(t *testing.T) {
		// 验证函数存在且可调用，不会panic
		assert.NotPanics(t, func() {
			err := DispatchLaravelCacheTableIpsConditionJob(123)
			_ = err
		})
	})

	t.Run("zero user id", func(t *testing.T) {
		assert.NotPanics(t, func() {
			err := DispatchLaravelCacheTableIpsConditionJob(0)
			_ = err
		})
	})

	t.Run("large user id", func(t *testing.T) {
		largeUserId := uint64(18446744073709551615) // max uint64
		assert.NotPanics(t, func() {
			err := DispatchLaravelCacheTableIpsConditionJob(largeUserId)
			_ = err
		})
	})

	t.Run("job factory exists", func(t *testing.T) {
		assert.NotNil(t, asynq.CacheTableIpsConditionJob)
		assert.IsType(t, &asynq.LaravelJobFactory{}, asynq.CacheTableIpsConditionJob)
	})
}

func TestDispatchLaravelCreateSensitiveKeywordJob(t *testing.T) {
	t.Run("function exists and callable", func(t *testing.T) {
		// 验证函数存在且可调用，不会panic
		assert.NotPanics(t, func() {
			err := DispatchLaravelCreateSensitiveKeywordJob(123, "test keyword", true, false, "task123")
			_ = err
		})
	})

	t.Run("empty keyword", func(t *testing.T) {
		assert.NotPanics(t, func() {
			err := DispatchLaravelCreateSensitiveKeywordJob(789, "", false, false, "")
			_ = err
		})
	})

	t.Run("special characters in keyword", func(t *testing.T) {
		specialKeyword := "测试关键词!@#$%^&*()"
		assert.NotPanics(t, func() {
			err := DispatchLaravelCreateSensitiveKeywordJob(999, specialKeyword, true, true, "special_task")
			_ = err
		})
	})

	t.Run("boolean parameters", func(t *testing.T) {
		// 测试不同的布尔值组合
		assert.NotPanics(t, func() {
			err := DispatchLaravelCreateSensitiveKeywordJob(1, "test", true, true, "task")
			_ = err
		})

		assert.NotPanics(t, func() {
			err := DispatchLaravelCreateSensitiveKeywordJob(2, "test", false, false, "task")
			_ = err
		})
	})

	t.Run("job factory exists", func(t *testing.T) {
		assert.NotNil(t, asynq.CreateSensitiveKeywordJob)
		assert.IsType(t, &asynq.LaravelJobFactory{}, asynq.CreateSensitiveKeywordJob)
	})
}

func TestDispatchLaravelTableAssetsDomainsSyncJob(t *testing.T) {
	t.Run("function exists and callable", func(t *testing.T) {
		// 验证函数存在且可调用，不会panic
		domains := []string{"example.com", "test.com"}
		assert.NotPanics(t, func() {
			err := DispatchLaravelTableAssetsDomainsSyncJob(123, "task456", "sync", domains, 10)
			_ = err
		})
	})

	t.Run("nil domains", func(t *testing.T) {
		assert.NotPanics(t, func() {
			err := DispatchLaravelTableAssetsDomainsSyncJob(111, nil, "test", nil, 5)
			_ = err
		})
	})

	t.Run("zero count", func(t *testing.T) {
		assert.NotPanics(t, func() {
			err := DispatchLaravelTableAssetsDomainsSyncJob(222, "task", "type", []string{}, 0)
			_ = err
		})
	})

	t.Run("different taskId types", func(t *testing.T) {
		// 测试string类型的taskId
		assert.NotPanics(t, func() {
			err := DispatchLaravelTableAssetsDomainsSyncJob(123, "string_task_id", "type", []string{"test.com"}, 1)
			_ = err
		})

		// 测试int类型的taskId
		assert.NotPanics(t, func() {
			err := DispatchLaravelTableAssetsDomainsSyncJob(123, 456, "type", []string{"test.com"}, 1)
			_ = err
		})
	})

	t.Run("large domains array", func(t *testing.T) {
		largeDomains := make([]string, 100)
		for i := range largeDomains {
			largeDomains[i] = "example" + string(rune(i)) + ".com"
		}
		assert.NotPanics(t, func() {
			err := DispatchLaravelTableAssetsDomainsSyncJob(1, "task", "type", largeDomains, uint64(len(largeDomains)))
			_ = err
		})
	})

	t.Run("job factory exists", func(t *testing.T) {
		assert.NotNil(t, asynq.TableAssetsDoaminsSyncJob)
		assert.IsType(t, &asynq.LaravelJobFactory{}, asynq.TableAssetsDoaminsSyncJob)
	})
}

func TestDispatchScanForadarAssetJob(t *testing.T) {
	// 由于ScanForadarAssetJob使用OnQueue方法，我们需要简化测试
	t.Run("function exists and callable", func(t *testing.T) {
		// 验证函数存在且可调用
		assert.NotPanics(t, func() {
			// 这里我们只验证函数不会panic，实际的dispatch可能会失败
			// 因为需要真实的Redis连接
			err := DispatchScanForadarAssetJob(12345)
			// 我们不验证错误，因为在测试环境中可能没有Redis
			_ = err
		})
	})

	t.Run("zero task id", func(t *testing.T) {
		assert.NotPanics(t, func() {
			err := DispatchScanForadarAssetJob(0)
			_ = err
		})
	})

	t.Run("large task id", func(t *testing.T) {
		largeTaskId := uint64(18446744073709551615) // max uint64
		assert.NotPanics(t, func() {
			err := DispatchScanForadarAssetJob(largeTaskId)
			_ = err
		})
	})
}

// 边界条件和错误场景测试
func TestEdgeCasesForLaravelJobHelper(t *testing.T) {
	t.Run("multiple concurrent dispatches", func(t *testing.T) {
		// 并发执行多个任务
		done := make(chan bool, 10)
		for i := 0; i < 10; i++ {
			go func() {
				assert.NotPanics(t, func() {
					err := DispatchLaravelCountClueAssetTotalJob()
					_ = err
				})
				done <- true
			}()
		}

		// 等待所有goroutine完成
		for i := 0; i < 10; i++ {
			<-done
		}
	})

	t.Run("very long keyword", func(t *testing.T) {
		longKeyword := string(make([]byte, 10000))
		for i := range longKeyword {
			longKeyword = longKeyword[:i] + "a" + longKeyword[i+1:]
		}

		assert.NotPanics(t, func() {
			err := DispatchLaravelCreateSensitiveKeywordJob(1, longKeyword, true, false, "")
			_ = err
		})
	})

	t.Run("extreme values", func(t *testing.T) {
		// 测试极值
		assert.NotPanics(t, func() {
			err := DispatchLaravelCacheTableIpsConditionJob(^uint64(0)) // max uint64
			_ = err
		})

		assert.NotPanics(t, func() {
			err := DispatchLaravelCacheTableIpsConditionJob(0) // min uint64
			_ = err
		})
	})

	t.Run("unicode and special characters", func(t *testing.T) {
		unicodeKeyword := "测试🚀关键词💻特殊字符"
		assert.NotPanics(t, func() {
			err := DispatchLaravelCreateSensitiveKeywordJob(1, unicodeKeyword, true, false, "unicode_task")
			_ = err
		})
	})
}

// 集成测试（验证函数可调用性）
func TestIntegration(t *testing.T) {
	t.Run("all functions are callable", func(t *testing.T) {
		// 验证所有函数都可以调用而不会panic
		assert.NotPanics(t, func() {
			_ = DispatchLaravelCountClueAssetTotalJob()
		})

		assert.NotPanics(t, func() {
			_ = DispatchLaravelCacheTableIpsConditionJob(123)
		})

		assert.NotPanics(t, func() {
			_ = DispatchLaravelCreateSensitiveKeywordJob(123, "test", true, false, "task")
		})

		assert.NotPanics(t, func() {
			_ = DispatchLaravelTableAssetsDomainsSyncJob(123, "task", "type", []string{"example.com"}, 1)
		})

		assert.NotPanics(t, func() {
			_ = DispatchScanForadarAssetJob(123)
		})
	})

	t.Run("real job factory structure", func(t *testing.T) {
		// 验证实际的任务工厂不为nil
		assert.NotNil(t, asynq.CountClueAssetTotalJob)
		assert.NotNil(t, asynq.CacheTableIpsConditionJob)
		assert.NotNil(t, asynq.CreateSensitiveKeywordJob)
		assert.NotNil(t, asynq.TableAssetsDoaminsSyncJob)
		assert.NotNil(t, asynq.ScanForadarAssetJob)
	})

	t.Run("job factory types", func(t *testing.T) {
		// 验证任务工厂的类型
		assert.IsType(t, &asynq.LaravelJobFactory{}, asynq.CountClueAssetTotalJob)
		assert.IsType(t, &asynq.LaravelJobFactory{}, asynq.CacheTableIpsConditionJob)
		assert.IsType(t, &asynq.LaravelJobFactory{}, asynq.CreateSensitiveKeywordJob)
		assert.IsType(t, &asynq.LaravelJobFactory{}, asynq.TableAssetsDoaminsSyncJob)
		assert.IsType(t, &asynq.LaravelJobFactory{}, asynq.ScanForadarAssetJob)
	})
}

// 参数验证测试
func TestParameterValidation(t *testing.T) {
	t.Run("DispatchLaravelCreateSensitiveKeywordJob parameter types", func(t *testing.T) {
		// 测试不同类型的参数
		assert.NotPanics(t, func() {
			_ = DispatchLaravelCreateSensitiveKeywordJob(0, "", false, false, "")
		})

		assert.NotPanics(t, func() {
			_ = DispatchLaravelCreateSensitiveKeywordJob(^uint64(0), "max_keyword", true, true, "max_task")
		})
	})

	t.Run("DispatchLaravelTableAssetsDomainsSyncJob parameter types", func(t *testing.T) {
		// 测试不同类型的参数
		assert.NotPanics(t, func() {
			_ = DispatchLaravelTableAssetsDomainsSyncJob(0, "", "", nil, 0)
		})

		assert.NotPanics(t, func() {
			_ = DispatchLaravelTableAssetsDomainsSyncJob(123, 456, "type", []string{}, ^uint64(0))
		})

		// 测试interface{}类型的taskId参数
		assert.NotPanics(t, func() {
			_ = DispatchLaravelTableAssetsDomainsSyncJob(123, "string_task_id", "type", []string{"test.com"}, 1)
		})
	})

	t.Run("empty and nil parameters", func(t *testing.T) {
		// 测试空参数
		assert.NotPanics(t, func() {
			_ = DispatchLaravelCreateSensitiveKeywordJob(0, "", false, false, "")
		})

		assert.NotPanics(t, func() {
			_ = DispatchLaravelTableAssetsDomainsSyncJob(0, nil, "", nil, 0)
		})
	})
}
