package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"micro-service/middleware/mysql/ip_history"
	"micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
)

type RecordIpHistoryJobPayload struct {
	UserId     int                      `json:"user_id"`
	CompanyId  int64                    `json:"company_id"`
	Ip         string                   `json:"ip"`
	Data       map[string]interface{}   `json:"data"`
	ThreatData []map[string]interface{} `json:"threat_data"`
	DataType   int                      `json:"data_type"`
	IsCheck    bool                     `json:"is_check"`
}

type ThreatDataByIp map[string][]map[string]interface{}

// RecordIpHistoryJob 记录IP历史任务
// 消息体:
//
//	{
//		"user_id": 1,        // 用户ID，必填
//		"company_id": 1,     // 企业ID，必填
//		"ip": "*******",    // IP地址，必填
//		"data": {},          // 资产数据，可选
//		"threat_data": [],   // 威胁数据，可选
//		"data_type": 0,      // 数据类型：0=资产数据，非0=威胁数据
//		"is_check": false    // 是否为核查：true=漏洞核查，false=漏洞扫描
//	}
func RecordIpHistoryJob(ctx context.Context, t *asyncq.Task) error {
	log.Infof("[RECORD_IP_HISTORY_DEBUG] RecordIpHistoryJob函数开始执行 - payload: %s", string(t.Payload))

	payloadInfo, err := parsePayloadForRecordIpHistoryJob([]byte(t.Payload))
	if err != nil {
		log.Errorf("[RECORD_IP_HISTORY_DEBUG] 解析payload失败: %v", err)
		return err
	}

	log.Infof("[RECORD_IP_HISTORY_DEBUG] payload解析成功 - userId: %d, companyId: %d, ip: %s",
		payloadInfo.UserId, payloadInfo.CompanyId, payloadInfo.Ip)

	// 设置Redis锁防止并发
	lockKey := cache.GetLockKey("record_ip_history", fmt.Sprintf("%d_%s", payloadInfo.UserId, payloadInfo.Ip))
	ok := redis.Lock(lockKey, 600*time.Second) // 10分钟锁
	if !ok {
		log.Error("RecordIpHistoryJob", "设置Redis锁失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"ip":      payloadInfo.Ip,
		})
		return errors.New("设置Redis锁失败")
	}
	defer redis.UnLock(lockKey)

	log.Info("RecordIpHistoryJob", "开始记录IP数据", map[string]interface{}{
		"user_id":    payloadInfo.UserId,
		"company_id": payloadInfo.CompanyId,
		"ip":         payloadInfo.Ip,
		"data_type":  payloadInfo.DataType,
		"is_check":   payloadInfo.IsCheck,
		"start_time": time.Now().Format("2006-01-02 15:04:05"),
	})

	if payloadInfo.DataType == 0 {
		// 资产数据记录
		err = processAssetData(payloadInfo)
	} else {
		// 威胁数据记录
		err = processThreatData(payloadInfo)
	}

	if err != nil {
		log.Error("RecordIpHistoryJob", "处理数据失败", map[string]interface{}{
			"user_id": payloadInfo.UserId,
			"ip":      payloadInfo.Ip,
			"error":   err.Error(),
		})
		return err
	}

	log.Info("RecordIpHistoryJob", "记录IP数据完成", map[string]interface{}{
		"user_id":  payloadInfo.UserId,
		"ip":       payloadInfo.Ip,
		"end_time": time.Now().Format("2006-01-02 15:04:05"),
	})

	return nil
}

// processAssetData 处理资产数据
func processAssetData(payload *RecordIpHistoryJobPayload) error {
	if len(payload.Data) == 0 {
		return nil
	}

	// 删除不需要的字段
	assetsData := make(map[string]interface{})
	for k, v := range payload.Data {
		if k != "host_list" && k != "hosts" {
			assetsData[k] = v
		}
	}

	dataBytes, err := json.Marshal(assetsData)
	if err != nil {
		return fmt.Errorf("序列化资产数据失败: %v", err)
	}

	ipHistoryModel := ip_history.NewModel()
	record := &ip_history.IpHistory{
		UserId:    uint64(payload.UserId),
		CompanyId: payload.CompanyId,
		Ip:        payload.Ip,
		Data:      string(dataBytes),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = ipHistoryModel.Insert(record)
	if err != nil {
		return fmt.Errorf("插入资产数据失败: %v", err)
	}

	return nil
}

// processThreatData 处理威胁数据
func processThreatData(payload *RecordIpHistoryJobPayload) error {
	if len(payload.ThreatData) == 0 {
		return nil
	}

	// 按IP维度分组威胁数据
	threatDataByIp := make(ThreatDataByIp)
	for _, threat := range payload.ThreatData {
		if ipVal, ok := threat["ip"].(string); ok {
			threatDataByIp[ipVal] = append(threatDataByIp[ipVal], threat)
		}
	}

	ipHistoryModel := ip_history.NewModel()

	if payload.IsCheck {
		// 漏洞核查
		return processVulnerabilityCheck(payload, threatDataByIp, ipHistoryModel)
	} else {
		// 漏洞扫描
		return processVulnerabilityScan(payload, threatDataByIp, ipHistoryModel)
	}
}

// processVulnerabilityCheck 处理漏洞核查
func processVulnerabilityCheck(payload *RecordIpHistoryJobPayload, threatDataByIp ThreatDataByIp, model ip_history.IpHistoryModel) error {
	for ip, threats := range threatDataByIp {
		// 获取该IP之前的核查漏洞数据
		existingRecords, err := model.FindByUserIpAndType(uint64(payload.UserId), payload.CompanyId, ip, 1)
		if err != nil {
			return fmt.Errorf("查询现有核查数据失败: %v", err)
		}

		// 提取当前威胁的common_title
		currentTitles := extractCommonTitles(threats)

		var existingTitles []string
		if len(existingRecords) > 0 {
			// 获取最近一条记录的威胁数据
			latestRecord := existingRecords[0]
			if latestRecord.ThreatData != "" {
				err = json.Unmarshal([]byte(latestRecord.ThreatData), &existingTitles)
				if err != nil {
					log.Error("RecordIpHistoryJob", "解析现有威胁数据失败", map[string]interface{}{
						"ip":    ip,
						"error": err.Error(),
					})
					existingTitles = []string{}
				}
			}
		}

		// 找出新增的威胁
		newThreats := findNewThreats(currentTitles, existingTitles)
		if len(newThreats) > 0 {
			newThreatsBytes, _ := json.Marshal(newThreats)
			record := &ip_history.IpHistory{
				UserId:         uint64(payload.UserId),
				CompanyId:      payload.CompanyId,
				Ip:             ip,
				ThreatData:     string(newThreatsBytes),
				ThreatDataType: 1, // 漏洞核查
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
			}

			err = model.Insert(record)
			if err != nil {
				return fmt.Errorf("插入核查数据失败: %v", err)
			}
		}
	}

	return nil
}

// processVulnerabilityScan 处理漏洞扫描
func processVulnerabilityScan(payload *RecordIpHistoryJobPayload, threatDataByIp ThreatDataByIp, model ip_history.IpHistoryModel) error {
	for ip, threats := range threatDataByIp {
		// 获取该IP最近一条扫描数据
		existingRecords, err := model.FindByUserIpAndType(uint64(payload.UserId), payload.CompanyId, ip, 2)
		if err != nil {
			return fmt.Errorf("查询现有扫描数据失败: %v", err)
		}

		// 提取当前威胁的common_title
		currentTitles := extractCommonTitles(threats)

		var existingTitles []string
		var latestScanRecord *ip_history.IpHistory

		if len(existingRecords) > 0 {
			latestScanRecord = &existingRecords[0]
			if latestScanRecord.ThreatData != "" {
				err = json.Unmarshal([]byte(latestScanRecord.ThreatData), &existingTitles)
				if err != nil {
					log.Error("RecordIpHistoryJob", "解析现有扫描数据失败", map[string]interface{}{
						"ip":    ip,
						"error": err.Error(),
					})
					existingTitles = []string{}
				}
			}
		}

		// 找出新增的威胁
		newThreats := findNewThreats(currentTitles, existingTitles)
		if len(newThreats) > 0 {
			newThreatsBytes, _ := json.Marshal(newThreats)
			record := &ip_history.IpHistory{
				UserId:         uint64(payload.UserId),
				CompanyId:      payload.CompanyId,
				Ip:             ip,
				ThreatData:     string(newThreatsBytes),
				ThreatDataType: 2, // 漏洞扫描
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
			}

			err = model.Insert(record)
			if err != nil {
				return fmt.Errorf("插入扫描数据失败: %v", err)
			}
		} else if latestScanRecord != nil {
			// 检查是否有中间的核查修复记录
			fixRecords, err := model.FindByUserIpTypeAfterID(uint64(payload.UserId), payload.CompanyId, ip, 1, latestScanRecord.Id)
			if err != nil {
				log.Error("RecordIpHistoryJob", "查询修复记录失败", map[string]interface{}{
					"ip":    ip,
					"error": err.Error(),
				})
				continue
			}

			if len(fixRecords) > 0 {
				// 收集所有修复的POC名称
				allFixedPocMap := make(map[string]bool)
				for _, record := range fixRecords {
					if record.ThreatData != "" {
						var fixedPocs []string
						err = json.Unmarshal([]byte(record.ThreatData), &fixedPocs)
						if err == nil {
							for _, fixedPoc := range fixedPocs {
								allFixedPocMap[fixedPoc] = true
							}
						}
					}
				}

				// 检查当前扫描结果中是否有已修复的漏洞重新出现
				// 使用map去重，避免重复添加
				seen := make(map[string]bool)
				var reappearedThreats []string

				for _, currentTitle := range currentTitles {
					// 检查是否是已修复的POC且未被处理过
					if allFixedPocMap[currentTitle] && !seen[currentTitle] {
						seen[currentTitle] = true
						reappearedThreats = append(reappearedThreats, currentTitle)
					}
				}

				if len(reappearedThreats) > 0 {
					reappearedThreatsBytes, _ := json.Marshal(reappearedThreats)
					record := &ip_history.IpHistory{
						UserId:         uint64(payload.UserId),
						CompanyId:      payload.CompanyId,
						Ip:             ip,
						ThreatData:     string(reappearedThreatsBytes),
						ThreatDataType: 2, // 漏洞扫描
						CreatedAt:      time.Now(),
						UpdatedAt:      time.Now(),
					}

					err = model.Insert(record)
					if err != nil {
						return fmt.Errorf("插入重现威胁数据失败: %v", err)
					}
				}
			}
		}
	}

	return nil
}

// extractCommonTitles 从威胁数据中提取common_title
func extractCommonTitles(threats []map[string]interface{}) []string {
	var titles []string
	titleSet := make(map[string]bool)

	for _, threat := range threats {
		if commonTitle, ok := threat["common_title"].(string); ok && commonTitle != "" {
			if !titleSet[commonTitle] {
				titles = append(titles, commonTitle)
				titleSet[commonTitle] = true
			}
		}
	}

	return titles
}

// findNewThreats 找出新增的威胁
func findNewThreats(current, existing []string) []string {
	existingSet := make(map[string]bool)
	for _, title := range existing {
		existingSet[title] = true
	}

	threats := make(map[string]bool)
	var newThreats []string
	for _, title := range current {
		if !existingSet[title] {
			threats[title] = true
			newThreats = append(newThreats, title)
		}
	}

	return newThreats
}

func parsePayloadForRecordIpHistoryJob(payload []byte) (payloadInfo *RecordIpHistoryJobPayload, err error) {
	err = json.Unmarshal(payload, &payloadInfo)
	if err != nil {
		log.Error("RecordIpHistoryJob", "解析payload失败", map[string]interface{}{
			"payload": string(payload),
			"error":   err.Error(),
		})
		return nil, err
	}

	// 验证必填字段
	if payloadInfo.UserId == 0 {
		return nil, fmt.Errorf("用户ID不能为空")
	}
	// 企业ID可以为0，这是正常现象，不需要验证
	if strings.TrimSpace(payloadInfo.Ip) == "" {
		return nil, fmt.Errorf("IP地址不能为空")
	}

	return payloadInfo, nil
}
