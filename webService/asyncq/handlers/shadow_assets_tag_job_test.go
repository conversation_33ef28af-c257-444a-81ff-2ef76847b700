package handlers

import (
	"encoding/json"
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"micro-service/middleware/elastic/fofaee_assets"
	asynq "micro-service/pkg/queue_helper"
)

func TestParseSureIPs(t *testing.T) {
	tests := []struct {
		name          string
		importSureIps string
		want          []string
		wantErr       error
	}{
		{
			name:          "空字符串",
			importSureIps: "",
			want:          []string{},
			wantErr:       nil,
		},
		{
			name:          "有效JSON",
			importSureIps: `["***********", "********"]`,
			want:          []string{"***********", "********"},
			wantErr:       nil,
		},
		{
			name:          "无效JSON",
			importSureIps: `["***********", "********"`,
			want:          nil,
			wantErr:       fmt.Errorf("解析ImportSureIps JSON失败: %v", fmt.<PERSON>rrorf("unexpected end of JSON input")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseSureIPs(tt.importSureIps)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestNormalizeSureIPs(t *testing.T) {
	tests := []struct {
		name    string
		sureIPs []string
		want    []string
	}{
		{
			name:    "空列表",
			sureIPs: []string{},
			want:    []string{},
		},
		{
			name:    "有效IPv4和IPv6地址",
			sureIPs: []string{"***********", "fe80::1"},
			want:    []string{"***********", "fe80:0000:0000:0000:0000:0000:0000:0001"},
		},
		{
			name:    "无效IP地址",
			sureIPs: []string{"********"},
			want:    []string{"********"},
		},
		{
			name:    "IPv6地址缩写",
			sureIPs: []string{"::1", "fe80::1:0:0:1"},
			want:    []string{"0000:0000:0000:0000:0000:0000:0000:0001", "fe80:0000:0000:0000:0001:0000:0000:0001"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := normalizeSureIPs(tt.sureIPs)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestNormalizeIP(t *testing.T) {
	tests := []struct {
		name string
		ip   string
		want string
	}{
		{
			name: "IPv4地址",
			ip:   "***********",
			want: "***********",
		},
		{
			name: "IPv6地址",
			ip:   "fe80::1",
			want: "fe80:0000:0000:0000:0000:0000:0000:0001",
		},
		{
			name: "空IP",
			ip:   "",
			want: "",
		},
		{
			name: "IPv6地址缩写",
			ip:   "::1",
			want: "0000:0000:0000:0000:0000:0000:0000:0001",
		},
		{
			name: "IPv6地址混合缩写",
			ip:   "fe80::1:0:0:1",
			want: "fe80:0000:0000:0000:0001:0000:0000:0001",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := normalizeIP(tt.ip)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestParsePayloadForShadowAssetsTag(t *testing.T) {
	type args struct {
		payload []byte
	}
	tests := []struct {
		name    string
		args    args
		want    *asynq.ShadowAssetsTagJobPayload
		wantErr error
	}{
		{
			name: "正常解析",
			args: args{
				payload: []byte(`{"user_id": 1, "task_id": 1, "detect_task_id": 1}`),
			},
			want: &asynq.ShadowAssetsTagJobPayload{
				UserId:       1,
				TaskId:       1,
				DetectTaskId: 1,
			},
			wantErr: nil,
		},
		{
			name: "解析失败",
			args: args{
				payload: []byte(`{"user_id": 1, "task_id": 1, "detect_task_id": 1`),
			},
			want:    nil,
			wantErr: &json.SyntaxError{},
		},
		{
			name: "用户ID为空",
			args: args{
				payload: []byte(`{"user_id": 0, "task_id": 1, "detect_task_id": 1}`),
			},
			want:    nil,
			wantErr: errors.New("用户ID不能为空"),
		},
		{
			name: "任务ID为空",
			args: args{
				payload: []byte(`{"user_id": 1, "task_id": 0, "detect_task_id": 1}`),
			},
			want:    nil,
			wantErr: errors.New("任务ID不能为空"),
		},
		{
			name: "测绘任务ID为空",
			args: args{
				payload: []byte(`{"user_id": 1, "task_id": 1, "detect_task_id": 0}`),
			},
			want:    nil,
			wantErr: errors.New("测绘任务ID不能为空"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parsePayloadForShadowAssetsTag(tt.args.payload)

			if tt.wantErr != nil {
				assert.Error(t, err)
				if _, ok := tt.wantErr.(*json.SyntaxError); ok {
					assert.IsType(t, tt.wantErr, err)
				} else {
					assert.EqualError(t, err, tt.wantErr.Error())
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

// TestQueryClaimedAssetsCondition 测试查询已认领资产的条件构建
func TestQueryClaimedAssetsCondition(t *testing.T) {
	tests := []struct {
		name     string
		userId   int
		taskId   int
		expected *fofaee_assets.FindCondition
	}{
		{
			name:   "正常查询条件",
			userId: 123,
			taskId: 456,
			expected: &fofaee_assets.FindCondition{
				UserId: 123,
				TaskId: 456,
				Status: []int{fofaee_assets.STATUS_CLAIMED},
			},
		},
		{
			name:   "taskId为0的情况",
			userId: 123,
			taskId: 0,
			expected: &fofaee_assets.FindCondition{
				UserId: 123,
				TaskId: 0,
				Status: []int{fofaee_assets.STATUS_CLAIMED},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 构建查询条件（模拟 queryClaimedAssets 函数中的逻辑）
			condition := &fofaee_assets.FindCondition{
				UserId: uint64(tt.userId),
				TaskId: tt.taskId,
				Status: []int{fofaee_assets.STATUS_CLAIMED},
			}

			// 验证条件是否正确构建
			assert.Equal(t, uint64(tt.expected.UserId), condition.UserId)
			assert.Equal(t, tt.expected.TaskId, condition.TaskId)
			assert.Equal(t, tt.expected.Status, condition.Status)
		})
	}
}

// TestFindConditionTaskIdField 测试 FindCondition 结构体是否包含 TaskId 字段
func TestFindConditionTaskIdField(t *testing.T) {
	condition := &fofaee_assets.FindCondition{}

	// 设置 TaskId 字段
	condition.TaskId = 123

	// 验证字段是否正确设置
	assert.Equal(t, 123, condition.TaskId)

	// 验证其他字段也能正常工作
	condition.UserId = 456
	condition.Status = []int{fofaee_assets.STATUS_CLAIMED}

	assert.Equal(t, uint64(456), condition.UserId)
	assert.Equal(t, []int{fofaee_assets.STATUS_CLAIMED}, condition.Status)
}
