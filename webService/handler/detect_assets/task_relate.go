package detect_assets

import (
	"context"
	"fmt"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/middleware/mysql/task"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	asyncq "micro-service/pkg/queue_helper"
	pb "micro-service/webService/proto"

	"go-micro.dev/v4/errors"
)

// TaskRelate 资产测绘关联异步任务
func TaskRelate(ctx context.Context, req *pb.TaskRelateRequest, rsp *pb.Empty) error {
	log.WithContextInfof(ctx, "资产测绘关联异步任务 - 开始处理: %+v", req)

	// 查询测绘任务信息
	detectAssetsInfo, err := detect_assets_tasks.NewModel().First(
		detect_assets_tasks.WithUserID(uint64(req.UserId)),
		detect_assets_tasks.WithID(uint64(req.ExpendId)),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 查询任务信息失败: %v", err)
		return errors.BadRequest(pb.ServiceName, "参数错误!")
	}

	if detectAssetsInfo == nil {
		log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 任务不存在: user_id=%d, expend_id=%d", req.UserId, req.ExpendId)
		return errors.BadRequest(pb.ServiceName, "参数错误!")
	}

	// 查询关联的扫描任务
	taskList, err := task.NewModel().FindAll(
		task.WithUserID(uint64(req.UserId)),
		task.WithDetectAssetsTasksID(uint64(req.ExpendId)),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 查询关联任务失败: %v", err)
		return errors.InternalServerError(pb.ServiceName, "查询关联任务失败")
	}

	// 提取任务ID列表
	tasks := make([]uint64, 0, len(taskList))
	for _, t := range taskList {
		tasks = append(tasks, uint64(t.ID))
	}

	// 判断是否有关联任务
	if len(tasks) == 0 {
		log.WithContextWarnf(ctx, "资产测绘关联异步任务 - 无关联任务: detect_assets_tasks_id=%d", detectAssetsInfo.ID)

		// 更新测绘任务状态
		updateData := map[string]interface{}{
			"is_extract_clue": req.IsExtractClue,
			"step":            detect_assets_tasks.StepFive,
			"step_detail":     detect_assets_tasks.StepFiveSync,
			"step_status":     detect_assets_tasks.StepStatusDone,
			"status":          detect_assets_tasks.StatusFinished,
		}

		// 根据来源类型添加不同的字段
		if detectAssetsInfo.ExpandSource != detect_assets_tasks.ExpandSourceDetect {
			updateData["is_check_risk"] = req.IsCheckRisk
			updateData["is_auto_business_api"] = req.IsAutoBusinessApi
		}

		err = detect_assets_tasks.NewModel().UpdateAny(updateData,
			detect_assets_tasks.WithID(uint64(req.ExpendId)),
			detect_assets_tasks.WithUserID(uint64(req.UserId)),
		)
		if err != nil {
			log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 更新任务状态失败: %v", err)
			return errors.InternalServerError(pb.ServiceName, "更新任务状态失败")
		}

		return nil
	}

	// 有关联任务，处理异步任务
	log.WithContextInfof(ctx, "资产测绘关联异步任务 - 关联任务处理: task_ids=%v", tasks)

	// 构建搜索条件
	search := map[string]interface{}{
		"user_id":  req.UserId,
		"status":   1, // ForadarAssets::STATUS_CLAIMED
		"is_all":   1,
		"task_ids": tasks,
	}

	// 获取测绘任务ID和分组ID
	deteId := uint64(req.ExpendId)
	groupId := detectAssetsInfo.GroupId

	// 1. 处理自动提取线索
	if req.IsExtractClue == 1 && detectAssetsInfo.ExpandSource != detect_assets_tasks.ExpandSourceDetect {
		if cfg.ExecGolangJob() {
			//todo 调用go的job
			err = asyncq.ExtractAssetCluesJob.Dispatch(req.UserId, search, deteId, groupId)
			if err != nil {
				log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 自动提取线索任务入队失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "资产测绘关联异步任务 - 自动提取线索任务下发成功: task_ids=%v", tasks)
			}
		} else {
			log.WithContextInfof(ctx, "资产测绘关联异步任务 - 自动提取线索任务下发: task_ids=%v", tasks)
			err = asyncq.ExtractAssetCluesJob.Dispatch(req.UserId, search, deteId, groupId)
			if err != nil {
				log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 自动提取线索任务入队失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "资产测绘关联异步任务 - 自动提取线索任务下发成功: task_ids=%v", tasks)
			}
		}
	} else {
		log.WithContextInfof(ctx, "资产测绘关联异步任务 - 不自动提取线索: is_extract_clue=%d, expand_source=%d",
			req.IsExtractClue, detectAssetsInfo.ExpandSource)
	}

	// 2. 处理自动监测风险事件
	if req.IsCheckRisk == 1 {
		for _, taskId := range tasks {
			if cfg.ExecGolangJob() {
				// // 调用go的job
				// err = asyncq.Enqueue(ctx, asyncq.UpdateRiskTypeAssetJob, asyncq.UpdateRiskTypeAssetJobPayload{
				// 	UserId:       uint64(req.UserId),
				// 	TaskId:       taskId,
				// 	CompanyId:    uint64(req.CompanyId),
				// 	DetectTaskId: &deteId,
				// })
				// if err != nil {
				// 	log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 计算风险事件资产任务入队失败: %v", err)
				// } else {
				// 	log.WithContextInfof(ctx, "资产测绘关联异步任务 - 计算风险事件资产任务入队成功: task_id=%d, detect_assets_id=%d", taskId, deteId)
				// }
				err = asyncq.UpdateRiskTypeAssets.Dispatch(req.UserId, taskId, req.CompanyId, deteId)
				if err != nil {
					log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 计算风险事件资产任务入队失败: %v", err)
				} else {
					log.WithContextInfof(ctx, "资产测绘关联异步任务 - 计算风险事件资产任务入队成功: task_id=%d, detect_assets_id=%d", taskId, deteId)
				}
			} else {
				err = asyncq.UpdateRiskTypeAssets.Dispatch(req.UserId, taskId, req.CompanyId, deteId)
				if err != nil {
					log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 计算风险事件资产任务入队失败: %v", err)
				} else {
					log.WithContextInfof(ctx, "资产测绘关联异步任务 - 计算风险事件资产任务入队成功: task_id=%d, detect_assets_id=%d", taskId, deteId)
				}
			}
		}
	}

	// 更新测绘任务状态
	updateData := map[string]interface{}{
		"is_extract_clue": req.IsExtractClue,
		"step":            detect_assets_tasks.StepFive,
		"step_detail":     detect_assets_tasks.StepFiveSync,
		"step_status":     detect_assets_tasks.StepStatusDone,
		"status":          detect_assets_tasks.StatusFinished,
	}

	// 根据来源类型添加不同的字段
	if detectAssetsInfo.ExpandSource != detect_assets_tasks.ExpandSourceDetect {
		updateData["is_check_risk"] = req.IsCheckRisk
		updateData["is_auto_business_api"] = req.IsAutoBusinessApi
	}

	err = detect_assets_tasks.NewModel().UpdateAny(updateData,
		detect_assets_tasks.WithID(uint64(req.ExpendId)),
		detect_assets_tasks.WithUserID(uint64(req.UserId)),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 更新任务状态失败: %v", err)
		return errors.InternalServerError(pb.ServiceName, "更新任务状态失败")
	}

	// 3. 处理单位测绘任务简报
	if detectAssetsInfo.ExpandSource == detect_assets_tasks.ExpandSourceDetect &&
		detectAssetsInfo.CreatedAt.Format("2006-01-02 15:04:05") > "2023-11-09 18:30:00" {
		if cfg.ExecGolangJob() {
			//todo 调用go的job
			err = asyncq.DetectReportJob.Dispatch(req.UserId, deteId)
			if err != nil {
				log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 生成任务简报入队失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "资产测绘关联异步任务 - 生成任务简报成功: user_id=%d, detect_task_id=%d", req.UserId, deteId)
			}
		} else {
			log.WithContextInfof(ctx, "资产测绘关联异步任务 - 生成任务简报: user_id=%d, detect_task_id=%d", req.UserId, deteId)
			err = asyncq.DetectReportJob.Dispatch(req.UserId, deteId)
			if err != nil {
				log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 生成任务简报入队失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "资产测绘关联异步任务 - 生成任务简报成功: user_id=%d, detect_task_id=%d", req.UserId, deteId)
			}
		}
	}

	// 4. 处理业务系统同步
	if req.IsAutoBusinessApi > 0 && detectAssetsInfo.ExpandSource == detect_assets_tasks.ExpandSourceRecommend {

		if cfg.ExecGolangJob() {
			//todo 调用go的job
			err = asyncq.BussinessAssets.Dispatch(req.UserId, tasks, req.CompanyId, deteId, 1)
			if err != nil {
				log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 同步URL资产到业务系统入队失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "资产测绘关联异步任务 - 同步URL资产到业务系统-成功: user_id=%d, detect_task_id=%d", req.UserId, deteId)
			}

		} else {
			err = asyncq.BussinessAssets.Dispatch(req.UserId, tasks, req.CompanyId, deteId, 1)
			if err != nil {
				log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 同步URL资产到业务系统入队失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "资产测绘关联异步任务 - 同步URL资产到业务系统-成功: user_id=%d, detect_task_id=%d", req.UserId, deteId)
			}
		}
	}

	// 记录操作日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(req.UserId),
		fmt.Sprintf("资产测绘关联异步任务, 任务id：%d", req.ExpendId),
		metadata.MustString(ctx, "client_ip"),
		uint64(req.CompanyId),
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 记录操作日志失败: %v", err)
	}

	return nil
}
