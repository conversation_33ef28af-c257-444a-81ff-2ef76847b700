package detect_assets

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	corePb "micro-service/coreService/proto"
	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clue_black_keyword"
	"micro-service/middleware/mysql/clue_task"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/clues_groups"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/company_clues"
	"micro-service/middleware/mysql/company_equity"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/detect_task_report_result"
	"micro-service/middleware/mysql/domain_task"
	"micro-service/middleware/mysql/foradar_report"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/middleware/mysql/organization_company_clues"
	"micro-service/middleware/mysql/phishing_fake_task"
	"micro-service/middleware/mysql/report_template"
	"micro-service/middleware/mysql/sensitive_data"
	"micro-service/middleware/mysql/sensitive_keyword"
	"micro-service/middleware/mysql/task"
	user2 "micro-service/middleware/mysql/user"
	redisMiddleware "micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	clues_pkg "micro-service/pkg/clues"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	"micro-service/pkg/microx"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"net/http"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type DetectAssetsHandler struct {
	detectAssetsTaskModel detect_assets_tasks.DetectAssetsTaskModel
	taskModel             task.TaskModel
	domainTaskModel       domain_task.DomainTaskModel
}

// CacheResult FOFA资产缓存结果结构
type CacheResult struct {
	Num     uint32 `json:"num"`
	FofaUrl string `json:"fofa_url"`
}

// parseFofaAssetsCache 解析 FOFA 资产缓存数据（兼容 PHP 序列化和 JSON 格式）
func parseFofaAssetsCache(ctx context.Context, cacheKey string) (*CacheResult, error) {
	redisClient := redisMiddleware.GetClient()
	rawValue, rawErr := redisClient.Get(ctx, cacheKey).Result()
	if rawErr != nil {
		// 检查是否是 key 不存在的错误
		if rawErr.Error() == "redis: nil" {
			log.WithContextInfof(ctx, "[parseFofaAssetsCache] 缓存键不存在: %s", cacheKey)
			return nil, fmt.Errorf("缓存键不存在")
		}
		log.WithContextErrorf(ctx, "[parseFofaAssetsCache] Redis获取失败: %s, 错误: %v", cacheKey, rawErr)
		return nil, rawErr
	}

	log.WithContextInfof(ctx, "[parseFofaAssetsCache] 缓存获取成功: %s, 数据长度: %d, 前100字符: %.100s",
		cacheKey, len(rawValue), rawValue)

	var result CacheResult

	// 解析 PHP 序列化的数据
	if strings.HasPrefix(rawValue, "s:") {
		// 找到第一个冒号后的引号
		firstQuote := strings.Index(rawValue, `:"`)
		if firstQuote > 0 {
			// 提取引号内的 JSON 字符串
			jsonStart := firstQuote + 2
			jsonEnd := strings.LastIndex(rawValue, `"`)
			if jsonEnd > jsonStart {
				jsonStr := rawValue[jsonStart:jsonEnd]
				// 反转义 JSON 字符串
				jsonStr = strings.ReplaceAll(jsonStr, `\/`, `/`)

				log.WithContextInfof(ctx, "[parseFofaAssetsCache] PHP序列化解析，提取JSON: %s", jsonStr)

				// 解析 JSON
				if err := json.Unmarshal([]byte(jsonStr), &result); err == nil {
					log.WithContextInfof(ctx, "[parseFofaAssetsCache] PHP序列化解析成功: num=%d, fofa_url=%s", result.Num, result.FofaUrl)
					return &result, nil
				} else {
					log.WithContextErrorf(ctx, "[parseFofaAssetsCache] PHP序列化JSON解析失败: %v", err)
					return nil, fmt.Errorf("PHP序列化数据中的JSON解析失败: %v", err)
				}
			}
		}
		log.WithContextErrorf(ctx, "[parseFofaAssetsCache] PHP序列化数据格式错误: %s", rawValue)
		return nil, fmt.Errorf("PHP序列化数据格式错误")
	} else {
		// 尝试直接解析为 JSON（兼容新的 Go 格式）
		log.WithContextInfof(ctx, "[parseFofaAssetsCache] 尝试直接JSON解析: %s", rawValue)
		if err := json.Unmarshal([]byte(rawValue), &result); err == nil {
			log.WithContextInfof(ctx, "[parseFofaAssetsCache] 直接JSON解析成功: num=%d, fofa_url=%s", result.Num, result.FofaUrl)
			return &result, nil
		} else {
			log.WithContextErrorf(ctx, "[parseFofaAssetsCache] 直接JSON解析失败: %v", err)
			return nil, fmt.Errorf("JSON格式解析失败: %v", err)
		}
	}
}

func NewDetectAssetsHandler() *DetectAssetsHandler {
	return &DetectAssetsHandler{
		detectAssetsTaskModel: detect_assets_tasks.NewModel(),
		taskModel:             task.NewModel(),
		domainTaskModel:       domain_task.NewDomainTaskModel(),
	}
}

// Info 获取任务详情
func (h *DetectAssetsHandler) Info(_ context.Context, req *pb.DetectAssetsInfoRequest, rsp *pb.DetectAssetsInfoResponse) error {
	// 从请求中获取用户ID
	userID := req.UserId

	// 如果提供了任务ID，查询指定任务
	if req.Id > 0 {
		info, err := h.detectAssetsTaskModel.First(
			detect_assets_tasks.WithUserID(userID),
			detect_assets_tasks.WithID(req.Id),
		)
		if err != nil {
			return err
		}
		if info == nil {
			return errors.New("任务不存在")
		}

		// 获取关联的扫描任务ID列表
		scanTaskIDs, err := h.taskModel.FindAll(
			task.WithUserID(userID),
			task.WithDetectAssetsTasksID(uint64(info.ID)),
		)
		if err != nil {
			return err
		}

		// 获取关联的域名任务ID列表
		domainTaskIDs, err := h.domainTaskModel.FindAll(
			domain_task.WithUserID(userID),
			domain_task.WithDetectTaskID(uint64(info.ID)),
		)
		if err != nil {
			return err
		}

		// 转换任务ID列表
		scanTaskIDList := make([]uint64, 0, len(scanTaskIDs))
		for _, t := range scanTaskIDs {
			scanTaskIDList = append(scanTaskIDList, uint64(t.ID))
		}

		domainTaskIDList := make([]uint64, 0, len(domainTaskIDs))
		for _, t := range domainTaskIDs {
			domainTaskIDList = append(domainTaskIDList, t.Id)
		}

		// 构建响应
		*rsp = pb.DetectAssetsInfoResponse{
			Id:                        uint64(info.ID),
			UserId:                    info.UserId,
			CompanyId:                 info.CompanyId,
			SafeUserId:                info.SafeUserId,
			Name:                      info.Name,
			Step:                      int32(info.Step),
			StepDetail:                int32(info.StepDetail),
			DetectType:                int32(info.DetectType),
			GroupId:                   info.GroupId,
			StepStatus:                int32(info.StepStatus),
			Status:                    int32(info.Status),
			ExpendFlags:               info.ExpendFlags,
			ExpendProgress:            float64(info.ExpendProgress),
			Progress:                  float64(info.Progress),
			ClueProgress:              float64(info.ClueProgress),
			CreatedAt:                 info.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:                 info.UpdatedAt.Format("2006-01-02 15:04:05"),
			UpdateAssetsLevelProgress: float64(info.UpdateAssetsLevelProgress),
			ReturnJson:                info.ReturnJson,
			SureIpNum:                 info.SureIpNum,
			UnsureIpNum:               info.UnsureIpNum,
			ThreatenIpNum:             info.ThreatenIpNum,
			ExpendFirstStepClueNode:   info.ExpendFirstStepClueNode,
			IsExtractClue:             int32(info.IsExtractClue),
			IsCheckRisk:               int32(info.IsCheckRisk),
			ExpandSource:              int32(info.ExpandSource),
			IsIntellectMode:           int32(info.IsIntellectMode),
			IsIntellectFailed:         int32(info.IsIntellectFailed),
			CluesCount:                info.CluesCount,
			ScanTaskIds:               scanTaskIDList,
			DomainTaskIds:             domainTaskIDList,
			CompanyJson:               info.CompanyJSON,
		}
		return nil
	}

	// 如果没有提供任务ID，查询当前用户未完成的任务
	info, err := h.detectAssetsTaskModel.First(
		detect_assets_tasks.WithUserID(userID),
		detect_assets_tasks.WithExpandSource(int(req.ExpandSource)),
		detect_assets_tasks.WithStepLessThan(5),
		detect_assets_tasks.WithStatusIn([]int{
			detect_assets_tasks.StatusDoing,
			detect_assets_tasks.StatusExceptionFinished,
		}),
	)
	if err != nil {
		return err
	}
	if info == nil {
		return nil // 如果没有未完成的任务，返回nil
	}

	// 获取关联的扫描任务ID列表
	scanTaskIDs, err := h.taskModel.FindAll(
		task.WithUserID(userID),
		task.WithDetectAssetsTasksID(uint64(info.ID)),
	)
	if err != nil {
		return err
	}

	// 获取关联的域名任务ID列表
	domainTaskIDs, err := h.domainTaskModel.FindAll(
		domain_task.WithUserID(userID),
		domain_task.WithDetectTaskID(uint64(info.ID)),
	)
	if err != nil {
		return err
	}

	// 转换任务ID列表
	scanTaskIDList := make([]uint64, 0, len(scanTaskIDs))
	for _, t := range scanTaskIDs {
		scanTaskIDList = append(scanTaskIDList, uint64(t.ID))
	}

	domainTaskIDList := make([]uint64, 0, len(domainTaskIDs))
	for _, t := range domainTaskIDs {
		domainTaskIDList = append(domainTaskIDList, t.Id)
	}

	// 构建响应
	*rsp = pb.DetectAssetsInfoResponse{
		Id:                        uint64(info.ID),
		UserId:                    info.UserId,
		CompanyId:                 info.CompanyId,
		SafeUserId:                info.SafeUserId,
		Name:                      info.Name,
		Step:                      int32(info.Step),
		StepDetail:                int32(info.StepDetail),
		DetectType:                int32(info.DetectType),
		GroupId:                   info.GroupId,
		StepStatus:                int32(info.StepStatus),
		Status:                    int32(info.Status),
		ExpendFlags:               info.ExpendFlags,
		ExpendProgress:            float64(info.ExpendProgress),
		Progress:                  float64(info.Progress),
		ClueProgress:              float64(info.ClueProgress),
		CreatedAt:                 info.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:                 info.UpdatedAt.Format("2006-01-02 15:04:05"),
		UpdateAssetsLevelProgress: float64(info.UpdateAssetsLevelProgress),
		ReturnJson:                info.ReturnJson,
		SureIpNum:                 info.SureIpNum,
		UnsureIpNum:               info.UnsureIpNum,
		ThreatenIpNum:             info.ThreatenIpNum,
		ExpendFirstStepClueNode:   info.ExpendFirstStepClueNode,
		IsExtractClue:             int32(info.IsExtractClue),
		IsCheckRisk:               int32(info.IsCheckRisk),
		ExpandSource:              int32(info.ExpandSource),
		IsIntellectMode:           int32(info.IsIntellectMode),
		IsIntellectFailed:         int32(info.IsIntellectFailed),
		CluesCount:                info.CluesCount,
		ScanTaskIds:               scanTaskIDList,
		DomainTaskIds:             domainTaskIDList,
		CompanyJson:               info.CompanyJSON,
	}
	return nil
}

// StartSyncJob 处理资产测绘同步任务
func StartSyncJob(ctx context.Context, req *pb.StartSyncJobRequest, rsp *pb.StartSyncJobResponse) error {
	// 校验当前企业是否还有测绘次数
	if req.CompanyId > 0 {
		companyInfo, err := company.NewCompanyModel().FindById(req.CompanyId, 0)
		if err != nil {
			return err
		}
		limitCount := companyInfo.CheckLimit("cloud_recommend", 0)
		if limitCount < 0 {
			return errors.New("已达到限制次数，无法再进行测绘任务")
		}
	}

	// 1. 获取任务详情
	detectAssetsTask, err := detect_assets_tasks.NewModel().First(detect_assets_tasks.WithID(req.DetectTaskId), detect_assets_tasks.WithUserID(req.UserId))
	if err != nil {
		return errors.New("该任务不存在，参数错误")
	}
	if detectAssetsTask == nil {
		return errors.New("任务不存在")
	}
	// 2. 处理 companyList
	var returnJson map[string]interface{}
	_ = json.Unmarshal([]byte(detectAssetsTask.ReturnJson), &returnJson)
	companyList := []string{}
	for k, v := range returnJson {
		if _, err := strconv.Atoi(k); err == nil {
			if arr, ok := v.([]interface{}); ok {
				for _, co := range arr {
					name, ok := co.(string)
					if !ok {
						continue
					}
					companyList = append(companyList, name)
					// 括号互转逻辑
					if strings.Count(name, "(") > 0 {
						newco := strings.ReplaceAll(strings.ReplaceAll(name, "(", "（"), ")", "）")
						companyList = append(companyList, newco)
					}
					if strings.Count(name, "（") > 0 {
						newco := strings.ReplaceAll(strings.ReplaceAll(name, "（", "("), "）", ")")
						companyList = append(companyList, newco)
					}
				}
			}
		}
	}

	// 3. 获取 preIds
	clueModel := clues_groups.NewCluesGrouper()
	var preIds []int64
	if dbClue, ok := clueModel.(interface{ DB() *gorm.DB }); ok {
		_ = dbClue.DB().Model(&clues.Clue{}).
			Where("user_id = ? AND group_id = ?", req.UserId, detectAssetsTask.GroupId).
			Where("is_from_check_table = ?", clues.YES_FROM_CHECK_TABLE).
			Where("status = ?", clues.CLUE_PASS_STATUS).
			Where("is_deleted = ?", clues.NOT_DELETE).
			Pluck("id", &preIds).Error
	}

	// 4. 获取企业的初始线索 DetectGolangCluesJob
	preIdsUint64 := make([]uint64, len(preIds))
	for i, id := range preIds {
		preIdsUint64[i] = uint64(id)
	}
	if cfg.ExecGolangJob() {
		payload := asyncq.NewDetectGolangCluesJobPayload(
			companyList,
			req.UserId,
			req.CompanyId,
			uint64(detectAssetsTask.ID),
			detectAssetsTask.GroupId,
			false,
			preIdsUint64,
			nil,
		)
		log.Infof("[StartSyncJob] 开始同步资产测绘任务-下发开始: user_id=%d, detect_task_id=%d, company_list=%v, pre_ids=%v",
			req.UserId, detectAssetsTask.ID, companyList, preIds)

		err = asyncq.Enqueue(ctx, asyncq.DetectGolangCluesJob, payload)
	} else {
		//调用 DetectAssetsTip1ProcessJob
		log.Infof("[StartSyncJob] 开始同步资产测绘任务-下发开始: user_id=%d, detect_task_id=%d, company_list=%v, pre_ids=%v",
			req.UserId, detectAssetsTask.ID, companyList, preIds)

		err = asyncq.DetectDirectOperatePhpJob.Dispatch(
			req.UserId,
			req.CompanyId,
			uint64(detectAssetsTask.ID),
			detectAssetsTask.GroupId,
			false,
			preIdsUint64,
		)
	}

	if err != nil {
		return err
	}
	log.Infof("[StartSyncJob] 开始同步资产测绘任务-下发结束: user_id=%d, detect_task_id=%d, company_list=%v, pre_ids=%v",
		req.UserId, detectAssetsTask.ID, companyList, preIds)
	// 5. 写日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(req.UserId, "开始同步资产测绘任务", req.ClientIp, req.CompanyId, operate_logs.FIND_ASSETS, operate_logs.TYPE_OPERATE))
	if err != nil {
		return err
	}
	// 6. 组装 data
	data := []*sensitive_data.SensitiveData{}
	now := time.Now()
	if detectAssetsTask.IsAutoLeakAsset == 1 && detectAssetsTask.IsAutoDataAsset == 1 {
		for _, name := range companyList {
			data = append(data, &sensitive_data.SensitiveData{
				Name: name, Type: 0, Status: 1, UserId: req.UserId, CompanyId: req.CompanyId, OperatorId: req.OperatorId, CreatedAt: now, UpdatedAt: now,
			})
		}
	} else if detectAssetsTask.IsAutoLeakAsset == 1 && detectAssetsTask.IsAutoDataAsset != 1 {
		for _, name := range companyList {
			data = append(data, &sensitive_data.SensitiveData{
				Name: name, Type: 2, Status: 1, UserId: req.UserId, CompanyId: req.CompanyId, OperatorId: req.OperatorId, CreatedAt: now, UpdatedAt: now,
			})
		}
	} else if detectAssetsTask.IsAutoLeakAsset != 1 && detectAssetsTask.IsAutoDataAsset == 1 {
		for _, name := range companyList {
			data = append(data, &sensitive_data.SensitiveData{
				Name: name, Type: 1, Status: 1, UserId: req.UserId, CompanyId: req.CompanyId, OperatorId: req.OperatorId, CreatedAt: now, UpdatedAt: now,
			})
		}
	}

	// 7. 插入 DetectAssetsTaskReport
	taskReport := &detect_task_report_result.Result{
		UserID:       req.UserId,
		CompanyID:    req.CompanyId,
		DetectTaskID: uint64(detectAssetsTask.ID),
		CreatedAt:    now,
	}
	_ = detect_task_report_result.NewCall().Create(taskReport)

	// 8. 敏感词表操作
	sensitiveKeywordModel := sensitive_keyword.NewModel()
	for _, v := range data {
		params := map[string]interface{}{
			"user_id": v.UserId,
			"type":    v.Type,
			"name":    v.Name,
		}
		infoList, _, _ := sensitiveKeywordModel.Search(params, 1, 1)
		var info sensitive_keyword.SensitiveKeyword
		if len(infoList) > 0 {
			info = infoList[0]
		}
		if info.Id > 0 {
			updates := map[string]interface{}{
				"status": 1,
				"type":   v.Type,
			}
			_ = sensitiveKeywordModel.Update(info.Id, updates)

			// 调用异步任务 - 更新情况
			if v.Type == 0 {
				if cfg.ExecGolangJob() {
					//todo 调用go的job
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 1, 1, uint64(detectAssetsTask.ID))
				} else {
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 1, 1, uint64(detectAssetsTask.ID))
				}
			} else if v.Type == 1 {
				if cfg.ExecGolangJob() {
					//todo 调用go的job
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 0, 1, uint64(detectAssetsTask.ID))
				} else {
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 0, 1, uint64(detectAssetsTask.ID))
				}
			} else {
				if cfg.ExecGolangJob() {
					//todo 调用go的job
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 1, 0, uint64(detectAssetsTask.ID))
				} else {
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 1, 0, uint64(detectAssetsTask.ID))
				}
			}
		} else {
			kw := &sensitive_keyword.SensitiveKeyword{
				UserId:       v.UserId,
				CompanyId:    v.CompanyId,
				OperatorId:   v.OperatorId,
				Name:         v.Name,
				Status:       int8(v.Status),
				Type:         int8(v.Type),
				DetectTaskId: uint64(detectAssetsTask.ID), // 如果有
				CreatedAt:    v.CreatedAt,
				UpdatedAt:    v.UpdatedAt,
			}
			_ = sensitiveKeywordModel.Create(kw)

			// 调用异步任务 - 新增情况
			if v.Type == 0 {
				if cfg.ExecGolangJob() {
					//todo 调用go的job
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 1, 1, uint64(detectAssetsTask.ID))
				} else {
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 1, 1, uint64(detectAssetsTask.ID))
				}
			} else if v.Type == 1 {
				if cfg.ExecGolangJob() {
					//todo 调用go的job
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 0, 1, uint64(detectAssetsTask.ID))
				} else {
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 0, 1, uint64(detectAssetsTask.ID))
				}
			} else {
				if cfg.ExecGolangJob() {
					//todo 调用go的job
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 1, 0, uint64(detectAssetsTask.ID))
				} else {
					_ = asyncq.CreateSensitiveKeywordJob.Dispatch(v.UserId, v.Name, 1, 0, uint64(detectAssetsTask.ID))
				}
			}
		}
	}

	*rsp = pb.StartSyncJobResponse{
		DetectTaskId: uint64(detectAssetsTask.ID),
		GroupId:      detectAssetsTask.GroupId,
	}
	return nil
}

// GetClueCount 获取线索数量统计
func GetClueCount(ctx context.Context, req *pb.ClueCountRequest, rsp *pb.ClueCountResponse) error {
	// 1. 获取 companyId
	// companyId := req.OperateCompanyId
	groupId := req.GroupId
	status := req.Status
	UserId := req.UserId
	rsp.CluesCount = make([]*pb.ClueTypeCount, 0)

	// 初始化 models
	clueModel := clues.NewCluer()
	clueGroupModel := clues_groups.NewCluesGrouper()

	// 2. 检查线索组是否存在
	_, err := clueGroupModel.First(
		mysql.WithColumnValue("user_id", UserId),
		mysql.WithColumnValue("id", groupId),
	)
	if err != nil {
		// 线索组不存在，返回所有类型数量为0
		for t, label := range clues.TYPE_LABELS {
			rsp.CluesCount = append(rsp.CluesCount, &pb.ClueTypeCount{
				Type:  int32(t),
				Label: label,
				Count: 0,
			})
		}
		rsp.ExpandFinish = 0
		return nil
	}

	// 3. 检查扩展任务是否完成
	expandFinish := 0
	var taskCount int64

	log.WithContextInfof(ctx, "[DEBUG] 开始检查扩展任务是否完成: UserId=%d, groupId=%d", UserId, groupId)

	// 直接使用mysql.GetDbClient()获取数据库实例
	db := mysql.GetDbClient()
	dbQuery := db.Model(&clue_task.ClueTask{})
	dbQuery = dbQuery.Where("user_id = ?", UserId)
	dbQuery = dbQuery.Where("group_id = ?", groupId)
	dbQuery = dbQuery.Where("is_show != ?", clue_task.Show)       // 修改为与PHP一致的逻辑：is_show != 1
	dbQuery = dbQuery.Where("status = ?", clue_task.FinishStatus) // 2=成功

	// 打印SQL查询语句用于调试
	sqlStr := dbQuery.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return tx.Count(&taskCount)
	})
	log.WithContextInfof(ctx, "[DEBUG] expandFinish查询SQL: %s", sqlStr)
	log.WithContextInfof(ctx, "[DEBUG] 查询参数: UserId=%d, groupId=%d, Show=%d, FinishStatus=%d",
		UserId, groupId, clue_task.Show, clue_task.FinishStatus)

	dbQuery.Count(&taskCount)
	log.WithContextInfof(ctx, "[DEBUG] expandFinish查询结果: taskCount=%d", taskCount)

	if taskCount > 0 {
		expandFinish = int(taskCount)
	}
	log.WithContextInfof(ctx, "[DEBUG] expandFinish最终结果: %d", expandFinish)

	// 4. 处理 detect_task_id 和 fake_detect_task_id 的特殊逻辑
	if req.DetectTaskId > 0 || req.FakeDetectTaskId > 0 {
		var returnJsonStr string

		if req.DetectTaskId > 0 {
			// 查询 detect_assets_tasks 表
			detectTaskModel := detect_assets_tasks.NewModel()
			taskInfo, err := detectTaskModel.First(
				detect_assets_tasks.WithID(req.DetectTaskId),
				detect_assets_tasks.WithUserID(req.UserId),
			)
			if err != nil {
				return err
			}
			if taskInfo == nil {
				return errors.New("任务不存在")
			}
			returnJsonStr = taskInfo.ReturnJson
		} else {
			// 查询 pft_tasks 表
			pftTaskModel := phishing_fake_task.NewTasker()
			taskInfo, err := pftTaskModel.First(
				mysql.WithColumnValue("id", req.FakeDetectTaskId),
				mysql.WithColumnValue("user_id", req.UserId),
			)
			if err != nil {
				return err
			}
			returnJsonStr = taskInfo.ReturnJson
		}

		var returnJson map[string]interface{}
		if err := json.Unmarshal([]byte(returnJsonStr), &returnJson); err != nil {
			return err
		}

		// 检查是否使用初始线索
		sceneGroupSet, _ := returnJson["scene_group_set"].(map[string]interface{})
		isUseInitClues := 0
		if expandInitClues, ok := sceneGroupSet["expand_init_clues"].(float64); ok {
			isUseInitClues = int(expandInitClues)
		}

		// 获取 scene_ids
		sceneIds, _ := returnJson["scene_ids"].([]interface{})

		// 如果不需要使用初始线索且有 scene_ids，则进行特殊处理
		if isUseInitClues == 0 && len(sceneIds) > 0 {
			// 获取线索列表
			conditions := []mysql.HandleFunc{
				mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
				mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO), // 去掉供应链统计
				mysql.WithColumnValue("user_id", UserId),
			}
			if status >= 0 {
				conditions = append(conditions, mysql.WithColumnValue("status", status))
			}
			if groupId > 0 {
				conditions = append(conditions, mysql.WithColumnValue("group_id", groupId))
			}
			clueList, err := clueModel.ListAll(conditions...)
			if err != nil {
				return err
			}

			// 统计每种类型数量
			clueTypes := make([]int32, 14) // 初始化14个0
			for _, clue := range clueList {
				// 检查是否在 scene_ids 中
				isInSceneIds := false
				for _, id := range sceneIds {
					if float64(clue.Id) == id.(float64) {
						isInSceneIds = true
						break
					}
				}
				if isInSceneIds {
					continue
				}

				// 特殊处理子域名类型
				if clue.Type == clues.TYPE_SUBDOMAIN {
					clueTypes[clues.TYPE_DOMAIN]++
				}
				clueTypes[clue.Type]++
			}

			// 构建响应
			for t, label := range clues.TYPE_LABELS {
				rsp.CluesCount = append(rsp.CluesCount, &pb.ClueTypeCount{
					Type:  int32(t),
					Label: label,
					Count: clueTypes[t],
				})
			}
			rsp.ExpandFinish = int32(expandFinish)
			return nil
		}
	}

	// 5. 获取线索列表
	conditions := []mysql.HandleFunc{
		mysql.WithColumnValue("user_id", UserId),
		mysql.WithColumnValue("group_id", groupId),
		mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
	}
	if status >= 0 {
		conditions = append(conditions, mysql.WithColumnValue("status", status))
	}
	clueList, err := clueModel.ListAll(conditions...)
	if err != nil {
		return err
	}

	// 6. 统计每种类型数量
	typeCount := make(map[int32]int32)
	for t := range clues.TYPE_LABELS {
		typeCount[int32(t)] = 0
	}
	for _, clue := range clueList {
		typeCount[int32(clue.Type)]++
	}
	for t, label := range clues.TYPE_LABELS {
		rsp.CluesCount = append(rsp.CluesCount, &pb.ClueTypeCount{
			Type:  int32(t),
			Label: label,
			Count: typeCount[int32(t)],
		})
	}
	rsp.ExpandFinish = int32(expandFinish)
	return nil
}

// 获取线索列表-NoWholeClueList
func GetNoWholeClueList(ctx context.Context, req *pb.ClueListRequest, rsp *pb.ClueListPageResponse) error {
	// 1. 获取用户ID和企业ID
	userId := req.UserId
	clueModel := clues.NewCluer()
	// 2. 初始化模型
	if req.DetectTaskId != 0 {
		detectTaskModel := detect_assets_tasks.NewModel()
		// 3. 获取检测任务信息
		detectInfo, err := detectTaskModel.First(
			detect_assets_tasks.WithID(req.DetectTaskId),
			detect_assets_tasks.WithUserID(userId),
		)
		if err != nil || detectInfo == nil {
			return errors.New("测绘任务为空")
		}
	}
	// 4. 构建查询条件
	conditions := []mysql.HandleFunc{
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO),
	}
	if req.GroupId > 0 {
		conditions = append(conditions, mysql.WithColumnValue("group_id", req.GroupId))
	}
	if req.Status >= 0 {
		conditions = append(conditions, mysql.WithColumnValue("status", req.Status))
	}
	if req.Type >= 0 {
		conditions = append(conditions, mysql.WithColumnValue("type", req.Type))
	}
	if req.Keyword != "" {
		conditions = append(conditions, mysql.WithLRLike("`content`", req.Keyword))
	}

	// 6. 分页获取数据
	page := req.Page
	pageSize := req.PerPage
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	clueList, total, err := clueModel.List(int(page), int(pageSize), conditions...)
	if err != nil {
		return err
	}
	conditionsNew := []mysql.HandleFunc{
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("group_id", req.GroupId),
	}
	clueListNew, err := clueModel.ListAll(conditionsNew...)
	if err != nil {
		return err
	}
	// 7. 构建分页响应
	clueInfos := make([]*pb.ClueInfo, 0)
	for _, clue := range clueList {
		clueInfo := &pb.ClueInfo{
			Id:              clue.Id,
			Content:         clue.Content,
			Type:            int32(clue.Type),
			Hash:            strconv.Itoa(clue.Hash),
			FromIp:          clue.FromIp,
			ParentId:        clue.ParentId,
			Source:          cast.ToInt32(clue.Source), // 使用标签转换函数
			ClueCompanyName: clue.ClueCompanyName,
			CreatedAt:       clue.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:       clue.UpdatedAt.Format("2006-01-02 15:04:05"),
			SourceLabel:     clues.GetSourceLabel(clue.Source), // 使用标签转换函数
			IsExpand:        int32(clue.IsExpand),
			Status:          int32(clue.Status),
		}
		// 构建链式列表
		// 只有当 parent_id > 0 时才构建链式列表
		if clue.ParentId > 0 && clue.ParentId != clue.Id {
			chainList := make([]*pb.ClueChain, 0)
			GetTopClue(int64(clue.ParentId), clueListNew, int64(clue.Id), &chainList)
			chainList = append(chainList, &pb.ClueChain{
				Content: clue.Content,
				Type:    int32(clue.Type),
			})
			clueInfo.ChainList = chainList
		} else {
			clueInfo.ChainList = []*pb.ClueChain{}
		}
		// 根据线索类型获取 FOFA 资产信息
		// 对应 PHP 逻辑: if ($item['type'] == Clue::TYPE_LOGO || $item['type'] == Clue::TYPE_CERT || $item['type'] == Clue::TYPE_IP)
		log.WithContextInfof(ctx, "[GetNoWholeClueList] 处理线索ID=%d, 类型=%d, 原始内容=%s", clue.Id, clue.Type, clue.Content)

		if clue.Type == clues.TYPE_LOGO || clue.Type == clues.TYPE_CERT || clue.Type == clues.TYPE_IP {
			log.WithContextInfof(ctx, "[GetNoWholeClueList] 线索类型匹配，开始获取FOFA资产信息，线索ID=%d", clue.Id)

			// 根据线索类型选择缓存键内容
			var cacheContent string
			if clue.Type == clues.TYPE_LOGO {
				// ICON 类型使用 hash 字段
				cacheContent = fmt.Sprintf("%d", clue.Hash)
				log.WithContextInfof(ctx, "[GetNoWholeClueList] ICON类型使用hash作为缓存键，hash=%d", clue.Hash)
			} else {
				// 其他类型使用 content 字段
				cacheContent = clue.Content
				log.WithContextInfof(ctx, "[GetNoWholeClueList] 非ICON类型使用content作为缓存键，content=%s", clue.Content)
			}

			// 构建缓存键，使用与 GetFofaAssetsNum 相同的格式，添加 foradar_cache 前缀
			cacheKey := fmt.Sprintf("foradar_cache:fofa_assets:clue:count:%s", cacheContent)
			log.WithContextInfof(ctx, "[GetNoWholeClueList] 构建缓存键=%s", cacheKey)

			// 添加详细的 Redis 连接和键检查
			log.WithContextInfof(ctx, "[GetNoWholeClueList] Redis调试开始，线索ID=%d, 缓存键=%s", clue.Id, cacheKey)

			redisClient := redisMiddleware.GetClient()

			// 检查 Redis 连接状态
			pong, pingErr := redisClient.Ping(ctx).Result()
			if pingErr != nil {
				log.WithContextErrorf(ctx, "[GetNoWholeClueList] Redis PING 失败，线索ID=%d, 错误: %v", clue.Id, pingErr)
			} else {
				log.WithContextInfof(ctx, "[GetNoWholeClueList] Redis PING 成功，线索ID=%d, 响应: %s", clue.Id, pong)
			}

			// 检查 Redis 连接信息
			log.WithContextInfof(ctx, "[GetNoWholeClueList] Redis连接信息，线索ID=%d, 地址: %s, 数据库: %d",
				clue.Id, redisClient.Options().Addr, redisClient.Options().DB)

			// 检查键是否存在
			exists, existsErr := redisClient.Exists(ctx, cacheKey).Result()
			if existsErr != nil {
				log.WithContextErrorf(ctx, "[GetNoWholeClueList] 检查键存在性失败，线索ID=%d, 错误: %v", clue.Id, existsErr)
			} else {
				log.WithContextInfof(ctx, "[GetNoWholeClueList] 键存在性检查，线索ID=%d, 键=%s, 存在=%d (1=存在, 0=不存在)",
					clue.Id, cacheKey, exists)
			}

			// 检查键的TTL
			ttl, ttlErr := redisClient.TTL(ctx, cacheKey).Result()
			if ttlErr != nil {
				log.WithContextErrorf(ctx, "[GetNoWholeClueList] 获取键TTL失败，线索ID=%d, 错误: %v", clue.Id, ttlErr)
			} else {
				log.WithContextInfof(ctx, "[GetNoWholeClueList] 键TTL信息，线索ID=%d, 键=%s, TTL=%v", clue.Id, cacheKey, ttl)
			}

			// 尝试直接获取原始值
			rawValue, rawErr := redisClient.Get(ctx, cacheKey).Result()
			if rawErr != nil {
				log.WithContextErrorf(ctx, "[GetNoWholeClueList] 直接获取键值失败，线索ID=%d, 错误: %v", clue.Id, rawErr)
			} else {
				log.WithContextInfof(ctx, "[GetNoWholeClueList] 直接获取键值成功，线索ID=%d, 数据长度=%d, 前200字符: %.200s",
					clue.Id, len(rawValue), rawValue)
			}

			// 构建缓存结果结构
			type CacheResult struct {
				Num     uint32 `json:"num"`
				FofaUrl string `json:"fofa_url"`
			}

			// 使用通用函数解析缓存数据
			if result, err := parseFofaAssetsCache(ctx, cacheKey); err == nil {
				log.WithContextInfof(ctx, "[GetNoWholeClueList] 当前线索缓存解析成功！线索ID=%d, num=%d, fofa_url=%s", clue.Id, result.Num, result.FofaUrl)
				clueInfo.FofaAssetsNum = &pb.FofaAssetsNum{
					Num:     result.Num,
					FofaUrl: result.FofaUrl,
				}
			} else {
				// 区分不同类型的错误
				if strings.Contains(err.Error(), "缓存键不存在") {
					log.WithContextInfof(ctx, "[GetNoWholeClueList] 当前线索缓存不存在，线索ID=%d, 缓存键=%s", clue.Id, cacheKey)
					// 缓存不存在是正常情况，不设置 FofaAssetsNum 字段
				} else {
					log.WithContextWarnf(ctx, "[GetNoWholeClueList] 当前线索缓存解析失败，线索ID=%d, 错误: %v", clue.Id, err)
				}
			}

		} else {
			log.WithContextInfof(ctx, "[GetNoWholeClueList] 线索类型不匹配，跳过FOFA资产信息获取，线索ID=%d, 类型=%d", clue.Id, clue.Type)
		}

		// 如果是 ICON 类型，将 content 转换为链接地址形式
		if clueInfo.Type == int32(clues.TYPE_LOGO) {
			clueInfo.Content = storage.GenAPIDownloadPath("", clueInfo.Content, ".ico")
		}
		clueInfos = append(clueInfos, clueInfo)
	}

	*rsp = pb.ClueListPageResponse{
		Items:       clueInfos,
		Total:       int32(total),
		CurrentPage: int32(page),
		PerPage:     int32(pageSize),
		LastPage:    int32((int(total) + int(pageSize) - 1) / int(pageSize)),
	}

	return nil
}

// 获取线索列表-WholeClueList
func GetWholeClueList(ctx context.Context, req *pb.ClueListRequest, rsp *pb.ClueListWholeResponse) error {
	// 1. 获取用户ID和企业ID
	userId := req.UserId

	// 2. 初始化模型
	clueModel := clues.NewCluer()
	detectTaskModel := detect_assets_tasks.NewModel()
	// 3. 获取检测任务信息
	detectInfo, err := detectTaskModel.First(
		detect_assets_tasks.WithID(req.DetectTaskId),
		detect_assets_tasks.WithUserID(userId),
	)
	if err != nil || detectInfo == nil {
		return errors.New("测绘任务为空")
	}
	// 4. 构建查询条件
	conditions := []mysql.HandleFunc{
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO),
	}
	conditionsNew := []mysql.HandleFunc{
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("group_id", req.GroupId),
	}
	if req.GroupId > 0 {
		conditions = append(conditions, mysql.WithColumnValue("group_id", req.GroupId))
	}
	if req.Status >= 0 {
		conditions = append(conditions, mysql.WithColumnValue("status", req.Status))
	}
	if req.Keyword != "" {
		conditions = append(conditions, mysql.WithColumnValue("content LIKE ?", "%"+req.Keyword+"%"))
	}

	// 5. 检查是否需要获取所有数据
	if req.IsWhole == 1 {
		// 检查线索数量是否超过1000
		clueList, err := clueModel.ListAll(conditions...)
		if err != nil {
			return err
		}
		if len(clueList) > 1000 {
			return errors.New("当前测绘任务超过了1000条数据，无法再进行测绘任务，请结束当前测绘任务重新进行测试")
		}
		clueListNew, err := clueModel.ListAll(conditionsNew...)
		if err != nil {
			return err
		}

		// 先解析 returnJson
		var returnJson map[string]interface{}
		if err := json.Unmarshal([]byte(detectInfo.ReturnJson), &returnJson); err != nil {
			return err
		}

		// 获取股权关系
		equityReflectList := make(map[string]string)
		if equityParentId := getEquityParentId(detectInfo.Name); equityParentId > 0 {
			equityChildList := getEquityChildList(equityParentId)
			for _, child := range equityChildList {
				equityReflectList[child.Name] = fmt.Sprintf("%.2f", child.Percent)
			}
		}

		// 获取历史所选控股公司
		var oldCompanyList []string
		if returnJson[cast.ToString(detect_assets_tasks.StepOneCompanyList)] != nil {
			if list, ok := returnJson["step_one_company_list"].([]interface{}); ok {
				for _, v := range list {
					if name, ok := v.(string); ok {
						oldCompanyList = append(oldCompanyList, name)
					}
				}
			}
		}

		// 处理股权关系
		for k := range equityReflectList {
			if !containsString(oldCompanyList, k) {
				delete(equityReflectList, k)
			}
			if k == detectInfo.Name {
				equityReflectList[k] = "100.00"
			}
		}

		// 获取IP和域名线索ID
		ipClueIds := getClueIds(int64(userId), int64(req.GroupId), clues.TYPE_IP)
		domainClueIds := getClueIds(int64(userId), int64(req.GroupId), clues.TYPE_DOMAIN)

		// 处理公司JSON
		var companyJsonArr []map[string]interface{}
		if detectInfo.CompanyJSON != "" {
			if err := json.Unmarshal([]byte(detectInfo.CompanyJSON), &companyJsonArr); err != nil {
				companyJsonArr = nil
			}
		}

		// 获取所有扩展企业名称列表 (allCompany)
		var allCompany []string
		if err := json.Unmarshal([]byte(detectInfo.ConfirmCompanyList), &allCompany); err != nil {
			allCompany = []string{}
		}
		// 用于记录已处理的股权关系
		reflectEquityList := make(map[string]string)

		// 构建响应数据
		items := make([]*pb.ClueWholeInfo, 0)
		for _, clue := range clueList {
			item := &pb.ClueWholeInfo{
				Id:              int64(clue.Id),
				UserId:          int64(clue.UserId),
				CompanyId:       int64(clue.CompanyId),
				ParentId:        int64(clue.ParentId),
				Content:         clue.Content,
				GroupId:         int64(clue.GroupId),
				Comment:         clue.Comment,
				ClueCompanyName: clue.ClueCompanyName,
				Hash:            strconv.Itoa(clue.Hash),
				Source:          int32(clue.Source),
				Count:           int32(clue.Count),
				Type:            int32(clue.Type),
				Status:          int32(clue.Status),
				CreatedAt:       clue.CreatedAt.Format("2006-01-02 15:04:05"),
				UpdatedAt:       clue.UpdatedAt.Format("2006-01-02 15:04:05"),
				SafeUserId:      int64(clue.SafeUserId),
				IsExpand:        int32(clue.IsExpand),
				FromIp:          clue.FromIp,
				IsDeleted:       int32(clue.IsDeleted),
				PunycodeDomain:  clue.PunycodeDomain,
				IsFromCheckTable: func() int32 {
					v, err := strconv.Atoi(clue.IsFromCheckTable)
					if err != nil {
						return 0
					}
					return int32(v)
				}(),
				IsSupplyChain:     int32(clue.IsSupplyChain),
				IsFakeIcp:         int32(clue.IsFakeIcp),
				IsHighlight:       true, // 初始设置为true
				EquityPercent:     "-",
				ParentCompanyName: "",
				ParentPercent:     "",
				ChainList:         []*pb.ClueChain{},
				FofaAssetsNum:     nil, // 默认值，后面会根据类型设置
			}

			// 根据线索类型获取 FOFA 资产信息
			// 对应 PHP 逻辑: if ($item['type'] == Clue::TYPE_LOGO || $item['type'] == Clue::TYPE_CERT || $item['type'] == Clue::TYPE_IP)
			if clue.Type == clues.TYPE_LOGO || clue.Type == clues.TYPE_CERT || clue.Type == clues.TYPE_IP {
				// 根据线索类型选择缓存键内容
				var cacheContent string
				if clue.Type == clues.TYPE_LOGO {
					// ICON 类型使用 hash 字段
					cacheContent = fmt.Sprintf("%d", clue.Hash)
				} else {
					// 其他类型使用 content 字段
					cacheContent = clue.Content
				}

				// 构建缓存键，使用与 GetFofaAssetsNum 相同的格式，添加 foradar_cache 前缀
				cacheKey := fmt.Sprintf("foradar_cache:fofa_assets:clue:count:%s", cacheContent)

				// 使用通用函数解析缓存数据
				if result, err := parseFofaAssetsCache(ctx, cacheKey); err == nil {
					item.FofaAssetsNum = &pb.FofaAssetsNum{
						Num:     result.Num,
						FofaUrl: result.FofaUrl,
					}
				} else {
					// 区分不同类型的错误
					if strings.Contains(err.Error(), "缓存键不存在") {
						log.WithContextInfof(ctx, "[GetWholeClueList] 线索缓存不存在，线索ID=%d, 缓存键=%s", clue.Id, cacheKey)
						// 缓存不存在是正常情况，不设置 FofaAssetsNum 字段
					} else {
						log.WithContextWarnf(ctx, "[GetWholeClueList] 线索缓存解析失败，线索ID=%d, 错误: %v", clue.Id, err)
					}
				}
			}

			// 如果是 ICON 类型，将 content 转换为链接地址形式
			if item.Type == int32(clues.TYPE_LOGO) {
				item.Content = storage.GenAPIDownloadPath("", item.Content, ".ico")
			}

			// 处理 is_highlight 逻辑
			companyName := clue.ClueCompanyName

			// LOGO类型且不是来自调研表的线索，设置为高亮
			if clue.Type == clues.TYPE_LOGO && item.IsFromCheckTable == 0 {
				item.IsHighlight = true
			}

			// 处理有公司名称的情况或者LOGO类型来自调研表的情况
			if ((clue.Type != clues.TYPE_LOGO) && (companyName != "")) || (clue.Type == clues.TYPE_LOGO && item.IsFromCheckTable == 1) {
				// 查询股权关系
				if _, exists := reflectEquityList[companyName]; !exists && (companyName != detectInfo.Name) {
					if equityPercent, ok := equityReflectList[companyName]; ok {
						reflectEquityList[companyName] = equityPercent
					}
				}

				// 如果公司名称等于检测任务名称，不高亮
				if companyName == detectInfo.Name {
					item.IsHighlight = false
				}

				// 股权大于等于50%不高亮 || 或者已经存在于所选公司时不高亮
				if equityPercent, ok := reflectEquityList[companyName]; ok {
					if percent, err := strconv.ParseFloat(equityPercent, 64); err == nil && percent >= 50 {
						item.IsHighlight = false
					}
					if containsString(oldCompanyList, companyName) {
						item.IsHighlight = false
					}
					item.EquityPercent = equityPercent
				} else {
					// 已经存在于所选公司时不高亮
					if containsString(oldCompanyList, companyName) {
						item.IsHighlight = false
					}
				}

				// 判断是否是初始线索扩展出来的企业名称，而且该企业不是控股公司
				if len(allCompany) > 0 && item.IsHighlight {
					if containsString(allCompany, companyName) {
						item.IsHighlight = false
					}
				}
			}

			// 如果是手动新建的线索也不设置为疑似线索
			if clue.Source == clues.SOURCE_MANUAL_ADD {
				item.IsHighlight = false
			}

			// 处理股权关系
			for _, arrdata := range companyJsonArr {
				if children, ok := arrdata["children"].([]interface{}); ok {
					for _, child := range children {
						if childMap, ok := child.(map[string]interface{}); ok {
							if childMap["name"] == clue.ClueCompanyName {
								if v, ok := arrdata["name"].(string); ok {
									item.ParentCompanyName = v
								}
								if percent, ok := childMap["percent"].(string); ok {
									percent = strings.TrimSuffix(percent, ".00%")
									percent = strings.TrimSuffix(percent, "%")
									item.ParentPercent = percent
								}
								break
							}
						}
					}
				}
			}

			// 处理链式列表
			// 只有当 parent_id > 0 时才构建链式列表
			if clue.ParentId > 0 && clue.ParentId != clue.Id {
				chainList := make([]*pb.ClueChain, 0)
				GetTopClue(int64(clue.ParentId), clueListNew, int64(clue.Id), &chainList)
				chainList = append(chainList, &pb.ClueChain{
					Content: clue.Content,
					Type:    int32(clue.Type),
				})
				item.ChainList = chainList
			}

			items = append(items, item)
		}
		// 根据步骤处理数据
		if detectInfo.StepDetail == detect_assets_tasks.StepTwoExpandIP {
			filteredItems := make([]*pb.ClueWholeInfo, 0)
			for _, item := range items {
				if item.Type == int32(clues.TYPE_LOGO) {
					if item.ParentId > 0 && !containsInt64(ipClueIds, item.ParentId) {
						continue
					}
				}
				if item.Type == int32(clues.TYPE_SUBDOMAIN) {
					item.Type = int32(clues.TYPE_DOMAIN)
				}
				filteredItems = append(filteredItems, item)
			}
			items = filteredItems
		} else if detectInfo.StepDetail == detect_assets_tasks.StepTwoExpandDomain {
			filteredItems := make([]*pb.ClueWholeInfo, 0)
			for _, item := range items {
				if item.Type == int32(clues.TYPE_LOGO) {
					if item.ParentId > 0 && (containsInt64(ipClueIds, item.ParentId) || !containsInt64(domainClueIds, item.ParentId)) {
						continue
					}
				}
				if item.Type == int32(clues.TYPE_SUBDOMAIN) {
					item.Type = int32(clues.TYPE_DOMAIN)
				}
				filteredItems = append(filteredItems, item)
			}
			items = filteredItems
		}
		// items 构建完后，分组
		var (
			domainList []*pb.ClueWholeInfo
			icpList    []*pb.ClueWholeInfo
			certList   []*pb.ClueWholeInfo
			iconList   []*pb.ClueWholeInfo
			ipList     []*pb.ClueWholeInfo
		)
		for _, item := range items {
			switch item.Type {
			case int32(clues.TYPE_DOMAIN):
				domainList = append(domainList, item)
			case int32(clues.TYPE_ICP):
				icpList = append(icpList, item)
			case int32(clues.TYPE_CERT):
				certList = append(certList, item)
			case int32(clues.TYPE_LOGO):
				iconList = append(iconList, item)
			case int32(clues.TYPE_IP):
				ipList = append(ipList, item)
			}
		}
		// 确保所有字段在为空时返回空切片而不是null
		if domainList == nil {
			domainList = []*pb.ClueWholeInfo{}
		}
		if icpList == nil {
			icpList = []*pb.ClueWholeInfo{}
		}
		if certList == nil {
			certList = []*pb.ClueWholeInfo{}
		}
		if iconList == nil {
			iconList = []*pb.ClueWholeInfo{}
		}
		if ipList == nil {
			ipList = []*pb.ClueWholeInfo{}
		}

		*rsp = pb.ClueListWholeResponse{
			Domain: domainList,
			Icp:    icpList,
			Cert:   certList,
			Icon:   iconList,
			Ip:     ipList,
		}
		return nil
	}
	return nil
}

func getEquityParentId(name string) int64 {
	parent, err := company_equity.NewCompanyEquityModel().First(
		mysql.WithColumnValue("parent_id", 0),
		mysql.WithColumnValue("name", name),
	)
	if err != nil {
		return 0
	}
	return int64(parent.Id)
}

func getEquityChildList(parentId int64) []struct {
	Name    string
	Percent float64
} {
	children, err := company_equity.NewCompanyEquityModel().ListAll(
		mysql.WithColumnValue("parent_id", parentId),
	)
	if err != nil {
		return nil
	}
	result := make([]struct {
		Name    string
		Percent float64
	}, 0, len(children))
	for _, c := range children {
		result = append(result, struct {
			Name    string
			Percent float64
		}{Name: c.Name, Percent: c.Percent})
	}
	return result
}

func getClueIds(userId int64, groupId int64, clueType int) []int64 {
	clueModel := clues.NewCluer()
	conditions := []mysql.HandleFunc{
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("group_id", groupId),
		mysql.WithColumnValue("type", clueType),
		mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
	}

	clueList, err := clueModel.ListAll(conditions...)
	if err != nil {
		return nil
	}

	ids := make([]int64, 0, len(clueList))
	for _, clue := range clueList {
		ids = append(ids, int64(clue.Id))
	}
	return ids
}

// GetRepeatOriginClues 资产测绘-重新获取原始线索
func GetRepeatOriginClues(ctx context.Context, req *pb.StartSyncJobRequest, rsp *pb.StartSyncJobResponse) error {
	// 校验当前企业是否还有测绘次数
	if req.CompanyId > 0 {
		companyInfo, err := company.NewCompanyModel().FindById(req.CompanyId, 0)
		if err != nil {
			return err
		}
		limitCount := companyInfo.CheckLimit("cloud_recommend", 0)
		if limitCount < 0 {
			return errors.New("已达到限制次数，无法再进行测绘任务")
		}
	}

	// 1. 获取任务详情
	detectAssetsTask, err := detect_assets_tasks.NewModel().First(detect_assets_tasks.WithID(req.DetectTaskId), detect_assets_tasks.WithUserID(req.UserId))
	if err != nil {
		return errors.New("该任务不存在，参数错误")
	}
	if detectAssetsTask == nil {
		return errors.New("任务不存在")
	}

	// 2. 解析 returnJson，获取 companyList
	var returnJson map[string]interface{}
	_ = json.Unmarshal([]byte(detectAssetsTask.ReturnJson), &returnJson)
	companyList := []string{}
	if v, ok := returnJson[cast.ToString(detect_assets_tasks.StepOneCompanyList)]; ok {
		if arr, ok := v.([]interface{}); ok {
			for _, co := range arr {
				if name, ok := co.(string); ok {
					companyList = append(companyList, name)
				}
			}
		}
	}

	// 3. 如果 companyList 为空，查股权接口
	if len(companyList) == 0 {
		// 假设 getEquityInfo(ctx, name, limit, force) 返回 ([]map[string]interface{}, error)
		data, err := getEquityInfo(ctx, detectAssetsTask.Name, false, 50)
		if err != nil {
			log.Errorf("[Cloud]: GetCompanyCascadeEquity -> Get equity info error: %v", err)
			return err
		}
		otherCompanyList := []string{}
		for _, item := range data {
			// 只取 color_flag 为假（或0）的公司名
			if !item.ColorFlag {
				otherCompanyList = append(otherCompanyList, item.CompanyName)
			}
			// 兼容 color_flag 为 int 类型
			// if v, ok := any(item.ColorFlag).(int); ok && v == 0 {
			// 	otherCompanyList = append(otherCompanyList, item.Name)
			// }
		}
		companyList = append([]string{detectAssetsTask.Name}, otherCompanyList...)
		_ = companyList
	}

	// 4. 分发 DetectGolangCluesJob（强制查询不取缓存）
	payload := asyncq.NewDetectGolangCluesJobPayload(
		companyList,
		req.UserId,
		req.CompanyId,
		uint64(detectAssetsTask.ID),
		detectAssetsTask.GroupId,
		true,
		nil,
		nil,
	)
	err = asyncq.Enqueue(ctx, asyncq.DetectGolangCluesJob, payload)
	if err != nil {
		return err
	}

	// 5. 写日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(req.UserId, "重新获取初始线索", req.ClientIp, req.CompanyId, operate_logs.FIND_ASSETS, operate_logs.TYPE_OPERATE))
	if err != nil {
		return err
	}
	*rsp = pb.StartSyncJobResponse{
		DetectTaskId: uint64(detectAssetsTask.ID),
		GroupId:      detectAssetsTask.GroupId,
	}
	return nil
}

func AssetsEvaluate(ctx context.Context, req *pb.AssetsEvaluateRequest, rsp *pb.AssetsEvaluateResponse) error {
	// 查询任务信息
	info, err := detect_assets_tasks.NewModel().First(mysql.WithWhere("id = ? AND user_id = ?", req.ExpendId, req.UserId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		log.Errorf("[AssetsEvaluate]: Get detect assets tasks err: %v", err)
		return fmt.Errorf("查询任务失败: %v", err)
	}
	if info == nil {
		return errors.New("任务不存在")
	}
	// 更新任务步骤
	err = detect_assets_tasks.NewModel().UpdateAny(map[string]interface{}{
		"step":        detect_assets_tasks.StepFour,
		"step_detail": detect_assets_tasks.StepFourPredict,
		"step_status": detect_assets_tasks.StepStatusDefault,
	}, mysql.WithWhere("id = ?", info.ID))
	if err != nil {
		log.Errorf("[AssetsEvaluate]: Update detect assets tasks step err: %v", err)
		return fmt.Errorf("更新任务步骤失败: %v", err)
	}

	if cfg.ExecGolangJob() {
		// 异步处理资产等级更新
		payload := asyncq.NewUpdateAssetsLevelTask(req.UserId, req.OperatorId, info.GroupId, info.ExpendFlags, strconv.FormatUint(uint64(info.ID), 10))
		err = asyncq.Enqueue(context.Background(), asyncq.UpdateAssetsLevel, payload)
		if err != nil {
			return err
		}
	} else {
		//调用php的job --UpdateAssetsLevelPhpJob
		err = asyncq.UpdateAssetsLevelPhpJob.Dispatch(req.UserId, info.GroupId, strconv.FormatUint(uint64(info.ID), 10))
		if err != nil {
			return err
		}
	}

	rsp.Success = true
	return nil
}

func StopAndDeleteTask(ctx context.Context, req *pb.DetectTaskStopRequest, rsp *pb.AssetsEvaluateResponse) error {
	// 查询任务信息
	info, err := detect_assets_tasks.NewModel().First(mysql.WithWhere("id = ? AND user_id = ?", req.ExpendId, req.UserId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("参数错误!")
		}
		log.Errorf("[StopAndDeleteTask]: 获取任务信息失败: %v", err)
		return fmt.Errorf("查询任务失败: %v", err)
	}
	if info == nil {
		return errors.New("任务不存在")
	}

	// 检查任务状态是否符合要求（步骤为2、3或4）
	if info.Step != detect_assets_tasks.StepTwo && info.Step != detect_assets_tasks.StepThree && info.Step != detect_assets_tasks.StepFour {
		return errors.New("任务状态不符合删除条件")
	}

	// 删除任务 (使用UpdateAny而不是Delete，因为模型中没有Delete方法)
	err = detect_assets_tasks.NewModel().UpdateAny(map[string]interface{}{
		"deleted_at": time.Now(), // 软删除，设置deleted_at字段
	}, mysql.WithWhere("id = ? AND user_id = ?", req.ExpendId, req.UserId))
	if err != nil {
		log.Errorf("[StopAndDeleteTask]: 删除任务失败: %v", err)
		return fmt.Errorf("删除任务失败: %v", err)
	}

	// 获取所有关联的线索任务ID
	clueTaskModel := clue_task.NewClueTasker()
	var allClueIds []uint64
	if dbGetter, ok := clueTaskModel.(interface{ DB() *gorm.DB }); ok {
		err = dbGetter.DB().Model(&clue_task.ClueTask{}).
			Where("detect_assets_tasks_id = ? AND user_id = ?", req.ExpendId, info.UserId).
			Pluck("id", &allClueIds).Error
		if err != nil {
			log.Errorf("[StopAndDeleteTask]: 获取线索任务ID失败: %v", err)
		}
	}

	// 记录日志
	log.Infof("[StopAndDeleteTask]: 停止并删除任务: expendId=%d, allClueIds=%v, user_id=%d", req.ExpendId, allClueIds, info.UserId)

	// 缓存已删除的线索任务ID
	cacheKey := fmt.Sprintf("delete_clue_tasks_id:%d", info.UserId)
	var existingIds []uint64
	// 获取已存在的缓存数据
	if ok := redisMiddleware.GetCache(cacheKey, &existingIds); !ok {
		existingIds = []uint64{}
	}

	// 合并并去重
	mergedIds := append(existingIds, allClueIds...)
	uniqueIds := make([]uint64, 0)
	seen := make(map[uint64]bool)
	for _, id := range mergedIds {
		if !seen[id] {
			seen[id] = true
			uniqueIds = append(uniqueIds, id)
		}
	}

	// 设置缓存，有效期24小时
	redisMiddleware.SetCache(cacheKey, 24*time.Hour, uniqueIds)

	// 删除线索任务
	err = clueTaskModel.DeleteByConditions(
		mysql.WithColumnValue("detect_assets_tasks_id", req.ExpendId),
		mysql.WithColumnValue("user_id", info.UserId),
	)
	if err != nil {
		log.Errorf("[StopAndDeleteTask]: 删除线索任务失败: %v", err)
	} else {
		log.Infof("[StopAndDeleteTask]: 成功删除线索任务, detect_assets_tasks_id=%d, user_id=%d", req.ExpendId, info.UserId)
	}

	// 删除线索任务ID
	if len(allClueIds) > 0 {
		// 直接使用GORM操作，不使用不存在的NewClueTaskIdser函数
		db := mysql.GetDbClient()
		var clueTaskIdsIds []uint64
		// 使用正确的表名和结构
		err = db.Table("clue_tasks_ids").
			Where("clue_tasks_id IN ?", allClueIds).
			Pluck("id", &clueTaskIdsIds).Error
		if err != nil {
			log.Errorf("[StopAndDeleteTask]: 获取线索任务ID的ID失败: %v", err)
		}

		log.Infof("[StopAndDeleteTask]: 删除线索任务ID: expendId=%d, clueTaskIdsIds=%v, user_id=%d", req.ExpendId, clueTaskIdsIds, info.UserId)

		// 删除记录
		err = db.Table("clue_tasks_ids").
			Where("clue_tasks_id IN ?", allClueIds).
			Delete(nil).Error
		if err != nil {
			log.Errorf("[StopAndDeleteTask]: 删除线索任务ID失败: %v", err)
		}
	}

	// 等待1秒
	time.Sleep(1 * time.Second)

	// 处理扩展标志
	if info.ExpendFlags != "" {
		// 删除缓存标签
		cacheTag := fmt.Sprintf("cloud_recommend_delete_flag:%d", info.UserId)
		tagKey := fmt.Sprintf("%s:%s", cacheTag, info.ExpendFlags)
		err = redisMiddleware.DelKey(ctx, tagKey)
		if err != nil {
			log.Warnf("[StopAndDeleteTask]: 删除缓存标签失败: %v", err)
		}

		// 异步删除推荐记录和结果
		go func() {
			time.Sleep(3 * time.Second)

			// 导入ES模型
			recordModel := recommend_record.NewRecommendRecordModel()

			// 删除ES索引中的推荐记录
			err := recordModel.DeleteById(info.ExpendFlags)
			if err != nil {
				log.Errorf("[StopAndDeleteTask]: 删除ES索引推荐记录失败: flag=%s, user_id=%d, err=%v", info.ExpendFlags, info.UserId, err)
			} else {
				log.Infof("[StopAndDeleteTask]: 删除ES索引推荐记录成功: flag=%s, user_id=%d", info.ExpendFlags, info.UserId)
			}

			// 删除ES索引中的推荐结果
			// 使用elastic.NewBoolQuery()创建查询条件
			query := elastic.NewBoolQuery().
				Must(elastic.NewTermQuery("flag", info.ExpendFlags)).
				Must(elastic.NewTermQuery("user_id", info.UserId))

			// 使用DeleteByQuery删除匹配的文档
			esClient := es.GetEsClient()
			_, err = esClient.DeleteByQuery().
				Index("foradar_recommend_result").
				Type("result").
				Query(query).
				Do(context.Background())

			if err != nil {
				log.Errorf("[StopAndDeleteTask]: 删除ES索引推荐结果失败: flag=%s, user_id=%d, err=%v", info.ExpendFlags, info.UserId, err)
			} else {
				log.Infof("[StopAndDeleteTask]: 删除ES索引推荐结果成功: flag=%s, user_id=%d", info.ExpendFlags, info.UserId)
			}
		}()
	}

	rsp.Success = true
	return nil
}

// DelDetectAssetsTask 删除资产测绘任务
func DelDetectAssetsTask(ctx context.Context, req *pb.DelDetectAssetsTaskRequest, rsp *pb.DelDetectAssetsTaskResponse) error {
	// 查询任务信息
	info, err := detect_assets_tasks.NewModel().First(mysql.WithWhere("id IN (?) AND user_id = ?", req.Id, req.UserId))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("参数错误!")
		}
		log.Errorf("[DelDetectAssetsTask]: 获取任务信息失败: %v", err)
		return fmt.Errorf("查询任务失败: %v", err)
	}
	if info == nil {
		return errors.New("任务不存在")
	}

	// 删除任务
	err = detect_assets_tasks.NewModel().UpdateAny(map[string]interface{}{
		"deleted_at": time.Now(), // 软删除，设置deleted_at字段
	}, mysql.WithWhere("id IN (?) AND user_id = ?", req.Id, req.UserId))
	if err != nil {
		log.Errorf("[DelDetectAssetsTask]: 删除任务失败: %v", err)
		return fmt.Errorf("删除任务失败: %v", err)
	}

	// 删除推荐线索任务
	clueTaskModel := clue_task.NewClueTasker()

	// 先查询要删除的记录数量，用于日志记录
	if dbGetter, ok := clueTaskModel.(interface{ DB() *gorm.DB }); ok {
		var deleteCount int64
		countErr := dbGetter.DB().Model(&clue_task.ClueTask{}).
			Where("detect_assets_tasks_id IN (?) AND user_id = ?", req.Id, req.UserId).
			Count(&deleteCount).Error
		if countErr != nil {
			log.Errorf("[DelDetectAssetsTask]: 查询待删除线索任务数量失败: %v", countErr)
		} else {
			log.Infof("[DelDetectAssetsTask]: 准备删除线索任务数量: %d, detect_assets_tasks_id=%v, user_id=%d", deleteCount, req.Id, req.UserId)
		}
	}

	// 使用模型的DeleteByConditions方法删除
	err = clueTaskModel.DeleteByConditions(
		mysql.WithValuesIn("detect_assets_tasks_id", req.Id),
		mysql.WithColumnValue("user_id", req.UserId),
	)
	if err != nil {
		log.Errorf("[DelDetectAssetsTask]: 删除线索任务失败: %v", err)
	} else {
		log.Infof("[DelDetectAssetsTask]: 成功删除线索任务, detect_assets_tasks_id=%v, user_id=%d", req.Id, req.UserId)
	}

	// 删除正在扫描或者扫描中的资产任务
	var deleteTaskIds []uint64

	log.Infof("[DelDetectAssetsTask]: 开始删除扫描任务 - UserId: %d, DetectAssetsTaskIds: %v", req.UserId, req.Id)
	var deleteTasks []struct {
		ID       uint
		UserID   uint64
		Status   int
		Progress float64
	}

	// 定义任务状态常量
	const (
		TaskStatusDefault = 0
		TaskStatusRunning = 1
		TaskStatusFailed  = 2
	)

	// 添加详细的查询日志
	log.Infof("[DelDetectAssetsTask]: 查询条件 - user_id: %d, detect_assets_tasks_id IN: %v, status IN: %v",
		req.UserId, req.Id, []int{TaskStatusDefault, TaskStatusRunning, TaskStatusFailed})

	// 直接使用 mysql.GetDbClient() 获取数据库连接
	db := mysql.GetDbClient()
	err = db.Table("scan_tasks").
		Where("user_id = ? AND detect_assets_tasks_id IN (?) AND status IN (?)",
			req.UserId, req.Id, []int{TaskStatusDefault, TaskStatusRunning, TaskStatusFailed}).
		Select("id, user_id, status, progress").
		Find(&deleteTasks).Error
	if err != nil {
		log.Errorf("[DelDetectAssetsTask]: 查询扫描任务失败: %v", err)
		return err
	}

	log.Infof("[DelDetectAssetsTask]: 查询到 %d 个待删除的扫描任务", len(deleteTasks))
	for i, t := range deleteTasks {
		log.Infof("[DelDetectAssetsTask]: 待删除任务[%d] - ID: %d, UserID: %d, Status: %d, Progress: %.2f",
			i, t.ID, t.UserID, t.Status, t.Progress)
	}

	// 收集要删除的任务ID
	var runningIds []uint64
	for i, t := range deleteTasks {
		deleteTaskIds = append(deleteTaskIds, uint64(t.ID))
		log.Infof("[DelDetectAssetsTask]: 处理任务[%d] - ID: %d, Status: %d, Progress: %.2f",
			i, t.ID, t.Status, t.Progress)

		// 停止正在运行的任务
		if t.Status == TaskStatusRunning && t.Progress < 41 {
			// TODO: 调用停止扫描API
			if cfg.ExecGolangJob() {
				//todo 调用go的job
				err := asyncq.DispatchStopScanJob.Dispatch(t.ID)
				if err != nil {
					log.WithContextErrorf(ctx, "[删除扫描任务] 调用停止扫描任务失败: %v", err)
				} else {
					log.WithContextInfof(ctx, "[删除扫描任务] 成功调用停止扫描任务: %v", t.ID)
				}
			} else {
				// 调用PHP的停止扫描任务job
				err := asyncq.DispatchStopScanJob.Dispatch(t.ID)
				if err != nil {
					log.WithContextErrorf(ctx, "[删除扫描任务] 调用停止扫描任务失败: %v", err)
				} else {
					log.WithContextInfof(ctx, "[删除扫描任务] 成功调用停止扫描任务: %v", t.ID)
				}
			}
			log.Infof("[DelDetectAssetsTask]: 需要停止正在运行的任务: %d (进度: %.2f)", t.ID, t.Progress)
		}

		if t.Status == TaskStatusRunning {
			runningIds = append(runningIds, uint64(t.ID))
			log.Infof("[DelDetectAssetsTask]: 添加到运行中任务列表: %d", t.ID)
		}
	}

	// 批量删除扫描任务（性能优化）
	if len(deleteTaskIds) > 0 {
		log.Infof("[DelDetectAssetsTask]: 开始批量删除 %d 个扫描任务: %v", len(deleteTaskIds), deleteTaskIds)

		// 使用批量软删除
		result := db.Table("scan_tasks").
			Where("id IN (?) AND user_id = ?", deleteTaskIds, req.UserId).
			Update("deleted_at", time.Now())

		if result.Error != nil {
			log.Errorf("[DelDetectAssetsTask]: 批量删除扫描任务失败: %v", result.Error)
		} else {
			log.Infof("[DelDetectAssetsTask]: 批量删除扫描任务成功，影响行数: %d", result.RowsAffected)
		}
	}

	// 记录日志
	if len(deleteTaskIds) > 0 {
		taskIdsJSON, _ := json.Marshal(deleteTaskIds)
		log.Infof("[DelDetectAssetsTask]: 删除扫描任务汇总: taskIds=%s, user_id=%d, 总数=%d",
			string(taskIdsJSON), req.UserId, len(deleteTaskIds))
	} else {
		log.Warnf("[DelDetectAssetsTask]: 没有找到需要删除的扫描任务 - UserId: %d, DetectAssetsTaskIds: %v",
			req.UserId, req.Id)
	}

	// 最终验证：检查是否还有相关任务存在
	var remainingCount int64
	err = db.Table("scan_tasks").
		Where("user_id = ? AND detect_assets_tasks_id IN (?)", req.UserId, req.Id).
		Count(&remainingCount).Error
	if err != nil {
		log.Errorf("[DelDetectAssetsTask]: 最终验证查询失败: %v", err)
	} else {
		log.Infof("[DelDetectAssetsTask]: 删除操作完成后，剩余相关任务数量: %d", remainingCount)
	}

	// 删除报告模版和报告
	// 1. 查询报告模板ID
	reportTemplateModel := report_template.NewModel()
	var reportTemplateIds []uint64

	// 查询要删除的报告模板ID
	if dbGetter, ok := reportTemplateModel.(interface{ DB() *gorm.DB }); ok {
		err = dbGetter.DB().Model(&report_template.ReportTemplate{}).
			Where("user_id = ? AND detect_assets_tasks_id IN (?)", req.UserId, req.Id).
			Pluck("id", &reportTemplateIds).Error
		if err != nil {
			log.Errorf("[DelDetectAssetsTask]: 查询报告模板ID失败: %v", err)
		}
	}

	// 2. 软删除报告模板（设置is_del=1）
	if len(reportTemplateIds) > 0 {
		err = reportTemplateModel.UpdateAny(map[string]interface{}{
			"is_del": report_template.DeleteOK,
		}, mysql.WithWhere("id IN (?) AND user_id = ?", reportTemplateIds, req.UserId))
		if err != nil {
			log.Errorf("[DelDetectAssetsTask]: 软删除报告模板失败: %v", err)
		}

		// 3. 删除报告
		foradarReportModel := foradar_report.NewModel()
		if dbGetter, ok := foradarReportModel.(interface{ DB() *gorm.DB }); ok {
			err = dbGetter.DB().Where("user_id = ? AND report_template_id IN (?)", req.UserId, reportTemplateIds).Delete(&foradar_report.ForadarReport{}).Error
			if err != nil {
				log.Errorf("[DelDetectAssetsTask]: 删除报告失败: %v", err)
			}
		}
	}

	// 删除推荐任务缓存标签
	if info.ExpendFlags != "" {
		cacheTag := fmt.Sprintf("cloud_recommend_delete_flag:%d", req.UserId)
		tagKey := fmt.Sprintf("%s:%s", cacheTag, info.ExpendFlags)
		err = redisMiddleware.DelKey(ctx, tagKey)
		if err != nil {
			log.Warnf("[DelDetectAssetsTask]: 删除缓存标签失败: %v", err)
		}
	}

	// 等待1秒
	time.Sleep(1 * time.Second)

	// 删除正在推荐中的资产任务
	// 使用ES查询和删除
	esClient := es.GetEsClient()

	// 1. 查询需要删除的flag
	ids := make([]interface{}, 0, len(req.Id))
	for _, id := range req.Id {
		ids = append(ids, id)
	}
	flagQuery := elastic.NewBoolQuery().
		Must(elastic.NewTermQuery("user_id", req.UserId)).
		Must(elastic.NewTermsQuery("status", 0, 1)). // 0:STATUS_DEFAULT, 1:STATUS_RUNNING
		Must(elastic.NewTermsQuery("detect_assets_tasks_id", ids...))

	// 执行搜索获取flag
	searchResult, err := esClient.Search().
		Index("foradar_recommend_record").
		Type("record").
		Query(flagQuery).
		Size(1000). // 设置一个较大的值以获取所有匹配记录
		FetchSource(true).
		Do(context.Background())

	if err != nil {
		log.Errorf("[DelDetectAssetsTask]: 查询推荐记录flag失败: %v", err)
	}

	// 提取flag值
	var flagArr []string
	if searchResult != nil && searchResult.Hits != nil {
		for _, hit := range searchResult.Hits.Hits {
			var record recommend_record.RecommendRecord
			if err := json.Unmarshal(*hit.Source, &record); err == nil && record.Flag != "" {
				flagArr = append(flagArr, record.Flag)

				// 删除单条记录
				_, err := esClient.Delete().
					Index("foradar_recommend_record").
					Type("record").
					Id(record.Id).
					Do(context.Background())

				if err != nil {
					log.Errorf("[DelDetectAssetsTask]: 删除ES索引推荐记录失败: id=%s, err=%v", record.Id, err)
				}
			}
		}
	}

	// 设置缓存标记删除
	for _, flag := range flagArr {
		cacheKey := fmt.Sprintf("RecommendRecord:delete:%d:%s", req.UserId, flag)
		redisMiddleware.SetCache(cacheKey, 3*24*time.Hour, 1)
	}

	// 删除推荐结果
	if len(flagArr) > 0 {
		// 使用elastic.NewBoolQuery()创建查询条件
		query := elastic.NewBoolQuery().
			Must(elastic.NewTermsQuery("flag", flagArr)).
			Must(elastic.NewTermQuery("user_id", req.UserId))

		// 使用DeleteByQuery删除匹配的文档
		_, err = esClient.DeleteByQuery().
			Index("foradar_recommend_result").
			Type("result").
			Query(query).
			Do(context.Background())

		if err != nil {
			log.Errorf("[DelDetectAssetsTask]: 删除ES索引推荐结果失败: flags=%v, user_id=%d, err=%v", flagArr, req.UserId, err)
		} else {
			log.Infof("[DelDetectAssetsTask]: 删除ES索引推荐结果成功: flags=%v, user_id=%d", flagArr, req.UserId)
		}
	}

	// 记录操作日志
	idsJSON, _ := json.Marshal(req.Id)
	log.Infof("[DelDetectAssetsTask]: 删除单位测绘任务: taskIds=%s, user_id=%d", string(idsJSON), req.UserId)
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		req.UserId,
		fmt.Sprintf("删除单位测绘任务，任务id：%s", string(idsJSON)),
		metadata.MustString(ctx, "client_ip"),
		req.CompanyId,
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		return err
	}

	rsp.Success = true
	return nil
}

func createTaskHosts(userId uint64, t task.Task, ips, ipv6Domains, topDomains []string) error {
	if len(topDomains) == 0 {
		return nil
	}
	rows, err := es.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
		{"MUST", [][]interface{}{
			{"user_id", "=", userId},
			{"flag", "=", t.Flag},
			{"ip", "in", ips},
		}},
	}, nil, "id")
	if err != nil {
		return fmt.Errorf("根据ip获取推荐url失败: %v", err)
	}
	hosts := utils.ListDistinctNonZero(append(ipv6Domains, utils.ListColumn[string, *recommend_result.RecommendResult](rows, func(v *recommend_result.RecommendResult) string {
		return v.Url
	})...))
	var newHosts = make([]string, 0)
	for _, host := range hosts {
		td := utils.GetTopDomain(host)
		if len(td) > 0 && slices.Contains(topDomains, td) {
			newHosts = append(newHosts, host)
		}
	}
	if len(newHosts) == 0 {
		return nil
	}
	chunks := slices.Chunk(newHosts, 50)
	// 性能问题，待优化
	for chunk := range chunks {
		taskHosts := task.TaskHost{
			TaskId: uint64(t.ID),
			Urls:   utils.AnyToStr(chunk),
		}
		_, err := mysql.NewDSL[task.TaskHost]().Create(taskHosts)
		if err != nil {
			return fmt.Errorf("创建任务主机失败: %v", err)
		}
	}
	return nil
}

// ExpandClues 扩展资产线索
func ExpandClues(ctx context.Context, req *pb.ExpandCluesRequest, rsp *pb.ExpandCluesResponse) error {
	// 根据任务ID获取任务信息
	detectTaskModel, err := detect_assets_tasks.NewModel().First(mysql.WithWhere("id = ? AND user_id = ?", req.DetectTaskId, req.UserId))
	if err != nil || detectTaskModel == nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("该任务不存在，参数错误")
		}
		log.Errorf("[ExpandClues]: 获取任务信息失败: %v", err)
		return fmt.Errorf("获取任务信息失败: %v", err)
	}

	// 检查任务状态
	if detectTaskModel.Status != detect_assets_tasks.StatusDoing {
		return errors.New("任务状态不允许扩展线索")
	}

	// 获取任务组ID
	clueGroupId := detectTaskModel.GroupId

	// 解析ReturnJson字段
	returnJson := make(map[string]interface{})
	if detectTaskModel.ReturnJson != "" {
		if err := json.Unmarshal([]byte(detectTaskModel.ReturnJson), &returnJson); err != nil {
			log.Errorf("[ExpandClues]: 解析ReturnJson失败: %v", err)
			return fmt.Errorf("解析ReturnJson失败: %v", err)
		}
	}

	// 更新任务信息
	updateData := map[string]interface{}{
		"clue_progress": 0,
		"step":          detect_assets_tasks.StepTwo, // 设置为第二步
	}
	var clueType int32
	var clueIds []uint64

	// 根据检测类型更新任务
	if req.DetectType == detect_assets_tasks.DetectTypeDepth {
		// 深度模式
		switch req.ExpandClueType {
		case 0: // 域名
			updateData["step_detail"] = detect_assets_tasks.StepTwoExpandDomain
			clueType = clues.TYPE_DOMAIN
			// 更新returnJson - 添加IP扩展步骤的线索ID
			returnJson[fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandIP)] = getClueIdsByStatus(clueGroupId, int32(clues.CLUE_DEFAULT_STATUS))
			log.Infof("[ExpandClues]: 扩展域名线索, 任务ID: %d", req.DetectTaskId)
		case 2: // ICP
			updateData["step_detail"] = detect_assets_tasks.StepTwoExpandICP
			clueType = clues.TYPE_ICP
			// 更新returnJson - 添加域名扩展步骤的线索ID
			returnJson[fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandDomain)] = getClueIdsByStatus(clueGroupId, int32(clues.CLUE_DEFAULT_STATUS))
			log.Infof("[ExpandClues]: 扩展ICP线索, 任务ID: %d", req.DetectTaskId)
		case 1: // 证书
			updateData["step_detail"] = detect_assets_tasks.StepTwoExpandCert
			clueType = clues.TYPE_CERT
			// 更新returnJson - 添加ICP扩展步骤的线索ID
			returnJson[fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandICP)] = getClueIdsByStatus(clueGroupId, int32(clues.CLUE_DEFAULT_STATUS))
			log.Infof("[ExpandClues]: 扩展证书线索, 任务ID: %d", req.DetectTaskId)
		case 6: // IP段
			updateData["step_detail"] = detect_assets_tasks.StepTwoExpandIP
			clueType = clues.TYPE_IP
			// 更新returnJson - 添加确认步骤的线索ID
			returnJson[fmt.Sprintf("%d", detect_assets_tasks.StepTwoConfirm)] = getClueIdsByStatus(clueGroupId, int32(clues.CLUE_PASS_STATUS))
			log.Infof("[ExpandClues]: 扩展IP段线索, 任务ID: %d", req.DetectTaskId)
		}

		// 更新returnJson到数据库
		returnJsonBytes, _ := json.Marshal(returnJson)
		updateData["return_json"] = string(returnJsonBytes)
		updateData["detect_type"] = detect_assets_tasks.DetectTypeDepth
		updateData["step_status"] = detect_assets_tasks.StepStatusDefault

		// 获取要扩展的线索ID
		if clueType == clues.TYPE_DOMAIN {
			// 域名类型包括根域和子域名
			clueIds = getClueIdsByTypesAndStatus(clueGroupId, []int32{int32(clues.TYPE_DOMAIN), int32(clues.TYPE_SUBDOMAIN)}, int32(clues.CLUE_REFUSE_STATUS), true)
		} else {
			// 其他类型
			clueIds = getClueIdsByTypeAndStatus(clueGroupId, clueType, int32(clues.CLUE_REFUSE_STATUS), true)
		}

		// 更新线索状态 - 深度模式
		if req.ExpandClueType == clues.TYPE_DOMAIN || req.ExpandClueType == clues.TYPE_IP {
			// 域名或IP扩展：不把LOGO设置为审核通过状态
			updateClueStatusExcludeType(clueGroupId, int32(clues.TYPE_LOGO))
		} else {
			// 其他类型：更新所有默认状态的线索为通过状态
			updateAllClueStatus(clueGroupId)
		}

	} else {
		// 极速模式
		updateData["step_detail"] = detect_assets_tasks.StepTwoExpandAll
		updateData["detect_type"] = detect_assets_tasks.DetectTypeSpeed
		updateData["step_status"] = detect_assets_tasks.StepStatusDefault
		// 更新returnJson - 添加确认步骤的线索ID
		returnJson[fmt.Sprintf("%d", detect_assets_tasks.StepTwoConfirm)] = getClueIdsByStatus(clueGroupId, int32(clues.CLUE_PASS_STATUS))
		returnJsonBytes, _ := json.Marshal(returnJson)
		updateData["return_json"] = string(returnJsonBytes)

		log.Infof("[ExpandClues]: 极速模式扩展线索, 任务ID: %d, 智能模式: %d", req.DetectTaskId, req.IsIntellectMode)

		// 极速模式线索优先级：IP -> 域名+子域名 -> ICP
		clueIds = getClueIdsByTypeAndStatus(clueGroupId, int32(clues.TYPE_IP), int32(clues.CLUE_PASS_STATUS), false)
		if len(clueIds) == 0 {
			clueIds = getClueIdsByTypesAndStatus(clueGroupId, []int32{int32(clues.TYPE_DOMAIN), int32(clues.TYPE_SUBDOMAIN)}, int32(clues.CLUE_PASS_STATUS), false)
			if len(clueIds) == 0 {
				clueIds = getClueIdsByTypeAndStatus(clueGroupId, int32(clues.TYPE_ICP), int32(clues.CLUE_PASS_STATUS), false)
			}
		}

		// 极速模式：忽略非手动输入的LOGO线索
		ignoreAutoLogoClues(clueGroupId)
		// 更新所有默认状态的线索为通过状态
		updateAllClueStatus(clueGroupId)
	}

	// 更新任务信息
	err = detect_assets_tasks.NewModel().UpdateAny(
		updateData,
		mysql.WithWhere("id = ?", detectTaskModel.ID),
	)
	if err != nil {
		log.Errorf("[ExpandClues]: 更新任务信息失败: %v", err)
		return fmt.Errorf("更新任务信息失败: %v", err)
	}

	log.Infof("[ExpandClues]: 线索扩展 - 下发线索ID集合, 任务ID: %d, 线索数量: %d, 线索ID列表: %v", req.DetectTaskId, len(clueIds), clueIds)

	// 如果没有线索ID，则标记任务完成并返回
	if len(clueIds) == 0 {
		err = detect_assets_tasks.NewModel().UpdateAny(
			map[string]interface{}{
				"step_status": detect_assets_tasks.StepStatusDone,
			},
			mysql.WithWhere("id = ?", detectTaskModel.ID),
		)
		if err != nil {
			log.Errorf("[ExpandClues]: 更新任务状态失败: %v", err)
		}

		rsp.Success = true
		rsp.IsEmpty = true
		return nil
	}

	// 准备创建扩展线索任务
	clueTaskModel := clue_task.NewClueTasker()

	// 创建线索任务名称
	taskName := ""
	if req.DetectType == detect_assets_tasks.DetectTypeDepth {
		// 深度模式：根据扩展类型设置任务名称
		typeName := utils.TypeToCn(int(req.ExpandClueType))
		taskName = "资产测绘-专家扩展模式-线索扩充-" + typeName + "-" + time.Now().Format("2006-01-02-15-04-05")
	} else {
		taskName = "资产测绘-极速或者智能模式-线索扩充-" + time.Now().Format("2006-01-02-15-04-05")
	}

	// 创建线索任务
	safeUserId := 0
	// 尝试从ctx中获取user_id信息,如果获取失败，则标记为1
	userID, err := metadata.GetInt64(ctx, "user_id")
	// 如果user_id不一致，则标记为1
	if req.UserId != uint64(userID) {
		safeUserId = int(userID)
	}
	newTask := &clue_task.ClueTask{
		UserId:              req.UserId,
		CompanyId:           req.OperateCompanyId,
		GroupId:             clueGroupId,
		DetectAssetsTasksId: req.DetectTaskId,
		Name:                taskName,
		Status:              clue_task.RunningStatus,
		Progress:            0,
		IsShow:              clue_task.ShowHidden,
		SafeUserId:          uint64(safeUserId),
	}

	err = clueTaskModel.Create(newTask)
	if err != nil {
		log.Errorf("[ExpandClues]: 创建线索任务失败: %v", err)
		return fmt.Errorf("创建线索任务失败: %v", err)
	}

	log.Infof("[ExpandClues]: 创建线索任务成功, 任务ID: %d, 类型: %d", newTask.Id, req.ExpandClueType)

	// 将线索ID插入到 clue_task_ids 表中
	db := mysql.GetDbClient()
	now := time.Now()

	// 批量插入
	for _, clueId := range clueIds {
		clueTaskIds := struct {
			ClueTasksID uint64    `gorm:"column:clue_tasks_id"`
			ClueID      uint64    `gorm:"column:clue_id"`
			Status      int       `gorm:"column:status"`
			CreatedAt   time.Time `gorm:"column:created_at"`
		}{
			ClueTasksID: newTask.Id,
			ClueID:      clueId,
			Status:      0, // 默认状态
			CreatedAt:   now,
		}

		err = db.Table("clue_tasks_ids").Create(&clueTaskIds).Error
		if err != nil {
			log.Errorf("[ExpandClues]: 插入线索任务ID关联失败, clueId: %d, error: %v", clueId, err)
		}
	}

	if cfg.ExecGolangJob() {
		// 调用异步任务处理线索扩展
		payload := &asyncq.ExpandCluesJobPayload{
			UserId:           req.UserId,
			ClueTaskId:       newTask.Id,
			DetectTaskId:     req.DetectTaskId,
			IsIntellectMode:  int32(detectTaskModel.IsIntellectMode),
			ClueType:         clueType,
			IsFakeClueExpend: 0, // 默认不是仿冒钓鱼的线索扩展流程
		}

		log.Infof("[ExpandClues] 准备下发Go异步任务 - UserId:%d, ClueTaskId:%d, DetectTaskId:%d, IsIntellectMode:%d, ClueType:%d, IsFakeClueExpend:%d",
			payload.UserId, payload.ClueTaskId, payload.DetectTaskId, payload.IsIntellectMode, payload.ClueType, payload.IsFakeClueExpend)

		if err := asyncq.Enqueue(ctx, asyncq.ExpandCluesJob, payload); err != nil {
			log.Errorf("[ExpandClues] Go异步任务下发失败 - UserId:%d, ClueTaskId:%d, DetectTaskId:%d, error:%v",
				payload.UserId, payload.ClueTaskId, payload.DetectTaskId, err)
		} else {
			log.Infof("[ExpandClues] Go异步任务下发成功 - UserId:%d, ClueTaskId:%d, DetectTaskId:%d",
				payload.UserId, payload.ClueTaskId, payload.DetectTaskId)
		}
	} else {
		// 调用 DispatchGolangJobExpendClue
		log.Infof("[ExpandClues] 准备下发PHP异步任务 - UserId:%d, ClueTaskId:%d, DetectTaskId:%d, IsIntellectMode:%d",
			req.UserId, newTask.Id, req.DetectTaskId, int32(detectTaskModel.IsIntellectMode))

		err = asyncq.DispatchGolangJobExpendClue.Dispatch(req.UserId, newTask.Id, req.DetectTaskId, int32(detectTaskModel.IsIntellectMode))
		if err != nil {
			log.Errorf("[ExpandClues] PHP异步任务下发失败 - UserId:%d, ClueTaskId:%d, DetectTaskId:%d, error:%v",
				req.UserId, newTask.Id, req.DetectTaskId, err)
		} else {
			log.Infof("[ExpandClues] PHP异步任务下发成功 - UserId:%d, ClueTaskId:%d, DetectTaskId:%d",
				req.UserId, newTask.Id, req.DetectTaskId)
		}
	}
	// 记录操作日志 - 使用异步任务
	logContent := fmt.Sprintf("扩展资产线索，任务ID: %d, 扩展类型: %d, 线索组ID: %d",
		req.DetectTaskId, req.ExpandClueType, clueGroupId)

	if err := asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		req.UserId,
		logContent,
		req.ClientIp,
		req.OperateCompanyId,
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	)); err != nil {
		log.Errorf("[ExpandClues]: 记录操作日志失败: %v", err)
	}

	// 设置返回值
	rsp.Success = true
	rsp.IsEmpty = false

	log.Infof("[ExpandClues]: 扩展资产线索完成，任务ID: %d, 线索组ID: %d, 扩展类型: %d, 线索数量: %d",
		req.DetectTaskId, clueGroupId, req.ExpandClueType, len(clueIds))
	return nil
}

// ConfirmAllClues 确认所有线索
func ConfirmAllClues(ctx context.Context, req *pb.ConfirmAllCluesRequest, rsp *pb.ExpandCluesResponse) error {
	// 1. 检查测绘任务是否存在
	detectTaskModel := detect_assets_tasks.NewModel()
	detectTask, err := detectTaskModel.First(
		detect_assets_tasks.WithID(req.DetectTaskId),
		detect_assets_tasks.WithUserID(req.UserId),
	)
	if err != nil || detectTask == nil {
		log.Errorf("[ConfirmAllClues] 获取测绘任务失败: %v", err)
		return errors.New("该任务不存在，参数错误")
	}

	// 2. 获取分组ID
	clueGroupId := detectTask.GroupId

	// 3. 根据测绘类型更新return_json
	if detectTask.DetectType == detect_assets_tasks.DetectTypeDepth {
		// 深度模式
		var returnArray map[string]interface{}
		json.Unmarshal([]byte(detectTask.ReturnJson), &returnArray)

		// 获取状态为默认的线索ID列表
		var clueIds []uint64
		err = mysql.GetDbClient().Table("clues").
			Where("is_deleted = ? AND group_id = ? AND status = ?",
				clues.NOT_DELETE, clueGroupId, clues.CLUE_DEFAULT_STATUS).
			Pluck("id", &clueIds).Error
		if err != nil {
			log.Errorf("[ConfirmAllClues] 获取线索列表失败: %v", err)
			return err
		}

		returnArray[fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandCert)] = clueIds
		returnJsonBytes, _ := json.Marshal(returnArray)
		detectTask.ReturnJson = string(returnJsonBytes)
	} else {
		// 极速模式
		var returnArray map[string]interface{}
		json.Unmarshal([]byte(detectTask.ReturnJson), &returnArray)

		// 获取状态为默认的线索ID列表
		var clueIds []uint64
		err = mysql.GetDbClient().Table("clues").
			Where("is_deleted = ? AND group_id = ? AND status = ?",
				clues.NOT_DELETE, clueGroupId, clues.CLUE_DEFAULT_STATUS).
			Pluck("id", &clueIds).Error
		if err != nil {
			log.Errorf("[ConfirmAllClues] 获取线索列表失败: %v", err)
			return err
		}

		returnArray[fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandAll)] = clueIds
		returnJsonBytes, _ := json.Marshal(returnArray)
		detectTask.ReturnJson = string(returnJsonBytes)
	}

	// 4. 更新测绘任务状态
	detectTask.StepDetail = detect_assets_tasks.StepTwoFormAll
	detectTask.StepStatus = detect_assets_tasks.StepStatusDone

	// 6. 更新线索状态为通过
	err = mysql.GetDbClient().Table("clues").
		Where("is_deleted = ? AND group_id = ? AND status = ?",
			clues.NOT_DELETE, clueGroupId, clues.CLUE_DEFAULT_STATUS).
		Update("status", clues.CLUE_PASS_STATUS).Error
	if err != nil {
		log.Errorf("[ConfirmAllClues] 更新线索状态失败: %v", err)
		return err
	}

	// 7. 更新测绘任务中的线索数量
	var cluesCount int64
	err = mysql.GetDbClient().Table("clues").
		Where("is_deleted = ? AND group_id = ? AND status = ? AND type IN ?",
			clues.NOT_DELETE, clueGroupId, clues.CLUE_PASS_STATUS,
			[]int{clues.TYPE_LOGO, clues.TYPE_IP, clues.TYPE_DOMAIN, clues.TYPE_ICP, clues.TYPE_CERT, clues.TYPE_SUBDOMAIN}).
		Count(&cluesCount).Error
	if err != nil {
		log.Errorf("[ConfirmAllClues] 统计线索数量失败: %v", err)
	} else {
		detectTask.CluesCount = cast.ToString(cluesCount)
	}

	// 8. 保存测绘任务更新
	err = detectTaskModel.UpdateAny(
		map[string]interface{}{
			"step_detail": detect_assets_tasks.StepTwoFormAll,
			"step_status": detect_assets_tasks.StepStatusDone,
			"clues_count": uint64(cluesCount),
			"return_json": detectTask.ReturnJson,
		},
		detect_assets_tasks.WithID(req.DetectTaskId),
	)
	if err != nil {
		log.Errorf("[ConfirmAllClues] 保存测绘任务失败: %v", err)
		return err
	}

	// 9. 更新线索分组为可见
	const SHOW_GROUP = 0 // 线索分组可见状态
	err = mysql.GetDbClient().Table("clues_groups").
		Where("id = ?", clueGroupId).
		Update("is_show", SHOW_GROUP).Error
	if err != nil {
		log.Errorf("[ConfirmAllClues] 更新线索分组状态失败: %v", err)
	}

	// 10. 记录操作日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		req.UserId,
		"资产测绘-生成线索总表",
		req.ClientIp,
		req.CompanyId,
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		return err
	}
	// 11. 处理数据泄露监控数据（如果需要）
	// 检查是否启用了自动数据泄漏监控
	var isAutoLeakAssets int
	err = mysql.GetDbClient().Table("detect_assets_tasks").
		Where("id = ?", req.DetectTaskId).
		Select("is_auto_leak_assets").
		Row().Scan(&isAutoLeakAssets)

	if err == nil && isAutoLeakAssets > 0 {
		// 获取域名类线索（去重）
		var domainClues []string
		err = mysql.GetDbClient().Table("clues").
			Where("is_deleted = ? AND group_id = ? AND status = ? AND type IN ?",
				clues.NOT_DELETE, clueGroupId, clues.CLUE_PASS_STATUS,
				[]int{clues.TYPE_IP, clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN}).
			Distinct("content").
			Pluck("content", &domainClues).Error
		if err != nil {
			log.Errorf("[ConfirmAllClues] 获取域名线索失败: %v", err)
		}

		// 处理每个域名线索
		for _, name := range domainClues {
			// 检查是否已存在
			var existingKeyword sensitive_keyword.SensitiveKeyword
			err = mysql.GetDbClient().Table("sensitive_keyword").
				Where("user_id = ? AND name = ?", req.UserId, name).
				First(&existingKeyword).Error

			if err == nil {
				// 更新已存在的关键词
				err = mysql.GetDbClient().Table("sensitive_keyword").
					Where("id = ?", existingKeyword.Id).
					Updates(map[string]interface{}{
						"status":         1, // 启用状态
						"type":           2, // 域名类型
						"detect_task_id": req.DetectTaskId,
						"updated_at":     time.Now(), // 添加更新时间
					}).Error
				if err != nil {
					log.Errorf("[ConfirmAllClues] 更新敏感关键词失败: user_id=%d, name=%s, error=%v", req.UserId, name, err)
				} else {
					log.Infof("[ConfirmAllClues] 更新敏感关键词成功: user_id=%d, name=%s, id=%d", req.UserId, name, existingKeyword.Id)
				}
			} else if errors.Is(err, gorm.ErrRecordNotFound) {
				// 创建新的关键词
				operatorId := uint64(0)
				if req.OperatorId > 0 {
					operatorId = req.OperatorId
				}

				now := time.Now()
				// 使用原始SQL插入，避免结构体字段问题
				err = mysql.GetDbClient().Exec(`
					INSERT INTO sensitive_keyword 
					(name, type, status, user_id, detect_task_id, company_id, operator_id, created_at, updated_at) 
					VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
					name, 2, 1, req.UserId, req.DetectTaskId, req.CompanyId, operatorId, now, now,
				).Error

				if err != nil {
					log.Errorf("[ConfirmAllClues] 创建敏感关键词失败: user_id=%d, name=%s, error=%v", req.UserId, name, err)
				} else {
					log.Infof("[ConfirmAllClues] 创建敏感关键词成功: user_id=%d, name=%s", req.UserId, name)
				}
			} else {
				// 处理其他数据库错误
				log.Errorf("[ConfirmAllClues] 查询敏感关键词失败: user_id=%d, name=%s, error=%v", req.UserId, name, err)
				continue // 继续处理下一个域名
			}

			// 异步调用创建敏感关键词任务
			// 域名类型的敏感关键词：canLeak=true(1), canData=false(0)
			if cfg.ExecGolangJob() {
				//todo 调用go的job
				_ = asyncq.CreateSensitiveKeywordJob.Dispatch(req.UserId, name, 1, 0, uint64(req.DetectTaskId))
			} else {
				_ = asyncq.CreateSensitiveKeywordJob.Dispatch(req.UserId, name, 1, 0, uint64(req.DetectTaskId))
			}
		}
	}

	return nil
}

// ClueBlack 标记线索到黑名单
func ClueBlack(ctx context.Context, req *pb.ClueBlackRequest, rsp *pb.Empty) error {

	// 1. 获取选择的线索ID列表
	ids, err := GetSelectClueIds(req)
	if err != nil {
		log.Errorf("[ClueBlack] 获取线索ID失败: %v", err)
		return err
	}

	if len(ids) == 0 {
		return errors.New("未选择任何线索")
	}

	// 2. 获取线索列表
	var clueList []*clues.Clue
	err = mysql.GetDbClient().Table("clues").
		Where("user_id = ? AND id IN ?", req.UserId, ids).
		Find(&clueList).Error
	if err != nil {
		log.Errorf("[ClueBlack] 获取线索列表失败: %v", err)
		return errors.New("获取线索列表失败")
	}

	if len(clueList) == 0 {
		return errors.New("未找到指定线索")
	}

	// 3. 将线索标记为已忽略
	err = mysql.GetDbClient().Table("clues").
		Where("user_id = ? AND id IN ?", req.UserId, ids).
		Update("status", clues.CLUE_REFUSE_STATUS).Error
	if err != nil {
		log.Errorf("[ClueBlack] 更新线索状态失败: %v", err)
	}

	// 3. 准备插入黑名单关键词的数据
	blackKeywordModel := clue_black_keyword.NewClueBlackKeyworder()
	insertData := make([]clue_black_keyword.ClueBlackKeyword, 0, len(clueList))
	// 4. 遍历线索，准备黑名单数据
	for _, clue := range clueList {
		// 检查是否已存在于黑名单
		var exists bool

		if clue.Type == clues.TYPE_LOGO {
			// 对于LOGO类型，通过hash检查是否存在
			var count int64
			err := mysql.GetDbClient().Table("clue_black_keyword").
				Where("hash = ?", clue.Hash).
				Count(&count).Error
			exists = err == nil && count > 0
			var info utils.DownloadFile
			// 处理LOGO内容路径
			if !exists {
				content := clue.Content
				// 检查是否需要解析下载URL
				if !strings.Contains(content, "pp/public") {
					s, err := utils.LaravelDecrypt(content)
					if err != nil {
						content = ""
					}
					err = json.Unmarshal([]byte(s), &info)
					if err != nil {
						content = ""
					}
					content = info.Url
				}

				// 统一处理文件路径格式，转换为app/public/xxx格式
				if content != "" {
					// 移除可能的前缀路径
					cleanPath := content
					cleanPath = strings.TrimPrefix(cleanPath, "/data/foradar/")
					cleanPath = strings.TrimPrefix(cleanPath, "/data/storage/")
					cleanPath = strings.TrimPrefix(cleanPath, "/data/")
					cleanPath = strings.TrimPrefix(cleanPath, "storage/")
					cleanPath = strings.TrimPrefix(cleanPath, "app/public/")
					cleanPath = strings.TrimPrefix(cleanPath, "/")

					// 构建最终的app/public路径
					content = "app/public/" + cleanPath
				}

				// 创建黑名单记录
				blackKeyword := clue_black_keyword.ClueBlackKeyword{
					Type:      int8(clue.Type),
					Hash:      clue.Hash,
					UserId:    req.OperatorId,
					CompanyId: req.CompanyId,
					Name:      content,
					Status:    clue_black_keyword.STATS_ENABLE,
				}
				insertData = append(insertData, blackKeyword)
			}
		} else {
			// 对于其他类型，通过内容检查是否存在
			var count int64
			err := mysql.GetDbClient().Table("clue_black_keyword").
				Where("name = ?", clue.Content).
				Count(&count).Error
			exists = err == nil && count > 0

			if !exists {
				// 创建黑名单记录
				blackKeyword := clue_black_keyword.ClueBlackKeyword{
					Type:      int8(clue.Type),
					Hash:      clue.Hash,
					UserId:    req.OperatorId,
					CompanyId: req.CompanyId,
					Name:      clue.Content,
					Status:    clue_black_keyword.STATS_ENABLE,
				}
				insertData = append(insertData, blackKeyword)
			}
		}
	}

	// 5. 批量插入黑名单数据
	if len(insertData) > 0 {
		for _, item := range insertData {
			err = blackKeywordModel.Create(&item)
			if err != nil {
				log.Errorf("[ClueBlack] 插入黑名单关键词失败: %v", err)
			}
		}
	}

	// 6. 记录操作日志
	if err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		req.UserId,
		"标记线索黑名单到库里",
		req.ClientIp,
		req.CompanyId,
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	)); err != nil {
		log.Errorf("[ClueBlack] 记录操作日志失败: %v", err)
	}

	return nil
}

// PassClue 审核线索状态
func PassClue(ctx context.Context, req *pb.PassClueRequest, resp *pb.Empty) error {
	log.Info("PassClue", "开始处理线索审核请求", map[string]interface{}{
		"user_id":  req.UserId,
		"group_id": req.GroupId,
		"status":   req.Status,
	})

	// 获取选择的线索ID列表
	ids, err := GetSelectIds(req, false)
	if err != nil {
		log.Error("PassClue", "获取线索ID失败", err)
		return err
	}

	if len(ids) == 0 {
		return errors.New("参数错误: 未选择任何线索")
	}

	// 更新线索状态
	err = mysql.GetDbClient().Table("clues").
		Where("user_id = ? AND id IN ? AND group_id = ? AND is_deleted = ?",
			req.UserId, ids, req.GroupId, clues.NOT_DELETE).
		Updates(map[string]interface{}{"status": req.Status}).Error
	if err != nil {
		log.Error("PassClue", "更新线索状态失败", err)
		return err
	}

	// 判断测绘任务是否在生成总表时进行操作-线索数量更新生成
	var detectInfo detect_assets_tasks.DetectAssetsTask
	err = mysql.GetDbClient().Table("detect_assets_tasks").
		Where("group_id = ? AND step_detail = ?",
			req.GroupId, detect_assets_tasks.StepTwoFormAll).
		First(&detectInfo).Error

	if err == nil {
		// 查询通过状态的线索数量
		var cluesCount int64
		err = mysql.GetDbClient().Table("clues").
			Where("is_deleted = ? AND group_id = ? AND status = ?",
				clues.NOT_DELETE, req.GroupId, clues.CLUE_PASS_STATUS).
			Count(&cluesCount).Error
		if err != nil {
			log.Error("PassClue", "查询通过状态的线索数量失败", err)
			return err
		}

		// 更新测绘任务的线索数量
		err = mysql.GetDbClient().Table("detect_assets_tasks").
			Where("id = ?", detectInfo.ID).
			Updates(map[string]interface{}{"clues_count": cluesCount}).Error
		if err != nil {
			log.Error("PassClue", "更新测绘任务的线索数量失败", err)
			return err
		}
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error("PassClue", "查询测绘任务失败", err)
		return err
	}

	// 记录操作日志
	var content string
	idsJSON, _ := json.Marshal(ids)
	idsStr := string(idsJSON)

	switch req.Status {
	case clues.CLUE_DEFAULT_STATUS:
		log.Info("PassClue", "线索审核为待确认", map[string]interface{}{
			"user_id":  req.UserId,
			"clue_ids": ids,
		})
		content = "这些线索被设置为待确认状态,操作的线索id集合：" + idsStr
	case clues.CLUE_REFUSE_STATUS:
		log.Info("PassClue", "线索审核未通过", map[string]interface{}{
			"user_id":  req.UserId,
			"clue_ids": ids,
		})
		content = "这些线索被设置为忽略状态,操作的线索id集合：" + idsStr
	case clues.CLUE_PASS_STATUS:
		log.Info("PassClue", "线索审核通过", map[string]interface{}{
			"user_id":  req.UserId,
			"clue_ids": ids,
		})
		content = "这些线索被设置为审核通过状态,操作的线索id集合：" + idsStr
	}

	// 使用异步任务记录操作日志
	// 注意：req.CompanyId不存在，使用0作为默认值
	var companyID uint64 = 0
	if req.OperatorId > 0 {
		companyID = uint64(req.OperatorId)
	}

	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		req.UserId,
		content,
		req.ClientIp,
		companyID,
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.Error("PassClue", "创建操作日志失败", err)
		return err
	}

	return nil
}

// TaskIndex 获取任务简报列表
func TaskIndex(ctx context.Context, req *pb.TaskIndexRequest, rsp *pb.TaskIndexResponse) error {
	// 获取用户ID和公司ID
	userID := req.UserId
	companyID := req.OperateCompanyId

	// 创建查询构建器
	model := detect_assets_tasks.NewModel()

	// 构建查询条件
	var conditions []mysql.HandleFunc
	conditions = append(conditions, detect_assets_tasks.WithUserID(uint64(userID)))

	// 应用搜索条件
	if req.Keyword != "" {
		conditions = append(conditions, mysql.WithLRLike("name", req.Keyword))
	}

	// 如果状态不为空，添加状态筛选
	if req.Status > 0 {
		conditions = append(conditions, mysql.WithColumnValue("status", req.Status))
	}

	// 如果检测类型不为空，添加检测类型筛选
	if req.DetectType > 0 {
		conditions = append(conditions, mysql.WithColumnValue("detect_type", req.DetectType))
	}

	// 如果分组ID不为空，添加分组ID筛选
	if req.GroupId > 0 {
		conditions = append(conditions, mysql.WithColumnValue("group_id", req.GroupId))
	}

	// 如果扩展来源不为空，添加扩展来源筛选
	if req.ExpandSource != "" {
		conditions = append(conditions, mysql.WithColumnValue("expand_source", req.ExpandSource))
	}
	// 添加创建时间范围条件
	if len(req.CreatedAtRange) == 2 {
		startTime, err := time.Parse("2006-01-02", req.CreatedAtRange[0])
		if err == nil {
			endTime, err := time.Parse("2006-01-02", req.CreatedAtRange[1])
			if err == nil {
				endTime = endTime.Add(24*time.Hour - time.Second) // 设置为当天的23:59:59
				conditions = append(conditions, mysql.WithBetween("created_at", startTime, endTime))
			}
		}
	}
	// 获取总数
	total, err := model.Count(conditions...)
	if err != nil {
		return err
	}

	// 计算偏移量
	offset := (req.Page - 1) * req.PerPage
	if offset < 0 {
		offset = 0
	}

	// 添加排序和分页条件
	conditions = append(conditions, func(db *gorm.DB) {
		db.Order("FIELD(status, 0,1,3,4,2), id DESC").Offset(int(offset)).Limit(int(req.PerPage))
	})

	// 获取分页数据
	tasks, err := model.FindAll(conditions...)
	if err != nil {
		return err
	}

	// 提取所有任务ID
	var taskIDs []uint64
	for _, taskItem := range tasks {
		taskIDs = append(taskIDs, uint64(taskItem.ID))
	}

	// 查询关联的扫描任务ID
	var scanTasks []task.Task
	if len(taskIDs) > 0 {
		scanTasks, err = task.NewModel().FindAll(
			task.WithUserID(uint64(userID)),
			mysql.WithValuesIn("detect_assets_tasks_id", taskIDs),
		)
		if err != nil {
			return err
		}
	}

	// 构建扫描任务ID映射
	scanTaskMap := make(map[uint64][]uint64)
	for _, st := range scanTasks {
		taskID := *st.DetectAssetsTasksID
		scanTaskMap[taskID] = append(scanTaskMap[taskID], uint64(st.ID))
	}

	// 查询关联的域名任务ID
	var domainTasks []domain_task.DomainTask
	if len(taskIDs) > 0 {
		domainTasks, err = domain_task.NewDomainTaskModel().FindAll(
			domain_task.WithUserID(uint64(userID)),
			mysql.WithValuesIn("detect_task_id", taskIDs),
		)
		if err != nil {
			return err
		}
	}

	// 构建域名任务ID映射
	domainTaskMap := make(map[uint64][]uint64)
	for _, dt := range domainTasks {
		taskID := dt.DetectTaskId
		domainTaskMap[taskID] = append(domainTaskMap[taskID], uint64(dt.Id))
	}

	// 查询用户名映射
	db := mysql.GetDbClient()
	var users []struct {
		ID   uint64 `gorm:"column:id"`
		Name string `gorm:"column:name"`
	}
	db.Table("users").Select("id, name").Find(&users)

	// 构建用户名映射
	userNameMap := make(map[uint64]string)
	for _, u := range users {
		userNameMap[u.ID] = u.Name
	}

	// 构建响应数据
	var items []*pb.InfoResponse
	for _, t := range tasks {
		item := &pb.InfoResponse{
			Id:                        uint64(t.ID),
			UserId:                    t.UserId,
			CompanyId:                 t.CompanyId,
			SafeUserId:                t.SafeUserId,
			Name:                      t.Name,
			Step:                      int32(t.Step),
			StepDetail:                int32(t.StepDetail),
			DetectType:                int32(t.DetectType),
			GroupId:                   t.GroupId,
			StepStatus:                int32(t.StepStatus),
			Status:                    int32(t.Status),
			ExpendFlags:               t.ExpendFlags,
			ExpendProgress:            float64(t.ExpendProgress),
			Progress:                  float64(t.Progress),
			ClueProgress:              float64(t.ClueProgress),
			UpdateAssetsLevelProgress: float64(t.UpdateAssetsLevelProgress),
			ReturnJson:                t.ReturnJson,
			SureIpNum:                 t.SureIpNum,
			UnsureIpNum:               t.UnsureIpNum,
			ThreatenIpNum:             t.ThreatenIpNum,
			ExpendFirstStepClueNode:   t.ExpendFirstStepClueNode,
			IsExtractClue:             int32(t.IsExtractClue),
			IsCheckRisk:               int32(t.IsCheckRisk),
			ExpandSource:              int32(t.ExpandSource),
			IsIntellectMode:           int32(t.IsIntellectMode),
			IsIntellectFailed:         int32(t.IsIntellectFailed),
			CluesCount:                t.CluesCount,
			CompanyJson:               t.CompanyJSON,
			Username:                  userNameMap[uint64(t.SafeUserId)],
			CreatedAt:                 t.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:                 t.UpdatedAt.Format("2006-01-02 15:04:05"),
			IsShow:                    int32(t.IsShow),
			IsAutoExpendIp:            int32(t.IsAutoExpendIp),
			IsAutoBusinessApi:         int32(t.IsAutoBusinessApi),
			IsNeedHunter:              int32(t.IsNeedHunter),
			IsNeedDnschecker:          int32(t.IsNeedDnschecker),
			IsAutoUrlApi:              int32(t.IsAutoURLApi),
			IsFinishUrlApi:            int32(t.IsFinishURLApi),
			IsAutoLeakAssets:          int32(t.IsAutoLeakAsset),
			IsAutoDataAssets:          int32(t.IsAutoDataAsset),
		}

		// 设置关联的扫描任务ID
		if scanIDs, ok := scanTaskMap[uint64(t.ID)]; ok {
			item.ScanTaskIds = scanIDs
		}

		// 设置关联的域名任务ID
		if domainIDs, ok := domainTaskMap[uint64(t.ID)]; ok {
			item.DomainTaskIds = domainIDs
		}

		items = append(items, item)
	}

	// 设置响应
	rsp.CurrentPage = req.Page
	rsp.PerPage = req.PerPage
	rsp.Total = int32(total)
	rsp.Items = items

	// 记录操作日志（使用异步任务）
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(userID),
		"云端推荐-获取任务简报列表",
		req.ClientIp,
		uint64(companyID),
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.Errorf("[TaskIndex]: 创建操作日志失败: %v", err)
	}

	return nil
}

// GetFofaAssetsNum 根据线索计算fofa资产的数量
// 对应PHP中的getFofaAssetsNum方法
func GetFofaAssetsNum(ctx context.Context, req *pb.GetFofaAssetsNumRequest, rsp *pb.GetFofaAssetsNumResponse) error {
	// 获取用户ID
	userId := req.UserId

	// 查找线索
	clue := &clues.Clue{}
	err := mysql.GetDbClient().Where("id = ? AND user_id = ? AND is_deleted = ?", req.Id, userId, clues.NOT_DELETE).First(clue).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("参数错误!")
		}
		return err
	}

	// 根据线索类型选择缓存键内容
	var cacheContent string
	if clue.Type == clues.TYPE_LOGO {
		// ICON 类型使用 hash 字段
		cacheContent = fmt.Sprintf("%d", clue.Hash)
	} else {
		// 其他类型使用 content 字段
		cacheContent = clue.Content
	}

	// 构建缓存键，添加 foradar_cache 前缀
	cacheKey := fmt.Sprintf("foradar_cache:fofa_assets:clue:count:%s", cacheContent)

	// 构建缓存结果结构
	type CacheResult struct {
		Num     uint32 `json:"num"`
		FofaUrl string `json:"fofa_url"`
	}

	// 尝试从缓存中获取结果（兼容PHP序列化和JSON格式）
	if result, err := parseFofaAssetsCache(ctx, cacheKey); err == nil {
		log.WithContextInfof(ctx, "[GetFofaAssetsNum] 缓存解析成功！num=%d, fofa_url=%s", result.Num, result.FofaUrl)
		// 缓存存在且解析成功，直接使用
		rsp.Num = result.Num
		rsp.FofaUrl = result.FofaUrl
		return nil
	} else {
		log.WithContextInfof(ctx, "[GetFofaAssetsNum] 缓存解析失败，错误: %v", err)
	}

	// 缓存不存在或解析失败，重新计算
	// 解析查询字符串
	queryStr := clues_pkg.ParseQueryStr([]*clues.Clue{clue}, false, false)

	// 构建请求参数
	fofaReq := &corePb.FofaQueryCountRequest{
		Qbase64:          queryStr,
		Full:             true,
		CanGetLimitCount: false,
	}

	// 调用fofa API获取资产数量
	var fofaResp *corePb.FofaQueryCountResponse

	// 根据配置选择调用方式
	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		fofaResp = &corePb.FofaQueryCountResponse{}
		err = corePb.HttpClient(http.MethodPost, "/api/v1/fofa/query_count", fofaReq, fofaResp)
		if err != nil {
			return err
		}
	} else {
		// 否则rpc微服务调用
		fofaResp, err = corePb.GetProtoCoreClient().FofaQueryCount(ctx, fofaReq, microx.SetTimeout(30, 29)...)
		if err != nil {
			return err
		}
	}

	// 构建结果
	result := CacheResult{
		Num:     fofaResp.Count,
		FofaUrl: fmt.Sprintf("https://fofa.info/result?qbase64=%s", base64.StdEncoding.EncodeToString([]byte(queryStr))),
	}

	// 保存到缓存
	cacheBytes, _ := json.Marshal(result)
	_ = redisMiddleware.Set(ctx, cacheKey, string(cacheBytes), 2*time.Hour)

	// 设置响应
	rsp.Num = result.Num
	rsp.FofaUrl = result.FofaUrl

	return nil
}

// WhoisInfo 获取域名的Whois信息
// 对应PHP中的whois方法
func WhoisInfo(ctx context.Context, req *pb.WhoisInfoRequest, rsp *pb.WhoisInfoResponse) error {
	// 获取用户ID和公司ID
	userId := req.UserId
	from := req.From

	var domainContent string

	// 根据来源类型获取线索内容
	if from == 0 {
		// 默认clues表的域名线索
		clue := &clues.Clue{}
		err := mysql.GetDbClient().Where("id = ? AND user_id = ? AND is_deleted = ?", req.Id, userId, clues.NOT_DELETE).First(clue).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("参数错误!")
			}
			return err
		}
		domainContent = clue.Content
	} else if from == 1 {
		// 企业线索库的域名
		companyClue := &company_clues.CompanyClues{}
		err := mysql.GetDbClient().Where("id = ? AND user_id = ?", req.Id, userId).First(companyClue).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("参数错误!")
			}
			return err
		}
		domainContent = companyClue.Content
	} else if from == 2 {
		// 组织架构企业的域名线索
		orgClue := &organization_company_clues.OrganizationCompanyClue{}
		err := mysql.GetDbClient().Where("id = ? AND user_id = ?", req.Id, userId).First(orgClue).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("参数错误!")
			}
			return err
		}
		domainContent = orgClue.ClueCompanyName
	} else {
		return fmt.Errorf("不支持的来源类型")
	}

	// 获取顶级域名
	whoisDomain := utils.GetTopDomain(domainContent)
	if whoisDomain == "" {
		// 域名为空，返回空结果
		return nil
	}

	// 创建请求参数
	param := &corePb.WhoisDomainBasicRequest{
		Domain: whoisDomain,
	}

	var whoisResp *corePb.WhoisBasicResponse
	var err error

	// 根据配置选择调用方式
	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		whoisResp = &corePb.WhoisBasicResponse{}
		err = corePb.HttpClient(http.MethodPost, "/api/v1/digital/whois_basic", param, whoisResp)
		if err != nil {
			return err
		}
	} else {
		// 否则rpc微服务调用
		whoisResp, err = corePb.GetProtoCoreClient().WhoisBasic(ctx, param, microx.SetTimeout(30, 29)...)
		if err != nil {
			return err
		}
	}

	// 设置响应数据
	rsp.SponsoringRegistrar = whoisResp.SponsoringRegistrar
	rsp.RegistrantName = whoisResp.RegistrantName
	rsp.RegistrantMobile = whoisResp.RegistrantMobile
	rsp.RegistrantOrg = whoisResp.RegistrantOrg
	rsp.RegistrationDate = whoisResp.RegistrationDate
	rsp.ExpirationDate = whoisResp.ExpirationDate

	return nil
}

// RecommendAssets 推荐资产
// 对应PHP中的recommandAssets方法
func RecommendAssets(ctx context.Context, req *pb.RecommendAssetsRequest, rsp *pb.RecommendAssetsResponse) error {
	// 获取用户ID和公司ID
	userId := req.UserId
	expendId := req.ExpendId
	groupId := req.GroupId

	// 查找测绘任务
	info, err := detect_assets_tasks.NewModel().First(
		detect_assets_tasks.WithID(expendId),
		detect_assets_tasks.WithUserID(userId),
		detect_assets_tasks.WithGroupID(groupId),
	)
	if err != nil || info == nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("测绘任务ID参数错误!")
		}
		return err
	}

	// 获取线索列表
	clueArr, err := clues.NewCluer().ListAll(
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("group_id", groupId),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
		mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO), // 供应链线索不进行推荐资产
		mysql.WithValuesIn("type", []int{
			clues.TYPE_DOMAIN,
			clues.TYPE_CERT,
			clues.TYPE_ICP,
			clues.TYPE_LOGO,
			clues.TYPE_IP,
			clues.TYPE_SUBDOMAIN,
		}),
	)
	if err != nil {
		return err
	}

	// 如果线索为空，直接跳到资产已经推荐完成页面
	if len(clueArr) == 0 {
		flag := utils.Md5sHash(fmt.Sprintf("%d%d%d", time.Now().Unix(), userId, expendId), false)
		err = detect_assets_tasks.NewModel().UpdateAny(
			map[string]interface{}{
				"expend_progress": 100,
				"step":            detect_assets_tasks.StepThree,
				"step_detail":     detect_assets_tasks.StepThreeRecommendAll,
				"step_status":     detect_assets_tasks.StepStatusDone,
				"expend_flags":    flag,
			},
			detect_assets_tasks.WithID(expendId),
		)
		if err != nil {
			return err
		}
		rsp.Flag = flag
		return nil
	}

	// 校验当前企业是否还有测绘次数
	if req.CompanyId > 0 {
		companyInfo, err := company.NewCompanyModel().FindById(req.CompanyId, 0)
		if err != nil {
			return err
		}
		limitCount := companyInfo.CheckLimit("cloud_recommend", 0)
		if limitCount < 0 {
			return errors.New("推荐资产,已达到限制次数!")
		}

		// 更新企业限制次数
		err = companyInfo.UpdateLimit("cloud_recommend", 1, true)
		if err != nil {
			log.Errorf("[RecommendAssets] 更新企业限制次数失败: %v", err)
		}
	}

	// 收集线索ID
	clueIds := make([]uint64, 0, len(clueArr))
	for _, clue := range clueArr {
		clueIds = append(clueIds, uint64(clue.Id))
	}

	// 生成任务名称
	taskName := fmt.Sprintf("单位资产测绘-云端推荐任务(%s)", time.Now().Format("2006-01-02 15:04:05"))

	// 生成flag
	flag := genFlag(userId, clueIds, taskName)

	// 更新测绘任务
	err = detect_assets_tasks.NewModel().UpdateAny(
		map[string]interface{}{
			"expend_flags": flag,
			"step":         detect_assets_tasks.StepThree,
			"step_detail":  detect_assets_tasks.StepThreeRecommendAll,
			"step_status":  detect_assets_tasks.StepStatusDefault,
		},
		detect_assets_tasks.WithID(expendId),
	)
	if err != nil {
		return err
	}

	log.Infof("[RecommendAssets] 将step_status设置为0, flag=%s, info=%+v", flag, info)

	// 同步实现addRecord逻辑，而不是使用异步任务
	err = addRecommendRecord(userId, req.CompanyId, groupId, taskName, flag, clueIds, clueArr, expendId, req.OperatorId)
	if err != nil {
		log.Errorf("[RecommendAssets] 创建推荐记录失败: %v", err)
		return err
	}
	if cfg.ExecGolangJob() {
		//调用推荐资产的job
		err = asyncq.Enqueue(context.Background(), asyncq.RecommendAssetJob, asyncq.RecommendAssetJobPayload{
			RecommendRecordId: flag,
			UserId:            userId,
			DetectTaskId:      expendId,
		})
		if err != nil {
			log.Errorf("[RecommendAssets] 调用推荐资产job失败: %v", err)
			return err
		}
	} else {
		// 调用 DispatchGolangJobRecommendJob 方法
		err = asyncq.DispatchGolangJobRecommendJob.Dispatch(flag)
		if err != nil {
			log.Errorf("[RecommendAssets] 调用 DispatchGolangJobRecommendJob 失败: %v", err)
			return err
		}
	}

	// 记录操作日志
	err = asyncq.Enqueue(context.Background(), asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		userId,
		"单位资产测绘-云端推荐任务",
		req.ClientIp,
		req.CompanyId,
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.Errorf("[RecommendAssets] 记录操作日志失败: %v", err)
	}

	// 更新表里面的线索数量
	var clueNum int64
	err = mysql.GetDbClient().Model(&clues.Clue{}).
		Where("user_id = ? AND is_deleted = ? AND group_id = ? AND status = ?", userId, clues.NOT_DELETE, groupId, clues.CLUE_PASS_STATUS).
		Where("type IN ?", []int{clues.TYPE_LOGO, clues.TYPE_IP, clues.TYPE_DOMAIN, clues.TYPE_ICP, clues.TYPE_CERT, clues.TYPE_SUBDOMAIN}).
		Count(&clueNum).Error
	if err != nil {
		log.Errorf("[RecommendAssets] 统计线索数量失败: %v", err)
	} else {
		err = detect_assets_tasks.NewModel().UpdateAny(
			map[string]interface{}{
				"clues_count": uint64(clueNum),
			},
			detect_assets_tasks.WithID(expendId),
		)
		if err != nil {
			log.Errorf("[RecommendAssets] 更新线索数量失败: %v", err)
		}
	}

	// 记录操作者日志
	if req.OperatorId > 0 && req.OperatorId != userId {
		err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
			userId,
			fmt.Sprintf("操作者用户id:%d,进行云端推荐资产操作", req.OperatorId),
			req.ClientIp,
			req.CompanyId,
			operate_logs.FIND_ASSETS,
			operate_logs.TYPE_OPERATE,
		))
		if err != nil {
			log.Errorf("[RecommendAssets] 记录操作者日志失败: %v", err)
		}
	}

	// 如果是安服账号，并且非本地化，那么把总库的数据确认
	// 对应PHP逻辑：只要单位测绘的任务才去确认
	if !cfg.LoadAPP().IsLocalForadar {
		// 获取用户信息
		userInfo, err := user2.NewUserModel().FindById(userId)
		if err == nil && userInfo != nil && userInfo.IsSafeUser() && info.ExpandSource == 0 {
			// 调用确认线索总库任务
			payload := asyncq.ConfirmGeneralCluesJobPayload{
				UserId:  userId,
				GroupId: groupId,
			}
			err = asyncq.Enqueue(ctx, asyncq.ConfirmGeneralCluesJob, payload)
			if err != nil {
				log.Errorf("[RecommendAssets] 调用确认线索总库任务失败: %v", err)
			} else {
				log.Infof("[RecommendAssets] 安服账号确认线索同步到线索总库任务下发成功, user_id=%d, group_id=%d", userId, groupId)
			}
		}
	}

	// 设置响应
	rsp.Flag = flag
	return nil
}

// addRecommendRecord 添加推荐记录
// 对应PHP中的addRecord方法
func addRecommendRecord(userId, companyId, groupId uint64, taskName, flag string, clueIds []uint64, clueArr []*clues.Clue, expendId, operatorId uint64) error {
	// 检查线索是否为空
	if len(clueArr) == 0 {
		return fmt.Errorf("线索不能为空!")
	}

	// 获取分组名称
	var groupName string
	if groupId > 0 {
		group, err := clues_groups.NewCluesGrouper().First(
			mysql.WithColumnValue("id", groupId),
		)
		if err == nil {
			groupName = group.Name
		}
	}

	// 按类型分组线索
	clueTypes := make(map[int][]map[string]interface{})
	for _, clue := range clueArr {
		item := map[string]interface{}{
			"id":                clue.Id,
			"type":              clue.Type,
			"content":           clue.Content,
			"group_id":          clue.GroupId,
			"clue_company_name": clue.ClueCompanyName,
			"source":            clue.Source,
			"punycode_domain":   clue.PunycodeDomain,
		}

		// 处理LOGO类型线索
		if clue.Type == clues.TYPE_LOGO && clue.Content != "" {
			item["content"] = storage.GenDownloadUrl(clue.Content, fmt.Sprintf("%d", clue.Hash))
		}

		if _, ok := clueTypes[clue.Type]; !ok {
			clueTypes[clue.Type] = []map[string]interface{}{}
		}
		clueTypes[clue.Type] = append(clueTypes[clue.Type], item)
	}

	// 判断是否只有子域名类型线索推荐资产
	typeArr := make([]int, 0)
	for _, clue := range clueArr {
		found := false
		for _, t := range typeArr {
			if t == clue.Type {
				found = true
				break
			}
		}
		if !found {
			typeArr = append(typeArr, clue.Type)
		}
	}
	isOnlySubdomain := 0
	if len(typeArr) == 1 && typeArr[0] == clues.TYPE_SUBDOMAIN {
		log.Infof("[RecommendAssets] 只有子域名推荐资产, expendId=%d, id=%s", expendId, flag)
		isOnlySubdomain = 1
	}

	// 检查记录是否已存在
	existingRecord, err := recommend_record.NewRecommendRecordModel().FindByID(flag)
	if err == nil && existingRecord.Id != "" {
		// 记录已存在，不需要创建
		return nil
	}

	// 构建推荐记录数据
	recordMap := map[string]interface{}{
		"id":                     flag,
		"flag":                   flag,
		"task_name":              taskName,
		"cron_id":                nil,
		"group_id":               int(groupId),
		"group_name":             groupName,
		"count":                  0,
		"user_id":                int(userId),
		"company_id":             int(companyId),
		"progress":               0,
		"status":                 0, // StatusDefault
		"confirm":                0, // ConfirmDefault
		"op_id":                  int(operatorId),
		"detect_assets_tasks_id": int(expendId),
		"clue_id":                convertUint64ToIntSlice(clueIds),
		"is_only_subdomain":      isOnlySubdomain,
		"created_at":             time.Now().Format(utils.DateTimeLayout),
		"updated_at":             time.Now().Format(utils.DateTimeLayout),
	}

	if subdomainClues, ok := clueTypes[clues.TYPE_SUBDOMAIN]; ok {
		recordMap["subdomain"] = subdomainClues
	}
	if domainClues, ok := clueTypes[clues.TYPE_DOMAIN]; ok {
		recordMap["domain"] = domainClues
	}
	if certClues, ok := clueTypes[clues.TYPE_CERT]; ok {
		recordMap["cert"] = certClues
	}
	if icpClues, ok := clueTypes[clues.TYPE_ICP]; ok {
		recordMap["icp"] = icpClues
	}
	if keywordClues, ok := clueTypes[clues.TYPE_KEYWORD]; ok {
		recordMap["title"] = keywordClues
	}
	if logoClues, ok := clueTypes[clues.TYPE_LOGO]; ok {
		recordMap["logo"] = logoClues
	}

	// 创建推荐记录
	_, err = es.GetEsClient().Index().Index("foradar_recommend_record").Type("record").
		Id(flag).BodyJson(recordMap).Do(context.Background())
	if err != nil {
		return err
	}

	return nil
}

// HunterSql 生成Hunter查询语句
func HunterSql(ctx context.Context, req *pb.HunterSqlRequest, rsp *pb.HunterSqlResponse) error {
	// 获取任务信息
	taskInfo, err := detect_assets_tasks.NewModel().First(
		detect_assets_tasks.WithID(uint64(req.Id)),
		detect_assets_tasks.WithUserID(uint64(req.UserId)),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[Hunter SQL] 获取任务信息失败: %v", err)
		return fmt.Errorf("参数错误")
	}

	// 检查任务信息是否为空
	if taskInfo == nil {
		log.WithContextErrorf(ctx, "[Hunter SQL] 任务不存在: id=%d, user_id=%d", req.Id, req.UserId)
		return fmt.Errorf("任务不存在")
	}

	// 获取线索ID
	if taskInfo.ExpendFlags == "" {
		log.WithContextErrorf(ctx, "[Hunter SQL] 线索为空，无法生成查询语句!")
		return fmt.Errorf("线索为空，无法生成查询语句")
	}

	// 先从RecommendRecord的ES索引取线索ID，然后再去clue表查询
	// 对应PHP: $clueIdArr = RecommendRecord::query()->where('user_id', $user_id)->where('id',$info['expend_flags'])->value('clue_id');
	recommendRecordModel := recommend_record.NewRecommendRecordModel()
	recommendRecord, err := recommendRecordModel.FindByID(taskInfo.ExpendFlags)
	if err != nil {
		log.WithContextErrorf(ctx, "[Hunter SQL] 获取推荐记录失败: %v", err)
		return fmt.Errorf("获取推荐记录失败")
	}
	if recommendRecord == nil || len(recommendRecord.ClueId) == 0 {
		log.WithContextErrorf(ctx, "[Hunter SQL] 线索为空，无法生成查询语句!")
		return fmt.Errorf("线索为空，无法生成查询语句!")
	}

	// 将int数组转换为uint64数组
	clueIds := make([]uint64, len(recommendRecord.ClueId))
	for i, id := range recommendRecord.ClueId {
		clueIds[i] = uint64(id)
	}

	// 获取线索列表 - 按照PHP的逻辑从clue表查询
	var clueItems []*pb.ClueInfo

	// 根据线索ID查询证书和域名类型线索
	allClues, err := clues.NewCluer().ListAll(
		mysql.WithColumnValue("user_id", uint64(req.UserId)),
		mysql.WithValuesIn("id", clueIds),
		mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
		mysql.WithColumnValue("is_deleted", 0),
		mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO),
		mysql.WithValuesIn("type", []int{clues.TYPE_CERT, clues.TYPE_DOMAIN}),
	)
	if err == nil {
		for _, clue := range allClues {
			clueItems = append(clueItems, &pb.ClueInfo{
				Id:      uint64(clue.Id),
				Content: clue.Content,
				Type:    int32(clue.Type),
				Hash:    strconv.Itoa(clue.Hash),
			})
		}
	}

	// 检查是否有线索
	if len(clueItems) == 0 {
		return fmt.Errorf("没有找到可用的线索，无法生成查询语句!")
	}

	// 将线索按每2个一组分组
	var sqlList []string
	for i := 0; i < len(clueItems); i += 2 {
		end := i + 2
		if end > len(clueItems) {
			end = len(clueItems)
		}
		group := clueItems[i:end]

		// 生成Hunter查询语句
		sql := ParseHunterQueryStr(group)
		if sql != "" {
			sqlList = append(sqlList, sql)
		}
	}

	// 设置响应
	rsp.Sql = sqlList
	return nil
}

// ImportResultsConfirm 导入推荐资产数据
func ImportResultsConfirm(ctx context.Context, req *pb.ImportResultsConfirmRequest, rsp *pb.ImportResultsConfirmResponse) error {
	// 获取任务信息
	taskInfo, err := detect_assets_tasks.NewModel().First(
		detect_assets_tasks.WithID(uint64(req.DetectTaskId)),
		detect_assets_tasks.WithUserID(uint64(req.UserId)),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[导入推荐资产] 获取任务信息失败: %v", err)
		return fmt.Errorf("任务不存在，参数错误!")
	}

	// 检查任务信息是否为空
	if taskInfo == nil {
		log.WithContextErrorf(ctx, "[导入推荐资产] 任务不存在: detect_task_id=%d, user_id=%d", req.DetectTaskId, req.UserId)
		return fmt.Errorf("任务不存在")
	}

	// 获取分组名称
	groupInfo, err := clues_groups.NewCluesGrouper().First(
		mysql.WithColumnValue("id", taskInfo.GroupId),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[导入推荐资产] 获取分组信息失败: %v", err)
		return fmt.Errorf("分组不存在，参数错误!")
	}

	// 读取Excel文件
	sheetData, err := batchImportRead(req.File)
	if err != nil {
		log.WithContextErrorf(ctx, "[导入推荐资产] 读取Excel文件失败: %v", err)
		return err
	}

	// 更新任务状态
	err = detect_assets_tasks.NewModel().UpdateAny(map[string]interface{}{
		"step":        detect_assets_tasks.StepThree,
		"step_detail": detect_assets_tasks.StepThreeImport,
		"step_status": detect_assets_tasks.StepStatusDone,
	}, detect_assets_tasks.WithID(uint64(req.DetectTaskId)))
	if err != nil {
		log.WithContextErrorf(ctx, "[导入推荐资产] 更新任务状态失败: %v", err)
		return fmt.Errorf("更新任务状态失败!")
	}

	// 获取已存在的推荐资产IP
	var recommendAllIps []string
	// 查询已有的IP列表
	recommendResults, err := recommend_result.NewRecommendResultModel().FindByCondition(&recommend_result.FindCondition{
		UserId: int(req.UserId),
		Flag:   taskInfo.ExpendFlags,
	})
	if err != nil {
		log.WithContextErrorf(ctx, "[导入推荐资产] 获取已存在的推荐资产IP失败: %v", err)
	} else {
		// 提取唯一IP
		ipMap := make(map[string]bool)
		for _, result := range recommendResults {
			if result.Ip != "" && !ipMap[result.Ip] {
				recommendAllIps = append(recommendAllIps, result.Ip)
				ipMap[result.Ip] = true
			}
		}
	}

	// 处理Excel数据
	countArray := make(map[string]int)
	countUniqueIps := 0
	now := time.Now().Format("2006-01-02 15:04:05")

	// 处理每行数据
	for _, row := range sheetData {
		// 处理数据，去掉特殊字符
		singleData := make(map[string]string)
		for key, value := range row {
			// 去掉BOM标记
			key = strings.TrimPrefix(key, "\ufeff")
			singleData[key] = value
		}

		// 检查必要字段
		ip := singleData["IP"]
		port := singleData["端口"]
		protocol := singleData["协议"]

		if ip == "" || port == "" || protocol == "" {
			log.WithContextInfof(ctx, "[导入推荐资产] 跳过缺少必要字段的数据: %v", singleData)
			continue
		}

		// 检查是否是内网IP
		isIP, isPrivate := utils.IsPrivateIP(ip)
		if !isIP || isPrivate {
			log.WithContextInfof(ctx, "[导入推荐资产] 跳过内网IP: %v", ip)
			continue
		}

		// 处理IPv6地址
		ip = strings.ToLower(utils.CompleteIPv6(ip))
		if !utils.IsIP(ip) {
			log.WithContextInfof(ctx, "[导入推荐资产] 跳过无效IP: %v", ip)
			continue
		}

		// 统计IP数量
		countArray[ip]++

		// 提取子域名
		subdomain := utils.GetSubdomain(singleData["url"])

		// 生成唯一ID
		id := utils.Md5sHash(ip+protocol+port+subdomain+taskInfo.ExpendFlags, false)

		// 检查记录是否已存在
		exists := false
		existsResults, err := recommend_result.NewRecommendResultModel().FindByCondition(&recommend_result.FindCondition{
			UserId: int(req.UserId),
			Flag:   taskInfo.ExpendFlags,
			Ids:    []string{id},
		})
		if err == nil && len(existsResults) > 0 {
			exists = true
		}
		if exists {
			log.WithContextInfof(ctx, "[导入推荐资产] 数据已存在，跳过: %v", map[string]interface{}{"id": id, "ip": ip, "subdomain": subdomain})
			continue
		}

		// 检查IP是否已存在
		ipExists := false
		for _, existingIP := range recommendAllIps {
			if existingIP == ip {
				ipExists = true
				break
			}
		}
		if !ipExists {
			countUniqueIps++
			recommendAllIps = append(recommendAllIps, ip)
		}

		// 构建推荐资产记录
		record := &recommend_result.RecommendResult{
			Id:           id,
			GroupId:      int(taskInfo.GroupId),
			GroupName:    groupInfo.Name,
			IsIPv6:       utils.IsIPv6(ip),
			Ip:           ip,
			Port:         port,
			Url:          singleData["url"],
			Protocol:     protocol,
			BaseProtocol: singleData["通讯协议"],
			Title:        singleData["网站标题"],
			Domain:       utils.GetTopDomain(singleData["url"]),
			Subdomain:    subdomain,
			Cert:         GetCert(singleData["证书"]),
			Icp:          singleData["ICP"],
			Logo: recommend_result.Logo{
				Hash:    0,
				Content: "",
			},
			SourceUpdatedAt: singleData["更新时间"],
			Reason:          []recommend_result.RecommendReason{},
			Flag:            taskInfo.ExpendFlags,
			UserId:          int(req.UserId),
			CompanyId:       int(req.CompanyId),
			CertRaw:         singleData["证书"],
			AssetsFrom:      1,                                     // 第三方资产
			AssetsSource:    recommend_result.ASSETS_SOURCE_HUNTER, // Hunter
			Status:          0,                                     // 默认状态
			Audit:           1,                                     // 已审核通过
			CreatedAt:       now,
			UpdatedAt:       singleData["更新时间"],
		}

		// 如果更新时间为空，使用当前时间
		if record.SourceUpdatedAt == "" {
			record.SourceUpdatedAt = now
		}
		if record.UpdatedAt == "" {
			record.UpdatedAt = now
		}
		if record.BaseProtocol == "" {
			record.BaseProtocol = "tcp"
		}

		// 保存推荐资产记录
		err = recommend_result.NewRecommendResultModel().Create([]*recommend_result.RecommendResult{record})
		if err != nil {
			log.WithContextErrorf(ctx, "[导入推荐资产] 保存推荐资产记录失败: %v", err)
			continue
		}
	}
	// 设置响应
	rsp.CountIps = int32(len(countArray))
	rsp.CountTotal = int32(sumMapValues(countArray))
	rsp.CountSuccessIps = int32(countUniqueIps)
	rsp.CountSuccessTotal = 0

	return nil
}

// getClueIdsByStatus 根据状态获取线索ID列表
func getClueIdsByStatus(groupId uint64, status int32) []interface{} {
	clueModel := clues.NewCluer()
	clueList, err := clueModel.ListAll(
		mysql.WithColumnValue("group_id", groupId),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		mysql.WithColumnValue("status", status),
	)
	if err != nil {
		log.Errorf("[getClueIdsByStatus]: 获取线索列表失败: %v", err)
		return []interface{}{}
	}

	ids := make([]interface{}, 0, len(clueList))
	for _, clue := range clueList {
		ids = append(ids, clue.Id)
	}
	return ids
}

// getClueIdsByTypeAndStatus 根据类型和状态获取线索ID列表（排除供应链线索）
func getClueIdsByTypeAndStatus(groupId uint64, clueType int32, excludeStatus int32, isExclude bool) []uint64 {
	clueModel := clues.NewCluer()

	var conditions []mysql.HandleFunc
	conditions = append(conditions,
		mysql.WithColumnValue("group_id", groupId),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		mysql.WithColumnValue("type", clueType),
		mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO), // 供应链线索不进行扩展
	)

	if isExclude {
		// 排除指定状态
		conditions = append(conditions, mysql.WithWhere("status != ?", excludeStatus))
	} else {
		// 包含指定状态
		conditions = append(conditions, mysql.WithColumnValue("status", excludeStatus))
	}

	clueList, err := clueModel.ListAll(conditions...)
	if err != nil {
		log.Errorf("[getClueIdsByTypeAndStatus]: 获取线索列表失败: %v", err)
		return []uint64{}
	}

	ids := make([]uint64, 0, len(clueList))
	for _, clue := range clueList {
		ids = append(ids, uint64(clue.Id))
	}
	return ids
}

// getClueIdsByTypesAndStatus 根据多个类型和状态获取线索ID列表（排除供应链线索）
func getClueIdsByTypesAndStatus(groupId uint64, clueTypes []int32, excludeStatus int32, isExclude bool) []uint64 {
	clueModel := clues.NewCluer()

	var conditions []mysql.HandleFunc
	conditions = append(conditions,
		mysql.WithColumnValue("group_id", groupId),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO), // 供应链线索不进行扩展
	)

	// 添加类型条件
	typeValues := make([]interface{}, len(clueTypes))
	for i, t := range clueTypes {
		typeValues[i] = t
	}
	conditions = append(conditions, mysql.WithWhere("type IN (?)", typeValues))

	if isExclude {
		// 排除指定状态
		conditions = append(conditions, mysql.WithWhere("status != ?", excludeStatus))
	} else {
		// 包含指定状态
		conditions = append(conditions, mysql.WithColumnValue("status", excludeStatus))
	}

	clueList, err := clueModel.ListAll(conditions...)
	if err != nil {
		log.Errorf("[getClueIdsByTypesAndStatus]: 获取线索列表失败: %v", err)
		return []uint64{}
	}

	ids := make([]uint64, 0, len(clueList))
	for _, clue := range clueList {
		ids = append(ids, uint64(clue.Id))
	}
	return ids
}

// updateClueStatusExcludeType 更新线索状态，排除指定类型
func updateClueStatusExcludeType(groupId uint64, excludeType int32) {
	db := mysql.GetDbClient()
	err := db.Table("clues").
		Where("is_deleted = ?", clues.NOT_DELETE).
		Where("status = ?", clues.CLUE_DEFAULT_STATUS).
		Where("group_id = ?", groupId).
		Where("type != ?", excludeType).
		Update("status", clues.CLUE_PASS_STATUS).Error

	if err != nil {
		log.Errorf("[updateClueStatusExcludeType]: 更新线索状态失败: %v", err)
	}
}

// updateAllClueStatus 更新所有默认状态的线索为通过状态
func updateAllClueStatus(groupId uint64) {
	db := mysql.GetDbClient()
	err := db.Table("clues").
		Where("is_deleted = ?", clues.NOT_DELETE).
		Where("status = ?", clues.CLUE_DEFAULT_STATUS).
		Where("group_id = ?", groupId).
		Update("status", clues.CLUE_PASS_STATUS).Error

	if err != nil {
		log.Errorf("[updateAllClueStatus]: 更新线索状态失败: %v", err)
	}
}

// ignoreAutoLogoClues 忽略非手动输入的LOGO线索
func ignoreAutoLogoClues(groupId uint64) {
	db := mysql.GetDbClient()
	err := db.Table("clues").
		Where("is_deleted = ?", clues.NOT_DELETE).
		Where("group_id = ?", groupId).
		Where("type = ?", clues.TYPE_LOGO).
		Where("source != ?", 0). // 非手动输入的线索
		Update("status", clues.CLUE_REFUSE_STATUS).Error

	if err != nil {
		log.Errorf("[ignoreAutoLogoClues]: 忽略LOGO线索失败: %v", err)
	}
}
