package detect_assets

import (
	"testing"

	"github.com/stretchr/testify/assert"
	core "micro-service/coreService/proto"
)

// TestQccResultToDropList 测试企查查结果转换函数
func TestQccResultToDropList(t *testing.T) {
	testCases := []struct {
		name     string
		input    *core.QCCNameSearchResponse
		expected int
		desc     string
	}{
		{
			name: "empty result",
			input: &core.QCCNameSearchResponse{
				Data: []*core.QCCNameSearchResponseNameSearchUnit{},
			},
			expected: 0,
			desc:     "空结果测试",
		},
		{
			name: "single result",
			input: &core.QCCNameSearchResponse{
				Data: []*core.QCCNameSearchResponseNameSearchUnit{
					{
						Name:      "腾讯科技(深圳)有限公司",
						HitReason: "企业名称",
					},
				},
			},
			expected: 1,
			desc:     "单个结果测试",
		},
		{
			name: "multiple results",
			input: &core.QCCNameSearchResponse{
				Data: []*core.QCCNameSearchResponseNameSearchUnit{
					{
						Name:      "腾讯科技(深圳)有限公司",
						HitReason: "企业名称",
					},
					{
						Name:      "腾讯音乐娱乐科技(深圳)有限公司",
						HitReason: "企业名称",
					},
					{
						Name:      "腾讯云计算(北京)有限责任公司",
						HitReason: "企业名称",
					},
				},
			},
			expected: 3,
			desc:     "多个结果测试",
		},
		{
			name: "results with empty fields",
			input: &core.QCCNameSearchResponse{
				Data: []*core.QCCNameSearchResponseNameSearchUnit{
					{
						Name:      "",
						HitReason: "企业名称",
					},
					{
						Name:      "正常公司名称",
						HitReason: "",
					},
				},
			},
			expected: 2,
			desc:     "包含空字段的结果测试",
		},
		{
			name: "nil data",
			input: &core.QCCNameSearchResponse{
				Data: nil,
			},
			expected: 0,
			desc:     "nil数据测试",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := qccResultToDropList(tc.input)

			assert.Equal(t, tc.expected, len(result), "Result length should match expected")
			assert.NotNil(t, result, "Result should not be nil")

			// 验证结果结构
			for i, detail := range result {
				assert.NotNil(t, detail, "Detail should not be nil at index %d", i)
				if tc.input.Data != nil && i < len(tc.input.Data) {
					assert.Equal(t, tc.input.Data[i].Name, detail.Name, "Name should match at index %d", i)
					assert.Equal(t, tc.input.Data[i].HitReason, detail.Reason, "Reason should match at index %d", i)
				}
				t.Logf("Result %d: Name=%s, Reason=%s", i, detail.Name, detail.Reason)
			}

			t.Logf("QccResultToDropList test (%s): input length=%d, result length=%d",
				tc.desc, len(tc.input.Data), len(result))
		})
	}
}

// TestCompanyHelperFunctions 测试公司相关辅助函数
func TestCompanyHelperFunctions(t *testing.T) {
	t.Run("企查查结果转换函数", func(t *testing.T) {
		// 测试各种输入情况
		testInputs := []*core.QCCNameSearchResponse{
			nil, // 这会导致panic，但我们要测试健壮性
			{Data: nil},
			{Data: []*core.QCCNameSearchResponseNameSearchUnit{}},
			{Data: []*core.QCCNameSearchResponseNameSearchUnit{
				{Name: "测试公司1", HitReason: "企业名称"},
				{Name: "测试公司2", HitReason: "法人代表"},
			}},
		}

		for i, input := range testInputs {
			if input == nil {
				// 跳过nil输入，避免panic
				t.Logf("Skipping nil input test %d", i)
				continue
			}

			result := qccResultToDropList(input)
			assert.NotNil(t, result, "Result should not be nil for input %d", i)
			t.Logf("Helper test %d: input data length=%d, result length=%d",
				i, len(input.Data), len(result))
		}
	})
}

// TestCompanyEdgeCases 公司相关边界情况测试
func TestCompanyEdgeCases(t *testing.T) {
	t.Run("空输入处理", func(t *testing.T) {
		// 测试空企查查结果转换
		result := qccResultToDropList(&core.QCCNameSearchResponse{Data: []*core.QCCNameSearchResponseNameSearchUnit{}})
		assert.Empty(t, result, "Empty input should return empty result")

		// 测试nil数据转换
		result = qccResultToDropList(&core.QCCNameSearchResponse{Data: nil})
		assert.Empty(t, result, "Nil data should return empty result")
	})

	t.Run("异常输入处理", func(t *testing.T) {
		// 测试包含特殊字符的数据
		specialData := &core.QCCNameSearchResponse{
			Data: []*core.QCCNameSearchResponseNameSearchUnit{
				{Name: "", HitReason: ""},
				{Name: "   ", HitReason: "   "},
				{Name: "测试\n公司", HitReason: "企业\t名称"},
				{Name: "🚀公司", HitReason: "特殊字符"},
			},
		}
		result := qccResultToDropList(specialData)
		assert.Equal(t, 4, len(result), "All special data should be preserved")

		// 验证特殊字符被正确处理
		for i, detail := range result {
			assert.Equal(t, specialData.Data[i].Name, detail.Name, "Name should match at index %d", i)
			assert.Equal(t, specialData.Data[i].HitReason, detail.Reason, "Reason should match at index %d", i)
		}
	})
}
