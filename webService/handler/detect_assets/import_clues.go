package detect_assets

import (
	"context"
	"errors"
	"fmt"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clue_task"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/domain_assets"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

// AsyncICPTask 异步ICP查询任务
type AsyncICPTask struct {
	ClueID      uint64
	QueryType   string // "domain", "icp", "subdomain"
	QueryValue  string
	UserId      uint64
	GroupId     uint64
	IsSubdomain bool
}

// ImportClues 导入线索
func ImportClues(ctx context.Context, req *pb.ImportCluesRequest, resp *pb.Empty) error {
	log.Info("ImportClues", "开始处理导入线索请求", map[string]interface{}{
		"user_id":  req.UserId,
		"group_id": req.GroupId,
	})

	var clueIgnoreCount int

	// 参数验证
	if len(req.Data) == 0 && req.File == "" {
		return errors.New("上传的线索内容为空，参数错误!")
	}

	// 设置状态
	// 使用 -1 作为特殊值表示"未设置"，允许用户明确传递 0
	setStatus := int(clues.CLUE_PASS_STATUS) // 默认值：1（通过状态）
	if req.SetStatus != -1 {                 // -1 表示未设置，使用默认值
		setStatus = int(req.SetStatus)
		log.Infof("ImportClues - 用户设置状态: %d", setStatus)
	} else {
		log.Infof("ImportClues - 使用默认状态: %d", setStatus)
	}

	newCompanyName := []string{}
	ClueInputName := req.CompanyName

	// 根据PHP逻辑：当IsTask为1时，需要把传进来的企业名称赋值给每个线索的企业名称
	needInsertCompanyName := req.IsTask == 1
	clueCompanyName := req.CompanyName

	// 收集新创建的线索ID，用于自动扩充
	var newClueIds []uint64

	// 收集已存在的线索ID，用于自动扩充
	var existingClueIds []uint64

	// 收集需要异步查询ICP的线索信息
	var asyncICPTasks []AsyncICPTask

	// 处理上传的文件
	if req.File != "" {
		// 解析上传文件路径
		fileInfo, err := UploadDecrypt(req.File)
		if err != nil {
			log.Error("ImportClues", "解析上传文件路径失败", err)
			return errors.New("解析上传文件失败: " + err.Error())
		}
		// 获取文件路径
		filePath := fileInfo.Url

		// 检查文件是否存在
		filePath = filepath.Join(storage.GetRootPath(), filePath)
		if b := utils.FileIsExist(filePath); !b {
			fmt.Println(filePath)
			return errors.New("未找到上传文件")
		}
		// 根据文件类型处理
		fileExt := strings.ToLower(filepath.Ext(filePath))
		if fileExt != ".xlsx" && fileExt != ".xls" {
			return errors.New("仅支持Excel文件格式(.xlsx或.xls)")
		}

		// 打开Excel文件
		xlFile, err := excelize.OpenFile(filePath)
		if err != nil {
			log.Error("ImportClues", "打开Excel文件失败", err)
			return errors.New("打开Excel文件失败: " + err.Error())
		}

		// 获取第一个Sheet的所有行
		sheetName := xlFile.GetSheetList()[0]
		rows, err := xlFile.GetRows(sheetName)
		if err != nil {
			log.Error("ImportClues", "读取Excel内容失败", err)
			return errors.New("读取Excel内容失败: " + err.Error())
		}

		// 检查是否有数据
		if len(rows) <= 1 {
			return errors.New("Excel文件内容为空")
		}

		// 跳过表头，从第二行开始处理数据
		var errors []string
		index := 2 // 对应PHP中的行号计数

		// 处理数据行，按照PHP逻辑使用固定列索引
		for i := 1; i < len(rows); i++ {
			row := rows[i]

			log.Info("ImportClues", "readUploadContent", map[string]interface{}{
				"row": row,
			})

			// 处理根域 (row[1])
			if len(row) > 1 && strings.TrimSpace(row[1]) != "" {
				content := strings.TrimSpace(row[1])
				// 获取顶级域名
				topDomain := utils.GetTopDomain(content)
				if topDomain == "" || !utils.IsDomain(topDomain) {
					errors = append(errors, fmt.Sprintf("上传的线索文件中第%d行里面根域信息填写错误,请检查!", index))
				} else {
					// 创建域名线索，不进行同步ICP查询
					clue := &clues.Clue{
						UserId:          req.UserId,
						GroupId:         req.GroupId,
						Type:            int(clues.TYPE_DOMAIN),
						Content:         topDomain,
						Status:          setStatus,
						IsDeleted:       clues.NOT_DELETE,
						Source:          clues.SOURCE_MANUAL_ADD,
						ClueCompanyName: "", // 先设为空，异步查询后更新
					}

					// 根据PHP逻辑：取不到企业名称，就默认取测绘任务的企业名称
					if clue.ClueCompanyName == "" && needInsertCompanyName {
						clue.ClueCompanyName = clueCompanyName
					}

					// 检查是否为供应链线索，如果是则跳过
					var supplyChainCount int64
					err = mysql.GetDbClient().Model(&clues.Clue{}).Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ? AND is_supply_chain = ?",
						req.UserId, req.GroupId, topDomain, clues.NOT_DELETE, clues.SUPPLY_CHAIN_YES).Count(&supplyChainCount).Error
					if err != nil {
						log.Error("ImportClues", "检查供应链线索失败", err)
					}
					if supplyChainCount > 0 {
						continue // 跳过供应链线索
					}

					// 检查线索是否已存在
					var existClue clues.Clue
					err = mysql.GetDbClient().Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ?",
						req.UserId, req.GroupId, topDomain, clues.NOT_DELETE).First(&existClue).Error

					if err == nil {
						// 线索已存在，更新状态
						err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", existClue.Id).
							Updates(map[string]interface{}{
								"status": setStatus,
							}).Error
						if err != nil {
							log.Error("ImportClues", "更新已存在线索状态失败", err)
						}
						clueIgnoreCount++
						// 收集已存在的线索ID，用于自动扩充
						existingClueIds = append(existingClueIds, existClue.Id)
						// 添加异步ICP查询任务
						asyncICPTasks = append(asyncICPTasks, AsyncICPTask{
							ClueID:     existClue.Id,
							QueryType:  "domain",
							QueryValue: topDomain,
							UserId:     req.UserId,
							GroupId:    req.GroupId,
						})
					} else {
						// 创建新线索
						err = clues.NewCluer().Create(clue)
						if err != nil {
							log.Error("ImportClues", "创建线索失败", err)
						} else {
							// 收集新创建的线索ID
							newClueIds = append(newClueIds, clue.Id)
							// 添加异步ICP查询任务
							asyncICPTasks = append(asyncICPTasks, AsyncICPTask{
								ClueID:     clue.Id,
								QueryType:  "domain",
								QueryValue: topDomain,
								UserId:     req.UserId,
								GroupId:    req.GroupId,
							})
						}
					}
				}
			}

			// 处理证书 (row[2])
			if len(row) > 2 && strings.TrimSpace(row[2]) != "" {
				content := strings.TrimSpace(row[2])
				// 验证证书格式
				if !utils.CheckCert(content) {
					errors = append(errors, fmt.Sprintf("上传的线索文件中第%d行里面证书信息填写错误,请检查!", index))
				} else {
					clue := &clues.Clue{
						UserId:          req.UserId,
						GroupId:         req.GroupId,
						Type:            int(clues.TYPE_CERT),
						Content:         content,
						Status:          setStatus,
						IsDeleted:       clues.NOT_DELETE,
						Source:          clues.SOURCE_MANUAL_ADD,
						ClueCompanyName: ClueInputName,
					}

					// 根据PHP逻辑：取不到企业名称，就默认取测绘任务的企业名称
					if clue.ClueCompanyName == "" && needInsertCompanyName {
						clue.ClueCompanyName = clueCompanyName
					}

					// 检查是否为供应链线索，如果是则跳过
					var supplyChainCount int64
					err := mysql.GetDbClient().Model(&clues.Clue{}).Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ? AND is_supply_chain = ?",
						req.UserId, req.GroupId, content, clues.NOT_DELETE, clues.SUPPLY_CHAIN_YES).Count(&supplyChainCount).Error
					if err != nil {
						log.Error("ImportClues", "检查供应链线索失败", err)
					}
					if supplyChainCount > 0 {
						continue // 跳过供应链线索
					}

					// 检查线索是否已存在
					var existClue clues.Clue
					err = mysql.GetDbClient().Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ?",
						req.UserId, req.GroupId, content, clues.NOT_DELETE).First(&existClue).Error

					if err == nil {
						// 线索已存在，更新状态
						err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", existClue.Id).
							Updates(map[string]interface{}{
								"status":            setStatus,
								"clue_company_name": ClueInputName,
							}).Error
						if err != nil {
							log.Error("ImportClues", "更新已存在线索状态失败", err)
						}
						clueIgnoreCount++
						// 收集已存在的线索ID，用于自动扩充
						existingClueIds = append(existingClueIds, existClue.Id)
					} else {
						// 创建新线索
						err = clues.NewCluer().Create(clue)
						if err != nil {
							log.Error("ImportClues", "创建线索失败", err)
						} else {
							// 收集新创建的线索ID
							newClueIds = append(newClueIds, clue.Id)
						}
					}
				}
			}

			// 处理ICP (row[3])
			if len(row) > 3 && strings.TrimSpace(row[3]) != "" {
				content := strings.TrimSpace(row[3])
				// 验证ICP格式
				if utils.GetIcpNumber(content) == "" {
					errors = append(errors, fmt.Sprintf("上传的线索文件中第%d行里面ICP信息填写错误,请检查!", index))
				} else {
					// 去除空格
					content = strings.ReplaceAll(content, " ", "")

					clue := &clues.Clue{
						UserId:          req.UserId,
						GroupId:         req.GroupId,
						Type:            int(clues.TYPE_ICP),
						Content:         content,
						Status:          setStatus,
						IsDeleted:       clues.NOT_DELETE,
						Source:          clues.SOURCE_MANUAL_ADD,
						ClueCompanyName: "", // 先设为空，异步查询后更新
					}

					// 根据PHP逻辑：取不到企业名称，就默认取测绘任务的企业名称
					if clue.ClueCompanyName == "" && needInsertCompanyName {
						clue.ClueCompanyName = clueCompanyName
					}

					// 检查是否为供应链线索，如果是则跳过
					var supplyChainCount int64
					err := mysql.GetDbClient().Model(&clues.Clue{}).Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ? AND is_supply_chain = ?",
						req.UserId, req.GroupId, content, clues.NOT_DELETE, clues.SUPPLY_CHAIN_YES).Count(&supplyChainCount).Error
					if err != nil {
						log.Error("ImportClues", "检查供应链线索失败", err)
					}
					if supplyChainCount > 0 {
						continue // 跳过供应链线索
					}

					// 检查线索是否已存在
					var existClue clues.Clue
					err = mysql.GetDbClient().Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ?",
						req.UserId, req.GroupId, content, clues.NOT_DELETE).First(&existClue).Error

					if err == nil {
						// 线索已存在，更新状态
						err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", existClue.Id).
							Updates(map[string]interface{}{
								"status": setStatus,
							}).Error
						if err != nil {
							log.Error("ImportClues", "更新已存在线索状态失败", err)
						}
						clueIgnoreCount++
						// 收集已存在的线索ID，用于自动扩充
						existingClueIds = append(existingClueIds, existClue.Id)
						// 添加异步ICP查询任务
						asyncICPTasks = append(asyncICPTasks, AsyncICPTask{
							ClueID:     existClue.Id,
							QueryType:  "icp",
							QueryValue: content,
							UserId:     req.UserId,
							GroupId:    req.GroupId,
						})
					} else {
						// 创建新线索
						err = clues.NewCluer().Create(clue)
						if err != nil {
							log.Error("ImportClues", "创建线索失败", err)
						} else {
							// 收集新创建的线索ID
							newClueIds = append(newClueIds, clue.Id)
							// 添加异步ICP查询任务
							asyncICPTasks = append(asyncICPTasks, AsyncICPTask{
								ClueID:     clue.Id,
								QueryType:  "icp",
								QueryValue: content,
								UserId:     req.UserId,
								GroupId:    req.GroupId,
							})
						}
					}
				}
			}

			// 处理ICON/LOGO (row[4])
			if len(row) > 4 && strings.TrimSpace(row[4]) != "" {
				content := strings.TrimSpace(row[4])

				// 下载图片并计算hash值
				iconPath, iconHash := downloadAndProcessIcon(content, req.UserId)
				if iconPath == "" || iconHash == 0 {
					errors = append(errors, fmt.Sprintf("上传的线索文件中第%d行里面LOGO信息填写错误!", index))
				} else {
					clue := &clues.Clue{
						UserId:          req.UserId,
						GroupId:         req.GroupId,
						Type:            int(clues.TYPE_LOGO),
						Content:         iconPath,
						Hash:            int(iconHash),
						Status:          setStatus,
						IsDeleted:       clues.NOT_DELETE,
						Source:          clues.SOURCE_MANUAL_ADD,
						ClueCompanyName: ClueInputName,
					}

					// 根据PHP逻辑：取不到企业名称，就默认取测绘任务的企业名称
					if clue.ClueCompanyName == "" && needInsertCompanyName {
						clue.ClueCompanyName = clueCompanyName
					}

					// 检查是否为供应链线索，如果是则跳过（ICON类型根据hash判断）
					var supplyChainCount int64
					err := mysql.GetDbClient().Model(&clues.Clue{}).Where("user_id = ? AND group_id = ? AND hash = ? AND type = ? AND is_deleted = ? AND is_supply_chain = ?",
						req.UserId, req.GroupId, int(iconHash), clues.TYPE_LOGO, clues.NOT_DELETE, clues.SUPPLY_CHAIN_YES).Count(&supplyChainCount).Error
					if err != nil {
						log.Error("ImportClues", "检查供应链线索失败", err)
					}
					if supplyChainCount > 0 {
						continue // 跳过供应链线索
					}

					// 检查线索是否已存在（ICON类型根据hash去重）
					var existClue clues.Clue
					err = mysql.GetDbClient().Where("user_id = ? AND group_id = ? AND hash = ? AND type = ? AND is_deleted = ?",
						req.UserId, req.GroupId, int(iconHash), clues.TYPE_LOGO, clues.NOT_DELETE).First(&existClue).Error

					if err == nil {
						// 线索已存在，更新状态
						err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", existClue.Id).
							Updates(map[string]interface{}{
								"status":            setStatus,
								"clue_company_name": ClueInputName,
								"hash":              int(iconHash),
							}).Error
						if err != nil {
							log.Error("ImportClues", "更新已存在线索状态失败", err)
						}
						clueIgnoreCount++
						// 收集已存在的线索ID，用于自动扩充
						existingClueIds = append(existingClueIds, existClue.Id)
					} else {
						// 创建新线索
						err = clues.NewCluer().Create(clue)
						if err != nil {
							log.Error("ImportClues", "创建线索失败", err)
						} else {
							// 收集新创建的线索ID
							newClueIds = append(newClueIds, clue.Id)
						}
					}
				}
			}

			// 处理关键词 (row[5])
			if len(row) > 5 && strings.TrimSpace(row[5]) != "" {
				content := strings.TrimSpace(row[5])

				clue := &clues.Clue{
					UserId:          req.UserId,
					GroupId:         req.GroupId,
					Type:            int(clues.TYPE_KEYWORD),
					Content:         content,
					Status:          setStatus,
					IsDeleted:       clues.NOT_DELETE,
					Source:          clues.SOURCE_MANUAL_ADD,
					ClueCompanyName: ClueInputName,
				}

				// 根据PHP逻辑：取不到企业名称，就默认取测绘任务的企业名称
				if clue.ClueCompanyName == "" && needInsertCompanyName {
					clue.ClueCompanyName = clueCompanyName
				}

				// 检查是否为供应链线索，如果是则跳过
				var supplyChainCount int64
				err := mysql.GetDbClient().Model(&clues.Clue{}).Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ? AND is_supply_chain = ?",
					req.UserId, req.GroupId, content, clues.NOT_DELETE, clues.SUPPLY_CHAIN_YES).Count(&supplyChainCount).Error
				if err != nil {
					log.Error("ImportClues", "检查供应链线索失败", err)
				}
				if supplyChainCount > 0 {
					continue // 跳过供应链线索
				}

				// 检查线索是否已存在
				var existClue clues.Clue
				err = mysql.GetDbClient().Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ?",
					req.UserId, req.GroupId, content, clues.NOT_DELETE).First(&existClue).Error

				if err == nil {
					// 线索已存在，更新状态
					err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", existClue.Id).
						Updates(map[string]interface{}{
							"status":            setStatus,
							"clue_company_name": ClueInputName,
						}).Error
					if err != nil {
						log.Error("ImportClues", "更新已存在线索状态失败", err)
					}
					clueIgnoreCount++
				} else {
					// 创建新线索
					err = clues.NewCluer().Create(clue)
					if err != nil {
						log.Error("ImportClues", "创建线索失败", err)
					} else {
						// 收集新创建的线索ID
						newClueIds = append(newClueIds, clue.Id)
					}
				}
			}

			// 处理IP (row[6])
			if len(row) > 6 && strings.TrimSpace(row[6]) != "" {
				content := strings.TrimSpace(row[6])
				// 验证IP格式
				ipParts := strings.Split(content, "/")
				if !utils.IsIP(ipParts[0]) {
					errors = append(errors, fmt.Sprintf("上传的线索文件中第%d行里面IP类型线索格式填写错误,IP类型线索格式只支持单个ip或者cidr格式的ip!", index))
				} else {
					clue := &clues.Clue{
						UserId:          req.UserId,
						GroupId:         req.GroupId,
						Type:            int(clues.TYPE_IP),
						Content:         content,
						Status:          setStatus,
						IsDeleted:       clues.NOT_DELETE,
						Source:          clues.SOURCE_MANUAL_ADD,
						ClueCompanyName: ClueInputName,
					}

					// 根据PHP逻辑：取不到企业名称，就默认取测绘任务的企业名称
					if clue.ClueCompanyName == "" && needInsertCompanyName {
						clue.ClueCompanyName = clueCompanyName
					}

					// 检查是否为供应链线索，如果是则跳过
					var supplyChainCount int64
					err := mysql.GetDbClient().Model(&clues.Clue{}).Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ? AND is_supply_chain = ?",
						req.UserId, req.GroupId, content, clues.NOT_DELETE, clues.SUPPLY_CHAIN_YES).Count(&supplyChainCount).Error
					if err != nil {
						log.Error("ImportClues", "检查供应链线索失败", err)
					}
					if supplyChainCount > 0 {
						continue // 跳过供应链线索
					}

					// 检查线索是否已存在
					var existClue clues.Clue
					err = mysql.GetDbClient().Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ?",
						req.UserId, req.GroupId, content, clues.NOT_DELETE).First(&existClue).Error

					if err == nil {
						// 线索已存在，更新状态
						err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", existClue.Id).
							Updates(map[string]interface{}{
								"status":            setStatus,
								"clue_company_name": ClueInputName,
							}).Error
						if err != nil {
							log.Error("ImportClues", "更新已存在线索状态失败", err)
						}
						clueIgnoreCount++
						// 收集已存在的线索ID，用于自动扩充
						existingClueIds = append(existingClueIds, existClue.Id)
					} else {
						// 创建新线索
						err = clues.NewCluer().Create(clue)
						if err != nil {
							log.Error("ImportClues", "创建线索失败", err)
						} else {
							// 收集新创建的线索ID
							newClueIds = append(newClueIds, clue.Id)
						}
					}
				}
			}

			// 处理子域名 (row[7])
			if len(row) > 7 && strings.TrimSpace(row[7]) != "" {
				content := strings.TrimSpace(row[7])
				// 去掉协议前缀
				content = strings.TrimPrefix(content, "https://")
				content = strings.TrimPrefix(content, "http://")

				// 验证域名格式
				topDomain := utils.GetTopDomain(content)
				if topDomain == "" || !utils.IsDomain(content) {
					errors = append(errors, fmt.Sprintf("上传的线索文件中第%d行里面子域名信息填写错误!", index))
				} else {
					clue := &clues.Clue{
						UserId:          req.UserId,
						GroupId:         req.GroupId,
						Type:            int(clues.TYPE_SUBDOMAIN),
						Content:         content,
						Status:          setStatus,
						IsDeleted:       clues.NOT_DELETE,
						Source:          clues.SOURCE_MANUAL_ADD,
						ClueCompanyName: "", // 先设为空，异步查询后更新
					}

					// 根据PHP逻辑：取不到企业名称，就默认取测绘任务的企业名称
					if clue.ClueCompanyName == "" && needInsertCompanyName {
						clue.ClueCompanyName = clueCompanyName
					}

					// 检查是否为供应链线索，如果是则跳过
					var supplyChainCount int64
					err := mysql.GetDbClient().Model(&clues.Clue{}).Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ? AND is_supply_chain = ?",
						req.UserId, req.GroupId, content, clues.NOT_DELETE, clues.SUPPLY_CHAIN_YES).Count(&supplyChainCount).Error
					if err != nil {
						log.Error("ImportClues", "检查供应链线索失败", err)
					}
					if supplyChainCount > 0 {
						continue // 跳过供应链线索
					}

					// 检查线索是否已存在
					var existClue clues.Clue
					err = mysql.GetDbClient().Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ?",
						req.UserId, req.GroupId, content, clues.NOT_DELETE).First(&existClue).Error

					if err == nil {
						// 线索已存在，更新状态
						err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", existClue.Id).
							Updates(map[string]interface{}{
								"status": setStatus,
							}).Error
						if err != nil {
							log.Error("ImportClues", "更新已存在线索状态失败", err)
						}
						clueIgnoreCount++
						// 收集已存在的线索ID，用于自动扩充
						existingClueIds = append(existingClueIds, existClue.Id)
						// 添加异步ICP查询任务
						asyncICPTasks = append(asyncICPTasks, AsyncICPTask{
							ClueID:      existClue.Id,
							QueryType:   "subdomain",
							QueryValue:  content,
							UserId:      req.UserId,
							GroupId:     req.GroupId,
							IsSubdomain: true,
						})
					} else {
						// 创建新线索
						err = clues.NewCluer().Create(clue)
						if err != nil {
							log.Error("ImportClues", "创建线索失败", err)
						} else {
							// 收集新创建的线索ID
							newClueIds = append(newClueIds, clue.Id)
							// 添加异步ICP查询任务
							asyncICPTasks = append(asyncICPTasks, AsyncICPTask{
								ClueID:      clue.Id,
								QueryType:   "subdomain",
								QueryValue:  content,
								UserId:      req.UserId,
								GroupId:     req.GroupId,
								IsSubdomain: true,
							})
						}
					}
				}
			}

			index++
		}

		// 记录错误信息
		if len(errors) > 0 {
			log.Warn("ImportClues", "线索解析失败", errors)
			// 可以选择返回错误或者继续处理
			// return errors.New(strings.Join(errors, "; "))
		}

		// 文件处理完成，线索已直接创建
		log.Info("ImportClues", "文件处理完成", map[string]interface{}{
			"created_clues_count": len(newClueIds),
			"ignored_clues_count": clueIgnoreCount,
		})
	}

	// 处理直接传入的线索数据（非文件导入）
	if req.File == "" && len(req.Data) > 0 {
		for _, clueItem := range req.Data {
			clueCompanyName := clueItem.ClueCompanyName

			for _, content := range clueItem.Content {
				// 内容长度检查
				if utf8.RuneCountInString(content) > 200 && clueItem.Type != clues.TYPE_LOGO {
					return errors.New("有线索长度超过200个字符，请修改后再提交!")
				}

				// 创建线索记录
				clue := &clues.Clue{
					UserId:          req.UserId,
					GroupId:         req.GroupId,
					Type:            int(clueItem.Type),
					Content:         content,
					Status:          setStatus,
					IsDeleted:       clues.NOT_DELETE,
					Source:          clues.SOURCE_MANUAL_ADD,
					ClueCompanyName: clueCompanyName,
				}

				// 根据PHP逻辑：取不到企业名称，就默认取测绘任务的企业名称
				if clue.ClueCompanyName == "" && needInsertCompanyName {
					clue.ClueCompanyName = req.CompanyName
				}

				// 处理ICON类型线索
				if clueItem.Type == clues.TYPE_LOGO {
					// 解析下载URL获取实际文件路径
					icoUrl := utils.ParseDownloadUrl(content, true)
					log.Info("ImportClues", "处理ICON线索", map[string]interface{}{
						"original_content": content,
						"parsed_url":       icoUrl,
					})

					if icoUrl != "" {
						// 读取图片文件数据
						imgData, err := os.ReadFile(icoUrl)
						if err != nil {
							log.Error("ImportClues", "读取ICON文件失败", err)
							continue
						}

						// 计算hash值
						hashStr := utils.Mmh3Hash32(utils.Mmh3Base64Encode(string(imgData)))
						hash, err := strconv.Atoi(hashStr)
						if err != nil {
							log.Error("ImportClues", "转换hash值失败", err)
							continue
						}
						clue.Hash = hash

						// 计算MD5作为comment
						clue.Comment = utils.Md5bHash(imgData, false)

						// 转换content为app/public格式的路径
						// 处理各种可能的路径格式，统一转换为app/public/xxx格式
						var finalPath string

						// 移除可能的前缀路径
						cleanPath := icoUrl
						log.Info("ImportClues", "路径处理步骤", map[string]interface{}{
							"step1_original": cleanPath,
						})

						cleanPath = strings.TrimPrefix(cleanPath, "/data/foradar/")
						log.Info("ImportClues", "路径处理步骤", map[string]interface{}{
							"step2_after_data_foradar": cleanPath,
						})

						cleanPath = strings.TrimPrefix(cleanPath, "/data/storage/")
						log.Info("ImportClues", "路径处理步骤", map[string]interface{}{
							"step3_after_data_storage": cleanPath,
						})

						cleanPath = strings.TrimPrefix(cleanPath, "/data/")
						cleanPath = strings.TrimPrefix(cleanPath, "storage/")
						cleanPath = strings.TrimPrefix(cleanPath, "app/public/")
						cleanPath = strings.TrimPrefix(cleanPath, "/")

						log.Info("ImportClues", "路径处理步骤", map[string]interface{}{
							"step4_final_clean": cleanPath,
						})

						// 构建最终的app/public路径
						finalPath = "app/public/" + cleanPath
						clue.Content = finalPath

						log.Info("ImportClues", "路径处理步骤", map[string]interface{}{
							"step5_final_path": finalPath,
						})

						log.Info("ImportClues", "ICON处理完成", map[string]interface{}{
							"hash":          hash,
							"comment":       clue.Comment,
							"final_content": clue.Content,
						})
					} else {
						log.Error("ImportClues", "解析ICON URL失败", map[string]interface{}{
							"content": content,
						})
						continue
					}
				}

				// 处理ICP类型线索
				if clueItem.Type == clues.TYPE_ICP {
					// 验证ICP格式
					if utils.GetIcpNumber(content) == "" {
						return errors.New("ICP格式非法，请重新填写")
					}

					// 如果已有公司名称，添加到新公司名称列表
					if clueCompanyName != "" && !containsString(newCompanyName, clueCompanyName) {
						newCompanyName = append(newCompanyName, clueCompanyName)
					}
					// 不进行同步ICP查询，设置为空，后续异步查询
					clue.ClueCompanyName = clueCompanyName
				}

				// 处理IP类型线索
				if clueItem.Type == clues.TYPE_IP {
					// 验证IP格式
					ipParts := strings.Split(content, "/")
					if !utils.IsIP(ipParts[0]) {
						return errors.New("线索ip格式不符合要求,不合格ip为：" + content)
					}
					// IP类型线索：如果没有企业名称且是测绘任务，使用测绘任务的企业名称
					if clue.ClueCompanyName == "" && needInsertCompanyName {
						clue.ClueCompanyName = req.CompanyName
					} else if clue.ClueCompanyName == "" {
						clue.ClueCompanyName = ClueInputName
					}
				}

				// 处理子域名类型线索
				if clueItem.Type == clues.TYPE_SUBDOMAIN {
					// 去掉子域名里面的协议
					content = strings.TrimPrefix(content, "https://")
					content = strings.TrimPrefix(content, "http://")
					clue.Content = content

					// 检查是否是主域名
					topDomain := utils.GetTopDomain(content)
					if topDomain == content {
						return errors.New("子域名类型线索下不可以填写主域名线索数据，当前域名为主域名：" + content)
					}

					// 不进行同步ICP查询，根据是否是测绘任务设置企业名称
					if clue.ClueCompanyName == "" && needInsertCompanyName {
						clue.ClueCompanyName = req.CompanyName
					} else {
						clue.ClueCompanyName = clueCompanyName
					}
				}

				// 处理关键词类型线索
				if clueItem.Type == clues.TYPE_KEYWORD {
					// 关键词类型线索直接使用传入的公司名称
				}

				// 处理证书类型线索
				if clueItem.Type == clues.TYPE_CERT {
					// 验证证书线索格式
					if !utils.CheckCert(content) {
						return errors.New("证书线索格式错误，请修改后再提交!")
					}
					// 证书类型线索直接使用传入的公司名称
				}

				// 处理域名类型线索
				if clueItem.Type == clues.TYPE_DOMAIN {
					// 获取顶级域名
					content = utils.GetTopDomain(content)
					if content == "" {
						return errors.New("根域格式非法，请重新填写")
					}

					// 检查域名格式
					if !utils.IsDomain(content) {
						return errors.New("根域格式非法，请重新填写")
					}

					// 不进行同步ICP查询，根据是否是测绘任务设置企业名称
					var companyName string
					if clueCompanyName != "" {
						companyName = clueCompanyName
					} else if needInsertCompanyName {
						companyName = req.CompanyName
					}

					// 处理国际域名
					if checkIdnDomain(content) {
						// 获取IDN域名
						punycodeDomain := getIdnDomain(content)
						if punycodeDomain != "" {
							// 创建一个新的线索
							clue := &clues.Clue{
								UserId:         req.UserId,
								GroupId:        req.GroupId,
								Type:           int(clueItem.Type),
								Content:        content,
								PunycodeDomain: punycodeDomain,
								Status:         setStatus,
								IsDeleted:      clues.NOT_DELETE,
								Source:         clues.SOURCE_MANUAL_ADD,
							}

							if clueCompanyName != "" {
								clue.ClueCompanyName = clueCompanyName
							} else if needInsertCompanyName {
								clue.ClueCompanyName = req.CompanyName
							}

							// 检查线索是否已存在
							var existClue clues.Clue
							err := mysql.GetDbClient().Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ?",
								req.UserId, req.GroupId, content, clues.NOT_DELETE).First(&existClue).Error

							if err == nil {
								// 线索已存在，更新状态
								err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", existClue.Id).
									Updates(map[string]interface{}{
										"status":            setStatus,
										"clue_company_name": clueCompanyName,
									}).Error
								if err != nil {
									log.Error("ImportClues", "更新已存在线索状态失败", err)
								}
								clueIgnoreCount++
								// 收集已存在的线索ID，用于自动扩充
								existingClueIds = append(existingClueIds, existClue.Id)
							} else {
								// 创建新线索
								err = mysql.GetDbClient().Create(clue).Error
								if err != nil {
									log.Error("ImportClues", "创建线索失败", err)
									continue
								}
								// 收集新创建的线索ID
								newClueIds = append(newClueIds, clue.Id)
							}
						}
					}

					// 公司名称处理
					if companyName != "" && !containsString(newCompanyName, companyName) {
						newCompanyName = append(newCompanyName, companyName)
					}
				}

				// 检查是否为供应链线索，如果是则跳过
				var supplyChainCount int64
				err := mysql.GetDbClient().Model(&clues.Clue{}).Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ? AND is_supply_chain = ?",
					req.UserId, req.GroupId, content, clues.NOT_DELETE, clues.SUPPLY_CHAIN_YES).Count(&supplyChainCount).Error
				if err != nil {
					log.Error("ImportClues", "检查供应链线索失败", err)
				}
				if supplyChainCount > 0 {
					continue // 跳过供应链线索
				}

				// 检查线索是否已存在
				var existClue clues.Clue
				err = mysql.GetDbClient().Where("user_id = ? AND group_id = ? AND content = ? AND is_deleted = ?",
					req.UserId, req.GroupId, content, clues.NOT_DELETE).First(&existClue).Error

				if err == nil {
					// 线索已存在，更新状态
					err = mysql.GetDbClient().Model(&clues.Clue{}).Where("id = ?", existClue.Id).
						Updates(map[string]interface{}{
							"status":            setStatus,
							"clue_company_name": clueCompanyName,
						}).Error
					if err != nil {
						log.Error("ImportClues", "更新已存在线索状态失败", err)
					}
					clueIgnoreCount++
					// 收集已存在的线索ID，用于自动扩充
					existingClueIds = append(existingClueIds, existClue.Id)

					// 为已存在的线索添加异步ICP查询任务（如果需要）
					if shouldAddICPTask(int(clueItem.Type)) {
						asyncICPTasks = append(asyncICPTasks, AsyncICPTask{
							ClueID:      existClue.Id,
							QueryType:   getQueryType(int(clueItem.Type)),
							QueryValue:  content,
							UserId:      req.UserId,
							GroupId:     req.GroupId,
							IsSubdomain: clueItem.Type == clues.TYPE_SUBDOMAIN,
						})
					}
				} else {
					if clue.ClueCompanyName == "" {
						if needInsertCompanyName {
							clue.ClueCompanyName = req.CompanyName
						} else {
							clue.ClueCompanyName = clueCompanyName
						}
					}
					// 创建新线索
					err = clues.NewCluer().Create(clue)
					if err != nil {
						log.Error("ImportClues", "创建线索失败", err)
						continue
					}
					// 收集新创建的线索ID
					newClueIds = append(newClueIds, clue.Id)

					// 为新创建的线索添加异步ICP查询任务（如果需要）
					if shouldAddICPTask(int(clueItem.Type)) {
						asyncICPTasks = append(asyncICPTasks, AsyncICPTask{
							ClueID:      clue.Id,
							QueryType:   getQueryType(int(clueItem.Type)),
							QueryValue:  content,
							UserId:      req.UserId,
							GroupId:     req.GroupId,
							IsSubdomain: clueItem.Type == clues.TYPE_SUBDOMAIN,
						})
					}
				}
			}
		}
	}

	// 检查是否有有效的线索数据
	if len(newClueIds) == 0 && req.File == "" && len(req.Data) == 0 {
		return errors.New("未创建任何有效线索数据")
	}

	// 判断测绘任务是否在生成总表时进行操作-线索数量更新生成
	var detectInfo detect_assets_tasks.DetectAssetsTask
	err := mysql.GetDbClient().Where("group_id = ? AND step_detail = ?",
		req.GroupId, detect_assets_tasks.StepTwoFormAll).First(&detectInfo).Error

	if err == nil {
		// 更新线索数量
		var cluesCount int64
		mysql.GetDbClient().Model(&clues.Clue{}).Where("is_deleted = ? AND group_id = ? AND status = ?",
			clues.NOT_DELETE, req.GroupId, clues.CLUE_PASS_STATUS).Count(&cluesCount)

		err = mysql.GetDbClient().Model(&detect_assets_tasks.DetectAssetsTask{}).Where("id = ?", detectInfo.ID).
			Update("clues_count", cluesCount).Error

		if err != nil {
			log.Error("ImportClues", "更新测绘任务线索数量失败", err)
		}
	}

	// 记录操作日志
	content := fmt.Sprintf("导入线索，忽略重复%d条", clueIgnoreCount)

	// 使用异步任务记录日志
	var companyID uint64 = 0
	if req.OperatorId > 0 {
		companyID = uint64(req.OperatorId)
	}

	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		req.UserId,
		content,
		req.ClientIp,
		companyID,
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))

	if err != nil {
		log.Error("ImportClues", "创建操作日志失败", err)
	}

	// 处理异步ICP查询任务
	if len(asyncICPTasks) > 0 {
		log.Info("ImportClues", "开始处理异步ICP查询任务", map[string]interface{}{
			"task_count": len(asyncICPTasks),
		})

		// 调用异步任务处理ICP查询
		for _, task := range asyncICPTasks {
			payload := &asyncq.ICPQueryJobPayload{
				ClueID:      task.ClueID,
				QueryType:   task.QueryType,
				QueryValue:  task.QueryValue,
				UserId:      task.UserId,
				GroupId:     task.GroupId,
				IsSubdomain: task.IsSubdomain,
			}

			if err := asyncq.Enqueue(ctx, asyncq.ICPQueryJob, payload); err != nil {
				log.Error("ImportClues", "调用异步ICP查询任务失败", map[string]interface{}{
					"clue_id": task.ClueID,
					"error":   err,
				})
			} else {
				log.Info("ImportClues", "成功调用异步ICP查询任务", map[string]interface{}{
					"clue_id":     task.ClueID,
					"query_type":  task.QueryType,
					"query_value": task.QueryValue,
				})
			}
		}
	}

	// 检查是否有域名类型的线索，如果有则调用域名同步任务
	hasDomainClue := checkHasDomainClue(ctx, req.UserId, req.GroupId, newClueIds, existingClueIds)
	if hasDomainClue {
		log.Info("ImportClues", "检测到域名类型线索，准备调用域名同步任务", map[string]interface{}{
			"user_id":  req.UserId,
			"group_id": req.GroupId,
		})

		// 检查是否为测试用例，测试用例跳过域名同步

		if cfg.ExecGolangJob() {
			err := asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
				req.UserId,                     // user_id
				nil,                            // task_id (null)
				domain_assets.ASSETS_RECOMMEND, // from (DomainAssets::ASSETS_RECOMMEND = 2)
				req.GroupId,                    // groupId
				nil,                            // domain_task_id (null)
				nil,                            // import_domains (null)
				nil,                            // flag (null)
				nil,                            // detect_task_id (null)
				nil,                            // organization_discover_task_id (null)
			)
			if err != nil {
				log.Error("ImportClues", "调用域名同步任务失败", err)
			} else {
				log.Info("ImportClues", "成功调用域名同步任务", map[string]interface{}{
					"user_id":  req.UserId,
					"group_id": req.GroupId,
					"from":     domain_assets.ASSETS_RECOMMEND,
				})
			}
		} else {
			err := asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
				req.UserId,                     // user_id
				nil,                            // task_id (null)
				domain_assets.ASSETS_RECOMMEND, // from (DomainAssets::ASSETS_RECOMMEND = 2)
				req.GroupId,                    // groupId
				nil,                            // domain_task_id (null)
				nil,                            // import_domains (null)
				nil,                            // flag (null)
				nil,                            // detect_task_id (null)
				nil,                            // organization_discover_task_id (null)
			)
			if err != nil {
				log.Error("ImportClues", "调用域名同步任务失败", err)
			} else {
				log.Info("ImportClues", "成功调用域名同步任务", map[string]interface{}{
					"user_id":  req.UserId,
					"group_id": req.GroupId,
					"from":     domain_assets.ASSETS_RECOMMEND,
				})
			}
		}
	}

	// 合并新线索和已存在线索的ID，用于自动扩充
	allClueIds := make([]uint64, 0, len(newClueIds)+len(existingClueIds))
	allClueIds = append(allClueIds, newClueIds...)
	allClueIds = append(allClueIds, existingClueIds...)

	// 线索自动扩充
	log.Info("ImportClues", "线索自动扩充检查", map[string]interface{}{
		"is_auto_expend":      req.IsAutoExpend,
		"group_id":            req.GroupId,
		"new_clue_count":      len(newClueIds),
		"existing_clue_count": len(existingClueIds),
		"total_clue_count":    len(allClueIds),
		"new_clue_ids":        newClueIds,
		"existing_clue_ids":   existingClueIds,
		"all_clue_ids":        allClueIds,
	})

	if cast.ToInt(req.IsAutoExpend) > 0 && len(allClueIds) > 0 {
		log.Info("ImportClues", "准备创建线索扩充任务", map[string]interface{}{
			"clue_count": len(allClueIds),
			"clue_ids":   allClueIds,
		})
		safeUserId := 0
		// 尝试从ctx中获取user_id信息,如果获取失败，则标记为1
		userID, err := metadata.GetInt64(ctx, "user_id")
		// 如果user_id不一致，则标记为1
		if req.UserId != uint64(userID) {
			safeUserId = int(userID)
		}
		// 创建线索任务
		clueTaskModel := clue_task.NewClueTasker()
		newTask := &clue_task.ClueTask{
			UserId:              req.UserId,
			GroupId:             req.GroupId,
			CompanyId:           companyID,
			SafeUserId:          uint64(safeUserId),
			Name:                fmt.Sprintf("线索扩充-%s", time.Now().Format("2006-01-02-15-04-05")),
			Status:              clue_task.DefaultStatus,
			Progress:            0,
			IsShow:              clue_task.ShowHidden, // 0表示显示
			DetectAssetsTasksId: 0,                    // 手动导入的线索扩充不关联测绘任务
		}

		// 如果当前用户是安服用户，设置安服用户ID
		// 这里简化处理，可以根据实际需求添加用户类型判断逻辑
		// newTask.SafeUserId = req.UserId

		err = clueTaskModel.Create(newTask)
		if err != nil {
			log.Error("ImportClues", "创建线索任务失败", err)
		} else {
			log.Info("ImportClues", "创建线索任务成功", map[string]interface{}{
				"task_id": newTask.Id,
			})

			// 创建线索任务ID关联
			db := mysql.GetDbClient()
			now := time.Now()
			for _, clueId := range allClueIds {
				clueTaskIds := struct {
					ClueTasksID uint64    `gorm:"column:clue_tasks_id"`
					ClueID      uint64    `gorm:"column:clue_id"`
					Status      int       `gorm:"column:status"`
					CreatedAt   time.Time `gorm:"column:created_at"`
				}{
					ClueTasksID: newTask.Id,
					ClueID:      clueId,
					Status:      0, // 默认状态
					CreatedAt:   now,
				}

				err = db.Table("clue_tasks_ids").Create(&clueTaskIds).Error
				if err != nil {
					log.Error("ImportClues", "创建线索任务ID关联失败", map[string]interface{}{
						"clue_id": clueId,
						"error":   err,
					})
				}
			}

			// 调用异步任务处理线索扩展
			if cfg.ExecGolangJob() {
				// 调用异步任务处理线索扩展
				payload := &asyncq.ExpandCluesJobPayload{
					UserId:           req.UserId,
					ClueTaskId:       newTask.Id,
					DetectTaskId:     0, // 手动导入的线索扩充不关联测绘任务
					IsIntellectMode:  0, // 默认非智能模式
					ClueType:         0, // 默认类型
					IsFakeClueExpend: 0, // 默认不是仿冒钓鱼的线索扩展流程
				}
				if err := asyncq.Enqueue(ctx, asyncq.ExpandCluesJob, payload); err != nil {
					log.Error("ImportClues", "调用异步任务失败", err)
				} else {
					log.Info("ImportClues", "成功调用异步线索扩展任务", map[string]interface{}{
						"task_id": newTask.Id,
					})
				}
			} else {
				// 调用 DispatchGolangJobExpendClue
				err = asyncq.DispatchGolangJobExpendClue.Dispatch(req.UserId, newTask.Id)
				if err != nil {
					log.Error("ImportClues", "调用 DispatchGolangJobExpendClue 失败", err)
				} else {
					log.Info("ImportClues", "成功调用 DispatchGolangJobExpendClue", map[string]interface{}{
						"task_id": newTask.Id,
					})
				}
			}
		}
	}
	return nil
}

// shouldAddICPTask 判断是否需要添加ICP查询任务
func shouldAddICPTask(clueType int) bool {
	return clueType == clues.TYPE_DOMAIN || clueType == clues.TYPE_ICP || clueType == clues.TYPE_SUBDOMAIN
}

// getQueryType 根据线索类型获取查询类型
func getQueryType(clueType int) string {
	switch clueType {
	case clues.TYPE_DOMAIN:
		return "domain"
	case clues.TYPE_ICP:
		return "icp"
	case clues.TYPE_SUBDOMAIN:
		return "subdomain"
	default:
		return ""
	}
}

// downloadAndProcessIcon 下载图片并处理，返回存储路径和hash值
func downloadAndProcessIcon(imageUrl string, userId uint64) (string, int64) {
	if imageUrl == "" {
		return "", 0
	}

	log.Info("downloadAndProcessIcon", "开始处理ICON", map[string]interface{}{
		"url":     imageUrl,
		"user_id": userId,
	})

	// 使用storage包中的SaveUrlMmh3函数下载图片并计算hash
	iconPath, iconHash := storage.SaveUrlMmh3(imageUrl, "", true)

	if iconPath == "" || iconHash == 0 {
		log.Error("downloadAndProcessIcon", "下载或处理图片失败", map[string]interface{}{
			"url":       imageUrl,
			"icon_path": iconPath,
			"icon_hash": iconHash,
		})
		return "", 0
	}

	log.Info("downloadAndProcessIcon", "ICON处理成功", map[string]interface{}{
		"url":       imageUrl,
		"icon_path": iconPath,
		"icon_hash": iconHash,
	})

	return iconPath, iconHash
}

// checkHasDomainClue 检查是否有域名类型的线索
func checkHasDomainClue(ctx context.Context, userId, groupId uint64, newClueIds, existingClueIds []uint64) bool {
	// 合并所有线索ID
	allClueIds := make([]uint64, 0, len(newClueIds)+len(existingClueIds))
	allClueIds = append(allClueIds, newClueIds...)
	allClueIds = append(allClueIds, existingClueIds...)

	if len(allClueIds) == 0 {
		return false
	}

	// 查询是否有域名类型的线索
	var count int64
	err := mysql.GetDbClient().Model(&clues.Clue{}).
		Where("id IN ? AND user_id = ? AND group_id = ? AND type = ? AND is_deleted = ?",
			allClueIds, userId, groupId, clues.TYPE_DOMAIN, clues.NOT_DELETE).
		Count(&count).Error

	if err != nil {
		log.Error("checkHasDomainClue", "查询域名类型线索失败", err)
		return false
	}

	log.Info("checkHasDomainClue", "域名类型线索检查结果", map[string]interface{}{
		"user_id":           userId,
		"group_id":          groupId,
		"total_clue_count":  len(allClueIds),
		"domain_clue_count": count,
		"has_domain_clue":   count > 0,
	})

	return count > 0
}
