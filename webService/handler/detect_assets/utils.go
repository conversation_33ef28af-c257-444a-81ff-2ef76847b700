package detect_assets

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"micro-service/apiService/upload"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"

	"golang.org/x/net/idna"
)

// GetSelectIds 获取选择的线索ID列表
// 对应PHP中的getSelectIds方法
// confirm参数用于指定是否只获取已确认的线索
func GetSelectIds(req *pb.PassClueRequest, confirm bool) ([]uint64, error) {
	if len(req.Data) == 0 {
		return nil, fmt.Errorf("参数错误: 数据为空")
	}

	var allIds []uint64

	// 处理每个数据项
	for _, item := range req.Data {
		// 如果直接提供了ID列表
		if len(item.Id) > 0 {
			allIds = append(allIds, item.Id...)
			continue
		}

		// 如果是全选
		if item.IsAll == 1 {
			var keyword string

			// 根据类型选择对应的关键词
			if req.Keyword != nil {
				switch item.Type {
				case clues.TYPE_DOMAIN:
					keyword = req.Keyword.DomainKeyword
				case clues.TYPE_IP:
					keyword = req.Keyword.IpKeyword
				case clues.TYPE_KEYWORD:
					keyword = req.Keyword.KeyKeyword
				case clues.TYPE_ICP:
					keyword = req.Keyword.IcpKeyword
				case clues.TYPE_CERT:
					keyword = req.Keyword.CertKeyword
				case clues.TYPE_SUBDOMAIN:
					keyword = req.Keyword.SubdomainKeyword
				}
			}

			// 构建查询
			query := mysql.GetDbClient().Table("clues").
				Select("id").
				Where("user_id = ? AND type = ? AND is_deleted = ? AND group_id = ? AND is_supply_chain != ?",
					req.UserId, item.Type, clues.NOT_DELETE, req.GroupId, 1)

			// 添加关键词搜索条件
			if keyword != "" {
				query = query.Where("(content LIKE ? OR clue_company_name LIKE ?)",
					"%"+keyword+"%", "%"+keyword+"%")
			}

			// 添加确认状态条件
			if confirm {
				query = query.Where("status = ?", clues.CLUE_PASS_STATUS)
			}

			// 执行查询获取ID列表
			var ids []uint64
			err := query.Pluck("id", &ids).Error
			if err != nil {
				return nil, fmt.Errorf("获取线索ID失败: %v", err)
			}

			allIds = append(allIds, ids...)
		}
	}

	// 根据状态过滤ID
	var finalIds []uint64
	if len(allIds) > 0 {
		query := mysql.GetDbClient().Table("clues").
			Select("id").
			Where("id IN ? AND is_deleted = ?", allIds, clues.NOT_DELETE)

		if confirm {
			query = query.Where("status = ?", clues.CLUE_PASS_STATUS)
		} else {
			query = query.Where("status = ?", req.TabStatus)
		}

		err := query.Pluck("id", &finalIds).Error
		if err != nil {
			return nil, fmt.Errorf("过滤线索ID失败: %v", err)
		}
	}

	// 去重
	uniqueIds := make(map[uint64]struct{})
	var result []uint64
	for _, id := range finalIds {
		if _, exists := uniqueIds[id]; !exists {
			uniqueIds[id] = struct{}{}
			result = append(result, id)
		}
	}

	return result, nil
}

// GetSelectClueIds 获取选择的线索ID列表
func GetSelectClueIds(req *pb.ClueBlackRequest) ([]uint64, error) {
	if len(req.Data) == 0 {
		return nil, fmt.Errorf("参数错误: 数据为空")
	}

	var allIds []uint64

	// 处理每个数据项
	for _, item := range req.Data {
		// 如果直接提供了ID列表
		if len(item.Id) > 0 {
			allIds = append(allIds, item.Id...)
			continue
		}

		// 如果是全选
		if item.IsAll != nil && *item.IsAll == 1 {
			var keyword string

			// 根据类型选择对应的关键词
			if req.Keyword != nil {
				switch item.Type {
				case clues.TYPE_DOMAIN:
					keyword = req.Keyword.DomainKeyword
				case clues.TYPE_IP:
					keyword = req.Keyword.IpKeyword
				case clues.TYPE_KEYWORD:
					keyword = req.Keyword.KeyKeyword
				case clues.TYPE_ICP:
					keyword = req.Keyword.IcpKeyword
				case clues.TYPE_CERT:
					keyword = req.Keyword.CertKeyword
				case clues.TYPE_SUBDOMAIN:
					keyword = req.Keyword.SubdomainKeyword
				}
			}

			// 构建查询
			query := mysql.GetDbClient().Table("clues").
				Select("id").
				Where("user_id = ? AND type = ? AND is_deleted = ? AND group_id = ? AND is_supply_chain != ?",
					req.UserId, item.Type, clues.NOT_DELETE, req.GroupId, 1)

			// 添加关键词搜索条件
			if keyword != "" {
				query = query.Where("(`content` LIKE ? OR `clue_company_name` LIKE ?)",
					"%"+keyword+"%", "%"+keyword+"%")
			}

			// 执行查询获取ID列表
			var ids []uint64
			err := query.Pluck("id", &ids).Error
			if err != nil {
				return nil, fmt.Errorf("获取线索ID失败: %v", err)
			}

			allIds = append(allIds, ids...)
		}
	}

	// 去重
	uniqueIds := make(map[uint64]struct{})
	var result []uint64
	for _, id := range allIds {
		if _, exists := uniqueIds[id]; !exists {
			uniqueIds[id] = struct{}{}
			result = append(result, id)
		}
	}

	return result, nil
}

// UploadDecrypt 解析上传文件路径
func UploadDecrypt(uploadPath string) (utils.DownloadFile, error) {
	uploadPath = strings.TrimPrefix(uploadPath, upload.FileAccessPathPrefix)
	if index := strings.LastIndex(uploadPath, "."); index > 0 {
		uploadPath = uploadPath[:index]
	}

	s, err := utils.LaravelDecrypt(uploadPath)
	if err != nil {
		return utils.DownloadFile{}, err
	}

	var info utils.DownloadFile
	err = json.Unmarshal([]byte(s), &info)
	if err != nil {
		return utils.DownloadFile{}, err
	}

	return info, nil
}

// containsInt64 检查一个int64切片中是否包含指定值
func containsInt64(slice []int64, item int64) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// 辅助函数：将 clues.Clue 转为 pb.ClueChain，full 为 true 时保留全部字段，否则只保留 type+content
func clueToChain(clue *clues.Clue, full bool) *pb.ClueChain {
	if clue == nil {
		return &pb.ClueChain{}
	}

	if full {
		return &pb.ClueChain{
			Id:              int64(clue.Id),
			Content:         clue.Content,
			Type:            int32(clue.Type),
			Hash:            strconv.Itoa(clue.Hash),
			FromIp:          clue.FromIp,
			ParentId:        int64(clue.ParentId),
			Source:          int32(clue.Source),
			ClueCompanyName: clue.ClueCompanyName,
		}
	} else {
		return &pb.ClueChain{
			Content: clue.FromIp,
			Type:    int32(clue.Type),
		}
	}
}

// 获取父级线索链（不包含当前线索本身，顺序：最顶级父→最近父）
func GetTopClue(parentId int64, clueList []*clues.Clue, id int64, chainList *[]*pb.ClueChain) {
	// 防止死循环：使用 visited 集合记录已访问的 ID
	visited := make(map[int64]bool)
	getTopClueRecursive(parentId, clueList, id, chainList, visited, 0)
}

// 递归实现，带有死循环检测
func getTopClueRecursive(parentId int64, clueList []*clues.Clue, id int64, chainList *[]*pb.ClueChain, visited map[int64]bool, depth int) {
	// 防止无限递归：检查递归深度
	if depth > 5 {
		log.Errorf("GetTopClue 递归深度过深，停止递归 - parentId:%d, id:%d, depth:%d", parentId, id, depth)
		return
	}

	// 防止死循环：检查是否已访问过这个 parentId
	if visited[parentId] {
		log.Errorf("GetTopClue 检测到循环引用，停止递归 - parentId:%d, id:%d, visited:%v", parentId, id, visited)
		return
	}

	// 如果 parentId 为 0 或负数，停止递归
	if parentId <= 0 {
		return
	}

	// 标记当前 parentId 为已访问
	visited[parentId] = true
	defer func() {
		// 递归返回时清除标记，允许其他路径访问
		delete(visited, parentId)
	}()

	// 如果线索列表过大，记录性能信息
	if len(clueList) > 500 {
		log.Infof("GetTopClue 处理大量线索 - parentId:%d, id:%d, clueListSize:%d, depth:%d", parentId, id, len(clueList), depth)
	}

	iterationCount := 0
	for _, clue := range clueList {
		iterationCount++

		// 处理初始线索的情况（当parentId == 0且clue.Id == id时）
		if parentId == 0 && int64(clue.Id) == id {
			if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
				// 如果不是首级线索，from_ip插入到末尾
				if clue.ParentId > 0 {
					*chainList = append(*chainList, clueToChain(clue, false))
				} else {
					// 首级线索，from_ip插入到开头
					*chainList = append([]*pb.ClueChain{clueToChain(clue, false)}, *chainList...)
				}
			}
		} else if int64(clue.Id) == parentId {
			// 检查是否会形成自引用
			if clue.Id == clue.ParentId {
				log.Errorf("GetTopClue 发现自引用线索，跳过 - clueId:%d, parentId:%d", clue.Id, clue.ParentId)
				continue
			}

			// 父级线索 unshift 到链表（保留全部字段）
			*chainList = append([]*pb.ClueChain{clueToChain(clue, true)}, *chainList...)
			// 推荐来源且有 from_ip，unshift 到链表（只保留 type+content）
			if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
				*chainList = append([]*pb.ClueChain{clueToChain(clue, false)}, *chainList...)
			}

			// 递归调用前记录深度
			if len(clueList) > 500 || depth > 10 {
				log.Infof("GetTopClue 递归调用 - 当前parentId:%d, 新parentId:%d, 链长度:%d, depth:%d",
					parentId, clue.ParentId, len(*chainList), depth)
			}

			// 递归调用
			getTopClueRecursive(int64(clue.ParentId), clueList, id, chainList, visited, depth+1)
		}
	}

	// 特殊处理：在第一次调用时，检查当前线索（id）的from_ip
	// 这对应PHP中的第一个if条件：empty($parent_id) && $clue['id'] == $id
	if depth == 0 {
		for _, clue := range clueList {
			if int64(clue.Id) == id {
				if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
					// 如果不是首级线索，from_ip插入到末尾
					if clue.ParentId > 0 {
						*chainList = append(*chainList, clueToChain(clue, false))
					} else {
						// 首级线索，from_ip插入到开头
						*chainList = append([]*pb.ClueChain{clueToChain(clue, false)}, *chainList...)
					}
				}
				break
			}
		}
	}

	// 如果遍历次数过多，记录性能警告
	if iterationCount > 500 {
		log.Warnf("GetTopClue 遍历次数过多 - parentId:%d, id:%d, iterations:%d, chainLength:%d, depth:%d",
			parentId, id, iterationCount, len(*chainList), depth)
	}
}

// 检查是否为IDN域名
func checkIdnDomain(domain string) bool {
	// 简单检查是否包含非ASCII字符
	for _, r := range domain {
		if r > 127 {
			return true
		}
	}
	return false
}

// 获取IDN域名的Punycode编码
func getIdnDomain(domain string) string {
	punycode, err := idna.ToASCII(domain)
	if err != nil {
		log.Error("getIdnDomain", "转换IDN域名失败", err)
		return ""
	}
	return punycode
}

// 判断字符串是否在切片中
func containsString(slice []string, str string) bool {
	for _, v := range slice {
		if v == str {
			return true
		}
	}
	return false
}

// maxNum 返回两个整数中的较大值
func maxNum(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// genFlag 生成推荐任务标识
func genFlag(userId uint64, clueIds []uint64, taskName string) string {
	// 简单实现，使用时间戳、用户ID和线索ID的哈希值作为flag
	data := fmt.Sprintf("%d_%s_%v", userId, taskName, clueIds)
	return utils.Md5sHash(data, false)
}

// convertUint64ToIntSlice 将uint64切片转换为int切片
func convertUint64ToIntSlice(ids []uint64) []int {
	result := make([]int, len(ids))
	for i, id := range ids {
		result[i] = int(id)
	}
	return result
}

// HUNTER_ATTRIBUTE_MAP 线索类型映射到Hunter查询前缀
var HUNTER_ATTRIBUTE_MAP = map[int]string{
	clues.TYPE_DOMAIN:    "domain.suffix",
	clues.TYPE_CERT:      "cert.subject_org",
	clues.TYPE_ICP:       "cert.subject_org",
	clues.TYPE_LOGO:      "icon_hash",
	clues.TYPE_SUBDOMAIN: "domain",
	clues.TYPE_IP:        "ip",
}

// ParseHunterQueryStr 将线索转换为Hunter查询语句
// 对应PHP中的parseHunterQueryStr方法
func ParseHunterQueryStr(params []*pb.ClueInfo) string {
	result := []string{}

	for _, item := range params {
		// 根据不同类型处理查询条件
		switch item.Type {
		case int32(clues.TYPE_CERT):
			// 处理证书类型
			if content := TransHunterferCert(item.Content); content != "" {
				result = append(result, content)
			}
		case int32(clues.TYPE_LOGO):
			// 处理logo类型，使用cast.ToString兼容各种类型
			result = append(result, fmt.Sprintf("icon_hash=\"%s\"", cast.ToString(item.Hash)))
		case int32(clues.TYPE_ICP):
			// 处理ICP类型
			content := utils.RemoveIcpNumber(item.Content)
			if content != "" {
				result = append(result, fmt.Sprintf("(cert.subject_org=\"%s\")", content))
			}
		case int32(clues.TYPE_DOMAIN):
			// 处理域名类型
			result = append(result, fmt.Sprintf("domain.suffix=%s", item.Content))
		case int32(clues.TYPE_SUBDOMAIN):
			// 处理子域名类型
			topDomain := utils.GetTopDomain(item.Content)
			if topDomain != "" {
				result = append(result, fmt.Sprintf("(domain=%s && domain.suffix=%s)", item.Content, topDomain))
			}
		}
	}

	// 组合查询条件
	return strings.Join(result, " || ")
}

// TransHunterferCert 转换证书内容为Hunter查询语句
// 对应PHP中的transHunterferCert方法
func TransHunterferCert(content string) string {
	// 如果证书内容为空，直接返回空字符串
	if content == "" {
		return ""
	}

	// 检查是否包含CN=或O=
	if strings.Contains(content, "CN=") || strings.Contains(content, "cn=") ||
		strings.Contains(content, "O=") || strings.Contains(content, "o=") {
		cnoCerts := []string{}

		// 提取CN值
		cnMatch := regexp.MustCompile(`(?i)CN="(.*?)"`).FindStringSubmatch(content)
		if len(cnMatch) > 1 {
			cn := cnMatch[1]
			if utils.IsDomain(cn) {
				// 如果是域名，移除通配符
				cn = strings.Replace(cn, "*.", "", -1)
				cnoCerts = append(cnoCerts, fmt.Sprintf("(cert.subject=\"%s\") || (cert.subject=\"*.%s\")", cn, cn))
			} else {
				cnoCerts = append(cnoCerts, fmt.Sprintf("(cert.subject=\"%s\")", cn))
			}
		}

		// 提取O值
		oMatch := regexp.MustCompile(`(?i)O="(.*?)"`).FindStringSubmatch(content)
		if len(oMatch) > 1 {
			o := oMatch[1]
			cnoCerts = append(cnoCerts, fmt.Sprintf("(cert.subject=\"%s\")", o))
		}

		// 根据结果数量返回
		if len(cnoCerts) == 0 {
			return ""
		} else if len(cnoCerts) == 1 {
			return cnoCerts[0]
		} else {
			return "(" + strings.Join(cnoCerts, " || ") + ")"
		}
	}

	// 检查是否是有效域名
	if utils.IsDomain(content) {
		return fmt.Sprintf("((cert.subject=\"%s\") || (cert.subject=\"*.%s\"))", content, content)
	} else {
		return fmt.Sprintf("(cert.subject=\"%s\")", content)
	}
}

// sumMapValues 计算map中所有值的总和
func sumMapValues(m map[string]int) int {
	sum := 0
	for _, v := range m {
		sum += v
	}
	return sum
}

// batchImportRead 读取Excel文件
func batchImportRead(files []string) ([]map[string]string, error) {
	if len(files) == 0 {
		return nil, fmt.Errorf("文件列表为空")
	}

	// 获取第一个文件路径
	filePath := files[0]
	filePath = filepath.Join(storage.GetRootPath(), "app/public/"+filePath)
	// 打开Excel文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %v", err)
	}
	defer f.Close()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %v", err)
	}

	// 检查是否有数据
	if len(rows) < 2 {
		return nil, fmt.Errorf("Excel文件格式不正确或没有数据")
	}

	// 获取表头
	headers := rows[0]

	// 处理数据
	var result []map[string]string
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		data := make(map[string]string)

		// 跳过空行
		if len(row) == 0 {
			continue
		}

		// 映射列数据到表头
		for j, header := range headers {
			if j < len(row) {
				data[header] = row[j]
			} else {
				data[header] = ""
			}
		}

		result = append(result, data)
	}

	return result, nil
}

// GetCert 提取证书信息
func GetCert(cert string) string {
	if cert == "" {
		return ""
	}

	// 使用utils.GetCert提取证书信息，不过滤证书
	certInfo := utils.GetCert(cert, false)
	if len(certInfo) == 0 {
		return ""
	}

	// 将证书信息数组转换为字符串
	return strings.Join(certInfo, " ")
}
