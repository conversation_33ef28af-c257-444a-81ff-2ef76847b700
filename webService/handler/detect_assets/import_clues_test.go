package detect_assets

import (
	"context"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	initmysql "micro-service/initialize/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
	"testing"
)

func TestImportClues(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()

	t.Run("import clues - empty data and file", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data:    []*pb.ImportCluesRequest_ClueItem{},
			File:    "",
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "上传的线索内容为空，参数错误!")
	})

	t.Run("import clues - data import success", func(t *testing.T) {
		initmysql.ResetMockInstance()
		mock := initmysql.GetMockInstance()

		// Mock 供应链线索检查
		mock.ExpectQuery("SELECT count.*FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		// Mock 线索存在性检查
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock 创建线索
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `clues` .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock 测绘任务查询
		mock.ExpectQuery("SELECT .* FROM `detect_assets_tasks` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:            clues.TYPE_DOMAIN,
					Content:         []string{"example.com"},
					ClueCompanyName: "测试公司",
				},
			},
			CompanyName:  "默认公司",
			IsTask:       0,
			IsAutoExpend: 0,
			SetStatus:    1,
			OperatorId:   1,
			ClientIp:     "127.0.0.1",
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.NoError(t, err)
	})

	t.Run("import clues - content too long", func(t *testing.T) {
		ctx := context.Background()
		longContent := make([]rune, 201)
		for i := range longContent {
			longContent[i] = 'a'
		}

		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_DOMAIN,
					Content: []string{string(longContent)},
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "有线索长度超过200个字符")
	})

	t.Run("import clues - invalid IP format", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_IP,
					Content: []string{"invalid-ip"},
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "线索ip格式不符合要求")
	})

	t.Run("import clues - invalid ICP format", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_ICP,
					Content: []string{"invalid-icp"},
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "ICP格式非法")
	})

	t.Run("import clues - invalid domain format", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_DOMAIN,
					Content: []string{"invalid-domain"},
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "根域格式非法")
	})

	t.Run("import clues - invalid cert format", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_CERT,
					Content: []string{"invalid-cert"},
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "证书线索格式错误")
	})

	t.Run("import clues - subdomain as main domain", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_SUBDOMAIN,
					Content: []string{"example.com"}, // 主域名作为子域名
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "子域名类型线索下不可以填写主域名线索数据")
	})
}

func TestImportCluesFileProcessing(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()

	t.Run("import clues - file not found", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			File:    "nonexistent-file",
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.Error(t, err)
		// 由于 UploadDecrypt 会失败，所以会返回解析错误
		assert.Contains(t, err.Error(), "解析上传文件失败")
	})

}

func TestImportCluesValidation(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()

	t.Run("import clues - valid IP addresses", func(t *testing.T) {
		initmysql.ResetMockInstance()
		mock := initmysql.GetMockInstance()

		// Mock 供应链线索检查
		mock.ExpectQuery("SELECT count.*FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		// Mock 线索存在性检查
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock 创建线索
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `clues` .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock 测绘任务查询
		mock.ExpectQuery("SELECT .* FROM `detect_assets_tasks` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_IP,
					Content: []string{"***********"},
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.NoError(t, err)
	})

	t.Run("import clues - valid ICP format", func(t *testing.T) {
		initmysql.ResetMockInstance()
		mock := initmysql.GetMockInstance()

		// Mock 供应链线索检查
		mock.ExpectQuery("SELECT count.*FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		// Mock 线索存在性检查
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock 创建线索
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `clues` .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock 测绘任务查询
		mock.ExpectQuery("SELECT .* FROM `detect_assets_tasks` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_ICP,
					Content: []string{"京ICP备12345678号"},
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.NoError(t, err)
	})
}

func TestImportCluesErrorHandling(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()

	t.Run("import clues - duplicate clue exists", func(t *testing.T) {
		initmysql.ResetMockInstance()
		mock := initmysql.GetMockInstance()

		// Mock 供应链线索检查
		mock.ExpectQuery("SELECT count.*FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		// Mock 线索存在性检查 - 返回已存在的线索
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "user_id", "group_id", "type", "content",
			}).AddRow(1, 1, 1, clues.TYPE_DOMAIN, "example.com"))

		// Mock 测绘任务查询
		mock.ExpectQuery("SELECT .* FROM `detect_assets_tasks` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_DOMAIN,
					Content: []string{"example.com"},
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		// 重复线索应该被跳过，不应该返回错误
		assert.NoError(t, err)
	})

	t.Run("import clues - create task mode", func(t *testing.T) {
		initmysql.ResetMockInstance()
		mock := initmysql.GetMockInstance()

		// Mock 供应链线索检查
		mock.ExpectQuery("SELECT count.*FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		// Mock 线索存在性检查
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock 创建线索
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `clues` .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock 测绘任务查询 - 返回已存在的任务
		mock.ExpectQuery("SELECT .* FROM `detect_assets_tasks` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "user_id", "group_id", "step", "status",
			}).AddRow(1, 1, 1, detect_assets_tasks.StepOne, detect_assets_tasks.StatusDefault))

		// Mock 更新任务
		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `detect_assets_tasks` SET .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:       1,
			GroupId:      1,
			IsTask:       1,
			IsAutoExpend: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_DOMAIN,
					Content: []string{"example.com"},
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		assert.NoError(t, err)
	})
}

func TestHelperFunctions1(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()

	t.Run("containsString - found", func(t *testing.T) {
		slice := []string{"apple", "banana", "cherry"}
		result := containsString(slice, "banana")
		assert.True(t, result)
	})

	t.Run("containsString - not found", func(t *testing.T) {
		slice := []string{"apple", "banana", "cherry"}
		result := containsString(slice, "orange")
		assert.False(t, result)
	})

	t.Run("containsString - empty slice", func(t *testing.T) {
		slice := []string{}
		result := containsString(slice, "apple")
		assert.False(t, result)
	})

	t.Run("checkIdnDomain - invalid domain", func(t *testing.T) {
		result := checkIdnDomain("invalid-domain")
		assert.False(t, result)
	})

	t.Run("checkIdnDomain - empty domain", func(t *testing.T) {
		result := checkIdnDomain("")
		assert.False(t, result)
	})

	t.Run("getIdnDomain - ascii domain", func(t *testing.T) {
		result := getIdnDomain("example.com")
		assert.Equal(t, "example.com", result)
	})

	t.Run("getIdnDomain - unicode domain", func(t *testing.T) {
		result := getIdnDomain("测试.com")
		// IDN 转换后应该是 punycode 格式
		assert.Contains(t, result, "xn--")
	})

	t.Run("getIdnDomain - empty domain", func(t *testing.T) {
		result := getIdnDomain("")
		assert.Equal(t, "", result)
	})
}

func TestImportCluesEdgeCases(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()

	t.Run("import clues - mixed valid and invalid data", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_DOMAIN,
					Content: []string{"example.com"}, // 有效
				},
				{
					Type:    clues.TYPE_IP,
					Content: []string{"invalid-ip"}, // 无效
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		// 应该在第一个无效数据处停止并返回错误
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "线索ip格式不符合要求")
	})

	t.Run("import clues - empty content in clue item", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:    clues.TYPE_DOMAIN,
					Content: []string{}, // 空内容
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		// 空内容应该被跳过，不应该返回错误
		assert.NoError(t, err)
	})

	t.Run("import clues - very long company name", func(t *testing.T) {
		initmysql.ResetMockInstance()
		mock := initmysql.GetMockInstance()

		// Mock 供应链线索检查
		mock.ExpectQuery("SELECT count.*FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		// Mock 线索存在性检查
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock 创建线索
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `clues` .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock 测绘任务查询
		mock.ExpectQuery("SELECT .* FROM `detect_assets_tasks` WHERE .*").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		longCompanyName := make([]rune, 300)
		for i := range longCompanyName {
			longCompanyName[i] = '公'
		}

		ctx := context.Background()
		req := &pb.ImportCluesRequest{
			UserId:  1,
			GroupId: 1,
			Data: []*pb.ImportCluesRequest_ClueItem{
				{
					Type:            clues.TYPE_DOMAIN,
					Content:         []string{"example.com"},
					ClueCompanyName: string(longCompanyName),
				},
			},
		}
		resp := &pb.Empty{}

		err := ImportClues(ctx, req, resp)

		// 长公司名应该被截断处理
		assert.NoError(t, err)
	})
}

// TestCheckHasDomainClue 测试域名线索检查函数
func TestCheckHasDomainClue(t *testing.T) {
	cfg.InitLoadCfg()
	log.Init()

	t.Run("checkHasDomainClue - empty clue ids", func(t *testing.T) {
		ctx := context.Background()
		result := checkHasDomainClue(ctx, 1, 1, []uint64{}, []uint64{})

		assert.False(t, result)
	})
}
