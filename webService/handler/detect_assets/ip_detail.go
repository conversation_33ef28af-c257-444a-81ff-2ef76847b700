package detect_assets

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/fofaee_service"
	"micro-service/middleware/elastic/fofaee_subdomain"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/ip_history"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
	"go-micro.dev/v4/errors"
)

// IpDetail 获取IP详情信息
func IpDetail(ctx context.Context, req *pb.IpDetailRequest, rsp *pb.IpDetailResponse) error {
	log.WithContextInfof(ctx, "Received Web.IpDetail request: %v", req)

	var (
		result      map[string]interface{}
		err         error
		keyRule     string
		ruleList    []map[string]interface{}
		resultBytes []byte
		ip          string
	)

	// 参数校验
	if req.Id == "" {
		return errors.BadRequest(pb.ServiceName, "ID不能为空")
	}

	// 根据来源选择查询模型
	if req.From == 0 {
		// 从IP资产查询
		log.WithContextInfof(ctx, "开始从IP资产查询，ID=%s", req.Id)
		ipAssetsModel := fofaee_assets.NewFofaeeAssetsModel()
		ipAsset := ipAssetsModel.FindFullById(ctx, req.Id)
		if ipAsset == nil {
			log.WithContextErrorf(ctx, "未找到该IP资产，ID=%s", req.Id)
			return errors.BadRequest(pb.ServiceName, "未找到该IP资产")
		}

		// 打印原始IP资产数据
		log.WithContextInfof(ctx, "找到IP资产，原始数据: IP=%s, PortSize=%d, OnlineState=%d, RuleTags长度=%d",
			ipAsset.Ip, ipAsset.PortSize, ipAsset.OnlineState, len(ipAsset.RuleTags))

		// 将结构体转为map
		resultBytes, err = json.Marshal(ipAsset)
		if err != nil {
			log.WithContextErrorf(ctx, "IP资产序列化失败: %v", err)
			return errors.BadRequest(pb.ServiceName, "数据处理错误")
		}
		if err = json.Unmarshal(resultBytes, &result); err != nil {
			log.WithContextErrorf(ctx, "IP资产数据解析错误: %v", err)
			return errors.BadRequest(pb.ServiceName, "数据解析错误")
		}
		log.WithContextInfof(ctx, "IP资产数据解析成功，字段数量: %d", len(result))

		// 打印result中的关键字段
		log.WithContextInfof(ctx, "result中的关键字段: ip=%v(类型:%T), port_list长度=%d, rule_tags长度=%d",
			result["ip"], result["ip"],
			getSliceLength(result["port_list"]),
			getSliceLength(result["rule_tags"]))

		keyRule = "rule_tags"
		log.WithContextInfof(ctx, "从IP资产查询成功，ID=%s", req.Id)
	} else {
		// 从任务资产查询
		log.WithContextInfof(ctx, "开始从任务资产查询，ID=%s", req.Id)
		taskAssetsModel := fofaee_task_assets.NewFofaeeTaskAssetsModel()

		// 处理IPv6的情况，按照PHP逻辑
		var queryId string
		queryId = req.Id
		log.WithContextInfof(ctx, "资产id: 用户ID=%d, 资产ID=%s", req.UserId, queryId)
		// 使用新的安全查询方法
		taskAsset, err := taskAssetsModel.FindByUserIdAndId(ctx, uint64(req.UserId), queryId)
		if err != nil {
			log.WithContextErrorf(ctx, "查询任务资产失败: %v", err)
			return errors.BadRequest(pb.ServiceName, "查询任务资产失败")
		}

		if taskAsset == nil {
			log.WithContextErrorf(ctx, "未找到用户的任务资产，用户ID=%d, 资产ID=%s", req.UserId, queryId)
			return errors.BadRequest(pb.ServiceName, "未找到该任务资产或无权限访问")
		}

		// 打印原始任务资产数据
		log.WithContextInfof(ctx, "找到任务资产，原始数据: IP=%s, UserId=%d, PortSize=%d",
			taskAsset.Ip, taskAsset.UserId, taskAsset.PortSize)

		// 将结构体转为map
		resultBytes, err = json.Marshal(taskAsset)
		if err != nil {
			log.WithContextErrorf(ctx, "任务资产序列化失败: %v", err)
			return errors.BadRequest(pb.ServiceName, "数据处理错误")
		}
		if err = json.Unmarshal(resultBytes, &result); err != nil {
			log.WithContextErrorf(ctx, "任务资产数据解析错误: %v", err)
			return errors.BadRequest(pb.ServiceName, "数据解析错误")
		}
		log.WithContextInfof(ctx, "任务资产数据解析成功，字段数量: %d", len(result))

		// 打印result中的关键字段
		log.WithContextInfof(ctx, "result中的关键字段: ip=%v(类型:%T), port_list长度=%d, rules长度=%d",
			result["ip"], result["ip"],
			getSliceLength(result["port_list"]),
			getSliceLength(result["rules"]))

		keyRule = "rules"
		log.WithContextInfof(ctx, "从任务资产查询成功，IP: %s", taskAsset.Ip)
	}

	// 提取IP字段，支持多种类型
	log.WithContextInfof(ctx, "开始提取IP字段，result[\"ip\"]存在: %t", result["ip"] != nil)
	if ipVal, ok := result["ip"].(string); ok && ipVal != "" {
		log.WithContextInfof(ctx, "✅ 提取到IP(string): %s", ipVal)
		ip = ipVal
	} else if ipVal, ok := result["ip"]; ok && ipVal != nil {
		// 尝试转换为字符串
		ipStr := cast.ToString(ipVal)
		log.WithContextInfof(ctx, "尝试转换IP: 原值=%v(类型:%T) -> 转换后=%s", ipVal, ipVal, ipStr)
		if ipStr != "" && ipStr != "0" {
			log.WithContextInfof(ctx, "✅ 提取到IP(转换): %s", ipStr)
			ip = ipStr
		} else {
			log.WithContextErrorf(ctx, "❌ 未能从结果中提取有效IP，result[ip]类型: %T, 值: %v", result["ip"], result["ip"])
		}
	} else {
		log.WithContextErrorf(ctx, "❌ 未能从结果中提取IP，result[ip]类型: %T, 值: %v", result["ip"], result["ip"])
	}

	// 处理规则标签
	if keyRule == "rule_tags" {
		result["rules"] = result["rule_tags"]
		log.WithContextInfof(ctx, "设置rules字段，rule_tags数量: %d", getSliceLength(result["rule_tags"]))
	} else {
		result["rule_tags"] = result["rules"]
		log.WithContextInfof(ctx, "设置rule_tags字段，rules数量: %d", getSliceLength(result["rules"]))
	}

	// 打印原始规则数据用于调试
	if ruleTags, ok := result[keyRule].([]interface{}); ok {
		log.WithContextInfof(ctx, "原始%s数据:", keyRule)
		for i, rule := range ruleTags {
			if ruleMap, ok := rule.(map[string]interface{}); ok {
				log.WithContextInfof(ctx, "  规则[%d]: cn_product=%v, port=%v", i, ruleMap["cn_product"], ruleMap["port"])
			}
		}
	}

	// 处理端口列表中的证书信息
	if portList, ok := result["port_list"].([]interface{}); ok {
		for k, v := range portList {
			if port, ok := v.(map[string]interface{}); ok {
				if cert, exists := port["cert"].(map[string]interface{}); exists && cert["subject_key"] != nil {
					result["port_list"].([]interface{})[k].(map[string]interface{})["cert"] = cert["subject_key"]
				}
			}
		}
	}

	// 提取域名
	var domains []string
	if hosts, ok := result["hosts"].([]interface{}); ok {
		for _, h := range hosts {
			if host, ok := h.(string); ok {
				domain := getSubdomain(host, false)
				if domain != "" {
					domains = append(domains, domain)
				}
			}
		}
	}
	result["domains"] = strings.Join(utils.UniqueStringSlice(domains), ",")

	// 去重公司名称
	if clueCompanyNames, ok := result["clue_company_name"].([]interface{}); ok {
		uniqueNames := make([]string, 0)
		for _, name := range clueCompanyNames {
			if strName, ok := name.(string); ok {
				uniqueNames = append(uniqueNames, strName)
			}
		}
		result["clue_company_name"] = utils.UniqueStringSlice(uniqueNames)
	}

	// 处理hosts，去掉http和https
	if hosts, ok := result["hosts"].([]interface{}); ok {
		newHosts := make([]string, 0)
		for _, h := range hosts {
			if host, ok := h.(string); ok {
				hostParts := strings.Split(host, "://")
				hostStr := host
				if len(hostParts) > 1 {
					hostStr = hostParts[1]
				}
				// 进一步处理端口
				hostParts = strings.Split(hostStr, ":")
				if len(hostParts) > 0 {
					hostStr = hostParts[0]
				}
				// 如果不是IP地址，则添加到新列表
				if !utils.IsIP(hostStr) {
					newHosts = append(newHosts, hostStr)
				}
			}
		}
		result["hosts"] = utils.UniqueStringSlice(newHosts)
	}

	// 确保IP已正确提取，如果还没有则尝试其他方式
	if ip == "" {
		if ipStr, ok := result["ip"].(string); ok && ipStr != "" {
			ip = ipStr
			log.WithContextInfof(ctx, "二次提取IP成功: %s", ip)
		} else if ipVal, ok := result["ip"]; ok && ipVal != nil {
			ipStr := cast.ToString(ipVal)
			if ipStr != "" && ipStr != "0" {
				ip = ipStr
				log.WithContextInfof(ctx, "二次提取IP成功(转换): %s", ip)
			}
		}

		// 如果还是没有IP，尝试从ID中提取
		if ip == "" && strings.Contains(req.Id, "_") {
			ipParts := strings.Split(req.Id, "_")
			if len(ipParts) > 1 {
				ip = ipParts[1]
				log.WithContextInfof(ctx, "从ID中提取IP: %s", ip)
			}
		}
	}

	log.WithContextInfof(ctx, "🔍 最终提取的IP: %s", ip)

	// 查询子域名资产和服务资产
	log.WithContextInfof(ctx, "开始查询子域名资产，IP: %s, UserId: %d", ip, req.UserId)
	subdomainModel := fofaee_subdomain.NewFofeeSubdomainModel()
	subAssets, err := subdomainModel.FindByIpPortWithUserIdAndRuleTags(ip, uint64(req.UserId))
	if err != nil {
		log.WithContextErrorf(ctx, "查询子域名资产失败: %v", err)
	} else {
		log.WithContextInfof(ctx, "查询子域名资产成功，数量: %d", len(subAssets))
		// 打印子域名资产的详细信息
		for i, sub := range subAssets {
			log.WithContextInfof(ctx, "子域名资产[%d]: Port=%v, RuleTags数量=%d", i, sub.Port, len(sub.RuleTags))
			for j, tag := range sub.RuleTags {
				log.WithContextInfof(ctx, "  RuleTag[%d]: CnProduct=%s", j, tag.CnProduct)
			}
		}
	}

	log.WithContextInfof(ctx, "开始查询服务资产，IP: %s, UserId: %d", ip, req.UserId)
	serviceModel := fofaee_service.NewFofeeServiceModel()
	serAssets, err := serviceModel.FindByIpPortWithUserIdAndRuleTags(ip, uint64(req.UserId))
	if err != nil {
		log.WithContextErrorf(ctx, "查询服务资产失败: %v", err)
	} else {
		log.WithContextInfof(ctx, "查询服务资产成功，数量: %d", len(serAssets))
		// 打印服务资产的详细信息
		for i, ser := range serAssets {
			log.WithContextInfof(ctx, "服务资产[%d]: Port=%d, RuleTags数量=%d", i, ser.Port, len(ser.RuleTags))
			for j, tag := range ser.RuleTags {
				log.WithContextInfof(ctx, "  RuleTag[%d]: CnProduct=%s", j, tag.CnProduct)
			}
		}
	}

	// 处理规则标签 - 按照PHP逻辑实现
	log.WithContextInfof(ctx, "开始处理规则标签，keyRule: %s", keyRule)
	if ruleTags, ok := result[keyRule].([]interface{}); ok {
		log.WithContextInfof(ctx, "原始规则数量: %d", len(ruleTags))

		for k, rule := range ruleTags {
			if ruleMap, ok := rule.(map[string]interface{}); ok {
				cnProduct := cast.ToString(ruleMap["cn_product"])
				log.WithContextInfof(ctx, "处理规则[%d]: cn_product=%s", k, cnProduct)

				// 处理子域名资产规则
				for i, sub := range subAssets {
					for j, tag := range sub.RuleTags {
						log.WithContextInfof(ctx, "比较子域名资产[%d]规则[%d]: %s vs %s", i, j, tag.CnProduct, cnProduct)
						if tag.CnProduct == cnProduct {
							log.WithContextInfof(ctx, "✅ 子域名资产规则匹配成功: %s, 端口: %v", cnProduct, sub.Port)
							// 按照PHP逻辑：创建规则副本并设置端口
							newRuleMap := make(map[string]interface{})
							for key, val := range ruleMap {
								newRuleMap[key] = val
							}
							newRuleMap["port"] = sub.Port
							// 添加到规则列表
							ruleList = append(ruleList, newRuleMap)
						}
					}
				}

				// 处理服务资产规则
				for i, ser := range serAssets {
					for j, tag := range ser.RuleTags {
						log.WithContextInfof(ctx, "比较服务资产[%d]规则[%d]: %s vs %s", i, j, tag.CnProduct, cnProduct)
						if tag.CnProduct == cnProduct {
							log.WithContextInfof(ctx, "✅ 服务资产规则匹配成功: %s, 端口: %d", cnProduct, ser.Port)
							// 按照PHP逻辑：创建规则副本并设置端口
							newRuleMap := make(map[string]interface{})
							for key, val := range ruleMap {
								newRuleMap[key] = val
							}
							newRuleMap["port"] = ser.Port
							// 添加到规则列表
							ruleList = append(ruleList, newRuleMap)
						}
					}
				}
			}
		}
	}

	log.WithContextInfof(ctx, "规则处理完成，ruleList数量: %d", len(ruleList))

	// 将规则添加到端口列表 - 按照PHP逻辑实现
	if portList, ok := result["port_list"].([]interface{}); ok {
		for _, rule := range ruleList {
			for kt, pt := range portList {
				if portMap, ok := pt.(map[string]interface{}); ok {
					if fmt.Sprintf("%v", portMap["port"]) == fmt.Sprintf("%v", rule["port"]) {
						// 按照PHP逻辑：检查rules字段是否存在
						if _, exists := portMap["rules"]; !exists {
							// 如果不存在，直接添加规则
							portMap["rules"] = []interface{}{rule}
						} else {
							// 如果存在，检查是否已有相同cn_product的规则
							if rules, ok := portMap["rules"].([]interface{}); ok {
								// 收集现有规则的cn_product
								existingProducts := make([]string, 0)
								for _, r := range rules {
									if rMap, ok := r.(map[string]interface{}); ok {
										if cnProduct, exists := rMap["cn_product"]; exists {
											existingProducts = append(existingProducts, cast.ToString(cnProduct))
										}
									}
								}

								// 检查当前规则的cn_product是否已存在
								currentProduct := cast.ToString(rule["cn_product"])
								productExists := false
								for _, product := range existingProducts {
									if product == currentProduct {
										productExists = true
										break
									}
								}

								// 如果不存在，则添加
								if !productExists {
									portMap["rules"] = append(rules, rule)
								}
							}
						}

						// 更新端口列表
						result["port_list"].([]interface{})[kt] = portMap
					}
				}
			}
		}
	}

	// 查询历史变化数据
	historyModel := ip_history.NewModel()
	histories, err := historyModel.FindByIp(uint64(req.UserId), ip)
	if err != nil {
		log.WithContextErrorf(ctx, "查询IP历史记录失败: %v", err)
	}

	// 处理历史记录
	historyResult := make([]map[string]interface{}, 0)
	for k, h := range histories {
		i := 0
		if k == 0 {
			i = 1
		}
		historyMap := make(map[string]interface{})
		historyMap["time"] = h.CreatedAt.Format("2006-01-02 15:04:05")
		historyMap["id"] = i
		historyMap["children"] = make([]interface{}, 0)

		// 处理端口变化
		if h.Data != "" {
			var oldData, newData map[string]interface{}

			// 解析当前数据
			err = json.Unmarshal([]byte(h.Data), &newData)
			if err != nil {
				continue
			}

			// 获取端口列表
			var oldPorts, newPorts []string
			if k > 0 {
				// 解析上一条记录的数据
				err = json.Unmarshal([]byte(histories[k-1].Data), &oldData)
				if err == nil {
					oldPortsData, ok := extractPortList(oldData["port_list"])
					if ok {
						oldPorts = oldPortsData
					}
				}
			}

			newPortsData, ok := extractPortList(newData["port_list"])
			if ok {
				newPorts = newPortsData
			}

			// 计算端口差异
			addedPorts := diffStringSlice(newPorts, oldPorts)
			removedPorts := diffStringSlice(oldPorts, newPorts)

			// 添加新增端口
			if len(addedPorts) > 0 {
				child := make(map[string]interface{})
				child["title"] = "新增端口"
				child["type"] = 1
				child["detail"] = strings.Join(addedPorts, ",")
				child["tip"] = "新增端口"
				historyMap["children"] = append(historyMap["children"].([]interface{}), child)
			}

			// 添加删除端口
			if len(removedPorts) > 0 {
				child := make(map[string]interface{})
				child["title"] = "删除端口"
				child["type"] = 2
				child["detail"] = strings.Join(removedPorts, ",")
				child["tip"] = "删除端口"
				historyMap["children"] = append(historyMap["children"].([]interface{}), child)
			}

			// 处理规则变化
			var oldRules, newRules []string
			if k > 0 && oldData != nil {
				oldRulesData, ok := extractRuleList(oldData[keyRule])
				if ok {
					oldRules = oldRulesData
				}
			}

			newRulesData, ok := extractRuleList(newData[keyRule])
			if ok {
				newRules = newRulesData
			}

			// 计算规则差异
			addedRules := diffStringSlice(newRules, oldRules)
			removedRules := diffStringSlice(oldRules, newRules)

			// 添加新增规则
			if len(addedRules) > 0 {
				child := make(map[string]interface{})
				child["title"] = "新增组件"
				child["type"] = 3
				child["detail"] = strings.Join(addedRules, ",")
				child["tip"] = "新增组件"
				historyMap["children"] = append(historyMap["children"].([]interface{}), child)
			}

			// 添加删除规则
			if len(removedRules) > 0 {
				child := make(map[string]interface{})
				child["title"] = "删除组件"
				child["type"] = 4
				child["detail"] = strings.Join(removedRules, ",")
				child["tip"] = "删除组件"
				historyMap["children"] = append(historyMap["children"].([]interface{}), child)
			}
		}

		// 添加到历史结果
		if len(historyMap["children"].([]interface{})) > 0 {
			historyResult = append(historyResult, historyMap)
		}
	}

	// 处理线索链
	chainList := make([][]map[string]interface{}, 0)

	// 从端口列表中提取reason数组，获取group_id
	var groupIds []interface{}
	if portList, ok := result["port_list"].([]interface{}); ok {
		for _, port := range portList {
			if portMap, ok := port.(map[string]interface{}); ok {
				if reasonArr, ok := portMap["reason"].([]interface{}); ok {
					for _, reason := range reasonArr {
						if reasonMap, ok := reason.(map[string]interface{}); ok {
							if groupId, exists := reasonMap["group_id"]; exists && groupId != nil {
								groupIds = append(groupIds, groupId)
								log.WithContextInfof(ctx, "提取到group_id: %v", groupId)
							}
						}
					}
				}
			}
		}
	}

	// 如果有group_id，查询相关线索
	if len(groupIds) > 0 {
		log.WithContextInfof(ctx, "查询线索链，group_id列表: %v", groupIds)
		// 查询线索
		clueModel := clues.NewCluer()
		clueList, err := clueModel.ListAll(
			mysql.WithWhere("user_id = ? AND status != ? AND group_id IN (?)",
				req.UserId,
				clues.CLUE_DEFAULT_STATUS,
				groupIds),
		)

		if err != nil {
			log.WithContextErrorf(ctx, "查询线索链失败: %v", err)
		} else {
			log.WithContextInfof(ctx, "查询到%d个线索", len(clueList))

			// 按照PHP逻辑：从port_list中收集所有reason，然后去重
			var allReasons []map[string]interface{}
			if portList, ok := result["port_list"].([]interface{}); ok {
				for _, port := range portList {
					if portMap, ok := port.(map[string]interface{}); ok {
						if reasonArr, ok := portMap["reason"].([]interface{}); ok {
							for _, reason := range reasonArr {
								if reasonMap, ok := reason.(map[string]interface{}); ok {
									allReasons = append(allReasons, reasonMap)
								}
							}
						}
					}
				}
			}

			// 去重：使用map来去重reason_id
			uniqueReasons := make(map[int64]map[string]interface{})
			for _, reason := range allReasons {
				if reasonId, exists := reason["id"]; exists && reasonId != nil {
					reasonIdInt := cast.ToInt64(reasonId)
					uniqueReasons[reasonIdInt] = reason
				}
			}

			log.WithContextInfof(ctx, "去重后的reason数量: %d", len(uniqueReasons))

			// 为每个唯一的reason构建线索链
			for reasonId := range uniqueReasons {
				var singleChainList []map[string]interface{}
				log.WithContextInfof(ctx, "构建线索链，reason_id: %d", reasonId)
				getTopClue(0, clueList, &singleChainList, reasonId)

				// 如果找到线索链，添加IP作为最后一个节点
				if len(singleChainList) > 0 {
					singleChainList = append(singleChainList, map[string]interface{}{
						"content": ip,
						"type":    clues.TYPE_IP,
					})
					chainList = append(chainList, singleChainList)
					log.WithContextInfof(ctx, "成功构建线索链，reason_id: %d, 链长度: %d", reasonId, len(singleChainList))
				}
			}

			// 如果没有找到任何线索链，添加默认的手动导入
			if len(chainList) == 0 {
				chainList = append(chainList, []map[string]interface{}{
					{"content": "手动导入"},
				})
				log.WithContextInfof(ctx, "没有找到线索链，添加默认的手动导入")
			}
		}
	}

	// 将线索链序列化为JSON字符串
	if chainListBytes, err := json.Marshal(chainList); err == nil {
		rsp.ChainList = string(chainListBytes)
		log.WithContextInfof(ctx, "线索链序列化成功，数量: %d", len(chainList))
	} else {
		rsp.ChainList = "[]"
		log.WithContextErrorf(ctx, "线索链序列化失败: %v", err)
	}

	// 在转换为protobuf之前，修复数据类型问题
	fixDataTypes(result)

	// 组装响应
	resultBytes, err = json.Marshal(result)
	if err != nil {
		log.WithContextErrorf(ctx, "响应数据序列化失败: %v", err)
		return errors.BadRequest(pb.ServiceName, "数据处理错误")
	}
	log.WithContextInfof(ctx, "响应数据JSON长度: %d", len(resultBytes))

	// 在解析到protobuf之前，处理类型不匹配的字段
	if err = preprocessResultForProtobuf(ctx, result); err != nil {
		log.WithContextErrorf(ctx, "数据预处理失败: %v", err)
		return errors.BadRequest(pb.ServiceName, "数据预处理错误")
	}

	// 重新序列化处理后的数据
	resultBytes, err = json.Marshal(result)
	if err != nil {
		log.WithContextErrorf(ctx, "数据重新序列化失败: %v", err)
		return errors.BadRequest(pb.ServiceName, "数据处理错误")
	}

	// 尝试解析到protobuf结构体
	if err = json.Unmarshal(resultBytes, rsp); err != nil {
		log.WithContextErrorf(ctx, "数据解析到protobuf失败: %v", err)

		// 输出完整的JSON数据用于调试
		jsonStr := string(resultBytes)
		log.WithContextErrorf(ctx, "完整的问题数据: %s", jsonStr)

		// 尝试解析到临时map来检查具体哪个字段有问题
		var tempMap map[string]interface{}
		if tempErr := json.Unmarshal(resultBytes, &tempMap); tempErr != nil {
			log.WithContextErrorf(ctx, "JSON本身有问题: %v", tempErr)
		} else {
			log.WithContextInfof(ctx, "JSON解析正常，问题在protobuf字段映射")
			// 检查关键字段的类型
			for key, value := range tempMap {
				log.WithContextInfof(ctx, "字段 %s: 类型=%T, 值=%v", key, value, value)
			}
		}

		return errors.BadRequest(pb.ServiceName, "数据解析错误")
	}

	// 添加历史记录
	log.WithContextInfof(ctx, "历史记录数量: %d", len(historyResult))
	for _, h := range historyResult {
		historyBytes, _ := json.Marshal(h)
		var history pb.IpDomainHistory
		if err = json.Unmarshal(historyBytes, &history); err != nil {
			continue
		}
		rsp.Histyory = append(rsp.Histyory, &history)
	}

	return nil
}

// preprocessResultForProtobuf 预处理结果数据，解决类型不匹配问题
func preprocessResultForProtobuf(ctx context.Context, result map[string]interface{}) error {
	// 处理 threaten_type 字段：从数字类型转换为字符串类型
	if threatenType, exists := result["threaten_type"]; exists && threatenType != nil {
		// 使用 cast.ToString 进行类型转换
		threatenTypeStr := cast.ToString(threatenType)
		result["threaten_type"] = threatenTypeStr
		log.WithContextInfof(ctx, "转换 threaten_type: %v(%T) -> %s", threatenType, threatenType, threatenTypeStr)
	}

	// 处理 threaten_type_name 字段：确保是字符串类型
	if threatenTypeName, exists := result["threaten_type_name"]; exists && threatenTypeName != nil {
		threatenTypeNameStr := cast.ToString(threatenTypeName)
		result["threaten_type_name"] = threatenTypeNameStr
		log.WithContextInfof(ctx, "转换 threaten_type_name: %v(%T) -> %s", threatenTypeName, threatenTypeName, threatenTypeNameStr)
	}

	return nil
}

// getTopClue 获取线索链，递归查找父级线索
// 参照PHP实现
func getTopClue(parentID int64, clueList []*clues.Clue, chainList *[]map[string]interface{}, id ...int64) {
	var currentID int64
	if len(id) > 0 {
		currentID = id[0]
	}
	for _, clue := range clueList {
		// 处理初始查找（通过ID查找）
		if parentID == 0 && clue.Id == uint64(currentID) {
			// 将当前线索添加到链中
			*chainList = append(*chainList, map[string]interface{}{
				"id":                  clue.Id,
				"user_id":             clue.UserId,
				"company_id":          clue.CompanyId,
				"parent_id":           clue.ParentId,
				"content":             clue.Content,
				"group_id":            clue.GroupId,
				"comment":             clue.Comment,
				"clue_company_name":   clue.ClueCompanyName,
				"hash":                clue.Hash,
				"source":              clue.Source,
				"count":               clue.Count,
				"type":                clue.Type,
				"status":              clue.Status,
				"created_at":          clue.CreatedAt.Format("2006-01-02 15:04:05"),
				"updated_at":          clue.UpdatedAt.Format("2006-01-02 15:04:05"),
				"safe_user_id":        clue.SafeUserId,
				"is_expand":           clue.IsExpand,
				"from_ip":             clue.FromIp,
				"is_deleted":          clue.IsDeleted,
				"punycode_domain":     clue.PunycodeDomain,
				"is_from_check_table": clue.IsFromCheckTable,
				"is_supply_chain":     clue.IsSupplyChain,
				"is_fake_icp":         clue.IsFakeIcp,
				"source_url":          nil,
			})

			// 如果是推荐来源且有from_ip
			if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
				// 如果不是首级线索，将from_ip添加到末尾
				if clue.ParentId > 0 {
					*chainList = append(*chainList, map[string]interface{}{
						"content": clue.FromIp,
					})
				} else {
					// 否则添加到开头
					*chainList = append([]map[string]interface{}{
						{"content": clue.FromIp},
					}, *chainList...)
				}
			}

			// 递归查找父级线索
			if clue.ParentId > 0 {
				getTopClue(int64(clue.ParentId), clueList, chainList)
			}

			// 找到后直接返回，避免重复处理
			return
		} else if parentID > 0 && clue.Id == uint64(parentID) {
			// 通过父ID查找
			// 将当前线索添加到链的开头
			*chainList = append([]map[string]interface{}{
				{
					"id":                  clue.Id,
					"user_id":             clue.UserId,
					"company_id":          clue.CompanyId,
					"parent_id":           clue.ParentId,
					"content":             clue.Content,
					"group_id":            clue.GroupId,
					"comment":             clue.Comment,
					"clue_company_name":   clue.ClueCompanyName,
					"hash":                clue.Hash,
					"source":              clue.Source,
					"count":               clue.Count,
					"type":                clue.Type,
					"status":              clue.Status,
					"created_at":          clue.CreatedAt.Format("2006-01-02 15:04:05"),
					"updated_at":          clue.UpdatedAt.Format("2006-01-02 15:04:05"),
					"safe_user_id":        clue.SafeUserId,
					"is_expand":           clue.IsExpand,
					"from_ip":             clue.FromIp,
					"is_deleted":          clue.IsDeleted,
					"punycode_domain":     clue.PunycodeDomain,
					"is_from_check_table": clue.IsFromCheckTable,
					"is_supply_chain":     clue.IsSupplyChain,
					"is_fake_icp":         clue.IsFakeIcp,
					"source_url":          nil,
				},
			}, *chainList...)

			// 如果是推荐来源且有from_ip
			if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
				*chainList = append([]map[string]interface{}{
					{"content": clue.FromIp},
				}, *chainList...)
			}

			// 递归查找父级线索
			if clue.ParentId > 0 {
				getTopClue(int64(clue.ParentId), clueList, chainList, currentID)
			}

			// 找到后直接返回，避免重复处理
			return
		}
	}
}

// extractPortList 提取端口列表
func extractPortList(portList interface{}) ([]string, bool) {
	if portList == nil {
		return nil, false
	}

	var ports []string
	switch v := portList.(type) {
	case string:
		if err := json.Unmarshal([]byte(v), &ports); err != nil {
			return nil, false
		}
	case []string:
		ports = v
	case []interface{}:
		result := make([]string, 0, len(v))
		for _, port := range v {
			if p, ok := port.(map[string]interface{}); ok {
				if portVal, exists := p["port"]; exists {
					result = append(result, fmt.Sprintf("%v", portVal))
				}
			}
		}
		return result, len(result) > 0
	default:
		return nil, false
	}

	return ports, true
}

// extractRuleList 提取规则列表
func extractRuleList(ruleList interface{}) ([]string, bool) {
	if ruleList == nil {
		return nil, false
	}

	var rules []string
	switch v := ruleList.(type) {
	case string:
		if err := json.Unmarshal([]byte(v), &rules); err != nil {
			return nil, false
		}
	case []string:
		rules = v
	case []interface{}:
		result := make([]string, 0, len(v))
		for _, rule := range v {
			if r, ok := rule.(map[string]interface{}); ok {
				if product, exists := r["cn_product"]; exists {
					if productStr, ok := product.(string); ok {
						result = append(result, productStr)
					}
				}
			}
		}
		return result, len(result) > 0
	default:
		return nil, false
	}

	return rules, true
}

// diffStringSlice 计算字符串切片差集
func diffStringSlice(a, b []string) []string {
	mb := make(map[string]struct{}, len(b))
	for _, x := range b {
		mb[x] = struct{}{}
	}
	var diff []string
	for _, x := range a {
		if _, found := mb[x]; !found {
			diff = append(diff, x)
		}
	}
	return diff
}

// getSliceLength 获取切片长度的辅助函数
func getSliceLength(val interface{}) int {
	if val == nil {
		return 0
	}
	if slice, ok := val.([]interface{}); ok {
		return len(slice)
	}
	return 0
}

// fixDataTypes 修复数据类型问题，确保与protobuf定义兼容
func fixDataTypes(result map[string]interface{}) {
	// 修复online_state字段：数字转布尔值
	// 参考PHP逻辑：优先取state字段，不存在则取online_state字段
	var finalOnlineState interface{}

	// 先尝试获取state字段
	if state, exists := result["state"]; exists && state != nil {
		finalOnlineState = state
	} else if onlineState, exists := result["online_state"]; exists && onlineState != nil {
		// state不存在或为空，则取online_state
		finalOnlineState = onlineState
	} else {
		// 两个字段都不存在或为空，设置为0
		result["online_state"] = 0
		return
	}

	// 根据不同类型处理最终的online_state值
	switch val := finalOnlineState.(type) {
	case int:
		result["online_state"] = val != 0
	case float64:
		result["online_state"] = val != 0
	case string:
		result["online_state"] = val != "0" && val != ""
	default:
		result["online_state"] = 0
	}

	// 修复is_ipv6字段：确保是布尔值
	if isIpv6, exists := result["is_ipv6"]; exists {
		switch val := isIpv6.(type) {
		case int:
			result["is_ipv6"] = val != 0
		case float64:
			result["is_ipv6"] = val != 0
		case string:
			result["is_ipv6"] = val == "true" || val == "1"
		}
	}

	// 修复is_cdn字段：确保是布尔值
	if isCdn, exists := result["is_cdn"]; exists {
		switch val := isCdn.(type) {
		case int:
			result["is_cdn"] = val != 0
		case float64:
			result["is_cdn"] = val != 0
		case string:
			result["is_cdn"] = val == "true" || val == "1"
		}
	}

	// 修复_id字段映射到doc_id
	if docId, exists := result["_id"]; exists {
		result["doc_id"] = cast.ToString(docId)
		delete(result, "_id")
	}

	// 修复reason_arr字段，将对象数组转换为字符串数组
	if reasonArr, ok := result["reason_arr"].([]interface{}); ok {
		reasonStrs := make([]string, 0, len(reasonArr))
		for _, r := range reasonArr {
			if rStr, ok := r.(string); ok {
				reasonStrs = append(reasonStrs, rStr)
			} else {
				// 如果是复杂对象，转换为JSON字符串
				if rBytes, err := json.Marshal(r); err == nil {
					reasonStrs = append(reasonStrs, string(rBytes))
				}
			}
		}
		result["reason_arr"] = reasonStrs
	}

	// 修复tags字段，确保是字符串数组
	if tags, ok := result["tags"].([]interface{}); ok {
		tagStrs := make([]string, 0, len(tags))
		for _, tag := range tags {
			tagStrs = append(tagStrs, cast.ToString(tag))
		}
		result["tags"] = tagStrs
	}

	// 删除task_id字段，避免不同ES索引间的类型冲突
	// 因为接口支持两个ES索引，它们的task_id字段类型定义不一样
	delete(result, "task_id")

	// 修复detect_assets_tasks_id字段，确保是int32数组
	if detectTaskIds, ok := result["detect_assets_tasks_id"].([]interface{}); ok {
		detectTaskIdInts := make([]int32, 0, len(detectTaskIds))
		for _, taskId := range detectTaskIds {
			detectTaskIdInts = append(detectTaskIdInts, cast.ToInt32(taskId))
		}
		result["detect_assets_tasks_id"] = detectTaskIdInts
	}

	// 修复org_detect_assets_tasks_id字段，确保是int32数组
	if orgDetectTaskIds, ok := result["org_detect_assets_tasks_id"].([]interface{}); ok {
		orgDetectTaskIdInts := make([]int32, 0, len(orgDetectTaskIds))
		for _, taskId := range orgDetectTaskIds {
			orgDetectTaskIdInts = append(orgDetectTaskIdInts, cast.ToInt32(taskId))
		}
		result["org_detect_assets_tasks_id"] = orgDetectTaskIdInts
	}

	// 修复organization_id字段，确保是int32数组
	if orgIds, ok := result["organization_id"].([]interface{}); ok {
		orgIdInts := make([]int32, 0, len(orgIds))
		for _, orgId := range orgIds {
			orgIdInts = append(orgIdInts, cast.ToInt32(orgId))
		}
		result["organization_id"] = orgIdInts
	}

	// 修复all_title字段，确保是字符串数组
	if allTitle, ok := result["all_title"].([]interface{}); ok {
		titleStrs := make([]string, 0, len(allTitle))
		for _, title := range allTitle {
			titleStrs = append(titleStrs, cast.ToString(title))
		}
		result["all_title"] = titleStrs
	}

	// 修复all_domain字段，确保是字符串数组
	if allDomain, ok := result["all_domain"].([]interface{}); ok {
		domainStrs := make([]string, 0, len(allDomain))
		for _, domain := range allDomain {
			domainStrs = append(domainStrs, cast.ToString(domain))
		}
		result["all_domain"] = domainStrs
	}

	// 修复cloud_name字段，确保是字符串数组
	if cloudName, ok := result["cloud_name"].([]interface{}); ok {
		cloudStrs := make([]string, 0, len(cloudName))
		for _, cloud := range cloudName {
			cloudStrs = append(cloudStrs, cast.ToString(cloud))
		}
		result["cloud_name"] = cloudStrs
	}

	// 修复hosts字段，确保是字符串数组
	if hosts, ok := result["hosts"].([]interface{}); ok {
		hostStrs := make([]string, 0, len(hosts))
		for _, host := range hosts {
			hostStrs = append(hostStrs, cast.ToString(host))
		}
		result["hosts"] = hostStrs
	}

	// 修复clue_company_name字段，确保是字符串数组
	if clueCompanyName, ok := result["clue_company_name"].([]interface{}); ok {
		companyStrs := make([]string, 0, len(clueCompanyName))
		for _, company := range clueCompanyName {
			companyStrs = append(companyStrs, cast.ToString(company))
		}
		result["clue_company_name"] = companyStrs
	}

	// 修复端口列表中的数据类型
	if portList, ok := result["port_list"].([]interface{}); ok {
		for i, port := range portList {
			if portMap, ok := port.(map[string]interface{}); ok {
				// 确保port字段是字符串类型
				if portVal, exists := portMap["port"]; exists {
					portMap["port"] = cast.ToString(portVal)
				}

				// 修复cert字段，将对象转换为JSON字符串
				if certVal, exists := portMap["cert"]; exists {
					portMap["cert"] = fixJSONField(certVal)
				}

				// 移除icp字段，不需要处理
				delete(portMap, "icp")

				// 修复logo字段，将对象转换为JSON字符串
				if logoVal, exists := portMap["logo"]; exists {
					portMap["logo"] = fixJSONField(logoVal)
				}

				// 修复reliability_score字段，转换为字符串
				if reliabilityScore, exists := portMap["reliability_score"]; exists {
					portMap["reliability_score"] = cast.ToString(reliabilityScore)
				}

				// 修复is_login字段，转换为字符串
				if isLogin, exists := portMap["is_login"]; exists {
					portMap["is_login"] = cast.ToString(isLogin)
				}

				// 修复http_status_code字段，转换为int32
				if httpStatusCode, exists := portMap["http_status_code"]; exists {
					if httpStatusCode == "" || httpStatusCode == nil {
						portMap["http_status_code"] = 0
					} else {
						portMap["http_status_code"] = cast.ToInt32(httpStatusCode)
					}
				}

				// 修复is_open字段，转换为int32
				if isOpen, exists := portMap["is_open"]; exists {
					if isOpen == nil {
						portMap["is_open"] = 0
					} else {
						portMap["is_open"] = cast.ToInt32(isOpen)
					}
				}

				// 修复online_state字段，转换为int32
				if onlineState, exists := portMap["online_state"]; exists {
					if onlineState == nil {
						portMap["online_state"] = 0
					} else {
						portMap["online_state"] = cast.ToInt32(onlineState)
					}
				}

				// 修复rules中的port字段
				if rules, ok := portMap["rules"].([]interface{}); ok {
					for j, rule := range rules {
						if ruleMap, ok := rule.(map[string]interface{}); ok {
							if portVal, exists := ruleMap["port"]; exists {
								ruleMap["port"] = cast.ToString(portVal)
							}
							rules[j] = ruleMap
						}
					}
					portMap["rules"] = rules
				}

				// 修复reason字段，确保是字符串数组
				if reason, ok := portMap["reason"].([]interface{}); ok {
					reasonStrs := make([]string, 0, len(reason))
					for _, r := range reason {
						if rStr, ok := r.(string); ok {
							reasonStrs = append(reasonStrs, rStr)
						} else {
							// 如果是复杂对象，转换为JSON字符串
							if rBytes, err := json.Marshal(r); err == nil {
								reasonStrs = append(reasonStrs, string(rBytes))
							}
						}
					}
					portMap["reason"] = reasonStrs
				}

				portList[i] = portMap
			}
		}
		result["port_list"] = portList
	}

	// 修复rule_tags中的port字段
	if ruleTags, ok := result["rule_tags"].([]interface{}); ok {
		for i, rule := range ruleTags {
			if ruleMap, ok := rule.(map[string]interface{}); ok {
				if portVal, exists := ruleMap["port"]; exists {
					ruleMap["port"] = cast.ToString(portVal)
				}
				ruleTags[i] = ruleMap
			}
		}
		result["rule_tags"] = ruleTags
	}

	// 修复rules字段（如果存在）
	if rules, ok := result["rules"].([]interface{}); ok {
		for i, rule := range rules {
			if ruleMap, ok := rule.(map[string]interface{}); ok {
				if portVal, exists := ruleMap["port"]; exists {
					ruleMap["port"] = cast.ToString(portVal)
				}
				rules[i] = ruleMap
			}
		}
		result["rules"] = rules
	}

	// 修复geo字段中的lat和lon，将数字转换为字符串
	if geo, ok := result["geo"].(map[string]interface{}); ok {
		// 修复lat字段
		if lat, exists := geo["lat"]; exists {
			geo["lat"] = cast.ToString(lat)
		}
		// 修复lon字段
		if lon, exists := geo["lon"]; exists {
			geo["lon"] = cast.ToString(lon)
		}
		result["geo"] = geo
	}

	// 修复host_list中的数据类型
	if hostList, ok := result["host_list"].([]interface{}); ok {
		for i, host := range hostList {
			if hostMap, ok := host.(map[string]interface{}); ok {
				// 确保port字段是字符串类型
				if portVal, exists := hostMap["port"]; exists {
					hostMap["port"] = cast.ToString(portVal)
				}

				// 修复cert字段，将对象转换为JSON字符串
				if certVal, exists := hostMap["cert"]; exists {
					hostMap["cert"] = fixJSONField(certVal)
				}

				// 移除icp字段，不需要处理
				delete(hostMap, "icp")

				// 修复logo字段，将对象转换为JSON字符串
				if logoVal, exists := hostMap["logo"]; exists {
					hostMap["logo"] = fixJSONField(logoVal)
				}

				delete(hostMap, "reliability_score")
				// 修复is_login字段，转换为字符串
				if isLogin, exists := hostMap["is_login"]; exists {
					hostMap["is_login"] = cast.ToString(isLogin)
				}

				// 修复http_status_code字段，转换为int32
				if httpStatusCode, exists := hostMap["http_status_code"]; exists {
					if httpStatusCode == "" || httpStatusCode == nil {
						hostMap["http_status_code"] = 0
					} else {
						hostMap["http_status_code"] = cast.ToInt32(httpStatusCode)
					}
				}

				// 修复is_open字段，转换为int32
				if isOpen, exists := hostMap["is_open"]; exists {
					if isOpen == nil {
						hostMap["is_open"] = 0
					} else {
						hostMap["is_open"] = cast.ToInt32(isOpen)
					}
				}

				// 修复online_state字段，转换为int32
				if onlineState, exists := hostMap["online_state"]; exists {
					if onlineState == nil {
						hostMap["online_state"] = 0
					} else {
						hostMap["online_state"] = cast.ToInt32(onlineState)
					}
				}

				// 修复reason字段，确保是字符串数组
				if reason, ok := hostMap["reason"].([]interface{}); ok {
					reasonStrs := make([]string, 0, len(reason))
					for _, r := range reason {
						if rStr, ok := r.(string); ok {
							reasonStrs = append(reasonStrs, rStr)
						} else {
							// 如果是复杂对象，转换为JSON字符串
							if rBytes, err := json.Marshal(r); err == nil {
								reasonStrs = append(reasonStrs, string(rBytes))
							}
						}
					}
					hostMap["reason"] = reasonStrs
				}

				hostList[i] = hostMap
			}
		}
		result["host_list"] = hostList
	}
}

// getSubdomain 获取子域名
func getSubdomain(url string, withProtocol bool) string {
	// 处理空URL
	if url == "" {
		return ""
	}

	// 移除协议部分
	protocol := ""
	if strings.HasPrefix(url, "http://") {
		protocol = "http://"
		url = url[7:]
	} else if strings.HasPrefix(url, "https://") {
		protocol = "https://"
		url = url[8:]
	}

	// 移除路径和查询参数
	if idx := strings.Index(url, "/"); idx != -1 {
		url = url[:idx]
	}

	// 移除端口
	if idx := strings.Index(url, ":"); idx != -1 {
		url = url[:idx]
	}

	// 检查是否为IP地址
	if utils.IsIP(url) {
		return ""
	}

	// 添加协议（如果需要）
	if withProtocol && protocol != "" {
		url = protocol + url
	}

	return url
}

// fixJSONField 安全地处理JSON字段，确保返回有效的JSON字符串
func fixJSONField(val interface{}) string {
	if val == nil {
		return ""
	}

	// 如果已经是字符串，验证是否为有效JSON
	if strVal, ok := val.(string); ok {
		// 如果是空字符串，返回空字符串
		if strVal == "" {
			return ""
		}

		// 尝试修复常见的JSON格式错误
		fixedJSON := fixCommonJSONErrors(strVal)

		// 尝试解析JSON以验证格式
		var temp interface{}
		if err := json.Unmarshal([]byte(fixedJSON), &temp); err != nil {
			// 如果JSON格式无效，返回空字符串
			return ""
		}
		return fixedJSON
	}

	// 如果是对象，转换为JSON字符串
	if objVal, ok := val.(map[string]interface{}); ok {
		if jsonBytes, err := json.Marshal(objVal); err == nil {
			return string(jsonBytes)
		}
	}

	// 其他情况，尝试转换为字符串
	strVal := cast.ToString(val)
	if strVal == "" || strVal == "0" {
		return ""
	}

	// 尝试修复常见的JSON格式错误
	fixedJSON := fixCommonJSONErrors(strVal)

	// 验证转换后的字符串是否为有效JSON
	var temp interface{}
	if err := json.Unmarshal([]byte(fixedJSON), &temp); err != nil {
		// 如果不是有效JSON，返回空字符串
		return ""
	}

	return fixedJSON
}

// fixCommonJSONErrors 修复常见的JSON格式错误
func fixCommonJSONErrors(jsonStr string) string {
	// 修复类似 "daull," 这样的错误，移除字符串值内的多余逗号
	// 匹配 "key":"value," 模式，其中value包含逗号但不应该有
	re := regexp.MustCompile(`"([^"]+)":"([^"]*),([^"]*)"`)
	fixed := re.ReplaceAllStringFunc(jsonStr, func(match string) string {
		// 提取key和value部分
		parts := strings.Split(match, `":"`)
		if len(parts) == 2 {
			key := parts[0] + `"`
			value := parts[1]
			// 移除value中的逗号（除了最后的逗号，如果它是JSON结构的一部分）
			if strings.HasSuffix(value, `,"`) {
				// 保留结尾的逗号和引号
				valueContent := value[:len(value)-2]
				valueContent = strings.ReplaceAll(valueContent, ",", "")
				return key + `":"` + valueContent + `"`
			} else if strings.HasSuffix(value, `"`) {
				// 移除value中的所有逗号
				valueContent := value[:len(value)-1]
				valueContent = strings.ReplaceAll(valueContent, ",", "")
				return key + `":"` + valueContent + `"`
			}
		}
		return match
	})

	return fixed
}
