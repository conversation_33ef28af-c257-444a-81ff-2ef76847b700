package detect_assets

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestChangeState 测试状态转换函数
func TestChangeState(t *testing.T) {
	testCases := []struct {
		name     string
		state    int
		expected string
		desc     string
	}{
		{
			name:     "online state",
			state:    1,
			expected: "在线",
			desc:     "在线状态测试",
		},
		{
			name:     "offline state",
			state:    0,
			expected: "离线",
			desc:     "离线状态测试",
		},
		{
			name:     "unknown state negative",
			state:    -1,
			expected: "未知",
			desc:     "负数未知状态测试",
		},
		{
			name:     "unknown state large",
			state:    999,
			expected: "未知",
			desc:     "大数值未知状态测试",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := changeState(tc.state)
			assert.Equal(t, tc.expected, result, "State conversion should match expected value")
			t.Logf("ChangeState test (%s): state=%d, result=%s", tc.desc, tc.state, result)
		})
	}
}

// TestConvertToStringSlice 测试类型转换函数
func TestConvertToStringSlice(t *testing.T) {
	testCases := []struct {
		name     string
		input    []any
		expected []string
		desc     string
	}{
		{
			name:     "empty slice",
			input:    []any{},
			expected: []string{},
			desc:     "空切片测试",
		},
		{
			name:     "string slice",
			input:    []any{"test1", "test2", "test3"},
			expected: []string{"test1", "test2", "test3"},
			desc:     "字符串切片测试",
		},
		{
			name:     "mixed types",
			input:    []any{"test1", 123, "test2", true, "test3"},
			expected: []string{"test1", "test2", "test3"},
			desc:     "混合类型测试",
		},
		{
			name:     "non-string types",
			input:    []any{123, 456, true, false},
			expected: []string{},
			desc:     "非字符串类型测试",
		},
		{
			name:     "nil values",
			input:    []any{"test1", nil, "test2"},
			expected: []string{"test1", "test2"},
			desc:     "包含nil值测试",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := convertToStringSlice(tc.input)
			assert.Equal(t, tc.expected, result, "String slice conversion should match expected")
			t.Logf("ConvertToStringSlice test (%s): input=%v, result=%v", tc.desc, tc.input, result)
		})
	}
}

// TestGetMapValueSafe 测试安全获取map值函数
func TestGetMapValueSafe(t *testing.T) {
	testCases := []struct {
		name     string
		input    interface{}
		key      string
		expected interface{}
		desc     string
	}{
		{
			name:     "nil map",
			input:    nil,
			key:      "test",
			expected: "",
			desc:     "空map测试",
		},
		{
			name:     "valid map with existing key",
			input:    map[string]interface{}{"test": "value", "number": 123},
			key:      "test",
			expected: "value",
			desc:     "有效map存在键测试",
		},
		{
			name:     "valid map with non-existing key",
			input:    map[string]interface{}{"test": "value"},
			key:      "missing",
			expected: "",
			desc:     "有效map不存在键测试",
		},
		{
			name:     "non-map type",
			input:    "not a map",
			key:      "test",
			expected: "",
			desc:     "非map类型测试",
		},
		{
			name:     "empty map",
			input:    map[string]interface{}{},
			key:      "test",
			expected: "",
			desc:     "空map测试",
		},
		{
			name:     "map with nil value",
			input:    map[string]interface{}{"test": nil},
			key:      "test",
			expected: nil,
			desc:     "map包含nil值测试",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := getMapValueSafe(tc.input, tc.key)
			assert.Equal(t, tc.expected, result, "Map value should match expected")
			t.Logf("GetMapValueSafe test (%s): input=%v, key=%s, result=%v", tc.desc, tc.input, tc.key, result)
		})
	}
}

// TestCopyQuery 测试查询条件复制函数
func TestCopyQuery(t *testing.T) {
	testCases := []struct {
		name  string
		input [][]interface{}
		desc  string
	}{
		{
			name:  "empty query",
			input: [][]interface{}{},
			desc:  "空查询条件测试",
		},
		{
			name:  "single condition",
			input: [][]interface{}{{"field", "value"}},
			desc:  "单个条件测试",
		},
		{
			name:  "multiple conditions",
			input: [][]interface{}{{"field1", "value1"}, {"field2", "in", []interface{}{"a", "b"}}},
			desc:  "多个条件测试",
		},
		{
			name:  "complex conditions",
			input: [][]interface{}{{"field1", "like", "value%"}, {"field2", ">=", 100}, {"field3", "in", []interface{}{1, 2, 3}}},
			desc:  "复杂条件测试",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := copyQuery(tc.input)

			// 验证长度相同
			assert.Equal(t, len(tc.input), len(result), "Copied query should have same length")

			// 验证内容相同但不是同一个引用
			for i, condition := range tc.input {
				if i < len(result) {
					assert.Equal(t, len(condition), len(result[i]), "Condition length should match")
					for j, value := range condition {
						if j < len(result[i]) {
							assert.Equal(t, value, result[i][j], "Condition value should match")
						}
					}
					// 验证不是同一个引用
					if len(condition) > 0 && len(result[i]) > 0 {
						assert.NotSame(t, &condition, &result[i], "Should be different references")
					}
				}
			}

			t.Logf("CopyQuery test (%s): input length=%d, result length=%d", tc.desc, len(tc.input), len(result))
		})
	}
}

// TestHelperFunctions 测试辅助函数
func TestHelperFunctions(t *testing.T) {
	t.Run("状态转换函数", func(t *testing.T) {
		// 测试所有可能的状态值
		states := []int{-1, 0, 1, 2, 999}
		for _, state := range states {
			result := changeState(state)
			assert.NotEmpty(t, result, "State conversion should return non-empty string")
			t.Logf("State %d -> %s", state, result)
		}
	})

	t.Run("类型转换函数", func(t *testing.T) {
		// 测试各种数据类型
		testData := []any{
			"string",
			123,
			123.45,
			true,
			false,
			nil,
			[]string{"nested", "array"},
			map[string]interface{}{"key": "value"},
		}

		result := convertToStringSlice(testData)
		assert.NotNil(t, result, "Result should not be nil")
		// 只有字符串类型应该被保留
		assert.Equal(t, 1, len(result), "Only string values should be kept")
		assert.Equal(t, "string", result[0], "String value should be preserved")
	})

	t.Run("安全map访问函数", func(t *testing.T) {
		// 测试各种map类型
		testMaps := []interface{}{
			nil,
			"not a map",
			123,
			map[string]interface{}{"key": "value"},
			map[string]interface{}{},
			map[string]interface{}{"nil_value": nil},
		}

		for i, testMap := range testMaps {
			result := getMapValueSafe(testMap, "key")
			t.Logf("Map test %d: input=%v, result=%v", i, testMap, result)
			// 所有情况都应该返回某个值（可能是空字符串或nil）
			// 不应该panic
		}
	})
}

// TestEdgeCases 边界情况测试
func TestEdgeCases(t *testing.T) {
	t.Run("空输入处理", func(t *testing.T) {
		// 测试空字符串切片转换
		result := convertToStringSlice([]any{})
		assert.Empty(t, result, "Empty input should return empty result")

		// 测试空查询复制
		copied := copyQuery([][]interface{}{})
		assert.Empty(t, copied, "Empty query should return empty result")

		// 测试nil map访问
		value := getMapValueSafe(nil, "any_key")
		assert.Equal(t, "", value, "Nil map should return empty string")
	})

	t.Run("极值输入处理", func(t *testing.T) {
		// 测试极大状态值
		result := changeState(999999)
		assert.Equal(t, "未知", result, "Large state value should return unknown")

		// 测试极小状态值
		result = changeState(-999999)
		assert.Equal(t, "未知", result, "Small state value should return unknown")
	})

	t.Run("异常输入处理", func(t *testing.T) {
		// 测试包含特殊字符的字符串
		specialStrings := []any{"", " ", "\n", "\t", "特殊字符", "🚀", "null", "undefined"}
		result := convertToStringSlice(specialStrings)
		assert.Equal(t, len(specialStrings), len(result), "All strings should be preserved")

		// 测试复杂嵌套结构
		complexData := []any{
			map[string]interface{}{
				"nested": map[string]interface{}{
					"deep": "value",
				},
			},
			[]interface{}{1, 2, 3},
		}
		result = convertToStringSlice(complexData)
		assert.Empty(t, result, "Complex nested data should not be converted to strings")
	})
}
