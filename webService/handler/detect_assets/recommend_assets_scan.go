package detect_assets

import (
	"context"
	"database/sql"
	"fmt"
	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/forbid"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/middleware/mysql/port_group"
	"micro-service/middleware/mysql/task"
	user2 "micro-service/middleware/mysql/user"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dns"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	"micro-service/pkg/microx"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	scan "micro-service/scanService/proto"
	pb "micro-service/webService/proto"
	"slices"
	"strconv"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
)

func ScanRecommend(ctx context.Context, req *pb.ScanRecommendRequest, rsp *pb.ScanRecommendResponse) error {
	log.Infof("[ScanRecommend] 开始处理推荐资产扫描任务 - 用户ID: %d, 任务ID: %d, 操作公司ID: %d", req.UserId, req.Id, req.OperateCompanyId)

	// 1. 查询任务
	t, err := detect_assets_tasks.NewModel().First(
		mysql.WithWhere("user_id = ?", req.UserId),
		mysql.WithWhere("id = ?", req.Id),
	)
	if err != nil || t == nil || t.ID == 0 {
		log.Errorf("[ScanRecommend] 查询任务失败 - 用户ID: %d, 任务ID: %d, 错误: %v", req.UserId, req.Id, err)
		rsp.WarnMessage = "参数错误!"
		return fmt.Errorf("参数错误")
	}
	log.Infof("[ScanRecommend] 任务查询成功 - 用户ID: %d, 任务ID: %d, 任务名称: %s, 扩展标识: %s", req.UserId, req.Id, t.Name, t.ExpendFlags)

	user, err := mysql.NewDSL[user2.User]().FindByID(req.UserId)
	if err != nil {
		rsp.WarnMessage = "获取用户信息失败"
		return fmt.Errorf("获取用户信息失败")
	}

	var companyInfo company.Company
	if req.OperateCompanyId > 0 {
		companyInfo, err = mysql.NewDSL[company.Company]().FindByID(uint64(req.OperateCompanyId))
		if err != nil {
			rsp.WarnMessage = "获取公司信息失败"
			return fmt.Errorf("获取公司信息失败")
		}
	}

	// 2. 检查是否已下发过扫描任务
	if t.StepDetail == detect_assets_tasks.StepFourScan {
		rsp.WarnMessage = "当前任务已经下发过扫描任务了，不允许重新下发!"
		return fmt.Errorf("当前任务已经下发过扫描任务了，不允许重新下发")
	}

	// 3. 组装扫描任务参数
	scanName := "单位资产测绘-扫描任务(" + time.Now().Format("2006-01-02 15:04:05") + ")"
	if t.ExpandSource != 0 {
		scanName = "云端推荐任务-扫描任务(" + time.Now().Format("2006-01-02 15:04:05") + ")"
	}
	bandwidth, _ := strconv.Atoi(t.Bandwidth)
	if bandwidth == 0 {
		bandwidth = 1000
	}
	protocolConcurrency := (bandwidth + 7) / 8 // 向上取整

	// 4. 端口组逻辑
	scanType := t.ScanType //
	portRange := task.PORT_RANGE_PROBE
	var dataPorts uint64 = 0
	if scanType == task.LIST_PORT {
		portRange = task.PORT_RANGE_PROBE
	} else {
		portRange = 0
		if scanType == task.NORMAL_PORT {
			// 查找常用端口组
			row, _ := mysql.NewDSL[port_group.PortGroup]().One(&mysql.QueryBuilder{
				Table: "port_groups",
				Where: []mysql.Condition{mysql.CompareCond{Field: "user_id", Operator: "=", Value: req.UserId}, mysql.CompareCond{Field: "name", Operator: "=", Value: "全部常用端口"}},
			})
			if row.Id == 0 {
				row, _ = mysql.NewDSL[port_group.PortGroup]().One(&mysql.QueryBuilder{
					Table: "port_groups",
					Where: []mysql.Condition{mysql.CompareCond{Field: "user_id", Operator: "=", Value: req.UserId}, mysql.CompareCond{Field: "name", Operator: "=", Value: "现有端口组"}},
				})
			}
			dataPorts = row.Id
		} else {
			row, _ := mysql.NewDSL[port_group.PortGroup]().One(&mysql.QueryBuilder{
				Table: "port_groups",
				Where: []mysql.Condition{mysql.CompareCond{Field: "user_id", Operator: "=", Value: req.UserId}, mysql.CompareCond{Field: "name", Operator: "=", Value: "0-65535"}},
			})
			dataPorts = row.Id
		}
		if dataPorts == 0 {
			rsp.WarnMessage = "您的账号不存在常用端口分组，无法下发资产扫描任务，请联系售后人员！"
			return fmt.Errorf("您的账号不存在常用端口分组，无法下发资产扫描任务，请联系售后人员！")
		}
	}

	// 5. 安服扫描标记
	taskFrom := 0
	// 如果user_id不一致，则标记为1
	if t.SafeUserId > 0 && t.UserId != t.SafeUserId {
		taskFrom = 1
	}
	currentUserID, err := metadata.GetInt64(ctx, "user_id")
	if err != nil {
		currentUserID = int64(req.UserId)
	}

	var scanTask = task.Task{}
	scanTask.UserID = req.UserId
	scanTask.CompanyID = utils.ToPointer(uint64(req.OperateCompanyId))
	scanTask.Name = scanName
	scanTask.Bandwidth = strconv.Itoa(bandwidth)
	scanTask.ProtocolConcurrency = strconv.Itoa(protocolConcurrency)
	scanTask.PingSwitch = 0
	scanTask.WebLogoSwitch = 0
	scanTask.TaskType = 1
	scanTask.ScanRange = utils.ToPointer(int8(0))
	scanTask.IPType = 1
	scanTask.AssetType = 1
	maxOrder, _ := mysql.NewDSL[task.Task]().MaxByParams("order", [][]interface{}{})
	scanTask.Order = uint64(maxOrder) + 1
	scanTask.CreatedAt = time.Now()
	scanTask.OpID = int64(currentUserID)
	scanTask.IsDefinePort = 0
	scanTask.DetectAssetsTasksID = utils.ToPointer(uint64(t.ID))
	scanTask.Flag = utils.ToPointer(t.ExpendFlags)
	scanTask.TaskFrom = int8(taskFrom)
	scanTask.PortRange = utils.ToPointer(int8(portRange))
	scanTask.ScanType = 1

	// 6. 校验推荐资产是否存在
	assetCount, err := es.CountByParams[recommend_result.RecommendResult]([][]interface{}{
		{"MUST", [][]interface{}{
			{"user_id", "=", req.UserId},
			{"flag", "=", t.ExpendFlags},
		}},
	})
	if err != nil {
		rsp.WarnMessage = "校验推荐资产是否存在失败"
		return fmt.Errorf("校验推荐资产是否存在失败")
	}
	if assetCount == 0 {
		rsp.WarnMessage = "当前测绘任务不存在资产，无法下发资产扫描任务!"
		return fmt.Errorf("当前测绘任务不存在资产，无法下发资产扫描任务")
	}

	// 7. 授权限制校验
	if req.OperateCompanyId > 0 {
		limit, err := company.CheckLimitById(req.OperateCompanyId, "ip_asset", 1)
		if err != nil {
			rsp.WarnMessage = "校验授权限制失败"
			return fmt.Errorf("校验授权限制失败")
		}
		if limit < 0 {
			rsp.WarnMessage = "您的台账数据已经超过限制，请联系售后人员！"
			return fmt.Errorf("您的台账数据已经超过限制，请联系售后人员")
		}
	}

	// 8.获取域名、子域名
	log.Infof("[ScanRecommend] 用户ID: %d - 开始获取域名和子域名线索", req.UserId)
	pluck := mysql.NewPluckResult[clues.Clue, string]()
	topDomains, err := pluck.PluckByParams("content", [][]interface{}{
		{"user_id", "=", req.UserId},
		{"group_id", "=", t.GroupId},
		{"status", "=", clues.CLUE_PASS_STATUS},
		{"is_deleted", "=", clues.NOT_DELETE},
		{"type", "in", []interface{}{clues.TYPE_DOMAIN}},
	})
	if err != nil {
		log.Errorf("[ScanRecommend] 用户ID: %d - 获取域名失败: %v", req.UserId, err)
		rsp.WarnMessage = "获取域名失败"
		return fmt.Errorf("获取域名失败")
	}
	log.Infof("[ScanRecommend] 用户ID: %d - 获取到顶级域名数量: %d, 域名列表: %v", req.UserId, len(topDomains), topDomains)

	subDomains, err := pluck.PluckByParams("content", [][]interface{}{
		{"user_id", "=", req.UserId},
		{"group_id", "=", t.GroupId},
		{"status", "=", clues.CLUE_PASS_STATUS},
		{"is_deleted", "=", clues.NOT_DELETE},
		{"type", "in", []interface{}{clues.TYPE_SUBDOMAIN}},
	})
	if err != nil {
		log.Errorf("[ScanRecommend] 用户ID: %d - 获取子域名失败: %v", req.UserId, err)
		rsp.WarnMessage = "获取子域名失败"
		return fmt.Errorf("获取子域名失败")
	}
	log.Infof("[ScanRecommend] 用户ID: %d - 获取到子域名数量: %d, 子域名列表: %v", req.UserId, len(subDomains), subDomains)

	// 9. 获取所有IPv6资产
	ipv6All, err := es.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
		{"MUST", [][]interface{}{
			{"user_id", "=", req.UserId},
			{"flag", "=", t.ExpendFlags},
			{"is_ipv6", "=", true},
		}},
	}, nil, "ip")
	if err != nil {
		rsp.WarnMessage = "获取IPv6资产失败"
		return fmt.Errorf("获取IPv6资产失败")
	}
	// 获取所有IPV4资产
	// 获取禁扫IP
	pluckForbiddenIps := mysql.NewPluckResult[forbid.ForbidIps, string]()
	forbiddenIps, err := pluckForbiddenIps.PluckByParams("ip_segment", [][]interface{}{
		{"user_id", "=", req.UserId},
	})
	if err != nil {
		rsp.WarnMessage = "获取禁扫IP失败"
		return fmt.Errorf("获取禁扫IP失败")
	}
	var paramsForIPV4 = [][]interface{}{}
	if len(forbiddenIps) > 0 {
		paramsForIPV4 = append(paramsForIPV4, []interface{}{
			"MUST_NOT", [][]interface{}{
				{"ip", "in", forbiddenIps},
			}}, []interface{}{
			"MUST", [][]interface{}{
				{"user_id", "=", req.UserId},
				{"flag", "=", t.ExpendFlags},
				{"is_ipv6", "=", false},
			}})
	} else {
		paramsForIPV4 = append(paramsForIPV4, []interface{}{
			"MUST", [][]interface{}{
				{"user_id", "=", req.UserId},
				{"flag", "=", t.ExpendFlags},
				{"is_ipv6", "=", false},
			}})
	}
	ipv4All, err := es.AllByParams[recommend_result.RecommendResult](1000, paramsForIPV4, nil, "ip")
	if err != nil {
		rsp.WarnMessage = "获取IPV4资产失败"
		return fmt.Errorf("获取IPV4资产失败")
	}
	ipv4s := utils.ListColumn[string, *recommend_result.RecommendResult](ipv4All, func(v *recommend_result.RecommendResult) string {
		return v.Ip
	})
	ipv4s = utils.ListDistinct(ipv4s)
	log.Infof("[IP数量计算] 用户ID: %d - 初始IPv4资产数量: %d", req.UserId, len(ipv4s))

	if len(ipv4s) == 0 && len(ipv6All) == 0 {
		log.Warnf("[ScanRecommend] 用户ID: %d - 没有需要扫描的IP数据", req.UserId)
		rsp.WarnMessage = "没有需要扫描的IP数据!"
		return fmt.Errorf("没有需要扫描的IP数据")
	}

	ipv6s := utils.ListColumn[string, *recommend_result.RecommendResult](ipv6All, func(v *recommend_result.RecommendResult) string {
		return v.Ip
	})
	ipv6s = utils.ListDistinct(ipv6s)
	log.Infof("[IP数量计算] 用户ID: %d - 初始IPv6资产数量: %d", req.UserId, len(ipv6s))
	// 10. 获取所有域名解析出来的IPv6
	allSubDomainRows, err := es.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
		{"MUST", [][]interface{}{
			{"user_id", "=", req.UserId},
			{"flag", "=", t.ExpendFlags},
			{"domain", "in", topDomains},
		}},
	}, nil, "ip", "subdomain")
	if err != nil {
		rsp.WarnMessage = "获取域名解析出来的IPv6失败"
		return fmt.Errorf("获取域名解析出来的IPv6失败")
	}
	allSubDomains := utils.ListColumn[string, *recommend_result.RecommendResult](allSubDomainRows, func(v *recommend_result.RecommendResult) string {
		return v.Subdomain
	})
	// 原PHP代码逻辑有问题，实际是要查子域名的子域名，这里暂时不处理
	if len(subDomains) > 0 {
		allSubDomainRows, err = es.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
			{"MUST", [][]interface{}{
				{"user_id", "=", req.UserId},
				{"flag", "=", t.ExpendFlags},
				{"domain", "in", subDomains},
			}},
		}, []elastic.Sorter{elastic.NewFieldSort("id").Desc()}, "ip", "subdomain")
		if err != nil {
			rsp.WarnMessage = "获取域名解析出来的IPv6失败"
			return fmt.Errorf("获取域名解析出来的IPv6失败")
		}
		allSubDomains = append(allSubDomains, utils.ListColumn[string, *recommend_result.RecommendResult](allSubDomainRows, func(v *recommend_result.RecommendResult) string {
			return v.Subdomain
		})...)
	}
	allSubDomains = utils.ListDistinct(allSubDomains)
	log.Infof("[ScanRecommend] 用户ID: %d - 获取到所有子域名数量: %d", req.UserId, len(allSubDomains))
	if len(allSubDomains) > 0 {
		log.Infof("[ScanRecommend] 用户ID: %d - 所有子域名列表: %v", req.UserId, allSubDomains)
	}

	ipv6Domain := make([]string, 0)
	ipV6DomainHasNsRecord := make([]string, 0)
	if len(allSubDomains) > 0 {
		log.Infof("[ScanRecommend] 用户ID: %d - 开始解析子域名的IPv6记录", req.UserId)
		for _, subDomain := range allSubDomains {
			records := dns.GetAAAARecords(subDomain)
			if len(records) == 0 {
				continue
			}
			ipv6Domain = append(ipv6Domain, records...)
			ipV6DomainHasNsRecord = append(ipV6DomainHasNsRecord, subDomain)
		}
	}
	log.Infof("[IP数量计算] 用户ID: %d - 域名解析IPv6数量: %d", req.UserId, len(ipv6Domain))

	// 解析IPv4地址
	ipv4Domain := make([]string, 0)
	ipV4DomainHasNsRecord := make([]string, 0)
	if len(allSubDomains) > 0 {
		log.Infof("[ScanRecommend] 用户ID: %d - 开始解析子域名的IPv4记录", req.UserId)
		for _, subDomain := range allSubDomains {
			records := dns.GetARecords(subDomain)
			if len(records) == 0 {
				continue
			}
			ipv4Domain = append(ipv4Domain, records...)
			ipV4DomainHasNsRecord = append(ipV4DomainHasNsRecord, subDomain)
		}
	}
	log.Infof("[IP数量计算] 用户ID: %d - 域名解析IPv4数量: %d", req.UserId, len(ipv4Domain))

	var originIpNum = 0
	var scanIpNum = 0
	var hasIpAsset = false
	var allIPv6 = utils.ListDistinctNonZero(append(ipv6s, ipv6Domain...))
	log.Infof("[IP数量计算] 用户ID: %d - 合并后所有IPv6数量: %d (原始IPv6: %d + 域名解析IPv6: %d)", req.UserId, len(allIPv6), len(ipv6s), len(ipv6Domain))
	if req.OperateCompanyId > 0 && len(allIPv6) > 0 {
		originIpNum = len(ipv6s)
		// 计算剩余可用数量（不包含当前要扫描的数量）
		var leftNum = companyInfo.LimitIpAsset - companyInfo.UsedIpAsset
		log.Infof("[IP数量计算-公司IPv6] 原始IPv6数量: %d, 公司限制: %d, 已使用: %d, 剩余可用: %d, 合并IPv6总数: %d", originIpNum, companyInfo.LimitIpAsset, companyInfo.UsedIpAsset, leftNum, len(allIPv6))

		if leftNum <= 0 {
			log.Warnf("[IP数量计算-公司IPv6] 剩余数量不足，无法下发扫描任务")
			rsp.WarnMessage = "当前测绘任务的资产入账扫描的数量已经超过系统授权IP数量，无法下发扫描任务!"
			return fmt.Errorf("当前测绘任务的资产入账扫描的数量已经超过系统授权IP数量，无法下发扫描任务")
		} else if len(allIPv6) > leftNum {
			// 要扫描的数量超过剩余数量，使用剩余数量*1.2倍进行下发
			targetNum := leftNum * 12 / 10
			if targetNum > len(allIPv6) {
				targetNum = len(allIPv6)
			}
			log.Infof("[IP数量计算-公司IPv6] 要扫描数量(%d)超过剩余数量(%d)，使用1.2倍下发，目标数量: %d", len(allIPv6), leftNum, targetNum)

			ipv6s = allIPv6[0:targetNum]
			hasIpAsset = true
			scanIpNum = len(ipv6s)
			log.Infof("[IP数量计算-公司IPv6] 最终IPv6扫描数量: %d", scanIpNum)
		} else {
			// 要扫描的数量在剩余范围内，直接使用
			ipv6s = allIPv6
			hasIpAsset = true
			scanIpNum = len(ipv6s)
			log.Infof("[IP数量计算-公司IPv6] 要扫描数量(%d)在剩余范围内(%d)，直接使用，最终IPv6扫描数量: %d", len(allIPv6), leftNum, scanIpNum)
		}
	} else if len(ipv6s) > 0 && cfg.LoadAPP().IsLocalForadar {
		hasIpAsset = true
		originIpNum = len(ipv6s)
		limitIpAsset, _ := strconv.Atoi(user.LimitIPAsset)

		//                     $usedNum = IpAssets::query()->where('user_id',$user_id)->whereIn('status',[1,4])->count();
		usedNum, err := es.CountByParams[fofaee_assets.FofaeeAssets]([][]interface{}{
			{"MUST", [][]interface{}{
				{"user_id", "=", req.UserId},
				{"status", "in", []interface{}{1, 4}},
			}},
		})
		if err != nil {
			rsp.WarnMessage = "获取已入账资产数量失败"
			return fmt.Errorf("获取已入账资产数量失败")
		}

		// 计算剩余可用数量（不包含当前要扫描的数量）
		leftNum := limitIpAsset - int(usedNum)
		log.Infof("[IP数量计算-本地IPv6] 原始IPv6数量: %d, 用户限制: %d, 已使用: %d, 剩余可用: %d", originIpNum, limitIpAsset, usedNum, leftNum)

		if leftNum <= 0 {
			log.Warnf("[IP数量计算-本地IPv6] 剩余数量不足，无法下发扫描任务")
			rsp.WarnMessage = "当前测绘任务的资产入账扫描的数量已经超过系统授权IP数量，无法下发扫描任务!"
			return fmt.Errorf("当前测绘任务的资产入账扫描的数量已经超过系统授权IP数量，无法下发扫描任务")
		} else if len(ipv6s) > leftNum {
			// 要扫描的数量超过剩余数量，使用剩余数量*1.2倍进行下发
			targetNum := leftNum * 12 / 10
			if targetNum > len(ipv6s) {
				targetNum = len(ipv6s)
			}
			log.Infof("[IP数量计算-本地IPv6] 要扫描数量(%d)超过剩余数量(%d)，使用1.2倍下发，目标数量: %d", len(ipv6s), leftNum, targetNum)

			ipv6s = ipv6s[0:targetNum]
			scanIpNum = len(ipv6s)
			log.Infof("[IP数量计算-本地IPv6] 最终IPv6扫描数量: %d", scanIpNum)
		} else {
			// 要扫描的数量在剩余范围内，直接使用
			scanIpNum = len(ipv6s)
			log.Infof("[IP数量计算-本地IPv6] 要扫描数量(%d)在剩余范围内(%d)，直接使用，最终IPv6扫描数量: %d", len(ipv6s), leftNum, scanIpNum)
		}
	}

	if len(ipv6s) > 0 || len(ipv6Domain) > 0 {
		scanTask.IPType = task.IP_TYPE_V6 // IpV6
		scanTask.ScanType = task.SCAN_TYPE_ACCURATE
		hasInfo := false
		if len(ipv6s) == 0 && len(ipv6Domain) > 0 {
			chunks := slices.Chunk(ipv6Domain, 1000)
			for scanIpArr := range chunks {
				scanTask.Name += "-IPV6"
				st := scanTask
				t, err := mysql.NewDSL[task.Task]().Create(st)
				if err != nil {
					rsp.WarnMessage = "创建IPv6扫描任务失败"
					return fmt.Errorf("创建IPv6扫描任务失败")
				}
				var taskProbeInfos = make([]task.TaskProbeInfo, 0)
				var taskIps = make([]task.TaskIps, 0)
				for _, ip := range scanIpArr {
					taskProbeInfos = append(taskProbeInfos, task.TaskProbeInfo{
						TaskId: uint64(t.ID),
						Ip: sql.NullString{
							String: dns.CompleteIPv6(ip),
							Valid:  true,
						},
						Port:         80,
						BaseProtocol: "tcp",
						CreatedAt: sql.NullTime{
							Time:  time.Now(),
							Valid: true,
						},
					})
					taskIps = append(taskIps, task.TaskIps{
						TaskId: uint64(t.ID),
						Ip: sql.NullString{
							String: dns.CompleteIPv6(ip),
							Valid:  true,
						},
						CreatedAt: sql.NullTime{
							Time:  time.Now(),
							Valid: true,
						},
					})
				}
				if scanType == task.NORMAL_PORT || scanType == task.ALL_PORT {
					_, err := mysql.NewDSL[task.TaskIps]().BatchCreate(taskIps)
					if err != nil {
						rsp.WarnMessage = "创建IPv6扫描任务IP失败"
						return fmt.Errorf("创建IPv6扫描任务IP失败")
					}
					taskPorts := task.TaskPorts{
						TaskId:    uint64(t.ID),
						PortsType: `App\Models\MySql\PortGroup`,
						PortsId:   dataPorts,
						CreatedAt: sql.NullTime{
							Time:  time.Now(),
							Valid: true,
						},
						UpdatedAt: sql.NullTime{
							Time:  time.Now(),
							Valid: true,
						},
					}
					_, err = mysql.NewDSL[task.TaskPorts]().Create(taskPorts)
					if err != nil {
						rsp.WarnMessage = "创建IPv6扫描任务端口失败"
						return fmt.Errorf("创建IPv6扫描任务端口失败")
					}
				} else {
					_, err := mysql.NewDSL[task.TaskProbeInfo]().BatchCreate(taskProbeInfos)
					if err != nil {
						rsp.WarnMessage = "创建IPv6扫描任务探活失败"
						return fmt.Errorf("创建IPv6扫描任务探活失败")
					}
				}
				err = createTaskHosts(req.UserId, t, scanIpArr, ipV6DomainHasNsRecord, topDomains)
				if err != nil {
					rsp.WarnMessage = "创建IPv6扫描任务主机失败"
					return fmt.Errorf("创建IPv6扫描任务主机失败")
				}
				if cfg.ExecGolangJob() {
					// 下发扫描任务到MQ - 对应PHP的ScanAssetMqJob::publish
					asyncq.Enqueue(context.Background(), asyncq.ScanAssetJob, &asyncq.TaskIdPayload{
						TaskId: uint64(t.ID),
						UserId: req.UserId,
					})
				} else {
					//调用php的job --DispatchGolangJobToPhpJob
					err = asyncq.DispatchGolangJobToPhpJob.Dispatch(uint64(t.ID))
				}
			}
		} else {
			chunks := slices.Chunk(ipv6s, 1000)
			for scanIpArr := range chunks {
				st := scanTask
				st.Name += "-IPV6"
				t, err := mysql.NewDSL[task.Task]().Create(st)
				if err != nil {
					rsp.WarnMessage = "创建IPv6扫描任务失败"
					return fmt.Errorf("创建IPv6扫描任务失败")
				}
				exists, err := es.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
					{"MUST", [][]interface{}{
						{"user_id", "=", req.UserId},
						{"is_ipv6", "=", true},
						{"ip", "in", scanIpArr},
					}},
				}, nil, "ip", "port", "base_protocol")
				if err != nil {
					rsp.WarnMessage = "获取IPv6资产失败"
					return fmt.Errorf("获取IPv6资产失败")
				}
				var taskProbeInfos = make([]task.TaskProbeInfo, 0)
				var taskIps = make([]task.TaskIps, 0)
				if len(exists) > 0 {
					var uniqueMap = make(map[string]struct{})
					for _, v := range exists {
						p := "tcp"
						if len(v.BaseProtocol) > 0 {
							p = v.BaseProtocol
						}
						key := fmt.Sprintf("%s:%d:%s", v.Ip, cast.ToInt(v.Port), p)
						if _, ok := uniqueMap[key]; ok {
							continue
						}
						uniqueMap[key] = struct{}{}
						taskProbeInfos = append(taskProbeInfos, task.TaskProbeInfo{
							TaskId: uint64(t.ID),
							Ip: sql.NullString{
								String: v.Ip,
								Valid:  true,
							},
							Port:         cast.ToInt(v.Port),
							BaseProtocol: p,
							CreatedAt: sql.NullTime{
								Time:  time.Now(),
								Valid: true,
							},
						})
					}
				}
				// 先查询需要更新的IPv6文档，获取文档ID
				documentsToUpdate, err := es.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
					{"MUST", [][]interface{}{
						{"user_id", "=", req.UserId},
						{"flag", "=", t.Flag},
						{"ip", "in", scanIpArr},
						{"is_ipv6", "=", true},
					}},
				}, nil, "id")
				if err != nil {
					log.Errorf("[更新IPv6资产] 查询待更新文档失败，用户ID: %d, 错误: %v", req.UserId, err)
					rsp.WarnMessage = "查询IPv6资产失败"
					return fmt.Errorf("查询IPv6资产失败: %v", err)
				}

				if len(documentsToUpdate) > 0 {
					// 使用批量更新方式
					var updateParams []*recommend_result.UpdateAnyParam
					for _, doc := range documentsToUpdate {
						updateParams = append(updateParams, &recommend_result.UpdateAnyParam{
							Id: doc.Id,
							Data: map[string]interface{}{
								"task_id": t.ID,
							},
						})
					}

					// 执行批量更新
					err = recommend_result.NewRecommendResultModel().UpdatesAny(updateParams)
					if err != nil {
						log.Errorf("[更新IPv6资产] 批量更新失败，用户ID: %d, 文档数量: %d, 错误: %v", req.UserId, len(updateParams), err)
						rsp.WarnMessage = "更新IPv6资产失败"
						return fmt.Errorf("更新IPv6资产失败: %v", err)
					}

					log.Infof("[更新IPv6资产] 成功更新文档数量: %d, 用户ID: %d, TaskID: %d", len(updateParams), req.UserId, t.ID)
				} else {
					log.Warnf("[更新IPv6资产] 没有找到需要更新的文档，用户ID: %d, scanIpArr大小: %d", req.UserId, len(scanIpArr))
				}
				if len(ipv6Domain) > 0 && !hasInfo {
					for _, ip := range ipv6Domain {
						taskProbeInfos = append(taskProbeInfos, task.TaskProbeInfo{
							TaskId: uint64(t.ID),
							Ip: sql.NullString{
								String: dns.CompleteIPv6(ip),
								Valid:  true,
							},
							Port:         80,
							BaseProtocol: "tcp",
							CreatedAt: sql.NullTime{
								Time:  time.Now(),
								Valid: true,
							},
							UpdatedAt: sql.NullTime{
								Time:  time.Now(),
								Valid: true,
							},
						})
						taskProbeInfos = append(taskProbeInfos, task.TaskProbeInfo{
							TaskId: uint64(t.ID),
							Ip: sql.NullString{
								String: dns.CompleteIPv6(ip),
								Valid:  true,
							},
							Port:         443,
							BaseProtocol: "tcp",
							CreatedAt: sql.NullTime{
								Time:  time.Now(),
								Valid: true,
							},
							UpdatedAt: sql.NullTime{
								Time:  time.Now(),
								Valid: true,
							},
						})
						taskIps = append(taskIps, task.TaskIps{
							TaskId: uint64(t.ID),
							Ip: sql.NullString{
								String: dns.CompleteIPv6(ip),
								Valid:  true,
							},
							CreatedAt: sql.NullTime{
								Time:  time.Now(),
								Valid: true,
							},
						})
					}
					hasInfo = true
				}
				if scanType == task.NORMAL_PORT || scanType == task.ALL_PORT {
					for _, ip := range scanIpArr {
						taskIps = append(taskIps, task.TaskIps{
							TaskId: uint64(t.ID),
							Ip: sql.NullString{
								String: dns.CompleteIPv6(ip),
								Valid:  true,
							},
							CreatedAt: sql.NullTime{
								Time:  time.Now(),
								Valid: true,
							},
						})
					}
					_, err := mysql.NewDSL[task.TaskIps]().BatchCreate(taskIps)
					if err != nil {
						rsp.WarnMessage = "创建IPv6扫描任务IP失败"
						return fmt.Errorf("创建IPv6扫描任务IP失败")
					}
					taskPorts := task.TaskPorts{
						TaskId:    uint64(t.ID),
						PortsType: `App\Models\MySql\PortGroup`,
						PortsId:   dataPorts,
						CreatedAt: sql.NullTime{
							Time:  time.Now(),
							Valid: true,
						},
						UpdatedAt: sql.NullTime{
							Time:  time.Now(),
							Valid: true,
						},
					}
					_, err = mysql.NewDSL[task.TaskPorts]().Create(taskPorts)
					if err != nil {
						rsp.WarnMessage = "创建IPv6扫描任务端口失败"
						return fmt.Errorf("创建IPv6扫描任务端口失败")
					}
				} else {
					_, err := mysql.NewDSL[task.TaskProbeInfo]().BatchCreate(taskProbeInfos)
					if err != nil {
						rsp.WarnMessage = "创建IPv6扫描任务探活失败"
						return fmt.Errorf("创建IPv6扫描任务探活失败")
					}
				}
				err = createTaskHosts(req.UserId, t, scanIpArr, ipV6DomainHasNsRecord, topDomains)
				if err != nil {
					rsp.WarnMessage = "创建IPv6扫描任务主机失败"
					return fmt.Errorf("创建IPv6扫描任务主机失败")
				}
				if cfg.ExecGolangJob() {
					// 下发扫描任务到MQ - 对应PHP的ScanAssetMqJob::publish
					asyncq.Enqueue(context.Background(), asyncq.ScanAssetJob, &asyncq.TaskIdPayload{
						TaskId: uint64(t.ID),
						UserId: req.UserId,
					})
					log.Infof("下发IPv6扫描任务到MQ: taskId=%d, userId=%d", t.ID, req.UserId)
				} else {
					//调用php的job --DispatchGolangJobToPhpJob
					err = asyncq.DispatchGolangJobToPhpJob.Dispatch(uint64(t.ID))
					log.Infof("下发IPv6扫描任务到PHP: taskId=%d, userId=%d", t.ID, req.UserId)
				}
			}
		}
	}

	// 合并IPv4域名解析结果
	var allIPv4 = utils.ListDistinctNonZero(append(ipv4s, ipv4Domain...))
	log.Infof("[IP数量计算] 用户ID: %d - 合并后所有IPv4数量: %d (原始IPv4: %d + 域名解析IPv4: %d)", req.UserId, len(allIPv4), len(ipv4s), len(ipv4Domain))
	ipv4s = allIPv4 // 更新ipv4s为合并后的结果

	if len(ipv4s) > 0 {
		log.Infof("[IP数量计算-IPv4处理] 用户ID: %d - 开始处理IPv4，当前scanIpNum: %d, IPv4数量: %d", req.UserId, scanIpNum, len(ipv4s))
		scanIpNum += len(ipv4s)
		log.Infof("[IP数量计算-IPv4处理] 用户ID: %d - 累加后scanIpNum: %d", req.UserId, scanIpNum)

		if req.OperateCompanyId > 0 {
			// 计算剩余可用数量（不包含当前要扫描的数量）
			leftNum := companyInfo.LimitIpAsset - companyInfo.UsedIpAsset
			log.Infof("[IP数量计算-公司IPv4] 公司限制: %d, 已使用: %d, 剩余可用: %d, 当前要扫描IPv4: %d", companyInfo.LimitIpAsset, companyInfo.UsedIpAsset, leftNum, len(ipv4s))

			if leftNum <= 0 {
				log.Warnf("[IP数量计算-公司IPv4] 剩余数量不足: %d", leftNum)
				if !hasIpAsset {
					rsp.WarnMessage = "当前测绘任务的资产入账扫描的数量已经超过系统授权IP数量，无法下发扫描任务!"
					return fmt.Errorf("当前测绘任务的资产入账扫描的数量已经超过系统授权IP数量，无法下发扫描任务")
				}
				log.Infof("[IP数量计算-公司IPv4] 清空IPv4列表")
				ipv4s = make([]string, 0)
			} else if len(ipv4s) > leftNum {
				// 要扫描的数量超过剩余数量，使用剩余数量*1.2倍进行下发
				targetNum := leftNum * 12 / 10
				if targetNum > len(ipv4s) {
					targetNum = len(ipv4s)
				}
				log.Infof("[IP数量计算-公司IPv4] 要扫描数量(%d)超过剩余数量(%d)，使用1.2倍下发，目标数量: %d", len(ipv4s), leftNum, targetNum)

				ipv4s = ipv4s[0:targetNum]
				originIpNum += len(ipv4s)
				log.Infof("[IP数量计算-公司IPv4] 最终IPv4数量: %d, 累加后originIpNum: %d", len(ipv4s), originIpNum)
			} else {
				// 要扫描的数量在剩余范围内，直接使用
				originIpNum += len(ipv4s)
				log.Infof("[IP数量计算-公司IPv4] 要扫描数量(%d)在剩余范围内(%d)，直接使用，累加后originIpNum: %d", len(ipv4s), leftNum, originIpNum)
			}
		} else if cfg.LoadAPP().IsLocalForadar {
			limitIpAsset, _ := strconv.Atoi(user.LimitIPAsset)

			usedNum, err := es.CountByParams[fofaee_assets.FofaeeAssets]([][]interface{}{
				{"MUST", [][]interface{}{
					{"user_id", "=", req.UserId},
					{"status", "in", []interface{}{1, 4}},
				}},
			})
			if err != nil {
				rsp.WarnMessage = "获取已入账资产数量失败"
				return fmt.Errorf("获取已入账资产数量失败")
			}

			// 计算剩余可用数量（不包含当前要扫描的数量）
			leftNum := limitIpAsset - int(usedNum)
			log.Infof("[IP数量计算-本地IPv4] 用户限制: %d, 已使用: %d, 剩余可用: %d, 当前要扫描IPv4: %d", limitIpAsset, usedNum, leftNum, len(ipv4s))

			if leftNum <= 0 {
				log.Warnf("[IP数量计算-本地IPv4] 剩余数量不足: %d", leftNum)
				if !hasIpAsset {
					rsp.WarnMessage = "当前测绘任务的资产入账扫描的数量已经超过系统授权IP数量，无法下发扫描任务!"
					return fmt.Errorf("当前测绘任务的资产入账扫描的数量已经超过系统授权IP数量，无法下发扫描任务")
				}
				log.Infof("[IP数量计算-本地IPv4] 清空IPv4列表")
				ipv4s = make([]string, 0)
			} else if len(ipv4s) > leftNum {
				// 要扫描的数量超过剩余数量，使用剩余数量*1.2倍进行下发
				targetNum := leftNum * 12 / 10
				if targetNum > len(ipv4s) {
					targetNum = len(ipv4s)
				}
				log.Infof("[IP数量计算-本地IPv4] 要扫描数量(%d)超过剩余数量(%d)，使用1.2倍下发，目标数量: %d", len(ipv4s), leftNum, targetNum)

				ipv4s = ipv4s[0:targetNum]
				originIpNum += len(ipv4s)
				log.Infof("[IP数量计算-本地IPv4] 最终IPv4数量: %d, 累加后originIpNum: %d", len(ipv4s), originIpNum)
			} else {
				// 要扫描的数量在剩余范围内，直接使用
				originIpNum += len(ipv4s)
				log.Infof("[IP数量计算-本地IPv4] 要扫描数量(%d)在剩余范围内(%d)，直接使用，累加后originIpNum: %d", len(ipv4s), leftNum, originIpNum)
			}
		}
		chunks := slices.Chunk(ipv4s, 1000)
		scanTask.IPType = task.IP_TYPE_V4
		scanTask.ScanType = task.SCAN_TYPE_SPEED
		for chunk := range chunks {
			st := scanTask
			st.Name += "-IPV4"
			t, err := mysql.NewDSL[task.Task]().Create(st)
			if err != nil {
				rsp.WarnMessage = "创建IPv4扫描任务失败"
				return fmt.Errorf("创建IPv4扫描任务失败")
			}
			exists, err := es.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
				{"MUST", [][]interface{}{
					{"user_id", "=", req.UserId},
					{"is_ipv6", "=", false},
					{"ip", "in", chunk},
					{"flag", "=", st.Flag},
				}},
			}, nil, "user_id", "flag", "ip", "port", "base_protocol")
			if err != nil {
				rsp.WarnMessage = "获取IPv4资产失败"
				return fmt.Errorf("获取IPv4资产失败")
			}
			var taskProbeInfos = make([]task.TaskProbeInfo, 0)
			var taskIps = make([]task.TaskIps, 0)
			var ipExists = make(map[string]struct{}, 0)

			// 处理ES中已存在的推荐结果
			if len(exists) > 0 {
				var uniqueMap = make(map[string]struct{})
				for _, v := range exists {
					p := "tcp"
					if len(v.BaseProtocol) > 0 {
						p = v.BaseProtocol
					}
					key := fmt.Sprintf("%s:%d:%s", v.Ip, cast.ToInt(v.Port), p)
					if _, ok := uniqueMap[key]; ok {
						continue
					}
					uniqueMap[key] = struct{}{}
					taskProbeInfos = append(taskProbeInfos, task.TaskProbeInfo{
						TaskId: uint64(t.ID),
						Ip: sql.NullString{
							String: v.Ip,
							Valid:  true,
						},
						Port:         cast.ToInt(v.Port),
						BaseProtocol: p,
						CreatedAt: sql.NullTime{
							Time:  time.Now(),
							Valid: true,
						},
					})
					if _, ok := ipExists[v.Ip]; !ok {
						ipExists[v.Ip] = struct{}{}
						taskIps = append(taskIps, task.TaskIps{
							TaskId: uint64(t.ID),
							Ip: sql.NullString{
								String: v.Ip,
								Valid:  true,
							},
							CreatedAt: sql.NullTime{
								Time:  time.Now(),
								Valid: true,
							},
						})
					}
				}
			}

			// 处理新解析出来的IPv4地址（不在ES推荐结果中的）
			for _, ip := range chunk {
				// 检查该IP是否已经在ES结果中处理过
				if _, exists := ipExists[ip]; exists {
					continue // 已经处理过，跳过
				}

				// 为新解析的IPv4地址创建默认的80和443端口任务
				taskProbeInfos = append(taskProbeInfos, task.TaskProbeInfo{
					TaskId: uint64(t.ID),
					Ip: sql.NullString{
						String: ip,
						Valid:  true,
					},
					Port:         80,
					BaseProtocol: "tcp",
					CreatedAt: sql.NullTime{
						Time:  time.Now(),
						Valid: true,
					},
				})
				taskProbeInfos = append(taskProbeInfos, task.TaskProbeInfo{
					TaskId: uint64(t.ID),
					Ip: sql.NullString{
						String: ip,
						Valid:  true,
					},
					Port:         443,
					BaseProtocol: "tcp",
					CreatedAt: sql.NullTime{
						Time:  time.Now(),
						Valid: true,
					},
				})

				// 添加到taskIps
				taskIps = append(taskIps, task.TaskIps{
					TaskId: uint64(t.ID),
					Ip: sql.NullString{
						String: ip,
						Valid:  true,
					},
					CreatedAt: sql.NullTime{
						Time:  time.Now(),
						Valid: true,
					},
				})

				// 标记为已处理
				ipExists[ip] = struct{}{}
			}

			// 先查询需要更新的文档，获取文档ID
			documentsToUpdate, err := es.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
				{"MUST", [][]interface{}{
					{"user_id", "=", req.UserId},
					{"flag", "=", t.Flag},
					{"ip", "in", chunk},
					{"is_ipv6", "=", false},
				}},
			}, nil, "id")
			if err != nil {
				log.Errorf("[更新IPv4资产] 查询待更新文档失败，用户ID: %d, 错误: %v", req.UserId, err)
				rsp.WarnMessage = "查询IPv4资产失败"
				return fmt.Errorf("查询IPv4资产失败: %v", err)
			}

			if len(documentsToUpdate) == 0 {
				log.Warnf("[更新IPv4资产] 没有找到需要更新的文档，用户ID: %d, chunk大小: %d", req.UserId, len(chunk))
				continue // 跳过这个chunk，继续处理下一个
			}

			// 使用批量更新方式
			var updateParams []*recommend_result.UpdateAnyParam
			for _, doc := range documentsToUpdate {
				updateParams = append(updateParams, &recommend_result.UpdateAnyParam{
					Id: doc.Id,
					Data: map[string]interface{}{
						"task_id": t.ID,
					},
				})
			}

			// 执行批量更新
			err = recommend_result.NewRecommendResultModel().UpdatesAny(updateParams)
			if err != nil {
				log.Errorf("[更新IPv4资产] 批量更新失败，用户ID: %d, 文档数量: %d, 错误: %v", req.UserId, len(updateParams), err)
				rsp.WarnMessage = "更新IPv4资产失败"
				return fmt.Errorf("更新IPv4资产失败: %v", err)
			}

			log.Infof("[更新IPv4资产] 成功更新文档数量: %d, 用户ID: %d, TaskID: %d", len(updateParams), req.UserId, t.ID)
			if scanType == task.NORMAL_PORT || scanType == task.ALL_PORT {
				if len(taskIps) > 0 {
					_, err := mysql.NewDSL[task.TaskIps]().BatchCreate(taskIps)
					if err != nil {
						rsp.WarnMessage = "创建IPv4扫描任务IP失败"
						return fmt.Errorf("创建IPv4扫描任务IP失败")
					}
				}
				taskPorts := task.TaskPorts{
					TaskId:    uint64(t.ID),
					PortsType: `App\Models\MySql\PortGroup`,
					PortsId:   dataPorts,
					CreatedAt: sql.NullTime{
						Time:  time.Now(),
						Valid: true,
					},
					UpdatedAt: sql.NullTime{
						Time:  time.Now(),
						Valid: true,
					},
				}
				_, err = mysql.NewDSL[task.TaskPorts]().Create(taskPorts)
				if err != nil {
					rsp.WarnMessage = "创建IPv4扫描任务端口失败"
					return fmt.Errorf("创建IPv4扫描任务端口失败")
				}
			} else {
				_, err := mysql.NewDSL[task.TaskProbeInfo]().BatchCreate(taskProbeInfos)
				if err != nil {
					rsp.WarnMessage = "创建IPv4扫描任务探活失败"
					return fmt.Errorf("创建IPv4扫描任务探活失败")
				}
			}
			err = createTaskHosts(req.UserId, t, chunk, ipV4DomainHasNsRecord, topDomains)
			if err != nil {
				rsp.WarnMessage = "创建IPv4扫描任务主机失败"
				return fmt.Errorf("创建IPv4扫描任务主机失败")
			}
			if cfg.ExecGolangJob() {
				// 下发扫描任务到MQ - 对应PHP的ScanAssetMqJob::publish
				asyncq.Enqueue(context.Background(), asyncq.ScanAssetJob, &asyncq.TaskIdPayload{
					TaskId: uint64(t.ID),
					UserId: req.UserId,
				})
				log.Infof("下发IPv4扫描任务到MQ: taskId=%d, userId=%d", t.ID, req.UserId)
			} else {
				//调用php的job --DispatchGolangJobToPhpJob
				err = asyncq.DispatchGolangJobToPhpJob.Dispatch(uint64(t.ID))
				log.Infof("下发IPv4扫描任务到PHP: taskId=%d, userId=%d", t.ID, req.UserId)
			}
		}
	}

	// 16. 更新推荐资产状态
	// 先查询需要更新状态的文档
	documentsToUpdateStatus, err := es.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
		{"MUST", [][]interface{}{
			{"user_id", "=", req.UserId},
			{"flag", "=", t.ExpendFlags},
		}},
	}, nil, "id")
	if err != nil {
		log.Errorf("[更新推荐资产状态] 查询待更新文档失败，用户ID: %d, flag: %s, 错误: %v", req.UserId, t.ExpendFlags, err)
	} else if len(documentsToUpdateStatus) > 0 {
		// 使用批量更新方式更新状态
		var updateStatusParams []*recommend_result.UpdateAnyParam
		for _, doc := range documentsToUpdateStatus {
			updateStatusParams = append(updateStatusParams, &recommend_result.UpdateAnyParam{
				Id: doc.Id,
				Data: map[string]interface{}{
					"status": recommend_result.STATUS_AUDITED,
				},
			})
		}

		// 执行批量更新状态
		err = recommend_result.NewRecommendResultModel().UpdatesAny(updateStatusParams)
		if err != nil {
			log.Errorf("[更新推荐资产状态] 批量更新失败，用户ID: %d, 文档数量: %d, 错误: %v", req.UserId, len(updateStatusParams), err)
		} else {
			log.Infof("[更新推荐资产状态] 成功更新文档数量: %d, 用户ID: %d, 状态: %d", len(updateStatusParams), req.UserId, recommend_result.STATUS_AUDITED)
		}
	} else {
		log.Warnf("[更新推荐资产状态] 没有找到需要更新状态的文档，用户ID: %d, flag: %s", req.UserId, t.ExpendFlags)
	}
	// 17. 更新测绘任务状态
	detect_assets_tasks.NewModel().UpdateAny(map[string]interface{}{
		"step":        detect_assets_tasks.StepFour,
		"step_detail": detect_assets_tasks.StepFourScan,
		"step_status": detect_assets_tasks.StepStatusDefault,
	}, mysql.WithWhere("id = ?", t.ID))

	// 18. 返回
	rsp.WarnMessage = ""
	log.Infof("[IP数量计算-最终结果] 用户ID: %d - originIpNum: %d, scanIpNum: %d, 公司ID: %d", req.UserId, originIpNum, scanIpNum, req.OperateCompanyId)

	if req.OperateCompanyId > 0 && originIpNum != scanIpNum {
		log.Infof("[IP数量计算-警告] 用户ID: %d - 原始IP数量与扫描IP数量不一致，生成警告消息", req.UserId)
		rsp.WarnMessage = fmt.Sprintf("下发资产数超入账限制，自动下发'%d'个资产", scanIpNum)
	}

	clientIp, _ := metadata.GetString(ctx, "client_ip")
	// 异步任务去获取该推荐记录里面的域名和子域名线索
	asyncq.Enqueue(context.Background(), asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(req.UserId, "单位资产测绘扫描任务", clientIp, uint64(req.OperateCompanyId), operate_logs.FIND_ASSETS, operate_logs.TYPE_OPERATE))
	//  $info = RecommendRecord::query()->where('flag',$detectTaskInfo->expend_flags)->first();
	// if($info){
	//     $domainClues = Clue::query()->where('user_id',$user_id)
	//         ->where('is_deleted', Clue::NOT_DELETE)
	//         ->whereIn('id',array_values($info['clue_id']))
	//         ->whereIn('type',[Clue::TYPE_DOMAIN,Clue::TYPE_SUBDOMAIN])
	//         ->get();
	//     $domains = [];
	//     foreach ($domainClues as $clue) {
	//         $content = $clue['content'];
	//         if ($clue['type'] == Clue::TYPE_SUBDOMAIN) {
	//             $content = getTopDomain($content);
	//         }
	//         $domains[] = $content;
	//     }
	//     $domains = array_values(array_unique($domains));
	//     if (config('app.is_local_foradar')) {
	//         //付费的接口需要调用远端的golang的微服务地址
	//         $host = config('app.saas_micro_host');
	//     } else {
	//         $host = config('app.micro_host');
	//     }
	//     //异步任务去获取登录入口数据
	//     MicroClientService::getIns($host)->getLoginAssetsByDomainClue($domains,$user_id,$companyId);
	//     LogService::info('通过搜索引擎获取根域的域名登录资产','通过搜索引擎获取根域的域名登录资产下发成功',['$domains'=>$domains,'user_id'=>$user_id,'$companyId'=>$companyId]);
	// }else{
	//     LogService::info('scanRecommend','scanRecommend-找不到推荐记录',['user_id'=>$user_id,'$companyId'=>$companyId]);
	//     return response()->errorBad('推荐任务不存在，参数错误!');
	// }
	rr, err := es.AllByParams[recommend_record.RecommendRecord](1, [][]interface{}{
		{"MUST", [][]interface{}{
			{"flag", "=", t.ExpendFlags},
		}},
	}, nil, "clue_id")
	if err != nil {
		return fmt.Errorf("获取推荐记录失败: %v", err)
	}
	if len(rr) == 0 {
		return fmt.Errorf("推荐记录不存在")
	}
	info := rr[0]
	clueIds := info.ClueId
	domainCubes, err := mysql.NewDSL[clues.Clue]().QueryByParams([][]interface{}{
		{"user_id", "=", req.UserId},
		{"is_deleted", "=", clues.NOT_DELETE},
		{"id", "in", clueIds},
		{"type", "in", []int{clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN}},
	})
	domains := make([]string, 0)
	for _, v := range domainCubes {
		if v.Type == clues.TYPE_DOMAIN {
			domains = append(domains, v.Content)
		} else {
			domains = append(domains, utils.GetTopDomain(v.Content))
		}
	}
	domains = utils.ListDistinct(domains)
	if len(domains) == 0 {
		return nil
	}
	// 异步调用登录页面截图服务
	go func() {
		var scanReq = &scan.LoginPageScreenshotByTopDomainRequest{
			UserId:    req.UserId,
			CompanyId: uint64(req.OperateCompanyId),
			Domains:   domains,
		}
		_, err := scan.GetProtoClient().LoginPageScreenshotByTopDomain(context.Background(), scanReq, utils.RpcTimeoutDur(30*time.Second), microx.ServerTimeoutDur(30*time.Second))
		if err != nil {
			log.Errorf("异步调用通过搜索引擎获取根域的域名登录资产失败: %v", err)
		} else {
			log.Infof("异步调用通过搜索引擎获取根域的域名登录资产成功: userId=%d, companyId=%d, domains=%v", req.UserId, req.OperateCompanyId, domains)
		}
	}()

	log.Infof("[ScanRecommend] 推荐资产扫描任务处理完成 - 用户ID: %d, 任务ID: %d", req.UserId, req.Id)
	return nil
}
