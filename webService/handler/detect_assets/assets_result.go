package detect_assets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	cluesModel "micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/task"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"strconv"
	"strings"

	elastic_search "github.com/olivere/elastic"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// GetDetectAssetsResultList 获取资产测绘结果列表
func GetDetectAssetsResultList(ctx context.Context, req *pb.DetectAssetsResultListRequest, rsp *pb.DetectAssetsResultListResponse) error {
	log.WithContextInfof(ctx, "Received GetDetectAssetsResultList request: %v", req)

	// 1. 获取任务信息
	detectTaskModel := detect_assets_tasks.NewModel()
	detectInfo, err := detectTaskModel.First(
		detect_assets_tasks.WithID(uint64(req.ExpendId)),
		detect_assets_tasks.WithUserID(uint64(req.UserId)),
	)
	if err != nil || detectInfo == nil {
		return errors.New("测绘任务为空")
	}

	// 1.1 获取关联的扫描任务ID列表
	taskModel := task.NewModel()
	scanTasks, err := taskModel.FindAll(
		task.WithUserID(uint64(req.UserId)),
		task.WithDetectAssetsTasksID(uint64(req.ExpendId)),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "获取扫描任务失败: %v", err)
		// 如果获取失败，继续执行，使用空的task_id
	}

	// 如果没有找到关联的扫描任务，尝试查询已删除的任务
	if len(scanTasks) == 0 {
		scanTasks, err = taskModel.FindAll(
			task.WithUserID(uint64(req.UserId)),
			task.WithDetectAssetsTasksID(uint64(req.ExpendId)),
			task.WithTrashed(),
		)
		if err != nil {
			log.WithContextErrorf(ctx, "获取已删除的扫描任务失败: %v", err)
		}

		// 如果仍然没有找到任何任务，返回空结果
		if len(scanTasks) == 0 {
			// 返回空结果
			rsp.KonwnTableNum = 0
			rsp.UnknownTableNum = 0
			rsp.ThreatTableNum = 0
			rsp.CurrentPage = req.Page
			rsp.PerPage = req.PerPage
			rsp.Total = 0
			rsp.Data = "[]"
			rsp.Condition = make(map[string]*pb.FilterCondition)
			return nil
		}
	}

	// 提取任务ID列表
	taskIds := make([]interface{}, 0, len(scanTasks))
	for _, t := range scanTasks {
		taskIds = append(taskIds, uint64(t.ID))
	}

	// 2. 构建查询条件
	var builder elastic.SearchBuilder

	// 使用StructToParams自动构建查询条件（参考AssetsAccountIpList）
	elastic.StructToParams(req, &builder)

	// 2.1 添加任务ID条件 - 使用从scan_tasks表获取的task_id
	builder.AddMust([]interface{}{"task_id", "in", elastic.ToInterfaceArray(taskIds)})
	builder.AddMust([]interface{}{"user_id", req.UserId})

	// 2.2 添加状态条件
	if req.Status >= 0 && req.Status <= 3 {
		builder.AddMust([]interface{}{"status", req.Status})
	}

	// 2.3 添加关键字搜索（参考AssetsAccountIpList的实现）
	if req.Keyword != nil && *req.Keyword != "" {
		var keywordQuery [][]interface{}
		_, err := strconv.Atoi(*req.Keyword)
		if err == nil {
			keywordQuery = append(keywordQuery, []interface{}{"port_list.logo.hash", *req.Keyword})
			keywordQuery = append(keywordQuery, []interface{}{"port", *req.Keyword})
		}
		keywordQuery = append(keywordQuery, []interface{}{"port_list.cert.subject_key", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.icp.no", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.title.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.url.raw", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.protocol", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.domain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.url", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.subdomain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.url.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"custom_tags.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"ip.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		builder.AddShould(keywordQuery...)
	}

	// 2.4 添加特殊的数组参数处理（参考AssetsAccountIpList）
	// Title
	if len(req.Title) > 0 {
		if utils.ListContains(req.Title, "-") {
			names := utils.ListReplace(req.Title, "-", "")
			builder.AddShould([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(req.Title)})
		}
	}

	// Domain
	if len(req.Domain) > 0 {
		if utils.ListContains(req.Domain, "-") {
			names := utils.ListReplace(req.Domain, "-", "")
			builder.AddShould([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(req.Domain)})
		}
	}

	// Cert
	if len(req.Cert) > 0 {
		builder.AddMust([]interface{}{"port_list.cert.subject_key", "in", elastic.ToInterfaceArray(req.Cert)})
	}

	// ICP
	if len(req.Icp) > 0 {
		builder.AddMust([]interface{}{"port_list.icp.no", "in", elastic.ToInterfaceArray(req.Icp)})
	}

	// Hosts
	if req.Hosts != nil && *req.Hosts != "" {
		var hostsQuery [][]interface{}
		hostsQuery = append(hostsQuery, []interface{}{"host_list.domain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"host_list.subdomain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
		builder.AddShould(hostsQuery...)
	}

	// SecondConfirm
	if req.SecondConfirm != nil && *req.SecondConfirm > 0 {
		if *req.SecondConfirm == 1 {
			builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.StatusUploadAsset, fofaee_assets.StatusConfirmAsset}})
			builder.AddMust([]interface{}{"second_confirm", 1})
			builder.AddShould([]interface{}{"type", "in", []interface{}{foradar_assets.TypeClaimed, foradar_assets.TypeRecommend}}, []interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		} else {
			builder.AddMust([]interface{}{"type", fofaee_assets.TYPE_RECOMMEND})
			builder.AddMustNot([]interface{}{"second_confirm", 1}, []interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		}
	}

	// 3. 执行查询
	var sorts []elastic_search.Sorter
	sorts = append(sorts, elastic_search.NewFieldSort("ip").Asc())
	query := builder.Build()

	log.WithContextInfof(ctx, "查询条件: %s", utils.AnyToStr(query))

	// 4. 获取分页数据
	total, list, err := elastic.ListByParams[fofaee_assets.FofaeeAssets](int(req.Page), int(req.PerPage), query, sorts)
	if err != nil {
		log.WithContextErrorf(ctx, "查询失败: %v", err)
		return err
	}

	// 5. 处理结果数据
	groupIds := []interface{}{}
	resultList := make([]map[string]interface{}, 0)

	for _, item := range list {
		// 5.1 处理端口详情
		detail := item.PortList
		var processedDetail []map[string]interface{}

		if detail != nil {
			// 收集groupIds
			if item.UpdatedAt > "2022-09-06 16:13:00" {
				// 处理ReasonArr
				for _, reasonAny := range item.ReasonArr {
					if reasonMap, ok := reasonAny.(map[string]interface{}); ok {
						if groupId, exists := reasonMap["group_id"]; exists {
							if gid, ok := groupId.(float64); ok && gid > 0 {
								groupIds = append(groupIds, int(gid))
							}
						}
					}
				}
			} else {
				for _, ds := range detail {
					for _, reason := range ds.Reason {
						if reason.GroupId > 0 {
							groupIds = append(groupIds, reason.GroupId)
						}
					}
				}
			}

			// 处理详情数据 - 创建新的结构体用于返回
			processedDetail = make([]map[string]interface{}, len(detail))
			for i, d := range detail {
				// 创建新的端口详情项
				portItem := map[string]interface{}{
					"fid":                  d.Fid,
					"reason":               d.Reason,
					"reason_arr":           d.ReasonArr,
					"http_status_code":     d.HttpStatusCode,
					"is_open":              d.IsOpen,
					"online_state":         d.OnlineState,
					"cname":                d.Cname,
					"assets_source":        d.AssetsSource,
					"assets_source_domain": d.AssetsSourceDomain,
					"source_updated_at":    d.SourceUpdatedAt,
					"oneforall_source":     d.OneforallSource,
					"banner":               d.Banner,
					"screenshot":           d.Screenshot,
					"title":                d.Title,
					"url":                  d.Url,
					"clue_company_name":    d.ClueCompanyName,
					"url_arr":              d.UrlArr,
					"protocol":             d.Protocol,
					"port":                 d.Port,
					"domain":               d.Domain,
					"reliability_score":    d.ReliabilityScore,
					"header":               d.Header,
					"subdomain":            d.Subdomain,
					"is_login":             d.IsLogin,
					"open_parse":           d.OpenParse,
					"risk_type":            d.RiskType,
				}

				// 处理证书 - 转为字符串格式，匹配PHP逻辑
				if d.Cert.SubjectKey != "" {
					portItem["cert"] = d.Cert.SubjectKey
				} else {
					portItem["cert"] = ""
				}

				// 处理ICP - 转为字符串格式，匹配PHP逻辑
				if d.Icp.No != nil {
					if icpNo, ok := d.Icp.No.(string); ok {
						portItem["icp"] = icpNo
					} else {
						portItem["icp"] = ""
					}
				} else {
					portItem["icp"] = ""
				}

				// 处理Logo - 统一处理方式，与AssetsAccountIpList保持一致
				if d.Logo.Hash != nil && d.Logo.Content != "" {
					portItem["logo"] = map[string]interface{}{
						"hash":    d.Logo.Hash,
						"content": storage.GenAPIDownloadPath("", d.Logo.Content, ".ico"),
					}
				} else {
					portItem["logo"] = map[string]interface{}{
						"hash":    nil,
						"content": "",
					}
				}

				processedDetail[i] = portItem
			}
		}

		// 5.2 处理主机列表 - 转换Logo为链接地址
		processedHostList := item.HostList
		if len(processedHostList) > 0 {
			for i, host := range processedHostList {
				// 处理Logo内容，转为链接地址形式
				if host.Logo.Hash != nil && host.Logo.Content != "" {
					processedHostList[i].Logo.Content = storage.GenAPIDownloadPath("", host.Logo.Content, ".ico")
				}
			}
		}

		// 5.3 构建结果项
		resultItem := map[string]interface{}{
			"detail":            processedDetail,
			"ip":                item.Ip,
			"param_ip":          item.Ip,
			"clue_company_name": convertToStringSlice(item.ClueCompanyName),
			"company_tag_name":  convertToStringSlice(item.ClueCompanyName),
			"create_time":       item.CreatedAt,
			"last_update_time":  item.UpdatedAt,
			"type":              item.Type,
			"id":                item.Id,
			"latitude":          getMapValueSafe(item.Geo, "lat"),
			"longitude":         getMapValueSafe(item.Geo, "lon"),
			"isp":               getMapValueSafe(item.Geo, "isp"),
			"province":          getMapValueSafe(item.Geo, "province"),
			"asn":               getMapValueSafe(item.Geo, "asn"),
			"state":             changeState(item.OnlineState),
			"host_list":         processedHostList,
			"rule_tags":         item.RuleTags,
			"online_state":      item.OnlineState,
			"update_time":       item.UpdatedAt,
			"created_at":        item.CreatedAt,
			"updated_at":        item.UpdatedAt,
			"reason_arr":        item.ReasonArr,
			"chain_list":        []interface{}{}, // 默认空数组，后面会被更新
			"is_cdn":            item.IsCdn,
			"cloud_name":        item.CloudName,
		}

		// 5.4 处理规则标签
		rules := ""
		if len(item.RuleTags) > 0 {
			ruleNames := make([]string, 0)
			for _, rule := range item.RuleTags {
				if rule.CnProduct != "" {
					ruleNames = append(ruleNames, rule.CnProduct)
				}
			}
			rules = strings.Join(utils.UniqueStringSlice(ruleNames), ",")
		}
		resultItem["rule_infos"] = rules

		// 5.5 处理域名和子域名
		domainAndSubDomain := make([]string, 0)
		for _, portItem := range processedDetail {
			if domain, ok := portItem["domain"].(string); ok && domain != "" {
				domainAndSubDomain = append(domainAndSubDomain, domain)
			}
			if subdomain, ok := portItem["subdomain"].(string); ok && subdomain != "" {
				domainAndSubDomain = append(domainAndSubDomain, subdomain)
			}
		}

		resultItem["hosts"] = utils.UniqueStringSlice(domainAndSubDomain)

		resultList = append(resultList, resultItem)
	}

	// 6. 处理线索链
	// 去重groupIds
	uniqueGroupIds := make([]interface{}, 0)
	groupIdMap := make(map[interface{}]bool)
	for _, gid := range groupIds {
		if !groupIdMap[gid] {
			uniqueGroupIds = append(uniqueGroupIds, gid)
			groupIdMap[gid] = true
		}
	}
	groupIds = uniqueGroupIds

	if len(groupIds) > 0 {
		log.WithContextInfof(ctx, "准备查询线索，有效的group_ids: %v", groupIds)

		// 查询线索表
		clueList, err := cluesModel.NewCluer().ListAll(func(db *gorm.DB) {
			db.Where("user_id = ?", req.UserId)
		})
		if err != nil {
			log.WithContextErrorf(ctx, "查询线索失败: %v", err)
		} else {
			// 处理每个资产项的证据链
			for i, item := range resultList {
				// 获取reason_arr
				var reasonArr []interface{}
				if arr, ok := item["reason_arr"].([]interface{}); ok {
					reasonArr = arr
				}

				// 去重reason_arr
				var uniqueReasonArr []interface{}
				reasonMap := make(map[string]bool)
				for _, r := range reasonArr {
					if reasonBytes, err := json.Marshal(r); err == nil {
						key := string(reasonBytes)
						if !reasonMap[key] {
							uniqueReasonArr = append(uniqueReasonArr, r)
							reasonMap[key] = true
						}
					}
				}
				reasonArr = uniqueReasonArr

				// 为每个reason构建线索链
				var chainLists [][]map[string]interface{}
				for _, r := range reasonArr {
					if reasonMap, ok := r.(map[string]interface{}); ok {
						// 获取reason的id
						var reasonID int64
						if id, ok := reasonMap["id"]; ok {
							reasonID = cast.ToInt64(id)
						}

						if reasonID > 0 {
							// 构建线索链
							var chainList []map[string]interface{}
							getTopClue(0, clueList, &chainList, reasonID)

							if len(chainList) > 0 {
								// 添加当前IP到证据链末尾
								ipStr := fmt.Sprintf("%v", item["ip"])
								chainList = append(chainList, map[string]interface{}{
									"content": ipStr,
								})
								// 添加到结果中
								chainLists = append(chainLists, chainList)
							}
						}
					}
				}

				// 将线索链添加到结果中
				if len(chainLists) > 0 {
					resultList[i]["chain_list"] = chainLists
				} else {
					log.WithContextInfof(ctx, "资产项[%d]没有构建出证据链", i)
				}
			}
		}
	}

	// 7. 处理聚合筛选条件
	condition := make(map[string]*pb.FilterCondition)

	// 7.1 构建聚合筛选条件的基础查询（参考PHP逻辑，只包含基础条件）
	var conditionBuilder elastic.SearchBuilder

	// 只添加基础条件：task_ids, user_id, status（参考PHP的$search数组）
	conditionBuilder.AddMust([]interface{}{"task_id", "in", elastic.ToInterfaceArray(taskIds)})
	conditionBuilder.AddMust([]interface{}{"user_id", req.UserId})

	// 添加status条件（如果有的话）
	if req.Status >= 0 && req.Status <= 3 {
		conditionBuilder.AddMust([]interface{}{"status", req.Status})
	}

	// 注意：这里不添加其他筛选条件（如title、domain、cert等），
	// 因为聚合筛选条件应该基于所有符合基础条件的数据，而不是当前列表的筛选结果

	conditionQuery := conditionBuilder.Build()
	_, conditionArr, err := elastic.ListByParams[fofaee_assets.FofaeeAssets](1, 10000, conditionQuery, nil)
	if err == nil && len(conditionArr) > 0 {
		// 协议
		protocols := make([]string, 0)
		// 端口
		ports := make([]string, 0)
		// 子域名
		subdomains := make([]string, 0)
		// 域名
		domains := make([]string, 0)
		// 证书
		certs := make([]string, 0)
		// ICP
		icps := make([]string, 0)
		// 标题
		titles := make([]string, 0)
		// Logo
		logos := make([]map[string]interface{}, 0)

		for _, asset := range conditionArr {
			if asset.PortList != nil {
				for _, port := range asset.PortList {
					// 协议
					if port.Protocol != "" {
						protocols = append(protocols, port.Protocol)
					}

					// 端口
					if port.Port != nil {
						if portVal, ok := port.Port.(float64); ok {
							ports = append(ports, fmt.Sprintf("%d", int(portVal)))
						}
					}

					// 子域名
					if port.Subdomain != "" {
						subdomains = append(subdomains, port.Subdomain)
					}

					// 域名
					if port.Domain != "" {
						domains = append(domains, port.Domain)
					}

					// 证书
					if port.Cert.SubjectKey != "" {
						certs = append(certs, port.Cert.SubjectKey)
					}

					// ICP
					if port.Icp.No != nil {
						if icpNo, ok := port.Icp.No.(string); ok && icpNo != "" {
							icps = append(icps, icpNo)
						}
					}

					// 标题
					if port.Title != nil {
						if title, ok := port.Title.(string); ok && strings.TrimSpace(title) != "" {
							titles = append(titles, title)
						}
					}

					// Logo
					if port.Logo.Hash != nil {
						var logoHash int64
						switch h := port.Logo.Hash.(type) {
						case float64:
							logoHash = int64(h)
						case int64:
							logoHash = h
						case int:
							logoHash = int64(h)
						}

						if logoHash > 0 {
							logoContent := ""
							if port.Logo.Content != "" {
								logoContent = storage.GenAPIDownloadPath("", port.Logo.Content, ".ico")
							}

							// 检查是否已存在相同hash
							exists := false
							for _, logo := range logos {
								if hash, ok := logo["hash"].(int64); ok && hash == logoHash {
									exists = true
									break
								}
							}

							if !exists {
								logos = append(logos, map[string]interface{}{
									"hash":    logoHash,
									"content": logoContent,
								})
							}
						}
					}
				}
			}
		}

		// 添加到筛选条件
		condition["protocol"] = &pb.FilterCondition{Values: utils.UniqueStringSlice(protocols)}
		condition["port"] = &pb.FilterCondition{Values: utils.UniqueStringSlice(ports)}
		condition["subdomain"] = &pb.FilterCondition{Values: utils.UniqueStringSlice(subdomains)}
		condition["domain"] = &pb.FilterCondition{Values: utils.UniqueStringSlice(domains)}
		condition["cert"] = &pb.FilterCondition{Values: utils.UniqueStringSlice(certs)}
		condition["icp"] = &pb.FilterCondition{Values: utils.UniqueStringSlice(icps)}
		condition["title"] = &pb.FilterCondition{Values: utils.UniqueStringSlice(titles)}

		// Logo需要特殊处理
		logoValues := make([]string, 0)
		if len(logos) > 0 {
			logoJSON, _ := json.Marshal(logos)
			logoValues = append(logoValues, string(logoJSON))
		}
		condition["logo"] = &pb.FilterCondition{Values: logoValues}
	}

	// 8. 统计各状态数量
	var konwnTableNum, unknownTableNum, threatTableNum int64

	// 创建基础查询条件（参考PHP逻辑，只包含基础条件，不包含筛选参数）
	var baseCountBuilder elastic.SearchBuilder

	// 只添加基础条件：task_ids, user_id（参考PHP的$search数组）
	baseCountBuilder.AddMust([]interface{}{"task_id", "in", elastic.ToInterfaceArray(taskIds)})
	baseCountBuilder.AddMust([]interface{}{"user_id", req.UserId})

	// 注意：这里不添加任何筛选条件（如title、domain、cert等），
	// 因为统计数量应该基于所有符合基础条件的数据，而不是当前列表的筛选结果
	// 这与PHP逻辑一致：unset($param['status']) 后只保留基础条件

	baseCountQuery := baseCountBuilder.Build()

	// 已确认资产
	claimedQuery := [][]interface{}{}
	for _, q := range baseCountQuery {
		claimedQuery = append(claimedQuery, q)
	}
	claimedQuery = append(claimedQuery, []interface{}{"status", fofaee_assets.STATUS_CLAIMED})
	konwnTableNum, _ = elastic.CountByParams[fofaee_assets.FofaeeAssets](claimedQuery)

	// 未确认资产
	defaultQuery := [][]interface{}{}
	for _, q := range baseCountQuery {
		defaultQuery = append(defaultQuery, q)
	}
	defaultQuery = append(defaultQuery, []interface{}{"status", fofaee_assets.STATUS_DEFAULT})
	unknownTableNum, _ = elastic.CountByParams[fofaee_assets.FofaeeAssets](defaultQuery)

	// 威胁资产
	threatenQuery := [][]interface{}{}
	for _, q := range baseCountQuery {
		threatenQuery = append(threatenQuery, q)
	}
	threatenQuery = append(threatenQuery, []interface{}{"status", fofaee_assets.STATUS_THREATEN})
	threatTableNum, _ = elastic.CountByParams[fofaee_assets.FofaeeAssets](threatenQuery)

	// 9. 构建响应
	rsp.KonwnTableNum = konwnTableNum
	rsp.UnknownTableNum = unknownTableNum
	rsp.ThreatTableNum = threatTableNum
	rsp.CurrentPage = req.Page
	rsp.PerPage = req.PerPage
	rsp.Total = total
	rsp.Condition = condition

	// 转换结果为JSON
	resultJSON, err := json.Marshal(resultList)
	if err != nil {
		log.WithContextErrorf(ctx, "序列化结果失败: %v", err)
		return err
	}
	rsp.Data = string(resultJSON)

	return nil
}

// changeState 转换在线状态
func changeState(state int) string {
	switch state {
	case 1:
		return "在线"
	case 0:
		return "离线"
	default:
		return "未知"
	}
}

// convertToStringSlice 将任意类型的切片转换为字符串切片
func convertToStringSlice(data []any) []string {
	result := make([]string, 0, len(data))
	for _, v := range data {
		if str, ok := v.(string); ok {
			result = append(result, str)
		}
	}
	return result
}

// getMapValueSafe 安全获取map中的值
func getMapValueSafe(m interface{}, key string) interface{} {
	if m == nil {
		return ""
	}

	if mapData, ok := m.(map[string]interface{}); ok {
		if val, exists := mapData[key]; exists {
			return val
		}
	}

	return ""
}

// copyQuery 复制查询条件
func copyQuery(query [][]interface{}) [][]interface{} {
	result := make([][]interface{}, len(query))
	for i, condition := range query {
		result[i] = make([]interface{}, len(condition))
		copy(result[i], condition)
	}
	return result
}
