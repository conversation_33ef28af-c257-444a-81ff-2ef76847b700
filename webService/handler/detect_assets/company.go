package detect_assets

import (
	"context"
	"errors"
	"fmt"
	core "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	sql "micro-service/middleware/mysql/company_icp"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"go-micro.dev/v4/metadata"
)

// GetCompanyDropList 获取公司下拉列表
func GetCompanyDropList(ctx context.Context, req *pb.CompanyDropListRequest, rsp *pb.CompanyDropListResponse) error {
	// 验证请求参数
	if req.Name == "" {
		return errors.New("name is required")
	}
	// 检查缓存
	cacheKey := fmt.Sprintf("detect_assets_company_list:%s", req.Name)
	if redis.GetCache(cacheKey, &rsp) {
		log.Infof("[DetectAssets]: GetCompanyDropList -> Cache: %s", cacheKey)
		return nil
	}
	var err error
	result := &core.QCCNameSearchResponse{}
	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		err = core.HttpClient(http.MethodGet, "/api/v1/qcc/company_search/"+req.Name, nil, result)
	} else {
		// 否则rpc微服务调用
		meta, _ := metadata.FromContext(ctx)
		param := &core.QCCNameSearchRequest{Search: req.Name, ClientId: meta["Client_id"]}
		result, err = core.GetProtoCoreClient().QCCNameSearch(ctx, param, utils.SetRpcTimeoutOpt(45))
	}
	if err != nil {
		log.Errorf("[DetectAssets]: GetCompanyDropList -> QCCNameSearch error: %v", err)
		return err
	}
	rsp.CompanyDetails = qccResultToDropList(result)
	// 如果企查查没有返回结果，则查询本地数据库
	if len(rsp.CompanyDetails) == 0 {
		list, _, err := sql.NewCompanyIcpModel().List(1, 5, mysql.WithLRLike("name", req.Name))
		if err != nil {
			log.Errorf("[DetectAssets]: GetCompanyDropList -> MySQL List error: %v", err)
			return err
		}
		for _, item := range list {
			rsp.CompanyDetails = append(rsp.CompanyDetails, &pb.CompanyDetail{Name: item.Name})
		}
	}
	// 缓存结果
	if len(rsp.CompanyDetails) == 0 {
		if !redis.SetCache(cacheKey, time.Hour, rsp.CompanyDetails) {
			log.Warnf("[DetectAssets]: GetCompanyDropList -> Cache set failed!")
		}
	}
	return nil
}

// qccResultToDropList 将企查查的结果转换为下拉列表格式
func qccResultToDropList(result *core.QCCNameSearchResponse) []*pb.CompanyDetail {
	companyDetails := make([]*pb.CompanyDetail, 0)
	for _, item := range result.Data {
		companyDetails = append(companyDetails, &pb.CompanyDetail{
			Name:   item.Name,
			Reason: item.HitReason,
		})
	}
	return companyDetails
}

// GetCompanyCascadeEquity 通过公司id查询子级股权分布信息
func GetCompanyCascadeEquity(ctx context.Context, req *pb.GetCompanyCascadeEquityRequest, rsp *pb.GetCompanyCascadeEquityResponse) error {
	companyName := req.CompanyId
	cacheKey := fmt.Sprintf("companyEquity:cache:%s", companyName)
	var err error

	log.Infof("[Cloud]: GetCompanyCascadeEquity -> 开始处理请求: company_id=%s, no_cache=%d, not_need_icp=%d",
		companyName, req.NoCache, req.NotNeedIcp)
	// 检查是否需要清除缓存
	if req.NoCache == 1 {
		err := redis.DelKey(ctx, cacheKey)
		if err != nil {
			log.Warnf("[Cloud]: GetCompanyCascadeEquity -> Cache delete failed: %v", err)
		}
	}
	// 从缓存中获取数据，如果不存在则从外部API获取
	if ok := redis.GetCache(cacheKey, rsp.Result); !ok {
		log.Infof("[Cloud]: GetCompanyCascadeEquity -> 缓存未命中，开始查询数据")
		// 模拟从外部API获取数据
		rsp.Result, err = getEquityInfo(ctx, companyName, req.NoCache == 1, 50)
		if err != nil {
			log.Errorf("[Cloud]: GetCompanyCascadeEquity -> Get equity info error: %v", err)
			return err
		}
		log.Infof("[Cloud]: GetCompanyCascadeEquity -> 查询完成，结果数量: %d", len(rsp.Result))
		redis.SetCache(cacheKey, time.Hour, rsp.Result)
	} else {
		log.Infof("[Cloud]: GetCompanyCascadeEquity -> 缓存命中，结果数量: %d", len(rsp.Result))
	}
	// 判断是否需要ICP
	if req.NotNeedIcp != 1 {

		if cfg.ExecGolangJob() {
			//todo 调用go的job
		} else {
			// 调用PHP的CompanyIcpLoadJob
			// 构建线索公司数组
			clueCompanyArray := make([]string, 0, len(rsp.Result))
			for _, item := range rsp.Result {
				if item.CompanyName != "" {
					clueCompanyArray = append(clueCompanyArray, item.CompanyName)
				}
			}

			// 从上下文获取用户ID
			meta, _ := metadata.FromContext(ctx)
			userId := meta["User_id"]
			if userId == "" {
				userId = "0" // 默认值
			}

			// 使用 req.CompanyId 作为 parentId，对应PHP中的 $params['company_id']
			parentId := req.CompanyId

			// 调用PHP job
			err := asyncq.CompanyIcpLoadJob.Dispatch(clueCompanyArray, userId, parentId)
			if err != nil {
				log.WithContextErrorf(ctx, "[GetCompanyCascadeEquity] 调用CompanyIcpLoadJob失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "[GetCompanyCascadeEquity] 成功调用CompanyIcpLoadJob: 公司数量=%d, 用户ID=%s, 公司ID=%s",
					len(clueCompanyArray), userId, parentId)
			}
		}
	}
	// 按rate排序
	sort.Slice(rsp.Result, func(i, j int) bool {
		return rsp.Result[i].Rate > rsp.Result[j].Rate
	})
	return nil
}

// getEquityInfo 获取公司股权穿透信息
func getEquityInfo(ctx context.Context, companyName string, noCache bool, rate int) (rsp []*pb.CompanyEquityResult, err error) {
	log.Infof("[Cloud]: getEquityInfo -> 开始查询公司股权信息: company=%s, noCache=%v, rate=%d", companyName, noCache, rate)

	// 统一使用 core 服务获取完整的股权穿透数据
	// 这样可以确保数据的完整性和实时性，避免数据库同步延迟问题
	log.Infof("[Cloud]: getEquityInfo -> 调用 core 服务获取完整股权穿透数据")
	result := &core.QCCInvestmentThroughResponse{}
	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用saas
		err = core.HttpClient(http.MethodGet, "/api/v1/qcc/investment_through/"+companyName+"?force="+utils.If(noCache, "1", "0"), nil, result)
	} else {
		// 否则rpc微服务调用
		meta, _ := metadata.FromContext(ctx)
		param := core.QCCInvestmentThroughRequest{
			Search:   companyName,
			ClientId: meta["Client_id"],
			Force:    int32(utils.If(noCache, 1, 0)),
		}
		result, err = core.GetProtoCoreClient().QCCInvestmentThrough(ctx, &param, utils.SetRpcTimeoutOpt(60))
	}
	if err != nil {
		log.Errorf("[Cloud]: getEquityInfo -> QCCInvestmentThrough error: %v", err)
		return nil, err
	}

	log.Infof("[Cloud]: getEquityInfo -> QCCInvestmentThrough success, got %d records for company %s", len(result.List), companyName)

	// 处理父级公司的第一层子公司数据
	log.Infof("[Cloud]: getEquityInfo -> 开始处理父级公司的第一层子公司数据，根公司数量: %d", len(result.List))

	for i, rootItem := range result.List {
		log.Infof("[Cloud]: getEquityInfo -> 根公司[%d]: %s, 子公司数量: %d", i+1, rootItem.Name, len(rootItem.ChildrenList))

		// 处理根公司的第一层子公司
		for j, childItem := range rootItem.ChildrenList {
			log.Infof("[Cloud]: getEquityInfo -> 处理第一层子公司[%d]: %s, 持股比例: %s", j+1, childItem.Name, childItem.Percent)

			// 解析百分比
			percent := 0.0
			rateStr := childItem.Percent
			if len(childItem.Percent) > 0 {
				if strings.HasSuffix(childItem.Percent, "%") {
					percentStr := childItem.Percent[:len(childItem.Percent)-1]
					percent, _ = strconv.ParseFloat(percentStr, 64)
					rateStr = fmt.Sprintf("%.2f%%", percent)
				} else {
					percent, _ = strconv.ParseFloat(childItem.Percent, 64)
					rateStr = fmt.Sprintf("%.2f%%", percent)
				}
			}

			// 添加子公司到结果集
			rsp = append(rsp, &pb.CompanyEquityResult{
				CompanyId:   childItem.Name,
				CompanyName: childItem.Name,
				RegStatus:   childItem.RegStatus,
				Rate:        rateStr,
				ColorFlag:   percent < float64(rate),
			})

			log.Infof("[Cloud]: getEquityInfo -> 添加子公司 %s 到结果集，当前结果集大小: %d", childItem.Name, len(rsp))
		}
	}

	log.Infof("[Cloud]: getEquityInfo -> core 服务数据处理完成，返回%d条记录", len(rsp))
	return rsp, nil
}
