package detect_assets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql"
	dat "micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/foradar_report"
	"micro-service/middleware/mysql/report_template"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	pb "micro-service/webService/proto"
	"time"

	"github.com/spf13/cast"

	"gorm.io/gorm"
)

// CreateReport 创建测绘报告
// 对应PHP中的createReport方法
func CreateReport(ctx context.Context, req *pb.CreateReportRequest, _ *pb.Empty) error {
	// 获取用户ID和公司ID
	userId := req.UserId
	companyId := req.CompanyId
	expendId := req.ExpendId

	// 查询测绘任务
	detectInfo, err := dat.NewModel().First(
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("id", expendId),
	)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("该任务不存在，参数错误!")
		}
		return err
	}

	// 检查任务是否存在（dat.First在记录不存在时返回nil, nil）
	if detectInfo == nil {
		return fmt.Errorf("该任务不存在，参数错误!")
	}

	// 创建报告模板
	templateName := "单位资产测绘报告模版_" + time.Now().Format("2006-01-02 15:04:05")
	if detectInfo.ExpandSource != 0 {
		templateName = "云端推荐任务报告模版_" + time.Now().Format("2006-01-02 15:04:05")
	}

	// 构建已知资产参数
	knowParam := map[string]interface{}{
		"is_all_rule":            true,
		"is_all_company":         true,
		"tags":                   []int{fofaee_assets.SAFE_REC, fofaee_assets.CLIENT_REC, fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN, fofaee_assets.CLIENT_IMPORT},
		"detect_assets_tasks_id": expendId,
		"user_id":                userId,
		"status":                 []int{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD},
	}

	// 构建未知资产参数
	unknowParam := map[string]interface{}{
		"user_id":                userId,
		"type":                   fofaee_assets.TYPE_RECOMMEND,
		"status":                 fofaee_assets.STATUS_DEFAULT,
		"detect_assets_tasks_id": expendId,
		"is_all_rule":            true,
		"tags":                   []int{fofaee_assets.SAFE_REC, fofaee_assets.CLIENT_REC, fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN, fofaee_assets.CLIENT_IMPORT},
		"is_all_company":         true,
	}

	// 构建威胁资产参数
	threatenParam := map[string]interface{}{
		"user_id":                userId,
		"status":                 fofaee_assets.STATUS_THREATEN,
		"detect_assets_tasks_id": expendId,
		"is_all_rule":            true,
		"tags":                   []int{fofaee_assets.SAFE_REC, fofaee_assets.CLIENT_REC, fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN, fofaee_assets.CLIENT_IMPORT},
		"is_all_company":         true,
	}

	// 构建事件参数
	eventParam := map[string]interface{}{
		"user_id":                userId,
		"detect_assets_tasks_id": expendId,
	}

	// 构建数字资产和数据泄露参数
	appAssetsParam := map[string]interface{}{
		"type":    []int{100},
		"status":  []int{300},
		"user_id": userId,
	}

	sensitiveDataParam := map[string]interface{}{
		"type":    []int{200},
		"status":  []int{400},
		"user_id": userId,
	}

	// 序列化参数
	knowParamJson, _ := json.Marshal(knowParam)
	unknowParamJson, _ := json.Marshal(unknowParam)
	threatenParamJson, _ := json.Marshal(threatenParam)
	eventParamJson, _ := json.Marshal(eventParam)
	appAssetsParamJson, _ := json.Marshal(appAssetsParam)
	sensitiveDataParamJson, _ := json.Marshal(sensitiveDataParam)

	// 创建报告模板
	template := &report_template.ReportTemplate{
		Name:                templateName,
		UserId:              userId,
		CompanyId:           companyId,
		Type:                2,
		DetectAssetsTasksId: expendId,
		ReportModule:        report_template.ReportModules{1, 2, 6},
	}

	err = report_template.NewModel().Create(template)
	if err != nil {
		log.Errorf("[CreateReport] 创建报告模板失败: %v", err)
		return err
	}

	// 更新自定义字段
	customFields := map[string]interface{}{
		"know_assets_param":     string(knowParamJson),
		"unknow_assets_param":   string(unknowParamJson),
		"threaten_assets_param": string(threatenParamJson),
		"warning_event_param":   string(eventParamJson),
		"app_assets_param":      string(appAssetsParamJson),
		"sensitive_data_param":  string(sensitiveDataParamJson),
		"email":                 "",
		"time_type":             1,
	}

	err = report_template.NewModel().UpdateAny(customFields,
		report_template.WithUserId(userId),
		report_template.WithDetectAssetsTasksId(expendId))
	if err != nil {
		log.Errorf("[CreateReport] 更新报告模板自定义字段失败: %v", err)
	}

	// 获取报告名称
	reportName := req.Name
	if reportName == "" {
		reportName = time.Now().Format("2006-01-02 15:04:05") + " 报告"
	}

	// 创建报告
	report := &foradar_report.ForadarReport{
		UserId:           userId,
		CompanyId:        companyId,
		ReportTemplateId: uint64(template.ID),
		Name:             reportName,
		Status:           foradar_report.StatusDefault,
		CreateStatus:     foradar_report.StatusDefault,
	}

	err = foradar_report.NewModel().Create(report)
	if err != nil {
		log.Errorf("[CreateReport] 创建报告失败: %v", err)
		return err
	}

	if cfg.ExecGolangJob() {
		//todo 调用go的job
		err = asyncq.CreateForadarReportJob.Dispatch(uint64(template.ID), uint64(report.ID), cast.ToInt(req.IsGuangfa))
		if err != nil {
			log.Errorf("[CreateReport] 调用生成报告任务失败: %v", err)
			return err
		}
	} else {
		// 调用生成报告事件
		err = asyncq.CreateForadarReportJob.Dispatch(uint64(template.ID), uint64(report.ID), cast.ToInt(req.IsGuangfa))
		if err != nil {
			log.Errorf("[CreateReport] 调用生成报告任务失败: %v", err)
			return err
		}
	}
	return nil
}
