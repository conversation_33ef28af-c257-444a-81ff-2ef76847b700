package url_detect

import (
	"errors"
	"github.com/spf13/cast"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/url_status_detect"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"path"
	"path/filepath"
)

type UrlStatusDetectInfo struct {
	UnonlineAssets []url_status_detect.UrlStatusDetectResult
	OnlineAssets   []url_status_detect.UrlStatusDetectResult
}

// GetUrlStatusDetectInfo 获取某一个table资产详情
func GetUrlStatusDetectInfo(in *pb.GetUrlStatusDetectInfoRequest) (*pb.GetUrlStatusDetectInfoResponse, error) {
	has, err := url_status_detect.NewUrlStatusDetectTaskModel(mysql.GetInstance()).HasTaskID(in.UserId, uint(in.TaskId))
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, errors.New("任务不存在")
	}

	d, total, err := url_status_detect.NewUrlStatusDetectResultModel(mysql.GetInstance()).
		FindByTaskID(in.TaskId, int(in.CurrentPage), int(in.PerPage))
	if err != nil {
		return nil, err
	}
	totaOnline, _ := url_status_detect.NewUrlStatusDetectResultModel(mysql.GetInstance()).
		CountFindByTaskID(in.TaskId, 1)
	totaOffline, _ := url_status_detect.NewUrlStatusDetectResultModel(mysql.GetInstance()).
		CountFindByTaskID(in.TaskId, 2)

	resp := &pb.GetUrlStatusDetectInfoResponse{}
	if total == 0 {
		return resp, nil
	}

	resp.Total = total
	resp.OfflineNum = totaOffline
	resp.OnlineNum = totaOnline
	resp.CurrentPage = in.CurrentPage
	resp.PerPage = in.PerPage
	datas := make([]*pb.GetUrlStatusDetectInfo, 0, len(d))
	for i := range d {
		createdAtStr := d[i].CreatedAt.Format("2006-01-02 15:04:05")
		datas = append(datas, &pb.GetUrlStatusDetectInfo{Ip: d[i].Ip,
			Port: d[i].Port, Url: d[i].Url, Protocol: d[i].Protocol,
			OnlineState: int64(d[i].OnlineState),
			Title:       d[i].Title,
			CreatedAt:   createdAtStr,
			StatusCode:  cast.ToString(d[i].StatusCode)})
	}
	resp.Items = datas
	return resp, nil
}

// GetUrlStatusDetectList 获取任务列表
func GetUrlStatusDetectList(in *pb.GetUrlStatusDetectListRequest) (*pb.GetUrlStatusDetectListResponse, error) {
	data, total, err := url_status_detect.NewUrlStatusDetectTaskModel(mysql.GetInstance()).
		FindByUserID(in.UserId, in.TaskId, int(in.CurrentPage), int(in.PerPage), in.UserName, in.CreatedAtRange)
	if err != nil {
		return nil, err
	}
	log.Infof("[Url Status Detect] Get Url Status Detect List, Request: %v", in)

	resp := &pb.GetUrlStatusDetectListResponse{}
	if total > 0 {
		resp.Total = total
		resp.CurrentPage = in.CurrentPage
		resp.PerPage = in.PerPage
	}

	datas := make([]*pb.GetUrlStatusDetectList, 0, len(data))
	for i := range data {
		filePath, _ := utils.LaravelEncrypt(utils.DownloadFile{
			Url:  filepath.Join(storage.GetPublicStoragePath(), data[i].FilePath),
			Name: "Url状态检测" + utils.GetExt(data[i].FilePath),
		})
		d := &pb.GetUrlStatusDetectList{
			ImportAssets:   data[i].Total,
			ProcessedData:  data[i].ProcessedData,
			Status:         int64(data[i].ProgressState),
			OnlineAssets:   data[i].OnlineAssets,
			UnonlineAssets: data[i].UnonlineAssets,
			Operator:       data[i].UserName,
			UserId:         data[i].UserId,
			//FilePath:       filepath.Join(storage.GetDownloadPrefix(), filePath+path.Ext(data[i].FilePath)),
			Id:   uint64(data[i].ID),
			Date: data[i].CreatedAt.Format(utils.DateTimeLayout),
		}
		if in.GetFilePath {
			d.FilePath = filepath.Join(storage.GetDownloadPrefix(), filePath+path.Ext(data[i].FilePath))
		}
		datas = append(datas, d)
	}
	resp.Items = datas
	return resp, nil
}

// DelUrlStatusDetectTask 删除任务
func DelUrlStatusDetectTask(in *pb.DelUrlStatusDetectTaskRequest) error {
	dels := []uint{}
	for i := 0; i < len(in.TaskIds); i++ {
		dels = append(dels, uint(in.TaskIds[i]))
	}

	err := url_status_detect.NewUrlStatusDetectTaskModel(mysql.GetInstance()).
		Del(dels, in.UserId, in.CreatedAtRange)
	if err != nil {
		log.Errorf("[Url Status Detect] Delete Url Status Detect Task -> TABLE:url_status_detect_task, err: %s", err.Error())
		return err
	}
	return nil
}
