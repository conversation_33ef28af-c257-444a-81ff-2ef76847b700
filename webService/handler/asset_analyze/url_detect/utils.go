package url_detect

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"golang.org/x/net/html"
	"golang.org/x/net/html/charset"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"io"
	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"mime"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
)

const (
	HTTP  = "http"
	HTTPS = "https"
	SIG   = true
)

// detectAndConvertEncoding 检测并转换字符编码
func detectAndConvertEncoding(body []byte, contentType string) (io.Reader, error) {
	// 首先尝试从Content-Type头部获取编码信息
	if contentType != "" {
		if enc := getEncodingFromContentType(contentType); enc != nil {
			log.Infof("[编码转换] 从Content-Type检测到编码，开始转换")
			reader := transform.NewReader(bytes.NewReader(body), enc.NewDecoder())
			return reader, nil
		}
	}

	// 如果Content-Type中没有编码信息，使用charset包自动检测
	reader, err := charset.NewReader(bytes.NewReader(body), "")
	if err != nil {
		log.Warnf("[编码转换] 自动检测编码失败: %v，尝试常见中文编码", err)
		// 如果自动检测失败，尝试常见的中文编码
		return tryCommonChineseEncodings(body)
	}

	log.Infof("[编码转换] 自动检测编码成功")
	return reader, nil
}

// getEncodingFromContentType 从Content-Type头部提取编码信息
func getEncodingFromContentType(contentType string) encoding.Encoding {
	contentType = strings.ToLower(contentType)

	if strings.Contains(contentType, "charset=gb2312") || strings.Contains(contentType, "charset=gb-2312") {
		log.Infof("[编码检测] 检测到GB2312编码")
		return simplifiedchinese.GB18030 // GB18030兼容GB2312
	}
	if strings.Contains(contentType, "charset=gbk") {
		log.Infof("[编码检测] 检测到GBK编码")
		return simplifiedchinese.GBK
	}
	if strings.Contains(contentType, "charset=gb18030") {
		log.Infof("[编码检测] 检测到GB18030编码")
		return simplifiedchinese.GB18030
	}

	return nil
}

// tryCommonChineseEncodings 尝试常见的中文编码
func tryCommonChineseEncodings(body []byte) (io.Reader, error) {
	// 尝试GB18030（兼容GB2312和GBK）
	log.Infof("[编码转换] 尝试GB18030编码")
	reader := transform.NewReader(bytes.NewReader(body), simplifiedchinese.GB18030.NewDecoder())

	// 读取一小部分来验证编码是否正确
	testBuf := make([]byte, 1024)
	n, err := reader.Read(testBuf)
	if err != nil && err != io.EOF {
		log.Warnf("[编码转换] GB18030编码转换失败: %v，使用原始数据", err)
		return bytes.NewReader(body), nil
	}

	// 检查转换后的文本是否包含乱码字符
	testStr := string(testBuf[:n])
	if strings.Contains(testStr, "�") {
		log.Warnf("[编码转换] GB18030转换后仍有乱码，使用原始数据")
		return bytes.NewReader(body), nil
	}

	log.Infof("[编码转换] GB18030编码转换成功")
	// 重新创建reader，因为之前的已经被读取了
	return transform.NewReader(bytes.NewReader(body), simplifiedchinese.GB18030.NewDecoder()), nil
}

func waitFileSync(file string) {
	log.Infof("[Assets Status Detect]等待上传文件%s同步", file)

	var index int
	total := 100 // 最大等待50s
	for {
		index++
		time.Sleep(200 * time.Millisecond)
		b := utils.FileIsExist(file)
		if b || index > total {
			return
		}
	}
}

func splitSlice(arr []string, n int) [][]string {
	// 计算每一份切片的长度
	size := (len(arr) + n - 1) / n

	// 将原始切片分割成多个子切片，并添加到二维切片中
	batches := make([][]string, 0)
	for i := 0; i < len(arr); i += size {
		end := i + size
		if end > len(arr) {
			end = len(arr)
		}
		batches = append(batches, arr[i:end])
	}

	return batches
}

func readExcel(path string) ([]string, int, error) {
	excelResp, err := excel.ReadExcel(path)
	if err != nil {
		return nil, 0, err
	}
	uniqueUrl := []string{}

	var validateCnt int
	for i := 1; i < len(excelResp); i++ {
		url, err := getUrl(i, excelResp[i])
		if err != nil {
			return nil, 0, err
		}
		if url == "" {
			continue
		}
		validateCnt++
		if url != "" {
			// 检测是否存在key
			// 如果已存在key，忽略
			// 如果不存在key, 写默认数据
			if !utils.ListContains(uniqueUrl, url) {
				uniqueUrl = append(uniqueUrl, url)
			}
		}
		if validateCnt > 5000 {
			return nil, 0, ErrNumExceed
		}
	}
	total := len(uniqueUrl)
	return uniqueUrl, total, nil
}

const maxValidateNum = 5000

var ErrNumExceed = fmt.Errorf("导入URL超过最大限制数%d条", maxValidateNum)

func getUrl(lineNum int, d []string) (string, error) {
	if len(d) == 0 {
		return "", nil
	}
	var urlstring string
	var centerUrl string
	if len(d) > 0 {
		centerUrl = strings.TrimSpace(d[0]) // 去除字符串两端的空白字符
		parsedURL, err := url.Parse(centerUrl)
		if err != nil {
			return "", nil
		}
		// 验证URL的有效性
		if parsedURL.Scheme == "" || parsedURL.Host == "" {
			return "", nil
		}
		urlstring = centerUrl
	}
	if urlstring == "" {
		return "", nil
	}
	return urlstring, nil
}

func writeExcel(path string, data *UrlStatusDetectInfo) error {
	online := make([][]string, 0, len(data.OnlineAssets))
	online = append(online, []string{"IP", "端口", "协议", "标题", "状态码", "URL"})
	for i := range data.OnlineAssets {
		ipOnline := data.OnlineAssets[i].Ip
		portOnline := data.OnlineAssets[i].Port
		protocolOnline := data.OnlineAssets[i].Protocol
		titleOnline := data.OnlineAssets[i].Title
		statusCodeOnline := cast.ToString(data.OnlineAssets[i].StatusCode)
		if statusCodeOnline == "0" {
			statusCodeOnline = ""
		}
		urlOnline := data.OnlineAssets[i].Url
		online = append(online, []string{ipOnline, portOnline, protocolOnline, titleOnline, statusCodeOnline, urlOnline})
	}

	unonline := make([][]string, 0, len(data.UnonlineAssets))
	unonline = append(unonline, []string{"IP", "端口", "协议", "标题", "状态码", "URL"})
	for i := range data.UnonlineAssets {
		ip := data.UnonlineAssets[i].Ip
		port := data.UnonlineAssets[i].Port
		protocol := data.UnonlineAssets[i].Protocol
		title := data.UnonlineAssets[i].Title
		statusCode := cast.ToString(data.UnonlineAssets[i].StatusCode)
		if statusCode == "0" {
			statusCode = ""
		}
		url := data.UnonlineAssets[i].Url
		unonline = append(unonline, []string{ip, port, protocol, title, statusCode, url})
	}

	sheet := map[string][][]string{
		"可访问URL":  online,
		"不可访问URL": unonline,
	}
	return excel.WriteMultiSheet(path, sheet)
}

// #nosec
func parseURL(urlStr string) (scanResult, error) {
	info := scanResult{}
	info.Url = urlStr

	u, err := url.Parse(urlStr)
	if err != nil {
		log.Errorf("[Url Status handleDetect] parseurl报错了 -> handleDetect, err: %s", err)
		return info, err
	}

	host, port, _ := net.SplitHostPort(u.Host)
	if host == "" {
		host = u.Host
	}

	if port == "" {
		switch u.Scheme {
		case HTTP:
			port = "80"
		case HTTPS:
			port = "443"
		}
	}

	ips, err := net.LookupIP(host)
	if err == nil && len(ips) > 0 {
		info.IP = ips[0].String()
		// 如果是内网的ip，直接返回数据为空就行
		if ips[0].IsLoopback() || ips[0].IsPrivate() {
			log.Errorf("[Url Status handleDetect] 如果是内网的ip，直接返回数据为空就行, urlStr: %s", urlStr)
			return info, nil
		}
	}
	info.Port = port
	info.Protocol = u.Scheme
	info.OnlineState = 2
	tr := &http.Transport{
		// InsecureSkipVerify 设置为 true 就表示禁用了 SSL 证书的验证。
		TLSClientConfig: &tls.Config{InsecureSkipVerify: SIG},
	}
	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
		Transport: tr,
		Timeout:   40 * time.Second,
	}
	resp, err := client.Get(u.String())
	if err != nil {
		// Handle error and return
		info.OnlineState = 2
		log.Errorf("[Url Status handleDetect] clientGet-urlString报错了 -> handleDetect, err: %s", err)
		return info, err
	}
	defer resp.Body.Close()

	info.StatusCode = cast.ToString(resp.StatusCode)
	code := cast.ToInt(info.StatusCode)

	if resp.StatusCode >= 300 && resp.StatusCode <= 399 {
		location, err := resp.Location()
		var newUrl string
		if err != nil || location == nil {
			// 如果在获取Location头部时发生错误，或者Location头部为空，
			// 则使用convertToHTTPS函数将URL转换为HTTPS
			log.Errorf("[Url Status handleDetect] Error getting location from redirect: %v", err)
			newUrl = convertToHTTPS(u.String())
		} else {
			newUrl = location.String()
		}
		log.Infof("[parseURL-重定向-取标题信息] contentType : %s url :%s", newUrl, u.String())
		newClient := &http.Client{
			Transport: tr,
			Timeout:   40 * time.Second,
		}

		respNew, errNew := newClient.Get(newUrl)
		if errNew != nil {
			info.OnlineState = 2
			return info, errNew
		}
		defer respNew.Body.Close()

		info.Title = getTitleAndCode(respNew, u.String())
		info.OnlineState = 1
		if respNew.StatusCode > 399 {
			info.OnlineState = 2
		}
		if info.Title != "" {
			info.OnlineState = 1
		}
		// 401是认为在线的，通常表示客户端尝试访问需要身份验证的资源，但没有提供正确的身份验证凭证，即“未授权”。
		if code == 401 {
			info.OnlineState = 1
		}
	} else {
		info.Title = getTitleAndCode(resp, u.String())
		info.OnlineState = 1
		if code > 399 {
			info.OnlineState = 2
		}
		if info.Title != "" {
			info.OnlineState = 1
		}
		// 401是认为在线的，通常表示客户端尝试访问需要身份验证的资源，但没有提供正确的身份验证凭证，即“未授权”。
		if code == 401 {
			info.OnlineState = 1
		}
	}
	return info, nil
}

func getTitle(body io.Reader, urlstring string) string {
	z := html.NewTokenizer(body)
	foundTitle := false
	ogTitle := ""

	// 检查是否包含 "mp.weixin.qq.com" 字符
	containsWeixin := strings.Contains(urlstring, "mp.weixin.qq.com")

	for {
		tt := z.Next()
		if tt == html.ErrorToken {
			break
		}
		if tt == html.StartTagToken || tt == html.SelfClosingTagToken {
			t := z.Token()
			if t.Data == "title" {
				tt = z.Next()
				if tt == html.TextToken {
					return z.Token().Data
				}
			} else if containsWeixin && t.Data == "meta" {
				for _, attr := range t.Attr {
					if attr.Key == "property" && attr.Val == "og:title" {
						for _, attr := range t.Attr {
							if attr.Key == "content" {
								ogTitle = attr.Val
							}
						}
					}
				}
			}
		}
	}

	// 如果找不到<title>标签，返回<meta property="og:title">标签的内容
	if !foundTitle && ogTitle != "" {
		return ogTitle
	}

	return ""
}

// 解析 URL 中的 IP、端口号和协议信息
func getIpandPortandPro(url string) (ip, port, protocol string, err error) {
	// 定义正则表达式
	re := regexp.MustCompile(`^(https?)://([0-9a-zA-Z.]+):?(\d*)`)

	// 使用正则表达式匹配 URL
	matches := re.FindStringSubmatch(url)
	if len(matches) != 4 {
		return "", "", "", fmt.Errorf("Invalid URL format")
	}

	// 提取 IP、端口号和协议信息
	protocol = matches[1]
	ip = matches[2]
	// 校验是否是ip
	ipCheck := isValidIPAddress(ip)
	if !ipCheck {
		ip = ""
	}
	if matches[3] != "" {
		port = matches[3]
	} else {
		port = "80"
		if protocol == HTTPS {
			port = "443"
		}
	}
	return ip, port, protocol, nil
}

// 判断 IP 地址是否为合法格式
func isValidIPAddress(ipAddress string) bool {
	return net.ParseIP(ipAddress) != nil
}

func convertToHTTPS(url string) string {
	// 将url转换为小写，方便后续比较
	lowerURL := strings.ToLower(url)

	// 判断是否已经是https协议
	if strings.HasPrefix(lowerURL, "https://") {
		// 如果是https协议，直接返回原始url
		return url
	} else if strings.HasPrefix(lowerURL, "http://") {
		// 如果是http协议，将"http://"替换为"https://"
		return "https://" + url[len("http://"):]
	} else {
		// 如果没有协议前缀，则默认添加"https://"作为协议
		return "https://" + url
	}
}

func getTitleAndCode(resp *http.Response, urlstring string) (title string) {
	contentType := resp.Header.Get("Content-Type")
	contentType = strings.ToLower(contentType)
	log.Infof("[parseURL-取标题信息] contentType : %s url :%s", contentType, urlstring)

	// 读取响应体数据
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("[parseURL-读取响应体失败] url: %s, error: %v", urlstring, err)
		return ""
	}

	isDownloadable := strings.Contains(contentType, "application/x-download") ||
		strings.Contains(contentType, "excel") ||
		strings.Contains(contentType, "csv")
	if isDownloadable {
		log.Infof("[parseURL-这个url地址是下载文件的地址啊]contentType :  %s url :%s", contentType, urlstring)
		// 如果响应的Content-Type为"application/octet-stream"或"application/x-download"，表示返回的是下载文件，那么将文件名称赋值给标题
		disposition := resp.Header.Get("Content-Disposition")
		if len(disposition) > 0 {
			_, params, mErr := mime.ParseMediaType(disposition)
			if mErr == nil {
				filename, ok := params["filename"]
				if ok && len(filename) > 0 {
					// 对文件名进行URL解码
					decodedFilename, uErr := url.QueryUnescape(filename)
					if uErr != nil {
						// 解码失败，保留原文件名
						title, _ = url.QueryUnescape(filename)
					} else {
						title, _ = url.QueryUnescape(decodedFilename)
					}
				}
			}
		}
	} else if strings.Contains(contentType, "application/json") {
		log.Infof("[parseURL-这个url地址是json格式的数据啊]contentType :  %s url :%s", contentType, urlstring)
		var data map[string]interface{}
		if dErr := json.NewDecoder(bytes.NewReader(bodyBytes)).Decode(&data); dErr == nil {
			titleBytes, _ := json.Marshal(data)
			title = string(titleBytes)
		} else {
			// JSON解析失败，尝试作为HTML处理
			reader, encErr := detectAndConvertEncoding(bodyBytes, contentType)
			if encErr != nil {
				log.Warnf("[parseURL-编码转换失败] url: %s, error: %v", urlstring, encErr)
				reader = bytes.NewReader(bodyBytes)
			}
			title = getTitle(reader, urlstring)
		}
	} else {
		log.Infof("[parseURL-这个url地址是正常的html页面啊]contentType :  %s url :%s", contentType, urlstring)
		// 检测并转换编码
		reader, encErr := detectAndConvertEncoding(bodyBytes, contentType)
		if encErr != nil {
			log.Warnf("[parseURL-编码转换失败] url: %s, error: %v", urlstring, encErr)
			reader = bytes.NewReader(bodyBytes)
		}
		title = getTitle(reader, urlstring)
	}

	log.Infof("[parseURL-标题提取完成] url: %s, title: %s", urlstring, title)
	return title
}
