package url_detect

import (
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql/url_status_detect"
	"micro-service/middleware/mysql/user"
	url_detect_cache "micro-service/middleware/redis/url_status_detect"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"net"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"

	"go.uber.org/atomic"
	"gorm.io/gorm"
)

// StartUlrStatusDetect 开始url检测
func StartUlrStatusDetect(currentUserID, companyUserId uint64, file string) (uint64, []string, error) {
	filePath := filepath.Join(storage.GetPublicPath(), file)
	// 本地调试文件路径的话，把这个注释打开，上面那个注释掉  {"file":"/Users/<USER>/GolandProjects/micro-service/aaaaaaaa.xlsx"}
	//filePath := "/Users/<USER>/GolandProjects/micro-service/bbbbbb.xlsx"
	// 等待文件同步
	waitFileSync(filePath)

	// 解析excel
	log.Info("[URL状态检测]读取excel文件")
	ips, total, err := readExcel(filePath)
	if err != nil {
		return 0, nil, err
	}
	if len(ips) == 0 {
		return 0, []string{}, errors.New("模版文件中未查询到数据，请重新修改")
	}
	localSettings := cfg.GetInstance().Common.Local
	if cfg.IsLocalClient() || localSettings.Enable {
		for _, v := range ips {
			v = strings.TrimSpace(v) // 去除字符串两端的空白字符
			u, err := url.Parse(v)
			if err != nil {
				return 0, []string{}, errors.New("包含非法的url地址，请重新修改，非法的url地址为：" + v)
			}
			host := u.Hostname()
			ip := net.ParseIP(host)
			if ip != nil {
				if ip.IsLoopback() {
					return 0, []string{}, errors.New("包含非法的内网url地址，请重新修改，非法的url地址为：" + v)
				}
				if ip.IsPrivate() {
					return 0, []string{}, errors.New("包含非法的内网url地址，请重新修改，非法的url地址为：" + v)
				}
			}
		}
	}
	// 创建任务记录表
	u, err := user.NewUserModel(mysql.GetInstance()).FindById(currentUserID)
	if err == gorm.ErrRecordNotFound {
		return 0, []string{}, errors.New("未查询到用户信息")
	}
	if err != nil {
		return 0, []string{}, err
	}

	id := currentUserID
	if companyUserId != currentUserID {
		id = companyUserId
	}

	taskid, err := url_status_detect.NewUrlStatusDetectTaskModel(mysql.GetInstance()).Add(
		url_status_detect.UrlStatusDetectTask{
			Total:          int64(total),
			ProcessedData:  0,
			ProgressState:  url_status_detect.TaskProgressStateIng,
			OnlineAssets:   0,
			UnonlineAssets: 0,
			UserName:       u.Name.String,
			UserId:         id,
			FilePath:       "",
			TaskType:       cast.ToString(2),
		})
	if err != nil {
		log.Errorf("[Url Status Detect] StartAt Url Status Detect -> TABLE:url_status_detect, err: %s", err.Error())
		return 0, []string{}, err
	}
	log.Infof("[Url Status Detect] StartAt Url Status Detect -> TABLE 创建, taskid %d, UserId %d, <===> currentUserID: %d, companyUserId: %d",
		taskid, id, currentUserID, companyUserId)

	// 设置缓存
	cacheKey := url_detect_cache.GetKey(taskid)
	err = url_detect_cache.NewUrlStatusDetectCache(redis.GetInstance()).
		SetCache(cacheKey, taskid, 0.00, url_status_detect.TaskProgressStateIng, cast.ToString(2))
	if err != nil {
		return 0, []string{}, err
	}

	go func() {
		// 获取数据
		out := make(chan scanResult, 200)
		wait := make(chan struct{}, 1)
		stop := make(chan struct{}, 1)

		list := splitSlice(ips, 100)
		for i := range list {
			go handleDetect(list[i], out, stop)
		}

		// 汇总
		resp := aggro(out, stop, wait, len(list), total, uint64(taskid))
		<-wait

		// 更新记录库、结果表，批量
		file := fmt.Sprintf("url_status_detect_%d_%s.xlsx", taskid, utils.RandString(20))
		err := setTaskWithResultTable(uint64(taskid), int64(total), file, resp)
		if err != nil {
			log.Errorf("[Url Status Detect] StartAt Url Status Detect -> wire excel, err: %s", err.Error())
			return
		}

		// 写excel
		writePath := filepath.Join(storage.GetPublicPath(), file)
		log.Infof("[Url Status Detect]  StartAt Assets Status Detect -> write excel, file: %s", writePath)
		err = writeExcel(writePath, resp)
		if err != nil {
			log.Errorf("[Url Status Detect] StartAt Assets Status Detect -> write excel, err: %s", err.Error())
			return
		}
	}()

	return uint64(taskid), []string{}, nil
}

func handleDetect(ips []string, outScanResult chan<- scanResult, stop chan struct{}) {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("[Url Status handleDetect] 解析url异常了 -> handleDetect, err: %s", err)
		}
		stop <- struct{}{}
	}()
	for _, v := range ips {
		scanResultNew, err := parseURL(v)
		if err != nil {
			log.Errorf("[Url Status handleDetect] 这个url异常了 -> handleDetect, err: %s", err)
			info := scanResult{}
			ip, portstring, protocol, err := getIpandPortandPro(v)
			if err != nil {
				info.Url = v
				info.Port = ""
				info.Protocol = ""
				info.OnlineState = 2
				info.Title = ""
				info.StatusCode = ""
				info.IP = ""
			} else {
				info.Url = v
				info.Port = portstring
				info.Protocol = protocol
				info.OnlineState = 2
				info.Title = ""
				info.StatusCode = ""
				info.IP = ip
			}
			scanResultNew = info
		}
		outScanResult <- scanResultNew
	}
}

type scanResult struct {
	Url         string
	IP          string
	Port        string
	Protocol    string
	Title       string
	StatusCode  string
	OnlineState int
}

func aggro(out chan scanResult, stop, wait chan struct{}, num, total int, taskId uint64) *UrlStatusDetectInfo {
	var (
		done     atomic.Int32
		resp     = &UrlStatusDetectInfo{}
		aggr     = 0
		cacheKey = url_detect_cache.GetKey(uint(taskId))
	)

	for {
		select {
		case <-stop:
			aggr++
			if num == aggr {
				close(out)
				stop = nil
			}

		case r, ok := <-out:
			if !ok {
				out = nil
				break
			}
			done.Add(1)
			onlineAssets := 0
			v := r.OnlineState
			if v == 1 {
				onlineAssets++
				OnlineResult := url_status_detect.UrlStatusDetectResult{
					Url:         r.Url,
					Ip:          r.IP,
					Port:        r.Port,
					Protocol:    r.Protocol,
					Title:       r.Title,
					StatusCode:  cast.ToString(r.StatusCode),
					OnlineState: v,
					TaskId:      taskId,
				}
				resp.OnlineAssets = append(resp.OnlineAssets, OnlineResult)
			} else if v == 2 {
				OfflineResult := url_status_detect.UrlStatusDetectResult{
					Url:         r.Url,
					Ip:          r.IP,
					Port:        r.Port,
					Protocol:    r.Protocol,
					Title:       r.Title,
					StatusCode:  cast.ToString(r.StatusCode),
					OnlineState: v,
					TaskId:      taskId,
				}
				resp.UnonlineAssets = append(resp.UnonlineAssets, OfflineResult)
			}
		}

		// 更新检测进度
		progress := progressValue(done.Load(), total)
		updateProgress(taskId, progress, url_status_detect.TaskProgressStateIng)

		log.Infof("[Url Status Detect] Progress --> Set Cache, key:%v, data:%v", cacheKey, progress)

		if out == nil && stop == nil {
			break
		}
	}

	wait <- struct{}{}
	return resp
}

// 计算进度
func progressValue(current int32, total int) float64 {
	f := strconv.FormatFloat(float64(100*current)/float64(total), 'f', 2, 64)
	p, _ := strconv.ParseFloat(f, 64)
	if p >= 100 {
		p = 100
	}
	return p
}

// 更新进度
func updateProgress(taskId uint64, progress float64, status int) {
	client := url_detect_cache.NewUrlStatusDetectCache(redis.GetInstance())
	key := url_detect_cache.GetKey(uint(taskId))

	err := client.SetCache(key, uint(taskId), progress, status, cast.ToString(2))
	if err != nil {
		log.Errorf("[Url Status Detect]Update progress failed, key->%s, %v", key, err)
	}
}

func setTaskWithResultTable(taskId uint64, total int64, filePath string, data *UrlStatusDetectInfo) error {
	defer updateProgress(taskId, 100, url_status_detect.TaskProgressStateDone)

	// 更新记录库
	err := url_status_detect.NewUrlStatusDetectTaskModel(mysql.GetInstance()).Update(
		url_status_detect.UrlStatusDetectTask{
			Model:          gorm.Model{ID: uint(taskId)},
			Total:          total,
			ProcessedData:  total,
			ProgressState:  url_status_detect.TaskProgressStateDone,
			OnlineAssets:   int64(len(data.OnlineAssets)),
			UnonlineAssets: total - int64(len(data.OnlineAssets)),
			FilePath:       filePath,
		})
	if err != nil {
		log.Errorf("[Url Status Detect] StartAt Url Status Detect -> TABLE:url_status_detect_task, err: %s", err.Error())
		return err
	}

	// 结果表，批量
	if len(data.OnlineAssets) > 0 {
		err = url_status_detect.NewUrlStatusDetectResultModel(mysql.GetInstance()).Adds(data.OnlineAssets)
		if err != nil {
			log.Errorf("[Url Status Detect] StartAt Url Status Detect -> TABLE:url_status_detect_result, err: %s", err.Error())
			return err
		}
	}

	if len(data.UnonlineAssets) > 0 {
		err = url_status_detect.NewUrlStatusDetectResultModel(mysql.GetInstance()).Adds(data.UnonlineAssets)
		if err != nil {
			log.Errorf("[Url Status Detect] Url Status Detect -> TABLE:url_status_detect_result, err: %s", err.Error())
			return err
		}
	}

	return nil
}
