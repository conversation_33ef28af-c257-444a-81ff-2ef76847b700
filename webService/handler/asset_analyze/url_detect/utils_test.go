package url_detect

import (
	"fmt"
	"micro-service/initialize/es"
	mysqllib "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

func Test_readaExcel(t *testing.T) {

	//file := "aaaaaaaa.xlsx"
	//filePath := filepath.Join(storage.GetPublicPath(), file)
	//fmt.Println(filePath)
	//r, _, _ := readExcel("./test31.xlsx")
	//fmt.Println(r)
}
func Init() {
	log.Init()
	cfg.InitLoadCfg()
	_ = mysqllib.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	es.GetInstance(cfg.LoadElastic())
}
func Test_parse(t *testing.T) {
	Init()
	urlstring := "http://jd.com"

	fmt.Println(parseURL(urlstring))
}
