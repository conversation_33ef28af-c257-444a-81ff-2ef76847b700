package asset_audit

import (
	"context"
	"fmt"
	"micro-service/middleware/elastic/audit_result"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_SyncToFofaeeAssets(t *testing.T) {
	InitDB()

	list := []audit_result.AuditResult{
		{
			Ip: "*************",
			Ports: []audit_result.ResultPort{
				{Port: 443},
				{Port: 3478},
				{Port: 8080},
			},
		},
		{
			Ip: "**************",
			Ports: []audit_result.ResultPort{
				{Port: 443},
				// {Port: 3478},
				// {Port: 8080},
			},
		},
		{
			Ip: "************",
			Ports: []audit_result.ResultPort{
				{Port: 443, Protocol: "http"},
				{Port: 8080, Protocol: "https"},
			},
		},
	}

	ips, ips2, cnt, err := syncToFofaeeAssets(context.Background(), syncParam{userId: 506, list: list}, nil)
	assert.Nil(t, err)
	fmt.Println(ips, ips2, cnt)
}
