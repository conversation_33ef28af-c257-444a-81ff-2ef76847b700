package asset_audit

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"micro-service/middleware/elastic/audit_result"
	ffa "micro-service/middleware/elastic/fofaee_assets"
	fra "micro-service/middleware/elastic/foradar_assets"
	cache "micro-service/middleware/redis/asset_audit"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/spf13/cast"
)

type auditImportUnit struct {
	SystemName string `json:"system_name"` // 系统名称
	Ip         string `json:"ip"`          // IP
	Port       string `json:"port"`        // 端口
	Protocol   string `json:"protocol"`    // 协议
	Domain     string `json:"domain"`      // 域名
	Url        string `json:"url"`         // URL
	Component  string `json:"component"`   // 组件信息
	StatusCode string `json:"status_code"` // 状态码
}

var fields = [...]string{"system_name", "ip", "port", "protocol", "domain", "url", "component", "status_code"}

func importMarshal(data []string) (info auditImportUnit, err error) {
	m := make(map[string]string, len(data))
	for i := range data {
		m[fields[i]] = data[i]
	}
	bs, err := json.Marshal(m)
	if err != nil {
		return auditImportUnit{}, err
	}

	err = json.Unmarshal(bs, &info)
	return info, err
}

// 数据去重: 以IP和port组合
func distinctData(l []auditImportUnit) []*auditImportUnit {
	if len(l) == 0 {
		return nil
	}

	m := make(map[string]*auditImportUnit, len(l))
	for i := range l {
		m[l[i].Ip+"+"+l[i].Port] = &l[i]
	}

	list := make([]*auditImportUnit, 0, len(m))
	for k := range m {
		list = append(list, m[k])
	}

	return list
}

// IP去重
func distinctIP(l []*auditImportUnit) map[string][]int64 {
	if len(l) == 0 {
		return nil
	}

	m := make(map[string][]int64, len(l))
	for i := range l {
		portInt64, _ := strconv.ParseInt(l[i].Port, 10, 0)
		m[l[i].Ip] = append(m[l[i].Ip], portInt64)
	}

	return m
}

func getDistinctIP(l []*auditImportUnit, isFullIp bool) map[string]struct{} {
	var empty = struct{}{}
	m := make(map[string]struct{}, len(l)/3)

	for i := range l {
		if isFullIp {
			m[utils.IPExpanded(l[i].Ip)] = empty
		} else {
			m[l[i].Ip] = empty
		}
	}
	return m
}

func beginningOfDay(unixSec int64) (t time.Time) {
	x := utils.UnixSecToTime(unixSec)

	t = utils.BeginningOfDay(x)
	return
}

// 数据转换: 读取的excel行转换为易处理的格式
func excelContentConvert(content [][]string, ipLimit int) ([]auditImportUnit, error) {
	list := make([]auditImportUnit, 0, len(content))
	index := 0
	for i := range content {
		if i == 0 || len(content[i]) == 0 { // 第一行或空行跳过, 第一行跳过因为是表头
			continue
		}

		item, err := importMarshal(content[i])
		if err != nil {
			continue
		}
		index++
		if ip := net.ParseIP(item.Ip); ip == nil { // 无效的IP
			return nil, fmt.Errorf("第%d行数据IP无效", i+1)
		}
		if _, err = strconv.Atoi(item.Port); err != nil && item.Port != "" { // 无效的端口
			return nil, fmt.Errorf("第%d行数据端口无效", i+1)
		}
		if index > ipLimit {
			return nil, fmt.Errorf("导入资产数不能超过%d条", auditLinesLimit)
		}

		list = append(list, item)
	}

	return list, nil
}

func updateAuditProgress(ctx context.Context, taskId uint64, progress float32, progressStatus, status int) {
	var msg = "Failed"
	if status == http.StatusOK || status == 0 {
		msg = "Success"
	}
	status = utils.If(status == http.StatusOK, 0, status)

	client := cache.NewCacheClient(0)
	data := cache.NewResponseData(taskId, progress, progressStatus, status, msg)
	err := client.SetValue(context.Background(), taskId, data, 6*30*utils.Day)
	if err != nil {
		log.Errorf("[资产核查]更新资产核查的缓存进度出错, task_id: %d, %+v", taskId, err)
	}
}

// nolint:unused,gocritic
type allAuditResultCount struct {
	Aduit     resultCountUnit `json:"audit,omitempty"`      // 资产核查
	Find      resultCountUnit `json:"find,omitempty"`       // 系统发现
	DataTable resultCountUnit `json:"data_table,omitempty"` // 数据总表
}

type resultCountUnit struct {
	Ip          int64 `json:"ip"`           // IP数量
	Port        int64 `json:"port"`         // 用户导入端口数量-当IP为用户导入时
	Included    int64 `json:"included"`     // 纳入管理
	NotIncluded int64 `json:"not_included"` // 未纳入管理
	NewPort     int64 `json:"new_port"`     // 系统发现
}

//nolint:unused,gocritic
func getResultCountCache(taskId uint, countType uint64) (item resultCountUnit, err error) {
	ctx := context.Background()
	var result allAuditResultCount

	db := cache.NewCacheClient(0)
	content, err := db.GetResultCount(ctx, uint64(taskId))
	if err != nil || content == "" {
		if err != nil {
			log.Errorf("[资产核查]task_id: %d,get result count cache: %v", taskId, err)
		}
		result, err = setResultCountCache(ctx, taskId, []audit_result.AuditResult{}, nil)
		if err != nil {
			return item, err
		}
	} else {
		_ = json.Unmarshal([]byte(content), &result)
	}

	switch countType {
	case ResultTypeAudit:
		item = result.Aduit
	case ResultTypeSystemFind:
		item = result.Find
	case ResultTypeDataTable:
		item = result.DataTable
	}

	return item, nil
}

func setResultCountCache(ctx context.Context, taskId uint, in []audit_result.AuditResult, im []*auditImportUnit) (result allAuditResultCount, err error) {
	var auditList, findList []audit_result.AuditResult
	for i := range in {
		switch in[i].Source {
		case audit_result.AssetSourceImport:
			auditList = append(auditList, in[i])
		case audit_result.AssetSourceSystem:
			findList = append(findList, in[i])
		}
	}
	result.Aduit = countByCategory(auditList, ResultTypeAudit)
	_, result.Aduit.Port = countImportData(im)
	result.Find = countByCategory(findList, ResultTypeSystemFind)
	result.DataTable = countByCategory(in, ResultTypeDataTable)
	if len(in) == 0 {
		result, err = allResultCountByHard(ctx, taskId)
		if err != nil {
			return allAuditResultCount{}, err
		}
	}

	s, _ := json.Marshal(result)
	db := cache.NewCacheClient(0)
	err = db.SetResultCount(ctx, uint64(taskId), string(s))
	if err != nil {
		log.Errorf("[资产核查]task_id: %d,update result count cache: %v", taskId, err)
	}
	return result, nil
}

//nolint:unused,gocritic
func allResultCountByHard(ctx context.Context, taskId uint) (allAuditResultCount, error) {
	var info allAuditResultCount
	var err error
	count := func(Type uint64) resultCountUnit {
		var item resultCountUnit
		if err == nil {
			item, err = resultCountByHard(ctx, uint64(taskId), Type)
		}
		return item
	}

	info.Aduit = count(ResultTypeAudit)
	info.Find = count(ResultTypeSystemFind)
	info.DataTable = count(ResultTypeDataTable)
	if err != nil {
		return info, err
	}
	return info, nil
}

func countByCategory(list []audit_result.AuditResult, category int) resultCountUnit {
	var countResult resultCountUnit
	countResult.Ip = int64(len(list))
	for i := range list {
		for j := range list[i].Ports {
			if list[i].Ports[j].Port != 0 {
				if list[i].Ports[j].IsIncluded != audit_result.AssetSystemFind && category == ResultTypeAudit {
					countResult.Port++
				} else if category != ResultTypeAudit {
					countResult.Port++
				}
			}
			switch list[i].Ports[j].IsIncluded {
			case audit_result.AssetIncluded:
				countResult.Included++
			case audit_result.AssetNotIncluded:
				countResult.NotIncluded++
			case audit_result.AssetSystemFind:
				countResult.NewPort++
			}
		}
	}
	return countResult
}

func countImportData(l []*auditImportUnit) (ip, port int64) {
	distinct := distinctIP(l)
	ip = int64(len(distinct))
	for k := range distinct {
		ports := distinct[k]
		for i := range ports {
			if ports[i] > 0 {
				port++
			}
		}
	}
	return ip, port
}

// 数据库硬查询
func resultCountByHard(ctx context.Context, taskId, resultType uint64) (info resultCountUnit, err error) {
	db := audit_result.NewAuditResultModel()
	count := func(source int, isIncluded ...int) resultCountUnit {
		var countResult resultCountUnit
		var list []audit_result.AuditResult
		list, err = db.ListAll(ctx, uint(taskId), source, isIncluded...)
		if err != nil {
			return countResult
		}
		countResult = countByCategory(list, int(resultType))
		return countResult
	}

	// 资产核查
	if resultType == ResultTypeAudit {
		// 纳入管理端口资产: 用户导入+系统发现
		// 未纳入管理端口资产: 用户导入
		// 新增端口资产: 用户导入+系统发现、系统发现
		info = count(audit_result.AssetSourceImport)
	}
	// 系统发现
	if resultType == ResultTypeSystemFind {
		info = count(audit_result.AssetSourceSystem)
	}
	// 数据总表
	if resultType == ResultTypeDataTable {
		info = count(0)
	}

	return info, err
}

func mapToAuditResult(taskId uint, source int, m map[string][]audit_result.ResultPort) []audit_result.AuditResult {
	list := make([]audit_result.AuditResult, 0, len(m))
	for k := range m {
		item := audit_result.AuditResult{
			Ip:     k,
			TaskId: taskId,
			Source: source,
			Ports:  m[k],
		}
		list = append(list, item)
	}
	return list
}

func isAccountAsset(status int) bool {
	return status == ffa.StatusConfirmAsset || status == ffa.StatusUploadAsset
}

func abstractDomain(str string) (domain, subdomain string) {
	s := utils.DomainFromUrl(str)
	subdomain = utils.If(s != "", s, str)
	domain, err := utils.FindRootDomain(subdomain)
	if err != nil {
		domain = subdomain
	}
	return domain, subdomain
}

func convertFofaeePort(item *audit_result.ResultPort) ffa.FofaeeAssetPort {
	domain, subdomain := abstractDomain(item.Domain)
	return ffa.FofaeeAssetPort{
		Port:           item.Port,
		Protocol:       item.Protocol,
		Domain:         domain,
		Subdomain:      subdomain,
		Url:            item.Url,
		Title:          item.SystemName,
		HttpStatusCode: cast.ToInt(item.StatusCode),
	}
}

func newForadarAsset(port *audit_result.ResultPort) fra.ForadarAsset {
	domain, subdomain := abstractDomain(port.Domain)
	item := fra.ForadarAsset{
		Port:           port.Port,
		Title:          port.SystemName,
		Domain:         domain,
		Subdomain:      subdomain,
		Protocol:       port.Protocol,
		Url:            port.Url,
		HTTPStatusCode: cast.ToInt(port.StatusCode),
		Status:         ffa.StatusUploadAsset, // 用户导入
	}
	split := strings.Split(port.Component, ",")
	split = utils.ListDistinct(split)
	for i := range split {
		c := strings.TrimSpace(split[i])
		if c == "" {
			continue
		}
		item.RuleTags = append(item.RuleTags, fra.RuleTag{Product: c, CnProduct: c})
	}
	return item
}

func newFofaeePorts(items []audit_result.ResultPort) ([]ffa.FofaeeAssetPort, []string) {
	var ports = make([]ffa.FofaeeAssetPort, 0, len(items))
	hosts := make([]string, 0, len(items))
	for i := range items {
		if items[i].Port == 0 {
			continue
		}
		domain, subdomain := abstractDomain(items[i].Domain)
		ports = append(ports, ffa.FofaeeAssetPort{
			Port:           items[i].Port,
			Protocol:       items[i].Protocol,
			Title:          items[i].SystemName,
			Url:            items[i].Url,
			Domain:         domain,
			Subdomain:      subdomain,
			HttpStatusCode: items[i].StatusCode,
		})
		if domain != "" {
			hosts = append(hosts, domain)
		}
		if subdomain != "" {
			hosts = append(hosts, subdomain)
		}
	}
	hosts = utils.ListDistinct(hosts)
	return ports, hosts
}

//nolint:unused,gocritic
func newFofaeeStatus(ip string, l ...audit_result.ResultPort) []portStatusUnit {
	var ports = make([]string, 0, len(l))
	for i := range l {
		if l[i].Port == 0 {
			continue
		}
		ports = append(ports, strconv.Itoa(l[i].Port))
	}
	if len(ports) == 0 {
		return []portStatusUnit{}
	}
	if len(ports) <= 3 {
		return itemIpPortsDetectByOrder(ip, ports)
	}
	return itemIpPortsDetect(ip, ports)
}

//nolint:unused,gocritic
func itemIpPortsDetectByOrder(ip string, ports []string) []portStatusUnit {
	list := make([]portStatusUnit, 0, len(ports))
	for i := range ports {
		_, status := portDetect(ip, ports[i])
		list = append(list, portStatusUnit{ip: ip, port: ports[i], status: status})
	}
	return list
}

//nolint:unused,gocritic
func itemIpPortsDetect(ip string, ports []string) []portStatusUnit {
	list := make([]portStatusUnit, 0, len(ports))
	wg := &sync.WaitGroup{}
	ch := make(chan portStatusUnit)
	go func() {
		wg.Wait()
		close(ch)
	}()

	for i := range ports {
		wg.Add(1)
		go func(p string) {
			defer wg.Done()
			_, status := portDetect(ip, p)
			ch <- portStatusUnit{ip: ip, port: p, status: status}
		}(ports[i])
	}

	for v := range ch {
		list = append(list, v)
	}
	return list
}

func getPortStatus(l []portStatusUnit, port string) int {
	for i := range l {
		if l[i].port == port {
			return l[i].status
		}
	}
	return ffa.StatusOffline
}

func distinctHosts(l []string) []string {
	m := make(map[string]struct{}, len(l))
	list := make([]string, 0, len(l))
	for i := range l {
		if l[i] == "" {
			continue
		}
		m[l[i]] = struct{}{}
	}
	for k := range m {
		list = append(list, k)
	}
	return list
}

func fofaeeRuleTagsByPorts(origin []ffa.RuleTag, l []audit_result.ResultPort) []ffa.RuleTag {
	if len(origin) == 0 {
		origin = make([]ffa.RuleTag, 0, len(l))
	}
	tags := make(map[string]bool, len(origin)+len(l))
	for i := range origin {
		tags[origin[i].CnProduct] = true
	}

	for i := range l {
		split := strings.Split(l[i].Component, ",")
		for j := range split {
			component := strings.TrimSpace(split[j])
			if component != "" {
				if _, ok := tags[component]; !ok {
					tags[component] = false
				}
			}
		}
	}

	for k, v := range tags {
		if !v {
			origin = append(origin, ffa.RuleTag{
				CnProduct: k,
				Product:   k,
			})
		}
	}
	return origin
}

func abstractRuleTags(ruleTags []fra.RuleTag) []string {
	products := make([]string, 0, len(ruleTags))
	for i := range ruleTags {
		products = append(products, ruleTags[i].CnProduct)
	}
	return utils.ListDistinctNonZero(products)
}
