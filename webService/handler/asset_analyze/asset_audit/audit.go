package asset_audit

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"micro-service/apiService/upload"
	"micro-service/initialize/es"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/audit_result"
	"micro-service/middleware/elastic/fofaee_assets"
	fra "micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	aat "micro-service/middleware/mysql/asset_audit_tasks"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/user"
	"micro-service/middleware/redis"
	"micro-service/middleware/redis/asset_audit"
	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
	"gorm.io/gorm"
)

func AuditTaskList(req *pb.AssetAuditTaskListRequest, rsp *pb.AssetAuditTaskListResponse) error {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithOrder("created_at DESC"))
	if req.UserId > 0 {
		handlers = append(handlers, mysql.WithColumnValue("user_id", req.UserId))
	}
	if req.OperatorId > 0 {
		handlers = append(handlers, mysql.WithColumnValue("operator_id", req.OperatorId))
	}
	if req.AuditType > 0 {
		handlers = append(handlers, mysql.WithColumnValue("audit_type", req.AuditType))
	}
	if req.DateAfter > 0 {
		handlers = append(handlers, mysql.WithWhere("created_at < ?", beginningOfDay(req.DateAfter)))
	}
	if req.DateBefore > 0 {
		handlers = append(handlers, mysql.WithWhere("created_at > ?", beginningOfDay(req.DateBefore)))
	}
	if req.UserName != "" {
		handlers = append(handlers, mysql.WithLRLike("operator", req.UserName))
	}

	conn := aat.NewAssetAuditor()
	list, total, err := conn.List(int(req.Page), int(req.Size), handlers...)
	if err != nil {
		return err
	}

	for i := range list {
		item := &pb.AssetAuditTaskListResponse_BaseInfo{
			Id:                uint64(list[i].ID),
			UserId:            list[i].UserID,
			Status:            int64(list[i].Status),
			ImportAssets:      int64(list[i].ImportAssetsTotal),
			NotIncludedAssets: int64(list[i].AssetsNotIncluded),
			IncludedAssets:    int64(list[i].AssetsIncluded),
			PortAssets:        int64(list[i].AssetsPort),
			SystemFind:        int64(list[i].AssetSystemFind),
			DataTable:         int64(list[i].AssetTotal),
			OperatorId:        int64(list[i].OperatorId),
			Operator:          list[i].Operator,
			Date:              list[i].CreatedAt.Format(utils.DateTimeLayout),
			FilePath:          storage.GenAPIDownloadPath("资产核查任务", list[i].ExportPath),
		}

		rsp.Items = append(rsp.Items, item)
	}
	rsp.Total = total

	return nil
}

func AuditTasksDelete(req *pb.AssetAuditTaskDeleteRequest) error {
	// 检查核查任务
	conn := aat.NewAssetAuditor()
	handlers := []mysql.HandleFunc{mysql.WithSelect("id")}
	handlers = append(handlers, mysql.WithColumnValue("user_id", req.UserId))
	if len(req.TaskIds) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("id", req.TaskIds))
	} else {
		if req.OperatorId > 0 {
			handlers = append(handlers, mysql.WithColumnValue("operator_id", req.OperatorId))
		}
		if req.DateBefore > 0 {
			handlers = append(handlers, mysql.WithWhere("created_at > ?", beginningOfDay(req.DateBefore)))
		}
		if req.DateAfter > 0 {
			handlers = append(handlers, mysql.WithWhere("created_at < ?", beginningOfDay(req.DateAfter)))
		}
		if req.UserName != "" {
			handlers = append(handlers, mysql.WithLRLike("operator", req.UserName))
		}
	}

	l, _, err := conn.List(0, 0, handlers...)
	if err != nil || len(l) == 0 {
		return err
	}

	// 删除核查任务操作
	taskIds := make([]uint, 0, len(l))
	for i := range l {
		_ = os.Remove(l[i].ImportPath)                                       // 删除导入文件
		_ = os.Remove(filepath.Join(storage.GetRootPath(), l[i].ExportPath)) // 删除任务, 删除文件
		taskIds = append(taskIds, l[i].ID)
	}
	// 删除任务
	_, err = conn.Delete(mysql.WithValuesIn("id", taskIds))
	if err != nil {
		return err
	}

	// 删除核查结果
	err = audit_result.NewAuditResultModel().Delete(context.Background(), taskIds)
	if err != nil {
		return err
	}

	return nil
}

func TaskUpdateFinished(ctx context.Context, userId, taskId uint64) error {
	db := aat.NewAssetAuditor()
	info, err := db.First(mysql.WithColumnValue("id", taskId), mysql.WithColumnValue("user_id", userId))
	if err != nil {
		return err
	}
	if info.Status != aat.AuditStatusDoing {
		return nil
	}

	info.Status = aat.AuditStatusDone
	if err := db.Update(info); err != nil {
		return err
	}

	// delete count cache
	progressKey := asset_audit.ProgressKey(taskId)
	countKey := asset_audit.ResultCountKey(taskId)
	if e := redis.DelKey(ctx, progressKey, countKey); e != nil {
		log.WithContextErrorf(ctx, "[资产核查]Delete cache key: %s %s, %v", progressKey, countKey, e)
	}
	return nil
}

func DownloadAuditResult(ctx context.Context, userId, taskId uint) (string, error) {
	conn := aat.NewAssetAuditor()
	handles := make([]mysql.HandleFunc, 0)
	handles = append(handles, mysql.WithColumnValue("id", taskId))
	handles = append(handles, mysql.WithColumnValue("user_id", userId))
	info, err := conn.First(handles...)
	if err != nil || info.ExportPath == "" {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", errors.New("核查任务不存在")
		}
		if err != nil {
			return "", err
		}
		p, err := genExcelWithDb(userId, taskId)
		if err != nil {
			return "", err
		}
		info.ExportPath = p

		go func() {
			ue := conn.Update(info)
			log.WithContextErrorfIf(ctx, ue, "[资产核查下载]update asset audit task: %+v", ue)
		}()
	}

	return info.ExportPath, nil
}

func genExcelWithDb(userId, taskId uint) (filePath string, err error) {
	conn := audit_result.NewAuditResultModel()
	l, err := conn.ListAll(context.Background(), taskId, 0)
	if err != nil {
		return "", err
	}

	filePath, err = genExcel(taskId, l)
	if err != nil {
		return "", err
	}

	return filePath, nil
}

func itoa(port int) string {
	return utils.If(port > 0, strconv.Itoa(port), "")
}

func genExcel(userId uint, l []audit_result.AuditResult) (string, error) {
	audit, find, table := 1, 1, 1
	for i := range l {
		table += len(l[i].Ports)
		switch l[i].Source {
		case audit_result.AssetSourceImport:
			audit += len(l[i].Ports)
		case audit_result.AssetSourceSystem:
			find += len(l[i].Ports)
		}
	}

	now := utils.TimeFormat(time.Now(), "20060102_150405")
	fileName := fmt.Sprintf("资产核查任务_%d_%s_%s.xlsx", userId, now, utils.RandString(5))
	apiAccessPath := filepath.Join(storage.GetPublicStoragePath(), fileName)

	firstLine := []string{"系统名称", "ip", "端口", "协议", "域名", "url", "组件", "状态码", "资产标签"}
	// 核查结果
	auditSheet := &excel.WriteByOrder{SheetName: "核查结果", Data: make([][]string, 0, audit)}
	auditSheet.Data = append(auditSheet.Data, firstLine)
	// 系统发现数据
	systemFindSheet := &excel.WriteByOrder{SheetName: "系统发现数据", Data: make([][]string, 0, find)}
	systemFindSheet.Data = append(systemFindSheet.Data, firstLine[:8])
	// 数据总表
	dataTableSheet := &excel.WriteByOrder{SheetName: "数据总表", Data: make([][]string, 0, table)}
	dataTableSheet.Data = append(dataTableSheet.Data, firstLine)

	for i := range l {
		for j := range l[i].Ports {
			line := excelLine(l[i].Ip, &l[i].Ports[j])

			dataTableSheet.Data = append(dataTableSheet.Data, line)
			switch l[i].Source {
			case audit_result.AssetSourceImport:
				auditSheet.Data = append(auditSheet.Data, line)
			case audit_result.AssetSourceSystem:
				systemFindSheet.Data = append(systemFindSheet.Data, line[:8])
			}
		}
	}

	filePath := filepath.Join(storage.GetRootPath(), apiAccessPath)
	err := excel.WriteSheetsByOrder(filePath, auditSheet, systemFindSheet, dataTableSheet)
	if err != nil {
		return "", err
	}

	return apiAccessPath, nil
}

func excelLine(ip string, pu *audit_result.ResultPort) []string {
	line := []string{pu.SystemName, ip, itoa(pu.Port), pu.Protocol, pu.Domain, pu.Url}
	line = append(line, pu.Component, pu.StatusCode, audit_result.Tags[pu.IsIncluded])

	return line
}

// NFS文件同步可能存在延迟
func waitFileNFSSync(filePath string) {
	log.Infof("[资产核查]等待上传文件同步%s", filePath)
	index, maxWait := 0, 400 // 最大等待80s
	for {
		time.Sleep(200 * time.Millisecond)

		b := utils.FileIsExist(filePath)
		if b || index > maxWait {
			return
		}
		index++
	}
}

// AuditFileCheck 核查任务上传文件检查
func AuditFileCheck(param string) error {
	// 解密文件
	fileMeta, err := upload.UploadDecrypt(param)
	if err != nil {
		return err
	}

	filePath := filepath.Join(storage.GetRootPath(), fileMeta.Url)
	waitFileNFSSync(filePath)
	if b := utils.FileIsExist(filePath); !b {
		return errors.New("未找到上传文件")
	}

	_, err = readAndCheckAuditFile(filePath)
	if err != nil {
		return err
	}
	return nil
}

// 读取excel文件并计算行数限制
func readAndCheckAuditFile(filePath string) ([]auditImportUnit, error) {
	log.Infof("[资产核查]开始读取核查任务excel文件: %s", filePath)
	content, err := excel.ReadExcel(filePath)
	if err != nil {
		return nil, err
	}
	if len(content)-1 > auditLinesLimit {
		return nil, fmt.Errorf("导入资产数不能超过%d条", auditLinesLimit)
	}
	data, err := excelContentConvert(content, auditLinesLimit)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func AuditTaskCreate(ctx context.Context, req *pb.AssetAuditTaskCreateRequest, rsp *pb.AssetAuditTaskCreateResponse) error {
	// 检查当前是否存在进行中的任务
	// 解密上传文件路径
	meta, err := upload.UploadDecrypt(req.FilePath)
	if err != nil {
		return fmt.Errorf("解密上传文件路径: %v", err)
	}

	req.FilePath = filepath.Join(storage.GetRootPath(), meta.Url)
	// 等待NFS文件同步
	waitFileNFSSync(req.FilePath)
	if b := utils.FileIsExist(req.FilePath); !b {
		return errors.New("未找到上传文件")
	}
	// 读取上传Excel文件
	data, err := readAndCheckAuditFile(req.FilePath)
	if err != nil {
		return err
	}

	x, err := user.NewUserModel(mysql.GetDbClient()).FindById(req.OperatorId)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产核查]get user info, 发起人id: %d: %v", req.OperatorId, err)
	}

	// 先创建核查任务
	var info = aat.AssetAuditTask{
		AuditType:  int(req.Type),
		UserID:     req.UserId,
		OperatorId: req.OperatorId,
		CompanyId:  req.CompanyId,
		Status:     aat.AuditStatusDoing,
		ImportPath: req.FilePath,
	}
	if x.Name != nil {
		info.Operator = x.Name.String // 发起人名称
	}

	// 创建核查任务
	conn := aat.NewAssetAuditor()
	err = conn.Create(&info)
	if err != nil {
		return err
	}

	rsp.TaskId = uint64(info.ID)
	updateAuditProgress(ctx, uint64(info.ID), utils.RandFloat32(0, 5, 1), aat.AuditStatusDoing, 0)

	log.Infof("开始异步执行核查任务")
	// 异步执行处理逻辑
	asyncData := distinctData(data)
	go asyncAuditTaskHandler(context.Background(), &info, asyncData)

	return nil
}

const auditLinesLimit = 5000

// asyncAuditTaskHandler 异步处理核查任务
func asyncAuditTaskHandler(ctx context.Context, info *aat.AssetAuditTask, data []*auditImportUnit) {
	conn := aat.NewAssetAuditor()
	err := conn.Update(*info)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产核查任务]Update table asset_audit_tasks: %+v", err)
		updateAuditProgress(ctx, uint64(info.ID), 100, aat.AuditStatusDone, 0)
		return
	}

	// 核查处理逻辑
	log.Infof("[资产核查任务]核查逻辑开始执行")
	var param = auditHandlerParam{
		userId:    int(info.UserID),
		taskId:    info.ID,
		auditType: info.AuditType,
	}
	in, err := auditHandler(ctx, param, data)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产核查任务]执行资产核查处理逻辑出错: %+v", err)
	}

	// 生成excel下载文件
	log.Infof("核查任务开始生成excel文件")
	excelPath, err := genExcel(uint(param.userId), in)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产探知-资产核查]生成excel文件: %+v", err)
	}

	// 核查结果入库
	resultConn := audit_result.NewAuditResultModel()
	if err = resultConn.Create(ctx, in); err != nil {
		_ = os.Remove(filepath.Join(storage.GetRootPath(), excelPath))
		log.WithContextErrorf(ctx, "[资产探知-资产核查]核查任务结果入库出错: %+v", err)
	} else {
		info.ExportPath = excelPath
		info.ImportAssetsTotal = len(data) // 导入资产数量
		updateAuditCount(info, in)
	}

	// 更新核查任务
	if err = conn.Update(*info); err != nil {
		log.WithContextErrorf(ctx, "[资产探知-资产核查]Update table asset_audit_tasks: %+v", err)
	}

	_, err = setResultCountCache(ctx, info.ID, in, data)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产探知-资产核查]Set result count cache: %v", err)
	}

	updateAuditProgress(ctx, uint64(info.ID), 100.00, aat.AuditStatusDone, http.StatusOK)
}

func updateAuditCount(info *aat.AssetAuditTask, l []audit_result.AuditResult) {
	for i := range l {
		info.AssetTotal += len(l[i].Ports) // 数据总表
		if l[i].Source == audit_result.AssetSourceSystem {
			info.AssetSystemFind += len(l[i].Ports)
			continue
		}

		if l[i].Source == audit_result.AssetSourceImport {
			for j := range l[i].Ports {
				switch l[i].Ports[j].IsIncluded {
				case audit_result.AssetIncluded:
					info.AssetsIncluded++ // 已纳入管理
				case audit_result.AssetNotIncluded:
					info.AssetsNotIncluded++ // 未纳入管理
				case audit_result.AssetSystemFind:
					info.AssetsPort++ // 新增端口资产
				}
			}
		}
	}
}

type auditHandlerParam struct {
	userId    int
	taskId    uint
	auditType int // 核查类型
}

func auditHandler(ctx context.Context, param auditHandlerParam, list []*auditImportUnit) (all []audit_result.AuditResult, err error) {
	if len(list) == 0 {
		return
	}

	// 获取核查结果
	switch param.auditType {
	case aat.AuditTypeAccountOfAssets:
		all, err = accountOfAssetsHandler(ctx, param, list) // 资产台账核查
	case aat.AuditTypeRecommendedAssets:
		all, err = recommendedAssetsHandler(ctx, param, list) // 推荐资产核查
	}
	if err != nil {
		return nil, err
	}

	// 获取系统发现
	find, err := systemFind(param, list)
	if err != nil {
		return nil, err
	}
	all = append(all, find...)

	updateAuditProgress(ctx, uint64(param.taskId), utils.RandFloat32(95, 99, 1), aat.AuditStatusDoing, http.StatusOK)

	return all, nil
}

func isEmpty(l []int64) bool {
	for _, v := range l {
		if v != 0 {
			return false
		}
	}
	return true
}

const esBatch = 150

// 资产台账
func accountOfAssetsHandler(ctx context.Context, param auditHandlerParam, list []*auditImportUnit) (in []audit_result.AuditResult, err error) {
	type unit struct {
		originIp string  // 用户导入的IP
		l        []int64 // ports
		onlyIp   bool    // 端口是否为空
		// exists   bool    // 无端口的IP是否纳入资产
	}

	// 要核查的数据
	mb := make(map[string]unit) // key: ip
	for k, v := range distinctIP(list) {
		ipFull := utils.IPExpanded(k)
		mb[ipFull] = unit{originIp: k, l: v, onlyIp: isEmpty(v)}
	}

	const size = esBatch
	index := 0
	batch := make([]elastic.IpPort, 0, size)
	client := fra.NewForadarAssetModel()
	usedIp := make(map[string][]audit_result.ResultPort, len(mb))
	esIp := make(map[string]struct{}, 0)
	for k := range mb {
		index++
		batch = append(batch, elastic.IpPort{Ip: k})

		batchCall := len(batch) == size || index == len(mb)
		if !batchCall {
			continue
		}

		time.Sleep(100 * time.Millisecond)
		progress := float32(index * 100.0 / len(mb))
		updateAuditProgress(context.Background(), uint64(param.taskId), progress, aat.AuditStatusDoing, http.StatusOK)

		esResult, err := client.FindByIpPort(ctx, param.userId, batch, fra.StatusConfirmAsset, fra.StatusUploadAsset)
		batch = make([]elastic.IpPort, 0, size)
		if err != nil || len(esResult) == 0 {
			if err != nil {
				log.WithContextErrorf(ctx, "[资产核查]get data from es, index: fofaee_assets, %+v", err)
			}
			continue
		}

		for i := range esResult {
			ip := esResult[i].Ip
			x, ok := mb[ip]
			if !ok {
				continue
			}
			esIp[ip] = struct{}{} // IP在台账中发现
			port := cast.ToInt(esResult[i].Port)
			if port == 0 {
				continue
			}

			info := usedIp[x.originIp]
			// 用户导入数据IP存在于系统中
			item := audit_result.ResultPort{
				Port:       port,
				SystemName: cast.ToString(esResult[i].Title),
				Protocol:   esResult[i].Protocol,
				Domain:     esResult[i].Subdomain,
				Url:        esResult[i].Url,
				StatusCode: itoa(cast.ToInt(esResult[i].HTTPStatusCode)),
			}
			item.Component = strings.Join(abstractRuleTags(esResult[i].RuleTags), ",")

			// 用户导入IP存在系统中, 但用户导入端口为空或端口存在系统中, 标签为: 用户导入+系统发现
			if x.onlyIp || utils.ListContains(x.l, int64(port)) {
				item.IsIncluded = audit_result.AssetIncluded
			} else {
				// IP存在于系统中, 端口不存在于系统中, 标签为: 系统发现
				item.IsIncluded = audit_result.AssetSystemFind
			}
			info = append(info, item)
			usedIp[x.originIp] = info
		}
	}

	// 用户导入数据的IP不存在系统中或IP存在但端口不存在 标签: 用户导入
	for i := range list {
		ip := list[i].Ip
		item := audit_result.ResultPort{
			Port:       cast.ToInt(list[i].Port),
			SystemName: list[i].SystemName,
			Protocol:   list[i].Protocol,
			Domain:     list[i].Domain,
			Url:        list[i].Url,
			Component:  list[i].Component,
			StatusCode: list[i].StatusCode,
			IsIncluded: audit_result.AssetNotIncluded,
		}
		if _, ok := esIp[utils.IPExpanded(ip)]; !ok {
			usedIp[ip] = append(usedIp[ip], item)
			continue
		}
		var ports = usedIp[ip]
		if list[i].Port == "" {
			continue
		}
		portExists := false
		for j := range ports {
			if list[i].Port == strconv.Itoa(ports[j].Port) {
				portExists = true
			}
		}
		if !portExists {
			ports = append(ports, item)
		}
		usedIp[ip] = ports
	}

	in = mapToAuditResult(param.taskId, audit_result.AssetSourceImport, usedIp)

	return in, nil
}

func systemFind(param auditHandlerParam, l []*auditImportUnit) (list []audit_result.AuditResult, err error) {
	switch param.auditType {
	case aat.AuditTypeAccountOfAssets:
		list, err = accountOfAssetsSystemFind(param.userId, param.taskId, l)
	case aat.AuditTypeRecommendedAssets:
		list, err = recommendedAssetsSystemFind(param.userId, param.taskId, l)
	}

	return
}

// 资产台账: 查找IP不存在于用户上传数据中的, 标签: 系统发现-系统发现
func accountOfAssetsSystemFind(userId int, taskId uint, l []*auditImportUnit) ([]audit_result.AuditResult, error) {
	m := getDistinctIP(l, true)
	client := fra.NewForadarAssetModel()
	list, err := client.FindByIpPort(context.Background(), userId, nil, fra.StatusConfirmAsset, fra.StatusUploadAsset)
	if err != nil {
		return nil, err
	}

	var auditResult = make(map[string][]audit_result.ResultPort, 0)
	for i := range list {
		ip := list[i].Ip
		if _, ok := m[ip]; ok {
			continue
		}

		portList := auditResult[ip]
		portList = append(portList, audit_result.ResultPort{
			Port:       cast.ToInt(list[i].Port),
			SystemName: cast.ToString(list[i].Title),
			Protocol:   list[i].Protocol,
			Domain:     list[i].Subdomain,
			Url:        list[i].Url,
			Component:  strings.Join(abstractRuleTags(list[i].RuleTags), ","), // 组件信息
			StatusCode: itoa(cast.ToInt(list[i].HTTPStatusCode)),              // 状态码
			IsIncluded: audit_result.AssetSystemFind,
		})
		auditResult[ip] = portList
	}
	in := mapToAuditResult(taskId, audit_result.AssetSourceSystem, auditResult)

	return in, nil
}

// 推荐资产, 数据已去重
func recommendedAssetsHandler(ctx context.Context, param auditHandlerParam, list []*auditImportUnit) (in []audit_result.AuditResult, err error) {
	type unit struct {
		originIp string  // 导入IP
		ports    []int64 // IP的端口列表
		onlyIp   bool    // 端口是否为空
	}

	distinct := distinctIP(list)
	mx := make(map[string]unit, len(distinct)) // key: ip
	for ip, v := range distinct {
		ipFull := utils.IPExpanded(ip)
		mx[ipFull] = unit{originIp: ip, ports: v, onlyIp: isEmpty(v)}
	}

	const size = esBatch
	index := 0
	batch := make([]elastic.IpPort, 0, size)
	esClient := recommend_result.NewRecommendResultModel(es.GetInstance())
	auditResult := make(map[string][]audit_result.ResultPort, 0)
	esIps := make(map[string][]string, 0)
	for ip := range mx {
		index++
		batch = append(batch, elastic.IpPort{Ip: ip})

		batchCall := len(batch) == size || index == len(mx)
		if !batchCall {
			continue
		}

		time.Sleep(100 * time.Millisecond)
		progress := float32(index * 100.0 / len(list))
		updateAuditProgress(ctx, uint64(param.taskId), progress, aat.AuditStatusDoing, http.StatusOK)

		esResult, err := esClient.FindByIPAndPort(param.userId, batch, -1) //nolint:govet // shadows err
		batch = make([]elastic.IpPort, 0, size)
		if err != nil {
			log.Errorf("[资产探知-资产核查]Get RecommendResult data in ElasticSearch: %+v", err)
			continue
		}

		esResult = recommend_result.DistinctAssets(esResult) // 去重
		for i := range esResult {
			port := cast.ToInt(esResult[i].Port)
			if port == 0 {
				continue
			}
			ip := esResult[i].Ip
			value, ok := mx[ip]
			if !ok {
				continue
			}

			esPort := esIps[ip]
			esPort = append(esPort, cast.ToString(esResult[i].Port))
			esIps[ip] = esPort

			portList := auditResult[value.originIp]
			item := audit_result.ResultPort{
				Port:       port,
				SystemName: esResult[i].Title,
				Protocol:   esResult[i].Protocol,
				Domain:     esResult[i].Subdomain,
				Url:        esResult[i].Url,
				Component:  "", // 组件
				StatusCode: "", // 状态码
			}

			// 用户导入IP存在系统中, 但用户导入端口为空/端口存在系统中, 标签为: 用户导入+系统发现
			if value.onlyIp || utils.ListContains(value.ports, int64(port)) {
				item.IsIncluded = audit_result.AssetIncluded
			} else {
				// 用户导入的IP存在于系统中, 但端口不是用户导入的, 标签为: 系统发现
				item.IsIncluded = audit_result.AssetSystemFind
			}

			portList = append(portList, item)
			auditResult[value.originIp] = portList
		}
	}

	for i := range list {
		originIp := list[i].Ip
		item := audit_result.ResultPort{
			Port:       cast.ToInt(list[i].Port),
			IsIncluded: audit_result.AssetNotIncluded, // 用户导入
			SystemName: list[i].SystemName,
			Protocol:   list[i].Protocol,
			Domain:     list[i].Domain,
			Url:        list[i].Url,
			StatusCode: list[i].StatusCode,
		}

		// 用户导入数据IP不存在系统中, 标签: 用户导入
		value, ok := esIps[utils.IPExpanded(list[i].Ip)]
		if !ok {
			auditResult[originIp] = append(auditResult[originIp], item)
			continue
		}
		if list[i].Port == "" {
			continue
		}
		ports := auditResult[originIp]
		// IP存在于推荐资产库, 判断端口不在推荐资产中
		if !utils.ListContains(value, list[i].Port) {
			ports = append(ports, item)
			auditResult[originIp] = ports
		}
	}

	in = mapToAuditResult(param.taskId, audit_result.AssetSourceImport, auditResult)

	return in, nil
}

// 资产推荐: ip非用户上传数据, 标签: 系统发现
func recommendedAssetsSystemFind(userId int, taskId uint, l []*auditImportUnit) ([]audit_result.AuditResult, error) {
	m := getDistinctIP(l, true)
	esClient := recommend_result.NewRecommendResultModel(es.GetInstance())
	result, err := esClient.FindByIPAndPort(userId, nil, -1)
	if err != nil {
		return nil, err
	}
	result = recommend_result.DistinctAssets(result)

	var auditResult = make(map[string][]audit_result.ResultPort, 0) // key: ip
	for i := range result {
		ip := result[i].Ip
		if _, ok := m[ip]; ok {
			continue
		}

		portList := auditResult[ip]
		portList = append(portList, audit_result.ResultPort{
			Port:       cast.ToInt(result[i].Port),
			SystemName: result[i].Title,
			Protocol:   result[i].Protocol,
			Domain:     result[i].Subdomain,
			Url:        result[i].Url,
			Component:  "", // 组件信息
			StatusCode: "", // 状态码
			IsIncluded: audit_result.AssetSystemFind,
		})
		auditResult[ip] = portList
	}
	in := mapToAuditResult(taskId, audit_result.AssetSourceSystem, auditResult)

	return in, nil
}

const (
	ResultTypeAudit      = iota + 1 // 核查结果类型: 核查结果
	ResultTypeSystemFind            // 核查结果类型: 系统发现
	ResultTypeDataTable             // 核查结果类型: 数据总表
)

func AssetAuditResultList(ctx context.Context, param *pb.AssetAuditTaskResultListRequest, rsp *pb.AssetAuditTaskResultListResponse) error {
	// step1: Check the task is belongs to user
	taskConn := aat.NewAssetAuditor()
	infoHandles := make([]mysql.HandleFunc, 0)
	infoHandles = append(infoHandles, mysql.WithColumnValue("user_id", param.UserId))
	infoHandles = append(infoHandles, mysql.WithColumnValue("id", param.TaskId))
	_, err := taskConn.First(infoHandles...)
	if err != nil {
		return err
	}

	// step2: Get Asset Audit Result
	resultConn := audit_result.NewAuditResultModel()
	var source int
	// 数据总表获取所有
	if param.ResultType != 0 && param.ResultType != ResultTypeDataTable {
		source = int(param.ResultType)
	}

	// 获取统计
	r, err := getResultCountCache(uint(param.TaskId), param.ResultType)
	if err != nil {
		return err
	}
	rsp.Ip = r.Ip
	rsp.Port = r.Port
	rsp.NewPort = r.NewPort
	rsp.Included = r.Included
	rsp.NotIncluded = r.NotIncluded

	// 根据IP查询核查结果
	list, total, err := resultConn.List(ctx, int(param.Page), int(param.Size), uint(param.TaskId), source)
	if err != nil {
		return err
	}
	rsp.Total = total

	for i := range list {
		var item = &pb.AssetAuditTaskResultListResponse_ItemUnit{
			Ip:    list[i].Ip,
			Ports: make([]*pb.AssetAuditTaskResultListResponse_ItemUnit_PortUnit, 0, len(list[i].Ports)),
		}
		for j := range list[i].Ports {
			port := pb.AssetAuditTaskResultListResponse_ItemUnit_PortUnit{
				SystemName: list[i].Ports[j].SystemName,
				Port:       int64(list[i].Ports[j].Port),
				Protocol:   list[i].Ports[j].Protocol,
				Domain:     list[i].Ports[j].Domain,
				Url:        list[i].Ports[j].Url,
				Component:  list[i].Ports[j].Component,
				StatusCode: list[i].Ports[j].StatusCode,
				IsIncluded: int64(list[i].Ports[j].IsIncluded),
			}
			item.Ports = append(item.Ports, &port)
		}
		rsp.Items = append(rsp.Items, item)
	}

	return nil
}

func AssetAuditTaskInfo(req *pb.AssetAuditTaskInfoRequest, rsp *pb.AssetAuditTaskInfoResponse) error {
	handles := make([]mysql.HandleFunc, 0, 4)
	handles = append(handles, mysql.WithColumnValue("user_id", req.UserId))
	if req.TaskId > 0 {
		handles = append(handles, mysql.WithColumnValue("id", req.TaskId))
	}
	if req.Status > 0 {
		handles = append(handles, mysql.WithColumnValue("status", req.Status))
	}
	if req.FirstTaskDoing {
		handles = append(handles, mysql.WithColumnValue("status", aat.AuditStatusDoing))
	}
	handles = append(handles, mysql.WithOrder("created_at DESC"))

	info, err := aat.NewAssetAuditor().First(handles...)
	if err != nil {
		if err == gorm.ErrRecordNotFound && req.FirstTaskDoing {
			return nil
		}
		return err
	}

	rsp.Id = uint64(info.ID)
	rsp.AuditType = uint64(info.AuditType)
	rsp.Status = uint64(info.Status)
	rsp.ImportAssetsTotal = uint64(info.ImportAssetsTotal)
	rsp.AssetsIncluded = uint64(info.AssetsIncluded)
	rsp.AssetsNotIncluded = uint64(info.AssetsNotIncluded)
	rsp.ExportPath = info.ExportPath

	return nil
}

// AuditResultSyncAssets 核查结果同步台账
func AuditResultSyncAssets(ctx context.Context, req *pb.AssetAuditSyncAssetsRequest, rsp *pb.AssetAuditSyncAssetsResponse) error {
	// 检查核查任务是否属于该用户
	param := &pb.AssetAuditTaskInfoRequest{
		UserId: req.UserId,
		TaskId: req.TaskId,
	}
	err := AssetAuditTaskInfo(param, &pb.AssetAuditTaskInfoResponse{})
	if err != nil {
		return err
	}

	// 获取核查结果
	client := audit_result.NewAuditResultModel()
	list, err := client.ListAll(ctx, uint(req.TaskId), audit_result.AssetSourceImport)
	if err != nil || len(list) == 0 {
		return err
	}

	// 查询用户的IP台账使用数量
	userInfo, err := user.NewUserModel(mysql.GetDbClient()).FindById(req.UserId)
	if err != nil {
		return err
	}

	// 计算本次同步的最大新增台账IP数量
	var ipNumLimit int
	var companyId uint
	var currentIpUsed int
	if userInfo.Role == user.UserRoleTenant {
		info, err2 := company.NewCompanyModel().FindById(req.CompanyId, req.UserId)
		if err2 != nil {
			return err
		}
		ipNumLimit = info.LimitIpAsset - info.UsedIpAsset
		companyId = uint(info.ID)
		currentIpUsed = info.UsedIpAsset
	} else {
		ipNumLimit = auditLinesLimit // 非企业用户台账不受限制
	}

	// 同步台账逻辑
	usedIp, ignoreAssets, err := syncUserImportAssets(ctx, syncParam{
		userId:     int(req.UserId),
		ipNumLimit: ipNumLimit,
		assetTag:   assetTag(int(req.UserId), int(req.OperatorId)),
		list:       list,
	})
	if err != nil {
		return err
	}
	rsp.Assets = int64(usedIp)      // 本次实际入账数量
	rsp.Limit = int64(ignoreAssets) // 本次入账忽略的资产数

	// 更新用户台账IP数量限制
	if userInfo.Role == user.UserRoleTenant && usedIp > 0 {
		m := make(map[string]any, 1)
		m["used_ip_asset"] = currentIpUsed + usedIp
		err = company.NewCompanyModel().UpdateWithMap(companyId, m)
	}

	return err
}

type syncParam struct {
	userId     int
	ipNumLimit int
	assetTag   int
	list       []audit_result.AuditResult
}

func assetTag(userId, operatorId int) int {
	userInfo, err := user.NewUserModel(mysql.GetDbClient()).FindById(operatorId)
	if err != nil {
		log.Errorf("[资产核查]Get user info from mysql: %v", err)
		return -1
	}
	if userInfo.Role == user.UserRoleSafe || userInfo.Role == user.UserRoleAdmin {
		if userId == operatorId {
			return fofaee_assets.TagUserScan
		}
		return fofaee_assets.TagSafeScan
	}
	return fofaee_assets.TagUserScan
}
