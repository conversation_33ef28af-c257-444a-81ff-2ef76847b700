package asset_audit

import (
	"context"
	"fmt"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"strconv"
	"testing"

	"github.com/smartystreets/assertions"
	"github.com/stretchr/testify/assert"
)

func InitDB() {
	cfg.InitLoadCfg()
	_ = es.GetInstance(cfg.LoadElastic())
	_ = mysql.GetInstance(cfg.LoadMysql())
}

func TestImportMarshal(t *testing.T) {
	data := []string{"123", "127.0.0.1", "8080", "http"}

	info, err := importMarshal(data)
	assertions.ShouldBeNil(err)
	fmt.Println(info.Ip, info.Port, info.Protocol)
}

func Test_allResultCountByHard(t *testing.T) {
	result, err := allResultCountByHard(context.Background(), 302)
	assert.Nil(t, err)
	fmt.Println(result)
}

func Test_DistinctIP(t *testing.T) {
	InitDB()

	var list = make([]*auditImportUnit, 0)
	list = append(list, &auditImportUnit{Ip: "localhost", Port: "443"})
	list = append(list, &auditImportUnit{Ip: "localhost", Port: ""})
	list = append(list, &auditImportUnit{Ip: "127.0.0.1", Port: ""})

	m := distinctIP(list)
	fmt.Println(m)
}

func Test_abstractDomain(t *testing.T) {
	domain, subdomain := abstractDomain("fofa.info")
	fmt.Println(domain, subdomain)
}

func Test_ItemIpPortsDetect(t *testing.T) {
	ports := make([]string, 0, 1000)
	ip := "**************" // bing public ip
	for i := 1; i < 500; i++ {
		ports = append(ports, strconv.Itoa(i))
	}

	// ports = []string{"443"}
	l := itemIpPortsDetect(ip, ports)
	for i := range l {
		fmt.Println(l[i].ip, l[i].port, l[i].status)
	}
}
