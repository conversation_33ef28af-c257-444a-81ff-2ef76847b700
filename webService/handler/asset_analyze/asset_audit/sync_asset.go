package asset_audit

import (
	"context"
	"net"
	"strconv"
	"sync"
	"time"

	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/audit_result"
	ffa "micro-service/middleware/elastic/fofaee_assets"
	fra "micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/spf13/cast"
)

// 同步核查结果到台账
func syncUserImportAssets(ctx context.Context, param syncParam) (int, int, error) {
	// convert ip full
	for i := range param.list {
		param.list[i].Ip = utils.IPExpanded(param.list[i].Ip)
	}
	// 资产状态检测
	statusMap := auditAssetsStatusDetect(param.list)
	// sync to fofaee_assets
	newAccountIps, portChangeIps, ignoreAssets, err := syncToFofaeeAssets(ctx, param, statusMap)
	if err != nil {
		return 0, 0, err
	}
	ipMap := make(map[string]struct{})
	for i := range newAccountIps {
		ipMap[newAccountIps[i]] = struct{}{}
	}
	portIpMap := utils.ListToSet(utils.ListDistinct(portChangeIps))
	// sync foradar_assets
	if err := syncToForadarAssets(ctx, param, ipMap, portIpMap, statusMap); err != nil {
		return 0, 0, err
	}

	return len(newAccountIps), ignoreAssets, nil
}

// 同步台账
// 更新foradar_assets
func syncToForadarAssets(ctx context.Context, param syncParam, ips, portChangeIps map[string]struct{}, statusMap map[string]assetStatus) error {
	l := param.list

	// 获取本次同步IP的es数据
	esConn := fra.NewForadarAssetModel()
	var ipPort = make([]elastic.IpPort, 0, 100)
	if len(l) < 100 {
		for i := range l {
			ipPort = append(ipPort, elastic.IpPort{Ip: l[i].Ip})
		}
	}
	foradarList, err := esConn.FindByIpPort(ctx, param.userId, ipPort) // 获取本次要同步的IP
	if err != nil {
		return err
	}

	foradarIps := make(map[string][]*fra.ForadarAsset, len(foradarList)) // key: ip
	for i := range foradarList {
		ip := foradarList[i].Ip
		foradarIps[ip] = append(foradarIps[ip], &foradarList[i])
	}

	insertList := make([]*fra.ForadarAsset, 0, len(l))
	updateList := make([]map[string]any, 0)
	for i := range l {
		ip := l[i].Ip
		if len(l[i].Ports) == 0 {
			continue
		}

		_, newAccountExists := ips[ip]           // 是否为新台账
		_, newPortExists := portChangeIps[ip]    // 是否为台账更新端口资产
		if !newAccountExists && !newPortExists { // 既不是新台账又不是更新端口资产
			continue
		}
		// 新台账(新IP或非台账资产)或更新端口
		ports, ipExists := foradarIps[ip] // 是否存在于es库
		// IP不在查询的es结果中但在新台账中, 则新增
		if !ipExists && newAccountExists {
			for j := range l[i].Ports {
				if l[i].Ports[j].Port == 0 {
					continue
				}
				port := strconv.Itoa(l[i].Ports[j].Port)
				item := newForadarAsset(&l[i].Ports[j])
				item.ID = fra.GenIndexId(ip, port, item.Protocol, item.Subdomain, uint64(param.userId))
				item.Ip = ip
				item.UserID = param.userId
				item.OnlineState = getPortStatus(statusMap[ip].Ports, port)

				insertList = append(insertList, &item)
			}
			continue
		}
		if !ipExists && !newAccountExists {
			continue
		}

		// ip存在于ES资产数据中, 1-端口不存在, 2-更新资产状态为台账
		for j := range ports {
			if !isAccountAsset(cast.ToInt(ports[j].Status)) && newAccountExists {
				updateList = append(updateList, map[string]any{
					"id":     ports[j].ID,
					"status": fra.StatusUploadAsset,
				})
			}
		}
		var geo fra.AssetGeo
		var clueCompanyName any
		for j := range l[i].Ports {
			port := l[i].Ports[j].Port
			if port == 0 {
				continue
			}
			portExists := false
			for k := range ports {
				if port == cast.ToInt(ports[k].Port) {
					portExists = true
				} else {
					geo = ports[k].Geo
					clueCompanyName = ports[k].ClueCompanyName
				}
			}
			// 端口不存在且属于新台账或台账更新端口
			if !portExists && (newAccountExists || newPortExists) {
				port := strconv.Itoa(l[i].Ports[j].Port)
				item := newForadarAsset(&l[i].Ports[j])
				item.ID = fra.GenIndexId(ip, port, item.Protocol, item.Subdomain, uint64(param.userId))
				item.Ip = ip
				item.Geo = geo
				item.ClueCompanyName = clueCompanyName
				item.UserID = param.userId
				item.OnlineState = getPortStatus(statusMap[ip].Ports, port)

				insertList = append(insertList, &item)
			}
		}
	}

	_, _, err = esConn.Create(ctx, insertList)
	if err != nil {
		return err
	}
	err = esConn.UpdateWithMap(ctx, updateList)
	if err != nil {
		return err
	}
	return nil
}

// 同步台账
// 更新fofaee_assets索引
func syncToFofaeeAssets(ctx context.Context, param syncParam, statusMap map[string]assetStatus) ([]string, []string, int, error) {
	l := param.list

	accoutPortChangeIps := make([]string, 0)
	updateAsset := make(map[string]ffa.UpdatePortsHost, 0) // key is index id
	nonAccountAsset := make(map[string]ffa.UpdatePortsHost, 0)
	insertList := make([]*ffa.FofaeeAssets, 0, len(l)/2)

	esConn := ffa.NewFofaeeAssetsModel()
	var ipPort = make([]elastic.IpPort, 0, 100)
	if len(l) < 100 {
		for i := range l {
			ipPort = append(ipPort, elastic.IpPort{Ip: l[i].Ip})
		}
	}
	result, err := esConn.FindByIpAndPort(param.userId, ipPort)
	if err != nil {
		return nil, nil, 0, err
	}
	esMap := make(map[string]*ffa.FofaeeAssets, len(result))
	for i := range result {
		esMap[result[i].Ip] = &result[i]
	}

	for i := range l {
		ip := l[i].Ip // storage ip
		// IP是否在资产库中
		asset, ok := esMap[ip]
		if !ok {
			// 不存在则新建资产
			id := ffa.GenId(param.userId, ip)
			var item = ffa.FofaeeAssets{Id: id, Ip: ip, UserId: param.userId, IsIpv6: utils.IsIPv6(l[i].Ip), Status: ffa.StatusUploadAsset}
			ports, host := newFofaeePorts(l[i].Ports)
			if len(ports) > 0 {
				item.Hosts = host
				item.PortList = ports
				item.HostList = ports
				item.RuleTags = fofaeeRuleTagsByPorts(nil, l[i].Ports)
				item.OnlineState = statusMap[l[i].Ip].ipStatus
				if param.assetTag >= 0 {
					item.Tags = []int{param.assetTag}
				}
				insertList = append(insertList, &item)
			}
			continue
		}

		// IP存在于资产库中, 端口不存在追加端口
		// 情形1: 该IP为台账资产, 则仅追加端口信息
		// 情形2: 该IP非台账资产, 则追加端口并更新资产状态
		isAccount := isAccountAsset(cast.ToInt(asset.Status))
		var updateUnit = ffa.UpdatePortsHost{
			Status:      utils.If(isAccount, cast.ToInt(asset.Status), ffa.StatusUploadAsset),
			List:        asset.PortList,
			HostList:    asset.HostList,
			Hosts:       asset.Hosts,
			OnlineState: asset.OnlineState,
			RuleTags:    fofaeeRuleTagsByPorts(asset.RuleTags, l[i].Ports),
			Tags:        asset.Tags,
		}

		portUpdate := false
		for j := range l[i].Ports {
			if l[i].Ports[j].Port == 0 {
				continue
			}
			portExists := false
			for k := range asset.PortList {
				if l[i].Ports[j].Port == cast.ToInt(asset.PortList[k].Port) {
					portExists = true
				}
			}
			if !portExists {
				domain, subdomain := abstractDomain(l[i].Ports[j].Domain)
				port := convertFofaeePort(&l[i].Ports[j])
				updateUnit.HostList = append(updateUnit.HostList, port)
				updateUnit.List = append(updateUnit.List, port)
				updateUnit.Hosts = append(updateUnit.Hosts, domain, subdomain)
				if status := statusMap[l[i].Ip].ipStatus; status == ffa.StatusOnline {
					updateUnit.OnlineState = ffa.StatusOnline
				}
				if param.assetTag >= 0 {
					updateUnit.Tags = append(updateUnit.Tags, param.assetTag)
				}

				portUpdate = true // 端口更新
			}
		}

		updateUnit.Hosts = distinctHosts(updateUnit.Hosts)
		updateUnit.Tags = utils.ListDistinct(updateUnit.Tags)
		if portUpdate && isAccount {
			accoutPortChangeIps = append(accoutPortChangeIps, asset.Ip)
			updateAsset[asset.Id] = updateUnit
		}
		if !isAccount {
			nonAccountAsset[asset.Id] = updateUnit
		}
	}

	// 台账资产更新
	if exp := esConn.UpdatePorts(ctx, updateAsset); exp != nil {
		return nil, nil, 0, err
	}

	if param.ipNumLimit <= 0 {
		return nil, accoutPortChangeIps, 0, nil
	}
	var ignoreAssets int // 本次入账忽略的资产数
	left := param.ipNumLimit - len(insertList)
	if left < 0 {
		ignoreAssets = len(insertList) - param.ipNumLimit
		insertList = insertList[:param.ipNumLimit]
	}
	if _, _, exp := esConn.Create(ctx, insertList); exp != nil {
		return nil, nil, 0, err
	}
	var ips = make([]string, 0, len(insertList))
	for i := range insertList {
		ips = append(ips, insertList[i].Ip)
	}
	if left <= 0 {
		return ips, accoutPortChangeIps, ignoreAssets, nil
	}

	realUpdate := make(map[string]ffa.UpdatePortsHost, left)
	index := 0
	nonAccountIps := make([]string, 0, left)
	for k := range nonAccountAsset {
		index++
		if index <= left {
			_, ip := ffa.ParseId(k)
			realUpdate[k] = nonAccountAsset[k]
			nonAccountIps = append(nonAccountIps, ip)
		} else {
			break
		}
	}
	if exp := esConn.UpdatePorts(ctx, realUpdate); exp != nil {
		log.WithContextErrorf(ctx, "[资产核查-同步台账]es index: fofaee_assets, update non-account: %v", exp)
		return ips, accoutPortChangeIps, ignoreAssets, nil
	}
	ignoreAssets += len(nonAccountAsset) - len(realUpdate)
	ips = append(ips, nonAccountIps...)

	return ips, accoutPortChangeIps, ignoreAssets, nil
}

func portDetect(ip, port string) (bool, int) {
	address := net.JoinHostPort(ip, port)
	conn, err := net.DialTimeout("tcp", address, 3*time.Second)
	if err != nil {
		return false, fra.OnlineStatusNO
	}
	defer conn.Close()

	if conn == nil {
		return false, fra.OnlineStatusNO
	}
	return true, fra.OnlineStatusYES
}

type portStatusUnit struct {
	ip     string
	port   string
	status int
}

type assetStatus struct {
	ipStatus int              // IP的状态
	Ports    []portStatusUnit // 各端口的状态
}

func auditAssetsStatusDetect(list []audit_result.AuditResult) map[string]assetStatus {
	m := make(map[string]assetStatus, len(list))
	wg := &sync.WaitGroup{}
	ch := make(chan portStatusUnit)
	go func() {
		wg.Wait()
		close(ch)
	}()

	for i := range list {
		for j := range list[i].Ports {
			if list[i].Ports[j].Port == 0 {
				continue
			}
			wg.Add(1)
			go func(ip string, port int) {
				defer wg.Done()
				p := strconv.Itoa(port)
				_, status := portDetect(ip, p)
				ch <- portStatusUnit{ip: ip, port: p, status: status}
			}(list[i].Ip, list[i].Ports[j].Port)
		}
	}

	for v := range ch {
		unit := m[v.ip]
		if v.status == ffa.StatusOnline {
			unit.ipStatus = ffa.StatusOnline
		}
		unit.Ports = append(unit.Ports, portStatusUnit{port: v.port, status: v.status})
		m[v.ip] = unit
	}
	return m
}
