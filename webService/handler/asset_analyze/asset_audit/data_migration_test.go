package asset_audit

import (
	"fmt"
	"micro-service/middleware/elastic/audit_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/asset_audit_tasks"
	"testing"
)

func TestAuditResult(t *testing.T) {
	InitDB()

	// task id range: 1-61
	task := asset_audit_tasks.NewAssetAuditor()
	l, _, err := task.List(0, 0)
	if err != nil {
		t.Error(err)
		return
	}

	conn := asset_audit_tasks.NewTaskResulter()
	for _, v := range l {
		if v.ID > 61 {
			break
		}
		list, _, err := conn.List(0, 0, mysql.WithColumnValue("task_id", v.ID))
		if len(list) == 0 || err != nil {
			if err != nil {
				fmt.Printf("task_id: %d, get audit result err: %v\n", v.ID, err)
			} else {
				fmt.Printf("task_id: %d no audit result\n", v.ID)
			}
			continue
		}
		esData := handle(v.ID, list)
		if len(esData) > 0 {
			// err := audit_result.NewAuditResultModel().Create(context.Background(), esData)
			fmt.Println("task_id:", v.ID, esData)
			if err != nil {
				fmt.Printf("task_id: %d, 操作人:%s 迁移失败\n", v.ID, v.Operator)
				continue
			}
		}
		fmt.Printf("task_id: %d, 操作人:%s 迁移成功\n", v.ID, v.Operator)
	}
}

func handle(taskId uint, l []asset_audit_tasks.TaskResult) []audit_result.AuditResult {
	m := make(map[string][]asset_audit_tasks.TaskResult, 0) // key: ip
	for i := range l {
		ip := l[i].IP
		m[ip] = append(m[ip], l[i])
	}

	list := make([]audit_result.AuditResult, 0, len(m))
	for k := range m {
		ports := m[k]
		var item audit_result.AuditResult
		for i := range ports {
			item.Ports = append(item.Ports, audit_result.ResultPort{
				Port:       ports[i].Port,
				IsIncluded: int(ports[i].IsIncluded),
			})
		}
		item.Ip = k
		item.TaskId = taskId
		item.Source = audit_result.AssetSourceImport
		list = append(list, item)
	}

	return list
}
