package assets_detect

import (
	"fmt"
	"net"
	"strconv"
	"sync"
	"time"

	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

func waitFileSync(file string) {
	log.Infof("[Assets Status Detect]等待上传文件%s同步", file)

	var index int
	total := 100 // 最大等待50s
	for {
		index++
		time.Sleep(200 * time.Millisecond)
		b := utils.FileIsExist(file)
		if b || index > total {
			return
		}
	}
}

func splitMap(list map[string]port, step int) [][]map[string]port {
	batach := make([][]map[string]port, 0, step)

	limit := 0
	tmp := []map[string]port{}
	for k, v := range list {
		limit++
		tmp = append(tmp, map[string]port{k: v})

		if limit >= step {
			limit = 0
			batach = append(batach, tmp)
			tmp = []map[string]port{}
		}
	}

	if len(tmp) > 0 {
		batach = append(batach, tmp)
	}
	return batach
}

var defaultPorts = []string{"512", "564", "9042", "4712", "5555", "102", "9333", "8291", "18081", "4786", "8545", "10333",
	"8009", "10001", "4369", "3333", "4444", "8333", "9000", "9100", "2375", "2376", "2001", "104", "2401", "2222", "3288",
	"3310", "67", "22105", "443", "8443", "9443", "8139", "4443", "2087", "444", "2083", "7071", "5986", "4064", "80", "81",
	"82", "83", "84", "88", "3000", "4567", "5985", "8080", "50070", "7547", "16010", "4022", "1234", "4000", "1991", "3689",
	"3128", "1311", "9981", "32400", "28017", "25105", "23424", "13579", "9944", "9595", "5357", "10243", "8880", "8889",
	"8112", "8098", "8090", "8089", "8086", "8081", "8069", "8060", "8010", "7779", "5560", "4848", "4040", "3780", "3749",
	"3541", "2480", "2080", "1471", "1177", "7474", "2086", "2082", "1025", "55553", "51106", "9191", "9080", "8888", "8834",
	"8334", "7548", "6664", "3542", "8008", "8001", "8200", "5678", "880", "9010", "8161", "16992", "16993", "7288", "8377",
	"8378", "8800", "138", "7001", "8983", "27015", "20000", "18245", "14000", "12345", "11211", "10000", "9999", "9418", "9003",
	"27017", "37777", "2379", "9200", "22", "41795", "22222", "64738", "62078", "61616", "61613", "50100", "7000", "50000", "32768",
	"8649", "11310", "9653", "11001", "20880", "9530", "9527", "60030", "16030", "8030", "60010", "8000", "5000", "5001", "8020",
	"8025", "8040", "9001", "9090", "25000", "1505", "23", "8500", "50050", "264", "6068", "18001", "2332", "2323", "23023", "4063",
	"43", "3690", "8126", "8125", "4840", "2049", "646", "5084", "5050", "49153", "49152", "49154", "49151", "49155", "9092", "9093",
	"9700", "9711", "14265", "113", "1344", "175", "4880", "7634", "1260", "5577", "1026", "2501", "6666", "2424", "9051", "9151",
	"3299", "3260", "3460", "1194", "7911", "33338", "1863", "25565", "1911", "789", "771", "631", "548", "523", "515", "502", "503",
	"636", "389", "873", "992", "902", "1883", "1723", "1521", "1433", "1241", "1200", "1201", "2455", "1099", "6000", "6488", "8686",
	"10162", "1080", "45554", "993", "199", "179", "49", "37", "25", "26", "465", "587", "59110", "21", "2121", "14147", "19", "17",
	"13", "11", "53", "110", "995", "143", "139", "135", "1027", "119", "111", "79", "70", "554", "8554", "445", "7", "1962", "5222",
	"5269", "8099", "5984", "5432", "8087", "5938", "7077", "6665", "6667", "6668", "6669", "6379", "4730", "3388", "3389", "3390",
	"5672", "5900", "5901", "6001", "3306", "3307", "20547", "4911", "5009", "2000", "7777", "5006", "5007", "4949", "2181", "2404",
	"5400", "2628", "5632", "2094", "47808", "69", "6969", "161", "162", "391", "705", "1993", "137", "5353", "30718", "5683", "3702",
	"1434", "2427", "500", "2425", "4500", "9600", "44818", "5094", "5060", "48899", "2123", "2152", "4070", "30310", "30311", "30312",
	"30313", "28784", "5554", "1604", "1701", "6881", "3283", "1967", "626", "623", "520", "123", "17185", "3784", "4800", "1812", "1645",
	"34962", "34964", "5351", "3671", "1900", "11300"}

type port struct {
	ports        []string
	defaultPorts bool // 默认端口表，true 用户未上传，使用默认端口表
}

func readExcel(path string) (map[string]port, int, error) {
	excelResp, err := excel.ReadExcel(path)
	if err != nil {
		return nil, 0, err
	}

	ips := make(map[string]port, len(excelResp))
	fileter := make(map[string]struct{}, len(excelResp)) // key: ip+port

	var validateCnt int
	for i := 1; i < len(excelResp); i++ {
		ipR, portR, err := getIPAndPort(i, excelResp[i])
		if err != nil {
			return nil, 0, err
		}
		if ipR == "" && portR == "" {
			continue
		}

		validateCnt++
		if ipR != "" && portR == "" {
			// 检测是否存在key
			// 如果已存在key，忽略
			// 如果不存在key, 写默认数据
			if _, ok := ips[ipR]; !ok {
				ips[ipR] = port{ports: defaultPorts, defaultPorts: true}
			}
		} else if ipR != "" && portR != "" {
			if v, ok := ips[excelResp[i][0]]; ok {
				// 当相同的ip, excel 之前 port没有传入，后面的port传入 ( excel 之前 port有传入，后面的port没传入)，
				// 已写入是默认端口数据, 删除默认数据。使用传入现有的的port
				// 已写入不是默认端口数据, 传入现有的的port,检测唯一
				if v.defaultPorts {
					ips[ipR] = port{ports: []string{portR}, defaultPorts: false}
					fileter[ipR+":"+portR] = struct{}{}
					continue
				}

				if _, ok := fileter[ipR+":"+portR]; !ok {
					p := ips[ipR]
					p.ports = append(p.ports, portR)
					ips[ipR] = p
					fileter[ipR+":"+portR] = struct{}{}
				}
			} else {
				ips[ipR] = port{ports: []string{portR}, defaultPorts: false}
				fileter[ipR+":"+portR] = struct{}{}
			}
		}

		if validateCnt > 5000 {
			return nil, 0, ErrNumExceed
		}
	}

	var total int
	for _, v := range ips {
		total += len(v.ports)
	}
	return ips, total, nil
}

const maxValidateNum = 5000

var ErrNumExceed = fmt.Errorf("导入资产超过最大限制数%d条", maxValidateNum)

func getIPAndPort(lineNum int, d []string) (string, string, error) {
	if len(d) == 0 {
		return "", "", nil
	}
	var ip, port string
	if len(d) > 0 {
		ip = d[0]
	}
	if len(d) > 1 {
		port = d[1]
	}
	if ip == "" && port == "" {
		return "", "", nil
	}
	address := net.ParseIP(ip)
	if address == nil {
		return "", "", fmt.Errorf("第%d行数据IP无效", lineNum+1)
	}
	if port != "" && !utils.IsValidatePort(port) {
		return "", "", fmt.Errorf("第%d行数据端口无效", lineNum+1)
	}
	return ip, port, nil
}

func writeExcel(path string, data *AssetsStatusDetectInfo) error {
	online := make([][]string, 0, len(data.OnlineAssets))
	online = append(online, []string{"ip", "port"})
	for i := range data.OnlineAssets {
		online = append(online, []string{data.OnlineAssets[i].Ip, strconv.Itoa(int(data.OnlineAssets[i].Port))})
	}

	offline := make([][]string, 0, len(data.UnonlineAssets))
	offline = append(offline, []string{"ip", "port"})
	for i := range data.UnonlineAssets {
		var p string
		if data.UnonlineAssets[i].Port != 0 {
			p = strconv.Itoa(int(data.UnonlineAssets[i].Port))
		}
		offline = append(offline, []string{data.UnonlineAssets[i].Ip, p})
	}

	sheet := map[string][][]string{
		"在线资产": online,
		"离线资产": offline,
	}
	return excel.WriteMultiSheet(path, sheet)
}

// 1 在线 2离线
func tcpGather(ip string, ports []string) map[string]string {
	var mm sync.Map
	var wg sync.WaitGroup

	tel := func(ip, port string) {
		address := net.JoinHostPort(ip, port)
		conn, err := net.DialTimeout("tcp", address, 3*time.Second)
		if err != nil {
			// results[port] = "2"
			mm.Store(port, "2")
		} else {
			if conn != nil {
				// results[port] = "1"
				mm.Store(port, "1")
				_ = conn.Close()
			} else {
				// results[port] = "2"
				mm.Store(port, "2")
			}
		}
		wg.Done()
	}

	for i := range ports {
		wg.Add(1)
		go tel(ip, ports[i])
	}
	wg.Wait()

	results := make(map[string]string, 5000)
	mm.Range(func(k, v interface{}) bool {
		kR := k.(string)
		vR := v.(string)
		results[kR] = vR
		return true
	})

	return results
}
