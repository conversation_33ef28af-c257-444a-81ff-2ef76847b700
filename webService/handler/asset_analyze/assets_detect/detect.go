package assets_detect

import (
	"errors"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/assets_status_detect"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"path"
	"path/filepath"
)

type AssetsStatusDetectInfo struct {
	UnonlineAssets []assets_status_detect.AssetsStatusDetectResult
	OnlineAssets   []assets_status_detect.AssetsStatusDetectResult
}

// GetAssetsStatusDetectInfo 获取某一个table资产详情
func GetAssetsStatusDetectInfo(in *pb.GetAssetsStatusDetectInfoRequest) (*pb.GetAssetsStatusDetectInfoResponse, error) {
	has, err := assets_status_detect.NewAssetsStatusDetectTaskModel(mysql.GetInstance()).HasTaskID(in.UserId, uint(in.TaskId))
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, errors.New("任务不存在")
	}

	d, total, err := assets_status_detect.NewAssetsStatusDetectResultModel(mysql.GetInstance()).
		FindByTaskID(in.TaskId, int(in.OnlineStatus), int(in.CurrentPage), int(in.PerPage))
	if err != nil {
		return nil, err
	}

	resp := &pb.GetAssetsStatusDetectInfoResponse{}
	if total == 0 {
		return resp, nil
	}

	resp.Total = total
	resp.CurrentPage = in.CurrentPage
	resp.PerPage = in.PerPage
	datas := make([]*pb.GetAssetsStatusDetectInfo, 0, len(d))
	for i := range d {
		datas = append(datas, &pb.GetAssetsStatusDetectInfo{Ip: d[i].Ip, Port: d[i].Port})
	}
	resp.Items = datas
	return resp, nil
}

// GetAssetsStatusDetectList 获取任务列表
func GetAssetsStatusDetectList(in *pb.GetAssetsStatusDetectListRequest) (*pb.GetAssetsStatusDetectListResponse, error) {
	data, total, err := assets_status_detect.NewAssetsStatusDetectTaskModel(mysql.GetInstance()).
		FindByUserID(in.UserId, in.TaskId, int(in.CurrentPage), int(in.PerPage), in.UserName, in.CreatedAtRange)
	if err != nil {
		return nil, err
	}
	log.Infof("[Assets Status Detect] Get Assets Status Detect List, Request: %v", in)

	resp := &pb.GetAssetsStatusDetectListResponse{}
	if total > 0 {
		resp.Total = total
		resp.CurrentPage = in.CurrentPage
		resp.PerPage = in.PerPage
	}

	datas := make([]*pb.GetAssetsStatusDetectList, 0, len(data))
	for i := range data {
		filePath, _ := utils.LaravelEncrypt(utils.DownloadFile{
			Url:  filepath.Join(storage.GetPublicStoragePath(), data[i].FilePath),
			Name: "资产状态检测" + utils.GetExt(data[i].FilePath),
		})
		d := &pb.GetAssetsStatusDetectList{
			ImportAssets:   data[i].Total,
			ProcessedData:  data[i].ProcessedData,
			Status:         int64(data[i].ProgressState),
			OnlineAssets:   data[i].OnlineAssets,
			UnonlineAssets: data[i].UnonlineAssets,
			Operator:       data[i].UserName,
			UserId:         data[i].UserId,
			//FilePath:       filepath.Join(storage.GetDownloadPrefix(), filePath+path.Ext(data[i].FilePath)),
			Id:       uint64(data[i].ID),
			Date:     data[i].CreatedAt.Format(utils.DateTimeLayout),
			TaskType: data[i].TaskType,
		}
		if in.GetFilePath {
			d.FilePath = filepath.Join(storage.GetDownloadPrefix(), filePath+path.Ext(data[i].FilePath))
		}
		datas = append(datas, d)
	}
	resp.Items = datas
	return resp, nil
}

// DelAssetsStatusDetectTask 删除任务
func DelAssetsStatusDetectTask(in *pb.DelAssetsStatusDetectTaskRequest) error {
	var dels []uint
	for i := 0; i < len(in.TaskIds); i++ {
		dels = append(dels, uint(in.TaskIds[i]))
	}

	err := assets_status_detect.NewAssetsStatusDetectTaskModel(mysql.GetInstance()).Del(dels, in.UserId, in.CreatedAtRange)
	if err != nil {
		log.Errorf("[Assets Status Detect] Delete Assets Status Detect Task -> TABLE:assets_status_detect_task, err: %s", err.Error())
		return err
	}
	return nil
}
