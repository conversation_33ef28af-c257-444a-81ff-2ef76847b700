package assets_detect

import (
	"errors"
	"fmt"
	"path/filepath"
	"strconv"

	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql/assets_status_detect"
	"micro-service/middleware/mysql/user"
	detect_cache "micro-service/middleware/redis/assets_status_detect"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"

	"github.com/spf13/cast"
	"go.uber.org/atomic"
	"gorm.io/gorm"
)

// StartAssetsStatusDetect 开始资产检测
func StartAssetsStatusDetect(currentUserID, companyUserId uint64, file string) (uint64, []string, error) {
	filePath := filepath.Join(storage.GetPublicPath(), file)
	// 等待文件同步
	waitFileSync(filePath)

	// 解析excel
	log.Info("[资产状态检测] 准备读取excel文件: %s", filePath)
	ips, total, err := readExcel(filePath)
	if err != nil {
		return 0, nil, err
	}
	if len(ips) == 0 {
		return 0, []string{}, errors.New("模版文件中未查询到数据，请重新修改")
	}

	// 创建任务记录表
	u, err := user.NewUserModel(mysql.GetInstance()).FindById(currentUserID)
	if err == gorm.ErrRecordNotFound {
		return 0, []string{}, errors.New("未查询到用户信息")
	}
	if err != nil {
		return 0, []string{}, err
	}

	id := currentUserID
	if companyUserId != currentUserID {
		id = companyUserId
	}

	taskid, err := assets_status_detect.NewAssetsStatusDetectTaskModel(mysql.GetInstance()).Add(
		assets_status_detect.AssetsStatusDetectTask{
			Total:          int64(total),
			ProcessedData:  0,
			ProgressState:  assets_status_detect.TaskProgressStateIng,
			OnlineAssets:   0,
			UnonlineAssets: 0,
			UserName:       u.Name.String,
			UserId:         id,
			FilePath:       "",
		})
	if err != nil {
		log.Errorf("[Assets Status Detect] StartAt Assets Status Detect -> TABLE:assets_status_detect_task, err: %s", err.Error())
		return 0, []string{}, err
	}
	log.Infof("[Assets Status Detect] StartAt Assets Status Detect -> TABLE 创建, taskid %d, UserId %d, <===> currentUserID: %d, companyUserId: %d",
		taskid, id, currentUserID, companyUserId)

	// 设置缓存
	cacheKey := detect_cache.GetKey(taskid)
	err = detect_cache.NewAssetsStatusDetectCache(redis.GetInstance()).
		SetCache(cacheKey, taskid, 0.00, assets_status_detect.TaskProgressStateIng, cast.ToString(1))
	if err != nil {
		return 0, []string{}, err
	}

	go func() {
		// 获取数据
		out := make(chan scanResult, 200)
		wait := make(chan struct{}, 1)
		stop := make(chan struct{}, 1)

		list := splitMap(ips, 100)
		for i := range list {
			go handleDetect(uint64(taskid), list[i], out, stop)
		}

		// 汇总
		resp := aggro(out, stop, wait, len(list), total, uint64(taskid))
		<-wait

		// 更新记录库、结果表，批量
		file := fmt.Sprintf("assets_status_detect_%d_%s.xlsx", taskid, utils.RandString(20))
		err := setTaskWithResultTable(uint64(taskid), int64(total), file, resp)
		if err != nil {
			log.Errorf("[Assets Status Detect] StartAt Assets Status Detect -> wire excel, err: %s", err.Error())
			return
		}

		// 写excel
		writePath := filepath.Join(storage.GetPublicPath(), file)
		log.Infof("[Assets Status Detect]  StartAt Assets Status Detect -> write excel, file: %s", writePath)
		err = writeExcel(writePath, resp)
		if err != nil {
			log.Errorf("[Assets Status Detect] StartAt Assets Status Detect -> write excel, err: %s", err.Error())
			return
		}
	}()

	return uint64(taskid), []string{}, nil
}

func handleDetect(taskId uint64, ips []map[string]port, outScanResult chan<- scanResult, stop chan struct{}) {
	defer func() {
		stop <- struct{}{}
	}()

	for i := range ips {
		tmp := ips[i]
		for k, v := range tmp {
			ports := tcpGather(k, v.ports)
			outScanResult <- scanResult{
				Ip:           k,
				Ports:        ports,
				TaskId:       taskId,
				defaultPorts: v.defaultPorts,
			}
		}
	}
}

type scanResult struct {
	Ip           string
	Ports        map[string]string
	defaultPorts bool
	TaskId       uint64
}

func aggro(out chan scanResult, stop, wait chan struct{}, num, total int, taskId uint64) *AssetsStatusDetectInfo {
	var (
		done     atomic.Int32
		resp     = &AssetsStatusDetectInfo{}
		aggr     = 0
		cacheKey = detect_cache.GetKey(uint(taskId))
	)

	for {
		select {
		case <-stop:
			aggr++
			if num == aggr {
				close(out)
				stop = nil
			}

		case r, ok := <-out:
			if !ok {
				out = nil
				break
			}

			done.Add(int32(len(r.Ports)))

			onlineAssets := 0
			for k, v := range r.Ports {
				if v == "1" {
					onlineAssets++
					p, _ := strconv.Atoi(k)
					on, _ := strconv.Atoi(v)
					resp.OnlineAssets = append(resp.OnlineAssets, assets_status_detect.AssetsStatusDetectResult{Ip: r.Ip, Port: int64(p), OnlineState: on, TaskId: taskId})
				} else if v == "2" && !r.defaultPorts {
					p, _ := strconv.Atoi(k)
					on, _ := strconv.Atoi(v)
					resp.UnonlineAssets = append(resp.UnonlineAssets, assets_status_detect.AssetsStatusDetectResult{Ip: r.Ip, Port: int64(p), OnlineState: on, TaskId: taskId})
				}
			}

			if onlineAssets == 0 && r.defaultPorts {
				resp.UnonlineAssets = append(resp.UnonlineAssets, assets_status_detect.AssetsStatusDetectResult{Ip: r.Ip, OnlineState: 2, TaskId: taskId})
			}
		}

		// 更新检测进度
		progress := progressValue(done.Load(), total)
		updateProgress(taskId, progress, assets_status_detect.TaskProgressStateIng)

		log.Infof("[Assets Status Detect] Progress --> Set Cache, key:%v, data:%v", cacheKey, progress)

		if out == nil && stop == nil {
			break
		}
	}

	wait <- struct{}{}
	return resp
}

// 计算进度
func progressValue(current int32, total int) float64 {
	f := strconv.FormatFloat(float64(100*current)/float64(total), 'f', 2, 64)
	p, _ := strconv.ParseFloat(f, 64)
	if p >= 100 {
		p = 100
	}
	return p
}

// 更新进度
func updateProgress(taskId uint64, progress float64, status int) {
	client := detect_cache.NewAssetsStatusDetectCache(redis.GetInstance())
	key := detect_cache.GetKey(uint(taskId))

	err := client.SetCache(key, uint(taskId), progress, status, cast.ToString(1))
	if err != nil {
		log.Errorf("[Assets Status Detect]Update progress failed, key->%s, %v", key, err)
	}
}

func setTaskWithResultTable(taskId uint64, total int64, filePath string, data *AssetsStatusDetectInfo) error {
	defer updateProgress(taskId, 100, assets_status_detect.TaskProgressStateDone)

	// 更新记录库
	err := assets_status_detect.NewAssetsStatusDetectTaskModel(mysql.GetInstance()).Update(
		assets_status_detect.AssetsStatusDetectTask{
			Model:          gorm.Model{ID: uint(taskId)},
			Total:          total,
			ProcessedData:  total,
			ProgressState:  assets_status_detect.TaskProgressStateDone,
			OnlineAssets:   int64(len(data.OnlineAssets)),
			UnonlineAssets: int64(len(data.UnonlineAssets)),
			FilePath:       filePath,
		})
	if err != nil {
		log.Errorf("[Assets Status Detect] StartAt Assets Status Detect -> TABLE:assets_status_detect_task, err: %s", err.Error())
		return err
	}

	// 结果表，批量
	if len(data.OnlineAssets) > 0 {
		err = assets_status_detect.NewAssetsStatusDetectResultModel(mysql.GetInstance()).Adds(data.OnlineAssets)
		if err != nil {
			log.Errorf("[Assets Status Detect] StartAt Assets Status Detect -> TABLE:assets_status_detect_result, err: %s", err.Error())
			return err
		}
	}

	if len(data.UnonlineAssets) > 0 {
		err = assets_status_detect.NewAssetsStatusDetectResultModel(mysql.GetInstance()).Adds(data.UnonlineAssets)
		if err != nil {
			log.Errorf("[Assets Status Detect] Assets Status Detect -> TABLE:assets_status_detect_result, err: %s", err.Error())
			return err
		}
	}

	return nil
}
