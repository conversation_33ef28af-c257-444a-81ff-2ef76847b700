package asset_overview

import (
	"context"
	"time"

	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

const overviewRuleCountCachePrefix = "foradar_cache:asset_overview:rule_count:" // +userId

func setRuleCountCache(ctx context.Context, key string, data []*pb.AssetsOverviewRuleCountResponse_BaseInfo) {
	err := redis.Set(context.Background(), key, utils.AnyToStr(data), 10*time.Minute)
	if err != nil {
		log.WithContextWarnf(ctx, "[资产概览] set rule count result to redis failed: %v", err)
	}
}
