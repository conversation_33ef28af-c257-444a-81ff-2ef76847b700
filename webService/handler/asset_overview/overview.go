package asset_overview

import (
	"context"
	"encoding/json"
	"sort"
	"strconv"
	"sync"
	"time"

	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/ip_history"
	"micro-service/middleware/mysql/sensitive_data"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
)

func RuleCount(ctx context.Context, userID uint64) ([]*pb.AssetsOverviewRuleCountResponse_BaseInfo, error) {
	// read rule count cache
	const ruleLen = 5
	data := make([]*pb.AssetsOverviewRuleCountResponse_BaseInfo, 0, ruleLen)
	key := overviewRuleCountCachePrefix + strconv.Itoa(int(userID))
	if redis.GetCache(key, &data) {
		log.WithContextInfof(ctx, "[资产概览] got rule count result from redis: %s", key)
		return data, nil
	}

	client := fofaee_assets.NewFofaeeAssetsModel()
	r, err := client.AggByRule(userID, ruleLen)
	if err != nil {
		return nil, err
	}

	ruleQuery := func(ruleName string) []*pb.AssetsOverviewRuleCountResponseIpUnit {
		if ruleName == "" {
			return nil
		}
		condition := fofaee_assets.NewFindCondition()
		condition.UserId = userID
		condition.ComponentNames = []string{ruleName}
		condition.Status = fofaee_assets.StatusAccount
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		list, _, err := client.FindByCondition(ctx, condition, 0, 0)
		if err != nil {
			return nil
		}
		var ips = make([]*pb.AssetsOverviewRuleCountResponseIpUnit, 0, len(list))
		for i := range list {
			ips = append(ips, &pb.AssetsOverviewRuleCountResponseIpUnit{Ip: list[i].Ip, Time: list[i].CreatedAt})
		}
		return ips
	}

	for i := range r {
		data = append(data, &pb.AssetsOverviewRuleCountResponse_BaseInfo{
			Name:  r[i].Name,
			Count: r[i].Count,
			Ips:   ruleQuery(r[i].Name),
		})
	}

	// write rule count cache
	// go setRuleCountCache(ctx, key, data)

	return data, nil
}

func DigitalAssets(userID uint64) ([]*pb.AssetsOverviewDigitalAssetsResponse_BaseInfo, error) {
	typeModel := []int{4, 5, 6}
	result := make([]*pb.AssetsOverviewDigitalAssetsResponse_BaseInfo, 0, 60)
	for i := range typeModel {
		v, _, err := sensitive_data.NewModel().FindByUserID(userID, typeModel[i], 1, 20)
		if err != nil {
			return nil, err
		}

		for j := range v {
			result = append(result, &pb.AssetsOverviewDigitalAssetsResponse_BaseInfo{
				Id:               v[j].Id,
				UserId:           v[j].UserId,
				CompanyId:        v[j].CompanyId,
				OperatorId:       v[j].OperatorId,
				Type:             int64(v[j].Type),
				Name:             v[j].Name,
				Account:          v[j].Account,
				Url:              v[j].Url,
				Img:              v[j].Img,
				Keyword:          v[j].Keyword,
				Owner:            v[j].Owner,
				Logo:             v[j].Logo,
				Status:           int32(v[j].Status),
				FirstFoundTime:   v[j].FirstFoundTime.Format(utils.DateTimeLayout),
				LastestFoundTime: v[j].LastestFoundTime.Format(utils.DateTimeLayout),
				CreatedAt:        v[j].CreatedAt.Format(utils.DateTimeLayout),
				UpdatedAt:        v[j].UpdatedAt.Format(utils.DateTimeLayout),
			})
		}
	}
	return result, nil
}

func ComparedLastMonth(userID int) (*pb.ComparedLastMonthResponse, error) {
	data := &pb.ComparedLastMonthResponse{}

	var wg sync.WaitGroup
	wg.Add(7)
	var ledger float64
	go func() {
		ledger = Ledger(userID)
		wg.Done()
	}()

	var doubt float64
	go func() {
		doubt = Doubt(userID)
		wg.Done()
	}()

	var threaten float64
	go func() {
		threaten = Threaten(userID)
		wg.Done()
	}()

	var digital float64
	go func() {
		digital = Digital(userID)
		wg.Done()
	}()

	var login *pb.ComparedLastMonthResponse_LoginInfo
	go func() {
		login = loginInfo(userID)
		wg.Done()
	}()

	var domain *pb.ComparedLastMonthResponse_DomainInfo
	go func() {
		domain = DomainAssets(userID)
		wg.Done()
	}()

	var cert *pb.ComparedLastMonthResponse_CertInfo
	go func() {
		cert = CertAssets(userID)
		wg.Done()
	}()

	wg.Wait()

	data.Ledger = ledger
	data.Doubt = doubt
	data.Threaten = threaten
	data.Digital = digital
	data.Login = login
	data.Domain = domain
	data.Cert = cert
	return data, nil
}

func AssetsDynamic(userID uint64, createAtRange []string, hasThreatData bool) ([]*pb.AssetsOverviewAssetsDynamicBaseInfo, error) {
	size := 50
	page := 1
	query := make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithColumnValue("user_id", userID))
	if len(createAtRange) > 0 {
		if len(createAtRange) == 1 {
			query = append(query, mysql.WithGT("created_at", createAtRange[0]))
		}
		if len(createAtRange) == 2 {
			query = append(query, mysql.WithBetween("created_at", createAtRange[0], createAtRange[1]))
		}
	}
	if hasThreatData {
		query = append(query, mysql.WithColumnNotNull("threat_data"))
	} else {
		query = append(query, mysql.WithColumnNull("threat_data"))
	}
	data, _, err := ip_history.NewModel().GetIp(page, size, query...)
	if err != nil {
		return nil, err
	}

	ip := make([]*pb.AssetsOverviewAssetsDynamicBaseInfo, 0, 50)
	ip = appendIP(userID, data, ip)
	return ip, nil
}

func appendIP(userID uint64, ipData []string, ip []*pb.AssetsOverviewAssetsDynamicBaseInfo) []*pb.AssetsOverviewAssetsDynamicBaseInfo {
	out := make(chan *pbIP, 100)
	wait := make(chan struct{}, 1)
	stop := make(chan struct{}, 1)

	if len(ipData) > 0 {
		for i := range ipData {
			go handel(userID, ipData[i], out, stop)
		}

		r := agg(out, stop, wait, len(ipData))
		<-wait

		ip = append(ip, r...)
	}
	return ip
}

func handel(userID uint64, ipStr string, out chan<- *pbIP, stop chan struct{}) {
	defer func() {
		stop <- struct{}{}
	}()

	// ip
	ipData, err := ip_history.NewModel().FindByIp(userID, ipStr)
	if err != nil {
		return
	}

	// decode ip
	type ThreatData struct {
		Err            error
		Data           []string
		CreatedAt      string
		ThreatDataType int32 // 1/漏洞核查的数据 2/漏洞扫描的数据
	}

	value := ThreatData{}
	assets := make([]fofaee_assets.FofaeeAssets, 0, len(ipData))
	for i := range ipData {
		if ipData[i].Data == "" {
			d := make([]string, 0, 10)
			value.Err = json.Unmarshal([]byte(ipData[i].ThreatData), &d)
			if value.Err != nil {
				break
			}
			value.Data = d
			value.CreatedAt = ipData[i].CreatedAt.Format(utils.DateTimeLayout)
			value.ThreatDataType = ipData[i].ThreatDataType
			break
		} else {
			d, err := fofaee_assets.Decode(ipData[i].Data)
			if err != nil {
				return
			}
			d.CreatedAt = ipData[i].CreatedAt.Format(utils.DateTimeLayout)
			assets = append(assets, d)
		}
	}
	// Convert uint64 to string
	userIDStr := cast.ToString(userID)

	// Concatenate the string
	result := userIDStr + "_" + ipStr
	// 比较
	if len(assets) == 0 {
		if value.Err == nil && len(value.Data) > 0 {
			time, _ := time.ParseInLocation("2006-01-02 15:04:05", value.CreatedAt, time.Local)
			if value.ThreatDataType == 1 {
				out <- &pbIP{
					Status:   2,
					Ip:       ipStr,
					DataType: 3,
					Data:     value.Data,
					Time:     time,
					Id:       result,
				}
			} else {
				out <- &pbIP{
					Status:   1,
					Ip:       ipStr,
					DataType: 3, // 漏洞信息
					Data:     value.Data,
					Time:     time,
					Id:       result,
				}
			}
		}
		return
	}

	if len(assets) == 1 {
		time, _ := time.ParseInLocation("2006-01-02 15:04:05", assets[0].CreatedAt, time.Local)
		out <- &pbIP{
			Status:   1,
			Ip:       ipStr,
			DataType: 4, // 资产增加
			Data:     []string{"发现新资产"},
			Time:     time,
			Id:       result,
		}
	} else if len(assets) > 1 {
		addRule, delRule := GetAddAndDel(assets[0].GetCnProduct(), assets[1].GetCnProduct())
		time, _ := time.ParseInLocation("2006-01-02 15:04:05", assets[0].CreatedAt, time.Local)
		if len(addRule) > 0 {
			out <- &pbIP{
				Status:   1,
				Ip:       ipStr,
				DataType: 1,
				Data:     addRule,
				Time:     time,
				Id:       result,
			}
		}

		if len(delRule) > 0 {
			out <- &pbIP{
				Status:   2,
				Ip:       ipStr,
				DataType: 2,
				Data:     delRule,
				Time:     time,
				Id:       result,
			}
		}

		addPort, delPort := GetAddAndDel(assets[0].GetPortStr(), assets[1].GetPortStr())
		if len(addPort) > 0 {
			out <- &pbIP{
				Status:   1,
				Ip:       ipStr,
				DataType: 2,
				Data:     addPort,
				Time:     time,
				Id:       result,
			}
		}

		if len(delPort) > 0 {
			out <- &pbIP{
				Status:   2,
				Ip:       ipStr,
				DataType: 2,
				Data:     delPort,
				Time:     time,
				Id:       result,
			}
		}
	}
}

func agg(out chan *pbIP, stop, wait chan struct{}, numThread int) []*pb.AssetsOverviewAssetsDynamicBaseInfo {
	var aggr = 0
	ip := make([]*pb.AssetsOverviewAssetsDynamicBaseInfo, 0, numThread)
	tmp := make(pbArrayIP, 0, numThread)
	for {
		select {
		case <-stop:
			aggr++
			if numThread == aggr {
				close(stop)
				close(out)
				stop = nil
			}

		case r, ok := <-out:
			if !ok {
				out = nil
				break
			}
			tmp = append(tmp, r)
		}

		if out == nil && stop == nil {
			break
		}
	}

	sort.Sort(tmp)

	for i := range tmp {
		ip = append(ip, &pb.AssetsOverviewAssetsDynamicBaseInfo{
			Status:   tmp[i].Status,
			Ip:       tmp[i].Ip,
			DataType: tmp[i].DataType,
			Data:     tmp[i].Data,
			Time:     tmp[i].Time.Format(utils.DateTimeLayout),
			Id:       tmp[i].Id,
		})
	}
	wait <- struct{}{}
	return ip
}

type pbIP struct {
	Status   int32    `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 1-增加 2-删除
	Ip       string   `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	DataType int32    `protobuf:"varint,3,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"` // 1-rule_tags 2-port_list
	Data     []string `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	Time     time.Time
	Id       string
}

type pbArrayIP []*pbIP

func (pa pbArrayIP) Len() int {
	return len(pa)
}

func (pa pbArrayIP) Swap(i, j int) {
	pa[i], pa[j] = pa[j], pa[i]
}

func (ms pbArrayIP) Less(i, j int) bool {
	return ms[i].Time.After(ms[j].Time)
}
