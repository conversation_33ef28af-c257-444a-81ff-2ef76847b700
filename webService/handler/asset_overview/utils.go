package asset_overview

import (
	"math"
	"time"
)

func NowMonthRange() (string, string) {
	now := time.Now()
	return monthRange(now)
}

func LastMonthRange() (string, string) {
	now := time.Now()
	lastMonth := now.AddDate(0, -1, 0)
	return monthRange(lastMonth)
}

func monthRange(now time.Time) (string, string) {
	start := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	end := start.AddDate(0, 1, -1).Add(time.Hour*23 + time.Minute*59 + time.Second*59)
	return start.Format("2006-01-02 15:04:05"), end.Format("2006-01-02 15:04:05")
}

func ComparedRate(last, now int64) float64 {
	if last == 0 && now > 0 {
		return 1
	} else if last == 0 && now == 0 {
		return 0
	} else {
		x := float64(now-last) / float64(last)
		return math.Round(x*100) / 100
	}
}

func GetAddAndDel(now, last []string) ([]string, []string) {
	addV := difference(now, last)
	delV := difference(last, now)
	return addV, delV
}

func difference(left, right []string) []string {
	// 使用map存储b中的元素
	m := make(map[string]bool)
	for _, v := range right {
		m[v] = true
	}

	// 遍历a中的元素，如果不在m中，则加入结果
	result := make([]string, 0)
	for _, v := range left {
		if !m[v] {
			result = append(result, v)
		}
	}
	return result
}
