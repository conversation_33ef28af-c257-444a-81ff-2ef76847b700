package asset_overview

import (
	"context"
	"fmt"
	"testing"

	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
)

func initCfg() {
	_ = godotenv.Load("../../../../.env")

	cfg.InitLoadCfg()
	log.Init()
	_ = es.GetInstance(cfg.LoadElastic())
	_ = mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

func Test_RuleCount(t *testing.T) {
	initCfg()

	list, err := RuleCount(context.Background(), 1)
	assert.Nil(t, err)
	fmt.Println("total:", len(list))
}
