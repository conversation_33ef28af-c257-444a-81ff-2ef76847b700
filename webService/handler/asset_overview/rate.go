package asset_overview

import (
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/middleware/elastic/fofaee_assets"
	db "micro-service/middleware/mysql"
	"micro-service/middleware/mysql/cert_assets"
	"micro-service/middleware/mysql/domain_assets"
	"micro-service/middleware/mysql/login_page_assets"
	"micro-service/middleware/mysql/sensitive_data"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

// 台账
func Ledger(userID int) float64 {
	lastStart, lastEnd := LastMonthRange()
	nowStart, nowEnd := NowMonthRange()

	nowLedger, err := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance()).
		CountByStatusAndUserIDAndRange(userID, []any{1, 4}, nowStart, nowEnd)
	if err != nil {
		log.Errorf("[asset overview] 台账环比 ->  err: %v", err)
		return 0
	}

	lastLedger, err := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance()).
		CountByStatusAndUserIDAndRange(userID, []any{1, 4}, lastStart, lastEnd)
	if err != nil {
		log.Errorf("[asset overview] 台账环比 ->  err: %v", err)
		return 0
	}
	return ComparedRate(lastLedger, nowLedger)
}

// 疑似
func Doubt(userID int) float64 {
	lastStart, lastEnd := LastMonthRange()
	nowStart, nowEnd := NowMonthRange()

	nowDoubt, err := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance()).
		CountByStatusAndUserIDAndRange(userID, []any{0}, nowStart, nowEnd)
	if err != nil {
		return 0
	}

	lastDoubt, err := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance()).
		CountByStatusAndUserIDAndRange(userID, []any{0}, lastStart, lastEnd)
	if err != nil {
		return 0
	}
	return ComparedRate(lastDoubt, nowDoubt)
}

// 威胁
func Threaten(userID int) float64 {
	lastStart, lastEnd := LastMonthRange()
	nowStart, nowEnd := NowMonthRange()

	nowThreaten, err := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance()).
		CountByStatusAndUserIDAndRange(userID, []any{3}, nowStart, nowEnd)
	if err != nil {
		return 0
	}

	lastThreaten, err := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance()).
		CountByStatusAndUserIDAndRange(userID, []any{3}, lastStart, lastEnd)
	if err != nil {
		return 0
	}
	return ComparedRate(lastThreaten, nowThreaten)
}

// 数字
func Digital(userID int) float64 {
	lastStart, lastEnd := LastMonthRange()
	nowStart, nowEnd := NowMonthRange()

	nowDigital, err := sensitive_data.NewModel(mysql.GetInstance()).CountByRange(uint64(userID), []int{4, 5, 6},
		nowStart, nowEnd)
	if err != nil {
		return 0
	}

	lastDigital, err := sensitive_data.NewModel(mysql.GetInstance()).CountByRange(uint64(userID), []int{4, 5, 6},
		lastStart, lastEnd)
	if err != nil {
		return 0
	}
	return ComparedRate(lastDigital, nowDigital)
}

// 登录入口
func loginInfo(userID int) *pb.ComparedLastMonthResponse_LoginInfo {
	lastStart, lastEnd := LastMonthRange()
	nowStart, nowEnd := NowMonthRange()
	var err error
	r := &pb.ComparedLastMonthResponse_LoginInfo{}
	r.ToConfirmed, err = login_page_assets.NewModel(mysql.GetInstance()).CountByUserID(uint64(userID), "", "", 0)
	if err != nil {
		return &pb.ComparedLastMonthResponse_LoginInfo{}
	}

	r.Confirmed, err = login_page_assets.NewModel(mysql.GetInstance()).CountByUserID(uint64(userID), "", "", 1)
	if err != nil {
		return &pb.ComparedLastMonthResponse_LoginInfo{}
	}

	r.Ignore, err = login_page_assets.NewModel(mysql.GetInstance()).CountByUserID(uint64(userID), "", "", 2)
	if err != nil {
		return &pb.ComparedLastMonthResponse_LoginInfo{}
	}

	now, err := login_page_assets.NewModel(mysql.GetInstance()).CountByUserID(uint64(userID), nowStart, nowEnd)
	if err != nil {
		return &pb.ComparedLastMonthResponse_LoginInfo{}
	}

	last, err := login_page_assets.NewModel(mysql.GetInstance()).CountByUserID(uint64(userID), lastStart, lastEnd)
	if err != nil {
		return &pb.ComparedLastMonthResponse_LoginInfo{}
	}

	r.Total = r.ToConfirmed + r.Confirmed + r.Ignore
	r.Rate = ComparedRate(last, now)
	return r
}

// 域名资产
func DomainAssets(userID int) *pb.ComparedLastMonthResponse_DomainInfo {
	var err error
	conn := domain_assets.NewModel(mysql.GetInstance())
	count := func(opts ...db.HandleFunc) int64 {
		var total int64
		if err == nil {
			opts = append(opts, db.WithColumnValue("user_id", userID))
			total, err = conn.Count(opts...)
		}
		return total
	}

	r := &pb.ComparedLastMonthResponse_DomainInfo{}
	r.Father = count(db.WithColumnValue("`type`", 1))
	r.Child = count(db.WithColumnValue("`type`", 0))
	r.Total = r.Father + r.Child

	lastStart, lastEnd := LastMonthRange()
	nowStart, nowEnd := NowMonthRange()
	last := count(db.WithBetween("created_at", lastStart, lastEnd))
	now := count(db.WithBetween("created_at", nowStart, nowEnd))
	r.Rate = ComparedRate(last, now)
	if err != nil {
		log.Errorf("%v", err)
		return &pb.ComparedLastMonthResponse_DomainInfo{}
	}
	return r
}

// 证书资产
func CertAssets(userID int) *pb.ComparedLastMonthResponse_CertInfo {
	lastStart, lastEnd := LastMonthRange()
	nowStart, nowEnd := NowMonthRange()

	var err error
	r := &pb.ComparedLastMonthResponse_CertInfo{}
	r.Valid, err = cert_assets.NewModel(mysql.GetInstance()).
		CountByUserID(uint64(userID), "", "", 1)
	if err != nil {
		return &pb.ComparedLastMonthResponse_CertInfo{}
	}

	r.NotValid, err = cert_assets.NewModel(mysql.GetInstance()).
		CountByUserID(uint64(userID), "", "", 0)
	if err != nil {
		return &pb.ComparedLastMonthResponse_CertInfo{}
	}
	r.Total = r.Valid + r.NotValid

	now, err := cert_assets.NewModel(mysql.GetInstance()).
		CountByUserID(uint64(userID), nowStart, nowEnd)
	if err != nil {
		return &pb.ComparedLastMonthResponse_CertInfo{}
	}

	last, err := cert_assets.NewModel(mysql.GetInstance()).
		CountByUserID(uint64(userID), lastStart, lastEnd)
	if err != nil {
		return &pb.ComparedLastMonthResponse_CertInfo{}
	}
	r.Rate = ComparedRate(last, now)
	return r
}
