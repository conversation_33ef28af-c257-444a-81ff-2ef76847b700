package handler

// 总库管理

import (
	"context"
	"path"

	"micro-service/apiService/upload"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/manage/black_keyword"
	"micro-service/webService/handler/manage/digital_asset"
	"micro-service/webService/handler/manage/icp"
	pb "micro-service/webService/proto"

	"go-micro.dev/v4/errors"
)

// ManageBeianList 获取备案信息列表
func (*Web) ManageBeianList(ctx context.Context, req *pb.ManageBeianListRequest, rsp *pb.ManageBeianListResponse) error {
	log.WithContextInfof(ctx, "[ICP备案管理-Domain] Received web.ManageBeianList request: %v", req)
	if err := icp.CompanyIcpList(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[ICP备案管理-Domain] company icp: Get icp list failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageBeianCreate 创建备案信息
func (*Web) ManageBeianCreate(ctx context.Context, req *pb.ManageBeianCreateRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[ICP备案管理-Domain] Received web.ManageBeianCreate request: %v", req)
	if err := icp.CompanyIcpCreate(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[ICP备案管理-Domain] company icp: Create icp failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) ManageBeianUpdate(ctx context.Context, req *pb.ManageBeianCreateRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[ICP备案管理-Domain] Received web.ManageBeianUpdate request: %v", utils.AnyToStr(req))
	if err := icp.CompanyIcpUpdate(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[ICP备案管理-Domain] update domain icp failed: %v, request: %s", err, utils.AnyToStr(req))
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) ManageIcpAppList(ctx context.Context, req *pb.IcpAppListRequest, rsp *pb.IcpAppListResponse) error {
	log.WithContextInfof(ctx, "[ICP备案管理-APP] Received Web.ManageIcpAppList request: %v", req)
	if err := icp.AppIcpList(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[ICP备案管理-APP] request web.ManageIcpAppList failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) ManageIcpAppCreate(ctx context.Context, req *pb.IcpAppListItem, rsp *pb.Empty) error {
	log.WithContextInfof(ctx, "[ICP备案管理-APP] Received web.ManageIcpAppCreate request: %v", req)
	if err := icp.AppIcpCreate(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[ICP备案管理-APP] request web.ManageIcpAppCreate failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) ManageIcpAppUpdate(ctx context.Context, req *pb.IcpAppListItem, rsp *pb.Empty) error {
	log.WithContextInfof(ctx, "[ICP备案管理-APP] Received web.ManageIcpAppUpdate request: %v", req)
	if err := icp.AppIcpUpdate(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[ICP备案管理-APP] request web.ManageIcpAppUpdate failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalKeywordInfo 数字资产-任务管理：关键词任务详情
func (*Web) ManageDigitalKeywordInfo(ctx context.Context, req *pb.ManageDigitalKeywordUpsertRequest, rsp *pb.ManageDigitalKeywordInfoResponse) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalKeywordInfo request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalKeywordInfo(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: get taskId: %d failed: %v", req.Id, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalKeywordUpsert 数字资产-任务管理：下发关键词任务或更新
func (*Web) ManageDigitalKeywordUpsert(ctx context.Context, req *pb.ManageDigitalKeywordUpsertRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalKeywordUpsert request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalKeywordUpsert(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: Upsert digital keyword failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalKeywordList 数字资产-任务管理：获取数字关键词列表
func (*Web) ManageDigitalKeywordList(ctx context.Context, req *pb.ManageDigitalKeywordListRequest, rsp *pb.ManageDigitalKeywordListResponse) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalKeywordList request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalKeywordList(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: Get digital keyword list failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalKeywordDelete 数字资产-任务管理：删除关键词记录
func (*Web) ManageDigitalKeywordDelete(ctx context.Context, req *pb.ManageDigitalKeywordListRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalKeywordDelete request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalKeywordDelete(req); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: Delete keywords failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalKeywordResultList 数字资产-任务详情：结果列表
func (*Web) ManageDigitalKeywordResultList(ctx context.Context, req *pb.ManageDigitalAssetsResultListRequest, rsp *pb.ManageDigitalAssetsResultListResponse) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalKeywordResultList request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalKeywordResultList(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Assets: Get TaskId->%d asset result failed: %v", req.TaskId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalAssetsResultList 数字资产-资产管理：获取数字资产结果列表
func (*Web) ManageDigitalAssetsResultList(ctx context.Context,
	req *pb.ManageDigitalAssetsResultListRequest, rsp *pb.ManageDigitalAssetsResultListResponse) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalAssetsResultList request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalAssetsResultList(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: Get digital assets result list failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalAssetsResultCreate 数字资产-：新建数字资产信息
func (*Web) ManageDigitalAssetsResultCreate(ctx context.Context, req *pb.ManageDigitalAssetsResultItem, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalAssetsResultCreate request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalAssetsResultCreate(req); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: Create digital assets result failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalAssetsResultImport 数字资产-：批量导入数字资产
func (w *Web) ManageDigitalAssetsResultImport(ctx context.Context, req *pb.ManageDigitalAssetsResultImportRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalAssetsResultImport request %s", utils.AnyToStr(req))
	filePath, err := upload.UploadDecrypt(req.FilePath)
	if err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] 资产文件路径导入解密出错：%s", err.Error())
		return errors.BadRequest(pb.ServiceName, "上传文件格式错误")
	}
	req.FilePath = path.Join(storage.GetRootPath(), filePath.Url)
	if err = digital_asset.DigitalAssetsResultImport(req.FilePath, req.Source); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: Import digital assets failure: %v, FilePath: %s", err, req.FilePath)
		// _ = os.Remove(req.FilePath)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalAssetsResultUpdate 数字资产-资产管理：编辑数字资产信息
func (*Web) ManageDigitalAssetsResultUpdate(ctx context.Context, req *pb.ManageDigitalAssetsResultItem, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalAssetsResultUpdate request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalAssetsResultUpdate(req); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: Update digital info failed: recordId->%d, %v", req.Id, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalAssetsResultDelete 数字资产-资产管理：删除数字资产关联信息
func (*Web) ManageDigitalAssetsResultDelete(ctx context.Context, req *pb.ManageDigitalAssetsResultListRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalAssetsResultDelete request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalAssetsResultDelete(req); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: Delete Digital assets relation result failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageDigitalFilterGroup 数字资产-资产管理：删除数字资产关联信息
func (*Web) ManageDigitalFilterGroup(ctx context.Context, req *pb.ManageDigitalAssetsResultImportRequest, rsp *pb.ManageDigitalFilterGroupResponse) error {
	log.WithContextInfof(ctx, "[数字资产总库] Received web.ManageDigitalFilterGroup request %s", utils.AnyToStr(req))
	if err := digital_asset.DigitalFilterGroup(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[数字资产总库] Digital Keyword: Get filter group list failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// BlackKeywordList 获取黄赌毒审核列表
func (*Web) BlackKeywordList(ctx context.Context, req *pb.BlackKeywordListRequest, rsp *pb.BlackKeywordListResponse) error {
	log.WithContextInfof(ctx, "[威胁词库] Received web.BlackKeywordList request: %s", utils.AnyToStr(req))
	if err := black_keyword.BlackKeywordList(ctx, req, rsp); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// BlackKeywordUpdate 黄赌毒审核
func (*Web) BlackKeywordUpdate(ctx context.Context, req *pb.BlackKeywordUpdateRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[威胁词库] Received web.BlackKeywordUpdate request: %s", utils.AnyToStr(req))
	if err := black_keyword.BlackKeywordUpdate(ctx, req); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageBlackKeywordTypeList 获取黄赌毒分类
func (*Web) ManageBlackKeywordTypeList(ctx context.Context, _ *pb.Empty, rsp *pb.BlackKeywordListResponse) error {
	log.WithContextInfof(ctx, "[威胁词库] Received web.ManageBlackKeywordTypeList request")
	if err := black_keyword.KeywordTypeList(rsp); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageBlackKeywordTypeCreate 新建黄赌毒分类
func (*Web) ManageBlackKeywordTypeCreate(ctx context.Context, req *pb.BlackKeywordTypeCreateRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[威胁词库] Received web.ManageBlackKeywordTypeList request: %s", utils.AnyToStr(req))
	if err := black_keyword.KeywordTypeCreate(req.GetNames()); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageBlackKeywordList 黄赌毒总库-列表
func (*Web) ManageBlackKeywordList(ctx context.Context, req *pb.BlackKeywordListRequest, rsp *pb.BlackKeywordListResponse) error {
	log.WithContextInfof(ctx, "[威胁词库] Received web.ManageBlackKeywordList request: %s", utils.AnyToStr(req))
	if err := black_keyword.ManageBlackKeywordList(req, rsp); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageBlackKeywordCreate 黄赌毒总库-新建
func (*Web) ManageBlackKeywordCreate(ctx context.Context, req *pb.ManageBlackKeywordCreateRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[威胁词库] Received web.ManageBlackKeywordCreate request: %s", utils.AnyToStr(req))
	if err := black_keyword.ManageBlackKeywordCreate(req); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageBlackKeywordDelete 黄赌毒总库-删除
func (*Web) ManageBlackKeywordDelete(ctx context.Context, req *pb.BlackKeywordListRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[威胁词库] Received web.ManageBlackKeywordDelete request: %s", utils.AnyToStr(req))
	if err := black_keyword.ManageBlackKeywordDelete(req); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ManageBlackKeywordUpdate 黄赌毒总库-更新
func (*Web) ManageBlackKeywordUpdate(ctx context.Context, req *pb.ManageBlackKeywordUpdateRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[威胁词库] Received web.ManageBlackKeywordUpdate request: %s", utils.AnyToStr(req))
	if err := black_keyword.ManageBlackKeywordUpdate(req); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}
