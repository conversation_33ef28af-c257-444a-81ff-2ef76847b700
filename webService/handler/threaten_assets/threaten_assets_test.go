package threaten_assets

import (
	"context"
	"micro-service/pkg/dbx"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	testcommon "micro-service/initialize/common_test"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql/clues"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/asset_account"
	pb "micro-service/webService/proto"
)

var (
	indexName  = fofaee_assets.FofaeeAssetsIndex
	docType    = fofaee_assets.FofaeeAssetsType
	indexName2 = foradar_assets.IndexName
)

// Init 初始化Mock服务
func Init() {
	cfg.InitLoadCfg()
	log.Init()

	// 启用测试环境
	testcommon.SetTestEnv(true)

	// 初始化Mock服务 - 使用testcommon的MockServer
	mock := testcommon.NewMockEsServer()

	// 查询语句Mock数据 - 威胁资产
	resultList := []fofaee_assets.FofaeeAssets{
		{
			Id:     "1_**********",
			Ip:     "**********",
			Status: fofaee_assets.STATUS_THREATEN,
			Type:   fofaee_assets.TYPE_RECOMMEND,
			HostList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:           80,
					Protocol:       "http",
					Title:          "威胁网站标题",
					Subdomain:      "threat.example.com",
					Domain:         "example.com",
					HttpStatusCode: "200",
					Logo: fofaee_assets.Icon{
						Content: "/storage/threat_logo.png",
						Hash:    "-**********",
					},
				},
			},
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:           "80",
					Protocol:       "http",
					Title:          "威胁网站标题",
					HttpStatusCode: "200",
				},
			},
			Geo: fofaee_assets.Geo{
				Asn:      "AS4134 China Telecom (Group)",
				Province: "Shanghai",
				Isp:      "China Telecom",
				Lat:      31.2304,
				Lon:      121.4737,
			},
			CloudName:       []any{"ThreatCloud", "456"},
			ClueCompanyName: []any{"威胁公司有限公司"},
			CustomerTags:    []string{"威胁标签1", "威胁标签2"},
			RuleTags: []fofaee_assets.RuleTag{
				{
					RuleId:    "threat_tag_123",
					Product:   "威胁组件",
					CnProduct: "威胁组件中文",
				},
			},
			ReasonArr: []any{
				map[string]interface{}{
					"group_id":          5678,
					"id":                2,
					"source":            4,
					"type":              0,
					"content":           "threat.example.com",
					"clue_company_name": "威胁公司有限公司",
				},
			},
			UpdatedAt:    "2025-06-06 16:13:00",
			CreatedAt:    "2025-06-05 10:00:00",
			UserId:       1,
			OnlineState:  1,
			ThreatenType: 1,
			Tags:         []int{fofaee_assets.SAFE_SCAN},
		},
		{
			Id:     "1_**********",
			Ip:     "**********",
			Status: fofaee_assets.STATUS_THREATEN,
			Type:   fofaee_assets.TYPE_RECOMMEND,
			HostList: []fofaee_assets.FofaeeAssetPort{
				{
					HttpStatusCode: 200,
					Port:           "443",
					Protocol:       "https",
					Title:          "另一个威胁网站",
				},
			},
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:           "443",
					HttpStatusCode: 200,
					Protocol:       "https",
					Reason: []fofaee_assets.AssetReason{
						{
							Id:              3,
							Source:          4,
							Type:            0,
							Content:         "threat2.example.com",
							ClueCompanyName: "另一个威胁公司",
						},
					},
				},
			},
			Geo: fofaee_assets.Geo{
				Province: "Beijing",
				Isp:      "China Unicom",
			},
			UpdatedAt:    "2025-06-06 15:00:00",
			CreatedAt:    "2025-06-05 09:00:00",
			UserId:       1,
			OnlineState:  0,
			ThreatenType: 2,
		},
	}

	// ForadarAsset Mock数据
	foradarResultList := []foradar_assets.ForadarAsset{
		{
			ID:        "foradar_1_**********",
			Ip:        "**********",
			Port:      80,
			Protocol:  "http",
			Title:     "威胁网站标题",
			Subdomain: "threat.example.com",
			Domain:    "example.com",
			Geo: foradar_assets.AssetGeo{
				Asn:      "AS4134 China Telecom (Group)",
				Province: "Shanghai",
				Isp:      "China Telecom",
			},
			ClueCompanyName: []string{"威胁公司有限公司"},
			RuleTags: []foradar_assets.RuleTag{
				{
					RuleID:    "threat_tag_123",
					Product:   "威胁组件",
					CnProduct: "威胁组件中文",
				},
			},
			Reason: []foradar_assets.AssetReason{
				{
					ID:              2,
					Source:          4,
					Type:            0,
					Content:         "threat.example.com",
					ClueCompanyName: "威胁公司有限公司",
				},
			},
			CreatedAt:      "2025-06-05 10:00:00",
			UpdatedAt:      "2025-06-06 16:13:00",
			UserID:         1,
			OnlineState:    1,
			ThreatenType:   1,
			Status:         fofaee_assets.STATUS_THREATEN,
			HTTPStatusCode: "200",
		},
		{
			ID:       "foradar_1_**********",
			Ip:       "**********",
			Port:     443,
			Protocol: "https",
			Title:    "另一个威胁网站",
			Geo: foradar_assets.AssetGeo{
				Province: "Beijing",
				Isp:      "China Unicom",
			},
			CreatedAt:      "2025-06-05 09:00:00",
			UpdatedAt:      "2025-06-06 15:00:00",
			UserID:         1,
			OnlineState:    0,
			ThreatenType:   2,
			Status:         fofaee_assets.STATUS_THREATEN,
			HTTPStatusCode: "403",
		},
	}

	// 注册FofaeeAssets查询Mock
	mock.Register("/"+indexName+"/_search", []*elastic.SearchHit{
		{
			Index:  indexName,
			Type:   docType,
			Id:     resultList[0].Id,
			Source: utils.ToJSON(resultList[0]),
		},
		{
			Index:  indexName,
			Type:   docType,
			Id:     resultList[1].Id,
			Source: utils.ToJSON(resultList[1]),
		},
	})

	// 注册ForadarAssets查询Mock
	mock.Register("/"+indexName2+"/_search", []*elastic.SearchHit{
		{
			Index:  indexName2,
			Type:   "assets",
			Id:     foradarResultList[0].ID,
			Source: utils.ToJSON(foradarResultList[0]),
		},
		{
			Index:  indexName2,
			Type:   "assets",
			Id:     foradarResultList[1].ID,
			Source: utils.ToJSON(foradarResultList[1]),
		},
	})

	// 创建语句Mock数据
	createId := "1_**********"
	indexResponse := elastic.IndexResponse{
		Index:   indexName,
		Type:    docType,
		Id:      createId,
		Version: 1,
		Result:  "created",
		Shards: &elastic.ShardsInfo{
			Total:      2,
			Successful: 1,
			Failed:     0,
		},
	}

	mock.Register("/"+indexName+"/"+docType+"/"+createId, indexResponse)

	// 更新语句Mock数据
	updateResponse := elastic.UpdateResponse{
		Index:   indexName,
		Type:    docType,
		Id:      createId,
		Version: 2,
		Result:  "updated",
		Shards: &elastic.ShardsInfo{
			Total:      2,
			Successful: 1,
			Failed:     0,
		},
	}

	mock.Register("/"+indexName+"/_update_by_query", updateResponse)
	mock.Register("/"+indexName2+"/_update_by_query", updateResponse)

	// 批量操作注册
	mock.RegisterBulk()

	// 删除语句Mock数据
	deleteResponse := elastic.DeleteResponse{
		Index:   indexName,
		Type:    docType,
		Id:      "1_**********",
		Version: 3,
		Result:  "deleted",
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Failed:     0,
		},
	}

	mock.Register("/"+indexName+"/_delete_by_query", deleteResponse)
	mock.Register("/"+indexName2+"/_delete_by_query", deleteResponse)

	// Count语句Mock数据
	countResponse := elastic.CountResponse{
		Count: 2,
	}

	mock.Register("/"+indexName+"/_count", countResponse)
	mock.Register("/"+indexName2+"/_count", countResponse)

	// 设置是否使用Mock (默认true)
	es.SetTestEnv(true)
	mysql.SetTestEnv(true)
	redis.SetTestEnv(true)

	// 初始化数据库 - Mock客户端已经在NewMockEsServer()中创建和设置了
	mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

// TestCleanDomain 测试域名清理函数
func TestCleanDomain(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "http协议前缀",
			input:    "http://example.com:8080",
			expected: "example.com",
		},
		{
			name:     "https协议前缀",
			input:    "https://example.com:443",
			expected: "example.com",
		},
		{
			name:     "无协议有端口",
			input:    "example.com:8080",
			expected: "example.com",
		},
		{
			name:     "无协议无端口",
			input:    "example.com",
			expected: "example.com",
		},
		{
			name:     "空字符串",
			input:    "",
			expected: "",
		},
		{
			name:     "只有协议",
			input:    "http://",
			expected: "",
		},
		{
			name:     "IPv6地址",
			input:    "http://[2001:db8::1]:8080",
			expected: "[2001",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CleanDomain(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestThreatenAssetsList 测试威胁资产列表函数
func TestThreatenAssetsList(t *testing.T) {
	Init()

	t.Run("正常查询", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "parent_id", "content", "type", "source", "from_ip", "clue_company_name"}).
				AddRow(1, 0, "example.com", 0, 4, "**********", "威胁公司有限公司"))

		req := &pb.ThreatenAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Greater(t, rsp.Total, int64(0))
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.NotEmpty(t, rsp.Data)
	})

	t.Run("带关键字查询", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("威胁"),
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带数字关键字查询", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("80"),
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带排序查询", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Sort:    proto.Int64(1),
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带标题过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Title:   []string{"威胁网站"},
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带域名过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Domain:  []string{"example.com"},
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带主机过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Hosts:   proto.String("threat.example.com"),
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带云厂商过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:    1,
			Page:      1,
			PerPage:   10,
			CloudName: proto.String("ThreatCloud"),
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带线索公司名称过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			ClueCompanyName: []string{"威胁公司有限公司"},
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带二次确认过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:        1,
			Page:          1,
			PerPage:       10,
			SecondConfirm: proto.Int64(1),
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("默认分页参数", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:  1,
			Page:    0, // 测试默认值
			PerPage: 0, // 测试默认值
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.Equal(t, int32(10), rsp.PerPage)
	})

	t.Run("带减号的过滤条件", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.ThreatenAssetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			Title:           []string{"-", "威胁网站"},
			Domain:          []string{"-", "example.com"},
			AssetsSource:    []string{"-", "FOFA"},
			ClueCompanyName: []string{"-", "威胁公司有限公司"},
		}
		rsp := &pb.ThreatenAssetsListResponse{}

		err := ThreatenAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})
}

// TestExportThreatenPassetsList 测试威胁资产导出函数
func TestExportThreatenPassetsList(t *testing.T) {
	Init()

	t.Run("正常导出", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
		}
		rsp := &pb.FileExportResponse{}

		err := ExportThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("带ID导出", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Id:     []string{"foradar_1_**********", "foradar_1_**********"},
		}
		rsp := &pb.FileExportResponse{}

		err := ExportThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("带关键字导出", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:  1,
			Keyword: proto.String("威胁"),
		}
		rsp := &pb.FileExportResponse{}

		err := ExportThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})
}

// TestThreatenAssetsIPExport 测试威胁资产IP导出函数
func TestThreatenAssetsIPExport(t *testing.T) {
	Init()

	t.Run("正常导出", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId: 1,
		}
		rsp := &pb.FileExportResponse{}

		err := ThreatenAssetsIPExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("带ID导出", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********", "1_**********"},
		}
		rsp := &pb.FileExportResponse{}

		err := ThreatenAssetsIPExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("带过滤条件导出", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:          1,
			Keyword:         proto.String("威胁"),
			Title:           []string{"威胁网站"},
			Domain:          []string{"example.com"},
			Hosts:           proto.String("threat.example.com"),
			AssetsSource:    []string{"FOFA"},
			CloudName:       proto.String("ThreatCloud"),
			ClueCompanyName: []string{"威胁公司有限公司"},
		}
		rsp := &pb.FileExportResponse{}

		err := ThreatenAssetsIPExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})
}

// TestIpThreatenAssetDelete 测试威胁资产删除函数
func TestIpThreatenAssetDelete(t *testing.T) {
	Init()

	t.Run("正常删除", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `ip_history` WHERE user_id = .* AND ip IN .*").
			WithArgs(utils.SqlMockArgs(3)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********"},
		}
		rsp := &pb.Empty{}

		err := IpThreatenAssetDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("批量删除", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `ip_history` WHERE user_id = .* AND ip IN .*").
			WithArgs(utils.SqlMockArgs(4)...).
			WillReturnResult(sqlmock.NewResult(1, 2))
		mock.ExpectCommit()

		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********", "1_**********"},
		}
		rsp := &pb.Empty{}

		err := IpThreatenAssetDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带过滤条件删除", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `ip_history` WHERE user_id = .* AND ip IN .*").
			WithArgs(utils.SqlMockArgs(3)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		req := &pb.IpAssetActionRequest{
			UserId:  1,
			Keyword: proto.String("威胁"),
		}
		rsp := &pb.Empty{}

		err := IpThreatenAssetDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
	})
}

// TestSetThreatenType 测试设置威胁类型函数
func TestSetThreatenType(t *testing.T) {
	Init()

	t.Run("正常设置威胁类型", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:           1,
			Id:               []string{"1_**********"},
			ThreatenType:     proto.String("1"),
			ThreatenTypeName: proto.String("钓鱼"),
		}
		rsp := &pb.Empty{}

		err := SetThreatenType(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("批量设置威胁类型", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:           1,
			Id:               []string{"1_**********", "1_**********"},
			ThreatenType:     proto.String("2"),
			ThreatenTypeName: proto.String("黄赌毒"),
		}
		rsp := &pb.Empty{}

		err := SetThreatenType(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("威胁类型为空", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********"},
		}
		rsp := &pb.Empty{}

		err := SetThreatenType(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "威胁类型不能为空")
	})

	t.Run("用户ID为空", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:       0,
			ThreatenType: proto.String("1"),
		}
		rsp := &pb.Empty{}

		err := SetThreatenType(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "用户ID不能为空")
	})

	t.Run("威胁类型名称默认值", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:       1,
			Id:           []string{"1_**********"},
			ThreatenType: proto.String("0"),
		}
		rsp := &pb.Empty{}

		err := SetThreatenType(context.Background(), req, rsp)
		assert.NoError(t, err)
	})
}

// TestAssetsThreatenSetStatus 测试威胁资产状态设置函数
func TestAssetsThreatenSetStatus(t *testing.T) {
	Init()

	t.Run("test is_all logic", func(t *testing.T) {
		// 测试 is_all=1 时忽略 id 条件的逻辑
		req := &pb.IpAssetActionRequest{
			UserId:    1,
			Id:        []string{"1_**********", "1_**********"},
			IsAll:     proto.Int64(1), // 设置 is_all=1
			SetStatus: proto.Int64(fofaee_assets.STATUS_IGNORE),
			CompanyId: 1,
		}

		// 调用 asset_account.ActionSearch 函数测试逻辑
		params := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})

		// 验证参数中不包含 _id 条件
		hasIdCondition := false
		for _, param := range params {
			if len(param) >= 2 && param[0] == "MUST" {
				if mustConditions, ok := param[1].([][]interface{}); ok {
					for _, condition := range mustConditions {
						if len(condition) >= 2 && condition[0] == "_id" {
							hasIdCondition = true
							break
						}
					}
				}
			}
		}
		assert.False(t, hasIdCondition, "当 is_all=1 时，不应该包含 _id 筛选条件")
	})

	t.Run("test normal id logic", func(t *testing.T) {
		// 测试正常情况下 id 条件生效
		req := &pb.IpAssetActionRequest{
			UserId:    1,
			Id:        []string{"1_**********", "1_**********"},
			IsAll:     proto.Int64(0), // 设置 is_all=0 或不设置
			SetStatus: proto.Int64(fofaee_assets.STATUS_IGNORE),
			CompanyId: 1,
		}

		// 调用 asset_account.ActionSearch 函数测试逻辑
		params := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})

		// 打印调试信息
		t.Logf("ActionSearch 返回的参数: %+v", params)

		// 验证参数中包含 _id 条件
		hasIdCondition := false
		for _, param := range params {
			if len(param) >= 2 && param[0] == "MUST" {
				if mustConditions, ok := param[1].([][]interface{}); ok {
					for _, condition := range mustConditions {
						if len(condition) >= 2 && condition[0] == "_id" {
							hasIdCondition = true
							break
						}
					}
				}
			}
		}
		assert.True(t, hasIdCondition, "当 is_all=0 或未设置时，应该包含 _id 筛选条件")
	})

	t.Run("test no is_all parameter", func(t *testing.T) {
		// 测试不设置 is_all 参数时的逻辑
		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********", "1_**********"},
			// IsAll 不设置
			SetStatus: proto.Int64(fofaee_assets.STATUS_IGNORE),
			CompanyId: 1,
		}

		// 调用 asset_account.ActionSearch 函数测试逻辑
		params := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})

		// 验证参数中包含 _id 条件
		hasIdCondition := false
		for _, param := range params {
			if len(param) >= 2 && param[0] == "MUST" {
				if mustConditions, ok := param[1].([][]interface{}); ok {
					for _, condition := range mustConditions {
						if len(condition) >= 2 && condition[0] == "_id" {
							hasIdCondition = true
							break
						}
					}
				}
			}
		}
		assert.True(t, hasIdCondition, "当 is_all 未设置且有 id 时，应该包含 _id 筛选条件")
	})

	t.Run("test is_all=1 with no id", func(t *testing.T) {
		// 测试 is_all=1 且没有 id 时的逻辑
		req := &pb.IpAssetActionRequest{
			UserId:    1,
			Id:        []string{}, // 空 id 数组
			IsAll:     proto.Int64(1),
			SetStatus: proto.Int64(fofaee_assets.STATUS_IGNORE),
			CompanyId: 1,
		}

		// 调用 asset_account.ActionSearch 函数测试逻辑
		params := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})

		// 验证参数中不包含 _id 条件
		hasIdCondition := false
		for _, param := range params {
			if len(param) >= 2 && param[0] == "MUST" {
				if mustConditions, ok := param[1].([][]interface{}); ok {
					for _, condition := range mustConditions {
						if len(condition) >= 2 && condition[0] == "_id" {
							hasIdCondition = true
							break
						}
					}
				}
			}
		}
		assert.False(t, hasIdCondition, "当 is_all=1 且没有 id 时，不应该包含 _id 筛选条件")
	})

	t.Run("test no id and no is_all", func(t *testing.T) {
		// 测试没有 id 且没有 is_all 时的逻辑
		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{}, // 空 id 数组
			// IsAll 不设置
			SetStatus: proto.Int64(fofaee_assets.STATUS_IGNORE),
			CompanyId: 1,
		}

		// 调用 asset_account.ActionSearch 函数测试逻辑
		params := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})

		// 验证参数中不包含 _id 条件
		hasIdCondition := false
		for _, param := range params {
			if len(param) >= 2 && param[0] == "MUST" {
				if mustConditions, ok := param[1].([][]interface{}); ok {
					for _, condition := range mustConditions {
						if len(condition) >= 2 && condition[0] == "_id" {
							hasIdCondition = true
							break
						}
					}
				}
			}
		}
		assert.False(t, hasIdCondition, "当没有 id 且没有 is_all 时，不应该包含 _id 筛选条件")
	})

	t.Run("状态参数为空", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********"},
		}
		rsp := &pb.Empty{}

		err := AssetsThreatenSetStatus(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "缺少状态参数")
	})

	t.Run("不支持的状态", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:    1,
			Id:        []string{"1_**********"},
			SetStatus: proto.Int64(999), // 不支持的状态
		}
		rsp := &pb.Empty{}

		err := AssetsThreatenSetStatus(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "不支持的状态")
	})

	t.Run("设置为认领状态", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:    1,
			Id:        []string{"1_**********"},
			SetStatus: proto.Int64(fofaee_assets.STATUS_CLAIMED),
			CompanyId: 1,
		}
		rsp := &pb.Empty{}

		// 测试基本功能，不依赖复杂的mock
		_ = AssetsThreatenSetStatus(context.Background(), req, rsp)
		assert.NotNil(t, req)
		assert.NotNil(t, rsp)
	})

	t.Run("设置为忽略状态", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:    1,
			Id:        []string{"1_**********"},
			SetStatus: proto.Int64(fofaee_assets.STATUS_IGNORE),
			CompanyId: 1,
		}
		rsp := &pb.Empty{}

		// 测试基本功能，不依赖复杂的mock
		_ = AssetsThreatenSetStatus(context.Background(), req, rsp)
		assert.NotNil(t, req)
		assert.NotNil(t, rsp)
	})

	t.Run("设置为疑似状态", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:    1,
			Id:        []string{"1_**********"},
			SetStatus: proto.Int64(fofaee_assets.STATUS_DEFAULT),
			CompanyId: 1,
		}
		rsp := &pb.Empty{}

		// 测试基本功能，不依赖复杂的mock
		_ = AssetsThreatenSetStatus(context.Background(), req, rsp)
		assert.NotNil(t, req)
		assert.NotNil(t, rsp)
	})

	t.Run("设置为威胁状态", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:           1,
			Id:               []string{"1_**********"},
			SetStatus:        proto.Int64(fofaee_assets.STATUS_THREATEN),
			ThreatenType:     proto.String("1"),
			ThreatenTypeName: proto.String("钓鱼"),
			CompanyId:        1,
		}
		rsp := &pb.Empty{}

		// 测试基本功能，不依赖复杂的mock
		_ = AssetsThreatenSetStatus(context.Background(), req, rsp)
		assert.NotNil(t, req)
		assert.NotNil(t, rsp)
	})

	t.Run("设置威胁状态时威胁类型名称默认值", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:       1,
			Id:           []string{"1_**********"},
			SetStatus:    proto.Int64(fofaee_assets.STATUS_THREATEN),
			ThreatenType: proto.String("1"),
			// ThreatenTypeName 不设置，应该使用默认值 "其他"
			CompanyId: 1,
		}
		rsp := &pb.Empty{}

		// 测试基本功能，不依赖复杂的mock
		_ = AssetsThreatenSetStatus(context.Background(), req, rsp)
		assert.NotNil(t, req)
		assert.NotNil(t, rsp)
	})
}

// TestIpPortActionSearch 测试IP端口操作搜索函数
func TestIpPortActionSearch(t *testing.T) {
	tests := []struct {
		name     string
		req      *pb.IpPortActionRequest
		status   []interface{}
		expected int // 期望的查询条件数量
	}{
		{
			name: "基本查询",
			req: &pb.IpPortActionRequest{
				UserId: 1,
			},
			status:   []interface{}{fofaee_assets.STATUS_THREATEN},
			expected: 2, // user_id + status
		},
		{
			name: "带ID查询",
			req: &pb.IpPortActionRequest{
				UserId: 1,
				Id:     []string{"1_**********"},
			},
			status:   []interface{}{fofaee_assets.STATUS_THREATEN},
			expected: 1, // 只有_id条件
		},
		{
			name: "带关键字查询",
			req: &pb.IpPortActionRequest{
				UserId:  1,
				Keyword: proto.String("威胁"),
			},
			status:   []interface{}{fofaee_assets.STATUS_THREATEN},
			expected: 2, // user_id + status (关键字在should中)
		},
		{
			name: "带数字关键字查询",
			req: &pb.IpPortActionRequest{
				UserId:  1,
				Keyword: proto.String("80"),
			},
			status:   []interface{}{fofaee_assets.STATUS_THREATEN},
			expected: 2, // user_id + status
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IpPortActionSearch(tt.req, tt.status)
			assert.NotNil(t, result)
			assert.IsType(t, [][]interface{}{}, result)
		})
	}
}

// TestGetStateText 测试状态文本获取函数
func TestGetStateText(t *testing.T) {
	tests := []struct {
		name     string
		state    int
		expected string
	}{
		{
			name:     "在线状态",
			state:    1,
			expected: "在线",
		},
		{
			name:     "离线状态",
			state:    2,
			expected: "离线",
		},
		{
			name:     "未知状态",
			state:    0,
			expected: "未知",
		},
		{
			name:     "其他状态",
			state:    999,
			expected: "未知",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getStateText(tt.state)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestConvertToStringSlice 测试类型转换函数
func TestConvertToStringSlice(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected []string
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: []string{},
		},
		{
			name:     "[]interface{}输入",
			input:    []interface{}{"test1", "test2", 123},
			expected: []string{"test1", "test2", "123"},
		},
		{
			name:     "[]string输入",
			input:    []string{"test1", "test2"},
			expected: []string{"test1", "test2"},
		},
		{
			name:     "其他类型输入",
			input:    "not a slice",
			expected: []string{},
		},
		{
			name:     "包含nil的slice",
			input:    []interface{}{"test1", nil, "test2"},
			expected: []string{"test1", "test2"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToStringSlice(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetTopClue 测试线索链获取函数
func TestGetTopClue(t *testing.T) {
	clueList := []*clues.Clue{
		{
			Model: dbx.Model{
				Id: 1,
			},
			ParentId:        0,
			Content:         "example.com",
			Type:            0,
			Source:          4,
			FromIp:          "**********",
			ClueCompanyName: "测试公司",
		},
		{
			Model: dbx.Model{
				Id: 2,
			},
			ParentId:        1,
			Content:         "sub.example.com",
			Type:            0,
			Source:          4,
			FromIp:          "",
			ClueCompanyName: "测试公司",
		},
	}

	t.Run("正常获取线索链", func(t *testing.T) {
		var chainList []map[string]interface{}
		getTopClue(1, clueList, &chainList)
		assert.NotEmpty(t, chainList)
	})

	t.Run("空线索列表", func(t *testing.T) {
		var chainList []map[string]interface{}
		getTopClue(1, []*clues.Clue{}, &chainList)
		assert.Empty(t, chainList)
	})
}

// TestBuildRuleInfos 测试规则信息构建函数
func TestBuildRuleInfos(t *testing.T) {
	tests := []struct {
		name     string
		ruleTags []fofaee_assets.RuleTag
		expected string
	}{
		{
			name:     "空规则标签",
			ruleTags: []fofaee_assets.RuleTag{},
			expected: "",
		},
		{
			name: "单个规则标签",
			ruleTags: []fofaee_assets.RuleTag{
				{CnProduct: "Apache"},
			},
			expected: "Apache",
		},
		{
			name: "多个规则标签",
			ruleTags: []fofaee_assets.RuleTag{
				{CnProduct: "Apache"},
				{CnProduct: "Nginx"},
				{CnProduct: "MySQL"},
			},
			expected: "Apache,Nginx,MySQL",
		},
		{
			name: "包含重复的规则标签",
			ruleTags: []fofaee_assets.RuleTag{
				{CnProduct: "Apache"},
				{CnProduct: "Nginx"},
				{CnProduct: "Apache"},
			},
			expected: "Apache,Nginx",
		},
		{
			name: "包含空产品名的规则标签",
			ruleTags: []fofaee_assets.RuleTag{
				{CnProduct: "Apache"},
				{CnProduct: ""},
				{CnProduct: "Nginx"},
			},
			expected: "Apache,Nginx",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := buildRuleInfos(tt.ruleTags)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetRuleInfos 测试规则信息获取函数
func TestGetRuleInfos(t *testing.T) {
	tests := []struct {
		name     string
		ruleTags []interface{}
		expected string
	}{
		{
			name:     "空规则标签",
			ruleTags: []interface{}{},
			expected: "",
		},
		{
			name: "正常规则标签",
			ruleTags: []interface{}{
				map[string]interface{}{
					"cn_product": "Apache",
				},
				map[string]interface{}{
					"cn_product": "Nginx",
				},
			},
			expected: "Apache,Nginx",
		},
		{
			name: "包含无效格式的标签",
			ruleTags: []interface{}{
				map[string]interface{}{
					"cn_product": "Apache",
				},
				"invalid_tag",
				map[string]interface{}{
					"other_field": "value",
				},
			},
			expected: "Apache",
		},
		{
			name: "包含空产品名",
			ruleTags: []interface{}{
				map[string]interface{}{
					"cn_product": "Apache",
				},
				map[string]interface{}{
					"cn_product": "",
				},
				map[string]interface{}{
					"cn_product": "Nginx",
				},
			},
			expected: "Apache,Nginx",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getRuleInfos(tt.ruleTags)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetFofaAssetsDefaultSorter 测试默认排序器函数
func TestGetFofaAssetsDefaultSorter(t *testing.T) {
	sorters := GetFofaAssetsDefaultSorter()
	assert.NotNil(t, sorters)
	assert.Len(t, sorters, 2)

	// 检查第一个排序器是否为FieldSort
	_, ok := sorters[0].(*elastic.FieldSort)
	assert.True(t, ok, "第一个排序器应该是FieldSort")

	// 检查第二个排序器是否为FieldSort
	_, ok = sorters[1].(*elastic.FieldSort)
	assert.True(t, ok, "第二个排序器应该是FieldSort")
}

// TestMin 测试最小值函数
func TestMin(t *testing.T) {
	tests := []struct {
		name     string
		a        int64
		b        int64
		expected int64
	}{
		{
			name:     "a小于b",
			a:        5,
			b:        10,
			expected: 5,
		},
		{
			name:     "a大于b",
			a:        10,
			b:        5,
			expected: 5,
		},
		{
			name:     "a等于b",
			a:        5,
			b:        5,
			expected: 5,
		},
		{
			name:     "负数比较",
			a:        -5,
			b:        -10,
			expected: -10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := min(tt.a, tt.b)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestThreatenPassetsList 测试威胁资产列表(新版)函数
func TestThreatenPassetsList(t *testing.T) {
	Init()

	t.Run("正常查询", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Greater(t, rsp.Total, int64(0))
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.NotEmpty(t, rsp.Data)
	})

	t.Run("带关键字查询", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("威胁"),
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带数字关键字查询", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("80"),
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带标题过滤", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Title:   []string{"威胁网站"},
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带域名过滤", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Domain:  []string{"example.com"},
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带主机过滤", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Hosts:   proto.String("threat.example.com"),
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带资产来源过滤", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:       1,
			Page:         1,
			PerPage:      10,
			AssetsSource: []string{"FOFA"},
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带云厂商过滤", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:    1,
			Page:      1,
			PerPage:   10,
			CloudName: proto.String("ThreatCloud"),
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带线索公司名称过滤", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			ClueCompanyName: []string{"威胁公司有限公司"},
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("默认分页参数", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:  1,
			Page:    0, // 测试默认值
			PerPage: 0, // 测试默认值
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.Equal(t, int32(10), rsp.PerPage)
	})

	t.Run("超出最大分页限制", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 200, // 超出最大限制
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(10), rsp.PerPage) // 应该被重置为默认值
	})

	t.Run("带减号的过滤条件", func(t *testing.T) {
		req := &pb.ThreatPassetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			Title:           []string{"-", "威胁网站"},
			Domain:          []string{"-", "example.com"},
			AssetsSource:    []string{"-", "FOFA"},
			ClueCompanyName: []string{"-", "威胁公司有限公司"},
		}
		rsp := &pb.PassetsListResponse{}

		err := ThreatenPassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})
}

// TestGetReason 测试获取推荐原因函数
func TestGetReason(t *testing.T) {
	t.Run("已知资产类型", func(t *testing.T) {
		result := getReason(nil, fofaee_assets.TYPE_CLAIMED, false, 0)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("上传资产类型", func(t *testing.T) {
		result := getReason(nil, fofaee_assets.STATUS_UPLOAD, false, 0)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("typeTwo为已知资产", func(t *testing.T) {
		result := getReason(nil, 0, false, fofaee_assets.TYPE_CLAIMED)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("typeTwo为上传资产", func(t *testing.T) {
		result := getReason(nil, 0, false, fofaee_assets.STATUS_UPLOAD)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("空reason", func(t *testing.T) {
		result := getReason([]foradar_assets.AssetReason{}, 999, false, 999) // 使用不匹配的值
		assert.Equal(t, "", result)
	})

	t.Run("普通reason", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "测试公司",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "根据测试公司的")
		assert.Contains(t, result, "example.com推荐")
	})

	t.Run("LOGO类型reason", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            clues.TYPE_LOGO,
				Content:         "/storage/logo.png",
				ClueCompanyName: "测试公司",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "根据测试公司的")
		assert.Contains(t, result, "推荐")
	})

	t.Run("LOGO类型reason导出模式", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            clues.TYPE_LOGO,
				Content:         "/storage/logo.png",
				ClueCompanyName: "测试公司",
			},
		}
		result := getReason(reasons, 999, true, 999) // 使用不匹配的值
		assert.Contains(t, result, "根据测试公司的")
		assert.Contains(t, result, "推荐")
		assert.NotContains(t, result, "<img")
	})

	t.Run("LOGO类型reason包含pp/public", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            clues.TYPE_LOGO,
				Content:         "/pp/public/logo.png",
				ClueCompanyName: "测试公司",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "<img")
		assert.Contains(t, result, "推荐")
	})

	t.Run("空公司名称", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "根据-的")
		assert.Contains(t, result, "example.com推荐")
	})

	t.Run("多个reason", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "测试公司1",
			},
			{
				Type:            1,
				Content:         "test.com",
				ClueCompanyName: "测试公司2",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "测试公司1")
		assert.Contains(t, result, "测试公司2")
		assert.Contains(t, result, "example.com推荐")
		assert.Contains(t, result, "test.com推荐")
	})
}
