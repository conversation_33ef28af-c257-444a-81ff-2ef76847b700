package threaten_assets

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql/clues"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"strconv"
	"strings"

	elastic_search "github.com/olivere/elastic"
	"github.com/pkg/errors"

	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

// ThreatenPassetsList 威胁资产列表(新版)
func ThreatenPassetsList(ctx context.Context, req *pb.ThreatPassetsListRequest, rsp *pb.PassetsListResponse) error {
	log.WithContextInfof(ctx, "Received thraten_assets.ThreatenPassetsList request: %v", req)

	// 详细打印请求参数
	reqJSON, _ := json.Marshal(req)
	log.WithContextInfof(ctx, "【详细调试】完整请求参数: %s", string(reqJSON))

	// 使用 elastic.StructToParams 构建查询条件
	var builder elastic.SearchBuilder
	elastic.StructToParams(req, &builder)

	// 添加默认查询条件
	builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.STATUS_THREATEN}})

	// 关键字
	if req.Keyword != nil {
		var keywordQuery [][]interface{}
		_, err := strconv.Atoi(*req.Keyword)
		if err == nil {
			keywordQuery = append(keywordQuery, []interface{}{"logo.hash", *req.Keyword})
			keywordQuery = append(keywordQuery, []interface{}{"port", *req.Keyword})
		}
		keywordQuery = append(keywordQuery, []interface{}{"subject_key", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"icp.no", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"title.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"url.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"protocol", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"domain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"url", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"subdomain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"custom_tags.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"ip.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		builder.AddShould(keywordQuery...)
	}
	// Title
	if len(req.Title) > 0 {
		if utils.ListContains(req.Title, "-") {
			names := utils.ListReplace(req.Title, "-", "")
			builder.AddShould([]interface{}{"title.keyword", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"title.keyword", "in", elastic.ToInterfaceArray(req.Title)})
		}
	}
	// Domain
	if len(req.Domain) > 0 {
		if utils.ListContains(req.Domain, "-") {
			names := utils.ListReplace(req.Domain, "-", "")
			builder.AddShould([]interface{}{"domain", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"domain", "in", elastic.ToInterfaceArray(req.Domain)})
		}
	}
	// Hosts
	if req.Hosts != nil {
		var hostsQuery [][]interface{}
		hostsQuery = append(hostsQuery, []interface{}{"domain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"subdomain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		builder.AddShould(hostsQuery...)
	}
	// Assets Source
	if len(req.AssetsSource) > 0 {
		if utils.ListContains(req.AssetsSource, "-") {
			names := utils.ListReplace(req.AssetsSource, "-", "")
			builder.AddShould([]interface{}{"assets_source", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"assets_source", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"assets_source", "in", elastic.ToInterfaceArray(req.AssetsSource)})
		}
	}
	// Cloud Name
	if req.CloudName != nil {
		var nameQuery [][]interface{}
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(*req.CloudName)})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToLower(*req.CloudName))})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToUpper(*req.CloudName))})
		builder.AddShould(nameQuery...)
	}
	// Clue Company Name
	if len(req.ClueCompanyName) > 0 {
		if utils.ListContains(req.ClueCompanyName, "-") {
			names := utils.ListReplace(req.ClueCompanyName, "-", "")
			builder.AddShould([]interface{}{"clue_company_name.keyword", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"clue_company_name", "EXISTS"}}})
		} else {
			builder.AddShould([]interface{}{"clue_company_name.keyword", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
			builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
		}
	}
	// 设置默认分页参数
	page := int(req.Page)
	if page < 1 {
		page = 1
	}
	perPage := int(req.PerPage)
	if perPage < 1 || perPage > 100 {
		perPage = 10
	}

	// 构建查询
	query := builder.Build()

	// 添加ES查询语句输出调试
	queryParams := elastic.ParseQuery(query)
	queryJSON, _ := json.MarshalIndent(map[string]interface{}{
		"index": "foradar_assets",
		"body": map[string]interface{}{
			"_source": true,
			"query":   queryParams,
			"sort":    []map[string]map[string]string{{"ip": {"order": "asc"}}},
			"size":    perPage,
		},
		"track_total_hits": true,
	}, "", "  ")
	log.WithContextInfof(ctx, "【ES查询JSON】: %s", string(queryJSON))

	// 打印ES查询语句
	log.WithContextInfof(ctx, "【ES查询调试】查询条件: %v", query)

	// 设置排序
	sorts := []elastic_search.Sorter{
		elastic_search.NewFieldSort("ip").Asc(),
	}

	// 执行查询
	total, list, err := elastic.ListByParams[foradar_assets.ForadarAsset](page, perPage, query, sorts)
	if err != nil {
		log.WithContextErrorf(ctx, "查询威胁资产列表失败: %v", err)
		return errors.Wrap(err, "查询威胁资产列表失败")
	}
	//我需要把结构体返回的reason转为字符串
	for _, item := range list {
		// 检查reason是否为空，避免空指针或空切片问题
		var reasonStr string
		if len(item.Reason) > 0 {
			reasonStr = getReason(item.Reason, item.Type, false, 3)
		} else {
			reasonStr = getReason(nil, item.Type, false, 3)
		}
		item.ReasonString = reasonStr
	}
	// 转换结果为JSON
	data, err := json.Marshal(list)
	if err != nil {
		log.WithContextErrorf(ctx, "转换威胁资产列表结果失败: %v", err)
		return errors.Wrap(err, "转换威胁资产列表结果失败")
	}

	// 设置响应
	rsp.Total = total
	rsp.CurrentPage = int32(page)
	rsp.PerPage = int32(perPage)
	rsp.LastPage = int32(total / int64(perPage))
	if total%int64(perPage) > 0 {
		rsp.LastPage++
	}
	rsp.From = int32((page-1)*perPage + 1)
	rsp.To = int32(min(int64(page*perPage), total))
	rsp.Data = string(data)

	return nil
}

// getReason 根据PHP逻辑获取推荐原因字符串
// 对应PHP的getReason函数
func getReason(reason []foradar_assets.AssetReason, assetType int, export bool, typeTwo int) string {
	// 对应PHP: if (\App\Models\Elastic\IpAssets::TYPE_CLAIMED === $type || \App\Models\Elastic\IpAssets::STATUS_UPLOAD === $type)
	if assetType == fofaee_assets.TYPE_CLAIMED || assetType == fofaee_assets.STATUS_UPLOAD {
		return "已知资产ip"
	}

	// 对应PHP: if (\App\Models\Elastic\IpAssets::TYPE_CLAIMED === $type_two || \App\Models\Elastic\IpAssets::STATUS_UPLOAD === $type_two)
	if typeTwo == fofaee_assets.TYPE_CLAIMED || typeTwo == fofaee_assets.STATUS_UPLOAD {
		return "已知资产ip"
	}

	if len(reason) == 0 {
		return ""
	}

	var info strings.Builder

	// 对应PHP的foreach ($reason as $val)
	for _, val := range reason {
		clueCompanyName := val.ClueCompanyName
		if clueCompanyName == "" {
			clueCompanyName = "-"
		}

		// 对应PHP: if (($val['type'] ?? '') == \App\Models\MySql\Clue::TYPE_LOGO)
		if val.Type == clues.TYPE_LOGO {
			var icoRecTxt string
			if export {
				// 对应PHP的导出模式
				icoRecTxt = storage.GenDownloadUrl(val.Content, "") + " 推荐;"
			} else {
				// 对应PHP的HTML模式
				// 检查content是否包含"pp/public"
				if strings.Contains(val.Content, "pp/public") {
					icoRecTxt = fmt.Sprintf(`<img class="reason_ico" src="%s" />推荐;`, storage.GenDownloadUrl(val.Content, ""))
				} else {
					icoRecTxt = fmt.Sprintf(`<img class="reason_ico" src="%s" />推荐;`, val.Content)
				}
			}
			// 对应PHP: $info .= '根据' . $clue_company_name . '的' . typeToCn($val['type'] ?? '') .' '. $icoRecTxt;
			info.WriteString(fmt.Sprintf("根据%s的%s %s", clueCompanyName, utils.TypeToCn(val.Type), icoRecTxt))
		} else {
			// 对应PHP: $info .= '根据' . $clue_company_name . '的' . typeToCn($val['type'] ?? '') .' '. $val['content'] . '推荐;';
			info.WriteString(fmt.Sprintf("根据%s的%s %s推荐;", clueCompanyName, utils.TypeToCn(val.Type), val.Content))
		}
	}

	return info.String()
}
