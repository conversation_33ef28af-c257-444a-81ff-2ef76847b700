package threaten_assets

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/cfg"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/webService/handler/asset_account"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	elastic_search "github.com/olivere/elastic"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"

	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/ip_history"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

// CleanDomain 清理域名，去除协议前缀和端口号
func CleanDomain(domain string) string {
	// 去掉协议前缀
	if strings.HasPrefix(domain, "http://") {
		domain = domain[7:]
	} else if strings.HasPrefix(domain, "https://") {
		domain = domain[8:]
	}

	// 去掉端口号
	if idx := strings.Index(domain, ":"); idx > 0 {
		domain = domain[:idx]
	}

	return domain
}

// ThreatenAssetsList 获取威胁资产列表
func ThreatenAssetsList(ctx context.Context, req *pb.ThreatenAssetsListRequest, rsp *pb.ThreatenAssetsListResponse) error {
	log.WithContextInfof(ctx, "[威胁资产列表] 请求参数: %+v", req)

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PerPage <= 0 {
		req.PerPage = 10
	}

	// 设置排序
	var sorts []elastic_search.Sorter
	if req.Sort != nil && *req.Sort != 0 {
		sorts = append(sorts, elastic_search.NewFieldSort("updated_at").Desc())
	} else {
		sorts = append(sorts, elastic_search.NewFieldSort("ip").Asc())
	}

	// 使用 elastic.StructToParams 构建查询条件
	var builder elastic.SearchBuilder
	elastic.StructToParams(req, &builder)
	// 关键字
	if req.Keyword != nil {
		var keywordQuery [][]interface{}
		_, err := strconv.Atoi(*req.Keyword)
		if err == nil {
			keywordQuery = append(keywordQuery, []interface{}{"port_list.logo.hash", *req.Keyword})
			keywordQuery = append(keywordQuery, []interface{}{"port", *req.Keyword})
		}
		keywordQuery = append(keywordQuery, []interface{}{"port_list.cert.subject_key", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.icp.no", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.title.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.url.raw", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.protocol", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.domain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.url", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.subdomain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.url.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"custom_tags.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"ip.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		builder.AddShould(keywordQuery...)
	}
	// Title
	if len(req.Title) > 0 {
		if utils.ListContains(req.Title, "-") {
			names := utils.ListReplace(req.Title, "-", "")
			builder.AddShould([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(req.Title)})
		}
	}
	// Domain
	if len(req.Domain) > 0 {
		if utils.ListContains(req.Domain, "-") {
			names := utils.ListReplace(req.Domain, "-", "")
			if len(names) == 1 {
				// all_domain不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
				// 方法2: 字段为null (在ES中null值通常不被索引，但为了完整性添加)
				builder.AddShould([]interface{}{"all_domain", "=", nil})
			} else {
				builder.AddShould([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(names)})
				// all_domain不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
				// 方法2: 字段为null
				builder.AddShould([]interface{}{"all_domain", "=", nil})
			}
		} else {
			builder.AddMust([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(req.Domain)})
		}
	}
	// Hosts
	if req.Hosts != nil {
		var hostsQuery [][]interface{}
		hostsQuery = append(hostsQuery, []interface{}{"port_list.domain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"port_list.subdomain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
		builder.AddShould(hostsQuery...)
	}
	// Assets Source
	if len(req.AssetsSource) > 0 {
		if utils.ListContains(req.AssetsSource, "-") {
			names := utils.ListReplace(req.AssetsSource, "-", "")
			builder.AddShould([]interface{}{"host_list.assets_source", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"host_list.assets_source", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"host_list.assets_source", "in", elastic.ToInterfaceArray(req.AssetsSource)})
		}
	}
	// Cloud Name
	if req.CloudName != nil {
		var nameQuery [][]interface{}
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(*req.CloudName)})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToLower(*req.CloudName))})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToUpper(*req.CloudName))})
		builder.AddShould(nameQuery...)
	}
	// Clue Company Name
	if len(req.ClueCompanyName) > 0 {
		if utils.ListContains(req.ClueCompanyName, "-") {
			names := utils.ListReplace(req.ClueCompanyName, "-", "")
			builder.AddShould([]interface{}{"clue_company_name.keyword", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"clue_company_name", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
		}
	}
	// 二次确认
	if req.SecondConfirm != nil {
		if *req.SecondConfirm == 1 {
			builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.StatusUploadAsset, fofaee_assets.StatusConfirmAsset}})
			builder.AddMust([]interface{}{"second_confirm", 1})
			builder.AddShould([]interface{}{"type", "in", []interface{}{foradar_assets.TypeClaimed, foradar_assets.TypeRecommend}}, []interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		} else {
			builder.AddMust([]interface{}{"type", fofaee_assets.TYPE_RECOMMEND})
			builder.AddMustNot([]interface{}{"second_confirm", 1}, []interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		}
	}
	builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.STATUS_THREATEN}})
	builder.AddMust([]interface{}{"user_id", req.UserId})
	// 构建查询
	query := builder.Build()
	log.WithContextInfof(ctx, "[威胁资产列表] 查询条件: %v", query)

	// 打印ES查询语句
	queryJson, _ := json.Marshal(query)
	log.WithContextInfof(ctx, "【ES查询调试】查询条件: %s", string(queryJson))

	sortJson, _ := json.Marshal(sorts)
	log.WithContextInfof(ctx, "【ES查询调试】排序条件: %s", string(sortJson))

	log.WithContextInfof(ctx, "【ES查询调试】请求页码: %d, 每页条数: %d, 索引: fofaee_assets", req.Page, req.PerPage)

	// 执行查询
	page := int(req.Page)
	if page < 1 {
		page = 1
	}
	perPage := int(req.PerPage)
	if perPage < 1 || perPage > 100 {
		perPage = 10
	}

	total, list, err := elastic.ListByParams[fofaee_assets.FofaeeAssets](page, perPage, query, sorts)
	if err != nil {
		log.WithContextErrorf(ctx, "ES查询失败: %v", err)
		return errors.Wrap(err, "ES查询失败")
	}

	// 处理结果
	lastPage := int32(total / int64(perPage))
	if total%int64(perPage) > 0 {
		lastPage++
	}

	rsp.Total = total
	rsp.PerPage = int32(perPage)
	rsp.CurrentPage = int32(page)
	rsp.LastPage = lastPage
	rsp.From = int32((page-1)*perPage + 1)
	rsp.To = int32(min(int64(page*perPage), total))

	// 处理查询结果
	items := make([]map[string]interface{}, 0)

	// 收集所有资产的group_id
	var groupIds []string
	for _, item := range list {
		log.WithContextInfof(ctx, "处理资产: ip=%v", item.Ip)

		// 新版数据结构
		if item.UpdatedAt > "2022-09-06 16:13:00" {
			// 处理ReasonArr，提取group_id
			for _, r := range item.ReasonArr {
				if reasonMap, ok := r.(map[string]interface{}); ok {
					if groupId, ok := reasonMap["group_id"].(float64); ok {
						strGroupId := fmt.Sprintf("%d", int64(groupId))
						log.WithContextInfof(ctx, "从reason_arr提取到group_id: %s (float64)", strGroupId)
						groupIds = append(groupIds, strGroupId)
					} else if groupId, ok := reasonMap["group_id"].(int64); ok {
						strGroupId := fmt.Sprintf("%d", groupId)
						log.WithContextInfof(ctx, "从reason_arr提取到group_id: %s (int64)", strGroupId)
						groupIds = append(groupIds, strGroupId)
					} else if groupId, ok := reasonMap["group_id"].(string); ok {
						log.WithContextInfof(ctx, "从reason_arr提取到group_id: %s (string)", groupId)
						groupIds = append(groupIds, groupId)
					} else if groupId, ok := reasonMap["group_id"].(json.Number); ok {
						strGroupId := groupId.String()
						log.WithContextInfof(ctx, "从reason_arr提取到group_id: %s (json.Number)", strGroupId)
						groupIds = append(groupIds, strGroupId)
					}
				} else {
					// 尝试JSON转换
					reasonBytes, err := json.Marshal(r)
					if err == nil {
						var reasonMap map[string]interface{}
						if err = json.Unmarshal(reasonBytes, &reasonMap); err == nil {
							if groupId, ok := reasonMap["group_id"].(float64); ok {
								strGroupId := fmt.Sprintf("%d", int64(groupId))
								log.WithContextInfof(ctx, "通过JSON转换提取到group_id: %s (float64)", strGroupId)
								groupIds = append(groupIds, strGroupId)
							} else if groupId, ok := reasonMap["group_id"].(int64); ok {
								strGroupId := fmt.Sprintf("%d", groupId)
								log.WithContextInfof(ctx, "通过JSON转换提取到group_id: %s (int64)", strGroupId)
								groupIds = append(groupIds, strGroupId)
							} else if groupId, ok := reasonMap["group_id"].(string); ok {
								log.WithContextInfof(ctx, "通过JSON转换提取到group_id: %s (string)", groupId)
								groupIds = append(groupIds, groupId)
							}
						}
					}
				}
			}
		} else {
			// 旧版数据结构，从port_list.reason中获取group_id
			log.WithContextInfof(ctx, "从旧版数据结构中提取group_id，port_list长度: %d", len(item.PortList))
			for i, port := range item.PortList {
				if port.Reason != nil {
					log.WithContextInfof(ctx, "port_list[%d]中的reason长度: %d", i, len(port.Reason))
					for j, reason := range port.Reason {
						strGroupId := fmt.Sprintf("%d", reason.GroupId)
						if strGroupId != "0" {
							log.WithContextInfof(ctx, "从port_list[%d].reason[%d]提取到group_id: %s", i, j, strGroupId)
							groupIds = append(groupIds, strGroupId)
						}
					}
				}
			}
		}

		// 处理端口列表中的Logo
		for pk, pv := range item.PortList {
			if pv.Logo.Content != "" {
				item.PortList[pk].Logo.Content = storage.GenAPIDownloadPath("", pv.Logo.Content, ".ico")
			}
		}

		// 处理主机列表中的Logo
		for hk, hv := range item.HostList {
			if hv.Logo.Content != "" {
				item.HostList[hk].Logo.Content = storage.GenAPIDownloadPath("", hv.Logo.Content, ".ico")
			}
		}

		// 构建详情
		detail := item.PortList

		// 将ClueCompanyName转换为字符串切片
		clueCompanyNames := convertToStringSlice(item.ClueCompanyName)

		// 构建返回数据
		data := map[string]interface{}{
			"detail":                     detail,
			"port_list":                  item.PortList,
			"ip":                         item.Ip,
			"param_ip":                   item.Ip,
			"company_tag_name":           utils.FilterSliceEmpty(clueCompanyNames),
			"clue_company_name":          utils.FilterSliceEmpty(clueCompanyNames),
			"create_time":                item.CreatedAt,
			"last_update_time":           item.UpdatedAt,
			"type":                       item.Type,
			"id":                         item.Id,
			"_id":                        item.Id,
			"latitude":                   item.Geo.Lat,
			"longitude":                  item.Geo.Lon,
			"isp":                        item.Geo.Isp,
			"province":                   item.Geo.Province,
			"asn":                        item.Geo.Asn,
			"state":                      getStateText(item.OnlineState),
			"reason_arr":                 item.ReasonArr,
			"tags":                       item.Tags,
			"customer_tags":              item.CustomerTags,
			"rule_tags":                  item.RuleTags,
			"ip_match":                   item.IpMatch,
			"port_size":                  len(item.PortList),
			"org_detect_assets_tasks_id": item.OrgDetectAssetsTasksId,
			"created_at":                 item.CreatedAt,
			"updated_at":                 item.UpdatedAt,
			"task_id":                    item.TaskId,
			"is_ipv6":                    item.IsIpv6,
			"geo":                        item.Geo,
			"is_shadow":                  item.IsShadow,
			"level":                      item.Level,
			"hosts":                      item.Hosts,
			"cloud_name":                 item.CloudName,
			"host_list":                  item.HostList,
			"detect_assets_tasks_id":     item.DetectAssetsTasksId,
			"online_state":               item.OnlineState,
			"user_id":                    item.UserId,
			"is_cdn":                     item.IsCdn,
			"website_message_id":         item.WebsiteMessageId,
			"organization_id":            item.OrganizationId,
			"reliability_score":          item.ReliabilityScore,
			"threaten_type":              item.ThreatenType,
			"status":                     item.Status,
			"threaten_type_name":         item.ThreatenTypeName,
			"port_list_total_page":       0,  // 默认值，可根据实际情况调整
			"port_list_per_page_num":     30, // 默认值，可根据实际情况调整
			"rule_infos":                 buildRuleInfos(item.RuleTags),
			"host_reflect":               []interface{}{}, // 默认空数组
			"chain_list":                 []interface{}{}, // 默认空数组，后面会被更新
		}

		// 处理云厂商
		if len(item.CloudName) > 0 {
			cloudNames := convertToStringSlice(item.CloudName)
			data["cloud_name"] = strings.Join(utils.FilterSliceEmpty(cloudNames), ",")
		} else {
			data["cloud_name"] = ""
		}

		// 处理域名和子域名
		var domainAndSubDomain []string
		if item.UpdatedAt > "2022-10-25 00:13:00" {
			// 新版数据结构
			for _, host := range item.HostList {
				if host.Domain != "" {
					domainAndSubDomain = append(domainAndSubDomain, host.Domain)
				}
				if host.Subdomain != "" {
					domainAndSubDomain = append(domainAndSubDomain, host.Subdomain)
				}
			}
			for _, port := range item.PortList {
				if port.Domain != "" {
					domainAndSubDomain = append(domainAndSubDomain, port.Domain)
				}
				if port.Subdomain != "" {
					domainAndSubDomain = append(domainAndSubDomain, port.Subdomain)
				}
			}
		} else {
			// 旧版数据结构
			for _, port := range detail {
				if port.Domain != "" {
					domainAndSubDomain = append(domainAndSubDomain, port.Domain)
				}
				if port.Subdomain != "" {
					domainAndSubDomain = append(domainAndSubDomain, port.Subdomain)
				}
			}
		}
		data["hosts"] = utils.FilterSliceEmpty(domainAndSubDomain)

		// 添加到结果列表
		items = append(items, data)
	}

	// 去重
	groupIds = utils.FilterSliceEmpty(groupIds)
	log.WithContextInfof(ctx, "收集到的去重后group_ids: %v, 数量: %d", groupIds, len(groupIds))

	// 处理证据链
	if len(groupIds) > 0 {
		// 将string类型的groupIds转换为uint64类型
		var groupIdsUint64 []uint64
		for _, gid := range groupIds {
			if id, err := strconv.ParseUint(gid, 10, 64); err == nil {
				groupIdsUint64 = append(groupIdsUint64, id)
			}
		}

		// 如果没有有效的group_id，则跳过线索查询
		if len(groupIdsUint64) == 0 {
			log.WithContextInfof(ctx, "没有有效的group_id，跳过线索查询")
			return nil
		}
		// 查询线索表
		clueList, err := clues.NewCluer().ListAll(func(db *gorm.DB) {
			db.Where("user_id = ?", req.UserId)
		})
		if err != nil {
			log.WithContextErrorf(ctx, "查询线索失败: %v", err)
		} else {
			log.WithContextInfof(ctx, "查询线索成功，查询到线索数量=%d", len(clueList))
			// 处理每个资产项的证据链
			for i, item := range items {
				// 获取reason_arr
				var reasonArr []interface{}
				if updatedAt, ok := item["last_update_time"].(string); ok && updatedAt > "2022-09-06 16:13:00" {
					// 新版数据结构，直接使用reason_arr
					if arr, ok := item["reason_arr"].([]interface{}); ok {
						reasonArr = arr
						log.WithContextInfof(ctx, "使用新版数据结构的reason_arr，长度: %d", len(reasonArr))
					}
				} else {
					// 旧版数据结构，从detail.reason中获取
					if detail, ok := item["detail"].([]interface{}); ok {
						for _, port := range detail {
							if portMap, ok := port.(map[string]interface{}); ok {
								if reason, ok := portMap["reason"].([]interface{}); ok {
									reasonArr = append(reasonArr, reason...)
									log.WithContextInfof(ctx, "从detail.reason中获取到%d个reason", len(reason))
								}
							}
						}
					}
				}

				// 去重reason_arr
				var uniqueReasonArr []interface{}
				reasonMap := make(map[string]bool)
				for _, r := range reasonArr {
					if reasonBytes, err := json.Marshal(r); err == nil {
						key := string(reasonBytes)
						if !reasonMap[key] {
							uniqueReasonArr = append(uniqueReasonArr, r)
							reasonMap[key] = true
						}
					}
				}
				reasonArr = uniqueReasonArr
				// 为每个reason构建线索链
				var chainLists [][]map[string]interface{}
				for _, r := range reasonArr {
					if reasonMap, ok := r.(map[string]interface{}); ok {
						// 获取reason的id
						var reasonID int64
						if id, ok := reasonMap["id"].(float64); ok {
							reasonID = int64(id)
						} else if id, ok := reasonMap["id"].(int64); ok {
							reasonID = id
						} else if id, ok := reasonMap["id"].(string); ok {
							if idVal, err := strconv.ParseInt(id, 10, 64); err == nil {
								reasonID = idVal
							}
						}

						if reasonID > 0 {
							// 构建线索链
							var chainList []map[string]interface{}
							getTopClue(reasonID, clueList, &chainList)

							if len(chainList) > 0 {
								// 添加当前IP到证据链末尾
								ipStr := fmt.Sprintf("%v", item["ip"])
								chainList = append(chainList, map[string]interface{}{
									"content": ipStr,
								})
								log.WithContextInfof(ctx, "使用reason_id=%d构建的证据链长度: %d", reasonID, len(chainList))

								// 添加到结果中
								chainLists = append(chainLists, chainList)
							}
						}
					}
				}

				// 将线索链添加到结果中
				if len(chainLists) > 0 {
					item["chain_list"] = chainLists
					log.WithContextInfof(ctx, "为资产项[%d]添加了%d条证据链", i, len(chainLists))
				} else {
					log.WithContextInfof(ctx, "资产项[%d]没有构建出证据链", i)
				}
			}
		}
	}

	// 序列化结果
	listBytes, err := json.Marshal(items)
	if err != nil {
		log.WithContextErrorf(ctx, "[威胁资产列表] 序列化结果失败: %v", err)
		return errors.Wrap(err, "序列化威胁资产列表失败")
	}

	rsp.Data = string(listBytes)

	return nil
}

// ExportThreatenPassetsList 威胁资产列表导出
func ExportThreatenPassetsList(ctx context.Context, req *pb.IpPortActionRequest, rsp *pb.FileExportResponse) error {
	log.Infof("[IP资产] 导出请求: %v", req)
	query := IpPortActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})
	log.Infof("[IP资产] 查询条件参数: %s", utils.AnyToStr(query))
	q := elastic.ParseQuery(query)
	list, err := elastic.All[foradar_assets.ForadarAsset](500, q, nil)
	if err != nil {
		log.Errorf("[IP资产] 查询失败: %v, req: %v", err, req)
		return err
	}
	// 生成Excel文件
	file := excelize.NewFile()
	sheet := "IP资产"
	err = file.SetSheetName("Sheet1", sheet)
	if err != nil {
		return err
	}

	// 设置表头 - 参考PHP逻辑
	headers := []string{"IP地址", "企业名称", "端口", "协议", "域名", "URL", "网站标题", "组件信息", "状态码", "云厂商", "运营商", "地理位置", "ASN", "经度", "纬度", "资产状态", "探测方式", "发现时间", "更新时间"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err = file.SetCellValue(sheet, cell, header)
		if err != nil {
			return err
		}
	}

	// 写入数据 - 参考PHP逻辑
	row := 2
	for _, r := range list {
		// 处理企业名称 - 支持数组和字符串两种格式
		companyName := ""
		if r.ClueCompanyName != nil {
			// 先尝试作为字符串处理
			if nameStr, ok := r.ClueCompanyName.(string); ok && nameStr != "" {
				companyName = nameStr
			} else if nameArray, ok := r.ClueCompanyName.([]interface{}); ok && len(nameArray) > 0 {
				// 如果是数组，取第一个元素
				if firstElement, ok := nameArray[0].(string); ok && firstElement != "" {
					companyName = firstElement
				}
			}
		}

		// 处理组件信息
		hardInfo := ""
		if len(r.RuleTags) > 0 {
			var components []string
			for _, rule := range r.RuleTags {
				if rule.CnProduct != "" {
					components = append(components, rule.CnProduct)
				}
			}
			hardInfo = strings.Join(components, ",")
		}

		// 处理云厂商名称
		cloudName := ""
		// ForadarAsset 可能没有 CloudName 字段，暂时留空

		// 处理资产状态
		onlineState := "离线"
		if cast.ToInt(r.OnlineState) == 1 {
			onlineState = "在线"
		}

		// 处理探测方式 - 从reason中获取
		var reasonStr string
		if len(r.Reason) > 0 {
			reasonStr = getReason(r.Reason, r.Type, true, 3)
		} else {
			reasonStr = getReason(nil, r.Type, true, 3)
		}

		// 处理网站标题 - 去除等号并截取前200个字符
		title := ""
		if r.Title != nil {
			if titleStr, ok := r.Title.(string); ok && titleStr != "" {
				title = strings.ReplaceAll(titleStr, "=", "")
				if len(title) > 200 {
					title = title[:200]
				}
			}
		}

		// 写入一行数据
		if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Ip); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), companyName); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), r.Port); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("D%d", row), r.Protocol); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("E%d", row), r.Subdomain); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("F%d", row), r.Url); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("G%d", row), title); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("H%d", row), hardInfo); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("I%d", row), r.HTTPStatusCode); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), cloudName); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), r.Geo.Isp); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), r.Geo.Province); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("M%d", row), r.Geo.Asn); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("N%d", row), r.Geo.Lat); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("O%d", row), r.Geo.Lon); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("P%d", row), onlineState); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("Q%d", row), reasonStr); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("R%d", row), r.CreatedAt); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("S%d", row), r.UpdatedAt); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}

		row++
	}

	// 保存文件
	filename := fmt.Sprintf("ip_passets_%s.xlsx", time.Now().Format("**************"))
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	fmt.Println(storagePath)
	if err := file.SaveAs(storagePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	fp, err := utils.DownloadFileEncrypt(accessPath, "IP端口资产数据"+path.Ext(filename), "", false)
	if err != nil {
		return fmt.Errorf("生成下载路径失败: %v", err)
	}
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(filename))

	// 设置返回的文件路径
	rsp.Url = apiFilePath
	return nil
}

// min 返回两个数中的较小值
func min(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}
func IpPortActionSearch(req *pb.IpPortActionRequest, status []interface{}) [][]interface{} {
	var builder elastic.SearchBuilder
	if len(req.Id) > 0 {
		builder.AddMust([]interface{}{"_id", "in", elastic.ToInterfaceArray(req.Id)})
	} else {
		// 通用字段查询构建
		elastic.StructToParams(req, &builder)
		// 特殊字段
		// 关键字
		if req.Keyword != nil {
			var keywordQuery [][]interface{}
			_, err := strconv.Atoi(*req.Keyword)
			if err == nil {
				keywordQuery = append(keywordQuery, []interface{}{"logo.hash", *req.Keyword})
				keywordQuery = append(keywordQuery, []interface{}{"port", *req.Keyword})
			}
			keywordQuery = append(keywordQuery, []interface{}{"subject_key", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"icp.no", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"title.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"url.raw", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"protocol", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"domain", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"url", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"subdomain", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"custom_tags.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"ip.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"clue_company_name.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			builder.AddShould(keywordQuery...)
		}
		// Title
		if len(req.Title) > 0 {
			if utils.ListContains(req.Title, "-") {
				names := utils.ListReplace(req.Title, "-", "")
				builder.AddShould([]interface{}{"title.keyword", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
			} else {
				builder.AddMust([]interface{}{"title.keyword", "in", elastic.ToInterfaceArray(req.Title)})
			}
		}
		// Domain
		if len(req.Domain) > 0 {
			if utils.ListContains(req.Domain, "-") {
				names := utils.ListReplace(req.Domain, "-", "")
				builder.AddShould([]interface{}{"domain.keyword", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
			} else {
				builder.AddMust([]interface{}{"domain.keyword", "in", elastic.ToInterfaceArray(req.Domain)})
			}
		}
		// Hosts
		if req.Hosts != nil {
			var hostsQuery [][]interface{}
			hostsQuery = append(hostsQuery, []interface{}{"domain", "like", elastic.WithRLAsterisk(*req.Hosts)})
			hostsQuery = append(hostsQuery, []interface{}{"subdomain", "like", elastic.WithRLAsterisk(*req.Hosts)})
			builder.AddShould(hostsQuery...)
		}
		// Assets Source
		if len(req.AssetsSource) > 0 {
			if utils.ListContains(req.AssetsSource, "-") {
				names := utils.ListReplace(req.AssetsSource, "-", "")
				// 过滤掉空字符串，避免ES查询错误
				filteredNames := make([]string, 0)
				for _, name := range names {
					if name != "" {
						filteredNames = append(filteredNames, name)
					}
				}

				// 只有在有有效的资产来源时才添加查询条件
				if len(filteredNames) > 0 {
					builder.AddShould([]interface{}{"assets_source", "in", elastic.ToInterfaceArray(filteredNames)})
				}
				// 添加不存在assets_source字段的条件（对应空值查询）
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"assets_source", "EXISTS"}}})
			} else {
				// 过滤掉空字符串
				filteredSources := make([]string, 0)
				for _, source := range req.AssetsSource {
					if source != "" {
						filteredSources = append(filteredSources, source)
					}
				}

				if len(filteredSources) > 0 {
					builder.AddMust([]interface{}{"assets_source", "in", elastic.ToInterfaceArray(filteredSources)})
				}
			}
		}
		// Cloud Name
		if req.CloudName != nil {
			var nameQuery [][]interface{}
			nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(*req.CloudName)})
			nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToLower(*req.CloudName))})
			nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToUpper(*req.CloudName))})
			builder.AddShould(nameQuery...)
		}
		// Clue Company Name
		if len(req.ClueCompanyName) > 0 {
			if utils.ListContains(req.ClueCompanyName, "-") {
				names := utils.ListReplace(req.ClueCompanyName, "-", "")
				builder.AddShould([]interface{}{"clue_company_name.keyword", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"clue_company_name", "EXISTS"}}})
			} else {
				builder.AddShould([]interface{}{"clue_company_name.keyword", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
				builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
			}
		}
	}
	builder.AddMust([]interface{}{"status", "in", status}, []interface{}{"user_id", req.UserId})
	return builder.Build()
}

// getStateText 获取状态文本
func getStateText(state int) string {
	switch state {
	case 1:
		return "在线"
	case 2:
		return "离线"
	default:
		return "未知"
	}
}

// convertToStringSlice 将任意类型的切片转换为字符串切片
func convertToStringSlice(slice interface{}) []string {
	if slice == nil {
		return []string{}
	}

	// 尝试类型断言为[]interface{}
	if values, ok := slice.([]interface{}); ok {
		result := make([]string, 0, len(values))
		for _, v := range values {
			if str, ok := v.(string); ok {
				result = append(result, str)
			} else if v != nil {
				// 尝试将其他类型转换为字符串
				result = append(result, fmt.Sprintf("%v", v))
			}
		}
		return result
	}

	// 尝试类型断言为[]string
	if values, ok := slice.([]string); ok {
		return values
	}

	// 如果都不是，返回空切片
	return []string{}
}

// getTopClue 获取线索链，递归查找父级线索
// 参照PHP实现重写
func getTopClue(parentID int64, clueList []*clues.Clue, chainList *[]map[string]interface{}, id ...int64) {
	var currentID int64
	if len(id) > 0 {
		currentID = id[0]
	}

	log.Infof("getTopClue调用: parentID=%d, currentID=%d, 线索数量=%d, 当前链长度=%d",
		parentID, currentID, len(clueList), len(*chainList))

	for _, clue := range clueList {
		// 处理初始查找（通过ID查找）
		if parentID == 0 && clue.Id == uint64(currentID) {
			// 如果是推荐来源且有from_ip
			if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
				// 如果不是首级线索，将from_ip添加到末尾
				if clue.ParentId > 0 {
					*chainList = append(*chainList, map[string]interface{}{
						"content": clue.FromIp,
					})
					log.Infof("添加非首级线索的from_ip到末尾: %s", clue.FromIp)
				} else {
					// 否则添加到开头
					*chainList = append([]map[string]interface{}{
						{"content": clue.FromIp},
					}, *chainList...)
					log.Infof("添加首级线索的from_ip到开头: %s", clue.FromIp)
				}
			}
		} else {
			// 通过父ID查找
			if clue.Id == uint64(parentID) {
				// 将当前线索添加到链的开头
				*chainList = append([]map[string]interface{}{
					{
						"id":                clue.Id,
						"content":           clue.Content,
						"type":              clue.Type,
						"parent_id":         clue.ParentId,
						"clue_company_name": clue.ClueCompanyName,
						"source":            clue.Source,
					},
				}, *chainList...)
				log.Infof("添加线索到链开头: id=%d, content=%s, parent_id=%d",
					clue.Id, clue.Content, clue.ParentId)

				// 如果是推荐来源且有from_ip
				if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
					*chainList = append([]map[string]interface{}{
						{"content": clue.FromIp},
					}, *chainList...)
					log.Infof("添加推荐来源线索的from_ip到开头: %s", clue.FromIp)
				}

				// 递归查找父级线索
				getTopClue(int64(clue.ParentId), clueList, chainList, currentID)
			}
		}
	}
}

func ThreatenAssetsIPExport(_ context.Context, req *pb.IpAssetActionRequest, resp *pb.FileExportResponse) error {
	log.Infof("[威胁资产] 导出请求: %v", req)
	query := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})
	log.Infof("[威胁资产] 查询条件参数: %s", utils.AnyToStr(query))
	q := elastic.ParseQuery(query)
	list, err := elastic.All[fofaee_assets.FofaeeAssets](500, q, asset_account.GetFofaAssetsDefaultSorter())
	if err != nil {
		log.Errorf("[威胁资产] 查询失败: %v, req: %v", err, req)
		return err
	}

	// 生成Excel文件
	file := excelize.NewFile()
	sheet := "威胁资产"
	err = file.SetSheetName("Sheet1", sheet)
	if err != nil {
		return err
	}

	// 设置表头
	headers := []string{"IP地址", "企业名称", "威胁类型", "端口", "协议", "域名", "URL", "网站标题", "状态码", "云厂商", "组件信息", "地理位置", "资产状态", "资产标签", "发现时间", "更新时间"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err = file.SetCellValue(sheet, cell, header)
		if err != nil {
			return err
		}
	}

	// 写入数据
	row := 2
	for _, r := range list {
		companyName := ""
		if len(r.ClueCompanyName) > 0 {
			companyName, _ = r.ClueCompanyName[0].(string)
		}
		cloudName := ""
		if len(r.CloudName) > 0 {
			cloudName, _ = r.CloudName[0].(string)
		}
		for _, u := range r.HostList {
			if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Ip); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), companyName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), r.ThreatenTypeName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("D%d", row), u.Port); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("E%d", row), u.Protocol); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("F%d", row), u.Subdomain); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("G%d", row), u.Url); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("H%d", row), u.Title); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("I%d", row), u.HttpStatusCode); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), cloudName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), asset_account.GetRuleTagString(r.RuleTags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), r.Geo.Province); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("M%d", row), utils.If(cast.ToInt(u.OnlineState) == 1, "在线", "离线")); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("N%d", row), asset_account.GetAssetTagString(r.Tags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("O%d", row), r.CreatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("P%d", row), r.UpdatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			row++
		}
		if len(r.HostList) == 0 {
			if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Ip); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), companyName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), asset_account.GetThreatenTypeString(cast.ToInt(r.ThreatenType))); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), cloudName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), asset_account.GetRuleTagString(r.RuleTags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), r.Geo.Province); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("M%d", row), ""); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("N%d", row), asset_account.GetAssetTagString(r.Tags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("O%d", row), r.CreatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("P%d", row), r.UpdatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			row++
		}
	}

	// 保存文件
	filename := fmt.Sprintf("threaten_assets_%s.xlsx", time.Now().Format("**************"))
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	if err := file.SaveAs(storagePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	fp, err := utils.DownloadFileEncrypt(accessPath, "威胁资产数据"+time.Now().Format(time.DateOnly)+path.Ext(filename), "", false)
	if err != nil {
		return fmt.Errorf("生成下载路径失败: %v", err)
	}
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(filename))

	// 设置返回的文件路径
	resp.Url = apiFilePath
	return nil
}

func IpThreatenAssetDelete(_ context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.Infof("[IP威胁资产删除] 开始删除操作，请求参数: %v", req)

	// 第一步：从 fofaee_assets 索引中查询要删除的资产，获取IP列表
	query := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})
	log.Infof("[IP威胁资产删除] 构建删除查询条件: %s", utils.AnyToStr(query))
	q := elastic.ParseQuery(query)

	// 查询要删除的资产列表
	assets, err := elastic.All[fofaee_assets.FofaeeAssets](500, q, nil)
	if err != nil {
		log.Errorf("[IP威胁资产删除] 查询要删除的资产失败: %v, req: %v", err, req)
		return err
	}

	if len(assets) == 0 {
		log.Infof("[IP威胁资产删除] 没有找到要删除的威胁资产")
		return nil
	}

	// 提取IP列表
	var ipList []string
	for _, asset := range assets {
		if asset.Ip != "" {
			ipList = append(ipList, asset.Ip)
		}
	}

	log.Infof("[IP威胁资产删除] 找到 %d 个威胁资产要删除，IP列表: %v", len(assets), ipList)

	// 第二步：删除 fofaee_assets 索引中的数据
	err = elastic.Delete[fofaee_assets.FofaeeAssets](q)
	if err != nil {
		log.Errorf("[IP威胁资产删除] 删除 fofaee_assets 中的威胁资产失败: %v, req: %v", err, req)
		return err
	}

	log.Infof("[IP威胁资产删除] 成功删除 fofaee_assets 中的 %d 个威胁资产", len(assets))

	// 第三步：如果有IP列表，删除 foradar_assets 索引中对应的数据
	if len(ipList) > 0 {
		// 构建删除 foradar_assets 的查询条件
		var foradarBuilder elastic.SearchBuilder
		foradarBuilder.AddMust([]interface{}{"user_id", req.UserId})
		foradarBuilder.AddMust([]interface{}{"ip", "in", elastic.ToInterfaceArray(ipList)})

		foradarQuery := elastic.ParseQuery(foradarBuilder.Build())
		log.Infof("[IP威胁资产删除] foradar_assets 删除条件: %v", foradarBuilder.Build())

		// 删除 foradar_assets 中的数据
		err = elastic.Delete[foradar_assets.ForadarAsset](foradarQuery)
		if err != nil {
			log.Errorf("[IP威胁资产删除] 删除 foradar_assets 失败: %v, IP列表: %v", err, ipList)
			// 这里不返回错误，因为主要的删除操作已经成功
			log.Warnf("[IP威胁资产删除] foradar_assets 删除失败，但 fofaee_assets 删除成功")
		} else {
			log.Infof("[IP威胁资产删除] 成功删除 foradar_assets 中 %d 个威胁资产IP的数据", len(ipList))
		}

		// 第四步：删除IP历史记录数据
		ipHistoryModel := ip_history.NewModel()
		err = ipHistoryModel.DeleteByUserIdAndIps(context.Background(), req.UserId, ipList)
		if err != nil {
			log.Errorf("[IP威胁资产删除] 删除威胁资产IP历史记录失败: %v, IP列表: %v", err, ipList)
			// 这里不返回错误，因为主要的删除操作已经成功
			log.Warnf("[IP威胁资产删除] IP历史记录删除失败，但主要删除操作成功")
		} else {
			log.Infof("[IP威胁资产删除] 成功删除 %d 个威胁资产IP的历史记录", len(ipList))
		}
	}

	log.Infof("[IP威胁资产删除] 删除操作完成，共处理 %d 个威胁资产", len(assets))
	return nil
}

func SetThreatenType(ctx context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.Infof("[设置威胁资产类型] 开始设置威胁资产类型，请求参数: %v", req)

	// 参数验证
	if req == nil || req.UserId == 0 {
		return errors.New("参数错误：用户ID不能为空")
	}

	if req.ThreatenType == nil || *req.ThreatenType == "" {
		return errors.New("请您选择要标记的威胁类型，威胁类型不能为空!")
	}

	// 第一步：从 fofaee_assets 索引中查询要修改的威胁资产，获取IP列表
	query := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})
	log.Infof("[设置威胁资产类型] 构建查询条件: %s", utils.AnyToStr(query))
	q := elastic.ParseQuery(query)

	// 查询要修改的资产列表
	assets, err := elastic.All[fofaee_assets.FofaeeAssets](500, q, nil)
	if err != nil {
		log.Errorf("[设置威胁资产类型] 查询要修改的威胁资产失败: %v, req: %v", err, req)
		return err
	}

	if len(assets) == 0 {
		log.Infof("[设置威胁资产类型] 没有找到要修改的威胁资产")
		return nil
	}

	// 提取IP列表
	var ipList []string
	for _, asset := range assets {
		if asset.Ip != "" {
			ipList = append(ipList, asset.Ip)
		}
	}

	log.Infof("[设置威胁资产类型] 找到 %d 个威胁资产要修改，IP列表: %v", len(assets), ipList)

	// 构建更新数据
	threatenTypeName := "其他"
	if req.ThreatenTypeName != nil && *req.ThreatenTypeName != "" {
		threatenTypeName = *req.ThreatenTypeName
	}

	updateData := map[string]interface{}{
		"threaten_type":      *req.ThreatenType,
		"threaten_type_name": threatenTypeName,
	}

	log.Infof("[设置威胁资产类型] 更新数据: %v", updateData)

	// 第二步：更新 fofaee_assets 索引中的数据
	err = elastic.UpdateByParams[fofaee_assets.FofaeeAssets](query, updateData)
	if err != nil {
		log.Errorf("[设置威胁资产类型] 更新 fofaee_assets 失败: %v, req: %v", err, req)
		return err
	}

	log.Infof("[设置威胁资产类型] 成功更新 fofaee_assets 中的 %d 个威胁资产", len(assets))

	// 第三步：如果有IP列表，更新 foradar_assets 索引中对应的数据
	if len(ipList) > 0 {
		// 构建更新 foradar_assets 的查询条件
		var foradarBuilder elastic.SearchBuilder
		foradarBuilder.AddMust([]interface{}{"user_id", req.UserId})
		foradarBuilder.AddMust([]interface{}{"ip", "in", elastic.ToInterfaceArray(ipList)})

		foradarQuery := foradarBuilder.Build()
		log.Infof("[设置威胁资产类型] foradar_assets 更新条件: %v", foradarQuery)

		// 更新 foradar_assets 中的数据
		err = elastic.UpdateByParams[foradar_assets.ForadarAsset](foradarQuery, updateData)
		if err != nil {
			log.Errorf("[设置威胁资产类型] 更新 foradar_assets 失败: %v, IP列表: %v", err, ipList)
			// 这里不返回错误，因为主要的更新操作已经成功
			log.Warnf("[设置威胁资产类型] foradar_assets 更新失败，但 fofaee_assets 更新成功")
		} else {
			log.Infof("[设置威胁资产类型] 成功更新 foradar_assets 中 %d 个威胁资产IP的数据", len(ipList))
		}
	}

	// 记录操作日志 - 参考PHP中的LogService::info
	log.Infof("[设置威胁资产类型] 修改操作完成，用户[%d]批量修改 %d 个威胁资产的类型为[%s:%s]",
		req.UserId, len(assets), *req.ThreatenType, threatenTypeName)

	return nil
}

func AssetsThreatenSetStatus(_ context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.Infof("[忽略资产] 资产处置请求: %v", req)
	// 参数验证
	if req.SetStatus == nil {
		return fmt.Errorf("缺少状态参数！")
	}

	var updateArr = make(map[string]any)
	updateArr["is_shadow"] = fofaee_assets.NOT_SHADOW

	// 根据不同状态处理
	switch *req.SetStatus {
	case fofaee_assets.STATUS_CLAIMED:
		// 检查资产限制
		if cfg.IsLocalClient() {
			// 本地化单独限制
			if req.CompanyId != 0 {
				param := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})
				total, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](param)
				if err != nil {
					return fmt.Errorf("获取IP列表失败: %v", err)
				}
				limit, err := company.CheckLimitById(int64(req.CompanyId), company.LIMIT_TYPE_IP, int(total))
				if err != nil {
					return err
				}
				if limit < 0 {
					return fmt.Errorf("您认领的资产数量超出限制%d个,请去除相应数量后重新操作", -limit)
				}
			} else {
				// 安服或者管理员
				usedNumParam := [][]interface{}{
					{"user_id", req.UserId},
					{"status", "in", []interface{}{1, 4}},
				}
				usedNum, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](usedNumParam)
				if err != nil {
					return fmt.Errorf("获取已使用资产数量失败: %v", err)
				}
				// 获取用户总的授权数量
				userInfo, err := user.NewUserModel().FindById(req.UserId)
				if err != nil {
					return fmt.Errorf("获取用户信息失败: %v", err)
				}

				canScanIpNum := cast.ToInt(userInfo.LimitIPAsset) - int(usedNum)
				if canScanIpNum <= 0 {
					return fmt.Errorf("您认领资产数量超出限制，无法认领")
				}

				param := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})
				total, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](param)
				if err != nil {
					return fmt.Errorf("获取IP列表失败: %v", err)
				}
				allCount := int(total) + int(usedNum)
				limitCount := cast.ToInt(userInfo.LimitIPAsset) - allCount
				if limitCount < 0 {
					return fmt.Errorf("您认领资产数量超出限制%d个,请去除相应数量后重新操作", -limitCount)
				}
			}
		} else {
			// 非本地化检查资产限制
			if req.CompanyId != 0 {
				param := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})

				total, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](param)
				if err != nil {
					return fmt.Errorf("获取IP列表失败: %v", err)
				}

				limitCount, err := company.CheckLimitById(int64(req.CompanyId), company.LIMIT_TYPE_IP, int(total))
				if err != nil {
					return err
				}
				if limitCount < 0 {
					return fmt.Errorf("您认领资产数量超出限制%d个,请去除相应数量后重新操作", -limitCount)
				}
			}
		}
		updateArr["status"] = fofaee_assets.STATUS_CLAIMED

	case fofaee_assets.STATUS_IGNORE:
		// 忽略资产
		updateArr["status"] = fofaee_assets.STATUS_IGNORE

	case fofaee_assets.STATUS_DEFAULT:
		// 疑似资产
		updateArr["status"] = fofaee_assets.STATUS_DEFAULT
		updateArr["type"] = fofaee_assets.TYPE_RECOMMEND
		updateArr["is_user_sign_unsure"] = 1

	case fofaee_assets.STATUS_THREATEN:
		// 标记威胁资产
		updateArr["status"] = fofaee_assets.STATUS_THREATEN
		if req.ThreatenType != nil {
			updateArr["threaten_type"] = *req.ThreatenType
			if req.ThreatenTypeName != nil {
				updateArr["threaten_type_name"] = *req.ThreatenTypeName
			} else {
				updateArr["threaten_type_name"] = "其他"
			}
		}

	default:
		return fmt.Errorf("不支持的状态: %d", *req.SetStatus)
	}

	// 获取IP列表和组织ID列表
	param := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_THREATEN})
	query := elastic.ParseQuery(param)
	assets, err := elastic.All[fofaee_assets.FofaeeAssets](500, query, GetFofaAssetsDefaultSorter(), "ip", "organization_id")
	if err != nil {
		log.Errorf("[忽略资产] 获取IP资产失败: %v", err)
		return err
	}

	ipList := utils.ListColumn(assets, func(t *fofaee_assets.FofaeeAssets) string { return t.Ip })
	var orgList []int64
	for _, item := range assets {
		for _, id := range item.OrganizationId {
			orgList = append(orgList, cast.ToInt64(id))
		}
	}
	orgList = utils.ListDistinctNonZero(orgList)

	// 更新FofaAssets
	log.Infof("[忽略资产] 更新资产状态查询条件: %s", utils.AnyToStr(param))
	err = elastic.UpdateByParams[fofaee_assets.FofaeeAssets](param, updateArr)
	if err != nil {
		log.Errorf("[忽略资产] 更新Fofa资产失败: %v, req: %v", err, req)
		return err
	}

	// 更新ForadarAssets
	if len(ipList) > 0 {
		par := [][]interface{}{
			{"user_id", req.UserId},
			{"ip", "in", elastic.ToInterfaceArray(ipList)},
		}
		log.Infof("[忽略资产] 更新Foradar资产查询条件: %s", utils.AnyToStr(par))
		err = elastic.UpdateByParams[foradar_assets.ForadarAsset](par, updateArr)
		if err != nil {
			log.Errorf("[忽略资产] 更新Foradar资产失败: %v, req: %v", err, req)
			return err
		}
	} else {
		return nil
	}

	// 计算资产数量限制
	if req.CompanyId != 0 {
		allUser, err := user.NewUserModel().ListAll(mysql.WithWhere("company_id", req.CompanyId))
		if err != nil {
			log.Errorf("[忽略资产] 获取公司用户失败: %v, req: %v", err, req)
			return err
		}
		ids := utils.ListColumn(allUser, func(t user.User) uint64 { return t.Id })

		var builder elastic.SearchBuilder
		builder.AddShould([]interface{}{"user_id", "in", ids})
		builder.AddShould([]interface{}{"company_id", req.CompanyId})
		builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}})

		assetCount, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](builder.Build())
		if err != nil {
			log.Errorf("[忽略资产] 获取Fofa资产数量失败: %v, req: %v", err, req)
		}

		comp, err := company.NewCompanyModel().FindById(req.CompanyId, req.UserId)
		if err != nil {
			log.Errorf("[忽略资产] 获取企业数据失败: %v, req: %v", err, req)
		}
		err = comp.UpdateLimit(company.LIMIT_TYPE_IP, int(assetCount), false)
		if err != nil {
			log.Errorf("[忽略资产] 更新公司IP资产数量限制失败: %v, req: %v", err, req)
			return err
		}
	}

	// 认领资产的额外处理
	if *req.SetStatus == fofaee_assets.STATUS_CLAIMED {
		log.Infof("[忽略资产] 忽略资产认领到台账: ip_array=%v, user_id=%d", ipList, req.UserId)

		// 生成证书维度数据
		certParam := [][]interface{}{
			{"user_id", req.UserId},
			{"ip", "in", elastic.ToInterfaceArray(ipList)},
			{"cert.subject_key", "exists"},
		}
		certAssets, err := elastic.All[foradar_assets.ForadarAsset](500, elastic.ParseQuery(certParam), nil)
		if err == nil && len(certAssets) > 0 {
			//for _, scanAsset := range certAssets {
			//	// TODO 处理证书资产 CertAssetService::dealCertAsset
			//	if err != nil {
			//		log.Warnf("[忽略资产] 忽略的资产-认领资产dealCertAsset，证书信息提取失败: %v", scanAsset)
			//	}
			//}
		}

		if cfg.ExecGolangJob() {
			//todo 调用go的job

		} else {
			// 提取线索
			err = asyncq.ExtractAssetCluesJob.Dispatch(req.UserId, ipList)
			if err != nil {
				log.Errorf("[忽略资产] 分发提取资产线索任务失败: %v", err)
			}

			// 统计线索已认领资产数量
			err = asyncq.CountClueAssetTotalJob.Dispatch(req.UserId)
			if err != nil {
				log.Errorf("[忽略资产] 分发统计线索资产任务失败: %v", err)
			}
		}
	}
	if cfg.ExecGolangJob() {
		//todo 调用go的job
		err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
		if err != nil {
			log.Errorf("[忽略资产] 分发缓存IP条件任务失败: %v", err)
		}
	} else {
		// 缓存IP条件
		err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
		if err != nil {
			log.Errorf("[忽略资产] 分发缓存IP条件任务失败: %v", err)
		}
	}
	return nil
}

// getRuleInfos 从rule_tags中提取cn_product字段并用逗号连接
func getRuleInfos(ruleTags []interface{}) string {
	var products []string
	for _, tag := range ruleTags {
		if tagMap, ok := tag.(map[string]interface{}); ok {
			if cnProduct, exists := tagMap["cn_product"]; exists {
				if productStr, ok := cnProduct.(string); ok && productStr != "" {
					products = append(products, productStr)
				}
			}
		}
	}
	return strings.Join(products, ",")
}

// buildRuleInfos 根据PHP逻辑构建rule_infos字段
// 对应PHP: $rules = implode(',', array_unique(array_column($rules, 'cn_product')));
func buildRuleInfos(ruleTags []fofaee_assets.RuleTag) string {
	if len(ruleTags) == 0 {
		return ""
	}

	// 提取cn_product字段 - 对应PHP的array_column($rules, 'cn_product')
	var products []string
	for _, tag := range ruleTags {
		if tag.CnProduct != "" {
			products = append(products, tag.CnProduct)
		}
	}

	// 去重 - 对应PHP的array_unique
	uniqueProducts := make(map[string]bool)
	var uniqueList []string
	for _, product := range products {
		if !uniqueProducts[product] {
			uniqueProducts[product] = true
			uniqueList = append(uniqueList, product)
		}
	}

	// 用逗号连接 - 对应PHP的implode(',', ...)
	return strings.Join(uniqueList, ",")
}

func GetFofaAssetsDefaultSorter() []elastic_search.Sorter {
	return []elastic_search.Sorter{
		elastic_search.NewFieldSort("updated_at").Desc(),
		elastic_search.NewFieldSort("_id").Desc(),
	}
}
