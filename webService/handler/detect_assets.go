package handler

import (
	"context"
	"micro-service/webService/handler/detect_assets"
	pb "micro-service/webService/proto"
)

func (w *Web) GetCompanyDropList(ctx context.Context, request *pb.CompanyDropListRequest, response *pb.CompanyDropListResponse) error {
	err := detect_assets.GetCompanyDropList(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) Info(ctx context.Context, request *pb.DetectAssetsInfoRequest, response *pb.DetectAssetsInfoResponse) error {
	detectAssetsHandler := detect_assets.NewDetectAssetsHandler()
	err := detectAssetsHandler.Info(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) StartSyncJob(ctx context.Context, request *pb.StartSyncJobRequest, response *pb.StartSyncJobResponse) error {
	err := detect_assets.StartSyncJob(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) GetCompanyCascadeEquity(ctx context.Context, request *pb.GetCompanyCascadeEquityRequest, response *pb.GetCompanyCascadeEquityResponse) error {
	err := detect_assets.GetCompanyCascadeEquity(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) GetClueCount(ctx context.Context, request *pb.ClueCountRequest, response *pb.ClueCountResponse) error {
	err := detect_assets.GetClueCount(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) NoWholeClueList(ctx context.Context, request *pb.ClueListRequest, response *pb.ClueListPageResponse) error {
	err := detect_assets.GetNoWholeClueList(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) WholeClueList(ctx context.Context, request *pb.ClueListRequest, response *pb.ClueListWholeResponse) error {
	err := detect_assets.GetWholeClueList(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) GetRepeatOriginClues(ctx context.Context, request *pb.StartSyncJobRequest, response *pb.StartSyncJobResponse) error {
	err := detect_assets.GetRepeatOriginClues(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) AssetsEvaluate(ctx context.Context, request *pb.AssetsEvaluateRequest, response *pb.AssetsEvaluateResponse) error {
	err := detect_assets.AssetsEvaluate(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) StopAndDeleteTask(ctx context.Context, request *pb.DetectTaskStopRequest, response *pb.AssetsEvaluateResponse) error {
	err := detect_assets.StopAndDeleteTask(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) DelDetectAssetsTask(ctx context.Context, request *pb.DelDetectAssetsTaskRequest, response *pb.DelDetectAssetsTaskResponse) error {
	err := detect_assets.DelDetectAssetsTask(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) ExpandClues(ctx context.Context, request *pb.ExpandCluesRequest, response *pb.ExpandCluesResponse) error {
	err := detect_assets.ExpandClues(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) ConfirmAllClues(ctx context.Context, request *pb.ConfirmAllCluesRequest, response *pb.ExpandCluesResponse) error {
	err := detect_assets.ConfirmAllClues(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) ClueBlack(ctx context.Context, request *pb.ClueBlackRequest, response *pb.Empty) error {
	err := detect_assets.ClueBlack(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) PassClue(ctx context.Context, request *pb.PassClueRequest, response *pb.Empty) error {
	err := detect_assets.PassClue(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) ImportClues(ctx context.Context, request *pb.ImportCluesRequest, response *pb.Empty) error {
	err := detect_assets.ImportClues(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) TaskIndex(ctx context.Context, request *pb.TaskIndexRequest, response *pb.TaskIndexResponse) error {
	err := detect_assets.TaskIndex(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) GetFofaAssetsNum(ctx context.Context, request *pb.GetFofaAssetsNumRequest, response *pb.GetFofaAssetsNumResponse) error {
	err := detect_assets.GetFofaAssetsNum(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}
