package website_message

import (
	"errors"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/website_message"
	"micro-service/pkg/dbx"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/manage"
	pb "micro-service/webService/proto"
	"time"

	"gorm.io/gorm"
)

func WebsiteMessageNewList(req *pb.WebsiteMessageNewListRequest, lastLoginTime time.Time, rsp *pb.WebsiteMessageNewListResponse) error {
	query := make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithColumnValue("user_id", req.UserId))
	if req.OperateCompanyId > 0 {
		query = append(query, mysql.WithColumnValue("company_id", req.OperateCompanyId))
	}
	query = append(query, mysql.WithGT("created_at", lastLoginTime))
	data, err := website_message.NewWebsiteMessageModel().MessageCountGroupByType(query...)
	// 报错不为 record not found 直接返回
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	rsp.Items = []*pb.WebsiteMessageNewListItem{}
	for k, v := range data {
		rsp.Items = append(rsp.Items, &pb.WebsiteMessageNewListItem{
			MsgType: k,
			Total:   v,
		})
	}
	return nil
}

func WebsiteMessageList(req *pb.WebsiteMessageListRequest, rsp *pb.WebsiteMessageListResponse) error {
	handlers := commonSelect(&pb.WebsiteMessageUpdateRequest{
		UserId:           req.UserId,
		OperateCompanyId: req.OperateCompanyId,
		Keyword:          req.Keyword,
		MsgType:          req.MsgType,
		MsgSubtype:       req.MsgSubtype,
		MsgRead:          req.MsgRead,
		CreatedAt:        req.CreatedAt,
	})

	// 查库
	messages, total, err := website_message.NewWebsiteMessageModel().MessageList(int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}
	rsp.Total = total
	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	rsp.Items = []*pb.WebsiteMessageItem{}
	for _, message := range messages {
		rsp.Items = append(rsp.Items, &pb.WebsiteMessageItem{
			Id:                    message.ID,
			CreatedAt:             message.CreatedAt.Format(utils.DateTimeLayout),
			MsgContent:            message.MsgContent,
			MsgType:               message.MsgType,
			MsgSubtype:            message.MsgSubtype,
			MsgStatus:             message.MsgStatus,
			LastTimeAffectAssetId: message.LastTimeAffectAssetId,
			ThisTimeAffectAssetId: message.ThisTimeAffectAssetId,
			AffectAssetId:         message.AffectAssetId,
			IsRead:                message.IsRead,
			AppType:               message.AppType,
		})
	}
	return nil
}

func WebsiteMessageRead(req *pb.WebsiteMessageUpdateRequest) error {
	handlers := commonSelect(req)
	// 已读
	if err := website_message.NewWebsiteMessageModel().MessageSave(handlers...); err != nil {
		return err
	}
	return nil
}

func WebsiteMessageDelete(req *pb.WebsiteMessageUpdateRequest) error {
	handlers := commonSelect(req)
	// 删除
	if err := website_message.NewWebsiteMessageModel().MessageDelete(handlers...); err != nil {
		return err
	}
	return nil
}

func commonSelect(req *pb.WebsiteMessageUpdateRequest) []mysql.HandleFunc {
	var handlers []mysql.HandleFunc
	handlers = append(handlers, mysql.WithColumnValue("user_id", req.UserId))

	if len(req.Ids) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("id", req.Ids))
	}
	if req.Keyword != "" {
		handlers = append(handlers, mysql.WithLRLike("msg_content", req.Keyword))
	}
	if req.MsgType != 0 {
		handlers = append(handlers, mysql.WithColumnValue("msg_type", req.MsgType))
	}
	if req.MsgSubtype != 0 {
		handlers = append(handlers, mysql.WithColumnValue("msg_subtype", req.MsgSubtype))
	}
	if req.MsgRead != 0 {
		handlers = append(handlers, mysql.WithColumnValue("is_read", req.MsgRead))
	}
	if len(req.CreatedAt) == 2 {
		d1, d2, _ := manage.ProcessDateRange(req.CreatedAt)
		handlers = append(handlers, mysql.WithBetween("created_at", d1, d2))
	}
	return handlers
}

func WebsiteMessageNotify(req *pb.WebsiteMessageNotifyRequest, rsp *pb.WebsiteMessageNotifyResponse) error {
	var handlers []mysql.HandleFunc
	handlers = append(handlers, mysql.WithColumnValue("user_id", req.UserId))

	notify, err := website_message.NewWebsiteMessageModel().NotificationList(handlers...)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}
	rsp.Id = notify.Id
	rsp.AssetChangeJson = notify.AssetChangeJson
	rsp.TaskJson = notify.TaskJson
	rsp.RiskFoundJson = notify.RiskFoundJson
	rsp.IsWebsiteMsg = notify.IsWebsiteMsg
	rsp.IsEmail = notify.IsEmail
	rsp.Email = notify.Email
	return nil
}

func WebsiteMessageUpdate(req *pb.WebsiteMessageNotifyRequest) error {
	var handlers []mysql.HandleFunc
	handlers = append(handlers, mysql.WithColumnValue("user_id", req.UserId))

	list, err := website_message.NewWebsiteMessageModel().NotificationList(handlers...)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}
	notify := website_message.Notify{
		Model:           dbx.Model{},
		UserId:          req.UserId,
		CompanyId:       utils.If(req.OperateCompanyId <= 0, 0, uint64(req.OperateCompanyId)),
		AssetChangeJson: req.AssetChangeJson,
		TaskJson:        req.TaskJson,
		RiskFoundJson:   req.RiskFoundJson,
		IsWebsiteMsg:    req.IsWebsiteMsg,
		IsEmail:         req.IsEmail,
		Email:           req.Email,
	}
	// 记录不为空
	if list.Id != 0 {
		notify.Id = list.Id
		notify.CreatedAt = list.CreatedAt
	}
	if err = website_message.NewWebsiteMessageModel().NotificationCreate(&notify); err != nil {
		return err
	}
	return nil
}
