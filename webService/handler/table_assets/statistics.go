package table_assets

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
	"time"

	"github.com/spf13/cast"
)

// printAssetDetails 打印资产详细信息用于调试
func printAssetDetails(asset *fofaee_assets.FofaeeAssets) {
	log.Infof("[资产详情] IP: %s", asset.Ip)
	log.Infof("[资产详情] 规则标签数量: %d", len(asset.RuleTags))

	// 打印规则标签
	if len(asset.RuleTags) > 0 {
		for i, tag := range asset.RuleTags {
			if i < 5 { // 只打印前5个
				log.Infof("[资产详情] 规则标签[%d]: CnProduct=%s, Product=%s", i, tag.CnProduct, tag.Product)
			} else {
				break
			}
		}
	}

	// 打印端口列表
	log.Infof("[资产详情] 端口列表数量: %d", len(asset.PortList))
	if len(asset.PortList) > 0 {
		for i, port := range asset.PortList {
			if i < 5 { // 只打印前5个
				log.Infof("[资产详情] 端口[%d]: Port=%v, Protocol=%s", i, port.Port, port.Protocol)
			} else {
				break
			}
		}
	}

	// 尝试将整个资产对象序列化为JSON并打印
	assetJSON, err := json.MarshalIndent(asset, "", "  ")
	if err == nil {
		log.Debugf("[资产详情] 完整资产JSON: %s", string(assetJSON))
	}
}

// TableAssetsStatistics 统计资产数据维度
func TableAssetsStatistics(ctx context.Context, req *pb.TableAssetsStatisticsRequest, rsp *pb.TableAssetsStatisticsResponse) error {
	// 参数验证
	if req == nil {
		log.Errorf("[统计资产数据维度] 请求参数为空")
		return fmt.Errorf("request is nil")
	}

	if rsp == nil {
		log.Errorf("[统计资产数据维度] 响应参数为空")
		return fmt.Errorf("response is nil")
	}

	if req.UserId <= 0 {
		log.Errorf("[统计资产数据维度] 用户ID无效: %d", req.UserId)
		return fmt.Errorf("invalid user id: %d", req.UserId)
	}

	log.Infof("[统计资产数据维度] 开始处理请求: userId=%d, companyId=%d, status=%d, type=%d",
		req.UserId, req.CompanyId, req.Status, req.Type)

	// 启用缓存
	useCache := true

	// 生成缓存键
	cacheKey := fmt.Sprintf("table_assets_statistics:%d:%d:%d:%d", req.UserId, req.CompanyId, req.Status, req.Type)

	// 尝试从缓存获取
	if useCache {
		var cachedResponse pb.TableAssetsStatisticsResponse
		if redis.GetCache(cacheKey, &cachedResponse) {
			log.Infof("[统计资产数据维度] 缓存命中: %s", cacheKey)
			rsp.RuleNum = cachedResponse.RuleNum
			rsp.PortNum = cachedResponse.PortNum
			rsp.IpNum = cachedResponse.IpNum
			return nil
		}
		log.Infof("[统计资产数据维度] 缓存未命中: %s", cacheKey)
	}

	// 构建查询条件
	query := [][]interface{}{
		{"user_id", req.UserId},
		{"status", req.Status},
	}

	// 如果指定了资产类型，添加类型过滤条件
	if req.Type != 0 {
		query = append(query, []interface{}{"type", int(req.Type)})
	}

	// 查询资产数据
	list, err := elastic.AllByParams[fofaee_assets.FofaeeAssets](10000, query, nil)
	if err != nil {
		log.Errorf("[统计资产数据维度] 查询资产数据失败: %v", err)
		return err
	}

	log.Infof("[统计资产数据维度] 查询到 %d 条资产数据", len(list))

	// 检查是否有更多数据
	totalCount, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](query)
	if err != nil {
		log.Errorf("[统计资产数据维度] 统计总数失败: %v", err)
	} else {
		log.Infof("[统计资产数据维度] 实际总数: %d, 获取到的数量: %d", totalCount, len(list))
		if totalCount > int64(len(list)) {
			log.Warnf("[统计资产数据维度] 警告: 获取的数据不完整，可能影响统计结果")
		}
	}

	// 打印第一条资产数据的详细信息，用于调试
	if len(list) > 0 {
		printAssetDetails(list[0])
	}

	// 统计数据 - 与PHP逻辑保持一致
	// PHP: $conditon['rule_num'] = $list->pluck('rule_tags')->collapse()->pluck('cn_product')->count();
	// PHP: $conditon['port_num'] = $list->pluck('port_list')->collapse()->pluck('port')->count();

	// 收集所有规则标签和端口
	var allRuleTags []string
	var allPorts []string
	ipList := make(map[string]struct{})

	// 统计所有规则标签和端口的总数（不去重）
	var totalRuleTags int
	var totalPorts int

	for _, item := range list {
		// 安全检查
		if item == nil {
			log.Warnf("[统计资产数据维度] 跳过空资产项")
			continue
		}

		log.Debugf("[统计资产数据维度] 处理资产: IP=%s, RuleTags长度=%d, PortList长度=%d",
			item.Ip, len(item.RuleTags), len(item.PortList))

		// 收集规则标签
		if item.RuleTags != nil {
			for _, tag := range item.RuleTags {
				totalRuleTags++
				if tag.CnProduct != "" {
					allRuleTags = append(allRuleTags, tag.CnProduct)
				}
			}
		}

		// 收集端口
		if item.PortList != nil {
			for _, port := range item.PortList {
				totalPorts++
				if port.Port != nil {
					portStr := cast.ToString(port.Port)
					if portStr != "" && portStr != "0" {
						allPorts = append(allPorts, portStr)
					}
				}
			}
		}

		// 统计IP
		if item.Ip != "" {
			ipList[item.Ip] = struct{}{}
		}
	}

	log.Infof("[统计资产数据维度] 收集到规则标签数量: %d (总数: %d), 端口数量: %d (总数: %d)",
		len(allRuleTags), totalRuleTags, len(allPorts), totalPorts)

	// 去重计数
	ruleTagsMap := make(map[string]struct{})
	for _, tag := range allRuleTags {
		ruleTagsMap[tag] = struct{}{}
	}

	portsMap := make(map[string]struct{})
	for _, port := range allPorts {
		portsMap[port] = struct{}{}
	}

	log.Infof("[统计资产数据维度] 去重后规则标签数量: %d, 端口数量: %d", len(ruleTagsMap), len(portsMap))

	// 打印部分规则标签和端口用于调试
	var ruleTagsSample []string
	var portsSample []string
	i := 0
	for tag := range ruleTagsMap {
		if i < 10 {
			ruleTagsSample = append(ruleTagsSample, tag)
			i++
		} else {
			break
		}
	}

	i = 0
	for port := range portsMap {
		if i < 10 {
			portsSample = append(portsSample, port)
			i++
		} else {
			break
		}
	}

	log.Infof("[统计资产数据维度] 规则标签样本: %v", ruleTagsSample)
	log.Infof("[统计资产数据维度] 端口样本: %v", portsSample)

	// 设置响应 - 尝试两种不同的计算方式
	// 方式1: 使用去重后的数量（当前方式）
	// 方式2: 使用总数（不去重）

	// 使用方式2: 总数（不去重）
	rsp.RuleNum = int32(totalRuleTags)
	rsp.PortNum = int32(totalPorts)
	rsp.IpNum = int32(len(ipList))

	log.Infof("[统计资产数据维度] 统计结果(去重) - 规则数量: %d, 端口数量: %d, IP数量: %d",
		len(ruleTagsMap), len(portsMap), len(ipList))
	log.Infof("[统计资产数据维度] 统计结果(不去重) - 规则数量: %d, 端口数量: %d, IP数量: %d",
		totalRuleTags, totalPorts, len(ipList))

	// 缓存结果（30分钟）
	if useCache {
		cacheResponse := &pb.TableAssetsStatisticsResponse{
			RuleNum: rsp.RuleNum,
			PortNum: rsp.PortNum,
			IpNum:   rsp.IpNum,
		}

		if redis.SetCache(cacheKey, 30*time.Minute, cacheResponse) {
			log.Infof("[统计资产数据维度] 缓存设置成功: %s", cacheKey)
		} else {
			log.Errorf("[统计资产数据维度] 缓存设置失败: %s", cacheKey)
		}
	}

	return nil
}
