package table_assets

import (
	"context"
	"testing"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"

	testcommon "micro-service/initialize/common_test"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

// 初始化Mock服务用于statistics测试
func InitStatistics() {
	cfg.InitLoadCfg()
	log.Init()

	// 启用测试环境
	testcommon.SetTestEnv(true)

	// 初始化Mock服务 - 使用testcommon的MockServer
	mock := testcommon.NewMockEsServer()

	// 创建测试用的资产数据
	resultList := []fofaee_assets.FofaeeAssets{
		{
			Id: "1_**********",
			Ip: "**********",
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:     "80",
					Protocol: "http",
				},
				{
					Port:     "443",
					Protocol: "https",
				},
			},
			RuleTags: []fofaee_assets.RuleTag{
				{
					RuleId:    "tag_123",
					Product:   "测试产品1",
					CnProduct: "测试产品1",
				},
				{
					RuleId:    "tag_456",
					Product:   "测试产品2",
					CnProduct: "测试产品2",
				},
			},
			UpdatedAt: "2025-06-06 16:13:00",
		},
		{
			Id: "1_**********",
			Ip: "**********",
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:     "22",
					Protocol: "ssh",
				},
				{
					Port:     "80",
					Protocol: "http",
				},
			},
			RuleTags: []fofaee_assets.RuleTag{
				{
					RuleId:    "tag_123",
					Product:   "测试产品1",
					CnProduct: "测试产品1",
				},
				{
					RuleId:    "tag_789",
					Product:   "测试产品3",
					CnProduct: "测试产品3",
				},
			},
			UpdatedAt: "2025-06-06 16:14:00",
		},
		{
			Id: "1_**********",
			Ip: "**********",
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:     "3306",
					Protocol: "mysql",
				},
			},
			RuleTags: []fofaee_assets.RuleTag{
				{
					RuleId:    "tag_999",
					Product:   "测试产品4",
					CnProduct: "测试产品4",
				},
			},
			UpdatedAt: "2025-06-06 16:15:00",
		},
	}

	// 注册查询Mock数据
	mock.Register("/"+fofaee_assets.FofaeeAssetsIndex+"/_search", []*elastic.SearchHit{
		{
			Index:  fofaee_assets.FofaeeAssetsIndex,
			Type:   fofaee_assets.FofaeeAssetsType,
			Id:     resultList[0].Id,
			Source: utils.ToJSON(resultList[0]),
		},
		{
			Index:  fofaee_assets.FofaeeAssetsIndex,
			Type:   fofaee_assets.FofaeeAssetsType,
			Id:     resultList[1].Id,
			Source: utils.ToJSON(resultList[1]),
		},
		{
			Index:  fofaee_assets.FofaeeAssetsIndex,
			Type:   fofaee_assets.FofaeeAssetsType,
			Id:     resultList[2].Id,
			Source: utils.ToJSON(resultList[2]),
		},
	})

	// Count语句Mock数据
	countResponse := elastic.CountResponse{
		Count: 3,
	}

	mock.Register("/"+fofaee_assets.FofaeeAssetsIndex+"/_count", countResponse)

	// 设置是否使用Mock (默认true)
	es.SetTestEnv(true)
	mysql.SetTestEnv(true)
	redis.ForceTest(true) // 强制使用Redis Mock

	// 初始化数据库 - Mock客户端已经在NewMockEsServer()中创建和设置了
	mysql.GetInstance(cfg.LoadMysql())
	// redis.GetInstance(cfg.LoadRedis()) // 不需要再调用，ForceTest已经初始化了
}

// setupStatisticsRedisMock 设置Statistics Redis Mock期望
func setupStatisticsRedisMock() {
	rdm := redis.GetMockInstance()
	// 设置Redis Mock期望，允许多次调用
	rdm.MatchExpectationsInOrder(false)

	// 为不同的用户ID设置HMSET期望
	rdm.Regexp().ExpectHMSet("assets:count:.*", "sure_rule_num", ".*", "sure_port_num", ".*", "sure_ip_num", ".*").SetVal(true)
	rdm.Regexp().ExpectHMSet("assets:count:.*", "sure_rule_num", ".*", "sure_port_num", ".*", "sure_ip_num", ".*").SetVal(true)
	rdm.Regexp().ExpectHMSet("assets:count:.*", "sure_rule_num", ".*", "sure_port_num", ".*", "sure_ip_num", ".*").SetVal(true)

	// 设置HGetAll期望
	rdm.Regexp().ExpectHGetAll("assets:count:.*").SetVal(map[string]string{})
}

func TestTableAssetsStatistics(t *testing.T) {
	InitStatistics()
	setupStatisticsRedisMock()

	t.Run("with specific type", func(t *testing.T) {
		req := &pb.TableAssetsStatisticsRequest{
			UserId: 1,
			Status: fofaee_assets.STATUS_CLAIMED,
			Type:   1, // 指定资产类型
		}
		rsp := &pb.TableAssetsStatisticsResponse{}

		err := TableAssetsStatistics(context.Background(), req, rsp)
		assert.NoError(t, err)

		// 应该有统计结果
		assert.GreaterOrEqual(t, rsp.RuleNum, int32(0))
		assert.GreaterOrEqual(t, rsp.PortNum, int32(0))
		assert.GreaterOrEqual(t, rsp.IpNum, int32(0))
	})

	t.Run("different status", func(t *testing.T) {
		req := &pb.TableAssetsStatisticsRequest{
			UserId: 1,
			Status: fofaee_assets.STATUS_DEFAULT,
			Type:   0,
		}
		rsp := &pb.TableAssetsStatisticsResponse{}

		err := TableAssetsStatistics(context.Background(), req, rsp)
		assert.NoError(t, err)

		// 应该有统计结果
		assert.GreaterOrEqual(t, rsp.RuleNum, int32(0))
		assert.GreaterOrEqual(t, rsp.PortNum, int32(0))
		assert.GreaterOrEqual(t, rsp.IpNum, int32(0))
	})

	t.Run("zero user id", func(t *testing.T) {
		req := &pb.TableAssetsStatisticsRequest{
			UserId: 0,
			Status: fofaee_assets.STATUS_CLAIMED,
			Type:   0,
		}
		rsp := &pb.TableAssetsStatisticsResponse{}

		err := TableAssetsStatistics(context.Background(), req, rsp)
		// 用户ID为0时应该返回错误，这是正确的业务逻辑
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid user id")
	})
}

func TestPrintAssetDetails(t *testing.T) {
	// 创建测试资产数据
	asset := &fofaee_assets.FofaeeAssets{
		Ip: "**********",
		PortList: []fofaee_assets.FofaeeAssetPort{
			{
				Port:     "80",
				Protocol: "http",
			},
			{
				Port:     "443",
				Protocol: "https",
			},
		},
		RuleTags: []fofaee_assets.RuleTag{
			{
				RuleId:    "tag_123",
				Product:   "测试产品1",
				CnProduct: "测试产品1",
			},
			{
				RuleId:    "tag_456",
				Product:   "测试产品2",
				CnProduct: "测试产品2",
			},
		},
	}

	t.Run("print asset details", func(t *testing.T) {
		// 这个函数主要是打印日志，不会返回错误
		// 我们只需要确保它不会panic
		assert.NotPanics(t, func() {
			printAssetDetails(asset)
		})
	})

	t.Run("print empty asset", func(t *testing.T) {
		emptyAsset := &fofaee_assets.FofaeeAssets{}
		assert.NotPanics(t, func() {
			printAssetDetails(emptyAsset)
		})
	})

	t.Run("print asset with many tags and ports", func(t *testing.T) {
		// 创建包含大量标签和端口的资产
		largeAsset := &fofaee_assets.FofaeeAssets{
			Ip: "**********",
		}

		// 添加10个端口
		for i := 0; i < 10; i++ {
			largeAsset.PortList = append(largeAsset.PortList, fofaee_assets.FofaeeAssetPort{
				Port:     string(rune(80 + i)),
				Protocol: "http",
			})
		}

		// 添加10个规则标签
		for i := 0; i < 10; i++ {
			largeAsset.RuleTags = append(largeAsset.RuleTags, fofaee_assets.RuleTag{
				RuleId:    "tag_" + string(rune(i)),
				Product:   "产品" + string(rune(i)),
				CnProduct: "产品" + string(rune(i)),
			})
		}

		assert.NotPanics(t, func() {
			printAssetDetails(largeAsset)
		})
	})
}
