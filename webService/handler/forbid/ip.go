package forbid

import (
	"context"
	"database/sql"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/forbid"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	asyncq "micro-service/pkg/queue_helper"
	pb "micro-service/webService/proto"
	"net"
	"strings"
	"time"

	"go-micro.dev/v4/errors"
)

// ForbidIpsList 获取禁扫IP列表
func ForbidIpsList(ctx context.Context, req *pb.ForbidIpsListRequest, rsp *pb.ForbidIpsListResponse) error {
	log.WithContextInfof(ctx, "Received forbid.ForbidIpsList request: %v", req)

	// 获取用户ID
	userID := req.UserId

	// 构建查询条件
	opts := []mysql.HandleFunc{
		mysql.WithWhere("user_id = ?", uint64(userID)),
	}

	// 关键字过滤
	keyword := ""
	if req.Keyword != nil {
		keyword = *req.Keyword
	}

	// 如果关键字不为空，添加过滤条件
	if keyword != "" {
		opts = append(opts, mysql.WithWhere("ip_segment LIKE ?", "%"+keyword+"%"))
	}

	// 分页查询
	page := int(req.Page)
	size := int(req.PerPage)

	// 查询数据
	model := forbid.NewForbidIpsModel()
	items, total, err := model.Page(page, size, opts...)
	if err != nil {
		log.WithContextErrorf(ctx, "查询禁扫IP列表失败: %v", err)
		return errors.InternalServerError("forbid_ips", "系统错误")
	}

	// 构建响应
	rsp.Total = int64(total)
	rsp.PerPage = req.PerPage
	rsp.CurrentPage = req.Page
	if total > 0 && size > 0 {
		rsp.LastPage = int32((total + int64(size) - 1) / int64(size))
	} else {
		rsp.LastPage = 1
	}
	rsp.From = (req.Page-1)*req.PerPage + 1
	if rsp.From < 1 {
		rsp.From = 1
	}
	rsp.To = req.Page * req.PerPage
	if rsp.To > int32(total) {
		rsp.To = int32(total)
	}

	// 转换数据
	rsp.Items = make([]*pb.ForbidIpItem, 0, len(items))
	for _, item := range items {
		ipItem := &pb.ForbidIpItem{
			Id:        int64(item.Id),
			IpSegment: item.IpSegment,
			UserId:    int64(item.UserId),
		}

		if item.CompanyId > 0 {
			companyID := int64(item.CompanyId)
			ipItem.CompanyId = &companyID
		}

		if item.CreatedAt.Valid {
			ipItem.CreatedAt = item.CreatedAt.Time.Format("2006-01-02 15:04:05")
		}
		if item.UpdatedAt.Valid {
			ipItem.UpdatedAt = item.UpdatedAt.Time.Format("2006-01-02 15:04:05")
		}
		if item.DeletedAt.Valid {
			ipItem.DeletedAt = item.DeletedAt.Time.Format("2006-01-02 15:04:05")
		}

		rsp.Items = append(rsp.Items, ipItem)
	}

	return nil
}

// AddForbidIps 添加禁扫IP
func AddForbidIps(ctx context.Context, req *pb.AddForbidIpsRequest, rsp *pb.AddForbidIpsResponse) error {
	log.WithContextInfof(ctx, "Received forbid.AddForbidIps request: %v", req)

	// 获取用户ID和企业ID
	userID := req.UserId
	var companyID int64
	if req.OperateCompanyId != nil {
		companyID = *req.OperateCompanyId
	}

	// 检查参数
	if len(req.Ips) == 0 {
		return errors.BadRequest("forbid_ips", "IP段不能为空")
	}

	// 限制IP数量
	if len(req.Ips) > 100 {
		return errors.BadRequest("forbid_ips", "单次填写的IP数量最多为100个，请去掉多余的重新填写")
	}

	// 验证IP格式
	validIPs := make([]string, 0, len(req.Ips))
	for _, ip := range req.Ips {
		// 去除空格
		ip = removeSpaces(ip)

		// 验证是否为有效IP
		if !isValidIPv4(ip) {
			return errors.BadRequest("forbid_ips", "不支持添加IP段或者CIDR格式的IP,只能填写单个IP，当前不合格的IP为："+ip)
		}

		validIPs = append(validIPs, ip)
	}

	// 去重
	validIPs = uniqueStrings(validIPs)
	if len(validIPs) == 0 {
		return errors.BadRequest("forbid_ips", "数据为空，请重新填写!")
	}

	// 查询已存在的禁扫IP
	model := forbid.NewForbidIpsModel()
	existingIPs, err := model.FindByUserID(uint64(userID))
	if err != nil {
		log.WithContextErrorf(ctx, "查询用户禁扫IP失败: %v", err)
		return errors.InternalServerError("forbid_ips", "系统错误")
	}

	// 提取已存在IP段
	existingIPMap := make(map[string]struct{})
	for _, ip := range existingIPs {
		existingIPMap[ip.IpSegment] = struct{}{}
	}

	// 构建新增数据
	now := time.Now()
	ips := make([]*forbid.ForbidIps, 0)
	for _, ip := range validIPs {
		// 检查IP是否已存在
		if _, exists := existingIPMap[ip]; !exists {
			ips = append(ips, &forbid.ForbidIps{
				UserId:    uint64(userID),
				CompanyId: uint64(companyID),
				IpSegment: ip,
				CreatedAt: sql.NullTime{Time: now, Valid: true},
				UpdatedAt: sql.NullTime{Time: now, Valid: true},
			})
		}
	}

	// 如果没有新增IP，直接返回成功
	if len(ips) == 0 {
		rsp.Success = true
		rsp.Count = 0
		return nil
	}

	// 保存数据
	err = model.BatchCreate(ips)
	if err != nil {
		log.WithContextErrorf(ctx, "添加禁扫IP失败: %v", err)
		return errors.InternalServerError("forbid_ips", "系统错误")
	}

	// 构建响应
	rsp.Success = true
	rsp.Count = int32(len(ips))

	// 记录操作日志 (使用异步任务)
	clientIP := metadata.MustString(ctx, "client_ip")
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(req.UserId),
		"添加禁扫IP",
		clientIP,
		uint64(companyID),
		operate_logs.SYSTEM_MANAGE,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "记录操作日志失败: %v", err)
	}

	return nil
}

// 去除字符串中的空格
func removeSpaces(s string) string {
	// 去除半角空格
	s = strings.ReplaceAll(s, " ", "")
	// 去除全角空格
	s = strings.ReplaceAll(s, "　", "")
	// 去除不间断空格
	s = strings.ReplaceAll(s, "\u00A0", "")
	return s
}

// 判断是否为有效的IPv4地址
func isValidIPv4(ip string) bool {
	// 使用net包验证IP地址
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}
	// 确保是IPv4地址
	return parsedIP.To4() != nil
}

// 字符串数组去重
func uniqueStrings(strs []string) []string {
	seen := make(map[string]struct{}, len(strs))
	result := make([]string, 0, len(strs))

	for _, str := range strs {
		if _, exists := seen[str]; !exists {
			seen[str] = struct{}{}
			result = append(result, str)
		}
	}

	return result
}

// DeleteForbidIps 删除禁扫IP
func DeleteForbidIps(ctx context.Context, req *pb.DeleteForbidIpsRequest, rsp *pb.DeleteForbidIpsResponse) error {
	log.WithContextInfof(ctx, "Received forbid.DeleteForbidIps request: %v", req)

	// 获取用户ID和企业ID
	userID := req.UserId
	var companyID int64
	if req.OperateCompanyId != nil {
		companyID = *req.OperateCompanyId
	}

	// 检查参数
	if len(req.Ids) == 0 {
		return errors.BadRequest("forbid_ips", "ID不能为空")
	}

	// 转换ID
	ids := make([]uint64, 0, len(req.Ids))
	for _, id := range req.Ids {
		ids = append(ids, uint64(id))
	}

	// 删除数据
	model := forbid.NewForbidIpsModel()
	err := model.DeleteByIds(uint64(userID), ids...)
	if err != nil {
		log.WithContextErrorf(ctx, "删除禁扫IP失败: %v", err)
		return errors.InternalServerError("forbid_ips", "系统错误")
	}

	// 构建响应
	rsp.Success = true

	// 记录操作日志 (使用异步任务)
	clientIP := metadata.MustString(ctx, "client_ip")
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(req.UserId),
		"删除禁扫IP",
		clientIP,
		uint64(companyID),
		operate_logs.SYSTEM_MANAGE,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "记录操作日志失败: %v", err)
	}
	return nil
}
