package cloud_recommend

import (
	"context"
	"strconv"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"go-micro.dev/v4/metadata"

	testcommon "micro-service/initialize/common_test"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/olivere/elastic"
)

const (
	indexName = "foradar_recommend_result"
	docType   = "result"
)

func InitDeleteTest() {
	cfg.InitLoadCfg()
	log.Init()

	mock := es.NewMockServer()

	// 注册索引存在检查Mock
	mock.Register("/"+indexName, map[string]interface{}{
		indexName: map[string]interface{}{
			"mappings": map[string]interface{}{
				docType: map[string]interface{}{
					"properties": map[string]interface{}{},
				},
			},
		},
	})

	// 注册计数查询Mock
	countResponse := elastic.CountResponse{
		Count: 2,
	}
	mock.Register("/"+indexName+"/_count", countResponse)

	// 注册删除操作Mock
	deleteResponse := elastic.BulkIndexByScrollResponse{
		Total:   2,
		Batches: 1,
		Deleted: 2,
	}
	mock.Register("/"+indexName+"/"+docType+"/_delete_by_query", deleteResponse)

	// 注册搜索Mock
	var searchHits []*elastic.SearchHit
	for i := 1; i <= 2; i++ {
		testRecord := map[string]interface{}{
			"id":      "test_id_" + strconv.Itoa(i),
			"user_id": 1,
			"flag":    "test_flag",
			"ip": map[string]interface{}{
				"ip_raw": "192.168.1." + strconv.Itoa(i),
			},
		}
		searchHits = append(searchHits, &elastic.SearchHit{
			Index:  indexName,
			Type:   docType,
			Id:     "test_id_" + strconv.Itoa(i),
			Source: utils.ToJSON(testRecord),
		})
	}
	mock.Register("/"+indexName+"/"+docType+"/_search", searchHits)

	mock.RegisterBulk()
	mockClient := mock.NewElasticMockClient()

	es.SetTestEnv(true)
	mysql.SetTestEnv(true)
	redis.SetTestEnv(true)

	// 设置mock客户端到testcommon，这样GetEsClient()就能获取到正确的客户端
	testcommon.SetElasticClient(mockClient)

	es.GetInstance(cfg.LoadElastic())
	mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

func TestDeleteRecommendResult(t *testing.T) {
	InitDeleteTest()

	// 设置Mock期望
	mysqlMock := mysql.GetMockInstance()
	mysqlMock.ExpectQuery("SELECT .* FROM `cron` WHERE").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	// Redis Mock期望已在InitUnified()中设置

	ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

	req := &pb.DeleteRecommendResultRequest{
		UserId:    1,
		CompanyId: 1,
		Flag:      "test_flag",
		IpArray:   []string{"***********", "***********"},
		Whole:     false,
	}
	rsp := &pb.DeleteRecommendResultResponse{}

	err := DeleteRecommendResult(ctx, req, rsp)

	assert.NoError(t, err)
	assert.GreaterOrEqual(t, rsp.Count, int32(0))
}

func TestDeleteRecommendResult_WholeTrue(t *testing.T) {
	InitUnified()

	// 设置Mock期望
	mysqlMock := mysql.GetMockInstance()
	mysqlMock.ExpectQuery("SELECT .* FROM `cron` WHERE").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	// Redis Mock期望已在InitUnified()中设置

	ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

	req := &pb.DeleteRecommendResultRequest{
		UserId:    1,
		CompanyId: 1,
		Flag:      "test_flag",
		IpArray:   []string{},
		Whole:     true,
	}
	rsp := &pb.DeleteRecommendResultResponse{}

	err := DeleteRecommendResult(ctx, req, rsp)

	assert.NoError(t, err)
	assert.GreaterOrEqual(t, rsp.Count, int32(0))
}

func TestDeleteRecommendResult_EmptyIpArray(t *testing.T) {
	InitUnified()

	// 设置Mock期望
	mysqlMock := mysql.GetMockInstance()
	mysqlMock.ExpectQuery("SELECT .* FROM `cron` WHERE").
		WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

	// Redis Mock期望已在InitUnified()中设置

	ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

	req := &pb.DeleteRecommendResultRequest{
		UserId:    1,
		CompanyId: 1,
		Flag:      "test_flag",
		IpArray:   []string{},
		Whole:     false,
	}
	rsp := &pb.DeleteRecommendResultResponse{}

	err := DeleteRecommendResult(ctx, req, rsp)

	assert.NoError(t, err)
	assert.GreaterOrEqual(t, rsp.Count, int32(0))
}
