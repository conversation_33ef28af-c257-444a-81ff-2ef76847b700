package cloud_recommend

import (
	"context"
	"strconv"
	"testing"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"go-micro.dev/v4/metadata"

	testcommon "micro-service/initialize/common_test"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

const (
	groupIpIndex = "foradar_recommend_result"
	groupIpType  = "result"
)

func InitGroupIpResults() {
	cfg.InitLoadCfg()
	log.Init()

	mock := es.NewMockServer()

	// 注册推荐结果搜索Mock - 返回分组结果
	testResults := []map[string]interface{}{
		{
			"id":                        "1",
			"user_id":                   1,
			"flag":                      "test_flag",
			"ip":                        "***********",
			"port":                      80,
			"protocol":                  "http",
			"title":                     "测试网站1",
			"domain":                    "example1.com",
			"subdomain":                 "www.example1.com",
			"cert":                      "CN=example1.com",
			"icp":                       "京ICP备12345678号",
			"url":                       "http://www.example1.com",
			"created_at":                "2024-01-01 12:00:00",
			"updated_at":                "2024-01-01 12:00:00",
			"source_updated_at":         "2024-01-01 12:00:00",
			"online_state":              true,
			"status":                    0,
			"audit":                     0,
			"assets_source":             1,
			"product":                   "Apache",
			"group_id":                  1,
			"assets_confidence_level":   2,
			"level":                     3,
			"cloud_name":                "阿里云",
			"organization_company_id":   100,
			"organization_company_name": "测试公司",
			"all_company_name":          []string{"测试公司1"},
			"clue_company_name":         []string{"测试公司1"},
			"logo": map[string]interface{}{
				"hash":    -123456,
				"content": "/storage/logos/test1.ico",
			},
			"reason": []map[string]interface{}{
				{
					"id":                "1",
					"group_id":          1,
					"source":            4,
					"type":              0,
					"content":           "example1.com",
					"clue_company_name": "测试公司1",
				},
			},
		},
		{
			"id":                        "2",
			"user_id":                   1,
			"flag":                      "test_flag",
			"ip":                        "***********",
			"port":                      443,
			"protocol":                  "https",
			"title":                     "测试网站2",
			"domain":                    "example1.com",
			"subdomain":                 "api.example1.com",
			"cert":                      "CN=example1.com",
			"icp":                       "京ICP备12345678号",
			"url":                       "https://api.example1.com",
			"created_at":                "2024-01-01 13:00:00",
			"updated_at":                "2024-01-01 13:00:00",
			"source_updated_at":         "2024-01-01 13:00:00",
			"online_state":              true,
			"status":                    0,
			"audit":                     0,
			"assets_source":             1,
			"product":                   "Nginx",
			"group_id":                  1,
			"assets_confidence_level":   2,
			"level":                     3,
			"cloud_name":                "阿里云",
			"organization_company_id":   100,
			"organization_company_name": "测试公司",
			"all_company_name":          []string{"测试公司1"},
			"clue_company_name":         []string{"测试公司1"},
			"logo": map[string]interface{}{
				"hash":    -789012,
				"content": "/storage/logos/test2.ico",
			},
			"reason": []map[string]interface{}{
				{
					"id":                "2",
					"group_id":          1,
					"source":            4,
					"type":              0,
					"content":           "example1.com",
					"clue_company_name": "测试公司1",
				},
			},
		},
		{
			"id":                        "3",
			"user_id":                   1,
			"flag":                      "test_flag",
			"ip":                        "***********",
			"port":                      80,
			"protocol":                  "http",
			"title":                     "测试网站3",
			"domain":                    "example2.com",
			"subdomain":                 "www.example2.com",
			"cert":                      "CN=example2.com",
			"icp":                       "沪ICP备87654321号",
			"url":                       "http://www.example2.com",
			"created_at":                "2024-01-02 12:00:00",
			"updated_at":                "2024-01-02 12:00:00",
			"source_updated_at":         "2024-01-02 12:00:00",
			"online_state":              true,
			"status":                    1,
			"audit":                     1,
			"assets_source":             2,
			"product":                   "Apache",
			"group_id":                  2,
			"assets_confidence_level":   1,
			"level":                     1,
			"cloud_name":                "腾讯云",
			"organization_company_id":   200,
			"organization_company_name": "测试公司2",
			"all_company_name":          []string{"测试公司2"},
			"clue_company_name":         []string{"测试公司2"},
			"logo": map[string]interface{}{
				"hash":    -345678,
				"content": "/storage/logos/test3.ico",
			},
			"reason": []map[string]interface{}{
				{
					"id":                "3",
					"group_id":          2,
					"source":            4,
					"type":              1,
					"content":           "example2.com",
					"clue_company_name": "测试公司2",
				},
			},
		},
	}

	var searchHits []*elastic.SearchHit
	for i, result := range testResults {
		searchHits = append(searchHits, &elastic.SearchHit{
			Index:  groupIpIndex,
			Type:   groupIpType,
			Id:     strconv.Itoa(i + 1),
			Source: utils.ToJSON(result),
		})
	}

	// 注册搜索路径，包含类型名
	mock.Register("/"+groupIpIndex+"/"+groupIpType+"/_search", searchHits)
	// 也注册不包含类型名的路径，以防万一
	mock.Register("/"+groupIpIndex+"/_search", searchHits)

	// 注册资产状态查询Mock
	assetList := []fofaee_assets.FofaeeAssets{
		{
			Id:     "1_***********",
			Ip:     "***********",
			Status: utils.ToPointer(1),
		},
		{
			Id:     "1_***********",
			Ip:     "***********",
			Status: utils.ToPointer(0),
		},
	}

	var assetHits []*elastic.SearchHit
	for _, asset := range assetList {
		assetHits = append(assetHits, &elastic.SearchHit{
			Index:  fofaee_assets.FofaeeAssetsIndex,
			Type:   fofaee_assets.FofaeeAssetsType,
			Id:     asset.Id,
			Source: utils.ToJSON(asset),
		})
	}

	mock.Register("/"+fofaee_assets.FofaeeAssetsIndex+"/_search", assetHits)

	mock.RegisterBulk()
	mockClient := mock.NewElasticMockClient()

	// 设置测试环境标志
	es.SetTestEnv(true)
	mysql.SetTestEnv(true)
	redis.ForceTest(true)       // 强制使用Redis Mock
	testcommon.SetTestEnv(true) // 确保testcommon也知道这是测试环境

	// 设置mock客户端到testcommon，这样GetEsClient()就能获取到正确的客户端
	testcommon.SetElasticClient(mockClient)

	es.GetInstance(cfg.LoadElastic())
	mysql.GetInstance(cfg.LoadMysql())
	// redis.GetInstance(cfg.LoadRedis()) // 不需要再调用，ForceTest已经初始化了

	// 设置足够的Redis Mock期望，支持多个测试用例
	redisMock := redis.GetMockInstance()
	redisMock.MatchExpectationsInOrder(false)
	for i := 0; i < 20; i++ { // 设置足够多的期望
		redisMock.Regexp().ExpectRPush("foradar:asyncq", ".*").SetVal(1)
	}
}

func TestGroupIpResults(t *testing.T) {
	InitGroupIpResults()

	// 设置MySQL期望 - 这些期望已经在InitUnified()中设置了
	// mysqlMock := mysql.GetMockInstance()
	// mysqlMock.ExpectQuery("SELECT .* FROM `clues` WHERE").
	//	WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "group_id", "parent_id", "status", "content"}))

	t.Run("基础查询-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId: 1,
			Flag:   "test_flag",
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带IP过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId: 1,
			Flag:   "test_flag",
			Ip:     "***********",
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带关键词查询-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:  1,
			Flag:    "test_flag",
			Keyword: "测试",
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带线索公司名称过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:          1,
			Flag:            "test_flag",
			ClueCompanyName: "测试公司1",
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带域名过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId: 1,
			Flag:   "test_flag",
			Domain: []string{"example1.com", "example2.com"},
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带端口过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId: 1,
			Flag:   "test_flag",
			Port:   []string{"80", "443"},
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带协议过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:   1,
			Flag:     "test_flag",
			Protocol: []string{"http", "https"},
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带IP数组过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:  1,
			Flag:    "test_flag",
			IpArray: []string{"***********", "***********"},
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带时间范围过滤-一年内", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:        1,
			Flag:          "test_flag",
			DateRangeType: 1, // 一年内
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带时间范围过滤-半年内", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:        1,
			Flag:          "test_flag",
			DateRangeType: 2, // 半年内
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带时间范围过滤-三个月内", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:        1,
			Flag:          "test_flag",
			DateRangeType: 3, // 三个月内
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带reason_type过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:     1,
			Flag:       "test_flag",
			ReasonType: 1,
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带organization_company_id过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:                1,
			Flag:                  "test_flag",
			OrganizationCompanyId: 100,
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带assets_confidence_level过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId:                1,
			Flag:                  "test_flag",
			AssetsConfidenceLevel: []int32{1, 2},
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带level过滤-成功", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId: 1,
			Flag:   "test_flag",
			Level:  []int32{1, 2, 3},
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})

	t.Run("带level特殊值-1测试", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.GroupIpResultsRequest{
			UserId: 1,
			Flag:   "test_flag",
			Level:  []int32{-1}, // 特殊值，表示level参数存在但值为空
		}
		rsp := &pb.GroupIpResultsResponse{}

		err := GroupIpResults(ctx, req, rsp)

		assert.NoError(t, err)
		assert.NotNil(t, rsp)
	})
}

// TestAddIpStatusToResults 测试将IP状态添加到结果中
func TestAddIpStatusToResults(t *testing.T) {
	tests := []struct {
		name            string
		groupedResults  map[string][]map[string]interface{}
		ipStatusMap     map[string]int
		expectedResults map[string][]map[string]interface{}
		description     string
	}{
		{
			name: "正常情况-所有IP都有状态",
			groupedResults: map[string][]map[string]interface{}{
				"***********": {
					{"id": "1", "ip": "***********", "port": 80},
					{"id": "2", "ip": "***********", "port": 443},
				},
				"***********": {
					{"id": "3", "ip": "***********", "port": 80},
				},
			},
			ipStatusMap: map[string]int{
				"***********": 1,
				"***********": 0,
			},
			expectedResults: map[string][]map[string]interface{}{
				"***********": {
					{"id": "1", "ip": "***********", "port": 80, "ip_status": 1},
					{"id": "2", "ip": "***********", "port": 443, "ip_status": 1},
				},
				"***********": {
					{"id": "3", "ip": "***********", "port": 80, "ip_status": 0},
				},
			},
			description: "所有IP在状态映射中都存在，应该添加ip_status字段",
		},
		{
			name: "部分IP有状态",
			groupedResults: map[string][]map[string]interface{}{
				"***********": {
					{"id": "1", "ip": "***********", "port": 80},
				},
				"***********": {
					{"id": "2", "ip": "***********", "port": 80},
				},
				"***********": {
					{"id": "3", "ip": "***********", "port": 80},
				},
			},
			ipStatusMap: map[string]int{
				"***********": 1,
				"***********": 0,
			},
			expectedResults: map[string][]map[string]interface{}{
				"***********": {
					{"id": "1", "ip": "***********", "port": 80, "ip_status": 1},
				},
				"***********": {
					{"id": "2", "ip": "***********", "port": 80},
				},
				"***********": {
					{"id": "3", "ip": "***********", "port": 80, "ip_status": 0},
				},
			},
			description: "只有部分IP在状态映射中存在，其他IP不应该添加ip_status字段",
		},
		{
			name: "空的IP状态映射",
			groupedResults: map[string][]map[string]interface{}{
				"***********": {
					{"id": "1", "ip": "***********", "port": 80},
				},
			},
			ipStatusMap: map[string]int{},
			expectedResults: map[string][]map[string]interface{}{
				"***********": {
					{"id": "1", "ip": "***********", "port": 80},
				},
			},
			description: "IP状态映射为空，不应该添加任何ip_status字段",
		},
		{
			name:           "空的分组结果",
			groupedResults: map[string][]map[string]interface{}{},
			ipStatusMap: map[string]int{
				"***********": 1,
			},
			expectedResults: map[string][]map[string]interface{}{},
			description:     "分组结果为空，不会有任何操作",
		},
		{
			name: "单个IP多条记录",
			groupedResults: map[string][]map[string]interface{}{
				"***********": {
					{"id": "1", "ip": "***********", "port": 80, "title": "网站1"},
					{"id": "2", "ip": "***********", "port": 443, "title": "网站2"},
					{"id": "3", "ip": "***********", "port": 8080, "title": "网站3"},
				},
			},
			ipStatusMap: map[string]int{
				"***********": 1,
			},
			expectedResults: map[string][]map[string]interface{}{
				"***********": {
					{"id": "1", "ip": "***********", "port": 80, "title": "网站1", "ip_status": 1},
					{"id": "2", "ip": "***********", "port": 443, "title": "网站2", "ip_status": 1},
					{"id": "3", "ip": "***********", "port": 8080, "title": "网站3", "ip_status": 1},
				},
			},
			description: "单个IP有多条记录，所有记录都应该添加相同的ip_status",
		},
		{
			name: "nil的分组结果记录",
			groupedResults: map[string][]map[string]interface{}{
				"***********": nil,
			},
			ipStatusMap: map[string]int{
				"***********": 1,
			},
			expectedResults: map[string][]map[string]interface{}{
				"***********": nil,
			},
			description: "分组结果中的记录为nil，不会崩溃",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行函数
			addIpStatusToResults(tt.groupedResults, tt.ipStatusMap)

			// 验证结果
			assert.Equal(t, tt.expectedResults, tt.groupedResults, tt.description)
		})
	}
}

// TestGetStateText 测试获取状态文本
func TestGetStateText(t *testing.T) {
	tests := []struct {
		name        string
		onlineState int
		expected    string
		description string
	}{
		{
			name:        "在线状态",
			onlineState: 1,
			expected:    "在线",
			description: "当状态为1时应该返回在线",
		},
		{
			name:        "离线状态-0",
			onlineState: 0,
			expected:    "离线",
			description: "当状态为0时应该返回离线",
		},
		{
			name:        "离线状态-负数",
			onlineState: -1,
			expected:    "离线",
			description: "当状态为负数时应该返回离线",
		},
		{
			name:        "离线状态-大于1",
			onlineState: 2,
			expected:    "离线",
			description: "当状态为大于1的数时应该返回离线",
		},
		{
			name:        "离线状态-极大值",
			onlineState: 999999,
			expected:    "离线",
			description: "当状态为极大值时应该返回离线",
		},
		{
			name:        "离线状态-极小值",
			onlineState: -999999,
			expected:    "离线",
			description: "当状态为极小值时应该返回离线",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getStateText(tt.onlineState)
			assert.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestAddIpStatusToResults_EdgeCases 测试边界情况
func TestAddIpStatusToResults_EdgeCases(t *testing.T) {
	t.Run("nil输入", func(t *testing.T) {
		// 测试nil输入不会崩溃
		assert.NotPanics(t, func() {
			addIpStatusToResults(nil, nil)
		})

		assert.NotPanics(t, func() {
			addIpStatusToResults(make(map[string][]map[string]interface{}), nil)
		})

		assert.NotPanics(t, func() {
			addIpStatusToResults(nil, make(map[string]int))
		})
	})

	t.Run("空记录列表", func(t *testing.T) {
		groupedResults := map[string][]map[string]interface{}{
			"***********": {},
		}
		ipStatusMap := map[string]int{
			"***********": 1,
		}

		// 不应该崩溃
		assert.NotPanics(t, func() {
			addIpStatusToResults(groupedResults, ipStatusMap)
		})

		// 结果应该保持不变
		assert.Equal(t, map[string][]map[string]interface{}{
			"***********": {},
		}, groupedResults)
	})

	t.Run("记录为nil的情况", func(t *testing.T) {
		groupedResults := map[string][]map[string]interface{}{
			"***********": {
				nil,
				{"id": "1", "ip": "***********"},
			},
		}
		ipStatusMap := map[string]int{
			"***********": 1,
		}

		// 当记录为nil时会崩溃，这是预期的行为
		assert.Panics(t, func() {
			addIpStatusToResults(groupedResults, ipStatusMap)
		})
	})

	t.Run("只有非nil记录的情况", func(t *testing.T) {
		groupedResults := map[string][]map[string]interface{}{
			"***********": {
				{"id": "1", "ip": "***********"},
				{"id": "2", "ip": "***********"},
			},
		}
		ipStatusMap := map[string]int{
			"***********": 1,
		}

		// 不应该崩溃
		assert.NotPanics(t, func() {
			addIpStatusToResults(groupedResults, ipStatusMap)
		})

		// 验证记录被正确处理
		assert.Equal(t, 1, groupedResults["***********"][0]["ip_status"])
		assert.Equal(t, 1, groupedResults["***********"][1]["ip_status"])
	})
}
