package cloud_recommend

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/pkg/utils"

	es "micro-service/middleware/elastic"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	asyncq "micro-service/pkg/queue_helper"
	pb "micro-service/webService/proto"

	"github.com/olivere/elastic"
)

// DeleteRecommendResult 删除推荐结果
func DeleteRecommendResult(ctx context.Context, req *pb.DeleteRecommendResultRequest, rsp *pb.DeleteRecommendResultResponse) error {
	log.WithContextInfof(ctx, "接收到删除推荐结果请求: %+v", req)

	// 构建查询参数
	ipList := req.IpArray
	if req.Whole {
		ipList = []string{}
	}
	log.WithContextInfof(ctx, "添加IP过滤条件: %v", ipList)

	// 获取ES客户端
	client := es.GetEsClient()

	// 构建基础查询条件
	baseQuery := elastic.NewBoolQuery().
		Must(elastic.NewTermQuery("user_id", req.UserId)).
		Must(elastic.NewTermQuery("flag", req.Flag))

	// 如果指定了IP数组，则按IP数组过滤
	if len(req.IpArray) > 0 {
		baseQuery.Must(elastic.NewTermsQuery("ip.ip_raw", utils.StringSliceToInterfaceSlice(req.IpArray)...))
	}

	// 获取查询的JSON表示
	querySource, err := baseQuery.Source()
	if err != nil {
		log.WithContextErrorf(ctx, "获取查询Source失败: %v", err)
	} else {
		queryJSON, _ := json.Marshal(querySource)
		log.WithContextInfof(ctx, "ES查询JSON: %s", string(queryJSON))
	}

	// 执行删除操作前检查索引是否存在
	exists, err := client.IndexExists("foradar_recommend_result").Do(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "检查索引是否存在失败: %v", err)
	} else {
		log.WithContextInfof(ctx, "索引foradar_recommend_result是否存在: %v", exists)
	}

	// 先检查是否有符合条件的记录
	countResult, err := client.Count("foradar_recommend_result").Query(baseQuery).Do(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "查询符合条件的记录数失败: %v", err)
	} else {
		log.WithContextInfof(ctx, "符合条件的记录数: %d", countResult)
	}

	// 直接使用DeleteByQuery批量删除
	deleteResult, err := client.DeleteByQuery().
		Index("foradar_recommend_result").
		Type("result").
		Query(baseQuery).
		Refresh("true").
		Do(ctx)

	if err != nil {
		log.WithContextErrorf(ctx, "批量删除推荐结果失败: %v", err)

		// 如果批量删除失败，尝试先查询出所有符合条件的IP，然后按IP批量删除
		log.WithContextInfof(ctx, "批量删除失败，尝试先查询出符合条件的IP，然后按IP批量删除...")

		// 查询符合条件的记录
		searchResult, err := client.Search().
			Index("foradar_recommend_result").
			Type("result").
			Query(baseQuery).
			Size(10000). // 设置较大的size以获取更多记录
			Do(ctx)

		if err != nil {
			log.WithContextErrorf(ctx, "查询符合条件的记录失败: %v", err)
			return err
		}

		// 收集所有不同的IP
		ipMap := make(map[string]bool)
		for _, hit := range searchResult.Hits.Hits {
			var record map[string]interface{}
			if err := json.Unmarshal(*hit.Source, &record); err != nil {
				log.WithContextErrorf(ctx, "解析记录内容失败: %v", err)
				continue
			}

			// 获取IP字段
			if ip, ok := record["ip"]; ok {
				ipStr, ok := ip.(string)
				if ok {
					ipMap[ipStr] = true
				}
			}
		}

		// 将IP转换为切片
		var uniqueIPs []interface{}
		for ip := range ipMap {
			uniqueIPs = append(uniqueIPs, ip)
		}

		log.WithContextInfof(ctx, "找到 %d 个不同的IP: %v", len(uniqueIPs), uniqueIPs)

		if len(uniqueIPs) > 0 {
			// 构建新的查询，使用收集到的IP
			ipQuery := elastic.NewBoolQuery().
				Must(elastic.NewTermQuery("user_id", req.UserId)).
				Must(elastic.NewTermQuery("flag", req.Flag)).
				Must(elastic.NewTermsQuery("ip.ip_raw", uniqueIPs...))

			// 执行删除
			ipDeleteResult, err := client.DeleteByQuery().
				Index("foradar_recommend_result").
				Type("result").
				Query(ipQuery).
				Refresh("true").
				Do(ctx)

			if err != nil {
				log.WithContextErrorf(ctx, "使用收集到的IP删除失败: %v", err)
				return err
			}

			deleteResult = ipDeleteResult
			log.WithContextInfof(ctx, "使用收集到的IP删除结果: 总删除数=%d, 批次数=%d",
				deleteResult.Total, deleteResult.Batches)
		} else {
			log.WithContextInfof(ctx, "未找到符合条件的IP，无法执行删除")
			return fmt.Errorf("未找到符合条件的IP，无法执行删除")
		}
	}

	log.WithContextInfof(ctx, "删除操作详情: 总删除数=%d, 批次数=%d", deleteResult.Total, deleteResult.Batches)

	// 记录操作日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(req.UserId),
		fmt.Sprintf("删除推荐结果，标识：%s", req.Flag),
		metadata.MustString(ctx, "client_ip"),
		uint64(req.CompanyId),
		operate_logs.KNOWN_ASSETS,
		operate_logs.TYPE_OPERATE,
	))

	if err != nil {
		log.WithContextErrorf(ctx, "记录操作日志失败: %v", err)
	}

	// 设置响应
	rsp.Count = int32(deleteResult.Total)

	log.WithContextInfof(ctx, "成功删除 %d 条推荐结果", deleteResult.Total)
	return nil
}
