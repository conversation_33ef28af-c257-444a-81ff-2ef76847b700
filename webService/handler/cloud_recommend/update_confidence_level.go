package cloud_recommend

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/pkg/utils"
	"strings"
	"time"

	"github.com/olivere/elastic"

	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/black_keyword_system"
	"micro-service/middleware/mysql/black_keyword_type"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	asyncq "micro-service/pkg/queue_helper"
	pb "micro-service/webService/proto"
)

// UpdateAssetsConfidenceLevel 更新资产可信度
func UpdateAssetsConfidenceLevel(ctx context.Context, req *pb.UpdateAssetsConfidenceLevelRequest, rsp *pb.UpdateAssetsConfidenceLevelResponse) error {
	log.WithContextInfof(ctx, "接收到更新资产可信度请求: %+v", req)

	// 参数检查
	if req.SetAssetsConfidenceLevel <= 0 {
		return fmt.Errorf("缺少可信度级别参数")
	}

	// 构建查询条件
	baseQuery := elastic.NewBoolQuery().
		Must(elastic.NewTermQuery("user_id", req.UserId)).
		Must(elastic.NewTermQuery("flag", req.Flag))

	// 如果指定了IP数组，则按IP数组过滤
	if len(req.IpArray) > 0 && len(req.IpArray[0]) > 0 {
		baseQuery.Must(elastic.NewTermsQuery("ip.ip_raw", utils.StringSliceToInterfaceSlice(req.IpArray)...))
	}

	// 获取ES客户端
	client := es.GetEsClient()

	// 查询符合条件的记录数
	countResult, err := client.Count("foradar_recommend_result").Query(baseQuery).Do(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "查询符合条件的记录数失败: %v", err)
		return err
	}
	log.WithContextInfof(ctx, "符合条件的记录数: %d", countResult)

	// 准备更新的字段
	updateData := map[string]interface{}{
		"assets_confidence_level": req.SetAssetsConfidenceLevel,
		"updated_at":              time.Now().Format("2006-01-02 15:04:05"),
	}

	// 根据可信度级别设置不同的资产级别和其他属性
	if req.SetAssetsConfidenceLevel == recommend_result.LOW {
		// 低可信度，标记为D级资产
		updateData["level"] = recommend_result.AssetsLevelD

		// 查询符合条件的记录，用于后续威胁词库匹配
		searchResult, err := client.Search().
			Index("foradar_recommend_result").
			Type("result").
			Query(baseQuery).
			Size(1000). // 设置较大的size以获取更多记录
			Do(ctx)

		if err != nil {
			log.WithContextErrorf(ctx, "查询符合条件的记录失败: %v", err)
			return err
		}

		// 获取威胁词库关键词
		blackTitles := getBlackTitles(ctx)
		log.WithContextInfof(ctx, "获取到威胁词库关键词数量: %d", len(blackTitles))

		// 获取威胁类型映射
		typeNameMap, err := getBlackKeywordTypeMap(ctx)
		if err != nil {
			log.WithContextErrorf(ctx, "获取威胁类型映射失败: %v", err)
			return err
		}

		// 处理每条记录，检查是否命中威胁词库
		for _, hit := range searchResult.Hits.Hits {
			var record map[string]interface{}
			if err := json.Unmarshal(*hit.Source, &record); err != nil {
				log.WithContextErrorf(ctx, "解析记录内容失败: %v", err)
				continue
			}

			// 获取标题
			title, ok := record["title"].(string)
			if !ok || title == "" {
				continue
			}

			// 检查是否命中威胁词库
			for keyword, typeID := range blackTitles {
				if strings.Contains(title, keyword) || title == keyword {
					// 命中威胁词库，更新记录
					typeName := typeNameMap[typeID]
					if typeName == "" {
						typeName = "其他"
					}

					// 构建更新请求
					updateFields := map[string]interface{}{
						"is_fake_assets":     true,
						"threaten_type":      typeID,
						"threaten_type_name": typeName,
						"updated_at":         time.Now().Format("2006-01-02 15:04:05"),
					}

					// 更新单条记录
					_, err := client.Update().
						Index("foradar_recommend_result").
						Type("result").
						Id(hit.Id).
						Doc(updateFields).
						Do(ctx)

					if err != nil {
						log.WithContextErrorf(ctx, "更新记录威胁信息失败, ID: %s, 错误: %v", hit.Id, err)
					} else {
						log.WithContextInfof(ctx, "更新记录威胁信息成功, ID: %s, 标题: %s, 威胁类型: %s", hit.Id, title, typeName)
					}
					break
				}
			}
		}
	} else {
		// 高可信度或中等可信度
		if req.SetAssetsConfidenceLevel == recommend_result.HIGH {
			// 高可信度，标记为A级资产
			updateData["level"] = recommend_result.AssetsLevelA
		} else {
			// 中等可信度，标记为C级资产
			updateData["level"] = recommend_result.AssetsLevelC
		}
		// 清除威胁标记
		updateData["is_fake_assets"] = false
		updateData["threaten_type"] = 0
	}

	// 执行批量更新
	script := elastic.NewScript("ctx._source.assets_confidence_level = params.assets_confidence_level; ctx._source.level = params.level; ctx._source.is_fake_assets = params.is_fake_assets; ctx._source.threaten_type = params.threaten_type; ctx._source.updated_at = params.updated_at;")
	script.Params(updateData)

	updateByQueryService := client.UpdateByQuery("foradar_recommend_result").
		Type("result").
		Query(baseQuery).
		Script(script).
		Refresh("true")

	updateResponse, err := updateByQueryService.Do(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "批量更新资产可信度失败: %v", err)
		return err
	}

	log.WithContextInfof(ctx, "批量更新资产可信度成功，更新记录数: %d", updateResponse.Updated)

	// 记录操作日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(req.UserId),
		fmt.Sprintf("IP维度-更改资产可信度，标识：%s", req.Flag),
		metadata.MustString(ctx, "client_ip"),
		uint64(req.CompanyId),
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))

	if err != nil {
		log.WithContextErrorf(ctx, "记录操作日志失败: %v", err)
	}

	// 设置响应
	rsp.Count = int32(updateResponse.Updated)

	return nil
}

// getBlackTitles 获取威胁词库关键词
func getBlackTitles(ctx context.Context) map[string]int64 {
	// 默认关键词
	defaultKeywords := []string{"金沙娱", "金年会", "智慧投注", "mg娱乐", "MG摆脱", "mile", "春秋彩票", "彩票", "亚美", "彩金", "皇冠体育", "皇冠app", "金莎", "大富豪"}

	// 从数据库一次性获取所有关键词及其类型ID
	blackTitles, err := black_keyword_system.NewModel().ListAllActiveKeywordWithTypeId()
	if err != nil {
		log.WithContextErrorf(ctx, "获取威胁词库关键词失败: %v", err)
		// 使用默认关键词
		blackTitles = make(map[string]int64)
		for _, keyword := range defaultKeywords {
			blackTitles[keyword] = 0 // 默认类型为0
		}
		return blackTitles
	}

	// 添加默认关键词（如果数据库中不存在）
	for _, keyword := range defaultKeywords {
		if _, exists := blackTitles[keyword]; !exists {
			blackTitles[keyword] = 0 // 默认类型为0
		}
	}

	log.WithContextInfof(ctx, "从数据库获取威胁词库关键词成功，数量: %d", len(blackTitles))
	return blackTitles
}

// getBlackKeywordTypeMap 获取威胁类型映射
func getBlackKeywordTypeMap(ctx context.Context) (map[int64]string, error) {
	typeMap := make(map[int64]string)

	// 获取所有类型
	types, _, err := black_keyword_type.NewTypeModel().List(1, 1000,
		mysql.WithWhere("status = ?", black_keyword_type.Enable),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "获取威胁类型列表失败: %v", err)
		return typeMap, err
	}

	// 构建类型ID到名称的映射
	for _, t := range types {
		typeMap[int64(t.Id)] = t.Name
	}

	return typeMap, nil
}
