package cloud_recommend

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql/clues"
	clues2 "micro-service/pkg/clues"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"
)

// CloudRecommendResults 云端推荐结果
// 对应PHP中的results方法
func CloudRecommendResults(ctx context.Context, req *pb.CloudRecommendResultsRequest, rsp *pb.CloudRecommendResultsResponse) error {
	userId := req.UserId
	page := int(req.Page)
	perPage := int(req.PerPage)
	if page <= 0 {
		page = 1
	}
	if perPage <= 0 {
		perPage = 10
	}

	// 打印请求参数
	log.WithContextInfof(ctx, "[云端推荐结果] 请求参数: userId=%d, flag=%s, page=%d, perPage=%d, keyword=%s, ip=%s, confirm=%d, domain=%v, protocol=%v",
		userId, req.Flag, page, perPage, req.Keyword, req.Ip, req.Confirm, req.Domain, req.Protocol)

	// 构建查询条件
	condition := buildSearchCondition(req)

	// 打印查询条件
	log.WithContextInfof(ctx, "[云端推荐结果] 查询条件: %+v", condition)

	// 查询推荐结果
	list, total, err := recommend_result.NewRecommendResultModel().FindByPageCondition(page, perPage, condition)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果] 查询推荐结果失败: %v", err)
		return fmt.Errorf("查询推荐结果失败: %v", err)
	}

	// 打印查询结果数量
	log.WithContextInfof(ctx, "[云端推荐结果] 查询结果: total=%d, 实际返回=%d", total, len(list))

	// 查询所有的线索组ID
	groupIds := make([]uint64, 0)
	for _, item := range list {
		for _, reason := range item.Reason {
			if reason.GroupId > 0 && !slices.Contains(groupIds, uint64(reason.GroupId)) {
				groupIds = append(groupIds, uint64(reason.GroupId))
			}
		}
	}

	// 查询线索列表
	clueList, err := clues.NewCluer().ListAll(func(db *gorm.DB) {
		db.Where("user_id = ?", userId).
			Where("status = ?", clues.CLUE_PASS_STATUS).
			Where("group_id IN ?", groupIds)
	})
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果] 查询线索列表失败: %v", err)
		return fmt.Errorf("查询线索列表失败: %v", err)
	}

	// 收集所有IP
	allIps := make([]string, 0, len(list))
	for _, item := range list {
		if item.Ip != "" {
			allIps = append(allIps, item.Ip)
		}
	}

	// 查询IP资产状态
	ipStatusMap := make(map[string]int)
	if len(allIps) > 0 {
		// 构建查询条件
		assetCondition := fofaee_assets.NewFindCondition()
		assetCondition.UserId = uint64(userId)
		assetCondition.Ids = make([]string, 0, len(allIps))

		// 构建ID列表
		for _, ip := range allIps {
			assetId := fofaee_assets.GenId(int(userId), ip)
			assetCondition.Ids = append(assetCondition.Ids, assetId)
		}

		// 查询资产
		assets, _, err := fofaee_assets.NewFofaeeAssetsModel().FindByCondition(ctx, assetCondition, 1, 1000)
		if err != nil {
			log.WithContextErrorf(ctx, "[云端推荐结果] 查询IP资产状态失败: %v", err)
		} else {
			for _, asset := range assets {
				status := 0
				if asset.Status != nil {
					status = cast.ToInt(asset.Status)
				}
				ipStatusMap[asset.Ip] = status
			}
		}
	}

	// 构建响应数据
	items := make([]*pb.CloudRecommendResultItem, 0, len(list))
	for _, item := range list {
		// 准备将其他信息放入reasons字段
		reasons := clues2.GetReason(item.Reason)

		// 将重要信息添加到reasons中，以便前端能够获取到
		if item.GroupName != "" {
			reasons = append(reasons, fmt.Sprintf("group_name=%s", item.GroupName))
		}
		if item.BaseProtocol != "" {
			reasons = append(reasons, fmt.Sprintf("base_protocol=%s", item.BaseProtocol))
		}
		if item.Cname != "" {
			reasons = append(reasons, fmt.Sprintf("cname=%s", item.Cname))
		}
		if item.Product != "" {
			reasons = append(reasons, fmt.Sprintf("product=%s", item.Product))
		}
		if item.OneforallSource != "" {
			reasons = append(reasons, fmt.Sprintf("oneforall_source=%s", item.OneforallSource))
		}
		if item.CertRaw != "" {
			// CertRaw太长，不添加到reasons
			reasons = append(reasons, "has_cert_raw=true")
		}

		// 添加调试日志
		log.WithContextInfof(ctx, "[云端推荐结果] 处理项目: IP=%s, AssetsConfidenceLevel=%d", item.Ip, item.AssetsConfidenceLevel)

		// 构建基本结构
		resultItem := &pb.CloudRecommendResultItem{
			Id:              item.Id,
			Ip:              item.Ip,
			Port:            cast.ToString(item.Port),
			Protocol:        item.Protocol,
			Title:           item.Title,
			Domain:          item.Domain,
			Subdomain:       item.Subdomain,
			Cert:            item.Cert,
			Icp:             item.Icp,
			Status:          int32(item.Status),
			Audit:           int32(item.Audit),
			OnlineState:     item.OnlineState,
			SourceUpdatedAt: item.SourceUpdatedAt,
			CreatedAt:       item.CreatedAt,
			UpdatedAt:       item.UpdatedAt,
			Reasons:         reasons,
			// 暂时移除ChainList，后面会根据链信息构建
			AssetsSource:          int32(item.AssetsSource),
			OneforallSource:       item.OneforallSource,
			ClueCompanyName:       item.AllCompanyName,
			AllCompanyName:        item.AllCompanyName,
			Product:               item.Product,
			Flag:                  item.Flag,
			GroupId:               int32(item.GroupId),
			IsCdn:                 item.IsCDN,
			CloudName:             item.CloudName,
			AssetsConfidenceLevel: int32(item.AssetsConfidenceLevel),
			Url:                   item.Url,
		}

		// 转换 AllAssetsSource 字段
		allAssetsSource := make([]int32, len(item.AllAssetsSource))
		for i, source := range item.AllAssetsSource {
			allAssetsSource[i] = int32(source)
		}
		resultItem.AllAssetsSource = allAssetsSource

		// 设置IP状态，如果没有匹配到资产数据则设置为特殊值100
		if status, exists := ipStatusMap[item.Ip]; exists {
			resultItem.IpStatus = int32(status)
		} else {
			resultItem.IpStatus = 100 // 特殊值，表示没有匹配到资产数据
		}

		// 处理logo字段，将content转换为API下载路径
		if item.Logo.Hash != 0 && item.Logo.Content != "" {
			logoContent := item.Logo.Content
			// 使用storage.GenAPIDownloadPath转换路径
			apiContent := storage.GenAPIDownloadPath("", logoContent, ".ico")
			resultItem.Logo = &pb.Logo{
				Hash:    int32(cast.ToInt(item.Logo.Hash)),
				Content: apiContent,
			}
		}

		// 构建线索链 - 按照PHP格式，每个reason对应一个独立的链数组
		// 注意：这里我们构建的是二维数组格式，但protobuf字段是一维的
		// 实际的二维数组格式会在JSON序列化时处理
		if len(item.Reason) > 0 {
			allChains := make([]*pb.ClueChain, 0)

			for _, reason := range item.Reason {
				chainList := make([]*pb.ClueChain, 0)
				getTopClue(int64(reason.Id), clueList, 0, &chainList)
				if len(chainList) > 0 {
					// 添加当前IP作为链的最后一个元素
					chainList = append(chainList, &pb.ClueChain{
						Content: item.Ip,
					})

					// 将这个链添加到总的链列表中
					// 注意：这里暂时是扁平化的，实际的二维结构会在后续处理
					allChains = append(allChains, chainList...)
				}
			}

			// 将所有链设置到ChainList字段
			if len(allChains) > 0 {
				resultItem.ChainList = allChains
			}
		}

		// 添加到结果列表
		items = append(items, resultItem)
	}

	// 设置响应
	rsp.Total = int32(total)
	rsp.Page = int32(page)
	rsp.PerPage = int32(perPage)
	rsp.Items = items

	// 添加调试日志，检查响应中的字段
	if len(items) > 0 {
		log.WithContextInfof(ctx, "[云端推荐结果] 响应中第一个项目的AssetsConfidenceLevel: %d", items[0].AssetsConfidenceLevel)
		// 尝试JSON序列化测试
		if jsonData, err := json.Marshal(items[0]); err == nil {
			log.WithContextInfof(ctx, "[云端推荐结果] JSON序列化测试: %s", string(jsonData))
		}
	}

	return nil
}

// ResultsClues 云端推荐-推荐结果-线索展示
// 对应PHP中的resultsClues方法
func ResultsClues(ctx context.Context, req *pb.CloudRecommendResultsRequest, rsp *pb.RecommendResultSearchMapResponse) error {
	userId := req.UserId
	flag := req.Flag

	// 打印请求参数
	log.WithContextInfof(ctx, "[云端推荐结果-线索展示] 请求参数: userId=%d, flag=%s", userId, flag)

	// 构建查询条件
	condition := recommend_result.NewFindCondition()
	condition.UserId = int(userId)
	condition.Flag = flag

	// 查询推荐结果
	list, err := recommend_result.NewRecommendResultModel().FindByCondition(condition)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果-线索展示] 查询推荐结果失败: %v", err)
		return fmt.Errorf("查询推荐结果失败: %v", err)
	}

	// 打印查询结果数量
	log.WithContextInfof(ctx, "[云端推荐结果-线索展示] 查询结果数量: %d", len(list))

	// 构建搜索映射结果
	data := buildSearchMap(list)

	// 设置响应
	// 处理logo字段 - 将对象数组转换为JSON字符串数组以匹配protobuf定义
	if logoItems, ok := data["logo"].([]map[string]interface{}); ok {
		logoStrings := make([]string, 0, len(logoItems))
		for _, logoItem := range logoItems {
			logoJSON, _ := json.Marshal(logoItem)
			logoStrings = append(logoStrings, string(logoJSON))
		}
		rsp.Logo = logoStrings
	}

	rsp.Domain = data["domain"].([]string)
	rsp.Cert = data["cert"].([]string)
	rsp.Icp = data["icp"].([]string)
	rsp.Subdomain = data["subdomain"].([]string)
	rsp.Port = data["port"].([]string)
	rsp.Protocol = data["protocol"].([]string)
	rsp.Title = data["title"].([]string)
	rsp.ClueCompanyName = data["clue_company_name"].([]string)
	rsp.CloudName = data["cloud_name"].([]string)
	rsp.Url = data["url"].([]string)

	// 设置空的organization_company_name数组
	rsp.OrganizationCompanyName = make([]string, 0)

	return nil
}

// buildSearchMap 构建搜索映射结果
// 对应PHP中的scopeSearchMap方法
func buildSearchMap(list []*recommend_result.RecommendResult) map[string]interface{} {
	data := make(map[string]interface{})

	// 初始化所有字段数组
	fields := []string{"domain", "cert", "icp", "subdomain", "port", "protocol", "title", "cloud_name", "url"}
	for _, field := range fields {
		data[field] = make([]string, 0)
	}
	// 特殊初始化clue_company_name字段
	data["clue_company_name"] = make([]string, 0)
	// 特殊初始化logo字段为对象数组
	data["logo"] = make([]map[string]interface{}, 0)

	// 用于去重的map
	uniqueMap := make(map[string]map[string]bool)
	for _, field := range fields {
		uniqueMap[field] = make(map[string]bool)
	}
	uniqueMap["clue_company_name"] = make(map[string]bool)

	// Logo特殊处理
	logoMap := make(map[int]bool)
	logoItems := make([]map[string]interface{}, 0)

	// 遍历所有结果项
	for _, item := range list {
		// 处理logo
		if item.Logo.Hash != 0 && item.Logo.Content != "" && !logoMap[cast.ToInt(item.Logo.Hash)] {
			logoMap[cast.ToInt(item.Logo.Hash)] = true
			// 将logo路径转换为api/v1/files格式
			// 获取文件名并移除扩展名，避免生成重复的.ico后缀
			baseFileName := filepath.Base(item.Logo.Content)
			fileNameWithoutExt := strings.TrimSuffix(baseFileName, filepath.Ext(baseFileName))
			apiContent := storage.GenAPIDownloadPath(fileNameWithoutExt, item.Logo.Content)
			logoObj := map[string]interface{}{
				"hash":    cast.ToInt(item.Logo.Hash),
				"content": apiContent,
			}
			logoItems = append(logoItems, logoObj)
		}

		// 处理domain
		if item.Domain != "" && !uniqueMap["domain"][item.Domain] {
			uniqueMap["domain"][item.Domain] = true
			data["domain"] = append(data["domain"].([]string), item.Domain)
		}

		// 处理cert
		if item.Cert != "" && !uniqueMap["cert"][item.Cert] {
			uniqueMap["cert"][item.Cert] = true
			data["cert"] = append(data["cert"].([]string), item.Cert)
		}

		// 处理icp
		if item.Icp != "" && !uniqueMap["icp"][item.Icp] {
			uniqueMap["icp"][item.Icp] = true
			data["icp"] = append(data["icp"].([]string), item.Icp)
		}

		// 处理subdomain
		if item.Subdomain != "" && !uniqueMap["subdomain"][item.Subdomain] {
			uniqueMap["subdomain"][item.Subdomain] = true
			data["subdomain"] = append(data["subdomain"].([]string), item.Subdomain)
		}

		// 处理port
		portStr := strconv.Itoa(cast.ToInt(item.Port))
		if portStr != "0" && !uniqueMap["port"][portStr] {
			uniqueMap["port"][portStr] = true
			data["port"] = append(data["port"].([]string), portStr)
		}

		// 处理protocol
		if item.Protocol != "" && !uniqueMap["protocol"][item.Protocol] {
			uniqueMap["protocol"][item.Protocol] = true
			data["protocol"] = append(data["protocol"].([]string), item.Protocol)
		}

		// 处理title
		if item.Title != "" && !uniqueMap["title"][item.Title] {
			uniqueMap["title"][item.Title] = true
			data["title"] = append(data["title"].([]string), item.Title)
		}

		// 处理cloud_name
		if item.CloudName != "" && !uniqueMap["cloud_name"][item.CloudName] {
			uniqueMap["cloud_name"][item.CloudName] = true
			data["cloud_name"] = append(data["cloud_name"].([]string), item.CloudName)
		}

		// 处理url
		if item.Url != "" && !uniqueMap["url"][item.Url] {
			uniqueMap["url"][item.Url] = true
			data["url"] = append(data["url"].([]string), item.Url)
		}

		// 处理reason中的clue_company_name
		for _, reason := range item.Reason {
			if reason.ClueCompanyName != "" && !uniqueMap["clue_company_name"][reason.ClueCompanyName] {
				uniqueMap["clue_company_name"][reason.ClueCompanyName] = true
				data["clue_company_name"] = append(data["clue_company_name"].([]string), reason.ClueCompanyName)
			}
		}
	}

	// 将logoItems设置为data["logo"]的返回值
	data["logo"] = logoItems

	return data
}

// buildSearchCondition 构建查询条件
func buildSearchCondition(req *pb.CloudRecommendResultsRequest) *recommend_result.FindCondition {
	condition := recommend_result.NewFindCondition()
	condition.UserId = int(req.UserId)
	condition.Flag = req.Flag
	condition.Keyword = req.Keyword
	condition.Ip = req.Ip
	// 设置高级参数，实际使用时需要确认FindCondition结构是否有对应字段
	// condition.Level = int(req.Level)
	// condition.DateRangeType = int(req.DateRangeType)
	// condition.AssetsConfidenceLevel = int(req.AssetsConfidenceLevel)
	// condition.Whole = req.Whole

	// 处理domain
	if len(req.Domain) > 0 {
		domains := make([]string, 0, len(req.Domain))
		for _, domain := range req.Domain {
			if domain != "" {
				domains = append(domains, domain)
			}
		}
		if len(domains) > 0 {
			condition.Domains = domains
		}
	}

	// 处理title
	if len(req.Title) > 0 {
		condition.Title = req.Title[0]
	}

	// 处理port
	log.Infof("[云端推荐结果-导出] 接收到的port参数: %v", req.Port)
	if len(req.Port) > 0 {
		ports := make([]int, 0, len(req.Port))
		for _, port := range req.Port {
			if port != "" {
				portInt, err := strconv.Atoi(port)
				if err == nil {
					ports = append(ports, portInt)
				} else {
					log.Errorf("[云端推荐结果-导出] 端口转换失败: %s, 错误: %v", port, err)
				}
			}
		}
		if len(ports) > 0 {
			condition.Ports = ports
			log.Infof("[云端推荐结果-导出] 设置查询端口条件: %v", ports)
		}
	}

	// 处理protocol
	if len(req.Protocol) > 0 {
		protocols := make([]string, 0, len(req.Protocol))
		for _, protocol := range req.Protocol {
			if protocol != "" {
				protocols = append(protocols, protocol)
			}
		}
		if len(protocols) > 0 {
			condition.Protocol = protocols
		}
	}

	// 处理subdomain
	if len(req.Subdomain) > 0 {
		condition.Subdomains = req.Subdomain
	}

	// 处理created_at
	if len(req.CreatedAt) == 2 {
		condition.SourceUpdatedAt[0] = req.CreatedAt[0]
		condition.SourceUpdatedAt[1] = req.CreatedAt[1]
	}

	// 处理updated_at
	if len(req.UpdatedAt) == 2 {
		condition.UpdatedAt[0] = req.UpdatedAt[0]
		condition.UpdatedAt[1] = req.UpdatedAt[1]
	}

	return condition
}

// getTopClue 获取线索链
// 对应PHP中的getTopClue函数
func getTopClue(reasonId int64, clueList []*clues.Clue, id int64, chainList *[]*pb.ClueChain) {
	for _, clue := range clueList {
		if reasonId > 0 && int64(clue.Id) == reasonId {
			// 找到对应的线索
			*chainList = append(*chainList, &pb.ClueChain{
				Id:              int64(clue.Id),
				Content:         clue.Content,
				Type:            int32(clue.Type),
				ClueCompanyName: clue.ClueCompanyName,
			})

			// 如果有父级线索，继续向上查找
			if clue.ParentId > 0 {
				getTopClue(int64(clue.ParentId), clueList, id, chainList)
			}
		}
	}
}

// ExportResults 导出推荐结果
// 对应PHP中的exportResults方法
func ExportResults(ctx context.Context, req *pb.CloudRecommendExportRequest, rsp *pb.CloudRecommendExportResponse) error {
	userId := req.UserId
	flag := req.Flag

	// 参数校验
	if len(req.Id) == 0 && req.Whole == 0 {
		return fmt.Errorf("没有可操作对象")
	}

	// 构建查询条件
	condition := recommend_result.NewFindCondition()
	condition.UserId = int(userId)
	condition.Flag = flag

	// 处理ID和整体导出
	if req.Whole == 0 {
		// 按ID查询
		condition.Ids = req.Id
	} else {
		// 按条件搜索
		condition = buildSearchCondition(&pb.CloudRecommendResultsRequest{
			UserId:    int64(userId),
			Flag:      flag,
			Keyword:   req.Keyword,
			Ip:        req.Ip,
			Domain:    req.Domain,
			Cert:      req.Cert,
			Icp:       req.Icp,
			Logo:      req.Logo,
			Port:      req.Port,
			Protocol:  req.Protocol,
			Subdomain: req.Subdomain,
			IpArray:   req.IpArray,
			CreatedAt: req.CreatedAt,
			UpdatedAt: req.UpdatedAt,
		})
	}

	// 打印查询条件用于调试
	log.WithContextInfof(ctx, "[云端推荐结果-导出] 查询条件: %+v", condition)

	// 获取推荐结果
	results, err := recommend_result.NewRecommendResultModel().FindByCondition(condition)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果-导出] 获取推荐结果失败: %v", err)
		return err
	}

	// 打印查询结果数量
	log.WithContextInfof(ctx, "[云端推荐结果-导出] 查询到 %d 条结果", len(results))

	// 创建Excel文件
	file := excelize.NewFile()
	sheet := "核对资产"
	err = file.SetSheetName("Sheet1", sheet)
	if err != nil {
		return err
	}

	// 设置表头
	headers := []string{"IP", "端口", "协议", "URL", "网站标题", "根域", "证书", "ICP", "ICON", "子域名", "推荐理由", "组件", "来源"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err = file.SetCellValue(sheet, cell, header)
		if err != nil {
			return err
		}
	}

	// 写入数据
	for i, item := range results {
		row := i + 2

		// 处理推荐理由 - 直接处理Reason数组，适用于导出模式
		var reason string
		if len(item.Reason) > 0 {
			reasonList := make([]string, 0, len(item.Reason))
			for _, val := range item.Reason {
				clueCompanyName := val.ClueCompanyName
				if clueCompanyName == "" {
					clueCompanyName = "-"
				}

				typeName := utils.TypeToCn(val.Type)
				if val.Type == clues.TYPE_LOGO {
					// Logo类型特殊处理 - 导出模式
					logoUrl := storage.GenAPIDownloadPath("", val.Content, ".ico")
					reasonList = append(reasonList, fmt.Sprintf("根据%s的%s %s推荐", clueCompanyName, typeName, logoUrl))
				} else {
					reasonList = append(reasonList, fmt.Sprintf("根据%s的%s %s推荐", clueCompanyName, typeName, val.Content))
				}
			}
			reason = strings.Join(reasonList, "; ")
		}

		// 处理ICON字段 - 从Logo.Content获取并转换为/api/v1/files格式
		var iconValue string
		if item.Logo.Hash != 0 && item.Logo.Content != "" {
			// 使用storage.GenAPIDownloadPath转换为/api/v1/files格式
			iconValue = storage.GenAPIDownloadPath("", item.Logo.Content, ".ico")
		}

		// 写入各列数据
		if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), item.Ip); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), cast.ToString(item.Port)); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), item.Protocol); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("D%d", row), item.Url); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("E%d", row), item.Title); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("F%d", row), item.Domain); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("G%d", row), item.Cert); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("H%d", row), item.Icp); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("I%d", row), iconValue); err != nil { // ICON字段
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), item.Subdomain); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), reason); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), item.Product); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("M%d", row), item.AssetsSource); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
	}

	// 保存文件
	filename := fmt.Sprintf("核对资产_%s.xlsx", time.Now().Format("20060102150405"))
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	if err := file.SaveAs(storagePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	fp, err := utils.DownloadFileEncrypt(accessPath, "核对资产_"+time.Now().Format(time.DateOnly)+path.Ext(filename), "", false)
	if err != nil {
		return fmt.Errorf("生成下载路径失败: %v", err)
	}

	// 设置返回的文件路径
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(filename))
	rsp.Url = apiFilePath

	return nil
}
