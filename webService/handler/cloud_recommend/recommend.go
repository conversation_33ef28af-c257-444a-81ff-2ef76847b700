package cloud_recommend

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/clues_groups"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/pkg/cfg"
	asynq "micro-service/pkg/queue_helper"
	"micro-service/pkg/storage"
	"strconv"
	"time"

	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/detect_assets"
	pb "micro-service/webService/proto"
)

// CloudRecommend 云端推荐
// 对应PHP中的recommend方法
func CloudRecommend(ctx context.Context, req *pb.CloudRecommendRequest, rsp *pb.CloudRecommendResponse) error {
	taskName := "推荐-" + time.Now().Format(time.DateTime)

	log.WithContextInfof(ctx, "[云端推荐] 开始处理云端推荐请求: userId=%d, companyId=%d, groupId=%d",
		req.UserId, req.CompanyId, req.GroupId)

	// 获取选择的线索ID
	clueIds, err := getSelectIds(ctx, req, true)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 获取选择线索ID失败: %v", err)
		return fmt.Errorf("获取选择线索ID失败: %v", err)
	}

	if len(clueIds) == 0 {
		return errors.New("只可对已确认线索下发")
	}

	// 检测是否有其他云端测绘任务正在进行
	detectAssetsCount, err := detect_assets_tasks.NewModel().Count(
		mysql.WithColumnValue("user_id", req.UserId),
		mysql.WithColumnValue("status", detect_assets_tasks.StatusDoing),
		mysql.WithColumnValue("expand_source", detect_assets_tasks.ExpandFromCloud),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 检查云端测绘任务失败: %v", err)
		return fmt.Errorf("检查云端测绘任务失败: %v", err)
	}

	if detectAssetsCount > 0 {
		return errors.New("检测到有其他云端资产测绘任务正在进行，请等待任务完成后再进行下发")
	}

	// 检查企业限制次数
	if req.CompanyId > 0 {
		limitCount, err := company.CheckLimitById(int64(req.CompanyId), company.LIMIT_TYPE_CLOUD_REC, 0)
		if err != nil {
			log.WithContextErrorf(ctx, "[云端推荐] 检查企业限制失败: %v", err)
			return fmt.Errorf("检查企业限制失败: %v", err)
		}
		if limitCount < 0 {
			return errors.New("已达到限制次数")
		}
	}

	// 去重线索ID
	uniqueIds := utils.ListDistinct(clueIds)
	clueCountNum := len(uniqueIds)

	// 生成flag
	flag := generateFlag(req.UserId, uniqueIds, taskName)

	// 更新企业限制次数
	if req.CompanyId > 0 {
		companyInfo, err := company.NewCompanyModel().FindById(req.CompanyId, 0)
		if err != nil {
			log.WithContextErrorf(ctx, "[云端推荐] 获取企业信息失败: %v", err)
			return fmt.Errorf("获取企业信息失败: %v", err)
		}
		err = companyInfo.UpdateLimit(company.LIMIT_TYPE_CLOUD_REC, 1, true)
		if err != nil {
			log.WithContextWarnf(ctx, "[云端推荐] 更新企业限制次数失败: %v", err)
		}
	}

	// 获取用户优先级
	priority := int32(0)
	userInfo, err := user.NewUserModel().FindById(req.UserId)
	if err == nil && userInfo != nil {
		priority = int32(userInfo.Priority)
	}

	// 创建云端测绘任务
	taskId, err := createDetectAssetsTask(ctx, req, taskName, flag, clueCountNum, &req.UserId)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 创建云端测绘任务失败: %v", err)
		return fmt.Errorf("创建云端测绘任务失败: %v", err)
	}
	clueArr, err := clues.NewCluer().ListAll(
		mysql.WithColumnValue("user_id", req.UserId),
		mysql.WithColumnValue("group_id", req.GroupId),
		mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
		mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
		mysql.WithColumnValue("is_supply_chain", clues.SUPPLY_CHAIN_NO), // 供应链线索不进行推荐资产
		mysql.WithValuesIn("type", []int{
			clues.TYPE_DOMAIN,
			clues.TYPE_CERT,
			clues.TYPE_ICP,
			clues.TYPE_LOGO,
			clues.TYPE_IP,
			clues.TYPE_SUBDOMAIN,
		}),
	)
	if err != nil {
		return err
	}

	// 如果线索为空，直接跳到资产已经推荐完成页面
	if len(clueArr) == 0 {
		flag := utils.Md5sHash(fmt.Sprintf("%d%d%d", time.Now().Unix(), req.UserId, taskId), false)
		err = detect_assets_tasks.NewModel().UpdateAny(
			map[string]interface{}{
				"expend_progress": 100,
				"step":            detect_assets_tasks.StepThree,
				"step_detail":     detect_assets_tasks.StepThreeRecommendAll,
				"step_status":     detect_assets_tasks.StepStatusDone,
				"expend_flags":    flag,
			},
			detect_assets_tasks.WithID(taskId),
		)
		if err != nil {
			return err
		}
		rsp.Flag = flag
		return nil
	}
	// 添加推荐记录
	err = addRecommendRecord(req.UserId, req.CompanyId, req.GroupId, taskName, flag, uniqueIds, clueArr, taskId, uint64(*req.OperateCompanyId))
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 创建推荐记录失败: %v", err)
		return fmt.Errorf("创建推荐记录失败: %v", err)
	}

	// 推荐资产
	err = dispatchRecommendAssetJob(ctx, flag, req.UserId, taskId, priority)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 调用推荐资产Job失败: %v", err)
		return fmt.Errorf("调用推荐资产Job失败: %v", err)
	}

	// 操作记录
	err = asynq.Enqueue(ctx, asynq.CreateLogEvent, asynq.NewCreateLogEventPayload(
		req.UserId,
		"云端推荐-立即推荐",
		req.ClientIp,
		req.CompanyId,
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 记录操作日志失败: %v", err)
	}

	// 设置响应
	rsp.Flag = flag
	rsp.ExpendId = taskId

	log.WithContextInfof(ctx, "[云端推荐] 云端推荐处理完成: flag=%s, expendId=%d", flag, taskId)
	return nil
}

// getSelectIds 获取选择的线索ID列表
func getSelectIds(ctx context.Context, req *pb.CloudRecommendRequest, confirm bool) ([]uint64, error) {
	// 构建PassClueRequest
	passClueReq := &pb.PassClueRequest{
		Data:    make([]*pb.PassClueRequest_DataItem, 0, len(req.Data)),
		GroupId: req.GroupId,
		Keyword: &pb.PassClueRequest_Keyword{
			DomainKeyword:    req.Keyword["domain_keyword"],
			CertKeyword:      req.Keyword["cert_keyword"],
			IcpKeyword:       req.Keyword["icp_keyword"],
			KeyKeyword:       req.Keyword["key_keyword"],
			IpKeyword:        req.Keyword["ip_keyword"],
			SubdomainKeyword: req.Keyword["subdomain_keyword"],
		},
	}

	// 转换数据格式
	for _, item := range req.Data {
		dataItem := &pb.PassClueRequest_DataItem{
			Id:    item.Id,
			IsAll: item.IsAll,
			Type:  item.Type,
		}
		passClueReq.Data = append(passClueReq.Data, dataItem)
	}

	return detect_assets.GetSelectIds(passClueReq, confirm)
}

// generateFlag 生成推荐任务标识
func generateFlag(userId uint64, clueIds []uint64, taskName string) string {
	// 使用与PHP相同的逻辑：用户ID + 线索ID + 任务名称的MD5哈希
	data := fmt.Sprintf("%d_%s_%v", userId, taskName, clueIds)
	return utils.Md5sHash(data, false)
}

// createDetectAssetsTask 创建云端测绘任务
func createDetectAssetsTask(ctx context.Context, req *pb.CloudRecommendRequest, taskName, flag string, clueCountNum int, safeUserId *uint64) (uint64, error) {
	now := time.Now()

	// 构建返回JSON
	returnJson := map[string]interface{}{
		fmt.Sprintf("%d", detect_assets_tasks.StepOneCompanyList):  []interface{}{},
		fmt.Sprintf("%d", detect_assets_tasks.StepTwoConfirm):      []interface{}{},
		fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandDomain): []interface{}{},
		fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandICP):    []interface{}{},
		fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandCert):   []interface{}{},
		fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandAll):    []interface{}{},
		fmt.Sprintf("%d", detect_assets_tasks.StepTwoExpandIP):     []interface{}{},
		"scene_group_set": map[string]interface{}{
			"expand_init_clues": 0,
			"scene_group_id":    0,
		},
		"scene_ids": []interface{}{},
	}

	returnJsonStr, err := json.Marshal(returnJson)
	if err != nil {
		return 0, fmt.Errorf("JSON序列化失败: %v", err)
	}

	// 创建任务 - 对应PHP中的DetectAssetsTask::query()->insertGetId
	task := &detect_assets_tasks.DetectAssetsTask{
		Name:             "云端测绘任务_" + now.Format(time.DateTime),
		UserId:           req.UserId,
		GroupId:          req.GroupId,
		FofaRange:        int(req.FofaRange),
		IsNeedHunter:     int(req.IsNeedHunter),
		IsNeedDnschecker: int(req.IsNeedDnschecker),
		IsAutoExpendIp:   int(req.IsAutoExpendIp),
		Step:             detect_assets_tasks.StepThree,
		StepDetail:       detect_assets_tasks.StepThreeRecommendAll,
		StepStatus:       detect_assets_tasks.StepStatusDefault,
		Status:           detect_assets_tasks.StatusDoing,
		ExpendFlags:      flag,
		CluesCount:       strconv.Itoa(clueCountNum),
		ReturnJson:       string(returnJsonStr),
		ExpandSource:     detect_assets_tasks.ExpandFromCloud,
	}

	// 设置安服用户ID（如果有）
	if safeUserId != nil {
		task.SafeUserId = *safeUserId
	}

	err = detect_assets_tasks.NewModel().Create(task)
	if err != nil {
		return 0, fmt.Errorf("创建测绘任务失败: %v", err)
	}

	return uint64(task.ID), nil
}

// dispatchRecommendAssetJob 调用推荐资产Job
func dispatchRecommendAssetJob(ctx context.Context, flag string, userId, taskId uint64, priority int32) error {
	if cfg.ExecGolangJob() {
		// 调用Go版本的推荐资产Job
		err := asynq.Enqueue(context.Background(), asynq.RecommendAssetJob, asynq.RecommendAssetJobPayload{
			RecommendRecordId: flag,
			UserId:            userId,
			DetectTaskId:      taskId,
		})
		if err != nil {
			return fmt.Errorf("调用推荐资产job失败: %v", err)
		}
	} else {
		// 调用PHP版本的Job - 对应PHP中的RecAssetMqJob::publish
		err := asynq.DispatchGolangJobRecommendJob.Dispatch(flag)
		if err != nil {
			return fmt.Errorf("调用 DispatchGolangJobRecommendJob 失败: %v", err)
		}
	}
	return nil
}

func addRecommendRecord(userId, companyId, groupId uint64, taskName, flag string, clueIds []uint64, clueArr []*clues.Clue, expendId, operatorId uint64) error {
	// 检查线索是否为空
	if len(clueArr) == 0 {
		return fmt.Errorf("线索不能为空!")
	}

	// 获取分组名称
	var groupName string
	if groupId > 0 {
		group, err := clues_groups.NewCluesGrouper().First(
			mysql.WithColumnValue("id", groupId),
		)
		if err == nil {
			groupName = group.Name
		}
	}

	// 按类型分组线索
	clueTypes := make(map[int][]map[string]interface{})
	for _, clue := range clueArr {
		item := map[string]interface{}{
			"id":                clue.Id,
			"type":              clue.Type,
			"content":           clue.Content,
			"group_id":          clue.GroupId,
			"clue_company_name": clue.ClueCompanyName,
			"source":            clue.Source,
			"punycode_domain":   clue.PunycodeDomain,
		}

		// 处理LOGO类型线索
		if clue.Type == clues.TYPE_LOGO && clue.Content != "" {
			item["content"] = storage.GenDownloadUrl(clue.Content, fmt.Sprintf("%d", clue.Hash))
		}

		if _, ok := clueTypes[clue.Type]; !ok {
			clueTypes[clue.Type] = []map[string]interface{}{}
		}
		clueTypes[clue.Type] = append(clueTypes[clue.Type], item)
	}

	// 判断是否只有子域名类型线索推荐资产
	typeArr := make([]int, 0)
	for _, clue := range clueArr {
		found := false
		for _, t := range typeArr {
			if t == clue.Type {
				found = true
				break
			}
		}
		if !found {
			typeArr = append(typeArr, clue.Type)
		}
	}
	isOnlySubdomain := 0
	if len(typeArr) == 1 && typeArr[0] == clues.TYPE_SUBDOMAIN {
		log.Infof("[RecommendAssets] 只有子域名推荐资产, expendId=%d, id=%s", expendId, flag)
		isOnlySubdomain = 1
	}

	// 检查记录是否已存在
	existingRecord, err := recommend_record.NewRecommendRecordModel().FindByID(flag)
	if err == nil && existingRecord.Id != "" {
		// 记录已存在，不需要创建
		return nil
	}

	// 构建推荐记录数据
	recordMap := map[string]interface{}{
		"id":                     flag,
		"flag":                   flag,
		"task_name":              taskName,
		"cron_id":                nil,
		"group_id":               int(groupId),
		"group_name":             groupName,
		"count":                  0,
		"user_id":                int(userId),
		"company_id":             int(companyId),
		"progress":               0,
		"status":                 0, // StatusDefault
		"confirm":                0, // ConfirmDefault
		"op_id":                  int(operatorId),
		"detect_assets_tasks_id": int(expendId),
		"clue_id":                convertUint64ToIntSlice(clueIds),
		"is_only_subdomain":      isOnlySubdomain,
		"created_at":             time.Now().Format(utils.DateTimeLayout),
		"updated_at":             time.Now().Format(utils.DateTimeLayout),
	}

	if subdomainClues, ok := clueTypes[clues.TYPE_SUBDOMAIN]; ok {
		recordMap["subdomain"] = subdomainClues
	}
	if domainClues, ok := clueTypes[clues.TYPE_DOMAIN]; ok {
		recordMap["domain"] = domainClues
	}
	if certClues, ok := clueTypes[clues.TYPE_CERT]; ok {
		recordMap["cert"] = certClues
	}
	if icpClues, ok := clueTypes[clues.TYPE_ICP]; ok {
		recordMap["icp"] = icpClues
	}
	if keywordClues, ok := clueTypes[clues.TYPE_KEYWORD]; ok {
		recordMap["title"] = keywordClues
	}
	if logoClues, ok := clueTypes[clues.TYPE_LOGO]; ok {
		recordMap["logo"] = logoClues
	}

	// 创建推荐记录
	_, err = es.GetEsClient().Index().Index("foradar_recommend_record").Type("record").
		Id(flag).BodyJson(recordMap).Do(context.Background())
	if err != nil {
		return err
	}

	return nil
}

// convertUint64ToIntSlice 将uint64切片转换为int切片
func convertUint64ToIntSlice(ids []uint64) []int {
	result := make([]int, len(ids))
	for i, id := range ids {
		result[i] = int(id)
	}
	return result
}
