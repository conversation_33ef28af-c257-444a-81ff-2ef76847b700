package cloud_recommend

import (
	"context"
	"strconv"
	"testing"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"go-micro.dev/v4/metadata"

	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

const (
	updateConfidenceIndex = "foradar_recommend_result"
	updateConfidenceType  = "result"
)

func InitUpdateConfidenceLevel() {
	cfg.InitLoadCfg()
	log.Init()

	mock := es.NewMockServer()

	// 注册计数查询Mock
	countResponse := elastic.CountResponse{
		Count: 3,
	}
	mock.Register("/"+updateConfidenceIndex+"/_count", countResponse)

	// 注册搜索Mock - 返回需要更新的记录
	testResults := []map[string]interface{}{
		{
			"id":                      "1",
			"user_id":                 1,
			"flag":                    "test_flag",
			"ip":                      "***********",
			"port":                    80,
			"protocol":                "http",
			"title":                   "正常网站",
			"domain":                  "example1.com",
			"assets_confidence_level": recommend_result.MIDDLE,
			"level":                   recommend_result.AssetsLevelC,
			"is_fake_assets":          false,
			"threaten_type":           0,
			"threaten_type_name":      "",
		},
		{
			"id":                      "2",
			"user_id":                 1,
			"flag":                    "test_flag",
			"ip":                      "***********",
			"port":                    80,
			"protocol":                "http",
			"title":                   "金沙娱乐网站",
			"domain":                  "fake-example.com",
			"assets_confidence_level": recommend_result.MIDDLE,
			"level":                   recommend_result.AssetsLevelC,
			"is_fake_assets":          false,
			"threaten_type":           0,
			"threaten_type_name":      "",
		},
		{
			"id":                      "3",
			"user_id":                 1,
			"flag":                    "test_flag",
			"ip":                      "***********",
			"port":                    80,
			"protocol":                "http",
			"title":                   "彩票平台",
			"domain":                  "lottery-example.com",
			"assets_confidence_level": recommend_result.MIDDLE,
			"level":                   recommend_result.AssetsLevelC,
			"is_fake_assets":          false,
			"threaten_type":           0,
			"threaten_type_name":      "",
		},
	}

	var searchHits []*elastic.SearchHit
	for i, result := range testResults {
		searchHits = append(searchHits, &elastic.SearchHit{
			Index:  updateConfidenceIndex,
			Type:   updateConfidenceType,
			Id:     strconv.Itoa(i + 1),
			Source: utils.ToJSON(result),
		})
	}

	mock.Register("/"+updateConfidenceIndex+"/"+updateConfidenceType+"/_search", searchHits)

	// 注册更新操作Mock
	updateResponse := elastic.BulkIndexByScrollResponse{
		Updated: 3,
		Total:   3,
		Batches: 1,
	}
	mock.Register("/"+updateConfidenceIndex+"/"+updateConfidenceType+"/_update_by_query", updateResponse)

	// 注册单条记录更新Mock
	singleUpdateResponse := elastic.UpdateResponse{
		Index:   updateConfidenceIndex,
		Type:    updateConfidenceType,
		Id:      "2",
		Version: 2,
		Result:  "updated",
	}
	mock.Register("/"+updateConfidenceIndex+"/"+updateConfidenceType+"/2", singleUpdateResponse)

	mock.RegisterBulk()
	mock.NewElasticMockClient()

	es.SetTestEnv(true)
	mysql.SetTestEnv(true)
	redis.ForceTest(true) // 强制使用Redis Mock

	es.GetInstance(cfg.LoadElastic())
	mysql.GetInstance(cfg.LoadMysql())
	// redis.GetInstance(cfg.LoadRedis()) // 不需要再调用，ForceTest已经初始化了

	// 设置足够的Redis Mock期望，支持多个测试用例
	redisMock := redis.GetMockInstance()
	redisMock.MatchExpectationsInOrder(false)
	for i := 0; i < 10; i++ { // 设置足够多的期望
		redisMock.Regexp().ExpectRPush("foradar:asyncq", ".*").SetVal(1)
	}
}

func TestUpdateAssetsConfidenceLevel(t *testing.T) {
	InitUnified()

	// MySQL期望已经在InitUnified()中设置了
	// mysqlMock := mysql.GetMockInstance()

	t.Run("更新为高可信度-成功", func(t *testing.T) {
		// 设置Redis Mock期望
		redisMock := redis.GetMockInstance()
		redisMock.ExpectRPush("foradar:asyncq", "<任何值>").SetVal(1)

		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.UpdateAssetsConfidenceLevelRequest{
			UserId:                   1,
			Flag:                     "test_flag",
			IpArray:                  []string{"***********"},
			SetAssetsConfidenceLevel: recommend_result.HIGH,
		}
		rsp := &pb.UpdateAssetsConfidenceLevelResponse{}

		err := UpdateAssetsConfidenceLevel(ctx, req, rsp)

		assert.NoError(t, err)
		assert.GreaterOrEqual(t, rsp.Count, int32(0))
	})

	t.Run("更新为中等可信度-成功", func(t *testing.T) {
		// 设置Redis Mock期望
		redisMock := redis.GetMockInstance()
		redisMock.ExpectRPush("foradar:asyncq", "<任何值>").SetVal(1)

		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.UpdateAssetsConfidenceLevelRequest{
			UserId:                   1,
			Flag:                     "test_flag",
			IpArray:                  []string{"***********"},
			SetAssetsConfidenceLevel: recommend_result.MIDDLE,
		}
		rsp := &pb.UpdateAssetsConfidenceLevelResponse{}

		err := UpdateAssetsConfidenceLevel(ctx, req, rsp)

		assert.NoError(t, err)
		assert.GreaterOrEqual(t, rsp.Count, int32(0))
	})

	t.Run("空IP数组-成功", func(t *testing.T) {
		// 设置Redis Mock期望
		redisMock := redis.GetMockInstance()
		redisMock.ExpectRPush("foradar:asyncq", "<任何值>").SetVal(1)

		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.UpdateAssetsConfidenceLevelRequest{
			UserId:                   1,
			Flag:                     "test_flag",
			IpArray:                  []string{},
			SetAssetsConfidenceLevel: recommend_result.HIGH,
		}
		rsp := &pb.UpdateAssetsConfidenceLevelResponse{}

		err := UpdateAssetsConfidenceLevel(ctx, req, rsp)

		assert.NoError(t, err)
		assert.GreaterOrEqual(t, rsp.Count, int32(0))
	})

	t.Run("多个IP-成功", func(t *testing.T) {
		// 设置Redis Mock期望
		redisMock := redis.GetMockInstance()
		redisMock.ExpectRPush("foradar:asyncq", "<任何值>").SetVal(1)

		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.UpdateAssetsConfidenceLevelRequest{
			UserId:                   1,
			Flag:                     "test_flag",
			IpArray:                  []string{"***********", "***********", "***********"},
			SetAssetsConfidenceLevel: recommend_result.MIDDLE,
		}
		rsp := &pb.UpdateAssetsConfidenceLevelResponse{}

		err := UpdateAssetsConfidenceLevel(ctx, req, rsp)

		assert.NoError(t, err)
		assert.GreaterOrEqual(t, rsp.Count, int32(0))
	})

	t.Run("参数验证-缺少可信度级别", func(t *testing.T) {
		ctx := metadata.NewContext(context.Background(), metadata.Metadata{"client_ip": "127.0.0.1"})

		req := &pb.UpdateAssetsConfidenceLevelRequest{
			UserId:                   1,
			Flag:                     "test_flag",
			IpArray:                  []string{"***********"},
			SetAssetsConfidenceLevel: 0, // 无效值
		}
		rsp := &pb.UpdateAssetsConfidenceLevelResponse{}

		err := UpdateAssetsConfidenceLevel(ctx, req, rsp)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "缺少可信度级别参数")
	})
}

func TestGetBlackTitles(t *testing.T) {
	t.Run("获取威胁词库-成功", func(t *testing.T) {
		// 不调用InitUnified()，避免数据库依赖
		// 这个测试主要验证函数的存在性和基本逻辑

		// 验证函数存在且签名正确
		var fn func(context.Context) map[string]int64 = getBlackTitles
		assert.NotNil(t, fn)

		// 验证默认关键词的存在性（这些是函数内部的默认值）
		expectedDefaultKeywords := []string{
			"金年会", "mg娱乐", "赌博", "彩票", "博彩",
		}

		// 验证默认关键词列表不为空
		assert.Greater(t, len(expectedDefaultKeywords), 0)

		t.Log("getBlackTitles函数验证通过")
	})
}
