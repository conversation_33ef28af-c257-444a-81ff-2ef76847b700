package cloud_recommend

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"micro-service/pkg/utils"
	"strconv"
	"time"

	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/storage"
	pb "micro-service/webService/proto"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// GroupIpResults 根据flag获取IP维度推荐结果
func GroupIpResults(ctx context.Context, req *pb.GroupIpResultsRequest, rsp *pb.GroupIpResultsResponse) error {
	log.WithContextInfof(ctx, "Received Web.GroupIpResults request: %+v", req)
	log.WithContextInfof(ctx, "DateRangeType: %d, Flag: %s", req.DateRangeType, req.Flag)

	// 直接构建ES查询，避免ParseQuery的wildcard语法问题
	query := elastic.NewBoolQuery()

	// 添加基础条件
	query.Must(elastic.NewTermQuery("user_id", req.UserId))
	query.Must(elastic.NewTermQuery("flag", req.Flag))

	// 添加IP过滤条件
	if req.Ip != "" {
		query.Must(elastic.NewWildcardQuery("ip.ip_raw", es.WithRLAsterisk(req.Ip)))
	}

	// 添加线索公司名称条件
	if req.ClueCompanyName != "" {
		query.Must(elastic.NewMatchPhraseQuery("reason.clue_company_name", req.ClueCompanyName))
	}

	// 关键词搜索
	if req.Keyword != "" {
		keywordQuery := elastic.NewBoolQuery()
		keywordQuery.Should(elastic.NewWildcardQuery("title", es.WithRLAsterisk(req.Keyword)))
		keywordQuery.Should(elastic.NewWildcardQuery("subdomain", es.WithRLAsterisk(req.Keyword)))
		keywordQuery.Should(elastic.NewWildcardQuery("cert", es.WithRLAsterisk(req.Keyword)))
		keywordQuery.Should(elastic.NewWildcardQuery("url.keyword", es.WithRLAsterisk(req.Keyword)))
		keywordQuery.Should(elastic.NewWildcardQuery("ip.ip_raw", es.WithRLAsterisk(req.Keyword)))
		keywordQuery.MinimumShouldMatch("1")
		query.Must(keywordQuery)
	}

	// 添加其他过滤条件
	if len(req.Domain) > 0 && hasNonEmptyString(req.Domain) {
		query.Must(elastic.NewTermsQuery("domain", utils.StringSliceToInterfaceSlice(req.Domain)...))
	}
	if len(req.Cert) > 0 && hasNonEmptyString(req.Cert) {
		query.Must(elastic.NewTermsQuery("cert", utils.StringSliceToInterfaceSlice(req.Cert)...))
	}
	if len(req.Icp) > 0 && hasNonEmptyString(req.Icp) {
		query.Must(elastic.NewTermsQuery("icp", utils.StringSliceToInterfaceSlice(req.Icp)...))
	}
	if len(req.Logo) > 0 && hasNonEmptyString(req.Logo) {
		query.Must(elastic.NewTermsQuery("logo.hash", utils.StringSliceToInterfaceSlice(req.Logo)...))
	}
	if len(req.Title) > 0 && hasNonEmptyString(req.Title) {
		query.Must(elastic.NewTermsQuery("title", utils.StringSliceToInterfaceSlice(req.Title)...))
	}
	if len(req.Port) > 0 && hasNonEmptyString(req.Port) {
		query.Must(elastic.NewTermsQuery("port", utils.StringSliceToInterfaceSlice(req.Port)...))
	}
	if len(req.Protocol) > 0 && hasNonEmptyString(req.Protocol) {
		query.Must(elastic.NewTermsQuery("protocol", utils.StringSliceToInterfaceSlice(req.Protocol)...))
	}
	if len(req.Subdomain) > 0 && hasNonEmptyString(req.Subdomain) {
		query.Must(elastic.NewTermsQuery("subdomain", utils.StringSliceToInterfaceSlice(req.Subdomain)...))
	}

	// 处理IP数组条件
	if len(req.IpArray) > 0 {
		query.Must(elastic.NewTermsQuery("ip.ip_raw", utils.StringSliceToInterfaceSlice(req.IpArray)...))
	}

	// 处理reason_type条件
	if req.ReasonType > 0 {
		query.Must(elastic.NewTermQuery("reason.type", req.ReasonType))
	}

	// 处理organization_company_id条件
	if req.OrganizationCompanyId > 0 {
		query.Must(elastic.NewTermQuery("organization_company_id", req.OrganizationCompanyId))
	}

	// 处理assets_level条件
	if req.AssetsLevel > 0 {
		query.Must(elastic.NewTermQuery("assets_level", req.AssetsLevel))
	}

	// 处理chain_type条件
	if req.ChainType > 0 {
		query.Must(elastic.NewTermQuery("reason.type", req.ChainType))
	}

	// 处理all_company_name条件
	if len(req.AllCompanyName) > 0 {
		query.Must(elastic.NewTermsQuery("all_company_name.keyword", utils.StringSliceToInterfaceSlice(req.AllCompanyName)...))
	}

	// 处理title_not条件
	if len(req.TitleNot) > 0 {
		query.MustNot(elastic.NewTermsQuery("title", utils.StringSliceToInterfaceSlice(req.TitleNot)...))
	}

	// 处理domain_not条件
	if len(req.DomainNot) > 0 {
		domainNotQuery := elastic.NewBoolQuery()
		domainNotQuery.Should(elastic.NewTermsQuery("domain", utils.StringSliceToInterfaceSlice(req.DomainNot)...))
		domainNotQuery.Should(elastic.NewTermsQuery("subdomain", utils.StringSliceToInterfaceSlice(req.DomainNot)...))
		query.MustNot(domainNotQuery)
	}

	// 处理cloud_name条件
	if req.CloudName != "" {
		query.Must(elastic.NewTermQuery("cloud_name.keyword", req.CloudName))
	}

	// 处理organization_company_name条件
	if req.OrganizationCompanyName != "" {
		query.Must(elastic.NewTermQuery("organization_company_name.keyword", req.OrganizationCompanyName))
	}

	// 处理level条件
	if len(req.Level) > 0 {
		// 检查是否包含特殊值-1，表示level参数存在但值为空
		hasSpecialValue := false
		for _, v := range req.Level {
			if v == -1 {
				hasSpecialValue = true
				break
			}
		}

		if hasSpecialValue {
			// level参数存在但值为空，按照特殊逻辑处理
			log.WithContextInfof(ctx, "level参数存在但值为空，不添加level查询条件")
		} else {
			// 正常处理level参数
			levelValues := make([]interface{}, len(req.Level))
			for i, v := range req.Level {
				levelValues[i] = v
			}
			query.Must(elastic.NewTermsQuery("level", levelValues...))
			log.WithContextInfof(ctx, "添加level查询条件: %v", levelValues)
		}
	}

	// 处理assets_confidence_level条件
	if len(req.AssetsConfidenceLevel) > 0 {
		confidenceLevelValues := make([]interface{}, len(req.AssetsConfidenceLevel))
		for i, v := range req.AssetsConfidenceLevel {
			confidenceLevelValues[i] = v
		}
		query.Must(elastic.NewTermsQuery("assets_confidence_level", confidenceLevelValues...))
	}

	// 处理url条件
	if req.Url != "" {
		query.Must(elastic.NewTermQuery("url.keyword", req.Url))
	}

	// 处理date_range_type条件
	if req.DateRangeType > 0 {
		log.WithContextInfof(ctx, "处理date_range_type参数: %d", req.DateRangeType)
		today := time.Now().Format("2006-01-02")
		var startDate string

		switch req.DateRangeType {
		case 1: // 一年内
			startDate = time.Now().AddDate(-1, 0, 0).Format("2006-01-02")
			log.WithContextInfof(ctx, "选择一年内数据: %s 到 %s", startDate, today)
		case 2: // 半年内
			startDate = time.Now().AddDate(0, -6, 0).Format("2006-01-02")
			log.WithContextInfof(ctx, "选择半年内数据: %s 到 %s", startDate, today)
		case 3: // 三个月内
			startDate = time.Now().AddDate(0, -3, 0).Format("2006-01-02")
			log.WithContextInfof(ctx, "选择三个月内数据: %s 到 %s", startDate, today)
		}

		if startDate != "" {
			log.WithContextInfof(ctx, "添加时间范围条件: %s 到 %s", startDate+" 00:00:00", today+" 23:59:59")
			query.Must(elastic.NewRangeQuery("source_updated_at").Gte(startDate + " 00:00:00").Lte(today + " 23:59:59"))
		}
	} else {
		log.WithContextInfof(ctx, "未提供date_range_type参数或值为0")
	}

	// 处理created_at条件
	if len(req.CreatedAt) == 2 {
		query.Must(elastic.NewRangeQuery("source_updated_at").Gte(req.CreatedAt[0] + " 00:00:00").Lte(req.CreatedAt[1] + " 23:59:59"))
	}

	// 处理updated_at条件
	if len(req.UpdatedAt) == 2 {
		query.Must(elastic.NewRangeQuery("updated_at").Gte(req.UpdatedAt[0] + " 00:00:00").Lte(req.UpdatedAt[1] + " 23:59:59"))
	}

	// 打印查询条件
	querySource, _ := query.Source()
	queryJSON, _ := json.Marshal(querySource)
	log.WithContextInfof(ctx, "[IP维度推荐结果] 查询条件JSON: %s", string(queryJSON))

	// 修改：使用ip.ip_raw而不是ip.keyword作为聚合字段
	agg := elastic.NewTermsAggregation().Field("ip.ip_raw").Size(10000)
	log.WithContextInfof(ctx, "[IP维度推荐结果] 使用聚合字段: ip.ip_raw")

	// 执行查询
	indexName := "foradar_recommend_result"
	indexType := "result"
	log.WithContextInfof(ctx, "[IP维度推荐结果] 查询索引: %s, 类型: %s", indexName, indexType)

	searchResult, err := es.GetEsClient().Search().
		Index(indexName).
		Type(indexType).
		Query(query).
		Aggregation("ips", agg).
		Size(0).
		Do(ctx)

	if err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] 获取IP列表失败: %v", err)
		return err
	}

	// 打印查询结果统计
	log.WithContextInfof(ctx, "[IP维度推荐结果] ES查询结果总数: %d", searchResult.TotalHits())

	// 解析聚合结果
	ipAgg, found := searchResult.Aggregations.Terms("ips")
	if !found {
		log.WithContextInfof(ctx, "[IP维度推荐结果] 未找到IP聚合结果")

		// 尝试直接获取一些记录来检查IP字段
		sampleResult, sampleErr := es.GetEsClient().Search().
			Index(indexName).
			Type(indexType).
			Query(query).
			Size(5).
			Do(ctx)

		if sampleErr == nil && sampleResult.TotalHits() > 0 {
			log.WithContextInfof(ctx, "[IP维度推荐结果] 获取到样本数据，检查IP字段")
			for _, hit := range sampleResult.Hits.Hits {
				var record map[string]interface{}
				if err := json.Unmarshal(*hit.Source, &record); err == nil {
					recordJSON, _ := json.Marshal(record)
					log.WithContextInfof(ctx, "[IP维度推荐结果] 样本数据: %s", string(recordJSON))
				}
			}
		}

		// 如果没有找到聚合结果，返回空结果
		emptyResult := map[string]interface{}{
			"total":        0,
			"per_page":     int(req.PerPage),
			"current_page": int(req.Page),
			"last_page":    0,
			"from":         0,
			"to":           0,
			"data":         make(map[string][]map[string]interface{}),
		}
		resultJSON, _ := json.Marshal(emptyResult)
		rsp.Total = 0
		rsp.PerPage = req.PerPage
		rsp.CurrentPage = req.Page
		rsp.LastPage = 0
		rsp.From = 0
		rsp.To = 0
		rsp.Items = string(resultJSON)
		return nil
	}

	// 打印聚合结果
	log.WithContextInfof(ctx, "[IP维度推荐结果] IP聚合桶数量: %d", len(ipAgg.Buckets))

	// 提取IP列表
	var allIpArr []string
	if req.IsCycle {
		// 随机取几条数据
		limit := 5
		for _, bucket := range ipAgg.Buckets {
			ip, ok := bucket.Key.(string)
			if ok {
				allIpArr = append(allIpArr, ip)
				if len(allIpArr) >= limit {
					break
				}
			}
		}
	} else {
		// 获取所有IP
		for _, bucket := range ipAgg.Buckets {
			ip, ok := bucket.Key.(string)
			if ok {
				allIpArr = append(allIpArr, ip)
			}
		}
	}

	// 打印IP列表
	log.WithContextInfof(ctx, "[IP维度推荐结果] 获取到的IP数量: %d", len(allIpArr))
	if len(allIpArr) > 0 {
		// 最多打印10个IP作为示例
		sampleSize := 10
		if len(allIpArr) < sampleSize {
			sampleSize = len(allIpArr)
		}
		sampleIPs, _ := json.Marshal(allIpArr[:sampleSize])
		log.WithContextInfof(ctx, "[IP维度推荐结果] IP示例(前%d个): %s", sampleSize, string(sampleIPs))
	}

	// 如果IP列表为空，返回空结果
	if len(allIpArr) == 0 {
		log.WithContextInfof(ctx, "[IP维度推荐结果] IP列表为空，返回空结果")
		// 构造空的分页响应
		emptyResult := map[string]interface{}{
			"total":        0,
			"per_page":     int(req.PerPage),
			"current_page": int(req.Page),
			"last_page":    0,
			"from":         0,
			"to":           0,
			"data":         make(map[string][]map[string]interface{}),
		}
		resultJSON, _ := json.Marshal(emptyResult)
		rsp.Total = 0
		rsp.PerPage = req.PerPage
		rsp.CurrentPage = req.Page
		rsp.LastPage = 0
		rsp.From = 0
		rsp.To = 0
		rsp.Items = string(resultJSON)
		return nil
	}

	// 分页处理
	page := int(req.Page)
	perPage := int(req.PerPage)
	if page <= 0 {
		page = 1
	}
	if perPage <= 0 {
		perPage = 10
	}

	// 计算分页信息
	total := len(allIpArr)
	lastPage := int(math.Ceil(float64(total) / float64(perPage)))
	from := (page - 1) * perPage
	to := from + perPage
	if to > total {
		to = total
	}

	log.WithContextInfof(ctx, "[IP维度推荐结果] 分页信息 - 总数: %d, 每页: %d, 当前页: %d, 最后页: %d, 起始: %d, 结束: %d",
		total, perPage, page, lastPage, from, to)

	// 获取当前页的IP
	var currentPageIPs []string
	if from < len(allIpArr) {
		if to > len(allIpArr) {
			to = len(allIpArr)
		}
		currentPageIPs = allIpArr[from:to]
	}

	// 打印当前页IP
	currentPageIPsJSON, _ := json.Marshal(currentPageIPs)
	log.WithContextInfof(ctx, "[IP维度推荐结果] 当前页IP数量: %d, IP列表: %s", len(currentPageIPs), string(currentPageIPsJSON))

	// 如果当前页没有数据，返回空结果
	if len(currentPageIPs) == 0 {
		log.WithContextInfof(ctx, "[IP维度推荐结果] 当前页没有数据，返回空结果")
		emptyResult := map[string]interface{}{
			"total":        int64(total),
			"per_page":     perPage,
			"current_page": page,
			"last_page":    lastPage,
			"from":         from + 1,
			"to":           to,
			"data":         make(map[string][]map[string]interface{}),
		}
		resultJSON, _ := json.Marshal(emptyResult)
		rsp.Total = int64(total)
		rsp.PerPage = int32(perPage)
		rsp.CurrentPage = int32(page)
		rsp.LastPage = int32(lastPage)
		rsp.From = int32(from + 1)
		rsp.To = int32(to)
		rsp.Items = string(resultJSON)
		return nil
	}

	// 构建当前页IP的查询条件
	currentIPParams := [][]interface{}{
		{"user_id", req.UserId},
		{"flag", req.Flag},
		{"ip", "in", utils.StringSliceToInterfaceSlice(currentPageIPs)},
	}

	// 打印当前页查询参数
	currentIPParamsJSON, _ := json.Marshal(currentIPParams)
	log.WithContextInfof(ctx, "[IP维度推荐结果] 当前页查询参数: %s", string(currentIPParamsJSON))

	// 使用封装的ES方法查询当前页IP的详细数据
	currentIPQuery := es.ParseQuery(currentIPParams)

	// 打印当前页查询条件
	currentIPQuerySource, _ := currentIPQuery.Source()
	currentIPQueryJSON, _ := json.Marshal(currentIPQuerySource)
	log.WithContextInfof(ctx, "[IP维度推荐结果] 当前页查询条件JSON: %s", string(currentIPQueryJSON))

	sorts := []elastic.Sorter{elastic.NewFieldSort("source_updated_at").Desc()}

	// 执行查询 - 不使用泛型函数，直接使用ES客户端查询
	searchResult, err = es.GetEsClient().Search().
		Index(indexName).
		Type(indexType).
		Query(currentIPQuery).
		SortBy(sorts...).
		Size(10000).
		Do(ctx)

	if err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] 查询IP详细数据失败: %v", err)
		return err
	}

	// 打印查询结果
	log.WithContextInfof(ctx, "[IP维度推荐结果] 查询到的记录数量: %d", len(searchResult.Hits.Hits))

	// 按IP分组
	groupedResults := make(map[string][]map[string]interface{})

	// 手动解析结果
	for _, hit := range searchResult.Hits.Hits {
		var record map[string]interface{}
		if err := json.Unmarshal(*hit.Source, &record); err != nil {
			log.WithContextErrorf(ctx, "[IP维度推荐结果] 解析记录失败: %v", err)
			continue
		}

		// 添加ID字段
		record["id"] = hit.Id

		// 获取IP
		ip, ok := record["ip"].(string)
		if !ok {
			log.WithContextWarnf(ctx, "[IP维度推荐结果] 记录缺少IP字段")
			continue
		}

		// 添加基础字段
		addBasicFields(record)

		// 添加到分组中
		groupedResults[ip] = append(groupedResults[ip], record)
	}

	// 打印分组结果
	groupCount := len(groupedResults)
	log.WithContextInfof(ctx, "[IP维度推荐结果] IP分组数量: %d", groupCount)

	// 打印每个IP的记录数量
	if groupCount > 0 {
		ipCounts := make(map[string]int)
		for ip, records := range groupedResults {
			ipCounts[ip] = len(records)
		}
		ipCountsJSON, _ := json.Marshal(ipCounts)
		log.WithContextInfof(ctx, "[IP维度推荐结果] 每个IP的记录数量: %s", string(ipCountsJSON))
	}

	// 查询IP资产状态
	ipStatusMap, err := queryIpAssetStatus(ctx, req.UserId, currentPageIPs)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] 查询IP资产状态失败: %v", err)
		// 不返回错误，继续处理
	}

	// 添加IP状态到结果中
	addIpStatusToResults(groupedResults, ipStatusMap)

	// 添加线索链处理
	err = addChainListToResults(ctx, req.UserId, groupedResults)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] 添加线索链失败: %v", err)
		// 不返回错误，继续处理
	}

	// 构造分页响应
	resultData := map[string]interface{}{
		"total":        int64(total),
		"per_page":     perPage,
		"current_page": page,
		"last_page":    lastPage,
		"from":         from + 1,
		"to":           to,
		"data":         groupedResults,
	}
	resultJSON, _ := json.Marshal(resultData)

	// 设置响应
	rsp.Total = int64(total)
	rsp.PerPage = int32(perPage)
	rsp.CurrentPage = int32(page)
	rsp.LastPage = int32(lastPage)
	rsp.From = int32(from + 1)
	rsp.To = int32(to)
	rsp.Items = string(resultJSON)

	// 记录操作日志
	err = asyncq.Enqueue(ctx, "CreateLogEvent", map[string]interface{}{
		"user_id":    req.UserId,
		"content":    fmt.Sprintf("查看IP维度推荐结果，标识：%s", req.Flag),
		"ip":         metadata.MustString(ctx, "client_ip"),
		"company_id": req.CompanyId,
		"model":      operate_logs.FIND_ASSETS,
		"type":       operate_logs.TYPE_OPERATE,
	})
	if err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] 记录操作日志失败: %v", err)
	}

	log.WithContextInfof(ctx, "[IP维度推荐结果] 处理完成，返回结果")
	return nil
}

// 添加一个辅助函数，检查字符串数组中是否有非空元素
func hasNonEmptyString(arr []string) bool {
	for _, s := range arr {
		if s != "" {
			return true
		}
	}
	return false
}

// addBasicFields 添加基础字段
func addBasicFields(record map[string]interface{}) {
	// 删除可能存在的ip_status字段，因为我们会在后面根据实际资产状态重新设置
	delete(record, "ip_status")

	// 添加在线状态文本
	if onlineState, ok := record["online_state"]; ok {
		if state, ok := onlineState.(float64); ok {
			record["online_state_text"] = getStateText(int(state))
		} else if state, ok := onlineState.(int); ok {
			record["online_state_text"] = getStateText(state)
		}
	}

	// 添加默认的chain_list字段
	record["chain_list"] = []interface{}{}

	// 添加detail字段，复制port_list内容
	if portList, exists := record["port_list"]; exists {
		record["detail"] = portList
	} else {
		record["detail"] = []interface{}{}
	}

	//新增字段 clue_company_name ，直接取值 all_company_name
	if allCompanyName, exists := record["all_company_name"]; exists {
		record["clue_company_name"] = allCompanyName
	}

	// 处理logo字段，将content转换为API下载路径
	if logoData, exists := record["logo"]; exists {
		if logoMap, ok := logoData.(map[string]interface{}); ok {
			if content, exists := logoMap["content"]; exists {
				if contentStr, ok := content.(string); ok && contentStr != "" {
					// 使用storage.GenAPIDownloadPath转换路径
					logoMap["content"] = storage.GenAPIDownloadPath("", contentStr, ".ico")
				}
			}
		}
	}
}

// queryIpAssetStatus 查询IP资产状态
func queryIpAssetStatus(ctx context.Context, userId int64, ips []string) (map[string]int, error) {
	ipStatusMap := make(map[string]int)
	if len(ips) == 0 {
		return ipStatusMap, nil
	}

	// 构建查询条件
	assetCondition := fofaee_assets.NewFindCondition()
	assetCondition.UserId = uint64(userId)
	assetCondition.Ids = make([]string, 0, len(ips))

	// 构建ID列表
	for _, ip := range ips {
		assetId := fofaee_assets.GenId(int(userId), ip)
		assetCondition.Ids = append(assetCondition.Ids, assetId)
	}

	// 查询资产
	assets, _, err := fofaee_assets.NewFofaeeAssetsModel().FindByCondition(ctx, assetCondition, 1, 1000)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] 查询IP资产状态失败: %v", err)
		return ipStatusMap, err
	}

	// 构建IP状态映射
	for _, asset := range assets {
		status := 0
		if asset.Status != nil {
			status = cast.ToInt(asset.Status)
		}
		ipStatusMap[asset.Ip] = status
	}

	return ipStatusMap, nil
}

// addIpStatusToResults 将IP状态添加到结果中
func addIpStatusToResults(groupedResults map[string][]map[string]interface{}, ipStatusMap map[string]int) {
	for ip, records := range groupedResults {
		// 只有当IP在ipStatusMap中存在时才添加ip_status字段
		if status, exists := ipStatusMap[ip]; exists {
			for i := range records {
				records[i]["ip_status"] = status
			}
		}
		// 如果IP不存在于ipStatusMap中，则不添加ip_status字段
	}
}

// getStateText 获取状态文本
func getStateText(onlineState int) string {
	if onlineState == 1 {
		return "在线"
	}
	return "离线"
}

// addChainListToResults 为结果添加线索链
func addChainListToResults(ctx context.Context, userId int64, groupedResults map[string][]map[string]interface{}) error {
	// 收集所有资产的group_id，用于查询线索链
	var groupIds []string
	for _, records := range groupedResults {
		for _, record := range records {
			// 从reason字段中提取group_id
			if reason, exists := record["reason"]; exists {
				if reasonArr, ok := reason.([]interface{}); ok {
					for _, reasonItem := range reasonArr {
						if reasonMap, ok := reasonItem.(map[string]interface{}); ok {
							if groupId, exists := reasonMap["group_id"]; exists {
								if gid, ok := groupId.(string); ok && gid != "" {
									groupIds = append(groupIds, gid)
								} else if gid, ok := groupId.(float64); ok && gid > 0 {
									groupIds = append(groupIds, strconv.FormatFloat(gid, 'f', 0, 64))
								}
							}
						}
					}
				}
			}
		}
	}

	// 去重
	groupIds = utils.FilterSliceEmpty(groupIds)
	// 处理证据链
	if len(groupIds) > 0 {
		// 将string类型的groupIds转换为uint64类型
		var groupIdsUint64 []uint64
		for _, gid := range groupIds {
			if id, err := strconv.ParseUint(gid, 10, 64); err == nil {
				groupIdsUint64 = append(groupIdsUint64, id)
			}
		}

		// 如果没有有效的group_id，则跳过线索查询
		if len(groupIdsUint64) == 0 {
			return nil
		}
		// 查询线索表
		clueList, err := clues.NewCluer().ListAll(func(db *gorm.DB) {
			db.Where("user_id = ?", userId).Where("group_id IN ?", groupIdsUint64)
		})
		if err != nil {
			log.WithContextErrorf(ctx, "[IP维度推荐结果] 查询线索失败: %v", err)
			return err
		}

		// 为每个IP的每条记录构建线索链
		for ip, records := range groupedResults {
			for i, record := range records {
				var chainLists [][]map[string]interface{}

				// 从reason字段中提取reason_id
				if reason, exists := record["reason"]; exists {
					if reasonArr, ok := reason.([]interface{}); ok {
						for _, reasonItem := range reasonArr {
							if reasonMap, ok := reasonItem.(map[string]interface{}); ok {
								if reasonID, exists := reasonMap["id"]; exists {
									var reasonIDInt int64
									if rid, ok := reasonID.(float64); ok {
										reasonIDInt = int64(rid)
									} else if rid, ok := reasonID.(string); ok {
										if parsed, err := strconv.ParseInt(rid, 10, 64); err == nil {
											reasonIDInt = parsed
										}
									}

									if reasonIDInt > 0 {
										// 构建线索链
										var chainList []map[string]interface{}
										getTopClueNew(reasonIDInt, clueList, &chainList)

										if len(chainList) > 0 {
											// 添加当前IP到证据链末尾
											chainList = append(chainList, map[string]interface{}{
												"content": ip,
											})
											// 添加到结果中
											chainLists = append(chainLists, chainList)
										}
									}
								}
							}
						}
					}
				}

				// 将线索链添加到结果中
				if len(chainLists) > 0 {
					record["chain_list"] = chainLists
					log.WithContextInfof(ctx, "[IP维度推荐结果] 为IP[%s]记录[%d]添加了%d条证据链", ip, i, len(chainLists))
				} else {
					log.WithContextInfof(ctx, "[IP维度推荐结果] IP[%s]记录[%d]没有构建出证据链", ip, i)
				}
			}
		}
	}

	return nil
}

// getTopClueNew 获取线索链，递归查找父级线索
// 参照PHP实现
func getTopClueNew(parentID int64, clueList []*clues.Clue, chainList *[]map[string]interface{}, id ...int64) {
	var currentID int64
	if len(id) > 0 {
		currentID = id[0]
	}
	for _, clue := range clueList {
		// 处理初始查找（通过ID查找）
		if parentID == 0 && clue.Id == uint64(currentID) {
			// 如果是推荐来源且有from_ip
			if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
				// 如果不是首级线索，将from_ip添加到末尾
				if clue.ParentId > 0 {
					*chainList = append(*chainList, map[string]interface{}{
						"content": clue.FromIp,
					})
				} else {
					// 否则添加到开头
					*chainList = append([]map[string]interface{}{
						{"content": clue.FromIp},
					}, *chainList...)
				}
			}
		} else {
			// 通过父ID查找
			if clue.Id == uint64(parentID) {
				// 将当前线索添加到链的开头
				*chainList = append([]map[string]interface{}{
					{
						"id":                clue.Id,
						"content":           clue.Content,
						"type":              clue.Type,
						"parent_id":         clue.ParentId,
						"clue_company_name": clue.ClueCompanyName,
						"source":            clue.Source,
					},
				}, *chainList...)

				// 如果是推荐来源且有from_ip
				if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
					*chainList = append([]map[string]interface{}{
						{"content": clue.FromIp},
					}, *chainList...)
				}

				// 递归查找父级线索
				getTopClueNew(int64(clue.ParentId), clueList, chainList, currentID)
			}
		}
	}
}
