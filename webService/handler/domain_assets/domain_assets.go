package domain_assets

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"micro-service/pkg/cfg"
	"micro-service/pkg/metadata"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"

	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/domain_assets"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/middleware/mysql/website_message"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/storage"
	pb "micro-service/webService/proto"
)

// 添加TableName常量
const TableName = "domain_assets"

// DomainAssetsList 域名总资产列表
func DomainAssetsList(ctx context.Context, req *pb.DomainAssetsListRequest, rsp *pb.DomainAssetsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.DomainAssetsList request: %v", req)

	// 打印类型和泛解析字段及分页字段
	fmt.Printf("收到请求参数 - Keyword: '%s', Type: %d, OpenParse: %d, Page: %d, PerPage: %d\n",
		req.Keyword, req.Type, req.OpenParse, req.Page, req.PerPage)

	// 构建查询条件
	conditions := buildConditions(req)

	// 获取分页参数
	page := int(req.Page)
	if page <= 0 {
		page = 1
		fmt.Println("Web服务中修正页码为1")
	}
	perPage := int(req.PerPage)
	if perPage <= 0 {
		perPage = 10
		fmt.Println("Web服务中修正每页数量为10")
	}

	fmt.Printf("使用分页参数: page=%d, perPage=%d\n", page, perPage)

	// 查询数据
	model := domain_assets.NewModel()
	list, total, err := model.List([]string{}, page, perPage, conditions...)
	if err != nil {
		log.WithContextErrorf(ctx, "查询域名资产列表失败: %v", err)
		return fmt.Errorf("查询域名资产列表失败: %v", err)
	}

	fmt.Printf("查询结果: 总数=%d, 当前页记录数=%d\n", total, len(list))

	// 查询所有域名资产，用于判断是否有下级域名
	var allDomainAssets []*domain_assets.DomainAssets
	if len(list) > 0 {
		allDomainAssets, err = model.ListAll([]string{}, mysql.WithUserID(uint64(req.UserId)))
		if err != nil {
			log.WithContextErrorf(ctx, "查询所有域名资产失败: %v", err)
			return fmt.Errorf("查询所有域名资产失败: %v", err)
		}
	}

	// 构建响应数据
	items := make([]*pb.DomainAssetsItem, 0, len(list))

	for _, item := range list {
		domainItem := convertToPbDomainAsset(item)

		// 计算唯一ID
		domainItem.Uniqud = calculateMD5(item.Id)

		// 检查是否有下级域名
		hasNext := checkHasNext(allDomainAssets, item.Domain)
		if hasNext {
			domainItem.HasNext = 1
		}

		items = append(items, domainItem)
	}

	// 填充响应数据
	rsp.Total = total
	rsp.PerPage = int32(perPage)
	rsp.CurrentPage = int32(page)
	rsp.LastPage = int32((total + int64(perPage) - 1) / int64(perPage))
	rsp.From = int32((page-1)*perPage + 1)
	rsp.To = int32(min(int(rsp.From)+perPage-1, int(total)))
	rsp.Items = items

	fmt.Printf("构建响应: Total=%d, CurrentPage=%d, PerPage=%d, LastPage=%d, From=%d, To=%d, Items=%d\n",
		rsp.Total, rsp.CurrentPage, rsp.PerPage, rsp.LastPage, rsp.From, rsp.To, len(rsp.Items))

	// 如果需要显示筛选条件
	if req.ShowCondition != "1" {
		condition := &pb.DomainAssetsCondition{}
		err = buildFilterCondition(ctx, req.UserId, condition)
		if err != nil {
			log.WithContextErrorf(ctx, "构建筛选条件失败: %v", err)
			return fmt.Errorf("构建筛选条件失败: %v", err)
		}
		rsp.Condition = condition
	}

	return nil
}

// buildConditions 构建查询条件
func buildConditions(req *pb.DomainAssetsListRequest) []mysql.HandleFunc {
	var conditions []mysql.HandleFunc

	// 添加用户ID条件
	conditions = append(conditions, mysql.WithUserID(uint64(req.UserId)))

	fmt.Printf("构建条件 - 用户ID: %d\n", req.UserId)
	fmt.Printf("构建条件 - Type: %d\n", req.Type)
	fmt.Printf("构建条件 - OpenParse: %d\n", req.OpenParse)

	// 关键字搜索
	if req.Keyword != "" {
		conditions = append(conditions, domain_assets.WithKeyword(req.Keyword))
		fmt.Printf("添加关键字条件: %s\n", req.Keyword)
	}

	// 企业名称
	if len(req.CompanyName) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("company_name IN ?", req.CompanyName)
		})
		fmt.Printf("添加企业名称条件: %v\n", req.CompanyName)
	}

	// 标题
	if len(req.Title) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("title IN ?", req.Title)
		})
		fmt.Printf("添加标题条件: %v\n", req.Title)
	}

	// ICP备案
	if len(req.Icp) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("icp IN ?", req.Icp)
		})
		fmt.Printf("添加ICP备案条件: %v\n", req.Icp)
	}

	// 状态码
	if len(req.StatusCode) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("status_code IN ?", req.StatusCode)
		})
		fmt.Printf("添加状态码条件: %v\n", req.StatusCode)
	}

	// 创建时间范围
	if len(req.CreatedAtRange) == 2 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("created_at BETWEEN ? AND ?", req.CreatedAtRange[0], req.CreatedAtRange[1]+" 23:59:59")
		})
		fmt.Printf("添加创建时间范围条件: %v\n", req.CreatedAtRange)
	}

	// 更新时间范围
	if len(req.UpdatedAtRange) == 2 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("updated_at BETWEEN ? AND ?", req.UpdatedAtRange[0], req.UpdatedAtRange[1]+" 23:59:59")
		})
		fmt.Printf("添加更新时间范围条件: %v\n", req.UpdatedAtRange)
	}

	// 域名来源 - 使用orWhere LIKE查询匹配PHP的查询方式
	if len(req.Source) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			// 构建OR条件组
			if len(req.Source) == 1 {
				db.Where("`source` LIKE ?", fmt.Sprintf("%%%d%%", req.Source[0]))
			} else {
				// 多个条件需要用括号分组
				var orConditions []string
				var args []interface{}
				for _, src := range req.Source {
					orConditions = append(orConditions, "`source` LIKE ?")
					args = append(args, fmt.Sprintf("%%%d%%", src))
				}
				db.Where("("+strings.Join(orConditions, " OR ")+")", args...)
			}
		})
		fmt.Printf("添加域名来源条件: %v\n", req.Source)
	}

	// 域名 - 使用模糊搜索匹配PHP版本逻辑
	if len(req.Domain) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			// 构建OR条件，每个域名使用LIKE模糊匹配
			if len(req.Domain) == 1 {
				// 单个域名直接使用LIKE
				db.Where("domain LIKE ?", "%"+req.Domain[0]+"%")
			} else {
				// 多个域名使用OR条件
				var orConditions []string
				var args []interface{}
				for _, domain := range req.Domain {
					orConditions = append(orConditions, "domain LIKE ?")
					args = append(args, "%"+domain+"%")
				}
				db.Where("("+strings.Join(orConditions, " OR ")+")", args...)
			}
		})
		fmt.Printf("添加域名模糊搜索条件: %v\n", req.Domain)
	}

	// 顶级域名
	if len(req.TopDomain) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("top_domain IN ?", req.TopDomain)
		})
		fmt.Printf("添加顶级域名条件: %v\n", req.TopDomain)
	}

	// 父级域名
	if len(req.FDomain) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("f_domain IN ?", req.FDomain)
		})
		fmt.Printf("添加父级域名条件: %v\n", req.FDomain)
	}

	// 网站信息ID
	if req.WebsiteMessageId > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("website_message_id = ?", req.WebsiteMessageId)
		})
		fmt.Printf("添加网站信息ID条件: %d\n", req.WebsiteMessageId)
	}

	// 域名类型
	// 只有当Type字段不为-1时才添加筛选条件（-1表示未指定）
	if req.Type != -1 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("type = ?", req.Type)
		})
		fmt.Printf("添加域名类型条件: %d\n", req.Type)
	} else {
		fmt.Printf("不添加域名类型条件，值为: %d\n", req.Type)
	}

	// 域名类型
	// 只有当Type字段不为-1时才添加筛选条件（-1表示未指定）
	if req.Status != -1 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("status = ?", req.Status)
		})
	}

	// 是否泛解析
	// 只有当OpenParse字段不为-1时才添加筛选条件（-1表示未指定）
	if req.OpenParse != -1 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("open_parse = ?", req.OpenParse)
		})
		fmt.Printf("添加泛解析条件: %d\n", req.OpenParse)
	} else {
		fmt.Printf("不添加泛解析条件，值为: %d\n", req.OpenParse)
	}

	// 默认按更新时间倒序排序
	conditions = append(conditions, func(db *gorm.DB) {
		db.Order("updated_at DESC")
	})

	// 打印构建的SQL预览
	fmt.Println("即将构建查询语句：SELECT * FROM domain_assets WHERE user_id = ? [条件：type=" + fmt.Sprintf("%d", req.Type) + ", open_parse=" + fmt.Sprintf("%d", req.OpenParse) + "]")

	return conditions
}

// buildFilterCondition 构建筛选条件
func buildFilterCondition(ctx context.Context, userId int64, condition *pb.DomainAssetsCondition) error {
	// 查询该用户下的所有域名资产
	model := domain_assets.NewModel()
	searchList, err := model.ListAll([]string{}, mysql.WithUserID(uint64(userId)))
	if err != nil {
		return err
	}

	// 企业名称列表
	companyNames := make(map[string]bool)
	companyNames["-"] = true // 添加默认值

	// 域名列表
	domainMap := make(map[string]bool)

	// 父级域名列表
	fDomainMap := make(map[string]bool)

	// 标题列表
	titleMap := make(map[string]bool)

	// 状态码列表
	codeMap := make(map[string]bool)

	// 需要排除的父级域名
	excludeFDomains := map[string]bool{
		"com.cn": true,
		"net.cn": true,
		"org.cn": true,
		"com.tr": true,
		"gov.cn": true,
		"com.au": true,
		"co.uk":  true,
		"com.tw": true,
	}

	// 遍历所有域名资产，收集筛选条件
	for _, item := range searchList {
		if item.CompanyName != "" {
			companyNames[item.CompanyName] = true
		}

		if item.TopDomain != "" {
			domainMap[item.TopDomain] = true
		}

		if item.FDomain != "" && !excludeFDomains[item.FDomain] {
			fDomainMap[item.FDomain] = true
		}

		if item.Title != "" {
			titleMap[item.Title] = true
		}

		if item.StatusCode != "" {
			codeMap[item.StatusCode] = true
		}
	}

	// 转换为数组
	condition.CompanyList = mapKeysToArray(companyNames)
	condition.Domain = mapKeysToArray(domainMap)
	condition.TopDomain = mapKeysToArray(domainMap) // 顶级域名与域名列表相同
	condition.FDomain = mapKeysToArray(fDomainMap)
	condition.Title = mapKeysToArray(titleMap)
	condition.StatusCode = mapKeysToArray(codeMap)

	return nil
}

// mapKeysToArray 将map的键转换为数组
func mapKeysToArray(m map[string]bool) []string {
	result := make([]string, 0, len(m))
	for k := range m {
		result = append(result, k)
	}
	// 确保返回空数组而不是nil，以匹配PHP的行为
	if result == nil {
		result = []string{}
	}
	return result
}

// convertToPbDomainAsset 将数据库模型转换为protobuf模型
func convertToPbDomainAsset(item *domain_assets.DomainAssets) *pb.DomainAssetsItem {
	return &pb.DomainAssetsItem{
		Id:                         int64(item.Id),
		UserId:                     int64(item.UserId),
		CompanyId:                  int64(item.CompanyId),
		Domain:                     item.Domain,
		CompanyName:                item.CompanyName,
		FDomain:                    item.FDomain,
		TopDomain:                  item.TopDomain,
		DnsA:                       item.DnsA,
		DnsAaaa:                    item.DnsAaaa,
		Cname:                      item.Cname,
		Source:                     item.Source,
		Depth:                      int32(item.Depth),
		Status:                     int32(item.Status),
		Type:                       int32(item.Type),
		CreatedAt:                  item.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:                  item.UpdatedAt.Format("2006-01-02 15:04:05"),
		OpenParse:                  int32(item.OpenParse),
		DetectTaskId:               item.DetectTaskId,
		StatusCode:                 item.StatusCode,
		Title:                      item.Title,
		Icp:                        item.Icp,                                              // 使用数据库中的实际值
		OrganizationDiscoverTaskId: convertStringToInt64(item.OrganizationDiscoverTaskId), // string转int64
		WebsiteMessageId:           int64(item.WebsiteMessageId),                          // uint64转int64
		OrganizationId:             convertStringToInt64(item.OrganizationId),             // string转int64
		CustomTags:                 item.CustomTags,
		HasNext:                    0, // 默认为0，后面会根据检查结果更新
	}
}

// calculateMD5 计算MD5值
func calculateMD5(id uint64) string {
	data := []byte(fmt.Sprintf("%d", id))
	hash := md5.Sum(data)
	return hex.EncodeToString(hash[:])
}

// checkHasNext 检查是否有下级域名
func checkHasNext(allDomainAssets []*domain_assets.DomainAssets, domain string) bool {
	for _, v := range allDomainAssets {
		// 判断当前的这个域名是否有下一级域名
		if v.FDomain == domain {
			return true
		}
		// 判断是否有跨层级的域名
		if v.Domain != domain && v.TopDomain == domain {
			return true
		}
	}
	return false
}

// convertStringToInt64 将字符串转换为int64，如果转换失败返回0
func convertStringToInt64(s string) int64 {
	if s == "" {
		return 0
	}
	val, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		return 0
	}
	return val
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// DomainAssetsExport 域名总资产导出
func DomainAssetsExport(ctx context.Context, req *pb.DomainAssetsExportRequest, rsp *pb.DomainAssetsExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.DomainAssetsExport request: %v", req)
	log.WithContextInfof(ctx, "[域名导出] 用户ID=%d, 步骤=2, 时间=%d", req.UserId, time.Now().Unix())

	// 构建查询条件
	conditions := buildExportConditions(req)
	model := domain_assets.NewModel()

	// 如果有domain_arr，则获取这些域名的ID并查询
	if len(req.DomainArr) > 0 {
		if len(req.Domain) > 0 {
			// 有domain_arr时，清空domain
			req.Domain = nil
		}

		// 先查询得到符合条件的所有ID
		idConditions := buildExportConditions(req)
		ids, err := getIdsByConditions(model, idConditions)
		if err != nil {
			log.WithContextErrorf(ctx, "[域名导出] 查询域名ID失败: %v", err)
			return fmt.Errorf("查询域名ID失败: %v", err)
		}

		// 使用ID进行查询
		conditions = []mysql.HandleFunc{mysql.WithValuesIn("id", ids)}
	}

	// 添加用户ID条件
	conditions = append(conditions, mysql.WithUserID(uint64(req.UserId)))

	// 查询数据
	fields := []string{"domain", "company_name", "cname", "dns_a", "dns_aaaa", "type", "source", "created_at", "status_code", "title", "updated_at", "open_parse", "status", "icp"}
	list, err := model.ListAll(fields, append(conditions, mysql.WithSelect(strings.Join(fields, ",")))...)
	if err != nil {
		log.WithContextErrorf(ctx, "[域名导出] 查询域名资产失败: %v", err)
		return fmt.Errorf("查询域名资产失败: %v", err)
	}

	log.WithContextInfof(ctx, "[域名导出] 用户ID=%d, 步骤=3, 时间=%d", req.UserId, time.Now().Unix())

	// 将查询结果转换为导出格式
	exportList := make([]map[string]string, 0, len(list))
	for _, item := range list {
		exportItem := make(map[string]string)
		exportItem["domain"] = item.Domain
		exportItem["company_name"] = item.CompanyName
		exportItem["dns_a"] = item.DnsA
		exportItem["dns_aaaa"] = item.DnsAaaa
		exportItem["cname"] = item.Cname
		exportItem["title"] = strings.Replace(SubString(item.Title, 0, 200), "=", "", -1)
		exportItem["status_code"] = item.StatusCode
		exportItem["type"] = DomainType(item.Type)
		exportItem["source"] = strings.TrimSuffix(DomainSource(item.Source), ",")

		// 处理状态
		status := "不可解析"
		if item.Status == domain_assets.StatusValid {
			status = "可解析"
		} else if item.Status == 2 { // 状态为2表示解析中
			status = "解析中"
		}
		exportItem["status"] = status

		exportItem["open_parse"] = OpenParse(item.OpenParse)
		exportItem["created_at"] = item.CreatedAt.Format("2006-01-02 15:04:05")
		exportItem["updated_at"] = item.UpdatedAt.Format("2006-01-02 15:04:05")
		exportItem["icp"] = item.Icp

		exportList = append(exportList, exportItem)
	}

	// 转换为Excel格式数据
	data := make([][]string, 0, len(exportList))
	for _, item := range exportList {
		row := []string{
			item["domain"],
			item["company_name"],
			item["dns_a"],
			item["dns_aaaa"],
			item["cname"],
			item["title"],
			item["status_code"],
			item["type"],
			item["source"],
			item["status"],
			item["open_parse"],
			item["created_at"],
			item["updated_at"],
			item["icp"],
		}
		data = append(data, row)
	}

	// 设置Excel标题
	name := req.Menu
	if name == "" {
		name = "域名资产数据"
	}
	title := []string{"域名", "企业名称", "A记录(IPV4)", "AAAA记录(IPV6)", "CNAME", "网站标题", "状态码", "域名类型", "资产来源", "是否可解析", "泛解析域名", "发现时间", "更新时间", "ICP备案号"}

	// 创建Excel文件
	file := excelize.NewFile()
	sheetName := "域名资产数据"
	file.SetSheetName("Sheet1", sheetName)

	// 写入标题行
	for i, t := range title {
		cell := fmt.Sprintf("%c1", 'A'+i)
		file.SetCellValue(sheetName, cell, t)
	}

	// 写入数据行
	for i, row := range data {
		for j, cell := range row {
			cellName := fmt.Sprintf("%c%d", 'A'+j, i+2)
			file.SetCellValue(sheetName, cellName, cell)
		}
	}

	// 保存Excel文件
	currentTime := time.Now().Format("20060102150405")
	fileName := fmt.Sprintf("%s_%s.xlsx", name, currentTime)

	// 生成存储路径
	publicDir := filepath.Join(storage.GetPublicPath(), "download")
	publicFilePath := filepath.Join(publicDir, fileName)

	// 确保目录存在
	if err := os.MkdirAll(publicDir, 0755); err != nil {
		log.WithContextErrorf(ctx, "[域名导出] 创建目录失败: %v", err)
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 保存Excel文件
	if err := file.SaveAs(publicFilePath); err != nil {
		log.WithContextErrorf(ctx, "[域名导出] 保存Excel文件失败: %v", err)
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	displayName := name + "_" + time.Now().Format("2006-01-02")

	// 获取相对路径
	relativePath := storage.GetDbPath(publicFilePath)

	// 生成API下载路径
	apiFilePath := storage.GenAPIDownloadPath(displayName, relativePath)

	// 设置返回URL
	rsp.Url = apiFilePath

	log.WithContextInfof(ctx, "[域名导出] 用户ID=%d, 步骤=4, 时间=%d", req.UserId, time.Now().Unix())

	return nil
}

// buildExportConditions 构建导出查询条件
func buildExportConditions(req *pb.DomainAssetsExportRequest) []mysql.HandleFunc {
	var conditions []mysql.HandleFunc

	// 添加用户ID条件
	conditions = append(conditions, mysql.WithUserID(uint64(req.UserId)))

	// 关键字搜索
	if req.Keyword != "" {
		conditions = append(conditions, domain_assets.WithKeyword(req.Keyword))
	}

	// 企业名称
	if len(req.CompanyName) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("company_name IN ?", req.CompanyName)
		})
	}

	// 标题
	if len(req.Title) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("title IN ?", req.Title)
		})
	}

	// ICP备案
	if len(req.Icp) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("icp IN ?", req.Icp)
		})
	}

	// 状态码
	if len(req.StatusCode) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("status_code IN ?", req.StatusCode)
		})
	}

	// 创建时间范围
	if len(req.CreatedAtRange) == 2 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("created_at BETWEEN ? AND ?", req.CreatedAtRange[0], req.CreatedAtRange[1]+" 23:59:59")
		})
	}

	// 更新时间范围
	if len(req.UpdatedAtRange) == 2 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("updated_at BETWEEN ? AND ?", req.UpdatedAtRange[0], req.UpdatedAtRange[1]+" 23:59:59")
		})
	}

	// 域名状态
	if req.Status >= 0 && req.Status != -1 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("status = ?", req.Status)
		})
	}

	// 域名来源 - 使用orWhere LIKE查询匹配PHP的查询方式
	if len(req.Source) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			// 构建OR条件组
			if len(req.Source) == 1 {
				db.Where("`source` LIKE ?", fmt.Sprintf("%%%d%%", req.Source[0]))
			} else {
				// 多个条件需要用括号分组
				var orConditions []string
				var args []interface{}
				for _, src := range req.Source {
					orConditions = append(orConditions, "`source` LIKE ?")
					args = append(args, fmt.Sprintf("%%%d%%", src))
				}
				db.Where("("+strings.Join(orConditions, " OR ")+")", args...)
			}
		})
	}

	// 域名 - 使用模糊搜索匹配PHP版本逻辑
	if len(req.Domain) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			// 构建OR条件，每个域名使用LIKE模糊匹配
			if len(req.Domain) == 1 {
				// 单个域名直接使用LIKE
				db.Where("domain LIKE ?", "%"+req.Domain[0]+"%")
			} else {
				// 多个域名使用OR条件
				var orConditions []string
				var args []interface{}
				for _, domain := range req.Domain {
					orConditions = append(orConditions, "domain LIKE ?")
					args = append(args, "%"+domain+"%")
				}
				db.Where("("+strings.Join(orConditions, " OR ")+")", args...)
			}
		})
	}

	// 顶级域名
	if len(req.TopDomain) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("top_domain IN ?", req.TopDomain)
		})
	}

	if len(req.DomainArr) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("domain IN ?", req.DomainArr)
		})
	}

	// 父级域名
	if len(req.FDomain) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("f_domain IN ?", req.FDomain)
		})
	}

	// 网站信息ID
	if req.WebsiteMessageId > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("website_message_id = ?", req.WebsiteMessageId)
		})
	}

	// 域名类型
	// 只有当Type字段不为-1时才添加筛选条件（-1表示未指定）
	if req.Type != -1 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("type = ?", req.Type)
		})
	}

	// 是否泛解析
	// 只有当OpenParse字段不为-1时才添加筛选条件（-1表示未指定）
	if req.OpenParse != -1 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("open_parse = ?", req.OpenParse)
		})
	}

	return conditions
}

// getIdsByConditions 根据条件获取ID列表
func getIdsByConditions(model domain_assets.DomainAssetsModel, conditions []mysql.HandleFunc) ([]uint64, error) {
	items, err := model.ListAll([]string{"id"}, append(conditions, mysql.WithSelect("id"))...)
	if err != nil {
		return nil, err
	}

	var ids []uint64
	for _, item := range items {
		ids = append(ids, item.Id)
	}
	return ids, nil
}

// DomainType 将域名类型转换为中文
func DomainType(typeVal int) string {
	if typeVal == domain_assets.TypeDomain {
		return "主域名"
	}
	return "子域名"
}

// DomainSource 将域名来源转换为中文
func DomainSource(source string) string {
	if source == "" {
		return ""
	}

	// 先尝试解析为数字数组
	var sources []int
	err := json.Unmarshal([]byte(source), &sources)
	if err != nil {
		// 如果失败，尝试解析为字符串数组
		var sourcesStr []string
		err2 := json.Unmarshal([]byte(source), &sourcesStr)
		if err2 != nil {
			return ""
		}
		// 转换字符串数组为数字数组
		for _, srcStr := range sourcesStr {
			if srcStr == "1" {
				sources = append(sources, 1)
			} else if srcStr == "2" {
				sources = append(sources, 2)
			} else if srcStr == "3" {
				sources = append(sources, 3)
			} else if srcStr == "4" {
				sources = append(sources, 4)
			} else if srcStr == "5" {
				sources = append(sources, 5)
			} else if srcStr == "6" {
				sources = append(sources, 6)
			}
		}
	}

	var result string
	for _, src := range sources {
		result += DomainSourceToCn(fmt.Sprintf("%d", src)) + ","
	}
	return result
}

// DomainSourceToCn 将单个域名来源码转换为中文
func DomainSourceToCn(source string) string {
	switch source {
	case "1":
		return "域名提取"
	case "2":
		return "资产推荐"
	case "3":
		return "域名枚举"
	case "4":
		return "域名导入"
	case "5":
		return "域名验证"
	case "6":
		return "域名扫描"
	default:
		return ""
	}
}

// OpenParse 将泛解析标识转换为中文
func OpenParse(openParse int) string {
	if openParse == domain_assets.OpenParseValid {
		return "是"
	}
	return "否"
}

// SubString 截取字符串
func SubString(str string, start, end int) string {
	rs := []rune(str)
	length := len(rs)

	if start < 0 || start > length {
		return ""
	}

	if end < 0 {
		end = length
	}
	if end > length {
		end = length
	}

	return string(rs[start:end])
}

// DomainAssetsDelete 删除域名资产
func DomainAssetsDelete(ctx context.Context, req *pb.DomainAssetsDeleteRequest, rsp *pb.DomainAssetsDeleteResponse) error {
	log.WithContextInfof(ctx, "[域名删除] 用户ID=%d, 步骤=1, 时间=%d", req.UserId, time.Now().Unix())

	// 构建查询条件
	var conditions []mysql.HandleFunc
	conditions = append(conditions, func(db *gorm.DB) {
		db.Where("user_id = ?", req.UserId)
	})

	// 处理关键词
	if req.Keyword != "" {
		conditions = append(conditions, domain_assets.WithKeyword(req.Keyword))
	}

	// 处理公司名称
	if len(req.CompanyName) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("company_name IN ?", req.CompanyName)
		})
	}

	// 处理标题
	if len(req.Title) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("title IN ?", req.Title)
		})
	}

	// 处理ICP备案
	if len(req.Icp) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("icp IN ?", req.Icp)
		})
	}

	// 处理WebsiteMessageId
	if req.WebsiteMessageId != "" {
		websiteMessageId, err := strconv.ParseUint(req.WebsiteMessageId, 10, 64)
		if err == nil && websiteMessageId > 0 {
			conditions = append(conditions, func(db *gorm.DB) {
				db.Where("website_message_id = ?", websiteMessageId)
			})
		}
	}

	// 处理状态码
	if len(req.StatusCode) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("status_code IN ?", req.StatusCode)
		})
	}

	// 处理创建时间范围
	if len(req.CreatedAt) == 2 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("created_at BETWEEN ? AND ?", req.CreatedAt[0], req.CreatedAt[1])
		})
	}

	// 处理更新时间范围
	if len(req.UpdatedAt) == 2 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("updated_at BETWEEN ? AND ?", req.UpdatedAt[0], req.UpdatedAt[1])
		})
	}

	// 处理状态
	if req.Status != "" {
		status, err := strconv.ParseInt(req.Status, 10, 64)
		if err == nil {
			conditions = append(conditions, func(db *gorm.DB) {
				db.Where("status = ?", status)
			})
		}
	}

	// 处理来源
	if len(req.Source) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("source IN ?", req.Source)
		})
	}

	// 处理域名 - 使用模糊搜索匹配PHP版本逻辑
	if len(req.Domain) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			// 构建OR条件，每个域名使用LIKE模糊匹配
			if len(req.Domain) == 1 {
				// 单个域名直接使用LIKE
				db.Where("domain LIKE ?", "%"+req.Domain[0]+"%")
			} else {
				// 多个域名使用OR条件
				var orConditions []string
				var args []interface{}
				for _, domain := range req.Domain {
					orConditions = append(orConditions, "domain LIKE ?")
					args = append(args, "%"+domain+"%")
				}
				db.Where("("+strings.Join(orConditions, " OR ")+")", args...)
			}
		})
	}

	// 处理域名类型
	if req.Type != "" {
		domainType, err := strconv.ParseInt(req.Type, 10, 64)
		if err == nil {
			conditions = append(conditions, func(db *gorm.DB) {
				db.Where("type = ?", domainType)
			})
		}
	}

	// 处理泛解析
	if req.OpenParse != "" {
		openParse, err := strconv.ParseInt(req.OpenParse, 10, 64)
		if err == nil {
			conditions = append(conditions, func(db *gorm.DB) {
				db.Where("open_parse = ?", openParse)
			})
		}
	}

	// 处理域名数组 - 使用模糊搜索匹配PHP版本逻辑
	if len(req.DomainArr) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			// 构建OR条件，每个域名使用LIKE模糊匹配
			if len(req.DomainArr) == 1 {
				// 单个域名直接使用LIKE
				db.Where("domain LIKE ?", "%"+req.DomainArr[0]+"%")
			} else {
				// 多个域名使用OR条件
				var orConditions []string
				var args []interface{}
				for _, domain := range req.DomainArr {
					orConditions = append(orConditions, "domain LIKE ?")
					args = append(args, "%"+domain+"%")
				}
				db.Where("("+strings.Join(orConditions, " OR ")+")", args...)
			}
		})
	}

	// 处理顶级域名
	if len(req.TopDomain) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("top_domain IN ?", req.TopDomain)
		})
	}

	// 处理父级域名
	if len(req.FDomain) > 0 {
		conditions = append(conditions, func(db *gorm.DB) {
			db.Where("f_domain IN ?", req.FDomain)
		})
	}

	log.WithContextInfof(ctx, "[域名删除] 用户ID=%d, 步骤=2, 时间=%d", req.UserId, time.Now().Unix())

	// 查询所有要删除的域名
	domainAssets, err := domain_assets.NewModel().ListAll([]string{"domain"}, conditions...)
	if err != nil {
		log.WithContextErrorf(ctx, "[域名删除] 查询要删除的域名失败: %v", err)
		return err
	}

	deleteCount := len(domainAssets)
	if deleteCount == 0 {
		rsp.DeleteCount = 0
		return nil
	}

	// 提取域名列表
	domainsToDelete := make([]string, 0, deleteCount)
	for _, asset := range domainAssets {
		domainsToDelete = append(domainsToDelete, asset.Domain)
	}

	log.WithContextInfof(ctx, "[域名删除] 用户ID=%d, 步骤=3, 时间=%d, 删除数量=%d", req.UserId, time.Now().Unix(), deleteCount)

	// 删除域名资产
	err = mysql.GetDbClient().Transaction(func(tx *gorm.DB) error {
		// 使用事务删除域名资产
		query := tx.Table(TableName)
		// 应用所有查询条件
		for _, condition := range conditions {
			condition(query)
		}

		// 执行删除操作
		if err := query.Delete(&domain_assets.DomainAssets{}).Error; err != nil {
			return err
		}

		// 删除域名解析历史记录
		if len(domainsToDelete) > 0 {
			if err := tx.Table("domain_history").Where("domain IN ?", domainsToDelete).Delete(nil).Error; err != nil {
				log.WithContextErrorf(ctx, "[域名删除] 删除域名解析历史记录失败: %v", err)
				// 不返回错误，继续执行
			}
		}

		return nil
	})

	if err != nil {
		log.WithContextErrorf(ctx, "[域名删除] 删除域名资产失败: %v", err)
		return err
	}

	// 获取客户端ip
	clientIp, err := metadata.GetString(ctx, "client_ip")
	if err != nil {
		log.WithContextErrorf(ctx, "[域名删除] 获取客户端ip失败: %v", err)
		// 不返回错误，继续执行
	}
	//todo 操作日志记录
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(req.UserId),
		fmt.Sprintf("操作者用户id:%d，删除域名资产数据", req.UserId),
		clientIp,
		uint64(req.CompanyId),
		operate_logs.KNOWN_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "[域名删除] 创建操作日志失败: %v", err)
		// 不返回错误，继续执行
	}

	if cfg.ExecGolangJob() {
		//todo 调用go的job
	} else {
		// 记录删除数据到站内信
		err = asyncq.DeleteTypeWebsiteMessage.Dispatch(uint64(req.UserId), website_message.DOMAIN_ASSETS, uint64(deleteCount), nil)
		if err != nil {
			log.WithContextErrorf(ctx, "[域名删除] 记录删除数据到站内信失败: %v", err)
			// 不返回错误，继续执行
		}

		// 更新缓存数据
		err = asyncq.CacheTableIpsConditionJob.Dispatch(uint64(req.UserId))
		if err != nil {
			log.WithContextErrorf(ctx, "[域名删除] 缓存更新任务入队失败: %v", err)
			// 不返回错误，继续执行
		}

	}

	rsp.DeleteCount = int32(deleteCount)
	log.WithContextInfof(ctx, "[域名删除] 用户ID=%d, 步骤=4, 时间=%d, 删除完成", req.UserId, time.Now().Unix())

	return nil
}
