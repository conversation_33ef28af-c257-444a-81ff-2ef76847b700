package domain_assets

import (
	"context"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/mysql/domain_assets"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

// Init 初始化Mock服务
func Init() {
	cfg.InitLoadCfg()
	log.Init()

	// 设置是否使用Mock (默认true)
	mysql.SetTestEnv(true)
	redis.SetTestEnv(true)

	// 初始化数据库
	mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

// setupBasicMock 设置基本的Mock
func setupBasicMock() {
	mock := mysql.GetMockInstance()
	mock.ExpectQuery("SELECT count.*").WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))
	mock.ExpectQuery("SELECT .* FROM `domain_assets`").WillReturnRows(sqlmock.NewRows([]string{"id"}))
	// 为筛选条件查询添加额外的Mock
	mock.ExpectQuery("SELECT .* FROM `domain_assets`").WillReturnRows(sqlmock.NewRows([]string{"id"}))
}

// TestDomainAssetsList 测试域名总资产列表函数
func TestDomainAssetsList(t *testing.T) {
	Init()

	t.Run("正常查询", func(t *testing.T) {
		setupBasicMock()
		req := &pb.DomainAssetsListRequest{
			UserId:    1,
			Page:      1,
			PerPage:   10,
			Type:      -1,
			Status:    -1,
			OpenParse: -1,
		}
		rsp := &pb.DomainAssetsListResponse{}

		err := DomainAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.Equal(t, int32(10), rsp.PerPage)
	})

	t.Run("带关键字查询", func(t *testing.T) {
		setupBasicMock()
		req := &pb.DomainAssetsListRequest{
			UserId:    1,
			Page:      1,
			PerPage:   10,
			Keyword:   "example",
			Type:      -1,
			Status:    -1,
			OpenParse: -1,
		}
		rsp := &pb.DomainAssetsListResponse{}

		err := DomainAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("默认分页参数", func(t *testing.T) {
		setupBasicMock()
		req := &pb.DomainAssetsListRequest{
			UserId:    1,
			Page:      0, // 测试默认值
			PerPage:   0, // 测试默认值
			Type:      -1,
			Status:    -1,
			OpenParse: -1,
		}
		rsp := &pb.DomainAssetsListResponse{}

		err := DomainAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.Equal(t, int32(10), rsp.PerPage)
	})

	t.Run("带过滤条件查询", func(t *testing.T) {
		setupBasicMock()
		req := &pb.DomainAssetsListRequest{
			UserId:      1,
			Page:        1,
			PerPage:     10,
			CompanyName: []string{"测试公司"},
			Title:       []string{"测试网站"},
			Domain:      []string{"example.com"},
			Type:        0,
			Status:      1,
			OpenParse:   1,
		}
		rsp := &pb.DomainAssetsListResponse{}

		err := DomainAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})
}

// TestDomainAssetsExport 测试域名总资产导出函数
func TestDomainAssetsExport(t *testing.T) {
	Init()

	t.Run("正常导出", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		// 使用空结果避免时间解析问题
		mock.ExpectQuery("SELECT .* FROM `domain_assets`").WillReturnRows(sqlmock.NewRows([]string{
			"domain", "company_name", "cname", "dns_a", "dns_aaaa", "type", "source", "created_at", "status_code", "title", "updated_at", "open_parse", "status",
		}))

		req := &pb.DomainAssetsExportRequest{
			UserId:    1,
			Menu:      "域名资产导出",
			Type:      -1,
			OpenParse: -1,
		}
		rsp := &pb.DomainAssetsExportResponse{}

		err := DomainAssetsExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("空数据导出", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `domain_assets`").WillReturnRows(sqlmock.NewRows([]string{
			"domain", "company_name", "cname", "dns_a", "dns_aaaa", "type", "source", "created_at", "status_code", "title", "updated_at", "open_parse", "status",
		}))

		req := &pb.DomainAssetsExportRequest{
			UserId:    1,
			Type:      -1,
			OpenParse: -1,
		}
		rsp := &pb.DomainAssetsExportResponse{}

		err := DomainAssetsExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})
}

// TestDomainAssetsDelete 测试删除域名资产函数
func TestDomainAssetsDelete(t *testing.T) {
	Init()

	t.Run("没有找到要删除的域名", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT `domain` FROM `domain_assets`").WillReturnRows(sqlmock.NewRows([]string{"domain"}))

		req := &pb.DomainAssetsDeleteRequest{
			UserId:    999, // 使用不存在的用户ID
			CompanyId: 1,
		}
		rsp := &pb.DomainAssetsDeleteResponse{}

		err := DomainAssetsDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(0), rsp.DeleteCount)
	})
}

// TestBuildConditions 测试构建查询条件函数
func TestBuildConditions(t *testing.T) {
	tests := []struct {
		name     string
		req      *pb.DomainAssetsListRequest
		expected int // 期望的查询条件数量
	}{
		{
			name: "基本查询",
			req: &pb.DomainAssetsListRequest{
				UserId:    1,
				Type:      -1,
				Status:    -1,
				OpenParse: -1,
			},
			expected: 2, // user_id + order by
		},
		{
			name: "带关键字查询",
			req: &pb.DomainAssetsListRequest{
				UserId:    1,
				Keyword:   "example",
				Type:      -1,
				Status:    -1,
				OpenParse: -1,
			},
			expected: 3, // user_id + keyword + order by
		},
		{
			name: "带域名类型查询",
			req: &pb.DomainAssetsListRequest{
				UserId:    1,
				Type:      0,
				Status:    -1,
				OpenParse: -1,
			},
			expected: 3, // user_id + type + order by
		},
		{
			name: "域名类型为-1不添加条件",
			req: &pb.DomainAssetsListRequest{
				UserId:    1,
				Type:      -1,
				Status:    -1,
				OpenParse: -1,
			},
			expected: 2, // user_id + order by
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			conditions := buildConditions(tt.req)
			assert.Len(t, conditions, tt.expected)
		})
	}
}

// TestCalculateMD5 测试MD5计算函数
func TestCalculateMD5(t *testing.T) {
	tests := []struct {
		name     string
		id       uint64
		expected string
	}{
		{
			name:     "ID为1",
			id:       1,
			expected: "c4ca4238a0b923820dcc509a6f75849b",
		},
		{
			name:     "ID为123",
			id:       123,
			expected: "202cb962ac59075b964b07152d234b70",
		},
		{
			name:     "ID为0",
			id:       0,
			expected: "cfcd208495d565ef66e7dff9f98764da",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := calculateMD5(tt.id)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestDomainType 测试域名类型转换函数
func TestDomainType(t *testing.T) {
	tests := []struct {
		name     string
		typeVal  int
		expected string
	}{
		{
			name:     "主域名",
			typeVal:  1, // domain_assets.TypeDomain = 1
			expected: "主域名",
		},
		{
			name:     "子域名",
			typeVal:  0, // domain_assets.TypeSubDomain = 0
			expected: "子域名",
		},
		{
			name:     "其他类型",
			typeVal:  999,
			expected: "子域名",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := DomainType(tt.typeVal)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestDomainSource 测试域名来源转换函数
func TestDomainSource(t *testing.T) {
	tests := []struct {
		name     string
		source   string
		expected string
	}{
		{
			name:     "空字符串",
			source:   "",
			expected: "",
		},
		{
			name:     "无效JSON",
			source:   "invalid json",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := DomainSource(tt.source)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestDomainSourceToCn 测试单个域名来源码转换函数
func TestDomainSourceToCn(t *testing.T) {
	tests := []struct {
		name     string
		source   string
		expected string
	}{
		{
			name:     "资产推荐",
			source:   "2", // domain_assets.SourceRecommend
			expected: "资产推荐",
		},
		{
			name:     "域名枚举",
			source:   "3", // domain_assets.SourceEnum
			expected: "域名枚举",
		},
		{
			name:     "域名导入",
			source:   "4", // domain_assets.SourceImport
			expected: "域名导入",
		},
		{
			name:     "域名验证",
			source:   "5", // domain_assets.SourceVerify
			expected: "域名验证",
		},
		{
			name:     "未知来源",
			source:   "999",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := DomainSourceToCn(tt.source)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestOpenParse 测试泛解析标识转换函数
func TestOpenParse(t *testing.T) {
	tests := []struct {
		name      string
		openParse int
		expected  string
	}{
		{
			name:      "是泛解析",
			openParse: 1, // domain_assets.OpenParseValid
			expected:  "是",
		},
		{
			name:      "不是泛解析",
			openParse: 0,
			expected:  "否",
		},
		{
			name:      "其他值",
			openParse: 999,
			expected:  "否",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := OpenParse(tt.openParse)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestSubString 测试字符串截取函数
func TestSubString(t *testing.T) {
	tests := []struct {
		name     string
		str      string
		start    int
		end      int
		expected string
	}{
		{
			name:     "正常截取",
			str:      "hello world",
			start:    0,
			end:      5,
			expected: "hello",
		},
		{
			name:     "截取全部",
			str:      "hello",
			start:    0,
			end:      -1,
			expected: "hello",
		},
		{
			name:     "超出范围",
			str:      "hello",
			start:    0,
			end:      100,
			expected: "hello",
		},
		{
			name:     "起始位置超出范围",
			str:      "hello",
			start:    100,
			end:      200,
			expected: "",
		},
		{
			name:     "起始位置为负数",
			str:      "hello",
			start:    -1,
			end:      3,
			expected: "",
		},
		{
			name:     "中文字符串",
			str:      "你好世界",
			start:    0,
			end:      2,
			expected: "你好",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SubString(tt.str, tt.start, tt.end)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestCheckHasNext 测试检查是否有下级域名函数
func TestCheckHasNext(t *testing.T) {
	// 需要先导入domain_assets包
	allDomainAssets := []*domain_assets.DomainAssets{
		{
			Domain:    "sub.example.com",
			FDomain:   "example.com",
			TopDomain: "example.com",
		},
		{
			Domain:    "api.example.com",
			FDomain:   "example.com",
			TopDomain: "example.com",
		},
		{
			Domain:    "test.sub.example.com",
			FDomain:   "sub.example.com",
			TopDomain: "example.com",
		},
	}

	tests := []struct {
		name     string
		domain   string
		expected bool
	}{
		{
			name:     "有下级域名",
			domain:   "example.com",
			expected: true,
		},
		{
			name:     "有下级域名(子域名)",
			domain:   "sub.example.com",
			expected: true,
		},
		{
			name:     "没有下级域名",
			domain:   "test.sub.example.com",
			expected: false,
		},
		{
			name:     "不存在的域名",
			domain:   "notexist.com",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkHasNext(allDomainAssets, tt.domain)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestMin 测试最小值函数
func TestMin(t *testing.T) {
	tests := []struct {
		name     string
		a        int
		b        int
		expected int
	}{
		{
			name:     "a小于b",
			a:        5,
			b:        10,
			expected: 5,
		},
		{
			name:     "a大于b",
			a:        10,
			b:        5,
			expected: 5,
		},
		{
			name:     "a等于b",
			a:        5,
			b:        5,
			expected: 5,
		},
		{
			name:     "负数比较",
			a:        -5,
			b:        -10,
			expected: -10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := min(tt.a, tt.b)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestMapKeysToArray 测试map键转换为数组函数
func TestMapKeysToArray(t *testing.T) {
	tests := []struct {
		name     string
		input    map[string]bool
		expected int // 期望的数组长度
	}{
		{
			name:     "空map",
			input:    map[string]bool{},
			expected: 0,
		},
		{
			name: "单个元素",
			input: map[string]bool{
				"test": true,
			},
			expected: 1,
		},
		{
			name: "多个元素",
			input: map[string]bool{
				"test1": true,
				"test2": true,
				"test3": false,
			},
			expected: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := mapKeysToArray(tt.input)
			assert.Len(t, result, tt.expected)
		})
	}
}

// TestConvertToPbDomainAsset 测试转换为protobuf域名资产函数
func TestConvertToPbDomainAsset(t *testing.T) {
	tests := []struct {
		name  string
		asset *domain_assets.DomainAssets
	}{
		{
			name: "正常转换",
			asset: &domain_assets.DomainAssets{
				UserId:      1,
				CompanyId:   1,
				Domain:      "example.com",
				CompanyName: "测试公司",
				FDomain:     "",
				TopDomain:   "example.com",
				DnsA:        "1.1.1.1",
				DnsAaaa:     "",
				Cname:       "",
				Source:      `["1"]`,
				Depth:       0,
				Status:      1,
				Type:        0,
				OpenParse:   0,
				StatusCode:  "200",
				Title:       "测试网站",
			},
		},
		{
			name: "有下级域名",
			asset: &domain_assets.DomainAssets{
				UserId:      1,
				CompanyId:   1,
				Domain:      "example.com",
				CompanyName: "测试公司",
				FDomain:     "",
				TopDomain:   "example.com",
				DnsA:        "1.1.1.1",
				DnsAaaa:     "",
				Cname:       "",
				Source:      `["1"]`,
				Depth:       0,
				Status:      1,
				Type:        1,
				OpenParse:   1,
				StatusCode:  "200",
				Title:       "测试网站",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToPbDomainAsset(tt.asset)
			assert.NotNil(t, result)
			assert.Equal(t, tt.asset.Domain, result.Domain)
			assert.Equal(t, tt.asset.CompanyName, result.CompanyName)
		})
	}
}

// TestGetIdsByConditions 测试根据条件获取ID函数
func TestGetIdsByConditions(t *testing.T) {
	// 这个函数需要复杂的mock设置，暂时跳过详细测试
	// 主要通过其他函数间接测试
	t.Skip("需要复杂的mock设置，通过其他函数间接测试")
}

// TestBuildExportConditions 测试构建导出条件函数
func TestBuildExportConditions(t *testing.T) {
	tests := []struct {
		name     string
		req      *pb.DomainAssetsExportRequest
		expected int // 期望的查询条件数量
	}{
		{
			name: "基本查询",
			req: &pb.DomainAssetsExportRequest{
				UserId: 1,
			},
			expected: 4, // user_id + 默认的其他条件
		},
		{
			name: "带关键字查询",
			req: &pb.DomainAssetsExportRequest{
				UserId:  1,
				Keyword: "example",
			},
			expected: 5, // user_id + keyword + 默认条件
		},
		{
			name: "带企业名称查询",
			req: &pb.DomainAssetsExportRequest{
				UserId:      1,
				CompanyName: []string{"测试公司"},
			},
			expected: 5, // user_id + company_name + 默认条件
		},
		{
			name: "带标题查询",
			req: &pb.DomainAssetsExportRequest{
				UserId: 1,
				Title:  []string{"测试网站"},
			},
			expected: 5, // user_id + title + 默认条件
		},
		{
			name: "带ICP查询",
			req: &pb.DomainAssetsExportRequest{
				UserId: 1,
				Icp:    []string{"京ICP备12345678号"},
			},
			expected: 5, // user_id + icp + 默认条件
		},
		{
			name: "带状态码查询",
			req: &pb.DomainAssetsExportRequest{
				UserId:     1,
				StatusCode: []string{"200"},
			},
			expected: 5, // user_id + status_code + 默认条件
		},
		{
			name: "带创建时间范围查询",
			req: &pb.DomainAssetsExportRequest{
				UserId:         1,
				CreatedAtRange: []string{"2025-06-01", "2025-06-30"},
			},
			expected: 5, // user_id + created_at range + 默认条件
		},
		{
			name: "带更新时间范围查询",
			req: &pb.DomainAssetsExportRequest{
				UserId:         1,
				UpdatedAtRange: []string{"2025-06-01", "2025-06-30"},
			},
			expected: 5, // user_id + updated_at range + 默认条件
		},
		{
			name: "带来源查询",
			req: &pb.DomainAssetsExportRequest{
				UserId: 1,
				Source: []int32{1, 2},
			},
			expected: 5, // user_id + source + 默认条件
		},
		{
			name: "带域名查询",
			req: &pb.DomainAssetsExportRequest{
				UserId: 1,
				Domain: []string{"example.com"},
			},
			expected: 5, // user_id + domain + 默认条件
		},
		{
			name: "带顶级域名查询",
			req: &pb.DomainAssetsExportRequest{
				UserId:    1,
				TopDomain: []string{"example.com"},
			},
			expected: 5, // user_id + top_domain + 默认条件
		},
		{
			name: "带父级域名查询",
			req: &pb.DomainAssetsExportRequest{
				UserId:  1,
				FDomain: []string{"sub.example.com"},
			},
			expected: 5, // user_id + f_domain + 默认条件
		},
		{
			name: "带网站信息ID查询",
			req: &pb.DomainAssetsExportRequest{
				UserId:           1,
				WebsiteMessageId: 123,
			},
			expected: 5, // user_id + website_message_id + 默认条件
		},
		{
			name: "带域名类型查询",
			req: &pb.DomainAssetsExportRequest{
				UserId: 1,
				Type:   0,
			},
			expected: 4, // user_id + type + 默认条件(不包括type)
		},
		{
			name: "域名类型为-1不添加条件",
			req: &pb.DomainAssetsExportRequest{
				UserId: 1,
				Type:   -1,
			},
			expected: 3, // user_id + 默认条件(不包括type)
		},
		{
			name: "带泛解析查询",
			req: &pb.DomainAssetsExportRequest{
				UserId:    1,
				OpenParse: 1,
			},
			expected: 4, // user_id + open_parse + 默认条件(不包括open_parse)
		},
		{
			name: "泛解析为-1不添加条件",
			req: &pb.DomainAssetsExportRequest{
				UserId:    1,
				OpenParse: -1,
			},
			expected: 3, // user_id + 默认条件(不包括open_parse)
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			conditions := buildExportConditions(tt.req)
			assert.Len(t, conditions, tt.expected)
		})
	}
}

// TestDomainAssetsDeleteExtended 测试删除域名资产函数的更多场景
func TestDomainAssetsDeleteExtended(t *testing.T) {
	Init()

	t.Run("正常删除", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT `domain` FROM `domain_assets`").WillReturnRows(
			sqlmock.NewRows([]string{"domain"}).AddRow("example.com"),
		)

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `domain_assets`").WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectExec("DELETE FROM `domain_history`").WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		req := &pb.DomainAssetsDeleteRequest{
			UserId:    1,
			CompanyId: 1,
		}
		rsp := &pb.DomainAssetsDeleteResponse{}

		err := DomainAssetsDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), rsp.DeleteCount)
	})

	t.Run("带关键字删除", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT `domain` FROM `domain_assets`").WillReturnRows(
			sqlmock.NewRows([]string{"domain"}).AddRow("example.com"),
		)

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `domain_assets`").WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectExec("DELETE FROM `domain_history`").WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		req := &pb.DomainAssetsDeleteRequest{
			UserId:    1,
			CompanyId: 1,
			Keyword:   "example",
		}
		rsp := &pb.DomainAssetsDeleteResponse{}

		err := DomainAssetsDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), rsp.DeleteCount)
	})

	t.Run("带过滤条件删除", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT `domain` FROM `domain_assets`").WillReturnRows(
			sqlmock.NewRows([]string{"domain"}).AddRow("example.com"),
		)

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `domain_assets`").WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectExec("DELETE FROM `domain_history`").WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		rdm := redis.GetMockInstance()
		rdm.Regexp().ExpectRPush("foradar_queues:default", ".*").SetVal(1)
		rdm.Regexp().ExpectRPush("foradar_queues:default:notify", ".*").SetVal(1)
		rdm.Regexp().ExpectRPush("foradar_queues:default", ".*").SetVal(1)
		rdm.Regexp().ExpectRPush("foradar_queues:default:notify", ".*").SetVal(1)
		rdm.Regexp().ExpectRPush("foradar_queues:default", ".*").SetVal(1)
		rdm.Regexp().ExpectRPush("foradar_queues:default:notify", ".*").SetVal(1)

		req := &pb.DomainAssetsDeleteRequest{
			UserId:           1,
			CompanyId:        1,
			CompanyName:      []string{"测试公司"},
			Title:            []string{"测试网站"},
			Icp:              []string{"京ICP备12345678号"},
			WebsiteMessageId: "123",
			StatusCode:       []string{"200"},
			CreatedAt:        []string{"2025-06-01", "2025-06-30"},
			UpdatedAt:        []string{"2025-06-01", "2025-06-30"},
			Status:           "1",
			Source:           []string{"1", "2"},
			Domain:           []string{"example.com"},
			Type:             "0",
			OpenParse:        "1",
			DomainArr:        []string{"example.com"},
			TopDomain:        []string{"example.com"},
			FDomain:          []string{"sub.example.com"},
		}
		rsp := &pb.DomainAssetsDeleteResponse{}

		err := DomainAssetsDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), rsp.DeleteCount)
	})
}

// TestDomainAssetsExportExtended 测试域名总资产导出函数的更多场景
func TestDomainAssetsExportExtended(t *testing.T) {
	Init()

	t.Run("带域名数组导出", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		// 第一次查询获取ID
		mock.ExpectQuery("SELECT `id` FROM `domain_assets`").WillReturnRows(
			sqlmock.NewRows([]string{"id"}).AddRow(1),
		)
		// 第二次查询根据ID获取数据
		mock.ExpectQuery("SELECT .* FROM `domain_assets`").WillReturnRows(
			sqlmock.NewRows([]string{
				"domain", "company_name", "cname", "dns_a", "dns_aaaa", "type", "source", "created_at", "status_code", "title", "updated_at", "open_parse", "status",
			}),
		)

		req := &pb.DomainAssetsExportRequest{
			UserId:    1,
			DomainArr: []string{"example.com"},
		}
		rsp := &pb.DomainAssetsExportResponse{}

		err := DomainAssetsExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("带过滤条件导出", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `domain_assets`").WillReturnRows(
			sqlmock.NewRows([]string{
				"domain", "company_name", "cname", "dns_a", "dns_aaaa", "type", "source", "created_at", "status_code", "title", "updated_at", "open_parse", "status",
			}),
		)

		req := &pb.DomainAssetsExportRequest{
			UserId:           1,
			CompanyName:      []string{"测试公司"},
			Title:            []string{"测试网站"},
			Icp:              []string{"京ICP备12345678号"},
			StatusCode:       []string{"200"},
			CreatedAtRange:   []string{"2025-06-01", "2025-06-30"},
			UpdatedAtRange:   []string{"2025-06-01", "2025-06-30"},
			Status:           1,
			Source:           []int32{1, 2},
			Domain:           []string{"example.com"},
			TopDomain:        []string{"example.com"},
			FDomain:          []string{"sub.example.com"},
			WebsiteMessageId: 123,
			Type:             0,
			OpenParse:        1,
		}
		rsp := &pb.DomainAssetsExportResponse{}

		err := DomainAssetsExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})
}
