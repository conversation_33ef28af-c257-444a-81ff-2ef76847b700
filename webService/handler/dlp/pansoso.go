package dlp

import (
	"context"
	"errors"
	"fmt"

	"micro-service/coreService/handler/dlp"
	corePb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	pss "micro-service/middleware/mysql/dataleak_pansoso"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"

	"gorm.io/gorm"
)

func PansosoSearchByTask(_ context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keywordHash, keywords := SortAndHash(req.Keyword)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	var (
		isQuery = false
		client  = pss.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
		var info = pss.Task{Keyword: keywordJoin(keywords), KeywordHash: keywordHash, Progress: 1, Status: pss.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt) {
			err := client.UpdateAny(uint(item.Id), map[string]any{"status": pss.StatusDoing, "progress": 1})
			if err != nil {
				return err
			}
			isQuery = true
		}
	}

	if isQuery {
		// 异步获取pansoso网盘数据
		syncCtx := context.Background()
		go syncPansosoSearch(syncCtx, rsp.TaskId, req.Keyword)
	}

	return nil
}

func getPansosoSearchResult(ctx context.Context, taskId uint64, keywords []string) error {
	var rsp = new(corePb.NetDiskResponse)
	err := dlp.SearchPansoso(ctx, keywords, rsp, 200) // 最大200条
	if err != nil {
		return err
	}

	got := make([]*pss.PansosoResult, 0, len(rsp.Result))
	for i := range rsp.Result {
		got = append(got, &pss.PansosoResult{
			TaskId:     uint(taskId),
			OriginUrl:  rsp.Result[i].OriginUrl,
			FileName:   rsp.Result[i].FileName,
			FileUrl:    rsp.Result[i].Url,
			Screenshot: rsp.Result[i].Screenshot,
		})
	}

	client := pss.NewResultModel()
	list, err := client.ListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("[PanSoso]Get task_id->%d result data from mysql failed: %v", taskId, err)
	}

	err = client.CompareAndUpdate(list, got)
	if err != nil {
		return err
	}
	return nil
}

func syncPansosoSearch(ctx context.Context, taskId uint64, keywords []string) {
	err := getPansosoSearchResult(ctx, taskId, keywords)
	if err != nil {
		log.WithContextError(ctx, err)
	}

	client := pss.NewTaskModel()
	err = client.UpdateAny(uint(taskId), map[string]any{"status": pss.StatusFinished, "progress": 100})
	if err != nil {
		log.Errorf("[Pansoso]Update task_id->%d progress failed: %v", taskId, err)
	}
}

func PansosoTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := pss.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("查询任务id=%d记录未找到", taskId)
		}
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

func PansosoTaskResult(taskId uint64, rsp *pb.Wangpan56ResultResponse) error {
	l, err := pss.NewResultModel().ListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return err
	}

	var items = make([]*pb.Wangpan56ResultResponseItem, 0, len(l))
	for i := range l {
		items = append(items, &pb.Wangpan56ResultResponseItem{
			Id:         l[i].Id,
			OriginUrl:  l[i].OriginUrl,
			FileName:   l[i].FileName,
			FileUrl:    l[i].FileUrl,
			Screenshot: l[i].Screenshot,
		})
	}

	rsp.Items = items
	return nil
}
