package dlp

import (
	"errors"
	"strings"
	"time"

	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/baidulibrary"
	dlw "micro-service/middleware/mysql/dataleak_56wangpan"
	dls "micro-service/middleware/mysql/dataleak_dashengpan"
	dd8 "micro-service/middleware/mysql/dataleak_doc88"
	dld "micro-service/middleware/mysql/dataleak_docin"
	dgc "micro-service/middleware/mysql/dataleak_gitcode"
	dlt "micro-service/middleware/mysql/dataleak_gitee"
	dlg "micro-service/middleware/mysql/dataleak_github"
	dlp "micro-service/middleware/mysql/dataleak_pansoso"
	dpm "micro-service/middleware/mysql/dataleak_postman"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/bytedance/sonic"
)

const (
	typeGithub       = iota + 1 // 代码泄露 Github
	typeGitee                   // 代码泄露 Gitee
	typeBaiduLibrary            // 文库 百度文库
	typeDocin                   // 文库 豆丁
	typePansoso                 // 网盘 盘搜搜
	type56Wangpan               // 网盘 56wangpan
	typeDoc88                   // 文库 道客巴巴
	typeAliPan                  // 网盘 阿里云盘
	typeBaiduPan                // 网盘 百度云盘
	typeXunleiPan               // 网盘 迅雷云盘
	typeQuarkPan                // 网盘 夸克云盘
	typePostman                 // 代码泄露 Postman
	typeGitCode                 // 代码泄露 GitCode
)

const (
	PanPlatformUnknown = iota
	PanPlatFormBaidu
	PanPlatFormAliyun
	PanPlatFormQuark
	PanPlatFormXunlei
)

func TaskResultList(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) error {
	var err error
	var total int64

	switch req.SourceType {
	case typeGithub:
		total, err = githubResult(req, rsp)
	case typeGitee:
		total, err = giteeResult(req, rsp)
	case typeBaiduLibrary:
		total, err = baiduLibraryResult(req, rsp)
	case typeDocin:
		total, err = docinResult(req, rsp)
	case typePansoso:
		total, err = pansosoResult(req, rsp)
	case type56Wangpan:
		total, err = wangpan56Result(req, rsp)
	case typeDoc88:
		total, err = doc88Result(req, rsp)
	case typeAliPan:
		total, err = dashengResult(req, rsp, PanPlatFormAliyun)
	case typeBaiduPan:
		total, err = dashengResult(req, rsp, PanPlatFormBaidu)
	case typeXunleiPan:
		total, err = dashengResult(req, rsp, PanPlatFormXunlei)
	case typeQuarkPan:
		total, err = dashengResult(req, rsp, PanPlatFormQuark)
	case typePostman:
		total, err = postmanResult(req, rsp)
	case typeGitCode:
		total, err = gitcodeResult(req, rsp)
	}

	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	rsp.Total = total
	return err
}

func githubResult(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Search != "" {
		handlers = append(handlers, dlg.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`repo_name`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`repo_url`", req.Url))
	}
	if req.Language != "" {
		handlers = append(handlers, mysql.WithLRLike("`language`", req.Language))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, dlg.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, dlg.WithBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, dlg.WithBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, dlg.WithResultOrder("`updated_at`", false))
	l, total, err := dlg.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:          l[i].Id,
			SourceType:  req.SourceType,
			Keywords:    keywordSplit(l[i].Keyword),
			Name:        l[i].RepoName,
			Url:         l[i].CodeUrl,
			Language:    l[i].Language,
			CodeSpippet: l[i].CodeSnippet,
			CreatedAt:   l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

func giteeResult(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Search != "" {
		handlers = append(handlers, dlt.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`repo_name`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`repo_url`", req.Url))
	}
	if req.Language != "" {
		handlers = append(handlers, mysql.WithLRLike("`language`", req.Language))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, dlt.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, dlt.WithBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, dlt.WithBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, dlt.WithResultOrder("`updated_at`", false))
	l, total, err := dlt.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:          l[i].Id,
			SourceType:  req.SourceType,
			Keywords:    keywordSplit(l[i].Keyword),
			Name:        l[i].RepoName,
			Url:         l[i].RepoUrl,
			Language:    l[i].Language,
			CodeSpippet: l[i].CodeSnippet,
			CreatedAt:   l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

func baiduLibraryResult(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Search != "" {
		handlers = append(handlers, baidulibrary.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`title`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`url`", req.Url))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, baidulibrary.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, baidulibrary.WithBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, baidulibrary.WithBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, baidulibrary.WithResultOrder("`updated_at`", false))
	l, total, err := baidulibrary.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		name := strings.ReplaceAll(strings.ReplaceAll(l[i].Title, "<em>", ""), "</em>", "")
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:         l[i].Id,
			SourceType: req.SourceType,
			Keywords:   keywordSplit(l[i].Keyword),
			Name:       name,
			Url:        l[i].Url,
			CreatedAt:  l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:  l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

func docinResult(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Search != "" {
		handlers = append(handlers, dld.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`doc_title`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`doc_url`", req.Url))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, dld.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, dld.WithBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, dld.WithBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, dld.WithResultOrder("`updated_at`", false))
	l, total, err := dld.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:         l[i].Id,
			SourceType: req.SourceType,
			Keywords:   keywordSplit(l[i].Keyword),
			Name:       l[i].DocTitle,
			Url:        l[i].DocUrl,
			CreatedAt:  l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:  l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

func pansosoResult(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Search != "" {
		handlers = append(handlers, dlp.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`file_name`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`file_url`", req.Url))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, dlp.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, dlp.WithBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, dlp.WithBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, dlp.WithResultOrder("`updated_at`", false))
	l, total, err := dlp.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:         l[i].Id,
			SourceType: req.SourceType,
			Keywords:   keywordSplit(l[i].Keyword),
			Name:       l[i].FileName,
			Url:        l[i].FileUrl,
			CreatedAt:  l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:  l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

func wangpan56Result(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Search != "" {
		handlers = append(handlers, dlw.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`file_name`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`file_url`", req.Url))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, dlw.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, dlw.WithBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, dlw.WithBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, dlw.WithResultOrder("`updated_at`", false))
	l, total, err := dlw.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:         l[i].Id,
			SourceType: req.SourceType,
			Keywords:   keywordSplit(l[i].Keyword),
			Name:       l[i].FileName,
			Url:        l[i].FileUrl,
			CreatedAt:  l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:  l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

func dashengResult(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse, platformType int) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	handlers = append(handlers, dls.WithPlatformType(platformType))
	if req.Search != "" {
		handlers = append(handlers, dls.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`file_name`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`file_url`", req.Url))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, dlw.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, dls.WithBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, dls.WithBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, dls.WithResultOrder("`updated_at`", false))
	l, total, err := dls.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:         l[i].Id,
			SourceType: req.SourceType,
			Keywords:   keywordSplit(l[i].Keyword),
			Name:       l[i].FileName,
			Url:        l[i].FileUrl,
			CreatedAt:  l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:  l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

func gitcodeResult(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Search != "" {
		handlers = append(handlers, dgc.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`repo_name`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`repo_url`", req.Url))
	}
	if req.Language != "" {
		handlers = append(handlers, mysql.WithLRLike("`language`", req.Language))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, dlt.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, dgc.WithBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, dgc.WithBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, dgc.WithResultOrder("`updated_at`", false))
	l, total, err := dgc.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:          l[i].Id,
			SourceType:  req.SourceType,
			Keywords:    keywordSplit(l[i].Keyword),
			Name:        l[i].RepoName,
			Url:         l[i].RepoUrl,
			Language:    l[i].Language,
			CodeSpippet: l[i].CodeSnippet,
			CreatedAt:   l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

func postmanResult(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Search != "" {
		handlers = append(handlers, dpm.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`repo_name`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`repo_url`", req.Url))
	}
	if req.Language != "" {
		handlers = append(handlers, mysql.WithLRLike("`language`", req.Language))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, dlt.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, dpm.WithBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, dpm.WithBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, dpm.WithResultOrder("`updated_at`", false))
	l, total, err := dpm.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:          l[i].Id,
			SourceType:  req.SourceType,
			Keywords:    keywordSplit(l[i].Keyword),
			Name:        l[i].RepoName,
			Url:         l[i].RepoUrl,
			Language:    l[i].Language,
			CodeSpippet: l[i].CodeSnippet,
			CreatedAt:   l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

func doc88Result(req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) (int64, error) {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Search != "" {
		handlers = append(handlers, dd8.WithKeyword(req.Search))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("`title`", req.Name))
	}
	if req.Url != "" {
		handlers = append(handlers, mysql.WithLRLike("`url`", req.Url))
	}
	//if req.Keyword != "" {
	//	handlers = append(handlers, dd8.WithKeyword(req.Keyword, true))
	//}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := parseDate(req.CreatedAt[0], req.CreatedAt[1])
		handlers = append(handlers, dd8.WithDateBetween("`created_at`", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := parseDate(req.UpdatedAt[0], req.UpdatedAt[1])
		handlers = append(handlers, dd8.WithDateBetween("`updated_at`", t1, t2))
	}
	handlers = append(handlers, dd8.WithResultOrder("`updated_at`", false))
	l, total, err := dd8.NewDoc88Fetcher().ResultList(int(req.Page), int(req.PerPage), handlers...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.DlpTaskResultListItem{
			Id:         l[i].Id,
			SourceType: req.SourceType,
			Keywords:   keywordSplit(l[i].Keyword),
			Name:       l[i].Title,
			Url:        l[i].Url,
			CreatedAt:  l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:  l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return total, err
}

type generalInterface interface {
	UpdateAny(uint64, map[string]any) error
}

func getUpdater(source uint64) generalInterface {
	switch source {
	case typeGithub:
		return dlg.NewResultModel()
	case typeGitee:
		return dlt.NewResultModel()
	case typePostman:
		return dpm.NewResultModel()
	case typeGitCode:
		return dgc.NewResultModel()
	case typeBaiduLibrary:
		return baidulibrary.NewResultModel()
	case typeDocin:
		return dld.NewResultModel()
	case typePansoso:
		return dlp.NewResultModel()
	case type56Wangpan:
		return dlw.NewResultModel()
	case typeDoc88:
		return dd8.NewDoc88Fetcher()
	case typeAliPan, typeBaiduPan, typeQuarkPan, typeXunleiPan:
		return dls.NewResultModel()
	}
	return nil
}

func ResultUpdate(req *pb.DlpResultUpdateRequest) error {
	var resultId uint64
	var err error
	updateMap := make(map[string]any, 3)
	switch req.SourceType {
	// 代码泄露
	case typeGithub, typeGitee, typePostman, typeGitCode:
		if req.Language == "" {
			return errors.New("编程语言类型不可为空")
		}
		updateMap = map[string]any{"repo_name": req.Name, "repo_url": req.Url, "language": req.Language}
		switch req.SourceType {
		case typeGithub:
			info, errF := dlg.NewResultModel().RelationFirst(mysql.WithId(req.Id))
			resultId = info.ResultId
			err = errF
		case typeGitee:
			info, errF := dlt.NewResultModel().RelationFirst(mysql.WithId(req.Id))
			resultId = info.ResultId
			err = errF
		case typePostman:
			info, errF := dpm.NewResultModel().RelationFirst(mysql.WithId(req.Id))
			resultId = info.ResultId
			err = errF
		case typeGitCode:
			info, errF := dgc.NewResultModel().RelationFirst(mysql.WithId(req.Id))
			resultId = info.ResultId
			err = errF
		}
	// 百度文库
	case typeBaiduLibrary:
		updateMap = map[string]any{"title": req.Name, "url": req.Url}
		info, errF := baidulibrary.NewResultModel().RelationFirst(mysql.WithId(req.Id))
		err = errF
		resultId = info.ResultId
	// 豆丁文档
	case typeDocin:
		info, errF := dld.NewResultModel().RelationFirst(mysql.WithId(req.Id))
		err = errF
		resultId = info.ResultId
		updateMap = map[string]any{"doc_title": req.Name, "doc_url": req.Url}
	// 道客巴巴
	case typeDoc88:
		info, errF := dd8.NewDoc88Fetcher().RelationFirst(mysql.WithId(req.Id))
		err = errF
		resultId = info.ResultId
		updateMap = map[string]any{"title": req.Name, "url": req.Url}
	// 网盘: 盘搜搜, 56网盘, 大圣盘 (阿里云盘, 百度云盘, 迅雷云盘, 夸克云盘)
	case typePansoso, type56Wangpan, typeAliPan, typeBaiduPan, typeXunleiPan, typeQuarkPan:
		updateMap = map[string]any{"file_name": req.Name, "file_url": req.Url}
		switch req.SourceType {
		case typePansoso:
			info, errF := dlp.NewResultModel().RelationFirst(mysql.WithId(req.Id))
			resultId = info.ResultId
			err = errF
		case type56Wangpan:
			info, errF := dlw.NewResultModel().RelationFirst(mysql.WithId(req.Id))
			resultId = info.ResultId
			err = errF
		case typeAliPan, typeBaiduPan, typeQuarkPan, typeXunleiPan:
			info, errF := dls.NewResultModel().RelationFirst(mysql.WithId(req.Id))
			resultId = info.ResultId
			err = errF
		}
	}
	if err != nil {
		return err
	}

	err = getUpdater(req.SourceType).UpdateAny(resultId, updateMap)
	return err
}

type generalTask interface {
	dlg.Task | dlw.Task | dld.Task | dlt.Task | dlp.Task | baidulibrary.Task | *dd8.Task
}

type generalDef struct {
	Keyword   string    `json:"keyword"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func generalResultConv[T generalTask](list []T) []generalDef {
	result := make([]generalDef, 0, len(list))
	for i := range list {
		var item generalDef
		bs, _ := sonic.Marshal(list[i])
		_ = sonic.Unmarshal(bs, &item)
		result = append(result, item)
	}
	return result
}

func TaskKeywordAgg(sourceType uint64, rsp *pb.DlpTaskAggResponse) error {
	var tasks = make([]generalDef, 0)
	var err error
	switch sourceType {
	case typeGithub:
		l, _, e := dlg.NewTaskModel().List(0, 0, mysql.WithSelect("`keyword`"))
		tasks = generalResultConv(l)
		err = e
	case typeGitee:
		l, _, e := dlt.NewTaskModel().List(0, 0, mysql.WithSelect("`keyword`"))
		tasks = generalResultConv(l)
		err = e
	case typeBaiduLibrary:
		l, _, e := baidulibrary.NewTaskModel().List(0, 0, mysql.WithSelect("`keyword`"))
		tasks = generalResultConv(l)
		err = e
	case typeDocin:
		l, _, e := dld.NewTaskModel().List(0, 0, mysql.WithSelect("`keyword`"))
		tasks = generalResultConv(l)
		err = e
	case typePansoso:
		l, _, e := dlp.NewTaskModel().List(0, 0, mysql.WithSelect("`keyword`"))
		tasks = generalResultConv(l)
		err = e
	case type56Wangpan:
		l, _, e := dlw.NewTaskModel().List(0, 0, mysql.WithSelect("`keyword`"))
		tasks = generalResultConv(l)
		err = e
	case typeDoc88:
		l, _, e := dd8.NewDoc88Fetcher().TaskList(0, 0, mysql.WithSelect("`keyword`"))
		tasks = generalResultConv(l)
		err = e
	}

	if err != nil {
		return err
	}

	for _, v := range tasks {
		rsp.Keywords = append(rsp.Keywords, v.Keyword)
	}
	return nil
}
