package dlp

import (
	"micro-service/pkg/utils"
	"sort"
	"strings"
	"time"

	"golang.org/x/exp/constraints"
)

func batchProgress(current, batch, total int) float32 {
	maxBatch := total / batch
	if total%batch > 0 {
		maxBatch++
	}
	return float32(current * 100 / maxBatch)
}

func SortAndHash(l []string) (string, []string) {
	list, _ := utils.ListFunc(l, func(s string) (string, bool) {
		return strings.ToLower(s), s != ""
	})
	list = utils.ListDistinct(list)
	if len(list) == 0 {
		return "", nil
	}
	sort.Strings(list)
	return utils.Md5sHash(keywordJoin(list), false), list
}

func keywordJoin(l []string) string {
	return strings.Join(l, "|+|")
}

func keywordSplit(keyword string) []string {
	return strings.Split(keyword, "|+|")
}

func isTaskUpdate[T constraints.Integer](force T, updatedAt time.Time) bool {
	return force == 1 || time.Now().After(updatedAt.Add(7*utils.Day))
}

func firstKeyword(keywords []string) string {
	for _, v := range keywords {
		if v != "" {
			return v
		}
	}
	return ""
}

func parseDate(d1, d2 string) (time.Time, time.Time, error) {
	t1, err := time.ParseInLocation(utils.DateLayout, d1, time.Now().Location())
	if err != nil {
		return t1, t1, err
	}
	t2, err := time.ParseInLocation(utils.DateLayout, d2, time.Now().Location())
	if err != nil {
		return t1, t1, err
	}
	t2 = t2.AddDate(0, 0, 1)
	return t1, t2, nil
}
