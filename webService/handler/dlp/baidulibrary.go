package dlp

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"micro-service/coreService/handler/dlp"
	core "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	baidu "micro-service/middleware/mysql/baidulibrary"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"gorm.io/gorm"
)

func BaiduLibrarySearch(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keyword := firstKeyword(req.Keyword)
	if keyword == "" {
		return errors.New("关键字不可为空")
	}

	var (
		isQuery     = false
		keywordHash = utils.Md5sHash(keyword, false)
		client      = baidu.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
		var info = baidu.Task{Keyword: keyword, KeywordHash: keywordHash, Progress: 1, Status: baidu.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt) {
			err := client.UpdateAny(uint(item.Id), map[string]any{"status": baidu.StatusDoing, "progress": 1})
			if err != nil {
				return err
			}
			isQuery = true
		}
	}

	if isQuery {
		// 异步获取百度文库数据
		go baiduLibrarySearchTaskSync(context.Background(), rsp.TaskId, keyword)
	}

	return nil
}

func baiduLibrarySearchTaskSync(ctx context.Context, taskId uint64, keyword string) {
	err := baiduLibrarySearch(context.Background(), taskId, keyword)
	if err != nil {
		log.Error(err)
	}

	err = baidu.NewTaskModel().UpdateAny(uint(taskId), map[string]any{
		"status":   baidu.StatusFinished,
		"progress": 100,
	})
	if err != nil {
		log.Errorf("[BaiduLibrary]Update task_id->%d progress failed: %v", taskId, err)
	}
}

func baiduLibrarySearch(ctx context.Context, taskId uint64, keyword string) error {
	var (
		rsp          = new(core.BaiduLibraryResponse)
		resultClient = baidu.NewResultModel()
	)

	err := dlp.GetBaiduLibrary(ctx, rsp, keyword, 20) // fetch 20 pages, per page is 10
	if err != nil {
		log.WithContextWarnf(ctx, "[BaiduLibrary]Get task_id->%d baidulibrary data failed in task mode, because of %v", taskId, err)
	}
	if len(rsp.GetResult()) == 0 {
		return nil
	}

	got := make([]*baidu.BaiduLibraryResult, 0, len(rsp.Result))
	for i := range rsp.Result {
		got = append(got, &baidu.BaiduLibraryResult{
			TaskId:     uint(taskId),
			Title:      rsp.Result[i].Title,
			Url:        rsp.Result[i].Url,
			Address:    extractAddress(rsp.Result[i].Url),
			Screenshot: rsp.Result[i].Screenshot,
		})
	}

	list, err := resultClient.ListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("[BaiduLibrary]Get task_id->%d result data from mysql failed: %v", taskId, err)
	}

	err = resultClient.CompareAndUpdate(list, got)
	if err != nil {
		return err
	}
	return nil
}

func extractAddress(s string) string {
	index := strings.Index(s, "?")
	if index > 0 {
		s = s[:index]
	}
	return s
}

func BaiduLibraryTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := baidu.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("查询任务id=%d记录未找到", taskId)
		}
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress

	return nil
}

func BaiduLibraryTaskResult(taskId uint64, rsp *pb.BaiduLibraryResponse) error {
	l, err := baidu.NewResultModel().ListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return err
	}

	for i := range l {
		rsp.Items = append(rsp.Items, &pb.BaiduLibraryResponseItem{
			Id:         l[i].Id,
			Address:    l[i].Address,
			Title:      l[i].Title,
			Url:        l[i].Url,
			Screenshot: l[i].Screenshot,
		})
	}
	return nil
}
