package dlp

import (
	"context"
	"errors"
	"fmt"

	"micro-service/coreService/handler/dlp"
	"micro-service/middleware/mysql"
	docin "micro-service/middleware/mysql/dataleak_docin"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"gorm.io/gorm"
)

// 数据泄露-豆丁

func DocinSearchByTask(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keyword := firstKeyword(req.Keyword)
	if keyword == "" {
		return errors.New("关键字不可为空")
	}

	var (
		isQuery     = false
		keywordHash = utils.Md5sHash(keyword, false)
		client      = docin.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
		var info = docin.Task{Keyword: keyword, KeywordHash: keywordHash, Progress: 1, Status: docin.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt) {
			err := client.UpdateAny(uint(item.Id), map[string]any{"status": docin.StatusDoing, "progress": 1})
			if err != nil {
				return err
			}
			isQuery = true
		}
	}

	if isQuery {
		// 异步获取豆丁数据
		go docinSearchTaskSync(context.Background(), rsp.TaskId, keyword)
	}

	return nil
}

func docinSearchTaskSync(ctx context.Context, taskId uint64, keyword string) {
	err := docinSearch(ctx, taskId, keyword)
	if err != nil {
		log.Error(err)
	}

	err = docin.NewTaskModel().UpdateAny(uint(taskId), map[string]any{
		"status":   docin.StatusFinished,
		"progress": 100,
	})
	if err != nil {
		log.Errorf("[Docin]Update task_id->%d progress failed: %v", taskId, err)
	}
}

func docinSearch(ctx context.Context, taskId uint64, keyword string) error {
	resultClient := docin.NewResultModel()
	docinResult, err := dlp.GetDocinSearch(ctx, keyword, 20) // fetch 5 pages, max 200 items
	if err != nil {
		return fmt.Errorf("[Docin]Get task_id->%d docin data failed in task mode, because of %v", taskId, err)
	}
	got := make([]*docin.DocinResult, 0, len(docinResult))
	for i := range docinResult {
		got = append(got, &docin.DocinResult{
			TaskId:     uint(taskId),
			DocTitle:   docinResult[i].Title,
			DocUrl:     docinResult[i].Url,
			Screenshot: docinResult[i].Screenshot,
		})
	}

	list, err := resultClient.ListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("[Docin]Get task_id->%d result data from mysql failed: %v", taskId, err)
	}

	err = resultClient.CompareAndUpdate(list, got)
	if err != nil {
		return err
	}
	return nil
}

func DocinTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := docin.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("查询任务id=%d记录未找到", taskId)
		}
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

func DocinTaskResult(taskId uint64) ([]*pb.DocinResultResponseItem, error) {
	l, err := docin.NewResultModel().ListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return nil, err
	}

	var result = make([]*pb.DocinResultResponseItem, 0, len(l))
	for i := range l {
		result = append(result, &pb.DocinResultResponseItem{
			Id:         l[i].Id,
			Title:      l[i].DocTitle,
			Url:        l[i].DocUrl,
			Screenshot: l[i].Screenshot,
		})
	}
	return result, nil
}
