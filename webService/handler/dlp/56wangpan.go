package dlp

import (
	"context"
	"errors"
	"fmt"
	"micro-service/coreService/handler/dlp"
	corePb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	wp56 "micro-service/middleware/mysql/dataleak_56wangpan"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"

	"gorm.io/gorm"
)

func Wangpan56SearchByTask(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	keywordHash, keywords := SortAndHash(req.Keyword)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	var (
		isQuery = false
		client  = wp56.NewTaskModel()
	)

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
		var info = wp56.Task{Keyword: keywordJoin(keywords), KeywordHash: keywordHash, Progress: 1, Status: wp56.StatusDoing}
		if err := client.Create(&info); err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt) {
			err := client.UpdateAny(uint(item.Id), map[string]any{"status": wp56.StatusDoing, "progress": 1})
			if err != nil {
				return err
			}
			isQuery = true
		}
	}

	if isQuery {
		//	异步获取56网盘数据
		go wangpan56SearchTaskSync(context.Background(), rsp.TaskId, req.Keyword)
	}

	return nil
}

func get56wangpanSearchResult(ctx context.Context, taskId uint64, keywords []string) error {
	var rsp = new(corePb.NetDiskResponse)
	err := dlp.Search56pan(ctx, keywords, rsp, 200) // 最大200条
	if err != nil {
		return err
	}
	got := make([]*wp56.Pan56Result, 0, len(rsp.Result))
	for i := range rsp.Result {
		got = append(got, &wp56.Pan56Result{
			TaskId:     uint(taskId),
			FileName:   rsp.Result[i].FileName,
			FileUrl:    rsp.Result[i].Url,
			Screenshot: rsp.Result[i].Screenshot,
		})
	}

	client := wp56.NewResultModel()
	list, err := client.ListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return fmt.Errorf("[56wangpan]Get task_id->%d result data from mysql failed: %v", taskId, err)
	}

	err = client.CompareAndUpdate(list, got)
	if err != nil {
		return err
	}
	return nil
}

func wangpan56SearchTaskSync(ctx context.Context, taskId uint64, keywords []string) {
	err := get56wangpanSearchResult(ctx, taskId, keywords)
	if err != nil {
		log.WithContextError(ctx, err)
	}

	client := wp56.NewTaskModel()
	err = client.UpdateAny(uint(taskId), map[string]any{"status": wp56.StatusFinished, "progress": 100})
	if err != nil {
		log.Errorf("[56wangpan]Update task_id->%d progress failed: %v", taskId, err)
	}
}

func Wangpan56TaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := wp56.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("查询任务id=%d记录未找到", taskId)
		}
		return err
	}

	rsp.TaskId = int64(info.Id)
	rsp.Keywords = keywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress
	return nil
}

func Wangpan56TaskResult(taskId uint64, rsp *pb.Wangpan56ResultResponse) error {
	l, err := wp56.NewResultModel().ListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return err
	}

	items := make([]*pb.Wangpan56ResultResponseItem, 0, len(l))
	for i := range l {
		items = append(items, &pb.Wangpan56ResultResponseItem{
			Id:         l[i].Id,
			FileName:   l[i].FileName,
			FileUrl:    l[i].FileUrl,
			Screenshot: l[i].Screenshot,
		})
	}

	rsp.Items = items
	return nil
}
