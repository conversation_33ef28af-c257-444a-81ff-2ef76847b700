package dlp

import (
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"gorm.io/gorm"

	"micro-service/coreService/handler/dlp"
	"micro-service/middleware/mysql"
	dlg "micro-service/middleware/mysql/dataleak_github"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

func GithubSearchByTask(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	log.Info("[GitHub]Search keyword by task: %v", req.Keyword)
	var (
		isQuery               bool
		keywordHash, keywords = dlg.SortAndHash(req.Keyword)
		client                = dlg.NewTaskModel()
	)
	if len(keywords) == 0 {
		return errors.New("查询关键字不可为空")
	}

	item, err := client.First(mysql.WithColumnValue("keyword_hash", keywordHash))
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return err
		}
		join := dlg.KeywordJoin(keywords)
		var info = dlg.Task{Keyword: join, KeywordHash: keywordHash, Progress: 1, Status: dlg.StatusDoing}
		err = client.Create(&info)
		if err != nil {
			return err
		}
		rsp.TaskId = info.Id
		isQuery = true
	} else {
		rsp.TaskId = item.Id
		if isTaskUpdate(req.Force, item.UpdatedAt) {
			if err := client.UpdateAny(uint(item.Id), map[string]any{
				"status":   dlg.StatusDoing,
				"progress": 1,
			}); err != nil {
				return err
			}
			isQuery = true
		}
	}

	if isQuery {
		// 异步获取GitHub查询数据
		go queryGitHubWithCode(context.Background(), uint(rsp.TaskId), 500, keywords)
	}

	return nil
}

func queryGitHubWithCode(ctx context.Context, taskId, maxTotal uint, keyword []string) {
	var (
		batch        = 150
		ch           = make(chan dlg.GithubResult)
		list         = make([]*dlg.GithubResult, 0, batch)
		currentBatch = 0
		conn         = dlg.NewResultModel()
		taskClient   = dlg.NewTaskModel()
	)

	go func() {
		ctxWithTimeout, cancel := context.WithTimeout(ctx, time.Hour)
		defer cancel()
		err := dlp.GetGitHubClient().GetSearchCodeResult(ctxWithTimeout, keyword, int(maxTotal), ch)
		if err != nil {
			log.WithContextErrorf(ctx, "GitHub search with code in task_id: %d: %v", taskId, err)
		}
	}()

	oldResult, _ := conn.ListAll(mysql.WithColumnValue("task_id", taskId))
	for v := range ch {
		list = append(list, &dlg.GithubResult{
			TaskId:      taskId,
			RepoName:    v.RepoName,
			RepoUrl:     v.RepoUrl,
			RepoDesc:    v.RepoDesc,
			CodeUrl:     v.CodeUrl,
			CodeSnippet: v.CodeSnippet,
			ScreenShot:  v.ScreenShot,
			Sha:         v.Sha,
			Language:    getLanguageBySuffix(filepath.Ext(v.CodeUrl)),
		})
		if len(list) == batch {
			currentBatch++
			err := conn.CompareAndUpdate(oldResult, list)
			if err != nil {
				log.WithContextErrorf(ctx, "[Data Leak GitHub]CompareAndUpdate: %v", err)
			}
			// 更新进度
			err = taskClient.UpdateAny(taskId, map[string]any{
				"progress": batchProgress(currentBatch, batch, int(maxTotal)),
			})
			if err != nil {
				log.WithContextErrorf(ctx, "[Gitee]Search Gitee by task update progress failed: %v", err)
			}
			list = make([]*dlg.GithubResult, 0, 150)
		}
	}
	err := conn.CompareAndUpdate(oldResult, list)
	if err != nil {
		log.WithContextErrorf(ctx, "[Data Leak GitHub]CompareAndUpdate: %v", err)
	}

	err = dlg.NewTaskModel().UpdateAny(taskId, map[string]any{
		"progress": 100,
		"status":   dlg.StatusFinished,
	})
	if err != nil {
		log.WithContextErrorf(ctx, "[Data Leak GitHub]GitHub search on task_id: %d, %v", taskId, err)
	}
}

func GithubTaskInfo(taskId uint64, rsp *pb.GitHubTaskInfoResponse) error {
	info, err := dlg.NewTaskModel().First(mysql.WithColumnValue("id", taskId))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("查询任务id=%d记录未找到", taskId)
		}
		return err
	}

	rsp.TaskId = int64(info.Id)
	// rsp.Keywords = dlg.KeywordSplit(info.Keyword)
	rsp.Status = int64(info.Status)
	rsp.Progress = info.Progress

	return nil
}

// GithubTaskResult 获取任务结果
func GithubTaskResult(taskId uint64) ([]*pb.GitHubCode, error) {
	l, err := dlg.NewResultModel().ListAll(mysql.WithColumnValue("task_id", taskId))
	if err != nil {
		return nil, err
	}

	var result = make([]*pb.GitHubCode, 0, len(l))
	for i := range l {
		result = append(result, &pb.GitHubCode{
			RepoName:    l[i].RepoName,
			RepoUrl:     l[i].RepoUrl,
			RepoDesc:    l[i].RepoDesc,
			CodeUrl:     l[i].CodeUrl,
			Screenshot:  l[i].ScreenShot,
			Sha:         l[i].Sha,
			CodeSnippet: l[i].CodeSnippet,
			Language:    l[i].Language,
		})
	}
	return result, nil
}

func getLanguageBySuffix(suffix string) string {
	suffix = strings.ToLower(suffix)
	if v, ok := githubLanguageSuffix[suffix]; ok {
		return v
	}
	return "Other"
}

var githubLanguageSuffix = map[string]string{
	".c":          "C",
	".h":          "C",
	".cpp":        "C++",
	".c++":        "C++",
	".cp":         "C++",
	".cmake":      "CMake",
	".lisp":       "Lisp",
	".java":       "Java",
	".jsp":        "Java Server Pages",
	".php":        "PHP",
	".go":         "Go",
	".lua":        "Lua",
	".md":         "Markdown",
	".py":         "Python",
	".pyp":        "Python",
	".pyt":        "Python",
	".sh":         "Shell",
	".bash":       "Shell",
	".zsh":        "Shell",
	".ksh":        "Shell",
	".js":         "JavaScript",
	".ts":         "TypeScript",
	".tsx":        "TypeScript",
	".vue":        "Vue",
	".csv":        "CSV",
	".html":       "HTML",
	".txt":        "Text",
	".xml":        "XML",
	".css":        "CSS",
	".json":       "JSON",
	".yaml":       "YAML",
	".yml":        "YAML",
	".sql":        "SQL",
	".plsql":      "PLSQL",
	".dockerfile": "Dockerfile",
	".mk":         "Makefile",
	".mkfile":     "Makefile",
	".swift":      "Swift",
}
