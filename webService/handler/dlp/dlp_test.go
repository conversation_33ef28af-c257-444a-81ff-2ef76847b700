package dlp

import (
	"micro-service/initialize/es"
	mysqllib "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
	"strings"
	"testing"
)

func Init() {
	log.Init()
	cfg.InitLoadCfg()
	_ = mysqllib.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	es.GetInstance(cfg.LoadElastic())
}

func TestTaskResultList(t *testing.T) {
	Init()
	tests := []struct {
		name string
		Data pb.DlpTaskResultListRequest
	}{
		{
			name: "有效查询",
			Data: pb.DlpTaskResultListRequest{
				UserId:           1,
				OperateCompanyId: 1,
				Page:             1,
				PerPage:          10,
				SourceType:       12,
				Search:           "银行",
				Name:             "",
				Url:              "",
				Keyword:          "",
				Language:         "",
				CreatedAt:        []string{"2024-01-01", "2025-12-31"},
				UpdatedAt:        []string{"2024-01-01", "2025-12-31"},
			},
		},
		{
			name: "无效查询",
			Data: pb.DlpTaskResultListRequest{
				UserId:           1,
				OperateCompanyId: 1,
				Page:             1,
				PerPage:          10,
				SourceType:       0,
				Search:           "",
				Name:             "",
				Url:              "",
				Keyword:          "",
				Language:         "",
				CreatedAt:        []string{"2024-01-01", "2025-12-31"},
				UpdatedAt:        []string{"2024-01-01", "2025-12-31"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rep := pb.DlpTaskResultListResponse{}
			err := TaskResultList(&tt.Data, &rep)
			if err != nil {
				t.Error(err)
			}
			if len(rep.Items) == 0 {
				t.Error("请求结果无数据")
			}
			if tt.Data.Search != "" {
				for _, item := range rep.Items {
					if strings.Index(item.Name, tt.Data.Search) == -1 {
						t.Error("请求结果数据不匹配")
					}
				}
			}
			t.Logf("%+v", rep)
		})
	}
}

func TestResultUpdate(t *testing.T) {
	Init()
	tests := []struct {
		name string
		Data pb.DlpResultUpdateRequest
	}{
		{
			name: "有效更新",
			Data: pb.DlpResultUpdateRequest{
				Id:         2383,
				SourceType: 12,
				Name:       "Update",
				Url:        "https://www.postman.com/planetary-equinox-540138/workspace/testapi/request/11653951-c7488f23-b0d5-489f-a0a7-82dca59e60f6 ",
				Language:   "other",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ResultUpdate(&tt.Data)
			if err != nil {
				t.Error(err)
			}
		})
	}
}
