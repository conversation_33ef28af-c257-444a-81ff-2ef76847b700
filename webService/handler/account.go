package handler

import (
	"context"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"micro-service/webService/handler/asset_account"
	"micro-service/webService/handler/ignore_assets"
	"micro-service/webService/handler/threaten_assets"
	"micro-service/webService/handler/unsure_assets"
	"micro-service/webService/service/account"

	pb "micro-service/webService/proto"

	"go-micro.dev/v4/errors"
)

func (*Web) AccountOpenByHand(ctx context.Context, req *pb.AccountOpenByHandRequest, rsp *pb.AccountOpenByHandResponse) error {
	log.WithContextInfof(ctx, "[账户手动开通-手动创建] Received Web.AccountOpenByHand request")
	passwd, err := account.NewAccountService().AccountOpenByHand(req)
	if err != nil {
		log.WithContextErrorf(ctx, "[账户手动开通-手动创建] Get AccountOpenByHandResponse failed: %v", err)
		if errx.IsFrontEnd(err) {
			return errors.InternalServerError(pb.ServiceName, err.Error())
		}
		return errors.InternalServerError(pb.ServiceName, errx.ErrFrontEndInternal.Error())
	}
	rsp.Passwd = passwd
	return nil
}

func (*Web) ListAccountOpenByHand(ctx context.Context, req *pb.ListAccountOpenByHandRequest, rsp *pb.ListAccountOpenByHandResponse) error {
	log.WithContextInfof(ctx, "[账户手动开通-列表展示] Received Web.ListAccountOpenByHand request")
	data, err := account.NewAccountService().ListAccountOpenByHand(req.UserId)
	if err != nil {
		log.WithContextErrorf(ctx, "[账户手动开通-列表展示] Get ListAccountOpenByHandResponse failed: %v", err)
		if errx.IsFrontEnd(err) {
			return errors.InternalServerError(pb.ServiceName, err.Error())
		}
		return errors.InternalServerError(pb.ServiceName, errx.ErrFrontEndInternal.Error())
	}
	rsp.Data = data
	return nil
}

// IpAssetList 获取IP资产列表
func (w *Web) IpAssetList(ctx context.Context, req *pb.IpAssetListRequest, resp *pb.IpAssetListResponse) error {
	log.WithContextInfof(ctx, "[IP资产列表] Received Web.IpAssetList request: %v", req)
	err := asset_account.AssetsAccountIpList(ctx, req, resp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP资产列表] Get IpAssetListResponse failed: %v", err)
		return errors.InternalServerError(pb.ServiceName, err.Error())
	}
	return nil
}

// IpAssetDelete 删除IP资产
func (w *Web) IpAssetDelete(ctx context.Context, req *pb.IpAssetActionRequest, resp *pb.Empty) error {
	log.WithContextInfof(ctx, "[IP资产删除] Received Web.IpAssetDelete request: %v", req)
	err := asset_account.AssetsAccountIpDelete(ctx, req, resp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP资产删除] Get IpAssetDeleteResponse failed: %v", err)
		return errors.InternalServerError(pb.ServiceName, err.Error())
	}
	return nil
}

// IpAssetExport 导出IP资产
func (w *Web) IpAssetExport(ctx context.Context, req *pb.IpAssetActionRequest, resp *pb.FileExportResponse) error {
	log.WithContextInfof(ctx, "[IP资产导出] Received Web.IpAssetExport request: %v", req)
	err := asset_account.AssetsAccountIpExport(ctx, req, resp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP资产导出] Get IpAssetExportResponse failed: %v", err)
		return errors.InternalServerError(pb.ServiceName, err.Error())
	}
	return nil
}

// IpUnsureAssetDelete 删除疑似IP资产
func (w *Web) IpUnsureAssetDelete(ctx context.Context, req *pb.IpAssetActionRequest, resp *pb.Empty) error {
	log.WithContextInfof(ctx, "[IP资产删除] Received Web.IpUnsureAssetDelete request: %v", req)
	err := unsure_assets.IpUnsureAssetDelete(ctx, req, resp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP资产删除] Get IpUnsureAssetDeleteResponse failed: %v", err)
		return errors.InternalServerError(pb.ServiceName, err.Error())
	}
	return nil
}

// IpIgnoreAssetDelete 删除忽略IP资产
func (w *Web) IpIgnoreAssetDelete(ctx context.Context, req *pb.IpAssetActionRequest, resp *pb.Empty) error {
	log.WithContextInfof(ctx, "[IP资产删除] Received Web.IpIgnoreAssetDelete request: %v", req)
	err := ignore_assets.IpIgnoreAssetDelete(ctx, req, resp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP资产删除] Get IpIgnoreAssetDeleteResponse failed: %v", err)
		return errors.InternalServerError(pb.ServiceName, err.Error())
	}
	return nil
}

// IpThreatenAssetDelete 删除威胁IP资产
func (w *Web) IpThreatenAssetDelete(ctx context.Context, req *pb.IpAssetActionRequest, resp *pb.Empty) error {
	log.WithContextInfof(ctx, "[IP资产删除] Received Web.IpThreatenAssetDelete request: %v", req)
	err := threaten_assets.IpThreatenAssetDelete(ctx, req, resp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP资产删除] Get IpThreatenAssetDeleteResponse failed: %v", err)
		return errors.InternalServerError(pb.ServiceName, err.Error())
	}
	return nil
}
