package user_tokens

import (
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/user"
	pb "micro-service/webService/proto"
	"strconv"
)

func UserOnlineList(currentPage, perPage int64) (*pb.UserTokenListResponse, error) {
	Items, total, err := user.NewUserModel(mysql.GetInstance()).List(int(currentPage), int(perPage))
	if err != nil {
		return nil, err
	}
	pbItems := make([]*pb.UserTokenListResponse_BaseInfo, 0, len(Items))
	for i := range Items {
		pbItems = append(pbItems, &pb.UserTokenListResponse_BaseInfo{
			Id:    Items[i].Id,
			Name:  Items[i].Name.String,
			Email: Items[i].Email.String,
			Level: strconv.Itoa(int(Items[i].Level)),
		})
	}
	return &pb.UserTokenListResponse{Items: pbItems, Total: total, CurrentPage: currentPage, PerPage: perPage}, nil
}
