package login_assets

import (
	"context"
	"fmt"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/login_page_assets"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"net/url"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/xuri/excelize/v2"
)

// LoginAssetsList 登录入口资产列表
func LoginAssetsList(_ context.Context, req *pb.LoginAssetsListRequest, resp *pb.LoginAssetsListResponse) error {
	query := make([]mysql.HandleFunc, 0, 10)
	if req.Keyword != "" {
		query = append(query, mysql.WithWhere("`title` LIKE ? OR `url` LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%"))
	}
	if req.Url != "" {
		query = append(query, mysql.WithWhere("url = ?", req.Url))
	}
	if req.Status != 999 {
		query = append(query, mysql.WithWhere("status = ?", req.Status))
	}
	if req.Ip != "" {
		query = append(query, mysql.WithWhere("ip = ?", req.Ip))
	}
	if req.WebsiteMessageId != 0 {
		query = append(query, mysql.WithWhere("website_message_id = ?", req.WebsiteMessageId))
	}
	if len(req.CreatedAtRange) == 2 {
		start, end, err := utils.GenerateTimeBetween([2]string(req.CreatedAtRange))
		if err != nil {
			return err
		}
		query = append(query, mysql.WithBetween("created_at", start, end))
	}
	if len(req.UpdatedAtRange) == 2 {
		start, end, err := utils.GenerateTimeBetween([2]string(req.UpdatedAtRange))
		if err != nil {
			return err
		}
		query = append(query, mysql.WithBetween("updated_at", start, end))
	}
	if len(req.Title) > 0 {
		query = append(query, mysql.WithValuesIn("title", req.Title))
	}
	query = append(query, mysql.WithWhere("user_id = ?", req.UserId))
	query = append(query, mysql.WithOrder("updated_at DESC"))
	list, total, err := login_page_assets.NewModel().List(req.Page, req.PerPage, query...)
	if err != nil {
		return err
	}
	resp.Total = total
	resp.CurrentPage = req.Page
	resp.PerPage = req.PerPage
	for _, i := range list {
		baseFileName := filepath.Base(i.ImgUrl)
		fileNameWithoutExt := strings.TrimSuffix(baseFileName, filepath.Ext(baseFileName))
		resp.Items = append(resp.Items, &pb.LoginPageAsset{
			Id:               i.Id,
			Title:            i.Title,
			Url:              i.Url,
			Ip:               i.Ip,
			Port:             i.Port,
			ImgUrl:           storage.GenAPIDownloadPath(fileNameWithoutExt, i.ImgUrl),
			Status:           int64(i.Status),
			Node:             i.Node,
			CreatedAt:        i.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:        i.UpdatedAt.Format("2006-01-02 15:04:05"),
			UserId:           i.UserId,
			CompanyId:        i.CompanyId,
			UniqueKey:        i.UniqueKey,
			BatchId:          i.BatchId,
			DetectTaskId:     i.DetectTaskId,
			WebsiteMessageId: i.WebsiteMessageId,
		})
	}
	titles, err := login_page_assets.NewModel().ListTitle(req.UserId)
	if err != nil {
		return err
	}
	resp.Condition = &pb.LoginAssetsListCondition{
		Title: titles,
	}
	return nil
}

// LoginAssetsDelete 删除登录入口资产
func LoginAssetsDelete(_ context.Context, req *pb.LoginAssetsDeleteRequest, resp *pb.Empty) error {
	if len(req.Id) > 0 {
		return login_page_assets.NewModel().DeleteByID(req.UserId, req.Id...)
	}
	query := make([]mysql.HandleFunc, 0, 10)
	if req.Keyword != "" {
		query = append(query, mysql.WithWhere("`title` LIKE ? OR `url` LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%"))
	}
	if req.Url != "" {
		query = append(query, mysql.WithWhere("url = ?", req.Url))
	}
	if req.Status != 999 {
		query = append(query, mysql.WithWhere("status = ?", req.Status))
	}
	if req.Ip != "" {
		query = append(query, mysql.WithWhere("ip = ?", req.Ip))
	}
	if req.WebsiteMessageId != 0 {
		query = append(query, mysql.WithWhere("website_message_id = ?", req.WebsiteMessageId))
	}
	if len(req.CreatedAtRange) == 2 {
		start, end, err := utils.GenerateTimeBetween([2]string(req.CreatedAtRange))
		if err != nil {
			return err
		}
		query = append(query, mysql.WithBetween("created_at", start, end))
	}
	if len(req.UpdatedAtRange) == 2 {
		start, end, err := utils.GenerateTimeBetween([2]string(req.UpdatedAtRange))
		if err != nil {
			return err
		}
		query = append(query, mysql.WithBetween("updated_at", start, end))
	}
	if len(req.Title) > 0 && req.Title[0] != "" {
		query = append(query, mysql.WithValuesIn("title", req.Title))
	}
	query = append(query, mysql.WithWhere("user_id = ?", req.UserId))
	return login_page_assets.NewModel().DeleteAny(query...)
}

func LoginAssetsUpdate(_ context.Context, req *pb.LoginAssetsUpdateRequest, resp *pb.Empty) error {
	updates := map[string]any{
		"status": req.SetStatus,
	}
	if len(req.Id) > 0 {
		return login_page_assets.NewModel().UpdateAny(updates, mysql.WithWhere("user_id = ?", req.UserId), mysql.WithValuesIn("id", req.Id))
	}
	query := make([]mysql.HandleFunc, 0, 10)
	if req.Keyword != "" {
		query = append(query, mysql.WithWhere("`title` LIKE ? OR `url` LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%"))
	}
	if req.Url != "" {
		query = append(query, mysql.WithWhere("url = ?", req.Url))
	}
	if req.Status != 999 {
		query = append(query, mysql.WithWhere("status = ?", req.Status))
	}
	if req.Ip != "" {
		query = append(query, mysql.WithWhere("ip = ?", req.Ip))
	}
	if req.WebsiteMessageId != 0 {
		query = append(query, mysql.WithWhere("website_message_id = ?", req.WebsiteMessageId))
	}
	if len(req.CreatedAtRange) == 2 {
		start, end, err := utils.GenerateTimeBetween([2]string(req.CreatedAtRange))
		if err != nil {
			return err
		}
		query = append(query, mysql.WithBetween("created_at", start, end))
	}
	if len(req.UpdatedAtRange) == 2 {
		start, end, err := utils.GenerateTimeBetween([2]string(req.UpdatedAtRange))
		if err != nil {
			return err
		}
		query = append(query, mysql.WithBetween("updated_at", start, end))
	}
	if len(req.Title) > 0 && req.Title[0] != "" {
		query = append(query, mysql.WithValuesIn("title", req.Title))
	}
	query = append(query, mysql.WithWhere("user_id = ?", req.UserId))
	return login_page_assets.NewModel().UpdateAny(updates, query...)
}

func LoginAssetsCountStatus(_ context.Context, req *pb.LoginAssetsCountStatusRequest, resp *pb.LoginAssetsCountStatusResponse) (err error) {
	resp.Default, resp.Confirm, resp.Ingore, err = login_page_assets.NewModel().GetStatusCount(req.UserId)
	return
}

func LoginAssetsExport(_ context.Context, req *pb.LoginAssetsDeleteRequest, resp *pb.FileExportResponse) error {
	// 获取导出数据
	query := make([]mysql.HandleFunc, 0, 10)
	if len(req.Id) > 0 {
		query = append(query, mysql.WithValuesIn("id", req.Id))
	} else {
		if req.Keyword != "" {
			query = append(query, mysql.WithWhere("`title` LIKE ? OR `url` LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%"))
		}
		if req.Url != "" {
			query = append(query, mysql.WithWhere("url = ?", req.Url))
		}
		if req.Status != 999 {
			query = append(query, mysql.WithWhere("status = ?", req.Status))
		}
		if req.Ip != "" {
			query = append(query, mysql.WithWhere("ip = ?", req.Ip))
		}
		if req.WebsiteMessageId != 0 {
			query = append(query, mysql.WithWhere("website_message_id = ?", req.WebsiteMessageId))
		}
		if len(req.CreatedAtRange) == 2 {
			start, end, err := utils.GenerateTimeBetween([2]string(req.CreatedAtRange))
			if err != nil {
				return err
			}
			query = append(query, mysql.WithBetween("created_at", start, end))
		}
		if len(req.UpdatedAtRange) == 2 {
			start, end, err := utils.GenerateTimeBetween([2]string(req.UpdatedAtRange))
			if err != nil {
				return err
			}
			query = append(query, mysql.WithBetween("updated_at", start, end))
		}
		if len(req.Title) > 0 && req.Title[0] != "" {
			query = append(query, mysql.WithValuesIn("title", req.Title))
		}
	}
	query = append(query, mysql.WithWhere("user_id = ?", req.UserId))
	list, err := login_page_assets.NewModel().ListAll(query...)
	if err != nil {
		return err
	}

	// 生成Excel文件
	file := excelize.NewFile()
	sheet := "登录入口资产数据"
	err = file.SetSheetName("Sheet1", sheet)
	if err != nil {
		return err
	}

	// 设置表头
	headers := []string{"URL", "标题", "状态", "发现时间"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err = file.SetCellValue(sheet, cell, header)
		if err != nil {
			return err
		}
	}

	// 写入数据
	for i, r := range list {
		row := i + 2
		if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Url); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), r.Title); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), getAssetsStatus(r.Status)); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("D%d", row), r.CreatedAt.Format(time.DateTime)); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
	}

	// 保存文件
	filename := fmt.Sprintf("login_assets_%s.xlsx", time.Now().Format("20060102150405"))
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	if err := file.SaveAs(storagePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	fp, err := utils.DownloadFileEncrypt(accessPath, "登录入口资产数据_"+time.Now().Format(time.DateOnly)+path.Ext(filename), "", false)
	if err != nil {
		return fmt.Errorf("生成下载路径失败: %v", err)
	}
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(filename))

	// 设置返回的文件路径
	resp.Url = apiFilePath
	return nil
}

func getAssetsStatus(status int) string {
	switch status {
	case 0:
		return "待处理"
	case 1:
		return "确认"
	case 2:
		return "忽略"
	}
	return ""
}

func LoginAssetsEdit(_ context.Context, req *pb.LoginAssetsEditRequest, resp *pb.Empty) error {
	// 获取登录入口资产
	if req.Id == 0 {
		return fmt.Errorf("id is required")
	}
	updates := make(map[string]any)
	if req.Title != "" {
		updates["title"] = req.Title
	}
	if req.Img != "" {
		url, err := storage.ParseDownloadUrl(req.Img)
		if err != nil {
			return err
		}
		updates["img_url"] = url.Url
	}
	return login_page_assets.NewModel().UpdateAny(updates, mysql.WithWhere("id = ?", req.Id), mysql.WithWhere("user_id = ?", req.UserId))
}

func LoginAssetsImport(_ context.Context, req *pb.LoginAssetsImportRequest, resp *pb.Empty) error {
	if len(req.Url) == 0 {
		return fmt.Errorf("URL内容为空，请重新上传文件")
	}
	if len(req.Url) > 1000 {
		return fmt.Errorf("单次最多上传1000个URL")
	}
	for i, u := range req.Url {
		if !isValidURL(u) {
			return fmt.Errorf("第%d行的url格式非法，请正确填写", i)
		}
	}
	batchId := uuid.New().String()
	var list []*login_page_assets.LoginPageAssets
	for _, u := range req.Url {
		host, port := GetIPAndPort(u)
		item := &login_page_assets.LoginPageAssets{
			Url:       u,
			Title:     "",
			Ip:        host,
			Port:      port,
			UserId:    req.UserId,
			CompanyId: req.CompanyId,
			UniqueKey: fmt.Sprintf("%d-%s", req.UserId, u),
			BatchId:   batchId,
		}
		list = append(list, item)
	}
	err := login_page_assets.NewModel().CreateDistinctByUrl(req.UserId, list)
	if err != nil {
		log.Errorf("批量导入登录入口资产失败: %v", err)
		return fmt.Errorf("批量导入登录入口资产失败")
	}

	if cfg.ExecGolangJob() {
		err = asyncq.LoginPageCrawlerJob.Dispatch(req.UserId, batchId)
		if err != nil {
			log.Errorf("批量导入登录入口 - 爬虫任务入队失败: %v", err)
		}
		err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
		if err != nil {
			log.Errorf("批量导入登录入口 - 缓存更新任务入队失败: %v", err)
		}
	} else {
		//调用php的job
		err = asyncq.LoginPageCrawlerJob.Dispatch(req.UserId, batchId)
		if err != nil {
			log.Errorf("批量导入登录入口 - 爬虫任务入队失败: %v", err)
		}
		err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
		if err != nil {
			log.Errorf("批量导入登录入口 - 缓存更新任务入队失败: %v", err)
		}

	}
	log.Infof("批量导入登录入口 - 操作者%d，批次ID: %s", req.UserId, batchId)
	return nil
}

func isValidURL(inputURL string) bool {
	_, err := url.ParseRequestURI(inputURL)
	if err != nil {
		return false
	}
	u, err := url.Parse(inputURL)
	if err != nil || u.Scheme == "" || u.Host == "" {
		return false
	}
	return true
}

// GetIPAndPort 解析 URL 并返回 IP/Host 和端口
func GetIPAndPort(rawURL string) (string, string) {
	u, err := url.Parse(rawURL)
	if err != nil {
		log.Errorf("解析URL出错: %v\n", err)
		return "", ""
	}
	host := u.Hostname()
	port := u.Port()
	// 处理默认端口
	if port == "" {
		switch strings.ToLower(u.Scheme) {
		case "https":
			port = "443"
		case "http":
			port = "80"
		}
	}
	return host, port
}
