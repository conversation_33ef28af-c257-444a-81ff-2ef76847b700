package login_assets

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

// 初始化测试环境
func Init() {
	cfg.InitLoadCfg()
	log.Init()
	mysql.SetTestEnv(true)
	redis.SetTestEnv(true)
	mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

func TestLoginAssetsList(t *testing.T) {
	Init()

	t.Run("normal list", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT count.*").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		mock.ExpectQuery("SELECT .* FROM `login_page_assets`").
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "title", "url", "ip", "port", "img_url", "status", "node",
				"created_at", "updated_at", "user_id", "company_id", "unique_key",
				"batch_id", "detect_task_id", "website_message_id",
			}).AddRow(
				1, "Test Title", "https://example.com", "***********", "443",
				"app/public/test.png", 0, "node1", time.Now(), time.Now(),
				123, 456, "123-https://example.com", "batch123", "task123", 789,
			))

		mock.ExpectQuery("SELECT DISTINCT.*").
			WillReturnRows(sqlmock.NewRows([]string{"title"}).AddRow("Test Title"))

		req := &pb.LoginAssetsListRequest{
			UserId:  123,
			Page:    1,
			PerPage: 10,
		}
		resp := &pb.LoginAssetsListResponse{}

		err := LoginAssetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), resp.Total)
		assert.Len(t, resp.Items, 1)
	})

	t.Run("database error", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT count.*").
			WillReturnError(errors.New("database error"))

		req := &pb.LoginAssetsListRequest{
			UserId:  123,
			Page:    1,
			PerPage: 10,
		}
		resp := &pb.LoginAssetsListResponse{}

		err := LoginAssetsList(context.Background(), req, resp)
		assert.Error(t, err)
	})
}

func TestLoginAssetsDelete(t *testing.T) {
	Init()

	t.Run("delete by id list", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `login_page_assets`").
			WillReturnResult(sqlmock.NewResult(1, 2))
		mock.ExpectCommit()

		req := &pb.LoginAssetsDeleteRequest{
			UserId: 123,
			Id:     []uint64{1, 2},
		}
		resp := &pb.Empty{}

		err := LoginAssetsDelete(context.Background(), req, resp)
		assert.NoError(t, err)
	})

	t.Run("database error", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `login_page_assets`").
			WillReturnError(errors.New("database error"))
		mock.ExpectRollback()

		req := &pb.LoginAssetsDeleteRequest{
			UserId: 123,
			Id:     []uint64{1, 2},
		}
		resp := &pb.Empty{}

		err := LoginAssetsDelete(context.Background(), req, resp)
		assert.Error(t, err)
	})
}

func TestLoginAssetsUpdate(t *testing.T) {
	Init()

	t.Run("update by id list", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `login_page_assets`").
			WillReturnResult(sqlmock.NewResult(1, 2))
		mock.ExpectCommit()

		req := &pb.LoginAssetsUpdateRequest{
			UserId:    123,
			Id:        []uint64{1, 2},
			SetStatus: 1,
		}
		resp := &pb.Empty{}

		err := LoginAssetsUpdate(context.Background(), req, resp)
		assert.NoError(t, err)
	})

	t.Run("database error", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `login_page_assets`").
			WillReturnError(errors.New("database error"))
		mock.ExpectRollback()

		req := &pb.LoginAssetsUpdateRequest{
			UserId:    123,
			Id:        []uint64{1, 2},
			SetStatus: 1,
		}
		resp := &pb.Empty{}

		err := LoginAssetsUpdate(context.Background(), req, resp)
		assert.Error(t, err)
	})
}

func TestLoginAssetsCountStatus(t *testing.T) {
	Init()

	t.Run("normal count", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT `status` FROM `login_page_assets`").
			WillReturnRows(sqlmock.NewRows([]string{"status"}).
				AddRow(0).AddRow(1).AddRow(2).AddRow(0).AddRow(1))

		req := &pb.LoginAssetsCountStatusRequest{
			UserId: 123,
		}
		resp := &pb.LoginAssetsCountStatusResponse{}

		err := LoginAssetsCountStatus(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Default)
		assert.Equal(t, int64(2), resp.Confirm)
		assert.Equal(t, int64(1), resp.Ingore)
	})

	t.Run("database error", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT `status` FROM `login_page_assets`").
			WillReturnError(errors.New("database error"))

		req := &pb.LoginAssetsCountStatusRequest{
			UserId: 123,
		}
		resp := &pb.LoginAssetsCountStatusResponse{}

		err := LoginAssetsCountStatus(context.Background(), req, resp)
		assert.Error(t, err)
	})
}

func TestLoginAssetsExport(t *testing.T) {
	Init()

	t.Run("export by id list", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `login_page_assets`").
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "title", "url", "ip", "port", "img_url", "status", "node",
				"created_at", "updated_at", "user_id", "company_id", "unique_key",
				"batch_id", "detect_task_id", "website_message_id",
			}).AddRow(
				1, "Test Title", "https://example.com", "***********", "443",
				"app/public/test.png", 0, "node1", time.Now(), time.Now(),
				123, 456, "123-https://example.com", "batch123", "task123", 789,
			))

		req := &pb.LoginAssetsDeleteRequest{
			UserId: 123,
			Id:     []uint64{1, 2},
		}
		resp := &pb.FileExportResponse{}

		err := LoginAssetsExport(context.Background(), req, resp)
		// 在测试环境中，由于文件路径不存在，这里会有错误，这是预期的
		t.Logf("Export test error (expected in test env): %v", err)
		// 主要验证函数逻辑不会panic
	})
}

func TestGetAssetsStatus(t *testing.T) {
	t.Run("status mapping", func(t *testing.T) {
		assert.Equal(t, "待处理", getAssetsStatus(0))
		assert.Equal(t, "确认", getAssetsStatus(1))
		assert.Equal(t, "忽略", getAssetsStatus(2))
		assert.Equal(t, "", getAssetsStatus(99))
	})
}

func TestLoginAssetsEdit(t *testing.T) {
	Init()

	t.Run("edit with title", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `login_page_assets`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		req := &pb.LoginAssetsEditRequest{
			UserId: 123,
			Id:     1,
			Title:  "Updated Title",
		}
		resp := &pb.Empty{}

		err := LoginAssetsEdit(context.Background(), req, resp)
		assert.NoError(t, err)
	})

	t.Run("missing id", func(t *testing.T) {
		req := &pb.LoginAssetsEditRequest{
			UserId: 123,
			Title:  "Updated Title",
		}
		resp := &pb.Empty{}

		err := LoginAssetsEdit(context.Background(), req, resp)
		assert.Error(t, err)
	})
}

func TestLoginAssetsImport(t *testing.T) {
	Init()

	t.Run("normal import", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `login_page_assets`").
			WillReturnRows(sqlmock.NewRows([]string{"id"}))

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `login_page_assets`").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		req := &pb.LoginAssetsImportRequest{
			UserId: 123,
			Url:    []string{"https://example.com"},
		}
		resp := &pb.Empty{}

		err := LoginAssetsImport(context.Background(), req, resp)
		assert.NoError(t, err)
	})

	t.Run("empty url list", func(t *testing.T) {
		req := &pb.LoginAssetsImportRequest{
			UserId: 123,
			Url:    []string{},
		}
		resp := &pb.Empty{}

		err := LoginAssetsImport(context.Background(), req, resp)
		assert.Error(t, err)
	})
}
