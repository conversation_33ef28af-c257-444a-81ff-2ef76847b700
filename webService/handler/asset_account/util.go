package asset_account

import (
	elastic_search "github.com/olivere/elastic"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/utils"
	"strconv"
	"strings"

	"github.com/spf13/cast"

	"micro-service/middleware/elastic/fofaee_service"
	"micro-service/middleware/elastic/fofaee_subdomain"
	pb "micro-service/webService/proto"
)

var levels = [...]string{"1", "2", "3", "4", "5"} // 目前有5层

func componentsByLevel(c1, c2 []*pb.IpAssetProfileResponse_Component) []*pb.IpAssetProfileResponse_Layer {
	c1 = append(c1, c2...)
	if len(c1) == 0 {
		return nil
	}

	var list = make([]*pb.IpAssetProfileResponse_Layer, 0, 5)
	for i := range levels {
		unit := &pb.IpAssetProfileResponse_Layer{Level: levels[i]}
		for j := range c1 {
			if c1[j].Level == levels[i] {
				unit.Components = append(unit.Components, c1[j])
				unit.Count++
			}
		}
		if unit.Count > 0 {
			list = append(list, unit)
		}
	}

	return list
}

func convertSubdomain(l []fofaee_subdomain.FofeeSubdomain) map[string][]*pb.IpAssetProfileResponse_Component {
	m := make(map[string][]*pb.IpAssetProfileResponse_Component)
	for i := range l {
		if len(l[i].RuleTags) == 0 {
			continue
		}
		port := cast.ToString(l[i].Port)
		tags := make([]*pb.IpAssetProfileResponse_Component, 0, len(l[i].RuleTags))
		for _, v := range l[i].RuleTags { //nolint:gocritic
			tags = append(tags, &pb.IpAssetProfileResponse_Component{
				Category:         v.Category,
				CnCategory:       v.CnCategory,
				CnCompany:        v.CnCompany,
				CnParentCategory: v.CnParentCategory,
				CnProduct:        v.CnProduct,
				Company:          v.Company,
				ParentCategory:   v.ParentCategory,
				Product:          v.Product,
				Softhard:         v.SoftHard,
				Level:            v.Level,
			})
		}
		m[port] = tags
	}

	return m
}

func convertService(l []fofaee_service.FofaeeService) map[string][]*pb.IpAssetProfileResponse_Component {
	m := make(map[string][]*pb.IpAssetProfileResponse_Component)
	for i := range l {
		if len(l[i].RuleTags) == 0 {
			continue
		}
		port := cast.ToString(l[i].Port)
		tags := make([]*pb.IpAssetProfileResponse_Component, 0, len(l[i].RuleTags))
		for _, v := range l[i].RuleTags { //nolint:gocritic
			tags = append(tags, &pb.IpAssetProfileResponse_Component{
				Category:         v.Category,
				CnCategory:       v.CnCategory,
				CnCompany:        v.CnCompany,
				CnParentCategory: v.CnParentCategory,
				CnProduct:        v.CnProduct,
				Company:          v.Company,
				ParentCategory:   v.ParentCategory,
				Product:          v.Product,
				Softhard:         v.SoftHard,
				Level:            v.Level,
			})
		}
		m[port] = tags
	}

	return m
}

// format: 2020-10-12T04:15Z => 2020-10-12
func cutPublishedDate(d string) string {
	index := strings.IndexByte(d, 'T')
	if index <= 0 {
		return ""
	}

	return d[:index]
}

// ActionSearch 通用筛选查询条件构造
func ActionSearch(req *pb.IpAssetActionRequest, status []interface{}) [][]interface{} {
	var builder elastic.SearchBuilder

	// 处理 is_all 逻辑：
	// 1. 当 is_all 不存在（或为0）且 id 存在时，按照 id 筛选
	// 2. 当 is_all=1 时，忽略 id 筛选条件，按照其他参数筛选
	isAll := req.IsAll != nil && *req.IsAll == 1

	if !isAll && len(req.Id) > 0 {
		// 情况1：is_all 不存在或为0，且有 id，按 id 筛选
		builder.AddMust([]interface{}{"_id", "in", elastic.ToInterfaceArray(req.Id)})
	} else {
		// 情况2：is_all=1 或没有 id，按其他条件筛选
		// 通用字段查询构建
		elastic.StructToParams(req, &builder)
		// 特殊字段
		// 关键字
		if req.Keyword != nil && *req.Keyword != "" {
			var keywordQuery [][]interface{}
			_, err := strconv.Atoi(*req.Keyword)
			if err == nil {
				keywordQuery = append(keywordQuery, []interface{}{"port_list.logo.hash", *req.Keyword})
				keywordQuery = append(keywordQuery, []interface{}{"port", *req.Keyword})
			}
			keywordQuery = append(keywordQuery, []interface{}{"port_list.cert.subject_key", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.icp.no", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.title.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.url.raw", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.protocol", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.domain", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.url", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.subdomain", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"host_list.url.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"custom_tags.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"ip.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
			builder.AddShould(keywordQuery...)
		}
		// Title
		if len(req.Title) > 0 {
			if utils.ListContains(req.Title, "-") {
				names := utils.ListReplace(req.Title, "-", "")
				if len(names) == 1 {
					// all_title不存在或为null的情况
					// 方法1: 字段不存在
					builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
					// 方法2: 字段为null
					builder.AddShould([]interface{}{"all_title", "=", nil})
				} else {
					builder.AddShould([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(names)})
					// all_title不存在或为null的情况
					// 方法1: 字段不存在
					builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
					// 方法2: 字段为null
					builder.AddShould([]interface{}{"all_title", "=", nil})
				}
			} else {
				builder.AddMust([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(req.Title)})
			}
		}
		// Domain
		if len(req.Domain) > 0 {
			if utils.ListContains(req.Domain, "-") {
				names := utils.ListReplace(req.Domain, "-", "")
				if len(names) == 1 {
					// all_domain不存在或为null的情况
					// 方法1: 字段不存在
					builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
					// 方法2: 字段为null (在ES中null值通常不被索引，但为了完整性添加)
					builder.AddShould([]interface{}{"all_domain", "=", nil})
				} else {
					builder.AddShould([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(names)})
					// all_domain不存在或为null的情况
					// 方法1: 字段不存在
					builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
					// 方法2: 字段为null
					builder.AddShould([]interface{}{"all_domain", "=", nil})
				}
			} else {
				builder.AddMust([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(req.Domain)})
			}
		}
		// Hosts
		if req.Hosts != nil {
			var hostsQuery [][]interface{}
			hostsQuery = append(hostsQuery, []interface{}{"port_list.domain", "like", elastic.WithRLAsterisk(*req.Hosts)})
			hostsQuery = append(hostsQuery, []interface{}{"port_list.subdomain", "like", elastic.WithRLAsterisk(*req.Hosts)})
			hostsQuery = append(hostsQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
			hostsQuery = append(hostsQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
			builder.AddShould(hostsQuery...)
		}
		// Assets Source
		if len(req.AssetsSource) > 0 {
			if utils.ListContains(req.AssetsSource, "-") {
				names := utils.ListReplace(req.AssetsSource, "-", "")
				// 过滤掉空字符串，避免ES查询错误
				filteredNames := make([]string, 0)
				for _, name := range names {
					if name != "" {
						filteredNames = append(filteredNames, name)
					}
				}

				// 只有在有有效的资产来源时才添加查询条件
				if len(filteredNames) > 0 {
					builder.AddShould([]interface{}{"host_list.assets_source", "in", elastic.ToInterfaceArray(filteredNames)})
				}
				// 添加不存在assets_source字段的条件（对应空值查询）
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"host_list.assets_source", "EXISTS"}}})
			} else {
				// 过滤掉空字符串
				filteredSources := make([]string, 0)
				for _, source := range req.AssetsSource {
					if source != "" {
						filteredSources = append(filteredSources, source)
					}
				}

				if len(filteredSources) > 0 {
					builder.AddMust([]interface{}{"host_list.assets_source", "in", elastic.ToInterfaceArray(filteredSources)})
				}
			}
		}
		// Cloud Name
		if req.CloudName != nil {
			var nameQuery [][]interface{}
			nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(*req.CloudName)})
			nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToLower(*req.CloudName))})
			nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToUpper(*req.CloudName))})
			builder.AddShould(nameQuery...)
		}
		// Clue Company Name
		if req.ClueCompanyName != nil {
			if utils.ListContains(req.ClueCompanyName, "-") {
				names := utils.ListReplace(req.ClueCompanyName, "-", "")
				builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"clue_company_name", "EXISTS"}}})
			} else {
				builder.AddMust([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
			}
		}
		// 二次确认
		if req.SecondConfirm != nil {
			if *req.SecondConfirm == 1 {
				builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.StatusUploadAsset, fofaee_assets.StatusConfirmAsset}})
				builder.AddShould([]interface{}{"type", fofaee_assets.TYPE_CLAIMED})
				builder.AddShould([]interface{}{"MUST", [][]interface{}{{"second_confirm", 1}, {"type", fofaee_assets.TYPE_RECOMMEND}}})
				builder.AddShould([]interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
			} else {
				builder.AddMust([]interface{}{"type", fofaee_assets.TYPE_RECOMMEND})
				builder.AddMustNot([]interface{}{"second_confirm", 1}, []interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
			}
		}
		// Not In Clue Domain
		if len(req.NotInClueDomain) > 0 {
			var domainQuery [][]interface{}
			domainQuery = append(domainQuery, []interface{}{"port_list.domain", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
			domainQuery = append(domainQuery, []interface{}{"port_list.subdomain", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
			domainQuery = append(domainQuery, []interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
			domainQuery = append(domainQuery, []interface{}{"host_list.subdomain.keyword", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
			builder.AddShould(domainQuery...)
		}
	}
	builder.AddMust([]interface{}{"status", "in", status}, []interface{}{"user_id", req.UserId})
	return builder.Build()
}

func GetFofaAssetsDefaultSorter() []elastic_search.Sorter {
	return []elastic_search.Sorter{
		elastic_search.NewFieldSort("updated_at").Desc(),
		elastic_search.NewFieldSort("_id").Desc(),
	}
}

func GetRuleTagString(tags []fofaee_assets.RuleTag) string {
	if len(tags) == 0 {
		return ""
	}
	var builder strings.Builder
	for i, tag := range tags {
		if i > 0 {
			builder.WriteString(",")
		}
		builder.WriteString(tag.CnProduct)
	}
	return builder.String()
}

func GetAssetTagString(tags []int) string {
	if len(tags) == 0 {
		return ""
	}
	var builder strings.Builder
	for i, tag := range tags {
		if i > 0 {
			builder.WriteString(",")
		}
		switch tag {
		case fofaee_assets.SAFE_SCAN:
			builder.WriteString("安服-扫描")
		case fofaee_assets.CLIENT_SCAN:
			builder.WriteString("用户-扫描")
		case fofaee_assets.CLIENT_REC:
			builder.WriteString("用户-推荐")
		case fofaee_assets.SAFE_REC:
			builder.WriteString("安服-推荐")
		case fofaee_assets.CLIENT_IMPORT:
			builder.WriteString("安服-导入")
		}
	}
	return builder.String()
}

func GetThreatenTypeString(t int) string {
	switch t {
	case foradar_assets.ThreatenTypeOther:
		return "其他类型"
	case fofaee_assets.ThreatenTypeDY:
		return "钓鱼仿冒"
	case fofaee_assets.ThreatenTypeHDD:
		return "黄赌毒网站"
	case fofaee_assets.ThreatenTypeFmICP:
		return "ICP盗用"
	case foradar_assets.ThreatenTypeDomain:
		return "域名混淆"
	default:
		return "其他类型"
	}
}
