package system_detect

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestAbstractProtocol(t *testing.T) {
	assert.Equal(t, "http", abstractProtocol("http://example.com", "fallback"))
	assert.Equal(t, "ftp", abstractProtocol("ftp://example.com", ""))
	assert.Equal(t, "custom", abstractProtocol("example.com", "custom"))
	assert.Equal(t, emptyProtocol, abstractProtocol("example.com", ""))
}
func TestAbstractDomain(t *testing.T) {
	// 正常域名
	d, sub := abstractDomain("http://www.example.com/path")
	assert.Equal(t, "example.com", d)
	assert.Equal(t, "www.example.com", sub)

	// 非 URL，fallback 用自身
	d, sub = abstractDomain("non-url-input")
	assert.Equal(t, "non-url-input", d)
	assert.Equal(t, "non-url-input", sub)
}
func TestIPExpanded(t *testing.T) {
	// IPv4
	assert.Equal(t, "*********", ipExpanded("*********"))

	// IPv6 - 函数会将IPv6展开为完整格式
	assert.Equal(t, "2001:0db8:0000:0000:0000:0000:0000:0001", ipExpanded("2001:db8::1"))

	// 混大小写输入 - 也会被展开为完整格式
	assert.Equal(t, "2001:0db8:0000:0000:0000:0000:0000:0001", ipExpanded("2001:DB8::1"))
}
