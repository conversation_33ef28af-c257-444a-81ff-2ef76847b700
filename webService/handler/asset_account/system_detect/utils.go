package system_detect

// 业务系统

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"
	"unicode/utf8"

	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	bs "micro-service/middleware/mysql/business_system"
	"micro-service/middleware/mysql/user"
	"micro-service/middleware/redis/system_detect"
	"micro-service/pkg/errx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/spf13/cast"
)

const emptyProtocol = "unknown"

func abstractProtocol(address, protocol string) string {
	if index := strings.Index(address, "://"); index > 0 {
		return address[:index]
	}
	if protocol != "" {
		return protocol
	}
	return emptyProtocol
}

func abstractDomain(str string) (domain, subdomain string) {
	s := utils.DomainFromUrl(str)
	subdomain = utils.If(s != "", s, str)
	domain, err := utils.FindRootDomain(subdomain)
	if err != nil {
		domain = subdomain
	}
	return domain, subdomain
}

func ipExpanded(ip string) string {
	ip = utils.IPExpanded(ip)
	return strings.ToLower(ip)
}

// 检测TCP连接状态并获取HTTP状态码
func detectStatus(ip, port, url string) (int, string, error) {
	address := net.JoinHostPort(ip, port)
	// 检查 TCP 连接
	conn, err := net.DialTimeout("tcp", address, 3*time.Second)
	tcpStatus := bs.SystemOffline
	if err == nil && conn != nil {
		tcpStatus = bs.SystemOnline
	}
	if conn != nil {
		conn.Close()
	}
	// 检查 URL 是否包含协议，如果没有，则添加 http
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		url = "http://" + url
	}

	// 创建一个 http client
	client := &http.Client{
		Timeout: 3 * time.Second,
	}
	// 发起 http 请求
	resp, err := client.Get(url)
	httpStatus := ""
	if err == nil && resp != nil {
		httpStatus = cast.ToString(resp.StatusCode)
	}
	if resp != nil {
		resp.Body.Close()
	}
	return tcpStatus, httpStatus, nil
}

// 过滤IP或端口为空的结果
func filterIpPort(l []bs.SystemResult, isIpExpanded bool) []*bs.SystemResult {
	list := make([]*bs.SystemResult, 0, len(l))
	for i := range l {
		if isIpExpanded {
			l[i].IP = ipExpanded(l[i].IP)
		}
		if l[i].IP != "" && l[i].Port != "" {
			list = append(list, &l[i])
		}
	}
	return list
}

func genFileName(userId uint64) string {
	now := utils.TimeFormat(time.Now(), "20060102_150405")
	fileName := fmt.Sprintf("业务系统_%d_%s.xlsx", userId, now)

	return fileName
}

const (
	progressDoing = iota + 1 // 进行中
	progressDone             // 完成
)

const (
	progressImport   = iota + 1 // 导入
	progressDownload            // 导出
)

type cacheProgress struct {
	Type       int     `json:"type"`        // 进度类型: 1导入 2导出
	Status     int64   `json:"status"`      // 状态: 1进行中 2完成
	Progress   float32 `json:"progress"`    // 进度百分比
	FilePath   string  `json:"file_path"`   // 文件路径
	IsDownload bool    `json:"is_download"` // 是否下载
	ErrMsg     string  `json:"err_msg"`     // 错误信息
}

func getProgress(ctx context.Context, userId uint64) (cacheProgress, error) {
	key := fmt.Sprintf("%s%d", system_detect.ProgressPrefix, userId)
	s, err := system_detect.NewBusinessSystemModel().GetValue(ctx, key)
	if err != nil {
		return cacheProgress{}, err
	}
	var item cacheProgress
	err = json.Unmarshal([]byte(s), &item)
	return item, err
}

const progressDur = 365 * utils.Day

func updateDetectProgress(detectType int, userId uint64, status int64, progress float32, e error) {
	key := fmt.Sprintf("%s%d", system_detect.ProgressPrefix, userId)
	item := cacheProgress{
		Type:     detectType,
		Status:   status,
		Progress: progress,
		ErrMsg:   errx.ErrString(e),
	}

	bs, _ := json.Marshal(item)
	ctx := context.Background()
	err := system_detect.NewBusinessSystemModel().SetValue(ctx, key, string(bs), progressDur)
	if err != nil {
		log.Errorf("[业务系统]更新进度%v", err)
	}
}

func updateDetectProgressWith(userId uint64, item cacheProgress) {
	key := fmt.Sprintf("%s%d", system_detect.ProgressPrefix, userId)

	bs, _ := json.Marshal(item)
	ctx := context.Background()
	err := system_detect.NewBusinessSystemModel().SetValue(ctx, key, string(bs), progressDur)
	if err != nil {
		log.Errorf("[业务系统]更新进度%v", err)
	}
}

func Upsert(userId uint, l []bs.SystemResult) ([]bs.SystemResult, error) {
	client := bs.NewBusinessSystemModel()
	queryResult, err := client.ListAll(mysql.WithColumnValue("user_id", userId))
	if err != nil {
		return nil, err
	}
	insertList := make([]bs.SystemResult, 0)
	for i := range l {
		exists := false
		for j := range queryResult {
			if queryResult[j].AssetKey == "" {
				queryResult[j].AssetKey = bs.UserAssetKey(queryResult[j])
			}
			if l[i].AssetKey != queryResult[j].AssetKey {
				continue
			}
			var item = l[i]
			item.ID = queryResult[j].ID
			//导入的默认是确认状态
			item.Status = 1
			item.LastLiveAt = nil
			insertList = append(insertList, item)
			exists = true
			break
		}
		if !exists {
			l[i].Status = 1
			insertList = append(insertList, l[i])
		}
	}
	_, err = client.Upsert(insertList)
	if err != nil {
		return nil, err
	}
	return insertList, nil
}

func companyNameAssert(l []any) []string {
	var list = make([]string, 0, len(l))
	for i := range l {
		list = append(list, cast.ToString(l[i]))
	}
	return list
}

func convertToCompanyName(l []string) []any {
	list := make([]any, 0, len(l))
	for i := range l {
		list = append(list, l[i])
	}
	return list
}

func clueCompanyNameFromPorts(l []fofaee_assets.FofaeeAssetPort) []any {
	var list = make([]string, 0, len(l))
	for i := range l {
		list = append(list, companyNameAssert(l[i].ClueCompanyName)...)
	}
	list = utils.ListDistinct(list)
	return convertToCompanyName(list)
}

// 判断是否为台账资产
func isAccountAsset(status int) bool {
	return status == fofaee_assets.StatusConfirmAsset || status == fofaee_assets.StatusUploadAsset
}

func assetTag(userId, operatorId int) int {
	userInfo, err := user.NewUserModel(mysql.GetDbClient()).FindById(operatorId)
	if err != nil {
		log.Errorf("[业务系统]Get user info from mysql: %v", err)
		return -1
	}
	if userInfo.Role == user.UserRoleSafe || userInfo.Role == user.UserRoleAdmin {
		if userId == operatorId {
			return fofaee_assets.TagUserScan
		}
		return fofaee_assets.TagSafeScan
	}
	return fofaee_assets.TagUserScan
}

func geoHandle(o, geo *foradar_assets.AssetGeo) foradar_assets.AssetGeo {
	if o.Country == "" && o.Lat == nil && o.Lon == nil && o.Isp == "" || o.City == "" || o.Asn == 0 {
		return *geo
	}
	return *o
}

const noteLen = 50

func fieldLimit(note string, limit int) string {
	l := []rune(note)
	if utf8.RuneCountInString(note) <= limit {
		return note
	}
	return string(l[:limit])
}
