package system_detect

// 业务系统

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net"
	"path"
	"path/filepath"
	"strconv"
	"sync"
	"time"

	"micro-service/middleware/elastic"
	ffa "micro-service/middleware/elastic/fofaee_assets"
	fra "micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	bs "micro-service/middleware/mysql/business_system"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/user"
	"micro-service/middleware/redis"
	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
)

func List(req *pb.SystemDetectListRequest, rsp *pb.SystemDetectListResponse) error {
	req.Ids = nil
	handlers := buildHandlerWithReq(req)
	handlers = append(handlers, mysql.WithOrder("last_live_at DESC"))
	client := bs.NewBusinessSystemModel()
	list, total, err := client.ListWithKeyword(req.Keyword, int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}

	rsp.Total = total
	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	for i := range list {
		source, _ := strconv.ParseInt(list[i].Source, 10, 64)
		var lastLiveAtStr string
		if list[i].LastLiveAt != nil {
			lastLiveAtStr = list[i].LastLiveAt.Format(utils.DateTimeLayout)
		} else {
			lastLiveAtStr = ""
		}
		item := &pb.SystemDetectListResponse_ListUnit{
			Id:           uint64(list[i].ID),
			SystemName:   list[i].SystemName,
			Address:      list[i].Address,
			Ip:           list[i].IP,
			Port:         list[i].Port,
			Domain:       list[i].Domain,
			TopDomain:    list[i].TopDomain,
			Protocol:     list[i].Protocol,
			StatusCode:   list[i].StatusCode,
			Belongs:      list[i].Belongs,
			Source:       source,
			SystemStatus: int64(list[i].SystemStatus),
			Status:       int64(list[i].Status),
			Note:         list[i].Note,
			CreatedAt:    list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:    list[i].UpdatedAt.Format(utils.DateTimeLayout),
			LastLiveAt:   lastLiveAtStr,
		}
		rsp.Items = append(rsp.Items, item)
	}

	ports, _ := client.Group("port", mysql.WithColumnValue("user_id", req.UserId))
	statusCode, _ := client.Group("status_code", mysql.WithColumnValue("user_id", req.UserId))
	protocols, _ := client.Group("protocol", mysql.WithColumnValue("user_id", req.UserId))
	mainHosts, _ := client.Group("top_domain", mysql.WithColumnValue("user_id", req.UserId))
	source, _ := strconv.ParseInt(bs.SourceUserImport, 10, 64)
	rsp.Condition = &pb.SystemDetectCondition{
		Port:       utils.ListDistinctNonZero(ports),
		Protocol:   utils.ListDistinctNonZero(protocols),
		Source:     []int64{source},
		StatusCode: utils.ListDistinctNonZero(statusCode),
		TopDomain:  utils.ListDistinctNonZero(mainHosts),
	}

	return nil
}

func UpdateAny(req *pb.SystemDetectUpdateRequest) error {
	client := bs.NewBusinessSystemModel()
	item, err := client.FindOne(mysql.WithColumnValue("id", req.Id), mysql.WithColumnValue("user_id", req.UserId))
	if err != nil {
		return err
	}
	if item.ID == 0 {
		return errors.New("记录未找到")
	}

	req.Protocol = utils.If(req.Protocol != "", req.Protocol, emptyProtocol)

	var updateMap = make(map[string]any, 8)
	var info bs.SystemResult
	bytes, _ := json.Marshal(req)
	_ = json.Unmarshal(bytes, &info)
	hashKey := bs.UserAssetKey(&info)
	if hashKey != item.AssetKey {
		item, err = client.FindOne(mysql.WithColumnValue("user_id", req.UserId), mysql.WithColumnValue("asset_key", hashKey))
		if err != nil {
			return err
		}
		if item.ID != 0 {
			return errors.New("数据重复, 无法编辑")
		}
		_ = json.Unmarshal(bytes, &updateMap)
		updateMap["asset_key"] = hashKey
		delete(updateMap, "id")
		delete(updateMap, "user_id")
	}

	if item.SystemName != req.SystemName {
		updateMap["system_name"] = req.SystemName
	}
	if item.Belongs != req.Belongs {
		updateMap["belongs"] = req.Belongs
	}
	if item.Note != req.Note {
		updateMap["note"] = fieldLimit(req.Note, noteLen)
	}
	if item.Domain != req.Domain {
		updateMap["domain"] = req.Domain
	}
	if item.Protocol != req.Protocol {
		updateMap["protocol"] = req.Protocol
	}
	if item.Address != req.Address {
		updateMap["address"] = req.Address
	}
	if item.OperatorId != uint(req.OperatorId) {
		updateMap["operator_id"] = req.OperatorId
	}

	if len(updateMap) == 0 {
		return nil
	}

	err = client.UpdateAny(uint(req.Id), updateMap)
	return err
}

func Delete(req *pb.SystemDetectListRequest) error {
	client := bs.NewBusinessSystemModel()
	handlers := buildHandlerWithReq(req)
	_, err := client.DeleteWithKeyword(req.Keyword, handlers...)
	if err != nil {
		return err
	}

	return nil
}

func UpdateStatus(req *pb.SystemDetectListRequest) error {
	client := bs.NewBusinessSystemModel()
	handlers := buildHandlerWithReq(req)
	_, err := client.UpdateStatus(req.SetStatus, handlers...)
	if err != nil {
		return err
	}

	return nil
}

func Download(ctx context.Context, req *pb.SystemDetectListRequest) (string, error) {
	handlers := buildHandlerWithReq(req)
	handlers = append(handlers, mysql.WithOrder("updated_at DESC"))
	client := bs.NewBusinessSystemModel()
	list, _, err := client.ListWithKeyword(req.Keyword, 0, 0, handlers...)
	if err != nil {
		return "", err
	}

	accessPath := filepath.Join(storage.GetPublicStoragePath(), genFileName(req.UserId))
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	fp, _ := utils.DownloadFileEncrypt(accessPath, "业务系统数据导出"+path.Ext(accessPath), "", false)
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(accessPath))

	downloadPath := utils.If(req.Redetect, "", apiFilePath)
	// 直接下载
	if !req.Redetect {
		err = downloadWriteData(storagePath, list)
	}

	// 再次检测
	if req.Redetect {
		// 检查当前用户是否存在正在进行的探测任务
		cache, errr := getProgress(ctx, req.UserId)
		if errr != nil && errr != redis.Nil {
			return "", err
		}
		if errr == nil && cache.Status != progressDone {
			return "", errors.New("当前存在检测任务, 无法进行新的检测")
		}

		updateDetectProgress(progressDownload, req.UserId, progressDoing, float32(rand.Intn(5)), nil)
		go func() {
			err = detectAndUpdate(req.UserId, list, progressDownload, false)
			if err != nil {
				updateDetectProgress(progressDownload, req.UserId, progressDone, 100, err)
				return
			}
			// 获取检测后的数据
			list, _, err = client.ListWithKeyword(req.Keyword, 0, 0, handlers...)
			if err != nil {
				updateDetectProgress(progressDownload, req.UserId, progressDone, 100, err)
				return
			}
			updateDetectProgress(progressDownload, req.UserId, progressDone, float32(utils.RandWithRange(80, 90)), nil)
			err = downloadWriteData(storagePath, list)
			if err != nil {
				updateDetectProgress(progressDownload, req.UserId, progressDone, 100, err)
				return
			}
			updateDetectProgressWith(req.UserId, cacheProgress{
				Type:     progressDownload,
				Status:   progressDone,
				Progress: 100.0,
				FilePath: apiFilePath,
			})
		}()
	}

	return downloadPath, err
}

func downloadWriteData(storagePath string, list []bs.SystemResult) error {
	var content = make([][]string, 0, len(list)+1)
	title := []string{"访问地址", "业务系统", "ip", "端口", "域名", "主域名", "协议", "归属", "来源", "资产状态", "状态码", "处理状态", "最后一次存活时间", "导入时间", "更新时间", "备注"}
	content = append(content, title)
	for i := range list {
		line := writeLine(&list[i])
		content = append(content, line)
	}

	err := excel.WriteExcel(storagePath, content)
	return err
}

func writeLine(item *bs.SystemResult) []string {
	var line = make([]string, 0, 10)
	line = append(line, item.Address, item.SystemName, item.IP, item.Port, item.Domain, item.TopDomain, item.Protocol)
	status := utils.If(item.SystemStatus == bs.SystemOnline, "在线", "离线")
	source := utils.If(item.Source == bs.SourceUserImport, "手动导入", "台账同步")
	statusCode := item.StatusCode
	var state string
	switch item.Status {
	case bs.SURE:
		state = "已认领"
	case bs.IGNORE:
		state = "已忽略"
	default:
		state = "待认领" // default value
	}
	LastLiveAt := ""
	if item.LastLiveAt != nil && !item.LastLiveAt.IsZero() {
		LastLiveAt = item.LastLiveAt.Format(utils.DateTimeLayout)
	}
	line = append(line, item.Belongs, source, status, statusCode, state, LastLiveAt, utils.TimeFormat(item.CreatedAt))
	line = append(line, utils.TimeFormat(item.UpdatedAt), item.Note)
	return line
}

func dayBeginning(unixSec int64) (t time.Time) {
	x := utils.UnixSecToTime(unixSec)
	t = utils.BeginningOfDay(x)
	return
}

func IpAssociation(ctx context.Context, req *pb.SystemDetectListRequest) error {
	handlers := buildHandlerWithReq(req)
	handlers = append(handlers, mysql.WithSelect("id,system_name,ip,port,domain,protocol,belongs"))

	client := bs.NewBusinessSystemModel()
	list, _, err := client.ListWithKeyword(req.Keyword, 0, 0, handlers...)
	if err != nil {
		return err
	}

	// 去除IP或port为空的
	validateList := filterIpPort(list, false)
	if len(validateList) == 0 {
		return nil
	}

	err = ipAssociateHandler(ctx, req.UserId, validateList)
	if err != nil {
		return err
	}

	return nil
}

func buildHandlerWithReq(req *pb.SystemDetectListRequest) []mysql.HandleFunc {
	var handlers = make([]mysql.HandleFunc, 0)
	handlers = append(handlers, mysql.WithColumnValue("user_id", req.UserId))
	if len(req.Ids) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("id", req.Ids))
		return handlers
	}
	handlers = append(handlers, mysql.WithLRLike("system_name", req.SystemName))
	handlers = append(handlers, mysql.WithLRLike("address", req.Address))
	handlers = append(handlers, mysql.WithLRLike("ip", req.Ip))
	handlers = append(handlers, mysql.WithLRLike("belongs", req.Belongs))
	handlers = append(handlers, mysql.WithLRLike("domain", req.Domain))
	if len(req.TopDomain) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("top_domain", req.TopDomain))
	}
	if len(req.Port) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("port", req.Port))
	}
	if len(req.Protocol) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("protocol", req.Protocol))
	}
	if req.SystemStatus > 0 {
		handlers = append(handlers, mysql.WithColumnValue("system_status", req.SystemStatus))
	}
	if req.Status > 0 {
		if req.Status == 3 {
			handlers = append(handlers, mysql.WithColumnValue("status", 0))
		} else {
			handlers = append(handlers, mysql.WithColumnValue("status", req.Status))
		}
	}
	if req.StatusCode != "" {
		handlers = append(handlers, mysql.WithColumnValue("status_code", req.StatusCode))
	}
	if len(req.Source) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("source", req.Source))
	}
	if req.WebsiteMessageId > 0 {
		handlers = append(handlers, mysql.WithColumnValue("website_message_id", req.WebsiteMessageId))
	}
	if req.CreatedAtPre > 0 {
		handlers = append(handlers, mysql.WithBetween("created_at", dayBeginning(req.CreatedAtPre), dayBeginning(req.CreatedAtNext)))
	}
	if req.LastLiveAtPre > 0 {
		handlers = append(handlers, mysql.WithBetween("last_live_at", dayBeginning(req.LastLiveAtPre), dayBeginning(req.LastLiveAtNext)))
	}
	if req.UpdatedAtPre > 0 {
		handlers = append(handlers, mysql.WithBetween("updated_at", dayBeginning(req.UpdatedAtPre), dayBeginning(req.UpdatedAtNext)))
	}
	return handlers
}

func ipAssociateHandler(ctx context.Context, userId uint64, l []*bs.SystemResult) error {
	m := make(map[string]struct{})
	for i := range l {
		m[ipExpanded(l[i].IP)] = struct{}{}
	}
	var (
		batch    = 100
		index    = 0
		ipPorts  = make([]elastic.IpPort, 0, batch)
		esResult = make([]ffa.FofaeeAssets, 0, len(m))
		client   = ffa.NewFofaeeAssetsModel()
	)

	for k := range m {
		index++
		ipPorts = append(ipPorts, elastic.IpPort{Ip: k})
		if len(ipPorts) != batch && index != len(m) {
			continue
		}
		assets, err := client.FindByIpAndPort(int(userId), ipPorts, ffa.StatusAccount...)
		if err != nil || len(assets) == 0 {
			if err != nil {
				log.WithContextErrorf(ctx, "[业务系统-IP关联]Es index: %s FindByIpAndPort, %v\n", ffa.FofaeeAssetsIndex, err)
			}
			continue
		}
		ipPorts = make([]elastic.IpPort, 0, batch)
		esResult = append(esResult, assets...)
	}

	err := ipAssociateCompareUpdate(ctx, l, esResult)
	return err
}

func ipAssociateCompareUpdate(ctx context.Context, srs []*bs.SystemResult, fas []ffa.FofaeeAssets) error {
	var list = make([]*bs.SystemResult, 0)
	for i := range srs {
		ip := ipExpanded(srs[i].IP)
		_, subdomain := abstractDomain(srs[i].Domain)
		for j := range fas {
			if ip != fas[j].Ip {
				continue
			}
			for k := range fas[j].HostList {
				port := cast.ToString(fas[j].HostList[k].Port)
				// 如果当前业务系统的端口或者协议或者子域名不等于台账的host_list里面的数据就不会进行更新
				if srs[i].Port != port || fas[j].HostList[k].Protocol != srs[i].Protocol || fas[j].HostList[k].Subdomain != subdomain {
					continue
				}
				var companyName = utils.ListFirstEle(companyNameAssert(fas[j].ClueCompanyName))
				var item bs.SystemResult
				item.ID = srs[i].ID
				title := fieldLimit(cast.ToString(fas[j].HostList[k].Title), 150)
				item.SystemName = utils.If(srs[i].SystemName == "", title, "")
				item.Belongs = utils.If(srs[i].Belongs == "", companyName, "")
				// 补充URL
				if item.Address == "" {
					item.Address = fas[j].HostList[k].Url
				}
				list = append(list, &item)
			}
		}
	}

	_, err := bs.NewBusinessSystemModel().Updates(list...)
	return err
}

func Progress(ctx context.Context, req *pb.SystemDetectProgressRequest, rsp *pb.SystemDetectProgressResponse) error {
	item, err := getProgress(ctx, req.UserId)
	if err == redis.Nil {
		return nil
	}
	if err != nil {
		return err
	}

	rsp.Progress = item.Progress
	rsp.Stauts = item.Status
	rsp.Type = int64(item.Type)
	if item.IsDownload || (item.Type == progressImport && item.Status == progressDone) {
		return nil
	}
	var update bool
	if item.Type == progressDownload && !item.IsDownload && item.Status == progressDone {
		rsp.FilePath = item.FilePath
		item.IsDownload = true
		update = true
	}

	if update {
		go func() {
			updateDetectProgressWith(req.UserId, item)
		}()
	}

	return nil
}

func Create(ctx context.Context, req *pb.SystemDetectCreateRequest) error {
	cache, err := getProgress(ctx, req.UserId)
	if err != nil && err != redis.Nil {
		return err
	}
	if err == nil && cache.Status != progressDone {
		return errors.New("当前存在检测任务, 无法进行新的检测")
	}

	// 等待文件同步
	storagePath := filepath.Join(storage.GetRootPath(), req.FilePath)
	waitFileNFSSync(storagePath)
	if !utils.FileIsExist(storagePath) {
		return errors.New("上传文件不存在")
	}
	log.Infof("业务系统]user_id: %d, 读取Excel文件:", req.UserId, storagePath)
	lines, err := excel.ReadExcel(storagePath)
	if err != nil {
		log.Errorf("[业务系统]read excel file failed from path: %s, %+v", storagePath, err)
		return err
	}

	list, err := readExcel(req.UserId, req.OperatorId, lines)
	if len(list) == 0 || err != nil {
		return err
	}

	list, err = Upsert(uint(req.UserId), list)
	if err != nil {
		return err
	}

	updateDetectProgress(progressImport, req.UserId, progressDoing, utils.RandFloat32(0, 5, 1), nil)
	// 检测逻辑
	go func() {
		log.Info("业务系统开始执行资产状态监测逻辑")
		if err := detectAndUpdate(req.UserId, list, progressImport, true); err != nil {
			log.WithContextErrorf(context.Background(), "[业务系统]detect failed: %v", err)
		}
	}()

	return nil
}

func waitFileNFSSync(filePath string) {
	// NFS文件同步可能存在延迟
	index, readCnt := 0, 500 // 最大等待100s
	for {
		time.Sleep(200 * time.Millisecond)

		b := utils.FileIsExist(filePath)
		if b || index > readCnt {
			break
		}
		index++
	}
}

func readExcel(userId, operatorId uint64, lines [][]string) ([]bs.SystemResult, error) {
	const uploadLimit = 5000
	index := 0
	// title: system_name,address,ip,port,domain,protocol,belongs,note
	columns := [...]string{"system_name", "address", "ip", "port", "domain", "protocol", "belongs", "note"}
	var list = make([]bs.SystemResult, 0)
	for i := range lines {
		if i == 0 {
			continue
		}

		m := make(map[string]string, 8)
		for j := range lines[i] {
			if j < 8 {
				m[columns[j]] = lines[i][j]
			}
		}
		var item bs.SystemResult
		marshalBytes, err := json.Marshal(m)
		if err != nil {
			log.Error("[业务系统]Read Excel, map json.Marshal:", err)
			continue
		}
		if err = json.Unmarshal(marshalBytes, &item); err != nil {
			log.Error("[业务系统]Read Excel, map json.Unmarshal:", err)
			continue
		}
		index++
		if index > uploadLimit {
			return nil, fmt.Errorf("上传资产超过最大限制%d条", uploadLimit)
		}
		if item.Address == "" {
			return nil, fmt.Errorf("第%d行数据业务系统地址不能为空", i+1)
		}
		if net.ParseIP(item.IP) == nil {
			return nil, fmt.Errorf("第%d行数据IP无效", i+1)
		}
		if !utils.IsValidatePort(item.Port) {
			return nil, fmt.Errorf("第%d行数据端口无效", i+1)
		}
		item.Protocol = abstractProtocol(item.Address, item.Protocol)
		item.UserId = uint(userId)
		item.TopDomain = utils.GetTopDomain(item.Domain)
		item.OperatorId = uint(operatorId)
		item.Source = bs.SourceUserImport
		item.AssetKey = bs.UserAssetKey(&item)
		item.Note = fieldLimit(item.Note, noteLen) // 限制长度
		list = append(list, item)
	}

	// data distinct, map key: asset_key
	m := make(map[string]struct{}, len(list))
	l := make([]bs.SystemResult, 0, len(list))
	for i := range list {
		key := list[i].AssetKey
		if _, ok := m[key]; !ok {
			m[key] = struct{}{}
			l = append(l, list[i])
		}
	}

	return l, nil
}

type updateUnit struct {
	Id         uint
	userId     uint
	Online     int
	statusCode string
}

func detectAndUpdate(userId uint64, l []bs.SystemResult, detectType int, progressFinished bool) error {
	m := make(map[string]*bs.SystemResult, len(l))
	for i := range l {
		m[l[i].AssetKey] = &l[i]
	}
	ml := len(m)
	var total = (4 * ml / 3) + 1
	batch := 10
	if ml > 100 {
		batch = ml / 10
	}

	wg := &sync.WaitGroup{}
	ch := make(chan updateUnit)

	go func() {
		wg.Wait()
		close(ch)
	}()

	var index int
	for k := range m {
		wg.Add(1)
		index++

		if index%batch == 0 || index == ml {
			time.Sleep(100 * time.Millisecond)
			progress := float32(index * 100.0 / total)
			updateDetectProgress(detectType, userId, progressDoing, progress, nil)
		}
		go func(info *bs.SystemResult) {
			defer wg.Done()
			status, statusCode, err := detectStatus(info.IP, info.Port, info.Address)
			if err != nil {
				// 处理错误
				fmt.Printf("error: %v\n", err)
				return
			}
			ch <- updateUnit{
				Id:         info.ID,
				userId:     info.UserId,
				Online:     status,
				statusCode: statusCode,
			}
		}(m[k])
	}

	list := make([]*bs.SystemResult, 0, len(m))
	var lastLiveAt *time.Time
	for v := range ch {
		// 如果是在线的话，写上当前时间，如果不在线的话，不需要写
		if v.Online == bs.SystemOnline {
			// 获取当前时间并赋值给lastLiveAt
			now := sql.NullTime{Time: time.Now(), Valid: true}
			lastLiveAt = &now.Time
		}

		item := &bs.SystemResult{
			UserId:       v.userId,
			SystemStatus: v.Online,
			StatusCode:   v.statusCode,
			LastLiveAt:   lastLiveAt,
		}
		item.ID = v.Id
		list = append(list, item)
	}

	client := bs.NewBusinessSystemModel()
	_, err := client.Updates(list...)
	if err != nil || progressFinished {
		time.Sleep(500 * time.Millisecond) // sleep 0.5s
		updateDetectProgress(detectType, userId, progressDone, 100, err)
		if err != nil {
			return fmt.Errorf("[业务系统]call updates func to update system_status failed: %v", err)
		}
	}
	return nil
}

type syncAssetParam struct {
	userId   int
	limitIp  int
	assetTag int
}

// SyncAssets 同步台账
func SyncAssets(ctx context.Context, req *pb.SystemDetectListRequest) error {
	// 根据条件获取要同步哪些资产到台账
	client := bs.NewBusinessSystemModel()
	handlers := buildHandlerWithReq(req)
	list, _, err := client.ListWithKeyword(req.Keyword, 0, 0, handlers...)
	if err != nil {
		return err
	}

	// 过滤IP或端口为空的
	l := filterIpPort(list, true)
	if len(l) == 0 {
		return nil
	}

	// 限制同步资产台账数量
	userInfo, err := user.NewUserModel(mysql.GetDbClient()).FindById(req.UserId)
	if err != nil {
		return err
	}

	// 计算本次同步的最大新增台账IP数量
	var ipNumLimit int
	var companyId uint
	var currentIpUsed int
	if userInfo.Role == user.UserRoleTenant {
		info, err2 := company.NewCompanyModel().FindById(uint64(req.OperateCompanyId), req.UserId)
		if err2 != nil {
			return err
		}
		ipNumLimit = info.LimitIpAsset - info.UsedIpAsset
		companyId = uint(info.ID)
		currentIpUsed = info.UsedIpAsset
	} else {
		ipNumLimit = 10000 * len(l) // 非企业用户台账不受限制
	}

	// 同步台账逻辑
	param := syncAssetParam{
		userId:   int(req.UserId),
		limitIp:  ipNumLimit,
		assetTag: assetTag(int(req.UserId), int(req.OperatorId)),
	}
	usedIp, err := ipSyncAssetAccount(ctx, param, l)
	if err != nil {
		return err
	}

	// 更新用户台账IP数量限制
	if userInfo.Role == user.UserRoleTenant && usedIp > 0 {
		m := make(map[string]any, 1)
		m["used_ip_asset"] = currentIpUsed + usedIp
		err = company.NewCompanyModel().UpdateWithMap(companyId, m)
		if err != nil {
			return err
		}
	}

	return nil
}

// 资产同步台账
// 要同步的资产数据必须是IP和端口均不为空的
// 如果IP和端口已经存在于资产台账中，则不同步
func ipSyncAssetAccount(ctx context.Context, param syncAssetParam, list []*bs.SystemResult) (usedIp int, err error) {
	// fofaee_assets sync
	ips, updateIps, err := syncFofaeeAssets(ctx, param, list)
	if err != nil {
		return 0, err
	}
	// foradar_assets sync
	err = syncForadarAssets(ctx, uint64(param.userId), list, ips, updateIps)
	if err != nil {
		return 0, err
	}
	return len(ips), nil
}

//nolint:unused,gocritic
func syncForadarAssets(ctx context.Context, userId uint64, l []*bs.SystemResult, ips, updateIps []string) error {
	var (
		// ipMap   = make(map[string]struct{}, len(l)/3)
		// index   = 0
		batch   = 100
		ipPorts = make([]elastic.IpPort, 0, batch)
		client  = fra.NewForadarAssetModel()
		// esAll   = make([]fra.ForadarAsset, 0, len(l))
	)
	// for i := range l {
	// 	ipMap[l[i].IP] = struct{}{}
	// }

	// for k := range ipMap {
	// 	index++
	// 	ipPorts = append(ipPorts, elastic.IpPort{Ip: k})
	// 	if len(ipPorts) != batch && index != len(ipMap) {
	// 		continue
	// 	}
	// 	assets, err := client.FindByIpPort(ctx, int(userId), ipPorts)
	// 	if len(assets) == 0 || err != nil {
	// 		if err != nil {
	// 			log.Errorf("[业务系统-同步台账]Get data from es foradar_assets: %v", err)
	// 		}
	// 		continue
	// 	}
	// 	ipPorts = make([]elastic.IpPort, 0, batch)
	// 	esAll = append(esAll, assets...)
	// }
	ipList := append(ips, updateIps...)
	for _, v := range ipList {
		ipPorts = append(ipPorts, elastic.IpPort{Ip: v})
	}
	assets, err := client.FindByIpPort(ctx, int(userId), ipPorts)
	if err != nil {
		return err
	}

	esList, update := foradarAssetsCompare(userId, l, assets, ips, updateIps)
	// 创建资产
	for i := range esList {
		esList[i].UserID = int(userId)
	}
	_, _, err = client.Create(ctx, esList)
	if err != nil {
		return err
	}
	// 更新状态
	err = client.UpdateWithMap(ctx, update)
	if err != nil {
		return err
	}

	return nil
}

//nolint:unused,gocritic
func foradarAssetsCompare(userId uint64, l []*bs.SystemResult, assets []fra.ForadarAsset, ips, updateIps []string) ([]*fra.ForadarAsset, []map[string]any) {
	newAccountIp := utils.ListToSet(ips)
	updateAccountIp := utils.ListToSet(updateIps)
	foradarIpPorts := make(map[string][]*fra.ForadarAsset, len(assets)/3)
	for i := range assets {
		foradarIpPorts[assets[i].Ip] = append(foradarIpPorts[assets[i].Ip], &assets[i])
	}

	insertList := make([]*fra.ForadarAsset, 0, len(l))
	for i := range l {
		ip := l[i].IP
		_, isUpdateAccount := updateAccountIp[ip] // 原台账是否追加端口
		_, isNewAccount := newAccountIp[ip]       // 新台账资产
		if !isNewAccount && !isUpdateAccount {
			continue
		}

		isInsert := false
		var geo fra.AssetGeo
		ps, isInForadar := foradarIpPorts[ip]
		domain, subdomain := abstractDomain(l[i].Domain)
		if !isInForadar {
			isInsert = true // ip不存在当前foradar_assets中, 插入
			log.Info("ip not exists", l[i])
		} else {
			// IP存在于foradar_assets资产中, 则对比IP、端口、协议、subdomain
			exist := false
			for j := range ps {
				port := cast.ToInt(ps[j].Port)
				geo = geoHandle(&geo, &ps[j].Geo)
				if cast.ToInt(l[i].Port) == port && ps[j].Subdomain == subdomain && ps[j].Protocol == l[i].Protocol {
					exist = true
				} else {
					log.Info("ip:", ip, "es:", ps[j].Port, ps[j].Subdomain, ps[j].Protocol, "system:", l[i].Port, subdomain, ps[j].Protocol)
				}
			}
			if !exist {
				log.Info("insert", l[i])
				isInsert = true
			}
		}

		if !isInsert {
			continue
		}

		id := fra.GenIndexId(ip, l[i].Port, l[i].Protocol, subdomain, userId)
		item := &fra.ForadarAsset{
			ID:              id,
			ClueCompanyName: []string{l[i].Belongs},
			Ip:              ip,
			Port:            cast.ToInt(l[i].Port),
			Subdomain:       subdomain, // eg: www.baidu.com
			Domain:          domain,    // eg: baidu.com
			Protocol:        l[i].Protocol,
			Title:           l[i].SystemName,
			Url:             l[i].Address,
			Geo:             geo,
			OnlineState:     utils.If(l[i].SystemStatus == bs.SystemOnline, fra.OnlineStatusYES, fra.OnlineStatusNO),
			Status:          fra.StatusUploadAsset,
			IsIpv6:          utils.IsIPv6(l[i].IP),
		}
		insertList = append(insertList, item)
	}

	// 非台账资产更新为台账资产
	mUpdate := make([]map[string]any, len(newAccountIp))
	for i := range assets {
		// 是否在新台账资产
		if _, ok := newAccountIp[assets[i].Ip]; !ok {
			continue
		}
		// 判断资产状态, 只更新非台账资产状态
		if isAccountAsset(cast.ToInt(assets[i].Status)) {
			continue
		}
		unit := make(map[string]any, 2)
		unit["id"] = assets[i].ID
		unit["status"] = fra.StatusUploadAsset
		mUpdate = append(mUpdate, unit)
	}

	var distinctInsert = make([]*fra.ForadarAsset, 0, len(insertList))
	m := make(map[string]struct{}, len(insertList))
	for i := range insertList {
		item := insertList[i]
		key := fra.GenIndexId(item.Ip, cast.ToString(item.Port), insertList[i].Protocol, item.Subdomain, userId)
		if _, ok := m[key]; !ok {
			distinctInsert = append(distinctInsert, insertList[i])
			m[key] = struct{}{}
		}
	}

	return distinctInsert, mUpdate
}

func syncFofaeeAssets(ctx context.Context, param syncAssetParam, l []*bs.SystemResult) (ips, updateIps []string, err error) {
	m := make(map[string]struct{}, len(l)/2)
	for i := range l {
		m[l[i].IP] = struct{}{}
	}

	var (
		batch   = 100
		index   = 0
		client  = ffa.NewFofaeeAssetsModel()
		ipPorts = make([]elastic.IpPort, 0, batch)
		assets  = make([]ffa.FofaeeAssets, 0, len(m))
	)

	for k := range m {
		index++
		ipPorts = append(ipPorts, elastic.IpPort{Ip: k})
		if len(ipPorts) != batch && index != len(m) {
			continue
		}

		result, exp := client.FindByIpAndPort(param.userId, ipPorts)
		if exp != nil || len(result) == 0 {
			if exp != nil {
				log.WithContextErrorf(ctx, "[业务系统]index_name:%s call FindByIpAndPort, %v", ffa.FofaeeAssetsIndex, exp)
			}
			continue
		}
		ipPorts = make([]elastic.IpPort, 0, batch)
		assets = append(assets, result...)
	}

	// compare and update
	updatesMap, nonAccountMap, insertList := fofaeeAssetsCompare(uint64(param.userId), l, assets, param.assetTag)
	// 台账资产更新数据
	for k := range updatesMap {
		_, ip := ffa.ParseId(k)
		updateIps = append(updateIps, ip)
	}
	err = client.UpdatePorts(ctx, updatesMap)
	if err != nil {
		return nil, nil, err
	}
	// 限制新增IP数量
	if len(insertList) > param.limitIp {
		insertList = insertList[:param.limitIp]
	}
	// 优先同步用户资产
	for i := range insertList {
		ips = append(ips, insertList[i].Ip)
	}
	_, _, err = client.Create(ctx, insertList)
	if err != nil {
		return nil, nil, err
	}
	// 同步非台账资产进入台账
	nonAccountLimit := param.limitIp - len(insertList)
	if nonAccountLimit > 0 && len(nonAccountMap) > 0 {
		nonAccountUpdate := make(map[string]ffa.UpdatePortsHost, nonAccountLimit)
		index := 0
		for k := range nonAccountMap {
			index++
			if index <= nonAccountLimit {
				nonAccountUpdate[k] = nonAccountMap[k]
				_, ip := ffa.ParseId(k)
				ips = append(ips, ip)
			}
		}
		if err = client.UpdatePorts(ctx, nonAccountUpdate); err != nil {
			log.WithContextErrorf(ctx, "[业务系统同步台账]fofaee_assets update: %v", err)
			return ips[:len(insertList)], updateIps, nil
		}
	}

	return ips, updateIps, nil
}

func fofaeeAssetsCompare(userId uint64, l []*bs.SystemResult, assets []ffa.FofaeeAssets, tag int) (map[string]ffa.UpdatePortsHost, map[string]ffa.UpdatePortsHost, []*ffa.FofaeeAssets) {
	insertMap := make(map[string][]ffa.FofaeeAssetPort, len(l)/3)      // key: index id
	nonAccountMap := make(map[string]ffa.UpdatePortsHost, len(assets)) // 非台账资产
	updateMap := make(map[string]ffa.UpdatePortsHost, len(assets))     // key is index id
	hosts := make(map[string][]string, len(l)/3)
	ipOnlineState := make(map[string]int, 0)

	for i := range l {
		id := ffa.GenId(int(userId), l[i].IP)
		var ipExist bool // IP是否存在
		for j := range assets {
			// 如果端口存在且资产在均录入台账
			if l[i].IP != assets[j].Ip {
				continue
			}
			ipExist = true

			// 确认资产状态
			assetStatus := cast.ToInt(assets[j].Status)
			isAccount := isAccountAsset(assetStatus) // 该IP是否为台账资产
			assetStatus = utils.If(isAccount, assetStatus, ffa.StatusUploadAsset)

			var portExists bool // 端口是否存在
			portList := make([]ffa.FofaeeAssetPort, 0, len(assets[j].PortList))
			for k := range assets[j].PortList {
				portItem := assets[j].PortList[k]
				port := cast.ToString(portItem.Port)
				if l[i].Port == port {
					portExists = true
				}
				portList = append(portList, assets[j].PortList[k])
			}
			hostList := assets[j].HostList
			onlineState := assets[j].OnlineState
			if _, ok := updateMap[id]; !ok {
				updateMap[id] = ffa.UpdatePortsHost{
					List:            portList,
					HostList:        hostList,
					Hosts:           assets[j].Hosts,
					Status:          assetStatus,
					ClueCompanyName: assets[j].ClueCompanyName,
					OnlineState:     onlineState,
					Tags:            assets[j].Tags,
				}
			}
			value := updateMap[id]
			if !portExists {
				value.OnlineState = utils.If(l[i].SystemStatus == bs.SystemOnline, ffa.StatusOnline, onlineState)
				domain, subdomain := abstractDomain(l[i].Domain)
				value.Hosts = append(value.Hosts, domain, subdomain)
				portUnit := ffa.FofaeeAssetPort{
					Port:            l[i].Port,
					Title:           l[i].SystemName,
					Protocol:        l[i].Protocol,
					Domain:          domain,
					Subdomain:       subdomain,
					ClueCompanyName: []any{l[i].Belongs},
					Url:             l[i].Address,
				}
				if tag >= 0 {
					value.Tags = append(value.Tags, tag)
					value.Tags = utils.ListDistinct(value.Tags)
				}
				value.List = append(value.List, portUnit)
				value.HostList = append(value.HostList, portUnit)
				companies := companyNameAssert(value.ClueCompanyName)
				companies = append(companies, l[i].Belongs)
				value.ClueCompanyName = convertToCompanyName(utils.ListDistinct(companies))

				if isAccount {
					updateMap[id] = value
				}
			}
			if !isAccount {
				nonAccountMap[id] = value
			}
		}
		if !ipExist {
			if l[i].SystemStatus == bs.SystemOnline {
				ipOnlineState[id] = ffa.StatusOnline
			}
			domain, subdomain := abstractDomain(l[i].Domain)
			hosts[id] = append(hosts[id], domain, subdomain)
			insertMap[id] = append(insertMap[id], ffa.FofaeeAssetPort{
				Port:            l[i].Port,
				Title:           l[i].SystemName,
				Protocol:        l[i].Protocol,
				Domain:          domain,
				Subdomain:       subdomain,
				ClueCompanyName: []any{l[i].Belongs},
				Url:             l[i].Address,
			})
		}
	}

	var insertList = make([]*ffa.FofaeeAssets, 0, len(insertMap))
	// 新增IP资产
	for k := range insertMap {
		_, ip := ffa.ParseId(k)
		portList := ffa.DistinctAssetPortList(insertMap[k])
		item := &ffa.FofaeeAssets{
			Id:              k,
			Ip:              ip,
			ClueCompanyName: clueCompanyNameFromPorts(portList),
			Hosts:           utils.ListDistinct(hosts[k]),
			HostList:        portList,
			UserId:          int(userId),
			PortList:        portList,
			IsIpv6:          utils.IsIPv6(ip),
			Status:          ffa.StatusUploadAsset,
			OnlineState:     ipOnlineState[k],
		}
		if tag >= 0 {
			item.Tags = []int{tag}
		}
		insertList = append(insertList, item)
	}

	return updateMap, nonAccountMap, insertList
}
