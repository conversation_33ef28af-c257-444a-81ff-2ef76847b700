package asset_account

import (
	"context"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"micro-service/middleware/elastic/foradar_assets"
	pb "micro-service/webService/proto"
	"testing"
)

func TestSurePassetsList(t *testing.T) {
	Init()

	t.Run("normal case", func(t *testing.T) {
		req := &pb.SurePassetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			Domain:          []string{"example.com"},
			Hosts:           proto.String("test.example.com"),
			Keyword:         proto.String("test"),
			Title:           []string{"test"},
			AssetsSource:    []string{"test"},
			CloudName:       proto.String("AAACloud"),
			ClueCompanyName: []string{"test"},
			SecondConfirm:   proto.Int32(1),
		}
		resp := &pb.PassetsListResponse{}
		err := SurePassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with domain containing dash", func(t *testing.T) {
		req := &pb.SurePassetsListRequest{
			UserId:          1,
			Page:            0,
			PerPage:         0,
			Domain:          []string{"-", "example.com"},
			Title:           []string{"-", "test"},
			AssetsSource:    []string{"-", "test"},
			ClueCompanyName: []string{"-", "test"},
		}
		resp := &pb.PassetsListResponse{}
		err := SurePassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
	})
}

func TestExportSurePassetsList(t *testing.T) {
	Init()

	t.Run("normal case", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Domain: []string{"example.com"},
		}
		resp := &pb.FileExportResponse{}
		_ = ExportSurePassetsList(context.Background(), req, resp)
	})

	t.Run("with specific IDs", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Id:     []string{"1_10.10.10.1", "1_10.10.10.2"},
		}
		resp := &pb.FileExportResponse{}
		_ = ExportSurePassetsList(context.Background(), req, resp)
	})

}

func TestIpPortActionSearch(t *testing.T) {
	Init()

	t.Run("search by ID", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			Id:              []string{"1_10.10.10.1", "1_10.10.10.2"},
			Title:           []string{"test"},
			AssetsSource:    []string{"test"},
			ClueCompanyName: []string{"test"},
			CloudName:       proto.String("AAACloud"),
		}
		status := []interface{}{1, 2}
		IpPortActionSearch(req, status)
	})

	t.Run("search with keyword", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			Keyword: proto.String("123456"),
		}
		status := []interface{}{1, 2}
		query := IpPortActionSearch(req, status)
		assert.NotNil(t, query)
	})

	t.Run("search with numeric keyword", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			Keyword: proto.String("80"),
		}
		status := []interface{}{1, 2}
		query := IpPortActionSearch(req, status)
		assert.NotNil(t, query)
	})

	t.Run("search with domain", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			Domain: []string{"example.com"},
		}
		status := []interface{}{1, 2}
		query := IpPortActionSearch(req, status)
		assert.NotNil(t, query)
	})

	t.Run("search with domain containing dash", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			Domain:          []string{"-", "example.com"},
			Title:           []string{"-", "test"},
			AssetsSource:    []string{"-", "test"},
			ClueCompanyName: []string{"-", "test"},
		}
		status := []interface{}{1, 2}
		query := IpPortActionSearch(req, status)
		assert.NotNil(t, query)
	})

	t.Run("search with hosts", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			Hosts: proto.String("test.example.com"),
		}
		status := []interface{}{1, 2}
		query := IpPortActionSearch(req, status)
		assert.NotNil(t, query)
	})
}

func TestGetReason(t *testing.T) {
	t.Run("claimed asset type", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				ID:              1,
				Source:          4,
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "Test Company",
			},
		}
		result := getReason(reason, 1, false, 0)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("upload asset type", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				ID:              1,
				Source:          4,
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "Test Company",
			},
		}
		result := getReason(reason, 2, false, 2)
		assert.Equal(t, "根据Test Company的根域 example.com推荐;", result)
	})

	t.Run("claimed asset type_two", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				ID:              1,
				Source:          4,
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "Test Company",
			},
		}
		result := getReason(reason, 1, false, 1)
		assert.Equal(t, "根据Test Company的根域 example.com推荐;", result)
	})

	t.Run("empty reason", func(t *testing.T) {
		var reason []foradar_assets.AssetReason
		getReason(reason, 0, false, 0)
	})

	t.Run("with reasons", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				ID:              1,
				Source:          4,
				Type:            3,
				Content:         "pp/public example.com",
				ClueCompanyName: "Test Company",
			},
			{
				ID:              2,
				Source:          3,
				Type:            3,
				Content:         "test.com",
				ClueCompanyName: "Another Company",
			},
		}
		result := getReason(reason, 1, false, 1)
		assert.NotEmpty(t, result)
	})

	t.Run("with export flag", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				ID:              1,
				Source:          4,
				Type:            3,
				Content:         "example.com",
				ClueCompanyName: "Test Company",
			},
		}
		result := getReason(reason, 1, true, 1)
		assert.NotEmpty(t, result)
	})
}

func TestGetFofaAssetsDefaultSorter(t *testing.T) {
	sorters := GetFofaAssetsDefaultSorter()
	assert.NotNil(t, sorters)
	assert.Len(t, sorters, 2)

	// Check if the sorter is a FieldSort
	_, ok := sorters[0].(*elastic.FieldSort)
	assert.True(t, ok, "Sorter should be a FieldSort")
}
