package asset_account

import (
	"context"
	"fmt"
	"net"
	"path/filepath"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"go-micro.dev/v4/errors"

	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

// CheckBookData 核对台账模版
func CheckBookData(ctx context.Context, req *pb.CheckBookDataRequest, resp *pb.CheckBookDataResponse) error {
	log.WithContextInfof(ctx, "[资产台账核对] 开始核对，请求参数: %v", req)

	// 检查文件参数
	if len(req.File) == 0 {
		return errors.BadRequest(pb.ServiceName, "文件不能为空")
	}

	// 获取文件路径
	originalFilePath := req.File[0]

	// 获取存储根路径
	rootPath := storage.GetRootPath()

	// 清理文件路径，去掉前导斜杠
	cleanFilePath := strings.TrimPrefix(originalFilePath, "/")

	// 拼接完整文件路径：rootPath + app/public + cleanFilePath
	filePath := filepath.Join(rootPath, "app", "public", cleanFilePath)

	// 检查文件是否存在
	fileExists := utils.FileIsExist(filePath)

	if !fileExists {
		// 尝试其他可能的路径
		alternativePath1 := filepath.Join(rootPath, "public", cleanFilePath)
		alternativePath2 := filepath.Join(rootPath, cleanFilePath)
		alternativePath3 := filepath.Join(rootPath, "storage", "app", "public", cleanFilePath)

		// 如果找到了备选路径，使用它
		if utils.FileIsExist(alternativePath1) {
			filePath = alternativePath1
		} else if utils.FileIsExist(alternativePath2) {
			filePath = alternativePath2
		} else if utils.FileIsExist(alternativePath3) {
			filePath = alternativePath3
		} else {
			log.WithContextErrorf(ctx, "[资产台账核对] 文件不存在: %s", originalFilePath)
			return errors.BadRequest(pb.ServiceName, "文件不存在！")
		}
	}

	log.WithContextInfof(ctx, "[资产台账核对] 使用文件: %s", filepath.Base(filePath))

	// 检查文件类型
	fileExt := filepath.Ext(filePath)
	if !strings.EqualFold(fileExt, ".xlsx") && !strings.EqualFold(fileExt, ".xls") {
		return errors.BadRequest(pb.ServiceName, "文件类型只允许为xlsx或xls")
	}

	// 打开Excel文件
	xlsx, err := excelize.OpenFile(filePath)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产台账核对] 打开Excel文件失败: %v", err)
		return errors.InternalServerError(pb.ServiceName, "打开文件失败")
	}
	defer xlsx.Close()

	// 获取第一个工作表
	sheets := xlsx.GetSheetList()
	if len(sheets) == 0 {
		return errors.BadRequest(pb.ServiceName, "Excel文件中没有工作表")
	}

	// 读取工作表数据
	rows, err := xlsx.GetRows(sheets[0])
	if err != nil {
		log.WithContextErrorf(ctx, "[资产台账核对] 读取Excel数据失败: %v", err)
		return errors.InternalServerError(pb.ServiceName, "读取数据失败")
	}

	if len(rows) == 0 {
		return errors.BadRequest(pb.ServiceName, "Excel文件中没有数据")
	}

	// 第一步：解析Excel文件中的IP地址
	var userIPs []string
	log.WithContextInfof(ctx, "[资产台账核对] 开始解析Excel文件，共 %d 行数据", len(rows))

	for _, row := range rows {
		if len(row) == 0 || strings.TrimSpace(row[0]) == "" {
			continue
		}

		ipStr := strings.TrimSpace(row[0])

		// 判断IP是否合法，参考PHP逻辑
		verification := strings.Split(ipStr, ":")
		num := len(verification)

		var validIP string
		if num == 2 || num == 1 {
			// IPv4带端口情况或者就只有IPv4
			if isValidIP(verification[0]) {
				validIP = completeIPv6(ipStr)
				userIPs = append(userIPs, validIP)
			}
		} else if num > 2 {
			// 可能是IPv6
			if isValidIP(ipStr) {
				validIP = completeIPv6(ipStr)
				userIPs = append(userIPs, validIP)
			}
		}

		// 记录有效IP（简化日志）
		if validIP != "" && len(userIPs) <= 5 {
			log.WithContextInfof(ctx, "[资产台账核对] 解析到有效IP: %s", validIP)
		}
	}

	if len(userIPs) == 0 {
		return errors.BadRequest(pb.ServiceName, "核对的台账文件内容为空")
	}

	log.WithContextInfof(ctx, "[资产台账核对] 解析完成，共获得 %d 个有效IP", len(userIPs))

	// 第二步：从ES中查询用户的台账资产
	log.WithContextInfof(ctx, "[资产台账核对] 开始查询用户[%d]的台账资产", req.UserId)

	var builder elastic.SearchBuilder
	builder.AddMust([]interface{}{"user_id", req.UserId})
	builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}})

	query := builder.Build()

	// 查询所有台账资产
	assets, err := elastic.All[fofaee_assets.FofaeeAssets](1000, elastic.ParseQuery(query), nil)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产台账核对] 查询ES失败: %v", err)
		return errors.InternalServerError(pb.ServiceName, "查询台账资产失败")
	}

	if len(assets) == 0 {
		log.WithContextInfof(ctx, "[资产台账核对] 用户[%d]没有台账资产", req.UserId)
		return errors.BadRequest(pb.ServiceName, "数据为空")
	}

	// 提取ES中的IP列表
	var esIPs []string
	for _, asset := range assets {
		if asset.Ip != "" {
			esIPs = append(esIPs, asset.Ip)
		}
	}

	log.WithContextInfof(ctx, "[资产台账核对] 从ES查询到 %d 个台账资产IP", len(esIPs))

	// 第三步：对比数据，参考PHP逻辑
	// 已纳入管理的台账资产 (交集)
	alreadyAssets := intersection(esIPs, userIPs)
	// 未纳入管理的台账资产 (差集)
	notAssets := difference(userIPs, esIPs)

	log.WithContextInfof(ctx, "[资产台账核对] 对比结果 - 已纳入管理: %d 个, 未纳入管理: %d 个", len(alreadyAssets), len(notAssets))

	// 第四步：生成Excel结果文件
	resultExcel := excelize.NewFile()

	// 创建"已纳入管理的台账资产"工作表
	managedSheetName := "已纳入管理的台账资产"
	resultExcel.SetSheetName("Sheet1", managedSheetName)

	// 设置表头
	resultExcel.SetCellValue(managedSheetName, "A1", "ip")

	// 写入已纳入管理的资产
	for i, ip := range alreadyAssets {
		resultExcel.SetCellValue(managedSheetName, fmt.Sprintf("A%d", i+2), ip)
	}

	// 创建"未纳入管理的台账资产"工作表
	notManagedSheetName := "未纳入管理的台账资产"
	resultExcel.NewSheet(notManagedSheetName)

	// 设置表头
	resultExcel.SetCellValue(notManagedSheetName, "A1", "ip")

	// 写入未纳入管理的资产
	for i, ip := range notAssets {
		resultExcel.SetCellValue(notManagedSheetName, fmt.Sprintf("A%d", i+2), ip)
	}

	// 保存文件 - 参考 ExportSurePassetsList 的逻辑
	timestamp := time.Now().Format("20060102150405")
	filename := fmt.Sprintf("核对结果_%s.xlsx", timestamp)
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)

	// 确保目录存在
	dir := filepath.Dir(storagePath)
	err = utils.Mkdir(dir)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产台账核对] 创建目录失败: %v", err)
		return errors.InternalServerError(pb.ServiceName, "创建目录失败")
	}

	// 保存结果文件
	err = resultExcel.SaveAs(storagePath)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产台账核对] 保存结果文件失败: %v", err)
		return errors.InternalServerError(pb.ServiceName, "保存结果文件失败")
	}

	// 生成前端可访问的路径 - 参考 ExportSurePassetsList 的逻辑
	fp, err := utils.DownloadFileEncrypt(accessPath, "资产台账核对结果"+filepath.Ext(filename), "", false)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产台账核对] 生成下载路径失败: %v", err)
		return errors.InternalServerError(pb.ServiceName, "生成下载路径失败")
	}
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+filepath.Ext(filename))

	// 设置响应
	resp.Url = apiFilePath
	resp.AssetsCount = int32(len(userIPs))
	resp.NotAssetsCount = int32(len(notAssets))

	log.WithContextInfof(ctx, "[资产台账核对] 核对完成，总资产: %d, 未纳入管理: %d, 下载链接: %s",
		resp.AssetsCount, resp.NotAssetsCount, resp.Url)

	return nil
}

// isValidIP 验证IP地址是否有效，参考PHP的filter_var($ip, FILTER_VALIDATE_IP)
func isValidIP(ip string) bool {
	return net.ParseIP(ip) != nil
}

// completeIPv6 补全IPv6地址，参考PHP的completeIPV6函数
func completeIPv6(ip string) string {
	// 如果包含端口，先分离
	if strings.Contains(ip, ":") && !strings.Contains(ip, "::") {
		parts := strings.Split(ip, ":")
		if len(parts) == 2 {
			// 可能是IPv4:port格式
			if net.ParseIP(parts[0]) != nil {
				return ip // 保持原样
			}
		}
	}

	// 检查是否为IPv6
	if parsedIP := net.ParseIP(ip); parsedIP != nil {
		if parsedIP.To4() == nil {
			// 是IPv6，返回标准格式
			return parsedIP.String()
		}
	}

	// 其他情况保持原样
	return ip
}

// intersection 计算两个字符串数组的交集，参考PHP的array_intersect
func intersection(slice1, slice2 []string) []string {
	set := make(map[string]bool)
	var result []string

	// 将第二个数组转为set
	for _, item := range slice2 {
		set[item] = true
	}

	// 检查第一个数组中的元素是否在set中
	seen := make(map[string]bool)
	for _, item := range slice1 {
		if set[item] && !seen[item] {
			result = append(result, item)
			seen[item] = true
		}
	}

	return result
}

// difference 计算两个字符串数组的差集，参考PHP的array_diff
func difference(slice1, slice2 []string) []string {
	set := make(map[string]bool)
	var result []string

	// 将第二个数组转为set
	for _, item := range slice2 {
		set[item] = true
	}

	// 检查第一个数组中的元素是否不在set中
	seen := make(map[string]bool)
	for _, item := range slice1 {
		if !set[item] && !seen[item] {
			result = append(result, item)
			seen[item] = true
		}
	}

	return result
}
