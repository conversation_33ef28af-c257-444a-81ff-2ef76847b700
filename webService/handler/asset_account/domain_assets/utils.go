package domain_assets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/jinzhu/now"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/sourcegraph/conc/pool"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	core "micro-service/coreService/proto"
	cron "micro-service/cronService/proto"
	"micro-service/middleware/mysql"
	cronm "micro-service/middleware/mysql/cron"
	"micro-service/middleware/mysql/domain_assets"
	dac "micro-service/middleware/mysql/domain_assets_cron"
	domain_utils "micro-service/pkg/domain"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

func genCronExp(d int) string {
	second := utils.RandWithRange(1, 59)
	minute := utils.RandWithRange(1, 59)
	hour := utils.RandWithRange(2, 4)
	return fmt.Sprintf("%d %d %d */%d * *", second, minute, hour, d)
}

type CronParam struct {
	UserId        uint64 `json:"user_id"`
	OperateUserId uint64 `json:"operate_user_id"`
}

func addCron(ctx context.Context, userId uint64, opId int64, spec string) (uint64, error) {
	var param = CronParam{UserId: userId, OperateUserId: uint64(opId)}
	bs, _ := json.Marshal(param)
	rsp, err := cron.GetProtoClient().AddCron(ctx, &cron.AddCronRequest{
		UserId: userId,
		Name:   fmt.Sprintf("域名资产定时解析任务(用户ID:%d)", userId),
		Status: cronm.StatusEnable,
		Type:   cronm.TypeUser,
		Spec:   spec,
		Method: cron.DomainAssetsMethodName,
		Params: string(bs),
	})
	if err != nil {
		return 0, err
	}
	return rsp.Id, nil
}

func updateCron(ctx context.Context, info *dac.Cron) error {
	var param = CronParam{UserId: info.UserId, OperateUserId: info.OperateUserId}
	bs, _ := json.Marshal(param)
	_, err := cron.GetProtoClient().UpdateCron(ctx, &cron.UpdateCronRequest{
		Id:     info.CronId,
		UserId: info.UserId,
		Name:   fmt.Sprintf("域名资产定时解析任务(用户ID:%d)", info.UserId),
		Status: int32(utils.If(info.Status == dac.Disable, 0, cronm.StatusEnable)),
		Type:   cronm.TypeUser,
		Spec:   info.CronExpression,
		Method: cron.DomainAssetsMethodName,
		Params: string(bs),
	})
	return err
}

func dnsIsParse(domain string) int {
	ips, err := net.LookupIP(domain)
	if err != nil || len(ips) == 0 {
		return domain_assets.StatusInvalid
	}
	return domain_assets.StatusValid
}

func dnsCName(domain string) string {
	cname, err := net.LookupCNAME(domain)
	if err != nil {
		return ""
	}
	return cname
}

func isWildCard(domain string) int {
	// 使用新的泛解析检测函数
	if domain_utils.IsWildcardDomain(domain) {
		return domain_assets.OpenParseValid
	}
	return domain_assets.OpenParseInvalid
}

func dnsRecord(domain string) (aRecord, aaaaRecord string) {
	ips, err := net.LookupIP(domain)
	if err != nil {
		return "", ""
	}

	aList := make([]string, 0)
	aaaaList := make([]string, 0)
	for i := range ips {
		if ips[i].To4() == nil {
			aaaaList = append(aaaaList, ips[i].String())
		} else {
			aList = append(aList, ips[i].String())
		}
	}
	aRecord = strings.Join(aList, ",")
	aaaaRecord = strings.Join(aaaaList, ",")
	return
}

func parentDomain(domain string) string {
	topDomain, _ := utils.FindRootDomain(domain)
	if domain == topDomain {
		return ""
	}
	firstDot := strings.Index(domain, ".")
	return domain[firstDot+1:]
}

// icpCompanyName
//
//nolint:unused,gocritic
func icpCompanyName(ctx context.Context, domains []string) cmap.ConcurrentMap[string, string] {
	domains = utils.ListDistinctNonZero(domains)
	if len(domains) == 0 {
		return cmap.ConcurrentMap[string, string]{}
	}

	p := pool.New().WithMaxGoroutines(utils.Page(len(domains), 5))
	var icpMap = cmap.New[string]()
	for _, v := range domains {
		domain := v
		p.Go(func() {
			rsp, err := core.GetProtoCoreClient().
				Domain(ctx, &core.IcpDomainRequest{Domain: domain}, utils.SetRpcTimeoutOpt(30))
			if err != nil {
				log.Errorf("[Web] DomainAssets: Get domain -> %v icp company failed: %v", domain, err)
			}
			if rsp != nil {
				icpMap.Set(domain, rsp.Info.GetCompanyName())
			}
		})
	}
	p.Wait()

	return icpMap
}

func filterBuilder(req *pb.DomainAssetsUpdateByCronRequest) []mysql.HandleFunc {
	var handlers = make([]mysql.HandleFunc, 0, 10)
	handlers = append(handlers, mysql.WithColumnValue("`user_id`", req.UserId))
	if len(req.Ids) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("`id`", utils.ListDistinct(req.Ids)))
		return handlers
	}

	if req.Keyword != "" {
		handlers = append(handlers, domain_assets.WithKeyword(req.Keyword))
	}
	if len(req.Domain) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("`domain`", req.Domain))
	}
	if len(req.TopDomain) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("`top_domain`", req.TopDomain))
	}
	if len(req.FDomain) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("`f_domain`", req.FDomain))
	}
	if len(req.CompanyName) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("`company_name`", req.CompanyName))
	}
	if req.OpenParse != "" {
		handlers = append(handlers, mysql.WithColumnValue("`open_parse`", cast.ToInt(req.OpenParse)))
	}
	if req.Type != "" {
		handlers = append(handlers, mysql.WithColumnValue("`type`", cast.ToInt(req.Type)))
	}
	if len(req.Source) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("`source`", req.Source))
	}
	if len(req.CreatedAtRange) == 2 {
		d1, d2 := parseDateRange(req.CreatedAtRange[0], req.CreatedAtRange[1])
		handlers = append(handlers, mysql.WithBetween("`created_at`", d1, d2.Add(24*time.Hour)))
	}
	if len(req.UpdatedAtRange) == 2 {
		d1, d2 := parseDateRange(req.UpdatedAtRange[0], req.UpdatedAtRange[1])
		handlers = append(handlers, mysql.WithBetween("`updated_at`", d1, d2.Add(24*time.Hour)))
	}
	return handlers
}

func parseDateRange(d1, d2 string) (time.Time, time.Time) {
	loc := time.Now().Location()
	t1, _ := time.ParseInLocation(utils.DateLayout, d1, loc)
	t2, _ := time.ParseInLocation(utils.DateLayout, d2, loc)
	return t1, t2
}

func checkUserUsedParse(userId uint64) (dac.Cron, error) {
	const maxParseEveryDay = 10
	item, err := dac.NewCroner().First(
		mysql.WithColumnValue("`user_id`", userId),
		mysql.WithColumnValue("`mode`", dac.ModeImmediately),
		mysql.WithSelect("id", "current_used", "last_updated_at"))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return dac.Cron{}, nil
	case err != nil:
		return dac.Cron{}, err
	case item.LastUpdatedAt.Before(now.BeginningOfDay()):
		return item, nil
	case item.CurrentUsed >= maxParseEveryDay:
		return item, fmt.Errorf("当前用户单次更新已超过每日%d次最大限额", maxParseEveryDay)
	}
	return item, nil
}

func incrUserUsedParse(userId uint64, operateUserId int64, item *dac.Cron) error {
	item.UserId = userId
	item.OperateUserId = uint64(operateUserId)
	if item.ID == 0 {
		item.Mode = dac.ModeImmediately
		item.CurrentUsed = 1
		return dac.NewCroner().Create(item)
	}

	beforeToday := item.LastUpdatedAt.Before(now.BeginningOfDay())
	item.CurrentUsed = utils.If(beforeToday, 1, item.CurrentUsed+1)
	item.LastUpdatedAt = time.Now()
	return dac.NewCroner().Updates(*item)
}

func genFindTaskName(isByCron bool) string {
	modeStr := utils.If(!isByCron, "单次", "周期")
	taskName := fmt.Sprintf("域名资产更新-域名验证任务-%s（%s）", modeStr, time.Now().Format(utils.DateTimeLayout))
	return taskName
}

func random(begin, end uint) int {
	return utils.RandWithRange(begin, end)
}
