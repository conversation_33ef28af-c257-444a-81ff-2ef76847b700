package domain_assets

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/webService/asyncq/handlers"
	"strconv"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"

	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/domain_assets"
	dac "micro-service/middleware/mysql/domain_assets_cron"
	"micro-service/middleware/mysql/domain_task"
	"micro-service/middleware/mysql/domain_task_golang"
	"micro-service/middleware/mysql/domain_task_result"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	scan "micro-service/scanService/proto"
	pb "micro-service/webService/proto"
)

func FofaUpdate(_ context.Context, req *pb.FofaUpdateRequest, _ *pb.Empty) error {
	// 0域名类型去fofa更新 1/ip类型去fofa更新
	if req.Type != 1 {
		// 检查域名资产
		count, err := domain_assets.NewModel().Count(domain_assets.WithUserID(req.UserId))
		if err != nil {
			log.Errorf("[FofaUpdate] Failed to count DomainAssets for user_id %d - %v", req.UserId, err)
			return err
		}
		if count == 0 {
			log.Infof("[FofaUpdate] No DomainAssets found for user_id %d", req.UserId)
			return errors.New("域名列表为空，请选择域名数据再进行下发任务!")
		}
	} else {
		// 检查IP资产（台账资产）
		query := [][]interface{}{{"user_id", req.UserId}, {"status", "in", []interface{}{1, 4}}}
		total, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](query)
		if err != nil {
			log.Errorf("[FofaUpdate] Failed to get FofaeeAssets for user_id %d - %v", req.UserId, err)
			return err
		}
		if total == 0 {
			log.Infof("[FofaUpdate] No FofaeeAssets found for user_id %d", req.UserId)
			return errors.New("台账资产列表为空，请选择台账数据再进行下发任务!")
		}
	}

	err := asyncq.Enqueue(context.Background(), asyncq.FofaDomainUpdateJob, handlers.FofaDomainUpdateJobPayload{
		Type:   int(req.Type),
		UserId: int(req.UserId),
	})
	if err != nil {
		log.Errorf("[FofaUpdate] Failed to enqueue FofaDomainUpdateJob - %v", err)
		return errors.New("下发更新任务失败，请重试!")
	}
	return nil
}

func PushDomainAssets(ctx context.Context, req *pb.PushDomainDataRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[PushDomainAssets] 开始处理域名推送请求, 用户ID: %d, 域名数量: %d", req.UserId, len(req.DomainList))

	if len(req.DomainList) == 0 {
		return errors.New("域名列表为空")
	}

	var createList []*domain_assets.DomainAssets
	for i, domain := range req.DomainList {
		if !utils.IsValidDomainStrict(domain) {
			return fmt.Errorf("第%d个域名[%s]格式不正确", i+1, domain)
		}
		var assets domain_assets.DomainAssets
		assets.Domain = domain
		assets.UserId = req.UserId
		assets.CompanyId = req.CompanyId
		assets.FDomain = getFdomain(domain)
		assets.TopDomain = utils.GetTopDomain(domain)
		assets.Depth = getDepthDomain(domain)
		if assets.Depth > 1 {
			assets.Type = domain_assets.YES_ANALYAIS_DOMAIN
		} else {
			assets.Type = domain_assets.NO_ANALYAIS_DOMAIN
		}
		assets.Status = domain_assets.YET_ANALYAIS
		// 暂时不查询备案信息，留空等待异步任务处理
		assets.CompanyName = ""

		first, err := domain_assets.NewModel().First(mysql.WithWhere("user_id = ? AND domain = ?", req.UserId, domain))
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Errorf("[PushDomainAssets] Failed to get domain_assets - %v", err)
			return err
		}
		if err == nil {
			var source []int
			err = json.Unmarshal([]byte(first.Source), &source)
			if err != nil {
				log.Errorf("[PushDomainAssets] Failed to unmarshal domain_assets source - %v", err)
				return err
			}
			source = append(source, domain_assets.DOMAIN_IMPORT)
			source = utils.ListDistinct(source)
			err = domain_assets.NewModel().UpdateMap(first.Id, map[string]any{
				"f_domain":     assets.FDomain,
				"top_domain":   assets.TopDomain,
				"depth":        assets.Depth,
				"type":         assets.Type,
				"status":       assets.Status,
				"company_name": assets.CompanyName,
				"source":       utils.AnyToStr(source),
			})
			if err != nil {
				log.Errorf("[PushDomainAssets] Failed to update domain_assets - %v", err)
				return err
			}
		} else {
			assets.CompanyName = ""
			assets.DnsA = ""
			assets.DnsAaaa = ""
			assets.Cname = ""
			assets.Source = utils.AnyToStr([]int{domain_assets.DOMAIN_IMPORT})
			createList = append(createList, &assets)
		}
	}
	// 新建域名资产记录
	if len(createList) > 0 {
		err := domain_assets.NewModel().Create(createList)
		if err != nil {
			log.Errorf("[PushDomainAssets] Failed to create domain_assets - %v", err)
			return err
		}
	}

	// 异步处理备案查询
	err := asyncq.Enqueue(ctx, asyncq.PushDomainIcpJob, handlers.PushDomainIcpJobPayload{
		UserID:     req.UserId,
		DomainList: req.DomainList,
	})
	if err != nil {
		log.Errorf("[PushDomainAssets] Failed to enqueue domain ICP job - %v", err)
		// 不返回错误，因为域名已经保存成功，只是备案查询会延后处理
		log.Warnf("[PushDomainAssets] 域名保存成功，但备案查询任务入队失败，将在后续处理")
	} else {
		log.WithContextInfof(ctx, "[PushDomainAssets] 成功入队域名备案查询任务，用户ID: %d, 域名数量: %d", req.UserId, len(req.DomainList))
	}

	log.WithContextInfof(ctx, "[PushDomainAssets] 域名推送处理完成, 用户ID: %d, 新增域名数量: %d", req.UserId, len(createList))
	return nil
}

func DomainAssetsDomainFilter(req *pb.DomainAssetsDomainFilterRequest, rsp *pb.DomainAssetsDomainFilterResponse) error {
	var h []mysql.HandleFunc
	h = append(h, mysql.WithSelect("domain"))
	h = append(h, mysql.WithColumnValue("`user_id`", req.UserId))
	if req.Keyword != "" {
		h = append(h, mysql.WithLRLike("`domain`", req.Keyword))
	}
	if req.Type != "" {
		h = append(h, mysql.WithColumnValue("`type`", req.Type))
	}

	dbDomains, err := domain_assets.NewModel().ListAll([]string{}, h...)
	for _, v := range dbDomains {
		rsp.Domains = append(rsp.Domains, v.Domain)
	}
	return err
}

func CreateByDomains(userId uint, domains []string) error {
	domains = utils.ListDistinctNonZero(domains)
	if len(domains) == 0 {
		return nil
	}

	var list = make([]*domain_assets.DomainAssets, 0, len(domains))
	for i := range domains {
		item := domainAssetByDomain(userId, domains[i])
		list = append(list, item)
	}

	err := upsert(userId, domains, list)
	if err != nil {
		return err
	}
	return nil
}

func domainAssetByDomain(userId uint, domain string) *domain_assets.DomainAssets {
	return &domain_assets.DomainAssets{
		UserId: uint64(userId),
	}
}

func upsert(userId uint, domains []string, list []*domain_assets.DomainAssets) error {
	db := domain_assets.NewModel()
	dbDomains, err := db.ListAll([]string{}, mysql.WithColumnValue("user_id", userId), mysql.WithValuesIn("domain", domains))
	if err != nil {
		return err
	}
	for i := range list {
		for j := range dbDomains {
			if list[i].Domain != dbDomains[j].Domain {
				continue
			}
			list[i].Id = dbDomains[j].Id
			break
		}
	}

	err = db.Upsert(list...)
	return err
}

func CronInfo(userId uint64, rsp *pb.DomainAssetsCronInfoResponse) error {
	item, err := dac.NewCroner().First(
		mysql.WithColumnValue("`user_id`", userId),
		mysql.WithColumnValue("`mode`", dac.ModeCron))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return nil
	case err != nil:
		return err
	}

	rsp.Ex = int64(item.Ex)
	rsp.Status = int64(item.Status)
	rsp.CreatedAt = item.CreatedAt.Format(utils.DateTimeLayout)
	rsp.UpdatedAt = item.UpdatedAt.Format(utils.DateTimeLayout)
	return nil
}

func UpdateByCron(ctx context.Context, req *pb.DomainAssetsUpdateByCronRequest) error {
	// 立即更新
	if req.Update {
		return updateDomainParse(ctx, req)
	}

	// 检查参数
	if req.Ex == 0 {
		return errors.New("周期任务周期不可为空")
	}
	if req.CronStatus == 0 {
		return errors.New("周期任务启用状态不可为空")
	}

	// 创建/更新定时任务
	croner := dac.NewCroner()
	domainCron, err := croner.First(mysql.WithColumnValue("`user_id`", req.UserId), mysql.WithColumnValue("`mode`", dac.ModeCron))
	newCron := false
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		newCron = true
	case err != nil:
		return err
	}

	cronItem := domainCron
	cronItem.Ex = uint64(req.Ex)
	cronItem.Status = int(req.CronStatus)
	cronItem.CronExpression = genCronExp(int(req.Ex))
	cronItem.LastUpdatedAt = time.Now()
	// 新增周期任务
	if newCron {
		cronItem.UserId = req.UserId
		cronItem.Mode = dac.ModeCron
		cronItem.OperateUserId = uint64(req.OperateUserId)
		// 新周期任务未启用，仅添加记录
		if req.CronStatus == dac.Disable {
			return croner.Create(&cronItem)
		}
		// 新周期任务启用，既添加任务又注册cron任务
		if req.CronStatus == dac.Enable {
			cronId, addErr := addCron(ctx, req.UserId, req.OperateUserId, cronItem.CronExpression)
			if addErr != nil {
				return fmt.Errorf("add cron task to cron service failed: %v", addErr)
			}
			cronItem.CronId = cronId
			return croner.Create(&cronItem)
		}
	}

	// 更新周期任务
	if int(req.CronStatus) == domainCron.Status && uint64(req.Ex) == domainCron.Ex {
		return nil
	}
	// 情形1: 仅添加了任务记录但未注册定时任务
	if cronItem.CronId == 0 {
		if req.CronStatus == dac.Enable {
			cronId, addErr := addCron(ctx, req.UserId, req.OperateUserId, cronItem.CronExpression)
			if addErr != nil {
				return fmt.Errorf("add cron task to cron service failed: %v", addErr)
			}
			cronItem.CronId = cronId
		}
		return croner.Updates(cronItem)
	}

	// 情形2: 既存在任务记录又注册了定时任务
	err = croner.Updates(cronItem)
	if err != nil {
		return err
	}
	return updateCron(ctx, &cronItem)
}

func updateDomainParse(ctx context.Context, req *pb.DomainAssetsUpdateByCronRequest) error {
	// 界面立即更新，检查每日限制次数
	var err error
	var cronItem dac.Cron
	// 判断是否通过定时任务形式调用，如果不是，需要检查用户的解析次数使用情况
	if !req.CallByCron {
		cronItem, err = checkUserUsedParse(req.UserId)
		if err != nil {
			return err
		}
	}

	list, err := domain_assets.NewModel().ListAll([]string{}, filterBuilder(req)...)
	if len(list) == 0 {
		return err
	}

	log.WithContextInfof(ctx, "[Domain-Asset] taskId: %d, 本次域名资产需要解析的结果数量为%d", req.DomainFindTaskId, len(list))
	// 异步更新解析
	go func() {
		if !req.CallByCron {
			if errIncr := incrUserUsedParse(req.UserId, req.OperateUserId, &cronItem); errIncr != nil {
				log.WithContextErrorf(ctx, "[Web] DomainAssets: process user used parse failed: %v", err)
			}
		}
		if errReParse := reparseDomains(ctx, req, list); errReParse != nil {
			log.WithContextErrorf(ctx, "[Web] DomainAssets: exec reParse domains failed: %v", errReParse)
		}
	}()

	return nil
}

func reparseDomains(ctx context.Context, req *pb.DomainAssetsUpdateByCronRequest, list []*domain_assets.DomainAssets) error {
	// 新建域名发现任务
	var domains = make([]string, 0, len(list))
	for _, v := range list {
		domains = append(domains, v.Domain)
	}
	domains = utils.ListDistinctNonZero(domains)
	domainFindTaskId, err := domainFindTask(req, domains)
	if err != nil {
		return err
	}

	req.DomainFindTaskId = domainFindTaskId
	// 创建Go验证模式任务
	domainTasker := domain_task.NewDomainTaskModel()
	verifyTaskId, err := domainAssetByGoVerify(ctx, domains)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web] DomainAsset: Create domain find verify task failed: %v", err)
	} else {
		err = domainTasker.UpdateAny(domainFindTaskId, map[string]any{"progress": random(10, 25), "golang_task_id": verifyTaskId})
		if err != nil {
			log.WithContextErrorf(ctx, "[Web] DomainAssets: update domain task info failed: %v", err)
		}
	}

	// 等待Go验证模式完成
	verifiedDomains, err := waitDomainAssetVerify(verifyTaskId)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web] DomainAssets: wait domain find task failed: %v", err)
	}
	_ = domainTasker.UpdateAny(domainFindTaskId, map[string]any{"progress": random(55, 70)})

	// 解析域名DNS记录和ICP信息
	log.WithContextInfof(ctx, "[Domain-Asset] taskId: %d, Parse domain meta info, like: DNS, CNAME, IsWild...", domainFindTaskId)
	parsed := parseDomain(ctx, list)

	_ = domainTasker.UpdateAny(domainFindTaskId, map[string]any{"progress": random(70, 85)})
	// 更新域名发现任务进度为100
	bs, _ := json.Marshal(&verifiedDomains)
	err = domainTasker.UpdateAny(domainFindTaskId,
		map[string]any{"status": domain_task.StatusFinished, "progress": 100, "verify_domain_list": string(bs)})
	if err != nil {
		log.WithContextErrorf(ctx, "[Web] DomainAssets: update domain task info failed: %v", err)
	}
	// 写入域名发现任务数据
	err = writeDomainFindResult(req, parsed, verifiedDomains)
	if err != nil {
		return err
	}

	// 写入域名资产数据
	err = domain_assets.NewModel().Upsert(parsed...)
	return err
}

// 新建域名发现任务
func domainFindTask(req *pb.DomainAssetsUpdateByCronRequest, domains []string) (uint64, error) {
	bs, _ := json.Marshal(domains)
	item := &domain_task.DomainTask{
		UserId:     strconv.FormatUint(req.UserId, 10),
		CompanyId:  &sql.NullInt64{Int64: req.CompanyId, Valid: true},
		OpId:       uint64(req.OperateUserId),
		Name:       genFindTaskName(req.CallByCron),
		DomainList: &sql.NullString{String: string(bs), Valid: true},
		Status:     domain_task.StatusDoing,
		Level:      0,
		Bandwidth:  "1000",
		Modify:     domain_task.VERIFY_TYPE,
		Progress:   5,
	}
	err := domain_task.NewDomainTaskModel().Create(item)
	if err != nil {
		return 0, err
	}
	return item.Id, nil
}

// 新建域名发现任务-Go验证模式任务
func domainAssetByGoVerify(ctx context.Context, domains []string) (uint64, error) {
	rsp, err := scan.GetProtoClient().DomainBurst(ctx, &scan.ParamsRequest{
		DomainList:   domains,
		Level:        0,
		Modify:       domain_task.VERIFY_TYPE,
		Bandwidth:    1000,
		SkipCallback: true,
	})
	if err != nil {
		return 0, err
	}
	return rsp.GolangTaskId, nil
}

// 等待Go验证模式任务完成
func waitDomainAssetVerify(taskId uint64) ([]string, error) {
	const maxWait = 32
	wait := 4
	for {
		time.Sleep(time.Duration(wait) * time.Second)
		info, err := domain_task_golang.NewTasker().FindById(taskId)
		if err != nil {
			return nil, err
		}
		if info.Id != 0 && info.Progress == 100 {
			var domains = make([]string, 0)
			if info.VerifyDomainList != nil {
				_ = json.Unmarshal([]byte(info.VerifyDomainList.String), &domains)
			}
			return domains, nil
		}
		wait = utils.If(wait <= maxWait, wait<<1, 4)
	}
}

// 域名解析结果和验证结果写入域名发现任务结果
func writeDomainFindResult(req *pb.DomainAssetsUpdateByCronRequest, list []*domain_assets.DomainAssets, domains []string) error {
	domainsMap := utils.ListToSet(domains)

	var result = make([]*domain_task_result.DomainResult, 0, len(list))
	for _, v := range list {
		var item = &domain_task_result.DomainResult{
			UserID:      req.UserId,
			CompanyID:   uint64(req.CompanyId),
			TaskID:      req.DomainFindTaskId,
			Domain:      v.Domain,
			FDomain:     v.FDomain,
			TopDomain:   v.TopDomain,
			ParseStatus: domain_task_result.DomainParseInvalid,
		}
		_, ok := domainsMap[v.Domain]
		if ok {
			item.ParseStatus = domain_task_result.DomainParseValid
			item.DnsAaaa = v.DnsAaaa
			item.DnsA = v.DnsA
			item.Cname = v.Cname
		}
		result = append(result, item)
	}

	return domain_task_result.NewResulter().Create(result)
}

func parseDomain(_ context.Context, l []*domain_assets.DomainAssets) []*domain_assets.DomainAssets {
	ch := make(chan *domain_assets.DomainAssets, 5)
	go func() {
		split := utils.ListSplit(l, 150)
		wg := sync.WaitGroup{}
		for j := range split {
			wg.Add(1)
			go func(list []*domain_assets.DomainAssets) {
				defer wg.Done()
				for i := range list {
					list[i].DnsA, list[i].DnsAaaa = dnsRecord(list[i].Domain)
					list[i].Status = dnsIsParse(list[i].Domain)
					list[i].FDomain = parentDomain(list[i].Domain)
					list[i].TopDomain = utils.GetTopDomain(list[i].Domain)
					list[i].Cname = dnsCName(list[i].Domain)
					list[i].Depth = strings.Count(list[i].Domain, ".")
					list[i].OpenParse = isWildCard(list[i].Domain)
					ch <- list[i]
				}
			}(split[j])
		}
		wg.Wait()
		close(ch)
	}()

	var parsed = make([]*domain_assets.DomainAssets, 0, len(l))
	for x := range ch {
		parsed = append(parsed, x)
	}
	return parsed
}

// 获取ICP备案信息
//
//nolint:unused,gocritic
func updateIcpCompany(ctx context.Context, list []*domain_assets.DomainAssets, domains []string) {
	topDomains := make([]string, 0, len(domains))
	for _, v := range domains {
		if topDomain, _ := utils.FindRootDomain(v); topDomain != "" {
			topDomains = append(topDomains, topDomain)
		}
	}
	icpCompanies := icpCompanyName(ctx, topDomains)
	for i := range list {
		topDomain, _ := utils.FindRootDomain(list[i].Domain)
		value, _ := icpCompanies.Get(topDomain)
		list[i].CompanyName = value
	}
}

// getFdomain 获取子域名的上一级域名，如果命中特殊列表则返回空字符串
func getFdomain(domain string) string {
	var specialParentDomains = map[string]struct{}{"com.cn": {}, "net.cn": {}, "org.cn": {}, "com.tr": {}, "gov.cn": {}, "com.au": {}, "co.uk": {}, "com.tw": {}}
	parts := strings.Split(domain, ".")
	// 如果域名部分不足 3 段，直接返回空
	if len(parts) <= 2 {
		return ""
	}
	// 去掉最左侧子域，拼接剩余部分
	fdomain := strings.Join(parts[1:], ".")
	// 过滤特殊父级域名
	if _, found := specialParentDomains[fdomain]; found {
		return ""
	}
	return fdomain
}

// getDepthDomain 返回域名深度
func getDepthDomain(domain string) int {
	parts := strings.Split(domain, ".")
	return len(parts) - 1
}
