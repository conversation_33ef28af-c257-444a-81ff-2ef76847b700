package asset_account

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql/clues"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"

	elastic_search "github.com/olivere/elastic"
	"github.com/pkg/errors"

	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

// SurePassetsList 台账IP端口资产列表
func SurePassetsList(ctx context.Context, req *pb.SurePassetsListRequest, rsp *pb.PassetsListResponse) error {
	log.WithContextInfof(ctx, "Received thraten_assets.SurePassetsList request: %v", req)

	// 详细打印请求参数
	reqJSON, _ := json.Marshal(req)
	log.WithContextInfof(ctx, "【详细调试】完整请求参数: %s", string(reqJSON))

	// 使用 elastic.StructToParams 构建查询条件
	var builder elastic.SearchBuilder
	elastic.StructToParams(req, &builder)

	// 添加默认查询条件
	builder.AddMust([]interface{}{"status", "in", []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}})

	// 关键字
	if req.Keyword != nil {
		var keywordQuery [][]interface{}
		_, err := strconv.Atoi(*req.Keyword)
		if err == nil {
			keywordQuery = append(keywordQuery, []interface{}{"logo.hash", *req.Keyword})
			keywordQuery = append(keywordQuery, []interface{}{"port", *req.Keyword})
		}
		keywordQuery = append(keywordQuery, []interface{}{"subject_key", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"icp.no", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"title.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"url.keywpord", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"protocol", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"domain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"url", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"subdomain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"custom_tags.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"ip.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
		builder.AddShould(keywordQuery...)
	}
	// Title
	if len(req.Title) > 0 {
		if utils.ListContains(req.Title, "-") {
			names := utils.ListReplace(req.Title, "-", "")
			builder.AddShould([]interface{}{"title.keyword", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"title.keyword", "in", elastic.ToInterfaceArray(req.Title)})
		}
	}
	// Domain
	if len(req.Domain) > 0 {
		if utils.ListContains(req.Domain, "-") {
			names := utils.ListReplace(req.Domain, "-", "")
			builder.AddShould([]interface{}{"domain", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"domain", "in", elastic.ToInterfaceArray(req.Domain)})
		}
	}
	// Hosts
	if req.Hosts != nil {
		var hostsQuery [][]interface{}
		hostsQuery = append(hostsQuery, []interface{}{"domain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"subdomain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		builder.AddShould(hostsQuery...)
	}
	// Assets Source
	if len(req.AssetsSource) > 0 {
		if utils.ListContains(req.AssetsSource, "-") {
			names := utils.ListReplace(req.AssetsSource, "-", "")
			builder.AddShould([]interface{}{"assets_source", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"assets_source", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"assets_source", "in", elastic.ToInterfaceArray(req.AssetsSource)})
		}
	}
	// Cloud Name
	if req.CloudName != nil {
		var nameQuery [][]interface{}
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(*req.CloudName)})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToLower(*req.CloudName))})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToUpper(*req.CloudName))})
		builder.AddShould(nameQuery...)
	}
	// Clue Company Name
	if len(req.ClueCompanyName) > 0 {
		if utils.ListContains(req.ClueCompanyName, "-") {
			names := utils.ListReplace(req.ClueCompanyName, "-", "")
			builder.AddShould([]interface{}{"clue_company_name.keyword", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"clue_company_name", "EXISTS"}}})
		} else {
			builder.AddShould([]interface{}{"clue_company_name.keyword", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
			builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
		}
	}
	// 设置默认分页参数
	page := int(req.Page)
	if page < 1 {
		page = 1
	}
	perPage := int(req.PerPage)
	if perPage < 1 || perPage > 100 {
		perPage = 10
	}

	// 构建查询
	query := builder.Build()

	// 添加ES查询语句输出调试
	queryParams := elastic.ParseQuery(query)
	queryJSON, _ := json.MarshalIndent(map[string]interface{}{
		"index": "foradar_assets",
		"body": map[string]interface{}{
			"_source": true,
			"query":   queryParams,
			"sort":    []map[string]map[string]string{{"ip": {"order": "asc"}}},
			"size":    perPage,
		},
		"track_total_hits": true,
	}, "", "  ")
	log.WithContextInfof(ctx, "【ES查询JSON】: %s", string(queryJSON))

	// 打印ES查询语句
	log.WithContextInfof(ctx, "【ES查询调试】查询条件: %v", query)

	// 设置排序
	sorts := []elastic_search.Sorter{
		elastic_search.NewFieldSort("ip").Asc(),
	}

	// 执行查询
	total, list, err := elastic.ListByParams[foradar_assets.ForadarAsset](page, perPage, query, sorts)
	if err != nil {
		log.WithContextErrorf(ctx, "查询台账资产列表失败: %v", err)
		return errors.Wrap(err, "查询台账资产列表失败")
	}
	//我需要把结构体返回的reason转为字符串
	for _, item := range list {
		// 检查reason是否为空，避免空指针或空切片问题
		var reasonStr string
		if len(item.Reason) > 0 {
			reasonStr = getReason(item.Reason, item.Type, false, 3)
		} else {
			reasonStr = getReason(nil, item.Type, false, 3)
		}
		item.ReasonString = reasonStr
	}
	// 转换结果为JSON
	data, err := json.Marshal(list)
	if err != nil {
		log.WithContextErrorf(ctx, "转换台账资产列表结果失败: %v", err)
		return errors.Wrap(err, "转换台账资产列表结果失败")
	}

	// 设置响应
	rsp.Total = total
	rsp.CurrentPage = int32(page)
	rsp.PerPage = int32(perPage)
	rsp.LastPage = int32(total / int64(perPage))
	if total%int64(perPage) > 0 {
		rsp.LastPage++
	}
	rsp.From = int32((page-1)*perPage + 1)
	rsp.To = int32(minInt64(int64(page*perPage), total))
	rsp.Data = string(data)

	return nil
}

// ExportSurePassetsList 导出IP资产
func ExportSurePassetsList(_ context.Context, req *pb.IpPortActionRequest, rsp *pb.FileExportResponse) error {
	log.Infof("[IP资产] 导出请求: %v", req)
	query := IpPortActionSearch(req, []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD})
	log.Infof("[IP资产] 查询条件参数: %s", utils.AnyToStr(query))
	q := elastic.ParseQuery(query)
	list, err := elastic.All[foradar_assets.ForadarAsset](500, q, GetFofaAssetsDefaultSorter())
	if err != nil {
		log.Errorf("[IP资产] 查询失败: %v, req: %v", err, req)
		return err
	}
	// 生成Excel文件
	file := excelize.NewFile()
	sheet := "IP资产"
	err = file.SetSheetName("Sheet1", sheet)
	if err != nil {
		return err
	}

	// 设置表头 - 参考PHP逻辑
	headers := []string{"IP地址", "企业名称", "端口", "协议", "域名", "URL", "网站标题", "组件信息", "状态码", "云厂商", "运营商", "地理位置", "ASN", "经度", "纬度", "资产状态", "探测方式", "发现时间", "更新时间"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err = file.SetCellValue(sheet, cell, header)
		if err != nil {
			return err
		}
	}

	// 写入数据 - 参考PHP逻辑
	row := 2
	for _, r := range list {
		// 处理企业名称 - 支持数组和字符串两种格式
		companyName := ""
		if r.ClueCompanyName != nil {
			// 先尝试作为字符串处理
			if nameStr, ok := r.ClueCompanyName.(string); ok && nameStr != "" {
				companyName = nameStr
			} else if nameArray, ok := r.ClueCompanyName.([]interface{}); ok && len(nameArray) > 0 {
				// 如果是数组，取第一个元素
				if firstElement, ok := nameArray[0].(string); ok && firstElement != "" {
					companyName = firstElement
				}
			}
		}

		// 处理组件信息
		hardInfo := ""
		if len(r.RuleTags) > 0 {
			var components []string
			for _, rule := range r.RuleTags {
				if rule.CnProduct != "" {
					components = append(components, rule.CnProduct)
				}
			}
			hardInfo = strings.Join(components, ",")
		}

		// 处理云厂商名称
		cloudName := ""
		// ForadarAsset 可能没有 CloudName 字段，暂时留空

		// 处理资产状态
		onlineState := "离线"
		if cast.ToInt(r.OnlineState) == 1 {
			onlineState = "在线"
		}

		// 处理探测方式 - 从reason中获取
		var reasonStr string
		if len(r.Reason) > 0 {
			reasonStr = getReason(r.Reason, r.Type, true, 3)
		} else {
			reasonStr = getReason(nil, r.Type, true, 3)
		}
		// 处理网站标题 - 去除等号并截取前200个字符
		title := ""
		if r.Title != nil {
			if titleStr, ok := r.Title.(string); ok && titleStr != "" {
				title = strings.ReplaceAll(titleStr, "=", "")
				if len(title) > 200 {
					title = title[:200]
				}
			}
		}

		// 写入一行数据
		if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Ip); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), companyName); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), r.Port); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("D%d", row), r.Protocol); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("E%d", row), r.Subdomain); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("F%d", row), r.Url); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("G%d", row), title); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("H%d", row), hardInfo); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("I%d", row), r.HTTPStatusCode); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), cloudName); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), r.Geo.Isp); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), r.Geo.Province); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("M%d", row), r.Geo.Asn); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("N%d", row), r.Geo.Lat); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("O%d", row), r.Geo.Lon); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("P%d", row), onlineState); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("Q%d", row), reasonStr); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("R%d", row), r.CreatedAt); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("S%d", row), r.UpdatedAt); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}

		row++
	}

	// 保存文件
	filename := fmt.Sprintf("ip_passets_%s.xlsx", time.Now().Format("20060102150405"))
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	fmt.Println(storagePath)
	if err := file.SaveAs(storagePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	fp, err := utils.DownloadFileEncrypt(accessPath, "IP端口资产数据"+path.Ext(filename), "", false)
	if err != nil {
		return fmt.Errorf("生成下载路径失败: %v", err)
	}
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(filename))

	// 设置返回的文件路径
	rsp.Url = apiFilePath
	return nil
}

func IpPortActionSearch(req *pb.IpPortActionRequest, status []interface{}) [][]interface{} {
	var builder elastic.SearchBuilder
	if len(req.Id) > 0 {
		builder.AddMust([]interface{}{"_id", "in", elastic.ToInterfaceArray(req.Id)})
	} else {
		// 通用字段查询构建
		elastic.StructToParams(req, &builder)
		// 特殊字段
		// 关键字
		if req.Keyword != nil {
			var keywordQuery [][]interface{}
			_, err := strconv.Atoi(*req.Keyword)
			if err == nil {
				keywordQuery = append(keywordQuery, []interface{}{"logo.hash", *req.Keyword})
				keywordQuery = append(keywordQuery, []interface{}{"port", *req.Keyword})
			}
			keywordQuery = append(keywordQuery, []interface{}{"subject_key", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"icp.no", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"clue_company_name.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"title.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"url.keywpord", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"protocol", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"domain", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"url", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"subdomain", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"custom_tags.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"ip.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
			builder.AddShould(keywordQuery...)
		}
		// Title
		if len(req.Title) > 0 {
			if utils.ListContains(req.Title, "-") {
				names := utils.ListReplace(req.Title, "-", "")
				builder.AddShould([]interface{}{"title.keyword", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
			} else {
				builder.AddMust([]interface{}{"title.keyword", "in", elastic.ToInterfaceArray(req.Title)})
			}
		}
		// Domain
		if len(req.Domain) > 0 {
			if utils.ListContains(req.Domain, "-") {
				names := utils.ListReplace(req.Domain, "-", "")
				builder.AddShould([]interface{}{"domain", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
			} else {
				builder.AddMust([]interface{}{"domain", "in", elastic.ToInterfaceArray(req.Domain)})
			}
		}
		// Hosts
		if req.Hosts != nil {
			var hostsQuery [][]interface{}
			hostsQuery = append(hostsQuery, []interface{}{"domain", "like", elastic.WithRLAsterisk(*req.Hosts)})
			hostsQuery = append(hostsQuery, []interface{}{"subdomain", "like", elastic.WithRLAsterisk(*req.Hosts)})
			builder.AddShould(hostsQuery...)
		}
		// Assets Source
		if len(req.AssetsSource) > 0 {
			if utils.ListContains(req.AssetsSource, "-") {
				names := utils.ListReplace(req.AssetsSource, "-", "")
				builder.AddShould([]interface{}{"assets_source", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"assets_source", "EXISTS"}}})
			} else {
				builder.AddMust([]interface{}{"assets_source", "in", elastic.ToInterfaceArray(req.AssetsSource)})
			}
		}
		// Cloud Name
		if req.CloudName != nil {
			var nameQuery [][]interface{}
			nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(*req.CloudName)})
			nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToLower(*req.CloudName))})
			nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToUpper(*req.CloudName))})
			builder.AddShould(nameQuery...)
		}
		// Clue Company Name
		if len(req.ClueCompanyName) > 0 {
			if utils.ListContains(req.ClueCompanyName, "-") {
				names := utils.ListReplace(req.ClueCompanyName, "-", "")
				builder.AddShould([]interface{}{"clue_company_name.keyword", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(names)})
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"clue_company_name", "EXISTS"}}})
			} else {
				builder.AddShould([]interface{}{"clue_company_name.keyword", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
				builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
			}
		}
	}
	builder.AddMust([]interface{}{"status", "in", status}, []interface{}{"user_id", req.UserId})
	return builder.Build()
}

// getReason 根据PHP逻辑获取推荐原因字符串
// 对应PHP的getReason函数
func getReason(reason []foradar_assets.AssetReason, assetType int, export bool, typeTwo int) string {
	// 对应PHP: if (\App\Models\Elastic\IpAssets::TYPE_CLAIMED === $type || \App\Models\Elastic\IpAssets::STATUS_UPLOAD === $type)
	if assetType == fofaee_assets.TYPE_CLAIMED || assetType == fofaee_assets.STATUS_UPLOAD {
		return "已知资产ip"
	}

	// 对应PHP: if (\App\Models\Elastic\IpAssets::TYPE_CLAIMED === $type_two || \App\Models\Elastic\IpAssets::STATUS_UPLOAD === $type_two)
	if typeTwo == fofaee_assets.TYPE_CLAIMED || typeTwo == fofaee_assets.STATUS_UPLOAD {
		return "已知资产ip"
	}

	if len(reason) == 0 {
		return ""
	}

	var info strings.Builder

	// 对应PHP的foreach ($reason as $val)
	for _, val := range reason {
		clueCompanyName := val.ClueCompanyName
		if clueCompanyName == "" {
			clueCompanyName = "-"
		}

		// 对应PHP: if (($val['type'] ?? '') == \App\Models\MySql\Clue::TYPE_LOGO)
		if val.Type == clues.TYPE_LOGO {
			var icoRecTxt string
			if export {
				// 对应PHP的导出模式
				icoRecTxt = storage.GenDownloadUrl(val.Content, "") + " 推荐;"
			} else {
				// 对应PHP的HTML模式
				// 检查content是否包含"pp/public"
				if strings.Contains(val.Content, "pp/public") {
					icoRecTxt = fmt.Sprintf(`<img class="reason_ico" src="%s" />推荐;`, storage.GenDownloadUrl(val.Content, ""))
				} else {
					icoRecTxt = fmt.Sprintf(`<img class="reason_ico" src="%s" />推荐;`, val.Content)
				}
			}
			// 对应PHP: $info .= '根据' . $clue_company_name . '的' . typeToCn($val['type'] ?? '') .' '. $icoRecTxt;
			info.WriteString(fmt.Sprintf("根据%s的%s %s", clueCompanyName, utils.TypeToCn(val.Type), icoRecTxt))
		} else {
			// 对应PHP: $info .= '根据' . $clue_company_name . '的' . typeToCn($val['type'] ?? '') .' '. $val['content'] . '推荐;';
			info.WriteString(fmt.Sprintf("根据%s的%s %s推荐;", clueCompanyName, utils.TypeToCn(val.Type), val.Content))
		}
	}

	return info.String()
}
