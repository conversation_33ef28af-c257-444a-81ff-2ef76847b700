package asset_account

import (
	"context"
	"errors"
	"github.com/google/uuid"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/task"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

func TestAssetAccountImportScan(t *testing.T) {
	Init()

	t.Run("invalid_source_parameter", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "invalid",
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "source参数必须为input、file或domain")
	})

	t.Run("missing_task_param", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "缺少task_param参数")
	})

	t.Run("invalid_define_port_missing_ports", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			IsDefinePort: 1,
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "自定义端口扫描时必须指定端口和协议")
	})

	t.Run("invalid_port_range", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			IsDefinePort:        1,
			DefinePorts:         []uint64{70000}, // Invalid port > 65536
			DefinePortProtocols: []uint64{1},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "端口范围必须在0-65536之间")
	})

	t.Run("input_source_ipv4_invalid_format", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "invalid.ip"},
				{Ip: "*******"},
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Contains(t, rsp.ErrorList, "invalid.ip")
		assert.Contains(t, rsp.DataList, "*******")
	})

	t.Run("input_source_ipv6_validation", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V6,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "2001:db8::1"},
				{Ip: "invalid::ipv6::format"},
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Contains(t, rsp.ErrorList, "invalid::ipv6::format")
	})

	t.Run("input_source_empty_ips", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "扫描目标ip为空，请重新下发扫描任务")
	})

	t.Run("domain_source_invalid_domain", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "domain",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "invalid-domain"},
				{Ip: "example.com"},
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Contains(t, rsp.ErrorList, "invalid-domain")
		assert.Contains(t, rsp.DataList, "example.com")
	})

	t.Run("domain_source_too_many_domains", func(t *testing.T) {
		var domains []*pb.AssetAccountImportScanIpDataItem
		for i := 0; i < 210; i++ {
			domains = append(domains, &pb.AssetAccountImportScanIpDataItem{
				Ip: uuid.New().String() + ".com",
			})
		}

		req := &pb.AssetAccountImportScanRequest{
			Source: "domain",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: domains,
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "单次任务不可以超过200个域名")
	})

	t.Run("user_permission_tenant_limit_exceeded", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		// Mock forbid IPs query
		mock.ExpectQuery("SELECT .* FROM `forbid_ips` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "ip_segment"}))

		// Mock user query - tenant role
		mock.ExpectQuery("SELECT .* FROM `users` WHERE id = .*").
			WithArgs(utils.SqlMockArgs(2)...).
			WillReturnRows(sqlmock.NewRows([]string{"id", "role"}).AddRow(1, user.UserRoleTenant))

		// Mock company query - limit exceeded
		mock.ExpectQuery("SELECT .* FROM `companies` WHERE id = .* AND owner_id = .*").
			WithArgs(utils.SqlMockArgs(2)...).
			WillReturnRows(sqlmock.NewRows([]string{"id", "limit_ip_asset", "used_ip_asset"}).
				AddRow(1, 100, 100)) // Used equals limit

		req := &pb.AssetAccountImportScanRequest{
			Source:     "input",
			UserId:     1,
			CompanyId:  1,
			OperatorId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "*******"},
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "您的台账数据已经超过限制，请联系售后人员")
	})

	t.Run("database_error_handling", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		// Mock forbid IPs query with error
		mock.ExpectQuery("SELECT .* FROM `forbid_ips` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnError(errors.New("database connection error"))

		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "*******"},
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database connection error")
	})

	t.Run("empty_data_ipv6_error", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V6,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "invalid::ipv6"},
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		_ = AssetAccountImportScan(context.Background(), req, rsp)
	})

	t.Run("ipv4_cidr_validation", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "*******/24"}, // Valid CIDR
				{Ip: "*******/33"}, // Invalid CIDR mask
				{Ip: "invalid/24"}, // Invalid IP in CIDR
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Contains(t, rsp.ErrorList, "*******/33")
		assert.Contains(t, rsp.ErrorList, "invalid/24")
	})

	t.Run("ipv4_range_validation", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "*******-10"},  // Valid range
				{Ip: "*******-300"}, // Invalid range (>255)
				{Ip: "invalid-10"},  // Invalid IP in range
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Contains(t, rsp.ErrorList, "*******-300")
		assert.Contains(t, rsp.ErrorList, "invalid-10")
	})

	t.Run("define_port_limit_exceeded", func(t *testing.T) {
		// Create ports that exceed 100 combinations
		var ports []uint64
		var protocols []uint64
		for i := 1; i <= 11; i++ {
			ports = append(ports, uint64(i))
		}
		for i := 1; i <= 10; i++ {
			protocols = append(protocols, uint64(i))
		}

		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "*******"},
			},
			IsDefinePort:        1,
			DefinePorts:         ports,     // 11 ports
			DefinePortProtocols: protocols, // 10 protocols = 110 combinations > 100
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "端口协议随机组合最多100个")
	})
}

func TestCheckHasInsideIP(t *testing.T) {
	t.Run("no_private_ips", func(t *testing.T) {
		ips := []string{"*******", "*******", "**************"}
		result := CheckHasInsideIP(ips)
		assert.Empty(t, result)
	})

	t.Run("contains_private_ipv4", func(t *testing.T) {
		ips := []string{"*******", "***********", "*******"}
		result := CheckHasInsideIP(ips)
		assert.Equal(t, "***********", result)
	})

	t.Run("contains_private_10_network", func(t *testing.T) {
		ips := []string{"*******", "********"}
		result := CheckHasInsideIP(ips)
		assert.Equal(t, "********", result)
	})

	t.Run("contains_private_172_network", func(t *testing.T) {
		ips := []string{"*******", "**********"}
		result := CheckHasInsideIP(ips)
		assert.Equal(t, "**********", result)
	})

	t.Run("contains_localhost", func(t *testing.T) {
		ips := []string{"*******", "***********"}
		result := CheckHasInsideIP(ips)
		assert.Equal(t, "***********", result)
	})

	t.Run("invalid_ip_format", func(t *testing.T) {
		ips := []string{"*******", "invalid.ip.format"}
		result := CheckHasInsideIP(ips)
		assert.Equal(t, "invalid.ip.format", result)
	})

	t.Run("empty_ip_list", func(t *testing.T) {
		ips := []string{}
		result := CheckHasInsideIP(ips)
		assert.Empty(t, result)
	})

	t.Run("returns_first_private_ip", func(t *testing.T) {
		ips := []string{"***********", "********", "**********"}
		result := CheckHasInsideIP(ips)
		assert.Equal(t, "***********", result) // Should return the first private IP found
	})
}

func TestAssetAccountImportScan_EdgeCases(t *testing.T) {
	Init()

	t.Run("user_not_found_error", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		// Mock forbid IPs query
		mock.ExpectQuery("SELECT .* FROM `forbid_ips` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "ip_segment"}))

		// Mock user query - user not found
		mock.ExpectQuery("SELECT .* FROM `users` WHERE `users`.`id` = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		req := &pb.AssetAccountImportScanRequest{
			Source: "input",
			UserId: 999, // Non-existent user
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "*******"},
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
	})

	t.Run("company_not_found_error", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		// Mock forbid IPs query
		mock.ExpectQuery("SELECT .* FROM `forbid_ips` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "ip_segment"}))

		// Mock user query - tenant role
		mock.ExpectQuery("SELECT .* FROM `users` WHERE `users`.`id` = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "role"}).AddRow(1, user.UserRoleTenant))

		// Mock company query - company not found
		mock.ExpectQuery("SELECT .* FROM `companies` WHERE id = .* AND owner_id = .*").
			WithArgs(utils.SqlMockArgs(2)...).
			WillReturnError(gorm.ErrRecordNotFound)

		req := &pb.AssetAccountImportScanRequest{
			Source:    "input",
			UserId:    1,
			CompanyId: 999, // Non-existent company
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "*******"},
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
	})

	t.Run("domain_source_empty_domains", func(t *testing.T) {
		req := &pb.AssetAccountImportScanRequest{
			Source: "domain",
			UserId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "扫描目标为空，请重新下发扫描任务")
	})

	t.Run("clues_group_not_found", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		// Mock forbid IPs query
		mock.ExpectQuery("SELECT .* FROM `forbid_ips` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "ip_segment"}))

		// Mock clues_groups query - not found
		mock.ExpectQuery("SELECT .* FROM `clues_groups` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock user query
		mock.ExpectQuery("SELECT .* FROM `users` WHERE `users`.`id` = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "role"}).AddRow(1, user.UserRoleTenant))

		// Mock company query
		mock.ExpectQuery("SELECT .* FROM `companies` WHERE id = .* AND owner_id = .*").
			WithArgs(utils.SqlMockArgs(2)...).
			WillReturnRows(sqlmock.NewRows([]string{"id", "limit_ip_asset", "used_ip_asset"}).
				AddRow(1, 1000, 0))

		req := &pb.AssetAccountImportScanRequest{
			Source:     "domain",
			UserId:     1,
			CompanyId:  1,
			OperatorId: 1,
			TaskParam: &pb.AssetAccountImportScanTaskParam{
				IpType: task.IP_TYPE_V4,
			},
			Data: []*pb.AssetAccountImportScanIpDataItem{
				{Ip: "example.com"},
			},
		}
		rsp := &pb.AssetAccountImportScanResponse{}
		err := AssetAccountImportScan(context.Background(), req, rsp)
		assert.Error(t, err)
	})
}
