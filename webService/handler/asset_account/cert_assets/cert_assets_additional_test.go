package cert_assets

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	pb "micro-service/webService/proto"
)

// 为CertAssetsList函数生成更多测试用例
func TestCertAssetsList_AdditionalCases(t *testing.T) {
	Init()

	tests := []struct {
		name string
		req  *pb.CertAssetListRequest
	}{
		{
			name: "with_keyword_search",
			req: &pb.CertAssetListRequest{
				UserId:  1,
				Page:    1,
				PerPage: 10,
				Keyword: "example",
			},
		},
		{
			name: "with_website_message_id",
			req: &pb.CertAssetListRequest{
				UserId:           1,
				Page:             1,
				PerPage:          10,
				WebsiteMessageId: 123,
			},
		},
		{
			name: "with_issuer_filters",
			req: &pb.CertAssetListRequest{
				UserId:    1,
				Page:      1,
				PerPage:   10,
				IssuerCn:  "Let's Encrypt",
				IssuerOrg: "Let's Encrypt",
			},
		},
		{
			name: "with_subject_filters",
			req: &pb.CertAssetListRequest{
				UserId:     1,
				Page:       1,
				PerPage:    10,
				SubjectCn:  "*.example.com",
				SubjectOrg: "Example Corp",
			},
		},
		{
			name: "with_domain_and_ip_filters",
			req: &pb.CertAssetListRequest{
				UserId:  1,
				Page:    1,
				PerPage: 10,
				Domain:  "example.com",
				Ip:      "***********",
				Port:    "443",
			},
		},
		{
			name: "with_version_filter",
			req: &pb.CertAssetListRequest{
				UserId:  1,
				Page:    1,
				PerPage: 10,
				Version: "v3",
			},
		},
		{
			name: "with_company_name_filter",
			req: &pb.CertAssetListRequest{
				UserId:      1,
				Page:        1,
				PerPage:     10,
				CompanyName: []string{"Example Corp", "Test Company"},
			},
		},
		{
			name: "with_time_range_filters",
			req: &pb.CertAssetListRequest{
				UserId:         1,
				Page:           1,
				PerPage:        10,
				CreatedAtRange: []string{time.Now().Add(-30 * 24 * time.Hour).Format("2006-01-02"), time.Now().Format("2006-01-02")},
				UpdatedAtRange: []string{time.Now().Add(-7 * 24 * time.Hour).Format("2006-01-02"), time.Now().Format("2006-01-02")},
			},
		},
		{
			name: "with_all_filters_combined",
			req: &pb.CertAssetListRequest{
				UserId:           1,
				Page:             1,
				PerPage:          10,
				Keyword:          "test",
				WebsiteMessageId: 456,
				IssuerCn:         "DigiCert",
				IssuerOrg:        "DigiCert Inc",
				SubjectCn:        "test.example.com",
				SubjectOrg:       "Test Corp",
				Domain:           "test.com",
				Ip:               "********",
				Port:             "8443",
				Version:          "v3",
				CompanyName:      []string{"Test Corp"},
			},
		},
		{
			name: "large_page_size",
			req: &pb.CertAssetListRequest{
				UserId:  1,
				Page:    1,
				PerPage: 500,
			},
		},
		{
			name: "high_page_number",
			req: &pb.CertAssetListRequest{
				UserId:  1,
				Page:    50,
				PerPage: 20,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := &pb.CertAssetListResponse{}
			err := CertAssetsList(context.Background(), tt.req, resp)
			// 在Mock环境中，主要验证函数不会panic
			t.Logf("CertAssetsList %s error (expected in mock env): %v", tt.name, err)
			assert.NotNil(t, resp)
		})
	}
}

// 为CertAssetsExport函数生成更多测试用例
func TestCertAssetsExport_AdditionalCases(t *testing.T) {
	Init()

	tests := []struct {
		name string
		req  *pb.CertAssetExportRequest
	}{
		{
			name: "export_by_single_id",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Ids:    []uint64{1},
			},
		},
		{
			name: "export_by_multiple_ids",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Ids:    []uint64{1, 2, 3, 4, 5},
			},
		},
		{
			name: "export_by_keyword",
			req: &pb.CertAssetExportRequest{
				UserId:  1,
				Keyword: "example",
			},
		},
		{
			name: "export_by_website_message_id",
			req: &pb.CertAssetExportRequest{
				UserId:           1,
				WebsiteMessageId: 123,
			},
		},
		{
			name: "export_by_issuer_info",
			req: &pb.CertAssetExportRequest{
				UserId:    1,
				IssuerCn:  "Let's Encrypt Authority X3",
				IssuerOrg: "Let's Encrypt",
			},
		},
		{
			name: "export_by_subject_info",
			req: &pb.CertAssetExportRequest{
				UserId:     1,
				SubjectCn:  "*.example.com",
				SubjectOrg: "Example Corporation",
			},
		},
		{
			name: "export_by_domain_and_network",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Domain: "example.com",
				Ip:     "***********",
				Port:   "443",
			},
		},
		{
			name: "export_by_version",
			req: &pb.CertAssetExportRequest{
				UserId:  1,
				Version: "v3",
			},
		},
		{
			name: "export_by_company_names",
			req: &pb.CertAssetExportRequest{
				UserId:      1,
				CompanyName: []string{"Example Corp", "Test Inc", "Demo LLC"},
			},
		},
		{
			name: "export_by_time_ranges",
			req: &pb.CertAssetExportRequest{
				UserId:         1,
				CreatedAtRange: []string{"2023-01-01", "2023-12-31"},
				UpdatedAtRange: []string{"2023-06-01", "2023-12-31"},
			},
		},
		{
			name: "export_with_all_filters",
			req: &pb.CertAssetExportRequest{
				UserId:           1,
				Keyword:          "secure",
				WebsiteMessageId: 789,
				IssuerCn:         "DigiCert SHA2 High Assurance Server CA",
				IssuerOrg:        "DigiCert Inc",
				SubjectCn:        "secure.example.com",
				SubjectOrg:       "Secure Corp",
				Domain:           "secure.com",
				Ip:               "***********",
				Port:             "8443",
				Version:          "v3",
				CompanyName:      []string{"Secure Corp"},
				CreatedAtRange:   []string{"2023-01-01", "2023-12-31"},
				UpdatedAtRange:   []string{"2023-01-01", "2023-12-31"},
			},
		},
		{
			name: "export_large_id_list",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Ids:    generateCertIDList(100),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := &pb.FileExportResponse{}
			err := CertAssetsExport(context.Background(), tt.req, resp)
			// 在Mock环境中，主要验证函数不会panic
			t.Logf("CertAssetsExport %s error (expected in mock env): %v", tt.name, err)
			assert.NotNil(t, resp)
		})
	}
}

// 为CertAssetsDelete函数生成更多测试用例
func TestCertAssetsDelete_AdditionalCases(t *testing.T) {
	Init()

	tests := []struct {
		name string
		req  *pb.CertAssetExportRequest
	}{
		{
			name: "delete_by_single_id",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Ids:    []uint64{1},
			},
		},
		{
			name: "delete_by_multiple_ids",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Ids:    []uint64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
			},
		},
		{
			name: "delete_by_keyword_filter",
			req: &pb.CertAssetExportRequest{
				UserId:  1,
				Keyword: "expired",
			},
		},
		{
			name: "delete_by_issuer_filter",
			req: &pb.CertAssetExportRequest{
				UserId:    1,
				IssuerCn:  "Expired CA",
				IssuerOrg: "Old Certificate Authority",
			},
		},
		{
			name: "delete_by_domain_filter",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Domain: "old-domain.com",
			},
		},
		{
			name: "delete_by_ip_and_port",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Ip:     "*************",
				Port:   "8443",
			},
		},
		{
			name: "delete_by_company_name",
			req: &pb.CertAssetExportRequest{
				UserId:      1,
				CompanyName: []string{"Defunct Corp", "Closed Company"},
			},
		},
		{
			name: "delete_by_old_time_range",
			req: &pb.CertAssetExportRequest{
				UserId:         1,
				CreatedAtRange: []string{"2020-01-01", "2021-12-31"},
				UpdatedAtRange: []string{"2020-01-01", "2021-12-31"},
			},
		},
		{
			name: "delete_with_multiple_filters",
			req: &pb.CertAssetExportRequest{
				UserId:     1,
				Keyword:    "test",
				SubjectOrg: "Test Organization",
				Version:    "v1", // 删除旧版本证书
			},
		},
		{
			name: "delete_large_batch",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Ids:    generateCertIDList(50),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CertAssetsDelete(context.Background(), tt.req, &pb.Empty{})
			// 在Mock环境中，主要验证函数不会panic
			t.Logf("CertAssetsDelete %s error (expected in mock env): %v", tt.name, err)
		})
	}
}

// 为CertAssetDetail函数生成更多测试用例
func TestCertAssetDetail_AdditionalCases(t *testing.T) {
	Init()

	tests := []struct {
		name string
		req  *pb.CertAssetDetailRequest
	}{
		{
			name: "detail_for_existing_cert",
			req: &pb.CertAssetDetailRequest{
				UserId: 1,
				Id:     1,
			},
		},
		{
			name: "detail_for_high_id",
			req: &pb.CertAssetDetailRequest{
				UserId: 1,
				Id:     999999,
			},
		},
		{
			name: "detail_for_different_user",
			req: &pb.CertAssetDetailRequest{
				UserId: 999,
				Id:     1,
			},
		},
		{
			name: "detail_with_zero_id",
			req: &pb.CertAssetDetailRequest{
				UserId: 1,
				Id:     0,
			},
		},
		{
			name: "detail_with_negative_user_id",
			req: &pb.CertAssetDetailRequest{
				UserId: 0,
				Id:     1,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := &pb.CertAssetItem{}
			err := CertAssetDetail(context.Background(), tt.req, resp)
			// 在Mock环境中，主要验证函数不会panic
			t.Logf("CertAssetDetail %s error (expected in mock env): %v", tt.name, err)
			assert.NotNil(t, resp)
		})
	}
}

// 辅助函数：生成证书ID列表
func generateCertIDList(count int) []uint64 {
	var ids []uint64
	for i := 1; i <= count; i++ {
		ids = append(ids, uint64(i))
	}
	return ids
}
