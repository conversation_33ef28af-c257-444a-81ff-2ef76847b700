package cert_assets

import (
	"context"
	"fmt"
	"github.com/xuri/excelize/v2"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/cert_assets"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"net"
	"path"
	"path/filepath"
	"strings"
	"time"
)

func CertAssetsList(_ context.Context, req *pb.CertAssetListRequest, resp *pb.CertAssetListResponse) error {
	// 如果关键词是IP，则将其转换为IP字段
	if ip := net.ParseIP(req.Keyword); ip != nil {
		req.Ip = req.Keyword
		req.Keyword = ""
	}
	// 查询参数
	query := make([]mysql.HandleFunc, 0, 10)
	keywordQuery := make([]mysql.HandleFunc, 0, 4)
	if req.Keyword != "" {
		keywordQuery = append(keywordQuery, mysql.WithLRLike("subject_cn", req.Keyword),
			mysql.WithOrLRLike("subject_org", req.Keyword),
			mysql.WithOrLRLike("issuer_cn", req.Keyword),
			mysql.WithOrLRLike("cert_ip_domains.domain", req.Keyword),
		)
	}
	if req.WebsiteMessageId != 0 {
		query = append(query, mysql.WithWhere("website_message_id = ?", req.WebsiteMessageId))
	}
	if req.Ip != "" {
		query = append(query, mysql.WithLRLike("cert_ip_domains.ip", req.Ip))
	}
	if req.IssuerCn != "" {
		query = append(query, mysql.WithLRLike("issuer_cn", req.IssuerCn))
	}
	if req.IssuerOrg != "" {
		query = append(query, mysql.WithLRLike("issuer_org", req.IssuerOrg))
	}
	if req.SubjectCn != "" {
		query = append(query, mysql.WithLRLike("subject_cn", req.SubjectCn))
	}
	if req.SubjectOrg != "" {
		query = append(query, mysql.WithLRLike("subject_org", req.SubjectOrg))
	}
	if req.Domain != "" {
		query = append(query, mysql.WithLRLike("cert_ip_domains.domain", req.Domain))
	}
	if req.Port != "" {
		query = append(query, mysql.WithWhere("cert_ip_domains.port = ?", req.Port))
	}
	if len(req.CompanyName) > 0 {
		query = append(query, mysql.WithValuesIn("company_name", req.CompanyName))
	}
	if req.Version != "" {
		query = append(query, mysql.WithLRLike("version", req.Version))
	}
	if req.IsSelfSign != 0 {
		query = append(query, mysql.WithWhere("is_self_sign = ?", utils.If(req.IsSelfSign == 1, 1, 0)))
	}
	if req.IsValid != 0 {
		query = append(query, mysql.WithWhere("cert_assets.is_valid = ?", utils.If(req.IsValid == 1, 1, 0)))
	}
	if req.NotAfter != -1 {
		// 证书是否过期 0已过期 1未过期，-1表示不过滤
		// 对应PHP逻辑：not_after字段与当前时间比较
		if req.NotAfter == 1 {
			// 未过期：not_after > 当前时间
			query = append(query, mysql.WithWhere("cert_assets.not_after > ?", time.Now().Format("2006-01-02 15:04:05")))
		} else if req.NotAfter == 0 {
			// 已过期：not_after <= 当前时间
			query = append(query, mysql.WithWhere("cert_assets.not_after <= ?", time.Now().Format("2006-01-02 15:04:05")))
		}
		// req.NotAfter == -1 时不添加任何过滤条件
	}
	if len(req.CreatedAtRange) == 2 {
		start, end, err := utils.GenerateTimeBetween([2]string(req.CreatedAtRange))
		if err != nil {
			log.Errorf("[证书资产] generate created_at_range error %v", err)
			return err
		}
		query = append(query, mysql.WithBetween("cert_assets.created_at", start, end))
	}
	if len(req.UpdatedAtRange) == 2 {
		start, end, err := utils.GenerateTimeBetween([2]string(req.UpdatedAtRange))
		if err != nil {
			log.Errorf("[证书资产] generate updated_at_range error %v", err)
			return err
		}
		query = append(query, mysql.WithBetween("cert_assets.updated_at", start, end))
	}
	query = append(query, mysql.WithWhere("cert_assets.user_id = ?", req.UserId))
	query = append(query, mysql.WithOrder("cert_assets.id DESC"))
	// 分页查询
	list, total, err := cert_assets.NewModel().List(req.Page, req.PerPage, keywordQuery, query...)
	if err != nil {
		return err
	}
	resp.Total = total
	resp.CurrentPage = req.Page
	// 数据处理
	for _, item := range list {
		domain, err := cert_assets.NewDomainModel().ListByAssetId(item.ID)
		if err != nil {
			log.Errorf("[证书资产] list cert_domain error %v", err)
			return err
		}
		var ipDomain []*pb.CertAssetIpDomain
		var ipDomainList = make(map[string]*pb.CertAssetDomainArray)
		for _, d := range domain {
			ipDomain = append(ipDomain, &pb.CertAssetIpDomain{
				Id:           d.ID,
				UserId:       d.UserId,
				CompanyId:    d.CompanyId,
				CertAssetsId: d.CertAssetsId,
				Ip:           d.Ip,
				Domain:       d.Domain,
				Source:       d.Source,
				CreatedAt:    d.CreatedAt.Format("2006-01-02 15:04:05"),
				UpdatedAt:    d.UpdatedAt.Format("2006-01-02 15:04:05"),
				Port:         d.Port,
				IsValid:      int64(d.IsValid),
			})
			// 使用新的CertAssetDomainArray结构，直接存储字符串数组
			if ipDomainList[d.Ip] == nil {
				ipDomainList[d.Ip] = &pb.CertAssetDomainArray{
					Values: []string{d.Domain},
				}
			} else {
				ipDomainList[d.Ip].Values = append(ipDomainList[d.Ip].Values, d.Domain)
			}
		}
		resp.Items = append(resp.Items, &pb.CertAssetItem{
			Id:               uint64(item.ID),
			UserId:           item.UserId,
			CompanyId:        item.CompanyId,
			CompanyName:      item.CompanyName,
			Sn:               item.Sn,
			IssuerCn:         item.IssuerCn,
			IssuerCns:        item.IssuerCns,
			IssuerOrg:        item.IssuerOrg,
			IssuerOu:         item.IssuerOu,
			Cert:             item.Cert,
			CertNum:          int64(item.CertNum),
			SigAlth:          item.SigAlth,
			SubjectCn:        item.SubjectCn,
			SubjectOrg:       item.SubjectOrg,
			SubjectOu:        item.SubjectOu,
			SubjectKey:       item.SubjectKey,
			ValidType:        item.ValidType,
			IsValid:          int64(item.IsValid),
			Version:          item.Version,
			Sha256:           item.Sha256,
			Sha1:             item.Sha1,
			CertDate:         item.CertDate,
			NotBefore:        item.NotBefore,
			NotAfter:         item.NotAfter,
			CreatedAt:        item.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:        item.UpdatedAt.Format("2006-01-02 15:04:05"),
			DetectTaskId:     item.DetectTaskId,
			WebsiteMessageId: item.WebsiteMessageId,
			IsSelfSign:       int64(item.IsSelfSign),
			IpDomainList:     ipDomainList,
			IpDomain:         ipDomain,
		})
	}
	companyList, err := cert_assets.NewModel().ListDistinctCompany(req.UserId)
	if err != nil {
		log.Errorf("[证书资产] list distinct company error %v", err)
		return err
	}
	resp.Condition = &pb.CertAssetListCondition{
		CompanyName: companyList,
	}
	return nil
}

func CertAssetsExport(_ context.Context, req *pb.CertAssetExportRequest, resp *pb.FileExportResponse) error {
	query := make([]mysql.HandleFunc, 0, 10)
	keywordQuery := make([]mysql.HandleFunc, 0, 4)
	if len(req.Ids) > 0 {
		query = append(query, mysql.WithValuesIn("cert_assets.id", req.Ids))
	} else {
		// 如果关键词是IP，则将其转换为IP字段
		if ip := net.ParseIP(req.Keyword); ip != nil {
			req.Ip = req.Keyword
			req.Keyword = ""
		}
		// 查询参数
		if req.Keyword != "" {
			keywordQuery = append(keywordQuery, mysql.WithLRLike("subject_cn", req.Keyword),
				mysql.WithOrLRLike("subject_org", req.Keyword),
				mysql.WithOrLRLike("issuer_cn", req.Keyword),
				mysql.WithOrLRLike("cert_ip_domains.domain", req.Keyword),
			)
		}
		if req.WebsiteMessageId != 0 {
			query = append(query, mysql.WithWhere("website_message_id = ?", req.WebsiteMessageId))
		}
		if req.Ip != "" {
			query = append(query, mysql.WithLRLike("cert_ip_domains.ip", req.Ip))
		}
		if req.IssuerCn != "" {
			query = append(query, mysql.WithLRLike("issuer_cn", req.IssuerCn))
		}
		if req.IssuerOrg != "" {
			query = append(query, mysql.WithLRLike("issuer_org", req.IssuerOrg))
		}
		if req.SubjectCn != "" {
			query = append(query, mysql.WithLRLike("subject_cn", req.SubjectCn))
		}
		if req.SubjectOrg != "" {
			query = append(query, mysql.WithLRLike("subject_org", req.SubjectOrg))
		}
		if req.Domain != "" {
			query = append(query, mysql.WithLRLike("cert_ip_domains.domain", req.Domain))
		}
		if req.Port != "" {
			query = append(query, mysql.WithWhere("cert_ip_domains.port = ?", req.Port))
		}
		if len(req.CompanyName) > 0 {
			query = append(query, mysql.WithValuesIn("company_name", req.CompanyName))
		}
		if req.Version != "" {
			query = append(query, mysql.WithLRLike("version", req.Version))
		}
		if req.IsSelfSign != 0 {
			query = append(query, mysql.WithWhere("is_self_sign = ?", utils.If(req.IsSelfSign == 1, 1, 0)))
		}
		if req.IsValid != 0 {
			query = append(query, mysql.WithWhere("is_valid = ?", utils.If(req.IsValid == 1, 1, 0)))
		}
		if req.NotAfter != -1 {
			// 证书是否过期 0已过期 1未过期，-1表示不过滤
			// 对应PHP逻辑：not_after字段与当前时间比较
			if req.NotAfter == 1 {
				// 未过期：not_after > 当前时间
				query = append(query, mysql.WithWhere("cert_assets.not_after > ?", time.Now().Format("2006-01-02 15:04:05")))
			} else if req.NotAfter == 0 {
				// 已过期：not_after <= 当前时间
				query = append(query, mysql.WithWhere("cert_assets.not_after <= ?", time.Now().Format("2006-01-02 15:04:05")))
			}
			// req.NotAfter == -1 时不添加任何过滤条件
		}
		if len(req.CreatedAtRange) == 2 {
			start, end, err := utils.GenerateTimeBetween([2]string(req.CreatedAtRange))
			if err != nil {
				log.Errorf("[证书资产] generate created_at_range error %v", err)
				return err
			}
			query = append(query, mysql.WithBetween("cert_assets.created_at", start, end))
		}
		if len(req.UpdatedAtRange) == 2 {
			start, end, err := utils.GenerateTimeBetween([2]string(req.UpdatedAtRange))
			if err != nil {
				log.Errorf("[证书资产] generate updated_at_range error %v", err)
				return err
			}
			query = append(query, mysql.WithBetween("cert_assets.updated_at", start, end))
		}

	}
	query = append(query, mysql.WithWhere("cert_assets.user_id = ?", req.UserId))
	query = append(query, mysql.WithOrder("cert_assets.id DESC"))
	list, err := cert_assets.NewModel().ListAll(keywordQuery, query...)
	if err != nil {
		log.Errorf("[证书资产] cert_assets list all err: %v", err)
		return err
	}

	// 生成Excel文件
	file := excelize.NewFile()
	sheet := "证书资产数据"
	err = file.SetSheetName("Sheet1", sheet)
	if err != nil {
		return err
	}

	// 设置表头
	headers := []string{"证书使用者", "证书所有者的组织名称", "证书所有者的组织单位", "证书颁发者", "签发证书的组织", "签发证书的组织单位", "证书版本", "颁发日期", "截止日期", "IP", "域名", "企业名称"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err = file.SetCellValue(sheet, cell, header)
		if err != nil {
			return err
		}
	}

	// 写入数据
	row := 2
	for _, r := range list {
		domain, err := cert_assets.NewDomainModel().ListByAssetId(r.ID)
		if err != nil {
			log.Errorf("[证书资产] list cert_domain error %v", err)
			return err
		}

		// 按照PHP逻辑，将同一个证书的IP和域名合并到一行
		// 按IP分组域名
		domainMap := make(map[string][]string)
		for _, d := range domain {
			domainMap[d.Ip] = append(domainMap[d.Ip], d.Domain)
		}

		// 收集所有IP和所有域名
		var allIPs []string
		var allDomains []string
		for ip, domains := range domainMap {
			allIPs = append(allIPs, ip)
			allDomains = append(allDomains, domains...)
		}

		// 去重域名
		uniqueDomains := make([]string, 0)
		domainSet := make(map[string]bool)
		for _, domain := range allDomains {
			if domain != "" && !domainSet[domain] {
				uniqueDomains = append(uniqueDomains, domain)
				domainSet[domain] = true
			}
		}

		// 限制字符串长度（按照PHP逻辑）
		subjectCn := r.SubjectCn
		if len(subjectCn) > 200 {
			subjectCn = subjectCn[:200]
		}
		subjectOrg := r.SubjectOrg
		if len(subjectOrg) > 200 {
			subjectOrg = subjectOrg[:200]
		}
		subjectOu := r.SubjectOu
		if len(subjectOu) > 200 {
			subjectOu = subjectOu[:200]
		}
		issuerCn := r.IssuerCn
		if len(issuerCn) > 200 {
			issuerCn = issuerCn[:200]
		}
		issuerOrg := r.IssuerOrg
		if len(issuerOrg) > 200 {
			issuerOrg = issuerOrg[:200]
		}
		issuerOu := r.IssuerOu
		if len(issuerOu) > 200 {
			issuerOu = issuerOu[:200]
		}
		version := r.Version
		if len(version) > 200 {
			version = version[:200]
		}
		notBefore := r.NotBefore
		if len(notBefore) > 200 {
			notBefore = notBefore[:200]
		}
		notAfter := r.NotAfter
		if len(notAfter) > 200 {
			notAfter = notAfter[:200]
		}
		ipList := strings.Join(allIPs, ",")
		if len(ipList) > 200 {
			ipList = ipList[:200]
		}
		domainList := strings.Join(uniqueDomains, ",")
		if len(domainList) > 200 {
			domainList = domainList[:200]
		}
		companyName := r.CompanyName
		if len(companyName) > 200 {
			companyName = companyName[:200]
		}

		// 写入一行数据
		if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), subjectCn); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), subjectOrg); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), subjectOu); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("D%d", row), issuerCn); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("E%d", row), issuerOrg); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("F%d", row), issuerOu); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("G%d", row), version); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("H%d", row), notBefore); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("I%d", row), notAfter); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), ipList); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), domainList); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), companyName); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		row++
	}

	// 保存文件
	filename := fmt.Sprintf("cert_assets_%s.xlsx", time.Now().Format("20060102150405"))
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	if err := file.SaveAs(storagePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	fp, err := utils.DownloadFileEncrypt(accessPath, "证书资产数据_"+time.Now().Format(time.DateOnly)+path.Ext(filename), "", false)
	if err != nil {
		return fmt.Errorf("生成下载路径失败: %v", err)
	}
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(filename))

	// 设置返回的文件路径
	resp.Url = apiFilePath
	return nil
}

func CertAssetsDelete(_ context.Context, req *pb.CertAssetExportRequest, _ *pb.Empty) error {
	query := make([]mysql.HandleFunc, 0, 10)
	keywordQuery := make([]mysql.HandleFunc, 0, 4)
	if len(req.Ids) > 0 {
		query = append(query, mysql.WithValuesIn("cert_assets.id", req.Ids))
	} else {
		// 如果关键词是IP，则将其转换为IP字段
		if ip := net.ParseIP(req.Keyword); ip != nil {
			req.Ip = req.Keyword
			req.Keyword = ""
		}
		// 查询参数
		if req.Keyword != "" {
			keywordQuery = append(keywordQuery, mysql.WithLRLike("subject_cn", req.Keyword),
				mysql.WithOrLRLike("subject_org", req.Keyword),
				mysql.WithOrLRLike("issuer_cn", req.Keyword),
				mysql.WithOrLRLike("cert_ip_domains.domain", req.Keyword),
			)
		}
		if req.WebsiteMessageId != 0 {
			query = append(query, mysql.WithWhere("website_message_id = ?", req.WebsiteMessageId))
		}
		if req.Ip != "" {
			query = append(query, mysql.WithLRLike("cert_ip_domains.ip", req.Ip))
		}
		if req.IssuerCn != "" {
			query = append(query, mysql.WithLRLike("issuer_cn", req.IssuerCn))
		}
		if req.IssuerOrg != "" {
			query = append(query, mysql.WithLRLike("issuer_org", req.IssuerOrg))
		}
		if req.SubjectCn != "" {
			query = append(query, mysql.WithLRLike("subject_cn", req.SubjectCn))
		}
		if req.SubjectOrg != "" {
			query = append(query, mysql.WithLRLike("subject_org", req.SubjectOrg))
		}
		if req.Domain != "" {
			query = append(query, mysql.WithLRLike("cert_ip_domains.domain", req.Domain))
		}
		if req.Port != "" {
			query = append(query, mysql.WithWhere("cert_ip_domains.port = ?", req.Port))
		}
		if len(req.CompanyName) > 0 {
			query = append(query, mysql.WithValuesIn("company_name", req.CompanyName))
		}
		if req.Version != "" {
			query = append(query, mysql.WithLRLike("version", req.Version))
		}
		if req.IsSelfSign != 0 {
			query = append(query, mysql.WithWhere("is_self_sign = ?", utils.If(req.IsSelfSign == 1, 1, 0)))
		}
		if req.IsValid != 0 {
			query = append(query, mysql.WithWhere("is_valid = ?", utils.If(req.IsValid == 1, 1, 0)))
		}
		if req.NotAfter != -1 {
			// 证书是否过期 0已过期 1未过期，-1表示不过滤
			// 对应PHP逻辑：not_after字段与当前时间比较
			if req.NotAfter == 1 {
				// 未过期：not_after > 当前时间
				query = append(query, mysql.WithWhere("cert_assets.not_after > ?", time.Now().Format("2006-01-02 15:04:05")))
			} else if req.NotAfter == 0 {
				// 已过期：not_after <= 当前时间
				query = append(query, mysql.WithWhere("cert_assets.not_after <= ?", time.Now().Format("2006-01-02 15:04:05")))
			}
			// req.NotAfter == -1 时不添加任何过滤条件
		}
		if len(req.CreatedAtRange) == 2 {
			start, end, err := utils.GenerateTimeBetween([2]string(req.CreatedAtRange))
			if err != nil {
				log.Errorf("[证书资产] generate created_at_range error %v", err)
				return err
			}
			query = append(query, mysql.WithBetween("cert_assets.created_at", start, end))
		}
		if len(req.UpdatedAtRange) == 2 {
			start, end, err := utils.GenerateTimeBetween([2]string(req.UpdatedAtRange))
			if err != nil {
				log.Errorf("[证书资产] generate updated_at_range error %v", err)
				return err
			}
			query = append(query, mysql.WithBetween("cert_assets.updated_at", start, end))
		}

	}
	query = append(query, mysql.WithWhere("cert_assets.user_id = ?", req.UserId))
	query = append(query, mysql.WithOrder("cert_assets.id DESC"))
	err := cert_assets.NewModel().Delete(keywordQuery, query...)
	if err != nil {
		log.Errorf("[证书资产] cert_assets list all err: %v", err)
		return err
	}
	return nil
}

func CertAssetDetail(_ context.Context, req *pb.CertAssetDetailRequest, resp *pb.CertAssetItem) error {
	list, total, err := cert_assets.NewModel().List(1, 1, nil,
		mysql.WithWhere("cert_assets.id = ?", req.Id),
		mysql.WithWhere("cert_assets.user_id = ?", req.UserId),
	)
	if err != nil {
		log.Errorf("[证书资产] cert_assets list all err: %v", err)
		return err
	}
	if total == 0 {
		return fmt.Errorf("证书资产不存在")
	}
	resp.Id = uint64(list[0].ID)
	resp.UserId = list[0].UserId
	resp.CompanyId = list[0].CompanyId
	resp.CompanyName = list[0].CompanyName
	resp.Sn = list[0].Sn
	resp.IssuerCn = list[0].IssuerCn
	resp.IssuerCns = list[0].IssuerCns
	resp.IssuerOrg = list[0].IssuerOrg
	resp.IssuerOu = list[0].IssuerOu
	resp.Cert = list[0].Cert
	resp.CertNum = int64(list[0].CertNum)
	resp.SigAlth = list[0].SigAlth
	resp.SubjectCn = list[0].SubjectCn
	resp.SubjectOrg = list[0].SubjectOrg
	resp.SubjectOu = list[0].SubjectOu
	resp.SubjectKey = list[0].SubjectKey
	resp.ValidType = list[0].ValidType
	resp.IsValid = int64(list[0].IsValid)
	resp.Version = list[0].Version
	resp.Sha256 = list[0].Sha256
	resp.Sha1 = list[0].Sha1
	resp.CertDate = list[0].CertDate
	resp.NotBefore = list[0].NotBefore
	resp.NotAfter = list[0].NotAfter
	resp.CreatedAt = list[0].CreatedAt.Format("2006-01-02 15:04:05")
	resp.UpdatedAt = list[0].UpdatedAt.Format("2006-01-02 15:04:05")
	resp.IsSelfSign = int64(list[0].IsSelfSign)
	resp.DetectTaskId = list[0].DetectTaskId
	resp.WebsiteMessageId = list[0].WebsiteMessageId
	domain, err := cert_assets.NewDomainModel().ListByAssetId(list[0].ID)
	if err != nil {
		log.Errorf("[证书资产] list cert_domain error %v", err)
		return err
	}
	resp.IpDomain = make([]*pb.CertAssetIpDomain, 0, len(domain))
	resp.IpDomainList = make(map[string]*pb.CertAssetDomainArray, len(domain))
	for _, d := range domain {
		resp.IpDomain = append(resp.IpDomain, &pb.CertAssetIpDomain{
			Id:           d.ID,
			UserId:       d.UserId,
			CompanyId:    d.CompanyId,
			CertAssetsId: d.CertAssetsId,
			Ip:           d.Ip,
			Domain:       d.Domain,
			Source:       d.Source,
			CreatedAt:    d.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:    d.UpdatedAt.Format("2006-01-02 15:04:05"),
			Port:         d.Port,
			IsValid:      int64(d.IsValid),
		})
		l, ok := resp.IpDomainList[d.Ip]
		if !ok {
			resp.IpDomainList[d.Ip] = &pb.CertAssetDomainArray{
				Values: []string{d.Domain},
			}
		} else {
			l.Values = append(l.Values, d.Domain)
			resp.IpDomainList[d.Ip] = l
		}
	}
	return nil
}
