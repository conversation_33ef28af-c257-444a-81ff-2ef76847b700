package cert_assets

import (
	"context"
	"database/sql/driver"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"

	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

func Init() {
	cfg.InitLoadCfg()
	log.Init()
	_ = es.GetInstance(cfg.LoadElastic())
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

func TestCertAssetsList(t *testing.T) {
	Init()

	tests := []struct {
		name string
		req  *pb.CertAssetListRequest
	}{
		{
			name: "normal case",
			req: &pb.CertAssetListRequest{
				UserId:  1,
				Page:    1,
				PerPage: 10,
			},
		},
		{
			name: "with props",
			req: &pb.CertAssetListRequest{
				UserId:           1,
				Keyword:          "test",
				WebsiteMessageId: 1,
				IssuerCn:         "test",
				IssuerOrg:        "test",
				SubjectCn:        "test",
				SubjectOrg:       "test",
				Domain:           "test",
				Port:             "443",
				Ip:               "**********",
				Version:          "v3",
			},
		},
		{
			name: "with IP keyword",
			req: &pb.CertAssetListRequest{
				UserId:  1,
				Keyword: "**********11",
			},
		},
		{
			name: "with time range",
			req: &pb.CertAssetListRequest{
				UserId:         1,
				CreatedAtRange: []string{time.Now().Add(-24 * time.Hour).Format("2006-01-02"), time.Now().Format("2006-01-02")},
				UpdatedAtRange: []string{time.Now().Add(-24 * time.Hour).Format("2006-01-02"), time.Now().Format("2006-01-02")},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := &pb.CertAssetListResponse{}
			err := CertAssetsList(context.Background(), tt.req, resp)
			// 在Mock环境中，主要验证函数不会panic
			t.Logf("CertAssetsList %s error (expected in mock env): %v", tt.name, err)
		})
	}

	// 测试数据库错误
	t.Run("database error", func(t *testing.T) {
		req := &pb.CertAssetListRequest{UserId: 1}
		resp := &pb.CertAssetListResponse{}
		err := CertAssetsList(context.Background(), req, resp)
		t.Logf("Database error test: %v", err)
	})

}

func TestCertAssetsExport(t *testing.T) {
	Init()

	tests := []struct {
		name string
		req  *pb.CertAssetExportRequest
	}{
		{
			name: "normal case",
			req:  &pb.CertAssetExportRequest{UserId: 1},
		},
		{
			name: "with ids",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Ids:    []uint64{1},
			},
		},
		{
			name: "with props",
			req: &pb.CertAssetExportRequest{
				UserId:           1,
				Keyword:          "test",
				WebsiteMessageId: 1,
				Ip:               "**********",
				IssuerCn:         "test",
				IssuerOrg:        "test",
				SubjectCn:        "test",
				SubjectOrg:       "test",
				Domain:           "test",
				Port:             "443",
				Version:          "v3",
			},
		},
		{
			name: "with time range",
			req: &pb.CertAssetExportRequest{
				UserId:         1,
				CreatedAtRange: []string{time.Now().Add(-24 * time.Hour).Format("2006-01-02"), time.Now().Format("2006-01-02")},
				UpdatedAtRange: []string{time.Now().Add(-24 * time.Hour).Format("2006-01-02"), time.Now().Format("2006-01-02")},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := &pb.FileExportResponse{}
			err := CertAssetsExport(context.Background(), tt.req, resp)
			// 在Mock环境中，主要验证函数不会panic
			t.Logf("CertAssetsExport %s error (expected in mock env): %v", tt.name, err)
		})
	}

	// 测试数据库错误
	t.Run("database error", func(t *testing.T) {
		req := &pb.CertAssetExportRequest{UserId: 1}
		resp := &pb.FileExportResponse{}
		err := CertAssetsExport(context.Background(), req, resp)
		t.Logf("Database error test: %v", err)
	})
}

func TestCertAssetsDelete(t *testing.T) {
	Init()

	tests := []struct {
		name string
		req  *pb.CertAssetExportRequest
	}{
		{
			name: "normal delete",
			req: &pb.CertAssetExportRequest{
				UserId: 1,
				Ids:    []uint64{1},
			},
		},
		{
			name: "delete with keyword",
			req: &pb.CertAssetExportRequest{
				UserId:  1,
				Keyword: "test",
			},
		},
		{
			name: "with props",
			req: &pb.CertAssetExportRequest{
				UserId:           1,
				Keyword:          "test",
				WebsiteMessageId: 1,
				IssuerCn:         "test",
				IssuerOrg:        "test",
				SubjectCn:        "test",
				SubjectOrg:       "test",
				Domain:           "test",
				Port:             "443",
				Ip:               "**********",
				CompanyName:      []string{"test company"},
				Version:          "v3",
			},
		},
		{
			name: "delete with time range",
			req: &pb.CertAssetExportRequest{
				UserId:         1,
				CreatedAtRange: []string{time.Now().Add(-24 * time.Hour).Format("2006-01-02"), time.Now().Format("2006-01-02")},
				UpdatedAtRange: []string{time.Now().Add(-24 * time.Hour).Format("2006-01-02"), time.Now().Format("2006-01-02")},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CertAssetsDelete(context.Background(), tt.req, &pb.Empty{})
			// 在Mock环境中，主要验证函数不会panic
			t.Logf("CertAssetsDelete %s error (expected in mock env): %v", tt.name, err)
		})
	}

	// 测试删除错误
	t.Run("delete error", func(t *testing.T) {
		req := &pb.CertAssetExportRequest{UserId: 0}
		err := CertAssetsDelete(context.Background(), req, &pb.Empty{})
		t.Logf("Delete error test: %v", err)
	})

}

func TestCertAssetDetail(t *testing.T) {
	Init()

	tests := []struct {
		name string
		req  *pb.CertAssetDetailRequest
	}{
		{
			name: "normal detail",
			req: &pb.CertAssetDetailRequest{
				UserId: 1,
				Id:     1,
			},
		},
		{
			name: "not found",
			req: &pb.CertAssetDetailRequest{
				UserId: 1,
				Id:     999,
			},
		},
		{
			name: "database error",
			req: &pb.CertAssetDetailRequest{
				UserId: 1,
				Id:     1,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := &pb.CertAssetItem{}
			err := CertAssetDetail(context.Background(), tt.req, resp)
			// 在Mock环境中，主要验证函数不会panic
			t.Logf("CertAssetDetail %s error (expected in mock env): %v", tt.name, err)

		})
	}
}

func WithArgsCount(count int) []driver.Value {
	args := make([]driver.Value, count)
	for i := 0; i < count; i++ {
		args[i] = sqlmock.AnyArg()
	}
	return args
}
