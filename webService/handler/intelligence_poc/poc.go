package intelligence_poc

import (
	"context"
	"fmt"
	"micro-service/initialize/es"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/scanService/handler/microkernel"
	"micro-service/webService/handler/user"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

var once sync.Once
var taskInfo *TaskInfo

type TaskInfo struct {
	Tasks sync.Map
}

func GetTaskInfo() *TaskInfo {
	if taskInfo == nil {
		once.Do(func() {
			taskInfo = &TaskInfo{}
		})
	}
	return taskInfo
}

func CheckTaskRunning(userId uint64) bool {
	if _, ok := GetTaskInfo().Tasks.Load(userId); ok {
		return true
	}
	return false
}

func AddTask(userId uint64) {
	GetTaskInfo().Tasks.Store(userId, 1)
}

func DeleteTask(userId uint64) {
	GetTaskInfo().Tasks.Delete(userId)
}

func GetTaskProcess(userId uint64) string {
	process, ok := GetTaskInfo().Tasks.Load(userId)
	if !ok {
		return "100"
	}
	return fmt.Sprintf("%.2f", process)
}

func MatchPoC(userId uint64, list []*intelligence.HotPoc, checkAll bool) {
	total := len(list)
	isAdmin, _ := user.IsUserAdmin(userId)
	assetUpdateList, relations := sync.Map{}, sync.Map{}
	if checkAll {
		GetTaskInfo().Tasks.Store(userId, 5.00)
		assetQuery := elastic.NewBoolQuery()
		if !isAdmin {
			assetQuery = assetQuery.Must(elastic.NewTermQuery("user_id", userId))
		}
		if err := foradar_assets.NewForadarAssetModel().UpdateByQuery(context.TODO(), assetQuery, map[string]any{"hot_poc_id": []uint64{}}); err != nil {
			log.Warn("MatchPoC userId:%d 重置全部POC,资产热点漏洞ID失败:%v", userId, err)
		}
		GetTaskInfo().Tasks.Store(userId, 10.00)
	}
	// 获取POC IDS
	ids := utils.ListDistinctNonZero(utils.ListColumn(list, func(t *intelligence.HotPoc) uint64 { return t.Id }))
	if len(ids) == 0 {
		return
	}
	// 关联信息表
	riClient := intelligence.NewRelatedIntelligence()
	// 删除关联信息
	idList := utils.ListSplit(ids, 1000)
	for y := range idList {
		_ = intelligence.NewUserHotPoc().DeleteBy(userId, idList[y])
	}
	// 关联情报表数据
	relatedIntelligenceList := make([]*intelligence.RelatedIntelligence, 0)
	for x, _ := range list {
		index := x + 1
		poc := list[x]
		// 获取POC 对应资产
		assets, err := GetUserPocAsset(userId, poc, isAdmin)
		// 检测指定POC时,重置用户该POC的资产数据
		if !checkAll {
			assetQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("user_id", userId), elastic.NewTermsQuery("hot_poc_id", poc.Id))
			if err := foradar_assets.NewForadarAssetModel().DeleteArrFieldKeyByQuery(context.TODO(), assetQuery, "hot_poc_id", poc.Id); err != nil {
				log.Warn("MatchPoC userId:%d 重置指定POC,资产热点漏洞ID失败:%v", userId, err)
			}
		}
		// 更新进度
		process := float32(index) / float32(total) * 100.0
		if cast.ToFloat32(GetTaskProcess(userId)) < process && cast.ToInt(process) != 100 {
			log.Infof(fmt.Sprintf("userID:%d,MatchPoC进度:%s", userId, cast.ToString(process)))
			GetTaskInfo().Tasks.Store(userId, process)
		}
		if err != nil {
			log.Warn("MatchPoC userId:%d,PocId:%d 获取资产失败:%v", userId, poc.Id, err)
			return
		}
		// 生成资产,热点漏洞ID,更新条件
		validData := false
		for _, asset := range assets {
			if maps, ok := assetUpdateList.Load(asset.ID); !ok {
				validData = true
				if !utils.ListContains(asset.HotPocId, poc.Id) {
					asset.HotPocId = append(asset.HotPocId, poc.Id)
				}
				// 一键检查时,清空所有POC
				if checkAll {
					asset.HotPocId = []uint64{poc.Id}
				}
				assetUpdateList.Store(asset.ID, map[string]any{"id": asset.ID, "hot_poc_id": asset.HotPocId})
			} else {
				canAdd := true
				hotIds := maps.(map[string]any)["hot_poc_id"].([]uint64)
				for vx := range hotIds {
					if cast.ToUint64(hotIds[vx]) == poc.Id {
						canAdd = false
						continue
					}
				}
				if canAdd {
					validData = true
					maps.(map[string]any)["hot_poc_id"] = append(maps.(map[string]any)["hot_poc_id"].([]uint64), poc.Id)
				}
			}
			if validData {
				foundTime := time.Now().Format("2006-01-02 15:04:05")
				query := make([]mysql.HandleFunc, 0)
				query = append(query, mysql.WithColumnValue("intelligence_id", poc.Id))
				query = append(query, mysql.WithColumnValue("risk_type", 1))
				query = append(query, mysql.WithColumnValue("user_id", userId))
				query = append(query, mysql.WithColumnValue("asset_ip", asset.Ip))
				switch asset.Port.(type) {
				case float64:
					query = append(query, mysql.WithColumnValue("asset_port", cast.ToString(asset.Port.(float64))))
				case string:
					query = append(query, mysql.WithColumnValue("asset_port", asset.Port.(string)))
				}
				existRi, err := riClient.FindBy(query...)
				if err != nil {
					if err != gorm.ErrRecordNotFound {
						log.Warn(fmt.Sprintf("MatchEvent: userId:%d,查询关联情报表数据失败,Error:%v!", userId, err))
					}
				} else {
					foundTime = existRi.FoundTime
				}
				// 生成关联情报表数据
				ri := &intelligence.RelatedIntelligence{
					IntelligenceID: poc.Id, UserID: userId, EnterpriseID: uint64(asset.CompanyID),
					AssetID: asset.ID, AssetIP: asset.Ip, AssetPort: fmt.Sprintf("%v", asset.Port),
					AssetProtocol: asset.Protocol, AssetURL: asset.Url, AssetTitle: fmt.Sprintf("%v", asset.Title), AssetStatus: cast.ToString(asset.Status.(float64)),
					RiskName: poc.Name, RiskType: "1",
					FoundTime: foundTime, UpdateTime: time.Now().Format("2006-01-02 15:04:05"),
				}
				if asset.ClueCompanyName != nil && len(asset.ClueCompanyName.([]interface{})) > 0 {
					if asset.ClueCompanyName.([]interface{})[0] != nil {
						switch asset.ClueCompanyName.([]interface{})[0].(type) {
						case string:
							ri.EnterpriseName = asset.ClueCompanyName.([]interface{})[0].(string)
						}
					}
				}
				ri.ServiceComponent = strings.Join(utils.ListColumn(asset.RuleTags, func(t foradar_assets.RuleTag) string { return t.CnProduct }), ",")
				relatedIntelligenceList = append(relatedIntelligenceList, ri)
			}
		}
		// 生成影响资产数,信息
		relations.Store(cast.ToString(userId)+"|"+cast.ToString(poc.Id), &intelligence.UserHotPoc{
			PocId: poc.Id, UserId: userId, RiskCount: len(assets),
		})
	}
	// 生成更新语句
	ups := make([]map[string]any, 0)
	assetUpdateList.Range(func(key, value any) bool {
		ups = append(ups, value.(map[string]any))
		return true
	})
	// 更新资产
	if err := foradar_assets.NewForadarAssetModel().UpdateWithMap(context.TODO(), ups); err != nil {
		log.Warn(fmt.Sprintf("MatchPoC: userId:%d,更新资产风险信息失败,Error:%v!", userId, err))
	}
	// 插入,影响资产数
	rels := make([]*intelligence.UserHotPoc, 0)
	relations.Range(func(key, value any) bool {
		rels = append(rels, value.(*intelligence.UserHotPoc))
		return true
	})
	if err := intelligence.NewUserHotPoc().CreateInBatches(rels); err != nil {
		log.Warn(fmt.Sprintf("MatchPoC: userId:%d,创建热点POC影响资产数量,Info:%+v,Error:%v", userId, rels, err.Error()))
	}
	// 删除旧的关联情报数据
	query := make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithColumnValue("user_id", userId))
	query = append(query, mysql.WithColumnValue("risk_type", 1))
	pocIdList := utils.ListColumn(list, func(t *intelligence.HotPoc) uint64 { return t.Id })
	query = append(query, mysql.WithValuesIn("intelligence_id", pocIdList))
	if err := riClient.DeleteBy(query...); err != nil {
		log.Warn(fmt.Sprintf("IntelligenceUserThreatMatch: userId:%d,删除关联情报表数据失败,Error:%v", userId, err.Error()))
	}
	// 插入关联情报表数据
	if err := intelligence.NewRelatedIntelligence().BatchCreate(relatedIntelligenceList); err != nil {
		log.Warn(fmt.Sprintf("MatchPoC: userId:%d,创建关联情报表数据失败,Error:%v", userId, err.Error()))
	}
	time.Sleep(2 * time.Second)
	GetTaskInfo().Tasks.Store(userId, 100)
	DeleteTask(userId)
	log.Info(fmt.Sprintf("MatchPoC: userId:%d,任务完成!", userId))
}

// GetUserPocAsset 获取PoC资产
func GetUserPocAsset(userId uint64, poc *intelligence.HotPoc, isAdmin bool) ([]foradar_assets.ForadarAsset, error) {
	assets := make([]foradar_assets.ForadarAsset, 0)
	log.Info(fmt.Sprintf("GetUserPocAsset: user_id:%d,poc_id:%d,poc_name:%s", userId, poc.Id, poc.Name))
	fofaQuery, err := microkernel.GetFOFAParse(utils.QBase64(poc.FofaQuery))
	if err != nil {
		return assets, err
	}
	var boolQuery *elastic.BoolQuery
	if !isAdmin {
		boolQuery = microkernel.GetInstance().NewQuery(fofaQuery.Query, userId)
	} else {
		boolQuery = microkernel.GetInstance().NewQuery(fofaQuery.Query)
	}
	query := es.GetInstance(cfg.LoadElastic()).Search(fofaQuery.Index...).Query(boolQuery)
	result, err := query.Aggregation("ip", elastic.NewTermsAggregation().Field("ip").Size(1000000)).Do(context.TODO())
	if err != nil {
		return assets, err
	}
	aggResult, found := result.Aggregations.Terms("ip")
	if !found {
		return assets, nil
	}
	log.Info(fmt.Sprintf("GetUserPocAsset: index:%s,count:%d", strings.Join(fofaQuery.Index, ","), len(aggResult.Buckets)))
	ipSplit := utils.ListSplit(utils.ListDistinctNonZero(utils.ListColumn(aggResult.Buckets, func(t *elastic.AggregationBucketKeyItem) string { return t.Key.(string) })), 5000)
	for x := range ipSplit {
		ips := utils.ListColumn(ipSplit[x], func(t string) any { return t })
		log.Info(fmt.Sprintf("GetUserPocAsset: index:%s,ips:%s", strings.Join(fofaQuery.Index, ","), strings.Join(ipSplit[x], ",")))
		assetQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("ip", ips...)).
			Must(elastic.NewTermsQuery("status", foradar_assets.StatusUploadAsset, foradar_assets.StatusConfirmAsset))
		if !isAdmin {
			assetQuery = assetQuery.Must(elastic.NewTermsQuery("user_id", userId))
		}
		l, err := foradar_assets.NewForadarAssetModel().ListAll(context.TODO(), assetQuery)
		if err != nil {
			return assets, err
		}
		log.Info(fmt.Sprintf("GetUserPocAsset: foradar_assets_ips:%s", strings.Join(utils.ListColumn(l, func(t foradar_assets.ForadarAsset) string { return t.Ip }), ",")))
		assets = append(assets, l...)
	}
	log.Info(fmt.Sprintf("GetUserPocAsset: foradar_assets_count:%d", len(assets)))
	return assets, nil
}
