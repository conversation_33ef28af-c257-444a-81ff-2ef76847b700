package handler

import (
	"context"
	stderrors "errors"
	"micro-service/middleware/elastic/fofaee_assets"
	fofaeethreats "micro-service/middleware/elastic/fofaee_threats"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/pkg/rule_engine"
	"micro-service/webService/handler/api_analyzer"
	"micro-service/webService/handler/asset_account/cert_assets"
	"micro-service/webService/handler/asset_account/login_assets"
	"micro-service/webService/handler/black_keyword"
	"micro-service/webService/handler/cloud_recommend"
	"micro-service/webService/handler/custom_poc"
	"micro-service/webService/handler/detect_assets"
	"micro-service/webService/handler/domain_assets"
	"micro-service/webService/handler/forbid"
	"micro-service/webService/handler/ignore_assets"
	"micro-service/webService/handler/ip_asset"
	"micro-service/webService/handler/scan_crontab"
	"micro-service/webService/handler/scan_task"
	"micro-service/webService/handler/table_assets"
	"micro-service/webService/handler/threaten_assets"
	"micro-service/webService/handler/unsure_assets"
	"micro-service/webService/handler/website_message"
	"net/http"
	"path"
	"path/filepath"
	"strings"
	"time"

	"micro-service/apiService/upload"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/api_counter"
	"micro-service/webService/handler/asset_account"
	account_domain "micro-service/webService/handler/asset_account/domain_assets"
	"micro-service/webService/handler/asset_account/system_detect"
	"micro-service/webService/handler/asset_analyze/asset_audit"
	"micro-service/webService/handler/asset_analyze/assets_detect"
	"micro-service/webService/handler/asset_analyze/url_detect"
	"micro-service/webService/handler/asset_overview"
	dat "micro-service/webService/handler/detect_asset_task"
	"micro-service/webService/handler/dlp"

	"micro-service/webService/handler/notice"
	phc "micro-service/webService/handler/phishing_fake"
	"micro-service/webService/handler/port_group"
	"micro-service/webService/handler/public"
	"micro-service/webService/handler/task_overview"
	"micro-service/webService/handler/user"
	"micro-service/webService/handler/user_tokens"
	pb "micro-service/webService/proto"

	"github.com/olivere/elastic"
	errorsr "github.com/pkg/errors"
	"go-micro.dev/v4/errors"
	"gorm.io/gorm"
)

type Web struct{}

func (w *Web) ScanRecommend(ctx context.Context, request *pb.ScanRecommendRequest, response *pb.ScanRecommendResponse) error {
	err := detect_assets.ScanRecommend(ctx, request, response)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) AssetAuditFileCheck(ctx context.Context, req *pb.AssetAuditTaskCreateRequest, rsp *pb.Empty) error {
	err := asset_audit.AuditFileCheck(req.FilePath)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产核查]校验用户上传文件%s", err.Error())
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// AssetAuditTaskCreate 新建核查任务
func (w *Web) AssetAuditTaskCreate(ctx context.Context, req *pb.AssetAuditTaskCreateRequest, rsp *pb.AssetAuditTaskCreateResponse) error {
	err := asset_audit.AuditTaskCreate(ctx, req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// AssetAuditTaskList 核查任务列表
func (w *Web) AssetAuditTaskList(ctx context.Context, req *pb.AssetAuditTaskListRequest, rsp *pb.AssetAuditTaskListResponse) error {
	err := asset_audit.AuditTaskList(req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	rsp.CurrentPage = req.Page
	rsp.PerPage = req.Size
	return nil
}

// AssetAuditTaskDelete 核查任务删除
func (w *Web) AssetAuditTaskDelete(ctx context.Context, req *pb.AssetAuditTaskDeleteRequest, rsp *pb.Empty) error {
	req.TaskIds = utils.ListDistinct(req.TaskIds)
	err := asset_audit.AuditTasksDelete(req)

	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	return nil
}

// 开始资产检测
func (w *Web) StartAssetsStatusDetect(ctx context.Context, in *pb.StartAssetsStatusDetectRequest, out *pb.StartAssetsStatusDetectResponse) error {
	r, ips, err := assets_detect.StartAssetsStatusDetect(in.CurrentUserId, in.CompanyUserId, in.File)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产状态检测] Request: web.StartAssetsStatusDetect failed: %v", err)
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	out.TaskId = r
	out.Ips = ips
	return nil
}

// 获取资产详情
func (w *Web) GetAssetsStatusDetectInfo(ctx context.Context, in *pb.GetAssetsStatusDetectInfoRequest, out *pb.GetAssetsStatusDetectInfoResponse) error {
	if in.OnlineStatus < 1 || in.OnlineStatus > 2 {
		return errors.New(pb.ServiceName, "参数错误", 400)
	}

	r, err := assets_detect.GetAssetsStatusDetectInfo(in)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产状态检测] Request: web.GetAssetsStatusDetectInfo failed: %v", err)
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	out.Total = r.Total
	out.CurrentPage = r.CurrentPage
	out.PerPage = r.PerPage
	out.Items = r.Items
	return nil
}

// GetAssetsStatusDetectList 获取任务列表
func (w *Web) GetAssetsStatusDetectList(ctx context.Context, in *pb.GetAssetsStatusDetectListRequest, out *pb.GetAssetsStatusDetectListResponse) error {
	r, err := assets_detect.GetAssetsStatusDetectList(in)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产状态检测] Request: web.GetAssetsStatusDetectList failed: %v", err)
		return errors.New(pb.ServiceName, err.Error(), 400)
	}

	out.Total = r.Total
	out.CurrentPage = r.CurrentPage
	out.PerPage = r.PerPage
	out.Items = r.Items
	return nil
}

// DelAssetsStatusDetectTask 删除检测任务
func (w *Web) DelAssetsStatusDetectTask(ctx context.Context, in *pb.DelAssetsStatusDetectTaskRequest, out *pb.Empty) error {
	err := assets_detect.DelAssetsStatusDetectTask(in)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产状态检测] Request: web.DelAssetsStatusDetectTask failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}

	return nil
}

// AssetAuditTaskFinished 更新任务进度为完成
func (w *Web) AssetAuditTaskFinished(ctx context.Context, req *pb.AssetAuditSyncAssetsRequest, rsp *pb.Empty) error {
	err := asset_audit.TaskUpdateFinished(ctx, req.UserId, req.TaskId)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// AssetAuditResultDownload 下载核查任务结果
func (w *Web) AssetAuditResultDownload(ctx context.Context, req *pb.AssetAuditResultDownloadRequest, rsp *pb.AssetAuditResultDownloadResponse) error {
	f, err := asset_audit.DownloadAuditResult(ctx, uint(req.UserId), uint(req.TaskId))
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}

	item := utils.DownloadFile{
		Url:  f,                // eg: app/public/config.json
		Name: filepath.Base(f), // eg: config.json
	}
	s, _ := utils.LaravelEncrypt(&item)

	rsp.FilePath = filepath.Join(storage.GetDownloadPrefix(), s+path.Ext(f))
	return nil
}

// AssetAuditResultList 获取核查任务结果
func (w *Web) AssetAuditResultList(ctx context.Context, req *pb.AssetAuditTaskResultListRequest, rsp *pb.AssetAuditTaskResultListResponse) error {
	err := asset_audit.AssetAuditResultList(ctx, req, rsp)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.NotFound(pb.ServiceName, "查找记录不存在")
		}
		return errors.BadRequest(pb.ServiceName, err.Error())
	}

	rsp.TaskId = req.TaskId
	rsp.CurrentPage = req.Page
	rsp.PerPage = req.Size
	return nil
}

// AssetAuditTaskInfo 资产核查任务详情
func (w *Web) AssetAuditTaskInfo(ctx context.Context, req *pb.AssetAuditTaskInfoRequest, rsp *pb.AssetAuditTaskInfoResponse) error {
	log.WithContextInfof(ctx, "[资产核查] Received Web.AssetAuditTaskInfo request: %s", utils.AnyToStr(req))
	err := asset_audit.AssetAuditTaskInfo(req, rsp)
	if err != nil {
		// if err == gorm.ErrRecordNotFound {
		// 	return errors.NotFound(pb.ServiceName, "任务记录未找到")
		// }
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// AssetAuditSyncAssets 核查任务结果同步台账
func (w *Web) AssetAuditSyncAssets(ctx context.Context, req *pb.AssetAuditSyncAssetsRequest, rsp *pb.AssetAuditSyncAssetsResponse) error {
	err := asset_audit.AuditResultSyncAssets(ctx, req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) HasLoggedIn(ctx context.Context, req *pb.HasLoggedInRequest, rsp *pb.HasLoggedInResponse) error {
	has, err := notice.HasLoggedIn(req.UserId)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	rsp.Has = has
	return nil
}

func (w *Web) PublicNoticeAdd(ctx context.Context, req *pb.PublicNoticeAddRequest, rsp *pb.PublicNoticeAddResponse) error {
	id, err := notice.PublicNoticeAdd(req)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	rsp.Id = id.Id
	return nil
}

func (w *Web) PublicNoticeDel(ctx context.Context, in *pb.PublicNoticeDelRequest, rsp *pb.Empty) error {
	err := notice.PublicNoticeDel(in)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PublicNoticeSave(ctx context.Context, req *pb.PublicNoticeSaveRequest, rsp *pb.Empty) error {
	err := notice.PublicNoticeSave(req)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PublicNoticeList(ctx context.Context, req *pb.PublicNoticeListRequest, rsp *pb.PublicNoticeListResponse) error {
	resp, err := notice.PublicNoticeList(req.CurrentPage, req.PerPage)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	rsp.Items = resp.Items
	rsp.Total = resp.Total
	rsp.CurrentPage = resp.CurrentPage
	rsp.PerPage = resp.PerPage
	return nil
}

func (w *Web) FindLatestNotice(ctx context.Context, req *pb.Empty, rsp *pb.FindLatestNoticeResponse) error {
	resp, err := notice.FindLatestNotice()
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	rsp.Notice = resp.Notice
	rsp.UpAtStart = resp.UpAtStart
	rsp.UpAtEnd = resp.UpAtEnd

	return nil
}

func (w *Web) TaskOverviewCountByCategory(ctx context.Context, req *pb.TaskOverviewCountByCategoryRequest, rsp *pb.TaskOverviewCountByCategoryResponse) error {
	err := task_overview.TaskOverviewCountByCategory(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}

	return nil
}

// TaskOverviewCount 根据任务状态统计
func (w *Web) TaskOverviewCount(ctx context.Context, req *pb.TaskOverviewCountRequest, rsp *pb.TaskOverviewCountResponse) error {
	err := task_overview.TaskOverviewCount(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}

	return nil
}

// IpAssetIpProfile IP资产-IP画像
func (w *Web) IpAssetIpProfile(ctx context.Context, req *pb.IpAssetProfileRequest, rsp *pb.IpAssetProfileResponse) error {
	r, err := asset_account.IPProfile(int(req.UserId), req.Ip)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}

	rsp.Ip = r.Ip
	rsp.Items = r.Items
	return nil
}

func (w *Web) AssetsOverviewRuleCount(ctx context.Context, req *pb.AssetsOverviewRuleCountRequest, rsp *pb.AssetsOverviewRuleCountResponse) error {
	log.WithContextInfof(ctx, "[资产概览] Received Web.AssetsOverviewRuleCount request: %s", utils.AnyToStr(req))
	var err error
	rsp.Items, err = asset_overview.RuleCount(ctx, req.UserId)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) AssetsOverviewDigitalAssets(ctx context.Context, req *pb.AssetsOverviewDigitalAssetsRequest, rsp *pb.AssetsOverviewDigitalAssetsResponse) error {
	log.WithContextInfof(ctx, "[资产概览] Received Web.AssetsOverviewDigitalAssets request: %s", utils.AnyToStr(req))
	resp, err := asset_overview.DigitalAssets(req.UserId)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	rsp.Items = resp
	return nil
}

func (w *Web) AssetsOverviewComparedLastMonth(ctx context.Context, req *pb.ComparedLastMonthRequest, rsp *pb.ComparedLastMonthResponse) error {
	resp, err := asset_overview.ComparedLastMonth(int(req.UserId))
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	rsp.Ledger = resp.Ledger
	rsp.Digital = resp.Digital
	rsp.Doubt = resp.Doubt
	rsp.Threaten = resp.Threaten
	rsp.Login = resp.Login
	rsp.Domain = resp.Domain
	rsp.Cert = resp.Cert
	return nil
}

func (w *Web) AssetsOverviewAssetsDynamic(ctx context.Context, req *pb.AssetsOverviewAssetsDynamicRequest, rsp *pb.AssetsOverviewAssetsDynamicResponse) error {
	Items, err := asset_overview.AssetsDynamic(req.UserId, req.CreatedAtRange, req.HasThreatData)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	rsp.Items = Items

	return nil
}

// AssetsOverviewAssetsDynamicCount 资产动态-统计
func (w *Web) AssetsOverviewAssetsDynamicCount(ctx context.Context, req *pb.AssetsOverviewAssetsDynamicCountRequest, rsp *pb.AssetsOverviewAssetsDynamicCountResponse) error {
	rsp.Items = make(map[string]int64)

	// 查询 es 资产
	findQuery := make([]elastic.Query, 0)
	findQuery = append(findQuery, elastic.NewTermsQuery("status", 1, 4))
	findQuery = append(findQuery, elastic.NewTermQuery("user_id", req.UserId))
	req.CreatedAtRange = utils.FormattingTime(req.CreatedAtRange)
	if len(req.CreatedAtRange) == 1 {
		findQuery = append(findQuery, elastic.NewRangeQuery("created_at").Gte(req.CreatedAtRange))
	}
	if len(req.CreatedAtRange) == 2 {
		findQuery = append(findQuery, elastic.NewRangeQuery("created_at").Gte(req.CreatedAtRange[0]).Lte(req.CreatedAtRange[1]))
	}
	boolQuery := elastic.NewBoolQuery().Must(findQuery...)
	// es.PrintEsQuery(boolQuery)
	assetCount, err := fofaee_assets.NewFofaeeAssetsModel().Count(context.TODO(), boolQuery)
	if err != nil {
		log.WithContextWarn(ctx, "[资产动态变更] 查询资产变更数量失败: %v", err)
		rsp.Items["asset_changes"] = 0
	} else {
		rsp.Items["asset_changes"] = assetCount
	}

	findQuery = make([]elastic.Query, 0)
	findQuery = append(findQuery, elastic.NewTermsQuery("state", 1, 4))
	findQuery = append(findQuery, elastic.NewTermQuery("user_id", req.UserId))
	if len(req.CreatedAtRange) == 1 {
		findQuery = append(findQuery, elastic.NewRangeQuery("created_at").Gte(req.CreatedAtRange))
	}
	if len(req.CreatedAtRange) == 2 {
		findQuery = append(findQuery, elastic.NewRangeQuery("created_at").Gte(req.CreatedAtRange[0]).Lte(req.CreatedAtRange[1]))
	}
	boolQuery = elastic.NewBoolQuery().Must(findQuery...).MustNot(elastic.NewTermQuery("is_del", 1))
	// es.PrintEsQuery(boolQuery)
	assetCount, err = fofaeethreats.NewFofaeeThreatsModel().Count(context.TODO(), boolQuery)
	if err != nil {
		log.WithContextWarn(ctx, "[资产动态变更] 查询漏洞变更数量失败: %v", err)
		rsp.Items["vulnerability_changes"] = 0
	} else {
		rsp.Items["vulnerability_changes"] = assetCount
	}
	return nil
}

// IpAssetVulns IP资产漏洞信息
func (w *Web) IpAssetVulns(ctx context.Context, req *pb.IpAssetIpVulnsRequest, rsp *pb.IpAssetIpVulnsResponse) error {
	err := asset_account.IpAssetVulns(ctx, req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}

	return nil
}

// SystemDetectUpdate 业务系统-更新记录
func (w *Web) SystemDetectUpdate(ctx context.Context, req *pb.SystemDetectUpdateRequest, rsp *pb.Empty) error {
	err := system_detect.UpdateAny(req)
	if err != nil {
		log.WithContextErrorf(ctx, "[业务系统]更新记录: %w, req: %+v", err, req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// SystemDetectDelete 业务系统-删除记录
func (w *Web) SystemDetectDelete(ctx context.Context, req *pb.SystemDetectListRequest, rsp *pb.Empty) error {
	err := system_detect.Delete(req)
	if err != nil {
		log.WithContextErrorf(ctx, "[业务系统]删除记录: %w, req: %+v", err, req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// SystemDetectIgnoreConfirm 业务系统-确认/忽略
func (w *Web) SystemDetectIgnoreConfirm(ctx context.Context, req *pb.SystemDetectListRequest, rsp *pb.Empty) error {
	err := system_detect.UpdateStatus(req)
	if err != nil {
		log.WithContextErrorf(ctx, "[业务系统]更新或者忽略: %w, req: %+v", err, req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// SystemDetectCreate 业务系统-建立记录
func (w *Web) SystemDetectCreate(ctx context.Context, req *pb.SystemDetectCreateRequest, rsp *pb.Empty) error {
	item, err := upload.UploadDecrypt(req.FilePath)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	req.FilePath = item.Url
	err = system_detect.Create(ctx, req)
	if err != nil {
		log.WithContextErrorf(ctx, "[业务系统]创建记录: %w, req: %+v", err, req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// SystemDetectDownload 业务系统-下载数据
func (w *Web) SystemDetectDownload(ctx context.Context, req *pb.SystemDetectListRequest, rsp *pb.SystemDetectDownloadResponse) error {
	s, err := system_detect.Download(ctx, req)
	if err != nil {
		log.WithContextErrorf(ctx, "[业务系统]下载数据: %w, req: %+v", err, req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	rsp.Path = s
	return nil
}

// SystemDetectList 业务系统-获取列表结果
func (w *Web) SystemDetectList(ctx context.Context, req *pb.SystemDetectListRequest, rsp *pb.SystemDetectListResponse) error {
	err := system_detect.List(req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[业务系统]获取结果列表失败: %w, req: %+v", err, req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// SystemDetectIpAssociation 业务系统-ip关联
func (w *Web) SystemDetectIpAssociation(ctx context.Context, req *pb.SystemDetectListRequest, rsp *pb.Empty) error {
	err := system_detect.IpAssociation(ctx, req)
	if err != nil {
		log.WithContextErrorf(ctx, "[业务系统]ip关联: %w, req: %+v", err, req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// SystemDetectSyncAssets 同步台账
func (w *Web) SystemDetectSyncAssets(ctx context.Context, req *pb.SystemDetectListRequest, rsp *pb.Empty) error {
	err := system_detect.SyncAssets(ctx, req)
	if err != nil {
		log.WithContextErrorf(ctx, "[业务系统]同步台账: %w, req: %+v", err, req)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// SystemDetectProgress 进度
func (w *Web) SystemDetectProgress(ctx context.Context, req *pb.SystemDetectProgressRequest, rsp *pb.SystemDetectProgressResponse) error {
	err := system_detect.Progress(ctx, req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// UserOnlineList 获取在线用户列表
func (w *Web) UserOnlineList(ctx context.Context, req *pb.UserTokenListRequest, rsp *pb.UserTokenListResponse) error {
	resp, err := user_tokens.UserOnlineList(req.CurrentPage, req.PerPage)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	rsp.Items = resp.Items
	rsp.Total = resp.Total
	rsp.CurrentPage = resp.CurrentPage
	rsp.PerPage = resp.PerPage
	return nil
}

func (w *Web) DlpGitHubTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	err := dlp.GithubSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: GitHub search by task: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) DlpGitHubTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	err := dlp.GithubTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get GitHub task info: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) DlpGitHubTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubCodeResponse) error {
	l, err := dlp.GithubTaskResult(req.TaskId)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get GitHub task result: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	rsp.Items = l
	return nil
}

func (w *Web) DlpDocinTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	err := dlp.DocinSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: GitHub search by task: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpDocinTaskInfo 豆丁任务详情
func (w *Web) DlpDocinTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	err := dlp.DocinTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get docin task info: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpDocinTaskResult 豆丁任务结果
func (w *Web) DlpDocinTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.DocinResultResponse) error {
	l, err := dlp.DocinTaskResult(req.TaskId)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get docin task result: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	rsp.Items = l
	return nil
}

// Dlp56WangpanTaskCreate 56网盘-创建任务
func (w *Web) Dlp56WangpanTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	err := dlp.Wangpan56SearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: 56wangpan search by task: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// Dlp56WangpanTaskInfo 56网盘-任务详情
func (w *Web) Dlp56WangpanTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	err := dlp.Wangpan56TaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get 56wangpan task info: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// Dlp56WangpanTaskResult 56网盘-任务结果
func (w *Web) Dlp56WangpanTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.Wangpan56ResultResponse) error {
	err := dlp.Wangpan56TaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get 56wangpan task result: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpPanSosoTaskCreate pansoso-创建任务
func (w *Web) DlpPanSosoTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	err := dlp.PansosoSearchByTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: PanSoso search by task: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpPanSosoTaskInfo PanSoso-任务详情
func (w *Web) DlpPanSosoTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	err := dlp.PansosoTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get PanSoso task info: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpPanSosoTaskResult PanSoso-任务结果
func (w *Web) DlpPanSosoTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.Wangpan56ResultResponse) error {
	err := dlp.PansosoTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get PanSoso task result: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) PortIndex(ctx context.Context, req *pb.PortIndexRequest, rsp *pb.PortIndexResponse) error {
	err := port_group.PortIndex(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortList(ctx context.Context, req *pb.PortListRequest, rsp *pb.PortListResponse) error {
	err := port_group.PortList(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortAdd(ctx context.Context, req *pb.PortAddRequest, rsp *pb.PortAddResponse) error {
	err := port_group.PortAdd(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortEdit(ctx context.Context, req *pb.PortEditRequest, rsp *pb.PortEditResponse) error {
	err := port_group.PortEdit(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortDel(ctx context.Context, req *pb.PortDelRequest, rsp *pb.PortDelResponse) error {
	err := port_group.PortDel(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortUpdateStatus(ctx context.Context, req *pb.PortUpdateStatusRequest, rsp *pb.PortUpdateStatusResponse) error {
	err := port_group.PortUpdateStatus(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortProtocolIndex(ctx context.Context, req *pb.PortProtocolIndexRequest, rsp *pb.PortProtocolIndexResponse) error {
	err := port_group.PortProtocolIndex(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortDetail(ctx context.Context, req *pb.PortDetailRequest, rsp *pb.PortDetailResponse) error {
	err := port_group.PortDetail(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortGroupIndex(ctx context.Context, req *pb.PortGroupIndexRequest, rsp *pb.PortGroupIndexResponse) error {
	err := port_group.PortGroupIndex(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortGroupAdd(ctx context.Context, req *pb.PortGroupAddRequest, rsp *pb.PortGroupAddResponse) error {
	err := port_group.PortGroupAdd(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortGroupDel(ctx context.Context, req *pb.PortGroupDelRequest, rsp *pb.PortGroupDelResponse) error {
	err := port_group.PortGroupDel(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

// PortGroupList 获取端口分组列表
func (w *Web) PortGroupList(ctx context.Context, req *pb.PortGroupListRequest, rsp *pb.PortGroupListResponse) error {
	log.WithContextInfof(ctx, "Received Web.PortGroupList request: %v", req)
	err := port_group.PortGroupList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[端口管理] 获取端口分组列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) PortGroupEdit(ctx context.Context, req *pb.PortGroupEditRequest, rsp *pb.PortGroupEditResponse) error {
	err := port_group.PortGroupEdit(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (w *Web) PortGroupDetail(ctx context.Context, req *pb.PortGroupDetailRequest, rsp *pb.PortGroupDetailResponse) error {
	err := port_group.PortGroupDetail(ctx, req, rsp)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

// DlpGiteeTaskCreate 数据泄露Gitee-创建任务
// func (w *Web) DlpGiteeTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
// 	err := dlp.GiteeSearchByTask(ctx, req, rsp)
// 	if err != nil {
// 		log.WithContextErrorf(ctx, "[Web]dlp: GitHub search by task: %v", err)
// 		return errors.BadRequest(pb.ServiceName, err.Error())
// 	}
// 	return nil
// }

// DlpGiteeTaskInfo 数据泄露Gitee-任务详情
// func (w *Web) DlpGiteeTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
// 	err := dlp.GiteeTaskInfo(req.TaskId, rsp)
// 	if err != nil {
// 		log.WithContextErrorf(ctx, "[Web]dlp: Get GitHub task info: %v", err)
// 		return errors.BadRequest(pb.ServiceName, err.Error())
// 	}
// 	return nil
// }

// DlpGiteeTaskResult 数据泄露Gitee-任务结果
// func (w *Web) DlpGiteeTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubCodeResponse) error {
// 	l, err := dlp.GiteeTaskResult(req.TaskId)
// 	if err != nil {
// 		log.WithContextErrorf(ctx, "[Web]dlp: Get dlp GitHub task result: %v", err)
// 		return errors.BadRequest(pb.ServiceName, err.Error())
// 	}
// 	rsp.Items = l
// 	return nil
// }

// DlpBaiduLibraryTaskCreate 数据泄露: 百度文库-创建任务
func (w *Web) DlpBaiduLibraryTaskCreate(ctx context.Context, req, rsp *pb.GitHubCodeRequest) error {
	err := dlp.BaiduLibrarySearch(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: BaiduLibrary search by task: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpBaiduLibraryTaskInfo 数据泄露: 百度文库-任务详情
func (w *Web) DlpBaiduLibraryTaskInfo(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.GitHubTaskInfoResponse) error {
	err := dlp.BaiduLibraryTaskInfo(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get BaiduLibrary task info: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DlpBaiduLibraryTaskResult 数据泄露: 百度文库-任务结果
func (w *Web) DlpBaiduLibraryTaskResult(ctx context.Context, req *pb.GitHubCodeRequest, rsp *pb.BaiduLibraryResponse) error {
	err := dlp.BaiduLibraryTaskResult(req.TaskId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]dlp: Get dlp BaiduLibrary task result: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitTaskList 钓鱼仿冒-任务列表
func (w *Web) CounterfeitTaskList(ctx context.Context, req *pb.CounterfeitTaskListRequest, rsp *pb.CounterfeitTaskListResponse) error {
	if err := phc.TaskList(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Web] Counterfeit: get task list failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitTaskDelete 钓鱼仿冒-任务删除
func (w *Web) CounterfeitTaskDelete(ctx context.Context, req *pb.CounterfeitTaskDelRequest, rsp *pb.Empty) error {
	if err := phc.TaskDelete(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[Web] Counterfeit: task delete failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitAssetsFind 钓鱼仿冒-仿冒资产发现
func (w *Web) CounterfeitAssetsFind(ctx context.Context, req *pb.CounterfeitAssetFindRequest, rsp *pb.Empty) error {
	if err := phc.CounterfeitAssetsFind(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[Web] Find counterfeit assets failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitAssetList 钓鱼仿冒-仿冒资产结果列表
func (w *Web) CounterfeitAssetList(ctx context.Context, req *pb.CounterfeitAssetListRequest, rsp *pb.CounterfeitAssetListResponse) error {
	err := phc.CounterfeitAssetList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web] Get counterfeit task result list: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitAssetsAgg 仿冒资产聚合统计
func (w *Web) CounterfeitAssetsAgg(ctx context.Context, req *pb.CounterfeitAssetsAggRequest, rsp *pb.CounterfeitAssetsAggResponse) error {
	if err := phc.CounterfeitAssetsAgg(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Web]Get counterfeit assets agg list: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) CounterfeitAssetsImport(ctx context.Context, req *pb.CounterfeitAssetsImportRequest, _ *pb.Empty) error {
	filePath, err := upload.UploadDecrypt(req.FilePath)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]钓鱼仿冒-解密上传文件路径出错: %v", err)
		return errors.BadRequest(pb.ServiceName, "上传文件无效, 请检查文件后重新上传")
	}
	req.FilePath = path.Join(storage.GetRootPath(), filePath.Url)
	if err = phc.CounterfeitAssetsImport(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[Web]Import fake assets failed: %v, file_path: %s", err, req.FilePath)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitAssetsDetect 钓鱼仿冒-地址在线探测
func (w *Web) CounterfeitAssetsDetect(ctx context.Context, req *pb.CounterfeitAssetDetectRequest, rsp *pb.Empty) error {
	if err := phc.CounterfeitAssetsDetect(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[Web]Counterfeit assets detect failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitAssetsRetryScreenshot 钓鱼仿冒-截图重试
func (w *Web) CounterfeitAssetsRetryScreenshot(ctx context.Context, req *pb.CounterfeitAssetListRequest, rsp *pb.Empty) error {
	if err := phc.CounterfeitAssetsRetryScreenshot(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[Web]Counterfeit assets retry screenshot failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitAssetsDeepDetect 钓鱼仿冒-入账&深度探测
func (w *Web) CounterfeitAssetsDeepDetect(ctx context.Context, req *pb.CounterfeitAssetListRequest, rsp *pb.CounterfeitAssetsSyncResponse) error {
	if err := phc.CounterfeitAssetsDeepDetect(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Web]Counterfeit: result sync to threat asset failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitAssetsCount 钓鱼仿冒-资产统计
func (w *Web) CounterfeitAssetsCount(ctx context.Context, req *pb.CounterfeitAssetDetectRequest, rsp *pb.CounterfeitAssetsCountResponse) error {
	if err := phc.CounterfeitAssetsCount(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Web]Get counterfeit assets count failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitAssetsSync 钓鱼仿冒-数据入账威胁资产
func (w *Web) CounterfeitAssetsSync(ctx context.Context, req *pb.CounterfeitAssetListRequest, rsp *pb.CounterfeitAssetsSyncResponse) error {
	if err := phc.SyncToThreatAsset(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Web]Counterfeit: result sync to threat asset failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CounterfeitAssetsDownload 钓鱼仿冒-数据下载
func (w *Web) CounterfeitAssetsDownload(ctx context.Context, req *pb.CounterfeitAssetDetectRequest, rsp *pb.AssetAuditResultDownloadResponse) error {
	var err error
	rsp.FilePath, err = phc.CounterfeitAssetsDownload(req)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web]Counterfeit: assets download failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// 开始url检测
func (w *Web) StartUrlStatusDetect(ctx context.Context, in *pb.StartUrlStatusDetectRequest, out *pb.StartUrlStatusDetectResponse) error {
	r, ips, err := url_detect.StartUlrStatusDetect(in.CurrentUserId, in.CompanyUserId, in.File)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	out.TaskId = r
	out.Ips = ips
	return nil
}

// 获取url详情
func (w *Web) GetUrlStatusDetectInfo(ctx context.Context, in *pb.GetUrlStatusDetectInfoRequest, out *pb.GetUrlStatusDetectInfoResponse) error {
	if in.OnlineStatus < 1 || in.OnlineStatus > 2 {
		return errors.New(pb.ServiceName, "参数错误", 400)
	}

	r, err := url_detect.GetUrlStatusDetectInfo(in)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	out.Total = r.Total
	out.OnlineNum = r.OnlineNum
	out.OfflineNum = r.OfflineNum
	out.CurrentPage = r.CurrentPage
	out.PerPage = r.PerPage
	out.Items = r.Items
	return nil
}

// 获取任务列表
func (w *Web) GetUrlStatusDetectList(ctx context.Context, in *pb.GetUrlStatusDetectListRequest, out *pb.GetUrlStatusDetectListResponse) error {
	r, err := url_detect.GetUrlStatusDetectList(in)
	if err != nil {
		return errors.New(pb.ServiceName, err.Error(), 400)
	}
	out.Total = r.Total
	out.CurrentPage = r.CurrentPage
	out.PerPage = r.PerPage
	out.Items = r.Items

	return nil
}

// DelUrlStatusDetectTask 删除url检测任务
func (w *Web) DelUrlStatusDetectTask(ctx context.Context, in *pb.DelUrlStatusDetectTaskRequest, out *pb.Empty) error {
	err := url_detect.DelUrlStatusDetectTask(in)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}

	return nil
}

func (w *Web) DlpTaskResultList(ctx context.Context, req *pb.DlpTaskResultListRequest, rsp *pb.DlpTaskResultListResponse) error {
	err := dlp.TaskResultList(req, rsp)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) DlpTaskResultUpdate(ctx context.Context, req *pb.DlpResultUpdateRequest, rsp *pb.Empty) error {
	err := dlp.ResultUpdate(req)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) DlpTaskAgg(ctx context.Context, req *pb.DlpTaskAggRequest, rsp *pb.DlpTaskAggResponse) error {
	err := dlp.TaskKeywordAgg(req.SourceType, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Data Leak] Get source_type-> keyword list failure: %v", req.SourceType, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) DomainAssetUpdateByCron(ctx context.Context, req *pb.DomainAssetsUpdateByCronRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[Domain-Asset] Received Web.DomainAssetUpdateByCron request: %s", utils.AnyToStr(req))
	err := account_domain.UpdateByCron(ctx, req)
	if err != nil {
		log.WithContextErrorf(ctx, "[Domain-Asset] Update domain assets by cron failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) DomainAssetCronInfo(ctx context.Context, req *pb.DomainAssetsUpdateByCronRequest, rsp *pb.DomainAssetsCronInfoResponse) error {
	err := account_domain.CronInfo(req.UserId, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web] Domain Assets: Get user: %d cron info failed: %v", req.UserId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) DomainAssetDomainFilter(ctx context.Context, req *pb.DomainAssetsDomainFilterRequest, rsp *pb.DomainAssetsDomainFilterResponse) error {
	if err := account_domain.DomainAssetsDomainFilter(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Web] Domain Assets: Get user: %d domains failed: %v", req.UserId, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) ApiRequestCountList(ctx context.Context, req *pb.ApiRequestCountListRequest, rsp *pb.ApiRequestCountListResponse) error {
	err := api_counter.RequestCountList(req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web] Api Request Counter: Get api request counter list failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) DetectAssetCluesUpdate(ctx context.Context, req *pb.DetectAssetCluesUpdateRequest, _ *pb.Empty) error {
	if err := dat.CluesUpdate(req); err != nil {
		log.WithContextErrorf(ctx, "[Web] Detect Asset Clues: Update clues failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) DetectAssetTaskCreate(ctx context.Context, req *pb.DetectAssetTaskCreateRequest, rsp *pb.DetectAssetTaskCreateResponse) error {
	log.WithContextInfof(ctx, "Received Web.DetectAssetTaskCreate request: %s", utils.AnyToStr(req))
	if err := dat.CreateTask(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[DetectAssetTask] Create new detect asset task failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ApplyApiSave api申请记录入库
func (w *Web) ApplyApiSave(ctx context.Context, req *pb.ApplyApiRequest, _ *pb.Empty) error {
	err := public.ApplyAdd(req)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web] ApplyApiSave: add Email->%s asset result failed: %v", req.Email, err)
		return errors.New(pb.ServiceName, err.Error(), http.StatusBadRequest)
	}
	return nil
}

func (*Web) ManageApplyAudit(ctx context.Context, req *pb.ApplyAuditRequest, _ *pb.Empty) error {
	if err := public.ApplyAudit(ctx, req); err != nil {
		log.WithContextErrorf(ctx, "[Web] Apply audit process failed, taskId->%d, caused by: %v", req.Id, err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) ManageApplyList(ctx context.Context, req *pb.ApplyListRequest, rsp *pb.ApplyListResponse) error {
	if err := public.ApplyList(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[Web] Get apply list failed: ", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// UserList 用户列表
func (*Web) UserList(ctx context.Context, req *pb.UserListRequest, rsp *pb.UserListResponse) error {
	if err := user.List(ctx, req, rsp); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (*Web) SetFirstTaskNotice(ctx context.Context, req *pb.SetFirstTaskNoticeRequest, rsp *pb.SetFirstTaskNoticeResponse) error {
	if err := user.SetFirstTaskNotice(ctx, req, rsp); err != nil {
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DetectAssetTaskReport 单位测绘-测绘简报详情
func (*Web) DetectAssetTaskReport(ctx context.Context, req *pb.DetectAssetTaskReportRequest, rsp *pb.DetectAssetTaskReportResponse) error {
	log.WithContextInfof(ctx, "[单位测绘-任务简报] Received Web.DetectAssetTaskReport request: %s", utils.AnyToStr(req))
	if err := dat.ReportInfo(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[单位测绘-任务简报] Get detect asset task report info failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DetectAssetTaskReportImportAssetDownload 单位测绘-任务简报-导入资产下载
func (*Web) DetectAssetTaskReportImportAssetDownload(ctx context.Context, req *pb.DetectAssetTaskReportRequest, rsp *pb.AssetAuditResultDownloadResponse) error {
	log.WithContextInfof(ctx, "[单位测绘-任务简报] Received Web.DetectAssetTaskImportAssetDownload request: %s", utils.AnyToStr(req))
	if err := dat.ImportAssetDownload(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[单位测绘-任务简报] Detect Asset Task Import Asset Download failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DetectAssetTaskReportClueList 单位测绘-任务简报-线索列表
func (*Web) DetectAssetTaskReportClueList(ctx context.Context, req *pb.DetectAssetTaskReportClueListRequest, rsp *pb.DetectAssetTaskReportClueListResponse) error {
	log.WithContextInfof(ctx, "[单位测绘-任务简报] Received Web.DetectAssetTaskReportClueList request: %s", utils.AnyToStr(req))
	if err := dat.ClueList(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[单位测绘-任务简报] Get detect_asset_task report clue list failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// EngineRuleDataSync 规则引擎-本地化同步saas平台规则
func (*Web) EngineRuleDataSync(ctx context.Context, _ *pb.Empty, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[规则引擎-本地化同步saas平台规则] Received Web.EngineRuleDataSync request")
	if err := rule_engine.EngineRuleDataSync(ctx); err != nil {
		log.WithContextErrorf(ctx, "[规则引擎-本地化同步saas平台规则] Get EngineRuleDataSync failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// EngineRuleExpose 规则引擎-saas平台暴露规则
func (*Web) EngineRuleExpose(ctx context.Context, req *pb.EngineRuleListRequest, rsp *pb.EngineRuleListResponse) error {
	log.WithContextInfof(ctx, "[规则引擎-saas平台暴露规则] Received Web.EngineRuleExpose request")
	if err := rule_engine.EngineRuleExpose(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[规则引擎-saas平台暴露规则] Get EngineRuleExpose failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// WebsiteMessageNewList 站内信-上次登录以来产生的新数据
func (*Web) WebsiteMessageNewList(ctx context.Context, req *pb.WebsiteMessageNewListRequest, rsp *pb.WebsiteMessageNewListResponse) error {
	log.WithContextInfo(ctx, "[站内信-上次登录以来产生的新数据] Received Web.WebsiteMessageNewList request")
	lastLoginTime, err := operate_logs.NewModel().GetLastLoginTime(req.UserId)
	if err != nil {
		// 如果是新用户没有登录记录，不应该返回错误，而是使用默认时间
		if stderrors.Is(err, gorm.ErrRecordNotFound) {
			log.WithContextInfof(ctx, "[站内信-上次登录以来产生的新数据] 新用户没有登录记录，使用默认时间")
			// 使用一个很早的时间作为默认值，这样可以查询到所有消息
			lastLoginTime = time.Time{}
		} else {
			return err
		}
	}
	if err := website_message.WebsiteMessageNewList(req, lastLoginTime, rsp); err != nil {
		log.WithContextErrorf(ctx, "[站内信-上次登录以来产生的新数据] Get WebsiteMessageNewListResponse failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// WebsiteMessageList 站内信-列表展示
func (*Web) WebsiteMessageList(ctx context.Context, req *pb.WebsiteMessageListRequest, rsp *pb.WebsiteMessageListResponse) error {
	log.WithContextInfof(ctx, "[站内信-列表展示] Received Web.WebsiteMessageListRequest request")
	if err := website_message.WebsiteMessageList(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[站内信-列表展示] Get WebsiteMessageListResponse failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// WebsiteMessageRead 站内信-已读
func (*Web) WebsiteMessageRead(ctx context.Context, req *pb.WebsiteMessageUpdateRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[站内信-已读] Received Web.WebsiteMessageReadRequest request")
	if err := website_message.WebsiteMessageRead(req); err != nil {
		log.WithContextErrorf(ctx, "[站内信-已读] Get WebsiteMessageReadResponse failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// WebsiteMessageDelete 站内信-删除
func (*Web) WebsiteMessageDelete(ctx context.Context, req *pb.WebsiteMessageUpdateRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[站内信-删除] Received Web.WebsiteMessageDelete request")
	if err := website_message.WebsiteMessageDelete(req); err != nil {
		log.WithContextErrorf(ctx, "[站内信-删除] Get WebsiteMessageDeleteResponse failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// WebsiteMessageNotify 站内信-策略展示
func (*Web) WebsiteMessageNotify(ctx context.Context, req *pb.WebsiteMessageNotifyRequest, rsp *pb.WebsiteMessageNotifyResponse) error {
	log.WithContextInfof(ctx, "[站内信-策略展示] Received Web.WebsiteMessageNotify request")
	if err := website_message.WebsiteMessageNotify(req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[站内信-策略展示] Get WebsiteMessageNotifyResponse failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// WebsiteMessageUpdate 站内信-策略更新
func (*Web) WebsiteMessageUpdate(ctx context.Context, req *pb.WebsiteMessageNotifyRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "[站内信-策略更新] Received Web.WebsiteMessageUpdate request")
	// 参数校验
	// 选择邮箱却没有填地址
	if req.IsEmail == 1 && !utils.IsValidEmail(req.Email) {
		return errors.BadRequest(pb.ServiceName, errorsr.New("格式不正确，邮箱地址为空").Error())
	}
	// 通知全不勾选
	if req.IsEmail == 2 && req.IsWebsiteMsg == 2 {
		return errors.BadRequest(pb.ServiceName, errorsr.New("站内信通知和邮箱通知必须选一个").Error())
	}
	// 消除格式
	if strings.Trim(req.AssetChangeJson, "[]") == "" && strings.Trim(req.TaskJson, "[]") == "" && strings.Trim(req.RiskFoundJson, "[]") == "" {
		return errors.BadRequest(pb.ServiceName, errorsr.New("至少选择一项通知内容").Error())
	}
	if err := website_message.WebsiteMessageUpdate(req); err != nil {
		log.WithContextErrorf(ctx, "[站内信-策略更新] Get WebsiteMessageUpdateResponse failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// GetApiAnalyzeResult 获取分析结果
func (w *Web) GetApiAnalyzeResult(ctx context.Context, request *pb.ApiAnalyzeResultRequest, response *pb.ApiAnalyzeResultResponse) error {
	err := api_analyzer.GetApiAnalyzeResult(request, response)
	if err != nil {
		return err
	}
	return nil
}

// GetApiAnalyzeDetail 获取分析接口/JS/外站地址列表
func (w *Web) GetApiAnalyzeDetail(ctx context.Context, request *pb.ApiAnalyzeResultRequest, response *pb.ApiAnalyzeDetailResponse) error {
	err := api_analyzer.GetApiAnalyzeDetail(request, response)
	if err != nil {
		return err
	}
	return nil
}

// GetApiAnalyzeTaskList 获取用户任务列表
func (w *Web) GetApiAnalyzeTaskList(ctx context.Context, request *pb.APiAnalyzeTaskListRequest, response *pb.ApiAnalyzeUserTaskListResponse) error {
	err := api_analyzer.GetApiAnalyzeList(request, response)
	if err != nil {
		return err
	}
	return nil
}

// GetApiAnalyzeUserTaskInfo 获取用户任务详情
func (w *Web) GetApiAnalyzeUserTaskInfo(ctx context.Context, request *pb.ApiAnalyzeUserTaskInfoRequest, response *pb.ApiAnalyzeUserTaskInfoResponse) error {
	err := api_analyzer.GetApiAnalyzeUserTaskInfo(request, response)
	if err != nil {
		return err
	}
	return nil
}

// DeleteApiAnalyzeUserTask 删除用户任务
func (w *Web) DeleteApiAnalyzeUserTask(ctx context.Context, request *pb.ApiAnalyzeDeleteRequest, response *pb.ApiAnalyzeDeleteResponse) error {
	err := api_analyzer.DeleteApiAnalyzeUserTask(request, response)
	if err != nil {
		return err
	}
	return nil
}

// DeleteApiAnalyzeTask 删除任务
func (w *Web) DeleteApiAnalyzeTask(ctx context.Context, request *pb.ApiAnalyzeDeleteRequest, response *pb.ApiAnalyzeDeleteResponse) error {
	err := api_analyzer.DeleteApiAnalyzeTask(request, response)
	if err != nil {
		return err
	}
	return nil
}

// ApiAnalyzeResultExport 结果导出
func (w *Web) ApiAnalyzeResultExport(ctx context.Context, request *pb.ApiAnalyzeResultExportRequest, response *pb.SystemDetectDownloadResponse) error {
	err := api_analyzer.ApiAnalyzeResultExport(request, response)
	if err != nil {
		return err
	}
	return nil
}

// LoginAssetsList 登录资产列表
func (w *Web) LoginAssetsList(ctx context.Context, request *pb.LoginAssetsListRequest, response *pb.LoginAssetsListResponse) error {
	err := login_assets.LoginAssetsList(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) LoginAssetsDelete(ctx context.Context, request *pb.LoginAssetsDeleteRequest, response *pb.Empty) error {
	err := login_assets.LoginAssetsDelete(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) LoginAssetsUpdate(ctx context.Context, request *pb.LoginAssetsUpdateRequest, response *pb.Empty) error {
	err := login_assets.LoginAssetsUpdate(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) LoginAssetsCountStatus(ctx context.Context, request *pb.LoginAssetsCountStatusRequest, response *pb.LoginAssetsCountStatusResponse) error {
	err := login_assets.LoginAssetsCountStatus(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) LoginAssetsExport(ctx context.Context, request *pb.LoginAssetsDeleteRequest, response *pb.FileExportResponse) error {
	err := login_assets.LoginAssetsExport(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) LoginAssetsEdit(ctx context.Context, request *pb.LoginAssetsEditRequest, response *pb.Empty) error {
	err := login_assets.LoginAssetsEdit(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) LoginAssetsImport(ctx context.Context, request *pb.LoginAssetsImportRequest, response *pb.Empty) error {
	err := login_assets.LoginAssetsImport(ctx, request, response)
	if err != nil {
		return err
	}
	return nil
}

func (w *Web) WhoisInfo(ctx context.Context, req *pb.WhoisInfoRequest, rsp *pb.WhoisInfoResponse) error {
	log.WithContextInfof(ctx, "[域名Whois信息] Received Web.WhoisInfo request: %s", utils.AnyToStr(req))
	if err := detect_assets.WhoisInfo(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[域名Whois信息] Get whois info failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) RecommendAssets(ctx context.Context, req *pb.RecommendAssetsRequest, rsp *pb.RecommendAssetsResponse) error {
	log.WithContextInfof(ctx, "[推荐资产] Received Web.RecommendAssets request: %s", utils.AnyToStr(req))
	if err := detect_assets.RecommendAssets(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[推荐资产] Recommend assets failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) CertAssetList(ctx context.Context, req *pb.CertAssetListRequest, rsp *pb.CertAssetListResponse) error {
	log.WithContextInfof(ctx, "[证书资产] Received Web.CertAssetList request: %s", utils.AnyToStr(req))
	if err := cert_assets.CertAssetsList(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[证书资产] Get cert asset list failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) CreateReport(ctx context.Context, req *pb.CreateReportRequest, rsp *pb.Empty) error {
	log.WithContextInfof(ctx, "[创建测绘报告] Received Web.CreateReport request: %s", utils.AnyToStr(req))
	if err := detect_assets.CreateReport(ctx, req, rsp); err != nil {
		log.WithContextErrorf(ctx, "[创建测绘报告] Create report failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) CertAssetExport(ctx context.Context, req *pb.CertAssetExportRequest, resp *pb.FileExportResponse) error {
	log.WithContextInfof(ctx, "[证书资产] Received Web.CertAssetExport request: %s", utils.AnyToStr(req))
	if err := cert_assets.CertAssetsExport(ctx, req, resp); err != nil {
		log.WithContextErrorf(ctx, "[证书资产] Export cert asset failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) CertAssetDelete(ctx context.Context, req *pb.CertAssetExportRequest, resp *pb.Empty) error {
	log.WithContextInfof(ctx, "[证书资产] Received Web.CertAssetDelete request: %s", utils.AnyToStr(req))
	if err := cert_assets.CertAssetsDelete(ctx, req, resp); err != nil {
		log.WithContextErrorf(ctx, "[证书资产] Delete cert asset failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) CertAssetDetail(ctx context.Context, req *pb.CertAssetDetailRequest, resp *pb.CertAssetItem) error {
	log.WithContextInfof(ctx, "[证书资产] Received Web.CertAssetDetail request: %s", utils.AnyToStr(req))
	if err := cert_assets.CertAssetDetail(ctx, req, resp); err != nil {
		log.WithContextErrorf(ctx, "[证书资产] Get cert asset detail failed: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CloudRecommendResults 云端推荐结果列表
func (w *Web) CloudRecommendResults(ctx context.Context, req *pb.CloudRecommendResultsRequest, rsp *pb.CloudRecommendResultsResponse) error {
	err := cloud_recommend.CloudRecommendResults(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果] 获取云端推荐结果失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CloudRecommendResultsClues 云端推荐结果线索展示
func (w *Web) CloudRecommendResultsClues(ctx context.Context, req *pb.CloudRecommendResultsRequest, rsp *pb.RecommendResultSearchMapResponse) error {
	err := cloud_recommend.ResultsClues(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果-线索展示] 获取云端推荐结果线索展示失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CloudRecommendResultsExport 云端推荐结果导出
func (w *Web) CloudRecommendResultsExport(ctx context.Context, req *pb.CloudRecommendExportRequest, rsp *pb.CloudRecommendExportResponse) error {
	err := cloud_recommend.ExportResults(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐结果-导出] 导出云端推荐结果失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// HunterSql Hunter SQL生成
func (w *Web) HunterSql(ctx context.Context, req *pb.HunterSqlRequest, rsp *pb.HunterSqlResponse) error {
	log.WithContextInfof(ctx, "Received Web.HunterSql request: %v", req)
	err := detect_assets.HunterSql(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[Hunter SQL] 生成Hunter查询语句失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ImportResultsConfirm 导入推荐资产数据
func (w *Web) ImportResultsConfirm(ctx context.Context, req *pb.ImportResultsConfirmRequest, rsp *pb.ImportResultsConfirmResponse) error {
	log.WithContextInfof(ctx, "Received Web.ImportResultsConfirm request: %v", req)
	err := detect_assets.ImportResultsConfirm(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[导入推荐资产] 导入推荐资产数据失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// GetDetectAssetsResultList 资产测绘结果列表
func (w *Web) GetDetectAssetsResultList(ctx context.Context, req *pb.DetectAssetsResultListRequest, rsp *pb.DetectAssetsResultListResponse) error {
	log.WithContextInfof(ctx, "Received Web.GetDetectAssetsResultList request: %v", req)
	err := detect_assets.GetDetectAssetsResultList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产测绘结果] 获取资产测绘结果列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DomainAssetsList 域名总资产列表
func (w *Web) DomainAssetsList(ctx context.Context, req *pb.DomainAssetsListRequest, rsp *pb.DomainAssetsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.DomainAssetsList request: %v", req)
	err := domain_assets.DomainAssetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[域名总资产列表] 获取域名总资产列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DomainAssetsExport 域名总资产导出
func (w *Web) DomainAssetsExport(ctx context.Context, req *pb.DomainAssetsExportRequest, rsp *pb.DomainAssetsExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.DomainAssetsExport request: %v", req)
	return domain_assets.DomainAssetsExport(ctx, req, rsp)
}

// DomainAssetsDelete 域名资产删除
func (w *Web) DomainAssetsDelete(ctx context.Context, req *pb.DomainAssetsDeleteRequest, rsp *pb.DomainAssetsDeleteResponse) error {
	log.WithContextInfof(ctx, "Received Web.DomainAssetsDelete request: %v", req)
	err := domain_assets.DomainAssetsDelete(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[域名资产] 删除域名资产数据失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// GetTaskList 获取扫描任务列表
func (w *Web) GetTaskList(ctx context.Context, req *pb.GetTaskListRequest, rsp *pb.GetTaskListResponse) error {
	log.WithContextInfof(ctx, "Received Web.GetTaskList request: %v", req)
	err := scan_task.GetTaskList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务列表] 获取扫描任务列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DeleteTask 删除扫描任务
func (w *Web) DeleteTask(ctx context.Context, req *pb.DeleteTaskRequest, rsp *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.DeleteTask request: %v", req)
	err := scan_task.DeleteTask(ctx, req)
	if err != nil {
		log.WithContextErrorf(ctx, "[删除扫描任务] 删除扫描任务失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// GetTaskDetail 获取扫描任务详情
func (w *Web) GetTaskDetail(ctx context.Context, req *pb.GetTaskDetailRequest, rsp *pb.GetTaskDetailResponse) error {
	log.WithContextInfof(ctx, "Received Web.GetTaskDetail request: %v", req)
	err := scan_task.GetTaskDetail(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务详情] 获取任务详情失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// GetTaskResult 获取扫描任务结果列表
func (w *Web) GetTaskResult(ctx context.Context, req *pb.GetTaskResultRequest, rsp *pb.GetTaskResultResponse) error {
	log.WithContextInfof(ctx, "Received Web.GetTaskResult request: %v", req)
	err := scan_task.GetTaskResult(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果] 获取扫描任务结果失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// GetTaskResultCondition 获取扫描任务结果筛选条件
func (w *Web) GetTaskResultCondition(ctx context.Context, req *pb.GetTaskResultConditionRequest, rsp *pb.GetTaskResultConditionResponse) error {
	log.WithContextInfof(ctx, "Received Web.GetTaskResultCondition request: %v", req)
	err := scan_task.GetTaskResultCondition(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果筛选条件] 获取筛选条件失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// TaskResultExport 任务结果导出
func (w *Web) TaskResultExport(ctx context.Context, req *pb.TaskResultExportRequest, rsp *pb.TaskResultExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.TaskResultExport request: %v", req)
	err := scan_task.TaskResultExport(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[任务结果导出] 导出失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// TaskResultAnalyse 扫描结果数据汇总
func (w *Web) TaskResultAnalyse(ctx context.Context, req *pb.TaskResultAnalyseRequest, rsp *pb.TaskResultAnalyseResponse) error {
	log.WithContextInfof(ctx, "Received Web.TaskResultAnalyse request: %v", req)
	err := scan_task.TaskResultAnalyse(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[扫描结果数据汇总] 汇总失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// GroupIpResults 获取IP维度推荐结果
func (w *Web) GroupIpResults(ctx context.Context, req *pb.GroupIpResultsRequest, rsp *pb.GroupIpResultsResponse) error {
	log.WithContextInfof(ctx, "Received Web.GroupIpResults request: %+v", req)
	err := cloud_recommend.GroupIpResults(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP维度推荐结果] 获取失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// DeleteRecommendResult 删除推荐结果
func (w *Web) DeleteRecommendResult(ctx context.Context, req *pb.DeleteRecommendResultRequest, rsp *pb.DeleteRecommendResultResponse) error {
	log.WithContextInfof(ctx, "Received Web.DeleteRecommendResult request: %+v", req)
	err := cloud_recommend.DeleteRecommendResult(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[删除推荐结果] 删除失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// UpdateAssetsConfidenceLevel 更新资产可信度
func (w *Web) UpdateAssetsConfidenceLevel(ctx context.Context, req *pb.UpdateAssetsConfidenceLevelRequest, rsp *pb.UpdateAssetsConfidenceLevelResponse) error {
	log.WithContextInfof(ctx, "Received Web.UpdateAssetsConfidenceLevel request: %+v", req)
	err := cloud_recommend.UpdateAssetsConfidenceLevel(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[更新资产可信度] 更新失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// TaskRelate 资产测绘关联异步任务
func (w *Web) TaskRelate(ctx context.Context, req *pb.TaskRelateRequest, rsp *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.TaskRelate request: %v", req)
	err := detect_assets.TaskRelate(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产测绘关联异步任务] 处理失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// UnsureAssetsList 获取疑似资产列表
func (w *Web) UnsureAssetsList(ctx context.Context, req *pb.UnsureAssetsListRequest, rsp *pb.UnsureAssetsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.UnsureAssetsList request: %v", req)
	err := unsure_assets.UnsureAssetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[疑似资产列表] 获取疑似资产列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// AnsysDataIndexV2 分析数据列表V2
func (w *Web) AnsysDataIndexV2(ctx context.Context, req *pb.UnsureAssetsListRequest, rsp *pb.UnsureAssetsListResponse) error {

	return nil
}

// IgnoreAssetsList 获取忽略资产列表
func (w *Web) IgnoreAssetsList(ctx context.Context, req *pb.IgnoreAssetsListRequest, rsp *pb.IgnoreAssetsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.IgnoreAssetsList request: %v", req)
	err := ignore_assets.IgnoreAssetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[忽略资产列表] 获取忽略资产列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ThreatenAssetsList 获取威胁资产列表
func (w *Web) ThreatenAssetsList(ctx context.Context, req *pb.ThreatenAssetsListRequest, rsp *pb.ThreatenAssetsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.ThreatenAssetsList request: %v", req)
	err := threaten_assets.ThreatenAssetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[威胁资产列表] 获取威胁资产列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) TableAssetsStatistics(ctx context.Context, req *pb.TableAssetsStatisticsRequest, rsp *pb.TableAssetsStatisticsResponse) error {
	log.WithContextInfof(ctx, "Received Web.TableAssetsStatistics request: %v", req)
	err := table_assets.TableAssetsStatistics(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[统计资产数据维度] 统计资产数据失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CrontabTaskList 周期任务列表
func (w *Web) CrontabTaskList(ctx context.Context, req *pb.CrontabTaskListRequest, rsp *pb.CrontabTaskListResponse) error {
	log.WithContextInfof(ctx, "Received Web.CrontabTaskList request: %v", req)
	err := scan_crontab.CrontabTaskList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 获取周期任务列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CrontabTaskDelete 删除周期任务
func (w *Web) CrontabTaskDelete(ctx context.Context, req *pb.CrontabTaskDeleteRequest, rsp *pb.CrontabTaskDeleteResponse) error {
	log.WithContextInfof(ctx, "Received Web.CrontabTaskDelete request: %v", req)
	err := scan_crontab.CrontabTaskDelete(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 删除周期任务失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CrontabTaskSwitch 周期任务开关设置
func (w *Web) CrontabTaskSwitch(ctx context.Context, req *pb.CrontabTaskSwitchRequest, rsp *pb.CrontabTaskSwitchResponse) error {
	log.WithContextInfof(ctx, "Received Web.CrontabTaskSwitch request: %v", req)
	err := scan_crontab.CrontabTaskSwitch(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 设置周期任务开关失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CrontabTaskDetail 周期任务详情
func (w *Web) CrontabTaskDetail(ctx context.Context, req *pb.CrontabTaskDetailRequest, rsp *pb.CrontabTaskDetailResponse) error {
	log.WithContextInfof(ctx, "Received Web.CrontabTaskDetail request: %v", req)
	err := scan_crontab.CrontabTaskDetail(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 获取周期任务详情失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CrontabTaskCreate 创建周期任务
func (w *Web) CrontabTaskCreate(ctx context.Context, req *pb.CrontabTaskCreateRequest, rsp *pb.CrontabTaskCreateResponse) error {
	log.WithContextInfof(ctx, "Received Web.CrontabTaskCreate request: %v", req)
	err := scan_crontab.CrontabTaskCreate(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 创建周期任务失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CrontabTaskEdit 编辑周期任务
func (w *Web) CrontabTaskEdit(ctx context.Context, req *pb.CrontabTaskEditRequest, rsp *pb.CrontabTaskEditResponse) error {
	log.WithContextInfof(ctx, "Received Web.CrontabTaskEdit request: %v", req)
	err := scan_crontab.EditCrontabTask(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 编辑周期任务失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ForbidIpsList 获取禁扫IP列表
func (w *Web) ForbidIpsList(ctx context.Context, req *pb.ForbidIpsListRequest, rsp *pb.ForbidIpsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.ForbidIpsList request: %v", req)
	err := forbid.ForbidIpsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[禁扫IP] 获取禁扫IP列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// AddForbidIps 添加禁扫IP
func (w *Web) AddForbidIps(ctx context.Context, req *pb.AddForbidIpsRequest, rsp *pb.AddForbidIpsResponse) error {
	log.WithContextInfof(ctx, "Received Web.AddForbidIps request: %v", req)
	err := forbid.AddForbidIps(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[禁扫IP] 添加禁扫IP失败: %v", err)
		return err // 直接返回原始错误，不再包装
	}
	return nil
}

// DeleteForbidIps 删除禁扫IP
func (w *Web) DeleteForbidIps(ctx context.Context, req *pb.DeleteForbidIpsRequest, rsp *pb.DeleteForbidIpsResponse) error {
	log.WithContextInfof(ctx, "Received Web.DeleteForbidIps request: %v", req)
	err := forbid.DeleteForbidIps(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[禁扫IP] 删除禁扫IP失败: %v", err)
		return err // 直接返回原始错误，不再包装
	}
	return nil
}

// IpDetail 获取IP详情信息
func (w *Web) IpDetail(ctx context.Context, req *pb.IpDetailRequest, rsp *pb.IpDetailResponse) error {
	log.WithContextInfof(ctx, "Received Web.IpDetail request: %v", req)
	err := detect_assets.IpDetail(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP详情] 获取IP详情信息失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) IpAssetCondition(ctx context.Context, request *pb.IpAssetConditionRequest, response *pb.IpAssetConditionResponse) error {
	log.WithContextInfof(ctx, "Received Web.IpAssetCondition request: %v", request)
	if err := asset_account.AssetsAccountIpCondition(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[IP资产条件] 获取IP资产条件失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) IpPortAssetCondition(ctx context.Context, request *pb.IpPortAssetConditionRequest, response *pb.IpPortAssetConditionResponse) error {
	log.WithContextInfof(ctx, "Received Web.IpPortAssetCondition request: %v", request)
	if err := asset_account.IpPortAssetCondition(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[IP端口资产条件] 获取IP端口资产条件失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) IpAssetConfirm(ctx context.Context, request *pb.IpAssetActionRequest, empty *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.IpAssetConfirm request: %v", request)
	if err := asset_account.AssetsAccountIpConfirm(ctx, request, empty); err != nil {
		log.WithContextErrorf(ctx, "[IP资产确权] 确权IP资产失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) IpAssetCustomerTag(ctx context.Context, request *pb.IpAssetActionRequest, empty *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.IpAssetCustomerTag request: %v", request)
	if err := asset_account.AssetsAccountSetCustomerTag(ctx, request, empty); err != nil {
		log.WithContextErrorf(ctx, "[IP资产自定义标签] 设置IP资产自定义标签失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// PassetsList 疑似资产列表
func (w *Web) PassetsList(ctx context.Context, req *pb.PassetsListRequest, rsp *pb.PassetsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.PassetsList request: %v", req)
	err := unsure_assets.PassetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP端口疑似资产列表] 获取列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// IngorePassetsList 忽略资产列表
func (w *Web) IgnorePassetsList(ctx context.Context, req *pb.IgnorePassetsListRequest, rsp *pb.PassetsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.IngorePassetsList request: %v", req)
	err := ignore_assets.IgnorePassetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP端口疑似资产列表] 获取列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ThreatenPassetsList 威胁资产列表
func (w *Web) ThreatenPassetsList(ctx context.Context, req *pb.ThreatPassetsListRequest, rsp *pb.PassetsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.ThreatenPassetsList request: %v", req)
	err := threaten_assets.ThreatenPassetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP端口威胁资产列表] 获取列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// SurePassetsList 台账资产列表
func (w *Web) SurePassetsList(ctx context.Context, req *pb.SurePassetsListRequest, rsp *pb.PassetsListResponse) error {
	log.WithContextInfof(ctx, "Received Web.SurePassetsList request: %v", req)
	err := asset_account.SurePassetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP端口台账资产列表] 获取列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) UnclaimAssetsIPExport(ctx context.Context, request *pb.IpAssetActionRequest, response *pb.FileExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.UnclaimAssetsIPExport request: %v", request)
	if err := unsure_assets.UnclaimAssetsIPExport(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[IP资产导出] 导出IP资产失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) ThreatenAssetsIPExport(ctx context.Context, request *pb.IpAssetActionRequest, response *pb.FileExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.ThreatenAssetsIPExport request: %v", request)
	if err := threaten_assets.ThreatenAssetsIPExport(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[威胁资产导出] 导出威胁资产失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) IgnoreAssetsIPExport(ctx context.Context, request *pb.IpAssetActionRequest, response *pb.FileExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.IgnoreAssetsIPExport request: %v", request)
	if err := ignore_assets.IgnoreAssetsIPExport(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[忽略资产导出] 导出忽略资产失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ExportPassetsList 疑似资产列表导出
func (w *Web) ExportPassetsList(ctx context.Context, req *pb.IpPortActionRequest, rsp *pb.FileExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.ExportPassetsList request: %v", req)
	err := unsure_assets.ExportPassetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP端口台账资产列表] 获取列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ExportSurePassetsList 台账资产列表导出
func (w *Web) ExportSurePassetsList(ctx context.Context, req *pb.IpPortActionRequest, rsp *pb.FileExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.ExportSurePassetsList request: %v", req)
	err := asset_account.ExportSurePassetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP端口台账资产列表] 获取列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ExportIgnorePassetsList 忽略资产列表导出
func (w *Web) ExportIgnorePassetsList(ctx context.Context, req *pb.IpPortActionRequest, rsp *pb.FileExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.ExportIgnorePassetsList request: %v", req)
	err := ignore_assets.ExportIgnorePassetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP端口忽略资产列表] 获取列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// ExportThreatenPassetsList 威胁资产列表导出
func (w *Web) ExportThreatenPassetsList(ctx context.Context, req *pb.IpPortActionRequest, rsp *pb.FileExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.ExportThreatenPassetsList request: %v", req)
	err := threaten_assets.ExportThreatenPassetsList(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[IP端口威胁资产列表] 获取列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) AddTitleBlackKeyword(ctx context.Context, request *pb.TitleBlackKeywordRequest, empty *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.AddTitleBlackKeyword request: %v", request)
	if err := black_keyword.AddTitleBlackKeyword(ctx, request, empty); err != nil {
		log.WithContextErrorf(ctx, "[添加标题黑名单关键词] 添加失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) FofaUpdate(ctx context.Context, request *pb.FofaUpdateRequest, empty *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.FofaUpdate request: %v", request)
	if err := account_domain.FofaUpdate(ctx, request, empty); err != nil {
		log.WithContextErrorf(ctx, "[Fofa更新] 更新Fofa信息失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) PushDomainData(ctx context.Context, request *pb.PushDomainDataRequest, empty *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.PushDomainData request: %v", request)
	if err := account_domain.PushDomainAssets(ctx, request, empty); err != nil {
		log.WithContextErrorf(ctx, "[上传域名数据保存] 推送域名数据失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// AssetsImportData 全局台账、疑似数据的导入：IP+端口+协议
func (w *Web) AssetsImportData(ctx context.Context, req *pb.AssetsImportDataRequest, _ *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.AssetsImportData request: %v", req)
	err := table_assets.AssetsImportData(ctx, req)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产导入] 导入资产数据失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// IpAssetUpdateCompanyName 批量编辑IP资产的企业名称
func (w *Web) IpAssetUpdateCompanyName(ctx context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.IpAssetUpdateCompanyName request: %v", req)

	// 参数校验
	if req.UserId == 0 {
		return errors.BadRequest(pb.ServiceName, "用户ID不能为空")
	}

	if req.SetClueCompanyName == nil || *req.SetClueCompanyName == "" {
		return errors.BadRequest(pb.ServiceName, "要设置的企业名称不能为空")
	}

	// 调用资产处理服务
	err := ip_asset.UpdateCompanyName(ctx, req)
	if err != nil {
		log.WithContextErrorf(ctx, "[资产台账] 批量编辑IP资产的企业名称失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}

	return nil
}

func (w *Web) BatchMatchBlackWord(ctx context.Context, request *pb.IpAssetActionRequest, response *pb.BatchResponse) error {
	log.WithContextInfof(ctx, "Received Web.BatchMatchBlackWord request: %v", request)
	if err := unsure_assets.BatchMatchBlackWord(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[批量匹配未知资产的黄赌毒关键词规则] 批量匹配未知资产的黄赌毒关键词规则失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) IpAssetSetStatus(ctx context.Context, request *pb.IpAssetActionRequest, empty *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.IpAssetSetStatus request: %v", request)
	if err := asset_account.AssetsAccountSetStatus(ctx, request, empty); err != nil {
		log.WithContextErrorf(ctx, "[IP资产处置] 设置IP资产状态失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) SetThreatenType(ctx context.Context, request *pb.IpAssetActionRequest, response *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.SetThreatenType request: %v", request)
	if err := threaten_assets.SetThreatenType(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[威胁资产列表/IP维度批量修改威胁资产类型] 威胁资产列表/IP维度批量修改威胁资产类型失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CheckBookData 核对台账模版
func (w *Web) CheckBookData(ctx context.Context, req *pb.CheckBookDataRequest, rsp *pb.CheckBookDataResponse) error {
	log.WithContextInfof(ctx, "Received Web.CheckBookData request: %v", req)
	err := asset_account.CheckBookData(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[核对台账模版] 核对台账模版失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) UnsureAssetSetStatus(ctx context.Context, request *pb.IpAssetActionRequest, empty *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.UnsureAssetSetStatus request: %v", request)
	if err := unsure_assets.AssetsUnsureSetStatus(ctx, request, empty); err != nil {
		log.WithContextErrorf(ctx, "[疑似资产处置] 设置疑似资产状态失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) IgnoreAssetSetStatus(ctx context.Context, request *pb.IpAssetActionRequest, empty *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.IgnoreAssetSetStatus request: %v", request)
	if err := ignore_assets.AssetsIgnoreSetStatus(ctx, request, empty); err != nil {
		log.WithContextErrorf(ctx, "[忽略资产处置] 设置忽略资产状态失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) ThreatenAssetSetStatus(ctx context.Context, request *pb.IpAssetActionRequest, empty *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.ThreatenAssetSetStatus request: %v", request)
	if err := threaten_assets.AssetsThreatenSetStatus(ctx, request, empty); err != nil {
		log.WithContextErrorf(ctx, "[威胁资产处置] 设置威胁资产状态失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) CreateScanTask(ctx context.Context, request *pb.CreateScanTaskRequest, response *pb.CreateScanTaskResponse) error {
	log.WithContextInfof(ctx, "Received Web.CreateScanTask request: %v", request)
	if err := scan_task.CreateScanTask(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[创建扫描任务] 创建扫描任务失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) AssetAccountImportScan(ctx context.Context, request *pb.AssetAccountImportScanRequest, response *pb.AssetAccountImportScanResponse) error {
	log.WithContextInfof(ctx, "Received Web.AssetAccountImportScan request: %v", request)
	if err := asset_account.AssetAccountImportScan(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[资产台账导入并扫描] 资产台账导入并扫描失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) CreateCustomPoc(ctx context.Context, request *pb.CustomPoc, response *pb.CreateCustomPocResponse) error {
	log.WithContextInfof(ctx, "Received Web.CreateCustomPoc request: %v", request)
	if err := custom_poc.Create(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[创建自定义poc] 创建自定义poc失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) UpdateCustomPoc(ctx context.Context, request *pb.CustomPoc, response *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.UpdateCustomPoc request: %v", request)
	if err := custom_poc.Update(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[更新自定义poc] 更新自定义poc失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) DeleteCustomPoc(ctx context.Context, request *pb.DeleteCustomPocRequest, response *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.DeleteCustomPoc request: %v", request)
	if err := custom_poc.Delete(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[删除自定义poc] 删除自定义poc失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) GetCustomPocDetail(ctx context.Context, request *pb.CustomPocSearch, response *pb.CustomPoc) error {
	log.WithContextInfof(ctx, "Received Web.GetCustomPocDetail request: %v", request)
	if err := custom_poc.GetDetail(ctx, request, response); err != nil {
		if err == gorm.ErrRecordNotFound {
			return err
		}
		log.WithContextInfof(ctx, "[查询自定义poc详情] 查询自定义poc详情失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) GetCustomPocList(ctx context.Context, request *pb.CustomPocQueryListAndSearch, response *pb.CustomPocList) error {
	log.WithContextInfof(ctx, "Received Web.GetCustomPocList request: %v", request)
	if err := custom_poc.List(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[获取自定义poc列表] 获取自定义poc列表失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

func (w *Web) PublishCustomPoc(ctx context.Context, request *pb.CustomPocSearch, response *pb.Empty) error {
	log.WithContextInfof(ctx, "Received Web.PublishCustomPoc request: %v", request)
	if err := custom_poc.Publish(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[发布/下线 自定义poc] 发布/下线 自定义poc失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}
func (w *Web) RiskAssetsUpdate(ctx context.Context, request *pb.RiskAssetsUpdateRequest, response *pb.RiskAssetsUpdateResponse) error {
	log.WithContextInfof(ctx, "Received Web.RiskAssetsUpdate request: %v", request)
	if err := custom_poc.RiskAssetsUpdate(ctx, request, response); err != nil {
		log.WithContextErrorf(ctx, "[风险资产更新] 风险资产更新失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}

// CloudRecommend 云端推荐
func (w *Web) CloudRecommend(ctx context.Context, req *pb.CloudRecommendRequest, rsp *pb.CloudRecommendResponse) error {
	err := cloud_recommend.CloudRecommend(ctx, req, rsp)
	if err != nil {
		log.WithContextErrorf(ctx, "[云端推荐] 云端推荐失败: %v", err)
		return errors.BadRequest(pb.ServiceName, err.Error())
	}
	return nil
}
