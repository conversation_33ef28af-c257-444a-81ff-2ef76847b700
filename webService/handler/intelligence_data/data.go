package intelligence_data

import (
	"context"
	"errors"
	"fmt"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/webService/handler/user"
	"regexp"

	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

var once sync.Once
var taskInfo *TaskInfo

type TaskInfo struct {
	Tasks sync.Map
}

func GetTaskInfo() *TaskInfo {
	if taskInfo == nil {
		once.Do(func() {
			taskInfo = &TaskInfo{}
		})
	}
	return taskInfo
}

func CheckTaskRunning(userId uint64) bool {
	if v, ok := GetTaskInfo().Tasks.Load(userId); ok {
		log.Info(fmt.Sprintf("CheckTaskRunning: userId:%d,任务正在运行,进度:%v", userId, v))
		return true
	}
	return false
}

func AddTask(userId uint64) {
	GetTaskInfo().Tasks.Store(userId, 1)
	redis.SetCache("threat-t:"+cast.ToString(userId), 1*time.Minute, 1)
}

func DeleteTask(userId uint64) {
	GetTaskInfo().Tasks.Delete(userId)
}

func GetTaskProcess(userId uint64) string {
	process, ok := GetTaskInfo().Tasks.Load(userId)
	if !ok {
		log.Warn(fmt.Sprintf("GetTaskProcess: userId:%d,任务不存在!", userId))
		return "100"
	}
	return fmt.Sprintf("%.2f", process)
}

func queryResult(userId uint64, ipList []any) ([]foradar_assets.ForadarAsset, error) {
	// 查询风险匹配资产
	boolQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("status", foradar_assets.StatusConfirmAsset, foradar_assets.StatusUploadAsset))
	if ok, _ := user.IsUserAdmin(userId); !ok {
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("user_id", userId))
	}
	if len(ipList) != 0 {
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("ip", ipList...))
		// es.PrintEsQuery(boolQuery)
		assetResult, err := foradar_assets.NewForadarAssetModel().ListAll(context.TODO(), boolQuery)
		if err != nil {
			return nil, err
		}
		return assetResult, nil
	} else {
		return nil, errors.New("ip list is empty")
	}
}

func MatchData(userId uint64, list []*intelligence.Data) {
	defer func() {
		if err := recover(); err != nil {
			log.Warn(fmt.Sprintf("MatchData: userId:%d,任务异常,Error:%v!", userId, err))
			DeleteTask(userId)
		}
	}()
	total := len(list)
	log.Info(fmt.Sprintf("IntelligenceDataMatch: userId:%d,开始匹配资产!数据数量:%d", userId, total))
	// 获取所有 Data ids
	ids := utils.ListDistinctNonZero(utils.ListColumn(list, func(t *intelligence.Data) uint64 { return t.ID }))
	if len(ids) == 0 {
		DeleteTask(userId)
		return
	}
	riClient := intelligence.NewRelatedIntelligence()
	// 统计资产数量
	assetCount := make([]intelligence.DataSummary, 0)
	// 按100条拆分
	listSplits := utils.ListSplit(list, 100)
	index := 0
	// 关联情报表数据
	relatedIntelligenceList := make([]*intelligence.RelatedIntelligence, 0)
	for x := range listSplits {
		splitDataList := listSplits[x]
		// 查询命中结果
		ipList := utils.ListColumn(splitDataList, func(t *intelligence.Data) any {
			// 正则表达式模式，用于匹配 IP 地址和端口
			pattern := `(?:(?:[0-9]{1,3}\.){3}[0-9]{1,3}|(?:[a-fA-F0-9]{1,4}:){7}[a-fA-F0-9]{1,4}):[0-9]{1,5}`
			// 编译正则表达式
			re := regexp.MustCompile(pattern)
			// 查找所有匹配项
			t.LeakSourceIP = strings.ReplaceAll(t.LeakSourceIP, "\r", "")
			t.LeakSourceIP = strings.ReplaceAll(t.LeakSourceIP, "\n", "")
			t.LeakSourceIP = strings.Trim(t.LeakSourceIP, " ")
			s := strings.ReplaceAll(t.LeakSourceIP, " ", "")
			s = strings.ReplaceAll(s, "\r", "")
			s = strings.ReplaceAll(s, "\n", "")
			matches := re.FindAllString(s, -1)
			if len(matches) > 0 {
				return strings.Split(matches[0], ":")[0]
			}
			return nil

			// ipPort := strings.Split(strings.Split(t.LeakSourceIP, "(")[0], "（")[0]
			// ip := strings.Split(ipPort, ":")[0]
			// ip = strings.ReplaceAll(ip, "\r", "")
			// ip = strings.ReplaceAll(ip, "\n", "")
			// // port := strings.Split(ipPort, ":")[1]
			// return ip
		})
		ips := make([]any, 0)
		// 去重
		for _, v := range ipList {
			if v != nil && v.(string) != "" {
				ips = append(ips, v)
			}
		}
		assets, err := queryResult(userId, ips)
		if err != nil {
			DeleteTask(userId)
			log.Warn(fmt.Sprintf("MatchData: userId:%d,查询用户资产信息失败,Error:%v!", userId, err))
			return
		}

		for _, asset := range assets {
			assetIpAndPort := fmt.Sprintf("%s:%v", asset.Ip, asset.Port)
			for y := range splitDataList {
				data := splitDataList[y]
				if data.LeakSourceIP == assetIpAndPort {
					foundTime := time.Now().Format("2006-01-02 15:04:05")
					query := make([]mysql.HandleFunc, 0)
					query = append(query, mysql.WithColumnValue("intelligence_id", data.ID))
					query = append(query, mysql.WithColumnValue("risk_type", 4))
					query = append(query, mysql.WithColumnValue("user_id", userId))
					query = append(query, mysql.WithColumnValue("asset_ip", asset.Ip))
					switch asset.Port.(type) {
					case float64:
						query = append(query, mysql.WithColumnValue("asset_port", cast.ToString(asset.Port.(float64))))
					case string:
						query = append(query, mysql.WithColumnValue("asset_port", asset.Port.(string)))
					}
					existRi, err := riClient.FindBy(query...)
					if err != nil {
						if err != gorm.ErrRecordNotFound {
							log.Warn(fmt.Sprintf("MatchEvent: userId:%d,查询关联情报表数据失败,Error:%v!", userId, err))
						}
					} else {
						foundTime = existRi.FoundTime
					}
					// 生成关联情报表数据
					ri := &intelligence.RelatedIntelligence{
						IntelligenceID: data.ID, UserID: userId, EnterpriseID: uint64(asset.CompanyID),
						AssetID: asset.ID, AssetIP: asset.Ip, AssetPort: fmt.Sprintf("%v", asset.Port),
						AssetProtocol: asset.Protocol, AssetURL: asset.Url, AssetTitle: fmt.Sprintf("%v", asset.Title), AssetStatus: cast.ToString(asset.Status.(float64)),
						SpecialProjectName: data.SpecialProjectName, RiskName: data.EventName, RiskType: "4",
						FoundTime: foundTime, UpdateTime: time.Now().Format("2006-01-02 15:04:05"),
					}
					if asset.ClueCompanyName != nil && len(asset.ClueCompanyName.([]interface{})) > 0 {
						if asset.ClueCompanyName.([]interface{})[0] != nil {
							ri.EnterpriseName = asset.ClueCompanyName.([]interface{})[0].(string)
						}
					}
					ri.ServiceComponent = strings.Join(utils.ListColumn(asset.RuleTags, func(t foradar_assets.RuleTag) string { return t.CnProduct }), ",")
					relatedIntelligenceList = append(relatedIntelligenceList, ri)
					// 统计资产和实体数量，注意不同资产对应实体可能相同
					exist := false
					if len(assetCount) > 0 {
						for _, v := range assetCount {
							if v.SpecialProjectName == data.SpecialProjectName {
								exist = true
								v.AssetNum++
								if asset.ClueCompanyName != nil && len(asset.ClueCompanyName.([]interface{})) > 0 {
									v.CompanyList = append(v.CompanyList, asset.ClueCompanyName.([]interface{})[0].(string))
									v.CompanyList = utils.ListDistinctNonZero(v.CompanyList)
									v.CompanyNum = len(v.CompanyList)
								}
							}
						}
					}
					if !exist {
						ds := intelligence.DataSummary{
							SpecialProjectName: data.SpecialProjectName,
							AssetNum:           1,
						}
						if asset.ClueCompanyName != nil && len(asset.ClueCompanyName.([]interface{})) > 0 {
							ds.CompanyList = []string{asset.ClueCompanyName.([]interface{})[0].(string)}
							ds.CompanyNum = 1
						} else {
							ds.CompanyList = []string{}
							ds.CompanyNum = 0
						}
						assetCount = append(assetCount, ds)
					}
				}
			}
		}
		// 计算进度
		index += len(splitDataList)
		progress := float32(index) / float32(total) * 100.0
		if progress >= 100 {
			// 防止进度超过100
			progress = 95
		}
		if cast.ToFloat32(GetTaskProcess(userId)) < progress && cast.ToInt(progress) != 100 {
			GetTaskInfo().Tasks.Store(userId, progress)
			log.Infof(fmt.Sprintf("userID:%d,MatchData进度:%f", userId, progress))
		}
	}
	// 删除旧的关联情报表数据
	query := make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithColumnValue("user_id", userId))
	query = append(query, mysql.WithColumnValue("risk_type", 4))
	idList := utils.ListColumn(list, func(t *intelligence.Data) uint64 { return t.ID })
	if len(idList) > 0 {
		query = append(query, mysql.WithValuesIn("intelligence_id", idList))
	}
	err := riClient.DeleteBy(query...)
	if err != nil {
		log.Warn(fmt.Sprintf("IntelligenceDataMatch: userId:%d,删除关联情报表数据失败,Error:%v", userId, err.Error()))
	}
	// 插入关联情报表数据
	if err := intelligence.NewRelatedIntelligence().BatchCreate(relatedIntelligenceList); err != nil {
		log.Warn(fmt.Sprintf("IntelligenceDataMatch: userId:%d,创建关联情报表数据失败,Error:%v", userId, err.Error()))
	}
	// 更新关联资产数量
	for _, v := range assetCount {
		if err := intelligence.NewDataSummary().UpdateRelatedAssetCount(v.SpecialProjectName, v.AssetNum, v.CompanyNum); err != nil {
			log.Warn(fmt.Sprintf("IntelligenceDataMatch: userId:%d,更新资产数量失败,Error:%v", userId, err.Error()))
		}
	}

	time.Sleep(2 * time.Second)
	GetTaskInfo().Tasks.Store(userId, 100)
	DeleteTask(userId)
	log.Info(fmt.Sprintf("IntelligenceDataMatch: userId:%d 任务完成!", userId))
}
