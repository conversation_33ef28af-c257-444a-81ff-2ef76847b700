package scan_task

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/olivere/elastic"

	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	pb "micro-service/webService/proto"
)

// TaskResultExport 任务结果导出
func TaskResultExport(ctx context.Context, req *pb.TaskResultExportRequest, rsp *pb.TaskResultExportResponse) error {
	log.WithContextInfof(ctx, "Received Web.TaskResultExport request: %v", req)

	// 查询任务信息
	taskModel := scan_task.NewModel()
	taskInfo, err := taskModel.First(
		scan_task.WithID(uint64(req.Id)),
		scan_task.WithUserID(uint64(req.UserId)),
		scan_task.WithStatus(scan_task.StatusFinished), // 只获取已完成的任务
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[任务结果导出] 获取任务信息失败: %v", err)
		return err
	}

	if taskInfo == nil {
		log.WithContextErrorf(ctx, "[任务结果导出] 任务不存在或未完成")
		return fmt.Errorf("任务不存在或未完成")
	}

	// 使用SearchBuilder构建查询条件
	var builder es.SearchBuilder

	// 添加任务ID条件
	builder.AddMust([]interface{}{"task_id", req.Id})

	// 添加用户ID条件
	builder.AddMust([]interface{}{"user_id", req.UserId})

	// 添加状态条件
	if len(req.State) > 0 {
		builder.AddMust([]interface{}{"state", "in", es.ToInterfaceArray(req.State)})
	}

	// 添加规则标签条件
	if len(req.RuleTags) > 0 {
		var ruleTagsQuery [][]interface{}
		for _, tag := range req.RuleTags {
			ruleTagsQuery = append(ruleTagsQuery, []interface{}{"rules.rule_id", tag})
		}
		builder.AddShould(ruleTagsQuery...)
	}

	// 添加端口条件
	if len(req.Ports) > 0 {
		var portsQuery [][]interface{}
		for _, port := range req.Ports {
			portsQuery = append(portsQuery, []interface{}{"ports", port})
		}
		builder.AddShould(portsQuery...)
	}

	// 添加协议条件
	if len(req.Protocols) > 0 {
		var protocolsQuery [][]interface{}
		for _, protocol := range req.Protocols {
			protocolsQuery = append(protocolsQuery, []interface{}{"protocols", protocol})
		}
		builder.AddShould(protocolsQuery...)
	}

	// 添加公司标签条件
	if len(req.CompanyTags) > 0 {
		var companyTagsQuery [][]interface{}
		for _, tag := range req.CompanyTags {
			companyTagsQuery = append(companyTagsQuery, []interface{}{"company_tags", tag})
		}
		builder.AddShould(companyTagsQuery...)
	}

	// 添加二级分类标签条件
	if len(req.SecondCatTag) > 0 {
		var secondCatTagQuery [][]interface{}
		for _, tag := range req.SecondCatTag {
			secondCatTagQuery = append(secondCatTagQuery, []interface{}{"rules.cn_category", tag})
		}
		builder.AddShould(secondCatTagQuery...)
	}

	// 添加关键词搜索条件
	if req.Keyword != "" {
		var keywordQuery [][]interface{}
		keywordQuery = append(keywordQuery, []interface{}{"ip", "like", es.WithRLAsterisk(req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.protocol", "like", es.WithRLAsterisk(req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.title", "like", es.WithRLAsterisk(req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.domain", "like", es.WithRLAsterisk(req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.url", "like", es.WithRLAsterisk(req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"rules.cn_product", "like", es.WithRLAsterisk(req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"rules.cn_company", "like", es.WithRLAsterisk(req.Keyword)})
		builder.AddShould(keywordQuery...)
	}

	var sorts []elastic.Sorter
	sorts = append(sorts, elastic.NewFieldSort("updated_at").Desc(), elastic.NewFieldSort("id").Desc())

	// 执行查询
	_, taskAssets, err := es.ListByParams[fofaee_task_assets.FofaeeTaskAssets](1, 2000, builder.Build(), sorts)
	if err != nil {
		log.WithContextErrorf(ctx, "[任务结果导出] 查询失败: %v", err)
		return err
	}

	// 组装Excel数据
	excelData := make([][]string, 0)
	// 添加表头
	excelData = append(excelData, []string{"IP", "端口", "协议", "组件信息", "资产状态", "域名"})

	// 处理数据
	for _, asset := range taskAssets {
		// 获取规则信息
		var rules []string
		for _, rule := range asset.Rules {
			if rule.CnProduct != "" {
				rules = append(rules, rule.CnProduct)
			}
		}
		ruleStr := joinStrings(uniqueStrings(rules), ",")

		// 获取域名信息
		var domains []string
		for _, host := range asset.Hosts {
			domains = append(domains, getFullDomain(host))
		}
		domainStr := joinStrings(uniqueStrings(domains), ",")

		// 资产状态
		stateStr := "离线"
		if asset.State == 1 {
			stateStr = "在线"
		}

		// 处理端口列表
		portList := asset.PortList
		if len(portList) == 0 {
			// 如果没有端口信息，添加一行
			excelData = append(excelData, []string{
				asset.Ip,
				"",
				"",
				ruleStr,
				stateStr,
				domainStr,
			})
		} else {
			// 如果有端口信息，每个端口添加一行
			for _, port := range portList {
				portStr := fmt.Sprintf("%v", port.Port)
				excelData = append(excelData, []string{
					asset.Ip,
					portStr,
					port.Protocol,
					ruleStr,
					stateStr,
					domainStr,
				})
			}
		}
	}

	// 生成Excel文件
	fileName := fmt.Sprintf("资产扫描结果_%s.xlsx", time.Now().Format("20060102150405"))
	path := storage.GenDownloadLocalPath(fileName, uint64(req.UserId))
	if err = excel.WriteExcel(path, excelData); err != nil {
		log.WithContextErrorf(ctx, "[任务结果导出] 生成Excel文件失败: %v", err)
		return err
	}

	// 设置响应
	rsp.FilePath = path
	rsp.FileName = fileName
	rsp.Url = storage.GenAPIDownloadPath(filepath.Base(path), path)

	return nil
}

// getFullDomain 获取完整域名
func getFullDomain(domain string) string {
	return domain // 在Go中直接返回域名，不需要特殊处理
}

// uniqueStrings 去重字符串切片
func uniqueStrings(strs []string) []string {
	if len(strs) == 0 {
		return strs
	}

	seen := make(map[string]struct{})
	result := make([]string, 0, len(strs))

	for _, str := range strs {
		if _, ok := seen[str]; !ok {
			seen[str] = struct{}{}
			result = append(result, str)
		}
	}

	return result
}

// joinStrings 连接字符串切片
func joinStrings(strs []string, sep string) string {
	return strings.Join(strs, sep)
}
