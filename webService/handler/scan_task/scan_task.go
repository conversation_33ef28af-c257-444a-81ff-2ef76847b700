package scan_task

import (
	"context"
	"fmt"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	asyncq "micro-service/pkg/queue_helper"
	pb "micro-service/webService/proto"
	"time"

	"gorm.io/gorm"
)

// UniqueUint64Slice 去重uint64切片
func UniqueUint64Slice(slice []uint64) []uint64 {
	if len(slice) == 0 {
		return slice
	}

	// 使用map去重
	seen := make(map[uint64]struct{}, len(slice))
	result := make([]uint64, 0, len(slice))

	for _, v := range slice {
		if _, ok := seen[v]; !ok {
			seen[v] = struct{}{}
			result = append(result, v)
		}
	}

	return result
}

// WithUserID 用户ID条件
func WithUserID(userID uint64) mysql.HandleFunc {
	return mysql.WithColumnValue("user_id", userID)
}

// WithColumnBetween 范围条件
func WithColumnBetween(column string, start, end interface{}) mysql.HandleFunc {
	return func(db *gorm.DB) {
		db.Where(column+" BETWEEN ? AND ?", start, end)
	}
}

// GetTaskList 获取扫描任务列表
func GetTaskList(ctx context.Context, req *pb.GetTaskListRequest, rsp *pb.GetTaskListResponse) error {
	// 创建查询构建器
	model := scan_task.NewScanTasksModel()

	// 构建查询条件
	var conditions []mysql.HandleFunc

	// 添加用户ID条件
	if req.UserId > 0 {
		conditions = append(conditions, WithUserID(uint64(req.UserId)))
	}

	// 添加企业ID条件
	//if req.CompanyId > 0 {
	//	conditions = append(conditions, mysql.WithColumnValue("company_id", req.CompanyId))
	//}

	// 添加任务类型条件
	if req.TaskType > 0 {
		conditions = append(conditions, mysql.WithColumnValue("task_type", req.TaskType))
	}

	// 添加状态条件 - 只有当字段不为-1时才添加
	if req.Status != -1 {
		conditions = append(conditions, mysql.WithColumnValue("status", req.Status))
	}

	// 添加操作人ID条件
	if req.OpId > 0 {
		conditions = append(conditions, mysql.WithColumnValue("op_id", req.OpId))
	}

	// 添加测绘任务ID条件
	if req.DetectAssetsTasksId > 0 {
		conditions = append(conditions, mysql.WithColumnValue("detect_assets_tasks_id", req.DetectAssetsTasksId))
	}

	// 添加是否为周期任务条件 - 只有当字段不为-1时才添加
	if req.Type != -1 {
		if req.Type == 0 {
			// 如果is_schedule=0，直接添加cron_id=0的条件
			conditions = append(conditions, mysql.WithColumnValue("type", 0))
		} else {
			// 如果is_schedule=1，添加cron_id>0的条件
			conditions = append(conditions, mysql.WithColumnValue("type", 1))
		}
	}

	// 添加任务名称条件
	if req.Name != "" {
		conditions = append(conditions, mysql.WithLRLike("name", req.Name))
	}

	// 添加创建时间范围条件
	if len(req.CreatedAtRange) == 2 {
		startTime, err := time.Parse("2006-01-02", req.CreatedAtRange[0])
		if err == nil {
			endTime, err := time.Parse("2006-01-02", req.CreatedAtRange[1])
			if err == nil {
				endTime = endTime.Add(24*time.Hour - time.Second) // 设置为当天的23:59:59
				conditions = append(conditions, WithColumnBetween("created_at", startTime, endTime))
			}
		}
	}

	// 添加下发时间范围条件
	if len(req.DispatchedAtRange) == 2 {
		startTime, err := time.Parse("2006-01-02", req.DispatchedAtRange[0])
		if err == nil {
			endTime, err := time.Parse("2006-01-02", req.DispatchedAtRange[1])
			if err == nil {
				endTime = endTime.Add(24*time.Hour - time.Second) // 设置为当天的23:59:59
				conditions = append(conditions, WithColumnBetween("start_at", startTime, endTime))
			}
		}
	}

	// 添加结束时间范围条件
	if len(req.EndAtRange) == 2 {
		startTime, err := time.Parse("2006-01-02", req.EndAtRange[0])
		if err == nil {
			endTime, err := time.Parse("2006-01-02", req.EndAtRange[1])
			if err == nil {
				endTime = endTime.Add(24*time.Hour - time.Second) // 设置为当天的23:59:59
				conditions = append(conditions, WithColumnBetween("end_at", startTime, endTime))
			}
		}
	}

	// 处理排序
	orderBy := ""
	if req.SortField != "" && req.SortOrder != "" {
		// 转换排序字段名称
		sortField := req.SortField
		if sortField == "created_at" {
			sortField = "created_at"
		} else if sortField == "dispatched_at" {
			sortField = "dispatched_at"
		} else if sortField == "end_at" {
			sortField = "end_at"
		} else if sortField == "status" {
			// 对于status字段，使用特殊的排序逻辑
			orderBy = "FIELD(is_audit, 0, 2, 1), FIELD(status, 1, 4, 0, 2, 3), end_at DESC, `order` ASC"
			if req.SortOrder == "desc" {
				// 如果是降序，则反转字段顺序
				orderBy = "FIELD(is_audit, 1, 2, 0), FIELD(status, 3, 2, 0, 4, 1), end_at ASC, `order` DESC"
			}
		} else {
			sortField = "`" + sortField + "`"
		}

		// 如果不是status字段的特殊排序，则使用常规排序
		if orderBy == "" {
			orderBy = sortField + " " + req.SortOrder
		}
	} else {
		// 默认排序
		orderBy = "FIELD(is_audit, 0, 2, 1), FIELD(status, 1, 4, 0, 2, 3), end_at DESC, `order` ASC"
	}

	// 获取总数
	count, err := model.Count(conditions...)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务列表] 获取总数失败: %v", err)
		return err
	}

	// 分页处理
	page := req.Page
	if page <= 0 {
		page = 1
	}
	perPage := req.PerPage
	if perPage <= 0 {
		perPage = 10
	}
	offset := (page - 1) * perPage

	// 添加分页和排序条件
	queryConditions := append([]mysql.HandleFunc{}, conditions...)
	queryConditions = append(queryConditions, func(db *gorm.DB) {
		db.Order(orderBy).Offset(int(offset)).Limit(int(perPage))
	})

	// 获取数据列表
	list, err := model.FindAll(queryConditions...)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务列表] 获取数据列表失败: %v", err)
		return err
	}

	// 获取用户信息
	var userIDs []uint64
	for _, task := range list {
		userIDs = append(userIDs, task.UserId)
		if task.OpId > 0 {
			userIDs = append(userIDs, uint64(task.OpId))
		}
	}

	// 去重
	userIDs = UniqueUint64Slice(userIDs)

	// 将uint64切片转换为[]uint切片
	uintIDs := make([]uint, 0, len(userIDs))
	for _, id := range userIDs {
		uintIDs = append(uintIDs, uint(id))
	}

	// 查询用户信息
	userModel := user.NewUserModel()
	userMap, err := userModel.FindByIds(uintIDs)
	if err != nil {
		log.WithContextErrorf(ctx, "查询用户信息失败: %v", err)
		return err
	}

	// 构建响应数据
	var items []*pb.TaskItem
	for _, task := range list {
		item := &pb.TaskItem{
			Id:                  int64(task.ID),
			UserId:              int64(task.UserId),
			CompanyId:           int64(task.CompanyId),
			Name:                task.Name,
			Bandwidth:           task.Bandwidth,
			Node:                task.Node,
			ProtocolConcurrency: task.ProtocolConcurrency,
			TaskType:            int32(task.TaskType),
			AssetType:           int32(task.AssetType),
			IpType:              int32(task.IpType),
			Type:                int32(task.Type),
			Progress:            fmt.Sprintf("%.2f", task.Progress), // 转换为字符串
			AssetNum:            task.AssetNum,
			ThreatNum:           task.ThreatNum,
			RuleNum:             task.RuleNum,
			Order:               int64(task.Order),
			PingSwitch:          int32(task.PingSwitch),
			WebLogoSwitch:       int32(task.WebLogoSwitch),
			UseSeconds:          task.UseSeconds,
			Status:              int32(task.Status),
			Step:                int32(task.Step),
			ScanType:            int32(task.ScanType),
			PocScanType:         int32(task.PocScanType),
			ScanRange:           int32(task.ScanRange),
			FileName:            task.FileName,
			TaskFrom:            int32(task.TaskFrom),
			CronId:              int32(task.CronId),
			OpId:                int64(task.OpId),
			IsAudit:             int32(task.IsAudit),
			ForbidStatus:        int32(task.ForbidStatus),
			Reason:              task.Reason,
			IsDefinePort:        int32(task.IsDefinePort),
			Flag:                task.Flag,
			ScanEngine:          task.ScanEngine,
			BatchId:             task.BatchId,
			IsTranscanner:       int32(task.IsTranscanner),
			TianrongxinTaskId:   task.TianrongxinTaskId,
			AssetsSyncNode:      task.AssetsSyncNode,
		}

		// 设置时间字段
		if task.StartAt != nil && !task.StartAt.IsZero() {
			item.StartAt = task.StartAt.Format("2006-01-02 15:04:05")
		}
		if task.EndAt != nil && !task.EndAt.IsZero() {
			item.EndAt = task.EndAt.Format("2006-01-02 15:04:05")
		}
		if task.PauseAt != nil && !task.PauseAt.IsZero() {
			item.PuaseAt = task.PauseAt.Format("2006-01-02 15:04:05")
		}
		if !task.CreatedAt.IsZero() {
			item.CreatedAt = task.CreatedAt.Format("2006-01-02 15:04:05")
		}
		if !task.UpdatedAt.IsZero() {
			item.UpdatedAt = task.UpdatedAt.Format("2006-01-02 15:04:05")
		}
		if !task.DeletedAt.Time.IsZero() {
			item.DeletedAt = task.DeletedAt.Time.Format("2006-01-02 15:04:05")
		}

		// 设置资产测绘任务ID
		if task.DetectAssetsTasksId != 0 {
			item.DetectAssetsTasksId = int64(task.DetectAssetsTasksId)
		}

		// 设置端口范围
		if task.PortRange != 0 {
			item.PortRange = fmt.Sprintf("%d", task.PortRange)
		}

		// 设置审核时间
		if task.AuditTime != "" {
			item.AuditTime = task.AuditTime
		}

		// 设置组织发现任务ID
		if task.OrganizationDiscoverTaskId != nil {
			item.OrganizationDiscoverTaskId = fmt.Sprintf("%d", *task.OrganizationDiscoverTaskId)
		}

		// 设置用户信息
		if user, ok := userMap[task.UserId]; ok {
			item.User = &pb.UserInfo{
				Id:   int64(user.Id),
				Name: user.Name.String,
			}
		}

		// 设置操作人信息
		if task.OpId > 0 {
			if op, ok := userMap[uint64(task.OpId)]; ok {
				item.Op = &pb.UserInfo{
					Id:   int64(op.Id),
					Name: op.Name.String,
				}
			}
		}

		items = append(items, item)
	}

	// 获取等待中的任务数量
	waitNum, err := model.Count(
		mysql.WithColumnValue("status", scan_task.StatusWaiting),
		mysql.WithColumnValue("task_type", scan_task.TASK_TYPE_ASSET),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "查询等待中的任务数量失败: %v", err)
		waitNum = 0
	}

	// 设置响应数据
	rsp.Total = int32(count)
	rsp.PerPage = perPage
	rsp.CurrentPage = page
	rsp.LastPage = int32((count + int64(perPage) - 1) / int64(perPage))
	rsp.From = int32(offset + 1)
	rsp.To = int32(offset) + int32(len(items))
	rsp.WaitNum = int32(waitNum)
	rsp.Items = items

	return nil
}

// DeleteTask 删除扫描任务
func DeleteTask(ctx context.Context, req *pb.DeleteTaskRequest) error {
	log.WithContextInfof(ctx, "[删除扫描任务] 开始删除扫描任务")
	log.WithContextInfof(ctx, "[删除扫描任务] 请求参数 - UserId: %d, TaskType: %d, Status: %d", req.UserId, req.TaskType, req.Status)
	log.WithContextInfof(ctx, "[删除扫描任务] 请求参数 - Name: %s, IsSchedule: %d", req.Name, req.IsSchedule)
	log.WithContextInfof(ctx, "[删除扫描任务] 请求参数 - CreatedAtRange: %v", req.CreatedAtRange)
	log.WithContextInfof(ctx, "[删除扫描任务] 请求参数 - DispatchedAtRange: %v", req.DispatchedAtRange)
	log.WithContextInfof(ctx, "[删除扫描任务] 请求参数 - Id: %v", req.Id)

	// 创建模型实例
	model := scan_task.NewScanTasksModel()
	db := mysql.GetDbClient()

	// 准备查询条件
	var conditions []mysql.HandleFunc

	// 添加用户ID条件
	conditions = append(conditions, mysql.WithColumnValue("user_id", req.UserId))
	log.WithContextInfof(ctx, "[删除扫描任务] 添加用户ID条件: %d", req.UserId)

	// 添加任务类型条件
	conditions = append(conditions, mysql.WithColumnValue("task_type", req.TaskType))
	log.WithContextInfof(ctx, "[删除扫描任务] 添加任务类型条件: %d", req.TaskType)

	// 添加状态条件 - 只有当字段不为-1时才添加
	if req.Status != -1 {
		conditions = append(conditions, mysql.WithColumnValue("status", req.Status))
		log.WithContextInfof(ctx, "[删除扫描任务] 添加状态条件: %d", req.Status)
	} else {
		log.WithContextInfof(ctx, "[删除扫描任务] 跳过状态条件 (值为-1)")
	}

	// 添加任务名称条件
	if req.Name != "" {
		conditions = append(conditions, mysql.WithLRLike("name", req.Name))
		log.WithContextInfof(ctx, "[删除扫描任务] 添加任务名称条件: %s", req.Name)
	} else {
		log.WithContextInfof(ctx, "[删除扫描任务] 跳过任务名称条件 (为空)")
	}

	// 添加创建时间范围条件
	if len(req.CreatedAtRange) == 2 {
		startTime, err := time.Parse("2006-01-02", req.CreatedAtRange[0])
		if err == nil {
			endTime, err := time.Parse("2006-01-02", req.CreatedAtRange[1])
			if err == nil {
				endTime = endTime.Add(24*time.Hour - time.Second) // 设置为当天的23:59:59
				conditions = append(conditions, mysql.WithBetween("created_at", startTime, endTime))
				log.WithContextInfof(ctx, "[删除扫描任务] 添加创建时间范围条件: %s 到 %s", startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
			} else {
				log.WithContextErrorf(ctx, "[删除扫描任务] 解析结束时间失败: %v", err)
			}
		} else {
			log.WithContextErrorf(ctx, "[删除扫描任务] 解析开始时间失败: %v", err)
		}
	} else {
		log.WithContextInfof(ctx, "[删除扫描任务] 跳过创建时间范围条件 (长度: %d)", len(req.CreatedAtRange))
	}

	// 添加下发时间范围条件
	if len(req.DispatchedAtRange) == 2 {
		startTime, err := time.Parse("2006-01-02", req.DispatchedAtRange[0])
		if err == nil {
			endTime, err := time.Parse("2006-01-02", req.DispatchedAtRange[1])
			if err == nil {
				endTime = endTime.Add(24*time.Hour - time.Second) // 设置为当天的23:59:59
				conditions = append(conditions, mysql.WithBetween("start_at", startTime, endTime))
				log.WithContextInfof(ctx, "[删除扫描任务] 添加下发时间范围条件: %s 到 %s", startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
			} else {
				log.WithContextErrorf(ctx, "[删除扫描任务] 解析下发结束时间失败: %v", err)
			}
		} else {
			log.WithContextErrorf(ctx, "[删除扫描任务] 解析下发开始时间失败: %v", err)
		}
	} else {
		log.WithContextInfof(ctx, "[删除扫描任务] 跳过下发时间范围条件 (长度: %d)", len(req.DispatchedAtRange))
	}

	// 添加是否为周期任务条件 - 只有当字段不为-1时才添加
	if req.Type != -1 {
		if req.Type == 0 {
			// 如果is_schedule=0，直接添加cron_id=0的条件
			conditions = append(conditions, mysql.WithColumnValue("type", 0))
		} else {
			// 如果is_schedule=1，添加cron_id>0的条件
			conditions = append(conditions, mysql.WithColumnValue("type", 1))
		}
	}

	// 声明 tasks 变量，用于后续资产测绘任务处理
	var tasks []scan_task.ScanTasks

	// 如果有指定任务ID，则按ID删除
	if len(req.Id) > 0 {
		// 删除指定ID的任务
		for _, id := range req.Id {
			// 查询任务信息
			var task scan_task.ScanTasks
			err := db.Where("user_id = ? AND id = ?", req.UserId, id).First(&task).Error
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					continue
				}
				log.WithContextErrorf(ctx, "[删除扫描任务] 查询任务失败: %v", err)
				return err
			}

			// 将任务添加到 tasks 列表中，用于后续处理
			tasks = append(tasks, task)

			// 如果是资产扫描任务
			if req.TaskType == scan_task.TASK_TYPE_ASSET {
				// 如果任务正在运行且进度小于41%，停止任务
				if task.Status == scan_task.StatusDoing && task.Progress < 41 {
					// 调用停止任务的方法 - 实际项目中需要实现此方法
					if cfg.ExecGolangJob() {
						//todo 调用go的job
						err := asyncq.DispatchStopScanJob.Dispatch(task.ID)
						if err != nil {
							log.WithContextErrorf(ctx, "[删除扫描任务] 调用停止扫描任务失败: %v", err)
						} else {
							log.WithContextInfof(ctx, "[删除扫描任务] 成功调用停止扫描任务: %v", task.ID)
						}
					} else {
						// 调用PHP的停止扫描任务job
						err := asyncq.DispatchStopScanJob.Dispatch(task.ID)
						if err != nil {
							log.WithContextErrorf(ctx, "[删除扫描任务] 调用停止扫描任务失败: %v", err)
						} else {
							log.WithContextInfof(ctx, "[删除扫描任务] 成功调用停止扫描任务: %v", task.ID)
						}
					}
					log.WithContextInfof(ctx, "[删除扫描任务] 停止资产扫描任务: %v", task.ID)
				}
			} else {
				// 如果是漏洞扫描任务，且任务正在运行
				if task.Status == scan_task.StatusDoing {
					// 调用停止漏洞扫描任务的方法 - 实际项目中需要实现此方法
					// 在PHP中对应的是 app('bmh.scan.poc')->stop($task['id'])
					log.WithContextInfof(ctx, "[删除扫描任务] 停止漏洞扫描任务: %v", task.ID)
				}
			}

			// 删除任务
			err = db.Delete(&task).Error
			if err != nil {
				log.WithContextErrorf(ctx, "[删除扫描任务] 删除任务失败: %v", err)
				return err
			}

			log.WithContextInfof(ctx, "[删除扫描任务] 成功删除任务: %v", task.ID)
		}
	} else {
		// 按条件查询任务列表
		var err error

		// 打印SQL调试信息
		log.WithContextInfof(ctx, "[删除扫描任务] 查询条件数量: %d", len(conditions))

		// 创建一个临时的DB实例来打印SQL
		debugDB := mysql.GetDbClient().Session(&gorm.Session{DryRun: true})
		for i, condition := range conditions {
			condition(debugDB)
			log.WithContextInfof(ctx, "[删除扫描任务] 条件 %d 应用后的SQL: %s, 参数: %v", i+1, debugDB.Statement.SQL.String(), debugDB.Statement.Vars)
		}

		// 构建完整的查询SQL用于调试
		debugQuery := mysql.GetDbClient().Session(&gorm.Session{DryRun: true}).Model(&scan_task.ScanTasks{})
		for _, condition := range conditions {
			condition(debugQuery)
		}
		log.WithContextInfof(ctx, "[删除扫描任务] 完整查询SQL: %s", debugQuery.Statement.SQL.String())
		log.WithContextInfof(ctx, "[删除扫描任务] 完整查询参数: %v", debugQuery.Statement.Vars)

		tasks, err = model.FindAll(conditions...)
		if err != nil {
			log.WithContextErrorf(ctx, "[删除扫描任务] 查询任务列表失败: %v", err)
			return err
		}

		log.WithContextInfof(ctx, "[删除扫描任务] 查询到的任务数量: %d", len(tasks))

		// 遍历任务列表并删除
		for _, task := range tasks {
			// 如果是资产扫描任务
			if req.TaskType == scan_task.TASK_TYPE_ASSET {
				// 如果任务正在运行且进度小于41%，停止任务
				if task.Status == scan_task.StatusDoing && task.Progress < 41 {
					// 调用停止任务的方法 - 实际项目中需要实现此方法
					if cfg.ExecGolangJob() {
						//todo 调用go的job
						err := asyncq.DispatchStopScanJob.Dispatch(task.ID)
						if err != nil {
							log.WithContextErrorf(ctx, "[删除扫描任务] 调用停止扫描任务失败: %v", err)
						} else {
							log.WithContextInfof(ctx, "[删除扫描任务] 成功调用停止扫描任务: %v", task.ID)
						}
					} else {
						// 调用PHP的停止扫描任务job
						err := asyncq.DispatchStopScanJob.Dispatch(task.ID)
						if err != nil {
							log.WithContextErrorf(ctx, "[删除扫描任务] 调用停止扫描任务失败: %v", err)
						} else {
							log.WithContextInfof(ctx, "[删除扫描任务] 成功调用停止扫描任务: %v", task.ID)
						}
					}
					log.WithContextInfof(ctx, "[删除扫描任务] 停止资产扫描任务: %v", task.ID)
				}
			} else {
				// 如果是漏洞扫描任务，且任务正在运行
				if task.Status == scan_task.StatusDoing {
					// 调用停止漏洞扫描任务的方法 - 实际项目中需要实现此方法
					// 在PHP中对应的是 app('bmh.scan.poc')->stop($task['id'])
					log.WithContextInfof(ctx, "[删除扫描任务] 停止漏洞扫描任务: %v", task.ID)
				}
			}

			// 删除任务
			err = db.Delete(&task).Error
			if err != nil {
				log.WithContextErrorf(ctx, "[删除扫描任务] 删除任务失败: %v", err)
				return err
			}

			log.WithContextInfof(ctx, "[删除扫描任务] 成功删除任务: %v", task.ID)
		}
	}

	// 处理资产测绘任务相关逻辑
	if req.TaskType == scan_task.TASK_TYPE_ASSET {
		// 获取被删除任务的ID列表
		var allDelIds []uint64

		// 如果有指定任务ID，则使用指定的ID
		if len(req.Id) > 0 {
			for _, id := range req.Id {
				allDelIds = append(allDelIds, uint64(id))
			}
		} else {
			// 否则，查询刚才被删除的任务ID（通过重新查询符合条件的任务）
			// 注意：这里需要查询刚才删除的任务，但由于已经删除，我们需要从之前查询的任务列表中获取
			for _, task := range tasks {
				allDelIds = append(allDelIds, uint64(task.ID))
			}
		}

		// 如果有删除的任务，检查是否需要更新测绘任务状态
		if len(allDelIds) > 0 {
			// 查询关联的测绘任务ID
			var detectTaskIds []scan_task.ScanTasks
			err := db.Unscoped().Where("user_id = ? AND id IN (?) AND detect_assets_tasks_id IS NOT NULL",
				req.UserId, allDelIds).Find(&detectTaskIds).Error
			if err != nil {
				log.WithContextErrorf(ctx, "[删除扫描任务] 查询关联测绘任务失败: %v", err)
				return err
			}

			// 获取唯一的测绘任务ID
			uniqueDetectTaskIds := make(map[int]bool)
			var detectAssetsTasksIds []int

			for _, task := range detectTaskIds {
				if task.DetectAssetsTasksId > 0 && !uniqueDetectTaskIds[task.DetectAssetsTasksId] {
					uniqueDetectTaskIds[task.DetectAssetsTasksId] = true
					detectAssetsTasksIds = append(detectAssetsTasksIds, task.DetectAssetsTasksId)
				}
			}

			// 处理每个测绘任务
			for _, detectTaskId := range detectAssetsTasksIds {
				// 查询测绘任务信息
				detectTaskModel := detect_assets_tasks.NewModel()
				detectTask, err := detectTaskModel.First(
					mysql.WithColumnValue("id", detectTaskId),
					mysql.WithColumnValue("user_id", req.UserId),
				)
				if err != nil {
					if err == gorm.ErrRecordNotFound {
						log.WithContextInfof(ctx, "[删除扫描任务] 测绘任务不存在: %v", detectTaskId)
						continue
					}
					log.WithContextErrorf(ctx, "[删除扫描任务] 查询测绘任务失败: %v", err)
					continue
				}

				// 检查detectTask是否为nil
				if detectTask == nil {
					log.WithContextInfof(ctx, "[删除扫描任务] 测绘任务为空: %v", detectTaskId)
					continue
				}

				// 如果测绘任务状态为进行中
				if detectTask.Status == detect_assets_tasks.StatusDoing {
					// 检查是否还有未完成的扫描任务
					var count int64
					err := db.Model(&scan_task.ScanTasks{}).Where(
						"user_id = ? AND detect_assets_tasks_id = ? AND status IN (?)",
						req.UserId, detectTaskId,
						[]int{scan_task.StatusWaiting, scan_task.StatusDoing, scan_task.StatusPause},
					).Count(&count).Error
					if err != nil {
						log.WithContextErrorf(ctx, "[删除扫描任务] 查询未完成任务失败: %v", err)
						continue
					}

					// 如果没有未完成的任务，标记测绘任务为异常完成
					if count == 0 {
						// 更新测绘任务状态
						err = detectTaskModel.UpdateAny(map[string]interface{}{
							"status":      detect_assets_tasks.StatusExceptionFinished,
							"step_status": detect_assets_tasks.StepStatusDone,
							"progress":    100,
						}, mysql.WithColumnValue("id", detectTaskId), mysql.WithColumnValue("user_id", req.UserId))
						if err != nil {
							log.WithContextErrorf(ctx, "[删除扫描任务] 更新测绘任务状态失败: %v", err)
							continue
						}

						log.WithContextInfof(ctx, "[删除扫描任务] 更新测绘任务状态为异常完成: %v", detectTaskId)
					}
				}
			}
		}
	}

	// 记录操作日志
	err := asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(req.UserId),
		"删除扫描任务",
		metadata.MustString(ctx, "client_ip"),
		uint64(req.CompanyId),
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "[删除扫描任务] 记录操作日志失败: %v", err)
	}

	return nil
}
