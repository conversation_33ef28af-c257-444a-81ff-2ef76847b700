package scan_task

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/middleware/mysql/scan_task_domains"
	"net"
	"slices"
	"strconv"
	"strings"
	"time"

	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/forbid"
	"micro-service/middleware/mysql/port_group"
	"micro-service/middleware/mysql/task"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dns"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	"micro-service/pkg/network"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

// CreateScanTask 创建扫描任务 - 完全按照PHP scansCreate方法实现
func CreateScanTask(ctx context.Context, req *pb.CreateScanTaskRequest, rsp *pb.CreateScanTaskResponse) error {
	log.WithContextInfof(ctx, "[创建扫描任务] 开始创建扫描任务，用户ID: %d", req.UserId)

	// 1. 参数验证 - 对应PHP的validate
	isPortRange := req.PortRange == 1
	if !isPortRange && (req.ScanRange <= 3 && len(req.Ips) == 0) {
		return errors.New("扫描目标ip为空，请检查")
	}

	// 2. 获取用户ID和企业ID - 对应PHP的getOpTargetId
	userID := req.UserId
	companyID := req.CompanyId

	// 3. 权限和限制检查 - 对应PHP的资产限制检查逻辑
	err := checkScanLimits(ctx, uint64(userID), companyID, req.TaskType)
	if err != nil {
		return err
	}

	// 4. 安服扫描标记 - 对应PHP的task_from逻辑
	taskFrom := int8(0)
	currentUserID := metadata.MustInt64(ctx, "user_id")
	if userID != currentUserID {
		taskFrom = 1
	}

	// 5. 构建scan_task参数 - 对应PHP的$scanTask数组
	maxOrder, _ := mysql.NewDSL[task.Task]().MaxByParams("order", [][]interface{}{})

	scanTask := &task.Task{
		UserID:              uint64(userID),
		CompanyID:           utils.ToPointer(uint64(companyID)),
		Name:                req.Name,
		Bandwidth:           strconv.Itoa(int(req.Bandwidth)),
		ProtocolConcurrency: strconv.Itoa(int(req.ProtocolConcurrency)),
		PingSwitch:          int8(req.PingSwitch),
		WebLogoSwitch:       int8(req.WebLogoSwitch),
		ScanType:            int8(req.ScanType),
		ScanRange:           utils.ToPointer(int8(req.ScanRange)),
		TaskType:            int8(req.TaskType),
		Order:               uint64(maxOrder) + 1,
		OpID:                currentUserID,
		TaskFrom:            taskFrom,
	}
	// 原PHP中0 empty，0是否，golang中需要直接判断
	if req.ProtocolConcurrency == 0 {
		scanTask.ProtocolConcurrency = strconv.Itoa(int(req.Bandwidth / 8))
	}

	if req.IsDefinePort == 1 {
		scanTask.IsDefinePort = int8(req.IsDefinePort)
	}

	if isPortRange {
		scanTask.PortRange = utils.ToPointer(int8(req.PortRange))
	}

	if req.TaskType == 1 {
		scanTask.IPType = int8(req.IpType)
	}

	// 6. 端口参数处理 - 对应PHP的端口验证逻辑
	var dataPorts []uint64
	err = validatePorts(req, &dataPorts)
	if err != nil {
		return err
	}

	// 7. 处理扫描目标 - 对应PHP的switch ($param['scan_range'])逻辑
	var finalIPs []string
	var domainDnsArr []string
	var errDomain []string

	isIPv6 := req.IpType == 2

	switch req.ScanRange {
	case 0, 1, 3: // INPUT_IP, FILTER_RANGE_IP, FILE_IP
		finalIPs, errDomain, err = processInputIPs(req.Ips, req.IpType)
		if err != nil {
			return err
		}

		// 如果有错误IP，返回错误信息
		if len(errDomain) > 0 {
			return errors.New("IP格式验证失败: " + strings.Join(errDomain, ","))
		}

		if len(finalIPs) == 0 && !isPortRange {
			return errors.New("扫描目标ip为空，请重新下发扫描任务")
		}

	case 2, 4, 5: // TABLE_IP - 台账IP
		finalIPs, err = getTableIPs(ctx, uint64(userID), isIPv6)
		if err != nil {
			return err
		}

		if len(finalIPs) == 0 && !isPortRange {
			return errors.New("扫描目标ip为空，请重新下发扫描任务")
		}

	case 6: // DOMAIN_IP - 域名
		finalIPs, domainDnsArr, errDomain, err = processDomains(req.Ips, req.IpType)
		if err != nil {
			return err
		}

		if len(errDomain) > 0 {
			return errors.New("域名解析失败: " + strings.Join(errDomain, ","))
		}
	}

	// 8. IP格式校验（资产扫描）- 对应PHP的checkIpV6/checkIpV4
	err = validateIPFormat(finalIPs, req.TaskType, req.IpType)
	if err != nil {
		return err
	}

	// 9. 安全检查 - 内网IP和禁扫IP
	err = performSecurityChecks(ctx, finalIPs, uint64(userID), isIPv6)
	if err != nil {
		return err
	}

	// 10. 检查IP数量 - 对应PHP的count($Ips) == 0检查
	if len(finalIPs) == 0 && !isPortRange {
		if len(errDomain) > 0 {
			return errors.New("域名解析失败: " + strings.Join(errDomain, ","))
		}
		return errors.New("系统过滤扫描目标以后，扫描目标为空，没有有效扫描目标，请重新下发扫描任务")
	}

	// 11. 资产扫描限制和企业台账限制 - 对应PHP的资产扫描数量限制
	var warnMessage string
	if req.TaskType == 1 {
		finalIPs, warnMessage, err = checkAssetLimits(ctx, req, finalIPs, companyID, isPortRange, isIPv6)
		if err != nil {
			return err
		}
	}

	// 12. 自定义端口验证 - 对应PHP的自定义端口逻辑
	err = validateDefinePorts(req, scanTask)
	if err != nil {
		return err
	}

	// 13. 创建任务记录 - 对应PHP的Task::query()->insertGetId($scanTask)
	err = task.NewModel().Create(scanTask)
	if err != nil {
		return fmt.Errorf("创建扫描任务失败: %v", err)
	}

	taskID := int64(scanTask.ID)

	// 14. 插入相关数据
	err = insertTaskData(ctx, taskID, finalIPs, domainDnsArr, req, dataPorts)
	if err != nil {
		return err
	}

	// 15. 更新ES资产状态 - 对应PHP的IpAssets和ForadarAssets更新
	if len(finalIPs) > 0 && req.TaskType == 1 {
		// 对应PHP的spliteIp($Ips,$param['ip_type'])
		updateIPs := network.SplitIP(finalIPs, req.IpType == 2) // true for IPv6
		if len(updateIPs) > 0 {
			log.WithContextInfof(ctx, "[创建扫描任务] 开始更新ES资产状态，IP数量: %d", len(updateIPs))

			// 对应PHP的IpAssets::query()->where()->whereIn()->update()
			var builder1 elastic.SearchBuilder
			builder1.AddMust([]interface{}{"user_id", req.UserId})
			builder1.AddMust([]interface{}{"status", "in", []interface{}{0, 2, 3}}) // STATUS_DEFAULT, STATUS_THREATEN, STATUS_IGNORE
			builder1.AddMust([]interface{}{"ip", "in", elastic.ToInterfaceArray(updateIPs)})

			updateData := map[string]interface{}{
				"status": 1, // STATUS_CLAIMED
			}

			query1 := builder1.Build()
			err := elastic.UpdateByParams[fofaee_assets.FofaeeAssets](query1, updateData)
			if err != nil {
				log.WithContextErrorf(ctx, "[创建扫描任务] 更新IpAssets状态失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "[创建扫描任务] 成功更新IpAssets状态")
			}

			// 对应PHP的ForadarAssets::query()->where()->whereIn()->update()
			var builder2 elastic.SearchBuilder
			builder2.AddMust([]interface{}{"user_id", req.UserId})
			builder2.AddMust([]interface{}{"status", "in", []interface{}{0, 2, 3}}) // STATUS_DEFAULT, STATUS_THREATEN, STATUS_IGNORE
			builder2.AddMust([]interface{}{"ip", "in", elastic.ToInterfaceArray(updateIPs)})

			query2 := builder2.Build()
			err = elastic.UpdateByParams[foradar_assets.ForadarAsset](query2, updateData)
			if err != nil {
				log.WithContextErrorf(ctx, "[创建扫描任务] 更新ForadarAssets状态失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "[创建扫描任务] 成功更新ForadarAssets状态")
			}
		}
	}

	// 16. 下发MQ任务 - 对应PHP的ScanAssetMqJob::publish
	err = publishScanTask(ctx, taskID, uint64(userID))
	if err != nil {
		log.WithContextErrorf(ctx, "[创建扫描任务] 下发MQ任务失败: %v", err)
	}

	// 17. 域名资产同步 - 对应PHP的TableAssetsDoaminsSync
	if len(domainDnsArr) > 0 {
		log.WithContextInfof(ctx, "[创建扫描任务] 需要同步域名资产: %v", domainDnsArr)
		if cfg.ExecGolangJob() {
			//todo 调用go的job
			// 对应PHP: TableAssetsDoaminsSync::dispatch($user_id,null,DomainAssets::DOMAIN_SCAN,null,null,$domainArr);
			_ = asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
				req.UserId,   // user_id
				nil,          // task_id (null)
				6,            // from (DomainAssets::DOMAIN_SCAN = 6)
				nil,          // groupId (null)
				nil,          // domain_task_id (null)
				domainDnsArr, // import_domains ($domainArr)
				nil,          // flag (null)
				nil,          // detect_task_id (null)
				nil,          // organization_discover_task_id (null)
			)
		} else {
			// 调用php的域名同步job
			// 对应PHP: TableAssetsDoaminsSync::dispatch($user_id,null,DomainAssets::DOMAIN_SCAN,null,null,$domainArr);
			_ = asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
				req.UserId,   // user_id
				nil,          // task_id (null)
				6,            // from (DomainAssets::DOMAIN_SCAN = 6)
				nil,          // groupId (null)
				nil,          // domain_task_id (null)
				domainDnsArr, // import_domains ($domainArr)
				nil,          // flag (null)
				nil,          // detect_task_id (null)
				nil,          // organization_discover_task_id (null)
			)
		}
	}

	// 18. 异步同步到域名资产里面 - 对应PHP的ScanTaskDomians插入逻辑
	if len(domainDnsArr) > 0 {
		domains := strings.Join(domainDnsArr, ",")
		scanTaskDomains := scan_task_domains.ScanTaskDomains{
			UserId:    uint64(req.UserId),
			CompanyId: strconv.FormatInt(req.CompanyId, 10),
			Domains:   domains,
			TaskId:    uint64(taskID),
		}

		scanTaskDomainsModel := scan_task_domains.NewScanTaskDomainsModel()
		err = scanTaskDomainsModel.Create(&scanTaskDomains)
		if err != nil {
			log.WithContextErrorf(ctx, "[创建扫描任务] 插入scan_task_domains失败: %v", err)
			// 不返回错误，只记录日志，避免影响主流程
		} else {
			log.WithContextInfof(ctx, "[创建扫描任务] 成功插入scan_task_domains，user_id=%d, task_id=%d, domains=%s",
				req.UserId, taskID, domains)
		}
	}

	// 19. 返回结果 - 对应PHP的response()->success
	rsp.TaskId = taskID
	rsp.WarnMessage = warnMessage

	log.WithContextInfof(ctx, "[创建扫描任务] 任务创建成功，任务ID: %d", taskID)
	return nil
}

// checkScanLimits 检查扫描权限和限制 - 对应PHP的权限检查逻辑
func checkScanLimits(ctx context.Context, userID uint64, companyID int64, taskType int32) error {
	// 本地化客户，资产限制单独算
	if cfg.IsLocalClient() {
		if companyID > 0 {
			// 资产扫描限制
			if companyID > 0 && taskType == 1 {
				limit, err := company.CheckLimitById(companyID, "ip_asset", 1)
				if err != nil {
					return errors.New("校验授权限制失败")
				}
				if limit < 0 {
					return errors.New("您的台账数据已经超过限制，请联系售后人员！")
				}
			}
		} else {
			// 安服或者管理员
			// 对应PHP的IpAssets::query()->where('user_id',$user_id)->whereIn('status',[1,4])->count()
			var builder elastic.SearchBuilder
			builder.AddMust([]interface{}{"user_id", userID})
			builder.AddMust([]interface{}{"status", "in", []interface{}{1, 4}}) // CLAIMED, UPLOAD

			query := builder.Build()
			// 使用All方法获取数据然后计算数量
			assets, err := elastic.All[fofaee_assets.FofaeeAssets](1000, elastic.ParseQuery(query), nil)
			if err != nil {
				return fmt.Errorf("查询已使用台账数量失败: %v", err)
			}
			usedNum := len(assets)

			// 对应PHP的User::query()->where('id',$user_id)->value('limit_ip_asset')
			userInfo, err := mysql.NewDSL[user.User]().FindByParams([][]interface{}{
				{"id", "=", userID},
			})
			if err != nil {
				return fmt.Errorf("查询用户信息失败: %v", err)
			}

			if userInfo.Id == 0 {
				return errors.New("用户不存在")
			}

			// 对应PHP的$canScanIpNum = $limit_ip_asset - $usedNum
			limitIPAsset, _ := strconv.ParseInt(userInfo.LimitIPAsset, 10, 64)
			canScanIPNum := limitIPAsset - int64(usedNum)
			if canScanIPNum <= 0 {
				return errors.New("台账扫描数量已经超过授权数量，无法下发扫描任务")
			}

			log.WithContextInfof(ctx, "[创建扫描任务] 用户[%d]授权数量检查通过，已用: %d, 限制: %d, 可用: %d",
				userID, usedNum, limitIPAsset, canScanIPNum)
		}
	} else {
		// 资产扫描限制
		if companyID > 0 && taskType == 1 {
			limit, err := company.CheckLimitById(companyID, "ip_asset", 1)
			if err != nil {
				return errors.New("校验授权限制失败")
			}
			if limit < 0 {
				return errors.New("您的台账数据已经超过限制，请联系售后人员！")
			}
		}
	}

	return nil
}

// validatePorts 验证端口配置 - 对应PHP的端口验证逻辑
func validatePorts(req *pb.CreateScanTaskRequest, dataPorts *[]uint64) error {
	if req.TaskType == 1 { // 资产扫描
		// 端口参数验证
		if len(req.PortIds) > 0 {
			if len(req.PortIds) > 50 {
				return errors.New("添加端口不能超过50个")
			}
			for _, portID := range req.PortIds {
				*dataPorts = append(*dataPorts, uint64(portID))
			}
		} else {
			// 使用的端口分组
			if req.PortGroupIds == 0 && req.IsDefinePort == 0 && req.PortRange != 1 {
				return errors.New("资产扫描，请至少指定一个端口或分组")
			}

			if req.IsDefinePort == 0 && req.PortRange != 1 {
				// 对应PHP的$dataPorts = $param['port_group_ids'];
				*dataPorts = []uint64{uint64(req.PortGroupIds)}

				// 对应PHP的PortGroup::query()->where('id', $param['port_group_ids'])->isAllPort()
				portGroupInfo, err := mysql.NewDSL[port_group.PortGroup]().FindByParams([][]interface{}{
					{"id", "=", req.PortGroupIds},
				})
				if err != nil {
					return fmt.Errorf("查询端口分组失败: %v", err)
				}

				// 对应PHP的scopeIsAllPort: object_get($query->first(), 'name') == '0-65535'
				isAllPort := portGroupInfo.Name == "0-65535"

				// 对应PHP的PortPortGroup::query()->where('port_group_id', $param['port_group_ids'])->count()
				portPortGroupCount, err := mysql.NewDSL[port_group.PortPortGroup]().CountByParams([][]interface{}{
					{"port_group_id", "=", req.PortGroupIds},
				})
				if err != nil {
					return fmt.Errorf("查询端口分组关联数据失败: %v", err)
				}

				// 对应PHP的!PortPortGroup::query()->count() && !$isAllPort
				if portPortGroupCount == 0 && !isAllPort {
					return errors.New("端口分组没有端口数据请检查!")
				}

				log.Infof("[创建扫描任务] 端口分组ID: %d, 是否全端口: %v, 关联端口数: %d", req.PortGroupIds, isAllPort, portPortGroupCount)
			}
		}
	}

	return nil
}

// processInputIPs 处理输入的IP列表 - 对应PHP的IP验证逻辑
func processInputIPs(ips []string, ipType int32) ([]string, []string, error) {
	var finalIPs []string
	var errDomain []string

	finalIPs = ips

	// 完整的IP格式验证逻辑 - 对应PHP的IP验证
	if ipType == 1 { // IPv4
		for _, checkIP := range finalIPs {
			if strings.Contains(checkIP, "-") || strings.Contains(checkIP, "/") {
				// 校验所有IPv4格式
				if strings.Contains(checkIP, "/") {
					// CIDR格式 **********/24
					parts := strings.Split(checkIP, "/")
					if len(parts) != 2 || net.ParseIP(parts[0]).To4() == nil {
						errDomain = append(errDomain, checkIP)
						continue
					}
					if prefix, err := strconv.Atoi(parts[1]); err != nil || prefix > 32 || prefix < 0 {
						errDomain = append(errDomain, checkIP)
						continue
					}
				} else if strings.Contains(checkIP, "*") {
					// 网段格式 192.168.1-10.*
					parts := strings.Split(checkIP, "-")
					if len(parts) != 2 || net.ParseIP(parts[0]+".1").To4() == nil {
						errDomain = append(errDomain, checkIP)
						continue
					}
					endParts := strings.Split(parts[1], ".")
					if len(endParts) == 0 {
						errDomain = append(errDomain, checkIP)
						continue
					}
					endNum := strings.Replace(endParts[0], "*", "", -1)
					if num, err := strconv.Atoi(endNum); err != nil || num > 255 || num < 0 {
						errDomain = append(errDomain, checkIP)
						continue
					}
				} else {
					// 范围格式 ***********-50
					parts := strings.Split(checkIP, "-")
					if len(parts) != 2 || net.ParseIP(parts[0]).To4() == nil {
						errDomain = append(errDomain, checkIP)
						continue
					}
					if strings.Contains(parts[1], ".") {
						if net.ParseIP(parts[1]).To4() == nil {
							errDomain = append(errDomain, checkIP)
							continue
						}
					} else {
						if num, err := strconv.Atoi(parts[1]); err != nil || num > 255 || num < 0 {
							errDomain = append(errDomain, checkIP)
							continue
						}
					}
				}
			} else {
				// 单个IP验证
				if net.ParseIP(checkIP).To4() == nil {
					errDomain = append(errDomain, checkIP)
					continue
				}
			}
			// IP验证通过
		}
	} else { // IPv6
		for _, checkIP := range finalIPs {
			if strings.Contains(checkIP, "-") || strings.Contains(checkIP, "/") {
				continue
			} else {
				parsedIP := net.ParseIP(checkIP)
				if parsedIP == nil || parsedIP.To4() != nil {
					errDomain = append(errDomain, checkIP)
					continue
				}
				// IP验证通过
			}
		}
	}

	return finalIPs, errDomain, nil
}

// getTableIPs 从ES获取台账IP - 对应PHP的ForadarAssets查询
func getTableIPs(ctx context.Context, userID uint64, isIPv6 bool) ([]string, error) {
	var builder elastic.SearchBuilder
	builder.AddMust([]interface{}{"user_id", userID})
	builder.AddMust([]interface{}{"status", "in", []interface{}{1, 4}}) // CLAIMED, UPLOAD
	builder.AddMust([]interface{}{"is_ipv6", isIPv6})

	query := builder.Build()
	assets, err := elastic.All[foradar_assets.ForadarAsset](1000, elastic.ParseQuery(query), nil)
	if err != nil {
		return nil, err
	}

	var ips []string
	seen := make(map[string]bool)
	for _, asset := range assets {
		if asset.Ip != "" && !seen[asset.Ip] {
			// 手动过滤IP类型，防止ES中标记错误的数据
			assetIsIPv6 := strings.Contains(asset.Ip, ":")
			if isIPv6 && !assetIsIPv6 {
				// 需要IPv6但资产是IPv4，跳过
				continue
			}
			if !isIPv6 && assetIsIPv6 {
				// 需要IPv4但资产是IPv6，跳过
				continue
			}

			ips = append(ips, asset.Ip)
			seen[asset.Ip] = true
		}
	}

	return ips, nil
}

// processDomains 处理域名解析 - 对应PHP的域名解析逻辑
func processDomains(domains []string, ipType int32) ([]string, []string, []string, error) {
	// 域名数量限制
	if len(domains) > 200 {
		return nil, nil, nil, errors.New("单次任务不可以超过200个域名！")
	}

	var ips []string
	var domainDnsArr []string
	var errDomain []string

	// 域名扫描 - 对应PHP的域名解析逻辑
	for _, domain := range domains {
		if !dns.IsValidDomain(domain) {
			errDomain = append(errDomain, domain)
		}

		// DNS解析
		if ipType == 1 { // IPv4
			domainIPs := dns.GetARecords(domain)
			if len(domainIPs) > 0 {
				for _, dnsIP := range domainIPs {
					ips = append(ips, strings.ToLower(utils.CompleteIPV6(dnsIP)))
					domainDnsArr = append(domainDnsArr, domain)
				}
			} else {
				errDomain = append(errDomain, domain)
			}
		} else { // IPv6
			domainIPs := dns.GetAAAARecords(domain)
			if len(domainIPs) > 0 {
				for _, dnsIP := range domainIPs {
					ips = append(ips, strings.ToLower(utils.CompleteIPV6(dnsIP)))
					domainDnsArr = append(domainDnsArr, domain)
				}
			} else {
				errDomain = append(errDomain, domain)
			}
		}
	}

	return ips, domainDnsArr, errDomain, nil
}

// validateIPFormat IP格式校验（资产扫描）- 对应PHP的checkIpV6/checkIpV4
func validateIPFormat(finalIPs []string, taskType int32, ipType int32) error {
	if taskType == 1 {
		// 安全的日志记录，避免在测试环境中出现空指针
		if log.GetLogger() != nil {
			log.WithContextInfof(context.Background(), "validateIPFormat ips:%+v, taskType: %d, ipType:%d", finalIPs, taskType, ipType)
		}
		if ipType == 1 { // IPv4
			for _, ip := range finalIPs {
				if !isValidIPv4Format(ip) {
					return errors.New("资产扫描，ip格式不符合要求，存在不是ipv4格式的ip: " + ip)
				}
			}
		} else { // IPv6
			for _, ip := range finalIPs {
				if !isValidIPv6Format(ip) {
					return errors.New("资产扫描，ip格式不符合要求，存在不是ipv6格式的ip: " + ip)
				}
			}
		}
	}
	return nil
}

// isValidIPv4Format 验证IPv4格式，支持单个IP、CIDR、范围和通配符格式
func isValidIPv4Format(ip string) bool {
	// 处理CIDR格式 (如 *********/30)
	if strings.Contains(ip, "/") {
		parts := strings.Split(ip, "/")
		if len(parts) != 2 {
			return false
		}
		// 验证IP部分
		parsedIP := net.ParseIP(parts[0])
		if parsedIP == nil || parsedIP.To4() == nil {
			return false
		}
		// 验证掩码部分
		prefix, err := strconv.Atoi(parts[1])
		if err != nil || prefix < 0 || prefix > 32 {
			return false
		}
		return true
	}

	// 处理范围格式 (如 *********-10 或 ***********-***********0)
	if strings.Contains(ip, "-") {
		parts := strings.Split(ip, "-")
		if len(parts) != 2 {
			return false
		}

		startIP := strings.TrimSpace(parts[0])
		endPart := strings.TrimSpace(parts[1])

		// 验证起始IP
		parsedStartIP := net.ParseIP(startIP)
		if parsedStartIP == nil || parsedStartIP.To4() == nil {
			return false
		}

		// 如果结束部分是完整IP地址
		if strings.Contains(endPart, ".") {
			parsedEndIP := net.ParseIP(endPart)
			return parsedEndIP != nil && parsedEndIP.To4() != nil
		} else {
			// 如果结束部分是数字 (如 *********-10)
			endNum, err := strconv.Atoi(endPart)
			if err != nil || endNum < 0 || endNum > 255 {
				return false
			}
			// 验证范围的合理性
			startParts := strings.Split(startIP, ".")
			if len(startParts) != 4 {
				return false
			}
			lastOctet, err := strconv.Atoi(startParts[3])
			if err != nil || endNum < lastOctet {
				return false
			}
			return true
		}
	}

	// 处理通配符格式 (如 192.168.1.*)
	if strings.Contains(ip, "*") {
		parts := strings.Split(ip, ".")
		if len(parts) != 4 {
			return false
		}
		for _, part := range parts {
			if part != "*" {
				num, err := strconv.Atoi(part)
				if err != nil || num < 0 || num > 255 {
					return false
				}
			}
		}
		return true
	}

	// 处理单个IP地址
	parsedIP := net.ParseIP(ip)
	return parsedIP != nil && parsedIP.To4() != nil
}

// isValidIPv6Format 验证IPv6格式，支持单个IP、CIDR格式
func isValidIPv6Format(ip string) bool {
	// 处理CIDR格式 (如 2001:db8::/32)
	if strings.Contains(ip, "/") {
		parts := strings.Split(ip, "/")
		if len(parts) != 2 {
			return false
		}
		// 验证IP部分
		parsedIP := net.ParseIP(parts[0])
		if parsedIP == nil || parsedIP.To4() != nil {
			return false
		}
		// 验证掩码部分
		prefix, err := strconv.Atoi(parts[1])
		if err != nil || prefix < 0 || prefix > 128 {
			return false
		}
		return true
	}

	// 处理单个IPv6地址
	parsedIP := net.ParseIP(ip)
	return parsedIP != nil && parsedIP.To4() == nil
}

// performSecurityChecks 安全检查 - 内网IP和禁扫IP
func performSecurityChecks(ctx context.Context, finalIPs []string, userID uint64, isIPv6 bool) error {
	// 内网IP检查 - 对应PHP的checkHasInsideIp
	splitIPs := network.SplitIP(finalIPs, isIPv6)
	if !cfg.CanScanInsideIp() {
		hasInsideIP := network.CheckHasInsideIP(splitIPs)
		if hasInsideIP != "" {
			return errors.New("您下发的扫描任务里面包含内网IP地址:" + hasInsideIP + "，系统扫描内网IP，请去掉内网IP后再下发任务")
		}
	}
	// 禁扫IP检查 - 对应PHP的ForbidIp查询和检查
	forbidIPs, err := mysql.NewDSL[forbid.ForbidIps]().QueryByParams([][]interface{}{
		{"user_id", "=", userID},
	})
	if err == nil && len(forbidIPs) > 0 {
		var taskTargetIPs []string

		// 解析扫描目标IP，将CIDR、范围等格式展开为单个IP
		// 使用统一的ParseIPRange函数，与其他地方保持一致
		for _, showIP := range splitIPs {
			expandedIPs, parseErr := network.ParseIPRange(showIP)
			if parseErr != nil {
				log.WithContextErrorf(ctx, "[安全检查] 解析IP范围失败: %s, 错误: %v", showIP, parseErr)
				continue
			}
			taskTargetIPs = append(taskTargetIPs, expandedIPs...)
		}

		var canNotScanIPs []string

		// 检查每个目标IP是否在禁扫列表中
		for _, targetIP := range taskTargetIPs {
			for _, forbidIPRecord := range forbidIPs {
				forbidSegment := forbidIPRecord.IpSegment

				// 检查目标IP是否匹配禁扫IP段
				if isIPInForbidSegment(targetIP, forbidSegment) {
					canNotScanIPs = append(canNotScanIPs, targetIP)
					break
				}
			}
		}

		if len(canNotScanIPs) > 0 {
			return errors.New("不能下发扫描任务，以下ip为禁扫ip:" + strings.Join(canNotScanIPs, ","))
		}
	}

	return nil
}

// checkAssetLimits 资产扫描限制和企业台账限制 - 对应PHP的资产扫描数量限制
func checkAssetLimits(ctx context.Context, req *pb.CreateScanTaskRequest, finalIPs []string, companyID int64, isPortRange bool, isIPv6 bool) ([]string, string, error) {
	var countIP int32
	if isPortRange {
		// 统计rawgrab_ports中的唯一IP
		if req.RawgrabPorts != "" {
			var rawgrabPorts []map[string]interface{}
			err := json.Unmarshal([]byte(req.RawgrabPorts), &rawgrabPorts)
			if err == nil {
				uniqueIPs := make(map[string]bool)
				for _, item := range rawgrabPorts {
					if ipVal, ok := item["ip"]; ok {
						ip := fmt.Sprintf("%v", ipVal)
						uniqueIPs[ip] = true
					}
				}
				countIP = int32(len(uniqueIPs))
			} else {
				countIP = 0
			}
		} else {
			countIP = 0
		}
	} else {
		// 对应PHP的countIps($Ips)计算 - 计算IP范围中的实际IP数量
		countIP = countIPsInRanges(finalIPs)
	}

	if countIP > 1000 {
		return nil, "", fmt.Errorf("超出资产扫描限制数量%d个", countIP-1000)
	}

	if countIP == 0 && !isPortRange {
		return nil, "", errors.New("下发任务的ip格式非法或者为禁扫ip，请重新下发扫描任务")
	}

	var warnMessage string

	// 企业台账限制处理 - 对应PHP的企业限制逻辑
	if companyID > 0 {
		// 对应PHP的Company::query()->where('id', $company_id)->first()
		companyInfo, err := mysql.NewDSL[company.Company]().FindByParams([][]interface{}{
			{"id", "=", companyID},
		})
		if err != nil {
			return nil, "", fmt.Errorf("查询企业信息失败: %v", err)
		}

		if companyInfo.ID > 0 {
			canScanIPNum := companyInfo.LimitIpAsset - companyInfo.UsedIpAsset
			if canScanIPNum <= 0 {
				return nil, "", errors.New("台账授权数量已经用完，无法下发扫描任务")
			}

			originIpNum := int(countIP)
			if originIpNum > int(canScanIPNum) {
				// 拆分IP并截取 - 对应PHP的spliteIp和array_slice
				splitIPs := network.SplitIP(finalIPs, isIPv6)
				if len(splitIPs) > int(canScanIPNum) {
					splitIPs = splitIPs[:canScanIPNum]
				}
				finalIPs = splitIPs
				scanIpNum := len(splitIPs)
				warnMessage = fmt.Sprintf("下发资产数超入账限制，自动下发%d个资产", scanIpNum)

				log.WithContextInfof(ctx, "[创建扫描任务] 资产扫描任务限制，原始IP数: %d, 实际扫描IP数: %d", originIpNum, scanIpNum)
			}
		}
	}

	return finalIPs, warnMessage, nil
}

// validateDefinePorts 自定义端口验证 - 对应PHP的自定义端口逻辑
func validateDefinePorts(req *pb.CreateScanTaskRequest, scanTask *task.Task) error {
	if req.IsDefinePort == 1 {
		definedPortNum := len(req.DefinePorts) * len(req.DefinePortProtocols)
		if definedPortNum > 100 {
			return errors.New("自定义端口扫描下，端口协议随机组合最多100个，现在已经超出数量限制，请重新设置")
		}

		// 全端口扫描检查 - 对应PHP的ALL_PORT_DEFINE_PORT检查
		if len(req.DefinePorts) == 1 && req.DefinePorts[0] == 65535 && definedPortNum == 1 {
			scanTask.ScanType = 1 // SCAN_TYPE_SPEED - mascan扫描器
		}
	}
	return nil
}

// insertTaskData 插入相关数据 - 对应PHP的各种数据插入逻辑
func insertTaskData(ctx context.Context, taskID int64, finalIPs []string, domainDnsArr []string, req *pb.CreateScanTaskRequest, dataPorts []uint64) error {
	// 插入扫描任务URL - 对应PHP的TaskHosts::query()->create
	if len(req.Urls) > 0 && len(domainDnsArr) == 0 {
		taskHost := &task.TaskHost{
			TaskId: uint64(taskID),
			Urls:   utils.AnyToStr(req.Urls),
		}
		_, err := mysql.NewDSL[task.TaskHost]().Create(*taskHost)
		if err != nil {
			return fmt.Errorf("插入任务URL失败: %v", err)
		}
	}

	// 插入自定义扫描数据 - 对应PHP的TaskProbeInfos插入
	if req.PortRange == 1 && req.RawgrabPorts != "" {
		log.WithContextInfof(ctx, "[创建扫描任务] 需要插入探活数据，JSON长度: %d", len(req.RawgrabPorts))

		// 解析JSON字符串 - 对应PHP的$param['rawgrab_ports']
		var rawgrabPorts []map[string]interface{}
		err := json.Unmarshal([]byte(req.RawgrabPorts), &rawgrabPorts)
		if err != nil {
			return fmt.Errorf("解析rawgrab_ports JSON失败: %v", err)
		}

		// 对应PHP的collect($param['rawgrab_ports'])->map()逻辑
		var probeInfos []task.TaskProbeInfo
		for _, item := range rawgrabPorts {
			// 提取字段值，处理类型转换
			ip := ""
			port := 0
			baseProtocol := ""

			if ipVal, ok := item["ip"]; ok {
				ip = fmt.Sprintf("%v", ipVal)
			}
			if portVal, ok := item["port"]; ok {
				if portFloat, ok := portVal.(float64); ok {
					port = int(portFloat)
				} else if portInt, ok := portVal.(int); ok {
					port = portInt
				}
			}
			if protocolVal, ok := item["base_protocol"]; ok {
				baseProtocol = fmt.Sprintf("%v", protocolVal)
			}

			probeInfos = append(probeInfos, task.TaskProbeInfo{
				TaskId: uint64(taskID),
				Ip: sql.NullString{
					String: ip,
					Valid:  true,
				},
				Port:         port,
				BaseProtocol: baseProtocol,
				CreatedAt: sql.NullTime{
					Time:  time.Now(),
					Valid: true,
				},
				UpdatedAt: sql.NullTime{
					Time:  time.Now(),
					Valid: true,
				},
			})
		}

		// 对应PHP的array_chunk($probeInfos, 50)逻辑
		chunkSize := 50
		for i := 0; i < len(probeInfos); i += chunkSize {
			end := i + chunkSize
			if end > len(probeInfos) {
				end = len(probeInfos)
			}

			// 对应PHP的TaskProbeInfos::query()->insert($indata)
			_, err := mysql.NewDSL[task.TaskProbeInfo]().BatchCreate(probeInfos[i:end])
			if err != nil {
				return fmt.Errorf("插入探活数据失败: %v", err)
			}
		}
	}

	// 插入IP数据 - 对应PHP的TaskIps::query()->insert($IpArr)
	if len(finalIPs) > 0 {
		var taskIPs []task.TaskIps
		for _, ip := range finalIPs {
			taskIPs = append(taskIPs, task.TaskIps{
				TaskId: uint64(taskID),
				Ip: sql.NullString{
					String: ip,
					Valid:  true,
				},
				CreatedAt: sql.NullTime{
					Time:  time.Now(),
					Valid: true,
				},
			})
		}
		_, err := mysql.NewDSL[task.TaskIps]().BatchCreate(taskIPs)
		if err != nil {
			return fmt.Errorf("插入IP数据失败: %v", err)
		}
	}

	// 插入端口数据 - 对应PHP的端口插入逻辑
	if req.IsDefinePort == 1 {
		log.WithContextInfof(ctx, "[创建扫描任务] 需要插入自定义端口，端口数: %d, 协议数: %d", len(req.DefinePorts), len(req.DefinePortProtocols))

		// 去重端口和协议 - 对应PHP的array_unique
		ports := slices.Clone(req.DefinePorts)
		slices.Sort(ports)
		ports = slices.Compact(ports)

		protocols := slices.Clone(req.DefinePortProtocols)
		slices.Sort(protocols)
		protocols = slices.Compact(protocols)

		// 创建自定义端口记录 - 对应PHP的DefinePort::query()->insert
		var insertDefinePorts []*port_group.DefinePort
		for _, dPort := range ports {
			for _, dProtocol := range protocols {
				insertDefinePorts = append(insertDefinePorts, &port_group.DefinePort{
					TaskId:         uint64(taskID),
					UserId:         uint64(req.UserId),
					Port:           int(dPort),
					PortProtocolId: uint64(dProtocol),
				})
			}
		}

		// 使用DefinePort模型的CreateBatch方法
		definePortModel := port_group.NewDefinePortModel()
		err := definePortModel.CreateBatch(insertDefinePorts)
		if err != nil {
			return fmt.Errorf("插入自定义端口数据失败: %v", err)
		}

	} else if req.PortGroupIds > 0 {
		// 端口分组 - 对应PHP的端口分组插入
		taskPort := &task.TaskPorts{
			TaskId:    uint64(taskID),
			PortsId:   uint64(req.PortGroupIds),
			PortsType: "App\\Models\\MySql\\PortGroup",
			CreatedAt: sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			},
		}
		_, err := mysql.NewDSL[task.TaskPorts]().Create(*taskPort)
		if err != nil {
			return fmt.Errorf("插入端口组失败: %v", err)
		}
	} else if len(dataPorts) > 0 {
		// 指定端口扫描 - 对应PHP的指定端口插入
		var taskPorts []task.TaskPorts
		for _, portID := range dataPorts {
			taskPorts = append(taskPorts, task.TaskPorts{
				TaskId:    uint64(taskID),
				PortsId:   portID,
				PortsType: "App\\Models\\MySql\\Port",
				CreatedAt: sql.NullTime{
					Time:  time.Now(),
					Valid: true,
				},
			})
		}
		_, err := mysql.NewDSL[task.TaskPorts]().BatchCreate(taskPorts)
		if err != nil {
			return fmt.Errorf("插入端口数据失败: %v", err)
		}
	}
	// 插入域名数据 - 对应PHP的TaskHosts插入
	if len(domainDnsArr) > 0 {
		log.WithContextInfof(ctx, "[创建扫描任务] 需要插入域名数据: %v", domainDnsArr)
		chunks := slices.Chunk(domainDnsArr, 50)
		for chunk := range chunks {
			taskHosts := task.TaskHost{
				TaskId: uint64(taskID),
				Urls:   utils.AnyToStr(chunk),
			}
			_, err := mysql.NewDSL[task.TaskHost]().Create(taskHosts)
			if err != nil {
				return fmt.Errorf("创建任务主机失败: %v", err)
			}
		}
	}

	return nil
}

// countIPsInRanges 计算IP范围中的IP数量 - 对应PHP的countIps($Ips)函数
func countIPsInRanges(ips []string) int32 {
	var total int32 = 0
	for _, ip := range ips {
		if strings.Contains(ip, "/") {
			// CIDR格式 - 对应PHP的CIDR计算
			_, cidr, err := net.ParseCIDR(ip)
			if err == nil {
				ones, bits := cidr.Mask.Size()
				total += int32(1 << (bits - ones))
			} else {
				total += 1
			}
		} else if strings.Contains(ip, "*") {
			// 网段格式：192.168.1-10.* - 对应PHP的网段计算
			parts := strings.Split(ip, "-")
			if len(parts) == 2 {
				startParts := strings.Split(parts[0], ".")
				if len(startParts) >= 3 {
					if startNum, err := strconv.Atoi(startParts[2]); err == nil {
						endPart := strings.Replace(parts[1], ".*", "", -1)
						if endNum, err := strconv.Atoi(endPart); err == nil {
							// 每个网段256个IP
							total += int32((endNum - startNum + 1) * 256)
						} else {
							total += 256
						}
					} else {
						total += 256
					}
				} else {
					total += 256
				}
			} else {
				total += 256
			}
		} else if strings.Contains(ip, "-") {
			// 范围格式 - 对应PHP的范围计算
			parts := strings.Split(ip, "-")
			if len(parts) == 2 {
				startIP := net.ParseIP(parts[0])
				if startIP != nil {
					if strings.Contains(parts[1], ".") {
						// 完整IP范围：***********-*************
						endIP := net.ParseIP(parts[1])
						if endIP != nil {
							// 简化计算，实际应该计算IP范围
							total += 100 // 简化为固定值
						} else {
							total += 1
						}
					} else {
						// 最后一段范围：***********-50
						if endNum, err := strconv.Atoi(parts[1]); err == nil {
							startParts := strings.Split(parts[0], ".")
							if len(startParts) == 4 {
								if startNum, err := strconv.Atoi(startParts[3]); err == nil {
									total += int32(endNum - startNum + 1)
								} else {
									total += 1
								}
							} else {
								total += 1
							}
						} else {
							total += 1
						}
					}
				} else {
					total += 1
				}
			} else {
				total += 1
			}
		} else {
			// 单个IP
			total += 1
		}
	}
	return total
}

// isIPInForbidSegment 检查IP是否在禁扫IP段中
func isIPInForbidSegment(targetIP, forbidSegment string) bool {
	// 直接匹配
	if targetIP == forbidSegment {
		return true
	}

	// 检查是否为CIDR格式的禁扫段
	if strings.Contains(forbidSegment, "/") {
		_, cidr, err := net.ParseCIDR(forbidSegment)
		if err == nil {
			ip := net.ParseIP(targetIP)
			if ip != nil {
				return cidr.Contains(ip)
			}
		}
	}

	// 检查单个IP加/32的情况
	if targetIP+"/32" == forbidSegment {
		return true
	}

	return false
}

// publishScanTask 下发扫描任务到MQ - 对应PHP的ScanAssetMqJob::publish
func publishScanTask(ctx context.Context, taskID int64, userID uint64) error {
	// 获取用户优先级 - 对应PHP的User::query()->where('id',$user_id)->first()
	userInfo, err := mysql.NewDSL[user.User]().FindByParams([][]interface{}{
		{"id", "=", userID},
	})
	if err != nil {
		log.WithContextErrorf(ctx, "[创建扫描任务] 获取用户优先级失败: %v", err)
		return err
	}

	priority := 0
	if userInfo.Id > 0 {
		priority = int(userInfo.Priority)
	}
	// 如果配置开启了调用golang的job，就调用golang的job，否则调用php的job
	if cfg.ExecGolangJob() {
		// 下发扫描任务到MQ - 对应PHP的ScanAssetMqJob::publish
		err = asyncq.Enqueue(ctx, asyncq.ScanAssetJob, &asyncq.TaskIdPayload{
			TaskId: uint64(taskID),
			UserId: userID,
		})
	} else {
		//调用php的job --DispatchGolangJobToPhpJob
		err = asyncq.DispatchGolangJobToPhpJob.Dispatch(uint64(taskID))
	}

	if err != nil {
		return fmt.Errorf("下发扫描任务到MQ失败: %v", err)
	}

	log.WithContextInfof(ctx, "[创建扫描任务] 成功下发扫描任务到MQ，任务ID: %d, 优先级: %d", taskID, priority)
	return nil
}
