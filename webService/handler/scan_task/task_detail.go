package scan_task

import (
	"context"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/port_group"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/mysql/scan_task_domains"
	"micro-service/middleware/mysql/task"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
	"strings"
	"time"
)

// GetTaskDetail 获取扫描任务详情
func GetTaskDetail(ctx context.Context, req *pb.GetTaskDetailRequest, rsp *pb.GetTaskDetailResponse) error {
	log.WithContextInfof(ctx, "Received Web.GetTaskDetail request: %v", req)

	// 查询任务信息
	taskModel := scan_task.NewModel()
	conditions := []mysql.HandleFunc{
		scan_task.WithID(uint64(req.TaskId)),
	}

	// 如果提供了用户ID，则添加用户ID条件
	if req.UserId > 0 {
		conditions = append(conditions, scan_task.WithUserID(uint64(req.UserId)))
	}

	// 如果提供了企业ID，则添加企业ID条件
	if req.CompanyId > 0 {
		conditions = append(conditions, scan_task.WithCompanyID(req.CompanyId))
	}

	// 查询任务信息
	taskInfo, err := taskModel.First(conditions...)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务详情] 查询任务信息失败: %v", err)
		return err
	}

	// 格式化时间
	startAt := ""
	if taskInfo.StartAt != nil && !taskInfo.StartAt.IsZero() {
		startAt = taskInfo.StartAt.Format("2006-01-02 15:04:05")
	}

	endAt := ""
	if taskInfo.EndAt != nil && !taskInfo.EndAt.IsZero() {
		endAt = taskInfo.EndAt.Format("2006-01-02 15:04:05")
	}

	pauseAt := ""
	if taskInfo.PauseAt != nil && !taskInfo.PauseAt.IsZero() {
		pauseAt = taskInfo.PauseAt.Format("2006-01-02 15:04:05")
	}

	createdAt := ""
	if !taskInfo.CreatedAt.IsZero() {
		createdAt = taskInfo.CreatedAt.Format("2006-01-02 15:04:05")
	}

	updatedAt := ""
	if !taskInfo.UpdatedAt.IsZero() {
		updatedAt = taskInfo.UpdatedAt.Format("2006-01-02 15:04:05")
	}

	deletedAt := ""
	if taskInfo.DeletedAt.Valid {
		deletedAt = taskInfo.DeletedAt.Time.Format("2006-01-02 15:04:05")
	}

	// 将progress转换为字符串
	progressStr := fmt.Sprintf("%.2f", taskInfo.Progress)

	// 将port_range转换为字符串
	portRangeStr := ""
	if taskInfo.PortRange != 0 {
		portRangeStr = fmt.Sprintf("%d", taskInfo.PortRange)
	}

	// 构建响应数据
	rsp.Id = int64(taskInfo.ID)
	rsp.UserId = int64(taskInfo.UserId)
	rsp.CompanyId = int64(taskInfo.CompanyId)
	rsp.Name = taskInfo.Name
	rsp.Bandwidth = taskInfo.Bandwidth
	rsp.Node = taskInfo.Node
	rsp.ProtocolConcurrency = taskInfo.ProtocolConcurrency
	rsp.TaskType = int32(taskInfo.TaskType)
	rsp.AssetType = int32(taskInfo.AssetType)
	rsp.IpType = int32(taskInfo.IpType)
	rsp.Type = int32(taskInfo.Type)
	rsp.Progress = progressStr
	rsp.AssetNum = taskInfo.AssetNum
	rsp.ThreatNum = taskInfo.ThreatNum
	rsp.RuleNum = taskInfo.RuleNum
	rsp.Order = int64(taskInfo.Order)
	rsp.PingSwitch = int32(taskInfo.PingSwitch)
	rsp.WebLogoSwitch = int32(taskInfo.WebLogoSwitch)
	rsp.UseSeconds = taskInfo.UseSeconds
	rsp.Status = int32(taskInfo.Status)
	rsp.Step = int32(taskInfo.Step)
	rsp.ScanType = int32(taskInfo.ScanType)
	rsp.PocScanType = int32(taskInfo.PocScanType)
	rsp.ScanRange = int32(taskInfo.ScanRange)
	rsp.StartAt = startAt
	rsp.EndAt = endAt
	rsp.PuaseAt = pauseAt
	rsp.CreatedAt = createdAt
	rsp.UpdatedAt = updatedAt
	rsp.DeletedAt = deletedAt
	rsp.FileName = taskInfo.FileName
	rsp.TaskFrom = int32(taskInfo.TaskFrom)
	rsp.CronId = int32(taskInfo.CronId)
	rsp.OpId = int64(taskInfo.OpId)
	rsp.IsAudit = int32(taskInfo.IsAudit)
	rsp.ForbidStatus = int32(taskInfo.ForbidStatus)
	rsp.Reason = taskInfo.Reason
	rsp.IsDefinePort = int32(taskInfo.IsDefinePort)
	rsp.PortRange = portRangeStr
	rsp.Flag = taskInfo.Flag
	rsp.DetectAssetsTasksId = int64(taskInfo.DetectAssetsTasksId)
	rsp.AuditTime = taskInfo.AuditTime
	rsp.ScanEngine = taskInfo.ScanEngine
	rsp.BatchId = taskInfo.BatchId
	rsp.IsTranscanner = int32(taskInfo.IsTranscanner)
	rsp.TianrongxinTaskId = taskInfo.TianrongxinTaskId
	rsp.AssetsSyncNode = taskInfo.AssetsSyncNode

	// 设置端口组
	// 获取任务端口配置
	if taskInfo.IsDefinePort != 1 {
		taskPortsRepo := task.NewTaskPortsModel()
		taskPorts, err := taskPortsRepo.FindByTaskID(uint64(req.TaskId))
		if err != nil {
			// 如果是记录不存在的错误，不影响整个接口，只记录日志
			if err == gorm.ErrRecordNotFound {
				log.WithContextInfof(ctx, "任务ID %d 没有配置端口信息", req.TaskId)
			} else {
				log.WithContextErrorf(ctx, "获取任务端口配置失败: task_id=%d, error=%v", req.TaskId, err)
			}
		} else if taskPorts != nil && taskPorts.PortsType == "App\\Models\\MySql\\PortGroup" {
			groupId := taskPorts.PortsId
			portGroupModel := port_group.NewPortGroupModel()
			portGroup, err := portGroupModel.FindById(groupId)
			if err != nil {
				log.WithContextErrorf(ctx, "获取端口分组失败: task_id=%d, group_id=%d, error=%v", req.TaskId, groupId, err)
			} else if portGroup != nil {
				rsp.PortGroup = append(rsp.PortGroup, &pb.PortGroupInfo{
					Id:        portGroup.Id,
					Name:      portGroup.Name,
					UserId:    portGroup.UserId,
					CompanyId: uint64(portGroup.CompanyId),
					CanDel:    portGroup.CanDel,
					CreatedAt: portGroup.CreatedAt.Format("2006-01-02 15:04:05"),
					UpdatedAt: portGroup.UpdatedAt.Format("2006-01-02 15:04:05"),
				})
			}
		}
	}

	// 设置组织发现任务ID
	if taskInfo.OrganizationDiscoverTaskId != nil {
		rsp.OrganizationDiscoverTaskId = fmt.Sprintf("%d", *taskInfo.OrganizationDiscoverTaskId)
	}

	// 查询操作人信息
	if taskInfo.OpId > 0 {
		userModel := user.NewUserModel()
		opInfo, err := userModel.FindById(uint(taskInfo.OpId))
		if err == nil && opInfo != nil {
			rsp.Op = &pb.UserInfo{
				Id:   int64(opInfo.Id),
				Name: opInfo.Name.String,
			}
		}
	}

	// 查询任务IP列表
	taskIpsModel := task.NewTaskIpsModel()
	taskIps, err := taskIpsModel.FindByQuerys(mysql.WithColumnValue("task_id", taskInfo.ID))
	if err == nil && len(taskIps) > 0 {
		for _, ip := range taskIps {
			rsp.Ips = append(rsp.Ips, &pb.TaskIpInfo{
				Id:     int64(ip.Id),
				TaskId: int64(ip.TaskId),
				Ip:     ip.Ip.String,
			})
		}
	}

	// 获取数据库连接
	db := mysql.GetDbClient()

	// 查询域名扫描目标信息（对应PHP代码中的ScanTaskDomians查询）
	// 获取一下当时域名的扫描目标信息
	var domainInfo scan_task_domains.ScanTaskDomains
	err = db.Table("scan_task_domains").
		Where("user_id = ? AND task_id = ?", taskInfo.UserId, taskInfo.ID).
		First(&domainInfo).Error
	if err == nil && domainInfo.Domains != "" {
		// 如果有域名信息，需要将域名添加到IP列表中（模拟PHP逻辑）
		domainArr := strings.Split(domainInfo.Domains, ",")
		// 去重处理
		domainMap := make(map[string]bool)
		for _, domain := range domainArr {
			domain = strings.TrimSpace(domain)
			if domain != "" {
				domainMap[domain] = true
			}
		}

		// 将域名作为IP添加到列表中
		for domain := range domainMap {
			rsp.Ips = append(rsp.Ips, &pb.TaskIpInfo{
				Id:     0, // 域名没有ID
				TaskId: int64(taskInfo.ID),
				Ip:     domain,
			})
		}
		log.WithContextInfof(ctx, "任务ID %d 添加了 %d 个域名到IP列表", taskInfo.ID, len(domainMap))
	} else if err != nil && err != gorm.ErrRecordNotFound {
		// 如果不是记录不存在的错误，记录日志
		log.WithContextErrorf(ctx, "查询域名扫描目标信息失败: task_id=%d, error=%v", taskInfo.ID, err)
	}

	// 查询任务探测信息
	var probeInfos []task.TaskProbeInfo
	err = db.Table("task_probe_infos").Where("task_id = ?", taskInfo.ID).Find(&probeInfos).Error
	if err == nil && len(probeInfos) > 0 {
		for _, probeInfo := range probeInfos {
			rsp.ProbeInfos = append(rsp.ProbeInfos, &pb.TaskProbeInfo{
				Id:           int64(probeInfo.Id),
				TaskId:       int64(probeInfo.TaskId),
				Ip:           probeInfo.Ip.String,
				Port:         int32(probeInfo.Port),
				BaseProtocol: probeInfo.BaseProtocol,
			})
		}
	}

	// 查询任务主机列表
	var hosts []struct {
		ID        uint64    `gorm:"column:id"`
		TaskID    uint64    `gorm:"column:task_id"`
		URLs      string    `gorm:"column:urls"`
		CreatedAt time.Time `gorm:"column:created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at"`
	}
	err = db.Table(task.TableName).Where("task_id = ?", taskInfo.ID).Find(&hosts).Error
	if err == nil && len(hosts) > 0 {
		for _, host := range hosts {
			createdAt := ""
			if !host.CreatedAt.IsZero() {
				createdAt = host.CreatedAt.Format("2006-01-02 15:04:05")
			}

			updatedAt := ""
			if !host.UpdatedAt.IsZero() {
				updatedAt = host.UpdatedAt.Format("2006-01-02 15:04:05")
			}

			// 解析URLs字符串为数组
			var urlList []string
			if host.URLs != "" {
				// 尝试解析JSON格式的URLs
				err := json.Unmarshal([]byte(host.URLs), &urlList)
				if err != nil {
					// 如果解析失败，则将其作为单个URL处理
					urlList = []string{host.URLs}
				}
			}

			rsp.Hosts = append(rsp.Hosts, &pb.TaskHostInfo{
				Id:        int64(host.ID),
				TaskId:    int64(host.TaskID),
				Urls:      urlList,
				CreatedAt: createdAt,
				UpdatedAt: updatedAt,
			})
		}
	}

	// 如果是自定义端口，获取自定义端口列表
	if taskInfo.IsDefinePort == 1 {
		var definePorts []struct {
			Port             int32 `gorm:"column:port"`
			PortProtocolId   int32 `gorm:"column:port_protocol_id"`
		}
		err = db.Table("define_ports").Where("task_id = ?", taskInfo.ID).Find(&definePorts).Error
		if err == nil && len(definePorts) > 0 {
			// 使用map进行去重
			portMap := make(map[int32]bool)
			protocolMap := make(map[int32]bool)
			for _, definePort := range definePorts {
				portMap[definePort.Port] = true
				protocolMap[definePort.PortProtocolId] = true
			}

			// 将去重后的数据添加到响应中
			for port := range portMap {
				rsp.DefinePorts = append(rsp.DefinePorts, port)
			}
			for protocol := range protocolMap {
				rsp.DefinePortProtocols = append(rsp.DefinePortProtocols, protocol)
			}
		}
	}

	// 设置台账资产类型
	// 如果是周期任务的话，查询台账ip扫描的模式
	if taskInfo.CronId > 0 && taskInfo.ScanRange == scan_task.TABLE_IP {
		// 查询周期任务的台账资产类型
		var cronTaskAssetsType int32
		err = db.Table("scan_crontab_tasks").
			Where("user_id = ? AND id = ?", taskInfo.UserId, taskInfo.CronId).
			Select("table_assets_type").
			Scan(&cronTaskAssetsType).Error
		if err == nil {
			rsp.TableAssetsType = cronTaskAssetsType
		} else if err != gorm.ErrRecordNotFound {
			log.WithContextErrorf(ctx, "查询周期任务台账资产类型失败: cron_id=%d, error=%v", taskInfo.CronId, err)
		}
	} else if taskInfo.TaskType == scan_task.TASK_TYPE_ASSET && taskInfo.ScanRange == scan_task.TABLE_IP {
		rsp.TableAssetsType = int32(taskInfo.AssetType)
	}

	return nil
}
