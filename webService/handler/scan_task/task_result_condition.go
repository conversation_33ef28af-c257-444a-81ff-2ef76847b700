package scan_task

import (
	"context"
	"encoding/json"
	"fmt"
	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/mysql/geo_areas"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"

	"github.com/olivere/elastic"
)

// GetTaskResultCondition 获取扫描任务结果筛选条件
func GetTaskResultCondition(ctx context.Context, req *pb.GetTaskResultConditionRequest, rsp *pb.GetTaskResultConditionResponse) error {
	log.WithContextInfof(ctx, "Received Web.GetTaskResultCondition request: %v", req)

	// 查询任务信息
	taskModel := scan_task.NewModel()
	taskInfo, err := taskModel.First(
		scan_task.WithID(uint64(req.Id)),
		scan_task.WithUserID(uint64(req.UserId)),
		scan_task.WithStatus(scan_task.StatusFinished), // 只获取已完成的任务
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果筛选条件] 获取任务信息失败: %v", err)
		return err
	}

	if taskInfo == nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果筛选条件] 任务不存在或未完成")
		return fmt.Errorf("任务不存在或未完成")
	}

	// 构建简化的查询条件，只使用必要的条件
	var query [][]interface{}

	// 添加任务ID条件
	query = append(query, []interface{}{"task_id", req.Id})

	// 添加用户ID条件
	query = append(query, []interface{}{"user_id", req.UserId})

	// 尝试直接使用elastic查询，而不是通过ListByParams
	indexName := "fofaee_task_assets" // 直接使用常量
	esClient := es.GetEsClient()

	// 构建bool查询
	boolQuery := elastic.NewBoolQuery()
	boolQuery.Must(elastic.NewTermQuery("task_id", req.Id))
	boolQuery.Must(elastic.NewTermQuery("user_id", req.UserId))

	// 执行查询
	searchResult, err := esClient.Search().
		Index(indexName).
		Query(boolQuery).
		Size(10000).
		Do(ctx)

	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果筛选条件] 查询失败: %v", err)
		return err
	}

	// 解析查询结果
	var taskAssets []*fofaee_task_assets.FofaeeTaskAssets
	for _, hit := range searchResult.Hits.Hits {
		var asset fofaee_task_assets.FofaeeTaskAssets
		if err := json.Unmarshal(*hit.Source, &asset); err != nil {
			log.WithContextErrorf(ctx, "[获取扫描任务结果筛选条件] 解析结果失败: %v", err)
			continue
		}
		taskAssets = append(taskAssets, &asset)
	}

	// 组件名称
	ruleNames := make(map[string]struct{})
	// 资产状态
	states := make(map[int32]struct{})
	// 开放端口
	portList := make(map[string]struct{})
	// 开放服务
	openServices := make(map[string]struct{})
	// 厂商
	companyList := make(map[string]struct{})
	// 规则类型
	ruleTypes := make(map[string]struct{})

	// 收集数据
	for _, asset := range taskAssets {
		// 收集资产状态
		states[int32(asset.State)] = struct{}{}

		// 收集端口
		for _, port := range asset.Ports {
			if portStr, ok := port.(string); ok {
				portList[portStr] = struct{}{}
			} else {
				portList[fmt.Sprintf("%v", port)] = struct{}{}
			}
		}

		// 收集协议
		for _, protocol := range asset.Protocols {
			openServices[protocol] = struct{}{}
		}

		// 收集规则相关信息
		for _, rule := range asset.Rules {
			// 组件名称
			if rule.CnProduct != "" {
				ruleNames[rule.CnProduct] = struct{}{}
			}
			// 厂商
			if rule.CnCompany != "" {
				companyList[rule.CnCompany] = struct{}{}
			}
			// 规则类型
			if rule.CnCategory != "" {
				ruleTypes[rule.CnCategory] = struct{}{}
			}
		}
	}

	// 转换为切片
	for ruleName := range ruleNames {
		rsp.RuleName = append(rsp.RuleName, ruleName)
	}

	for state := range states {
		rsp.State = append(rsp.State, state)
	}

	for port := range portList {
		rsp.PortList = append(rsp.PortList, port)
	}

	for service := range openServices {
		rsp.OpenService = append(rsp.OpenService, service)
	}

	for company := range companyList {
		rsp.CompanyList = append(rsp.CompanyList, company)
	}

	for ruleType := range ruleTypes {
		rsp.RuleType = append(rsp.RuleType, ruleType)
	}

	// 获取地理位置信息
	geoModel := geo_areas.NewModel()
	provinces, cities, err := geoModel.GetProvinceWithCities()
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果筛选条件] 获取地理位置信息失败: %v", err)
		return err
	}

	// 将省份和城市组织成树形结构
	cityMap := make(map[uint64][]*pb.AreaCityInfo)
	for _, city := range cities {
		cityInfo := &pb.AreaCityInfo{
			Id:       int64(city.ID),
			ParentId: int64(city.ParentID),
			Name:     city.Name,
		}
		cityMap[city.ParentID] = append(cityMap[city.ParentID], cityInfo)
	}

	// 构建省份列表
	for _, province := range provinces {
		provinceInfo := &pb.AreaProvinceInfo{
			Id:   int64(province.ID),
			Name: province.Name,
		}

		// 添加该省份下的城市
		if cities, ok := cityMap[province.ID]; ok {
			provinceInfo.List = cities
		}

		rsp.Area = append(rsp.Area, provinceInfo)
	}

	return nil
}
