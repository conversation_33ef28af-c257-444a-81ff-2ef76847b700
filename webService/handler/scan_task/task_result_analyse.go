package scan_task

import (
	"context"
	"fmt"
	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
	"strconv"

	"github.com/olivere/elastic"
)

// TaskResultAnalyse 扫描结果数据汇总
func TaskResultAnalyse(ctx context.Context, req *pb.TaskResultAnalyseRequest, rsp *pb.TaskResultAnalyseResponse) error {
	log.WithContextInfof(ctx, "Received Web.TaskResultAnalyse request: %v", req)

	// 将ID转换为int64
	taskID, err := strconv.ParseInt(req.Id, 10, 64)
	if err != nil {
		log.WithContextErrorf(ctx, "[扫描结果数据汇总] 任务ID格式错误: %v", err)
		return fmt.Errorf("任务ID格式错误")
	}

	// 查询任务信息
	taskModel := scan_task.NewModel()
	taskInfo, err := taskModel.First(
		scan_task.WithID(uint64(taskID)),
		scan_task.WithUserID(uint64(req.UserId)),
		scan_task.WithStatus(scan_task.StatusFinished), // 只获取已完成的任务
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[扫描结果数据汇总] 获取任务信息失败: %v", err)
		return err
	}

	if taskInfo == nil {
		log.WithContextErrorf(ctx, "[扫描结果数据汇总] 任务不存在或未完成")
		return fmt.Errorf("任务不存在或未完成")
	}

	// 使用SearchBuilder构建查询条件
	var builder es.SearchBuilder

	// 添加任务ID条件
	builder.AddMust([]interface{}{"task_id", taskID})

	// 添加用户ID条件
	builder.AddMust([]interface{}{"user_id", req.UserId})

	// 添加IP过滤条件
	if req.Ip != "" {
		builder.AddMust([]interface{}{"ip", "like", es.WithRLAsterisk(req.Ip)})
	}

	// 添加状态条件
	if len(req.State) > 0 {
		builder.AddMust([]interface{}{"state", "in", es.ToInterfaceArray(req.State)})
	}

	// 添加规则标签条件
	if len(req.RuleTags) > 0 {
		var ruleTagsQuery [][]interface{}
		for _, tag := range req.RuleTags {
			ruleTagsQuery = append(ruleTagsQuery, []interface{}{"rules.rule_id", tag})
		}
		builder.AddShould(ruleTagsQuery...)
	}

	// 添加城市条件
	if len(req.City) > 0 {
		var cityQuery [][]interface{}
		for _, city := range req.City {
			cityQuery = append(cityQuery, []interface{}{"city", "like", es.WithRLAsterisk(city)})
		}
		builder.AddShould(cityQuery...)
	}

	// 添加端口条件
	if len(req.Ports) > 0 {
		var portsQuery [][]interface{}
		for _, port := range req.Ports {
			portsQuery = append(portsQuery, []interface{}{"ports", port})
		}
		builder.AddShould(portsQuery...)
	}

	// 添加协议条件
	if len(req.Protocols) > 0 {
		var protocolsQuery [][]interface{}
		for _, protocol := range req.Protocols {
			protocolsQuery = append(protocolsQuery, []interface{}{"protocols", protocol})
		}
		builder.AddShould(protocolsQuery...)
	}

	// 添加公司标签条件
	if len(req.CompanyTags) > 0 {
		var companyTagsQuery [][]interface{}
		for _, tag := range req.CompanyTags {
			companyTagsQuery = append(companyTagsQuery, []interface{}{"company_tags", tag})
		}
		builder.AddShould(companyTagsQuery...)
	}

	// 添加二级分类标签条件
	if len(req.SecondCatTag) > 0 {
		var secondCatTagQuery [][]interface{}
		for _, tag := range req.SecondCatTag {
			secondCatTagQuery = append(secondCatTagQuery, []interface{}{"rules.cn_category", tag})
		}
		builder.AddShould(secondCatTagQuery...)
	}

	// 添加关键词搜索条件
	if len(req.Keyword) > 0 {
		var keywordQuery [][]interface{}
		for _, keyword := range req.Keyword {
			// 尝试将关键词解析为整数，如果是整数则可能是端口
			_, err := strconv.Atoi(keyword)
			if err == nil {
				keywordQuery = append(keywordQuery, []interface{}{"ports", keyword})
			}

			// 添加其他可能的关键词搜索字段
			keywordQuery = append(keywordQuery, []interface{}{"ip", "like", es.WithRLAsterisk(keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.protocol", "like", es.WithRLAsterisk(keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.title", "like", es.WithRLAsterisk(keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"port_list.domain", "like", es.WithRLAsterisk(keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"rules.cn_product", "like", es.WithRLAsterisk(keyword)})
			keywordQuery = append(keywordQuery, []interface{}{"rules.cn_company", "like", es.WithRLAsterisk(keyword)})
		}
		builder.AddShould(keywordQuery...)
	}

	// 设置排序
	var sorts []elastic.Sorter
	sorts = append(sorts, elastic.NewFieldSort("updated_at").Desc(), elastic.NewFieldSort("_id").Desc())

	// 执行查询
	_, list, err := es.ListByParams[fofaee_task_assets.FofaeeTaskAssets](1, 10000, builder.Build(), sorts)
	if err != nil {
		log.WithContextErrorf(ctx, "[扫描结果数据汇总] 查询失败: %v", err)
		return err
	}

	// 初始化响应数据
	rsp.Tag = make([]*pb.TaskResultAnalyseResponse_TagCount, 5)
	rsp.Tag[0] = &pb.TaskResultAnalyseResponse_TagCount{Name: "业务层", Num: 0}
	rsp.Tag[1] = &pb.TaskResultAnalyseResponse_TagCount{Name: "支撑层", Num: 0}
	rsp.Tag[2] = &pb.TaskResultAnalyseResponse_TagCount{Name: "服务层", Num: 0}
	rsp.Tag[3] = &pb.TaskResultAnalyseResponse_TagCount{Name: "系统层", Num: 0}
	rsp.Tag[4] = &pb.TaskResultAnalyseResponse_TagCount{Name: "硬件层", Num: 0}

	// 统计在线IP数量
	ipCount := 0
	for _, asset := range list {
		if asset.State == 1 {
			ipCount++
		}
	}
	rsp.IpCount = int64(ipCount)

	// 如果没有在线IP，直接返回
	if ipCount == 0 {
		return nil
	}

	// 统计厂商和端口数据
	companyMap := make(map[string]int32)
	portMap := make(map[string]int32)

	for _, asset := range list {
		// 只处理在线的资产
		if asset.State != 1 {
			continue
		}

		// 处理端口数据
		for _, port := range asset.Ports {
			portStr := fmt.Sprintf("%v", port)
			portMap[portStr]++
		}

		// 处理规则数据
		for _, rule := range asset.Rules {
			// 厂商统计
			if rule.CnCompany != "" {
				companyMap[rule.CnCompany]++
			}

			// 层级统计
			level := 0
			if rule.Level != "" {
				level, _ = strconv.Atoi(rule.Level)
			}

			switch level {
			case 1:
				rsp.Tag[4].Num++ // 硬件层
			case 2:
				rsp.Tag[3].Num++ // 系统层
			case 3:
				rsp.Tag[2].Num++ // 服务层
			case 4:
				rsp.Tag[1].Num++ // 支撑层
			default:
				rsp.Tag[0].Num++ // 业务层
			}
		}
	}

	// 转换厂商统计数据
	for name, count := range companyMap {
		rsp.Company = append(rsp.Company, &pb.TaskResultAnalyseResponse_CompanyCount{
			Name: name,
			Num:  count,
		})
	}

	// 转换端口统计数据
	for name, count := range portMap {
		rsp.Port = append(rsp.Port, &pb.TaskResultAnalyseResponse_PortCount{
			Name: name,
			Num:  count,
		})
	}

	return nil
}
