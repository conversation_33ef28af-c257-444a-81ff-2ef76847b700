package scan_task

import (
	"testing"
)

func TestGetFullDomain(t *testing.T) {
	domain := "example.com"
	got := getFullDomain(domain)
	want := "example.com"

	if got != want {
		t.Errorf("getFullDomain(%q) = %q; want %q", domain, got, want)
	}
}

func TestUniqueStrings(t *testing.T) {
	tests := []struct {
		input []string
		want  []string
	}{
		{[]string{}, []string{}},
		{[]string{"a", "b", "a", "c", "b"}, []string{"a", "b", "c"}},
		{[]string{"x", "x", "x"}, []string{"x"}},
		{[]string{"1", "2", "3"}, []string{"1", "2", "3"}},
	}

	for _, tt := range tests {
		got := uniqueStrings(tt.input)

		// convert to map for comparison
		gotMap := make(map[string]bool)
		for _, v := range got {
			gotMap[v] = true
		}
		for _, v := range tt.want {
			if !gotMap[v] {
				t.<PERSON><PERSON><PERSON>("uniqueStrings(%v) missing %v in result %v", tt.input, v, got)
			}
		}
	}
}

func TestJoinStrings(t *testing.T) {
	tests := []struct {
		input []string
		sep   string
		want  string
	}{
		{[]string{"a", "b", "c"}, ",", "a,b,c"},
		{[]string{"hello", "world"}, " ", "hello world"},
		{[]string{}, "-", ""},
		{[]string{"x"}, ";", "x"},
	}

	for _, tt := range tests {
		got := joinStrings(tt.input, tt.sep)
		if got != tt.want {
			t.Errorf("joinStrings(%v, %q) = %q; want %q", tt.input, tt.sep, got, tt.want)
		}
	}
}
