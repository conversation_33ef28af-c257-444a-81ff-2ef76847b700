package scan_task

import (
	"context"
	"encoding/json"
	"fmt"
	es "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
	"net"
	"strconv"
	"strings"

	"github.com/olivere/elastic"
)

// GetTaskResult 获取已经完成扫描的资产扫描任务的扫描结果列表
func GetTaskResult(ctx context.Context, req *pb.GetTaskResultRequest, rsp *pb.GetTaskResultResponse) error {
	log.WithContextInfof(ctx, "Received Web.GetTaskResult request: %v", req)

	// 查询任务信息
	taskModel := scan_task.NewModel()
	taskInfo, err := taskModel.First(
		scan_task.WithID(uint64(req.Id)),
		scan_task.WithUserID(uint64(req.UserId)),
		scan_task.WithStatus(scan_task.StatusFinished), // 只获取已完成的任务
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果] 获取任务信息失败: %v", err)
		return err
	}

	if taskInfo == nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果] 任务不存在或未完成")
		return fmt.Errorf("任务不存在或未完成")
	}

	// 使用SearchBuilder构建查询条件
	var builder es.SearchBuilder

	// 添加任务ID条件
	builder.AddMust([]interface{}{"task_id", req.Id})

	// 添加用户ID条件
	builder.AddMust([]interface{}{"user_id", req.UserId})

	// 添加状态条件
	if len(req.State) > 0 {
		builder.AddMust([]interface{}{"state", "in", es.ToInterfaceArray(req.State)})
	}

	// 添加规则标签条件
	if len(req.RuleTags) > 0 {
		var ruleTagsQuery [][]interface{}
		for _, tag := range req.RuleTags {
			ruleTagsQuery = append(ruleTagsQuery, []interface{}{"rules.cn_product", tag})
		}
		builder.AddShould(ruleTagsQuery...)
	}

	// 添加城市条件
	if len(req.City) > 0 {
		var cityQuery [][]interface{}
		for _, city := range req.City {
			cityQuery = append(cityQuery, []interface{}{"city", "like", es.WithRLAsterisk(city)})
		}
		builder.AddShould(cityQuery...)
	}

	// 添加端口条件
	if len(req.Ports) > 0 {
		var portsQuery [][]interface{}
		for _, port := range req.Ports {
			portsQuery = append(portsQuery, []interface{}{"ports", port})
		}
		builder.AddShould(portsQuery...)
	}

	// 添加协议条件
	if len(req.Protocols) > 0 {
		var protocolsQuery [][]interface{}
		for _, protocol := range req.Protocols {
			protocolsQuery = append(protocolsQuery, []interface{}{"protocols", protocol})
		}
		builder.AddShould(protocolsQuery...)
	}

	// 添加公司标签条件
	if len(req.CompanyTags) > 0 {
		var companyTagsQuery [][]interface{}
		for _, tag := range req.CompanyTags {
			companyTagsQuery = append(companyTagsQuery, []interface{}{"rules.cn_company", tag})
		}
		builder.AddShould(companyTagsQuery...)
	}

	// 添加二级分类标签条件
	if len(req.SecondCatTag) > 0 {
		var secondCatTagQuery [][]interface{}
		for _, tag := range req.SecondCatTag {
			secondCatTagQuery = append(secondCatTagQuery, []interface{}{"rules.cn_category", tag})
		}
		builder.AddShould(secondCatTagQuery...)
	}

	// 添加关键词搜索条件
	if req.Keyword != "" {
		var keywordQuery [][]interface{}

		// 尝试将关键词解析为整数，如果是整数则可能是端口
		_, err := strconv.Atoi(req.Keyword)
		if err == nil {
			keywordQuery = append(keywordQuery, []interface{}{"ports", req.Keyword})
		}

		// 添加其他可能的关键词搜索字段
		keywordQuery = append(keywordQuery, []interface{}{"ip.ip_raw", "like", es.WithRLAsterisk(req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.protocol", "like", es.WithRLAsterisk(req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"rules.cn_product.keyword", "like", es.WithRLAsterisk(req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"rules.cn_company.keyword", "like", es.WithRLAsterisk(req.Keyword)})

		builder.AddShould(keywordQuery...)
	}

	// 设置分页参数
	page := int(req.Page)
	if page <= 0 {
		page = 1
	}

	perPage := int(req.PerPage)
	if perPage <= 0 {
		perPage = 10
	}

	// 设置排序
	var sorts []elastic.Sorter
	sorts = append(sorts, elastic.NewFieldSort("updated_at").Desc(), elastic.NewFieldSort("_id").Desc())

	// 构建查询条件
	queryParams := builder.Build()
	log.WithContextInfof(ctx, "[获取扫描任务结果] 查询条件: %v", queryParams)

	// 添加详细的调试信息
	queryJson, _ := json.Marshal(queryParams)
	log.WithContextInfof(ctx, "【ES查询调试】查询条件JSON: %s", string(queryJson))

	// 解析查询条件并打印最终的ES查询
	esQuery := es.ParseQuery(queryParams)
	esQuerySource, _ := esQuery.Source()
	esQueryJson, _ := json.Marshal(esQuerySource)
	log.WithContextInfof(ctx, "【ES查询调试】最终ES查询: %s", string(esQueryJson))

	// 执行查询
	total, list, err := es.ListByParams[fofaee_task_assets.FofaeeTaskAssets](page, perPage, builder.Build(), sorts)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果] 查询失败: %v", err)
		return err
	}

	// 添加查询结果日志
	log.WithContextInfof(ctx, "[获取扫描任务结果] 查询结果 - 总数: %d, 返回记录数: %d, 页码: %d, 每页大小: %d", total, len(list), page, perPage)

	// 检查分页结果是否异常
	expectedFrom := (page - 1) * perPage
	expectedTo := expectedFrom + perPage
	if expectedTo > int(total) {
		expectedTo = int(total)
	}
	expectedCount := expectedTo - expectedFrom
	if expectedCount > 0 && len(list) != expectedCount {
		log.WithContextWarnf(ctx, "[获取扫描任务结果] 分页结果异常 - 期望: %d 条, 实际: %d 条, from: %d, to: %d", expectedCount, len(list), expectedFrom, expectedTo)
	}

	// 处理查询结果，确保每条记录都有正确的ID
	for i := range list {
		if list[i].Id == "" {
			// 手动拼接ID：task_id_ip 格式
			taskIdStr := fmt.Sprintf("%v", list[i].TaskId)

			// 处理IPv6的特殊情况：需要将完整格式转换为简化格式
			ipForId := list[i].Ip
			if list[i].IsIpv6 {
				// IPv6需要简化格式用于ID
				ipForId = simplifyIPv6(list[i].Ip)
				log.WithContextInfof(ctx, "[获取扫描任务结果] IPv6简化处理: %s -> %s", list[i].Ip, ipForId)
			}

			list[i].Id = fmt.Sprintf("%s_%s", taskIdStr, ipForId)
			log.WithContextInfof(ctx, "[获取扫描任务结果] 为第%d条记录设置ID: %s (task_id: %v, ip: %s, is_ipv6: %t)",
				i, list[i].Id, list[i].TaskId, list[i].Ip, list[i].IsIpv6)
		}
	}
	log.WithContextInfof(ctx, "[获取扫描任务结果] 查询成功，总数: %d, 当前页记录数: %d", total, len(list))

	// 计算最后一页
	lastPage := int32(1)
	if total > 0 && perPage > 0 {
		lastPage = int32((total + int64(perPage) - 1) / int64(perPage))
	}

	// 设置响应
	rsp.Total = total
	rsp.CurrentPage = int32(page)
	rsp.PerPage = int32(perPage)
	rsp.LastPage = lastPage
	rsp.From = int32((page-1)*perPage + 1)

	// 计算to，确保不超过总数
	to := int64(page) * int64(perPage)
	if to > total {
		to = total
	}
	rsp.To = int32(to)
	rsp.HasData = total > 0

	// 序列化结果
	itemsBytes, err := json.Marshal(list)
	if err != nil {
		log.WithContextErrorf(ctx, "[获取扫描任务结果] 序列化结果失败: %v", err)
		return err
	}
	rsp.Items = itemsBytes

	return nil
}

// simplifyIPv6 将完整格式的IPv6地址转换为简化格式
// 例如: 2405:57c0:0000:0001:0000:0000:0000:0022 -> 2405:57c0:0:1::22
func simplifyIPv6(ipv6 string) string {
	// 使用Go标准库解析IPv6地址
	ip := net.ParseIP(ipv6)
	if ip == nil {
		// 如果解析失败，返回原始字符串
		return ipv6
	}

	// 转换为IPv6格式并简化
	simplified := ip.String()

	// 如果简化后的格式与原始格式相同，说明可能需要手动处理
	if simplified == ipv6 {
		// 手动处理一些特殊情况，比如去掉前导零
		parts := strings.Split(ipv6, ":")
		var newParts []string

		for _, part := range parts {
			// 去掉前导零，但保留单个0
			if part == "0000" {
				newParts = append(newParts, "0")
			} else {
				// 去掉前导零
				trimmed := strings.TrimLeft(part, "0")
				if trimmed == "" {
					trimmed = "0"
				}
				newParts = append(newParts, trimmed)
			}
		}

		// 重新组合
		result := strings.Join(newParts, ":")

		// 使用Go标准库再次解析和格式化，以获得最简化的形式
		if finalIP := net.ParseIP(result); finalIP != nil {
			return finalIP.String()
		}

		return result
	}

	return simplified
}
