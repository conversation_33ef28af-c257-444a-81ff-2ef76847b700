package icp

import (
	"context"
	"database/sql"
	"errors"
	"time"

	core "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/icp_apps"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"gorm.io/gorm"
)

func AppIcpList(_ context.Context, req *pb.IcpAppListRequest, rsp *pb.IcpAppListResponse) error {
	var hf []mysql.HandleFunc
	if req.Search != "" {
		q := "%" + req.Search + "%"
		hf = append(hf, mysql.WithWhere("`name` LIKE ? OR `icp` LIKE ? OR `app_name` LIKE ?", q, q, q))
	}
	if req.CompanyType != 0 {
		hf = append(hf, mysql.WithColumnValue("company_type", req.CompanyType))
	}
	if req.Status != 0 {
		hf = append(hf, mysql.WithColumnValue("status", req.Status))
	}
	if req.Source != "" {
		hf = append(hf, mysql.WithColumnValue("source", req.Source))
	}
	if len(req.RecordTime) == 2 {
		t0, _ := time.Parse(utils.DateLayout, req.RecordTime[0])
		t1, _ := time.Parse(utils.DateLayout, req.RecordTime[1])
		hf = append(hf, mysql.WithBetween("record_time", t0, t1))
	}
	hf = append(hf, mysql.WithValuesIn("type", []uint32{0, req.Type}))
	hf = append(hf, mysql.WithOrder("updated_at DESC"))

	list, total, err := icp_apps.NewModel().EquityList(int(req.Page), int(req.PerPage), hf...)
	if err != nil {
		return err
	}

	for _, v := range list {
		var checkTime string
		if v.RecordTime.Valid {
			checkTime = v.RecordTime.Time.Format(utils.DateTimeLayout)
		}
		rsp.Items = append(rsp.Items, &pb.IcpAppListItem{
			Id:          v.Id,
			ParentId:    v.ParentId,
			CompanyName: v.Name,
			Name:        v.AppName,
			Icp:         v.Icp,
			CompanyType: int32(v.Type),
			Status:      int32(v.Status),
			Source:      v.Source,
			RecordTime:  checkTime,
			CreatedAt:   v.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   v.UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	rsp.Total = total
	return nil
}

type appIcp struct{}

func (appIcp) appIcpForceUpdate(ctx context.Context, item *icp_apps.Equity) error {
	req := &core.IcpAppRequest{Search: item.Icp, Force: true}
	// 子节点
	if item.ParentId != 0 {
		req.AppType = int32(item.Type)
		_, err := core.GetProtoCoreClient().IcpAppByIcp(ctx, req)
		return err
	}

	// 父节点
	for _, v := range []int32{icp_apps.App, icp_apps.MiniProgram, icp_apps.FastApp} {
		req.AppType = v
		_, err := core.GetProtoCoreClient().IcpAppByIcp(ctx, req)
		if err != nil {
			log.WithContextErrorf(ctx, "[icp-app] IcpAppByIcp error: %s", err.Error())
		}
	}
	return nil
}

func AppIcpUpdate(ctx context.Context, req *pb.IcpAppListItem) error {
	if req.Id == 0 {
		return errors.New("ID不可为空")
	}

	db := icp_apps.NewModel()
	info, err := db.EquityFirst(mysql.WithId(req.Id))
	if err != nil {
		return err
	}

	// 强制更新
	if req.ForceUpdate {
		ai := appIcp{}
		return ai.appIcpForceUpdate(ctx, &info)
	}

	info.Icp = req.Icp
	info.Name = req.CompanyName
	info.AppName = req.Name
	info.CompanyType = int(req.CompanyType)
	info.Status = int(req.Status)
	t, _ := time.Parse(utils.DateLayout, req.RecordTime)
	if !t.IsZero() {
		info.RecordTime = sql.NullTime{Time: t, Valid: true}
	}
	err = db.EquityUpdate(info)
	return err
}

func AppIcpCreate(_ context.Context, req *pb.IcpAppListItem) error {
	// check icp
	parent, child, err := utils.AppIcpCheck(req.Icp)
	if err != nil {
		return err
	}

	if child != "" && req.Type == 0 {
		return errors.New("ICP备案号子类型不可为空")
	}

	var recordTime time.Time
	if req.RecordTime != "" {
		recordTime, _ = time.Parse(utils.DateLayout, req.RecordTime)
	}

	db := icp_apps.NewModel()
	parentIcp, err := db.EquityFirst(mysql.WithColumnValue("icp", parent))
	if errors.Is(err, gorm.ErrRecordNotFound) {
		parentIcp = icp_apps.Equity{
			Name:        req.CompanyName,
			Icp:         parent,
			Source:      icp_apps.SourceImport,
			ParentId:    parentIcp.Id,
			CompanyType: int(req.CompanyType),
			Type:        0,
			Status:      int(req.Status),
			RecordTime:  sql.NullTime{Time: recordTime, Valid: !recordTime.IsZero()},
		}
		err = db.EquityCreate(&parentIcp)
	}
	if err != nil || child == "" {
		return err
	}

	_, err = db.EquityFirst(mysql.WithColumnValue("icp", req.Icp))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
	case err != nil:
		return err
	default:
		return errors.New("ICP备案号已存在")
	}

	err = db.EquityCreate(&icp_apps.Equity{
		Name:        req.CompanyName,
		Icp:         req.Icp,
		AppName:     req.Name,
		Source:      icp_apps.SourceImport,
		ParentId:    parentIcp.Id,
		CompanyType: int(req.CompanyType),
		Type:        0,
		Status:      int(req.Status),
		RecordTime:  sql.NullTime{Time: recordTime, Valid: !recordTime.IsZero()},
	})
	return err
}
