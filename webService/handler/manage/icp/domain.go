package icp

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"sync"

	"gorm.io/gorm"

	core "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company_icp"
	"micro-service/middleware/mysql/company_record"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/manage"
	pb "micro-service/webService/proto"
)

func CompanyIcpCreate(ctx context.Context, req *pb.ManageBeianCreateRequest) error {
	// validate: icp
	if icpNum := utils.GetIcpNumber(req.Icp); icpNum == "" {
		return errors.New("ICP备案号格式错误")
	}

	db := company_icp.NewCompanyIcpModel()
	var handler []mysql.HandleFunc
	handler = append(handler, mysql.WithSelect("id"), mysql.WithColumnValue("icp", req.Icp))
	if req.CompanyName != "" {
		handler = append(handler, mysql.WithColumnValue("name", req.CompanyName))
	}
	if req.Name != "" {
		handler = append(handler, mysql.WithColumnValue("domain", req.Name))
	}
	_, err := db.First(handler...)
	switch {
	case mysql.IsNotFound(err):
	case err != nil:
		return err
	default:
		return errors.New("ICP备案已存在，无法重复创建")
	}

	var parentICP company_icp.CompanyIcp
	index := strings.LastIndexByte(req.Icp, '-')
	if index > 0 {
		pICP := req.Icp[:index]
		parentICP, err = db.First(mysql.WithWhere("`icp`=? AND parent_id=0", pICP))
		switch {
		case mysql.IsNotFound(err):
			parentICP.Icp = pICP
			parentICP.Name = req.CompanyName
			parentICP.Source = company_icp.SourceImport
			err = db.Create(&parentICP)
			if err != nil {
				return err
			}
		case err != nil:
			return err
		}
	}

	recordTime, _ := utils.ParseTime(req.RecordTime)
	newICP := &company_icp.CompanyIcp{
		Name:       req.CompanyName, // 主办单位
		ParentId:   utils.If(index > 0, parentICP.Id, 0),
		Type:       int8(req.CompanyType), // 单位性质
		Domain:     req.Name,
		RecordTime: sql.NullTime{Time: recordTime, Valid: !recordTime.IsZero()},
		Icp:        req.Icp,
		Status:     int(req.Status),
		Source:     company_icp.SourceImport,
	}

	// 新建icp备案
	err = db.Create(newICP)
	if err != nil {
		return err
	}

	// 强制更新
	client := core.GetProtoCoreClient()
	if index > 0 {
		_, err = client.IcpByChild(ctx, &core.IcpRequest{Icp: req.Icp, Force: true}, microx.ServerTimeout(15))
	} else {
		_, err = client.Icp(ctx, &core.IcpRequest{Icp: req.Icp, Force: true}, microx.ServerTimeout(15))
	}
	return err
}

func CompanyIcpCreateByLevel(_ context.Context, req *pb.ManageBeianCreateRequest) error {
	req.Name = utils.If(req.ParentId == 0, "", req.Name)
	if req.ParentId != 0 && req.Name == "" {
		return errors.New("子节点ICP备案域名不可为空")
	}
	req.RecordTime = utils.If(req.ParentId == 0, "", req.RecordTime)
	if req.ParentId != 0 && req.RecordTime == "" {
		return errors.New("备案时间不能为空")
	}
	if req.RecordTime != "" {
		if _, err := utils.ParseTime(req.RecordTime); err != nil {
			return fmt.Errorf("备案时间:%s格式错误, 应为%s格式", req.RecordTime, utils.DateTimeLayout)
		}
	}

	// 查询挂载父级节点是否存在
	if req.ParentId > 0 {
		if _, err := companyIcpIdExists(uint64(req.ParentId)); err != nil {
			return err
		}
	}

	db := company_icp.NewCompanyIcpModel()
	handles := []mysql.HandleFunc{
		mysql.WithColumnValue("name", req.CompanyName),
		mysql.WithColumnValue("icp", req.Icp),
		mysql.WithColumnValue("parent_id", req.ParentId)}
	if req.Name != "" {
		mysql.WithColumnValue("domain", req.Name)
	}
	_, err := db.First(handles...)
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
	case err != nil:
		return err
	default:
		return errors.New("记录已存在，无法重复创建")
	}

	parsedRecordTime, _ := utils.ParseTime(req.RecordTime)
	err = db.Create(&company_icp.CompanyIcp{
		Name:       req.CompanyName,
		Icp:        req.Icp,
		ParentId:   uint64(req.ParentId),
		Type:       int8(req.CompanyType),
		Domain:     req.Name,
		RecordTime: sql.NullTime{Valid: !parsedRecordTime.IsZero(), Time: parsedRecordTime},
		Source:     req.Source,
		Status:     int(req.Status),
	})
	return err
}

func companyIcpQueryBuilder(req *pb.ManageBeianListRequest) []mysql.HandleFunc {
	var handlers = make([]mysql.HandleFunc, 0, 10)
	if req.Keyword != "" {
		handlers = append(handlers, company_icp.WithKeyword(req.Keyword))
	}
	if req.CompanyName != "" {
		handlers = append(handlers, mysql.WithLRLike("name", req.Name))
	}
	if req.Name != "" {
		handlers = append(handlers, mysql.WithLRLike("domain", req.Name))
	}
	if req.Icp != "" {
		handlers = append(handlers, mysql.WithLRLike("`icp`", req.Icp))
	}
	if len(req.CompanyType) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("type", req.CompanyType))
	}
	if len(req.Source) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("source", req.Source))
	}
	if req.Status != "" {
		handlers = append(handlers, mysql.WithColumnValue("status", req.Status))
	}
	if req.ParentId != -1 {
		handlers = append(handlers, mysql.WithColumnValue("parent_id", req.ParentId))
	}
	if len(req.RecordTime) == 2 {
		d1, d2, _ := manage.ProcessDateRange(req.RecordTime)
		handlers = append(handlers, mysql.WithBetween("record_time", d1, d2))
	}

	return handlers
}

func CompanyIcpList(req *pb.ManageBeianListRequest, rsp *pb.ManageBeianListResponse) error {
	handlers := companyIcpQueryBuilder(req)
	// handlers = append(handlers, mysql.WithOrder("`name`, `type`"))
	list, total, err := company_icp.NewCompanyIcpModel().List(int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}

	rsp.Total = total
	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	rsp.Items = make([]*pb.ManageBeianListResponse_BeianInfo, len(list))
	for i := range list {
		rt := utils.If(list[i].RecordTime.Valid, list[i].RecordTime.Time.Format(utils.DateTimeLayout), "")
		rsp.Items[i] = &pb.ManageBeianListResponse_BeianInfo{
			Id:          list[i].Id,
			CompanyName: list[i].Name, // 企业名称
			ParentId:    list[i].ParentId,
			CompanyType: int64(list[i].Type),
			Icp:         list[i].Icp,
			Name:        list[i].Domain,
			Status:      int32(list[i].Status),
			Source:      list[i].Source,
			RecordTime:  rt,
			CreatedAt:   list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   list[i].UpdatedAt.Format(utils.DateTimeLayout),
		}
	}
	return nil
}

func companyIcpIdExists(id uint64) (company_icp.CompanyIcp, error) {
	item, err := company_icp.NewCompanyIcpModel().First(mysql.WithId(id))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return item, fmt.Errorf("查询ID: %d不存在", id)
	case err != nil:
		return item, err
	}
	return item, nil
}

func CompanyIcpUpdate(ctx context.Context, req *pb.ManageBeianCreateRequest) error {
	if len(req.Ids) == 0 {
		return errors.New("id不可为空")
	}

	if req.Force == 1 {
		wg := sync.WaitGroup{}
		for _, v := range req.Ids {
			wg.Add(1)
			go func(id uint64) {
				defer wg.Done()
				item, err := companyIcpIdExists(id)
				if err != nil {
					log.WithContextWarnf(ctx, "[ICP备案管理-Domain] query domain-icp id: %d failed: %v", id, err)
				} else {
					if item.ParentId > 0 {
						_, err = core.GetProtoCoreClient().IcpByChild(ctx, &core.IcpRequest{Icp: item.Icp, Force: true})
					} else {
						_, err = core.GetProtoCoreClient().Icp(ctx, &core.IcpRequest{Icp: item.Icp, GetEquals: true, Force: true})
					}
					if err != nil {
						log.WithContextWarnf(ctx, "[ICP备案管理-Domain] query icp: %s failed by core service: %v", item.Icp, err)
					}
				}
			}(v)
		}
		wg.Wait()
		return nil
	}

	item, err := companyIcpIdExists(req.Ids[0])
	if err != nil {
		return err
	}

	icpNum := utils.GetIcpNumber(req.Icp)
	if icpNum == "" {
		return errors.New("icp备案号格式错误")
	}

	// check
	req.Name = utils.If(item.ParentId == 0, "", req.Name)
	req.RecordTime = utils.If(item.ParentId == 0, "", req.RecordTime)
	parsedTime, _ := utils.ParseTime(req.RecordTime)
	item.Name = req.CompanyName
	item.Type = int8(req.CompanyType)
	item.Icp = req.Icp
	item.Domain = req.Name
	item.Status = int(req.Status)
	item.Source = req.Source
	item.RecordTime = sql.NullTime{Valid: !parsedTime.IsZero(), Time: parsedTime}

	return company_icp.NewCompanyIcpModel().Updates(item)
}

// CompanyIcpDelete delete company icp by conditions
//
// Deprecated: doesn't need it
func CompanyIcpDelete(req *pb.ManageBeianListRequest) error {
	handlers := companyIcpQueryBuilder(req)
	handlers = append(handlers, mysql.WithSelect("id"))
	list, err := company_icp.NewCompanyIcpModel().ListAll(handlers...)
	if len(list) == 0 {
		return err
	}

	var ids = make([]uint64, 0, len(list))
	for i := range list {
		ids = append(ids, list[i].Id)
	}
	err = company_icp.NewCompanyIcpModel().DeleteByIds(ids)
	if err != nil {
		return err
	}

	recorder := company_record.NewCompanyRecorder()
	records, err := recorder.ListAll(mysql.WithValuesIn("`icp_id`", ids))
	if err != nil {
		return err
	}
	for i := range records {
		records[i].IcpId = 0
	}
	err = recorder.Save(records...)
	return err
}
