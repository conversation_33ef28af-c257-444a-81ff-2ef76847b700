package icp

import (
	"context"
	"testing"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"

	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	pb "micro-service/webService/proto"
)

func initCfg() {
	godotenv.Load("./../../../.env")
	cfg.InitLoadCfg()
	mysql.GetInstance(cfg.LoadMysql())
}

func Test_CompanyIcpCreate(t *testing.T) {
	initCfg()

	err := CompanyIcpCreate(context.TODO(), &pb.ManageBeianCreateRequest{
		Icp: "123",
	})
	assert.NotNil(t, err)
}
