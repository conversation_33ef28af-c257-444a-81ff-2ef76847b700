package manage

import (
	"errors"
	"time"

	"micro-service/pkg/utils"
)

func ProcessDateRange(l []string) (d1, d2 string, err error) {
	t1, t2, err := ParseDateToTime(l)
	if err != nil {
		return "", "", err
	}

	d1 = t1.Format(utils.DateLayout)
	d2 = t2.Format(utils.DateLayout)
	return d1, d2, nil
}

func ParseDateToTime(l []string) (t1, t2 time.Time, err error) {
	if len(l) == 0 {
		return time.Time{}, time.Time{}, errors.New("date range is empty")
	}

	var d1 = l[0]
	var d2 string
	if len(l) > 1 {
		d2 = l[1]
	}

	t2 = time.Now()
	t1, _ = time.Parse(utils.DateLayout, d1)
	if d2 != "" {
		t2, _ = time.Parse(utils.DateLayout, d2)
		t2 = t2.Add(24 * time.Hour)
	}
	return t1, t2, nil
}
