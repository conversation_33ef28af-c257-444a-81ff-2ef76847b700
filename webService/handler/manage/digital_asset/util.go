package digital_asset

import (
	"errors"
	"path"
	"strings"

	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
)

// date format: 2006-01-02

const (
	platformIOS     = "1"
	platformAndroid = "2"
)

func convertAppType(s string) []string {
	if s == platformIOS {
		return []string{"apple"}
	}
	return []string{"android", "安卓"}
}

func checkDigitalType[T int | uint64 | int64](dt T) error {
	switch dt {
	case sourceWechat, sourceApps:
		return nil
	}
	return errors.New("查询类型错误")
}

func assetShowURL(u string) string {
	if !strings.HasPrefix(u, storage.GetPublicStoragePath()) {
		return u
	}
	name, ext := utils.GetFileName(u)
	d, _ := utils.DownloadFileEncrypt(u, name+ext, "", false)
	return path.Join(storage.GetDownloadPrefix(), d) + ext
}
