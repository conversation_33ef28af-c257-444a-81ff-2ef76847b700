package digital_asset

import (
	"testing"

	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
)

func initCfg() {
	godotenv.Load("./../../../.env")

	cfg.InitLoadCfg()
	log.Init()
	mysql.GetInstance(cfg.LoadMysql())
}

//func Test_digitalAssetsWechatImport(t *testing.T) {
//	initCfg()
//
//	filepath := "./公众号数据导入模板.xlsx"
//	var da = digitalAsset{}
//	err := da.digitalAssetsWechatImport(filepath)
//	assert.Nil(t, err)
//}

func Test_digitalAssetsAppImport(t *testing.T) {
	initCfg()

	filepath := "./" + utils.RandAlphabet(15) + ".xlsx"
	var da = digitalAsset{}
	err := da.digitalAssetsAppImport(filepath)
	assert.Equal(t, excel.ErrFileNotFound, err)
}
