package digital_asset

import (
	"errors"
	"fmt"
	"time"

	"micro-service/middleware/mysql/apps"
	"micro-service/middleware/mysql/official_account"
	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
)

// 数字资产导入: 公众号，app

func waitSync(importFile string) error {
	// max wait time: 30s
	for i := 0; i < 120; i++ {
		if utils.FileIsExist(importFile) {
			return nil
		}
		time.Sleep(250 * time.Millisecond)
	}
	return fmt.Errorf("file: %s not found", importFile)
}

var titleLine = []string{"name", "account", "company_name", "describe", "is_online"}

type digitalImportAccountItem struct {
	Name        string `json:"name"`         // 公众号名称，必填
	Account     string `json:"account"`      // 微信号，必填
	CompanyName string `json:"company_name"` // 账号主体，必填
	Describe    string `json:"describe"`     // 简介
	IsOnline    string `json:"is_online"`    // 是否在线
}

func (digitalAsset) checkImportAccount(line []string) (*digitalImportAccountItem, error) {
	var item digitalImportAccountItem
	_ = excel.LineDecode(line, titleLine, &item)
	if item.Name == "" {
		return nil, errors.New("公众号名称不可为空")
	}
	if item.Account == "" {
		return nil, errors.New("微信号不可为空")
	}
	if item.CompanyName == "" {
		return nil, errors.New("账号主体不可为空")
	}

	return &item, nil
}

// digitalAssetsWechatImport 数字资产微信公众号数据导入
func (da digitalAsset) digitalAssetsWechatImport(fp string) error {
	log.Infof("[Digital Assets] Read excel WeChat data from file: %s", fp)
	excelData, err := excel.ReadExcel(fp)
	if len(excelData) == 0 {
		return err
	}

	var accountKeywordMap = make(map[string]*digitalImportAccountItem, len(excelData))
	for i := 0; i < len(excelData); i++ {
		// skip excel first line
		if i == 0 {
			continue
		}
		account, errImport := da.checkImportAccount(excelData[i])
		if errImport != nil {
			return fmt.Errorf("第%d行导入数据校验未通过, %s", i+1, errImport.Error())
		}
		// 去重
		accountKeywordMap[account.Account] = account
	}

	var accountDb = official_account.NewOfficialAccountModel()
	values := utils.GetMapValues[string, *digitalImportAccountItem](accountKeywordMap)
	accounts := make([]*official_account.OfficialAccount, 0, len(values))
	for _, v := range values {
		accounts = append(accounts, &official_account.OfficialAccount{
			Name:        v.Name,
			Account:     v.Account,
			CompanyName: v.CompanyName,
			QRCode:      wechatQrUrl(v.Account),
			Platform:    official_account.PlatformImport,
			Describe:    v.Describe,
			IsOnline:    utils.If(v.IsOnline == "在线", official_account.Online, official_account.Offline), //nolint:goconst,gocritic
		})
	}
	// 导入总库资产
	err = accountDb.BatchUpdateOrInsert("", accounts)
	if err != nil {
		return err
	}
	return nil
}

type digitalImportAppItem struct {
	Name        string `json:"name" validate:"required" zh:"应用名称"`         // 应用名称
	Url         string `json:"url" validate:"required" zh:"下载链接"`          // 下载链接
	CompanyName string `json:"company_name" validate:"required" zh:"企业名称"` // 企业名称
	IsOnline    string `json:"is_online"`                                  // 是否在线
}

var appTitleLine = []string{"name", "url", "company_name", "is_online"}

func (digitalAsset) checkImportApp(line []string) (*digitalImportAppItem, error) {
	var lineItem digitalImportAppItem
	_ = excel.LineDecode(line, appTitleLine, &lineItem)
	if ok, msg := validate.Validator(&lineItem); !ok {
		return nil, errors.New(msg)
	}
	return &lineItem, nil
}

// import fields: name, url, company_name, is_online, keyword
func (da digitalAsset) digitalAssetsAppImport(fp string) error {
	log.Infof("[Digital Assets] Read excel app data from file: %s", fp)
	excelData, err := excel.ReadExcel(fp)
	if len(excelData) == 0 {
		return err
	}

	distinctApps := make(map[string]*digitalImportAppItem, len(excelData))
	for i := 0; i < len(excelData); i++ {
		// skip excel first line
		if i == 0 {
			continue
		}
		item, errImport := da.checkImportApp(excelData[i])
		if errImport != nil {
			return fmt.Errorf("第%d行导入数据校验未通过: %s", i+1, errImport.Error())
		}
		k := utils.Md5Hash(digitalImportAppItem{Name: item.Name, CompanyName: item.CompanyName})
		distinctApps[k] = item
	}

	appDb := apps.NewAppsModel()
	insertApps := utils.GetMapValues(distinctApps)
	companyApps := make(map[string][]*apps.Apps, len(distinctApps))
	for _, val := range insertApps {
		l, ok := companyApps[val.CompanyName]
		appItem := &apps.Apps{
			Name:        val.Name,
			Logo:        "",
			Url:         val.Url,
			Category:    "",
			CompanyName: val.CompanyName,
			Platform:    apps.PlatformImport,
			Developer:   val.CompanyName,
			IsOnline:    utils.If(val.IsOnline == "在线", apps.Online, apps.Offline),
		}
		if !ok {
			l = make([]*apps.Apps, 0, 20)
		}
		l = append(l, appItem)
		companyApps[val.CompanyName] = l
	}

	for k, v := range companyApps {
		errUpsert := appDb.UpsertByCompany(k, v)
		if errUpsert != nil {
			log.Warnf("%v", errUpsert)
		}
	}

	return nil
}
