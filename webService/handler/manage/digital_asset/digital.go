package digital_asset

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"sync"
	"time"

	"github.com/spf13/cast"
	"gorm.io/gorm"

	corePb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/apps"
	"micro-service/middleware/mysql/official_account"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/manage"
	pb "micro-service/webService/proto"
)

type digitalAsset struct{}

// 数字资产来源
// 1-微信公众号
// 2-app

const (
	sourceWechat = iota + 1 // 微信公众号
	sourceApps              // app
)

func (digitalAsset) digitalListBuilder(req *pb.ManageDigitalKeywordListRequest) []mysql.HandleFunc {
	if len(req.Ids) > 0 {
		return []mysql.HandleFunc{mysql.WithValuesIn("id", req.Ids)}
	}

	var handlers []mysql.HandleFunc
	if req.Search != "" {
		handlers = append(handlers, mysql.WithLRLike("keyword", req.Search))
	}
	if req.Keyword != "" {
		handlers = append(handlers, mysql.WithLRLike("keyword", req.Keyword))
	}
	if req.Progress != 0 {
		handlers = append(handlers, mysql.WithWhere(fmt.Sprintf("`progress` %s 100", utils.If(req.Progress == 1, "<", "="))))
	}
	if len(req.CreatedAt) == 2 {
		d1, d2, _ := manage.ProcessDateRange(req.CreatedAt)
		handlers = append(handlers, mysql.WithBetween("created_at", d1, d2))
	}
	if len(req.UpdatedAt) == 2 {
		d1, d2, _ := manage.ProcessDateRange(req.UpdatedAt)
		handlers = append(handlers, mysql.WithBetween("updated_at", d1, d2))
	}
	return handlers
}

// DigitalKeywordInfo 任务管理：获取任务详情
func DigitalKeywordInfo(_ context.Context, req *pb.ManageDigitalKeywordUpsertRequest, rsp *pb.ManageDigitalKeywordInfoResponse) error {
	if req.Id == 0 {
		return errors.New("查询任务不可为空")
	}

	da := digitalAsset{}
	var err error
	switch req.Source {
	case sourceWechat:
		err = da.wechatKeywordInfo(req.Id, rsp)
	case sourceApps:
		err = da.appKeywordInfo(req.Id, rsp)
	}

	return err
}

func (digitalAsset) wechatKeywordInfo(id uint64, rsp *pb.ManageDigitalKeywordInfoResponse) error {
	info, err := official_account.NewHistoryModel().First(mysql.WithId(id))
	if err != nil {
		return err
	}

	rsp.Id = id
	rsp.Name = info.Keyword
	rsp.Progress = info.Progress
	rsp.CreatedAt = info.CreatedAt.Format(utils.DateTimeLayout)
	rsp.UpdatedAt = info.UpdatedAt.Format(utils.DateTimeLayout)
	return nil
}

func (digitalAsset) appKeywordInfo(id uint64, rsp *pb.ManageDigitalKeywordInfoResponse) error {
	info, err := apps.NewHistoryModel().First(mysql.WithId(id))
	if err != nil {
		return err
	}

	rsp.Id = id
	rsp.Name = info.Keyword
	rsp.Progress = info.Progress
	rsp.CreatedAt = info.CreatedAt.Format(utils.DateTimeLayout)
	rsp.UpdatedAt = info.UpdatedAt.Format(utils.DateTimeLayout)
	return nil
}

// DigitalKeywordUpsert 任务管理：强制刷新或更新任务
func DigitalKeywordUpsert(ctx context.Context, req *pb.ManageDigitalKeywordUpsertRequest) error {
	if len(req.Ids) == 0 && len(req.Name) == 0 {
		return errors.New("更新任务或下发关键词不可为空")
	}

	var da = digitalAsset{}
	// 强制刷新
	if len(req.Ids) > 0 {
		if req.Force != "1" {
			return nil
		}
		// 强制刷新任务
		go da.asyncDigitalAssetForceUpdate(ctx, req)
		return nil
	}

	// 下发数字资产任务
	keywords := utils.ListDistinctNonZero(req.Name)
	if len(keywords) == 0 {
		return errors.New("下发任务关键词不可为空")
	}
	for _, v := range keywords {
		keyword := v
		switch req.Source {
		case sourceWechat:
			go func() { _ = da.callDigitalWechatUpsert(ctx, 0, keyword) }()
		case sourceApps:
			go func() { _ = da.callDigitalAppUpsert(ctx, 0, keyword) }()
		}
	}
	return nil
}

func (da digitalAsset) asyncDigitalAssetForceUpdate(ctx context.Context, req *pb.ManageDigitalKeywordUpsertRequest) {
	type defCh struct {
		err error
		id  uint64
	}

	ch := make(chan defCh)
	go func() {
		wg := sync.WaitGroup{}
		for _, v := range req.Ids {
			wg.Add(1)
			go func(id uint64) {
				defer wg.Done()
				var err error
				ctxF, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()
				if req.Source == sourceWechat {
					err = da.callDigitalWechatUpsert(ctxF, id, "")
				} else if req.Source == sourceApps {
					err = da.callDigitalAppUpsert(ctxF, id, "")
				}
				ch <- defCh{err: err, id: id}
			}(v)
		}
		wg.Wait()
		close(ch)
	}()
	for x := range ch {
		if x.err != nil {
			log.Infof("[数据资产总库] 强制刷新关键词, task_id: %d, task_source: %d", x.id, req.Source)
		}
	}
}

func (digitalAsset) callDigitalWechatUpsert(ctx context.Context, taskId uint64, keyword string) error {
	if cfg.IsLocalClient() {
		return nil
	}

	if taskId != 0 {
		item, err := official_account.NewHistoryModel().First(mysql.WithColumnValue("`id`", taskId))
		if err != nil {
			return err
		}
		keyword = item.Keyword
	}

	log.WithContextInfof(ctx, "[数据资产总库-微信公众号] 关键词：%s upsert", keyword)
	var param = &corePb.GitHubCodeRequest{Keyword: []string{keyword}, Force: 1}
	_, err := corePb.GetProtoCoreClient().DigitalWechatTaskUpsert(ctx, param)
	if err != nil {
		log.WithContextErrorf(ctx, "[数据资产总库-微信公众号] callDigitalWechatUpsert failed: %v", err)
	}
	return nil
}

func (digitalAsset) callDigitalAppUpsert(ctx context.Context, taskId uint64, keyword string) error {
	if cfg.IsLocalClient() {
		return nil
	}

	if taskId != 0 {
		item, err := apps.NewHistoryModel().First(mysql.WithColumnValue("id", taskId))
		if err != nil {
			return err
		}
		keyword = item.Keyword
	}

	log.WithContextInfof(ctx, "[数据资产总库-app] 关键词：%s upsert", keyword)
	var param = &corePb.GitHubCodeRequest{Keyword: []string{keyword}, Force: 1}
	_, err := corePb.GetProtoCoreClient().DigitalAndroidAppTaskCreate(ctx, param)
	if err != nil {
		log.WithContextErrorf(ctx, "[数据资产总库-app] callDigitalAppUpsert failed: %v", err)
	}
	return nil
}

// DigitalKeywordList 任务管理：任务列表
func DigitalKeywordList(req *pb.ManageDigitalKeywordListRequest, rsp *pb.ManageDigitalKeywordListResponse) error {
	var err error
	var da = digitalAsset{}
	handlers := da.digitalListBuilder(req)
	handlers = append(handlers, mysql.WithOrder("`updated_at` DESC"))
	switch req.Source {
	case sourceWechat:
		l, c, e := official_account.NewHistoryModel().List(int(req.Page), int(req.PerPage), handlers...)
		for _, v := range l {
			rsp.Items = append(rsp.Items, &pb.ManageDigitalKeywordInfoResponse{
				Id:        v.Id,
				Name:      v.Keyword,
				Progress:  v.Progress,
				CreatedAt: v.CreatedAt.Format(utils.DateTimeLayout),
				UpdatedAt: v.UpdatedAt.Format(utils.DateTimeLayout),
			})
		}
		err = e
		rsp.Total = c
	case sourceApps:
		l, c, e := apps.NewHistoryModel().List(int(req.Page), int(req.PerPage), handlers...)
		for _, v := range l {
			rsp.Items = append(rsp.Items, &pb.ManageDigitalKeywordInfoResponse{
				Id:        v.Id,
				Name:      v.Keyword,
				Progress:  v.Progress,
				CreatedAt: v.CreatedAt.Format(utils.DateTimeLayout),
				UpdatedAt: v.UpdatedAt.Format(utils.DateTimeLayout),
			})
		}
		err = e
		rsp.Total = c
	}

	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	return err
}

// DigitalKeywordDelete 任务管理：删除任务
func DigitalKeywordDelete(req *pb.ManageDigitalKeywordListRequest) error {
	var err error
	var da = digitalAsset{}
	var ids = make([]uint64, 0)
	handlers := da.digitalListBuilder(req)
	switch req.Source {
	case sourceWechat:
		l, _, e := official_account.NewHistoryModel().List(0, 0, handlers...)
		for i := range l {
			ids = append(ids, l[i].Id)
		}
		err = e
	case sourceApps:
		l, _, e := apps.NewHistoryModel().List(0, 0, handlers...)
		for i := range l {
			ids = append(ids, l[i].Id)
		}
		err = e
	}
	if len(ids) == 0 {
		return err
	}

	// 删除关联记录但不删除总库数字资产
	switch req.Source {
	case sourceWechat:
		err = official_account.NewHistoryModel().DeleteByIds(ids)
		if err == nil {
			err = official_account.NewOfficialAccountModel().DeleteRelation(mysql.WithValuesIn("`official_account_history_id`", ids))
		}
	case sourceApps:
		err = apps.NewHistoryModel().Delete(mysql.WithValuesIn("`id`", ids))
		if err == nil {
			err = apps.NewAppsModel().RelationDelete(mysql.WithValuesIn("`history_id`", ids))
		}
	}
	return err
}

// DigitalKeywordResultList 任务详情：数字资产任务结果
func DigitalKeywordResultList(req *pb.ManageDigitalAssetsResultListRequest, rsp *pb.ManageDigitalAssetsResultListResponse) error {
	var err error
	da := digitalAsset{}
	switch req.Source {
	case sourceWechat:
		err = da.digitalKeywordWechatResult(req, rsp)
	case sourceApps:
		err = da.digitalKeywordAppsResult(req, rsp)
	default:
		return errors.New("查询类型错误")
	}

	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	return err
}

// digitalKeywordWechatResult 任务详情：微信公众号任务结果
func (da digitalAsset) digitalKeywordWechatResult(req *pb.ManageDigitalAssetsResultListRequest, rsp *pb.ManageDigitalAssetsResultListResponse) error {
	h := da.keywordResultListBuilder(req)
	h = append(h, mysql.WithColumnValue("`official_account_history_id`", req.TaskId))

	accounts, total, err := official_account.NewOfficialAccountModel().ListAccounts(int(req.Page), int(req.PerPage), h...)
	for i := range accounts {
		rsp.Items = append(rsp.Items, &pb.ManageDigitalAssetsResultItem{
			Id:          accounts[i].Id,
			Name:        accounts[i].Name,
			Account:     accounts[i].Account,
			Logo:        accounts[i].QRCode,
			CompanyName: accounts[i].CompanyName,
			Platform:    accounts[i].Platform,
			IsOnline:    cast.ToString(accounts[i].IsOnline),
			Description: accounts[i].Describe,
			CreatedAt:   accounts[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   accounts[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	rsp.Total = total
	return err
}

func (da digitalAsset) digitalKeywordAppsResult(req *pb.ManageDigitalAssetsResultListRequest, rsp *pb.ManageDigitalAssetsResultListResponse) error {
	h := da.keywordResultListBuilder(req)
	h = append(h, mysql.WithColumnValue("history_id", req.TaskId))
	list, total, err := apps.NewAppsModel().ListByHistory(int(req.Page), int(req.PerPage), h...)
	rsp.Items = make([]*pb.ManageDigitalAssetsResultItem, len(list))
	for i, v := range list {
		rsp.Items[i] = &pb.ManageDigitalAssetsResultItem{
			Id:          v.Id,
			Name:        v.Name,
			Logo:        assetShowURL(v.Logo),
			Url:         v.Url,
			CompanyName: v.CompanyName,
			Platform:    v.Platform,
			IsOnline:    strconv.Itoa(v.IsOnline),
			CreatedAt:   v.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   v.UpdatedAt.Format(utils.DateTimeLayout),
		}
	}

	rsp.Total = total
	return err
}

// 任务详情：资产查询
func (digitalAsset) keywordResultListBuilder(req *pb.ManageDigitalAssetsResultListRequest) []mysql.HandleFunc {
	if ids := utils.ListDistinctNonZero(req.Ids); len(ids) > 0 {
		return []mysql.HandleFunc{mysql.WithValuesIn("id", ids)}
	}

	h := make([]mysql.HandleFunc, 0, 10)
	if req.Search != "" {
		search := "%" + req.Search + "%"
		h = append(h, mysql.WithWhere("`name` LIKE ? OR `company_name` LIKE ?", search, search))
	}
	if req.Name != "" {
		h = append(h, mysql.WithLRLike("`name`", req.Name))
	}
	if req.Account != "" && req.Source == sourceWechat {
		h = append(h, mysql.WithLRLike("`account`", req.Account))
	}
	if req.Platform != "" && req.Source == sourceApps {
		h = append(h, mysql.WithValuesIn("`platform`", convertAppType(req.Platform)))
	}
	if len(req.CompanyName) > 0 {
		h = append(h, mysql.WithValuesIn("company_name", req.CompanyName))
	}
	if req.IsOnline != 0 {
		h = append(h, mysql.WithColumnValue("`is_online`", req.IsOnline))
	}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := manage.ProcessDateRange(req.CreatedAt)
		table := utils.If[string](req.Source == sourceApps, apps.RelationTable, official_account.RelationTableName)
		h = append(h, mysql.WithBetween2(table, "created_at", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := manage.ProcessDateRange(req.UpdatedAt)
		table := utils.If[string](req.Source == sourceApps, apps.RelationTable, official_account.RelationTableName)
		h = append(h, mysql.WithBetween2(table, "updated_at", t1, t2))
	}

	return h
}

// DigitalAssetsResultList 资产管理：总库资产列表
func DigitalAssetsResultList(req *pb.ManageDigitalAssetsResultListRequest, rsp *pb.ManageDigitalAssetsResultListResponse) error {
	if err := checkDigitalType(int(req.Source)); err != nil {
		return err
	}

	var err error
	var da = digitalAsset{}
	switch req.Source {
	case sourceWechat:
		err = da.digitalAssetsWechatResultList(req, rsp)
	case sourceApps:
		err = da.digitalAssetsAppResultList(req, rsp)
	}
	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	return err
}

func (digitalAsset) digitalAssetsAppResultBuilder(req *pb.ManageDigitalAssetsResultListRequest) []mysql.HandleFunc {
	var h []mysql.HandleFunc
	if req.Search != "" {
		h = append(h, apps.WithKeyword(req.Search, "name", "company_name"))
	}
	if req.Name != "" {
		h = append(h, mysql.WithLRLike("name", req.Name))
	}
	if len(req.CompanyName) > 0 {
		h = append(h, mysql.WithValuesIn("company_name", req.CompanyName))
	}
	if req.Platform != "" && req.Source == sourceApps {
		h = append(h, mysql.WithValuesIn("platform", convertAppType(req.Platform)))
	}
	if req.IsOnline != 0 {
		h = append(h, mysql.WithColumnValue("is_online", req.IsOnline))
	}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := manage.ProcessDateRange(req.CreatedAt)
		h = append(h, mysql.WithBetween("created_at", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := manage.ProcessDateRange(req.UpdatedAt)
		h = append(h, mysql.WithBetween("updated_at", t1, t2))
	}
	return h
}

// 资产管理：数字资产app总库资产列表
func (da digitalAsset) digitalAssetsAppResultList(req *pb.ManageDigitalAssetsResultListRequest, rsp *pb.ManageDigitalAssetsResultListResponse) error {
	h := da.digitalAssetsAppResultBuilder(req)
	h = append(h, mysql.WithOrder("`updated_at` DESC"))
	list, total, err := apps.NewAppsModel().List(int(req.Page), int(req.PerPage), h...)
	for i := range list {
		rsp.Items = append(rsp.Items, &pb.ManageDigitalAssetsResultItem{
			Id:          list[i].Id,
			Name:        list[i].Name,
			Logo:        assetShowURL(list[i].Logo),
			CompanyName: list[i].CompanyName,
			Platform:    list[i].Platform,
			IsOnline:    strconv.Itoa(list[i].IsOnline),
			Url:         list[i].Url,
			Description: list[i].Category,
			CreatedAt:   list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   list[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	rsp.Total = total
	return err
}

// 资产管理：数字资产微信公众号总库资产列表
func (da digitalAsset) digitalAssetsWechatResultList(req *pb.ManageDigitalAssetsResultListRequest, rsp *pb.ManageDigitalAssetsResultListResponse) error {
	h := da.digitalAssetsWechatResultBuilder(req)
	h = append(h, mysql.WithOrder("`updated_at` DESC"))
	db := official_account.NewOfficialAccountModel()
	l, total, err := db.List(int(req.Page), int(req.PerPage), h...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.ManageDigitalAssetsResultItem{
			Id:          l[i].Id,
			Name:        l[i].Name,
			Account:     l[i].Account,
			Logo:        l[i].QRCode,
			CompanyName: l[i].CompanyName,
			Platform:    l[i].Platform,
			Description: l[i].Describe,
			IsOnline:    cast.ToString(l[i].IsOnline),
			CreatedAt:   l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	rsp.Total = total
	return err
}

// DigitalAssetsResultDelete 资产管理：总库资产删除
func DigitalAssetsResultDelete(req *pb.ManageDigitalAssetsResultListRequest) error {
	if err := checkDigitalType(req.Source); err != nil {
		return err
	}

	var err error
	var da = digitalAsset{}
	switch req.Source {
	case sourceWechat:
		err = da.digitalAssetsWechatResultDelete(req)
	case sourceApps:
		err = da.digitalAssetsAppResultDelete(req)
	}
	return err
}

func (digitalAsset) digitalAssetsWechatResultBuilder(req *pb.ManageDigitalAssetsResultListRequest) []mysql.HandleFunc {
	h := make([]mysql.HandleFunc, 0)
	if req.Search != "" { // 公众号名称，账号主体，关键词
		q := "%" + req.Search + "%"
		h = append(h, mysql.WithWhere("`name` LIKE ? OR `company_name` LIKE ?", q, q))
	}
	if req.Name != "" {
		h = append(h, mysql.WithLRLike("name", req.Name))
	}
	if req.Account != "" {
		h = append(h, mysql.WithLRLike("account", req.Account))
	}
	if len(req.CompanyName) > 0 {
		h = append(h, mysql.WithValuesIn("company_name", req.CompanyName))
	}
	if req.IsOnline != 0 {
		h = append(h, mysql.WithColumnValue("is_online", req.IsOnline))
	}
	if len(req.CreatedAt) == 2 {
		t1, t2, _ := manage.ProcessDateRange(req.CreatedAt)
		h = append(h, mysql.WithBetween("created_at", t1, t2))
	}
	if len(req.UpdatedAt) == 2 {
		t1, t2, _ := manage.ProcessDateRange(req.UpdatedAt)
		h = append(h, mysql.WithBetween("updated_at", t1, t2))
	}
	return h
}

// 资产管理：微信公众号总库资产删除
func (da digitalAsset) digitalAssetsWechatResultDelete(req *pb.ManageDigitalAssetsResultListRequest) error {
	var ids = utils.ListDistinctNonZero(req.Ids)
	db := official_account.NewOfficialAccountModel()
	if len(ids) == 0 {
		h := da.digitalAssetsWechatResultBuilder(req)
		h = append(h, mysql.WithSelect("id"))
		l, _, err := db.List(0, 0, h...)
		if len(l) == 0 {
			return err
		}
		ids = utils.ListColumn[uint64](l, func(x official_account.OfficialAccount) uint64 { return x.Id })
	}
	if len(ids) == 0 {
		return nil
	}

	// 删除关联记录
	err := db.DeleteRelation(mysql.WithValuesIn("`official_account_id`", ids))
	if err != nil {
		return err
	}

	// 删除总库资产信息
	err = db.Delete(mysql.WithValuesIn("`id`", ids))
	return err
}

// 资产管理：app总库资产删除
func (da digitalAsset) digitalAssetsAppResultDelete(req *pb.ManageDigitalAssetsResultListRequest) error {
	ids := utils.ListDistinctNonZero(req.Ids)
	apper := apps.NewAppsModel()
	if len(ids) == 0 {
		h := da.digitalAssetsAppResultBuilder(req)
		h = append(h, mysql.WithSelect("id"))
		list, _, err := apper.List(0, 0, h...)
		if len(list) == 0 {
			return err
		}
		ids = utils.ListColumn(list, func(x apps.Apps) uint64 { return x.Id })
	}

	if len(ids) == 0 {
		return nil
	}

	// 删除关联关系中的总库资产
	err := apper.RelationDelete(mysql.WithValuesIn("app_id", ids))
	if err != nil {
		return err
	}

	// 总库资产删除
	err = apper.AppsDelete(mysql.WithValuesIn("id", ids))
	return err
}

// 拼接WeChat账号二维码
func wechatQrUrl(account string) string {
	const qrPrefix = "https://open.weixin.qq.com/qr/code?username="
	return utils.If(account != "", qrPrefix+account, "")
}

func (digitalAsset) digitalAssetsWechatCreate(req *pb.ManageDigitalAssetsResultItem) error {
	dbo := official_account.NewOfficialAccountModel()
	asset := &official_account.OfficialAccount{
		Name:        req.Name,
		Account:     req.Account,
		QRCode:      utils.If(req.Logo != "", req.Logo, wechatQrUrl(req.Account)),
		CompanyName: req.CompanyName,
		Platform:    official_account.PlatformImport, // 手动导入
		IsOnline:    cast.ToInt(req.IsOnline),
		Describe:    req.Description,
	}
	err := dbo.Upsert([]*official_account.OfficialAccount{asset})
	if err != nil {
		return err
	}

	h, err := official_account.NewHistoryModel().
		First(mysql.WithColumnValue("keyword", req.CompanyName), mysql.WithSelect("id"))
	if err != nil {
		return nil
	}

	re, err := dbo.FirstRelation(mysql.WithColumnValue("official_account_history_id", h.Id),
		mysql.WithColumnValue("official_account_id", asset.Id))
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
	case err != nil:
		return nil
	}

	re.OfficialAccountHistoryId = h.Id
	re.OfficialAccountId = asset.Id
	return dbo.SaveRelation(re)
}

func (digitalAsset) digitalAssetsAppCreate(req *pb.ManageDigitalAssetsResultItem) error {
	db := apps.NewAppsModel()
	info, errF := db.First(mysql.WithWhere("`name`=? AND `company_name`=? AND `platform`=?", req.Name, req.CompanyName, req.Platform))
	switch {
	case errors.Is(errF, gorm.ErrRecordNotFound):
	case errF != nil:
		return errF
	}
	info.Name = req.Name
	info.Platform = utils.If[string](req.Platform == platformIOS, apps.PlatformApple, apps.PlatformAndroid)
	info.CompanyName = req.CompanyName
	info.Url = utils.If(req.Url != "", req.Url, info.Url)
	info.Logo = utils.If(req.Logo == "", info.Logo, req.Logo)
	info.Developer = req.CompanyName
	info.IsOnline = utils.If(req.IsOnline != "", cast.ToInt(req.IsOnline), info.IsOnline)
	err := db.AppsUpsert([]apps.Apps{info})
	if err != nil {
		return err
	}
	return nil
}

// DigitalAssetsResultCreate 资产管理：新建总库数字资产
func DigitalAssetsResultCreate(req *pb.ManageDigitalAssetsResultItem) error {
	if err := checkDigitalType(req.Source); err != nil {
		return err
	}

	switch req.Source {
	case sourceWechat:
		if req.Account == "" {
			return errors.New("微信号不可为空")
		}
	case sourceApps:
		if req.Url == "" {
			return errors.New("app下载链接不可为空")
		}
		if !utils.ListContains([]string{platformIOS, platformAndroid}, req.Platform) {
			return errors.New("应用分类错误")
		}
		if req.IsOnline == "" {
			return errors.New("是否在线不可为空")
		}
	}

	var err error
	var da = digitalAsset{}
	switch req.Source {
	case sourceWechat:
		err = da.digitalAssetsWechatCreate(req)
	case sourceApps:
		err = da.digitalAssetsAppCreate(req)
	}
	return err
}

// DigitalAssetsResultUpdate 资产管理：总库数字资产结果编辑
func DigitalAssetsResultUpdate(req *pb.ManageDigitalAssetsResultItem) error {
	if req.Id == 0 {
		return errors.New("资产ID不可为空")
	}

	if err := checkDigitalType(req.Source); err != nil {
		return err
	}

	if req.Source == sourceWechat && req.Account == "" {
		return errors.New("微信号不可为空")
	}
	if req.Source == sourceApps && req.Url == "" {
		return errors.New("app下载链接不可为空")
	}

	var err error
	switch req.Source {
	case sourceWechat:
		db := official_account.NewOfficialAccountModel()
		_, err = db.First(mysql.WithId(req.Id), mysql.WithSelect("id"))
	case sourceApps:
		_, err = apps.NewAppsModel().First(mysql.WithId(req.Id), mysql.WithSelect("id"))
	}
	if err != nil {
		return err
	}

	var updateMap = make(map[string]any, 5)
	updateMap["name"] = req.GetName()
	updateMap["company_name"] = req.GetCompanyName()
	if req.Source == sourceApps && req.Platform != "" {
		updateMap["platform"] = utils.If[string](req.Platform == "1", apps.PlatformApple, apps.PlatformAndroid)
	}
	updateMap["is_online"] = cast.ToInt(req.IsOnline)
	switch req.Source {
	case sourceWechat:
		if req.Logo != "" {
			updateMap["qrcode"] = utils.If(req.Logo != "", req.Logo, wechatQrUrl(req.Account))
		}
		if req.Description != "" {
			updateMap["describe"] = req.Description
		}
		return official_account.NewOfficialAccountModel().UpdateAny(req.Id, updateMap)
	case sourceApps:
		if req.Logo != "" {
			updateMap["logo"] = req.Logo
		}
		return apps.NewAppsModel().AppUpdateAny(req.Id, updateMap)
	}
	return nil
}

// DigitalAssetsResultImport 资产管理：数字资产导入
func DigitalAssetsResultImport(filePath string, source int64) error {
	log.Infof("[Digital Assets] Waiting import file: %s sync", filePath)
	err := waitSync(filePath)
	if err != nil {
		return err
	}

	var da = digitalAsset{}
	switch source {
	case sourceWechat:
		err = da.digitalAssetsWechatImport(filePath)
	case sourceApps:
		err = da.digitalAssetsAppImport(filePath)
	default:
		err = errors.New("未知分类")
	}

	return err
}

func DigitalFilterGroup(req *pb.ManageDigitalAssetsResultImportRequest, rsp *pb.ManageDigitalFilterGroupResponse) error {
	if err := checkDigitalType(req.Source); err != nil {
		return err
	}

	var err error
	var da = digitalAsset{}
	switch req.Source {
	case sourceWechat:
		err = da.digitalWechatFilterGroup(req.TaskId, rsp)
	case sourceApps:
		err = da.digitalAppFilterGroup(req.TaskId, rsp)
	}
	return err
}

func (digitalAsset) digitalWechatFilterGroup(taskId uint64, rsp *pb.ManageDigitalFilterGroupResponse) error {
	var err error
	rsp.CompanyName, err = official_account.NewOfficialAccountModel().AccountGroup(taskId, "company_name")
	if err != nil {
		return err
	}
	sort.Strings(rsp.CompanyName)
	return nil
}

func (digitalAsset) digitalAppFilterGroup(taskId uint64, rsp *pb.ManageDigitalFilterGroupResponse) error {
	var err error
	rsp.CompanyName, err = apps.NewAppsModel().AppsGroup(taskId, "company_name")
	if err != nil {
		return err
	}
	sort.Strings(rsp.CompanyName)
	return nil
}
