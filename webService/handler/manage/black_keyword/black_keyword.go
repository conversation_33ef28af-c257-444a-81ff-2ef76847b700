package black_keyword

import (
	"context"
	"errors"
	"fmt"
	"micro-service/initialize/redis"
	"strconv"
	"strings"

	"github.com/elliotchance/pie/v2"
	"github.com/spf13/cast"

	"micro-service/middleware/mysql"
	bks "micro-service/middleware/mysql/black_keyword_system"
	bkt "micro-service/middleware/mysql/black_keyword_type"
	tbk "micro-service/middleware/mysql/title_black_keyword"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/manage"
	pb "micro-service/webService/proto"
)

// 内置类型值偏移+1
var builtinType = [...]string{
	"其他类型",
	"钓鱼仿冒",
	"黄赌毒网站",
	"ICP盗用",
	"域名混淆",
}

func KeywordTypeList(rsp *pb.BlackKeywordListResponse) error {
	l, _, err := bkt.NewTypeModel().List(0, 0)
	if err != nil {
		return err
	}

	rsp.Items = make([]*pb.BlackKeywordListResponseItem, 0, len(l))
	// 自定义类型
	for _, v := range l {
		rsp.Items = append(rsp.Items, &pb.BlackKeywordListResponseItem{
			Id:        int64(v.Id),
			Status:    strconv.Itoa(v.Status),
			TypeName:  v.Name,
			CreatedAt: v.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt: v.UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	return nil
}

func KeywordTypeCreate(typeNames []string) error {
	typeNames = utils.ListDistinct(typeNames)
	for _, v := range typeNames {
		for _, x := range builtinType {
			if strings.EqualFold(v, x) {
				return fmt.Errorf("%s是系统内置威胁类型，无法创建", v)
			}
		}
	}

	l, _, err := bkt.NewTypeModel().List(0, 0)
	if err != nil {
		return err
	}

	var insert = make([]*bkt.KeywordType, 0, len(typeNames))
	for _, v := range typeNames {
		exist := false
		for _, w := range l {
			if v == w.Name {
				exist = true
				break
			}
		}
		if !exist {
			insert = append(insert, &bkt.KeywordType{Name: v, Status: bkt.Enable})
		}
	}
	return bkt.NewTypeModel().Create(insert...)
}

func listBuilder(req *pb.BlackKeywordListRequest) []mysql.HandleFunc {
	if req.Ids = utils.ListDistinctNonZero(req.Ids); len(req.Ids) > 0 {
		return []mysql.HandleFunc{mysql.WithValuesIn("id", req.Ids)}
	}

	var h []mysql.HandleFunc
	if len(req.Search) > 0 {
		h = append(h, mysql.WithLRLike("keyword", req.Search))
	}
	if req.Keyword != "" {
		h = append(h, mysql.WithLRLike("keyword", req.Keyword))
	}
	if status := utils.ListDistinctNonZero(req.Status); len(status) > 0 {
		h = append(h, mysql.WithValuesIn("status", pie.Ints(status)))
	}
	if req.UserId = utils.ListDistinctNonZero(req.UserId); len(req.UserId) > 0 {
		h = append(h, mysql.WithValuesIn("user_id", req.UserId))
	}
	if req.TypeId = utils.ListDistinctNonZero(req.TypeId); len(req.TypeId) > 0 {
		h = append(h, mysql.WithValuesIn("type_id", req.TypeId))
	}
	if createdAt := utils.ListNonZero(req.CreatedAt); len(createdAt) == 2 {
		t1, t2, _ := manage.ParseDateToTime(createdAt)
		h = append(h, mysql.WithBetween("created_at", t1, t2))
	}
	return h
}

func getTypeNames(typeIds []int64) map[int64]string {
	var typeNames = make(map[int64]string, len(typeIds))
	typeIds = utils.ListDistinctNonZero(typeIds)
	if len(typeIds) == 0 {
		return typeNames
	}

	l, _, _ := bkt.NewTypeModel().List(0, 0, mysql.WithValuesIn("id", typeIds))
	for _, v := range l {
		typeNames[int64(v.Id)] = v.Name
	}
	return typeNames
}

func BlackKeywordList(_ context.Context, req *pb.BlackKeywordListRequest, rsp *pb.BlackKeywordListResponse) error {
	h := listBuilder(req)
	h = append(h, mysql.WithOrder2("", "updated_at", true))
	l, total, err := tbk.NewModel().List(int(req.Page), int(req.PerPage), h...)
	if err != nil {
		return err
	}

	var typeIds = make([]int64, 0, req.PerPage)
	var userIds = make([]uint, 0, req.PerPage)
	for _, v := range l {
		typeIds = append(typeIds, v.TypeId)
		userIds = append(userIds, uint(v.UserId))
		rsp.Items = append(rsp.Items, &pb.BlackKeywordListResponseItem{
			Id:        int64(v.Id),
			UserId:    v.UserId,
			CompanyId: v.CompanyId,
			TypeId:    v.TypeId,
			Status:    strconv.Itoa(v.Status),
			Enable:    uint64(v.Enable),
			Keyword:   v.Keyword,
			CreatedAt: v.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt: v.UpdatedAt.Format(utils.DateTimeLayout),
		})
	}

	var userNames = make(map[uint64]user.User, len(userIds))
	if userIds = utils.ListDistinctNonZero(userIds); len(userIds) > 0 {
		userNames, _ = user.NewUserModel().FindByIds(userIds)
	}
	typeNames := getTypeNames(typeIds)
	for _, v := range rsp.Items {
		u := userNames[v.UserId]
		if u.Name != nil {
			v.Username = u.Name.String
		}
		v.TypeName = typeNames[v.TypeId]
	}

	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	rsp.Total = total
	return nil
}

func BlackKeywordUpdate(_ context.Context, req *pb.BlackKeywordUpdateRequest) error {
	req.Ids = utils.ListDistinctNonZero(req.Ids)
	if !req.IsAudit {
		if len(req.Ids) != 1 {
			return errors.New("只能编辑一条数据")
		}
		_, err := bkt.NewTypeModel().First(mysql.WithId(req.AuditTypeId))
		if err != nil {
			return err
		}
		info, errF := tbk.NewModel().First(mysql.WithId(req.Ids[0]), mysql.WithColumnValue("status", tbk.StatusDefault))
		if errF != nil {
			return errF
		}
		info.TypeId = int64(req.AuditTypeId)
		return tbk.NewModel().Update(&info)
	}

	h := listBuilder(&pb.BlackKeywordListRequest{
		Search:    req.Search,
		CreatedAt: req.CreatedAt,
		UserId:    req.UserId,
		TypeId:    req.TypeId,
		Ids:       req.Ids,
		Keyword:   req.Keyword,
	})
	// 从tbk表中查询所有未审核过的关键词
	h = append(h, mysql.WithColumnValue("status", tbk.StatusDefault))
	list, _, err := tbk.NewModel().List(0, 0, h...)
	if len(list) == 0 {
		return err
	}
	// 修改状态存到数据库中
	status := cast.ToInt(req.Status)
	for _, v := range list {
		v.Status = status
	}
	err = tbk.NewModel().Save(list...)
	if err != nil || status == tbk.StatusRefused {
		return err
	}
	// 审核通过继续走
	var m = make(map[int64][]string, 10) // key: type_id, value: keywords
	for _, v := range list {
		x, ok := m[v.TypeId]
		if !ok {
			x = make([]string, 0, 50)
		}
		m[v.TypeId] = append(x, v.Keyword)
	}

	for k, v := range m {
		v = utils.ListDistinctNonZero[string](v)
		if k == 0 || len(v) == 0 {
			continue
		}
		err = ManageBlackKeywordCreate(&pb.ManageBlackKeywordCreateRequest{Keyword: v, TypeId: k})
		if err != nil {
			return fmt.Errorf("同步关键词: %v 到黄赌毒总库失败, 原因: %s", v, err.Error())
		}
	}

	keywordCache := make(map[string]uint64)
	const prefix = "foradar_cache:keyword_system:"
	for _, v := range list {
		key := keywordCacheKey(v.Keyword, v.TypeId)
		id, ok := keywordCache[key]
		if !ok {
			r, _ := redis.GetInstance().Get(context.TODO(), prefix+key).Result()
			if id = cast.ToUint64(r); id > 0 {
				keywordCache[key] = id
				goto next
			}
			x, err := bks.NewModel().FirstOrCreate(v.Keyword, v.TypeId)
			if err != nil {
				return err
			}
			keywordCache[key] = x.Id
			id = x.Id
		}
	next:
		update := map[string]any{"black_keyword_system_id": id, "enable": bkt.Enable}
		errAny := tbk.NewModel().UpdateAny(update, mysql.WithId(v.Id))
		if errAny != nil {
			return errAny
		}
	}

	// 删除威胁词库缓存
	go delUserBlackKeywordCache("keyword_sync")

	return nil
}

func keywordCacheKey(keyword string, typeId int64) string {
	s := utils.AnyToStr(bks.Keyword{Keyword: keyword, TypeId: typeId})
	return utils.Md5sHash(s, false)
}
