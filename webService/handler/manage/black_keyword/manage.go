package black_keyword

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"micro-service/middleware/mysql"
	bks "micro-service/middleware/mysql/black_keyword_system"
	bkt "micro-service/middleware/mysql/black_keyword_type"
	tbk "micro-service/middleware/mysql/title_black_keyword"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

// 总库对黄赌毒词库进行管理

func manageListBuilder(req *pb.BlackKeywordListRequest) []mysql.HandleFunc {
	if len(req.Ids) > 0 {
		return []mysql.HandleFunc{mysql.WithValuesIn("id", req.Ids)}
	}
	var h []mysql.HandleFunc
	if req.Search != "" {
		h = append(h, mysql.WithLRLike("keyword", req.Search))
	}
	if typeIds := utils.ListDistinctNonZero(req.TypeId); len(typeIds) > 0 {
		h = append(h, mysql.WithValuesIn("type_id", typeIds))
	}
	return h
}

// ManageBlackKeywordList 总库 列表
func ManageBlackKeywordList(req *pb.BlackKeywordListRequest, rsp *pb.BlackKeywordListResponse) error {
	var h = manageListBuilder(req)
	h = append(h, mysql.WithOrder("`updated_at` DESC"))
	list, total, err := bks.NewModel().List(int(req.Page), int(req.PerPage), h...)
	if err != nil {
		return err
	}

	var typeIds = make([]int64, 0, 10)
	for _, v := range list {
		typeIds = append(typeIds, v.TypeId)
		rsp.Items = append(rsp.Items, &pb.BlackKeywordListResponseItem{
			Id:        int64(v.Id),
			TypeId:    v.TypeId,
			Keyword:   v.Keyword,
			Enable:    uint64(v.Status),
			CreatedAt: v.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt: v.UpdatedAt.Format(utils.DateTimeLayout),
		})
	}

	typeNames := getTypeNames(typeIds)
	for _, v := range rsp.Items {
		v.TypeName = typeNames[v.TypeId]
	}
	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	rsp.Total = total
	return nil
}

// ManageBlackKeywordCreate 总库 新建
func ManageBlackKeywordCreate(req *pb.ManageBlackKeywordCreateRequest) error {
	keywords := utils.ListDistinct(req.Keyword)
	if len(keywords) == 0 {
		return errors.New("关键词不可为空")
	}

	_, err := bkt.NewTypeModel().First(mysql.WithId(req.TypeId), mysql.WithSelect("id"))
	if err != nil {
		return fmt.Errorf("[黄赌毒总库]check type failed, type_id: %d, %v", req.TypeId, err)
	}

	db := bks.NewModel()
	for _, v := range keywords {
		info, errFirst := db.First(mysql.WithColumnValue("keyword", v), mysql.WithColumnValue("type_id", req.TypeId))
		switch {
		case errors.Is(errFirst, gorm.ErrRecordNotFound):
			info.Keyword = v
			info.TypeId = req.TypeId
			info.Status = bkt.Enable
		case errFirst != nil:
			return errFirst
		}
		if errUpsert := db.Upsert(&info); errUpsert != nil {
			return errUpsert
		}

		key := "foradar_cache:keyword_system:" + keywordCacheKey(v, req.TypeId)
		_ = redis.Set(context.TODO(), key, info.Id, 15*time.Second)
	}

	go delUserBlackKeywordCache("system_create")

	return nil
}

// ManageBlackKeywordDelete 总库 删除
// Deprecated
func ManageBlackKeywordDelete(req *pb.BlackKeywordListRequest) error {
	var ids = req.Ids
	if len(ids) == 0 {
		var h = manageListBuilder(req)
		h = append(h, mysql.WithSelect("id"))
		list, _, err := bks.NewModel().List(0, 0, h...)
		if len(list) == 0 {
			return err
		}
		for _, v := range list {
			ids = append(ids, v.Id)
		}
	}

	err := bks.NewModel().DeleteByIds(ids...)
	if err != nil {
		return err
	}

	// 删除缓存
	go delUserBlackKeywordCache("delete")

	return err
}

// ManageBlackKeywordUpdate 总库 更新
func ManageBlackKeywordUpdate(req *pb.ManageBlackKeywordUpdateRequest) error {
	var h []mysql.HandleFunc
	if ids := utils.ListDistinct(req.Ids); len(ids) > 0 {
		h = append(h, mysql.WithValuesIn("id", ids))
	} else if req.Search != "" {
		h = append(h, mysql.WithLRLike("keyword", req.Search))
	}
	list, _, err := bks.NewModel().List(0, 0, h...)
	if len(list) == 0 {
		return err
	}

	var ids = make([]uint64, 0, len(list))
	for i := range list {
		list[i].Status = int(req.Enable)
		ids = append(ids, list[i].Id)
	}

	err = bks.NewModel().Upsert(list...)
	if err != nil {
		return err
	}

	go delUserBlackKeywordCache("system_enable/disable") // 删除缓存

	opt := mysql.WithValuesIn("black_keyword_system_id", ids)
	err = tbk.NewModel().UpdateAny(map[string]any{"enable": req.Enable}, opt)
	return err
}

func delUserBlackKeywordCache(op string) {
	const key = "foradar_cache:black_title_keyword"
	err := redis.DelKey(context.TODO(), key)
	if err != nil {
		log.Warnf("[BlackKeyword] Delete redis key: %s cache failed in %s operation, %v", key, op, err)
	}
	cacheKey := "black_title_keyword"
	err = redis.DelKey(context.TODO(), cacheKey)
	if err != nil {
		log.Warnf("[BlackKeyword] Delete redis key: %s cache failed in %s operation, %v", cacheKey, op, err)
	}
}
