package ignore_assets

import (
	"context"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	testcommon "micro-service/initialize/common_test"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql/clues"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dbx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

var (
	indexName  = fofaee_assets.FofaeeAssetsIndex
	docType    = fofaee_assets.FofaeeAssetsType
	indexName2 = foradar_assets.IndexName
)

// Init 初始化Mock服务
func Init() {
	cfg.InitLoadCfg()
	log.Init()
	// 初始化Mock服务
	mock := es.NewMockServer()

	// 查询语句Mock数据 - 忽略资产
	resultList := []fofaee_assets.FofaeeAssets{
		{
			Id:     "1_**********",
			Ip:     "**********",
			Status: fofaee_assets.STATUS_IGNORE,
			Type:   fofaee_assets.TYPE_RECOMMEND,
			HostList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:           80,
					Protocol:       "http",
					Title:          "忽略网站标题",
					Subdomain:      "ignore.example.com",
					Domain:         "example.com",
					HttpStatusCode: "200",
					Logo: fofaee_assets.Icon{
						Content: "/storage/ignore_logo.png",
						Hash:    "-1111123333",
					},
				},
			},
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:           "80",
					Protocol:       "http",
					Title:          "忽略网站标题",
					HttpStatusCode: "200",
				},
			},
			Geo: fofaee_assets.Geo{
				Asn:      "AS4134 China Telecom (Group)",
				Province: "Shanghai",
				Isp:      "China Telecom",
				Lat:      31.2304,
				Lon:      121.4737,
			},
			CloudName:       []any{"IgnoreCloud", "456"},
			ClueCompanyName: []any{"忽略公司有限公司"},
			CustomerTags:    []string{"忽略标签1", "忽略标签2"},
			RuleTags: []fofaee_assets.RuleTag{
				{
					RuleId:    "ignore_tag_123",
					Product:   "忽略组件",
					CnProduct: "忽略组件中文",
				},
			},
			ReasonArr: []any{
				map[string]interface{}{
					"group_id":          5678,
					"id":                2,
					"source":            4,
					"type":              0,
					"content":           "ignore.example.com",
					"clue_company_name": "忽略公司有限公司",
				},
			},
			UpdatedAt:   "2025-06-06 16:13:00",
			CreatedAt:   "2025-06-05 10:00:00",
			UserId:      1,
			OnlineState: 1,
			Tags:        []int{fofaee_assets.SAFE_SCAN},
		},
		{
			Id:     "1_**********",
			Ip:     "**********",
			Status: fofaee_assets.STATUS_IGNORE,
			Type:   fofaee_assets.TYPE_RECOMMEND,
			HostList: []fofaee_assets.FofaeeAssetPort{
				{
					HttpStatusCode: 200,
					Port:           "443",
					Protocol:       "https",
					Title:          "另一个忽略网站",
				},
			},
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:           "443",
					HttpStatusCode: 200,
					Protocol:       "https",
					Reason: []fofaee_assets.AssetReason{
						{
							Id:              3,
							Source:          4,
							Type:            0,
							Content:         "ignore2.example.com",
							ClueCompanyName: "另一个忽略公司",
						},
					},
				},
			},
			Geo: fofaee_assets.Geo{
				Province: "Beijing",
				Isp:      "China Unicom",
			},
			UpdatedAt:   "2025-06-06 15:00:00",
			CreatedAt:   "2025-06-05 09:00:00",
			UserId:      1,
			OnlineState: 0,
		},
	}

	// ForadarAsset Mock数据
	foradarResultList := []foradar_assets.ForadarAsset{
		{
			ID:        "foradar_1_**********",
			Ip:        "**********",
			Port:      80,
			Protocol:  "http",
			Title:     "忽略网站标题",
			Subdomain: "ignore.example.com",
			Domain:    "example.com",
			Geo: foradar_assets.AssetGeo{
				Asn:      "AS4134 China Telecom (Group)",
				Province: "Shanghai",
				Isp:      "China Telecom",
			},
			ClueCompanyName: []string{"忽略公司有限公司"},
			RuleTags: []foradar_assets.RuleTag{
				{
					RuleID:    "ignore_tag_123",
					Product:   "忽略组件",
					CnProduct: "忽略组件中文",
				},
			},
			Reason: []foradar_assets.AssetReason{
				{
					ID:              2,
					Source:          4,
					Type:            0,
					Content:         "ignore.example.com",
					ClueCompanyName: "忽略公司有限公司",
				},
			},
			CreatedAt:      "2025-06-05 10:00:00",
			UpdatedAt:      "2025-06-06 16:13:00",
			UserID:         1,
			OnlineState:    1,
			Status:         fofaee_assets.STATUS_IGNORE,
			HTTPStatusCode: "200",
		},
		{
			ID:       "foradar_1_**********",
			Ip:       "**********",
			Port:     443,
			Protocol: "https",
			Title:    "另一个忽略网站",
			Geo: foradar_assets.AssetGeo{
				Province: "Beijing",
				Isp:      "China Unicom",
			},
			CreatedAt:      "2025-06-05 09:00:00",
			UpdatedAt:      "2025-06-06 15:00:00",
			UserID:         1,
			OnlineState:    0,
			Status:         fofaee_assets.STATUS_IGNORE,
			HTTPStatusCode: "403",
		},
	}

	// 注册FofaeeAssets查询Mock
	mock.Register("/"+indexName+"/_search", []*elastic.SearchHit{
		{
			Index:  indexName,
			Type:   docType,
			Id:     resultList[0].Id,
			Source: utils.ToJSON(resultList[0]),
		},
		{
			Index:  indexName,
			Type:   docType,
			Id:     resultList[1].Id,
			Source: utils.ToJSON(resultList[1]),
		},
	})

	// 注册ForadarAssets查询Mock
	mock.Register("/"+indexName2+"/_search", []*elastic.SearchHit{
		{
			Index:  indexName2,
			Type:   "assets",
			Id:     foradarResultList[0].ID,
			Source: utils.ToJSON(foradarResultList[0]),
		},
		{
			Index:  indexName2,
			Type:   "assets",
			Id:     foradarResultList[1].ID,
			Source: utils.ToJSON(foradarResultList[1]),
		},
	})

	// 创建语句Mock数据
	createId := "1_**********"
	indexResponse := elastic.IndexResponse{
		Index:   indexName,
		Type:    docType,
		Id:      createId,
		Version: 1,
		Result:  "created",
		Shards: &elastic.ShardsInfo{
			Total:      2,
			Successful: 1,
			Failed:     0,
		},
	}

	mock.Register("/"+indexName+"/"+docType+"/"+createId, indexResponse)

	// 更新语句Mock数据
	updateResponse := elastic.UpdateResponse{
		Index:   indexName,
		Type:    docType,
		Id:      createId,
		Version: 2,
		Result:  "updated",
		Shards: &elastic.ShardsInfo{
			Total:      2,
			Successful: 1,
			Failed:     0,
		},
	}

	mock.Register("/"+indexName+"/_update_by_query", updateResponse)
	mock.Register("/"+indexName2+"/_update_by_query", updateResponse)

	// 批量操作注册
	mock.RegisterBulk()

	// 删除语句Mock数据
	deleteResponse := elastic.DeleteResponse{
		Index:   indexName,
		Type:    docType,
		Id:      "1_**********",
		Version: 3,
		Result:  "deleted",
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Failed:     0,
		},
	}

	mock.Register("/"+indexName+"/_delete_by_query", deleteResponse)
	mock.Register("/"+indexName2+"/_delete_by_query", deleteResponse)

	// Count语句Mock数据
	countResponse := elastic.CountResponse{
		Count: 2,
	}

	mock.Register("/"+indexName+"/_count", countResponse)
	mock.Register("/"+indexName2+"/_count", countResponse)

	// 创建Mock服务
	mockClient := mock.NewElasticMockClient()

	// 设置是否使用Mock (默认true)
	es.SetTestEnv(true)
	mysql.SetTestEnv(true)
	redis.SetTestEnv(true)
	testcommon.SetTestEnv(true) // 确保testcommon也知道这是测试环境

	// 设置mock客户端到testcommon，这样GetEsClient()就能获取到正确的客户端
	testcommon.SetElasticClient(mockClient)

	// 初始化数据库
	es.GetInstance(cfg.LoadElastic())
	mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

// TestCleanDomain 测试域名清理函数
func TestCleanDomain(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "http协议前缀",
			input:    "http://example.com:8080",
			expected: "example.com",
		},
		{
			name:     "https协议前缀",
			input:    "https://example.com:443",
			expected: "example.com",
		},
		{
			name:     "无协议有端口",
			input:    "example.com:8080",
			expected: "example.com",
		},
		{
			name:     "无协议无端口",
			input:    "example.com",
			expected: "example.com",
		},
		{
			name:     "空字符串",
			input:    "",
			expected: "",
		},
		{
			name:     "只有协议",
			input:    "http://",
			expected: "",
		},
		{
			name:     "IPv6地址",
			input:    "http://[2001:db8::1]:8080",
			expected: "[2001",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CleanDomain(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestIgnoreAssetsList 测试忽略资产列表函数
func TestIgnoreAssetsList(t *testing.T) {
	Init()

	t.Run("正常查询", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "parent_id", "content", "type", "source", "from_ip", "clue_company_name"}).
				AddRow(1, 0, "example.com", 0, 4, "**********", "忽略公司有限公司"))

		req := &pb.IgnoreAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Greater(t, rsp.Total, int64(0))
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.NotEmpty(t, rsp.Data)
	})

	t.Run("带关键字查询", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("忽略"),
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带数字关键字查询", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("80"),
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带排序查询", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Sort:    proto.Int64(1),
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带标题过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Title:   []string{"忽略网站"},
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带域名过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Domain:  []string{"example.com"},
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带主机过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Hosts:   proto.String("ignore.example.com"),
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带云厂商过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:    1,
			Page:      1,
			PerPage:   10,
			CloudName: proto.String("IgnoreCloud"),
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带线索公司名称过滤", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			ClueCompanyName: []string{"忽略公司有限公司"},
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("默认分页参数", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:  1,
			Page:    0, // 测试默认值
			PerPage: 0, // 测试默认值
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.Equal(t, int32(10), rsp.PerPage)
	})

	t.Run("带减号的过滤条件", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectQuery("SELECT .* FROM `clues` WHERE user_id = .*").
			WithArgs(sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

		req := &pb.IgnoreAssetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			Title:           []string{"-", "忽略网站"},
			Domain:          []string{"-", "example.com"},
			ClueCompanyName: []string{"-", "忽略公司有限公司"},
		}
		rsp := &pb.IgnoreAssetsListResponse{}

		err := IgnoreAssetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})
}

// TestExportIgnorePassetsList 测试忽略资产导出函数
func TestExportIgnorePassetsList(t *testing.T) {
	Init()

	t.Run("正常导出", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
		}
		rsp := &pb.FileExportResponse{}

		err := ExportIgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("带ID导出", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Id:     []string{"foradar_1_**********", "foradar_1_**********"},
		}
		rsp := &pb.FileExportResponse{}

		err := ExportIgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("带关键字导出", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:  1,
			Keyword: proto.String("忽略"),
		}
		rsp := &pb.FileExportResponse{}

		err := ExportIgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})
}

// TestIgnoreAssetsIPExport 测试忽略资产IP导出函数
func TestIgnoreAssetsIPExport(t *testing.T) {
	Init()

	t.Run("正常导出", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId: 1,
		}
		rsp := &pb.FileExportResponse{}

		err := IgnoreAssetsIPExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("带ID导出", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********", "1_**********"},
		}
		rsp := &pb.FileExportResponse{}

		err := IgnoreAssetsIPExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})

	t.Run("带过滤条件导出", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:          1,
			Keyword:         proto.String("忽略"),
			Title:           []string{"忽略网站"},
			Domain:          []string{"example.com"},
			Hosts:           proto.String("ignore.example.com"),
			CloudName:       proto.String("IgnoreCloud"),
			ClueCompanyName: []string{"忽略公司有限公司"},
		}
		rsp := &pb.FileExportResponse{}

		err := IgnoreAssetsIPExport(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NotEmpty(t, rsp.Url)
	})
}

// TestIpIgnoreAssetDelete 测试忽略资产删除函数
func TestIpIgnoreAssetDelete(t *testing.T) {
	Init()

	t.Run("正常删除", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `ip_history` WHERE user_id = .* AND ip IN .*").
			WithArgs(utils.SqlMockArgs(3)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********"},
		}
		rsp := &pb.Empty{}

		err := IpIgnoreAssetDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("批量删除", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `ip_history` WHERE user_id = .* AND ip IN .*").
			WithArgs(utils.SqlMockArgs(4)...).
			WillReturnResult(sqlmock.NewResult(1, 2))
		mock.ExpectCommit()

		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********", "1_**********"},
		}
		rsp := &pb.Empty{}

		err := IpIgnoreAssetDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带过滤条件删除", func(t *testing.T) {
		mock := mysql.GetMockInstance()
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `ip_history` WHERE user_id = .* AND ip IN .*").
			WithArgs(utils.SqlMockArgs(3)...).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		req := &pb.IpAssetActionRequest{
			UserId:  1,
			Keyword: proto.String("忽略"),
		}
		rsp := &pb.Empty{}

		err := IpIgnoreAssetDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("没有找到要删除的资产", func(t *testing.T) {
		// 模拟查询返回空结果
		req := &pb.IpAssetActionRequest{
			UserId: 999, // 使用不存在的用户ID
		}
		rsp := &pb.Empty{}

		err := IpIgnoreAssetDelete(context.Background(), req, rsp)
		assert.NoError(t, err) // 应该正常返回，不报错
	})
}

// TestIpPortActionSearch 测试IP端口操作搜索函数
func TestIpPortActionSearch(t *testing.T) {
	tests := []struct {
		name     string
		req      *pb.IpPortActionRequest
		status   []interface{}
		expected int // 期望的查询条件数量
	}{
		{
			name: "基本查询",
			req: &pb.IpPortActionRequest{
				UserId: 1,
			},
			status:   []interface{}{fofaee_assets.STATUS_IGNORE},
			expected: 2, // user_id + status
		},
		{
			name: "带ID查询",
			req: &pb.IpPortActionRequest{
				UserId: 1,
				Id:     []string{"1_**********"},
			},
			status:   []interface{}{fofaee_assets.STATUS_IGNORE},
			expected: 1, // 只有_id条件
		},
		{
			name: "带关键字查询",
			req: &pb.IpPortActionRequest{
				UserId:  1,
				Keyword: proto.String("忽略"),
			},
			status:   []interface{}{fofaee_assets.STATUS_IGNORE},
			expected: 2, // user_id + status (关键字在should中)
		},
		{
			name: "带数字关键字查询",
			req: &pb.IpPortActionRequest{
				UserId:  1,
				Keyword: proto.String("80"),
			},
			status:   []interface{}{fofaee_assets.STATUS_IGNORE},
			expected: 2, // user_id + status
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IpPortActionSearch(tt.req, tt.status)
			assert.NotNil(t, result)
			assert.IsType(t, [][]interface{}{}, result)
		})
	}
}

// TestGetStateText 测试状态文本获取函数
func TestGetStateText(t *testing.T) {
	tests := []struct {
		name     string
		state    int
		expected string
	}{
		{
			name:     "在线状态",
			state:    1,
			expected: "在线",
		},
		{
			name:     "离线状态",
			state:    2,
			expected: "离线",
		},
		{
			name:     "未知状态",
			state:    0,
			expected: "离线",
		},
		{
			name:     "其他状态",
			state:    999,
			expected: "离线",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getStateText(tt.state)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetTopClue 测试线索链获取函数
func TestGetTopClue(t *testing.T) {
	clueList := []*clues.Clue{
		{
			Model: dbx.Model{
				Id: 1,
			},
			ParentId:        0,
			Content:         "example.com",
			Type:            0,
			Source:          4,
			FromIp:          "**********",
			ClueCompanyName: "测试公司",
		},
		{
			Model: dbx.Model{
				Id: 2,
			},
			ParentId:        1,
			Content:         "sub.example.com",
			Type:            0,
			Source:          4,
			FromIp:          "",
			ClueCompanyName: "测试公司",
		},
	}

	t.Run("正常获取线索链", func(t *testing.T) {
		var chainList []map[string]interface{}
		getTopClue(1, clueList, &chainList)
		assert.NotEmpty(t, chainList)
	})

	t.Run("空线索列表", func(t *testing.T) {
		var chainList []map[string]interface{}
		getTopClue(1, []*clues.Clue{}, &chainList)
		assert.Empty(t, chainList)
	})
}

// TestBuildRuleInfos 测试规则信息构建函数
func TestBuildRuleInfos(t *testing.T) {
	tests := []struct {
		name     string
		ruleTags []fofaee_assets.RuleTag
		expected string
	}{
		{
			name:     "空规则标签",
			ruleTags: []fofaee_assets.RuleTag{},
			expected: "",
		},
		{
			name: "单个规则标签",
			ruleTags: []fofaee_assets.RuleTag{
				{CnProduct: "Apache"},
			},
			expected: "Apache",
		},
		{
			name: "多个规则标签",
			ruleTags: []fofaee_assets.RuleTag{
				{CnProduct: "Apache"},
				{CnProduct: "Nginx"},
				{CnProduct: "MySQL"},
			},
			expected: "Apache,Nginx,MySQL",
		},
		{
			name: "包含重复的规则标签",
			ruleTags: []fofaee_assets.RuleTag{
				{CnProduct: "Apache"},
				{CnProduct: "Nginx"},
				{CnProduct: "Apache"},
			},
			expected: "Apache,Nginx",
		},
		{
			name: "包含空产品名的规则标签",
			ruleTags: []fofaee_assets.RuleTag{
				{CnProduct: "Apache"},
				{CnProduct: ""},
				{CnProduct: "Nginx"},
			},
			expected: "Apache,Nginx",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := buildRuleInfos(tt.ruleTags)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestGetFofaAssetsDefaultSorter 测试默认排序器函数
func TestGetFofaAssetsDefaultSorter(t *testing.T) {
	sorters := GetFofaAssetsDefaultSorter()
	assert.NotNil(t, sorters)
	assert.Len(t, sorters, 2)

	// 检查第一个排序器是否为FieldSort
	_, ok := sorters[0].(*elastic.FieldSort)
	assert.True(t, ok, "第一个排序器应该是FieldSort")

	// 检查第二个排序器是否为FieldSort
	_, ok = sorters[1].(*elastic.FieldSort)
	assert.True(t, ok, "第二个排序器应该是FieldSort")
}

// TestMin 测试最小值函数
func TestMin(t *testing.T) {
	tests := []struct {
		name     string
		a        int64
		b        int64
		expected int64
	}{
		{
			name:     "a小于b",
			a:        5,
			b:        10,
			expected: 5,
		},
		{
			name:     "a大于b",
			a:        10,
			b:        5,
			expected: 5,
		},
		{
			name:     "a等于b",
			a:        5,
			b:        5,
			expected: 5,
		},
		{
			name:     "负数比较",
			a:        -5,
			b:        -10,
			expected: -10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := min(tt.a, tt.b)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestIgnorePassetsList 测试忽略资产列表(新版)函数
func TestIgnorePassetsList(t *testing.T) {
	Init()

	t.Run("正常查询", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Greater(t, rsp.Total, int64(0))
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.NotEmpty(t, rsp.Data)
	})

	t.Run("带关键字查询", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("忽略"),
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带数字关键字查询", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("80"),
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带标题过滤", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Title:   []string{"忽略网站"},
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带域名过滤", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Domain:  []string{"example.com"},
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带主机过滤", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Hosts:   proto.String("ignore.example.com"),
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带资产来源过滤", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:       1,
			Page:         1,
			PerPage:      10,
			AssetsSource: []string{"FOFA"},
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带云厂商过滤", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:    1,
			Page:      1,
			PerPage:   10,
			CloudName: proto.String("IgnoreCloud"),
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("带线索公司名称过滤", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			ClueCompanyName: []string{"忽略公司有限公司"},
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})

	t.Run("默认分页参数", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:  1,
			Page:    0, // 测试默认值
			PerPage: 0, // 测试默认值
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.Equal(t, int32(10), rsp.PerPage)
	})

	t.Run("超出最大分页限制", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 200, // 超出最大限制
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int32(10), rsp.PerPage) // 应该被重置为默认值
	})

	t.Run("带减号的过滤条件", func(t *testing.T) {
		req := &pb.IgnorePassetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			Title:           []string{"-", "忽略网站"},
			Domain:          []string{"-", "example.com"},
			AssetsSource:    []string{"-", "FOFA"},
			ClueCompanyName: []string{"-", "忽略公司有限公司"},
		}
		rsp := &pb.PassetsListResponse{}

		err := IgnorePassetsList(context.Background(), req, rsp)
		assert.NoError(t, err)
	})
}

// TestGetReason 测试获取推荐原因函数
func TestGetReason(t *testing.T) {
	t.Run("已知资产类型", func(t *testing.T) {
		result := getReason(nil, fofaee_assets.TYPE_CLAIMED, false, 0)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("上传资产类型", func(t *testing.T) {
		result := getReason(nil, fofaee_assets.STATUS_UPLOAD, false, 0)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("typeTwo为已知资产", func(t *testing.T) {
		result := getReason(nil, 0, false, fofaee_assets.TYPE_CLAIMED)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("typeTwo为上传资产", func(t *testing.T) {
		result := getReason(nil, 0, false, fofaee_assets.STATUS_UPLOAD)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("空reason", func(t *testing.T) {
		result := getReason([]foradar_assets.AssetReason{}, 999, false, 999) // 使用不匹配的值
		assert.Equal(t, "", result)
	})

	t.Run("普通reason", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "测试公司",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "根据测试公司的")
		assert.Contains(t, result, "example.com推荐")
	})

	t.Run("LOGO类型reason", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            clues.TYPE_LOGO,
				Content:         "/storage/logo.png",
				ClueCompanyName: "测试公司",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "根据测试公司的")
		assert.Contains(t, result, "推荐")
	})

	t.Run("LOGO类型reason导出模式", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            clues.TYPE_LOGO,
				Content:         "/storage/logo.png",
				ClueCompanyName: "测试公司",
			},
		}
		result := getReason(reasons, 999, true, 999) // 使用不匹配的值
		assert.Contains(t, result, "根据测试公司的")
		assert.Contains(t, result, "推荐")
		assert.NotContains(t, result, "<img")
	})

	t.Run("LOGO类型reason包含pp/public", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            clues.TYPE_LOGO,
				Content:         "/pp/public/logo.png",
				ClueCompanyName: "测试公司",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "<img")
		assert.Contains(t, result, "推荐")
	})

	t.Run("空公司名称", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "根据-的")
		assert.Contains(t, result, "example.com推荐")
	})

	t.Run("多个reason", func(t *testing.T) {
		reasons := []foradar_assets.AssetReason{
			{
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "测试公司1",
			},
			{
				Type:            1,
				Content:         "test.com",
				ClueCompanyName: "测试公司2",
			},
		}
		result := getReason(reasons, 999, false, 999) // 使用不匹配的值
		assert.Contains(t, result, "测试公司1")
		assert.Contains(t, result, "测试公司2")
		assert.Contains(t, result, "example.com推荐")
		assert.Contains(t, result, "test.com推荐")
	})
}

// TestAssetsIgnoreSetStatus 测试忽略资产状态设置函数
func TestAssetsIgnoreSetStatus(t *testing.T) {
	Init()

	t.Run("缺少状态参数", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********"},
			// SetStatus: nil, // 缺少状态参数
		}
		resp := &pb.Empty{}
		err := AssetsIgnoreSetStatus(context.Background(), req, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "缺少状态参数")
	})

	t.Run("不支持的状态", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:    1,
			Id:        []string{"1_**********"},
			SetStatus: proto.Int64(999), // 不支持的状态
		}
		resp := &pb.Empty{}
		err := AssetsIgnoreSetStatus(context.Background(), req, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "不支持的状态")
	})

	t.Run("设置为认领状态", func(t *testing.T) {
		// 初始化MySQL Mock
		mysql.GetMockInstance()

		req := &pb.IpAssetActionRequest{
			UserId:    1,
			CompanyId: 1,
			Id:        []string{"1_**********"},
			SetStatus: proto.Int64(fofaee_assets.STATUS_CLAIMED),
		}
		resp := &pb.Empty{}
		err := AssetsIgnoreSetStatus(context.Background(), req, resp)
		// 由于Mock数据的限制，这里可能会有错误，但不应该是参数验证错误
		if err != nil {
			assert.NotContains(t, err.Error(), "缺少状态参数")
			assert.NotContains(t, err.Error(), "不支持的状态")
		}
	})

	t.Run("设置为忽略状态", func(t *testing.T) {
		// 初始化MySQL Mock
		mysql.GetMockInstance()

		req := &pb.IpAssetActionRequest{
			UserId:    1,
			CompanyId: 1,
			Id:        []string{"1_**********"},
			SetStatus: proto.Int64(fofaee_assets.STATUS_IGNORE),
		}
		resp := &pb.Empty{}
		err := AssetsIgnoreSetStatus(context.Background(), req, resp)
		// 由于Mock数据的限制，这里可能会有错误，但不应该是参数验证错误
		if err != nil {
			assert.NotContains(t, err.Error(), "缺少状态参数")
			assert.NotContains(t, err.Error(), "不支持的状态")
		}
	})

	t.Run("设置为疑似状态", func(t *testing.T) {
		// 初始化MySQL Mock
		mysql.GetMockInstance()

		req := &pb.IpAssetActionRequest{
			UserId:    1,
			CompanyId: 1,
			Id:        []string{"1_**********"},
			SetStatus: proto.Int64(fofaee_assets.STATUS_DEFAULT),
		}
		resp := &pb.Empty{}
		err := AssetsIgnoreSetStatus(context.Background(), req, resp)
		// 由于Mock数据的限制，这里可能会有错误，但不应该是参数验证错误
		if err != nil {
			assert.NotContains(t, err.Error(), "缺少状态参数")
			assert.NotContains(t, err.Error(), "不支持的状态")
		}
	})

	t.Run("设置为威胁状态", func(t *testing.T) {
		// 初始化MySQL Mock
		mysql.GetMockInstance()

		req := &pb.IpAssetActionRequest{
			UserId:           1,
			CompanyId:        1,
			Id:               []string{"1_**********"},
			SetStatus:        proto.Int64(fofaee_assets.STATUS_THREATEN),
			ThreatenType:     proto.String("1"),
			ThreatenTypeName: proto.String("测试威胁类型"),
		}
		resp := &pb.Empty{}
		err := AssetsIgnoreSetStatus(context.Background(), req, resp)
		// 由于Mock数据的限制，这里可能会有错误，但不应该是参数验证错误
		if err != nil {
			assert.NotContains(t, err.Error(), "缺少状态参数")
			assert.NotContains(t, err.Error(), "不支持的状态")
		}
	})

	t.Run("设置威胁状态但没有威胁类型名称", func(t *testing.T) {
		// 初始化MySQL Mock
		mysql.GetMockInstance()

		req := &pb.IpAssetActionRequest{
			UserId:       1,
			CompanyId:    1,
			Id:           []string{"1_**********"},
			SetStatus:    proto.Int64(fofaee_assets.STATUS_THREATEN),
			ThreatenType: proto.String("1"),
			// ThreatenTypeName: nil, // 没有威胁类型名称，应该使用默认值"其他"
		}
		resp := &pb.Empty{}
		err := AssetsIgnoreSetStatus(context.Background(), req, resp)
		// 由于Mock数据的限制，这里可能会有错误，但不应该是参数验证错误
		if err != nil {
			assert.NotContains(t, err.Error(), "缺少状态参数")
			assert.NotContains(t, err.Error(), "不支持的状态")
		}
	})
}
