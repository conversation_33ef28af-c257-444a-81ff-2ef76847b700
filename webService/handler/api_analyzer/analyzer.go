package api_analyzer

import (
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/middleware/mysql"
	sql "micro-service/middleware/mysql/api_analyze"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
)

const (
	AnalyzeStatusProcessing = iota + 1
	AnalyzeStatusFinished
)

// GetApiAnalyzeResult 获取分析结果
func GetApiAnalyzeResult(req *pb.ApiAnalyzeResultRequest, rsp *pb.ApiAnalyzeResultResponse) error {
	task, err := sql.NewTaskModel().First(mysql.WithWhere("id = ?", req.TaskId))
	if err != nil {
		return err
	}
	if task.UserId != req.UserId {
		return errors.New("permission denied")
	}
	rsp.TaskId = task.Id
	if task.Status == AnalyzeStatusProcessing {
		rsp.Status = AnalyzeStatusProcessing
		return nil
	}
	rsp.Status = AnalyzeStatusFinished
	handlers := make([]mysql.HandleFunc, 0, 4)
	handlers = append(handlers, mysql.WithWhere("task_id = ?", req.TaskId))
	if req.Url != "" {
		handlers = append(handlers, sql.WithResultKeyword(req.Url))
	}
	if req.Type != 0 {
		handlers = append(handlers, mysql.WithWhere("risk_type = ?", req.Type))
	}
	if req.StatusCode != 0 {
		handlers = append(handlers, mysql.WithWhere("code = ?", req.StatusCode))
	}
	results, total, err := sql.NewResultModel().List(int(req.Page), int(req.PerPage), handlers...)
	rsp.Total = total
	if err != nil {
		return err
	}
	if len(results) == 0 {
		return nil
	}
	rsp.Results = make([]*pb.ApiAnalyzeResult, len(results))
	var statusCodes []int
	for i, r := range results {
		rsp.Results[i] = &pb.ApiAnalyzeResult{
			Url:          r.Url,
			Api:          r.Api,
			RiskType:     int64(r.RiskType),
			Tag:          r.Tag,
			Description:  r.Describe,
			StatusCode:   int64(r.Code),
			Size:         int64(r.Size),
			ResponseType: r.ResponseType,
			Count:        int64(r.Count),
			Matches:      strings.Split(r.Matches, ","),
			RiskPoint:    getRiskPoint(r.RiskType),
		}
		statusCodes = append(statusCodes, r.Code)
	}
	rsp.StatusCode, _ = sql.NewResultModel().SelectStatusCodeList(req.TaskId)
	return nil
}

// GetApiAnalyzeDetail 获取分析详情
func GetApiAnalyzeDetail(req *pb.ApiAnalyzeResultRequest, rsp *pb.ApiAnalyzeDetailResponse) error {
	task, err := sql.NewTaskModel().First(mysql.WithWhere("id = ?", req.TaskId))
	if err != nil {
		return err
	}
	if task.UserId != req.UserId {
		return errors.New("permission denied")
	}
	rsp.TaskId = task.Id
	if task.Status == AnalyzeStatusProcessing {
		rsp.Status = AnalyzeStatusProcessing
		return nil
	}
	rsp.Status = AnalyzeStatusFinished
	detail, err := sql.NewDetailModel().First(mysql.WithWhere("task_id = ?", req.TaskId))
	if err != nil {
		return err
	}
	rsp.JsList = strings.Split(detail.JsList, ",")
	rsp.UrlList = strings.Split(detail.ApiList, ",")
	return json.Unmarshal([]byte(detail.ExternalUrl), &rsp.ExternalList)
}

// GetApiAnalyzeList 获取分析任务列表
func GetApiAnalyzeList(req *pb.APiAnalyzeTaskListRequest, rsp *pb.ApiAnalyzeUserTaskListResponse) error {
	var handlers = make([]mysql.HandleFunc, 0, 6)
	if req.Operator != "" {
		handlers = append(handlers, mysql.WithWhere("operator_name = ?", req.Operator))
	}
	if req.Name != "" {
		handlers = append(handlers, sql.WithKeyword(req.Name))
	}
	if req.Status != -1 {
		handlers = append(handlers, mysql.WithWhere("status = ?", req.Status))
	}
	if len(req.CreatedAtRange) == 2 {
		t1, t2, _ := parseDate(req.CreatedAtRange[0], req.CreatedAtRange[1])
		handlers = append(handlers, sql.WithBetween("`start_time`", t1, t2))
	}
	if len(req.EndAtRange) == 2 {
		t1, t2, _ := parseDate(req.EndAtRange[0], req.EndAtRange[1])
		handlers = append(handlers, sql.WithBetween("`end_time`", t1, t2))
	}
	handlers = append(handlers, mysql.WithWhere("user_id = ?", req.UserId))
	handlers = append(handlers, mysql.WithOrder("status ASC"))
	handlers = append(handlers, mysql.WithOrder("updated_at DESC"))
	list, length, err := sql.NewUserTaskModel().List(
		req.Page,
		req.PerPage,
		handlers...,
	)
	if err != nil {
		log.Errorf("api_analyze - 获取任务列表失败: %v", err)
		return err
	}
	rsp.Total = length
	for _, i := range list {
		temp := pb.ApiAnalyzeUserTaskInfo{
			UserTaskId:  i.Id,
			Name:        i.Name,
			Status:      i.Status,
			Progress:    i.Progress,
			SubTasksNum: i.SubTaskNum,
			TotalRisks:  i.TotalRisks,
			Operator:    i.OperatorName,
		}
		if i.StartTime != nil {
			temp.StartTime = i.StartTime.Format(time.DateTime)
		}
		if i.StartTime != nil && i.EndTime != nil {
			temp.EndTime = i.EndTime.Format(time.DateTime)
			temp.UsedTime = fmt.Sprintf("%.0f", i.EndTime.Sub(*i.StartTime).Seconds())
		}
		rsp.TaskList = append(rsp.TaskList, &temp)
	}
	return nil
}

func GetApiAnalyzeUserTaskInfo(req *pb.ApiAnalyzeUserTaskInfoRequest, rsp *pb.ApiAnalyzeUserTaskInfoResponse) error {
	handlers := make([]mysql.HandleFunc, 0, 1)
	if req.Url != "" {
		handlers = append(handlers, sql.WithTaskKeyword(req.Url))
	}
	tasks, err := sql.NewTaskModel().ListByUserTaskId(req.UserTaskId, req.UserId, handlers...)
	if err != nil {
		log.Errorf("api_analyze - 获取任务信息失败: %v", err)
		return err
	}
	rsp.Total = int64(len(tasks))
	for _, i := range tasks {
		count, _ := sql.NewResultModel().Count(i.Id)
		temp := pb.ApiAnalyzeTaskInfo{
			TaskId:    i.Id,
			Status:    int64(i.Status),
			Progress:  i.Progress,
			Url:       i.TargetURL,
			Domain:    i.Domain,
			UserAgent: i.UserAgent,
			Fuzzy:     i.Fuzz == 1,
			JsCount:   int64(i.JsCount),
			ApiCount:  int64(i.ApiCount),
			StartTime: i.UpdatedAt.Format("2006-01-02 15:04:05"),
			RiskCount: count,
		}
		rsp.TaskList = append(rsp.TaskList, &temp)
	}
	return nil
}

func DeleteApiAnalyzeUserTask(req *pb.ApiAnalyzeDeleteRequest, rsp *pb.ApiAnalyzeDeleteResponse) error {
	if req.TaskId == nil || len(req.TaskId) == 0 {
		handlers := make([]mysql.HandleFunc, 0, 6)
		if req.Operator != "" {
			handlers = append(handlers, mysql.WithWhere("operator_name = ?", req.Operator))
		}
		if req.Name != "" {
			handlers = append(handlers, sql.WithKeyword(req.Name))
		}
		if req.Status != -1 {
			handlers = append(handlers, mysql.WithWhere("status = ?", req.Status))
		}
		if len(req.CreatedAtRange) == 2 {
			t1, t2, _ := parseDate(req.CreatedAtRange[0], req.CreatedAtRange[1])
			handlers = append(handlers, sql.WithBetween("`start_time`", t1, t2))
		}
		if len(req.EndAtRange) == 2 {
			t1, t2, _ := parseDate(req.EndAtRange[0], req.EndAtRange[1])
			handlers = append(handlers, sql.WithBetween("`end_time`", t1, t2))
		}
		handlers = append(handlers, mysql.WithWhere("user_id = ?", req.UserId))
		return sql.NewUserTaskModel().DeleteList(handlers...)
	}
	for _, id := range req.TaskId {
		task, err := sql.NewUserTaskModel().First(mysql.WithWhere("id = ?", id))
		if err != nil {
			log.Errorf("api_analyze - 获取任务信息失败: %v", err)
			return err
		}
		if task.UserId != req.UserId {
			return errors.New("permission denied")
		}
		err = sql.NewUserTaskModel().Delete(id)
		if err != nil {
			log.Errorf("api_analyze - 删除任务失败: %v", err)
			return err
		}
	}
	rsp.Status = 1
	return nil
}

func DeleteApiAnalyzeTask(req *pb.ApiAnalyzeDeleteRequest, rsp *pb.ApiAnalyzeDeleteResponse) error {
	if req.TaskId == nil || len(req.TaskId) == 0 {
		userTask, err := sql.NewUserTaskModel().First(mysql.WithWhere("id = ?", req.UserTaskId))
		if err != nil {
			return err
		}
		if userTask.UserId != req.UserId {
			return errors.New("permission denied")
		}
		return sql.NewTaskModel().DeleteList(req.UserTaskId, req.Url)
	}
	for _, id := range req.TaskId {
		task, err := sql.NewTaskModel().First(mysql.WithWhere("id = ?", id))
		if err != nil {
			log.Errorf("api_analyze - 获取任务信息失败: %v", err)
			return err
		}
		if task.UserId != req.UserId {
			return errors.New("permission denied")
		}
		count, err := sql.NewResultModel().Count(task.Id)
		if err != nil {
			return err
		}
		err = sql.NewUserTaskModel().DeleteRisksCount(task.Id, count)
		if err != nil {
			return err
		}
		err = sql.NewTaskModel().Delete(id)
		if err != nil {
			log.Errorf("api_analyze - 删除任务失败: %v", err)
			return err
		}
	}
	rsp.Status = 1
	return nil
}

func parseDate(d1, d2 string) (time.Time, time.Time, error) {
	t1, err := time.ParseInLocation(utils.DateLayout, d1, time.Now().Location())
	if err != nil {
		return t1, t1, err
	}
	t2, err := time.ParseInLocation(utils.DateLayout, d2, time.Now().Location())
	if err != nil {
		return t1, t1, err
	}
	t2 = t2.Add(time.Hour*24 - time.Second)
	return t1, t2, nil
}

func getRiskPoint(riskType int) string {
	switch riskType {
	case 1:
		return "资源泄露，弱权限"
	case 2:
		return "接口滥用，未授权访问"
	case 3:
		return "信息泄露"
	case 4:
		return "可利用信息"
	default:
		return ""
	}
}

func getRiskTypeText(riskType int) string {
	switch riskType {
	case 1:
		return "可疑文件"
	case 2:
		return "可疑API"
	case 3:
		return "敏感信息"
	case 4:
		return "可构造包"
	default:
		return "未知类型"
	}
}

// ApiAnalyzeResultExport 结果导出
func ApiAnalyzeResultExport(req *pb.ApiAnalyzeResultExportRequest, rsp *pb.SystemDetectDownloadResponse) error {
	// 验证任务权限
	tasks, _, err := sql.NewTaskModel().List(0, 0, mysql.WithWhere("id in ? AND user_id = ?", req.TaskIds, req.UserId))
	if err != nil {
		return err
	}
	if len(tasks) == 0 {
		return errors.New("no tasks found or permission denied")
	}

	// 获取已授权的任务ID列表
	authorizedTaskIds := make([]uint64, 0, len(tasks))
	for _, task := range tasks {
		authorizedTaskIds = append(authorizedTaskIds, task.Id)
	}

	// 获取结果数据
	handlers := make([]mysql.HandleFunc, 0, 4)
	handlers = append(handlers, mysql.WithWhere("task_id in ?", authorizedTaskIds))
	results, err := sql.NewResultModel().ListAll(handlers...)
	if err != nil {
		return err
	}
	if len(results) == 0 {
		return nil
	}

	// 生成Excel文件
	file := excelize.NewFile()
	sheet := "API分析结果"
	err = file.SetSheetName("Sheet1", sheet)
	if err != nil {
		return err
	}

	// 设置表头---风险类型：1-可疑文件,2-可疑API,3-敏感信息,4-可构造包
	headers := []string{"URL", "风险类型", "风险指向", "描述", "状态码", "响应大小", "数据类型"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err = file.SetCellValue(sheet, cell, header)
		if err != nil {
			return err
		}
	}

	// 写入数据
	for i, r := range results {
		row := i + 2
		if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Url); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), getRiskTypeText(r.RiskType)); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), getRiskPoint(r.RiskType)); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("D%d", row), r.Describe); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("E%d", row), r.Code); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("F%d", row), r.Size); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
		if err = file.SetCellValue(sheet, fmt.Sprintf("G%d", row), r.ResponseType); err != nil {
			return fmt.Errorf("设置单元格值失败: %v", err)
		}
	}

	// 保存文件
	filename := fmt.Sprintf("api_analyze_result_%s.xlsx", time.Now().Format("20060102150405"))
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	if err := file.SaveAs(storagePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	fp, err := utils.DownloadFileEncrypt(accessPath, "API分析结果"+path.Ext(filename), "", false)
	if err != nil {
		return fmt.Errorf("生成下载路径失败: %v", err)
	}
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(filename))

	// 设置返回的文件路径
	rsp.Path = apiFilePath
	return nil
}
