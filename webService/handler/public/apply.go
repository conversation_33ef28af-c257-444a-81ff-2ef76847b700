package public

import (
	"context"
	"errors"
	"fmt"
	"micro-service/pkg/email"
	"strconv"
	"time"

	"micro-service/apiService/router"
	"micro-service/initialize/mysql"
	mysql2 "micro-service/middleware/mysql"
	"micro-service/middleware/mysql/apply_api"
	aac "micro-service/middleware/mysql/auth_access_client"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

func ApplyAdd(req *pb.ApplyApiRequest) error {
	err := apply_api.NewModel(mysql.GetInstance()).AddRecord(apply_api.Apply{Email: req.Email, Description: req.Description})
	if err != nil {
		return err
	}
	return nil
}

func ApplyList(req *pb.ApplyListRequest, rsp *pb.ApplyListResponse) error {
	var h []mysql2.HandleFunc
	if req.Search != "" {
		h = append(h, apply_api.WithSearch(req.Search))
	}
	if req.Email != "" {
		h = append(h, mysql2.WithLRLike("`email`", req.Email))
	}
	if req.EmailStatus != "" {
		h = append(h, mysql2.WithColumnValue("`email_status`", req.EmailStatus))
	}
	if req.AuditStatus != "" {
		h = append(h, mysql2.WithColumnValue("`is_pass`", req.AuditStatus))
	}
	h = append(h, mysql2.WithOrder("`updated_at` DESC"))

	l, total, err := apply_api.NewModel().List(int(req.Page), int(req.PerPage), h...)
	for i := range l {
		rsp.Items = append(rsp.Items, &pb.ApplyListResponseItem{
			Id:          l[i].Id,
			Email:       l[i].Email,
			Description: l[i].Description,
			Reason:      l[i].Reason,
			EmailStatus: l[i].EmailStatus,
			AuditStatus: l[i].IsPass,
			CreatedAt:   l[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:   l[i].UpdatedAt.Format(utils.DateTimeLayout),
		})
	}
	rsp.Total = total
	return err
}

func ApplyAudit(ctx context.Context, req *pb.ApplyAuditRequest) error {
	db := apply_api.NewModel()
	info, err := db.First(mysql2.WithColumnValue("`id`", req.Id))
	if err != nil {
		return err
	}
	if info.IsPass != apply_api.AuditWait {
		return errors.New("该申请已被审核")
	}

	var emailContent string
	switch req.AuditStatus {
	case apply_api.AuditApproved:
		// insert a new client record
		clientInfo, errClient := createClient(info.Email)
		if errClient != nil {
			return errClient
		}
		emailContent = approvedEmail(info.Email, clientInfo.Secret)
	case apply_api.AuditRejected:
		if req.Reason == "" {
			return errors.New("审核未通过的原因不能为空")
		}
		emailContent = rejectEmail(req.Reason)
		info.Reason = req.Reason
	}

	info.IsPass = req.AuditStatus
	info.EmailStatus = apply_api.EmailSending
	err = db.Update(&info)
	if err != nil {
		return err
	}

	log.WithContextInfof(ctx, "[Web] client apply audit, send to email: %s, content: %s", info.Email, emailContent)
	go sendEmail(ctx, &info, emailContent)

	return nil
}

func sendEmail(ctx context.Context, info *apply_api.Apply, content string) {
	err := email.Send("申请结果通知", content, []string{info.Email})
	if err != nil {
		info.EmailStatus = apply_api.EmailFail
		log.WithContextErrorf(ctx, "[Web] client apply audit, send email failed, email: %s, caused by: %v", info.Id, err)
	} else {
		info.EmailStatus = apply_api.EmailSuccess
	}

	err = apply_api.NewModel().Update(info)
	if err != nil {
		log.WithContextErrorf(ctx, "[Web] client apply audit, update apply record failed, id->%d, caused by: %v", info.Id, err)
	}
}

func approvedEmail(email, secretKey string) string {
	n := time.Now()
	return fmt.Sprintf(`尊敬的用户：
	
	您好！我非常高兴的通知您，您提交的申请已通过我们的审核。
	
	您的客户端ID为%s，Secret为%s，您单日可下发任务的最大限额为10次，累计100次。
	请您妥善保管以上信息。

	如您有任何问题，欢迎您与我们进一步联系。

	FORadar团队
	%d年%d月%d日`, email, secretKey, n.Year(), n.Month(), n.Day())
}

func rejectEmail(reason string) string {
	n := time.Now()
	return fmt.Sprintf(`尊敬的用户：
	
	您好！我非常遗憾的通知您，您提交的申请未能通过我们的审核。
原因如下：
	%s

	如您有任何问题，欢迎您与我们进一步联系。

	FORadar团队
	%d年%d月%d日`, reason, n.Year(), n.Month(), n.Day())
}

func createClient(email string) (aac.AuthAccessClient, error) {
	client := aac.AuthAccessClient{
		CompanyName:        "",
		Domain:             "",
		UserId:             "",
		Status:             strconv.Itoa(aac.AuthAccessClientStatusEnable),
		ClientId:           email,
		Secret:             utils.RandString(12),
		Data:               "",
		Type:               0,
		Scope:              router.ScopeDiscover,
		ExpiredAt:          time.Date(2099, 12, 31, 23, 59, 59, 0, time.Now().Location()),
		IpWhitelist:        "*",
		AccountType:        aac.ClientTypeGoby,
		DateKey:            0,
		PerCnameLimit:      0,
		DayAssetsLimit:     0,
		MonDetectLimit:     0,
		DayAssetsCount:     0,
		UsedDetectCount:    0,
		DayClientTaskLimit: 10,
		ClientTaskTotal:    100,
	}
	err := aac.NewAuthAccessClientModel().Add(&client)
	return client, err
}
