package custom_poc

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	es "micro-service/initialize/es"
	elasticx "micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/custom_pocs"
	"micro-service/middleware/mysql/scan_pocs"
	"micro-service/middleware/mysql/user_poc"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dbx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/scanService/handler/microkernel"
	pb "micro-service/webService/proto"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/olivere/elastic"
	"gorm.io/gorm"
)

// List 获取自定义poc列表
func List(ctx context.Context, req *pb.CustomPocQueryListAndSearch, rsp *pb.CustomPocList) error {
	var cond []mysql.HandleFunc
	if req.List == nil {
		req.List = &pb.CustomPocQueryList{Page: 1, PageSize: 15}
	} else {
		if req.List.Page <= 0 {
			req.List.Page = 1
		}
		if req.List.PageSize <= 0 || req.List.PageSize > 100 {
			req.List.PageSize = 15
		}
	}
	// 查询条件 关键字 等级 状态 漏洞类型
	if req.Search != nil && req.Search.Keyword != "" {
		cond = append(cond, mysql.WithLRLike("name", req.Search.Keyword))
	}
	if req.Search != nil && req.Search.Level != nil && len(req.Search.Level) > 0 {
		cond = append(cond, mysql.WithValuesIn("level", req.Search.Level))
	}
	// 特殊处理 0 全部 1 未发布 2发布 对应表中-1
	if req.Search != nil && req.Search.Publish != nil && req.Search.Publish[0] > 0 {
		cond = append(cond, mysql.WithColumnValue("publish", req.Search.Publish[0]-1))
	}

	// 只能获取自己的自定义poc
	cond = append(cond, mysql.WithColumnValue("user_id", req.Search.UserId))

	if req.Search != nil && req.Search.VulType != nil && len(req.Search.VulType) > 0 {
		// 多个漏洞类型按照or进行组合
		t := ""
		for i, v := range req.Search.VulType {
			t = t + "vul_type LIKE '%" + fmt.Sprintf("%v", v) + "%'"
			if i < len(req.Search.VulType)-1 {
				t = t + " or "
			}

		}
		cond = append(cond, mysql.WithWhere(t))

	}

	data, total, err := custom_pocs.NewCustomPocsModel().List(int(req.List.Page), int(req.List.PageSize), cond...)
	if err != nil {
		log.WithContextErrorf(ctx, "获取自定义poc列表失败: %v", err)
		return err
	}
	rsp.Total = total
	for _, v := range data {
		rsp.Items = append(rsp.Items, Model2ListResponse(&v))
	}
	return nil
}

// Create 创建自定义poc
func Create(ctx context.Context, req *pb.CustomPoc, rsp *pb.CreateCustomPocResponse) error {
	item := Request2Model(req)
	uniq, err := CheckUniqPoCName(item)
	if err != nil {
		return err
	}
	if !uniq {
		return errors.New("poc名称已存在")
	}
	if item.DisclosureDate == "" {
		item.DisclosureDate = time.Now().Format(time.DateOnly)
	}
	// 计算风险资产数量
	riskCount, err := CountRiskIp(ctx, item.FofaQuery, item.UserId)
	if err != nil {
		log.WithContextWarnf(ctx, "计算风险资产数量失败，设置为0: %v", err)
		item.FofaRecords = 0
	} else {
		item.FofaRecords = riskCount
	}

	//事务操作
	tx := mysql.GetDbClient().Begin()
	if tx.Error != nil {
		log.WithContextErrorf(ctx, "开始事务失败: %v", tx.Error)
		return tx.Error
	}
	// 先插入custom_poc表 后续scan_poc需要custom_poc.id
	if err = custom_pocs.NewCustomPocsModel(tx).Create(item); err != nil {
		log.WithContextErrorf(ctx, "创建自定义poc失败: %v", err)
		tx.Rollback()
		return err
	}
	// 如果是发布状态 插入scan_poc表和user_record表中 得到scan_poc_id后写入到cutsom_poc
	if req.SaveAndPublish {
		if err = AddPoc2ScanAndUser(tx, item, 0, req.OperateCompanyId); err != nil {
			log.WithContextErrorf(ctx, "发布自定义poc失败: %v", err)
			tx.Rollback()
			return err
		}
		// 更新custom_poc的poc_id
		if err = custom_pocs.NewCustomPocsModel(tx).Update(&map[string]interface{}{
			"poc_id": item.PocId,
		}, func(db *gorm.DB) {
			db.Where("id = ?", item.ID)
		}); err != nil {
			log.WithContextErrorf(ctx, "更新自定义poc失败: %v", err)
			tx.Rollback()
			return err
		}
	}

	rsp.Id = item.ID
	tx.Commit()
	return nil
}

// Update 更新自定义poc
func Update(ctx context.Context, req *pb.CustomPoc, rsp *pb.Empty) error {
	item := Request2Model(req)

	uniq, err := CheckUniqPoCName(item)
	if err != nil {
		return err
	}
	if !uniq {
		return errors.New("poc名称已存在")
	}

	// 修改查询语句等情况需要重新计算
	riskCount, err := CountRiskIp(ctx, item.FofaQuery, item.UserId)
	if err != nil {
		log.WithContextWarnf(ctx, "更新时计算风险资产数量失败: %v", err)
		// 可以选择保持原值或设置为0
	} else {
		item.FofaRecords = riskCount
	}

	// 事务操作
	tx := mysql.GetDbClient().Begin()
	if tx.Error != nil {
		log.WithContextErrorf(ctx, "开始事务失败: %v", tx.Error)
		return tx.Error
	}

	// 存在scan_poc表的id则已经发布过
	if req.PocId != 0 {
		// 发布 == 直接更新 不发布 == 直接删除
		if req.SaveAndPublish {
			if err = UpdatePocInScanAndUser(tx, item, 0); err != nil {
				log.WithContextErrorf(ctx, "更新自定义poc失败: %v", err)
				tx.Rollback()
				return err
			}
		} else {
			if err = DeletePocInScanAndUser(tx, item, true); err != nil {
				log.WithContextErrorf(ctx, "下线自定义poc失败: %v", err)
				tx.Rollback()
				return err
			}
			// 更新custom_poc的poc_id=0 scan_pocs表记录删除了
			item.PocId = 0
		}

		// 不存在poc_id并且需要发布 插入新纪录
	} else if req.SaveAndPublish {
		if err = AddPoc2ScanAndUser(tx, item, 0, req.OperateCompanyId); err != nil {
			log.WithContextErrorf(ctx, "发布自定义poc失败: %v", err)
			tx.Rollback()
			return err
		}
	}

	// 转成interface{} 保证每个字段均可被更新
	itemMap := make(map[string]interface{})
	itemMap, _ = utils.StructToMap(item, "json")

	// 更新custom_poc表
	if err = custom_pocs.NewCustomPocsModel(tx).Update(itemMap, func(db *gorm.DB) {
		db.Where("id = ?", item.ID)
	}); err != nil {
		log.WithContextErrorf(ctx, "更新自定义poc失败: %v", err)
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}

// Delete 删除自定义poc
func Delete(ctx context.Context, req *pb.DeleteCustomPocRequest, rsp *pb.Empty) error {
	// 删除三表记录和文件
	// 事务操作
	tx := mysql.GetDbClient().Begin()
	if tx.Error != nil {
		log.WithContextErrorf(ctx, "开始事务失败: %v", tx.Error)
		return tx.Error
	}

	// 构造文件名删除scan_poc表记录和user_poc表记录
	for _, id := range req.Ids {
		item, err := custom_pocs.NewCustomPocsModel(tx).GetById(id)
		if err != nil {
			log.WithContextErrorf(ctx, "获取自定义poc失败: %v", err)
			tx.Rollback()
			return err
		}

		if err = custom_pocs.NewCustomPocsModel(tx).Delete([]uint64{id}); err != nil {
			log.WithContextErrorf(ctx, "删除自定义poc失败: %v", err)
			tx.Rollback()
			return err
		}

		if err = DeletePocInScanAndUser(tx, item, false); err != nil {
			log.WithContextErrorf(ctx, "删除scan_poc表和user_poc表记录失败: %v", err)
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

// GetDetail 获取自定义poc详情
func GetDetail(ctx context.Context, req *pb.CustomPocSearch, rsp *pb.CustomPoc) error {
	var res *custom_pocs.CustomPocs
	var err error
	// 优先查询id
	if len(req.Ids) == 1 {
		res, err = custom_pocs.NewCustomPocsModel().GetById(req.Ids[0])
	} else if req.Name != "" {
		res, err = custom_pocs.NewCustomPocsModel().First(mysql.WithColumnValue("name", req.Name))
	} else {
		return fmt.Errorf("暂不支持的查询参数")
	}
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return gorm.ErrRecordNotFound
	}
	if err != nil {
		log.WithContextErrorf(ctx, "获取自定义poc详情失败: %v", err)
		return err
	}

	*rsp = *Model2PoCResponse(res)
	return nil
}

// Publish 发布/下线 自定义poc
func Publish(ctx context.Context, req *pb.CustomPocSearch, rsp *pb.Empty) error {

	// 事务操作
	tx := mysql.GetDbClient().Begin()
	if tx.Error != nil {
		log.WithContextErrorf(ctx, "开始事务失败: %v", tx.Error)
		return tx.Error
	}

	for _, v := range req.Ids {
		// 获取记录后更新
		item, err := custom_pocs.NewCustomPocsModel(tx).GetById(v)
		if err != nil {
			log.WithContextErrorf(ctx, "获取自定义poc失败: %v", err)
			tx.Rollback()
			return err
		}
		item.Publish = req.Publish[0] == 1 // 0为下线 1为发布
		// 查询返回会自动把时间填充为RFC3339格式，需要转换为YYYY-MM-DD格式
		item.DisclosureDate = RFC3339ToYYYYMMDD(item.DisclosureDate)
		if item.Publish {
			if item.PocId != 0 {
				return errors.New("已发布状态不能重复发布")
			}
			item.PocId = 0 // 插入新数据会产生新的poc_id
			// 发布则插入scan_poc表和user_poc表
			if err = AddPoc2ScanAndUser(tx, item, 0, req.OperateCompanyId); err != nil {
				log.WithContextErrorf(ctx, "发布自定义poc失败: %v", err)
				tx.Rollback()
				return err
			}
		} else {
			// 下线直接硬删除scan_pocs表记录和user_pocs表记录 并更新custom_poc表的poc_id为0
			if err = DeletePocInScanAndUser(tx, item, true); err != nil {
				log.WithContextErrorf(ctx, "下线自定义poc失败: %v", err)
				tx.Rollback()
				return err
			}
			item.PocId = 0
		}

		// 更新custom_poc表
		itemMap, _ := utils.StructToMap(item, "json")
		if err = custom_pocs.NewCustomPocsModel(tx).Update(itemMap, func(db *gorm.DB) {
			db.Where("id = ?", item.ID)
		}); err != nil {
			log.WithContextErrorf(ctx, "更新自定义poc失败: %v", err)
			tx.Rollback()
			return err
		}

	}
	tx.Commit()
	return nil
}

// CountRiskIp 统计风险ip
func CountRiskIp(ctx context.Context, fofaSearch string, userId uint64) (int, error) {
	// 解析fofa查询语句
	fofaRsp, err := microkernel.GetFOFAParse(base64.StdEncoding.EncodeToString([]byte(fofaSearch)))
	if err != nil {
		return 0, err
	}

	// 检查索引是否为空
	if len(fofaRsp.Index) == 0 {
		return 0, errors.New("没有可查询的索引")
	}

	// 检查查询语句是否有效
	if strings.TrimSpace(fofaRsp.Query) == "" {
		return 0, errors.New("查询语句无效")
	}

	// 获取用户台账IP列表
	tableIps, err := getUserTableIps(ctx, userId)
	if err != nil {
		return 0, err
	}

	// 如果用户没有台账IP，直接返回0
	if len(tableIps) == 0 {
		return 0, nil
	}

	// 构建ES查询，包含用户过滤条件和台账IP过滤
	boolQuery := microkernel.GetInstance().NewQuery(fofaRsp.Query, userId)

	// 添加台账IP过滤条件
	boolQuery.Must(elastic.NewTermsQuery("ip", utils.StringSliceToInterfaceSlice(tableIps)...))

	count, err := es.GetInstance(cfg.LoadElastic()).Count(fofaRsp.Index...).Query(boolQuery).Do(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "ES查询风险资产数量失败: %v", err)
		return 0, err
	}

	log.WithContextInfof(ctx, "用户 %d 的风险资产统计完成，FOFA查询: %s，台账IP数量: %d，匹配数量: %d", userId, fofaSearch, len(tableIps), count)
	return int(count), nil
}

// getUserTableIps 获取用户台账IP列表
func getUserTableIps(ctx context.Context, userId uint64) ([]string, error) {

	// 获取缓存数据
	cacheKey := fmt.Sprintf("foradar_cache:fofaee_assets:ips:%d", userId)
	var Ips []string
	if redis.GetCache(cacheKey, &Ips) {
		log.WithContextInfof(ctx, "用户 %d 台账IP从缓存获取，数量: %d", userId, len(Ips))
		return Ips, nil
	}

	// 构建查询条件：只查询已确认和上传的资产
	var builder elasticx.SearchBuilder
	builder.AddMust([]interface{}{"user_id", "eq", userId})
	builder.AddMust([]interface{}{"status", "in", []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}})

	params := builder.Build()

	// 获取所有台账IP
	assets, err := elasticx.AllByParams[fofaee_assets.FofaeeAssets](500, params, nil, "ip")
	if err != nil {
		return nil, err
	}

	// 提取IP列表并去重
	ipSet := make(map[string]bool)
	for _, asset := range assets {
		if asset.Ip != "" {
			ipSet[asset.Ip] = true
		}
	}

	// 转换为字符串切片
	tableIps := make([]string, 0, len(ipSet))
	for ip := range ipSet {
		tableIps = append(tableIps, ip)
	}
	// 缓存结果
	redis.SetCache(cacheKey, 1*time.Minute, tableIps)

	log.WithContextInfof(ctx, "用户 %d 台账IP从数据库获取，数量: %d", userId, len(tableIps))
	return tableIps, nil
}

// RiskAssetsUpdate 风险资产更新
func RiskAssetsUpdate(ctx context.Context, req *pb.RiskAssetsUpdateRequest, rsp *pb.RiskAssetsUpdateResponse) error {
	key := fmt.Sprintf("custom_poc:risk_assets_update_time:%v", req.UserId)
	// 存在缓存 两者相差时间肯定未到一小时
	if redis.GetCache(key, &rsp.UpdateTime) {
		t, err := time.ParseInLocation(time.DateTime, rsp.UpdateTime, time.Now().Location())
		if err != nil {
			log.WithContextErrorf(ctx, "解析缓存时间失败: %v", err)
			return err
		}

		if time.Since(t) < time.Hour {
			return nil
		}
	}
	// 非手动点击 不进行风险资产更新
	if !req.Click {
		return nil
	}
	// 查询当前user的所有自定义poc进行风险资产计算
	customPocs, _, err := custom_pocs.NewCustomPocsModel().List(0, 0, mysql.WithColumnValue("user_id", req.UserId))
	if err != nil {
		log.Errorf("查询自定义poc失败: %v", err)
		return err
	}
	// 开启事务
	tx := mysql.GetDbClient().Begin()
	for _, v := range customPocs {
		// 计算风险资产数量
		riskCount, err := CountRiskIp(ctx, v.FofaQuery, v.UserId)
		if err != nil {
			log.WithContextWarnf(ctx, "%v,%v计算风险资产数量失败: %v", v.ID, v.Name, err)
			tx.Rollback()
			return err
		}
		v.FofaRecords = riskCount
		v.DisclosureDate = RFC3339ToYYYYMMDD(v.DisclosureDate)
		// 更新custom_pocs表 只需要更新fofa_records字段
		if err = custom_pocs.NewCustomPocsModel(tx).Update(&map[string]interface{}{
			"fofa_records": v.FofaRecords,
		}, func(db *gorm.DB) {
			db.Where("id = ?", v.ID)
		}); err != nil {
			log.Errorf("更新custom_pocs表失败: %v", err)
			tx.Rollback()
			return err
		}
		// 未发布 不需要更新user_pocs表
		if v.PocId == 0 {
			continue
		}
		// 更新user_pocs表
		if err = user_poc.NewUsersPocsModel(tx).Update(&map[string]interface{}{
			"risk_num": v.FofaRecords,
		}, func(db *gorm.DB) {
			db.Where("pocs_id = ?", v.PocId)
		}); err != nil {
			log.Errorf("更新user_pocs表失败: %v", err)
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	// 缓存更新时间
	rsp.UpdateTime = time.Now().Format(time.DateTime)
	redis.SetCache(key, 0, rsp.UpdateTime)

	return nil

}

// AddPoc2ScanAndUser 添加scan_poc表和user_poc列表 0启用 1禁用
func AddPoc2ScanAndUser(tx *gorm.DB, item *custom_pocs.CustomPocs, status int, ocId int64) error {
	// 生成poc文件
	fn, err := BuildFile(int(item.ID), item.Editor, false)
	if err != nil {
		return err
	}

	res := customPoc2ScanPoc(item)
	res.Filename = fn
	if err = scan_pocs.NewScanPocModel(tx).Create(res); err != nil {
		return err
	}
	// 更新custom_poc表的poc_id列
	if err = custom_pocs.NewCustomPocsModel(tx).Update(&map[string]interface{}{
		"poc_id": res.ID,
	}, func(db *gorm.DB) { db.Where("id = ?", item.ID) }); err != nil {
		return err
	}
	// 加入user_poc表
	u := &user_poc.UsersPocs{
		UserId: item.UserId,
		PocsId: res.ID,
		Status: status,
	}
	if ocId > 0 {
		u.CompanyId = uint64(ocId)
	}
	if err = user_poc.NewUsersPocsModel(tx).Create(u); err != nil {
		return err
	}

	// 保存scan_poc表的id
	item.PocId = res.ID
	return nil
}

// UpdatePocInScanAndUser 更新scan_poc表和user_poc列表
func UpdatePocInScanAndUser(tx *gorm.DB, item *custom_pocs.CustomPocs, status int) error {
	// 生成poc文件 存在则覆盖
	fn, err := BuildFile(int(item.ID), item.Editor, false)
	if err != nil {
		return err
	}

	// 更新scan_poc表
	res := customPoc2ScanPoc(item)
	res.Filename = fn
	resMap, err := utils.StructToMap(res, "json")
	if err != nil {
		return err
	}
	// 更新scan_poc表
	if err = scan_pocs.NewScanPocModel(tx).Update(resMap, func(db *gorm.DB) {
		db.Where("id = ?", res.ID)
	}); err != nil {
		return err
	}
	// 更新user_poc表
	if err = user_poc.NewUsersPocsModel(tx).Update(&map[string]interface{}{
		"status":   status,
		"risk_num": item.FofaRecords,
	}, func(db *gorm.DB) {
		db.Where("pocs_id = ?", res.ID)
	}); err != nil {
		return err
	}

	return nil
}

// DeletePocInScanAndUser 删除scan_poc表和user_poc表的记录
func DeletePocInScanAndUser(tx *gorm.DB, item *custom_pocs.CustomPocs, hard bool) error {
	// 没有发布过 其他表不存在数据
	if item.PocId == 0 {
		return nil
	}
	// 删除scan_poc表记录
	if err := scan_pocs.NewScanPocModel(tx).DeleteById([]uint64{item.PocId}, hard); err != nil {
		return err
	}
	// 删除user_poc表记录
	if err := user_poc.NewUsersPocsModel(tx).DeleteByPocId(item.PocId); err != nil {
		return err
	}
	// 删除文件
	if _, err := BuildFile(int(item.ID), "", true); err != nil {
		return err
	}
	return nil
}

// BuildFile 生成custom_poc_id文件
func BuildFile(id int, document string, isDel bool) (string, error) {

	if len(document) > 10*1024*1024 { // 10MB限制
		return "", errors.New("document too large")
	}
	filePathDir := filepath.Join(cfg.GetGoScanner(), "exploits/user")
	if _, err := os.Stat(filePathDir); os.IsNotExist(err) {
		if err = os.MkdirAll(filePathDir, 0755); err != nil {
			log.Errorf("创建目录失败: %v", err)
			return "", err
		}
		// 目录不存在 文件一定不存在 直接返回
		if isDel {
			return "", nil
		}
	}
	filename := fmt.Sprintf("custom_poc_%d.go", id)
	filePath := filepath.Join(filePathDir, filename)

	// 删除文件
	if isDel {
		if err := os.Remove(filePath); err != nil {
			log.Infof("删除文件失败: %v", err)
		}
		return filename, nil
	}

	file, err := os.Create(filePath)
	if err != nil {
		log.Errorf("创建文件失败: %v", err)
		return "", err
	}
	if err = os.Chmod(filePath, 0777); err != nil {
		log.Errorf("更新%s文件权限失败:%v", filePath, err)
		return "", err
	}

	defer file.Close()

	if _, err = file.WriteString(document); err != nil {
		log.Errorf("写入文件失败: %v", err)
		return "", err
	}
	log.Infof("自定义poc文件生成完成,路径: %s", filePath)
	return filename, nil
}

func RFC3339ToYYYYMMDD(t string) string {
	if t == "" {
		return ""
	}
	// RFC3339格式转YYYY-MM-DD格式
	tt, _ := time.Parse(time.RFC3339, t)
	return tt.Format(time.DateOnly)
}

// Model2ListResponse 数据库模型转列表响应结构
func Model2ListResponse(v *custom_pocs.CustomPocs) *pb.CustomPoc {
	tmpDisTime := v.DisclosureDate
	disTime := strings.Split(v.DisclosureDate, "T")
	if len(disTime) == 2 {
		tmpDisTime = disTime[0]
		if disTime[0] == "0001-01-01" {
			tmpDisTime = ""
		}
	}
	return &pb.CustomPoc{
		Id:             v.ID,
		Name:           v.Name,
		Level:          uint32(v.Level),
		SaveAndPublish: v.Publish,
		VulType:        strings.Split(v.VulType, ","),
		UpdateTime:     v.UpdatedAt.Format(time.DateTime),
		RiskCount:      int32(v.FofaRecords),
		Cve:            strings.Split(v.Cve, ","),
		DisclosureDate: tmpDisTime,
		State:          !v.Publish,
		PocId:          v.PocId,
	}
}

// Request2Model 请求结构转数据库模型
func Request2Model(req *pb.CustomPoc) *custom_pocs.CustomPocs {
	//处理Editor字段，如果Editor 里面的CVEIDs字段是空字符的话，转为数组格式，如果是已经数组格式不需要处理
	processedEditor := processEditorCVEIDs(req.Editor)

	return &custom_pocs.CustomPocs{
		ModelFull:      dbx.ModelFull{ID: req.Id},
		UserId:         req.UserId,
		Name:           req.Name,
		Description:    req.Description,
		Product:        req.Product,
		Homepage:       req.Homepage,
		DisclosureDate: req.DisclosureDate,
		Author:         req.Author,
		FofaQuery:      req.FofaQuery,
		Level:          uint8(req.Level),
		Impact:         req.Impact,
		Recommendation: req.Recommendation,
		References:     req.References,
		Is0Day:         req.Is0Day,
		ScanSteps:      req.ScanSteps,
		VulType:        strings.Join(req.VulType, ","),
		Cve:            strings.Join(req.Cve, ","),
		Editor:         processedEditor,
		Publish:        req.SaveAndPublish,
		Cnnvd:          strings.Join(req.Cnnvd, ","),
		Cnvd:           strings.Join(req.Cnvd, ","),
		CvssScore:      req.CvssScore,
		AttackSurfaces: req.AttackSurfaces,
		PocId:          req.PocId,
	}
}

// Model2PoCResponse 数据库模型转PoC响应结构
func Model2PoCResponse(v *custom_pocs.CustomPocs) *pb.CustomPoc {
	return &pb.CustomPoc{
		Id:             v.ID,
		Name:           v.Name,
		Description:    v.Description,
		Product:        v.Product,
		Homepage:       v.Homepage,
		DisclosureDate: v.DisclosureDate,
		Author:         v.Author,
		FofaQuery:      v.FofaQuery,
		Level:          uint32(v.Level),
		Impact:         v.Impact,
		Recommendation: v.Recommendation,
		References:     v.References,
		Is0Day:         v.Is0Day,
		ScanSteps:      v.ScanSteps,
		VulType:        strings.Split(v.VulType, ","),
		Cve:            strings.Split(v.Cve, ","),
		Editor:         v.Editor,
		UpdateTime:     v.UpdatedAt.Format(time.DateTime),
		RiskCount:      int32(v.FofaRecords),
		Cnnvd:          strings.Split(v.Cnnvd, ","),
		Cnvd:           strings.Split(v.Cnvd, ","),
		CvssScore:      v.CvssScore,
		AttackSurfaces: v.AttackSurfaces,
		SaveAndPublish: v.Publish,
		State:          !v.Publish,
		PocId:          v.PocId,
	}
}

// customPoc2ScanPoc 自定义poc转scan_poc
func customPoc2ScanPoc(v *custom_pocs.CustomPocs) *scan_pocs.ScanPoc {
	res := &scan_pocs.ScanPoc{
		Name:           v.Name,
		Author:         v.Author,
		Level:          int(v.Level),
		Description:    v.Description,
		Impact:         v.Impact,
		Recommendation: v.Recommendation,
		DisclosureDate: v.DisclosureDate,
		FofaQuery:      v.FofaQuery,
		Homepage:       v.Homepage,
		Product:        v.Product,
		VulType:        v.VulType,
		CveIds:         v.Cve,
		References:     v.References,
		Tags:           v.Tags,
		AttackSurfaces: v.AttackSurfaces,
		Cnvd:           v.Cnvd,
		Cnnvd:          v.Cnnvd,
		CvssScore:      v.CvssScore,
		From:           2, //自定义
		ModelFull:      dbx.ModelFull{ID: v.PocId},
	}

	return res
}

func CheckUniqPoCName(item *custom_pocs.CustomPocs) (bool, error) {
	// 查询是否存在这个名字的poc (scan_poc和custom_poc)

	res, err := scan_pocs.NewScanPocModel().FindByQuerys(func(db *gorm.DB) {
		db.Where("name = ?", item.Name)
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Errorf("查询scan_pocs表出错:%v", err)
		return false, err
	}
	ans, err := custom_pocs.NewCustomPocsModel().First(func(db *gorm.DB) {
		db.Where("name = ?", item.Name)
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Errorf("查询custom_poc表出错:%v", err)
		return false, err
	}

	if (len(res) == 0 || res[0].ID == item.PocId) && (ans == nil || ans.ID == item.ID) {
		return true, nil
	}

	return false, nil

}

// processEditorCVEIDs 处理Editor字段中的CVEIDs，如果CVEIDs是空字符串则转为数组格式
func processEditorCVEIDs(editor string) string {
	// 如果Editor为空，直接返回
	if editor == "" {
		return editor
	}

	// 查找expJson变量中的JSON字符串
	// 找到JSON字符串的开始位置
	jsonStartIndex := strings.Index(editor, "expJson := `")
	if jsonStartIndex == -1 {
		// 如果没有找到expJson，返回原始内容
		return editor
	}

	// 找到JSON字符串的开始位置（`之后）
	jsonContentStart := jsonStartIndex + len("expJson := `")

	// 找到JSON字符串的结束位置
	jsonContentEnd := strings.Index(editor[jsonContentStart:], "`")
	if jsonContentEnd == -1 {
		// 如果没有找到结束标记，返回原始内容
		return editor
	}

	// 提取JSON字符串
	jsonContent := editor[jsonContentStart : jsonContentStart+jsonContentEnd]

	// 尝试解析JSON
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(jsonContent), &jsonData); err != nil {
		// 如果解析失败，返回原始内容
		log.Infof("解析expJson中的JSON失败，保持原始内容: %v", err)
		return editor
	}

	// 检查CVEIDs字段
	if cveIDs, exists := jsonData["CVEIDs"]; exists {
		// 如果CVEIDs是空字符串，转换为空数组
		if cveIDsStr, ok := cveIDs.(string); ok && cveIDsStr == "" {
			jsonData["CVEIDs"] = []string{}

			// 重新序列化JSON
			if processedBytes, err := json.Marshal(jsonData); err == nil {
				// 替换原始Editor中的JSON部分
				newEditor := editor[:jsonContentStart] + string(processedBytes) + editor[jsonContentStart+jsonContentEnd:]
				return newEditor
			} else {
				log.Infof("重新序列化JSON失败，保持原始内容: %v", err)
				return editor
			}
		}
	}

	// 如果CVEIDs不存在或已经是数组格式，返回原始内容
	return editor
}
