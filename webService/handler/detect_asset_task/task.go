// 单位资产测绘

package detect_asset_task

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/pkg/cfg"
	"net"
	"path"
	"strings"
	"time"

	"micro-service/apiService/upload"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/clues_groups"
	"micro-service/middleware/mysql/company"
	dat "micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"micro-service/pkg/utils/image"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
)

const (
	masterFlag = "master"
	otherFlag  = "other"
)

//nolint:gocyclo,gocritic
func CreateTask(ctx context.Context, req *pb.DetectAssetTaskCreateRequest, rsp *pb.DetectAssetTaskCreateResponse) error {
	req.CompanyList = utils.ListDistinct(req.CompanyList)
	req.OtherCompanyList = utils.ListDistinct(req.OtherCompanyList)
	log.Infof("[DetectAssetTask] 接收到的参数: %+v", req)
	// check-1: 检查用户剩余的测绘任务额度
	var err error
	// 如果是本地化环境的话，其他公司最多不超过100个，控股公司选择不超过200个，已知ip/域名等线索加起来不超过100个（封包的性能指标
	localSettings := cfg.GetInstance().Common.Local
	if cfg.IsLocalClient() || localSettings.Enable {
		companyCount, otherCompanyCount, otherClueCount := 0, 0, 0
		companyCount = len(req.CompanyList)
		if companyCount > localSettings.CompanyCount {
			log.Infof("[DetectAssetTask] company count:%d", companyCount)
			return fmt.Errorf("控股公司超过%d个，无法进行测绘", localSettings.CompanyCount)
		}
		otherCompanyCount = len(req.OtherCompanyList)
		if otherCompanyCount > localSettings.OtherCompanyCount {
			log.Infof("[DetectAssetTask] company count:%d, other company count:%d ", companyCount, otherCompanyCount)
			return fmt.Errorf("其他公司超过%d个，无法进行测绘", localSettings.OtherCompanyCount)
		}
		var ipError error
		for _, v := range req.Data {
			otherClueCount += len(v.Content)
			// 短路模式解析IP地址，本地化环境下，已知ip线索中不允许存在错误IP地址，回环地址，内网地址
			if v.Type == 6 && ipError == nil {
				for _, i := range v.Content {
					// 如果是IP段，则截取第一个IP地址
					if strings.Contains(i, "/") {
						i = strings.Split(i, "/")[0]
					}
					ip := net.ParseIP(i)
					if ip == nil {
						ipError = errors.New("已知ip线索中存在错误IP地址，该IP信息为:" + i)
						break
					}
					if ip.IsLoopback() {
						ipError = errors.New("已知ip线索中存在回环地址，该IP信息为:" + i)
						break
					}
					if ip.IsPrivate() {
						ipError = errors.New("已知ip线索中存在内网地址，该IP信息为:" + i)
						break
					}
				}
			}
		}
		if otherClueCount > localSettings.OtherClueCount {
			log.Infof("[DatectAssetTask] company count:%d, other companyCount:%d, other clue count:%d", companyCount, otherCompanyCount, otherClueCount)
			return fmt.Errorf("已知线索超过%d个，无法进行测绘", localSettings.OtherClueCount)
		}
		if ipError != nil {
			return ipError
		}
	}
	var companyInfo company.Company
	if req.CompanyId > 0 {
		companyDb := company.NewCompanyModel()
		companyInfo, err = companyDb.First(mysql.WithId(req.CompanyId), mysql.WithColumnValue("owner_id", req.UserId))
		if err != nil {
			return err
		}
		if companyInfo.LimitCloudRecommend-companyInfo.UsedCloudRecommend <= 0 {
			return errors.New("用户已达到限制次数，无法进行新的测绘任务")
		}
	}

	// check-2: 检查是否存在正在进行的测绘任务
	if errDoing := hasDoingTask(req.UserId); errDoing != nil {
		return errDoing
	}

	// check-3: 检查用户输入的线索
	// check-3.1: 用户初始线索校验
	cluesList, err := importCluesCheck(ctx, req)
	if err != nil {
		return err
	}
	// check-3.2: 检查导入IP
	importIps := utils.ListDistinct(req.ImportSureIps, true)
	for _, v := range importIps {
		if !utils.IsIP(v) {
			return fmt.Errorf("导入IP: %s不是有效的IP格式", v)
		}
	}

	userInfo, err := user.NewUserModel().FindById(req.UserId)
	if err != nil {
		return err
	}

	var realDetectCompanies = []string{req.Name}                                  // 本次实际测绘的公司
	var companyJSON []*pb.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit // 测绘公司及控股公司
	if userInfo.Role == user.UserRoleTenant || req.SafeUserId > 0 {
		if req.NoNeedControyCompany == "1" { // 仅测绘主体公司
			req.CompanyList = nil
		}

		realDetectCompanies = append(realDetectCompanies, req.CompanyList...)
		realDetectCompanies = append(realDetectCompanies, req.OtherCompanyList...)
		// 安服用户自己账户下发任务
		safeBySelf := companyInfo.ID == 0 && req.SafeUserId > 0
		userCompanyName := utils.If(safeBySelf || userInfo.IsRealCustomer == 0, req.Name, companyInfo.Name)

		var errByName error
		var childName1 []*holdingsUnit
		var childName = []string{userCompanyName} // 主体公司+控股公司（主体）+可测绘公司及其控股公司（创建填写）
		if req.NoNeedControyCompany == "" {
			childName1, errByName = getChildByName(ctx, 0, userCompanyName)
			if errByName != nil {
				log.WithContextWarnf(ctx, "[DetectAssetTask] get company: %s holding failed: %v", userCompanyName, err)
			}
		}
		log.Infof("[DetectAssetTask] master company: %s, company_list: %v, get holdings list: %v", userCompanyName, req.CompanyList, childName1)

		// 追加主体测绘企业json
		master := &pb.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit{Name: userCompanyName, CompanyFlag: masterFlag}
		if req.NoNeedControyCompany == "1" {
			companyJSON = append(companyJSON, master)
		}
		if len(req.CompanyList) > 0 {
			for _, y := range req.CompanyList {
				for _, x := range childName1 {
					if y != x.Name {
						continue
					}
					log.Infof("[DetectAssetTask] %s match, percent: %s", y, x.Percent)
					master.Children = append(master.Children, &pb.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit{Name: y, Percent: x.Percent})
					break
				}
			}
			sortByPercent(master)
			companyJSON = append(companyJSON, master)
		}

		fv := func(ori, tar []string) error {
			if safeBySelf {
				return nil
			}
			for _, v := range tar {
				if utils.ListContains(ori, v) {
					continue
				}
				// 测绘企业不在用户控股企业时, 用户为安服用户时且企业为非真实客户时可以继续测绘
				if !(req.SafeUserId > 0 && userInfo.IsRealCustomer <= 0) {
					return fmt.Errorf("您没有%s企业的资产测绘权限, 请修改后重新填写", v)
				}
			}
			return nil
		}

		temp0 := utils.ListColumn(childName1, func(x *holdingsUnit) string { return x.Name })
		childName = utils.ListDistinct(append(childName, temp0...), true)
		// 校验主体公司和勾选的控股企业的测绘权限
		masterDetect := append(append([]string{}, req.Name), req.CompanyList...)
		if !safeBySelf {
			var detectCompanies []string
			if userInfo.DetectCompanyName != "" {
				_ = json.Unmarshal([]byte(userInfo.DetectCompanyName), &detectCompanies)
			}

			// 校验其他测绘公司的权限
			if errFv := fv(detectCompanies, req.OtherCompanyList); errFv != nil {
				return errFv
			}

			childName = append(childName, detectCompanies...)
			otherCompanyHoldings, errHolding := getChildByName(ctx, 0, detectCompanies...)
			if errHolding != nil {
				log.WithContextWarnf(ctx, "[DetectAssetTask] get company: %v holding failed: %v", detectCompanies, err)
			}
			for _, x := range otherCompanyHoldings {
				childName = append(childName, x.Name)
			}
		}
		// 校验用户输入企业名称及勾选控股公司的测绘权限
		if errFv := fv(utils.ListDistinct(childName), masterDetect); errFv != nil {
			return errFv
		}

		// 如果用户没有勾选控股公司, 默认选择控股比例大于50%的
		if len(req.CompanyList) == 0 && req.NoNeedControyCompany == "" {
			defaultChild, errName := getChildByName(ctx, 50, req.Name)
			if errName != nil {
				log.WithContextWarnf(ctx, "[DetectAssetTask] get company: %s holding failed: %v", req.Name, err)
			}
			companyJSON = append(companyJSON, holdingNames(defaultChild, masterFlag, req.Name)...)
			temp := utils.ListColumn(defaultChild, func(x *holdingsUnit) string { return x.Name })
			realDetectCompanies = append(realDetectCompanies, temp...)
		}

		// 当控股比例大于0时, 获取其他公司的控股公司
		if len(req.OtherCompanyList) > 0 {
			var errName error
			var oclNames []*holdingsUnit
			if req.Percent > 0 {
				oclNames, errName = getChildByName(ctx, req.Percent, req.OtherCompanyList...)
				if errName != nil {
					log.WithContextWarnf(ctx, "[DetectAssetTask] get company: %v holdings failed: %v", req.OtherCompanyList, errName)
				}
				temp := utils.ListDistinct(utils.ListColumn(oclNames, func(x *holdingsUnit) string { return x.Name }))
				realDetectCompanies = append(realDetectCompanies, temp...)
			}
			companyJSON = append(companyJSON, holdingNames(oclNames, otherFlag, req.OtherCompanyList...)...)
		}

		realDetectCompanies = utils.ListDistinct(realDetectCompanies, true)
	}

	// 新建分组
	groupItem := &clues_groups.ClueGroup{
		Name:      "资产测绘-" + req.Name + time.Now().Format(utils.DateTimeLayout),
		UserId:    uint(req.UserId),
		CompanyId: uint(req.CompanyId),
		IsShow:    clues_groups.Show,
	}
	err = clues_groups.NewCluesGrouper().Create(groupItem)
	if err != nil {
		return err
	}

	// 用户初始线索与分组建立关联
	for i := range cluesList {
		cluesList[i].GroupId = groupItem.Id
		if errUpsert := clueUpsert(cluesList[i]); errUpsert != nil {
			log.Warn(errUpsert)
		}
	}

	// 创建新的测绘任务记录
	newTask := &dat.DetectAssetsTask{
		UserId:             req.UserId,
		CompanyId:          req.CompanyId,
		SafeUserId:         req.SafeUserId,
		Name:               req.Name,
		GroupId:            groupItem.Id,
		Step:               dat.StepTwo,
		StepDetail:         dat.StepTwoConfirm,
		StepStatus:         dat.StatusDefault,
		Status:             dat.StatusDoing,
		CompanyJSON:        utils.AnyToStr(companyJSON),
		ConfirmCompanyList: utils.AnyToStr(realDetectCompanies),
		ReturnJson: utils.AnyToStr(map[string]any{
			itoa(dat.StepOneCompanyList):  realDetectCompanies,
			itoa(dat.StepTwoConfirm):      []int{},
			itoa(dat.StepTwoExpandDomain): []string{},
			itoa(dat.StepTwoExpandICP):    []string{},
			itoa(dat.StepTwoExpandCert):   []string{},
			itoa(dat.StepTwoExpandAll):    []string{},
			itoa(dat.StepTwoExpandIP):     []string{},
			"scene_group_set":             map[string]int{"expand_init_clues": 0, "scene_group_id": 0},
			"scene_ids":                   []int{},
		}),
		OffAssetsTo:       int(req.OffAssetsTo),         // 离线资产去向
		Bandwidth:         cast.ToString(req.Bandwidth), // 扫描带宽
		Percent:           cast.ToString(req.Percent),
		ImportSureIps:     utils.If(len(req.ImportSureIps) > 0, utils.AnyToStr(importIps), ""),
		ScanType:          int(req.ScanType), // 扫描端口类型
		IsAutoDomainBurst: int(req.IsAutoDomainBrust),
		IsAutoLeakAsset:   int(req.IsAutoLeakAssets),
		IsAutoDataAsset:   int(req.IsAutoDataAssets),
		IsAutoBusinessApi: int(req.IsAutoBusinessApi),
		IsNeedHunter:      int(req.IsNeedHunter),
		IsAutoExpendIp:    int(req.IsAutoExpendIp),
		IsNeedDnschecker:  int(req.IsNeedDnschecker),
		IsAutoURLApi:      int(req.IsAutoUrlApi),
		IsCheckRisk:       int(req.IsCheckRisk),
		FofaRange:         int(req.FofaRange),
		DetectMode:        int(req.DetectMode),
	}
	// 探测模式设置转换
	detectModeConv(newTask, req.DetectMode)

	err = dat.NewModel().Create(newTask)
	if err != nil {
		return err
	}

	rsp.DetectTaskId = uint64(newTask.ID)
	rsp.GroupId = groupItem.Id
	return nil
}

func hasDoingTask(userId uint64) error {
	cnt, err := dat.NewModel().Count(
		mysql.WithColumnValue("user_id", userId),
		mysql.WithColumnValue("status", dat.StatusDoing),
		mysql.WithColumnValue("expand_source", dat.ExpandSourceDetect))
	if err != nil {
		return err
	}
	if cnt > 0 {
		return errors.New("当前用户存在正在进行的测绘任务，请等待任务完成后再下发新的测绘任务")
	}
	return nil
}

// 探测模式设置转换
func detectModeConv(newTask *dat.DetectAssetsTask, mode uint64) {
	switch mode {
	case dat.ModeSmart:
		newTask.IsIntellectMode = dat.True
		newTask.DetectType = dat.DetectTypeSpeed
	case dat.ModeDefault:
		newTask.DetectType = dat.DetectTypeSpeed
	case dat.ModeExpert:
		newTask.DetectType = dat.DetectTypeDepth
	case dat.ModeAuto:
		newTask.IsIntellectMode = dat.True
		newTask.DetectType = dat.DetectTypeSpeed
	}
}

func importCluesCheck(ctx context.Context, req *pb.DetectAssetTaskCreateRequest) ([]*clues.Clue, error) {
	var clueNum int
	for _, v := range req.Data {
		clueNum += len(v.Content)
	}

	var cluesList = make([]*clues.Clue, 0, clueNum)
	for i := range req.Data {
		clueType := req.Data[i].Type
		distinctContent := utils.ListDistinct(req.Data[i].Content, true)
		// 检查IP
		if clueType == clues.TYPE_IP {
			err := checkClueIps(distinctContent...)
			if err != nil {
				return nil, err
			}
		}

		for _, content := range distinctContent {
			clueItem := &clues.Clue{
				UserId:           req.UserId,
				CompanyId:        req.CompanyId,
				Content:          content,
				Source:           clues.ClueSourceManual,
				Type:             int(req.Data[i].Type),
				Status:           clues.AuditPassed,
				SafeUserId:       req.SafeUserId,
				IsFromCheckTable: clues.IsFromCheckTableTrue,
			}
			if clueType == clues.TYPE_LOGO {
				iconMeta, errUpload := upload.UploadDecrypt(content)
				if errUpload != nil {
					log.WithContextWarnf(ctx, "[DetectAssetTask] 上传icon文件解密失败, 原因: %v", errUpload)
					return nil, fmt.Errorf("上传icon文件: %s无效, 请重新上传", iconMeta.Name)
				}
				b64, errBase64 := image.ToBase64(path.Join(storage.GetRootPath(), iconMeta.Url))
				if errBase64 != nil {
					log.WithContextWarnf(ctx, "[DetectAssetTask]上传icon文件: %s无效, 原因: %v", iconMeta.Name, errBase64)
					return nil, fmt.Errorf("上传icon文件: %s无效, 请重新上传", iconMeta.Name)
				}
				decodeB64, _ := base64.StdEncoding.DecodeString(b64)
				clueItem.Hash = cast.ToInt(utils.Mmh3Hash32(utils.Mmh3Base64Encode(string(decodeB64))))
				clueItem.Content = storage.SaveIco(string(decodeB64), int64(clueItem.Hash), false) // address
			} else {
				if len([]rune(content)) > 200 {
					return nil, fmt.Errorf("线索: %s的长度超过200字符最大限制", content)
				}
				clueItem.Content = content
				switch clueType {
				case clues.TYPE_IP: // IP已经在上面校验过了
					// clueItem.ClueCompanyName = companyInfo.Name
				case clues.TYPE_DOMAIN:
					// 全部转小写
					d, errD := utils.FindRootDomain(content)
					if d == "" || errD != nil {
						return nil, fmt.Errorf("%s不是有效的域名", content)
					}
					clueItem.Content = strings.ToLower(d)
				case clues.TYPE_SUBDOMAIN:
					// 全部转小写
					clueItem.Content = strings.ToLower(content)
				case clues.TYPE_ICP:
					if icpNum := utils.GetIcpNumber(content); icpNum == "" {
						return nil, fmt.Errorf("ICP备案号: %s格式错误", content)
					}
				case clues.TYPE_CERT:
					if !utils.CheckCert(content) {
						return nil, fmt.Errorf("%s不是有效的证书格式", content)
					}
				}
			}
			cluesList = append(cluesList, clueItem)
		}
	}
	return cluesList, nil
}
