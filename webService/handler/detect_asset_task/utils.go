package detect_asset_task

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"net"
	"sort"
	"strconv"
	"strings"

	"sync"

	pb "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/pkg/cfg"
	"micro-service/pkg/localization"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	web "micro-service/webService/proto"

	"github.com/spf13/cast"
	"gorm.io/gorm"
)

var itoa = strconv.Itoa

// IP上限5000个
func checkClueIps(ips ...string) error {
	ips = utils.ListDistinctNonZero(ips)
	ipList := make([]string, 0, 5000)
	totalIPs := big.NewInt(0)
	threshold := big.NewInt(5000)
	for _, v := range ips {
		if isIP, isPrivate := utils.IsPrivateIP(v); isIP {
			if isPrivate {
				return fmt.Errorf("ip: %s是内网IP", v)
			}
			ipList = append(ipList, v)
			continue
		}
		ip, ipNet, err := net.ParseCIDR(v)
		if err != nil {
			return fmt.Errorf("ip: %s格式非法", v)
		}
		maskOnes, _ := ipNet.Mask.Size()
		if ip.To4() != nil { // 判断是否为 IPv4
			ipCount := new(big.Int).Lsh(big.NewInt(1), uint(32-maskOnes))
			totalIPs.Add(totalIPs, ipCount)
		} else { // 否则为 IPv6
			ipCount := new(big.Int).Lsh(big.NewInt(1), uint(128-maskOnes))
			totalIPs.Add(totalIPs, ipCount)
		}
		if totalIPs.Cmp(threshold) > 0 {
			return errors.New("ip或ip段的数量综合超过了5000个最大范围，请删除部分ip或者ip段")
		}
		ipRange, _ := utils.CIDRToIPRange(v)
		ipList = append(ipList, ipRange...)
	}
	ipList = utils.ListDistinct(ipList, true)
	if len(ipList) > 5000 {
		return errors.New("ip或ip段的数量综合超过了5000个最大范围，请删除部分ip或者ip段")
	}
	return nil
}

type holdingsUnit struct {
	From    string
	Name    string
	Percent string
}

// 获取控股公司
func getChildByName(ctx context.Context, percent uint32, names ...string) ([]*holdingsUnit, error) {
	if len(names) == 0 {
		return nil, nil
	}

	var ch = make(chan holdingsUnit)
	go func() {
		wg := sync.WaitGroup{}
		// 是否本地化
		for _, v := range names {
			wg.Add(1)
			go func(companyName string) {
				defer wg.Done()
				var err error
				var rsp = new(pb.QCCInvestmentThroughResponse)
				var req = &pb.QCCInvestmentThroughRequest{Search: companyName}
				if cfg.IsLocalClient() {
					err = localization.NewCaller().CoreQCCInvestmentThrough(req, rsp)
				} else {
					// err = tianyancha.InvestTree(ctx, req, rsp)
					rsp, err = pb.GetProtoCoreClient().QCCInvestmentThrough(ctx, req)
				}
				log.Infof("[单位资产测绘] QCC company_name: %s, holdings list: %s", companyName, utils.AnyToStr(rsp))
				if err != nil {
					log.Warnf("[单位资产测绘] Get company: %s holdings failed: %v", companyName, err)
					return
				}
				if cfg.IsLocalClient() {
					for _, child := range rsp.List {
						ch <- holdingsUnit{From: companyName, Name: child.Name, Percent: child.Percent}
					}
				} else {
					for _, children := range rsp.List {
						for _, child := range children.ChildrenList {
							ch <- holdingsUnit{From: companyName, Name: child.Name, Percent: child.Percent}
						}
					}
				}
			}(v)
		}
		wg.Wait()
		close(ch)
	}()

	var list = make([]*holdingsUnit, 0, 5*len(names))
	for x := range ch {
		if !percentCompare(percent, x.Percent) {
			continue
		}
		list = append(list, &holdingsUnit{From: x.From, Name: x.Name, Percent: x.Percent})
	}
	return list, nil
}

func percentCompare(percent uint32, holdingPercent string) bool {
	index := strings.Index(holdingPercent, "%")
	if percent == 0 || holdingPercent == "" || index <= 0 {
		return true
	}
	return cast.ToFloat32(holdingPercent[:index]) >= cast.ToFloat32(percent)
}

func holdingNames(holdingList []*holdingsUnit, companyFlag string, parents ...string) []*web.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit {
	var l = make([]*web.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit, 0, len(parents))
	for _, v := range parents {
		var item = &web.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit{Name: v, CompanyFlag: companyFlag}
		for _, x := range holdingList {
			if v == x.From {
				item.Children = append(item.Children,
					&web.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit{Name: x.Name, Percent: x.Percent})
			}
		}
		sortByPercent(item)
		l = append(l, item)
	}
	return l
}

func clueUpsert(clueItem *clues.Clue) error {
	h := []mysql.HandleFunc{
		mysql.WithSelect("id"),
		mysql.WithColumnValue("user_id", clueItem.UserId),
		mysql.WithColumnValue("group_id", clueItem.GroupId),
		mysql.WithColumnValue("type", clueItem.Type),
		mysql.WithColumnValue("is_deleted", clues.DeletedFalse),
	}
	if clueItem.Type == clues.TYPE_LOGO {
		h = append(h, mysql.WithColumnValue("hash", clueItem.Hash))
	} else {
		h = append(h, mysql.WithColumnValue("content", clueItem.Content))
	}
	info, err := clues.NewCluer().First(h...)
	switch {
	case errors.Is(err, gorm.ErrRecordNotFound): // 记录不存在
		return clues.NewCluer().Create(clueItem)
	case err != nil:
		return err
	default: // 记录存在
		if info.Status != clues.AuditPassed {
			m := map[string]any{"status": clues.AuditPassed}
			return clues.NewCluer().UpdateAny(m, mysql.WithId(info.Id))
		}
	}
	return nil
}

func strToSlice[T any](kw string) []T {
	if kw == "" {
		return nil
	}
	n := strings.Count(kw, ",")
	l := make([]T, 0, n)
	_ = json.Unmarshal([]byte(kw), &l)
	return l
}

func sortByPercent(item *web.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit) {
	var l = item.Children
	sort.Slice(l, func(i, j int) bool {
		idx1 := strings.IndexByte(l[i].Percent, '%')
		idx2 := strings.IndexByte(l[j].Percent, '%')
		return cast.ToFloat32(l[i].Percent[:idx1]) > cast.ToFloat32(l[j].Percent[:idx2])
	})
	item.Children = l
}
