package detect_asset_task

import (
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/general_clues"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

func CluesUpdate(req *pb.DetectAssetCluesUpdateRequest) error {
	handlers := make([]mysql.HandleFunc, 0)
	handlers = append(handlers,
		mysql.WithSelect("content", "status"),
		mysql.WithColumnValue("`user_id`", req.UserId),
		mysql.WithColumnValue("`is_deleted`", clues.DeletedFalse))
	if len(req.Ids) > 0 {
		handlers = append(handlers, mysql.WithValuesIn("`id`", utils.ListDistinct(req.Ids)))
	}

	var cluer = clues.NewCluer()
	userClues, err := cluer.ListAll(handlers...)
	if err != nil || len(userClues) == 0 {
		return err
	}

	contentList := make([]string, 0, len(userClues))
	for i := range userClues {
		contentList = append(contentList, userClues[i].Content)
	}
	contentList = utils.ListDistinct(contentList)
	allClues, err := cluer.ListAll(
		mysql.WithColumnValue("`user_id`", req.UserId),
		mysql.WithValuesIn("`content`", contentList),
		mysql.WithColumnValue("`is_deleted`", clues.DeletedFalse))
	if err != nil || len(allClues) == 0 {
		return err
	}

	for i := range allClues {
		allClues[i].ClueCompanyName = req.GetClueCompanyName()
	}
	err = cluer.Save(allClues)
	if err != nil {
		return err
	}

	if !req.Sync {
		return nil
	}

	// 线索总表里面已确认的线索，需全部存储到线索总库，并打上已确认标签
	confirmedClues := make([]*clues.Clue, 0, len(userClues))
	for _, v := range userClues {
		if v.Status == clues.AuditPassed {
			confirmedClues = append(confirmedClues, v)
		}
	}

	err = syncGeneralClues(confirmedClues, req.ClueCompanyName)
	return err
}

func syncGeneralClues(confirmedClues []*clues.Clue, companyName string) error {
	if len(confirmedClues) == 0 {
		return nil
	}

	var content = make([]string, 0, len(confirmedClues))
	for _, v := range confirmedClues {
		content = append(content, v.Content)
	}
	var db = general_clues.NewGeneralCluesModel()
	clueList, _, err := db.List(0, 0, mysql.WithValuesIn("`content`", content))
	if err != nil {
		return err
	}

	updateList := make([]general_clues.GeneralClues, 0, len(clueList))
	for i := range clueList {
		for _, v := range confirmedClues {
			if clueList[i].Content == v.Content {
				clueList[i].CompanyName = companyName
				clueList[i].Confirmed = general_clues.Confirmed
				updateList = append(updateList, clueList[i])
				break
			}
		}
	}
	insertList := make([]*general_clues.GeneralClues, 0, len(confirmedClues))
	for _, v := range confirmedClues {
		exist := false
		for i := range clueList {
			if clueList[i].Content == v.Content {
				exist = true
			}
		}
		if !exist {
			insertList = append(insertList, &general_clues.GeneralClues{
				Type:        uint8(utils.If(v.Type == clues.TYPE_DOMAIN, general_clues.ClueTypeSubdomain, v.Type)),
				Content:     v.Content,
				CompanyName: companyName,
				Confirmed:   general_clues.Confirmed,
				Platform:    general_clues.PlatformInput,
				Source:      "", // ?
				Hash:        int64(v.Hash),
				CertValid:   0,
			})
		}
	}

	err = db.Save(updateList)
	if err != nil {
		return err
	}
	err = db.CreateInBatches(insertList)
	return err
}
