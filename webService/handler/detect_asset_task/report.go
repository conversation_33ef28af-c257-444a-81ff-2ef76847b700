package detect_asset_task

import (
	"encoding/json"
	"errors"
	"fmt"
	"path"
	"sort"

	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	dat "micro-service/middleware/mysql/detect_assets_tasks"
	dtr "micro-service/middleware/mysql/detect_task_report_result"
	"micro-service/pkg/excel"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

func ImportAssetDownload(req *pb.DetectAssetTaskReportRequest, rsp *pb.AssetAuditResultDownloadResponse) error {
	// 检查是否可展示任务简报
	if errShow := isShowReport(req.UserId, req.TaskId); errShow != nil {
		return errShow
	}

	filename := fmt.Sprintf("detect_asset_task-%d_userId_%d_import_sure_ips.xlsx", req.TaskId, req.UserId)
	basePath := path.Join(storage.GetPublicStoragePath(), filename)
	writePath := path.Join(storage.GetRootPath(), basePath)
	if !utils.FileIsExist(writePath) {
		info, err := taskInfo(req.UserId, req.TaskId, "import_sure_ips")
		if err != nil {
			return err
		}
		ips := utils.ListDistinctNonZero(strToSlice[string](info.ImportSureIps))
		var data = make([][]string, len(ips)+1)
		data[0] = []string{"IP"}
		for i := range ips {
			data[i+1] = []string{ips[i]}
		}

		// write Excel file
		err = excel.WriteExcel(writePath, data)
		if err != nil {
			return err
		}
	}

	// generate download url
	de, _ := utils.DownloadFileEncrypt(basePath, "已知导入资产.xlsx", "", false)
	rsp.FilePath = path.Join(storage.GetDownloadPrefix(), de+".xlsx")
	return nil
}

func ReportInfo(req *pb.DetectAssetTaskReportRequest, rsp *pb.DetectAssetTaskReportResponse) error {
	// 检查是否可展示任务简报
	if errShow := isShowReport(req.UserId, req.TaskId); errShow != nil {
		return errShow
	}
	// 任务信息
	taskItem, err := taskInfo(req.UserId, req.TaskId)
	if err != nil {
		return err
	}

	// 任务简报信息
	info, err := reportInfo(req.UserId, req.TaskId)
	if err != nil {
		return err
	}

	// 测绘结果汇总
	rsp.ResultStat = &pb.DetectAssetTaskReportResponseReport{
		Company:         strToSlice[string](taskItem.ConfirmCompanyList), // 测绘企业
		AssetIp:         info.AllIPNum,                                   // IP资产
		AssetDomain:     info.DomainNum,                                  // 域名资产
		AssetCert:       info.CertNum,                                    // 证书资产
		AssetLogin:      info.LoginPageNum,                               // 登录入口资产
		AssetShadow:     info.ShadowAssetsNum,                            // 影子资产
		Digital:         info.DataAssetsNum,                              // 数字资产
		DigitalWechat:   info.WechatNum,                                  // 数字资产-公众号
		DigitalApp:      info.AppNum,                                     // 数字资产-app
		RiskEvent:       info.RiskEventNum,                               // 风险事件
		DataLeak:        info.DataLeakNum,                                // 数据泄漏
		DataLeakNetDisk: info.NetDiskNum,                                 // 数据泄漏-网盘
		DataLeakLibrary: info.LibraryNum,                                 // 数据泄露-文库
		DataLeakCode:    info.CodeNum,                                    // 数据泄漏-代码仓库
	}

	// 任务参数
	rsp.DetectTaskStat = &pb.DetectAssetTaskReportResponseDetectTaskParam{
		DetectCompanyList: detectCompanyList(info.CompanyJSON),            // 测绘企业清单
		InputClues:        clueTypeConvert(clueParse(info.InputClueJSON)), // 已知线索
		ScanType:          itoa(taskItem.ScanType),                        // 扫描端口
		DetectMode:        int32(getDetectMode(taskItem)),                 // 测绘模式
		FofaRange:         itoa(taskItem.FofaRange),                       // 资产梳理范围
		OffAssetsTo:       int32(taskItem.OffAssetsTo),                    // 离线资产去向
		Bandwidth:         taskItem.Bandwidth,                             // 扫描带宽
		IsImportSureIps:   len(taskItem.ImportSureIps) > 0,                // 是否导入已知资产
		Percent:           taskItem.Percent + "%",                         // 其他测绘企业控股比例
	}

	// 线索获取详情
	rsp.ClueStat = &pb.DetectAssetTaskReportResponseClueStatType{
		Input:  clueNumByType(info.InputClueJSON),      // 已知线索
		Icp:    clueNumByType(info.ICPClueJSON),        // 初始线索
		Expend: clueNumByType(info.ExpendClueJSON),     // 扩展线索
		All:    clueNumByType(info.ConfirmAllClueJSON), // 线索总表
	}

	// 资产发现
	rsp.AssetDiscoveryStat = &pb.DetectAssetTaskReportResponseAssetDiscovery{
		IpDiscovery:     info.RecommendIPNum,       // 自动发现
		IpImport:        info.RecommendInputIPNum,  // 第三方导入
		RecommendHigh:   info.RecommendIPHighNum,   // 高可信度资产
		RecommendMiddle: info.RecommendIPMiddleNum, // 中可信度资产
		RecommendLow:    info.RecommendIPLowNum,    // 低可信度资产
		AssetTable:      info.TableIPNum,           // 台账
		AssetUnsure:     info.UnsureIPNum,          // 疑似
		AssetThreaten:   info.ThreatenIPNum,        // 威胁
		UrlApiNum:       info.UrlApiNum,            // URL-API数量
	}

	// 关联任务
	// 1-数字资产
	rsp.TaskStat = append(rsp.TaskStat, &pb.DetectAssetTaskReportResponseDigitalAsset{
		Type:    1,
		IsShow:  int32(taskItem.IsAutoDataAsset),
		Wechat:  info.WechatNum, // 公众号
		App:     info.AppNum,    // app
		Keyword: strToSlice[string](info.DataAssetsKeyword)})
	// 2-数据泄露
	rsp.TaskStat = append(rsp.TaskStat, &pb.DetectAssetTaskReportResponseDigitalAsset{
		Type:    2,
		IsShow:  int32(taskItem.IsAutoLeakAsset),
		NetDisk: info.NetDiskNum, // 网盘
		Library: info.LibraryNum, // 文库
		Code:    info.CodeNum,    // 代码
		Keyword: strToSlice[string](info.DataLeakKeyword)})
	// 3-域名枚举
	burstDomain := strToSlice[string](info.BurstDomainKeyword)
	rsp.TaskStat = append(rsp.TaskStat, &pb.DetectAssetTaskReportResponseDigitalAsset{
		Type:    3,
		IsShow:  int32(taskItem.IsAutoDomainBurst),
		Domain:  info.BurstDomainResultNum, // 爆破结果数量
		Keyword: burstDomain})
	// 4-风险事件
	rsp.TaskStat = append(rsp.TaskStat, &pb.DetectAssetTaskReportResponseDigitalAsset{
		Type:        4,
		IsShow:      int32(taskItem.IsCheckRisk),
		RiskEvent:   info.RiskEventNum,
		MatchIp:     strToSlice[string](info.MatchIPShortData),
		MatchIpFull: strToSlice[string](info.MatchIPData),
	})
	// 5-URL-API统计
	rsp.TaskStat = append(rsp.TaskStat, &pb.DetectAssetTaskReportResponseDigitalAsset{
		Type:      5,
		IsShow:    int32(taskItem.IsAutoURLApi),
		UrlApiNum: info.UrlApiNum,
		Keyword:   strToSlice[string](info.UrlApiKeyword),
	})
	return nil
}

func reportInfo(userId, taskId uint64, columns ...string) (dtr.Result, error) {
	info, err := dtr.NewCall().First(
		mysql.WithSelect(columns...),
		mysql.WithColumnValue("detect_task_id", taskId),
		mysql.WithColumnValue("user_id", userId),
	)
	return info, err
}

func taskInfo(userId, taskId uint64, columns ...string) (*dat.DetectAssetsTask, error) {
	info, err := dat.NewModel().First(
		mysql.WithColumnValue("id", taskId),
		mysql.WithColumnValue("user_id", userId),
		mysql.WithSelect(columns...))
	if err != nil {
		return nil, err
	}
	return info, nil
}

func isShowReport(userId, taskId uint64) error {
	info, err := taskInfo(userId, taskId, "is_show")
	if err != nil {
		return err
	}
	if info.IsShow != dat.True {
		return errors.New("查询记录不存在")
	}
	return nil
}

type clueDef struct {
	Content string `json:"content"`
	Type    int64  `json:"type"`
}

func clueParse(str string) []*clueDef {
	var l []*clueDef
	_ = json.Unmarshal(utils.String2Bytes(str), &l)
	return l
}

func clueTypeConvert(l []*clueDef) []*pb.DetectAssetTaskReportClueUnit {
	var list = make([]*pb.DetectAssetTaskReportClueUnit, len(l))
	for i := range l {
		if l[i].Type == clues.TYPE_LOGO {
			f, e := utils.GetFileName(l[i].Content)
			x, _ := utils.DownloadFileEncrypt(l[i].Content, f+e, "", false)
			l[i].Content = path.Join(storage.GetDownloadPrefix(), x+e)
		}
		list[i] = &pb.DetectAssetTaskReportClueUnit{
			Content: l[i].Content,
			Type:    itoa(int(l[i].Type)),
		}
	}
	return list
}

func clueNumByType(str string) *pb.DetectAssetTaskReportResponseClueTypeNum {
	list := clueParse(str)
	var ip, domain, cert, icp, icon, keyword int
	for _, v := range list {
		switch v.Type {
		case clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN:
			domain++
		case clues.TYPE_CERT:
			cert++
		case clues.TYPE_ICP:
			icp++
		case clues.TYPE_LOGO:
			icon++
		// case clues.ClueTypeKeyword:
		// 	keyword++
		case clues.TYPE_IP:
			ip++
		}
	}
	var item = &pb.DetectAssetTaskReportResponseClueTypeNum{}
	item.Ip = itoa(ip)
	item.Domain = itoa(domain)
	item.Cert = itoa(cert)
	item.Icp = itoa(icp)
	item.Icon = itoa(icon)
	// item.Keyword = itoa(keyword)
	item.Sum = itoa(ip + domain + cert + icp + icon + keyword)
	return item
}

func detectCompanyList(s string) []*pb.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit {
	if s == "" {
		return nil
	}

	var l []*pb.DetectAssetTaskReportResponseDetectTaskParamCompanyUnit
	_ = json.Unmarshal([]byte(s), &l)
	return l
}

func ClueList(req *pb.DetectAssetTaskReportClueListRequest, rsp *pb.DetectAssetTaskReportClueListResponse) error {
	// 检查是否可展示任务简报
	if errShow := isShowReport(req.UserId, req.TaskId); errShow != nil {
		return errShow
	}

	clueStepMap := map[int32]string{
		1: "input_clue_json",       // 已知线索
		2: "icp_clue_json",         // 初始线索
		3: "expend_clue_json",      // 扩展线索
		4: "confirm_all_clue_json", // 线索总表
	}
	stepColumn, ok := clueStepMap[req.ClueStep]
	if !ok {
		return errors.New("未知步骤线索")
	}

	info, err := reportInfo(req.UserId, req.TaskId, stepColumn)
	if err != nil {
		return err
	}

	clueContent := func(clue dtr.Result, t int32) string {
		switch t {
		case 1:
			return clue.InputClueJSON
		case 2:
			return clue.ICPClueJSON
		case 3:
			return clue.ExpendClueJSON
		case 4:
			return clue.ConfirmAllClueJSON
		}
		return ""
	}(info, req.ClueStep)

	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	if clueContent == "" {
		return nil
	}

	clueList := clueParse(clueContent)
	clueList, _ = utils.ListFunc(clueList, func(x *clueDef) (*clueDef, bool) { return x, x.Type != clues.TYPE_KEYWORD })
	rsp.Total = int64(len(clueList))
	rsp.Items = clueByPageOrder(clueList, req.Page, req.PerPage)
	return nil
}

func clueByPageOrder(l []*clueDef, page, size int32) []*pb.DetectAssetTaskReportClueUnit {
	sort.SliceStable(l, func(i, j int) bool { return l[i].Type < l[j].Type })
	clueOrder := utils.PageBy(l, int(page), int(size))
	return clueTypeConvert(clueOrder)
}

func getDetectMode(item *dat.DetectAssetsTask) int {
	if item.IsIntellectMode == dat.True && item.DetectType == dat.DetectTypeSpeed {
		return dat.ModeSmart
	}
	if item.DetectType == dat.DetectTypeDepth {
		return dat.ModeExpert
	}
	return dat.ModeDefault
}
