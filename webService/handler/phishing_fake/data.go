package phishing_fake

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"path"
	"sort"
	"strconv"
	"time"

	rr "micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	phc "micro-service/middleware/mysql/phishing_fake_task"
	"micro-service/middleware/redis"
	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
)

// 等待上传文件同步, 默认1分钟
func syncFileWaiting(filePath string) {
	for i := 0; i < 240; i++ {
		if b := utils.FileIsExist(filePath); b {
			return
		}
		time.Sleep(250 * time.Millisecond)
	}
}

func isEmptyLine(line []string, maxColumn int) bool {
	for i := range line {
		if i >= maxColumn {
			break
		}
		if line[i] != "" {
			return false
		}
	}
	return true
}

func checkFakeSource(s string) (int, bool) {
	switch s {
	case "钓鱼仿冒":
		return rr.TypeFakeTaskFake, true
	case "域名混淆":
		return rr.TypeFakeTaskDomain, true
		// case "其他":
		// 	return rr.TypeFakeTaskOther, true
	}
	return 0, false
}

func checkFileSize(filePath string) error {
	const maxSize = 20 // Mb
	size, _ := utils.FileSize(filePath, 'M', 2)
	if size > maxSize {
		return fmt.Errorf("上传文件大小超出最大%dMb限制", maxSize)
	}
	return nil
}

func uploadFileHandling(filePath string) ([]*rr.RecommendResult, error) {
	log.Infof("[钓鱼仿冒] 等待同步上传文件: %s", filePath)
	// 等待同步
	syncFileWaiting(filePath)
	if !utils.FileIsExist(filePath) {
		return nil, errors.New("上传文件不存在")
	}

	// 检查文件大小是否超出限制, 最大20M
	if err := checkFileSize(filePath); err != nil {
		return nil, err
	}
	return parseFile(filePath)
}

var importExcelTitle = []string{
	"fake_source", "ip", "fake_company", "port", "protocol", "subdomain",
	"fake_icp_company", "url", "title", "source_updated_at",
}

// 处理上传文件
func parseFile(filePath string) ([]*rr.RecommendResult, error) {
	log.Infof("[钓鱼仿冒] 开始解析上传文件: %s", filePath)
	// 读取Excel文件
	data, err := excel.ReadExcel(filePath)
	if err != nil {
		return nil, err
	}

	titleLen := len(importExcelTitle)
	list := make([]*rr.RecommendResult, 0, len(data))
	for i := range data {
		if i == 0 || isEmptyLine(data[i], titleLen) {
			continue
		}

		m := make(map[string]string, titleLen)
		for j, v2 := range data[i] {
			if j >= titleLen {
				break
			}
			m[importExcelTitle[j]] = v2
		}

		ft, typeValid := checkFakeSource(m["fake_source"])
		if !typeValid {
			return nil, fmt.Errorf("上传文件第%d行数据仿冒类型错误, 应为钓鱼仿冒、域名混淆2种类型之一", i+1)
		}

		bs, _ := json.Marshal(m)
		var item = rr.RecommendResult{}
		if err := json.Unmarshal(bs, &item); err != nil {
			log.Info(err)
			continue
		}
		item.FakeType = ft // 仿冒类型
		item.IsFakeAssets = ft == rr.TypeFakeTaskFake

		if !utils.IsIP(item.Ip) {
			return nil, fmt.Errorf("上传文件第%d行数据, ip列%s不合法", i+1, item.Ip)
		}
		item.IsIPv6 = utils.IsIPv6(item.Ip)
		item.Ip = utils.IPExpanded(item.Ip)
		if !utils.IsValidatePort(cast.ToString(item.Port)) {
			return nil, fmt.Errorf("上传文件第%d行数据, 端口列%s不合法", i+1, item.Port)
		}
		item.Port = cast.ToInt(item.Port)
		if item.Protocol == "" {
			return nil, fmt.Errorf("上传文件第%d行数据, 协议列不能为空", i+1)
		}
		if m["source_updated_at"] != "" {
			if _, err := time.Parse(utils.DateTimeLayout, m["source_updated_at"]); err != nil {
				return nil, fmt.Errorf("上传文件第%d行数据, 发现时间列格式错误", i+1)
			}
			// item.SourceUpdatedAt = m["source_updated_at"]
		} else {
			item.SourceUpdatedAt = time.Now().Format(utils.DateTimeLayout)
		}
		item.ThreatenType = cast.ToInt(getThreatenType(ft))
		// item.Subdomain = utils.DomainFromUrl(item.Subdomain)
		item.Domain = utils.GetTopDomain(item.Subdomain)
		item.AssetsFrom = rr.AssetFromImport // 第三方导入
		item.Status = 1
		item.Level = 4

		list = append(list, &item)
	}
	log.Infof("[钓鱼仿冒] 解析上传文件完成, 共%d条数据", len(list))
	return list, nil
}

func compareImportAssets(upload, origin []*rr.RecommendResult) []*rr.RecommendResult {
	genId := func(v *rr.RecommendResult) string {
		return fmt.Sprintf("%s_%s_%s_%s_%d", v.Ip, cast.ToString(v.Port), v.Protocol, v.Subdomain, v.FakeType)
	}

	originMap := make(map[string]struct{}, len(origin))
	for i := range origin {
		originMap[genId(origin[i])] = struct{}{}
	}

	var insert = make([]*rr.RecommendResult, 0, len(upload))
	for i := range upload {
		id := genId(upload[i])
		if _, ok := originMap[id]; !ok {
			insert = append(insert, upload[i])
		}
	}
	return insert
}

func resultDownload(task *phc.Task) (storagePath string, err error) {
	// 获取仿冒数据资产
	req := &pb.CounterfeitAssetListRequest{AssetType: rr.TypeFakeTaskFake}
	fakeList, err := getRecommendResult(task, req)
	if err != nil {
		return "", err
	}

	// 资产排序
	sort.SliceStable(fakeList, func(i, j int) bool { return fakeList[i].OnlineState })
	storagePath, err = resultWriteExcel(task.ID, fakeList)

	return storagePath, err
}

func resultWriteExcel(taskId uint64, list []*rr.RecommendResult) (string, error) {
	fakeCount := 0
	for _, v := range list {
		switch v.FakeType {
		case rr.TypeFakeTaskFake:
			fakeCount++
		}
	}

	title := []string{"IP", "仿冒目标", "端口", "协议", "子域名", "域名备案企业", "URL", "网站标题", "发现时间", "存活状态"}
	// 钓鱼仿冒sheet
	fakeSheet := &excel.WriteByOrder{SheetName: "钓鱼仿冒数据", Data: make([][]string, 0, fakeCount)}
	fakeSheet.Data = append(fakeSheet.Data, title)
	offlineCount, onlineCount := 0, 0
	for _, v := range list {
		switch v.FakeType {
		case rr.TypeFakeTaskFake:
			if v.OnlineState {
				onlineCount++
			} else {
				offlineCount++
			}
			fakeSheet.Data = append(fakeSheet.Data, convertToLine(v))
		}
	}
	_ = phc.NewTasker().UpdateAny(taskId, map[string]any{"phishing_online": onlineCount, "phishing_offline": offlineCount})
	rd, ts := utils.RandString(20), time.Now().Format(utils.DateTimeIntegerLayout)
	fileName := fmt.Sprintf("phishing-fake-task_%d_%s_%s.xlsx", taskId, rd, ts) // Excel文件名
	storagePath := path.Join(storage.GetPublicStoragePath(), fileName)
	driverPath := path.Join(storage.GetRootPath(), storagePath) // 服务器存储实际路径
	if utils.FileIsExist(driverPath) {
		_ = os.Remove(driverPath)
	}
	err := excel.WriteSheetsByOrder(driverPath, fakeSheet)
	return storagePath, err
}

func convertToLine(asset *rr.RecommendResult) []string {
	var line = make([]string, 0, 11)
	line = append(line, asset.Ip, asset.FakeCompany, cast.ToString(asset.Port))
	line = append(line, asset.Protocol, asset.Subdomain, asset.FakeIcpCompany, asset.Url)
	line = append(line, asset.Title, cast.ToString(asset.SourceUpdatedAt))
	var state string
	if asset.OnlineState == true {
		state = "在线"
	} else {
		state = "离线"
	}
	line = append(line, state)
	return line
}

func asyncResultWriteExcel(task *phc.Task) {
	storagePath, err := resultDownload(task)
	if err != nil {
		log.Errorf("[钓鱼仿冒] 异步生成下载Excel文件失败, call resultDownload: %v", err)
		return
	}
	err = phc.NewTasker().UpdateAny(task.ID, map[string]any{"download_path": storagePath})
	if err != nil {
		log.Errorf("[钓鱼仿冒] 异步更新任务下载路径失败: %v", err)
	}
}

func getClues(task *phc.Task) ([]*clues.Clue, error) {
	list, err := clues.NewCluer().ListAll(
		mysql.WithColumnValue("user_id", task.UserId),
		mysql.WithColumnValue("group_id", task.GroupId),
		mysql.WithValuesIn("type", []int{clues.TYPE_DOMAIN, clues.TYPE_LOGO, clues.TYPE_KEYWORD, clues.TYPE_FID, clues.TYPE_SUBDOMAIN}),
		mysql.WithColumnValue("status", clues.AuditPassed),
		mysql.WithColumnValue("is_deleted", clues.DeletedFalse),
	)

	return list, err
}

func getRecommendResult(task *phc.Task, req *pb.CounterfeitAssetListRequest) ([]*rr.RecommendResult, error) {
	param := rr.NewFindCondition()
	param.UserId = int(task.UserId)
	param.Ids = req.Ids
	param.Keyword = req.Keyword
	param.FakeTaskId = int(task.ID)
	param.FakeType = int(req.AssetType)
	param.GroupId = int(task.GroupId)
	param.Flag = task.ExpendFlags
	param.IpFuzzy = req.Ip
	param.Domains = utils.ListDistinct(req.Domain)
	param.Subdomains = utils.ListDistinct(req.Subdomain)
	param.FakeCompany = utils.ListDistinct(req.Name)
	param.Ports = utils.ListDistinct(atoi(req.Port))
	param.Protocol = utils.ListDistinct(req.Protocol)
	param.FakeIcpCompany = utils.ListDistinct(req.IcpCompany)
	param.Icon = utils.ListDistinct(ixToi(req.Logo))
	param.Title = req.Title
	param.UrlFuzzy = req.Url
	if req.OnlineState != "" {
		param.OnlineState = utils.If(req.OnlineState == "true", rr.Online, param.OnlineState) //nolint:goconst,gocritic
	}
	if req.ChainType != "" {
		param.ReasonType = cast.ToInt(req.ChainType)
	}
	if len(req.CreatedAt) == 2 {
		param.SourceUpdatedAt = [2]string{req.CreatedAt[0], req.CreatedAt[1]}
	}
	if len(req.UpdatedAt) == 2 {
		param.UpdatedAt = [2]string{req.UpdatedAt[0], req.UpdatedAt[1]}
	}
	list, err := rr.NewRecommendResultModel().FindByCondition(param)
	return list, err
}

// 去重
func distinctRecommendAssets(list []*rr.RecommendResult) []*rr.RecommendResult {
	if len(list) == 0 {
		return nil
	}

	id := func(v *rr.RecommendResult) string {
		return fmt.Sprintf("%s_%s_%s_%s_%d", v.Ip, cast.ToString(v.Port), v.Protocol, v.Subdomain, v.FakeType)
	}
	m := make(map[string]*rr.RecommendResult, len(list))
	for i := range list {
		t, _ := utils.ParseTime(cast.ToString(list[i].SourceUpdatedAt))
		x, ok := m[id(list[i])]
		if !ok {
			m[id(list[i])] = list[i]
		} else if t2, _ := utils.ParseTime(cast.ToString(x.SourceUpdatedAt)); t2.Before(t) {
			m[id(list[i])] = list[i]
		}
	}
	distinct := make([]*rr.RecommendResult, 0, len(m))
	for _, v := range m {
		distinct = append(distinct, v)
	}
	return distinct
}

func cacheKey(taskId uint64) string {
	return "foradar_cache:phishing_fake:url_code:" + strconv.Itoa(int(taskId))
}

// url地址探测的状态码缓存
func setUrlCodeCache(ctx context.Context, taskId uint64, m map[string]int) {
	urlCode := make(map[string]int, len(m)+1)
	urlCode["http://127.0.0.1"] = http.StatusInternalServerError
	for k, code := range m {
		if code > 0 {
			urlCode[k] = code
		}
	}

	bs, _ := json.Marshal(urlCode)
	err := redis.Set(ctx, cacheKey(taskId), string(bs), 2*utils.Day)
	if err != nil {
		log.WithContextErrorf(ctx, "[钓鱼仿冒]异步存储url status code缓存失败: %v", err)
	}
}

func getUrlCodeCache(taskId uint64) (map[string]int, bool) {
	var m = make(map[string]int)
	err := redis.Get(context.TODO(), cacheKey(taskId), &m)
	if err != nil {
		return m, false
	}
	return m, true
}

func ipPortKey(taskId uint64) string {
	return "foradar_cache:phishing_fake:ip_port:" + strconv.Itoa(int(taskId))
}

func getIpPortCache(taskId uint64) (map[string]bool, bool) {
	var m = make(map[string]bool)
	err := redis.Get(context.TODO(), ipPortKey(taskId), &m)
	if err != nil {
		return nil, false
	}
	return m, true
}

func deleteTaskCache(ctx context.Context, taskId uint64) {
	urlKey, portKey := cacheKey(taskId), ipPortKey(taskId)
	err := redis.DelKey(ctx, urlKey, portKey)
	if err != nil {
		log.WithContextErrorf(ctx, "[钓鱼仿冒]删除仿冒任务时同步删除缓存失败: %v, cache keys: %s, %s", err, urlKey, portKey)
	}
}
