package phishing_fake

import (
	"context"
	"strconv"
	"strings"
	"time"

	ffa "micro-service/middleware/elastic/fofaee_assets"
	fra "micro-service/middleware/elastic/foradar_assets"
	rr "micro-service/middleware/elastic/recommend_result"
	phc "micro-service/middleware/mysql/phishing_fake_task"
	"micro-service/pkg/detect"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
)

// 仿冒数据入账威胁资产的业务逻辑:
// 1-如果仿冒数据IP不存在于资产库，则仿冒数据的该IP和IP下的所有端口资产全部入账威胁资产；
// 2-如果仿冒数据的IP存在于资产库中，但资产类型为台账，则仿冒数据不同步，若端口不存在，则追加该端口数据，并修改资产类型为威胁资产。
func syncFakeAssets(ctx context.Context, task *phc.Task, req *pb.CounterfeitAssetListRequest, isWriteFidClue bool) ([]string, error) {
	list, err := getSyncAssets(ctx, task, req)
	if err != nil || len(list) == 0 {
		log.WithContextInfof(ctx, "[钓鱼仿冒]获取推荐资产的数据为空或者报错了, taskId:%d", task.ID)
		return nil, err
	}

	var urlCode map[string]int
	// 获取URL的状态码缓存，否则直接探测
	if m, b := getUrlCodeCache(task.ID); b {
		urlCode = m
	} else {
		log.WithContextWarnf(ctx, "[钓鱼仿冒]未获取到URL的状态码缓存, 将同步进行URL同步探测, taskId:%d", task.ID)
		urlCode = urlDetect(ctx, list, task.ID, false, 1)
	}

	// sync assets to foradar_assets: threat asset
	ipPorts, err := foradarAssetSync(task, list, urlCode)
	if err != nil {
		log.WithContextError(ctx, "[钓鱼仿冒]获取foradar资产报错了, taskId:%d", task.ID)
		return nil, err
	}
	log.WithContextInfof(ctx, "[钓鱼仿冒]完成foradarassets索引同步, taskId:%d", task.ID)
	// sync assets to fofaee_assets: threat asset
	accountIps, err := fofaeeAssetSync(req, task, list, urlCode, isWriteFidClue)
	if err != nil {
		log.WithContextError(ctx, "[钓鱼仿冒]获取fofaeeAssets资产报错了, taskId:%d", task.ID)
		return nil, err
	}
	log.WithContextInfof(ctx, "[钓鱼仿冒]完成fofaeeAssets索引同步, taskId:%d", task.ID)
	// 异步探测ip port
	if len(ipPorts) > 0 {
		go asyncDetectIpPortAndUpdate(context.TODO(), task.UserId, task.ID, ipPorts)
	}

	return accountIps, nil
}

func getSyncAssets(_ context.Context, task *phc.Task, req *pb.CounterfeitAssetListRequest) ([]*rr.RecommendResult, error) {
	// 同步用户勾选中的资产
	if len(req.Ids) > 0 {
		req = &pb.CounterfeitAssetListRequest{Ids: req.Ids, AssetType: req.AssetType}
	}
	// 仅同步可访问资产
	b, _ := strconv.ParseBool(req.OnlineState)
	if b {
		req = &pb.CounterfeitAssetListRequest{AssetType: req.AssetType, OnlineState: "true"}
	}
	list, err := getRecommendResult(task, req)
	return list, err
}

// ip资产维度
func fofaeeAssetSync(
	req *pb.CounterfeitAssetListRequest,
	task *phc.Task,
	list []*rr.RecommendResult,
	statusCode map[string]int,
	isWriteFidClue bool,
) ([]string, error) {
	var (
		ctx        = context.Background()
		client     = ffa.NewFofaeeAssetsModel()
		accountIps = make([]string, 0, len(list)/10)
	)

	// 获取用户资产
	feAssets, err := client.FindByIpAndPort(int(task.UserId), nil)
	if err != nil {
		return nil, err
	}
	feAssetsMap := make(map[string]*ffa.FofaeeAssets, len(feAssets))
	for i := range feAssets {
		feAssetsMap[feAssets[i].Ip] = &feAssets[i]
	}

	updateIps := make(map[string]struct{}, len(feAssets))
	insertMap := make(map[string]*ffa.FofaeeAssets, len(list)/5)
	if isWriteFidClue {
		log.Infof("[钓鱼仿冒] 同步资产 开始写入FID线索, taskId:%d", task.ID)
		// 获取资产FID,&来源线索
		fids := make(map[string]int, 0)
		for x := range list {
			// 检查 Fid 是否为空
			if list[x].Fid == "" {
				log.Warnf("[钓鱼仿冒] 同步资产时发现空FID, 跳过该记录, taskId:%d", task.ID)
				continue
			}

			// 检查 Reason 是否为空
			if len(list[x].Reason) == 0 {
				log.Warnf("[钓鱼仿冒] 同步资产时发现空Reason, 跳过该记录, taskId:%d FID:%s", task.ID, list[x].Fid)
				continue
			}

			// 写入唯一 Fid 和 Reason[0].Id
			if _, ok := fids[list[x].Fid]; !ok {
				fids[list[x].Fid] = list[x].Reason[0].Id
				log.Infof("[钓鱼仿冒] 同步资产 准备写入FID线索, taskId:%d FID:%s", task.ID, list[x].Fid)
			}
		}
		// 写入FID线索
		_, _, _, _ = insertFakeFidClueAndGetQueryInfo(task, fids)
	}
	// 对比逻辑
	for _, v := range list {
		code := statusCode[v.Url]
		// 先确认IP是否存在, 如果IP不存在则新增
		asset, ok := feAssetsMap[v.Ip]
		if !ok {
			insertAsset, insertExist := insertMap[v.Ip]
			if !insertExist {
				insertAsset = newFofaeeAsset(v, code)
				insertMap[v.Ip] = insertAsset
			}
			appendFofaeeAssetPort(insertAsset, v, code)
			continue
		}

		// 如果IP存在, 则确认资产状态, 台账资产跳过
		if utils.ListContains(ffa.StatusAccount, cast.ToInt(asset.Status)) {
			accountIps = append(accountIps, asset.Ip) // 记录存在于台账ip
			continue
		}

		updateStatus := cast.ToInt(asset.Status) != ffa.StatusThreatAsset
		// 如果端口不存在否则追加端口, 并修改资产状态为威胁资产
		isAppend := appendFofaeeAssetPort(asset, v, code)
		if isAppend || updateStatus {
			asset.ThreatenType = getThreatenType(v.FakeType)
			updateIps[v.Ip] = struct{}{}
		}
	}

	// 更新已有资产
	updateList := make([]*ffa.FofaeeAssets, 0, len(updateIps))
	for k, v := range feAssetsMap {
		if _, ok := updateIps[k]; !ok {
			continue
		}
		v.Status = ffa.StatusThreatAsset
		v.Tags = newFofaeeTag(v.Tags, req.UserId, req.OperatorId)
		v.Hosts = utils.ListDistinctNonZero(v.Hosts)
		v.ClueCompanyName = distinctClueCompany(v.ClueCompanyName)
		updateList = append(updateList, v)
	}
	if updateErr := client.Updates(ctx, updateList...); updateErr != nil {
		return nil, updateErr
	}

	// 插入新的威胁资产
	insertList := make([]*ffa.FofaeeAssets, 0, len(insertMap))
	for k := range insertMap {
		asset := insertMap[k]
		asset.Tags = newFofaeeTag(nil, req.UserId, req.OperatorId)
		asset.Hosts = utils.ListDistinctNonZero(asset.Hosts)
		asset.ClueCompanyName = distinctClueCompany(asset.ClueCompanyName)
		insertList = append(insertList, asset)
	}
	_, _, err = client.Create(ctx, insertList)
	return accountIps, err
}

func newFofaeeAsset(item *rr.RecommendResult, code int) *ffa.FofaeeAssets {
	port := ffa.FofaeeAssetPort{
		HttpStatusCode: code,
		Screenshot:     item.Screenshot,
		Title:          item.Title,
		Url:            item.Url,
		Protocol:       item.Protocol,
		Port:           cast.ToInt(item.Port),
		Domain:         item.Domain,
		Subdomain:      item.Subdomain,
		Logo:           ffa.Icon{Hash: item.Logo.Hash, Content: item.Logo.Content},
	}
	asset := &ffa.FofaeeAssets{
		UserId:       item.UserId,
		Ip:           item.Ip,
		IsIpv6:       item.IsIPv6,
		PortList:     []ffa.FofaeeAssetPort{port},
		HostList:     []ffa.FofaeeAssetPort{port},
		Type:         1,
		Level:        4,
		ThreatenType: getThreatenType(item.FakeType),
		Status:       ffa.StatusThreatAsset,
		CreatedAt:    time.Now().Format(utils.DateTimeLayout),
		UpdatedAt:    time.Now().Format(utils.DateTimeLayout),
	}
	if item.FakeCompany != "" {
		asset.ClueCompanyName = []any{item.FakeCompany}
	}
	for i := range item.Reason {
		ids := utils.ListColumn(asset.ReasonArr, func(t any) int {

			if t == nil {
				return 0
			}
			switch t.(type) {
			case ffa.AssetReason:
				return t.(ffa.AssetReason).Id
			case ffa.FofaeeAssetPort:
				return t.(ffa.FofaeeAssetPort).Reason[0].Id
			case map[string]any:
				return cast.ToInt(t.(map[string]any)["id"])
			}
			return 0
		})
		if !utils.ListContains(ids, item.Reason[i].Id) {
			asset.ReasonArr = append(asset.ReasonArr, ffa.AssetReason{
				Id:              item.Reason[i].Id,
				GroupId:         item.Reason[i].GroupId,
				Source:          item.Reason[i].Source,
				Type:            item.Reason[i].Type,
				Content:         item.Reason[i].Content,
				ClueCompanyName: item.Reason[i].ClueCompanyName,
			})
		}
		port.Reason = append(port.Reason, ffa.AssetReason{
			Id:              item.Reason[i].Id,
			GroupId:         item.Reason[i].GroupId,
			Source:          item.Reason[i].Source,
			Type:            item.Reason[i].Type,
			Content:         item.Reason[i].Content,
			ClueCompanyName: item.Reason[i].ClueCompanyName,
		})
	}
	asset.Hosts = append(asset.Hosts, item.Domain, item.Subdomain)
	return asset
}

func newFofaeeTag(originTag []int, userId, operatorId uint64) []int {
	tag := utils.If(userId == operatorId, ffa.TagUserRecommend, ffa.TagSafeRecommend)
	if len(originTag) == 0 {
		return []int{tag}
	}
	exist := false
	for _, v := range originTag {
		if v == tag {
			exist = true
		}
	}
	if !exist {
		originTag = append(originTag, tag)
	}
	return originTag
}

func appendFofaeeAssetPort(asset *ffa.FofaeeAssets, item *rr.RecommendResult, code int) (isAppend bool) {
	portItem := ffa.FofaeeAssetPort{
		HttpStatusCode: code,
		Screenshot:     item.Screenshot,
		Title:          item.Title,
		Url:            item.Url,
		Protocol:       item.Protocol,
		Port:           cast.ToInt(item.Port),
		Domain:         item.Domain,
		Subdomain:      item.Subdomain,
		Logo:           ffa.Icon{Hash: item.Logo.Hash, Content: item.Logo.Content},
	}
	for i := range item.Reason {
		portItem.Reason = append(portItem.Reason, ffa.AssetReason{
			Id:              item.Reason[i].Id,
			GroupId:         item.Reason[i].GroupId,
			Source:          item.Reason[i].Source,
			Type:            item.Reason[i].Type,
			Content:         item.Reason[i].Content,
			ClueCompanyName: item.Reason[i].ClueCompanyName,
		})
	}
	if item.FakeCompany != "" {
		asset.ClueCompanyName = append(asset.ClueCompanyName, item.FakeCompany)
	}

	portExists := false
	for i := range asset.PortList {
		if cast.ToInt(asset.PortList[i].Port) == cast.ToInt(portItem.Port) {
			portExists = true
			break
		}
	}
	if !portExists {
		isAppend = true
		asset.PortList = append(asset.PortList, portItem)
		// 追加Port时,添加数据来源
		for i := range item.Reason {
			ids := utils.ListColumn(asset.ReasonArr, func(t any) int {
				if t == nil {
					return 0
				}
				switch t.(type) {
				case ffa.AssetReason:
					return t.(ffa.AssetReason).Id
				case ffa.FofaeeAssetPort:
					return t.(ffa.FofaeeAssetPort).Reason[0].Id
				case map[string]any:
					return cast.ToInt(t.(map[string]any)["id"])
				}
				return 0
			})
			if !utils.ListContains(ids, item.Reason[i].Id) {
				asset.ReasonArr = append(asset.ReasonArr, ffa.AssetReason{
					Id:              item.Reason[i].Id,
					GroupId:         item.Reason[i].GroupId,
					Source:          item.Reason[i].Source,
					Type:            item.Reason[i].Type,
					Content:         item.Reason[i].Content,
					ClueCompanyName: item.Reason[i].ClueCompanyName,
				})
			}
		}
	}

	hostExists := false
	for i := range asset.HostList {
		if cast.ToInt(asset.HostList[i].Port) == cast.ToInt(item.Port) &&
			asset.HostList[i].Subdomain == item.Subdomain &&
			asset.HostList[i].Protocol == item.Protocol {
			hostExists = true
			break
		}
	}
	if !hostExists {
		isAppend = true
		asset.HostList = append(asset.HostList, portItem)
		// 追加HostList时,添加数据来源
		for i := range item.Reason {
			ids := utils.ListColumn(asset.ReasonArr, func(t any) int {
				if t == nil {
					return 0
				}
				switch t.(type) {
				case ffa.AssetReason:
					return t.(ffa.AssetReason).Id
				case ffa.FofaeeAssetPort:
					return t.(ffa.FofaeeAssetPort).Reason[0].Id
				case map[string]any:
					return cast.ToInt(t.(map[string]any)["id"])
				}
				return 0
			})
			if !utils.ListContains(ids, item.Reason[i].Id) {
				asset.ReasonArr = append(asset.ReasonArr, ffa.AssetReason{
					Id:              item.Reason[i].Id,
					GroupId:         item.Reason[i].GroupId,
					Source:          item.Reason[i].Source,
					Type:            item.Reason[i].Type,
					Content:         item.Reason[i].Content,
					ClueCompanyName: item.Reason[i].ClueCompanyName,
				})
			}
		}
	}
	asset.Hosts = append(asset.Hosts, item.Domain, item.Subdomain)
	return isAppend
}

// ip+port资产维度
func foradarAssetSync(task *phc.Task, list []*rr.RecommendResult, urlCode map[string]int) (map[string][]string, error) {
	ctx := context.Background()
	client := fra.NewForadarAssetModel()

	// 获取用户所有资产
	faAssets, err := client.FindByIpPort(ctx, int(task.UserId), nil)
	if err != nil {
		return nil, err
	}
	ipAssets := make(map[string][]*fra.ForadarAsset, len(faAssets)/5)
	for i := range faAssets {
		ipAssets[faAssets[i].Ip] = append(ipAssets[faAssets[i].Ip], &faAssets[i])
	}

	detectIpPortList := make(map[string][]string, len(list)/4)
	insert := make([]*fra.ForadarAsset, 0, len(list))
	updateAny := make([]map[string]any, 0, len(faAssets))
	updateAnyIds := make(map[string]struct{}, len(faAssets)/5)
	for _, v := range list {
		key := v.Ip + "_" + cast.ToString(v.Port)
		ps := v.Protocol + "_" + v.Subdomain
		code := urlCode[v.Url]

		ports, ok := ipAssets[v.Ip]
		// IP不存在于资产库则新增威胁资产
		if !ok {
			detectIpPortList[key] = append(detectIpPortList[key], ps)
			item := convertResult2FraAsset(task, v)
			item.HTTPStatusCode = code
			insert = append(insert, item)
			continue
		}

		isInsert := true
		for _, x := range ports {
			status := cast.ToInt(x.Status)
			// 如果IP是台账资产，则不需要同步
			if utils.ListContains(ffa.StatusAccount, status) {
				isInsert = false
				break
			}

			// 如果IP存在且资产状态为疑似或忽略则更新为威胁资产
			if _, ok := updateAnyIds[x.ID]; !ok {
				updateAnyIds[x.ID] = struct{}{}
				updateAny = append(updateAny, map[string]any{
					"id":            x.ID,
					"level":         4,
					"status":        fra.StatusThreatAsset,
					"threaten_type": getThreatenType(cast.ToInt(v.FakeType)),
				})
			}
			if x.Subdomain == v.Subdomain && cast.ToInt(x.Port) == cast.ToInt(v.Port) && x.Protocol == v.Protocol {
				isInsert = false
			}
		}
		// IP存在, 端口、协议、子域名不一样
		if isInsert {
			detectIpPortList[key] = append(detectIpPortList[key], ps)
			item := convertResult2FraAsset(task, v)
			item.HTTPStatusCode = code
			insert = append(insert, item)
		}
	}

	// 新增资产数据
	// 新增资产去重
	_, _, err = client.Create(ctx, insert)
	if err != nil {
		return nil, err
	}

	// 将非威胁资产状态更新为威胁资产（台账资产除外）
	err = client.UpdateWithMap(ctx, updateAny)
	if err != nil {
		return nil, err
	}
	return detectIpPortList, nil
}

func convertResult2FraAsset(task *phc.Task, asset *rr.RecommendResult) *fra.ForadarAsset {
	item := &fra.ForadarAsset{
		ID:         "",
		IsIpv6:     asset.IsIPv6,
		Screenshot: asset.Screenshot,
		Type:       1, //
		Protocol:   asset.Protocol,
		Logo: struct {
			Hash    any    `json:"hash"` // int
			Content string `json:"content"`
		}{
			Hash:    asset.Logo.Hash,
			Content: asset.Logo.Content,
		},
		CompanyID: asset.CompanyId,
		Level:     4,
		Title:     asset.Title,
		Cert: fra.AssetCert{
			IsValid: asset.CertsValid,
			Raw:     asset.CertRaw,
		},
		Ip:           asset.Ip,
		Url:          asset.Url,
		Port:         cast.ToInt(asset.Port),
		UserID:       int(task.UserId),
		Domain:       asset.Domain,
		Subdomain:    asset.Subdomain,
		ThreatenType: getThreatenType(asset.FakeType),
		IsFakeAssets: asset.IsFakeAssets,
		Status:       fra.StatusThreatAsset,
		CreatedAt:    utils.CurrentTime(),
		UpdatedAt:    utils.CurrentTime(),
	}
	if asset.FakeCompany != "" {
		item.ClueCompanyName = []string{asset.FakeCompany}
	}
	for i := range asset.Reason {
		item.Reason = append(item.Reason, fra.AssetReason{
			ID:              asset.Reason[i].Id,
			GroupID:         asset.Reason[i].GroupId,
			Source:          asset.Reason[i].Source,
			Type:            asset.Reason[i].Type,
			Content:         asset.Reason[i].Content,
			ClueCompanyName: asset.Reason[i].ClueCompanyName,
		})
	}
	return item
}

func getThreatenType(fakeType int) int {
	switch fakeType {
	case rr.TypeFakeTaskFake:
		return fra.ThreatenTypeDY
	case rr.TypeFakeTaskDomain:
		return fra.ThreatenTypeDomain
	case rr.TypeFakeTaskOther:
		return fra.ThreatenTypeOther
	}
	return rr.TypeFakeTaskOther
}

func distinctClueCompany(l []any) []any {
	if len(l) == 0 {
		return nil
	}
	m := make(map[string]struct{})
	list := make([]any, 0, len(l))
	for _, v := range l {
		cc := cast.ToString(v)
		if _, ok := m[cc]; !ok {
			m[cc] = struct{}{}
			list = append(list, v)
		}
	}
	return list
}

func detectIpPort(ctx context.Context, l []*rr.RecommendResult) map[string]bool {
	ipPortMap := utils.ListToSetFunc(l, func(v *rr.RecommendResult) (string, bool) {
		if v.Ip == "" || cast.ToString(v.Port) == "" {
			return "", false
		}
		key := detect.JoinIpPort(v.Ip, cast.ToString(v.Port))
		return key, true
	})

	req := detect.ReqDetect{
		IpPortGroup: utils.GetMapKeys(ipPortMap),
		BatchSize:   30,
		Network:     "tcp",
		Timeout:     3,
		Retry:       2,
	}
	detectResult := detect.Do(ctx, req)
	return detectResult
}

func convertOnline(b bool) int {
	return utils.If(b, fra.OnlineStatusYES, fra.OnlineStatusNO)
}

func asyncDetectIpPortAndUpdate(ctx context.Context, userId, taskId uint64, m map[string][]string) {
	err := ipAssetsDetectAndUpdate(ctx, int(userId), taskId, m)
	if err != nil {
		log.WithContextErrorf(ctx, "[钓鱼仿冒]异步探测IP、端口且同步威胁资产失败: %v", err)
	}
}

func ipAssetsDetectAndUpdate(ctx context.Context, userId int, taskId uint64, m map[string][]string) error {
	detectResult, exists := getIpPortCache(taskId)
	// 如果没有缓存，则重新探测
	if len(detectResult) == 0 || !exists {
		list := utils.GetMapKeys(m)
		detectResult = detect.DetectIpPort(ctx, list, "tcp", 5) // key: ip_port
	}

	// 1. updates foradar_assets
	fraClient := fra.NewForadarAssetModel()
	faAssets, err := fraClient.FindByIpPort(context.TODO(), userId, nil, fra.StatusThreatAsset)
	if err != nil {
		return err
	}

	var fraUpdateAny = make([]map[string]any, 0, 500)
	for i := range faAssets {
		key := detect.JoinIpPort(faAssets[i].Ip, cast.ToString(faAssets[i].Port))
		ps := faAssets[i].Protocol + "_" + faAssets[i].Subdomain
		value, ok := m[key]
		if !ok || !utils.ListContains(value, ps) {
			continue
		}
		fraUpdateAny = append(fraUpdateAny, map[string]any{"id": faAssets[i].ID, "online_state": convertOnline(detectResult[key])})
		if len(fraUpdateAny) < 500 {
			continue
		}
		err = fraClient.UpdateWithMap(ctx, fraUpdateAny)
		if err != nil {
			return err
		}
		fraUpdateAny = make([]map[string]any, 0, 500)
	}
	err = fraClient.UpdateWithMap(ctx, fraUpdateAny)
	if err != nil {
		return err
	}

	// get only ip online state
	ipState := make(map[string]bool, len(detectResult)/5)
	for k := range detectResult {
		sp := strings.Split(k, "_") // k format: ip_port
		if len(sp) != 2 {
			continue
		}
		if _, ok := ipState[sp[0]]; !ok || detectResult[k] {
			ipState[sp[0]] = detectResult[k]
		}
	}

	// 2. updates fofaee_assets
	ffaClient := ffa.NewFofaeeAssetsModel()
	ffaList, err := ffaClient.FindByIpAndPort(userId, nil, fra.StatusThreatAsset)
	if err != nil {
		return err
	}

	ffaUpdateList := make([]*ffa.FofaeeAssets, 0, 500)
	for i := range ffaList {
		ip := ffaList[i].Ip
		isOnline, ok := ipState[ip]
		if !ok {
			continue
		}
		ffaList[i].OnlineState = utils.If(isOnline, ffa.StatusOnline, ffa.StatusOffline)
		// handle port_list
		for j := range ffaList[i].PortList {
			key := detect.JoinIpPort(ip, cast.ToString(ffaList[i].PortList[j].Port))
			online := detectResult[key]
			ffaList[i].PortList[j].IsOpen = utils.If(online, ffa.StatusOnline, cast.ToInt(ffaList[i].PortList[j].IsOpen))
			ffaList[i].PortList[j].OnlineState = utils.If(online, ffa.StatusOnline, cast.ToInt(ffaList[i].PortList[j].OnlineState))
		}
		// handle host_list
		for j := range ffaList[i].HostList {
			key := detect.JoinIpPort(ip, cast.ToString(ffaList[i].HostList[j].Port))
			online := detectResult[key]
			ffaList[i].HostList[j].IsOpen = utils.If(online, ffa.StatusOnline, cast.ToInt(ffaList[i].HostList[j].IsOpen))
			ffaList[i].HostList[j].OnlineState = utils.If(online, ffa.StatusOnline, cast.ToInt(ffaList[i].HostList[j].OnlineState))
		}

		ffaUpdateList = append(ffaUpdateList, &ffaList[i])
	}

	split := utils.ListSplit[*ffa.FofaeeAssets](ffaUpdateList, 500)
	for i := range split {
		err = ffaClient.Updates(ctx, split[i]...)
		if err != nil {
			return err
		}
	}
	return nil
}
