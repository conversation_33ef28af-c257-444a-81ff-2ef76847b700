package phishing_fake

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/panjf2000/ants/v2"

	core "micro-service/coreService/proto"
	crawler "micro-service/crawlerService/proto"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clue_task"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/clues_groups"
	pft "micro-service/middleware/mysql/phishing_fake_task"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/spf13/cast"
)

func TaskList(req *pb.CounterfeitTaskListRequest, rsp *pb.CounterfeitTaskListResponse) error {
	handles := taskListHandles(req.Keyword, req.UserId, req.DateBefore, req.DateAfter)
	db := pft.NewTasker()
	list, total, err := db.List(int(req.Page), int(req.PerPage), handles...)
	if err != nil {
		return err
	}

	for i := range list {
		item := &pb.CounterfeitTaskListUnit{
			Id:              list[i].ID,
			Operator:        list[i].Operator,           // 发起人
			Name:            list[i].Name,               // 仿冒企业名称
			Status:          uint64(list[i].Status),     // 任务状态
			Step:            uint64(list[i].Step),       // 任务步骤
			StepDetail:      uint64(list[i].StepDetail), // 任务小步骤
			StepStatus:      uint64(list[i].StepStatus), // 小步骤状态
			CluesCount:      list[i].CluesCount,
			PhishingOnline:  uint64(list[i].PhishingOnline),
			PhishingOffline: uint64(list[i].PhishingOffline),
			DomainOnline:    uint64(list[i].DomainOnline),
			DomainOffline:   uint64(list[i].DomainOffline),
			ExpendFlags:     list[i].ExpendFlags,
			CreatedAt:       list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:       list[i].UpdatedAt.Format(utils.DateTimeLayout),
		}
		if list[i].DownloadPath != "" {
			name := "钓鱼仿冒任务资产数据" + utils.GetExt(list[i].DownloadPath)
			item.FilePath = getDownloadFilePath(list[i].DownloadPath, name)
		}
		rsp.Items = append(rsp.Items, item)
	}
	rsp.Total = total
	return nil
}

func TaskDelete(ctx context.Context, req *pb.CounterfeitTaskDelRequest) error {
	handles := make([]mysql.HandleFunc, 0)
	if len(req.Ids) == 0 {
		handles = taskListHandles(req.Keyword, req.UserId, req.DateBefore, req.DateAfter)
	} else {
		handles = append(handles, mysql.WithValuesIn("id", req.Ids), mysql.WithColumnValue("user_id", req.UserId))
	}
	data, err := pft.NewTasker().ListAll(handles...)
	if err != nil || len(data) == 0 {
		return err
	}

	taskIds := make([]uint64, 0, len(data))
	groupId := make([]uint64, 0, len(data))
	for i := range data {
		taskIds = append(taskIds, data[i].ID)
		groupId = append(groupId, data[i].GroupId)
		go deleteTaskCache(context.TODO(), data[i].ID)
	}

	// delete task records
	err = pft.NewTasker().Delete(taskIds...)
	if err != nil {
		return err
	}

	// Delete clue task records
	err = clue_task.NewClueTasker().DeleteByConditions(
		mysql.WithValuesIn("group_id", utils.ListDistinct(groupId)),
		mysql.WithColumnValue("user_id", req.UserId),
	)

	return err
}

func CounterfeitAssetsAgg(req *pb.CounterfeitAssetsAggRequest, rsp *pb.CounterfeitAssetsAggResponse) error {
	taskInfo, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return err
	}

	param := recommend_result.NewFindCondition()
	param.UserId = int(taskInfo.UserId)
	param.FakeTaskId = int(taskInfo.ID)
	param.FakeType = int(req.AssetType)
	param.Flag = taskInfo.ExpendFlags
	agg, err := recommend_result.NewRecommendResultModel().AssetsAgg(param)
	if err != nil {
		return err
	}
	rsp.Domain = utils.ListDistinctNonZero(agg.Domain)
	rsp.Subdomain = utils.ListDistinctNonZero(agg.Subdomain)
	rsp.IcpCompany = utils.ListDistinctNonZero(agg.IcpCompany)
	rsp.Name = utils.ListDistinctNonZero(agg.FakeCompany)
	rsp.Port = utils.ListDistinctNonZero(agg.Port)
	rsp.Protocol = utils.ListDistinctNonZero(agg.Protocol)
	for _, v := range agg.Icons {
		if v.Hash == 0 {
			continue
		}
		iconName, _ := utils.GetFileName(v.Content)
		rsp.Logo = append(rsp.Logo, &pb.Icon{
			Hash:    cast.ToInt64(v.Hash),
			Content: getDownloadFilePath(v.Content, iconName),
		})
	}
	return nil
}

// CounterfeitAssetList 获取仿冒资产列表
func CounterfeitAssetList(ctx context.Context, req *pb.CounterfeitAssetListRequest, rsp *pb.CounterfeitAssetListResponse) error {
	// 检查任务是否属于该用户
	task, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return err
	}

	param := recommend_result.NewFindCondition()
	param.UserId = int(task.UserId)
	param.Keyword = req.Keyword
	param.FakeTaskId = int(task.ID)
	param.FakeType = int(req.AssetType)
	param.GroupId = int(task.GroupId)
	param.Flag = task.ExpendFlags
	param.IpFuzzy = req.Ip
	param.Domains = utils.ListDistinct(req.Domain)
	param.FakeCompany = utils.ListDistinct(req.Name)
	param.Ports = utils.ListDistinct(atoi(req.Port))
	param.Protocol = utils.ListDistinct(req.Protocol)
	param.FakeIcpCompany = utils.ListDistinct(req.IcpCompany)
	param.Icon = utils.ListDistinct(ixToi(req.Logo))
	param.Title = req.Title
	param.UrlFuzzy = req.Url
	param.Subdomains = utils.ListDistinct(req.Subdomain)
	if req.OnlineState != "" {
		param.OnlineState = utils.If(req.OnlineState == "true", recommend_result.Online, recommend_result.Offline) //nolint:goconst,gocritic
	}
	if req.ChainType != "" {
		param.ReasonType = cast.ToInt(req.ChainType)
	}
	if len(req.CreatedAt) == 2 {
		param.SourceUpdatedAt = [2]string{req.CreatedAt[0], req.CreatedAt[1]}
	}
	if len(req.UpdatedAt) == 2 {
		param.UpdatedAt = [2]string{req.UpdatedAt[0], req.UpdatedAt[1]}
	}
	// 标准模式下&任务状态为进行中时,默认只返回深度检测的结果
	if task.StepDetail == 402 && task.Status == 1 && task.DetectType == pft.DetectTypeStandard {
		param.FakeDeep = true
	}
	list, total, err := recommend_result.NewRecommendResultModel().
		FindByPageCondition(int(req.Page), int(req.PerPage), param)
	if err != nil {
		log.WithContextErrorf(ctx, "PhishingFakeAssetList CounterfeitAssetList error failed: %v", err)
		return nil
	}

	for _, v := range list {
		item := &pb.CounterfeitAssetInfo{
			Id:            v.Id,
			Ip:            v.Ip,
			Name:          v.FakeCompany, // 仿冒目标
			Port:          cast.ToString(v.Port),
			Protocol:      v.Protocol,
			Domain:        v.Subdomain,
			Url:           v.Url,
			Title:         v.Title,
			IconHash:      strconv.Itoa(cast.ToInt(v.Logo.Hash)),
			IconContent:   getChainContent(clues.TYPE_LOGO, v.Logo.Content),
			IcpCompany:    v.FakeIcpCompany,                                               // 仿冒地址ICP备案企业
			OnlineState:   v.OnlineState,                                                  // 在线状态
			Screenshot:    getDownloadFilePath(v.Screenshot, filepath.Base(v.Screenshot)), // 截图地址
			CreatedAt:     cast.ToString(v.SourceUpdatedAt),                               // 发现时间
			UpdatedAt:     v.UpdatedAt,
			FakeAssetFrom: v.FakeAssetFrom,
		}
		for i := range v.Reason {
			item.ChainList = append(item.ChainList, &pb.CounterfeitAssetChain{
				Id:              uint64(v.Reason[i].Id),
				Type:            uint64(v.Reason[i].Type),
				GroupId:         uint64(v.Reason[i].GroupId),
				Source:          uint64(v.Reason[i].Source),
				Content:         getChainContent(v.Reason[i].Type, v.Reason[i].Content),
				ClueCompanyName: v.Reason[i].ClueCompanyName,
			})
		}
		if len(item.ChainList) > 0 {
			item.ChainList = append(item.ChainList, &pb.CounterfeitAssetChain{Content: v.Ip})
		}
		rsp.Items = append(rsp.Items, item)
	}
	rsp.Total = total
	return nil
}

func assetsImportHandler(ctx context.Context, task *pft.Task, uploadData []*recommend_result.RecommendResult) error {
	log.Infof("[钓鱼仿冒]数据导入开始, userId: %d, taskId: %d, uploadData: %d", task.UserId, task.ID, len(uploadData))
	// 获取用户仿冒资产数据
	assets, err := getRecommendResult(task, &pb.CounterfeitAssetListRequest{})
	if err != nil {
		return err
	}

	// 对比导入数据
	insert := compareImportAssets(uploadData, assets)
	log.Infof("[钓鱼仿冒]数据导入对比完成, userId: %d, taskId: %d, insert: %d", task.UserId, task.ID, len(insert))

	// 更新ICP信息
	ch := make(chan updateICPChan)
	go updateFakeAssetsIcpCompany(context.TODO(), task, insert, ch)
	for range ch {
	}
	err = recommend_result.NewRecommendResultModel().Create(insert) // 用户导入数据入库
	return err
}

// CounterfeitAssetsImport 数据导入 Step: 3, StepDetail: 301
func CounterfeitAssetsImport(ctx context.Context, req *pb.CounterfeitAssetsImportRequest) error {
	log.Infof("[钓鱼仿冒]CounterfeitAssetsImport, userId: %d, taskId: %d, filePath: %s", req.UserId, req.TaskId, req.FilePath)
	task, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return err
	}

	step3 := pft.DetectStep3
	stepDetail := 100*step3 + 1
	// 要完成仿冒数据发现操作才能进行该步骤
	if task.Step != step3 {
		return errors.New("请先完成上一步再进行该操作")
	}

	// 更新任务的小步骤
	err = updateTaskStep(task.ID, step3, stepDetail, pft.StepStatusDoing)
	if err != nil {
		return err
	}

	// 处理上传文件
	uploadData, err := uploadFileHandling(req.FilePath)
	if err != nil {
		_ = updateTaskStep(task.ID, step3, stepDetail, pft.StepStatusFailed)
		return err
	}

	stepStatus := pft.StepStatusDone
	if err = assetsImportHandler(ctx, &task, uploadData); err != nil {
		log.Errorf("[钓鱼仿冒]Assets Import Handler err: %v\n", err)
		stepStatus = pft.StepStatusFailed
	}

	// 更新本步骤为完成
	err = updateTaskStep(task.ID, step3, stepDetail, stepStatus)
	return err
}

func CounterfeitAssetsCount(req *pb.CounterfeitAssetDetectRequest, rsp *pb.CounterfeitAssetsCountResponse) error {
	// 检查任务是否属于该用户
	task, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return err
	}

	condition := recommend_result.NewFindCondition()
	condition.UserId = int(task.UserId)
	condition.FakeTaskId = int(task.ID)
	condition.Flag = task.ExpendFlags
	condition.FakeType = recommend_result.TypeFakeTaskFake
	// 标准模式下&任务状态为进行中时,默认只返回深度检测的结果
	if task.StepDetail == 402 && task.Status == 1 && task.DetectType == pft.DetectTypeStandard {
		condition.FakeDeep = true
	}
	_, total, err := recommend_result.NewRecommendResultModel().
		FindByPageCondition(1, 1, condition)
	if err != nil {
		log.Errorf("[钓鱼仿冒]CounterfeitAssetsCount TypeFakeTaskFake err: %v\n", err)
		rsp.DomainTotal = 0
		rsp.FakeTotal = 0
		return nil
	}
	rsp.FakeTotal = total

	condition.FakeType = recommend_result.TypeFakeTaskDomain
	_, total, err = recommend_result.NewRecommendResultModel().FindByPageCondition(1, 1, condition)
	if err != nil {
		rsp.DomainTotal = 0
		log.Errorf("[钓鱼仿冒]CounterfeitAssetsCount DomainTotal err: %v\n", err)
		return nil
	}
	rsp.DomainTotal = total
	return nil
}

// CounterfeitAssetsDownload 下载仿冒资产
func CounterfeitAssetsDownload(req *pb.CounterfeitAssetDetectRequest) (string, error) {
	task, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return "", err
	}

	var fileExist bool // 下载文件是否存在
	if task.DownloadPath != "" {
		fileExist = utils.FileIsExist(path.Join(storage.GetRootPath(), task.DownloadPath))
	}
	if !fileExist {
		storePath, err := resultDownload(&task)
		if err != nil {
			return "", err
		}
		task.DownloadPath = storePath

		// 更新任务文件存储路径
		err = pft.NewTasker().Update(task)
		if err != nil {
			return "", err
		}
	}

	name := "钓鱼仿冒任务数据" + utils.GetExt(task.DownloadPath)
	storePath := getDownloadFilePath(task.DownloadPath, name)
	return storePath, nil
}

// CounterfeitAssetsFind 仿冒数据发现 Step:3, StepDetail: 300
func CounterfeitAssetsFind(ctx context.Context, req *pb.CounterfeitAssetFindRequest) error {
	// 检查用户任务
	task, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return err
	}

	// 检查操作步骤是否正确
	if task.Step != pft.DetectStep2 {
		return errors.New("请先完成上一步再进行该操作")
	}

	// 获取线索
	clueList, err := getClues(&task)
	if err != nil {
		return err
	}

	// 更新任务至step3和线索数量
	taskName := fmt.Sprintf("钓鱼仿冒推荐任务-%d_%s", task.ID, time.Now().Format(utils.DateTimeLayout))
	task.ExpendFlags = utils.If(task.ExpendFlags != "", task.ExpendFlags, pft.GetExpendFlagId(task.UserId, taskName))
	task.CluesCount = strconv.Itoa(len(clueList))
	task.Step = pft.DetectStep3
	task.StepDetail = task.Step * 100
	if err := pft.NewTasker().UpdateAny(task.ID, map[string]any{
		"expend_flags": task.ExpendFlags,
		"clues_count":  task.CluesCount,
		"step":         task.Step,
		"step_detail":  task.StepDetail,
		"step_status":  pft.StepStatusDoing,
	}); err != nil {
		return err
	}

	// 创建任务日志记录
	if err := taskRecommendRecord(clueList, &task, taskName); err != nil {
		return err
	}

	// async Counterfeit Assets Find
	go asyncAssetsFind(context.TODO(), &task, clueList)
	return nil
}

// 仿冒测绘流程发现资产
func asyncAssetsFind(ctx context.Context, task *pft.Task, list []*clues.Clue) {
	var (
		index    int
		clueList = make([]*clues.Clue, 0, len(list))
		domains  = make([]string, 0)
		wg       = &sync.WaitGroup{}
		assets   = make([]*recommend_result.RecommendResult, 0, 50*len(list))
	)

	for _, v := range list {
		switch v.Type {
		case clues.TYPE_LOGO, clues.TYPE_FID, clues.TYPE_KEYWORD: // 可查仿冒
			clueList = append(clueList, v)
		case clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN:
			domains = append(domains, v.Content)
			clueList = append(clueList, v) // Hunter域名相似查询
		default:
			log.Errorf("[钓鱼仿冒]线索类型错误: %d", v.Type)
		}
	}

	type assetChan struct {
		assets []*recommend_result.RecommendResult
		err    error
	}

	ch := make(chan assetChan)
	stepDur := 500 * time.Millisecond
	domainsJoin := domainsToQuery(utils.ListDistinct(domains))
	domainsJoinHunter := domainsToQueryHunter(utils.ListDistinct(domains))
	domainsJoinQuake := domainsToQueryQuake(utils.ListDistinct(domains))
	taskCount := 1

	for i, v := range clueList {
		wg.Add(1)
		i := i
		go func(item *clues.Clue) {
			time.Sleep(time.Duration(i) * stepDur)
			defer wg.Done()
			// 去fofa获取仿冒数据
			fakeAssets, err := getFofaFakeAssets(context.TODO(), domainsJoin, item, list, task.HasCloudAssets)
			ch <- assetChan{assets: fakeAssets, err: err}
			if task.IsHunter == 1 {
				taskCount++
				hunterAssets, err := getHunterFakeAssets(context.TODO(), domainsJoinHunter, item, list, task.HasCloudAssets)
				ch <- assetChan{assets: hunterAssets, err: err}
			}
			if task.IsQuake == 1 {
				taskCount++
				quakeAssets, err := getQuakeFakeAssets(context.TODO(), domainsJoinQuake, item, list, task.HasCloudAssets)
				ch <- assetChan{assets: quakeAssets, err: err}
			}
		}(v)
	}

	go func() {
		wg.Wait()
		close(ch)
	}()

	for x := range ch {
		index++
		if x.err != nil {
			log.Errorf("[钓鱼仿冒]获取钓鱼仿冒数据: %v", x.err)
		} else {
			assets = append(assets, x.assets...)
		}
		updateAssetDiscoveryProgress(task, index, 2*len(clueList)*taskCount) // 更新进度, max progress: 50%
	}
	log.Infof("[钓鱼仿冒]获取钓鱼仿冒数据完成, 任务ID: %d, 资产数量: %d", task.ID, len(assets))

	// 资产过滤-根域在用户线索中的
	assets = clueDomainFilter(assets, domains)

	// 仿冒资产补充用户、分组、ICP备案等信息, 并实时获取进度
	icpCh := make(chan updateICPChan)
	go updateFakeAssetsIcpCompany(context.TODO(), task, assets, icpCh)
	for x := range icpCh {
		current := (x.index + x.total) / 2
		updateAssetDiscoveryProgress(task, current, x.total)
	}

	// 资产过滤-cdn资产过滤
	assets = cdnFilter(assets)

	// 资产去重并入库
	distinctAssets := distinctRecommendAssets(assets)
	err := recommend_result.NewRecommendResultModel().Create(distinctAssets)
	if err != nil {
		log.Errorf("[钓鱼仿冒]钓鱼仿冒资产入库出错: %v", err)
	}

	// 更新任务状态
	task.ExpendProgress = 100
	task.StepStatus = pft.StepStatusDone
	if err = pft.NewTasker().Update(*task); err != nil {
		log.Errorf("[钓鱼仿冒]仿冒资产发现-更新任务状态失败: %v", err)
	}
}

type updateICPChan struct{ index, total int }

// 查询资产上域名的备案信息
func updateFakeAssetsIcpCompany(ctx context.Context, task *pft.Task, assets []*recommend_result.RecommendResult, ch chan<- updateICPChan) {
	defer close(ch)
	if len(assets) == 0 {
		return
	}

	groupInfo, _ := clues_groups.NewCluesGrouper().First(mysql.WithColumnValue("id", task.GroupId))
	icpDomains := make(map[string]struct{}, len(assets)/5)
	for i := range assets {
		assets[i].FakeTaskId = int(task.ID)
		assets[i].UserId = int(task.UserId)
		assets[i].GroupId = int(task.GroupId)
		assets[i].GroupName = groupInfo.Name
		assets[i].Flag = task.ExpendFlags
		if topDomain, _ := utils.FindRootDomain(assets[i].Domain); topDomain != "" {
			icpDomains[topDomain] = struct{}{}
		}
	}

	if len(icpDomains) == 0 {
		return
	}

	var (
		index  atomic.Int32
		total  = len(icpDomains)
		wg     = &sync.WaitGroup{}
		conMap = cmap.New[string]()
	)

	for v := range icpDomains {
		wg.Add(1)
		// time.Sleep(50 * time.Millisecond)
		go func(domain string) {
			defer wg.Done()
			var err error
			req := &core.IcpDomainRequest{Domain: domain}
			rsp := &core.IcpResponse{}
			if cfg.IsLocalClient() {
				err = core.HttpClient(http.MethodGet, "/api/v1/beian/domain/"+domain, nil, rsp)
			} else {
				ctxDomain, cancel := context.WithTimeout(context.TODO(), 60*time.Second)
				defer cancel()
				rsp, err = core.GetProtoCoreClient().Domain(ctxDomain, req)
			}
			ch <- updateICPChan{int(index.Add(1)), total}
			if err != nil {
				log.Errorf("[钓鱼仿冒] Get domain->%s icp company info failed: %v", domain, err)
			}
			conMap.Set(domain, rsp.GetInfo().GetCompanyName())
		}(v)
	}
	wg.Wait()

	for i := range assets {
		topDomain, _ := utils.FindRootDomain(assets[i].Domain)
		icpCompany, _ := conMap.Get(topDomain)
		if assets[i].FakeIcpCompany == "" || icpCompany != "" {
			assets[i].FakeIcpCompany = icpCompany
		}
	}
}

// CounterfeitAssetsDetect 仿冒数据探活 Step: 4, StepDetail: 400
func CounterfeitAssetsDetect(ctx context.Context, req *pb.CounterfeitAssetDetectRequest) error {
	task, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return err
	}
	if task.Step != pft.DetectStep3 {
		return errors.New("请先完成上一步再进行该操作")
	}

	// 更新小步骤状态
	err = updateTaskStep(task.ID, pft.DetectStep4, pft.DetectStep4*100, pft.StepStatusDoing)
	if err != nil {
		return err
	}

	// 获取仿冒资产数据
	list, err := getRecommendResult(&task, &pb.CounterfeitAssetListRequest{})
	if err != nil {
		return err
	}
	_ = pft.NewTasker().UpdateAny(task.ID, map[string]any{"progress": 3.5})

	// async fake assets ip+port detect
	go fakeAssetsPortDetect(ctx, task.ID, list)
	// async assets url detect
	go asyncUrlDetect(ctx, &task, list, pft.DetectStep4*100)
	return nil
}

// CounterfeitAssetsDeepDetectHandle 仿冒数据深度探测处理
func CounterfeitAssetsDeepDetectHandle(ctx context.Context, task *pft.Task, req *pb.CounterfeitAssetListRequest, ips []string) {
	log.WithContextInfof(ctx, "[钓鱼仿冒] deep start taskId: %d", task.ID)
	assets := make([]*recommend_result.RecommendResult, 0)
	search := &pb.CounterfeitAssetListRequest{AssetType: recommend_result.TypeFakeTaskFake}
	// 同步用户勾选中的资产
	if len(req.Ids) > 0 {
		search.Ids = req.Ids
	}
	updateDeepData := make([]*recommend_result.UpdateAnyParam, 0)
	list, err := getRecommendResult(task, search)
	// 去除台账IP
	list = utils.ListFilter(list, func(result *recommend_result.RecommendResult) bool {
		param := &recommend_result.UpdateAnyParam{Id: result.Id, Data: make(map[string]any, 1)}
		param.Data["fake_deep"] = true
		updateDeepData = append(updateDeepData, param)
		return !utils.ListContains(ips, result.Ip)
	})
	if err != nil {
		log.WithContextWarnf(ctx, "[钓鱼仿冒] deep taskId: %d, 获取仿冒列表失败:%s", task.ID, err.Error())
		return
	}
	// 获取资产FID,&来源线索
	fids := make(map[string]int, 0)
	for x := range list {
		// 检查 Fid 是否为空
		if list[x].Fid == "" {
			log.Warnf("[钓鱼仿冒] 同步资产时发现空FID, 跳过该记录, taskId:%d", task.ID)
			continue
		}
		// 检查 Reason 是否为空
		if len(list[x].Reason) == 0 {
			log.Warnf("[钓鱼仿冒] 同步资产时发现空Reason, 跳过该记录, taskId:%d FID:%s", task.ID, list[x].Fid)
			continue
		}
		// 写入唯一 Fid 和 Reason[0].Id
		if _, ok := fids[list[x].Fid]; !ok {
			fids[list[x].Fid] = list[x].Reason[0].Id
			log.Infof("[钓鱼仿冒] 同步资产 准备写入FID线索, taskId:%d FID:%s", task.ID, list[x].Fid)
		}
	}
	groupAllClues, clueList, domainQuery, domains := insertFakeFidClueAndGetQueryInfo(task, fids)
	// 获取FOFA资产
	for x := range clueList {
		l, err := getFofaFakeAssets(context.TODO(), domainQuery, clueList[x], groupAllClues, task.HasCloudAssets)
		if err != nil {
			log.WithContextWarnf(ctx, "[钓鱼仿冒] deep taskId: %d, 获取Fofa资产失败:%s", task.ID, err.Error())
			continue
		}
		assets = append(assets, l...)
	}
	// 资产过滤-根域在用户线索中的
	assets = clueDomainFilter(assets, domains)
	// 仿冒资产补充用户、分组、ICP备案等信息, 并实时获取进度
	icpCh := make(chan updateICPChan)
	go updateFakeAssetsIcpCompany(context.TODO(), task, assets, icpCh)
	for x := range icpCh {
		log.Infof("[钓鱼仿冒] deep taskId:%d, updateFakeAssetsIcpCompany total:%d,index:%d", task.ID, x.total, x.index)
	}
	// 资产过滤-cdn资产过滤
	assets = cdnFilter(assets)
	// 去除台账IP
	utils.ListFilter(assets, func(result *recommend_result.RecommendResult) bool {
		return !utils.ListContains(ips, result.Ip)
	})
	// 资产去重并入库
	distinctAssets := distinctRecommendAssets(assets)
	err = recommend_result.NewRecommendResultModel().Create(distinctAssets)
	if err != nil {
		log.Errorf("[钓鱼仿冒] deep taskId:%d, 钓鱼仿冒资产入库出错: %v", task.ID, err)
	}
	// 更新深度发现数据信息
	_ = recommend_result.NewRecommendResultModel().UpdatesAny(updateDeepData)
	// 资产探活
	asyncUrlDetect(ctx, task, assets, pft.DetectStep4*100+2)
	// 更新任务状态
	task.StepStatus = pft.StepStatusDone
	if err = pft.NewTasker().Update(*task); err != nil {
		log.Errorf("[钓鱼仿冒] deep taskId:%d,仿冒资产发现-更新任务状态失败: %v", task.ID, err)
	}
	time.Sleep(2 * time.Second)
	// 异步生成Excel文件
	go asyncResultWriteExcel(task)
}

// CounterfeitAssetsDeepDetect 仿冒数据入账深度探测 Step: 4, StepDetail: 402
func CounterfeitAssetsDeepDetect(ctx context.Context, req *pb.CounterfeitAssetListRequest, rsp *pb.CounterfeitAssetsSyncResponse) error {
	// 检查任务时候是否该用户
	task, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return err
	}
	// 步骤合规检测
	if task.Step != pft.DetectStep4 {
		return errors.New("用户未进行数据存活性探测, 无法进行该操作")
	}
	if task.DetectType != pft.DetectTypeStandard {
		return errors.New("用户未选择标准模式, 无法进行该操作")
	}
	if task.StepDetail == pft.DetectStep4*100+2 {
		return errors.New("该任务已进行过深度探测,请勿重复操作")
	}
	// 更新小步骤状态,重置进度
	err = updateTaskStep(task.ID, pft.DetectStep4, pft.DetectStep4*100+2, pft.StepStatusDoing)
	if err != nil {
		return err
	}
	_ = pft.NewTasker().UpdateAny(task.ID, map[string]any{"progress": 3.5})
	// 入账威胁资产
	log.WithContextInfof(ctx, "[钓鱼仿冒]taskId: %d, CounterfeitAssetsDeepDetect-即将进行入账威胁资产操作...", task.ID)

	req.OperatorId = task.OperateUserId
	accountIps, err := syncFakeAssets(ctx, &task, req, false)
	if err != nil {
		return err
	}
	rsp.Ip = utils.ListDistinctNonZero(accountIps)
	log.WithContextInfof(ctx, "[钓鱼仿冒]taskId: %d, CounterfeitAssetsDeepDetect-入账威胁资产操作完成...", task.ID)
	// FID深度探测
	go CounterfeitAssetsDeepDetectHandle(ctx, &task, req, rsp.Ip)
	time.Sleep(time.Second) // sleep 1.0s
	return nil
}

// 异步执行探测任务
func asyncUrlDetect(ctx context.Context, task *pft.Task, list []*recommend_result.RecommendResult, stepDetail int) {
	// url detect by batch
	detectResult := urlDetect(ctx, list, task.ID, true, 2)

	task.DomainOffline, task.DomainOnline, task.PhishingOffline, task.PhishingOnline = 0, 0, 0, 0
	data := make([]*recommend_result.UpdateAnyParam, 0, len(list))
	// 截图进度单独计算
	total, current := len(list), 1
	if total != 0 {
		current = cast.ToInt(total / 2)
	} else {
		total, current = 1, 1
	}
	pool, _ := ants.NewPool(10, ants.WithExpiryDuration(1*time.Hour))
	defer pool.Release()
	for i, v := range list {
		v.OnlineState = onlineByCode(detectResult[v.Url])
		switch v.FakeType {
		case recommend_result.TypeFakeTaskFake:
			if v.OnlineState {
				task.PhishingOnline++
			} else {
				task.PhishingOffline++
			}
		case recommend_result.TypeFakeTaskDomain:
			if v.OnlineState {
				task.DomainOnline++
			} else {
				task.DomainOffline++
			}
		}
		// 更新在线状态
		param := &recommend_result.UpdateAnyParam{Id: v.Id, Data: make(map[string]any, 1)}
		param.Data["online_state"] = v.OnlineState
		data = append(data, param)
		// 大于50%,开始计数
		if i <= current {
			current += 1
		}
		updateDetectProgress(context.TODO(), task.ID, i+1, total)
		log.Infof("[钓鱼仿冒] taskId:%d, Screenshot index:%d,Total:%d", task.ID, i+1, total)
		// 异步截图
		_ = pool.Submit(func() {
			log.Infof("[钓鱼仿冒] 开始更新任务%d的URL截图: %v", task.ID, v.Url)
			screenshotData := make([]*recommend_result.UpdateAnyParam, 0, 1)
			screenshotParam := &recommend_result.UpdateAnyParam{Id: v.Id, Data: make(map[string]any, 1)}
			screenshotParam.Data["screenshot"] = GetScreenshot(context.TODO(), v.Url, v.OnlineState, task.UserId)
			screenshotData = append(screenshotData, screenshotParam)
			err := recommend_result.NewRecommendResultModel().UpdatesAny(screenshotData)
			if err != nil {
				log.Errorf("[钓鱼仿冒] 更新任务%d的URL截图: %v", task.ID, err)
			}
		})
	}

	err := recommend_result.NewRecommendResultModel().UpdatesAny(data)
	if err != nil {
		log.Errorf("[钓鱼仿冒] 更新任务%d的URL探测结果出错: %v", task.ID, err)
	}

	task.Step = pft.DetectStep4
	task.StepDetail = stepDetail
	task.StepStatus = pft.StepStatusDone
	task.Progress = 100.0
	// 如果是存活探测步骤, 生成excel
	if stepDetail == pft.DetectStep4*100 {
		go asyncResultWriteExcel(task)
	}
	err = pft.NewTasker().Update(*task)
	if err != nil {
		log.Errorf("[钓鱼仿冒] Update task_id: detect progress failed: %v\n", task.ID, err)
	}
}

func CounterfeitAssetsRetryScreenshot(ctx context.Context, req *pb.CounterfeitAssetListRequest) error {
	task, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return err
	}
	search := &pb.CounterfeitAssetListRequest{AssetType: recommend_result.TypeFakeTaskFake}
	// 同步用户勾选中的资产
	if len(req.Ids) > 0 {
		search.Ids = req.Ids
	}
	list, err := getRecommendResult(&task, search)
	if err != nil {
		return err
	}
	for x := range list {
		data := make([]*recommend_result.UpdateAnyParam, 0, len(list))
		addr := GetScreenshot(context.TODO(), list[x].Url, true, task.UserId)
		if addr == "" {
			return errors.New("获取截图失败,请稍后再试")
		}
		param := &recommend_result.UpdateAnyParam{Id: list[x].Id, Data: make(map[string]any, 1)}
		param.Data["screenshot"] = addr
		data = append(data, param)
		if urr := recommend_result.NewRecommendResultModel().UpdatesAny(data); urr != nil {
			return urr
		}
	}
	return nil
}

// SyncToThreatAsset Step:4, StepDetail: 401
func SyncToThreatAsset(ctx context.Context, req *pb.CounterfeitAssetListRequest, rsp *pb.CounterfeitAssetsSyncResponse) error {
	// 检查任务时候是否该用户
	task, err := checkUserTask(req.UserId, req.TaskId)
	if err != nil {
		return err
	}

	// 步骤合规检测
	if task.Step != pft.DetectStep4 {
		return errors.New("用户未进行数据存活性探测, 无法进行该操作")
	}

	// 不进行资产同步, 更新进度为完成
	if req.IsFinished {
		task.StepStatus = pft.StepStatusDone
		task.Status = pft.StatusDone
		if updateErr := pft.NewTasker().Update(task); updateErr != nil {
			return err
		}
		// 异步生成Excel文件
		go asyncResultWriteExcel(&task)

		time.Sleep(500 * time.Millisecond)
		return nil
	}
	// 入账威胁资产
	log.WithContextInfof(ctx, "[钓鱼仿冒]taskId: %d, SyncToThreatAsset-即将进行入账威胁资产操作...", task.ID)
	req.OperatorId = task.OperateUserId
	accountIps, err := syncFakeAssets(ctx, &task, req, true)
	if err != nil {
		fmt.Println(333333)
		return err
	}
	log.WithContextInfof(ctx, "[钓鱼仿冒]taskId: %d, SyncToThreatAsset-入账威胁资产操作完成...", task.ID)
	rsp.Ip = utils.ListDistinctNonZero(accountIps)
	time.Sleep(time.Second) // sleep 1.0s
	return nil
}

func urlDetect(ctx context.Context, l []*recommend_result.RecommendResult, taskId uint64, updateProgress bool, ratio int) map[string]int {
	urlMap := make(map[string]struct{}, len(l)/2)
	for _, v := range l {
		if v.Url == "" {
			continue
		}
		if index := strings.Index(v.Url, "://"); index > 0 {
			urlMap[v.Url] = struct{}{}
		}
	}
	if len(urlMap) == 0 {
		return make(map[string]int)
	}

	list := make([]string, 0, len(urlMap))
	for k := range urlMap {
		list = append(list, k)
	}

	type result struct {
		rsp *crawler.UrlDetectResponse
		err error
	}

	const batch = 60
	ch := make(chan result, 5)
	split := utils.ListSplit[string](list, batch)
	go func() {
		const timeout = 3
		wg := &sync.WaitGroup{}
		for i := range split {
			wg.Add(1)
			go func(l []string) {
				defer wg.Done()
				req := crawler.UrlDetectRequest{SkipVerify: true, Timeout: timeout, Urls: l}
				rsp, err := crawler.GetProtoClient().UrlDetect(ctx, &req, microx.ServerTimeout(batch*timeout+5))
				ch <- result{rsp: rsp, err: err}
			}(split[i])
		}
		wg.Wait()
		close(ch)
	}()

	var index = 0
	resultMap := make(map[string]int, len(list))
	for v := range ch {
		index++
		if v.err != nil {
			log.Errorf("[钓鱼仿冒] Url detect by crawler failed: %v", v.err)
		}
		if v.rsp != nil {
			for _, unit := range v.rsp.Results {
				resultMap[unit.GetUrl()] = int(unit.StatusCode)
			}
		}
		if updateProgress {
			updateDetectProgress(context.TODO(), taskId, index, len(split)*ratio)
		}
	}

	// 异步存储URL状态码
	go setUrlCodeCache(context.Background(), taskId, resultMap)

	return resultMap
}

// 仿冒资产IP端口存活性探测
func fakeAssetsPortDetect(ctx context.Context, taskId uint64, assets []*recommend_result.RecommendResult) {
	log.WithContextInfof(ctx, "[钓鱼仿冒]异步探测仿冒资产IP端口存活状态")
	// 探测
	detectResult := detectIpPort(ctx, assets)
	cacheContent := make(map[string]bool, len(detectResult))
	for k, v := range detectResult {
		if v {
			cacheContent[k] = true
		}
	}
	if len(cacheContent) == 0 {
		cacheContent["empty"] = false
	}

	bs, _ := json.Marshal(cacheContent)
	err := redis.Set(context.Background(), ipPortKey(taskId), string(bs), 2*utils.Day)
	if err != nil {
		log.WithContextErrorf(ctx, "[钓鱼仿冒]异步存储仿冒资产IP+Port在线状态缓存失败: %v", err)
	}
}
