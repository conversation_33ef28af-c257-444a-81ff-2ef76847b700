package phishing_fake

import (
	"context"
	"errors"
	"fmt"
	"micro-service/pkg/microx"
	"net/http"
	"path"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"golang.org/x/time/rate"

	core "micro-service/coreService/proto"
	crawler "micro-service/crawlerService/proto"
	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/clues"
	phc "micro-service/middleware/mysql/phishing_fake_task"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"micro-service/pkg/utils/image"

	"github.com/google/uuid"
	"github.com/spf13/cast"
	"golang.org/x/exp/constraints"
	"gorm.io/gorm"
)

// checkUserTask checks if a user has a specific task identified by userId and taskId.
func checkUserTask(userId, taskId uint64) (phc.Task, error) {
	task, err := phc.NewTasker().First(mysql.WithColumnValue("id", taskId))
	if err != nil && err != gorm.ErrRecordNotFound {
		return phc.Task{}, err
	}
	if task.UserId != userId {
		return phc.Task{}, errors.New("查询任务记录不存在")
	}
	return task, nil
}

func updateTaskStep(id uint64, step, stepDetail, stepStatus int) error {
	return phc.NewTasker().UpdateAny(id, map[string]any{
		"step":        step,
		"step_detail": stepDetail,
		"step_status": stepStatus,
	})
}

func taskRecommendRecord(clues []*clues.Clue, task *phc.Task, taskName string) error {
	record := recommend_record.RecommendRecord{
		Id:       task.ExpendFlags,
		UserId:   task.UserId,
		TaskName: taskName,
		Flag:     task.ExpendFlags,
		GroupId:  int(task.GroupId),
		ClueId:   make([]int, 0, len(clues)),
		Status:   recommend_record.StatusDoing,
		Progress: 0.0,
	}
	for _, v := range clues {
		record.ClueId = append(record.ClueId, int(v.Id))
	}
	err := recommend_record.NewRecommendRecordModel().Create(record)
	return err
}

func taskListHandles(keyword string, userId, dateBefore, dateAfter uint64) []mysql.HandleFunc {
	handles := []mysql.HandleFunc{mysql.WithColumnValue("user_id", userId)}
	handles = append(handles, mysql.WithOrder("created_at DESC"))
	if dateBefore != 0 && dateAfter != 0 {
		before, after := utils.UnixSecToTime(dateBefore), utils.UnixSecToTime(dateAfter)
		handles = append(handles, phc.WithCreatedAt(before, after))
	}
	if keyword != "" {
		handles = append(handles, phc.WithKeyword(keyword))
	}
	return handles
}

//nolint:unused,gocritic
func GetScreenshot(ctx context.Context, url string, isOnline bool, userId uint64) string {
	if !isOnline || url == "" {
		return ""
	}
	reqParam := &crawler.ScreenshotRequest{Url: url, Wait: 2}
	var screenshot string
	for i := 0; i < 3; i++ {
		rsp, err := crawler.GetProtoClient().Screenshot(context.Background(), reqParam, microx.SetTimeout(6, 5)...)
		if err != nil {
			log.Errorf("[钓鱼仿冒]Get domain: %s screenshot failed: %v", url, err)
			continue
		}
		if rsp.Img != "" {
			screenshot = rsp.Img
			break
		}
	}
	if screenshot != "" {
		imageName := fmt.Sprintf("%s-%s.png", utils.Md5sHash(url, false), uuid.NewString())
		imageName = path.Join(storage.DownloadUserDirPath(userId), imageName)
		err := image.WriteImage(crawler.DecodeBy(screenshot), path.Join(storage.GetRootPath(), imageName))
		if err != nil {
			log.Errorf("[钓鱼仿冒]Get domain: %s  save path: %s screenshot failed: %v", url, path.Join(storage.GetRootPath(), imageName), err)
			return ""
		}
		return imageName
	}
	return ""
}

// 拼接仿冒资产发现的域名语句
func domainsToQuery(domains []string) string {
	domainsList := make([]string, len(domains))
	for i := range domains {
		if utils.GetTopDomain(domains[i]) == domains[i] {
			domainsList[i] = fmt.Sprintf("domain!=%q", domains[i])
		} else {
			domainsList[i] = fmt.Sprintf("host!=%q", utils.GetTopDomain(domains[i]))
		}
	}
	return strings.Join(domainsList, " && ")
}

// 拼接Hunter仿冒资产发现的域名语句
func domainsToQueryHunter(domains []string) string {
	domainsList := make([]string, len(domains))
	for i := range domains {
		if utils.GetTopDomain(domains[i]) == domains[i] {
			domainsList[i] = fmt.Sprintf("domain.suffix!=%q", domains[i])
		} else {
			domainsList[i] = fmt.Sprintf("domain.suffix!=%q", utils.GetTopDomain(domains[i]))
		}
	}
	return strings.Join(domainsList, "&&")
}

// 拼接Quake仿冒资产发现的域名语句
func domainsToQueryQuake(domains []string) string {
	domainsList := make([]string, len(domains))
	for i := range domains {
		if utils.GetTopDomain(domains[i]) == domains[i] {
			domainsList[i] = fmt.Sprintf("NOT domain: %q", domains[i])
		} else {
			domainsList[i] = fmt.Sprintf("NOT domain: %q", utils.GetTopDomain(domains[i]))
		}
	}
	return strings.Join(domainsList, " AND ")
}

func insertFakeFidClueAndGetQueryInfo(task *phc.Task, fids map[string]int) ([]*clues.Clue, []*clues.Clue, string, []string) {
	newClues := make([]*clues.Clue, 0)
	domains := make([]string, 0)
	clueList, err := getClues(task)
	if err != nil {
		log.Errorf("[钓鱼仿冒] 获取线索失败:%s", err.Error())
		return clueList, newClues, "", domains
	}
	// 获取已存在线索的FID
	existFids := utils.ListColumn(utils.ListFilter(clueList, func(clue *clues.Clue) bool {
		return clues.TYPE_FID != clue.Type
	}), func(t *clues.Clue) string {
		return t.Content
	})
	// 插入线索
	for fid, parentID := range fids {
		if !utils.ListContains(existFids, fid) {
			cl := &clues.Clue{
				UserId:    task.UserId,
				GroupId:   task.GroupId,
				Type:      clues.TYPE_FID,
				Content:   fid,
				Status:    clues.AuditPassed,
				IsDeleted: clues.DeletedFalse,
				ParentId:  cast.ToUint64(parentID),
			}
			// 检查线索是否存在
			exitClue, _ := clues.NewCluer().First(
				mysql.WithWhere("type", clues.TYPE_FID),
				mysql.WithWhere("content", fid),
				mysql.WithWhere("group_id", task.GroupId),
			)
			if exitClue.Id != 0 {
				newClues = append(newClues, &exitClue)
				if exitClue.IsDeleted == 1 {
					if iErr := clues.NewCluer().UpdateAny(map[string]any{"is_deleted": clues.DeletedFalse}, mysql.WithWhere("id", exitClue.Id)); iErr != nil {
						log.Errorf("[钓鱼仿冒] 更新FID线索删除状态,失败:%s", iErr.Error())
					}
				}
				log.Errorf("[钓鱼仿冒] 插入FID失败,FID线索已存在:%d", exitClue.Id)
				continue
			}
			if iErr := clues.NewCluer().Create(cl); iErr != nil {
				log.Errorf("[钓鱼仿冒] 插入FID线索失败-2:%s", iErr.Error())
				continue
			}
			newClues = append(newClues, cl)
			clueList = append(clueList, cl)
		}
	}
	// 获取域名
	for _, v := range clueList {
		switch v.Type {
		case clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN:
			domains = append(domains, v.Content)
		}
	}
	// 拼装域名
	domainsJoin := domainsToQuery(utils.ListDistinct(domains))
	return clueList, newClues, domainsJoin, domains
}

func getDeepFofaQueryStr(ctx context.Context, fids []string, task *phc.Task) string {
	if len(fids) == 0 {
		log.WithContextWarnf(ctx, "[钓鱼仿冒] deep taskId: %d, fid empty", task.ID)
		return ""
	}
	// 获取线索
	domains := make([]string, 0)
	clueList, err := getClues(task)
	if err != nil {
		log.WithContextWarnf(ctx, "[钓鱼仿冒] deep taskId: %d, 获取线索列表失败:%s", task.ID, err.Error())
		return ""
	}
	// 获取域名
	for _, v := range clueList {
		switch v.Type {
		case clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN:
			domains = append(domains, v.Content)
		}
	}
	// 拼装Fid
	for i := range fids {
		fids[i] = fmt.Sprintf("fid!=%q", fids[i])
	}
	// 拼装域名
	domainsJoin := domainsToQuery(utils.ListDistinct(domains))
	return "(" + strings.Join(fids, " || ") + ") && (" + domainsJoin + ")"
}

// getAssetsByFOFA queries the Fofa API for assets with an obfuscated domain that matches the given domain.
// 仿冒资产获取修改为获取全量的数据，不只取1年内的数据
func getAssetsByFOFA(ctx context.Context, query string) ([]*core.Asset, error) {
	var err error
	var data = make([]*core.Asset, 0, 1000)
	for i := 1; i <= 2; i++ {
		req := &core.FofaQueryRequest{Qbase64: query, Page: uint32(i), Size: 500, Full: true, Field: []string{
			"ip", "port", "protocol", "base_protocol", "host", "link", "domain",
			"title", "icp", "cert", "isp", "fid", "lastupdatetime", "icon", "header",
			"icon_hash", "certs_valid", "cloud_name", "status_code", "banner", "fid",
		}}
		rsp := &core.FofaQueryResponse{}
		if cfg.IsLocalClient() {
			// 本地化通过HTTP调用saas
			err = core.HttpClient(http.MethodPost, "/api/v1/fofa/query", req, rsp)
		} else {
			// 否则rpc微服务调用
			rsp, err = core.GetProtoCoreClient().FofaQuery(ctx, req, microx.SetTimeout(30, 29)...)
		}
		// 获取失败时,重试一次
		if err != nil {
			time.Sleep(300 * time.Millisecond)
			if cfg.IsLocalClient() {
				// 本地化通过HTTP调用saas
				err = core.HttpClient(http.MethodPost, "/api/v1/fofa/query", req, rsp)
			} else {
				// 否则rpc微服务调用
				rsp, err = core.GetProtoCoreClient().FofaQuery(ctx, req, microx.SetTimeout(30, 29)...)
			}
		}
		if err != nil {
			log.Errorf("[钓鱼仿冒]Get fofa data failed with query: %s, err: %s", query, err.Error())
			continue
		}
		if len(rsp.GetSdata()) == 0 {
			log.Infof("[钓鱼仿冒]Get fofa data with query: %s，fofa-rsp-num:%d", query, rsp.Size)
			break
		} else {
			log.Infof("[钓鱼仿冒]Get fofa data with query: %s，fofa-rsp-num:%d", query, rsp.Size)
		}
		data = append(data, rsp.GetSdata()...)
		time.Sleep(300 * time.Millisecond)
	}

	if err != nil {
		log.Errorf("[钓鱼仿冒]Get fofa data failed with query: %s, err: %s", query, err.Error())
	}
	return data, nil
}

func clueDomainFilter(l []*recommend_result.RecommendResult, domains []string) []*recommend_result.RecommendResult {
	// delete assets in user root domain clues
	clueDomains := make(map[string]struct{}, len(domains))
	for _, v := range domains {
		clueDomains[v] = struct{}{}
	}
	var list = make([]*recommend_result.RecommendResult, 0, len(l))
	for i := range l {
		// 过滤用户线索中域名
		if _, ok := clueDomains[l[i].Domain]; ok {
			log.Infof("phishing fake assets filter domain t:%d,ip:%s,port:%d,url:%s", l[i].FakeTaskId, l[i].Ip, l[i].Port, l[i].Url)
			continue
		}
		// 过滤子域名
		for x := range domains {
			if strings.HasSuffix(l[i].Subdomain, "."+domains[x]) {
				log.Infof("phishing fake assets filter subdomain t:%d,ip:%s,port:%d,url:%s", l[i].FakeTaskId, l[i].Ip, l[i].Port, l[i].Url)
				continue
			}
		}
		list = append(list, l[i])
	}
	return list
}

var filters = []string{"浙江阿里巴巴云计算有限公司", "优刻得科技股份有限公司"}

func getIcpCompanyFilter() []string {
	fs := utils.ListDistinctNonZero(cfg.LoadCommon().IcpCompanyFilter)
	if len(fs) == 0 {
		fs = filters
	}
	return fs
}

// filter assets by cdn and user root domain clues
func cdnFilter(l []*recommend_result.RecommendResult) []*recommend_result.RecommendResult {
	// CDN
	filter := getIcpCompanyFilter()
	cdnCompanies := make(map[string]struct{}, len(filter))
	for _, v := range filter {
		cdnCompanies[v] = struct{}{}
	}

	var list = make([]*recommend_result.RecommendResult, 0, len(l))
	for i := range l {
		// 过滤CDN
		if _, ok := cdnCompanies[l[i].FakeIcpCompany]; ok {
			log.Infof("phishing fake assets filter cdn t:%d,ip:%s,port:%d,url:%s", l[i].FakeTaskId, l[i].Ip, l[i].Port, l[i].Url)
			continue
		}
		// 过滤有备案的数据
		if l[i].FakeIcpCompany != "" {
			log.Infof("phishing fake assets filter icp t:%d,ip:%s,port:%d,url:%s", l[i].FakeTaskId, l[i].Ip, l[i].Port, l[i].Url)
			continue
		}
		list = append(list, l[i])
	}
	return list
}

// 获取fofa资产
func getFofaFakeAssets(_ context.Context, domainQuery string, task *clues.Clue, clueList []*clues.Clue, HasCloudAssets int) ([]*recommend_result.RecommendResult, error) {

	if domainQuery == "" && (task.Type == clues.TYPE_LOGO || task.Type == clues.TYPE_KEYWORD || task.Type == clues.TYPE_FID) {
		return nil, nil
	}
	// is_cloud!="true" 过滤CDN
	var query string
	var fakeType = recommend_result.TypeFakeTaskFake
	cloudQuery := " && cloud_name=\"\""
	if HasCloudAssets == 1 {
		cloudQuery = ""
	}
	switch task.Type {
	case clues.TYPE_LOGO:
		if task.Hash == 0 {
			return nil, nil
		}
		query = fmt.Sprintf(`icon_hash==%q && (domain!="" && %s) && (is_honeypot=true || is_fraud=true || is_honeypot=false || is_fraud=false)%s`, strconv.Itoa(task.Hash), domainQuery, cloudQuery)
	case clues.TYPE_KEYWORD:
		if task.Content == "" {
			return nil, nil
		}
		titleQuery := ""
		if utf8.RuneCountInString(task.Content) > 4 {
			// 如果 task.Content 字符长度大于4的话，进行一种情况处理
			titleQuery = fmt.Sprintf(`title=%q`, task.Content)
		} else {
			// 其余是另外一种情况处理
			titleQuery = fmt.Sprintf(`title==%q`, task.Content)
		}
		query = fmt.Sprintf(`%s && (domain!="" && %s) && (is_honeypot=true || is_fraud=true || is_honeypot=false || is_fraud=false)%s`, titleQuery, domainQuery, cloudQuery)
	case clues.TYPE_FID:
		if task.Content == "" {
			return nil, nil
		}
		fidQueryStr := ""
		if strings.HasSuffix(task.Content, "==") {
			fidQueryStr = fmt.Sprintf(`fid=%q`, task.Content)
		} else {
			fidQueryStr = fmt.Sprintf(`fidv2=%q`, task.Content)
		}
		query = fmt.Sprintf(`%s && (%s) && (is_honeypot=true || is_fraud=true || is_honeypot=false || is_fraud=false)%s`, fidQueryStr, domainQuery, cloudQuery)
	case clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN:
		// Fofa 不查询相似域名
		return nil, nil
	default:
		log.Warnf("[钓鱼仿冒] task type %d not support", task.Type)
		return nil, nil
	}
	log.Infof("phishing fake assets fofa query: clue:%v, query:%s , HasCloudAssets:%d", task.Content, query, HasCloudAssets)
	list, err := getAssetsByFOFA(context.TODO(), query)
	if err != nil {
		return nil, err
	}
	// 过滤域名匹配出来的ip，如果需要过滤证书出来的实际为客户的ip，那么需要，用域名组装证书的fofa语句，查出来的ip作为已知ip，上边仿冒发现的语句的fofa资产的ip过滤这部分域名组装的证书语句发现的ip----todo

	var data = make([]*recommend_result.RecommendResult, 0, len(list))
	for _, v := range list {
		url := utils.If(v.GetLink() != "", v.GetLink(), v.GetHost())
		isCertValid, _ := strconv.ParseBool(v.GetCertsValid())
		iconHash, _ := strconv.ParseInt(v.GetIconHash(), 10, 64)
		// 仿冒目标为空时,使用上一级的企业名称
		if task.ClueCompanyName == "" {
			for cx := range clueList {
				if clueList[cx].Id == task.ParentId {
					task.ClueCompanyName = clueList[cx].ClueCompanyName
					break
				}
			}
		}
		log.Infof("phishing fake assets: clue:%v, ip:%s,port:%d,url:%s", task.Content, v.GetIp(), cast.ToInt(v.GetPort()), url)
		data = append(data, &recommend_result.RecommendResult{
			Ip:              utils.IPExpanded(v.GetIp()),
			Port:            cast.ToInt(v.GetPort()),
			Protocol:        v.GetProtocol(),
			BaseProtocol:    v.GetBaseProtocol(),
			Title:           v.GetTitle(),
			Url:             url,
			Subdomain:       utils.DomainFromUrl(url),
			Domain:          v.GetDomain(),
			FakeCompany:     task.ClueCompanyName,
			Cert:            strings.Join(utils.GetCert(v.GetCert(), false), " "),
			CertRaw:         v.GetCert(),
			CertsValid:      isCertValid,
			FakeType:        fakeType,
			Fid:             v.GetFid(),
			AssetsFrom:      recommend_result.AssetFromDefault,
			Logo:            recommend_result.NewLogo(v.GetIconHash(), storage.SaveIco(v.GetIcon(), iconHash, true)),
			Icp:             v.GetIcp(),
			ISP:             v.GetIsp(),
			Server:          v.GetServer(),
			IsIPv6:          utils.IsIPv6(v.GetIp()),
			CloudName:       v.GetCloudName(),
			ThreatenType:    cast.ToInt(getThreatenType(fakeType)),
			IsFakeAssets:    true,
			SourceUpdatedAt: v.GetLastupdatetime(),
			Status:          1,
			Level:           4,
			FakeDeep:        false,
			Reason: []recommend_result.RecommendReason{{
				Id:              int(task.Id),
				Type:            task.Type,
				Content:         task.Content,
				GroupId:         int(task.GroupId),
				ClueCompanyName: task.ClueCompanyName,
				Source:          task.Source,
			}},
			FakeAssetFrom: "fofa",
		})
	}
	return data, nil
}

func updateDetectProgress(_ context.Context, taskId uint64, index, total int) {
	if index != total && index%5 != 0 {
		return
	}

	progress := utils.Percent(index, total, 2)
	err := phc.NewTasker().UpdateAny(taskId, map[string]any{"progress": progress})
	if err != nil {
		log.Errorf("[钓鱼仿冒] Update task_id: %d detect progress failed: %v", taskId, err)
	}
}

func updateAssetDiscoveryProgress(task *phc.Task, index, total int) {
	if index != total && index%15 != 0 {
		return
	}
	progress := utils.Percent(index, total, 2)
	err := phc.NewTasker().UpdateAny(task.ID, map[string]any{"expend_progress": progress})
	if err != nil {
		log.Errorf("[钓鱼仿冒] Update task phishing_fake_task assets find progress failed: %v", err)
	}

	// update recommend record task progress
	err = recommend_record.NewRecommendRecordModel().
		UpdateAny(task.ExpendFlags, map[string]any{
			"progress": progress,
			"status":   utils.If(progress < 100, recommend_record.StatusDoing, recommend_record.StatusFinished),
		})
	if err != nil {
		log.Errorf("[钓鱼仿冒] Update recommend record progress failed: %v, task_id: %d, es_doc_id: %s", err, task.ID, task.ExpendFlags)
	}
}

func getDownloadFilePath(fp, name string) string {
	if fp == "" {
		return ""
	}
	en, err := utils.LaravelEncrypt(utils.DownloadFile{Url: fp, Name: name})
	if err != nil {
		return ""
	}
	return path.Join(storage.GetDownloadPrefix(), en) + utils.GetExt(fp)
}

func getChainContent(clueType int, content string) string {
	if clueType == clues.TYPE_LOGO {
		iconName, _ := utils.GetFileName(content)
		return getDownloadFilePath(content, iconName)
	}
	return content
}

func atoi(l []string) []int {
	r := make([]int, 0, len(l))
	for _, v := range l {
		if i := cast.ToInt(v); i > 0 {
			r = append(r, i)
		}
	}
	return r
}

func ixToi[T constraints.Integer](l []T) []int {
	r := make([]int, 0, len(l))
	for _, v := range l {
		r = append(r, int(v))
	}
	return r
}

func onlineByCode(code int) bool {
	return http.StatusOK <= code && code < http.StatusBadRequest
}

// 获取Hunter仿冒资产
func getHunterFakeAssets(_ context.Context, domainQuery string, task *clues.Clue, clueList []*clues.Clue, HasCloudAssets int) ([]*recommend_result.RecommendResult, error) {
	if domainQuery == "" && (task.Type == clues.TYPE_LOGO || task.Type == clues.TYPE_KEYWORD || task.Type == clues.TYPE_FID) {
		return nil, nil
	}
	// 本地化不请求
	if cfg.IsLocalClient() {
		return nil, nil
	}
	log.Infof("[钓鱼仿冒] getHunterFakeAssets: clue:%v, domainQuery:%s", task.Content, domainQuery)
	var query string
	var fakeType = recommend_result.TypeFakeTaskFake
	// Hunter API请求方式不支过滤CDN
	queryOverLength := false
	switch task.Type {
	case clues.TYPE_LOGO:
		if task.Comment == "" {
			return nil, nil
		}
		query = fmt.Sprintf(`web.icon="%s"&&ip.country!="CN"&&icp.number=""&&%s`, task.Comment, domainQuery)
	case clues.TYPE_KEYWORD:
		if task.Content == "" {
			return nil, nil
		}
		query = fmt.Sprintf(`web.title="%s"&&ip.country!="CN"&&icp.number=""&&%s`, task.Content, domainQuery)
	case clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN:
		if task.Content == "" {
			return nil, nil
		}
		query = fmt.Sprintf(`domain="%s"&&ip.country!="CN"&&icp.number=""&&%s`, task.Content, domainQuery)
	default:
		log.Warnf("[钓鱼仿冒] task type %d not support", task.Type)
		return nil, nil
	}
	var overFiled []string
	// 将超出长度的域名加入筛选列表
	log.Infof("hunter url lenth: %d, truncation: %v", len(query), len(query) > 1024)
	if len(query) > 1024 {
		queryOverLength = true
		for len(query) <= 1024 {
			idx := strings.LastIndex(query, "&&")
			if idx == -1 {
				break
			}
			if strings.Index(query[idx:], "domain.suffix!==") >= 0 {
				overFiled = append(overFiled, query[idx+19:len(query)-1])
			}
			// 缩减查询长度
			query = query[:idx]
		}
	}
	log.Infof("phishing fake assets hunter query: clue:%v , query:%s , HasCloudAssets:%d", task.Content, query, HasCloudAssets)

	list, err := getAssetsByHunter(context.TODO(), query)
	if err != nil {
		return nil, err
	}

	var data = make([]*recommend_result.RecommendResult, 0, len(list))
outList:
	for _, v := range list {
		// 如果查询超长，执行二次筛选
		if queryOverLength {
			for _, f := range overFiled {
				if v.GetDomain() == f {
					continue outList
				}
			}
		}
		url := utils.If(v.GetLink() != "", v.GetLink(), v.GetHost())
		isCertValid, _ := strconv.ParseBool(v.GetCertsValid())
		iconHash, _ := strconv.ParseInt(v.GetIconHash(), 10, 64)
		// 仿冒目标为空时,使用上一级的企业名称
		if task.ClueCompanyName == "" {
			for cx := range clueList {
				if clueList[cx].Id == task.ParentId {
					task.ClueCompanyName = clueList[cx].ClueCompanyName
					break
				}
			}
		}
		log.Infof("phishing fake assets: clue:%v, ip:%s,port:%d,url:%s", task.Content, v.GetIp(), cast.ToInt(v.GetPort()), url)
		data = append(data, &recommend_result.RecommendResult{
			Ip:              utils.IPExpanded(v.GetIp()),
			Port:            cast.ToInt(v.GetPort()),
			Protocol:        v.GetProtocol(),
			BaseProtocol:    v.GetBaseProtocol(),
			Title:           v.GetTitle(),
			Url:             url,
			Subdomain:       utils.DomainFromUrl(url),
			Domain:          v.GetDomain(),
			FakeCompany:     task.ClueCompanyName,
			Cert:            strings.Join(utils.GetCert(v.GetCert(), false), " "),
			CertRaw:         v.GetCert(),
			CertsValid:      isCertValid,
			FakeType:        fakeType,
			Fid:             v.GetFid(),
			AssetsFrom:      recommend_result.AssetFromDefault,
			Logo:            recommend_result.NewLogo(v.GetIconHash(), storage.SaveIco(v.GetIcon(), iconHash, true)),
			Icp:             v.GetIcp(),
			ISP:             v.GetIsp(),
			Server:          v.GetServer(),
			IsIPv6:          utils.IsIPv6(v.GetIp()),
			CloudName:       v.GetCloudName(),
			ThreatenType:    cast.ToInt(getThreatenType(fakeType)),
			IsFakeAssets:    true,
			SourceUpdatedAt: v.GetLastupdatetime() + " 00:00:00",
			Status:          1,
			Level:           4,
			FakeDeep:        false,
			Reason: []recommend_result.RecommendReason{{
				Id:              int(task.Id),
				Type:            task.Type,
				Content:         task.Content,
				GroupId:         int(task.GroupId),
				ClueCompanyName: task.ClueCompanyName,
				Source:          task.Source,
			}},
			FakeAssetFrom: "hunter",
		})
	}
	return data, nil
}

var HunterLimiter = rate.NewLimiter(rate.Every(time.Second*4), 1)

func getAssetsByHunter(ctx context.Context, query string) ([]*core.Asset, error) {
	var err error
	var data = make([]*core.Asset, 0, 100)
	req := &core.HunterQueryRequest{
		Search:     query,
		Page:       1,
		PageSize:   100,
		StartTime:  time.Now().AddDate(0, -1, -15).Format(time.DateOnly),
		EndTime:    time.Now().Format(time.DateOnly),
		IsWeb:      1,
		StatusCode: 0,
		PortFilter: false,
	}

	rsp := &core.FofaQueryResponse{}
	retry := false
req:
	// 限流
	if err = HunterLimiter.Wait(ctx); err != nil {
		log.Errorf("[钓鱼仿冒] get hunter rate limit err: %s", err.Error())
	}
	rsp, err = core.GetProtoCoreClient().HunterQuery(ctx, req, microx.SetTimeout(30, 29)...)
	// 获取失败时,重试一次
	if err != nil && !retry {
		retry = true
		goto req
	}
	if err != nil {
		log.Errorf("[钓鱼仿冒]Get hunter data failed with query: %s, err: %s", query, err.Error())
		return nil, err
	}
	log.Infof("[钓鱼仿冒]Get hunter data with query: %s，hunter-rsp-num:%d", query, rsp.Size)
	data = append(data, rsp.GetSdata()...)
	time.Sleep(300 * time.Millisecond)
	return data, nil
}

// 获取Hunter仿冒资产
func getQuakeFakeAssets(_ context.Context, domainQuery string, task *clues.Clue, clueList []*clues.Clue, HasCloudAssets int) ([]*recommend_result.RecommendResult, error) {
	if domainQuery == "" && (task.Type == clues.TYPE_LOGO || task.Type == clues.TYPE_KEYWORD || task.Type == clues.TYPE_FID) {
		return nil, nil
	}
	// 本地化不请求
	if cfg.IsLocalClient() {
		return nil, nil
	}
	log.Infof("[钓鱼仿冒] getQuakeFakeAssets: clue:%v, domainQuery:%s", task.Content, domainQuery)
	var query string
	var fakeType = recommend_result.TypeFakeTaskFake
	switch task.Type {
	case clues.TYPE_LOGO:
		if task.Comment == "" {
			return nil, nil
		}
		query = fmt.Sprintf(`favicon: "%s" AND NOT country:"CN" AND (service: "http" OR service: "http/ssl") AND %s`, task.Comment, domainQuery)
	case clues.TYPE_KEYWORD:
		query = fmt.Sprintf(`title: "%s" AND NOT country:"CN" AND (service: "http" OR service: "http/ssl") AND %s`, task.Content, domainQuery)
	case clues.TYPE_DOMAIN, clues.TYPE_SUBDOMAIN:
		// Quake 不查询相似域名
		return nil, nil
	default:
		log.Warnf("[钓鱼仿冒] task type %d not support", task.Type)
		return nil, nil
	}

	log.Infof("phishing fake assets quake query: clue:%v , query:%s , HasCloudAssets:%d", task.Content, query, HasCloudAssets)

	list, err := getAssetsByQuake(context.TODO(), query, uint32(HasCloudAssets))
	if err != nil {
		return nil, err
	}

	var data = make([]*recommend_result.RecommendResult, 0, len(list))

	for _, v := range list {
		url := utils.If(v.GetLink() != "", v.GetLink(), v.GetHost())
		isCertValid, _ := strconv.ParseBool(v.GetCertsValid())
		iconHash, _ := strconv.ParseInt(v.GetIconHash(), 10, 64)
		// 仿冒目标为空时,使用上一级的企业名称
		if task.ClueCompanyName == "" {
			for cx := range clueList {
				if clueList[cx].Id == task.ParentId {
					task.ClueCompanyName = clueList[cx].ClueCompanyName
					break
				}
			}
		}
		log.Infof("phishing fake assets: clue:%v, ip:%s,port:%d,url:%s", task.Content, v.GetIp(), cast.ToInt(v.GetPort()), url)
		data = append(data, &recommend_result.RecommendResult{
			Ip:              utils.IPExpanded(v.GetIp()),
			Port:            cast.ToInt(v.GetPort()),
			Protocol:        v.GetProtocol(),
			BaseProtocol:    v.GetBaseProtocol(),
			Title:           v.GetTitle(),
			Url:             url,
			Subdomain:       utils.DomainFromUrl(url),
			Domain:          v.GetDomain(),
			FakeCompany:     task.ClueCompanyName,
			Cert:            strings.Join(utils.GetCert(v.GetCert(), false), " "),
			CertRaw:         v.GetCert(),
			CertsValid:      isCertValid,
			FakeType:        fakeType,
			Fid:             v.GetFid(),
			AssetsFrom:      recommend_result.AssetFromDefault,
			Logo:            recommend_result.NewLogo(v.GetIconHash(), storage.SaveIco(v.GetIcon(), iconHash, true)),
			Icp:             v.GetIcp(),
			ISP:             v.GetIsp(),
			Server:          v.GetServer(),
			IsIPv6:          utils.IsIPv6(v.GetIp()),
			CloudName:       v.GetCloudName(),
			ThreatenType:    cast.ToInt(getThreatenType(fakeType)),
			IsFakeAssets:    true,
			SourceUpdatedAt: v.GetLastupdatetime(),
			Status:          1,
			Level:           4,
			FakeDeep:        false,
			Reason: []recommend_result.RecommendReason{{
				Id:              int(task.Id),
				Type:            task.Type,
				Content:         task.Content,
				GroupId:         int(task.GroupId),
				ClueCompanyName: task.ClueCompanyName,
				Source:          task.Source,
			}},
			FakeAssetFrom: "quake",
		})
	}
	return data, nil
}

var QuakeLimiter = rate.NewLimiter(rate.Every(time.Second), 1)

func getAssetsByQuake(ctx context.Context, query string, hasCloudAssets uint32) ([]*core.Asset, error) {
	var err error
	var data = make([]*core.Asset, 0, 1000)
	req := &core.HunterQueryRequest{
		Search:     query,
		Page:       1,
		PageSize:   100,
		StartTime:  time.Now().AddDate(0, -1, -15).Format(time.DateOnly),
		EndTime:    time.Now().Format(time.DateOnly),
		IsWeb:      hasCloudAssets,
		StatusCode: 0,
		PortFilter: false,
	}

	rsp := &core.FofaQueryResponse{}
	retry := false
req:
	if err = QuakeLimiter.Wait(ctx); err != nil {
		log.Errorf("[钓鱼仿冒] get quake rate limit err: %s", err.Error())
	}
	rsp, err = core.GetProtoCoreClient().QuakeQuery(ctx, req, microx.SetTimeout(30, 29)...)
	// 获取失败时,重试一次
	if err != nil && !retry {
		retry = true
		goto req
	}
	if err != nil {
		log.Errorf("[钓鱼仿冒]Get quake data failed with query: %s, err: %s", query, err.Error())
		return nil, err
	}
	log.Infof("[钓鱼仿冒]Get quake data with query: %s，quake-rsp-num:%d", query, rsp.Size)
	data = append(data, rsp.GetSdata()...)
	time.Sleep(300 * time.Millisecond)
	return data, nil
}
