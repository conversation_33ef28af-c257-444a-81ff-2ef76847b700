package phishing_fake

import (
	"encoding/json"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql/phishing_fake_task"
	"testing"

	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"
)

func Test_getClues(t *testing.T) {
	initCfg()

	task := &phishing_fake_task.Task{UserId: 1, GroupId: 1}
	_, err := getClues(task)
	assert.Nil(t, err)
}

func Test_mapDecode(t *testing.T) {
	m := make(map[string]string, 5)
	m["fake_company"] = "beijing"
	m["fake_icp_company"] = "google"
	m["port"] = "8080"
	m["source_updated_at"] = "2020-01-01 00:00:00"
	bs, _ := json.Marshal(m)

	var item = &recommend_result.RecommendResult{}
	err := json.Unmarshal(bs, item)
	assert.Nil(t, err)
	assert.Equal(t, "beijing", item.FakeCompany)
	assert.Equal(t, "google", item.FakeIcpCompany)
	assert.Equal(t, "8080", item.Port)
	assert.Equal(t, "2020-01-01 00:00:00", cast.ToString(item.SourceUpdatedAt))
}
