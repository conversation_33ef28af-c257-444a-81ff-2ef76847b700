package phishing_fake

import (
	"fmt"
	"micro-service/pkg/log"
	"strconv"
	"testing"

	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"

	"github.com/stretchr/testify/assert"
)

func initCfg() {
	cfg.InitLoadCfg()
	log.Init()
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

//func Test_getFofaData(t *testing.T) {
//	initCfg()
//
//	q := `host="www.vegantheworld.com" && is_domain=true`
//	list, err := getAssetsByFOFA(context.Background(), q)
//	assert.Nil(t, err)
//	if len(list) > 10 {
//		list = list[:10]
//	}
//	for _, v := range list {
//		fmt.Printf("domain: %s, link: %s, host: %s, protocol: %s, status_code: %s\n",
//			v.GetDomain(), v.GetLink(), v.GetHost(), v.GetProtocol(), v.GetStatusCode())
//	}
//}

func Test_checkUserTask(t *testing.T) {
	initCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())

	_, err := checkUserTask(1, 1)
	assert.NotNil(t, err)
}

func Test_domainsToQuery(t *testing.T) {
	domains := []string{"baidu.com", "google.com"}
	q := domainsToQuery(domains)
	q = fmt.Sprintf(`icon_hash==%q && domain!="" && %s`, strconv.Itoa(-1000), q)
	fmt.Println(q)
}

//func TestDetect(t *testing.T) {
//	req := &crawler.UrlDetectRequest{
//		Urls:       []string{},
//		Timeout:    10,
//		SkipVerify: true,
//	}
//	req.Urls = append(req.Urls, "http://www.baidu.com")
//	req.Urls = append(req.Urls, "http://www.ydsc.com.cn.cixs.cn")
//	req.Urls = append(req.Urls, "https://www.crcacorp.com")
//
//	rsp, err := crawler.GetProtoClient().UrlDetect(context.TODO(), req)
//	assert.Nil(t, err)
//	fmt.Println(rsp)
//}
