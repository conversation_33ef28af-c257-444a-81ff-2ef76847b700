package phishing_fake

import (
	"context"
	"go.uber.org/zap"
	"micro-service/initialize/es"
	mysqllib "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	web "micro-service/webService/proto"
	"testing"
	"time"
)

func initTest() {
	cfg.InitLoadCfg()
	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	_ = mysqllib.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	es.GetInstance(cfg.LoadElastic())
}

//func Test_urlDetect(t *testing.T) {
//	initTest()
//
//	task, err := phishing_fake_task.NewTasker().First(mysql.WithId(565))
//	assert.NoError(t, err, assert.FailNow(t, err.Error()))
//
//	list, err := getRecommendResult(&task, &pb.CounterfeitAssetListRequest{})
//	assert.Nil(t, err, assert.FailNow(t, err.Error()))
//
//	m := urlDetect(context.TODO(), list, task.ID, true, 1)
//	fmt.Println(m)
//}

//func Test_AssetList(t *testing.T) {
//	initTest()
//
//	task, err := phishing_fake_task.NewTasker().First(mysql.WithId(109))
//	assert.NoError(t, err)
//
//	rsp := &pb.CounterfeitAssetListResponse{}
//	err = CounterfeitAssetList(context.TODO(), &pb.CounterfeitAssetListRequest{UserId: task.UserId, TaskId: task.ID, Page: 1, PerPage: 10}, rsp)
//	if err != nil {
//		panic(err)
//	}
//	for x := range rsp.Items {
//		fmt.Println(rsp.Items[x])
//	}
//}

func TestAsyncAssetsFind(t *testing.T) {
	initTest()
	req := web.CounterfeitAssetFindRequest{
		UserId:           1,
		TaskId:           564,
		OperateCompanyId: 1,
	}
	err := CounterfeitAssetsFind(context.TODO(), &req)
	if err != nil {
		t.Error(err)
	}
	time.Sleep(10 * time.Minute)
}
