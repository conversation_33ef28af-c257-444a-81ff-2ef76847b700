package intelligence_event

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/initialize/es"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/scanService/handler/microkernel"
	"micro-service/webService/handler/user"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

var once sync.Once
var taskInfo *TaskInfo

type TaskInfo struct {
	Tasks sync.Map
}

func GetTaskInfo() *TaskInfo {
	if taskInfo == nil {
		once.Do(func() {
			taskInfo = &TaskInfo{}
		})
	}
	return taskInfo
}

func CheckTaskRunning(userId uint64) bool {
	if _, ok := GetTaskInfo().Tasks.Load(userId); ok {
		return true
	}
	return false
}

func AddTask(userId uint64) {
	GetTaskInfo().Tasks.Store(userId, 1)
}

func DeleteTask(userId uint64) {
	GetTaskInfo().Tasks.Delete(userId)
}

func GetTaskProcess(userId uint64) string {
	progress, ok := GetTaskInfo().Tasks.Load(userId)
	if !ok {
		log.Warn(fmt.Sprintf("GetTaskProcess: userId:%d,任务不存在!", userId))
		return "100"
	}
	return fmt.Sprintf("%.2f", progress)
}

func MatchEvent(userId uint64, list []*intelligence.Event) {
	defer func() {
		if err := recover(); err != nil {
			log.Warn(fmt.Sprintf("MatchEvent: userId:%d,任务异常,Error:%v!", userId, err))
			DeleteTask(userId)
		}
	}()
	total := len(list)
	log.Info(fmt.Sprintf("IntelligenceEventMatch: userId:%d,开始匹配资产!数据数量:%d", userId, total))
	isAdmin, _ := user.IsUserAdmin(userId)
	assetUpdateList := sync.Map{}
	// 获取event IDS
	ids := utils.ListDistinctNonZero(utils.ListColumn(list, func(t *intelligence.Event) uint64 { return t.ID }))
	if len(ids) == 0 {
		DeleteTask(userId)
		return
	}
	// 统计资产数量
	assetCount := make([]intelligence.DataSummary, 0)
	// 关联信息表
	riClient := intelligence.NewRelatedIntelligence()
	// 关联情报表数据
	relatedIntelligenceList := make([]*intelligence.RelatedIntelligence, 0)
	for x := range list {
		index := x + 1
		event := list[x]
		// 获取event 对应资产
		assets, err := GetAssetsByUserAndEvent(userId, isAdmin, event)
		// 更新进度
		progress := float32(index) / float32(total) * 100.0
		if progress >= 100 {
			// 防止进度超过100
			progress = 95
		}
		if cast.ToFloat32(GetTaskProcess(userId)) < progress && cast.ToInt(progress) != 100 {
			log.Infof(fmt.Sprintf("userID:%d,MatchEvent进度:%s", userId, cast.ToString(progress)))
			GetTaskInfo().Tasks.Store(userId, progress)
		}
		if err != nil {
			DeleteTask(userId)
			log.Warn("MatchEvent userId:%d,EventId:%d 获取资产失败:%v", userId, event.ID, err)
			return
		}
		// 生成资产,Event ID,更新条件
		for _, asset := range assets {
			validData := false
			if maps, ok := assetUpdateList.Load(asset.ID); !ok {
				validData = true
				if !utils.ListContains(asset.EventId, event.ID) {
					asset.EventId = append(asset.EventId, event.ID)
				}
				assetUpdateList.Store(asset.ID, map[string]any{"id": asset.ID, "event_id": asset.EventId})
			} else {
				canAdd := true
				eventIds := maps.(map[string]any)["event_id"].([]uint64)
				for vx := range eventIds {
					if cast.ToUint64(eventIds[vx]) == event.ID {
						canAdd = false
						continue
					}
				}
				if canAdd {
					validData = true
					maps.(map[string]any)["event_id"] = append(maps.(map[string]any)["event_id"].([]uint64), event.ID)
				}
			}
			if validData {
				foundTime := time.Now().Format("2006-01-02 15:04:05")
				query := make([]mysql.HandleFunc, 0)
				query = append(query, mysql.WithColumnValue("intelligence_id", event.ID))
				query = append(query, mysql.WithColumnValue("risk_type", 3))
				query = append(query, mysql.WithColumnValue("user_id", userId))
				query = append(query, mysql.WithColumnValue("asset_ip", asset.Ip))
				switch asset.Port.(type) {
				case float64:
					query = append(query, mysql.WithColumnValue("asset_port", cast.ToString(asset.Port.(float64))))
				case string:
					query = append(query, mysql.WithColumnValue("asset_port", asset.Port.(string)))
				}
				existRi, err := riClient.FindBy(query...)
				if err != nil {
					if err != gorm.ErrRecordNotFound {
						log.Warn(fmt.Sprintf("MatchEvent: userId:%d,查询关联情报表数据失败,Error:%v!", userId, err))
					}
				} else {
					foundTime = existRi.FoundTime
				}
				// 生成关联情报表数据
				ri := &intelligence.RelatedIntelligence{
					IntelligenceID: event.ID, UserID: userId, EnterpriseID: uint64(asset.CompanyID),
					AssetID: asset.ID, AssetIP: asset.Ip, AssetPort: fmt.Sprintf("%v", asset.Port),
					AssetProtocol: asset.Protocol, AssetURL: asset.Url, AssetTitle: fmt.Sprintf("%v", asset.Title), AssetStatus: fmt.Sprintf("%v", asset.Status),
					SpecialProjectName: event.Name, RiskName: event.Name, RiskType: "3", IntelligenceType: event.Category,
					FoundTime: foundTime, UpdateTime: time.Now().Format("2006-01-02 15:04:05"),
				}
				if asset.ClueCompanyName != nil && len(asset.ClueCompanyName.([]interface{})) > 0 {
					if asset.ClueCompanyName.([]interface{})[0] != nil {
						ri.EnterpriseName = asset.ClueCompanyName.([]interface{})[0].(string)
					}
				}
				ri.ServiceComponent = strings.Join(utils.ListColumn(asset.RuleTags, func(t foradar_assets.RuleTag) string { return t.CnProduct }), ",")
				relatedIntelligenceList = append(relatedIntelligenceList, ri)

				// 统计资产和实体数量，注意不同资产对应实体可能相同
				exist := false
				if len(assetCount) > 0 {
					for _, v := range assetCount {
						if v.SpecialProjectName == event.Name {
							exist = true
							v.AssetNum++
							if asset.ClueCompanyName != nil && len(asset.ClueCompanyName.([]interface{})) > 0 {
								v.CompanyList = append(v.CompanyList, asset.ClueCompanyName.([]interface{})[0].(string))
								v.CompanyList = utils.ListDistinctNonZero(v.CompanyList)
								v.CompanyNum = len(v.CompanyList)
							}
						}
					}
				}
				if !exist {
					ds := intelligence.DataSummary{
						SpecialProjectName: event.Name,
						AssetNum:           1,
					}
					if asset.ClueCompanyName != nil && len(asset.ClueCompanyName.([]interface{})) > 0 {
						ds.CompanyList = []string{asset.ClueCompanyName.([]interface{})[0].(string)}
						ds.CompanyNum = 1
					} else {
						ds.CompanyList = []string{}
						ds.CompanyNum = 0
					}
					assetCount = append(assetCount, ds)
				}
			}
		}
	}
	// 删除关联情报表数据
	query := make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithColumnValue("risk_type", 3))
	query = append(query, mysql.WithColumnValue("user_id", userId))
	idList := utils.ListColumn(list, func(t *intelligence.Event) uint64 { return t.ID })
	query = append(query, mysql.WithValuesIn("intelligence_id", idList))
	err := riClient.DeleteBy(query...)
	if err != nil {
		log.Warn(fmt.Sprintf("MatchEvent: userId:%d,删除关联情报表数据失败,Error:%v", userId, err.Error()))
	}

	// 插入关联情报表数据
	if err := riClient.BatchCreate(relatedIntelligenceList); err != nil {
		log.Warn(fmt.Sprintf("MatchEvent: userId:%d,创建关联情报表数据失败,Error:%v", userId, err.Error()))
	}
	// 更新关联资产数量
	for _, v := range assetCount {
		if err := intelligence.NewEvent().UpdateRelatedAssetCount(v.SpecialProjectName, v.AssetNum, v.CompanyNum, strings.Join(v.CompanyList, ",")); err != nil {
			log.Warn(fmt.Sprintf("MatchEvent: userId:%d,更新资产数量失败,Error:%v", userId, err.Error()))
		}
	}
	time.Sleep(2 * time.Second)
	GetTaskInfo().Tasks.Store(userId, 100)
	DeleteTask(userId)
	log.Info(fmt.Sprintf("MatchEvent: userId:%d,任务完成!", userId))
}

func GetAssetsByUserAndEvent(userId uint64, isAdmin bool, event *intelligence.Event) ([]foradar_assets.ForadarAsset, error) {
	assets := make([]foradar_assets.ForadarAsset, 0)
	log.Info(fmt.Sprintf("GetAssetsByUserAndEvent: user_id:%d,event_id:%d,event_name:%s", userId, event.ID, event.Name))
	fofaQuery, err := microkernel.GetFOFAParse(utils.QBase64(event.FofaQuery))
	if err != nil {
		return assets, err
	}
	var boolQuery *elastic.BoolQuery
	if !isAdmin {
		boolQuery = microkernel.GetInstance().NewQuery(fofaQuery.Query, userId)
	} else {
		boolQuery = microkernel.GetInstance().NewQuery(fofaQuery.Query)
	}
	query := es.GetInstance(cfg.LoadElastic()).Search(fofaQuery.Index...).Query(boolQuery)
	result, err := query.Aggregation("ip", elastic.NewTermsAggregation().Field("ip").Size(1000000)).Do(context.TODO())
	if err != nil {
		return assets, err
	}
	// 获取 fofaee_subdomain,fofaee_service 中的 ip 聚合信息
	aggResult, found := result.Aggregations.Terms("ip")
	if !found {
		return assets, nil
	}
	log.Info(fmt.Sprintf("GetAssetsByUserAndEvent: index:%s,count:%d", strings.Join(fofaQuery.Index, ","), len(aggResult.Buckets)))
	ipSplit := utils.ListSplit(utils.ListDistinctNonZero(utils.ListColumn(aggResult.Buckets, func(t *elastic.AggregationBucketKeyItem) string { return t.Key.(string) })), 5000)
	// 循环内做两件事，1.获取foradar_assets索引中的资产信息 2.分析fofaee_subdomain,fofaee_service 查询结果中 ip 对应的端口信息
	for x := range ipSplit {
		ips := utils.ListColumn(ipSplit[x], func(t string) any { return t })
		// log.Info(fmt.Sprintf("GetAssetsByUserAndEvent: index:%s,ips:%s", strings.Join(fofaQuery.Index, ","), strings.Join(ipSplit[x], ",")))
		assetQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("ip", ips...)).
			Must(elastic.NewTermsQuery("status", foradar_assets.StatusUploadAsset, foradar_assets.StatusConfirmAsset))
		if !isAdmin {
			assetQuery = assetQuery.Must(elastic.NewTermsQuery("user_id", userId))
		}
		// 获取foradar_assets索引中的资产信息
		l, err := foradar_assets.NewForadarAssetModel().ListAll(context.TODO(), assetQuery)
		if err != nil {
			return assets, err
		}
		log.Info(fmt.Sprintf("GetAssetsByUserAndEvent: foradar_assets_ips:%s", strings.Join(utils.ListDistinct(utils.ListColumn(l, func(t foradar_assets.ForadarAsset) string { return t.Ip })), ",")))
		// 遍历foradar_assets索引中的资产信息，同时匹配fofaee_subdomain,fofaee_service 中的 ip和端口信息
		for _, asset := range l {
			// log.Info(fmt.Sprintf("asset: ip:%s,port:%v,id:%s", asset.Ip, asset.Port, asset.ID))
			// 获取fofaee_subdomain,fofaee_service 中的 ip 对应的端口信息
			portmatch := false
			for _, hit := range result.Hits.Hits {
				var doc Document
				bytes, err := hit.Source.MarshalJSON()
				if err != nil {
					log.Warn("Error marshalling document: %s", err)
					continue
				}
				err = json.Unmarshal(bytes, &doc)
				if err != nil {
					log.Fatalf("Error unmarshalling document: %s", err)
					continue
				}
				// 匹配 port
				var assetPort string
				switch asset.Port.(type) {
				case float64:
					assetPort = cast.ToString(asset.Port.(float64))
				case string:
					assetPort = asset.Port.(string)
				}
				if doc.IP == asset.Ip && cast.ToString(doc.Port) == assetPort {
					portmatch = true
					break
				}
			}
			if portmatch {
				assets = append(assets, asset)
			}
		}
	}
	log.Info(fmt.Sprintf("GetAssetsByUserAndEvent: foradar_assets_count:%d", len(assets)))
	return assets, nil
}

type Document struct {
	IP   string `json:"ip"`
	Port int    `json:"port"`
}
