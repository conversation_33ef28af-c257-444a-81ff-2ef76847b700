package handler

import (
	"context"
	"fmt"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/auth_access_client"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/pkg/cfg"
	"micro-service/pkg/excel"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/intelligence_data"
	"micro-service/webService/handler/intelligence_event"
	"micro-service/webService/handler/intelligence_poc"
	"micro-service/webService/handler/intelligence_threat"
	"micro-service/webService/handler/user"
	pb "micro-service/webService/proto"
	"path"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"go-micro.dev/v4/errors"
)

// IntelligenceCount 情报管理-统计
func (*Web) IntelligenceCount(ctx context.Context, req *pb.Empty, rsp *pb.IntelligenceCountResponse) error {
	// 模块名称和对应的模块实例映射
	modules := map[string]intelligence.Module{
		"HotPoc": intelligence.NewHotPoc(),
		"Event":  intelligence.NewEvent(),
		"Threat": intelligence.NewThreat(),
		"Data":   intelligence.NewDataSummary(),
		"Fake":   intelligence.NewFake(),
		"Other":  intelligence.NewOther(),
	}

	rsp.Items = make([]*pb.IntelligenceCountItem, 0, len(modules))

	// 创建一个等待组，以等待所有 goroutine 完成
	var wg sync.WaitGroup
	wg.Add(len(modules))

	// 启动一个 goroutine 来并发地获取每个模块的计数
	for moduleName, module := range modules {
		go func(name string, mod intelligence.Module) {
			defer wg.Done()

			count, err := mod.Count()
			if err != nil {
				count = 0
			}
			rsp.Items = append(rsp.Items, &pb.IntelligenceCountItem{
				Module: name,
				Count:  count,
			})
		}(moduleName, module)
	}
	wg.Wait()
	return nil
}

// IntelligenceUserThreatList 情报中心-威胁情报-列表
func (*Web) IntelligenceUserThreatList(ctx context.Context, req *pb.IntelligenceUserThreatListRequest, rsp *pb.IntelligenceUserThreatListResponse) error {
	queries := make([]mysql.HandleFunc, 0)
	if req.Url != "" {
		queries = append(queries, mysql.WithLike("url", req.Url))
	}
	if req.Ip != "" {
		queries = append(queries, mysql.WithLike("ip", req.Ip))
	}
	if req.Country != "" {
		queries = append(queries, mysql.WithWhere("country", req.Country))
	}
	if req.Status != 0 {
		queries = append(queries, mysql.WithWhere("status", req.Status))
	}
	if req.Source != "" {
		queries = append(queries, mysql.WithWhere("source", req.Source))
	}
	if req.Type != "" {
		queries = append(queries, mysql.WithWhere("type", req.Type))
	}
	if req.Hit != 0 {
		queries = append(queries, mysql.WithWhere("hit", req.Hit))
	}
	if req.Tags != "" {
		queries = append(queries, mysql.WithWhere("tags", req.Tags))
	}
	if req.Domain != "" {
		queries = append(queries, mysql.WithLike("domain", req.Domain))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("found_at", req.FoundAt))
	}
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("intelligence_threat.created_at", req.CreatedAt))
	}
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("intelligence_threat.updated_at", req.UpdatedAt))
	}
	queries = append(queries, mysql.WithOrder("intelligence_user_threat.updated_at desc,intelligence_threat.id desc"))
	list, count, err := intelligence.NewUserThreat().List(cast.ToInt(req.Page), cast.ToInt(req.PerPage), req.UserId, queries...)
	if err != nil {
		return err
	}
	rsp.CurrentPage = cast.ToInt64(req.Page)
	rsp.Total = count
	rsp.Page = req.PerPage
	for x := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceThreat{
			Id:        list[x].Threat.Id,
			Ip:        list[x].Threat.Ip,
			Source:    list[x].Threat.Source,
			Url:       list[x].Threat.Url,
			Country:   list[x].Threat.Country,
			Status:    cast.ToUint32(list[x].Threat.Status),
			FoundAt:   list[x].Threat.FoundAt.Format("2006-01-02 15:04:05"),
			CreatedAt: list[x].Threat.CreatedAt.Format("2006-01-02 15:04:05"),
			Type:      list[x].Threat.Type,
			Tags:      list[x].Threat.Tags,
			Domain:    list[x].Threat.Domain,
			Hit:       cast.ToUint32(list[x].UserThreat.Hit),
		})
	}
	return nil
}

// IntelligenceUserThreatMatch 情报中心-威胁情报-列表
func (*Web) IntelligenceUserThreatMatch(ctx context.Context, req *pb.IntelligenceUserThreatMatchRequest, _ *pb.Empty) error {
	if intelligence_threat.CheckTaskRunning(req.UserId) {
		return errors.BadRequest(pb.ServiceName, "任务正在运行")
	}
	intelligence_threat.AddTask(req.UserId)
	list := make([]*intelligence.Threat, 0)
	req.Id = utils.ListDistinctNonZero(req.Id)
	queries := make([]mysql.HandleFunc, 0)
	if req.Url != "" {
		queries = append(queries, mysql.WithLike("url", req.Url))
	}
	if req.Ip != "" {
		queries = append(queries, mysql.WithLike("ip", req.Ip))
	}
	if req.Country != "" {
		queries = append(queries, mysql.WithWhere("country", req.Country))
	}
	if req.Status != 0 {
		queries = append(queries, mysql.WithWhere("status", req.Status))
	}
	if req.Source != "" {
		queries = append(queries, mysql.WithWhere("source", req.Source))
	}
	if req.Type != "" {
		queries = append(queries, mysql.WithWhere("type", req.Type))
	}
	if req.Hit != 0 {
		queries = append(queries, mysql.WithWhere("hit", req.Hit))
	}
	if req.Tags != "" {
		queries = append(queries, mysql.WithWhere("tags", req.Tags))
	}
	if req.Domain != "" {
		queries = append(queries, mysql.WithLike("domain", req.Domain))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("found_at", req.FoundAt))
	}
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("created_at", req.CreatedAt))
	}
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("updated_at", req.UpdatedAt))
	}
	if len(req.Id) > 0 {
		if l, err := intelligence.NewThreat().ListAll(mysql.WithValuesIn("id", req.Id)); err != nil {
			intelligence_threat.DeleteTask(req.UserId)
			return err
		} else {
			list = append(list, l...)
		}
		intelligence_threat.MatchThreat(req.UserId, list)
	} else {
		if l, err := intelligence.NewThreat().ListAll(queries...); err != nil {
			intelligence_threat.DeleteTask(req.UserId)
			return err
		} else {
			list = append(list, l...)
		}
		go intelligence_threat.MatchThreat(req.UserId, list)
	}
	return nil
}

// IntelligenceUserThreatMatchProcess 情报中心-威胁情报-匹配进度
func (*Web) IntelligenceUserThreatMatchProcess(ctx context.Context, req *pb.IntelligenceUserThreatMatchProcessRequest, rsp *pb.IntelligenceUserThreatMatchProcessResponse) error {
	rsp.Process = cast.ToFloat32(intelligence_threat.GetTaskProcess(req.UserId))
	if cast.ToInt(rsp.Process) == 100 {
		intelligence_threat.DeleteTask(req.UserId)
		time.Sleep(1 * time.Second)
	}
	return nil
}

// IntelligenceOtherUserList 情报中心-威胁情报-列表
func (*Web) IntelligenceOtherUserList(ctx context.Context, req *pb.IntelligenceOtherUserListRequest, rsp *pb.IntelligenceOtherUserListResponse) error {
	queries := make([]mysql.HandleFunc, 0)
	if !cfg.IsLocalClient() && req.CompanyId != 0 {
		if companyInfo, err := company.NewCompanyModel().First(mysql.WithWhere("id", req.CompanyId)); err == nil {
			queries = append(queries, mysql.WithWhere("(FIND_IN_SET(?,company) or is_public = ?)", companyInfo.Name, intelligence.IsPublicTrue))
		}
	}
	if req.Url != "" {
		queries = append(queries, mysql.WithLike("url", req.Url))
	}
	if req.Keyword != "" {
		queries = append(queries, mysql.WithLike("keyword", req.Keyword))
	}
	if req.Title != "" {
		queries = append(queries, mysql.WithLike("title", req.Title))
	}
	if req.Company != "" {
		queries = append(queries, mysql.WithLike("company", req.Company))
	}
	if req.Platform != "" {
		queries = append(queries, mysql.WithWhere("platform", req.Platform))
	}
	if req.ArticleId != "" {
		queries = append(queries, mysql.WithWhere("article_id", req.ArticleId))
	}
	if req.ArticleContext != "" {
		queries = append(queries, mysql.WithLike("article_context", req.ArticleContext))
	}
	if req.Poster != "" {
		queries = append(queries, mysql.WithLike("poster", req.Poster))
	}
	if len(req.ArticleCreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("article_created_at", req.ArticleCreatedAt))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("found_at", req.FoundAt))
	}
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("created_at", req.CreatedAt))
	}
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("updated_at", req.UpdatedAt))
	}
	queries = append(queries, mysql.WithOrder("id desc"))
	list, count, err := intelligence.NewOther().List(cast.ToInt(req.Page), cast.ToInt(req.PerPage), queries...)
	if err != nil {
		return err
	}
	rsp.CurrentPage = cast.ToInt64(req.Page)
	rsp.Total = count
	rsp.Page = req.PerPage
	for x := range list {
		// 处理如果是Screenshot包含字符 pp/public ,再转义图片地址，不包含的话原样输出
		imageUrl := list[x].Screenshot
		if strings.Contains(imageUrl, "public") {
			imageUrl = storage.GenAPIDownloadPath(filepath.Base(list[x].Screenshot), list[x].Screenshot)
		}
		rsp.Items = append(rsp.Items, &pb.IntelligenceOther{
			Id:               list[x].Id,
			Url:              list[x].Url,
			FoundAt:          list[x].FoundAt.Format("2006-01-02 15:04:05"),
			Company:          list[x].Company,
			Poster:           list[x].Poster,
			Platform:         list[x].Platform,
			Keyword:          list[x].Keyword,
			Title:            list[x].Title,
			Screenshot:       imageUrl,
			Sample:           storage.GenAPIDownloadPath(filepath.Base(list[x].Sample), list[x].Sample),
			SampleFileName:   filepath.Base(list[x].Sample),
			IsPublic:         cast.ToUint32(list[x].IsPublic),
			ArticleId:        list[x].ArticleId,
			ArticleContext:   list[x].ArticleContext,
			ArticleCreatedAt: list[x].ArticleCreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return nil
}

// IntelligenceFakeUserList 仿冒列表
func (*Web) IntelligenceFakeUserList(ctx context.Context, req *pb.IntelligenceFakeUserListRequest, rsp *pb.IntelligenceFakeUserListResponse) error {
	queries := make([]mysql.HandleFunc, 0)
	if req.Url != "" {
		queries = append(queries, mysql.WithLike("url", req.Url))
	}
	if req.Title != "" {
		queries = append(queries, mysql.WithLike("title", req.Title))
	}
	if req.Ip != "" {
		queries = append(queries, mysql.WithLike("ip", req.Ip))
	}
	if req.CloudName != "" {
		queries = append(queries, mysql.WithLike("cloud_name", req.CloudName))
	}
	if req.Country != "" {
		queries = append(queries, mysql.WithWhere("country", req.Country))
	}
	if req.Status != 0 {
		queries = append(queries, mysql.WithWhere("status", req.Status))
	}
	if req.Source != "" {
		queries = append(queries, mysql.WithWhere("source", req.Source))
	}
	if req.Target != "" {
		queries = append(queries, mysql.WithWhere("target", req.Target))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("found_at", req.FoundAt))
	}
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("created_at", req.CreatedAt))
	}
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("updated_at", req.UpdatedAt))
	}
	sort := "id DESC"
	if len(req.Sort) > 0 {
		sort += ","
		sort += strings.Join(req.Sort, ",")
	}
	queries = append(queries, mysql.WithOrder(sort))
	list, count, err := intelligence.NewFake().List(cast.ToInt(req.Page), cast.ToInt(req.PerPage), queries...)
	if err != nil {
		return err
	}
	rsp.CurrentPage = cast.ToInt64(req.Page)
	rsp.Total = count
	rsp.Page = req.PerPage
	for x := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceFake{
			Id:        list[x].Id,
			Ip:        list[x].Ip,
			Target:    list[x].Target,
			Source:    list[x].Source,
			Url:       list[x].Url,
			Title:     list[x].Title,
			CloudName: list[x].CloudName,
			Country:   list[x].Country,
			Status:    cast.ToUint32(list[x].Status),
			FoundAt:   list[x].FoundAt.Format("2006-01-02 15:04:05"),
			CreatedAt: list[x].CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	return nil
}

// IntelligenceHotPocCountByLevel 情报中心-热点POC-级别数量
func (*Web) IntelligenceHotPocCountByLevel(ctx context.Context, _ *pb.Empty, rsp *pb.IntelligenceHotPocCountByLevelResponse) error {
	list, err := intelligence.NewHotPoc().CountByLevel()
	if err != nil {
		return err
	}
	// 初始化总和为0
	sum := int64(0)
	// 遍历 map，并将值相加
	for _, value := range list {
		sum += value
	}
	rsp.Items = make([]*pb.IntelligenceHotPocCountInfo, 0)
	for x := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceHotPocCountInfo{
			Level:   x,
			Count:   list[x],
			Percent: cast.ToFloat32(list[x]) / cast.ToFloat32(sum) * 100,
		})
	}
	return nil
}

// IntelligenceHotPocUserList 热点POC列表
func (*Web) IntelligenceHotPocUserList(ctx context.Context, req *pb.IntelligenceHotPocUserListRequest, rsp *pb.IntelligenceHotPocUserListResponse) error {
	queries := make([]mysql.HandleFunc, 0)
	if req.Name != "" {
		queries = append(queries, mysql.WithLike("name", req.Name))
	}
	if req.Cve != "" {
		queries = append(queries, mysql.WithWhere("cve", req.Cve))
	}
	if req.Cnnvd != "" {
		queries = append(queries, mysql.WithWhere("cnnvd", req.Cnnvd))
	}
	if req.RiskLevel != "" {
		queries = append(queries, mysql.WithWhere("risk_level", req.RiskLevel))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("intelligence_hot_poc.found_at", req.FoundAt))
	}
	req.CreatedAt = utils.FormattingTime(req.CreatedAt)
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAtOriginal("intelligence_hot_poc.created_at", req.CreatedAt))
	}
	req.UpdatedAt = utils.FormattingTime(req.UpdatedAt)
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAtOriginal("intelligence_hot_poc.updated_at", req.UpdatedAt))
	}
	if len(req.Tag) > 0 {
		s := make([]string, 0)
		for _, v := range req.Tag {
			s = append(s, "tag like '%"+v+"%'")
		}
		queries = append(queries, mysql.WithSQl(strings.Join(s, " or ")))
	}
	var sort string
	if len(req.Sort) > 0 {
		for _, s := range req.Sort {
			sort += fmt.Sprintf("intelligence_hot_poc.%s,", s)
		}
	}
	sort += "intelligence_user_hot_poc.risk_count desc,intelligence_hot_poc.found_at desc"
	queries = append(queries, mysql.WithOrder(sort))
	list, count, err := intelligence.NewUserHotPoc().List(cast.ToInt(req.Page), cast.ToInt(req.PerPage), req.UserId, queries...)
	if err != nil {
		return err
	}
	rsp.CurrentPage = cast.ToInt64(req.Page)
	rsp.Total = count
	rsp.Page = req.PerPage
	for x := range list {
		poc := &pb.IntelligenceHotPoc{
			Id:              list[x].HotPoc.Id,
			Name:            list[x].HotPoc.Name,
			Cve:             list[x].HotPoc.Cve,
			Cnnvd:           list[x].HotPoc.Cnnvd,
			ImpactProduct:   list[x].HotPoc.ImpactProduct,
			ImpactVersion:   list[x].HotPoc.ImpactVersion,
			ImpactRange:     list[x].HotPoc.ImpactRange,
			RiskLevel:       list[x].HotPoc.RiskLevel,
			Introduce:       list[x].HotPoc.Introduce,
			FofaCount:       list[x].HotPoc.FofaCount,
			FoundAt:         list[x].HotPoc.FoundAt.Format("2006-01-02 15:04:05"),
			Solution:        list[x].HotPoc.Solution,
			RiskCount:       cast.ToInt64(list[x].UserHotPoc.RiskCount),
			HasMaskedReport: list[x].Report.MaskedReportLocalLink != "",
			Tag: func() []string {
				if len(list[x].Tag) > 0 {
					return strings.Split(list[x].HotPoc.Tag, ",")
				} else {
					return []string{}
				}
			}(),
		}
		if req.GetEsFields {
			poc.FofaQuery = list[x].HotPoc.FofaQuery
			poc.EsQuery = list[x].HotPoc.ESQuery
			poc.EsIndex = list[x].HotPoc.ESIndex
			poc.EsKeywords = list[x].HotPoc.ESKeywords
		}
		rsp.Items = append(rsp.Items, poc)
	}
	return nil
}

// IntelligenceHotPocConditionResponse 情报中心-热点POC-高级筛选数据源
func (*Web) IntelligenceHotPocCondition(ctx context.Context, req *pb.IntelligenceHotPocConditionRequest, rsp *pb.IntelligenceHotPocConditionResponse) error {
	// 查询标签信息
	query := make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithWhere("user_id", req.UserId))
	pid, err := intelligence.NewUserHotPoc().DistinctColumn("poc_id", query...)
	if err != nil {
		return err
	}
	query = make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithValuesIn("id", pid))
	query = append(query, mysql.WithColumnNotNull("tag"))
	tags, err := intelligence.NewHotPoc().DistinctColumn("tag", query...)
	if err != nil {
		return err
	}
	rsp.Tag = make([]string, 0)
	tagList := make([]string, 0)
	for _, x := range tags {
		tagList = append(tagList, strings.Split(x, ",")...)
	}
	rsp.Tag = utils.ListDistinctNonZero(tagList)
	return nil
}

// IntelligenceHotPocUserUpdate 热点POC自定义风险等级
func (*Web) IntelligenceHotPocUserUpdate(ctx context.Context, req *pb.IntelligenceHotPocUserUpdateRequest, rsp *pb.Empty) error {
	queries := make([]mysql.HandleFunc, 0)
	if req.Name != "" {
		queries = append(queries, mysql.WithLike("name", req.Name))
	}
	if req.Cve != "" {
		queries = append(queries, mysql.WithWhere("cve", req.Cve))
	}
	if req.Cnnvd != "" {
		queries = append(queries, mysql.WithWhere("cnnvd", req.Cnnvd))
	}
	if req.RiskLevel != "" {
		queries = append(queries, mysql.WithWhere("risk_level", req.RiskLevel))
	}
	if len(req.Id) > 0 {
		queries = append(queries, mysql.WithValuesIn("id", req.Id))
	}
	list, err := intelligence.NewHotPoc().ListAll(queries...)
	if err != nil {
		return err
	}
	for x := range list {
		list[x].RiskLevel = req.SetRiskLevel
		if err = intelligence.NewHotPoc().Update(list[x]); err != nil {
			return err
		}
	}
	return nil
}

// IntelligenceHotPocInfoAsset 热点POC资产列表
func (*Web) IntelligenceHotPocInfoAsset(ctx context.Context, req *pb.IntelligenceHotPocInfoAssetRequest, rsp *pb.IntelligenceHotPocInfoAssetResponse) error {
	poc, err := intelligence.NewHotPoc().First(mysql.WithWhere("id", req.Id))
	if err != nil {
		return err
	}
	rsp.PocInfo = &pb.IntelligenceHotPoc{
		Id:            poc.Id,
		Name:          poc.Name,
		Cve:           poc.Cve,
		Cnnvd:         poc.Cnnvd,
		ImpactProduct: poc.ImpactProduct,
		ImpactVersion: poc.ImpactVersion,
		ImpactRange:   poc.ImpactRange,
		RiskLevel:     poc.RiskLevel,
		Introduce:     poc.Introduce,
		FofaCount:     poc.FofaCount,
		FoundAt:       poc.FoundAt.Format("2006-01-02 15:04:05"),
		Solution:      poc.Solution,
	}
	assetQuery := elastic.NewBoolQuery().Must(elastic.NewExistsQuery("hot_poc_id")).MustNot(elastic.NewTermQuery("hot_poc_id", 0)).
		Must(elastic.NewTermQuery("hot_poc_id", req.Id)).
		Must(elastic.NewTermsQuery("status", foradar_assets.StatusUploadAsset, foradar_assets.StatusConfirmAsset))
	if ok, _ := user.IsUserAdmin(req.UserId); !ok {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("user_id", req.UserId))
	}
	total, list, err := foradar_assets.NewForadarAssetModel().List(context.TODO(), assetQuery, cast.ToInt(req.Page), cast.ToInt(req.PerPage))
	if err != nil {
		return err
	}
	if total != 0 {
		rsp.Total = total
		rsp.Page = req.PerPage
		rsp.CurrentPage = cast.ToInt64(req.Page)
	}
	for x := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceHotPocInfoAsset{
			Id:     list[x].ID,
			Ip:     list[x].Ip,
			Port:   cast.ToUint32(list[x].Port),
			Url:    list[x].Url,
			Title:  cast.ToString(list[x].Title),
			Status: cast.ToString(utils.If(cast.ToInt(list[x].OnlineState) == foradar_assets.OnlineStatusYES, "在线", "离线")),
		})
	}
	return nil
}

// IntelligenceHotPocAssetList 热点POC-IP资产列表
func (*Web) IntelligenceHotPocAssetList(ctx context.Context, req *pb.IntelligenceHotPocAssetListRequest, rsp *pb.IntelligenceHotPocAssetListResponse) error {
	assetQuery := elastic.NewBoolQuery().Must(elastic.NewExistsQuery("hot_poc_id")).MustNot(elastic.NewTermQuery("hot_poc_id", 0)).
		Must(elastic.NewTermsQuery("status", foradar_assets.StatusUploadAsset, foradar_assets.StatusConfirmAsset))
	if ok, _ := user.IsUserAdmin(req.UserId); !ok {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("user_id", req.UserId))
	}
	if req.Ip != "" {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("ip.keyword", req.Ip))
	}
	if req.Port != 0 {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("port", req.Port))
	}
	if req.Url != "" {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("url", req.Url))
	}
	if req.Title != "" {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("title", req.Title))
	}
	if req.Status > 0 && req.Status == 1 {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("online_state", foradar_assets.OnlineStatusYES))
	}
	if req.Status > 0 && req.Status == 2 {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("online_state", foradar_assets.OnlineStatusNO))
	}
	if req.HotPocName != "" {
		reqPocs, err := intelligence.NewHotPoc().ListAll(mysql.WithLike("name", req.HotPocName))
		if err != nil {
			return err
		}
		reqPocIds := utils.ListDistinctNonZero(utils.ListColumn(reqPocs, func(t *intelligence.HotPoc) uint64 { return t.Id }))
		if len(reqPocIds) < 0 {
			return nil
		}
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("hot_poc_id", utils.ListColumn(reqPocIds, func(t uint64) any { return t })...))
	}
	total, list, err := foradar_assets.NewForadarAssetModel().List(context.TODO(), assetQuery, cast.ToInt(req.Page), cast.ToInt(req.PerPage))
	if err != nil {
		return err
	}
	if total != 0 {
		rsp.Total = total
		rsp.Page = req.PerPage
		rsp.CurrentPage = cast.ToInt64(req.Page)
	}
	pocIds := make([]uint64, 0)
	_ = utils.ListColumn(list, func(t foradar_assets.ForadarAsset) []uint64 {
		pocIds = append(pocIds, t.HotPocId...)
		return t.HotPocId
	})
	pocIds = utils.ListDistinctNonZero(pocIds)
	pocs, err := intelligence.NewHotPoc().ListAll(mysql.WithValuesIn("id", pocIds))
	if err != nil {
		return err
	}
	pocGroups := utils.ListToMapFunc(pocs, func(v *intelligence.HotPoc) (key uint64, ok bool) { return v.Id, true })
	for x := range list {
		asset := &pb.IntelligenceHotPocAssetList{
			Id:      list[x].ID,
			Ip:      list[x].Ip,
			Port:    cast.ToUint32(list[x].Port),
			Url:     list[x].Url,
			Title:   cast.ToString(list[x].Title),
			Status:  cast.ToString(utils.If(cast.ToInt(list[x].OnlineState) == foradar_assets.OnlineStatusYES, "在线", "离线")),
			HotPocs: make([]*pb.IntelligenceHotPocAssetList_HotPocInfo, 0),
			Rules:   make([]*pb.Rule, 0),
		}
		// 提取POC信息
		for i := range list[x].HotPocId {
			asset.HotPocs = append(asset.HotPocs, &pb.IntelligenceHotPocAssetList_HotPocInfo{
				HotPocId:   list[x].HotPocId[i],
				HotPocName: pocGroups[list[x].HotPocId[i]].Name,
			})
		}
		// 提取组件信息
		for i := range list[x].RuleTags {
			asset.Rules = append(asset.Rules, &pb.Rule{
				CnCategory:       list[x].RuleTags[i].CnCategory,
				CnParentCategory: list[x].RuleTags[i].CnParentCategory,
				CnProduct:        list[x].RuleTags[i].CnProduct,
				Company:          list[x].RuleTags[i].Company,
				Softhard:         list[x].RuleTags[i].Softhard,
				Product:          list[x].RuleTags[i].Product,
				ParentCategory:   list[x].RuleTags[i].ParentCategory,
				Category:         list[x].RuleTags[i].Category,
				Level:            list[x].RuleTags[i].Level,
			})
		}
		rsp.Items = append(rsp.Items, asset)
	}
	return nil
}

// IntelligenceHotPocAssetExport 热点POC-IP资产列表-导出
func (*Web) IntelligenceHotPocAssetExport(_ context.Context, req *pb.IntelligenceHotPocAssetListRequest, rsp *pb.IntelligenceHotPocAssetExportResponse) error {
	assetQuery := elastic.NewBoolQuery().Must(elastic.NewExistsQuery("hot_poc_id")).MustNot(elastic.NewTermQuery("hot_poc_id", 0)).
		Must(elastic.NewTermsQuery("status", foradar_assets.StatusUploadAsset, foradar_assets.StatusConfirmAsset))
	if ok, _ := user.IsUserAdmin(req.UserId); !ok {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("user_id", req.UserId))
	}
	if len(req.Id) > 0 {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("id", utils.ListColumn(req.Id, func(t string) any { return t })...))
	}
	if req.Ip != "" {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("ip.keyword", req.Ip))
	}
	if req.Port != 0 {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("port", req.Port))
	}
	if req.Url != "" {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("url", req.Url))
	}
	if req.Title != "" {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("title", req.Title))
	}
	if req.Status > 0 && req.Status == 1 {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("online_state", foradar_assets.OnlineStatusYES))
	}
	if req.Status > 0 && req.Status == 2 {
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("online_state", foradar_assets.OnlineStatusNO))
	}
	if req.HotPocName != "" {
		reqPocs, err := intelligence.NewHotPoc().ListAll(mysql.WithLike("name", req.HotPocName))
		if err != nil {
			return err
		}
		reqPocIds := utils.ListDistinctNonZero(utils.ListColumn(reqPocs, func(t *intelligence.HotPoc) uint64 { return t.Id }))
		if len(reqPocIds) < 0 {
			return nil
		}
		assetQuery = assetQuery.Must(elastic.NewTermsQuery("hot_poc_id", utils.ListColumn(reqPocIds, func(t uint64) any { return t })...))
	}
	list, err := foradar_assets.NewForadarAssetModel().ListAll(context.TODO(), assetQuery)
	if err != nil {
		return err
	}
	pocIds := make([]uint64, 0)
	_ = utils.ListColumn(list, func(t foradar_assets.ForadarAsset) []uint64 {
		pocIds = append(pocIds, t.HotPocId...)
		return t.HotPocId
	})
	pocIds = utils.ListDistinctNonZero(pocIds)
	pocs, err := intelligence.NewHotPoc().ListAll(mysql.WithValuesIn("id", pocIds))
	if err != nil {
		return err
	}
	data := make([][]string, 0)
	data = append(data, []string{"IP", "端口", "URL", "状态", "漏洞", "标题", "组件"})
	pocGroups := utils.ListToMapFunc(pocs, func(v *intelligence.HotPoc) (key uint64, ok bool) { return v.Id, true })
	for x := range list {
		data = append(data, []string{
			list[x].Ip,
			cast.ToString(list[x].Port),
			list[x].Url,
			cast.ToString(utils.If(cast.ToInt(list[x].OnlineState) == foradar_assets.OnlineStatusYES, "在线", "离线")),
			strings.Join(utils.ListColumn(list[x].HotPocId, func(t uint64) string {
				return pocGroups[t].Name
			}), ","),
			cast.ToString(list[x].Title),
			strings.Join(utils.ListColumn(list[x].RuleTags, func(t foradar_assets.RuleTag) string {
				return t.CnProduct
			}), ","),
		})
	}
	path := storage.GenDownloadLocalPath(fmt.Sprintf("热点漏洞列表_%d.xlsx", time.Now().Unix()), req.UserId)
	if err = excel.WriteExcel(path, data); err != nil {
		return err
	}
	rsp.Url = storage.GenAPIDownloadPath(filepath.Base(path), path)
	return nil
}

func (*Web) IntelligenceCompanyList(_ context.Context, _ *pb.Empty, rsp *pb.IntelligenceCompanyListResponse) error {
	companies, err := company.NewCompanyModel().ListAll()
	if err != nil {
		return err
	}
	for _, v := range companies {
		rsp.Name = append(rsp.Name, v.Name)
	}
	clients, err := auth_access_client.NewAuthAccessClientModel().ListAll()
	if err != nil {
		return err
	}
	for _, v := range clients {
		rsp.Name = append(rsp.Name, v.CompanyName)
	}
	rsp.Name = utils.ListDistinctNonZero(rsp.Name)
	return nil
}

// IntelligenceHotPocCheck 热点POC一键检查
func (*Web) IntelligenceHotPocCheck(ctx context.Context, req *pb.IntelligenceHotPocCheckRequest, _ *pb.Empty) error {
	if intelligence_poc.CheckTaskRunning(req.UserId) {
		return errors.BadRequest(pb.ServiceName, "任务正在运行")
	}
	intelligence_poc.AddTask(req.UserId)
	req.Id = utils.ListDistinctNonZero(req.Id)
	list := make([]*intelligence.HotPoc, 0)
	queries := make([]mysql.HandleFunc, 0)
	if req.Name != "" {
		queries = append(queries, mysql.WithLike("name", req.Name))
	}
	if req.Cve != "" {
		queries = append(queries, mysql.WithWhere("cve", req.Cve))
	}
	if req.Cnnvd != "" {
		queries = append(queries, mysql.WithWhere("cnnvd", req.Cnnvd))
	}
	if req.RiskLevel != "" {
		queries = append(queries, mysql.WithWhere("risk_level", req.RiskLevel))
	}
	if len(req.FoundAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("found_at", req.FoundAt))
	}
	if len(req.CreatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("created_at", req.CreatedAt))
	}
	if len(req.UpdatedAt) > 0 {
		queries = append(queries, mysql.WithBetweenAt("updated_at", req.UpdatedAt))
	}
	if len(req.Id) > 0 {
		if l, err := intelligence.NewHotPoc().ListAll(mysql.WithValuesIn("id", req.Id)); err != nil {
			intelligence_poc.DeleteTask(req.UserId)
			return err
		} else {
			list = append(list, l...)
		}
		intelligence_poc.MatchPoC(req.UserId, list, false)
	} else {
		if l, err := intelligence.NewHotPoc().ListAll(queries...); err != nil {
			intelligence_poc.DeleteTask(req.UserId)
			return err
		} else {
			list = append(list, l...)
		}
		go intelligence_poc.MatchPoC(req.UserId, list, true)
	}
	return nil
}

// IntelligenceUserHotPocMatchProcess 情报中心-热点POC-检测进度
func (*Web) IntelligenceUserHotPocMatchProcess(ctx context.Context, req *pb.IntelligenceUserHotPocMatchProcessRequest, rsp *pb.IntelligenceUserHotPocMatchProcessResponse) error {
	rsp.Process = cast.ToFloat32(intelligence_poc.GetTaskProcess(req.UserId))
	if cast.ToInt(rsp.Process) == 100 {
		intelligence_poc.DeleteTask(req.UserId)
		time.Sleep(1 * time.Second)
	}
	return nil
}

// IntelligenceHotPocUploadReport 情报中心-热点POC-上传报告
func (*Web) IntelligenceHotPocUploadReport(ctx context.Context, req *pb.IntelligenceHotPocUploadReportRequest, rsp *pb.IntelligenceUploadReportResponse) error {
	poc, err := intelligence.NewHotPoc().GetById(uint(req.PocId))
	if err != nil {
		return err
	}
	//保存文件到磁盘
	fileName := fmt.Sprintf("%s-Upload.pdf", poc.Name)
	path := storage.SaveIntelligenceReport(req.FileContent, fileName)
	if path == "" {
		return errors.BadRequest(pb.ServiceName, "文件上传失败")
	}
	//修改数据库
	now := time.Now()
	updatePoc := intelligence.HotPoc{}
	updatePoc.Id = poc.Id
	updatePoc.MaskedReportLocalLink = path
	updatePoc.MaskedReportCreatedId = req.UserId
	updatePoc.MaskedReportUpdatedTime = &now
	err = intelligence.NewHotPoc().Update(&updatePoc)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, "数据保存失败")
	}

	rsp.MaskedReportLocalLink = path
	return nil
}

// IntelligenceHotPocDownloadMaskedReport 情报中心-热点漏洞-下载脱敏报告
func (*Web) IntelligenceHotPocDownloadMaskedReport(ctx context.Context, req *pb.IntelligenceHotPocDownloadReportRequest, rsp *pb.IntelligenceDownloadReportResponse) error {
	poc, err := intelligence.NewHotPoc().GetById(uint(req.PocId))
	if err != nil {
		return err
	}
	if poc.MaskedReportLocalLink == "" {
		return fmt.Errorf("脱敏报告不存在")
	}
	rsp.FilePath = fmt.Sprintf("%s%s", storage.GetRootPath(), poc.MaskedReportLocalLink)
	rsp.FileName = fmt.Sprintf("%s%s", poc.Name, path.Ext(poc.MaskedReportLocalLink))
	return nil
}

// IntelligenceHotPocDownloadReport 情报中心-热点漏洞-下载原始报告
func (*Web) IntelligenceHotPocDownloadReport(ctx context.Context, req *pb.IntelligenceHotPocDownloadReportRequest, rsp *pb.IntelligenceDownloadReportResponse) error {
	poc, err := intelligence.NewHotPoc().GetById(uint(req.PocId))
	if err != nil {
		return err
	}
	if poc.LocalLink == "" {
		return fmt.Errorf("报告不存在")
	}
	downloadLink := fmt.Sprintf("%s%s", storage.GetRootPath(), poc.LocalLink)
	rsp.FilePath = downloadLink
	rsp.FileName = fmt.Sprintf("%s%s.pdf", poc.Name, path.Ext(poc.LocalLink))
	return nil
}

// IntelligenceEventCountAfterLastLogin 情报中心-事件专项-上次登录后新产生的数量
func (*Web) IntelligenceEventCountAfterLastLogin(ctx context.Context, req *pb.IntelligenceEventCountAfterLastLoginRequest, rsp *pb.IntelligenceEventCountAfterLastLoginResponse) error {
	lastLoginTime, err := operate_logs.NewModel().GetLastLoginTime(req.UserId)
	if err != nil {
		return err
	}
	count, err := intelligence.NewEvent().Count(mysql.WithGT("creation_time", lastLoginTime))
	if err != nil {
		return err
	}
	rsp.Count = count
	return nil
}

// IntelligenceEventIPCount 情报中心-事件专项-IP数量
func (*Web) IntelligenceEventIPCount(ctx context.Context, _ *pb.Empty, rsp *pb.IntelligenceEventIPCountResponse) error {
	allCount, err := intelligence.NewEvent().IPCount()
	if err != nil {
		return err
	}
	list, total, err := intelligence.NewEvent().List(1, 9999, mysql.WithBetween("creation_time", time.Now().AddDate(0, 0, -7), time.Now()))
	if err != nil {
		return err
	}
	rsp.AllCount = allCount
	rsp.LatestCount = 0
	if total > 0 {
		for _, v := range list {
			rsp.LatestCount += v.IpCount
		}
	}
	return nil
}

// IntelligenceEventCount 情报中心-事件专项-数量
func (*Web) IntelligenceEventCount(ctx context.Context, _ *pb.Empty, rsp *pb.IntelligenceEventCountResponse) error {
	count, err := intelligence.NewEvent().Count()
	if err != nil {
		return err
	}
	rsp.Count = count
	return nil
}

// IntelligenceEventWarning 情报中心-事件专项-预警
func (*Web) IntelligenceEventWarning(ctx context.Context, req *pb.IntelligenceEventWarningRequest, rsp *pb.IntelligenceEventWarningResponse) error {
	query := make([]mysql.HandleFunc, 0)
	// 预警仅包括“专项事件”
	query = append(query, mysql.WithWhere("risk_type", 3))
	if req.UserId > 0 {
		query = append(query, mysql.WithWhere("user_id", req.UserId))
	}
	if req.OperateCompanyId > 0 {
		query = append(query, mysql.WithWhere("enterprise_id", req.OperateCompanyId))
	}
	data, err := intelligence.NewRelatedIntelligence().DistinctColumn("intelligence_id", query...)
	if err != nil {
		return err
	}
	// 说明存在用户相关的预警
	if len(data) > 0 {
		// 查找专项数据
		query = make([]mysql.HandleFunc, 0)
		query = append(query, mysql.WithValuesIn("id", data))
		query = append(query, mysql.WithOrder("disclosure_time desc"))
		eventData, _, err := intelligence.NewEvent().List(1, 5, query...)
		if err != nil {
			return err
		}
		rsp.Total = uint32(len(data))
		rsp.DataType = "1" // 1:用户相关
		rsp.Items = make([]*pb.IntelligenceEventWarningResponse_IntelligenceEventWarning, 0)
		for _, v := range eventData {
			rsp.Items = append(rsp.Items, &pb.IntelligenceEventWarningResponse_IntelligenceEventWarning{
				Id:       v.ID,
				UserId:   cast.ToString(req.UserId),
				RiskName: v.Name,
			})
		}
		return nil
	} else {
		query = make([]mysql.HandleFunc, 0)
		// 按照披露时间倒序
		query = append(query, mysql.WithOrder("disclosure_time desc"))
		event, total, err := intelligence.NewEvent().List(1, 5, query...)
		if err != nil {
			return nil
		}
		if total > 0 {
			rsp.Total = uint32(total)
			rsp.DataType = "2" // 2:登录相关
			rsp.Items = make([]*pb.IntelligenceEventWarningResponse_IntelligenceEventWarning, 0)
			for _, v := range event {
				rsp.Items = append(rsp.Items, &pb.IntelligenceEventWarningResponse_IntelligenceEventWarning{
					Id:       v.ID,
					UserId:   cast.ToString(req.UserId),
					RiskName: v.Name,
				})
			}
			return nil
		}
	}
	rsp.DataType = "3" // 3:无数据
	rsp.Total = 0
	rsp.Items = make([]*pb.IntelligenceEventWarningResponse_IntelligenceEventWarning, 0)
	return nil
}

// IntelligenceEventListForUser 情报中心-事件专项-总览
func (*Web) IntelligenceEventListForUser(ctx context.Context, req *pb.IntelligenceEventListForUserRequest, rsp *pb.IntelligenceEventListResponse) error {
	if req.UserId < 1 {
		return errors.BadRequest(pb.ServiceName, "用户ID不能为空")
	}
	query := make([]mysql.HandleFunc, 0)
	req.Category = utils.ListDistinctNonZero(req.Category)
	if len(req.Category) > 0 {
		query = append(query, mysql.WithValuesIn("category", req.Category))
	}
	req.DisclosureTime = utils.FormattingTime(req.DisclosureTime)
	if len(req.DisclosureTime) > 0 {
		query = append(query, mysql.WithBetweenAtOriginal("disclosure_time", req.DisclosureTime))
	}
	if req.Keyword != "" {
		query = append(query, mysql.WithLRLike("name", req.Keyword))
	}
	events, total, err := intelligence.NewEvent().List(int(req.Page), int(req.PerPage), query...)
	if err != nil {
		return err
	}
	rsp.Page = req.PerPage
	rsp.CurrentPage = req.Page
	rsp.Total = total
	rsp.Items = make([]*pb.IntelligenceEvent, 0)
	for _, v := range events {
		ie := &pb.IntelligenceEvent{
			Id:               uint32(v.ID),
			Name:             v.Name,
			CreationTime:     v.CreationTime.Format(time.DateTime),
			DisclosureTime:   v.DisclosureTime.Format(time.DateTime),
			Tags:             v.Tags,
			IpCount:          v.IpCount,
			LocalLink:        v.LocalLink,
			Category:         v.Category,
			AssetNum:         0,
			CompanyList:      "",
			CompanyNum:       0,
			Summary:          v.Summary,
			AssociatedDevice: v.AssociatedDevice,
			HasMaskedReport:  v.MaskedReportLocalLink != "",
		}
		// 从关联表获取关联资产数量
		query := make([]mysql.HandleFunc, 0)
		query = append(query, mysql.WithColumnValue("risk_type", 3))
		query = append(query, mysql.WithColumnValue("user_id", req.UserId))
		query = append(query, mysql.WithColumnValue("intelligence_id", v.ID))
		count, err := intelligence.NewRelatedIntelligence().Count(query...)
		if err == nil {
			ie.AssetNum = uint64(count)
		}
		// 从关联表获取关联企业数量
		ename, err := intelligence.NewRelatedIntelligence().DistinctColumn("enterprise_name", query...)
		if err == nil {
			ename = utils.ListDistinctNonZero(ename)
			ie.CompanyList = strings.Join(ename, ",")
			ie.CompanyNum = uint64(len(ename))
		}
		rsp.Items = append(rsp.Items, ie)
	}
	return nil
}

// IntelligenceEventList 情报中心-事件专项-列表
func (*Web) IntelligenceEventList(ctx context.Context, req *pb.IntelligenceEventListRequest, rsp *pb.IntelligenceEventListResponse) error {
	query := make([]mysql.HandleFunc, 0)
	if len(req.Category) > 0 {
		query = append(query, mysql.WithValuesIn("category", req.Category))
	}
	req.DisclosureTime = utils.FormattingTime(req.DisclosureTime)
	if len(req.DisclosureTime) > 0 {
		query = append(query, mysql.WithBetweenAtOriginal("disclosure_time", req.DisclosureTime))
	}
	if req.Keyword != "" {
		query = append(query, mysql.WithLRLike("name", req.Keyword))
	}
	events, total, err := intelligence.NewEvent().List(int(req.Page), int(req.PerPage), query...)
	if err != nil {
		return err
	}
	rsp.Page = req.PerPage
	rsp.CurrentPage = req.Page
	rsp.Total = total
	rsp.Items = make([]*pb.IntelligenceEvent, 0)
	for _, v := range events {
		ie := &pb.IntelligenceEvent{
			Id:               uint32(v.ID),
			Name:             v.Name,
			CreationTime:     v.CreationTime.Format(time.DateTime),
			DisclosureTime:   v.DisclosureTime.Format(time.DateTime),
			Tags:             v.Tags,
			IpCount:          v.IpCount,
			LocalLink:        v.LocalLink,
			Category:         v.Category,
			AssetNum:         0,
			CompanyList:      "",
			CompanyNum:       0,
			Summary:          v.Summary,
			AssociatedDevice: v.AssociatedDevice,
			HasMaskedReport:  v.MaskedReportLocalLink != "",
		}
		// 从关联表获取关联资产数量
		query := make([]mysql.HandleFunc, 0)
		query = append(query, mysql.WithColumnValue("risk_type", 3))
		query = append(query, mysql.WithColumnValue("user_id", req.UserId))
		query = append(query, mysql.WithColumnValue("intelligence_id", v.ID))
		count, err := intelligence.NewRelatedIntelligence().Count(query...)
		if err == nil {
			ie.AssetNum = uint64(count)
		}
		// 从关联表获取关联企业数量
		ename, err := intelligence.NewRelatedIntelligence().DistinctColumn("enterprise_name", query...)
		if err == nil {
			ename = utils.ListDistinctNonZero(ename)
			ie.CompanyList = strings.Join(ename, ",")
			ie.CompanyNum = uint64(len(ename))
		}
		rsp.Items = append(rsp.Items, ie)
	}
	return nil
}

// IntelligenceEventDetail 情报中心-事件专项-高级筛选数据源
func (*Web) IntelligenceEventCondition(ctx context.Context, req *pb.Empty, rsp *pb.IntelligenceEventConditionResponse) error {
	// 获取分类
	categories, err := intelligence.NewEvent().DistinctColumn("category")
	if err != nil {
		return err
	}
	rsp.Category = categories
	return nil
}

// IntelligenceEventDetail 情报中心-事件专项-详情
func (*Web) IntelligenceEventDetail(ctx context.Context, req *pb.IntelligenceEventDetailRequest, rsp *pb.IntelligenceEventDetailResponse) error {
	if req.EventId < 1 {
		return errors.BadRequest(pb.ServiceName, "event_id不能为空")
	}
	var condition []mysql.HandleFunc
	condition = append(condition, mysql.WithColumnValue("event_id", req.EventId))

	var createAt []string
	if req.LastUpdateAtStart > 0 {
		st := time.Unix(cast.ToInt64(req.LastUpdateAtStart), 0)
		createAt = append(createAt, st.Format("2006-01-02 15:04:05"))
	}
	if req.LastUpdateAtEnd > 0 {
		et := time.Unix(cast.ToInt64(req.LastUpdateAtEnd), 0)
		createAt = append(createAt, et.Format("2006-01-02 15:04:05"))
	}
	condition = append(condition, mysql.WithBetweenAt("last_updated", createAt))

	if req.Ip != "" {
		condition = append(condition, mysql.WithWhere("ip", req.Ip))
	}
	if req.City != "" {
		condition = append(condition, mysql.WithLRLike("city", req.City))
	}
	if req.RelatedDevice != "" {
		condition = append(condition, mysql.WithWhere("related_device", req.RelatedDevice))
	}

	list, total, err := intelligence.NewEventDetail().List(int(req.Page), int(req.PerPage), condition...)
	if err != nil {
		return err
	}
	rsp.Total = total
	rsp.Page = req.PerPage
	rsp.CurrentPage = int64(req.Page)
	rsp.Items = make([]*pb.IntelligenceEventDetail, 0)
	for _, v := range list {
		ie := &pb.IntelligenceEventDetail{
			EventId:       uint32(v.EventId),
			RelatedDevice: v.RelatedDevice,
			Vulnerability: v.Vulnerability,
			RiskLevel:     v.RiskLevel,
			RiskType:      v.RiskType,
			IsVulnerable:  v.IsVulnerable,
			IpAddress:     v.IPAddress,
			Port:          v.Port,
			Protocol:      v.Protocol,
			Country:       v.Country,
			City:          v.City,
			Url:           v.URL,
			Case:          v.Case,
			Object:        v.Object,
			IsCdn:         v.IsCDN,
			Tags:          v.Tags,
			Os:            v.OS,
			StatusCode:    v.StatusCode,
			Title:         v.Title,
			Domain:        v.Domain,
			Certificate:   v.Certificate,
			Organization:  v.Organization,
			Institution:   v.Institution,
			Component:     v.Component,
			Category:      v.Category,
			Icon:          v.Icon,
			Fingerprint:   v.Fingerprint,
			AssetCount:    v.AssetCount,
			VulnAssetCnt:  v.VulnAssetCnt,
			LastUpdated:   v.LastUpdated.Format(time.DateTime),
		}
		if v.FirstDetected != nil {
			ie.FirstDetected = v.FirstDetected.Format(time.DateTime)
		}
		if v.LastDetected != nil {
			ie.LastDetected = v.LastDetected.Format(time.DateTime)
		}
		rsp.Items = append(rsp.Items, ie)
	}
	return nil
}

// IntelligenceEventCategoryList 情报中心-事件专项-分类列表
func (*Web) IntelligenceEventCategoryList(ctx context.Context, req *pb.Empty, rsp *pb.IntelligenceEventCategoryListResponse) error {
	query := make([]mysql.HandleFunc, 0)
	list, err := intelligence.NewEvent().DistinctColumn("category", query...)
	if err != nil {
		return err
	}
	rsp.Category = list
	return nil
}

// IntelligenceEventUpdateCategory 情报中心-事件专项-更新分类
func (*Web) IntelligenceEventUpdateCategory(ctx context.Context, req *pb.IntelligenceEventUpdateCategoryRequest, rsp *pb.Empty) error {
	event, err := intelligence.NewEvent().GetById(uint(req.EventId))
	if err != nil {
		return err
	}
	//修改数据库
	updateEvent := intelligence.Event{}
	updateEvent.ID = event.ID
	updateEvent.Category = req.Category

	err = intelligence.NewEvent().Update(&updateEvent)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, "数据保存失败")
	}
	return nil
}

// IntelligenceEventUploadReport 情报中心-事件专项-上传报告
func (*Web) IntelligenceEventUploadReport(ctx context.Context, req *pb.IntelligenceEventUploadReportRequest, rsp *pb.IntelligenceUploadReportResponse) error {
	event, err := intelligence.NewEvent().GetById(uint(req.EventId))
	if err != nil {
		return err
	}
	//保存文件到磁盘
	fileName := fmt.Sprintf("%s-Upload.pdf", event.Name)
	err = storage.DeleteByFileName(fileName)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, "文件删除失败")
	}
	path := storage.SaveIntelligenceReport(req.FileContent, fileName)
	if path == "" {
		return errors.BadRequest(pb.ServiceName, "文件上传失败")
	}
	//修改数据库
	now := time.Now()
	updateEvent := intelligence.Event{}
	updateEvent.ID = event.ID
	updateEvent.MaskedReportLocalLink = path
	updateEvent.MaskedReportCreatedId = req.UserId
	updateEvent.MaskedReportUpdatedTime = &now
	err = intelligence.NewEvent().Update(&updateEvent)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, "数据保存失败")
	}

	rsp.MaskedReportLocalLink = path
	return nil
}

// IntelligenceEventDownloadMaskedReport 情报中心-事件专项-下载掩码报告
func (*Web) IntelligenceEventDownloadMaskedReport(ctx context.Context, req *pb.IntelligenceEventDownloadReportRequest, rsp *pb.IntelligenceDownloadReportResponse) error {
	event, err := intelligence.NewEvent().GetById(uint(req.EventId))
	if err != nil {
		return err
	}
	if event.MaskedReportLocalLink == "" {
		return fmt.Errorf("脱敏报告不存在")
	}
	rsp.FilePath = fmt.Sprintf("%s%s", storage.GetRootPath(), event.MaskedReportLocalLink)
	rsp.FileName = fmt.Sprintf("%s%s", event.Name, path.Ext(event.MaskedReportLocalLink))
	return nil
}

// IntelligenceEventDownloadReport 情报中心-事件专项-下载原始报告
func (*Web) IntelligenceEventDownloadReport(ctx context.Context, req *pb.IntelligenceEventDownloadReportRequest, rsp *pb.IntelligenceDownloadReportResponse) error {
	event, err := intelligence.NewEvent().GetById(uint(req.EventId))
	if err != nil {
		return err
	}
	//path := storage.GenDownloadLocalPath(fmt.Sprintf("热点漏洞列表_%d.xlsx", time.Now().Unix()), req.UserId)
	//if err = excel.WriteExcel(path, data); err != nil {
	//	return err
	//}
	//rsp.Url = storage.GenAPIDownloadPath(filepath.Base(path), path)

	if event.LocalLink == "" {
		return fmt.Errorf("报告不存在")
	}
	downloadLink := fmt.Sprintf("%s%s", storage.GetRootPath(), event.LocalLink)
	rsp.FilePath = downloadLink
	rsp.FileName = fmt.Sprintf("%s%s.pdf", event.Name, path.Ext(event.LocalLink))
	return nil
}

// IntelligenceDataCount 情报中心-数据专项-数量
func (*Web) IntelligenceDataCount(ctx context.Context, _ *pb.Empty, rsp *pb.IntelligenceDataCountResponse) error {
	count, err := intelligence.NewData().Count()
	if err != nil {
		return err
	}
	rsp.Count = count
	return nil
}

// IntelligenceDataSummaryListForUser 情报中心-数据专项-总览
func (*Web) IntelligenceDataSummaryListForUser(ctx context.Context, req *pb.IntelligenceDataSummaryListForUserRequest, rsp *pb.IntelligenceDataSummaryListResponse) error {
	// // 查询用户相关的数据专项名称，然后再根据专项名称查询数据专项列表。
	// // 数据专项列表是根据数据专项名称汇总出来的表，所以需要先查询数据专项名称
	// var condition []mysql.HandleFunc
	// condition = append(condition, mysql.WithColumnValue("risk_type", 4))
	// condition = append(condition, mysql.WithColumnValue("user_id", req.UserId))
	// if req.SpecialProjectName != "" {
	// 	condition = append(condition, mysql.WithLRLike("special_project_name", req.SpecialProjectName))
	// }
	// specialProjectNameList, err := intelligence.NewRelatedIntelligence().DistinctColumn("special_project_name", condition...)
	// if err != nil {
	// 	log.WithContextErrorf(ctx, "IntelligenceDataSummaryListForUser 查询RelatedIntelligence失败， error", err)
	// 	return errors.InternalServerError(pb.ServiceName, "查询失败")
	// }
	// if len(specialProjectNameList) < 1 {
	// 	rsp.Total = 0
	// 	rsp.Page = req.PerPage
	// 	rsp.CurrentPage = int64(req.Page)
	// 	rsp.Items = make([]*pb.IntelligenceDataSummary, 0)
	// 	return nil
	// }
	//

	condition := make([]mysql.HandleFunc, 0)
	if req.Keyword != "" {
		condition = append(condition, mysql.WithLRLike("special_project_name", req.Keyword))
	}
	if len(req.SpecialProjectName) > 0 && req.SpecialProjectName[0] != "" {
		condition = append(condition, mysql.WithValuesIn("special_project_name", req.SpecialProjectName))
	}
	if len(req.Entities) > 0 && req.Entities[0] != "" {
		for _, v := range req.Entities {
			condition = append(condition, mysql.WithLRLike("entities", v))
		}
	}
	req.LastUpdateTime = utils.FormattingTime(req.LastUpdateTime)
	if len(req.LastUpdateTime) > 0 {
		condition = append(condition, mysql.WithBetweenAtOriginal("last_update_time", req.LastUpdateTime))
	}
	list, total, err := intelligence.NewDataSummary().List(int(req.Page), int(req.PerPage), condition...)
	if err != nil {
		log.WithContextErrorf(ctx, "IntelligenceDataSummaryListForUser 查询DataSummary失败， error", err)
		return err
	}
	rsp.Total = total
	rsp.Page = req.PerPage
	rsp.CurrentPage = int64(req.Page)
	rsp.Items = make([]*pb.IntelligenceDataSummary, 0)
	for _, v := range list {
		ds := &pb.IntelligenceDataSummary{
			Id:                 uint32(v.ID),
			SpecialProjectName: v.SpecialProjectName,
			DataVolume:         int64(v.DataVolume),
			Entities:           "",
			CompanyNum:         0,
			AssetNum:           0,
			LastUpdateTime:     v.LastUpdateTime.Format(time.DateTime),
		}
		// 从关联表获取关联资产数量
		query := make([]mysql.HandleFunc, 0)
		query = append(query, mysql.WithColumnValue("risk_type", 4))
		query = append(query, mysql.WithColumnValue("user_id", req.UserId))
		query = append(query, mysql.WithColumnValue("special_project_name", v.SpecialProjectName))
		count, err := intelligence.NewRelatedIntelligence().Count(query...)
		if err == nil {
			ds.AssetNum = int32(count)
		}
		// 从关联表获取关联企业数量
		ename, err := intelligence.NewRelatedIntelligence().DistinctColumn("enterprise_name", query...)
		if err == nil {
			ename = utils.ListDistinctNonZero(ename)
			ds.Entities = strings.Join(ename, ",")
			ds.CompanyNum = int32(len(ename))
		}
		rsp.Items = append(rsp.Items, ds)
	}
	return nil
}

// IntelligenceDataSummaryList 情报中心-数据专项-概要列表
func (*Web) IntelligenceDataSummaryList(ctx context.Context, req *pb.IntelligenceDataSummaryListRequest, rsp *pb.IntelligenceDataSummaryListResponse) error {
	condition := make([]mysql.HandleFunc, 0)
	if req.Keyword != "" {
		condition = append(condition, mysql.WithLRLike("special_project_name", req.Keyword))
	}
	if len(req.SpecialProjectName) > 0 && req.SpecialProjectName[0] != "" {
		condition = append(condition, mysql.WithValuesIn("special_project_name", req.SpecialProjectName))
	}
	if len(req.Entities) > 0 && req.Entities[0] != "" {
		for _, v := range req.Entities {
			condition = append(condition, mysql.WithLRLike("entities", v))
		}
	}
	req.LastUpdateTime = utils.FormattingTime(req.LastUpdateTime)
	if len(req.LastUpdateTime) > 0 {
		condition = append(condition, mysql.WithBetweenAtOriginal("last_update_time", req.LastUpdateTime))
	}
	list, total, err := intelligence.NewDataSummary().List(int(req.Page), int(req.PerPage), condition...)
	if err != nil {
		return err
	}
	rsp.Total = total
	rsp.Page = req.PerPage
	rsp.CurrentPage = int64(req.Page)
	rsp.Items = make([]*pb.IntelligenceDataSummary, 0)
	for _, v := range list {
		ds := &pb.IntelligenceDataSummary{
			Id:                 uint32(v.ID),
			SpecialProjectName: v.SpecialProjectName,
			Entities:           "",
			DataVolume:         int64(v.DataVolume),
			CompanyNum:         0,
			AssetNum:           0,
			LastUpdateTime:     v.LastUpdateTime.Format(time.DateTime),
		}
		// 从关联表获取关联资产数量
		query := make([]mysql.HandleFunc, 0)
		query = append(query, mysql.WithColumnValue("risk_type", 4))
		query = append(query, mysql.WithColumnValue("user_id", req.UserId))
		query = append(query, mysql.WithColumnValue("special_project_name", v.SpecialProjectName))
		count, err := intelligence.NewRelatedIntelligence().Count(query...)
		if err == nil {
			ds.AssetNum = int32(count)
		}
		// 从关联表获取关联企业数量
		ename, err := intelligence.NewRelatedIntelligence().DistinctColumn("enterprise_name", query...)
		if err == nil {
			ename = utils.ListDistinctNonZero(ename)
			ds.Entities = strings.Join(ename, ",")
			ds.CompanyNum = int32(len(ename))
		}
		rsp.Items = append(rsp.Items, ds)
	}
	return nil
}

// IntelligenceDataSummaryConditionList 情报中心-数据专项-高级筛选数据源
func (*Web) IntelligenceDataSummaryCondition(ctx context.Context, req *pb.IntelligenceDataSummaryConditionRequest, rsp *pb.IntelligenceDataSummaryConditionResponse) error {
	isAdmin, err := user.IsUserAdmin(req.UserId)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, "用户权限查询失败")
	}
	if !isAdmin {
		// 获取用户权限，如果是普通用户，则获取归属企业，如果是安服，除了归属企业还需要获取关联企业
		// 获取用户权限,用户类型: 1/2/3/4 超级管理员/安服人员/企业租户/售后
		companyNameList, err := user.GetUserCompanyNameList(req.UserId)
		// entities, err := intelligence.NewRelatedIntelligence().DistinctColumn("enterprise_name", query...)
		if err != nil {
			rsp.Entities = []string{}
		}
		rsp.Entities = companyNameList

		specialProjectName, err := intelligence.NewDataSummary().DistinctColumn("special_project_name")
		if err != nil {
			rsp.SpecialProjectName = []string{}
		}
		rsp.SpecialProjectName = specialProjectName
		return nil
	}
	entities, err := intelligence.NewDataSummary().DistinctColumn("entities")
	if err != nil {
		rsp.Entities = []string{}
	}
	for _, v := range entities {
		entityNameList := strings.Split(v, ",")
		rsp.Entities = append(rsp.Entities, entityNameList...)
	}
	rsp.Entities = utils.ListDistinctNonZero(rsp.Entities)
	specialProjectName, err := intelligence.NewDataSummary().DistinctColumn("special_project_name")
	if err != nil {
		rsp.SpecialProjectName = []string{}
	}
	rsp.SpecialProjectName = specialProjectName
	return nil
}

func (*Web) IntelligenceDataListForUser(ctx context.Context, req *pb.IntelligenceDataListForUserRequest, rsp *pb.IntelligenceDataListResponse) error {
	rsp.Page = req.PerPage
	rsp.CurrentPage = int64(req.Page)
	if req.UserId < 1 {
		return errors.BadRequest(pb.ServiceName, "用户ID不能为空")
	}

	// 通过 DataSummaryId 查询情报数据
	data, err := intelligence.NewDataSummary().GetById(uint(req.DataSummaryId))
	if err != nil {
		return err
	}
	// 查询关联结果，获取关联企业
	query := make([]mysql.HandleFunc, 0)
	query = append(query, mysql.WithColumnValue("user_id", req.UserId))
	query = append(query, mysql.WithColumnValue("risk_type", 4))
	query = append(query, mysql.WithColumnValue("special_project_name", data.SpecialProjectName))
	enterpriseName, err := intelligence.NewRelatedIntelligence().DistinctColumn("enterprise_name", query...)
	if err != nil {
		return err
	}
	enterpriseName = utils.ListDistinctNonZero(enterpriseName)
	if len(enterpriseName) == 0 {
		return nil
	}

	condition := make([]mysql.HandleFunc, 0)
	if req.Ip != "" {
		condition = append(condition, mysql.WithColumnValue("leak_source_ip", req.Ip))
	}
	if req.IpRelatedLocation != "" {
		condition = append(condition, mysql.WithLRLike("ip_related_location", req.IpRelatedLocation))
	}
	if req.Entity != "" {
		condition = append(condition, mysql.WithColumnValue("data_entity", req.Entity))
	}
	if req.EntityLocation != "" {
		condition = append(condition, mysql.WithColumnValue("data_entity_location", req.EntityLocation))
	}
	// 添加专项名称条件
	condition = append(condition, mysql.WithColumnValue("special_project_name", data.SpecialProjectName))
	// 添加关联企业条件
	condition = append(condition, mysql.WithValuesIn("data_entity", enterpriseName))
	list, total, err := intelligence.NewData().List(int(req.Page), int(req.PerPage), condition...)
	if err != nil {
		return err
	}
	rsp.Total = total
	rsp.Items = make([]*pb.IntelligenceData, 0)
	for _, v := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceData{
			Id:                     uint32(v.ID),
			SpecialProjectName:     v.SpecialProjectName,
			EventName:              v.EventName,
			SpecialProjectCategory: v.SpecialProjectCategory,
			Overview:               v.Overview,
			DisclosureTime: func() string {
				if v.DisclosureTime == nil {
					return ""
				}
				return v.DisclosureTime.Format("2006-01-02 15:04:05")
			}(),
			LeakSourceIp:         v.LeakSourceIP,
			IpRelatedLocation:    v.IPRelatedLocation,
			LeakServiceComponent: v.LeakServiceComponent,
			DataVolume:           int32(v.DataVolume),
			DataContent:          v.DataContent,
			DataEntity:           v.DataEntity,
			DataEntityLocation:   v.DataEntityLocation,
			LeakReason:           v.LeakReason,
			FixSolution:          v.FixSolution,
			HasMaskedReport:      v.MaskedReportLocalLink != "",
		})
	}
	return nil
}

// IntelligenceDataList 情报中心-数据专项-列表
func (*Web) IntelligenceDataList(ctx context.Context, req *pb.IntelligenceDataListRequest, rsp *pb.IntelligenceDataListResponse) error {
	ds, err := intelligence.NewDataSummary().GetById(uint(req.DataSummaryId))
	if err != nil {
		return err
	}
	condition := []mysql.HandleFunc{
		mysql.WithWhere("special_project_name", ds.SpecialProjectName),
	}
	if req.Ip != "" {
		condition = append(condition, mysql.WithColumnValue("leak_source_ip", req.Ip))
	}
	if req.IpRelatedLocation != "" {
		condition = append(condition, mysql.WithLRLike("ip_related_location", req.IpRelatedLocation))
	}
	if req.Entity != "" {
		condition = append(condition, mysql.WithColumnValue("data_entity", req.Entity))
	}
	if req.EntityLocation != "" {
		condition = append(condition, mysql.WithColumnValue("data_entity_location", req.EntityLocation))
	}
	list, total, err := intelligence.NewData().List(int(req.Page), int(req.PerPage), condition...)
	if err != nil {
		return err
	}
	rsp.Total = total
	rsp.Page = req.PerPage
	rsp.CurrentPage = int64(req.Page)
	rsp.Items = make([]*pb.IntelligenceData, 0)
	for _, v := range list {
		rsp.Items = append(rsp.Items, &pb.IntelligenceData{
			Id:                     uint32(v.ID),
			SpecialProjectName:     v.SpecialProjectName,
			EventName:              v.EventName,
			SpecialProjectCategory: v.SpecialProjectCategory,
			Overview:               v.Overview,
			DisclosureTime: func() string {
				if v.DisclosureTime == nil {
					return ""
				}
				return v.DisclosureTime.Format("2006-01-02 15:04:05")
			}(),
			LeakSourceIp:         v.LeakSourceIP,
			IpRelatedLocation:    v.IPRelatedLocation,
			LeakServiceComponent: v.LeakServiceComponent,
			DataVolume:           int32(v.DataVolume),
			DataContent:          v.DataContent,
			DataEntity:           v.DataEntity,
			DataEntityLocation:   v.DataEntityLocation,
			LeakReason:           v.LeakReason,
			FixSolution:          v.FixSolution,
			HasMaskedReport:      v.MaskedReportLocalLink != "",
		})
	}
	return nil
}

// IntelligenceDataDetail 情报中心-数据专项-详情
func (*Web) IntelligenceDataDetail(ctx context.Context, req *pb.IntelligenceDataDetailRequest, rsp *pb.IntelligenceData) error {
	if req.DataId < 1 {
		return errors.BadRequest(pb.ServiceName, "data_id不能为空")
	}
	data, err := intelligence.NewData().GetById(uint(req.DataId))
	if err != nil {
		return err
	}
	rsp.Id = uint32(data.ID)
	rsp.SpecialProjectName = data.SpecialProjectName
	rsp.EventName = data.EventName
	rsp.SpecialProjectCategory = data.SpecialProjectCategory
	rsp.Overview = data.Overview
	rsp.DisclosureTime = data.DisclosureTime.Format(time.DateTime)
	rsp.LeakSourceIp = data.LeakSourceIP
	rsp.IpRelatedLocation = data.IPRelatedLocation
	rsp.LeakServiceComponent = data.LeakServiceComponent
	rsp.DataVolume = int32(data.DataVolume)
	rsp.DataContent = data.DataContent
	rsp.DataEntity = data.DataEntity
	rsp.DataEntityLocation = data.DataEntityLocation
	rsp.LeakReason = data.LeakReason
	rsp.FixSolution = data.FixSolution
	rsp.HasMaskedReport = data.MaskedReportLocalLink != ""
	return nil
}

// IntelligenceDataUploadReport 情报中心-数据专项-上传报告
func (*Web) IntelligenceDataUploadReport(ctx context.Context, req *pb.IntelligenceDataUploadReportRequest, rsp *pb.IntelligenceUploadReportResponse) error {
	data, err := intelligence.NewData().GetById(uint(req.DataId))
	if err != nil {
		return err
	}
	//保存文件到磁盘
	fileName := fmt.Sprintf("%s-Upload.pdf", data.EventName)
	err = storage.DeleteByFileName(fileName)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, "文件删除失败")
	}
	path := storage.SaveIntelligenceReport(req.FileContent, fileName)
	if path == "" {
		return errors.BadRequest(pb.ServiceName, "文件上传失败")
	}
	//修改数据库
	now := time.Now()
	updateData := intelligence.Data{}
	updateData.ID = data.ID
	updateData.MaskedReportLocalLink = path
	updateData.MaskedReportCreatedId = req.UserId
	updateData.MaskedReportUpdatedTime = &now
	err = intelligence.NewData().Update(&updateData)
	if err != nil {
		return errors.BadRequest(pb.ServiceName, "数据保存失败")
	}

	rsp.MaskedReportLocalLink = path
	return nil
}

// IntelligenceDataDownloadMaskedReport 情报中心-数据专项-下载掩码报告
func (*Web) IntelligenceDataDownloadMaskedReport(ctx context.Context, req *pb.IntelligenceDataDownloadReportRequest, rsp *pb.IntelligenceDownloadReportResponse) error {
	data, err := intelligence.NewData().GetById(uint(req.DataId))
	if err != nil {
		return err
	}
	if data.MaskedReportLocalLink == "" {
		return fmt.Errorf("脱敏报告不存在")
	}
	rsp.FilePath = fmt.Sprintf("%s%s", storage.GetRootPath(), data.MaskedReportLocalLink)
	rsp.FileName = fmt.Sprintf("%s%s.pdf", data.EventName, path.Ext(data.MaskedReportLocalLink))
	return nil
}

// IntelligenceDataDownloadReport 情报中心-数据专项-下载报告
func (*Web) IntelligenceDataDownloadReport(ctx context.Context, req *pb.IntelligenceDataDownloadReportRequest, rsp *pb.IntelligenceDataDownloadReportResponse) error {
	data, err := intelligence.NewData().GetById(uint(req.DataId))
	if err != nil {
		return err
	}
	if data.LocalLink == "" {
		return fmt.Errorf("报告不存在")
	}
	rsp.FilePath = fmt.Sprintf("%s%s", storage.GetRootPath(), data.LocalLink)
	rsp.FileName = fmt.Sprintf("%s%s.pdf", data.EventName, path.Ext(data.LocalLink))
	return nil
}

// IntelligenceEventUserMatch 情报中心-事件专项-一键匹配
func (*Web) IntelligenceEventUserMatch(ctx context.Context, req *pb.IntelligenceEventUserMatchRequest, rsp *pb.Empty) error {
	if intelligence_event.CheckTaskRunning(req.UserId) {
		return errors.BadRequest(pb.ServiceName, "任务正在运行")
	}
	intelligence_event.AddTask(req.UserId)
	req.Id = utils.ListDistinctNonZero(req.Id)
	list := make([]*intelligence.Event, 0)
	queries := make([]mysql.HandleFunc, 0)
	if len(req.Id) > 0 {
		queries = append(queries, mysql.WithValuesIn("id", req.Id))
	} else {
		if req.Name != "" {
			queries = append(queries, mysql.WithLike("name", req.Name))
		}
		if req.Category != "" {
			queries = append(queries, mysql.WithColumnValue("category", req.Category))
		}
		if len(req.DisclosureTime) > 0 {
			queries = append(queries, mysql.WithBetweenAt("disclosure_time", req.DisclosureTime))
		}
	}
	// 查询事件列表
	if l, err := intelligence.NewEvent().ListAll(queries...); err != nil {
		intelligence_event.DeleteTask(req.UserId)
		return err
	} else {
		list = append(list, l...)
	}
	intelligence_event.GetTaskInfo().Tasks.Store(req.UserId, 5)
	// 执行匹配
	go intelligence_event.MatchEvent(req.UserId, list)
	log.Info(fmt.Sprintf("IntelligenceEventUserMatch: userId:%d 下发任务!", req.UserId))
	return nil
}

// IntelligenceEventUserMatchProgress 情报中心-事件专项-匹配进度
func (*Web) IntelligenceEventUserMatchProgress(ctx context.Context, req *pb.IntelligenceEventUserMatchProgressRequest, rsp *pb.IntelligenceEventUserMatchProgressResponse) error {
	rsp.Progress = cast.ToFloat32(intelligence_event.GetTaskProcess(req.UserId))
	if cast.ToInt(rsp.Progress) >= 100 {
		intelligence_event.DeleteTask(req.UserId)
		time.Sleep(1 * time.Second)
	}
	return nil
}

// IntelligenceDataUserMatch 情报中心-数据专项-一键匹配
func (*Web) IntelligenceDataUserMatch(ctx context.Context, req *pb.IntelligenceDataUserMatchRequest, rsp *pb.Empty) error {
	if intelligence_data.CheckTaskRunning(req.UserId) {
		return errors.BadRequest(pb.ServiceName, "任务正在运行")
	}
	intelligence_data.AddTask(req.UserId)
	req.Id = utils.ListDistinctNonZero(req.Id)
	list := make([]*intelligence.Data, 0)
	queries := make([]mysql.HandleFunc, 0)
	if len(req.Id) > 0 {
		spnList, err := intelligence.NewDataSummary().DistinctColumn("special_project_name", mysql.WithValuesIn("id", req.Id))
		if err != nil {
			intelligence_data.DeleteTask(req.UserId)
			return errors.BadRequest(pb.ServiceName, "查询数据专项名称失败")
		}
		if len(spnList) < 1 {
			intelligence_data.DeleteTask(req.UserId)
			return errors.BadRequest(pb.ServiceName, "找不到要检测的数据专项")
		}
		queries = append(queries, mysql.WithValuesIn("special_project_name", spnList))
	} else {
		if req.Name != "" {
			queries = append(queries, mysql.WithLike("name", req.Name))
		}
		if len(req.UpdateTime) > 0 {
			queries = append(queries, mysql.WithBetweenAt("disclosure_time", req.UpdateTime))
		}
	}
	if l, err := intelligence.NewData().ListAll(queries...); err != nil {
		intelligence_data.DeleteTask(req.UserId)
		return err
	} else {
		list = append(list, l...)
	}
	intelligence_data.GetTaskInfo().Tasks.Store(req.UserId, 5)
	go intelligence_data.MatchData(req.UserId, list)
	log.Info(fmt.Sprintf("IntelligenceDataUserMatch: userId:%d 下发任务!", req.UserId))
	return nil
}

// IntelligenceDataUserMatchProgress 情报中心-数据专项-匹配进度
func (*Web) IntelligenceDataUserMatchProgress(ctx context.Context, req *pb.IntelligenceDataUserMatchProgressRequest, rsp *pb.IntelligenceDataUserMatchProgressResponse) error {
	rsp.Progress = cast.ToFloat32(intelligence_data.GetTaskProcess(req.UserId))
	if cast.ToInt(rsp.Progress) >= 100 {
		intelligence_data.DeleteTask(req.UserId)
		time.Sleep(1 * time.Second)
	}
	return nil
}

// IntelligenceRelatedList 情报中心-关联情报-列表
func (*Web) IntelligenceRelatedList(ctx context.Context, req *pb.IntelligenceRelatedListRequest, rsp *pb.IntelligenceRelatedListResponse) error {
	query := intelligenceRelatedListRequestToQuery(req)
	// 排序条件
	sort := strings.Join(req.Sort, ",")
	// id 作为兜底条件
	if sort == "" {
		sort = "id desc"
	} else {
		if !strings.Contains(sort, "id desc") && !strings.Contains(sort, "id asc") {
			sort += ",id desc"
		}
	}
	query = append(query, mysql.WithOrder(sort))

	related, total, err := intelligence.NewRelatedIntelligence().List(int(req.Page), int(req.PerPage), query...)
	if err != nil {
		return err
	}
	rsp.Total = total
	rsp.CurrentPage = req.Page
	rsp.Page = req.PerPage
	rsp.Items = make([]*pb.IntelligenceRelatedItem, 0)
	for _, v := range related {
		rsp.Items = append(rsp.Items, &pb.IntelligenceRelatedItem{
			Id:                  v.ID,
			AssetId:             v.AssetID,
			EnterpriseId:        fmt.Sprintf("%d", v.EnterpriseID),
			EnterpriseName:      v.EnterpriseName,
			IntelligenceId:      fmt.Sprintf("%d", v.IntelligenceID),
			RiskType:            v.RiskType,
			RiskName:            v.RiskName,
			SpecialProjectName:  v.SpecialProjectName,
			AssetIp:             v.AssetIP,
			AssetPort:           v.AssetPort,
			AssetProtocol:       v.AssetProtocol,
			AssetDomain:         v.AssetDomain,
			AssetUrl:            v.AssetURL,
			AssetTitle:          v.AssetTitle,
			AssetStatus:         v.AssetStatus,
			ServiceComponent:    v.ServiceComponent,
			FoundTime:           v.FoundTime,
			UpdateTime:          v.UpdateTime,
			IntelligenceTags:    v.IntelligenceTags,
			IntelligenceCountry: v.IntelligenceCountry,
			IntelligenceType:    v.IntelligenceType,
		})
	}
	return nil
}

// intelligenceRelatedListRequestToQuery 将请求参数转换为查询条件
func intelligenceRelatedListRequestToQuery(req *pb.IntelligenceRelatedListRequest) []mysql.HandleFunc {
	query := make([]mysql.HandleFunc, 0)
	if req.UserId > 0 {
		query = append(query, mysql.WithColumnValue("user_id", req.UserId))
	}
	if req.OperateCompanyId > 0 {
		query = append(query, mysql.WithColumnValue("enterprise_id", req.OperateCompanyId))
	}
	if req.Keyword != "" {
		// 风险类型，1: 热点漏洞，2: 威胁情报，3: 专项事件 4: 数据泄露
		switch req.RiskType {
		case "1":
			query = append(query, mysql.WithSQl("asset_ip like '%"+req.Keyword+"%' or risk_name like '%"+req.Keyword+"%'"))
		case "2":
			query = append(query, mysql.WithSQl("asset_ip like '%"+req.Keyword+"%' or asset_domain = '"+req.Keyword+"' or intelligence_type = '"+req.Keyword+"'"))
		case "3":
			query = append(query, mysql.WithSQl("asset_ip like '%"+req.Keyword+"%' or special_project_name like '%"+req.Keyword+"%' or intelligence_type like '%"+req.Keyword+"%'"))
		case "4":
			query = append(query, mysql.WithSQl("asset_ip like '%"+req.Keyword+"%' or special_project_name like '%"+req.Keyword+"%'"))
		}
	}
	if len(req.RiskName) > 0 {
		req.RiskName = utils.ListDistinctNonZero(req.RiskName)
		query = append(query, mysql.WithValuesIn("risk_name", req.RiskName))
	}
	if len(utils.ListDistinctNonZero(req.SpecialProjectName)) > 0 {
		query = append(query, mysql.WithValuesIn("special_project_name", req.SpecialProjectName))
	}
	if len(utils.ListDistinctNonZero(req.AssetIp)) > 0 {
		query = append(query, mysql.WithValuesIn("asset_ip", req.AssetIp))
	}
	if len(utils.ListDistinctNonZero(req.AssetPort)) > 0 {
		query = append(query, mysql.WithValuesIn("asset_port", req.AssetPort))
	}
	if len(utils.ListDistinctNonZero(req.AssetProtocol)) > 0 {
		query = append(query, mysql.WithValuesIn("asset_protocol", req.AssetProtocol))
	}
	if req.RiskType != "" {
		query = append(query, mysql.WithColumnValue("risk_type", req.RiskType))
	}
	if len(utils.ListDistinctNonZero(req.AssetUrl)) > 0 {
		query = append(query, mysql.WithValuesIn("asset_url", req.AssetUrl))
	}
	if len(utils.ListDistinctNonZero(req.AssetTitle)) > 0 {
		query = append(query, mysql.WithValuesIn("asset_title", req.AssetTitle))
	}
	if len(utils.ListDistinctNonZero(req.AssetDomain)) > 0 {
		query = append(query, mysql.WithValuesIn("asset_domain", req.AssetDomain))
	}
	if req.AssetStatus != "" {
		query = append(query, mysql.WithColumnValue("asset_status", req.AssetStatus))
	}
	if len(utils.ListDistinctNonZero(req.IntelligenceType)) > 0 {
		query = append(query, mysql.WithValuesIn("intelligence_type", req.IntelligenceType))
	}
	if len(utils.ListDistinctNonZero(req.IntelligenceTags)) > 0 {
		scSQl := make([]string, 0)
		for _, v := range req.IntelligenceTags {
			scSQl = append(scSQl, "intelligence_tags like '%"+v+"%'")
		}
		query = append(query, mysql.WithSQl(strings.Join(scSQl, " or ")))
	}
	if len(utils.ListDistinctNonZero(req.IntelligenceCountry)) > 0 {
		query = append(query, mysql.WithValuesIn("intelligence_country", req.IntelligenceCountry))
	}
	req.ServiceComponent = utils.ListDistinctNonZero(req.ServiceComponent)
	if len(utils.ListDistinctNonZero(req.ServiceComponent)) > 0 {
		scSQl := make([]string, 0)
		for _, v := range req.ServiceComponent {
			scSQl = append(scSQl, "service_component like '%"+v+"%'")
		}
		query = append(query, mysql.WithSQl(strings.Join(scSQl, " or ")))
	}
	req.FoundTime = utils.FormattingTime(req.FoundTime)
	if len(req.FoundTime) > 0 {
		query = append(query, mysql.WithBetweenAtOriginal("found_time", req.FoundTime))
	}
	req.UpdateTime = utils.FormattingTime(req.UpdateTime)
	if len(req.UpdateTime) > 0 {
		query = append(query, mysql.WithBetweenAtOriginal("update_time", req.UpdateTime))
	}
	return query
}

// IntelligenceRelatedExport 情报中心-关联情报-导出
func (*Web) IntelligenceRelatedExport(ctx context.Context, req *pb.IntelligenceRelatedExportRequest, rsp *pb.IntelligenceRelatedExportResponse) error {
	query := make([]mysql.HandleFunc, 0)
	if len(req.Id) > 0 {
		query = append(query, mysql.WithValuesIn("id", req.Id))
	} else {
		if req.UserId > 0 {
			query = append(query, mysql.WithColumnValue("user_id", req.UserId))
		}
		if len(utils.ListDistinctNonZero(req.RiskName)) > 0 {
			if len(req.RiskName) == 1 {
				query = append(query, mysql.WithLRLike("risk_name", req.RiskName[0]))
			} else {
				query = append(query, mysql.WithValuesIn("risk_name", req.RiskName))
			}
		}
		if len(utils.ListDistinctNonZero(req.AssetIp)) > 0 {
			query = append(query, mysql.WithValuesIn("asset_ip", req.AssetIp))
		}
		if len(utils.ListDistinctNonZero(req.AssetPort)) > 0 {
			query = append(query, mysql.WithValuesIn("asset_port", req.AssetPort))
		}
		if len(utils.ListDistinctNonZero(req.AssetProtocol)) > 0 {
			query = append(query, mysql.WithValuesIn("asset_protocol", req.AssetProtocol))
		}
		if req.RiskType != "" {
			query = append(query, mysql.WithColumnValue("risk_type", req.RiskType))
		}
		if len(utils.ListDistinctNonZero(req.AssetUrl)) > 0 {
			query = append(query, mysql.WithValuesIn("asset_url", req.AssetUrl))
		}
		if len(utils.ListDistinctNonZero(req.AssetTitle)) > 0 {
			query = append(query, mysql.WithValuesIn("asset_title", req.AssetTitle))
		}
		if len(utils.ListDistinctNonZero(req.AssetDomain)) > 0 {
			query = append(query, mysql.WithValuesIn("asset_domain", req.AssetDomain))
		}
		if req.AssetStatus != "" {
			query = append(query, mysql.WithColumnValue("asset_status", req.AssetStatus))
		}
		if len(utils.ListDistinctNonZero(req.IntelligenceType)) > 0 {
			query = append(query, mysql.WithValuesIn("intelligence_type", req.IntelligenceType))
		}
		if len(utils.ListDistinctNonZero(req.IntelligenceTags)) > 0 {
			scSQl := make([]string, 0)
			for _, v := range req.IntelligenceTags {
				scSQl = append(scSQl, "intelligence_tags like '%"+v+"%'")
			}
			query = append(query, mysql.WithSQl(strings.Join(scSQl, " or ")))
		}
		if len(utils.ListDistinctNonZero(req.IntelligenceCountry)) > 0 {
			query = append(query, mysql.WithValuesIn("intelligence_country", req.IntelligenceCountry))
		}
		req.ServiceComponent = utils.ListDistinctNonZero(req.ServiceComponent)
		if len(utils.ListDistinctNonZero(req.ServiceComponent)) > 0 {
			scSQl := make([]string, 0)
			for _, v := range req.ServiceComponent {
				scSQl = append(scSQl, "service_component like '%"+v+"%'")
			}
			query = append(query, mysql.WithSQl(strings.Join(scSQl, " or ")))
		}
		req.FoundTime = utils.FormattingTime(req.FoundTime)
		if len(req.FoundTime) > 0 {
			query = append(query, mysql.WithBetweenAtOriginal("found_time", req.FoundTime))
		}
		req.UpdateTime = utils.FormattingTime(req.UpdateTime)
		if len(req.UpdateTime) > 0 {
			query = append(query, mysql.WithBetweenAtOriginal("update_time", req.UpdateTime))
		}
	}
	// 排序条件
	sort := strings.Join(req.Sort, ",")
	// id 作为兜底条件
	if sort == "" {
		sort = "id desc"
	} else {
		if !strings.Contains(sort, "id desc") && !strings.Contains(sort, "id asc") {
			sort += ",id desc"
		}
	}
	query = append(query, mysql.WithOrder(sort))
	related, err := intelligence.NewRelatedIntelligence().ListAll(query...)
	if err != nil {
		return err
	}
	convertState := func(s string) string {
		if s == "1" {
			return "在线"
		}
		return "离线"
	}
	data := make([][]string, 0)
	riskType := ""
	switch req.RiskType {
	case "1":
		riskType = "热点漏洞"
		data = append(data, []string{"IP", "端口", "协议", "组件", "风险类型", "热点漏洞", "URL", "标题", "状态", "发现时间", "更新时间"})
		for _, v := range related {
			data = append(data, []string{
				v.AssetIP, v.AssetPort, v.AssetProtocol, v.ServiceComponent, riskType, v.RiskName, v.AssetURL, v.AssetTitle, convertState(v.AssetStatus), v.FoundTime, v.UpdateTime,
			})
		}
	case "2":
		riskType = "威胁情报"
		data = append(data, []string{"IP", "端口", "域名", "标签", "国家", "风险类型", "风险名称/URL", "URL", "标题", "状态", "发现时间", "更新时间"})
		for _, v := range related {
			data = append(data, []string{
				v.AssetIP, v.AssetPort, v.AssetDomain, v.IntelligenceTags, v.IntelligenceCountry, riskType, v.RiskName, v.AssetURL, v.AssetTitle, convertState(v.AssetStatus), v.FoundTime, v.UpdateTime,
			})
		}
	case "3":
		riskType = "专项事件"
		data = append(data, []string{"IP", "端口", "组件", "状态", "情报类型", "风险名称/URL", "URL", "标题", "发现时间", "更新时间"})
		for _, v := range related {
			data = append(data, []string{
				v.AssetIP, v.AssetPort, v.ServiceComponent, convertState(v.AssetStatus), v.IntelligenceType, v.RiskName, v.AssetURL, v.AssetTitle, v.FoundTime, v.UpdateTime,
			})
		}
	case "4":
		riskType = "数据泄露"
		data = append(data, []string{"IP", "端口", "组件", "企业名称", "专项名称", "事件名称", "URL", "标题", "发现时间", "更新时间"})
		for _, v := range related {
			data = append(data, []string{
				v.AssetIP, v.AssetPort, v.ServiceComponent, v.EnterpriseName, v.SpecialProjectName, v.RiskName, v.AssetURL, v.AssetTitle, v.FoundTime, v.UpdateTime,
			})
		}
	default:
		riskType = "未知"
		return errors.BadRequest(pb.ServiceName, "风险类型错误")
	}
	fileName := fmt.Sprintf("关联情报列表_%s_%d.xlsx", riskType, time.Now().Unix())
	path := storage.GenDownloadLocalPath(fileName, req.UserId)
	if err = excel.WriteExcel(path, data); err != nil {
		return err
	}
	rsp.FilePath = path
	rsp.FileName = fileName
	rsp.Url = storage.GenAPIDownloadPath(filepath.Base(path), path)
	return nil
}

// IntelligenceRelatedCondition 情报中心-关联情报-高级搜索条件数据源
func (*Web) IntelligenceRelatedCondition(ctx context.Context, req *pb.IntelligenceRelatedConditionRequest, rsp *pb.IntelligenceRelatedConditionResponse) error {
	query := make([]mysql.HandleFunc, 0)
	if req.OperateCompanyId > 0 {
		query = append(query, mysql.WithWhere("enterprise_id", req.OperateCompanyId))
	}
	if req.RiskType != "" {
		query = append(query, mysql.WithColumnValue("risk_type", req.RiskType))
	}
	query = append(query, mysql.WithColumnValue("user_id", req.UserId))
	fieldList := []string{"asset_port", "asset_status", "risk_name", "enterprise_name", "asset_title", "asset_domain", "asset_url",
		"special_project_name", "intelligence_type", "intelligence_tags", "intelligence_country", "service_component"}
	var wg sync.WaitGroup
	wg.Add(len(fieldList))
	for _, v := range fieldList {
		go func(field string, q ...mysql.HandleFunc) {
			defer wg.Done()
			originalField := field
			queryTemp := query
			queryTemp = append(queryTemp, mysql.WithColumnNotNull(field))
			queryTemp = append(queryTemp, mysql.WithColumnNotValue(field, ""))
			if list, err := intelligence.NewRelatedIntelligence().DistinctColumn(field, queryTemp...); err == nil {
				sort.Strings(list)
				switch originalField {
				case "asset_port":
					rsp.AssetPort = list
				case "asset_status":
					rsp.AssetStatus = list
				case "risk_name":
					rsp.RiskName = list
				case "enterprise_name":
					rsp.EnterpriseName = list
				case "asset_title":
					rsp.AssetTitle = list
				case "asset_domain":
					rsp.AssetDomain = list
				case "asset_url":
					rsp.AssetUrl = list
				case "special_project_name":
					rsp.SpecialProjectName = list
				case "intelligence_type":
					rsp.IntelligenceType = list
				case "intelligence_tags":
					if len(list) > 0 {
						for _, v := range list {
							rsp.IntelligenceTags = append(rsp.IntelligenceTags, strings.Split(v, ",")...)
						}
						rsp.IntelligenceTags = utils.ListDistinctNonZero(rsp.IntelligenceTags)
					}
				case "intelligence_country":
					rsp.IntelligenceCountry = list
				case "service_component":
					if len(list) > 0 {
						for _, v := range list {
							rsp.ServiceComponent = append(rsp.ServiceComponent, strings.Split(v, ",")...)
						}
						rsp.ServiceComponent = utils.ListDistinctNonZero(rsp.ServiceComponent)
					}
				}
			}
		}(v, query...)
	}
	wg.Wait()
	return nil
}
