package api_counter

import (
	"time"

	"micro-service/middleware/mysql"
	apicounter "micro-service/middleware/mysql/api_counter"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

func RequestCountList(req *pb.ApiRequestCountListRequest, rsp *pb.ApiRequestCountListResponse) error {
	handlers := make([]mysql.HandleFunc, 0)
	if req.ClientId != "" {
		handlers = append(handlers, mysql.WithLRLike("`client_id`", req.ClientId))
	}
	if req.Year == 0 {
		req.Year = int64(time.Now().Year())
	}
	if req.Month == 0 {
		req.Month = int64(time.Now().Month())
	}
	handlers = append(handlers, mysql.WithColumnValue("`year`", req.Year))
	handlers = append(handlers, mysql.WithColumnValue("`month`", req.Month))
	if req.Search != "" {
		handlers = append(handlers, apicounter.WithSearch(req.Search))
	}
	handlers = append(handlers, mysql.WithOrder("`updated_at` DESC"))

	list, total, err := apicounter.NewCounter().List(int(req.Page), int(req.PerPage), handlers...)
	if err != nil {
		return err
	}

	rsp.Total = total
	rsp.Page = req.Page
	rsp.PerPage = req.PerPage
	for i := range list {
		rsp.Items = append(rsp.Items, &pb.ApiRequestCountListResponse_Item{
			Id:        list[i].Id,
			CreatedAt: list[i].CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt: list[i].UpdatedAt.Format(utils.DateTimeLayout),
			Year:      list[i].Year,
			Month:     list[i].Month,
			Total:     list[i].Total,
			Path:      list[i].Path,
		})
	}
	return nil
}
