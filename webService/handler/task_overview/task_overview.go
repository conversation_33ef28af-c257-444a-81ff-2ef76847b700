package task_overview

import (
	"context"
	"sync"
	"time"

	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/middleware/elastic/fofaee_assets"
	middlemysql "micro-service/middleware/mysql"
	"micro-service/middleware/mysql/asset_audit_tasks"
	"micro-service/middleware/mysql/assets_status_detect"
	dat "micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/domain_task"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
)

const daySecond = 24 * time.Hour

const (
	countByWeek    = iota + 1 // 周
	countByMonth              // 月
	countByQuarter            // 季度
)

func countTypeParse(ct int64) time.Time {
	var after time.Time // if after is zero, count all tasks
	now := time.Now()
	switch ct {
	case countByWeek:
		after = now.Add(-7 * daySecond)
	case countByMonth:
		after = now.Add(-30 * daySecond)
	case countByQuarter:
		after = now.Add(-90 * daySecond)
	}

	return after
}

func logErrorf(ctx context.Context, format string, err error, args ...any) {
	if err != nil {
		log.WithContextErrorf(ctx, format, args...)
	}
}

func TaskOverviewCountByCategory(ctx context.Context, req *pb.TaskOverviewCountByCategoryRequest, rsp *pb.TaskOverviewCountByCategoryResponse) error {
	after := countTypeParse(req.CountType)

	wg := &sync.WaitGroup{}
	wg.Add(6)
	// 云端资产推荐
	go func() {
		rsp.RecommendRecord = new(pb.TaskOverviewCountByCategoryResponse_AssetRecommendation)
		err := detectAssetTaskCount(req.UserId, after, dat.ExpandSourceRecommend, rsp.RecommendRecord)
		logErrorf(ctx, "[任务概览-云端资产推荐]%v", err, err)
		wg.Done()
	}()
	// 单位资产测绘
	go func() {
		rsp.DetectAssetTask = new(pb.TaskOverviewCountByCategoryResponse_AssetRecommendation)
		err := detectAssetTaskCount(req.UserId, after, dat.ExpandSourceDetect, rsp.DetectAssetTask)
		logErrorf(ctx, "[任务概览-单位资产测绘]%v", err, err)
		wg.Done()
	}()
	// 扫描任务
	go func() {
		rsp.ScanTask = new(pb.TaskOverviewCountByCategoryResponse_ScanTask)
		err := scanTaskCount(req.UserId, after, rsp.ScanTask)
		logErrorf(ctx, "[任务概览-扫描任务]%v", err, err)
		wg.Done()
	}()
	// 资产核查
	go func() {
		rsp.AssetAudit = new(pb.TaskOverviewCountByCategoryResponse_AssetAudit)
		err := assetAuditCount(uint(req.UserId), after, rsp.AssetAudit)
		logErrorf(ctx, "[任务概览-资产核查]%v", err, err)
		wg.Done()
	}()
	// 资产状态检测
	go func() {
		rsp.AssetStatus = new(pb.TaskOverviewCountByCategoryResponse_AssetStatus)
		err := assetStatusCount(uint(req.UserId), after, rsp.AssetStatus)
		logErrorf(ctx, "[任务概览-资产状态检测]%v", err, err)
		wg.Done()
	}()
	// 域名发现任务
	go func() {
		rsp.DomainFind = new(pb.TaskOverviewCountByCategoryResponse_DomainFind)
		err := domainFindCount(uint(req.UserId), after, rsp.DomainFind)
		logErrorf(ctx, "[任务概览-域名发现任务]%v", err, err)
		wg.Done()
	}()

	wg.Wait()

	return nil
}

// 资产扫描任务
func scanTaskCount(userID int64, after time.Time, rsp *pb.TaskOverviewCountByCategoryResponse_ScanTask) error {
	isFetchAll := after.IsZero() // true: get all, false: 时间趋势统计(仅统计任务数)

	handlers := []middlemysql.HandleFunc{middlemysql.WithColumnValue("user_id", userID)}
	handlers = append(handlers, middlemysql.WithColumnValue("task_type", scan_task.ScanTypeAsset))
	if !isFetchAll {
		handlers = append(handlers, middlemysql.WithGT("created_at", after))
	}
	list, err := scan_task.NewScanTasksModel(mysql.GetInstance()).FindAll(handlers...)
	rsp.Total = int64(len(list))
	if !isFetchAll || len(list) == 0 {
		return err
	}

	ids := make([]uint, 0, rsp.Total) // 任务ID
	for i := range list {
		ids = append(ids, list[i].ID)
	}

	// ip数量
	ips, err := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance()).CountIpByUserTask(int(userID), ids)
	if err != nil {
		return err
	}

	// 组件数量
	rules, err := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance()).CountRuleByUserTask(int(userID), ids)
	if err != nil {
		return err
	}

	rsp.Ip = int64(ips)
	rsp.Component = int64(rules)

	return nil
}

// 单位资产测绘/云端资产推荐
func detectAssetTaskCount(userId int64, after time.Time, source int, rsp *pb.TaskOverviewCountByCategoryResponse_AssetRecommendation) error {
	isFetchAll := after.IsZero() // true: get all, false: 时间趋势统计(仅统计任务数)

	handlers := []middlemysql.HandleFunc{middlemysql.WithColumnValue("user_id", userId)}
	handlers = append(handlers, middlemysql.WithColumnValue("expand_source", source))
	handlers = append(handlers, middlemysql.WithSelect("sure_ip_num,unsure_ip_num,threaten_ip_num"))
	if !isFetchAll {
		handlers = append(handlers, middlemysql.WithGT("created_at", after))
	}

	// 获取单位资产测绘任务数据
	list, err := dat.NewModel().FindAll(handlers...)
	if err != nil || len(list) == 0 {
		return err
	}

	// var ids = make([]uint, 0, len(list))
	for i := range list {
		rsp.Total++                                              // 任务总数
		rsp.AccountAssets += cast.ToInt64(list[i].SureIpNum)     // 台账资产
		rsp.SuspectedAssets += cast.ToInt64(list[i].UnsureIpNum) // 疑似资产
		rsp.ThreatAssets += cast.ToInt64(list[i].ThreatenIpNum)  // 威胁资产
	}

	// l, err := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance()).GroupByStatusWithDetectTask(uint(userId), ids)
	// if err != nil {
	// 	return err
	// }

	// for i := range l {
	// 	switch l[i].Status {
	// 	case fofaee_assets.StatusConfirmAsset, fofaee_assets.StatusUploadAsset: // 台账 = 已确认 + 导入
	// 		rsp.AccountAssets += l[i].Total
	// 	case fofaee_assets.StatusSuspectedAsset: // 疑似资产
	// 		rsp.SuspectedAssets += l[i].Total
	// 	case fofaee_assets.StatusThreatAsset: // 威胁资产
	// 		rsp.ThreatAssets += l[i].Total
	// 	}
	// }

	return nil
}

// 资产核查任务统计
func assetAuditCount(userId uint, after time.Time, rsp *pb.TaskOverviewCountByCategoryResponse_AssetAudit) error {
	client := asset_audit_tasks.NewAssetAuditor()
	handles := []middlemysql.HandleFunc{middlemysql.WithSelect("assets_included,assets_not_included")}
	handles = append(handles, middlemysql.WithColumnValue("user_id", userId))
	if !after.IsZero() {
		handles = append(handles, middlemysql.WithGT("created_at", after))
	}
	l, total, err := client.List(0, 0, handles...) // 获取所有结果
	if err != nil {
		return err
	}

	for i := range l {
		rsp.IncludedAssets += int64(l[i].AssetsIncluded)       // 纳入管理资产
		rsp.NotIncludedAssets += int64(l[i].AssetsNotIncluded) // 未纳入管理资产
	}
	rsp.Total = total // 任务总数

	return nil
}

// 资产状态监测统计
func assetStatusCount(userId uint, recentTime time.Time, rsp *pb.TaskOverviewCountByCategoryResponse_AssetStatus) error {
	client := assets_status_detect.NewAssetsStatusDetectTaskModel(mysql.GetInstance())
	l, err := client.FindAll(int64(userId), recentTime)
	if err != nil {
		return err
	}

	for i := range l {
		rsp.Total++                              // 任务总数
		rsp.OnlineAssets += l[i].OnlineAssets    // 在线资产
		rsp.OfflineAssets += l[i].UnonlineAssets // 离线资产
	}

	return nil
}

// 域名发现任务统计
func domainFindCount(userId uint, recentTime time.Time, rsp *pb.TaskOverviewCountByCategoryResponse_DomainFind) (err error) {
	client := domain_task.NewDomainTaskModel(mysql.GetInstance())
	handlers := []middlemysql.HandleFunc{middlemysql.WithColumnValue("user_id", userId)}
	if !recentTime.IsZero() {
		handlers = append(handlers, middlemysql.WithGT("created_at", recentTime))
	}
	l, total, err := client.List(0, 0, handlers...)
	if err != nil {
		return err
	}

	for i := range l {
		switch l[i].Modify {
		case domain_task.BRUST_TYPE:
			rsp.EnumType++
		case domain_task.VERIFY_TYPE:
			rsp.VerifyType++
		}
	}
	rsp.Total = total

	return nil
}
