package task_overview

import (
	"context"
	"strconv"
	"sync"

	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/middleware/elastic/recommend_record"
	middlemysql "micro-service/middleware/mysql"
	aat "micro-service/middleware/mysql/asset_audit_tasks"
	asd "micro-service/middleware/mysql/assets_status_detect"
	detectassetstasks "micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/domain_task"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

func TaskOverviewCount(ctx context.Context, req *pb.TaskOverviewCountRequest, rsp *pb.TaskOverviewCountResponse) error {
	var result taskCount
	var err error
	lock := &sync.Mutex{}

	wg := &sync.WaitGroup{}
	wg.Add(6)
	async := func(f func(uint) (taskCount, error)) {
		lock.Lock()
		result, err = f(uint(req.UserId))
		if err != nil {
			log.WithContextErrorf(ctx, "[任务概览]任务统计: %+v", err)
		}
		rsp.Total += result.total
		rsp.Doing += result.doing
		rsp.Wait += result.wait
		lock.Unlock()

		wg.Done()
	}

	go async(assetAuditCountWithStatus)      // 资产核查
	go async(assetStatusCountWithStatus)     // 资产状态检测
	go async(domainTaskCountWithStatus)      // 域名发现
	go async(scanTaskCountWithStatus)        // 扫描任务
	go async(recommendTaskCountWithStatus)   // 单位资产测绘
	go async(departAssetTaskCountWithStatus) // 云端资产推荐

	wg.Wait()

	return nil
}

type taskCount struct {
	total int64
	doing int64
	wait  int64
}

// 资产核查根据状态统计
func assetAuditCountWithStatus(userId uint) (result taskCount, err error) {
	client := aat.NewAssetAuditor(mysql.GetInstance())

	l, err := client.GroupStatus(middlemysql.WithColumnValue("user_id", userId))
	if err != nil {
		return
	}

	for i := range l {
		result.total += l[i].Count // 统计各个status的总和
		if l[i].Status == aat.AuditStatusDoing {
			result.doing += l[i].Count // 进行中
		}
	}
	result.wait = 0 // 资产审核无等待状态

	return
}

// 资产状态根据状态统计
func assetStatusCountWithStatus(userId uint) (result taskCount, err error) {
	client := asd.NewAssetsStatusDetectTaskModel(mysql.GetInstance())
	l, err := client.GroupStatus(middlemysql.WithColumnValue("user_id", userId))
	if err != nil {
		return
	}

	for i := range l {
		result.total += l[i].Count // 任务总数
		if l[i].Status == asd.TaskProgressStateIng {
			result.doing += l[i].Count // 任务进行中
		}
	}
	result.wait = 0 // 资产审核无等待状态

	return
}

// 域名任务: 根据状态统计
func domainTaskCountWithStatus(userId uint) (result taskCount, err error) {
	client := domain_task.NewDomainTaskModel(mysql.GetInstance())

	f := func(status int) int64 {
		var total int64
		if err == nil {
			handler := []middlemysql.HandleFunc{middlemysql.WithColumnValue("user_id", userId)}
			if status != -1 {
				handler = append(handler, middlemysql.WithColumnValue("status", status))
			}
			total, err = client.Count(handler...)
		}
		return total
	}

	result.total = f(-1)                       // 所有任务
	result.doing = f(domain_task.StatusDoing)  // 进行中
	result.wait = f(domain_task.StatusDefault) // 等待中

	return
}

// 扫描任务: 根据状态统计
func scanTaskCountWithStatus(userId uint) (result taskCount, err error) {
	handlers := []middlemysql.HandleFunc{middlemysql.WithColumnValue("task_type", scan_task.ScanTypeAsset)}
	handlers = append(handlers, middlemysql.WithColumnValue("user_id", userId))

	client := scan_task.NewScanTasksModel(mysql.GetInstance())
	l, err := client.GroupStatus(handlers...)
	if err != nil {
		return
	}

	for i := range l {
		result.total += l[i].Count                // 所有
		if l[i].Status == scan_task.StatusDoing { // 进行中
			result.doing += l[i].Count
		} else if l[i].Status == scan_task.StatusWaiting { // 等待中
			result.wait += l[i].Count
		}
	}

	return
}

// 云端资产推荐: 根据状态统计
//
//nolint:unused,gocritic
func recommendRecordCountWithStatus(userId uint) (result taskCount, err error) {
	client := recommend_record.NewRecommendRecordModel(es.GetInstance())

	f := func(status int) int64 {
		var total int64
		if err == nil {
			total, err = client.CountByUserID(strconv.Itoa(int(userId)), status)
		}
		return total
	}

	result.total = f(-1)                            // 所有任务
	result.doing = f(recommend_record.StatusDoing)  // 进行中
	result.wait = f(recommend_record.StatusDefault) // 等待中

	return
}

// 单位资产测绘
func departAssetTaskCountWithStatus(userId uint) (result taskCount, err error) {
	return detectAssetTaskCountWithStatus(userId, detectassetstasks.ExpandSourceDetect)
}

// 云端资产推荐
func recommendTaskCountWithStatus(userId uint) (result taskCount, err error) {
	return detectAssetTaskCountWithStatus(userId, detectassetstasks.ExpandSourceRecommend)
}

// 单位资产测绘(check=1) / 云端资产推荐(check=0)
func detectAssetTaskCountWithStatus(userId uint, source int) (result taskCount, err error) {
	client := detectassetstasks.NewModel()

	f := func(status int) int64 {
		var total int64
		if err == nil {
			handlers := []middlemysql.HandleFunc{middlemysql.WithColumnValue("user_id", userId)}
			handlers = append(handlers, middlemysql.WithColumnValue("expand_source", source))
			if status != -1 {
				handlers = append(handlers, middlemysql.WithColumnValue("status", status))
			}
			total, err = client.Count(handlers...)
		}
		return total
	}

	result.total = f(-1)                             // 所有
	result.doing = f(detectassetstasks.StatusDoing)  // 进行中
	result.wait = f(detectassetstasks.StatusDefault) // 等待中

	return
}
