package black_keyword

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/title_black_keyword"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

func AddTitleBlackKeyword(_ context.Context, req *pb.TitleBlackKeywordRequest, rsp *pb.Empty) error {
	_, err := title_black_keyword.NewModel().First(mysql.WithWhere("user_id = ? AND  keyword = ?", req.UserId, req.Keyword))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Errorf("[AddTitleBlackKeyword] Failed query title black keyword: %s", req.Keyword)
		return err
	}
	if err == nil {
		log.Errorf("[AddTitleBlackKeyword] Title black keyword already exists: %s", req.Keyword)
		return errors.New("关键词已经存在")
	}
	black := title_black_keyword.BlackKeyword{
		UserId:  req.UserId,
		TypeId:  req.TypeId,
		Keyword: req.Keyword,
		Status:  0,
	}
	err = title_black_keyword.NewModel().Create(&black)
	if err != nil {
		log.Errorf("[AddTitleBlackKeyword] Failed to create title black keyword: %s, error: %v", req.Keyword, err)
		return err
	}
	keywords, err := title_black_keyword.NewModel().ListAllKeywords(mysql.WithWhere("status = 1"))
	if err != nil {
		log.Errorf("[AddTitleBlackKeyword] Failed to list all keywords: %v", err)
	}
	redis.SetCache("black_title_keyword", 0, keywords)
	return nil
}
