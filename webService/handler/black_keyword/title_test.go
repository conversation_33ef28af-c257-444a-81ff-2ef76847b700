package black_keyword

import (
	"context"
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	initmysql "micro-service/initialize/mysql"
	initredis "micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

// 初始化Mock服务
func Init() {
	cfg.InitLoadCfg()
	log.Init()

	// 设置是否使用Mock (默认true)
	initmysql.SetTestEnv(true)
	initredis.SetTestEnv(true)

	// 初始化数据库
	initmysql.GetInstance(cfg.LoadMysql())
	initredis.GetInstance(cfg.LoadRedis())
}

func TestAddTitleBlackKeyword(t *testing.T) {
	Init()

	t.Run("success_create_new_keyword", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		rdm := initredis.GetMockInstance()

		// Mock First query - keyword not found (should return ErrRecordNotFound)
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(123, "test_keyword").
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock Create operation - GORM inserts 9 fields: created_at, updated_at, user_id, company_id, type_id, keyword, status, enable, black_keyword_system_id
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `title_black_keyword`").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 123, 0, 1, "test_keyword", 0, 0, 0).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock ListAllKeywords query
		mock.ExpectQuery("SELECT `keyword` FROM `title_black_keyword` WHERE status = 1").
			WillReturnRows(sqlmock.NewRows([]string{"keyword"}).AddRow("test1").AddRow("test2"))

		// Mock Redis SetCache
		rdm.Regexp().ExpectSet("black_title_keyword", ".*", 0).SetVal("OK")

		req := &pb.TitleBlackKeywordRequest{
			Keyword: "test_keyword",
			UserId:  123,
			TypeId:  1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
		assert.NoError(t, rdm.ExpectationsWereMet())
	})

	t.Run("error_keyword_already_exists", func(t *testing.T) {
		mock := initmysql.GetMockInstance()

		// Mock First query - keyword found (no error)
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(123, "existing_keyword").
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "keyword"}).AddRow(1, 123, "existing_keyword"))

		req := &pb.TitleBlackKeywordRequest{
			Keyword: "existing_keyword",
			UserId:  123,
			TypeId:  1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Equal(t, "关键词已经存在", err.Error())
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error_database_query_failed", func(t *testing.T) {
		mock := initmysql.GetMockInstance()

		// Mock First query - database error (not ErrRecordNotFound)
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(123, "test_keyword").
			WillReturnError(errors.New("database connection failed"))

		req := &pb.TitleBlackKeywordRequest{
			Keyword: "test_keyword",
			UserId:  123,
			TypeId:  1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Equal(t, "database connection failed", err.Error())
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("error_create_failed", func(t *testing.T) {
		mock := initmysql.GetMockInstance()

		// Mock First query - keyword not found
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(123, "test_keyword").
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock Create operation - fails
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `title_black_keyword`").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 123, 0, 1, "test_keyword", 0, 0, 0).
			WillReturnError(errors.New("insert failed"))
		mock.ExpectRollback()

		req := &pb.TitleBlackKeywordRequest{
			Keyword: "test_keyword",
			UserId:  123,
			TypeId:  1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.Equal(t, "insert failed", err.Error())
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("success_with_list_keywords_error", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		rdm := initredis.GetMockInstance()

		// Mock First query - keyword not found
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(123, "test_keyword").
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock Create operation - success
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `title_black_keyword`").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 123, 0, 1, "test_keyword", 0, 0, 0).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock ListAllKeywords query - fails (but function should still succeed)
		mock.ExpectQuery("SELECT `keyword` FROM `title_black_keyword` WHERE status = 1").
			WillReturnError(errors.New("list keywords failed"))

		// Mock Redis SetCache - should still be called with empty slice
		rdm.Regexp().ExpectSet("black_title_keyword", ".*", 0).SetVal("OK")

		req := &pb.TitleBlackKeywordRequest{
			Keyword: "test_keyword",
			UserId:  123,
			TypeId:  1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.NoError(t, err) // Function should still succeed even if ListAllKeywords fails
		assert.NoError(t, mock.ExpectationsWereMet())
		assert.NoError(t, rdm.ExpectationsWereMet())
	})

	t.Run("edge_case_empty_keyword", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		rdm := initredis.GetMockInstance()

		// Mock First query with empty keyword
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(123, "").
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock Create operation
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `title_black_keyword`").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 123, 0, 1, "", 0, 0, 0).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock ListAllKeywords query
		mock.ExpectQuery("SELECT `keyword` FROM `title_black_keyword` WHERE status = 1").
			WillReturnRows(sqlmock.NewRows([]string{"keyword"}))

		// Mock Redis SetCache
		rdm.Regexp().ExpectSet("black_title_keyword", ".*", 0).SetVal("OK")

		req := &pb.TitleBlackKeywordRequest{
			Keyword: "",
			UserId:  123,
			TypeId:  1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
		assert.NoError(t, rdm.ExpectationsWereMet())
	})

	t.Run("edge_case_zero_user_id", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		rdm := initredis.GetMockInstance()

		// Mock First query with zero user ID
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(0, "test_keyword").
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock Create operation
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `title_black_keyword`").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 0, 0, 1, "test_keyword", 0, 0, 0).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock ListAllKeywords query
		mock.ExpectQuery("SELECT `keyword` FROM `title_black_keyword` WHERE status = 1").
			WillReturnRows(sqlmock.NewRows([]string{"keyword"}))

		// Mock Redis SetCache
		rdm.Regexp().ExpectSet("black_title_keyword", ".*", 0).SetVal("OK")

		req := &pb.TitleBlackKeywordRequest{
			Keyword: "test_keyword",
			UserId:  0,
			TypeId:  1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
		assert.NoError(t, rdm.ExpectationsWereMet())
	})

	t.Run("edge_case_negative_type_id", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		rdm := initredis.GetMockInstance()

		// Mock First query with negative type ID
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(123, "test_keyword").
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock Create operation
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `title_black_keyword`").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 123, 0, -1, "test_keyword", 0, 0, 0).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock ListAllKeywords query
		mock.ExpectQuery("SELECT `keyword` FROM `title_black_keyword` WHERE status = 1").
			WillReturnRows(sqlmock.NewRows([]string{"keyword"}))

		// Mock Redis SetCache
		rdm.Regexp().ExpectSet("black_title_keyword", ".*", 0).SetVal("OK")

		req := &pb.TitleBlackKeywordRequest{
			Keyword: "test_keyword",
			UserId:  123,
			TypeId:  -1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
		assert.NoError(t, rdm.ExpectationsWereMet())
	})

	t.Run("success_with_special_characters", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		rdm := initredis.GetMockInstance()

		// Test with special characters in keyword
		specialKeyword := "test@#$%^&*()_+-=[]{}|;':\",./<>?"

		// Mock First query - keyword not found
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(123, specialKeyword).
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock Create operation
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `title_black_keyword`").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 123, 0, 1, specialKeyword, 0, 0, 0).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock ListAllKeywords query
		mock.ExpectQuery("SELECT `keyword` FROM `title_black_keyword` WHERE status = 1").
			WillReturnRows(sqlmock.NewRows([]string{"keyword"}).AddRow(specialKeyword))

		// Mock Redis SetCache
		rdm.Regexp().ExpectSet("black_title_keyword", ".*", 0).SetVal("OK")

		req := &pb.TitleBlackKeywordRequest{
			Keyword: specialKeyword,
			UserId:  123,
			TypeId:  1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
		assert.NoError(t, rdm.ExpectationsWereMet())
	})

	t.Run("success_with_unicode_characters", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		rdm := initredis.GetMockInstance()

		// Test with Unicode characters
		unicodeKeyword := "测试关键词🔥💯中文"

		// Mock First query - keyword not found
		mock.ExpectQuery("SELECT .* FROM `title_black_keyword` WHERE user_id = .* AND  keyword = .*").
			WithArgs(123, unicodeKeyword).
			WillReturnError(gorm.ErrRecordNotFound)

		// Mock Create operation
		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `title_black_keyword`").
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), 123, 0, 1, unicodeKeyword, 0, 0, 0).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// Mock ListAllKeywords query
		mock.ExpectQuery("SELECT `keyword` FROM `title_black_keyword` WHERE status = 1").
			WillReturnRows(sqlmock.NewRows([]string{"keyword"}).AddRow(unicodeKeyword))

		// Mock Redis SetCache
		rdm.Regexp().ExpectSet("black_title_keyword", ".*", 0).SetVal("OK")

		req := &pb.TitleBlackKeywordRequest{
			Keyword: unicodeKeyword,
			UserId:  123,
			TypeId:  1,
		}
		rsp := &pb.Empty{}

		err := AddTitleBlackKeyword(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
		assert.NoError(t, rdm.ExpectationsWereMet())
	})
}
