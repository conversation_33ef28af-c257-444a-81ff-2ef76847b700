package notice

import (
	"errors"

	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/middleware/mysql/public_notice"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"gorm.io/gorm"
)

func HasLoggedIn(userID uint64) (bool, error) {
	first, err := public_notice.NewModel(mysql.GetInstance()).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, err
	}

	day := first.UpAtStart.AddDate(0, 0, -5).Format("2006-01-02 15:04:05")
	count, err := operate_logs.NewModel(mysql.GetInstance()).CountByUserIDAndTime(userID, day)
	if err != nil {
		return false, err
	}

	if count >= 1 {
		return true, nil
	}
	return false, nil
}

func PublicNoticeAdd(in *pb.PublicNoticeAddRequest) (*pb.PublicNoticeAddResponse, error) {
	s, err := utils.PraseStringTime(in.UpAtStart, utils.DateTimeLayout)
	if err != nil {
		return nil, err
	}

	e, err := utils.PraseStringTime(in.UpAtEnd, utils.DateTimeLayout)
	if err != nil {
		return nil, err
	}

	if e.Before(s) {
		return nil, errors.New("结束时间不能大于开始时间")
	}

	id, err := public_notice.NewModel(mysql.GetInstance()).Add(public_notice.PublicNotice{Notice: in.Notice, UpAtStart: s, UpAtEnd: e})
	if err != nil {
		return nil, err
	}

	return &pb.PublicNoticeAddResponse{Id: uint64(id)}, nil
}

func PublicNoticeDel(in *pb.PublicNoticeDelRequest) error {
	err := public_notice.NewModel(mysql.GetInstance()).Del(in.Id)
	if err != nil {
		return err
	}

	return nil
}

func PublicNoticeSave(in *pb.PublicNoticeSaveRequest) error {
	s, err := utils.PraseStringTime(in.UpAtStart, utils.DateTimeLayout)
	if err != nil {
		return err
	}

	e, err := utils.PraseStringTime(in.UpAtEnd, utils.DateTimeLayout)
	if err != nil {
		return err
	}

	if e.Before(s) {
		return errors.New("结束时间不能大于开始时间")
	}

	err = public_notice.NewModel(mysql.GetInstance()).Update(public_notice.PublicNotice{Model: gorm.Model{ID: uint(in.Id)}, Notice: in.Notice, UpAtStart: s, UpAtEnd: e})
	if err != nil {
		return err
	}
	return nil
}

func PublicNoticeList(currentPage, perPage int64) (*pb.PublicNoticeListResponse, error) {
	Items, total, err := public_notice.NewModel(mysql.GetInstance()).List(int(currentPage), int(perPage))
	if err != nil {
		return nil, err
	}

	pbItems := make([]*pb.PublicNoticeListResponse_BaseInfo, 0, len(Items))
	for i := range Items {
		pbItems = append(pbItems, &pb.PublicNoticeListResponse_BaseInfo{
			Notice:    Items[i].Notice,
			UpAtStart: Items[i].UpAtStart.Format(utils.DateTimeLayout),
			UpAtEnd:   Items[i].UpAtEnd.Format(utils.DateTimeLayout),
			Id:        uint64(Items[i].ID),
			CreatedAt: Items[i].CreatedAt.Format(utils.DateTimeLayout),
		})
	}
	return &pb.PublicNoticeListResponse{Items: pbItems, Total: total, CurrentPage: currentPage, PerPage: perPage}, nil
}

func FindLatestNotice() (*pb.FindLatestNoticeResponse, error) {
	Items, err := public_notice.NewModel(mysql.GetInstance()).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &pb.FindLatestNoticeResponse{}, nil
		}
		return nil, err
	}
	return &pb.FindLatestNoticeResponse{Notice: Items.Notice,
			UpAtStart: Items.UpAtStart.Format(utils.DateTimeLayout),
			UpAtEnd:   Items.UpAtEnd.Format(utils.DateTimeLayout)},
		nil
}
