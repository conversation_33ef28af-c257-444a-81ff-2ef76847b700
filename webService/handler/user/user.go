package user

import (
	"context"
	"errors"

	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/safe_user_company"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"gorm.io/gorm"
)

func List(_ context.Context, req *pb.UserListRequest, rsp *pb.UserListResponse) error {
	users, _, err := user.NewUserModel().Find(int(req.Page), int(req.PerPage))
	if err != nil {
		return err
	}

	for _, v := range users {
		var companyId int64
		if v.CompanyId != nil {
			companyId = v.CompanyId.Int64
		}
		rsp.Items = append(rsp.Items, &pb.UserListResponseUserItem{
			Id:          v.Id,
			Name:        mysql.SqlString(v.Name),
			Email:       mysql.SqlString(v.Email),
			Mobile:      mysql.SqlString(v.Mobile),
			Level:       uint32(v.Level),
			CompanyId:   uint64(companyId),
			Role:        uint32(v.Role),
			Status:      uint32(v.Status),
			AgreeStatus: uint64(v.AgreeStatus),
			ExpiresAt:   "",
		})
	}
	return nil
}

func IsUserAdmin(userId uint64) (bool, error) {
	info, err := user.NewUserModel().FindById(userId)
	if err != nil {
		return false, err
	}

	if info == nil {
		return false, errors.New("用户不存在")
	}

	return info.Role == user.UserRoleAdmin, nil
}

func GetUserRole(userId uint64) (uint8, error) {
	info, err := user.NewUserModel().FindById(userId)
	if err != nil {
		return 0, err
	}

	if info == nil {
		return 0, errors.New("用户不存在")
	}

	return info.Role, nil
}

func GetUserCompanyNameList(userId uint64) ([]string, error) {
	// 获取用户权限，如果是普通用户，则获取归属企业，如果是安服，除了归属企业还需要获取关联企业
	// 获取用户权限,用户类型: 1/2/3/4 超级管理员/安服人员/企业租户/售后
	companyNameList := make([]string, 0)
	role, err := GetUserRole(userId)
	if err != nil {
		return companyNameList, err
	}
	if role == 2 {
		safaUserCompanyList, err := safe_user_company.NewModel().Find(userId)
		if err != nil {
			return companyNameList, err
		}
		companyIds := utils.ListDistinct(utils.ListColumn(safaUserCompanyList, func(t safe_user_company.SafeUserCompany) uint64 { return t.CompanyId }))
		if len(companyIds) == 0 {
			return companyNameList, nil
		}
		companyNameList, err = company.NewCompanyModel().DistinctColumn("name", mysql.WithValuesIn("id", companyIds))
		if err != nil {
			return companyNameList, err
		}
	}
	if role == 3 {
		company, err := company.NewCompanyModel().First(mysql.WithColumnValue("owner_id", userId))
		if err != nil {
			return companyNameList, err
		}
		companyNameList = append(companyNameList, company.Name)
	}
	return companyNameList, nil
}

func SetFirstTaskNotice(ctx context.Context, req *pb.SetFirstTaskNoticeRequest, rsp *pb.SetFirstTaskNoticeResponse) error {
	userInfo, err := user.NewUserModel().FindById(req.UserId)
	if err != nil {
		rsp.Success = false
		rsp.Message = err.Error()
		if err == gorm.ErrRecordNotFound {
			rsp.Message = "用户不存在"
		}
		return nil
	}
	// 如果用户已通知，则直接返回
	if userInfo.HasNotice == 1 {
		rsp.Success = true
		return nil
	}
	err = user.NewUserModel().UpdateAny(req.UserId, map[string]interface{}{
		"has_notice": 1,
	})
	if err != nil {
		rsp.Success = false
		rsp.Message = err.Error()
		return nil
	}
	rsp.Success = true
	return nil
}
