package intelligence_threat

import (
	"context"
	"errors"
	"fmt"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/intelligence"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/user"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

var once sync.Once
var taskInfo *TaskInfo

type TaskInfo struct {
	Tasks sync.Map
}

func GetTaskInfo() *TaskInfo {
	if taskInfo == nil {
		once.Do(func() {
			taskInfo = &TaskInfo{}
		})
	}
	return taskInfo
}

func CheckTaskRunning(userId uint64) bool {
	if _, ok := GetTaskInfo().Tasks.Load(userId); ok {
		return true
	}
	return false
}

func AddTask(userId uint64) {
	GetTaskInfo().Tasks.Store(userId, 1)
	redis.SetCache("threat-t:"+cast.ToString(userId), 1*time.Minute, 1)
}

func DeleteTask(userId uint64) {
	GetTaskInfo().Tasks.Delete(userId)
}

func GetTaskProcess(userId uint64) string {
	process, ok := GetTaskInfo().Tasks.Load(userId)
	if !ok {
		return "100"
	}
	return fmt.Sprintf("%.2f", process)
}

func queryResult(userId uint64, domains, ips []string) ([]foradar_assets.ForadarAsset, error) {
	// 查询风险匹配资产
	boolQuery := elastic.NewBoolQuery().Must(elastic.NewTermsQuery("status", foradar_assets.StatusConfirmAsset, foradar_assets.StatusUploadAsset))
	if ok, _ := user.IsUserAdmin(userId); !ok {
		boolQuery = boolQuery.Must(elastic.NewTermsQuery("user_id", userId))
	}
	findQuerys := make([]elastic.Query, 0)
	if len(domains) != 0 {
		findQuerys = append(findQuerys, elastic.NewTermsQuery("domain", utils.ListColumn(domains, func(t string) any {
			return t
		})...))
	}
	if len(ips) != 0 {
		findQuerys = append(findQuerys, elastic.NewTermsQuery("ip", utils.ListColumn(ips, func(t string) any {
			return t
		})...))
	}
	if len(findQuerys) > 0 {
		boolQuery = boolQuery.Must(elastic.NewBoolQuery().Should(findQuerys...))
		assetResult, err := foradar_assets.NewForadarAssetModel().ListAll(context.TODO(), boolQuery)
		if err != nil {
			return nil, err
		}
		return assetResult, nil
	} else {
		return nil, errors.New("ip and domain empty")
	}
}

func MatchThreat(userId uint64, list []*intelligence.Threat) {
	defer func() {
		if err := recover(); err != nil {
			log.Warn(fmt.Sprintf("MatchThreat: userId:%d,任务异常,Error:%v!", userId, err))
			DeleteTask(userId)
		}
	}()
	total := len(list)
	// 获取所有 Threat ids
	ids := utils.ListDistinctNonZero(utils.ListColumn(list, func(t *intelligence.Threat) uint64 { return t.Id }))
	if len(ids) == 0 {
		return
	}
	riClient := intelligence.NewRelatedIntelligence()
	// 删除所有关联信息
	idList := utils.ListSplit(ids, 1000)
	for y := range idList {
		_ = intelligence.NewUserThreat().DeleteBy(userId, idList[y])
	}
	// 关联情报表数据
	relatedIntelligenceList := make([]*intelligence.RelatedIntelligence, 0)
	// 按1000条拆分
	listSplits := utils.ListSplit(list, 1000)
	index := 1
	for x := range listSplits {
		splitThreatList := listSplits[x]
		// 查询命中结果
		domains := utils.ListDistinctNonZero(utils.ListColumn(splitThreatList, func(t *intelligence.Threat) string { return t.Domain }))
		ips := utils.ListDistinctNonZero(utils.ListColumn(splitThreatList, func(t *intelligence.Threat) string { return t.Ip }))
		assets, err := queryResult(userId, domains, ips)
		if err != nil {
			log.Warn(fmt.Sprintf("MatchThreat: userId:%d,查询用户资产信息失败,Error:%v!", userId, err))
		}
		resultIps := utils.ListDistinctNonZero(utils.ListColumn(assets, func(t foradar_assets.ForadarAsset) string { return t.Ip }))
		resultDomains := utils.ListDistinctNonZero(utils.ListColumn(assets, func(t foradar_assets.ForadarAsset) string { return t.Domain }))
		// 生成批量插入语句
		insertRes := make([]*intelligence.UserThreat, 0)
		for y := range splitThreatList {
			threat := splitThreatList[y]
			if utils.ListContains(resultIps, threat.Ip) || utils.ListContains(resultDomains, threat.Domain) {
				insertRes = append(insertRes, &intelligence.UserThreat{
					ThreatId: threat.Id,
					UserId:   userId,
					Hit:      intelligence.ThreatHitTrue,
				})
				for _, asset := range assets {
					foundTime := time.Now().Format("2006-01-02 15:04:05")
					query := make([]mysql.HandleFunc, 0)
					query = append(query, mysql.WithColumnValue("intelligence_id", threat.Id))
					query = append(query, mysql.WithColumnValue("risk_type", 2))
					query = append(query, mysql.WithColumnValue("user_id", userId))
					query = append(query, mysql.WithColumnValue("asset_ip", asset.Ip))
					switch asset.Port.(type) {
					case float64:
						query = append(query, mysql.WithColumnValue("asset_port", cast.ToString(asset.Port.(float64))))
					case string:
						query = append(query, mysql.WithColumnValue("asset_port", asset.Port.(string)))
					}
					existRi, err := riClient.FindBy(query...)
					if err != nil {
						if err != gorm.ErrRecordNotFound {
							log.Warn(fmt.Sprintf("MatchEvent: userId:%d,查询关联情报表数据失败,Error:%v!", userId, err))
						}
					} else {
						foundTime = existRi.FoundTime
					}
					// 生成关联情报表数据
					ri := &intelligence.RelatedIntelligence{
						IntelligenceID: threat.Id, UserID: userId, EnterpriseID: uint64(asset.CompanyID),
						AssetID: asset.ID, AssetIP: asset.Ip, AssetPort: fmt.Sprintf("%v", asset.Port), AssetDomain: asset.Domain, AssetStatus: cast.ToString(asset.Status.(float64)),
						AssetProtocol: asset.Protocol, AssetURL: asset.Url, AssetTitle: fmt.Sprintf("%v", asset.Title),
						RiskName: threat.Url, RiskType: "2", IntelligenceType: threat.Type, IntelligenceTags: threat.Tags, IntelligenceCountry: threat.Country,
						FoundTime: foundTime, UpdateTime: time.Now().Format("2006-01-02 15:04:05"),
					}
					if asset.ClueCompanyName != nil && len(asset.ClueCompanyName.([]interface{})) > 0 {
						if asset.ClueCompanyName.([]interface{})[0] != nil {
							ri.EnterpriseName = asset.ClueCompanyName.([]interface{})[0].(string)
						}
					}
					ri.ServiceComponent = strings.Join(utils.ListColumn(asset.RuleTags, func(t foradar_assets.RuleTag) string { return t.CnProduct }), ",")
					relatedIntelligenceList = append(relatedIntelligenceList, ri)
				}
			} else {
				insertRes = append(insertRes, &intelligence.UserThreat{
					ThreatId: threat.Id,
					UserId:   userId,
					Hit:      intelligence.ThreatHitFalse,
				})
			}
			index += 1
		}
		// 写入关联信息
		if err = intelligence.NewUserThreat().CreateInBatches(insertRes); err != nil {
			log.Warn(fmt.Sprintf("IntelligenceUserThreatMatch: userId:%d 创建风险命信息失败,info:%+v,Error:%v", userId, insertRes, err.Error()))
		}
		process := float32(index) / float32(total) * 100.0
		if cast.ToFloat32(GetTaskProcess(userId)) < process && cast.ToInt(process) != 100 {
			GetTaskInfo().Tasks.Store(userId, process)
			log.Infof(fmt.Sprintf("userID:%d,MatchThreat进度:", userId), cast.ToString(process))
		}
	}
	// 删除旧的关联情报数据
	if err := riClient.DeleteBy(mysql.WithColumnValue("risk_type", 2), mysql.WithColumnValue("user_id", userId)); err != nil {
		log.Warn(fmt.Sprintf("IntelligenceUserThreatMatch: userId:%d,删除关联情报表数据失败,Error:%v", userId, err.Error()))
	}
	// 插入关联情报表数据
	if err := intelligence.NewRelatedIntelligence().BatchCreate(relatedIntelligenceList); err != nil {
		log.Warn(fmt.Sprintf("IntelligenceUserThreatMatch: userId:%d,创建关联情报表数据失败,Error:%v", userId, err.Error()))
	}
	time.Sleep(2 * time.Second)
	GetTaskInfo().Tasks.Store(userId, 100)
	DeleteTask(userId)
	log.Info(fmt.Sprintf("IntelligenceUserThreatMatch: userId:%d 任务完成!", userId))
}
