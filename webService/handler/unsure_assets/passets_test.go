package unsure_assets

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	testcommon "micro-service/initialize/common_test"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

var (
	indexName  = fofaee_assets.FofaeeAssetsIndex
	docType    = fofaee_assets.FofaeeAssetsType
	indexName2 = foradar_assets.IndexName
)

// 初始化Mock服务
func Init() {
	cfg.InitLoadCfg()
	log.Init()
	// 启用测试环境
	testcommon.SetTestEnv(true)

	// 初始化Mock服务 - 使用testcommon的MockServer
	mock := testcommon.NewMockEsServer()

	// 查询语句Mock数据 - foradar_assets
	foradarResultList := []foradar_assets.ForadarAsset{
		{
			ID:             "1_**********",
			Ip:             "**********",
			Port:           80,
			Protocol:       "http",
			Title:          "Example Title 1",
			Subdomain:      "test.example.com",
			Domain:         "example.com",
			HTTPStatusCode: 200,
			Logo: struct {
				Hash    any    `json:"hash"`
				Content string `json:"content"`
			}{
				Hash:    "-1111123222",
				Content: "/storage/logo.png",
			},
			Geo: foradar_assets.AssetGeo{
				Asn:      "AS4134 China Telecom (Group)",
				Province: "Shanghai",
				Isp:      "China Telecom",
				Lat:      31.2304,
				Lon:      121.4737,
			},
			ClueCompanyName: []any{"上海XXX有限公司"},
			RuleTags: []foradar_assets.RuleTag{
				{
					RuleID:    "tag_123",
					Product:   "测试1",
					CnProduct: "测试产品1",
				},
			},
			Reason: []foradar_assets.AssetReason{
				{
					GroupID:         1234,
					ID:              1,
					Source:          4,
					Type:            0,
					Content:         "example.com",
					ClueCompanyName: "上海XXX有限公司",
				},
			},
			Type:        fofaee_assets.TYPE_RECOMMEND,
			Status:      fofaee_assets.STATUS_DEFAULT,
			OnlineState: 1,
			UserID:      1,
			CreatedAt:   "2025-06-06 16:13:00",
			UpdatedAt:   "2025-06-06 16:13:00",
		},
		{
			ID:             "1_**********",
			Ip:             "**********",
			Port:           443,
			Protocol:       "https",
			Title:          "金沙娱乐网站",
			Subdomain:      "api.example.com",
			Domain:         "example.com",
			HTTPStatusCode: 200,
			Geo: foradar_assets.AssetGeo{
				Asn:      "AS4134 China Telecom (Group)",
				Province: "Beijing",
				Isp:      "China Telecom",
				Lat:      39.9042,
				Lon:      116.4074,
			},
			ClueCompanyName: "北京YYY有限公司",
			RuleTags: []foradar_assets.RuleTag{
				{
					RuleID:    "tag_456",
					Product:   "测试2",
					CnProduct: "测试产品2",
				},
			},
			Reason: []foradar_assets.AssetReason{
				{
					GroupID:         5678,
					ID:              2,
					Source:          4,
					Type:            1,
					Content:         "api.example.com",
					ClueCompanyName: "北京YYY有限公司",
				},
			},
			Type:        fofaee_assets.TYPE_RECOMMEND,
			Status:      fofaee_assets.STATUS_DEFAULT,
			OnlineState: 0,
			UserID:      1,
			CreatedAt:   "2025-06-06 16:14:00",
			UpdatedAt:   "2025-06-06 16:14:00",
		},
	}

	// 查询语句Mock数据 - fofaee_assets
	fofaeeResultList := []fofaee_assets.FofaeeAssets{
		{
			Id: "1_**********",
			Ip: "**********",
			HostList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:           80,
					Protocol:       "http",
					Title:          "Example Title 1",
					Subdomain:      "test.example.com",
					Domain:         "example.com",
					HttpStatusCode: "200",
					Logo: fofaee_assets.Icon{
						Content: "/storage/logo.png",
						Hash:    "-1111123222",
					},
				},
			},
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:           "80",
					Protocol:       "http",
					Title:          "Example Title 1",
					HttpStatusCode: "200",
				},
			},
			Geo: fofaee_assets.Geo{
				Asn:      "AS4134 China Telecom (Group)",
				Province: "Shanghai",
				Isp:      "China Telecom",
			},
			CloudName:       []any{"AAACloud", "123"},
			ClueCompanyName: []any{"上海XXX有限公司"},
			CustomerTags:    []string{"测试标签1", "测试标签2"},
			RuleTags: []fofaee_assets.RuleTag{
				{
					RuleId:  "tag_123",
					Product: "测试1",
				},
			},
			ReasonArr: []any{
				map[string]interface{}{
					"group_id":          1234,
					"id":                1,
					"source":            4,
					"type":              0,
					"content":           "example.com",
					"clue_company_name": "上海XXX有限公司",
				},
			},
			Type:      fofaee_assets.TYPE_RECOMMEND,
			Status:    fofaee_assets.STATUS_DEFAULT,
			UpdatedAt: "2025-06-06 16:13:00",
		},
		{
			Id: "1_**********",
			Ip: "**********",
			HostList: []fofaee_assets.FofaeeAssetPort{
				{
					HttpStatusCode: 200,
					Port:           "443",
					Title:          "金沙娱乐网站",
				},
			},
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:           "443",
					HttpStatusCode: 200,
					Title:          "金沙娱乐网站",
					Reason: []fofaee_assets.AssetReason{
						{
							Id:              2,
							Source:          4,
							Type:            0,
							Content:         "api.example.com",
							ClueCompanyName: "北京YYY有限公司",
						},
					},
				},
			},
			Type:   fofaee_assets.TYPE_RECOMMEND,
			Status: fofaee_assets.STATUS_DEFAULT,
		},
	}

	// 注册foradar_assets索引的搜索Mock
	mock.Register("/"+indexName2+"/_search", []*elastic.SearchHit{
		{
			Index:  indexName2,
			Type:   docType,
			Id:     foradarResultList[0].ID,
			Source: utils.ToJSON(foradarResultList[0]),
		},
		{
			Index:  indexName2,
			Type:   docType,
			Id:     foradarResultList[1].ID,
			Source: utils.ToJSON(foradarResultList[1]),
		},
	})

	// 注册fofaee_assets索引的搜索Mock
	mock.Register("/"+indexName+"/_search", []*elastic.SearchHit{
		{
			Index:  indexName,
			Type:   docType,
			Id:     fofaeeResultList[0].Id,
			Source: utils.ToJSON(fofaeeResultList[0]),
		},
		{
			Index:  indexName,
			Type:   docType,
			Id:     fofaeeResultList[1].Id,
			Source: utils.ToJSON(fofaeeResultList[1]),
		},
	})

	// 批量操作注册
	mock.RegisterBulk()

	// Count语句Mock数据
	countResponse := elastic.CountResponse{
		Count: 2,
	}

	mock.Register("/"+indexName2+"/_count", countResponse)
	mock.Register("/"+indexName+"/_count", countResponse)

	// 更新语句Mock数据
	updateResponse := elastic.UpdateResponse{
		Index:   indexName,
		Type:    docType,
		Id:      "1_**********",
		Version: 2,
		Result:  "updated",
		Shards: &elastic.ShardsInfo{
			Total:      2,
			Successful: 1,
			Failed:     0,
		},
	}

	mock.Register("/"+indexName+"/_update_by_query", updateResponse)
	mock.Register("/"+indexName2+"/_update_by_query", updateResponse)

	// 删除语句Mock数据
	deleteResponse := elastic.DeleteResponse{
		Index:   indexName,
		Type:    docType,
		Id:      "1_**********",
		Version: 3,
		Result:  "deleted",
		Shards: &elastic.ShardsInfo{
			Total:      1,
			Successful: 1,
			Failed:     0,
		},
	}

	mock.Register("/"+indexName+"/_delete_by_query", deleteResponse)
	mock.Register("/"+indexName2+"/_delete_by_query", deleteResponse)

	// 设置是否使用Mock (默认true)
	es.SetTestEnv(true)
	mysql.SetTestEnv(true)
	redis.SetTestEnv(true)

	// 初始化数据库
	es.GetInstance(cfg.LoadElastic())
	mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

func TestPassetsList(t *testing.T) {
	Init()

	t.Run("normal request", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
		assert.Equal(t, int32(1), resp.CurrentPage)
		assert.Equal(t, int32(10), resp.PerPage)
		assert.NotEmpty(t, resp.Data)

		// 验证返回的数据格式
		var items []map[string]interface{}
		err = json.Unmarshal([]byte(resp.Data), &items)
		assert.NoError(t, err)
		assert.Len(t, items, 2)
	})

	t.Run("with keyword search", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("example"),
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with numeric keyword", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Keyword: proto.String("80"),
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with title filter", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Title:   []string{"Example Title 1"},
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with title filter containing dash", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Title:   []string{"-", "Example Title 1"},
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with domain filter", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Domain:  []string{"example.com"},
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with domain filter containing dash", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Domain:  []string{"-", "example.com"},
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with hosts filter", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
			Hosts:   proto.String("test.example"),
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with assets source filter", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:       1,
			Page:         1,
			PerPage:      10,
			AssetsSource: []string{"FOFA"},
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with assets source filter containing dash", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:       1,
			Page:         1,
			PerPage:      10,
			AssetsSource: []string{"-", "FOFA"},
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with cloud name filter", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:    1,
			Page:      1,
			PerPage:   10,
			CloudName: proto.String("AliCloud"),
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with clue company name filter", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			ClueCompanyName: []string{"上海XXX有限公司"},
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with clue company name filter containing dash", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:          1,
			Page:            1,
			PerPage:         10,
			ClueCompanyName: []string{"-", "上海XXX有限公司"},
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
	})

	t.Run("with invalid page parameters", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    0, // Invalid page
			PerPage: 0, // Invalid per_page
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int32(1), resp.CurrentPage) // Should default to 1
		assert.Equal(t, int32(10), resp.PerPage)    // Should default to 10
	})

	t.Run("with large per_page", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 200, // Exceeds max limit
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int32(10), resp.PerPage) // Should default to 10
	})
}

func TestExportPassetsList(t *testing.T) {
	Init()

	t.Run("normal export", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
		// The URL should contain .xlsx extension (either in filename or encoded URL)
		assert.Contains(t, resp.Url, ".xlsx")
	})

	t.Run("export with keyword filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:  1,
			Keyword: proto.String("example"),
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with numeric keyword", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:  1,
			Keyword: proto.String("80"),
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with title filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Title:  []string{"Example Title 1"},
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with title filter containing dash", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Title:  []string{"-", "Example Title 1"},
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with domain filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Domain: []string{"example.com"},
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with domain filter containing dash", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Domain: []string{"-", "example.com"},
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with hosts filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Hosts:  proto.String("test.example"),
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with assets source filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:       1,
			AssetsSource: []string{"FOFA"},
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with assets source filter containing dash", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:       1,
			AssetsSource: []string{"-", "FOFA"},
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with cloud name filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:    1,
			CloudName: proto.String("AliCloud"),
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with clue company name filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:          1,
			ClueCompanyName: []string{"上海XXX有限公司"},
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with clue company name filter containing dash", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:          1,
			ClueCompanyName: []string{"-", "上海XXX有限公司"},
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with specific IDs", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Id:     []string{"1_**********", "1_**********"},
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})
}

func TestIpPortActionSearch(t *testing.T) {
	t.Run("search with IDs", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Id:     []string{"1_**********", "1_**********"},
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)

		// The result should be a slice of query conditions
		// Convert to string to check for ID filter
		resultStr := fmt.Sprintf("%v", result)
		assert.Contains(t, resultStr, "_id", "Should contain ID filter")
		assert.Contains(t, resultStr, "1_**********", "Should contain specific ID")
	})

	t.Run("search without IDs", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:  1,
			Keyword: proto.String("example"),
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)

		// Convert to string to check for filters
		resultStr := fmt.Sprintf("%v", result)
		assert.Contains(t, resultStr, "user_id", "Should contain user_id filter")
		assert.Contains(t, resultStr, "status", "Should contain status filter")
	})

	t.Run("search with keyword", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:  1,
			Keyword: proto.String("example"),
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with numeric keyword", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:  1,
			Keyword: proto.String("80"),
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with title filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Title:  []string{"Example Title 1"},
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with title filter containing dash", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Title:  []string{"-", "Example Title 1"},
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with domain filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Domain: []string{"example.com"},
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with domain filter containing dash", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Domain: []string{"-", "example.com"},
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with hosts filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
			Hosts:  proto.String("test.example"),
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with assets source filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:       1,
			AssetsSource: []string{"FOFA"},
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with assets source filter containing dash", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:       1,
			AssetsSource: []string{"-", "FOFA"},
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with cloud name filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:    1,
			CloudName: proto.String("AliCloud"),
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with clue company name filter", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:          1,
			ClueCompanyName: []string{"上海XXX有限公司"},
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})

	t.Run("search with clue company name filter containing dash", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId:          1,
			ClueCompanyName: []string{"-", "上海XXX有限公司"},
		}
		status := []interface{}{fofaee_assets.STATUS_DEFAULT}
		result := IpPortActionSearch(req, status)

		assert.NotNil(t, result)
		assert.Greater(t, len(result), 0)
	})
}

func TestMin(t *testing.T) {
	t.Run("first value is smaller", func(t *testing.T) {
		result := min(5, 10)
		assert.Equal(t, int64(5), result)
	})

	t.Run("second value is smaller", func(t *testing.T) {
		result := min(15, 8)
		assert.Equal(t, int64(8), result)
	})

	t.Run("values are equal", func(t *testing.T) {
		result := min(7, 7)
		assert.Equal(t, int64(7), result)
	})

	t.Run("negative values", func(t *testing.T) {
		result := min(-5, -10)
		assert.Equal(t, int64(-10), result)
	})

	t.Run("zero values", func(t *testing.T) {
		result := min(0, 5)
		assert.Equal(t, int64(0), result)
	})
}

func TestGetReason(t *testing.T) {
	t.Run("claimed asset type", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{}
		result := getReason(reason, fofaee_assets.TYPE_CLAIMED, false, 0)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("upload asset type", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{}
		result := getReason(reason, fofaee_assets.STATUS_UPLOAD, false, 0)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("claimed type_two", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{}
		result := getReason(reason, 0, false, fofaee_assets.TYPE_CLAIMED)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("upload type_two", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{}
		result := getReason(reason, 0, false, fofaee_assets.STATUS_UPLOAD)
		assert.Equal(t, "已知资产ip", result)
	})

	t.Run("empty reason array", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{}
		result := getReason(reason, 5, false, 5) // Use types that are not TYPE_CLAIMED or STATUS_UPLOAD
		assert.Equal(t, "", result)
	})

	t.Run("nil reason array", func(t *testing.T) {
		result := getReason(nil, 5, false, 5) // Use types that are not TYPE_CLAIMED or STATUS_UPLOAD
		assert.Equal(t, "", result)
	})

	t.Run("normal reason with domain type", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				GroupID:         1234,
				ID:              1,
				Source:          4,
				Type:            0, // TYPE_DOMAIN
				Content:         "example.com",
				ClueCompanyName: "上海XXX有限公司",
			},
		}
		result := getReason(reason, 5, false, 5) // Use a type that's not TYPE_CLAIMED or STATUS_UPLOAD
		expected := "根据上海XXX有限公司的根域 example.com推荐;"
		assert.Equal(t, expected, result)
	})

	t.Run("reason with empty clue company name", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				GroupID:         1234,
				ID:              1,
				Source:          4,
				Type:            0, // TYPE_DOMAIN
				Content:         "example.com",
				ClueCompanyName: "",
			},
		}
		result := getReason(reason, 5, false, 5) // Use a type that's not TYPE_CLAIMED or STATUS_UPLOAD
		expected := "根据-的根域 example.com推荐;"
		assert.Equal(t, expected, result)
	})

	t.Run("reason with logo type non-export mode", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				GroupID:         1234,
				ID:              1,
				Source:          4,
				Type:            3, // TYPE_LOGO
				Content:         "/storage/logo.png",
				ClueCompanyName: "上海XXX有限公司",
			},
		}
		result := getReason(reason, 5, false, 5) // Use a type that's not TYPE_CLAIMED or STATUS_UPLOAD
		assert.Contains(t, result, "根据上海XXX有限公司的ICON")
		assert.Contains(t, result, "<img class=\"reason_ico\"")
		assert.Contains(t, result, "推荐;")
	})

	t.Run("reason with logo type export mode", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				GroupID:         1234,
				ID:              1,
				Source:          4,
				Type:            3, // TYPE_LOGO
				Content:         "/storage/logo.png",
				ClueCompanyName: "上海XXX有限公司",
			},
		}
		result := getReason(reason, 5, true, 5) // Use a type that's not TYPE_CLAIMED or STATUS_UPLOAD
		assert.Contains(t, result, "根据上海XXX有限公司的ICON")
		assert.Contains(t, result, "推荐;")
		assert.NotContains(t, result, "<img")
	})

	t.Run("reason with logo type containing pp/public", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				GroupID:         1234,
				ID:              1,
				Source:          4,
				Type:            3, // TYPE_LOGO
				Content:         "/pp/public/storage/logo.png",
				ClueCompanyName: "上海XXX有限公司",
			},
		}
		result := getReason(reason, 5, false, 5) // Use a type that's not TYPE_CLAIMED or STATUS_UPLOAD
		assert.Contains(t, result, "根据上海XXX有限公司的ICON")
		assert.Contains(t, result, "<img class=\"reason_ico\"")
		assert.Contains(t, result, "推荐;")
	})

	t.Run("multiple reasons", func(t *testing.T) {
		reason := []foradar_assets.AssetReason{
			{
				GroupID:         1234,
				ID:              1,
				Source:          4,
				Type:            0, // TYPE_DOMAIN
				Content:         "example.com",
				ClueCompanyName: "上海XXX有限公司",
			},
			{
				GroupID:         5678,
				ID:              2,
				Source:          4,
				Type:            1, // TYPE_CERT
				Content:         "证书内容",
				ClueCompanyName: "北京YYY有限公司",
			},
		}
		result := getReason(reason, 5, false, 5) // Use a type that's not TYPE_CLAIMED or STATUS_UPLOAD
		assert.Contains(t, result, "根据上海XXX有限公司的根域 example.com推荐;")
		assert.Contains(t, result, "根据北京YYY有限公司的证书 证书内容推荐;")
	})
}

// Test edge cases and error scenarios
func TestPassetsListEdgeCases(t *testing.T) {
	Init()

	t.Run("empty response data", func(t *testing.T) {
		// This test verifies that the function handles empty results correctly
		// Since we're using the same mock data, we'll test with a different user ID
		// that would logically return no results
		req := &pb.PassetsListRequest{
			UserId:  999, // Non-existent user
			Page:    1,
			PerPage: 10,
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		// The mock will still return data, but we can verify the structure is correct
		assert.NotNil(t, resp.Data)
	})

	t.Run("pagination calculation", func(t *testing.T) {
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    2,
			PerPage: 1,
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int32(2), resp.CurrentPage)
		assert.Equal(t, int32(1), resp.PerPage)
		assert.Equal(t, int32(2), resp.From)
		assert.Equal(t, int32(2), resp.To)
		assert.Equal(t, int32(2), resp.LastPage)
	})

	t.Run("reason string processing with nil reason", func(t *testing.T) {
		// Test the reason string processing logic
		req := &pb.PassetsListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
		}
		resp := &pb.PassetsListResponse{}
		err := PassetsList(context.Background(), req, resp)
		assert.NoError(t, err)

		// Verify that the response contains processed reason strings
		var items []map[string]interface{}
		err = json.Unmarshal([]byte(resp.Data), &items)
		assert.NoError(t, err)

		for _, item := range items {
			if reasonString, exists := item["reason_string"]; exists {
				assert.IsType(t, "", reasonString)
			}
		}
	})
}

func TestExportPassetsListEdgeCases(t *testing.T) {
	Init()

	t.Run("export with complex data types", func(t *testing.T) {
		// Test export with various data types in ClueCompanyName
		req := &pb.IpPortActionRequest{
			UserId: 1,
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with empty rule tags", func(t *testing.T) {
		req := &pb.IpPortActionRequest{
			UserId: 1,
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})

	t.Run("export with long title", func(t *testing.T) {
		// Test title truncation logic
		req := &pb.IpPortActionRequest{
			UserId: 1,
		}
		resp := &pb.FileExportResponse{}
		err := ExportPassetsList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.NotEmpty(t, resp.Url)
	})
}

func TestGetReasonEdgeCases(t *testing.T) {
	t.Run("reason with different clue company name types", func(t *testing.T) {
		// Test with interface{} type for ClueCompanyName
		reason := []foradar_assets.AssetReason{
			{
				GroupID:         1234,
				ID:              1,
				Source:          4,
				Type:            0,
				Content:         "example.com",
				ClueCompanyName: "公司1", // String type
			},
		}
		result := getReason(reason, 5, false, 5) // Use a type that's not TYPE_CLAIMED or STATUS_UPLOAD
		assert.Contains(t, result, "推荐;")
	})

	t.Run("reason with all clue types", func(t *testing.T) {
		testCases := []struct {
			clueType     int
			expectedText string
		}{
			{0, "根域"},   // TYPE_DOMAIN
			{1, "证书"},   // TYPE_CERT
			{2, "ICP"},  // TYPE_ICP
			{3, "ICON"}, // TYPE_LOGO
			{4, "关键词"},  // TYPE_KEYWORD
			{5, "子域名"},  // TYPE_SUBDOMAIN
			{6, "IP"},   // TYPE_IP
			{10, "FID"}, // TYPE_FID
			{99, "未知"},  // Unknown type
		}

		for _, tc := range testCases {
			reason := []foradar_assets.AssetReason{
				{
					GroupID:         1234,
					ID:              1,
					Source:          4,
					Type:            tc.clueType,
					Content:         "test content",
					ClueCompanyName: "测试公司",
				},
			}
			result := getReason(reason, 5, false, 5) // Use a type that's not TYPE_CLAIMED or STATUS_UPLOAD
			assert.Contains(t, result, tc.expectedText, "Type %d should contain %s", tc.clueType, tc.expectedText)
		}
	})
}
