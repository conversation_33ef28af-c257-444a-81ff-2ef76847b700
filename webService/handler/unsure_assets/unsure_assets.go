package unsure_assets

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/black_keyword_system"
	"micro-service/middleware/mysql/black_keyword_type"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/cfg"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	"micro-service/webService/handler/asset_account"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"

	"github.com/xuri/excelize/v2"

	elastic_search "github.com/olivere/elastic"
	"github.com/pkg/errors"
	"gorm.io/gorm"

	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/ip_history"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	pb "micro-service/webService/proto"
)

// CleanDomain 清理域名，去除协议前缀和端口号
func CleanDomain(domain string) string {
	// 去掉协议前缀
	if strings.HasPrefix(domain, "http://") {
		domain = domain[7:]
	} else if strings.HasPrefix(domain, "https://") {
		domain = domain[8:]
	}

	// 去掉端口号
	if idx := strings.Index(domain, ":"); idx > 0 {
		domain = domain[:idx]
	}

	return domain
}

// UnsureAssetsList 获取疑似资产列表
func UnsureAssetsList(ctx context.Context, req *pb.UnsureAssetsListRequest, rsp *pb.UnsureAssetsListResponse) error {
	log.WithContextInfof(ctx, "Received unsure_assets.UnsureAssetsList request: %v", req)

	// 添加日志，输出title参数的值
	log.WithContextInfof(ctx, "【调试】Title参数值: %v", req.Title)

	// 设置排序
	var sorts []elastic_search.Sorter
	if req.Sort != nil && *req.Sort != 0 {
		sorts = append(sorts, elastic_search.NewFieldSort("updated_at").Desc())
	} else {
		sorts = append(sorts, elastic_search.NewFieldSort("ip").Asc())
	}

	// 使用 elastic.StructToParams 构建查询条件
	var builder elastic.SearchBuilder
	elastic.StructToParams(req, &builder)

	// 只查询推荐资产且状态为未确认
	builder.AddMust([]interface{}{"type", fofaee_assets.TYPE_RECOMMEND}) // 推荐资产
	builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.STATUS_DEFAULT}})

	if req.Keyword != nil {
		var keywordQuery [][]interface{}
		_, err := strconv.Atoi(*req.Keyword)
		if err == nil {
			keywordQuery = append(keywordQuery, []interface{}{"port_list.logo.hash", *req.Keyword})
			keywordQuery = append(keywordQuery, []interface{}{"port", *req.Keyword})
		}
		keywordQuery = append(keywordQuery, []interface{}{"port_list.cert.subject_key", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.icp.no", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.title.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.url.raw", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.protocol", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.domain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.url", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.subdomain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.url.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"custom_tags.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"ip.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
		builder.AddShould(keywordQuery...)
	}
	// Title
	if len(req.Title) > 0 {
		if utils.ListContains(req.Title, "-") {
			names := utils.ListReplace(req.Title, "-", "")
			if len(names) == 1 {
				// all_title不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
				// 方法2: 字段为null
				builder.AddShould([]interface{}{"all_title", "=", nil})
			} else {
				builder.AddShould([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(names)})
				// all_title不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
				// 方法2: 字段为null
				builder.AddShould([]interface{}{"all_title", "=", nil})
			}
		} else {
			builder.AddMust([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(req.Title)})
		}
	}
	// Domain
	if len(req.Domain) > 0 {
		if utils.ListContains(req.Domain, "-") {
			names := utils.ListReplace(req.Domain, "-", "")
			if len(names) == 1 {
				// all_domain不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
				// 方法2: 字段为null (在ES中null值通常不被索引，但为了完整性添加)
				builder.AddShould([]interface{}{"all_domain", "=", nil})
			} else {
				builder.AddShould([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(names)})
				// all_domain不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
				// 方法2: 字段为null
				builder.AddShould([]interface{}{"all_domain", "=", nil})
			}
		} else {
			builder.AddMust([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(req.Domain)})
		}
	}
	// Hosts
	if req.Hosts != nil {
		var hostsQuery [][]interface{}
		hostsQuery = append(hostsQuery, []interface{}{"port_list.domain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"port_list.subdomain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
		builder.AddShould(hostsQuery...)
	}
	// Assets Source
	if len(req.AssetsSource) > 0 {
		if utils.ListContains(req.AssetsSource, "-") {
			names := utils.ListReplace(req.AssetsSource, "-", "")
			// 过滤掉空字符串，避免ES查询错误
			filteredNames := make([]string, 0)
			for _, name := range names {
				if name != "" {
					filteredNames = append(filteredNames, name)
				}
			}

			// 只有在有有效的资产来源时才添加查询条件
			if len(filteredNames) > 0 {
				builder.AddShould([]interface{}{"host_list.assets_source", "in", elastic.ToInterfaceArray(filteredNames)})
			}
			// 添加不存在assets_source字段的条件（对应空值查询）
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"host_list.assets_source", "EXISTS"}}})
		} else {
			// 过滤掉空字符串
			filteredSources := make([]string, 0)
			for _, source := range req.AssetsSource {
				if source != "" {
					filteredSources = append(filteredSources, source)
				}
			}

			if len(filteredSources) > 0 {
				builder.AddMust([]interface{}{"host_list.assets_source", "in", elastic.ToInterfaceArray(filteredSources)})
			}
		}
	}
	// Cloud Name
	if req.CloudName != nil {
		var nameQuery [][]interface{}
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(*req.CloudName)})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToLower(*req.CloudName))})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToUpper(*req.CloudName))})
		builder.AddShould(nameQuery...)
	}
	// Clue Company Name
	if len(req.ClueCompanyName) > 0 {
		if utils.ListContains(req.ClueCompanyName, "-") {
			names := utils.ListReplace(req.ClueCompanyName, "-", "")
			builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"clue_company_name", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
		}
	}
	// 二次确认
	if req.SecondConfirm != nil {
		if *req.SecondConfirm == 1 {
			builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.StatusUploadAsset, fofaee_assets.StatusConfirmAsset}})
			builder.AddMust([]interface{}{"second_confirm", 1})
			builder.AddShould([]interface{}{"type", "in", []interface{}{foradar_assets.TypeClaimed, foradar_assets.TypeRecommend}}, []interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		} else {
			builder.AddMust([]interface{}{"type", fofaee_assets.TYPE_RECOMMEND})
			builder.AddMustNot([]interface{}{"second_confirm", 1}, []interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		}
	}
	// Not In Clue Domain
	if len(req.NotInClueDomain) > 0 {
		var domainQuery [][]interface{}
		domainQuery = append(domainQuery, []interface{}{"port_list.domain", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
		domainQuery = append(domainQuery, []interface{}{"port_list.subdomain", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
		domainQuery = append(domainQuery, []interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
		domainQuery = append(domainQuery, []interface{}{"host_list.subdomain.keyword", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
		builder.AddShould(domainQuery...)
	}

	// 设置默认分页参数
	page := int(req.Page)
	if page < 1 {
		page = 1
	}
	perPage := int(req.PerPage)
	if perPage < 1 || perPage > 100 {
		perPage = 10
	}

	// 构建查询
	query := builder.Build()
	// 打印ES查询语句
	log.WithContextInfof(ctx, "【ES查询调试】查询条件: %v", query)

	// 执行查询
	total, list, err := elastic.ListByParams[fofaee_assets.FofaeeAssets](page, perPage, query, sorts)
	if err != nil {
		log.WithContextErrorf(ctx, "ES查询失败: %v", err)
		return errors.Wrap(err, "ES查询失败")
	}

	// 处理结果
	lastPage := int32(total / int64(perPage))
	if total%int64(perPage) > 0 {
		lastPage++
	}

	rsp.Total = total
	rsp.PerPage = int32(perPage)
	rsp.CurrentPage = int32(page)
	rsp.LastPage = lastPage
	rsp.From = int32((page-1)*perPage + 1)
	rsp.To = int32(min(int64(page*perPage), total))

	// 处理数据项
	var items []map[string]interface{}

	// 收集所有资产的group_id，用于查询线索链
	var groupIds []string
	for _, item := range list {
		resultItem := map[string]interface{}{}

		// 处理端口列表
		portList := item.PortList
		if portList != nil {
			// 处理端口详情
			for i, port := range portList {
				// 处理Logo内容，转为链接地址形式
				if port.Logo.Hash != nil && port.Logo.Content != "" {
					portList[i].Logo.Content = storage.GenAPIDownloadPath("", port.Logo.Content, ".ico")
				}

				// 处理HTTP状态码，转为整数
				if port.HttpStatusCode != nil {
					switch v := port.HttpStatusCode.(type) {
					case string:
						code, _ := strconv.Atoi(v)
						portList[i].HttpStatusCode = code
					case float64:
						portList[i].HttpStatusCode = int(v)
					}
				}

				// 处理端口号，转为整数
				if port.Port != nil {
					switch v := port.Port.(type) {
					case float64:
						portList[i].Port = int(v)
					case string:
						port, _ := strconv.Atoi(v)
						portList[i].Port = port
					}
				}
			}

			// 处理主机列表
			hostList := item.HostList
			if len(hostList) > 0 {
				for i, host := range hostList {
					// 处理Logo内容
					if host.Logo.Hash != nil && host.Logo.Content != "" {
						hostList[i].Logo.Content = storage.GenAPIDownloadPath("", host.Logo.Content, ".ico")
					}

					// 处理HTTP状态码
					if host.HttpStatusCode != nil {
						switch v := host.HttpStatusCode.(type) {
						case string:
							code, _ := strconv.Atoi(v)
							hostList[i].HttpStatusCode = code
						case float64:
							hostList[i].HttpStatusCode = int(v)
						}
					}

					// 处理端口号
					if host.Port != nil {
						switch v := host.Port.(type) {
						case float64:
							hostList[i].Port = int(v)
						case string:
							port, _ := strconv.Atoi(v)
							hostList[i].Port = port
						}
					}

					// 处理域名和子域名，去除协议前缀和端口号
					if host.Domain != "" {
						hostList[i].Domain = CleanDomain(host.Domain)
					}
					if host.Subdomain != "" {
						hostList[i].Subdomain = CleanDomain(host.Subdomain)
					}
				}
			}

			resultItem["port_list"] = portList
			resultItem["host_list"] = hostList
		}

		// 处理基本信息
		resultItem["id"] = item.Id
		resultItem["_id"] = item.Id
		resultItem["ip"] = item.Ip
		resultItem["param_ip"] = item.Ip
		resultItem["user_id"] = item.UserId
		resultItem["type"] = item.Type
		resultItem["status"] = item.Status
		resultItem["created_at"] = item.CreatedAt
		resultItem["updated_at"] = item.UpdatedAt
		resultItem["create_time"] = item.CreatedAt
		resultItem["last_update_time"] = item.UpdatedAt
		resultItem["geo"] = item.Geo
		resultItem["latitude"] = item.Geo.Lat
		resultItem["longitude"] = item.Geo.Lon
		resultItem["isp"] = item.Geo.Isp
		resultItem["province"] = item.Geo.Province
		resultItem["asn"] = item.Geo.Asn
		resultItem["clue_company_name"] = item.ClueCompanyName
		resultItem["company_tag_name"] = item.ClueCompanyName
		resultItem["cloud_name"] = item.CloudName
		resultItem["reason_arr"] = item.ReasonArr
		resultItem["customer_tags"] = item.CustomerTags
		resultItem["rule_tags"] = item.RuleTags
		resultItem["tags"] = item.Tags
		resultItem["port_size"] = len(item.PortList)
		resultItem["org_detect_assets_tasks_id"] = item.OrgDetectAssetsTasksId
		resultItem["task_id"] = item.TaskId
		resultItem["is_ipv6"] = item.IsIpv6
		resultItem["is_shadow"] = item.IsShadow
		resultItem["level"] = item.Level
		resultItem["hosts"] = item.Hosts
		resultItem["host_list"] = item.HostList
		resultItem["detect_assets_tasks_id"] = item.DetectAssetsTasksId
		resultItem["online_state"] = item.OnlineState
		resultItem["state"] = getStateText(item.OnlineState)
		resultItem["is_cdn"] = item.IsCdn
		resultItem["website_message_id"] = item.WebsiteMessageId
		resultItem["organization_id"] = item.OrganizationId
		resultItem["reliability_score"] = item.ReliabilityScore
		resultItem["threaten_type"] = item.ThreatenType
		resultItem["port_list_total_page"] = 0    // 默认值，可根据实际情况调整
		resultItem["port_list_per_page_num"] = 30 // 默认值，可根据实际情况调整
		resultItem["rule_infos"] = buildRuleInfos(item.RuleTags)
		resultItem["host_reflect"] = []interface{}{} // 默认空数组
		resultItem["chain_list"] = []interface{}{}   // 默认空数组，后面会被更新
		resultItem["ip_match"] = item.IpMatch
		items = append(items, resultItem)

		// 从ReasonArr中提取group_id
		log.WithContextInfof(ctx, "从reason_arr中提取group_id，数组长度: %d", len(item.ReasonArr))

		// 处理ReasonArr，提取group_id
		for _, r := range item.ReasonArr {
			if reasonMap, ok := r.(map[string]interface{}); ok {
				if groupId, ok := reasonMap["group_id"].(float64); ok {
					strGroupId := fmt.Sprintf("%d", int64(groupId))
					log.WithContextInfof(ctx, "从reason_arr提取到group_id: %s (float64)", strGroupId)
					groupIds = append(groupIds, strGroupId)
				} else if groupId, ok := reasonMap["group_id"].(int64); ok {
					strGroupId := fmt.Sprintf("%d", groupId)
					log.WithContextInfof(ctx, "从reason_arr提取到group_id: %s (int64)", strGroupId)
					groupIds = append(groupIds, strGroupId)
				} else if groupId, ok := reasonMap["group_id"].(string); ok {
					log.WithContextInfof(ctx, "从reason_arr提取到group_id: %s (string)", groupId)
					groupIds = append(groupIds, groupId)
				} else if groupId, ok := reasonMap["group_id"].(json.Number); ok {
					strGroupId := groupId.String()
					log.WithContextInfof(ctx, "从reason_arr提取到group_id: %s (json.Number)", strGroupId)
					groupIds = append(groupIds, strGroupId)
				}
			} else {
				// 尝试JSON转换
				reasonBytes, err := json.Marshal(r)
				if err == nil {
					var reasonMap map[string]interface{}
					if err = json.Unmarshal(reasonBytes, &reasonMap); err == nil {
						if groupId, ok := reasonMap["group_id"].(float64); ok {
							strGroupId := fmt.Sprintf("%d", int64(groupId))
							log.WithContextInfof(ctx, "通过JSON转换提取到group_id: %s (float64)", strGroupId)
							groupIds = append(groupIds, strGroupId)
						} else if groupId, ok := reasonMap["group_id"].(int64); ok {
							strGroupId := fmt.Sprintf("%d", groupId)
							log.WithContextInfof(ctx, "通过JSON转换提取到group_id: %s (int64)", strGroupId)
							groupIds = append(groupIds, strGroupId)
						} else if groupId, ok := reasonMap["group_id"].(string); ok {
							log.WithContextInfof(ctx, "通过JSON转换提取到group_id: %s (string)", groupId)
							groupIds = append(groupIds, groupId)
						}
					}
				}
			}
		}
	}

	// 去重
	groupIds = utils.FilterSliceEmpty(groupIds)
	log.WithContextInfof(ctx, "收集到的去重后group_ids: %v, 数量: %d", groupIds, len(groupIds))

	// 处理证据链
	if len(groupIds) > 0 {
		// 将string类型的groupIds转换为uint64类型
		var groupIdsUint64 []uint64
		for _, gid := range groupIds {
			if id, err := strconv.ParseUint(gid, 10, 64); err == nil {
				groupIdsUint64 = append(groupIdsUint64, id)
			}
		}

		// 如果没有有效的group_id，则跳过线索查询
		if len(groupIdsUint64) == 0 {
			log.WithContextInfof(ctx, "没有有效的group_id，跳过线索查询")
			return nil
		}

		log.WithContextInfof(ctx, "准备查询线索，有效的group_ids: %v", groupIdsUint64)

		// 查询线索表
		clueList, err := clues.NewCluer().ListAll(func(db *gorm.DB) {
			db.Where("user_id = ?", req.UserId)
		})
		if err != nil {
			log.WithContextErrorf(ctx, "查询线索失败: %v", err)
		} else {
			log.WithContextInfof(ctx, "查询线索成功，查询到线索数量=%d", len(clueList))
			// 处理每个资产项的证据链
			for i, item := range items {
				log.WithContextInfof(ctx, "处理资产项[%d]: ip=%v", i, item["ip"])

				// 获取reason_arr
				var reasonArr []interface{}
				if updatedAt, ok := item["updated_at"].(string); ok && updatedAt > "2022-09-06 16:13:00" {
					// 新版数据结构，直接使用reason_arr
					if arr, ok := item["reason_arr"].([]interface{}); ok {
						reasonArr = arr
						log.WithContextInfof(ctx, "使用新版数据结构的reason_arr，长度: %d", len(reasonArr))
					}
				} else {
					// 旧版数据结构，从port_list.reason中获取
					if portList, ok := item["port_list"].([]interface{}); ok {
						for _, port := range portList {
							if portMap, ok := port.(map[string]interface{}); ok {
								if reason, ok := portMap["reason"].([]interface{}); ok {
									reasonArr = append(reasonArr, reason...)
									log.WithContextInfof(ctx, "从port_list.reason中获取到%d个reason", len(reason))
								}
							}
						}
					}
				}

				// 去重reason_arr
				var uniqueReasonArr []interface{}
				reasonMap := make(map[string]bool)
				for _, r := range reasonArr {
					if reasonBytes, err := json.Marshal(r); err == nil {
						key := string(reasonBytes)
						if !reasonMap[key] {
							uniqueReasonArr = append(uniqueReasonArr, r)
							reasonMap[key] = true
						}
					}
				}
				reasonArr = uniqueReasonArr
				log.WithContextInfof(ctx, "去重后的reason_arr长度: %d", len(reasonArr))

				// 为每个reason构建线索链
				var chainLists [][]map[string]interface{}
				for _, r := range reasonArr {
					if reasonMap, ok := r.(map[string]interface{}); ok {
						// 获取reason的id
						var reasonID int64
						if id, ok := reasonMap["id"].(float64); ok {
							reasonID = int64(id)
						} else if id, ok := reasonMap["id"].(int64); ok {
							reasonID = id
						} else if id, ok := reasonMap["id"].(string); ok {
							if idVal, err := strconv.ParseInt(id, 10, 64); err == nil {
								reasonID = idVal
							}
						}

						if reasonID > 0 {
							// 构建线索链
							var chainList []map[string]interface{}
							getTopClue(reasonID, clueList, &chainList)

							if len(chainList) > 0 {
								// 添加当前IP到证据链末尾
								ipStr := fmt.Sprintf("%v", item["ip"])
								chainList = append(chainList, map[string]interface{}{
									"content": ipStr,
								})
								log.WithContextInfof(ctx, "使用reason_id=%d构建的证据链长度: %d", reasonID, len(chainList))

								// 添加到结果中
								chainLists = append(chainLists, chainList)
							}
						}
					}
				}

				// 将线索链添加到结果中
				if len(chainLists) > 0 {
					item["chain_list"] = chainLists
					log.WithContextInfof(ctx, "为资产项[%d]添加了%d条证据链", i, len(chainLists))
				} else {
					log.WithContextInfof(ctx, "资产项[%d]没有构建出证据链", i)
				}
			}
		}
	}

	// 将结果转为JSON
	jsonData, err := json.Marshal(items)
	if err != nil {
		log.WithContextErrorf(ctx, "序列化结果失败: %v", err)
		return errors.Wrap(err, "序列化结果失败")
	}

	rsp.Data = string(jsonData)
	return nil
}

// getTopClue 获取线索链，递归查找父级线索
func getTopClue(parentID int64, clueList []*clues.Clue, chainList *[]map[string]interface{}, id ...int64) {
	var currentID int64
	if len(id) > 0 {
		currentID = id[0]
	}

	log.Infof("getTopClue调用: parentID=%d, currentID=%d, 线索数量=%d, 当前链长度=%d",
		parentID, currentID, len(clueList), len(*chainList))

	for _, clue := range clueList {
		// 处理初始查找（通过ID查找）
		if parentID == 0 && clue.Id == uint64(currentID) {
			// 如果是推荐来源且有from_ip
			if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
				// 如果不是首级线索，将from_ip添加到末尾
				if clue.ParentId > 0 {
					*chainList = append(*chainList, map[string]interface{}{
						"content": clue.FromIp,
					})
					log.Infof("添加非首级线索的from_ip到末尾: %s", clue.FromIp)
				} else {
					// 否则添加到开头
					*chainList = append([]map[string]interface{}{
						{"content": clue.FromIp},
					}, *chainList...)
					log.Infof("添加首级线索的from_ip到开头: %s", clue.FromIp)
				}
			}
		} else {
			// 通过父ID查找
			if clue.Id == uint64(parentID) {
				// 将当前线索添加到链的开头
				*chainList = append([]map[string]interface{}{
					{
						"id":                clue.Id,
						"content":           clue.Content,
						"type":              clue.Type,
						"parent_id":         clue.ParentId,
						"clue_company_name": clue.ClueCompanyName,
						"source":            clue.Source,
					},
				}, *chainList...)
				log.Infof("添加线索到链开头: id=%d, content=%s, parent_id=%d",
					clue.Id, clue.Content, clue.ParentId)

				// 如果是推荐来源且有from_ip
				if clue.Source == clues.SOURCE_RECOMMEND && clue.FromIp != "" {
					*chainList = append([]map[string]interface{}{
						{"content": clue.FromIp},
					}, *chainList...)
					log.Infof("添加推荐来源线索的from_ip到开头: %s", clue.FromIp)
				}

				// 递归查找父级线索
				getTopClue(int64(clue.ParentId), clueList, chainList, currentID)
			}
		}
	}
}

func UnclaimAssetsIPExport(_ context.Context, req *pb.IpAssetActionRequest, resp *pb.FileExportResponse) error {
	log.Infof("[疑似资产] 导出请求: %v", req)
	query := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_DEFAULT})
	log.Infof("[疑似资产] 查询条件参数: %s", utils.AnyToStr(query))
	q := elastic.ParseQuery(query)
	list, err := elastic.All[fofaee_assets.FofaeeAssets](500, q, asset_account.GetFofaAssetsDefaultSorter())
	if err != nil {
		log.Errorf("[疑似资产] 查询失败: %v, req: %v", err, req)
		return err
	}

	// 生成Excel文件
	file := excelize.NewFile()
	sheet := "疑似资产"
	err = file.SetSheetName("Sheet1", sheet)
	if err != nil {
		return err
	}

	// 设置表头
	headers := []string{"IP地址", "企业名称", "端口", "协议", "域名", "URL", "网站标题", "状态码", "云厂商", "组件信息", "地理位置", "资产状态", "资产标签", "发现时间", "更新时间"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err = file.SetCellValue(sheet, cell, header)
		if err != nil {
			return err
		}
	}

	// 写入数据
	row := 2
	for _, r := range list {
		companyName := ""
		if len(r.ClueCompanyName) > 0 {
			companyName, _ = r.ClueCompanyName[0].(string)
		}
		cloudName := ""
		if len(r.CloudName) > 0 {
			cloudName, _ = r.CloudName[0].(string)
		}
		for _, u := range r.HostList {
			if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Ip); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), companyName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), u.Port); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("D%d", row), u.Protocol); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("E%d", row), u.Subdomain); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("F%d", row), u.Url); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("G%d", row), u.Title); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("H%d", row), u.HttpStatusCode); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("I%d", row), cloudName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), asset_account.GetRuleTagString(r.RuleTags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), r.Geo.Province); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), utils.If(cast.ToInt(u.OnlineState) == 1, "在线", "离线")); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("M%d", row), asset_account.GetAssetTagString(r.Tags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("N%d", row), r.CreatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("O%d", row), r.UpdatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			row++
		}
		if len(r.HostList) == 0 {
			if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Ip); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), companyName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("I%d", row), cloudName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), asset_account.GetRuleTagString(r.RuleTags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), r.Geo.Province); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), ""); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("M%d", row), asset_account.GetAssetTagString(r.Tags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("N%d", row), r.CreatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("O%d", row), r.UpdatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			row++
		}
	}

	// 保存文件
	filename := fmt.Sprintf("unclaim_assets_%s.xlsx", time.Now().Format("**************"))
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	if err := file.SaveAs(storagePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	fp, err := utils.DownloadFileEncrypt(accessPath, "疑似资产数据"+time.Now().Format(time.DateOnly)+path.Ext(filename), "", false)
	if err != nil {
		return fmt.Errorf("生成下载路径失败: %v", err)
	}
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(filename))

	// 设置返回的文件路径
	resp.Url = apiFilePath
	return nil
}

func IpUnsureAssetDelete(_ context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.Infof("[IP疑似资产删除] 开始删除操作，请求参数: %v", req)

	// 第一步：从 fofaee_assets 索引中查询要删除的资产，获取IP列表
	query := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_DEFAULT})
	log.Infof("[IP疑似资产删除] 构建删除查询条件: %s", utils.AnyToStr(query))
	q := elastic.ParseQuery(query)

	// 查询要删除的资产列表
	assets, err := elastic.All[fofaee_assets.FofaeeAssets](500, q, nil)
	if err != nil {
		log.Errorf("[IP疑似资产删除] 查询要删除的资产失败: %v, req: %v", err, req)
		return err
	}

	if len(assets) == 0 {
		log.Infof("[IP疑似资产删除] 没有找到要删除的疑似资产")
		return nil
	}

	// 提取IP列表
	var ipList []string
	for _, asset := range assets {
		if asset.Ip != "" {
			ipList = append(ipList, asset.Ip)
		}
	}

	log.Infof("[IP疑似资产删除] 找到 %d 个疑似资产要删除，IP列表: %v", len(assets), ipList)

	// 第二步：删除 fofaee_assets 索引中的数据
	err = elastic.Delete[fofaee_assets.FofaeeAssets](q)
	if err != nil {
		log.Errorf("[IP疑似资产删除] 删除 fofaee_assets 中的疑似资产失败: %v, req: %v", err, req)
		return err
	}

	log.Infof("[IP疑似资产删除] 成功删除 fofaee_assets 中的 %d 个疑似资产", len(assets))

	// 第三步：如果有IP列表，删除 foradar_assets 索引中对应的数据
	if len(ipList) > 0 {
		// 构建删除 foradar_assets 的查询条件
		var foradarBuilder elastic.SearchBuilder
		foradarBuilder.AddMust([]interface{}{"user_id", req.UserId})
		foradarBuilder.AddMust([]interface{}{"ip", "in", elastic.ToInterfaceArray(ipList)})

		foradarQuery := elastic.ParseQuery(foradarBuilder.Build())
		log.Infof("[IP疑似资产删除] foradar_assets 删除条件: %v", foradarBuilder.Build())

		// 删除 foradar_assets 中的数据
		err = elastic.Delete[foradar_assets.ForadarAsset](foradarQuery)
		if err != nil {
			log.Errorf("[IP疑似资产删除] 删除 foradar_assets 失败: %v, IP列表: %v", err, ipList)
			// 这里不返回错误，因为主要的删除操作已经成功
			log.Warnf("[IP疑似资产删除] foradar_assets 删除失败，但 fofaee_assets 删除成功")
		} else {
			log.Infof("[IP疑似资产删除] 成功删除 foradar_assets 中 %d 个疑似资产IP的数据", len(ipList))
		}

		// 第四步：删除IP历史记录数据
		ipHistoryModel := ip_history.NewModel()
		err = ipHistoryModel.DeleteByUserIdAndIps(context.Background(), req.UserId, ipList)
		if err != nil {
			log.Errorf("[IP疑似资产删除] 删除疑似资产IP历史记录失败: %v, IP列表: %v", err, ipList)
			// 这里不返回错误，因为主要的删除操作已经成功
			log.Warnf("[IP疑似资产删除] IP历史记录删除失败，但主要删除操作成功")
		} else {
			log.Infof("[IP疑似资产删除] 成功删除 %d 个疑似资产IP的历史记录", len(ipList))
		}
	}

	log.Infof("[IP疑似资产删除] 删除操作完成，共处理 %d 个疑似资产", len(assets))
	return nil
}

func BatchMatchBlackWord(ctx context.Context, req *pb.IpAssetActionRequest, resp *pb.BatchResponse) error {
	log.Infof("[批量匹配威胁词库] 开始匹配操作，请求参数: %v", req)

	// 第一步：构建查询条件，查询疑似资产
	query := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_DEFAULT})
	log.Infof("[批量匹配威胁词库] 构建查询条件: %s", utils.AnyToStr(query))

	// 添加类型过滤：只匹配推荐类型的资产
	var builder elastic.SearchBuilder
	builder.AddMust([]interface{}{"type", fofaee_assets.TYPE_RECOMMEND})
	builder.AddMust([]interface{}{"user_id", req.UserId})
	builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.STATUS_DEFAULT}})

	// 如果有select_ip，添加IP过滤
	if len(req.SelectIp) > 0 {
		var ipIds []string
		for _, ip := range req.SelectIp {
			ipIds = append(ipIds, utils.AnyToStr(req.UserId)+"_"+ip)
		}
		builder.AddMust([]interface{}{"_id", "in", elastic.ToInterfaceArray(ipIds)})
	} else if len(req.Id) > 0 {
		// 如果有ID，使用ID过滤
		builder.AddMust([]interface{}{"_id", "in", elastic.ToInterfaceArray(req.Id)})
	}

	finalQuery := elastic.ParseQuery(builder.Build())

	// 查询要匹配的资产列表
	assets, err := elastic.All[fofaee_assets.FofaeeAssets](500, finalQuery, nil)
	if err != nil {
		log.Errorf("[批量匹配威胁词库] 查询疑似资产失败: %v, req: %v", err, req)
		return err
	}

	if len(assets) == 0 {
		log.Infof("[批量匹配威胁词库] 没有找到要匹配的疑似资产")
		resp.Num = 0
		return nil
	}

	log.Infof("[批量匹配威胁词库] 找到 %d 个疑似资产要进行威胁词库匹配", len(assets))

	// 第二步：获取威胁词库关键词
	blackTitles, err := getBlackTitles(ctx)
	if err != nil {
		log.Errorf("[批量匹配威胁词库] 获取威胁词库关键词失败: %v", err)
		return err
	}

	log.Infof("[批量匹配威胁词库] 获取到 %d 个威胁词库关键词", len(blackTitles))

	// 第三步：获取威胁类型映射
	typeToName, err := getBlackKeywordTypeMap(ctx)
	if err != nil {
		log.Errorf("[批量匹配威胁词库] 获取威胁类型映射失败: %v", err)
		return err
	}

	// 第四步：匹配威胁关键词，收集需要更新的IP和威胁信息
	var threatenIps []string
	var threatenUpdates []map[string]interface{}

	for _, asset := range assets {
		// 获取该资产的所有标题
		var allTitles []string
		for _, host := range asset.HostList {
			if host.Title != nil {
				if titleStr, ok := host.Title.(string); ok && strings.TrimSpace(titleStr) != "" {
					allTitles = append(allTitles, titleStr)
				}
			}
		}

		// 匹配威胁关键词
		matched, fakeTypeId, fakeTypeName := matchBlackKeywords(allTitles, blackTitles, typeToName)
		if matched {
			threatenIps = append(threatenIps, asset.Ip)
			threatenUpdates = append(threatenUpdates, map[string]interface{}{
				"ip":                 asset.Ip,
				"id":                 asset.Id,
				"status":             fofaee_assets.STATUS_THREATEN,
				"threaten_type":      fakeTypeId,
				"threaten_type_name": fakeTypeName,
			})
		}
	}

	if len(threatenIps) == 0 {
		log.Infof("[批量匹配威胁词库] 没有匹配到威胁关键词的资产")
		resp.Num = 0
		return nil
	}

	log.Infof("[批量匹配威胁词库] 匹配到 %d 个威胁资产，IP列表: %v", len(threatenIps), threatenIps)

	// 第五步：批量更新 fofaee_assets 索引
	err = batchUpdateFofaeeAssets(ctx, threatenUpdates, req.UserId)
	if err != nil {
		log.Errorf("[批量匹配威胁词库] 批量更新 fofaee_assets 失败: %v", err)
		return err
	}

	// 第六步：批量更新 foradar_assets 索引
	err = batchUpdateForadarAssets(ctx, threatenUpdates, req.UserId)
	if err != nil {
		log.Errorf("[批量匹配威胁词库] 批量更新 foradar_assets 失败: %v", err)
		// 不返回错误，因为主要更新已经成功
		log.Warnf("[批量匹配威胁词库] foradar_assets 更新失败，但 fofaee_assets 更新成功")
	}

	resp.Num = int32(len(threatenIps))
	log.Infof("[批量匹配威胁词库] 威胁词库匹配完成，共处理 %d 个威胁资产", len(threatenIps))

	return nil
}

// getBlackTitles 获取威胁词库关键词（包含缓存逻辑）
func getBlackTitles(ctx context.Context) (map[string]int64, error) {
	// 从数据库获取所有活跃的关键词及其类型ID
	blackTitles, err := black_keyword_system.NewModel().ListAllActiveKeywordWithTypeId()
	if err != nil {
		log.Errorf("[批量匹配威胁词库] 从数据库获取威胁词库关键词失败: %v", err)
		return nil, nil
	}
	// 更新缓存
	var keywordList []string
	for keyword := range blackTitles {
		keywordList = append(keywordList, keyword)
	}
	log.Infof("[批量匹配威胁词库] 从数据库获取到 %d 个关键词并更新缓存", len(blackTitles))
	return blackTitles, nil
}

// getBlackKeywordTypeMap 获取威胁类型映射
func getBlackKeywordTypeMap(ctx context.Context) (map[int64]string, error) {
	typeModel := black_keyword_type.NewTypeModel()
	typeList, _, err := typeModel.List(0, 0, func(db *gorm.DB) {
		db.Where("status = ?", black_keyword_type.Enable)
	})
	if err != nil {
		log.Errorf("[批量匹配威胁词库] 获取威胁类型映射失败: %v", err)
		return nil, err
	}

	result := make(map[int64]string)
	for _, typeInfo := range typeList {
		result[int64(typeInfo.Id)] = typeInfo.Name
	}

	// 添加默认类型
	if _, exists := result[0]; !exists {
		result[0] = "其他"
	}

	return result, nil
}

// matchBlackKeywords 匹配威胁关键词
func matchBlackKeywords(titles []string, blackTitles map[string]int64, typeToName map[int64]string) (bool, int64, string) {
	for _, title := range titles {
		if strings.TrimSpace(title) == "" {
			continue
		}

		for blackKeyword, typeId := range blackTitles {
			// 使用PHP相同的模糊匹配逻辑：count(explode($b, $title)) > 1 || ($title == $b)
			// explode($b, $title) 相当于 strings.Split(title, blackKeyword)
			// count() > 1 表示关键词在标题中至少出现一次（分割后数组长度大于1）
			if len(strings.Split(title, blackKeyword)) > 1 || title == blackKeyword {
				typeName := typeToName[typeId]
				if typeName == "" {
					typeName = "其他"
				}
				log.Infof("[批量匹配威胁词库] 匹配到威胁关键词: %s, 标题: %s, 类型: %s", blackKeyword, title, typeName)
				return true, typeId, typeName
			}
		}
	}
	return false, 0, ""
}

// batchUpdateFofaeeAssets 批量更新 fofaee_assets 索引
func batchUpdateFofaeeAssets(ctx context.Context, updates []map[string]interface{}, userId uint64) error {
	if len(updates) == 0 {
		return nil
	}

	// 逐个更新每个资产，因为每个资产的威胁类型可能不同
	successCount := 0
	for _, update := range updates {
		id, ok := update["id"].(string)
		if !ok {
			log.Warnf("[批量匹配威胁词库] 跳过无效ID的更新记录: %v", update)
			continue
		}

		// 构建单个资产的更新查询条件
		var builder elastic.SearchBuilder
		builder.AddMust([]interface{}{"_id", id})
		builder.AddMust([]interface{}{"user_id", userId})

		// 使用该资产特定的威胁信息
		updateData := map[string]interface{}{
			"status":             fofaee_assets.STATUS_THREATEN,
			"threaten_type":      update["threaten_type"],
			"threaten_type_name": update["threaten_type_name"],
		}

		query := builder.Build()
		err := elastic.UpdateByParams[fofaee_assets.FofaeeAssets](query, updateData)
		if err != nil {
			log.Errorf("[批量匹配威胁词库] 更新 fofaee_assets 资产失败, ID: %s, 错误: %v", id, err)
			continue
		}
		successCount++
	}

	log.Infof("[批量匹配威胁词库] 成功更新 fofaee_assets 中的 %d/%d 个资产", successCount, len(updates))
	return nil
}

// batchUpdateForadarAssets 批量更新 foradar_assets 索引
func batchUpdateForadarAssets(ctx context.Context, updates []map[string]interface{}, userId uint64) error {
	if len(updates) == 0 {
		return nil
	}

	// 逐个更新每个IP的资产，因为每个IP的威胁类型可能不同
	successCount := 0
	for _, update := range updates {
		ip, ok := update["ip"].(string)
		if !ok {
			log.Warnf("[批量匹配威胁词库] 跳过无效IP的更新记录: %v", update)
			continue
		}

		// 构建单个IP的更新查询条件
		var builder elastic.SearchBuilder
		builder.AddMust([]interface{}{"ip", ip})
		builder.AddMust([]interface{}{"user_id", userId})

		// 使用该IP特定的威胁信息
		updateData := map[string]interface{}{
			"status":             fofaee_assets.STATUS_THREATEN,
			"threaten_type":      update["threaten_type"],
			"threaten_type_name": update["threaten_type_name"],
		}

		query := builder.Build()
		err := elastic.UpdateByParams[foradar_assets.ForadarAsset](query, updateData)
		if err != nil {
			log.Errorf("[批量匹配威胁词库] 更新 foradar_assets 资产失败, IP: %s, 错误: %v", ip, err)
			continue
		}
		successCount++
	}

	log.Infof("[批量匹配威胁词库] 成功更新 foradar_assets 中 %d/%d 个IP的数据", successCount, len(updates))
	return nil
}

func AssetsUnsureSetStatus(_ context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.Infof("[疑似资产] 资产处置请求: %v", req)
	// 参数验证
	if req.SetStatus == nil {
		return fmt.Errorf("缺少状态参数！")
	}

	var updateArr = make(map[string]any)
	updateArr["is_shadow"] = fofaee_assets.NOT_SHADOW

	// 根据不同状态处理
	switch *req.SetStatus {
	case fofaee_assets.STATUS_CLAIMED:
		// 检查资产限制
		if cfg.IsLocalClient() {
			// 本地化单独限制
			if req.CompanyId != 0 {
				param := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_DEFAULT})
				param = append(param, []any{"type", fofaee_assets.TYPE_RECOMMEND})
				total, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](param)
				if err != nil {
					return fmt.Errorf("获取IP列表失败: %v", err)
				}
				limit, err := company.CheckLimitById(int64(req.CompanyId), company.LIMIT_TYPE_IP, int(total))
				if err != nil {
					return err
				}
				if limit < 0 {
					return fmt.Errorf("您认领的资产数量超出限制%d个,请去除相应数量后重新操作", -limit)
				}
			} else {
				// 安服或者管理员
				usedNumParam := [][]interface{}{
					{"user_id", req.UserId},
					{"status", "in", []interface{}{1, 4}},
				}
				usedNum, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](usedNumParam)
				if err != nil {
					return fmt.Errorf("获取已使用资产数量失败: %v", err)
				}
				// 获取用户总的授权数量
				userInfo, err := user.NewUserModel().FindById(req.UserId)
				if err != nil {
					return fmt.Errorf("获取用户信息失败: %v", err)
				}

				canScanIpNum := cast.ToInt(userInfo.LimitIPAsset) - int(usedNum)
				if canScanIpNum <= 0 {
					return fmt.Errorf("您认领资产数量超出限制，无法认领")
				}

				param := asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_DEFAULT})
				total, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](param)
				if err != nil {
					return fmt.Errorf("获取IP列表失败: %v", err)
				}
				allCount := int(total) + int(usedNum)
				limitCount := cast.ToInt(userInfo.LimitIPAsset) - allCount
				if limitCount < 0 {
					return fmt.Errorf("您认领资产数量超出限制%d个,请去除相应数量后重新操作", -limitCount)
				}
			}
		} else {
			// 非本地化检查资产限制
			if req.CompanyId != 0 {
				param := [][]interface{}{
					{"type", fofaee_assets.TYPE_RECOMMEND},
				}
				param = append(param, asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_DEFAULT})...)

				total, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](param)
				if err != nil {
					return fmt.Errorf("获取IP列表失败: %v", err)
				}

				limitCount, err := company.CheckLimitById(int64(req.CompanyId), company.LIMIT_TYPE_IP, int(total))
				if err != nil {
					return err
				}
				if limitCount < 0 {
					return fmt.Errorf("您认领资产数量超出限制%d个,请去除相应数量后重新操作", -limitCount)
				}
			}
		}
		updateArr["status"] = fofaee_assets.STATUS_CLAIMED

	case fofaee_assets.STATUS_IGNORE:
		// 忽略资产
		updateArr["status"] = fofaee_assets.STATUS_IGNORE

	case fofaee_assets.STATUS_DEFAULT:
		// 疑似资产
		updateArr["status"] = fofaee_assets.STATUS_DEFAULT
		updateArr["type"] = fofaee_assets.TYPE_RECOMMEND
		updateArr["is_user_sign_unsure"] = 1

	case fofaee_assets.STATUS_THREATEN:
		// 标记威胁资产
		updateArr["status"] = fofaee_assets.STATUS_THREATEN
		if req.ThreatenType != nil {
			updateArr["threaten_type"] = *req.ThreatenType
			if req.ThreatenTypeName != nil {
				updateArr["threaten_type_name"] = *req.ThreatenTypeName
			} else {
				updateArr["threaten_type_name"] = "其他"
			}
		}

	default:
		return fmt.Errorf("不支持的状态: %d", *req.SetStatus)
	}

	// 获取IP列表和组织ID列表
	param := [][]interface{}{
		{"type", fofaee_assets.TYPE_RECOMMEND},
	}
	param = append(param, asset_account.ActionSearch(req, []any{fofaee_assets.STATUS_DEFAULT})...)
	query := elastic.ParseQuery(param)
	assets, err := elastic.All[fofaee_assets.FofaeeAssets](500, query, GetFofaAssetsDefaultSorter(), "ip", "organization_id")
	if err != nil {
		log.Errorf("[IP资产] 获取IP资产失败: %v", err)
		return err
	}

	ipList := utils.ListColumn(assets, func(t *fofaee_assets.FofaeeAssets) string { return t.Ip })
	var orgList []int64
	for _, item := range assets {
		for _, id := range item.OrganizationId {
			orgList = append(orgList, cast.ToInt64(id))
		}
	}
	orgList = utils.ListDistinctNonZero(orgList)

	// 更新FofaAssets
	log.Infof("[疑似资产] 更新资产状态查询条件: %s", utils.AnyToStr(param))
	err = elastic.UpdateByParams[fofaee_assets.FofaeeAssets](param, updateArr)
	if err != nil {
		log.Errorf("[疑似资产] 更新Fofa资产失败: %v, req: %v", err, req)
		return err
	}

	// 更新ForadarAssets
	if len(ipList) > 0 {
		par := [][]interface{}{
			{"user_id", req.UserId},
			{"ip", "in", elastic.ToInterfaceArray(ipList)},
		}
		log.Infof("[疑似资产] 更新Foradar资产查询条件: %s", utils.AnyToStr(par))
		err = elastic.UpdateByParams[foradar_assets.ForadarAsset](par, updateArr)
		if err != nil {
			log.Errorf("[疑似资产] 更新Foradar资产失败: %v, req: %v", err, req)
			return err
		}
	} else {
		return nil
	}

	// 计算资产数量限制
	if req.CompanyId != 0 {
		allUser, err := user.NewUserModel().ListAll(mysql.WithWhere("company_id", req.CompanyId))
		if err != nil {
			log.Errorf("[疑似资产] 获取公司用户失败: %v, req: %v", err, req)
			return err
		}
		ids := utils.ListColumn(allUser, func(t user.User) uint64 { return t.Id })

		var builder elastic.SearchBuilder
		builder.AddShould([]interface{}{"user_id", "in", ids})
		builder.AddShould([]interface{}{"company_id", req.CompanyId})
		builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}})

		assetCount, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](builder.Build())
		if err != nil {
			log.Errorf("[疑似资产] 获取Fofa资产数量失败: %v, req: %v", err, req)
		}

		comp, err := company.NewCompanyModel().FindById(req.CompanyId, req.UserId)
		if err != nil {
			log.Errorf("[疑似资产] 获取企业数据失败: %v, req: %v", err, req)
		}
		err = comp.UpdateLimit(company.LIMIT_TYPE_IP, int(assetCount), false)
		if err != nil {
			log.Errorf("[疑似资产] 更新公司IP资产数量限制失败: %v, req: %v", err, req)
			return err
		}
	}

	// 认领资产的额外处理
	if *req.SetStatus == fofaee_assets.STATUS_CLAIMED {
		log.Infof("[疑似资产] 疑似资产认领到台账: ip_array=%v, user_id=%d", ipList, req.UserId)

		// 生成证书维度数据
		certParam := [][]interface{}{
			{"user_id", req.UserId},
			{"ip", "in", elastic.ToInterfaceArray(ipList)},
			{"cert.subject_key", "exists"},
		}
		certAssets, err := elastic.All[foradar_assets.ForadarAsset](500, elastic.ParseQuery(certParam), nil)
		if err == nil && len(certAssets) > 0 {
			//for _, scanAsset := range certAssets {
			//	// TODO 处理证书资产 CertAssetService::dealCertAsset
			//	if err != nil {
			//		log.Warnf("[疑似资产] 忽略的资产-认领资产dealCertAsset，证书信息提取失败: %v", scanAsset)
			//	}
			//}
		}

		if cfg.ExecGolangJob() {
			//todo 调用go的job
			// 缓存IP条件
			err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
			if err != nil {
				log.Errorf("[忽略资产] 分发缓存IP条件任务失败: %v", err)
			}
		} else {
			// 提取线索
			err = asyncq.ExtractAssetCluesJob.Dispatch(req.UserId, ipList)
			if err != nil {
				log.Errorf("[忽略资产] 分发提取资产线索任务失败: %v", err)
			}

			// 统计线索已认领资产数量
			err = asyncq.CountClueAssetTotalJob.Dispatch(req.UserId)
			if err != nil {
				log.Errorf("[忽略资产] 分发统计线索资产任务失败: %v", err)
			}
			// 缓存IP条件
			err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
			if err != nil {
				log.Errorf("[忽略资产] 分发缓存IP条件任务失败: %v", err)
			}
		}
	}

	if cfg.ExecGolangJob() {
		//todo 调用go的job
		err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
		if err != nil {
			log.Errorf("[忽略资产] 分发缓存IP条件任务失败: %v", err)
		}
	} else {
		// 缓存IP条件
		err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
		if err != nil {
			log.Errorf("[忽略资产] 分发缓存IP条件任务失败: %v", err)
		}
	}

	return nil
}

func GetFofaAssetsDefaultSorter() []elastic_search.Sorter {
	return []elastic_search.Sorter{
		elastic_search.NewFieldSort("updated_at").Desc(),
		elastic_search.NewFieldSort("_id").Desc(),
	}
}

// getStateText 获取状态文本
func getStateText(onlineState interface{}) string {
	var state int
	switch v := onlineState.(type) {
	case int:
		state = v
	case int64:
		state = int(v)
	case float64:
		state = int(v)
	default:
		state = 0
	}

	if state == 1 {
		return "在线"
	}
	return "离线"
}

// buildRuleInfos 根据PHP逻辑构建rule_infos字段
// 对应PHP: $rules = implode(',', array_unique(array_column($rules, 'cn_product')));
func buildRuleInfos(ruleTags []fofaee_assets.RuleTag) string {
	if len(ruleTags) == 0 {
		return ""
	}

	// 提取cn_product字段 - 对应PHP的array_column($rules, 'cn_product')
	var products []string
	for _, tag := range ruleTags {
		if tag.CnProduct != "" {
			products = append(products, tag.CnProduct)
		}
	}

	// 去重 - 对应PHP的array_unique
	uniqueProducts := make(map[string]bool)
	var uniqueList []string
	for _, product := range products {
		if !uniqueProducts[product] {
			uniqueProducts[product] = true
			uniqueList = append(uniqueList, product)
		}
	}

	// 用逗号连接 - 对应PHP的implode(',', ...)
	return strings.Join(uniqueList, ",")
}
