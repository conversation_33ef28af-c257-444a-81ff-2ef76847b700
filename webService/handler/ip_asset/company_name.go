package ip_asset

import (
	"context"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/pkg/errors"
)

// UpdateNameSearch 构建查询条件 - 参考删除操作的查询方式
func UpdateNameSearch(req *pb.IpAssetActionRequest, status []any) [][]interface{} {
	var builder elastic.SearchBuilder

	// 如果有select_ip，优先使用IP过滤
	if len(req.SelectIp) > 0 {
		var ipIds []string
		for _, ip := range req.SelectIp {
			ipIds = append(ipIds, utils.AnyToStr(req.UserId)+"_"+ip)
		}
		builder.AddMust([]interface{}{"_id", "in", elastic.ToInterfaceArray(ipIds)})
	} else if len(req.Id) > 0 {
		// 如果有ID，使用ID过滤
		builder.AddMust([]interface{}{"_id", "in", elastic.ToInterfaceArray(req.Id)})
	}

	// 添加状态过滤
	builder.AddMust([]interface{}{"status", "in", status})

	// 添加用户ID过滤
	builder.AddMust([]interface{}{"user_id", req.UserId})

	return builder.Build()
}

// UpdateCompanyName 批量编辑IP资产的企业名称
func UpdateCompanyName(ctx context.Context, req *pb.IpAssetActionRequest) error {
	log.Infof("[IP台账资产企业名称修改] 开始修改操作，请求参数: %v", req)

	// 参数验证
	if req == nil || req.UserId == 0 || req.SetClueCompanyName == nil || *req.SetClueCompanyName == "" {
		return errors.New("参数错误：用户ID、企业名称不能为空")
	}

	// 第一步：从 fofaee_assets 索引中查询要修改的资产，获取IP列表
	query := UpdateNameSearch(req, []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD})
	log.Infof("[IP台账资产企业名称修改] 构建查询条件: %s", utils.AnyToStr(query))
	q := elastic.ParseQuery(query)

	// 查询要修改的资产列表
	assets, err := elastic.All[fofaee_assets.FofaeeAssets](500, q, nil)
	if err != nil {
		log.Errorf("[IP台账资产企业名称修改] 查询要修改的资产失败: %v, req: %v", err, req)
		return err
	}

	if len(assets) == 0 {
		log.Infof("[IP台账资产企业名称修改] 没有找到要修改的台账资产")
		return nil
	}

	// 提取IP列表
	var ipList []string
	for _, asset := range assets {
		if asset.Ip != "" {
			ipList = append(ipList, asset.Ip)
		}
	}

	log.Infof("[IP台账资产企业名称修改] 找到 %d 个台账资产要修改，IP列表: %v", len(assets), ipList)

	// 构建更新数据 - 参考PHP逻辑，企业名称存储为数组格式
	updateData := map[string]interface{}{
		"clue_company_name": []string{*req.SetClueCompanyName},
	}

	// 第二步：更新 fofaee_assets 索引中的数据
	err = elastic.UpdateByParams[fofaee_assets.FofaeeAssets](query, updateData)
	if err != nil {
		log.Errorf("[IP台账资产企业名称修改] 更新 fofaee_assets 失败: %v, req: %v", err, req)
		return err
	}

	log.Infof("[IP台账资产企业名称修改] 成功更新 fofaee_assets 中的 %d 个台账资产", len(assets))

	// 第三步：如果有IP列表，更新 foradar_assets 索引中对应的数据
	if len(ipList) > 0 {
		// 构建更新 foradar_assets 的查询条件
		var foradarBuilder elastic.SearchBuilder
		foradarBuilder.AddMust([]interface{}{"user_id", req.UserId})
		foradarBuilder.AddMust([]interface{}{"ip", "in", elastic.ToInterfaceArray(ipList)})

		foradarQuery := foradarBuilder.Build()
		log.Infof("[IP台账资产企业名称修改] foradar_assets 更新条件: %v", foradarQuery)

		// 更新 foradar_assets 中的数据
		err = elastic.UpdateByParams[foradar_assets.ForadarAsset](foradarQuery, updateData)
		if err != nil {
			log.Errorf("[IP台账资产企业名称修改] 更新 foradar_assets 失败: %v, IP列表: %v", err, ipList)
			// 这里不返回错误，因为主要的更新操作已经成功
			log.Warnf("[IP台账资产企业名称修改] foradar_assets 更新失败，但 fofaee_assets 更新成功")
		} else {
			log.Infof("[IP台账资产企业名称修改] 成功更新 foradar_assets 中 %d 个台账资产IP的数据", len(ipList))
		}
	}

	// 记录操作日志 - 参考PHP中的LogService::info
	log.Infof("[IP台账资产企业名称修改] 修改操作完成，用户[%d]批量修改 %d 个台账资产的企业名称为[%s]",
		req.UserId, len(assets), *req.SetClueCompanyName)

	return nil
}
