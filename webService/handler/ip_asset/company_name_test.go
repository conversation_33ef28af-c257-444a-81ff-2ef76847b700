package ip_asset

import (
	"context"
	"fmt"
	"testing"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	testcommon "micro-service/initialize/common_test"
	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

var (
	indexName  = fofaee_assets.FofaeeAssetsIndex
	docType    = fofaee_assets.FofaeeAssetsType
	indexName2 = foradar_assets.IndexName
)

// 初始化Mock服务
func initMockServices() {
	cfg.InitLoadCfg()
	log.Init()

	// 初始化Mock服务
	mock := es.NewMockServer()

	// 查询语句Mock数据
	resultList := []fofaee_assets.FofaeeAssets{
		{
			Id:              "1_**********",
			Ip:              "**********",
			UserId:          1,
			Status:          fofaee_assets.STATUS_CLAIMED,
			ClueCompanyName: []any{"上海XXX有限公司"},
			HostList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:     80,
					Protocol: "http",
					Title:    "Example Title 1",
				},
			},
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:     "80",
					Protocol: "http",
					Title:    "Example Title 1",
				},
			},
			UpdatedAt: "2025-06-06 16:13:00",
		},
		{
			Id:              "1_**********",
			Ip:              "**********",
			UserId:          1,
			Status:          fofaee_assets.STATUS_UPLOAD,
			ClueCompanyName: []any{"北京YYY科技有限公司"},
			HostList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:     443,
					Protocol: "https",
					Title:    "Example Title 2",
				},
			},
			PortList: []fofaee_assets.FofaeeAssetPort{
				{
					Port:     "443",
					Protocol: "https",
					Title:    "Example Title 2",
				},
			},
			UpdatedAt: "2025-06-06 16:14:00",
		},
	}

	// 注册搜索响应
	mock.Register("/"+indexName+"/_search", []*elastic.SearchHit{
		{
			Index:  indexName,
			Type:   docType,
			Id:     resultList[0].Id,
			Source: utils.ToJSON(resultList[0]),
		},
		{
			Index:  indexName,
			Type:   docType,
			Id:     resultList[1].Id,
			Source: utils.ToJSON(resultList[1]),
		},
	})

	mock.Register("/"+indexName2+"/_search", []*elastic.SearchHit{
		{
			Index:  indexName2,
			Type:   docType,
			Id:     resultList[0].Id,
			Source: utils.ToJSON(resultList[0]),
		},
		{
			Index:  indexName2,
			Type:   docType,
			Id:     resultList[1].Id,
			Source: utils.ToJSON(resultList[1]),
		},
	})

	// 更新语句Mock数据
	updateResponse := elastic.UpdateResponse{
		Index:   indexName,
		Type:    docType,
		Id:      "1_**********",
		Version: 2,
		Result:  "updated",
		Shards: &elastic.ShardsInfo{
			Total:      2,
			Successful: 1,
			Failed:     0,
		},
	}

	mock.Register("/"+indexName+"/_update_by_query", updateResponse)
	mock.Register("/"+indexName2+"/_update_by_query", updateResponse)

	// 创建Mock服务
	mockClient := mock.NewElasticMockClient()

	// 设置是否使用Mock (默认true)
	es.SetTestEnv(true)
	mysql.SetTestEnv(true)
	redis.SetTestEnv(true)
	testcommon.SetTestEnv(true) // 确保testcommon也知道这是测试环境

	// 设置mock客户端到testcommon，这样GetEsClient()就能获取到正确的客户端
	testcommon.SetElasticClient(mockClient)

	// 初始化数据库
	es.GetInstance(cfg.LoadElastic())
	mysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

func TestUpdateNameSearch(t *testing.T) {
	initMockServices()

	t.Run("with select_ip", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:   1,
			SelectIp: []string{"**********", "**********"},
		}
		status := []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}

		result := UpdateNameSearch(req, status)

		assert.NotNil(t, result)
		assert.Len(t, result, 1) // Should have one MUST clause

		// Verify the structure contains the expected conditions
		mustClause := result[0]
		assert.Equal(t, "MUST", mustClause[0])

		conditions := mustClause[1].([][]interface{})
		assert.Len(t, conditions, 3) // _id, status, user_id

		// Check _id condition
		idCondition := conditions[0]
		assert.Equal(t, "_id", idCondition[0])
		assert.Equal(t, "in", idCondition[1])

		// Check status condition
		statusCondition := conditions[1]
		assert.Equal(t, "status", statusCondition[0])
		assert.Equal(t, "in", statusCondition[1])

		// Check user_id condition
		userIdCondition := conditions[2]
		assert.Equal(t, "user_id", userIdCondition[0])
		assert.Equal(t, uint64(1), userIdCondition[1])
	})

	t.Run("with id", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId: 1,
			Id:     []string{"1_**********", "1_**********"},
		}
		status := []any{fofaee_assets.STATUS_CLAIMED}

		result := UpdateNameSearch(req, status)

		assert.NotNil(t, result)
		assert.Len(t, result, 1)

		mustClause := result[0]
		assert.Equal(t, "MUST", mustClause[0])

		conditions := mustClause[1].([][]interface{})
		assert.Len(t, conditions, 3) // _id, status, user_id
	})

	t.Run("without select_ip and id", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId: 1,
		}
		status := []any{fofaee_assets.STATUS_CLAIMED}

		result := UpdateNameSearch(req, status)

		assert.NotNil(t, result)
		assert.Len(t, result, 1)

		mustClause := result[0]
		conditions := mustClause[1].([][]interface{})
		assert.Len(t, conditions, 2) // Only status and user_id, no _id condition
	})

	t.Run("empty select_ip and id", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:   1,
			SelectIp: []string{},
			Id:       []string{},
		}
		status := []any{fofaee_assets.STATUS_CLAIMED}

		result := UpdateNameSearch(req, status)

		assert.NotNil(t, result)
		mustClause := result[0]
		conditions := mustClause[1].([][]interface{})
		assert.Len(t, conditions, 2) // Only status and user_id
	})
}

func TestUpdateCompanyName(t *testing.T) {
	initMockServices()

	t.Run("success with select_ip", func(t *testing.T) {
		ctx := context.Background()
		companyName := "新测试公司有限公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********", "**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})

	t.Run("success with id", func(t *testing.T) {
		ctx := context.Background()
		companyName := "新测试公司有限公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			Id:                 []string{"1_**********", "1_**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})

	t.Run("success with no assets found", func(t *testing.T) {
		ctx := context.Background()
		companyName := "新测试公司有限公司"
		req := &pb.IpAssetActionRequest{
			UserId:             999, // Non-existent user
			SelectIp:           []string{"***********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err) // Should not return error when no assets found
	})

	t.Run("error - nil request", func(t *testing.T) {
		ctx := context.Background()

		err := UpdateCompanyName(ctx, nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "参数错误：用户ID、企业名称不能为空")
	})

	t.Run("error - zero user_id", func(t *testing.T) {
		ctx := context.Background()
		companyName := "测试公司"
		req := &pb.IpAssetActionRequest{
			UserId:             0,
			SelectIp:           []string{"**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "参数错误：用户ID、企业名称不能为空")
	})

	t.Run("error - nil company name", func(t *testing.T) {
		ctx := context.Background()
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********"},
			SetClueCompanyName: nil,
		}

		err := UpdateCompanyName(ctx, req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "参数错误：用户ID、企业名称不能为空")
	})

	t.Run("error - empty company name", func(t *testing.T) {
		ctx := context.Background()
		companyName := ""
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "参数错误：用户ID、企业名称不能为空")
	})

	t.Run("success with assets having empty IP", func(t *testing.T) {
		// This test covers the case where assets are found but some have empty IP
		ctx := context.Background()
		companyName := "测试公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})

	t.Run("success with special characters in company name", func(t *testing.T) {
		ctx := context.Background()
		companyName := "测试公司（北京）有限责任公司-分公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})

	t.Run("success with long company name", func(t *testing.T) {
		ctx := context.Background()
		companyName := "这是一个非常长的公司名称用来测试系统对于长字符串的处理能力包括中文英文数字和特殊字符1234567890!@#$%^&*()"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})

	t.Run("success with both select_ip and id provided", func(t *testing.T) {
		// When both are provided, select_ip should take priority
		ctx := context.Background()
		companyName := "优先级测试公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********"},
			Id:                 []string{"1_**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})

	t.Run("success with single IP", func(t *testing.T) {
		ctx := context.Background()
		companyName := "单IP测试公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})

	t.Run("success with multiple IPs", func(t *testing.T) {
		ctx := context.Background()
		companyName := "多IP测试公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********", "**********", "**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})
}

// TestUpdateCompanyNameWithMockErrors tests error scenarios with mock setup
func TestUpdateCompanyNameWithMockErrors(t *testing.T) {
	initMockServices()

	t.Run("elasticsearch query error", func(t *testing.T) {
		// This test simulates an Elasticsearch query error
		// In a real scenario, we would need to mock the ES client to return an error
		ctx := context.Background()
		companyName := "测试公司"
		req := &pb.IpAssetActionRequest{
			UserId:             999, // Use a user ID that might cause issues
			SelectIp:           []string{"invalid.ip.address"},
			SetClueCompanyName: &companyName,
		}

		// The mock should handle this gracefully and return no error
		// since our mock is set up to return empty results for unknown queries
		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err) // Should not error, just return no assets found
	})

	t.Run("context cancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel the context immediately

		companyName := "测试公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		// The function should handle context cancellation gracefully
		// In our current implementation, it might not check context cancellation
		// but this test ensures we're aware of this scenario
		assert.NoError(t, err) // Current implementation doesn't check context
	})
}

// TestUpdateNameSearchEdgeCases tests edge cases for UpdateNameSearch function
func TestUpdateNameSearchEdgeCases(t *testing.T) {
	t.Run("nil request", func(t *testing.T) {
		// This should panic or handle gracefully
		defer func() {
			if r := recover(); r != nil {
				// Expected to panic due to nil pointer dereference
				assert.NotNil(t, r)
			}
		}()

		status := []any{fofaee_assets.STATUS_CLAIMED}
		result := UpdateNameSearch(nil, status)
		assert.Nil(t, result) // Should not reach here if panic occurs
	})

	t.Run("empty status array", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:   1,
			SelectIp: []string{"**********"},
		}
		status := []any{}

		result := UpdateNameSearch(req, status)

		assert.NotNil(t, result)
		mustClause := result[0]
		conditions := mustClause[1].([][]interface{})

		// Should still have the status condition even with empty array
		statusCondition := conditions[1]
		assert.Equal(t, "status", statusCondition[0])
		assert.Equal(t, "in", statusCondition[1])
		assert.Equal(t, []any{}, statusCondition[2])
	})

	t.Run("nil status array", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:   1,
			SelectIp: []string{"**********"},
		}

		result := UpdateNameSearch(req, nil)

		assert.NotNil(t, result)
		mustClause := result[0]
		conditions := mustClause[1].([][]interface{})

		// Should still have the status condition
		statusCondition := conditions[1]
		assert.Equal(t, "status", statusCondition[0])
		assert.Equal(t, "in", statusCondition[1])
		assert.Nil(t, statusCondition[2])
	})

	t.Run("mixed status types", func(t *testing.T) {
		req := &pb.IpAssetActionRequest{
			UserId:   1,
			SelectIp: []string{"**********"},
		}
		status := []any{fofaee_assets.STATUS_CLAIMED, "string_status", 999}

		result := UpdateNameSearch(req, status)

		assert.NotNil(t, result)
		mustClause := result[0]
		conditions := mustClause[1].([][]interface{})

		statusCondition := conditions[1]
		assert.Equal(t, "status", statusCondition[0])
		assert.Equal(t, "in", statusCondition[1])
		assert.Equal(t, status, statusCondition[2])
	})
}

// TestUpdateCompanyNameIntegration tests the complete flow
func TestUpdateCompanyNameIntegration(t *testing.T) {
	initMockServices()

	t.Run("complete flow with assets found", func(t *testing.T) {
		ctx := context.Background()
		companyName := "集成测试公司有限公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			SelectIp:           []string{"**********", "**********"},
			SetClueCompanyName: &companyName,
		}

		// This should execute the complete flow:
		// 1. Build query conditions
		// 2. Query fofaee_assets
		// 3. Extract IP list
		// 4. Update fofaee_assets
		// 5. Update foradar_assets
		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})

	t.Run("complete flow with ID-based query", func(t *testing.T) {
		ctx := context.Background()
		companyName := "ID查询测试公司"
		req := &pb.IpAssetActionRequest{
			UserId:             1,
			Id:                 []string{"1_**********", "1_**********"},
			SetClueCompanyName: &companyName,
		}

		err := UpdateCompanyName(ctx, req)
		assert.NoError(t, err)
	})
}

// TestUpdateNameSearchConcurrency tests concurrent access to UpdateNameSearch
func TestUpdateNameSearchConcurrency(t *testing.T) {
	const numGoroutines = 10
	const numIterations = 100

	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			for j := 0; j < numIterations; j++ {
				req := &pb.IpAssetActionRequest{
					UserId:   uint64(id),
					SelectIp: []string{fmt.Sprintf("10.10.10.%d", j%255)},
				}
				status := []any{fofaee_assets.STATUS_CLAIMED}

				result := UpdateNameSearch(req, status)
				assert.NotNil(t, result)
			}
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}
}

// TestUpdateCompanyNameValidation tests input validation thoroughly
func TestUpdateCompanyNameValidation(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name        string
		req         *pb.IpAssetActionRequest
		expectError bool
		errorMsg    string
	}{
		{
			name:        "nil request",
			req:         nil,
			expectError: true,
			errorMsg:    "参数错误：用户ID、企业名称不能为空",
		},
		{
			name: "zero user ID",
			req: &pb.IpAssetActionRequest{
				UserId:             0,
				SetClueCompanyName: proto.String("测试公司"),
			},
			expectError: true,
			errorMsg:    "参数错误：用户ID、企业名称不能为空",
		},
		{
			name: "nil company name",
			req: &pb.IpAssetActionRequest{
				UserId:             1,
				SetClueCompanyName: nil,
			},
			expectError: true,
			errorMsg:    "参数错误：用户ID、企业名称不能为空",
		},
		{
			name: "empty company name",
			req: &pb.IpAssetActionRequest{
				UserId:             1,
				SetClueCompanyName: proto.String(""),
			},
			expectError: true,
			errorMsg:    "参数错误：用户ID、企业名称不能为空",
		},
		{
			name: "whitespace only company name",
			req: &pb.IpAssetActionRequest{
				UserId:             1,
				SetClueCompanyName: proto.String("   "),
			},
			expectError: false, // Current implementation doesn't trim whitespace, so this passes
			errorMsg:    "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := UpdateCompanyName(ctx, tc.req)
			if tc.expectError {
				assert.Error(t, err)
				if err != nil {
					assert.Contains(t, err.Error(), tc.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
