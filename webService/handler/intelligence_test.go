package handler

import (
	"context"
	_ "encoding/json"
	"micro-service/initialize/es"
	mysqllib "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
	"testing"
)

func Init() {
	log.Init()
	cfg.InitLoadCfg()
	_ = mysqllib.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
	es.GetInstance(cfg.LoadElastic())
}

func TestIntelligence(t *testing.T) {
	Init()
	web := Web{}
	err := web.IntelligenceHotPocCheck(context.TODO(), &pb.IntelligenceHotPocCheckRequest{UserId: 5, Id: []uint64{}}, &pb.Empty{})
	if err != nil {
		panic(err)
	}
}

func TestPocInfo(t *testing.T) {
	Init()
	web := Web{}
	rsp := pb.IntelligenceHotPocInfoAssetResponse{}
	err := web.IntelligenceHotPocInfoAsset(context.TODO(), &pb.IntelligenceHotPocInfoAssetRequest{UserId: 5, Id: 71, Page: 1, PerPage: 10}, &rsp)
	if err != nil {
		panic(err)
	}
}
func TestPocAssetList(t *testing.T) {
	Init()
	web := Web{}
	rsp := pb.IntelligenceHotPocAssetListResponse{}
	err := web.IntelligenceHotPocAssetList(context.TODO(), &pb.IntelligenceHotPocAssetListRequest{UserId: 5, Page: 1, PerPage: 10}, &rsp)
	if err != nil {
		panic(err)
	}
}
