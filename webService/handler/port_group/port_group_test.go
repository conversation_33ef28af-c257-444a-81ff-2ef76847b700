package port_group

import (
	"context"
	"errors"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
	"testing"
)

// 初始化Mock服务
func InitPortGroup() {
	cfg.InitLoadCfg()
	log.Init()
	// 设置是否使用Mock (默认true)
	mysql.SetTestEnv(true)
	// 初始化数据库
	mysql.GetInstance(cfg.LoadMysql())
}

func TestPortGroupIndex(t *testing.T) {
	t.Run("database error on count", func(t *testing.T) {
		InitPortGroup()
		mock := mysql.GetMockInstance()

		// Mock count query with error
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `port_groups` WHERE user_id = \\?").
			WithArgs(uint64(1)).
			WillReturnError(errors.New("database connection error"))

		req := &pb.PortGroupIndexRequest{
			UserId: 1,
			Page:   1,
			Size:   10,
		}
		resp := &pb.PortGroupIndexResponse{}

		err := PortGroupIndex(context.Background(), req, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database connection error")
	})

	t.Run("successful case with empty data", func(t *testing.T) {
		InitPortGroup()
		mock := mysql.GetMockInstance()

		// Mock count query
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `port_groups` WHERE user_id = \\?").
			WithArgs(uint64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		// Mock data query
		mock.ExpectQuery("SELECT \\* FROM `port_groups` WHERE user_id = \\? ORDER BY id desc LIMIT \\?").
			WithArgs(uint64(1), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "name", "can_del", "created_at", "updated_at"}))

		req := &pb.PortGroupIndexRequest{
			UserId: 1,
			Page:   1,
			Size:   10,
		}
		resp := &pb.PortGroupIndexResponse{}

		err := PortGroupIndex(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), resp.Page)
		assert.Equal(t, int64(10), resp.Size)
		assert.Equal(t, int64(0), resp.Total)
		assert.Len(t, resp.Items, 0)
	})



	t.Run("with keyword filter", func(t *testing.T) {
		InitPortGroup()
		mock := mysql.GetMockInstance()

		// Mock count query with keyword
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `port_groups` WHERE user_id = \\? AND name LIKE \\?").
			WithArgs(uint64(1), "%test%").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		// Mock data query with keyword
		mock.ExpectQuery("SELECT \\* FROM `port_groups` WHERE user_id = \\? AND name LIKE \\? ORDER BY id desc LIMIT \\?").
			WithArgs(uint64(1), "%test%", sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "name", "can_del", "created_at", "updated_at"}))

		req := &pb.PortGroupIndexRequest{
			UserId:  1,
			Page:    1,
			Size:    10,
			Keyword: "test",
		}
		resp := &pb.PortGroupIndexResponse{}

		err := PortGroupIndex(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(0), resp.Total)
	})

	t.Run("database error on data query", func(t *testing.T) {
		InitPortGroup()
		mock := mysql.GetMockInstance()

		// Mock count query success
		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `port_groups` WHERE user_id = \\?").
			WithArgs(uint64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		// Mock data query with error
		mock.ExpectQuery("SELECT \\* FROM `port_groups` WHERE user_id = \\? ORDER BY id desc LIMIT \\?").
			WithArgs(uint64(1), sqlmock.AnyArg()).
			WillReturnError(errors.New("data query error"))

		req := &pb.PortGroupIndexRequest{
			UserId: 1,
			Page:   1,
			Size:   10,
		}
		resp := &pb.PortGroupIndexResponse{}

		err := PortGroupIndex(context.Background(), req, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "data query error")
	})
}

func TestPortGroupAdd(t *testing.T) {
	t.Run("basic functionality test", func(t *testing.T) {
		req := &pb.PortGroupAddRequest{
			UserId:           1,
			OperateCompanyId: 1,
			Name:             "Test Group",
			Ports:            []uint64{80, 443},
		}
		resp := &pb.PortGroupAddResponse{}

		// Test that the function can be called without panicking
		_ = PortGroupAdd(context.Background(), req, resp)
		assert.NotNil(t, req)
		assert.NotNil(t, resp)
	})
}

func TestPortGroupDel(t *testing.T) {
	t.Run("basic functionality test", func(t *testing.T) {
		req := &pb.PortGroupDelRequest{
			UserId: 1,
			Id:     []uint64{1, 2},
		}
		resp := &pb.PortGroupDelResponse{}

		// Test that the function can be called without panicking
		_ = PortGroupDel(context.Background(), req, resp)
		assert.NotNil(t, req)
		assert.NotNil(t, resp)
	})
}

func TestPortGroupList(t *testing.T) {
	t.Run("basic functionality test", func(t *testing.T) {
		req := &pb.PortGroupListRequest{
			UserId: 1,
		}
		resp := &pb.PortGroupListResponse{}

		// Test that the function can be called without panicking
		_ = PortGroupList(context.Background(), req, resp)
		assert.NotNil(t, req)
		assert.NotNil(t, resp)
	})

	t.Run("with keyword", func(t *testing.T) {
		req := &pb.PortGroupListRequest{
			UserId:  1,
			Keyword: "test",
		}
		resp := &pb.PortGroupListResponse{}

		// Test that the function can be called without panicking
		_ = PortGroupList(context.Background(), req, resp)
		assert.NotNil(t, req)
		assert.NotNil(t, resp)
	})
}

func TestPortGroupEdit(t *testing.T) {
	t.Run("basic functionality test", func(t *testing.T) {
		req := &pb.PortGroupEditRequest{
			UserId:           1,
			OperateCompanyId: 1,
			Id:               1,
			Name:             "Updated Group",
			Ports:            []uint64{80, 443},
		}
		resp := &pb.PortGroupEditResponse{}

		// Test that the function can be called without panicking
		_ = PortGroupEdit(context.Background(), req, resp)
		assert.NotNil(t, req)
		assert.NotNil(t, resp)
	})
}

func TestPortGroupDetail(t *testing.T) {
	t.Run("basic functionality test", func(t *testing.T) {
		req := &pb.PortGroupDetailRequest{
			UserId: 1,
			Id:     1,
		}
		resp := &pb.PortGroupDetailResponse{}

		// Test that the function can be called without panicking
		_ = PortGroupDetail(context.Background(), req, resp)
		assert.NotNil(t, req)
		assert.NotNil(t, resp)
	})
}
