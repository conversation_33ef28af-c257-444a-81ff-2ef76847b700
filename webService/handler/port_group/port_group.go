// 端口分组

package port_group

import (
	"context"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/port_group"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
	"strconv"
)

func PortGroupIndex(ctx context.Context, req *pb.PortGroupIndexRequest, rsp *pb.PortGroupIndexResponse) error {
	// 获取端口表数据
	if rsp.Page < 1 {
		rsp.Page = 1
	}
	data, total, err := port_group.NewPortGroupModel(mysql.GetInstance()).GetIndexData(req.UserId, req, int(req.Page), int(req.Size))
	if err != nil {
		return err
	}
	for _, v := range data {
		initData := pb.PortGroupInfo{}
		initData.Id = v.Id
		initData.Port = v.Name
		initData.CountPorts = uint64(len(v.Ports))
		initData.CanDel = v.CanDel
		initData.CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")

		var Portlist []uint64
		for _, protocol := range v.Ports {
			Portlist = append(Portlist, uint64(protocol.Port))
		}
		initData.Portlist = Portlist
		rsp.Items = append(rsp.Items, &initData)
	}
	rsp.Page = int64(int(req.Page))
	rsp.Size = int64(int(req.Size))
	rsp.Total = total
	return nil
}

func PortGroupAdd(ctx context.Context, req *pb.PortGroupAddRequest, rsp *pb.PortGroupAddResponse) error {
	err := port_group.NewPortGroupModel(mysql.GetInstance()).PortGroupAdd(req)
	if err != nil {
		return err
	}
	return nil
}

func PortGroupDel(ctx context.Context, req *pb.PortGroupDelRequest, rsp *pb.PortGroupDelResponse) error {
	err := port_group.NewPortGroupModel(mysql.GetInstance()).PortGroupDel(req)
	if err != nil {
		return err
	}
	return nil
}

// PortGroupList 获取端口分组列表
func PortGroupList(ctx context.Context, req *pb.PortGroupListRequest, rsp *pb.PortGroupListResponse) error {
	log.WithContextInfof(ctx, "Received Web.PortGroupList request: %v", req)

	// 创建端口分组模型
	portGroupModel := port_group.NewPortGroupModel()

	// 如果有关键词，使用GetIndexData方法进行搜索
	if req.Keyword != "" {
		// 构建搜索请求
		searchReq := &pb.PortGroupIndexRequest{
			UserId:  req.UserId,
			Keyword: req.Keyword,
		}

		// 获取端口分组列表（带关键词搜索）
		portGroups, _, err := portGroupModel.GetIndexData(uint64(req.UserId), searchReq, 1, 1000) // 使用较大的页码和页大小，相当于不分页
		if err != nil {
			log.WithContextErrorf(ctx, "[端口管理] 获取端口分组列表失败: %v", err)
			return err
		}

		// 构建响应
		for _, group := range portGroups {
			rsp.Items = append(rsp.Items, &pb.PortGroupListInfo{
				Id:   uint64(group.Id),
				Name: group.Name,
			})
		}

		return nil
	}

	// 没有关键词，使用GetIndexList方法获取全部列表
	portGroups, err := portGroupModel.GetIndexList(uint64(req.UserId))
	if err != nil {
		log.WithContextErrorf(ctx, "[端口管理] 获取端口分组列表失败: %v", err)
		return err
	}

	// 构建响应
	for _, group := range portGroups {
		rsp.Items = append(rsp.Items, &pb.PortGroupListInfo{
			Id:   uint64(group.Id),
			Name: group.Name,
		})
	}

	return nil
}

func PortGroupEdit(ctx context.Context, req *pb.PortGroupEditRequest, rsp *pb.PortGroupEditResponse) error {
	err := port_group.NewPortGroupModel(mysql.GetInstance()).PortGroupEdit(req)
	if err != nil {
		return err
	}
	return nil
}

func PortGroupDetail(ctx context.Context, req *pb.PortGroupDetailRequest, rsp *pb.PortGroupDetailResponse) error {
	// 获取端口表数据
	detail, err := port_group.NewPortGroupModel(mysql.GetInstance()).FindByIdAndUserId(req.Id, req.UserId)
	if err != nil {
		return err
	}
	rsp.Id = detail.Id
	rsp.Name = detail.Name
	for _, v := range detail.Ports {
		var initPort pb.PortInfo
		initPort.Key = strconv.FormatUint(v.Id, 10)
		initPort.Title = strconv.Itoa(int(v.Port))
		rsp.Ports = append(rsp.Ports, &initPort)
	}
	return nil
}
