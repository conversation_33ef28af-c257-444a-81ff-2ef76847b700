// 端口

package port_group

import (
	"context"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/port_group"
	pb "micro-service/webService/proto"
	"strconv"
	"strings"
)

func PortIndex(ctx context.Context, req *pb.PortIndexRequest, rsp *pb.PortIndexResponse) error {
	// 获取端口表数据
	if req.Page < 1 {
		req.Page = 1
	}
	data, total, err := port_group.NewPortModel(mysql.GetInstance()).GetIndexData(req.UserId, req, int(req.Page), int(req.Size))

	if err != nil {
		return err
	}

	for i := range data {
		initData := pb.PortInfo{}
		initData.Id = data[i].Id
		initData.Key = strconv.FormatUint(data[i].Id, 10)
		initData.Port = uint64(data[i].Port)
		initData.Title = strconv.Itoa(int(data[i].Port))
		initData.Protocols = ""
		initData.Groups = ""
		var thisProtocols []string
		for _, protocol := range data[i].Protocols {
			thisProtocols = append(thisProtocols, protocol.Protocol)
		}
		initData.Protocols = strings.Join(thisProtocols, ",")
		var thisGroups []string
		for _, group := range data[i].Groups {
			thisGroups = append(thisGroups, group.Name)
		}
		initData.Groups = strings.Join(thisGroups, ",")
		initData.Status = port_group.PortStatusMap[data[i].Status]
		initData.Source = uint32(data[i].Source)
		initData.CreatedAt = data[i].CreatedAt.Format("2006-01-02 15:04:05")
		initData.UpdatedAt = data[i].UpdatedAt.Format("2006-01-02 15:04:05")
		if initData.UpdatedAt == "0001-01-01 00:00:00" {
			initData.UpdatedAt = "-"
		}
		rsp.Items = append(rsp.Items, &initData)
	}
	rsp.Page = int64(int(req.Page))
	rsp.Size = int64(int(req.Size))
	rsp.Total = total
	return nil
}

func PortList(ctx context.Context, req *pb.PortListRequest, rsp *pb.PortListResponse) error {
	// 获取端口表数据
	data, err := port_group.NewPortModel(mysql.GetInstance()).GetIndexList(req.UserId)
	if err != nil {
		return err
	}
	for i := range data {
		initData := pb.PortListInfo{}
		initData.Id = data[i].Id
		initData.Port = uint64(data[i].Port)
		rsp.Items = append(rsp.Items, &initData)
	}
	return nil
}

func PortAdd(ctx context.Context, req *pb.PortAddRequest, rsp *pb.PortAddResponse) error {
	var params = port_group.PortChangeParam{}
	params.Ports = req.Ports
	params.ProtocolIds = req.Protocols
	params.PortGroupIds = req.PortGroups
	params.UserId = req.UserId
	params.CompanyId = int64(req.OperateCompanyId)

	err := port_group.NewPortModel(mysql.GetInstance()).PortStore(&params)
	if err != nil {
		return err
	}
	return nil
}

func PortEdit(ctx context.Context, req *pb.PortEditRequest, rsp *pb.PortEditResponse) error {
	// 获取端口表数据
	var params = port_group.PortChangeParam{}
	params.Id = req.Id
	params.Ports = req.Ports
	params.ProtocolIds = req.Protocols
	params.PortGroupIds = req.PortGroups
	params.UserId = req.UserId
	params.CompanyId = int64(req.OperateCompanyId)
	err := port_group.NewPortModel(mysql.GetInstance()).PortEdit(&params)
	if err != nil {
		return err
	}
	return nil
}

func PortDel(ctx context.Context, req *pb.PortDelRequest, rsp *pb.PortDelResponse) error {
	// 获取端口表数据
	err := port_group.NewPortModel(mysql.GetInstance()).PortDel(req)
	if err != nil {
		return err
	}
	return nil
}

func PortUpdateStatus(ctx context.Context, req *pb.PortUpdateStatusRequest, rsp *pb.PortUpdateStatusResponse) error {
	// 获取端口表数据
	err := port_group.NewPortModel(mysql.GetInstance()).PortUpdateStatus(req)
	if err != nil {
		return err
	}
	return nil
}

func PortProtocolIndex(ctx context.Context, req *pb.PortProtocolIndexRequest, rsp *pb.PortProtocolIndexResponse) error {
	// 获取端口表数据
	data, err := port_group.NewPortModel(mysql.GetInstance()).GetPortProtocolList(req)
	if err != nil {
		return err
	}
	for _, v := range data {
		initData := pb.PortProtocolListInfo{}
		initData.Id = v.Id
		initData.Protocol = v.Protocol
		initData.Type = uint32(v.Type)
		initData.Protocol = v.Protocol
		initData.CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		initData.UpdatedAt = v.UpdatedAt.Format("2006-01-02 15:04:05")
		rsp.Items = append(rsp.Items, &initData)
	}
	return nil
}

func PortDetail(ctx context.Context, req *pb.PortDetailRequest, rsp *pb.PortDetailResponse) error {
	// 获取端口表数据
	detail, err := port_group.NewPortModel(mysql.GetInstance()).FindById(req.Id, req.UserId, true)
	if err != nil {
		return err
	}
	rsp.Id = detail.Id
	rsp.Port = uint64(detail.Port)
	var groups = make([]uint64, 0, len(detail.Groups))
	for _, v := range detail.Groups {
		groups = append(groups, v.Id)
	}
	rsp.Groups = groups
	for _, v := range detail.Protocols {
		var initProtocol pb.ProtocolInfo
		initProtocol.Id = v.Id
		initProtocol.Protocol = v.Protocol
		var initPivot = pb.PivotInfo{}
		initPivot.PortId = detail.Id
		initPivot.PortProtocolId = v.Id
		initProtocol.Pivot = &initPivot
		rsp.Protocols = append(rsp.Protocols, &initProtocol)
	}
	return nil
}
