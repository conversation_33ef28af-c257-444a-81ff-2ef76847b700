package port_group

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/port_group"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	pb "micro-service/webService/proto"
)

// 初始化Mock服务
func Init() {
	cfg.InitLoadCfg()
	log.Init()
	// 设置是否使用Mock (默认true)
	mysql.SetTestEnv(true)
	// 初始化数据库
	mysql.GetInstance(cfg.LoadMysql())
}

func TestPortIndex(t *testing.T) {
	Init()

	t.Run("normal case with pagination", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE user_id = \\?").
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))

		mock.ExpectQuery("SELECT \\* FROM `ports` WHERE user_id = \\? ORDER BY id desc LIMIT \\?").
			WithArgs(1, sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at"}).
				AddRow(1, 1, 1, 80, 0, 1, time.Now(), time.Now()).
				AddRow(2, 1, 1, 443, 0, 1, time.Now(), time.Now()))

		mock.ExpectQuery("SELECT \\* FROM `port_port_group`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}).
				AddRow(1, 1, 1, time.Now(), time.Now()).
				AddRow(2, 2, 1, time.Now(), time.Now()))

		mock.ExpectQuery("SELECT \\* FROM `port_groups`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "name", "can_del", "created_at", "updated_at"}).
				AddRow(1, 1, 1, "Web服务", 1, time.Now(), time.Now()))

		mock.ExpectQuery("SELECT \\* FROM `port_port_protocol`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_protocol_id", "created_at", "updated_at"}).
				AddRow(1, 1, 1, time.Now(), time.Now()).
				AddRow(2, 2, 2, time.Now(), time.Now()))

		mock.ExpectQuery("SELECT \\* FROM `port_protocols`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "protocol", "type", "created_at", "updated_at"}).
				AddRow(1, "http", 0, time.Now(), time.Now()).
				AddRow(2, "https", 0, time.Now(), time.Now()))

		req := &pb.PortIndexRequest{
			UserId: 1,
			Page:   1,
			Size:   10,
		}
		resp := &pb.PortIndexResponse{}

		err := PortIndex(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), resp.Total)
		assert.Equal(t, int64(1), resp.Page)
		assert.Equal(t, int64(10), resp.Size)
		assert.Len(t, resp.Items, 2)
		assert.Equal(t, uint64(80), resp.Items[0].Port)
		assert.Equal(t, "80", resp.Items[0].Title)
		assert.Equal(t, port_group.PortStatusMap[0], resp.Items[0].Status)
	})

	t.Run("with keyword filter", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE user_id = \\? AND port LIKE \\?").
			WithArgs(1, "%80%").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		mock.ExpectQuery("SELECT \\* FROM `ports` WHERE user_id = \\? AND port LIKE \\? ORDER BY id desc LIMIT \\?").
			WithArgs(1, "%80%", sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at"}).
				AddRow(1, 1, 1, 80, 0, 1, time.Now(), time.Now()))

		mock.ExpectQuery("SELECT \\* FROM `port_port_group`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}))

		mock.ExpectQuery("SELECT \\* FROM `port_port_protocol`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_protocol_id", "created_at", "updated_at"}))

		req := &pb.PortIndexRequest{
			UserId:  1,
			Keyword: "80",
			Page:    1,
			Size:    10,
		}
		resp := &pb.PortIndexResponse{}

		err := PortIndex(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), resp.Total)
	})

	t.Run("page less than 1 defaults to 1", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE user_id = \\?").
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		mock.ExpectQuery("SELECT \\* FROM `ports` WHERE user_id = \\? ORDER BY id desc LIMIT \\?").
			WithArgs(1, sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at"}))

		req := &pb.PortIndexRequest{
			UserId: 1,
			Page:   0,
			Size:   10,
		}
		resp := &pb.PortIndexResponse{}

		err := PortIndex(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), resp.Page)
	})

	t.Run("database error on count", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE user_id = \\?").
			WithArgs(1).
			WillReturnError(errors.New("database error"))

		req := &pb.PortIndexRequest{
			UserId: 1,
			Page:   1,
			Size:   10,
		}
		resp := &pb.PortIndexResponse{}

		err := PortIndex(context.Background(), req, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database error")
	})

	t.Run("database error on data query", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE user_id = \\?").
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		mock.ExpectQuery("SELECT \\* FROM `ports` WHERE user_id = \\? ORDER BY id desc LIMIT \\?").
			WithArgs(1, sqlmock.AnyArg()).
			WillReturnError(errors.New("query error"))

		req := &pb.PortIndexRequest{
			UserId: 1,
			Page:   1,
			Size:   10,
		}
		resp := &pb.PortIndexResponse{}

		err := PortIndex(context.Background(), req, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "query error")
	})

	t.Run("updated_at zero time handling", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT count\\(\\*\\) FROM `ports` WHERE user_id = \\?").
			WithArgs(1).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		zeroTime := time.Time{}
		mock.ExpectQuery("SELECT \\* FROM `ports` WHERE user_id = \\? ORDER BY id desc LIMIT \\?").
			WithArgs(1, sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at"}).
				AddRow(1, 1, 1, 80, 0, 1, time.Now(), zeroTime))

		mock.ExpectQuery("SELECT \\* FROM `port_port_group`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}))

		mock.ExpectQuery("SELECT \\* FROM `port_port_protocol`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_protocol_id", "created_at", "updated_at"}))

		req := &pb.PortIndexRequest{
			UserId: 1,
			Page:   1,
			Size:   10,
		}
		resp := &pb.PortIndexResponse{}

		err := PortIndex(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, "-", resp.Items[0].UpdatedAt)
	})
}

func TestPortList(t *testing.T) {
	Init()

	t.Run("normal case", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT id,port FROM `ports` WHERE user_id = \\? ORDER BY updated_at desc").
			WithArgs(uint64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"id", "port"}).
				AddRow(1, 80).
				AddRow(2, 443))

		req := &pb.PortListRequest{
			UserId: 1,
		}
		resp := &pb.PortListResponse{}

		err := PortList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Len(t, resp.Items, 2)
		assert.Equal(t, uint64(1), resp.Items[0].Id)
		assert.Equal(t, uint64(80), resp.Items[0].Port)
		assert.Equal(t, uint64(2), resp.Items[1].Id)
		assert.Equal(t, uint64(443), resp.Items[1].Port)
	})

	t.Run("user_id is 0", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT id,port FROM `ports` ORDER BY updated_at desc").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port"}).
				AddRow(1, 80))

		req := &pb.PortListRequest{
			UserId: 0,
		}
		resp := &pb.PortListResponse{}

		err := PortList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Len(t, resp.Items, 1)
	})

	t.Run("database error", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT id,port FROM `ports` WHERE user_id = \\? ORDER BY updated_at desc").
			WithArgs(uint64(1)).
			WillReturnError(errors.New("database error"))

		req := &pb.PortListRequest{
			UserId: 1,
		}
		resp := &pb.PortListResponse{}

		err := PortList(context.Background(), req, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database error")
	})

	t.Run("empty result", func(t *testing.T) {
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT id,port FROM `ports` WHERE user_id = \\? ORDER BY updated_at desc").
			WithArgs(uint64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"id", "port"}))

		req := &pb.PortListRequest{
			UserId: 1,
		}
		resp := &pb.PortListResponse{}

		err := PortList(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Len(t, resp.Items, 0)
	})
}

func TestPortAdd(t *testing.T) {
	t.Run("normal case", func(t *testing.T) {
		Init()
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `ports` WHERE user_id = \\? AND port = \\? ORDER BY `ports`.`id` LIMIT \\?").
			WithArgs(1, uint64(8080), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at"}))

		mock.ExpectQuery("SELECT \\* FROM `port_port_protocol` WHERE `port_port_protocol`.`port_protocol_id` = \\? ORDER BY `port_port_protocol`.`id` LIMIT \\?").
			WithArgs(uint64(1), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_protocol_id", "created_at", "updated_at"}))

		mock.ExpectQuery("SELECT \\* FROM `port_port_group` WHERE `port_port_group`.`port_group_id` = \\? ORDER BY `port_port_group`.`id` LIMIT \\?").
			WithArgs(uint64(2), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}))

		mock.ExpectQuery("SELECT \\* FROM `port_port_group` WHERE `port_port_group`.`port_group_id` = \\? ORDER BY `port_port_group`.`id` LIMIT \\?").
			WithArgs(uint64(port_group.DefaultGroupId), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}))

		req := &pb.PortAddRequest{
			UserId:           1,
			OperateCompanyId: 1,
			Ports:            []uint64{8080},
			Protocols:        []uint64{1},
			PortGroups:       []uint64{2},
		}
		resp := &pb.PortAddResponse{}

		err := PortAdd(context.Background(), req, resp)
		assert.NoError(t, err)
	})
}

func TestPortEdit(t *testing.T) {
	t.Run("basic functionality test", func(t *testing.T) {

		req := &pb.PortEditRequest{
			Id:               1,
			UserId:           1,
			OperateCompanyId: 1,
			Ports:            []uint64{8080},
			Protocols:        []uint64{2},
			PortGroups:       []uint64{3},
		}
		resp := &pb.PortEditResponse{}

		_ = PortEdit(context.Background(), req, resp)
		assert.NotNil(t, req)
		assert.NotNil(t, resp)
	})
}

func TestPortDel(t *testing.T) {
	t.Run("basic functionality test", func(t *testing.T) {
		Init()
		req := &pb.PortDelRequest{
			UserId: 1,
			Ids:    []uint64{1},
		}
		resp := &pb.PortDelResponse{}

		_ = PortDel(context.Background(), req, resp)
		assert.NotNil(t, req)
		assert.NotNil(t, resp)
	})
}

func TestPortUpdateStatus(t *testing.T) {
	t.Run("basic functionality test", func(t *testing.T) {
		Init()
		req := &pb.PortUpdateStatusRequest{
			UserId: 1,
			Ids:    []uint64{1},
			Status: 1,
		}
		resp := &pb.PortUpdateStatusResponse{}

		_ = PortUpdateStatus(context.Background(), req, resp)
		assert.NotNil(t, req)
		assert.NotNil(t, resp)
	})
}

func TestPortProtocolIndex(t *testing.T) {
	t.Run("normal case", func(t *testing.T) {
		Init()
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `port_protocols` ORDER BY updated_at desc").
			WillReturnRows(sqlmock.NewRows([]string{"id", "protocol", "type", "created_at", "updated_at"}).
				AddRow(1, "http", 0, time.Now(), time.Now()).
				AddRow(2, "https", 0, time.Now(), time.Now()))

		req := &pb.PortProtocolIndexRequest{}
		resp := &pb.PortProtocolIndexResponse{}

		err := PortProtocolIndex(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Len(t, resp.Items, 2)
		assert.Equal(t, uint64(1), resp.Items[0].Id)
		assert.Equal(t, "http", resp.Items[0].Protocol)
		assert.Equal(t, uint32(0), resp.Items[0].Type)
	})

	t.Run("with keyword filter", func(t *testing.T) {
		Init()
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `port_protocols` WHERE protocol LIKE \\? ORDER BY updated_at desc").
			WithArgs("%http%").
			WillReturnRows(sqlmock.NewRows([]string{"id", "protocol", "type", "created_at", "updated_at"}).
				AddRow(1, "http", 0, time.Now(), time.Now()))

		req := &pb.PortProtocolIndexRequest{
			Keyword: "http",
		}
		resp := &pb.PortProtocolIndexResponse{}

		err := PortProtocolIndex(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Len(t, resp.Items, 1)
		assert.Equal(t, "http", resp.Items[0].Protocol)
	})

	t.Run("database error", func(t *testing.T) {
		Init()
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `port_protocols` ORDER BY updated_at desc").
			WillReturnError(errors.New("database error"))

		req := &pb.PortProtocolIndexRequest{}
		resp := &pb.PortProtocolIndexResponse{}

		err := PortProtocolIndex(context.Background(), req, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database error")
	})

	t.Run("empty result", func(t *testing.T) {
		Init()
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `port_protocols` ORDER BY updated_at desc").
			WillReturnRows(sqlmock.NewRows([]string{"id", "protocol", "type", "created_at", "updated_at"}))

		req := &pb.PortProtocolIndexRequest{}
		resp := &pb.PortProtocolIndexResponse{}

		err := PortProtocolIndex(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Len(t, resp.Items, 0)
	})
}

func TestPortDetail(t *testing.T) {
	t.Run("normal case", func(t *testing.T) {
		Init()
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `ports` WHERE user_id = \\? and id = \\? ORDER BY id desc").
			WithArgs(uint64(1), uint64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at"}).
				AddRow(1, 1, 1, 80, 0, 1, time.Now(), time.Now()))

		mock.ExpectQuery("SELECT \\* FROM `port_port_group`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_group_id", "created_at", "updated_at"}).
				AddRow(1, 1, 1, time.Now(), time.Now()).
				AddRow(2, 1, 2, time.Now(), time.Now()))

		mock.ExpectQuery("SELECT \\* FROM `port_groups`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "name", "can_del", "created_at", "updated_at"}).
				AddRow(1, 1, 1, "Web服务", 1, time.Now(), time.Now()).
				AddRow(2, 1, 1, "常用端口", 1, time.Now(), time.Now()))

		mock.ExpectQuery("SELECT \\* FROM `port_port_protocol`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "port_id", "port_protocol_id", "created_at", "updated_at"}).
				AddRow(1, 1, 1, time.Now(), time.Now()).
				AddRow(2, 1, 2, time.Now(), time.Now()))

		mock.ExpectQuery("SELECT \\* FROM `port_protocols`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "protocol", "type", "created_at", "updated_at"}).
				AddRow(1, "http", 0, time.Now(), time.Now()).
				AddRow(2, "https", 0, time.Now(), time.Now()))

		req := &pb.PortDetailRequest{
			Id:     1,
			UserId: 1,
		}
		resp := &pb.PortDetailResponse{}

		err := PortDetail(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, uint64(1), resp.Id)
		assert.Equal(t, uint64(80), resp.Port)
		assert.Len(t, resp.Groups, 2)
		assert.Contains(t, resp.Groups, uint64(1))
		assert.Contains(t, resp.Groups, uint64(2))
		assert.Len(t, resp.Protocols, 2)
		assert.Equal(t, uint64(1), resp.Protocols[0].Id)
		assert.Equal(t, "http", resp.Protocols[0].Protocol)
		assert.Equal(t, uint64(1), resp.Protocols[0].Pivot.PortId)
		assert.Equal(t, uint64(1), resp.Protocols[0].Pivot.PortProtocolId)
	})

	t.Run("database error", func(t *testing.T) {
		Init()
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `ports` WHERE user_id = \\? and id = \\? ORDER BY id desc").
			WithArgs(uint64(1), uint64(1)).
			WillReturnError(errors.New("database error"))

		req := &pb.PortDetailRequest{
			Id:     1,
			UserId: 1,
		}
		resp := &pb.PortDetailResponse{}

		err := PortDetail(context.Background(), req, resp)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database error")
	})

	t.Run("port not found", func(t *testing.T) {
		Init()
		mock := mysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `ports` WHERE user_id = \\? and id = \\? ORDER BY id desc").
			WithArgs(uint64(1), uint64(999)).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "port", "status", "source", "created_at", "updated_at"}))

		req := &pb.PortDetailRequest{
			Id:     999,
			UserId: 1,
		}
		resp := &pb.PortDetailResponse{}

		err := PortDetail(context.Background(), req, resp)
		assert.NoError(t, err)
		assert.Equal(t, uint64(0), resp.Id)
		assert.Equal(t, uint64(0), resp.Port)
		assert.Len(t, resp.Groups, 0)
		assert.Len(t, resp.Protocols, 0)
	})
}
