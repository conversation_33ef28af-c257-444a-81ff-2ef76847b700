package scan_crontab

import (
	"context"
	"fmt"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/cron_task_pocs"
	"micro-service/middleware/mysql/forbid"
	"micro-service/middleware/mysql/operate_logs"
	"micro-service/middleware/mysql/port_group"
	"micro-service/middleware/mysql/scan_crontab_tasks"
	"micro-service/middleware/mysql/scan_crontab_tasks_ips"
	"micro-service/middleware/mysql/scan_crontab_tasks_ports"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/log"
	"micro-service/pkg/metadata"
	"micro-service/pkg/network"
	asyncq "micro-service/pkg/queue_helper"
	pb "micro-service/webService/proto"
	"strconv"
	"strings"
	"time"

	"github.com/olivere/elastic"
	"go-micro.dev/v4/errors"
	"gorm.io/gorm"
)

// removeDuplicateIPs 去除IP数组中的重复项
func removeDuplicateIPs(ips []string) []string {
	if len(ips) == 0 {
		return ips
	}

	// 使用map来记录已经出现的IP
	seen := make(map[string]bool)
	result := make([]string, 0, len(ips))

	for _, ip := range ips {
		// 去除空白字符
		ip = strings.TrimSpace(ip)
		if ip == "" {
			continue
		}

		// 如果IP没有出现过，则添加到结果中
		if !seen[ip] {
			seen[ip] = true
			result = append(result, ip)
		}
	}

	return result
}

// CrontabTaskList 周期任务列表
func CrontabTaskList(ctx context.Context, req *pb.CrontabTaskListRequest, rsp *pb.CrontabTaskListResponse) error {
	log.WithContextInfof(ctx, "Received scan_crontab.CrontabTaskList request: %v", req)

	// 构建查询参数
	params := make(map[string]interface{})
	params["user_id"] = req.UserId

	if req.TaskType != nil {
		params["task_type"] = *req.TaskType
	}

	if req.OpId != nil {
		params["op_id"] = *req.OpId
	}

	if req.Name != nil && *req.Name != "" {
		params["name"] = *req.Name
	}

	if len(req.CreatedAt) > 0 {
		params["created_at_range"] = req.CreatedAt
	}

	if req.SortField != "" && req.SortOrder != "" {
		params["sort_field"] = req.SortField
		params["sort_order"] = req.SortOrder
	}

	// 实例化模型
	model := scan_crontab_tasks.NewModel()

	// 分页查询
	page := int(req.Page)
	perPage := int(req.PerPage)

	// 创建查询条件函数
	queryFunc := mysql.HandleFunc(func(db *gorm.DB) {
		// 应用查询条件
		if req.UserId > 0 {
			db.Where("user_id = ?", req.UserId)
		}

		if req.TaskType != nil {
			db.Where("task_type = ?", *req.TaskType)
		}

		if req.OpId != nil {
			db.Where("op_id = ?", *req.OpId)
		}

		if req.Name != nil && *req.Name != "" {
			db.Where("name LIKE ?", "%"+*req.Name+"%")
		}

		if len(req.CreatedAt) == 2 {
			db.Where("created_at BETWEEN ? AND ?", req.CreatedAt[0], req.CreatedAt[1])
		}

		if req.SortField != "" && req.SortOrder != "" {
			db.Order(req.SortField + " " + req.SortOrder)
		} else {
			db.Order("updated_at DESC")
		}
	})

	// 执行查询
	tasks, total, err := model.Page(page, perPage, queryFunc)

	if err != nil {
		log.WithContextErrorf(ctx, "查询周期任务列表失败: %v", err)
		return err
	}

	// 设置响应
	rsp.Total = int64(total)
	rsp.CurrentPage = req.Page
	rsp.PerPage = req.PerPage

	// 计算最后一页、起始记录和结束记录
	lastPage := int32(0)
	if rsp.PerPage > 0 {
		lastPage = int32((total + int64(rsp.PerPage) - 1) / int64(rsp.PerPage))
	}
	rsp.LastPage = lastPage

	from := int32(0)
	if page > 0 && perPage > 0 {
		from = (req.Page-1)*req.PerPage + 1
	}
	rsp.From = from

	to := from + int32(len(tasks)) - 1
	if to < from {
		to = from
	}
	rsp.To = to

	rsp.Items = make([]*pb.CrontabTaskItem, 0, len(tasks))

	// 获取用户信息
	userModel := user.NewUserModel()
	userMap := make(map[uint64]user.User)

	// 收集所有需要查询的用户ID
	userIDs := make([]uint, 0)
	opIDs := make([]uint, 0)

	for _, task := range tasks {
		// 添加用户ID
		userIDs = append(userIDs, uint(task.UserId))

		// 添加操作者ID
		if task.OpId > 0 {
			opIDs = append(opIDs, uint(task.OpId))
		}
	}

	// 查询所有用户信息
	allUserMap, err := userModel.FindByIds(append(userIDs, opIDs...))
	if err != nil {
		log.WithContextErrorf(ctx, "查询用户信息失败: %v", err)
	} else {
		userMap = allUserMap
	}

	// 转换数据
	for _, task := range tasks {
		item := &pb.CrontabTaskItem{
			Id:                  task.ID,
			UserId:              task.UserId,
			CompanyId:           task.CompanyId,
			Name:                task.Name,
			Bandwidth:           task.Bandwidth,
			ProtocolConcurrency: task.ProtocolConcurrency,
			TaskType:            int32(task.TaskType),
			IpType:              int32(task.IpType),
			ScanRange:           int32(task.ScanRange),
			PingSwitch:          int32(task.PingSwitch),
			WebLogoSwitch:       int32(task.WebLogoSwitch),
			Type:                int32(task.Type),
			ScheduleTime:        task.ScheduleTime,
			DayOfX:              task.DayOfX,
			ScanType:            int32(task.ScanType),
			Switch:              int32(task.Switch),
			CreatedAt:           task.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:           task.UpdatedAt.Format("2006-01-02 15:04:05"),
			FileName:            task.FileName,
			TaskFrom:            int32(task.TaskFrom),
			OpId:                task.OpId,
			IsAudit:             int32(task.IsAudit),
			Reason:              task.Reason,
			IsDefinePort:        int32(task.IsDefinePort),
			ScanEngine:          task.ScanEngine,
			EngineRole:          int32(task.EngineRole),
			TableAssetsType:     int32(task.TableAssetsType),
		}

		// 添加用户信息
		if userInfo, ok := userMap[task.UserId]; ok {
			userName := ""
			if userInfo.Name != nil && userInfo.Name.Valid {
				userName = userInfo.Name.String
			}

			item.User = &pb.UserInfo{
				Id:   int64(userInfo.Id),
				Name: userName,
			}
		}

		// 添加操作者信息
		if task.OpId > 0 {
			if opInfo, ok := userMap[uint64(task.OpId)]; ok {
				opName := ""
				if opInfo.Name != nil && opInfo.Name.Valid {
					opName = opInfo.Name.String
				}

				item.Op = &pb.UserInfo{
					Id:   int64(opInfo.Id),
					Name: opName,
				}
			}
		}

		rsp.Items = append(rsp.Items, item)
	}

	return nil
}

// CrontabTaskDelete 删除周期任务
func CrontabTaskDelete(ctx context.Context, req *pb.CrontabTaskDeleteRequest, rsp *pb.CrontabTaskDeleteResponse) error {
	log.WithContextInfof(ctx, "Received scan_crontab.CrontabTaskDelete request: %v", req)

	// 删除周期任务
	err := scan_crontab_tasks.NewModel().Delete(
		scan_crontab_tasks.WithUserID(uint64(req.UserId)),
		scan_crontab_tasks.WithIDs(req.Id),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 删除周期任务失败: %v", err)
		return err
	}

	// 删除周期任务相关的IP
	err = mysql.GetDbClient().Table("cron_task_ips").
		Where("cron_task_id IN ?", req.Id).
		Delete(nil).Error
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 删除周期任务IP失败: %v", err)
		return err
	}

	// 删除周期任务相关的端口
	err = mysql.GetDbClient().Table("cron_task_ports").
		Where("cron_task_id IN ?", req.Id).
		Delete(nil).Error
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 删除周期任务端口失败: %v", err)
		return err
	}

	// 删除周期任务相关的POC
	err = mysql.GetDbClient().Table("cron_task_pocs").
		Where("cron_task_id IN ?", req.Id).
		Delete(nil).Error
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 删除周期任务POC失败: %v", err)
		return err
	}

	rsp.Success = true
	return nil
}

// CrontabTaskSwitch 周期任务开关设置
func CrontabTaskSwitch(ctx context.Context, req *pb.CrontabTaskSwitchRequest, rsp *pb.CrontabTaskSwitchResponse) error {
	log.WithContextInfof(ctx, "Received scan_crontab.CrontabTaskSwitch request: %v", req)

	// 查询任务是否存在
	_, err := scan_crontab_tasks.NewModel().First(
		scan_crontab_tasks.WithID(req.Id),
		scan_crontab_tasks.WithUserID(uint64(req.UserId)),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 查询周期任务失败: %v", err)
		return err
	}

	// 使用DB原生方法只更新Switch字段
	err = mysql.GetDbClient().Table("scan_crontab_tasks").
		Where("id = ? AND user_id = ?", req.Id, req.UserId).
		UpdateColumn("switch", int(req.Switch)).Error
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 设置周期任务开关失败: %v", err)
		return err
	}

	rsp.Success = true
	return nil
}

// CrontabTaskDetail 获取周期任务详情
func CrontabTaskDetail(ctx context.Context, req *pb.CrontabTaskDetailRequest, rsp *pb.CrontabTaskDetailResponse) error {
	log.WithContextInfof(ctx, "Received scan_crontab.CrontabTaskDetail request: %v", req)

	// 查询任务详情
	task, err := scan_crontab_tasks.NewModel().First(
		scan_crontab_tasks.WithID(req.Id),
		scan_crontab_tasks.WithUserID(uint64(req.UserId)),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 查询周期任务详情失败: %v", err)
		return err
	}

	// 查询操作者信息
	opUser, err := user.NewUserModel().FindById(task.OpId)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.WithContextErrorf(ctx, "[周期任务] 查询操作者信息失败: %v", err)
		return err
	}

	// 查询任务的IP列表
	var ips []*pb.CrontabTaskIpItem
	err = mysql.GetDbClient().Table("cron_task_ips").
		Where("cron_task_id = ?", task.ID).
		Find(&ips).Error
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 查询任务IP列表失败: %v", err)
		return err
	}

	// 处理IP列表的时间格式
	for _, ip := range ips {
		if ip.CreatedAt != "" {
			// 解析时间并重新格式化，去掉时区信息
			t, err := time.Parse(time.RFC3339, ip.CreatedAt)
			if err == nil {
				ip.CreatedAt = t.Format("2006-01-02 15:04:05")
			}
		}
		// 将空字符串设置为null（在proto中表示为空字符串）
		if ip.UpdatedAt == "" {
			ip.UpdatedAt = "null"
		}
	}

	// 查询任务的端口列表
	var ports []*pb.CrontabTaskPortItem
	err = mysql.GetDbClient().Table("cron_task_ports").
		Where("cron_task_id = ?", task.ID).
		Find(&ports).Error
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 查询任务端口列表失败: %v", err)
		return err
	}

	// 处理端口列表的时间格式
	for _, port := range ports {
		if port.CreatedAt != "" {
			// 解析时间并重新格式化，去掉时区信息
			t, err := time.Parse(time.RFC3339, port.CreatedAt)
			if err == nil {
				port.CreatedAt = t.Format("2006-01-02 15:04:05")
			}
		}
		// 将空字符串设置为null（在proto中表示为空字符串）
		if port.UpdatedAt == "" {
			port.UpdatedAt = "null"
		}
	}

	// 查询端口组信息
	for _, port := range ports {
		if port.CronportsType == "App\\Models\\MySql\\PortGroup" {
			var portGroup pb.PortGroupItem
			err = mysql.GetDbClient().Table("port_groups").
				Where("id = ?", port.CronportsId).
				First(&portGroup).Error
			if err != nil && err != gorm.ErrRecordNotFound {
				log.WithContextErrorf(ctx, "[周期任务] 查询端口组信息失败: %v", err)
				return err
			}

			// 处理端口组时间格式
			if portGroup.CreatedAt != "" {
				t, err := time.Parse(time.RFC3339, portGroup.CreatedAt)
				if err == nil {
					portGroup.CreatedAt = t.Format("2006-01-02 15:04:05")
				}
			}
			if portGroup.UpdatedAt != "" {
				t, err := time.Parse(time.RFC3339, portGroup.UpdatedAt)
				if err == nil {
					portGroup.UpdatedAt = t.Format("2006-01-02 15:04:05")
				}
			}
			port.Cronports = &portGroup
		}
	}

	// 查询自定义端口
	definePorts, err := port_group.NewDefinePortModel().FindWithProtocolsByCronTaskId(task.ID)
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 查询自定义端口失败: %v", err)
		return err
	}

	// 提取端口和协议ID，使用map进行去重
	portMap := make(map[int32]bool)
	protocolMap := make(map[int32]bool)
	for _, dp := range definePorts {
		portMap[int32(dp.Port)] = true
		protocolMap[int32(dp.PortProtocolId)] = true
	}

	// 将去重后的数据转换为切片
	var portList []int32
	var protocolList []int32
	for port := range portMap {
		portList = append(portList, port)
	}
	for protocol := range protocolMap {
		protocolList = append(protocolList, protocol)
	}

	// 确保即使没有数据也返回空数组而不是null
	if portList == nil {
		portList = []int32{}
	}
	if protocolList == nil {
		protocolList = []int32{}
	}
	// 填充响应
	rsp.Id = task.ID
	rsp.UserId = task.UserId
	rsp.CompanyId = task.CompanyId
	rsp.Name = task.Name
	rsp.Bandwidth = task.Bandwidth
	rsp.PocScanType = int32(task.PocScanType)
	rsp.ProtocolConcurrency = task.ProtocolConcurrency
	rsp.TaskType = int32(task.TaskType)
	rsp.IpType = int32(task.IpType)
	rsp.ScanRange = int32(task.ScanRange)
	rsp.PingSwitch = int32(task.PingSwitch)
	rsp.WebLogoSwitch = int32(task.WebLogoSwitch)
	rsp.Type = int32(task.Type)
	rsp.ScheduleTime = task.ScheduleTime
	rsp.DayOfX = task.DayOfX
	rsp.ScanType = int32(task.ScanType)
	rsp.Switch = int32(task.Switch)
	rsp.CreatedAt = task.CreatedAt.Format("2006-01-02 15:04:05")
	rsp.UpdatedAt = task.UpdatedAt.Format("2006-01-02 15:04:05")
	if task.DeletedAt.Valid {
		rsp.DeletedAt = task.DeletedAt.Time.Format("2006-01-02 15:04:05")
	} else {
		rsp.DeletedAt = "null" // 与PHP保持一致，返回null而不是空字符串
	}
	rsp.FileName = task.FileName
	rsp.TaskFrom = int32(task.TaskFrom)
	rsp.OpId = task.OpId
	rsp.IsAudit = int32(task.IsAudit)
	rsp.Reason = task.Reason
	rsp.IsDefinePort = int32(task.IsDefinePort)
	rsp.ScanEngine = task.ScanEngine
	rsp.EngineRole = int32(task.EngineRole)
	rsp.TableAssetsType = int32(task.TableAssetsType)

	// 设置IP列表
	rsp.Ips = ips

	// 设置端口列表
	rsp.Ports = ports

	// 设置操作者信息
	if opUser != nil {
		rsp.Op = &pb.UserInfo{
			Id:   int64(opUser.Id),
			Name: opUser.Name.String,
		}
	}

	// 设置自定义端口和协议
	rsp.DefinePorts = portList
	rsp.DefinePortProtocols = protocolList

	return nil
}

// CrontabTaskCreate 创建周期任务
func CrontabTaskCreate(ctx context.Context, req *pb.CrontabTaskCreateRequest, rsp *pb.CrontabTaskCreateResponse) error {
	log.WithContextInfof(ctx, "Received scan_crontab.CrontabTaskCreate request: %v", req)

	// 获取操作目标ID
	userID := uint64(req.UserId)
	companyID := uint64(req.CompanyId)
	// 创建任务基本信息
	scanTask := &scan_crontab_tasks.ScanCrontabTask{
		UserId:              userID,
		CompanyId:           companyID,
		Name:                req.Name,
		Bandwidth:           req.Bandwidth,
		PocScanType:         int(req.PocScanType),
		ProtocolConcurrency: req.ProtocolConcurrency,
		TaskType:            int(req.TaskType),
		IpType:              int(req.IpType),
		ScanRange:           int(req.ScanRange),
		PingSwitch:          int(req.PingSwitch),
		WebLogoSwitch:       int(req.WebLogoSwitch),
		Type:                int(req.Type),
		ScheduleTime:        req.ScheduleTime,
		DayOfX:              req.DayOfX,
		ScanType:            int(req.ScanType),
		Switch:              0, // 默认周期任务开启
		FileName:            req.FileName,
		TaskFrom:            0, // 默认用户自己下发的扫描任务
		OpId:                int64(req.OperateCompanyId),
		IsDefinePort:        int(req.IsDefinePort),
		ScanEngine:          req.ScanEngine,
		TableAssetsType:     int(req.TableAssetsType),
		CreatedAt:           time.Now(),
	}

	// 如果是安服下发的任务
	if userID != uint64(req.UserId) {
		scanTask.TaskFrom = 1
	}

	// 验证IP格式
	var ips []string
	var errDomain []string
	var returnData []string

	// 处理不同的扫描范围
	switch req.ScanRange {
	case 0, 1, 2: // CronInputIP, CronFilterRangeIP, CronFileIP
		ips = req.Ips
		// 校验IP格式
		if req.TaskType == 1 { // CronTaskTypeAsset
			if req.IpType == 1 { // IPv4
				// 校验单个ip是否正确
				for _, checkIp := range ips {
					if strings.Contains(checkIp, "-") || strings.Contains(checkIp, "/") {
						// 校验所有ipv4的格式是否正确
						if strings.Contains(checkIp, "/") {
							// **********/24
							if !network.IsValidIPv4(strings.Split(checkIp, "/")[0]) {
								errDomain = append(errDomain, checkIp)
								continue
							}
							mask, err := strconv.Atoi(strings.Split(checkIp, "/")[1])
							if err != nil || mask > 32 || mask < 0 {
								errDomain = append(errDomain, checkIp)
								continue
							}
						} else if strings.Contains(checkIp, "*") {
							// 192.168.1-10.*（代表***********-************共10个网段）
							if !network.IsValidIPv4(strings.Split(checkIp, "-")[0] + ".1") {
								errDomain = append(errDomain, checkIp)
								continue
							}
							end, err := strconv.Atoi(strings.Split(strings.Split(checkIp, "-")[1], ".")[0])
							if err != nil || end > 255 || end < 0 {
								errDomain = append(errDomain, checkIp)
								continue
							}
						} else {
							if !network.IsValidIPv4(strings.Split(checkIp, "-")[0]) {
								errDomain = append(errDomain, checkIp)
								continue
							}
							end, err := strconv.Atoi(strings.Split(checkIp, "-")[1])
							if err != nil || end > 255 || end < 0 || len(strings.Split(strings.Split(checkIp, "-")[1], ".")) > 1 {
								errDomain = append(errDomain, checkIp)
								continue
							}
						}
					} else {
						if !network.IsValidIPv4(checkIp) {
							errDomain = append(errDomain, checkIp)
							continue
						}
					}
					returnData = append(returnData, checkIp)
				}
			} else {
				// 校验单个ip是否正确 IPv6
				for _, checkIp := range ips {
					if strings.Contains(checkIp, "-") || strings.Contains(checkIp, "/") {
						errDomain = append(errDomain, checkIp)
						continue
					} else {
						if !network.IsValidIPv6(checkIp) {
							errDomain = append(errDomain, checkIp)
							continue
						}
						returnData = append(returnData, checkIp)
					}
				}
			}
		}
		// 使用验证后的IP数据，并进行去重
		ips = removeDuplicateIPs(returnData)

	case 3: // CronSureIP
		// 认领资产的ip
		var isIpv6 bool
		if req.IpType == 2 {
			isIpv6 = true
		}
		query := elastic.NewBoolQuery().
			Must(elastic.NewTermQuery("user_id", req.UserId)).
			Must(elastic.NewTermQuery("is_ipv6", isIpv6))

		// 添加状态过滤
		statusQuery := elastic.NewBoolQuery()
		statusQuery.Should(elastic.NewTermQuery("status", foradar_assets.StatusConfirmAsset))
		query.Must(statusQuery)

		// 查询资产
		assets, err := foradar_assets.NewForadarAssetModel().ListAll(ctx, query)
		if err != nil {
			log.WithContextErrorf(ctx, "[周期任务] 查询认领资产失败: %v", err)
			return err
		}
		if len(assets) == 0 {
			return fmt.Errorf("扫描目标ip为空，请重新下发扫描任务")
		}
		for _, asset := range assets {
			ips = append(ips, asset.Ip)
		}
		// 对从资产中获取的IP进行去重
		ips = removeDuplicateIPs(ips)

	case 4: // CronTableIP
		// 台账ip
		var isIpv6 bool
		if req.IpType == 2 {
			isIpv6 = true
		}
		query := elastic.NewBoolQuery().
			Must(elastic.NewTermQuery("user_id", req.UserId)).
			Must(elastic.NewTermQuery("is_ipv6", isIpv6))

		// 添加状态过滤
		statusQuery := elastic.NewBoolQuery()
		statusQuery.Should(elastic.NewTermQuery("status", foradar_assets.StatusConfirmAsset))
		statusQuery.Should(elastic.NewTermQuery("status", foradar_assets.StatusUploadAsset))
		query.Must(statusQuery)

		// 查询资产
		assets, err := foradar_assets.NewForadarAssetModel().ListAll(ctx, query)
		if err != nil {
			log.WithContextErrorf(ctx, "[周期任务] 查询台账资产失败: %v", err)
			return err
		}
		if len(assets) == 0 {
			return fmt.Errorf("扫描目标ip为空，请重新下发扫描任务")
		}
		for _, asset := range assets {
			ips = append(ips, asset.Ip)
		}
		// 对从资产中获取的IP进行去重
		ips = removeDuplicateIPs(ips)

	}

	// 判断当前ip是否为禁扫ip
	forbidIps, err := forbid.NewForbidIpsModel().FindByUserID(userID)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.WithContextErrorf(ctx, "[周期任务] 查询禁扫IP失败: %v", err)
		return err
	}

	var canNotScanIps []string
	if len(forbidIps) > 0 {
		spliteIpArr := network.SplitIP(ips, req.IpType == 2)
		var taskTargetIp []string

		for _, showip := range spliteIpArr {
			ipRange, err := network.ParseIPRange(showip)
			if err != nil {
				log.WithContextErrorf(ctx, "[周期任务] 解析IP范围失败: %v", err)
				continue
			}
			for _, ip := range ipRange {
				taskTargetIp = append(taskTargetIp, ip)
			}
		}

		for _, ip := range taskTargetIp {
			for _, forbidIp := range forbidIps {
				if forbidIp.IpSegment == ip || forbidIp.IpSegment == ip+"/32" {
					canNotScanIps = append(canNotScanIps, ip)
					break
				}
			}
		}
	}

	if len(canNotScanIps) > 0 {
		return fmt.Errorf("不能下发扫描任务，以下ip为禁扫ip: %s", strings.Join(canNotScanIps, ","))
	}

	// 创建任务
	err = mysql.GetDbClient().Transaction(func(tx *gorm.DB) error {
		// 创建任务
		result := tx.Table(scan_crontab_tasks.TableName).Create(scanTask)
		if result.Error != nil {
			log.WithContextErrorf(ctx, "[周期任务] 创建周期任务失败: %v", result.Error)
			return result.Error
		}

		taskID := scanTask.ID

		// 插入IP（无论是资产扫描还是POC扫描都需要插入IP）
		if len(ips) > 0 {
			var ipArr []scan_crontab_tasks_ips.ScanCrontabTasksIp
			for _, ip := range ips {
				ipArr = append(ipArr, scan_crontab_tasks_ips.ScanCrontabTasksIp{
					CronTaskId: taskID,
					Ip:         ip,
					CreatedAt:  time.Now(),
				})
			}

			if len(ipArr) > 0 {
				// 使用原生SQL批量插入，避免MySQL错误1295
				sqlStr := fmt.Sprintf("INSERT INTO %s (cron_task_id, ip, created_at) VALUES ", scan_crontab_tasks_ips.TableName)
				vals := []interface{}{}

				// 构建SQL语句和参数
				for i, ip := range ipArr {
					if i > 0 {
						sqlStr += ","
					}
					sqlStr += "(?, ?, ?)"
					vals = append(vals, ip.CronTaskId, ip.Ip, ip.CreatedAt)
				}

				// 执行原生SQL
				result = tx.Exec(sqlStr, vals...)
				if result.Error != nil {
					log.WithContextErrorf(ctx, "[周期任务] 创建周期任务IP失败: %v", result.Error)
					return result.Error
				}
			}
		}

		if req.TaskType == 1 { // CronTaskTypeAsset - 资产扫描
			// 处理端口
			if req.IsDefinePort == 1 {
				// 自定义端口
				var definePorts []port_group.DefinePort
				for _, port := range req.DefinePorts {
					for _, protocol := range req.DefinePortProtocols {
						definePorts = append(definePorts, port_group.DefinePort{
							CronTaskId:     taskID,
							UserId:         userID,
							Port:           int(port),
							PortProtocolId: uint64(protocol),
						})
					}
				}

				if len(definePorts) > 0 {
					// 使用原生SQL批量插入，避免MySQL错误1295
					sqlStr := "INSERT INTO define_ports (cron_task_id, user_id, port, port_protocol_id) VALUES "
					vals := []interface{}{}

					// 构建SQL语句和参数
					for i, port := range definePorts {
						if i > 0 {
							sqlStr += ","
						}
						sqlStr += "(?, ?, ?, ?)"
						vals = append(vals, port.CronTaskId, port.UserId, port.Port, port.PortProtocolId)
					}

					// 执行原生SQL
					result = tx.Exec(sqlStr, vals...)
					if result.Error != nil {
						log.WithContextErrorf(ctx, "[周期任务] 创建自定义端口失败: %v", result.Error)
						return result.Error
					}
				}
			} else {
				// 使用端口组
				if req.PortGroupIds > 0 {
					err = tx.Exec(fmt.Sprintf("INSERT INTO %s (cron_task_id, cronports_type, cronports_id, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())",
						scan_crontab_tasks_ports.TableName),
						taskID, "App\\Models\\MySql\\PortGroup", req.PortGroupIds).Error
					if err != nil {
						log.WithContextErrorf(ctx, "[周期任务] 创建周期任务端口失败: %v", err)
						return err
					}
				}
			}
		} else { // TaskType == 2 - POC扫描
			// POC扫描
			pocIds := make([]int64, 0)
			portGroupIds := make([]int64, 0)

			// 根据扫描类型准备数据
			if req.PocScanType == cron_task_pocs.POC_SCAN_TYPE_RANGE && len(req.PocIds) > 0 {
				// 指定POC扫描
				for _, id := range req.PocIds {
					pocIds = append(pocIds, int64(id))
				}
			} else if req.PocScanType == cron_task_pocs.POC_SCAN_TYPE_GROUP && req.PortGroupIds > 0 {
				// POC分组扫描
				portGroupIds = append(portGroupIds, int64(req.PortGroupIds))
			}

			// 创建POC数据
			pocData, flag := cron_task_pocs.CreatePocData(taskID, int(req.PocScanType), pocIds, portGroupIds)

			// 只有在有数据需要插入时才执行插入操作
			if flag && len(pocData) > 0 {
				if err := tx.Table("cron_task_pocs").Create(&pocData).Error; err != nil {
					log.WithContextErrorf(ctx, "[周期任务] 插入POC数据失败: %v", err)
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 创建周期任务事务失败: %v", err)
		return err
	}

	// 记录操作日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(req.UserId),
		"下发资产扫描周期任务",
		metadata.MustString(ctx, "client_ip"),
		uint64(req.CompanyId),
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 记录操作日志失败: %v", err)
	}

	// 设置响应
	rsp.Success = true
	rsp.DataList = returnData
	rsp.ErrorList = errDomain

	return nil
}

// EditCrontabTask 编辑周期任务
func EditCrontabTask(ctx context.Context, req *pb.CrontabTaskEditRequest, rsp *pb.CrontabTaskEditResponse) error {
	log.WithContextInfof(ctx, "Received EditCrontabTask request: %v", req)

	// 1. 验证任务ID是否存在
	if req.Id == 0 {
		return errors.BadRequest(pb.ServiceName, "任务id不存在")
	}

	model := scan_crontab_tasks.NewModel()
	_, err := model.First(
		scan_crontab_tasks.WithID(req.Id),
		scan_crontab_tasks.WithUserID(uint64(req.UserId)),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "查询任务失败: %v", err)
		return errors.BadRequest(pb.ServiceName, "任务id不存在")
	}

	// 2. 获取操作目标ID
	userID := uint64(req.UserId)
	companyID := uint64(req.CompanyId)

	// 对应PHP: [$user_id, $companyId] = $this->user()->getOpTargetId($param['operate_company_id'] ?? null);
	if req.OperateCompanyId != 0 {
		// 安服扫描的标记 - 对应PHP: if ($user_id != $this->user()->id) { $scanTask['task_from'] = 1; }
		log.WithContextInfof(ctx, "安服扫描任务，operate_company_id: %d", req.OperateCompanyId)
	}

	// 3. 创建任务基本信息
	scanTask := &scan_crontab_tasks.ScanCrontabTask{
		UserId:              userID,
		CompanyId:           companyID,
		Name:                req.Name,
		Bandwidth:           req.Bandwidth,
		PocScanType:         int(req.PocScanType),
		ProtocolConcurrency: req.ProtocolConcurrency,
		TaskType:            int(req.TaskType),
		IpType:              int(req.IpType),
		ScanRange:           int(req.ScanRange),
		PingSwitch:          int(req.PingSwitch),
		WebLogoSwitch:       int(req.WebLogoSwitch),
		Type:                int(req.Type),
		ScheduleTime:        req.ScheduleTime,
		DayOfX:              req.DayOfX,
		ScanType:            int(req.ScanType),
		Switch:              0, // 默认周期任务开启
		FileName:            req.FileName,
		TaskFrom:            0, // 默认用户自己下发的扫描任务
		OpId:                int64(req.UserId),
		IsDefinePort:        int(req.IsDefinePort),
		ScanEngine:          req.ScanEngine,
		TableAssetsType:     int(req.TableAssetsType),
		CreatedAt:           time.Now(),
	}

	// 如果是安服下发的任务
	if userID != uint64(req.UserId) {
		scanTask.TaskFrom = 1
	}

	// 4. 处理调度时间相关参数 - 对应PHP的调度时间逻辑
	if req.Type > 2 { // 对应 CronTask::CRON_TYPE_SCHEDULE_INSTANCE
		scanTask.ScheduleTime = req.ScheduleTime
		if req.Type == 3 || req.Type == 4 || req.Type == 6 { // 月度、周度、单次
			scanTask.DayOfX = req.DayOfX
		}
	}

	// 5. 验证IP格式和处理不同的扫描范围
	var ips []string
	var errDomain []string
	var returnData []string

	// 处理不同的扫描范围
	switch req.ScanRange {
	case 0, 1, 2: // CronInputIP, CronFilterRangeIP, CronFileIP
		ips = req.Ips
		// 校验IP格式
		if req.TaskType == 1 { // CronTaskTypeAsset
			if req.IpType == 1 { // IPv4
				// 校验单个ip是否正确
				for _, checkIp := range ips {
					if strings.Contains(checkIp, "-") || strings.Contains(checkIp, "/") {
						// 校验所有ipv4的格式是否正确
						if strings.Contains(checkIp, "/") {
							// **********/24
							if !network.IsValidIPv4(strings.Split(checkIp, "/")[0]) {
								errDomain = append(errDomain, checkIp)
								continue
							}
							mask, err := strconv.Atoi(strings.Split(checkIp, "/")[1])
							if err != nil || mask > 32 || mask < 0 {
								errDomain = append(errDomain, checkIp)
								continue
							}
						} else if strings.Contains(checkIp, "*") {
							// 192.168.1-10.*（代表***********-************共10个网段）
							if !network.IsValidIPv4(strings.Split(checkIp, "-")[0] + ".1") {
								errDomain = append(errDomain, checkIp)
								continue
							}
							end, err := strconv.Atoi(strings.Split(strings.Split(checkIp, "-")[1], ".")[0])
							if err != nil || end > 255 || end < 0 {
								errDomain = append(errDomain, checkIp)
								continue
							}
						} else {
							if !network.IsValidIPv4(strings.Split(checkIp, "-")[0]) {
								errDomain = append(errDomain, checkIp)
								continue
							}
							end, err := strconv.Atoi(strings.Split(checkIp, "-")[1])
							if err != nil || end > 255 || end < 0 || len(strings.Split(strings.Split(checkIp, "-")[1], ".")) > 1 {
								errDomain = append(errDomain, checkIp)
								continue
							}
						}
					} else {
						if !network.IsValidIPv4(checkIp) {
							errDomain = append(errDomain, checkIp)
							continue
						}
					}
					returnData = append(returnData, checkIp)
				}
			} else {
				// 校验单个ip是否正确 IPv6
				for _, checkIp := range ips {
					if strings.Contains(checkIp, "-") || strings.Contains(checkIp, "/") {
						errDomain = append(errDomain, checkIp)
						continue
					} else {
						if !network.IsValidIPv6(checkIp) {
							errDomain = append(errDomain, checkIp)
							continue
						}
						returnData = append(returnData, checkIp)
					}
				}
			}
		}
		// 使用验证后的IP数据，并进行去重
		ips = removeDuplicateIPs(returnData)

	case 3: // CronSureIP - 认领资产的ip
		var isIpv6 bool
		if req.IpType == 2 {
			isIpv6 = true
		}
		query := elastic.NewBoolQuery().
			Must(elastic.NewTermQuery("user_id", req.UserId)).
			Must(elastic.NewTermQuery("is_ipv6", isIpv6))

		// 添加状态过滤
		statusQuery := elastic.NewBoolQuery()
		statusQuery.Should(elastic.NewTermQuery("status", foradar_assets.StatusConfirmAsset))
		query.Must(statusQuery)

		// 查询资产
		assets, err := foradar_assets.NewForadarAssetModel().ListAll(ctx, query)
		if err != nil {
			log.WithContextErrorf(ctx, "[周期任务] 查询认领资产失败: %v", err)
			return err
		}
		if len(assets) == 0 {
			return fmt.Errorf("扫描目标ip为空，请重新下发扫描任务")
		}
		for _, asset := range assets {
			ips = append(ips, asset.Ip)
		}
		// 对从资产中获取的IP进行去重
		ips = removeDuplicateIPs(ips)

	case 4: // CronTableIP - 台账ip
		var isIpv6 bool
		if req.IpType == 2 {
			isIpv6 = true
		}
		query := elastic.NewBoolQuery().
			Must(elastic.NewTermQuery("user_id", req.UserId)).
			Must(elastic.NewTermQuery("is_ipv6", isIpv6))

		// 添加状态过滤
		statusQuery := elastic.NewBoolQuery()
		statusQuery.Should(elastic.NewTermQuery("status", foradar_assets.StatusConfirmAsset))
		statusQuery.Should(elastic.NewTermQuery("status", foradar_assets.StatusUploadAsset))
		query.Must(statusQuery)

		// 查询资产
		assets, err := foradar_assets.NewForadarAssetModel().ListAll(ctx, query)
		if err != nil {
			log.WithContextErrorf(ctx, "[周期任务] 查询台账资产失败: %v", err)
			return err
		}
		if len(assets) == 0 {
			return fmt.Errorf("扫描目标ip为空，请重新下发扫描任务")
		}
		for _, asset := range assets {
			ips = append(ips, asset.Ip)
		}
		// 对从资产中获取的IP进行去重
		ips = removeDuplicateIPs(ips)
	}

	// 6. 判断端口是否超出数量限制
	if req.IsDefinePort == 1 {
		definedPortNum := 0
		ports := make(map[int32]bool)
		protocols := make(map[int32]bool)

		// 去重端口和协议
		for _, port := range req.DefinePorts {
			ports[port] = true
		}
		for _, protocol := range req.DefinePortProtocols {
			protocols[protocol] = true
		}

		// 计算组合数量
		for range ports {
			for range protocols {
				definedPortNum++
			}
		}

		// 自定义端口最多100个
		if definedPortNum > 100 {
			return errors.BadRequest(pb.ServiceName, "自定义端口扫描下，端口协议随机组合最多100个，现在已经超出数量限制，请重新设置")
		}
	}

	// 7. 判断当前ip是否为禁扫ip
	forbidIps, err := forbid.NewForbidIpsModel().FindByUserID(userID)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.WithContextErrorf(ctx, "[周期任务] 查询禁扫IP失败: %v", err)
		return err
	}

	var canNotScanIps []string
	if len(forbidIps) > 0 {
		spliteIpArr := network.SplitIP(ips, req.IpType == 2)
		var taskTargetIp []string

		for _, showip := range spliteIpArr {
			ipRange, err := network.ParseIPRange(showip)
			if err != nil {
				log.WithContextErrorf(ctx, "[周期任务] 解析IP范围失败: %v", err)
				continue
			}
			for _, ip := range ipRange {
				taskTargetIp = append(taskTargetIp, ip)
			}
		}

		for _, ip := range taskTargetIp {
			for _, forbidIp := range forbidIps {
				if forbidIp.IpSegment == ip || forbidIp.IpSegment == ip+"/32" {
					canNotScanIps = append(canNotScanIps, ip)
					break
				}
			}
		}
	}

	if len(canNotScanIps) > 0 {
		return errors.BadRequest(pb.ServiceName, fmt.Sprintf("不能下发扫描任务，以下ip为禁扫ip: %s", strings.Join(canNotScanIps, ",")))
	}

	// 8. 创建新任务（事务处理）
	err = mysql.GetDbClient().Transaction(func(tx *gorm.DB) error {
		// 创建任务
		result := tx.Table(scan_crontab_tasks.TableName).Create(scanTask)
		if result.Error != nil {
			log.WithContextErrorf(ctx, "[周期任务] 创建周期任务失败: %v", result.Error)
			return result.Error
		}

		taskID := scanTask.ID

		// 9. 如果需要关闭旧任务 - 对应PHP: if ($param['close_old'] ?? false) { CronTask::query()->where('id', $param['id'])->update(['switch' => 1]); }
		if req.CloseOld == 1 {
			if err := tx.Table(scan_crontab_tasks.TableName).Where("id = ?", req.Id).Update("switch", 1).Error; err != nil {
				log.WithContextErrorf(ctx, "[周期任务] 关闭旧任务失败: %v", err)
				return err
			}
		}

		// 10. 插入IP（无论是资产扫描还是POC扫描都需要插入IP）
		if len(ips) > 0 {
			var ipArr []scan_crontab_tasks_ips.ScanCrontabTasksIp
			for _, ip := range ips {
				ipArr = append(ipArr, scan_crontab_tasks_ips.ScanCrontabTasksIp{
					CronTaskId: taskID,
					Ip:         ip,
					CreatedAt:  time.Now(),
				})
			}

			if len(ipArr) > 0 {
				// 使用原生SQL批量插入，避免MySQL错误1295
				sqlStr := fmt.Sprintf("INSERT INTO %s (cron_task_id, ip, created_at) VALUES ", scan_crontab_tasks_ips.TableName)
				vals := []interface{}{}

				// 构建SQL语句和参数
				for i, ip := range ipArr {
					if i > 0 {
						sqlStr += ","
					}
					sqlStr += "(?, ?, ?)"
					vals = append(vals, ip.CronTaskId, ip.Ip, ip.CreatedAt)
				}

				// 执行原生SQL
				result = tx.Exec(sqlStr, vals...)
				if result.Error != nil {
					log.WithContextErrorf(ctx, "[周期任务] 创建周期任务IP失败: %v", result.Error)
					return result.Error
				}
			}
		}

		if req.TaskType == 1 { // CronTaskTypeAsset - 资产扫描
			// 处理端口
			if req.IsDefinePort == 1 {
				// 自定义端口
				var definePorts []port_group.DefinePort
				for _, port := range req.DefinePorts {
					for _, protocol := range req.DefinePortProtocols {
						definePorts = append(definePorts, port_group.DefinePort{
							CronTaskId:     taskID,
							UserId:         userID,
							Port:           int(port),
							PortProtocolId: uint64(protocol),
						})
					}
				}

				if len(definePorts) > 0 {
					// 使用原生SQL批量插入，避免MySQL错误1295
					sqlStr := "INSERT INTO define_ports (cron_task_id, user_id, port, port_protocol_id) VALUES "
					vals := []interface{}{}

					// 构建SQL语句和参数
					for i, port := range definePorts {
						if i > 0 {
							sqlStr += ","
						}
						sqlStr += "(?, ?, ?, ?)"
						vals = append(vals, port.CronTaskId, port.UserId, port.Port, port.PortProtocolId)
					}

					// 执行原生SQL
					result = tx.Exec(sqlStr, vals...)
					if result.Error != nil {
						log.WithContextErrorf(ctx, "[周期任务] 创建自定义端口失败: %v", result.Error)
						return result.Error
					}
				}
			} else {
				// 使用端口组
				if req.PortGroupIds > 0 {
					err = tx.Exec(fmt.Sprintf("INSERT INTO %s (cron_task_id, cronports_type, cronports_id, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())",
						scan_crontab_tasks_ports.TableName),
						taskID, "App\\Models\\MySql\\PortGroup", req.PortGroupIds).Error
					if err != nil {
						log.WithContextErrorf(ctx, "[周期任务] 创建周期任务端口失败: %v", err)
						return err
					}
				}
			}
		} else { // TaskType == 2 - POC扫描
			// POC扫描
			pocIds := make([]int64, 0)
			portGroupIds := make([]int64, 0)

			// 根据扫描类型准备数据
			if req.PocScanType == cron_task_pocs.POC_SCAN_TYPE_RANGE && len(req.PocIds) > 0 {
				// 指定POC扫描
				for _, id := range req.PocIds {
					pocIds = append(pocIds, int64(id))
				}
			} else if req.PocScanType == cron_task_pocs.POC_SCAN_TYPE_GROUP && req.PortGroupIds > 0 {
				// POC分组扫描
				portGroupIds = append(portGroupIds, int64(req.PortGroupIds))
			}

			// 创建POC数据
			pocData, flag := cron_task_pocs.CreatePocData(taskID, int(req.PocScanType), pocIds, portGroupIds)

			// 只有在有数据需要插入时才执行插入操作
			if flag && len(pocData) > 0 {
				if err := tx.Table("cron_task_pocs").Create(&pocData).Error; err != nil {
					log.WithContextErrorf(ctx, "[周期任务] 插入POC数据失败: %v", err)
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 编辑周期任务事务失败: %v", err)
		return err
	}

	// 记录操作日志
	err = asyncq.Enqueue(ctx, asyncq.CreateLogEvent, asyncq.NewCreateLogEventPayload(
		uint64(req.UserId),
		"编辑资产扫描周期任务",
		metadata.MustString(ctx, "client_ip"),
		uint64(req.CompanyId),
		operate_logs.FIND_ASSETS,
		operate_logs.TYPE_OPERATE,
	))
	if err != nil {
		log.WithContextErrorf(ctx, "[周期任务] 记录操作日志失败: %v", err)
	}

	// 设置响应
	rsp.Success = true
	rsp.TaskId = scanTask.ID

	return nil
}
