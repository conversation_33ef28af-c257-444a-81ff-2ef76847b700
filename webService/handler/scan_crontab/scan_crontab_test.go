package scan_crontab

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"go-micro.dev/v4/metadata"
	"gorm.io/gorm"

	"micro-service/initialize/es"
	initmysql "micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
)

var (
	indexName = foradar_assets.IndexName
)

// 初始化测试环境
func Init() {
	cfg.InitLoadCfg()
	log.Init()

	// 初始化Mock服务
	mock := es.NewMockServer()

	// 查询语句Mock数据 - 资产数据
	resultList := []foradar_assets.ForadarAsset{
		{
			ID:     "1_**********",
			Ip:     "**********",
			UserID: 1,
			Status: foradar_assets.StatusConfirmAsset,
			IsIpv6: false,
		},
		{
			ID:     "1_**********",
			Ip:     "**********",
			UserID: 1,
			Status: foradar_assets.StatusUploadAsset,
			IsIpv6: false,
		},
	}

	mock.Register("/"+indexName+"/_search", []*elastic.SearchHit{
		{
			Index:  indexName,
			Type:   "_doc",
			Id:     resultList[0].ID,
			Source: utils.ToJSON(resultList[0]),
		},
		{
			Index:  indexName,
			Type:   "_doc",
			Id:     resultList[1].ID,
			Source: utils.ToJSON(resultList[1]),
		},
	})

	// Count语句Mock数据
	countResponse := elastic.CountResponse{
		Count: 2,
	}
	mock.Register("/"+indexName+"/_count", countResponse)

	// 创建Mock服务
	mock.NewElasticMockClient()

	// 设置是否使用Mock (默认true)
	es.SetTestEnv(true)
	initmysql.SetTestEnv(true)
	redis.SetTestEnv(true)

	// 初始化数据库
	es.GetInstance(cfg.LoadElastic())
	initmysql.GetInstance(cfg.LoadMysql())
	redis.GetInstance(cfg.LoadRedis())
}

func TestRemoveDuplicateIPs(t *testing.T) {
	t.Run("empty slice", func(t *testing.T) {
		result := removeDuplicateIPs([]string{})
		assert.Equal(t, []string{}, result)
	})

	t.Run("no duplicates", func(t *testing.T) {
		ips := []string{"***********", "***********", "***********"}
		result := removeDuplicateIPs(ips)
		assert.Equal(t, ips, result)
	})

	t.Run("with duplicates", func(t *testing.T) {
		ips := []string{"***********", "***********", "***********", "***********", "***********"}
		expected := []string{"***********", "***********", "***********"}
		result := removeDuplicateIPs(ips)
		assert.Equal(t, expected, result)
	})

	t.Run("with empty strings and whitespace", func(t *testing.T) {
		ips := []string{"***********", "", " *********** ", "", "***********", "  "}
		expected := []string{"***********", "***********"}
		result := removeDuplicateIPs(ips)
		assert.Equal(t, expected, result)
	})

	t.Run("all empty strings", func(t *testing.T) {
		ips := []string{"", "  ", "", "   "}
		result := removeDuplicateIPs(ips)
		assert.Equal(t, []string{}, result)
	})
}

func TestCrontabTaskList(t *testing.T) {
	t.Run("success with basic parameters", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		mock.ExpectQuery("SELECT count.*").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		mock.ExpectQuery("SELECT .* FROM .*scan_crontab_tasks.*").
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
				"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
				"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
				"switch", "created_at", "updated_at", "deleted_at", "file_name",
				"task_from", "op_id", "is_audit", "reason", "is_define_port",
				"scan_engine", "engine_role", "table_assets_type",
			}).AddRow(
				1, 1, 1, "测试任务", "100M", 1, "2", 1, 1, 0, 0, 0, 6, "10:00", "01", 1, 0,
				time.Now(), time.Now(), nil, "test.txt", 0, 1, 0, "", 0, "", 0, 0,
			))

		mock.ExpectQuery("SELECT .* FROM .*users.*").
			WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(1, "测试用户"))

		req := &pb.CrontabTaskListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
		}
		rsp := &pb.CrontabTaskListResponse{}

		err := CrontabTaskList(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), rsp.Total)
		assert.Equal(t, int32(1), rsp.CurrentPage)
		assert.Equal(t, int32(10), rsp.PerPage)
		assert.Len(t, rsp.Items, 1)
		assert.Equal(t, uint64(1), rsp.Items[0].Id)
		assert.Equal(t, "测试任务", rsp.Items[0].Name)
	})

	t.Run("database error", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		mock.ExpectQuery("SELECT count.*").
			WillReturnError(errors.New("database error"))

		req := &pb.CrontabTaskListRequest{
			UserId:  1,
			Page:    1,
			PerPage: 10,
		}
		rsp := &pb.CrontabTaskListResponse{}

		err := CrontabTaskList(context.Background(), req, rsp)
		assert.Error(t, err)
	})
}

func TestCrontabTaskDelete(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		// 删除主表记录
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `scan_crontab_tasks` WHERE user_id = \\? AND id IN \\(\\?,\\?\\)").
			WithArgs(uint64(1), uint64(1), uint64(2)).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		// 删除关联表记录
		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `cron_task_ips` WHERE cron_task_id IN \\(\\?,\\?\\)").
			WithArgs(uint64(1), uint64(2)).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `cron_task_ports` WHERE cron_task_id IN \\(\\?,\\?\\)").
			WithArgs(uint64(1), uint64(2)).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `cron_task_pocs` WHERE cron_task_id IN \\(\\?,\\?\\)").
			WithArgs(uint64(1), uint64(2)).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		req := &pb.CrontabTaskDeleteRequest{
			UserId: 1,
			Id:     []uint64{1, 2},
		}
		rsp := &pb.CrontabTaskDeleteResponse{}

		err := CrontabTaskDelete(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.True(t, rsp.Success)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		mock.ExpectBegin()
		mock.ExpectExec("DELETE FROM `scan_crontab_tasks` WHERE user_id = \\? AND id IN \\(\\?\\)").
			WithArgs(uint64(1), uint64(1)).
			WillReturnError(errors.New("database error"))
		mock.ExpectRollback()

		req := &pb.CrontabTaskDeleteRequest{
			UserId: 1,
			Id:     []uint64{1},
		}
		rsp := &pb.CrontabTaskDeleteResponse{}

		err := CrontabTaskDelete(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCrontabTaskSwitch(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		// 查询任务是否存在
		mock.ExpectQuery("SELECT \\* FROM `scan_crontab_tasks` WHERE id = \\? AND user_id = \\? AND `scan_crontab_tasks`\\.`deleted_at` IS NULL ORDER BY `scan_crontab_tasks`\\.`id` LIMIT \\?").
			WithArgs(uint64(1), uint64(1), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
				"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
				"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
				"switch", "created_at", "updated_at", "deleted_at", "file_name",
				"task_from", "op_id", "is_audit", "reason", "is_define_port",
				"scan_engine", "engine_role", "table_assets_type",
			}).AddRow(
				1, 1, 1, "测试任务", "100M", 1, "2", 1, 1, 0, 0, 0, 6, "10:00", "01", 1, 0,
				time.Now(), time.Now(), nil, "test.txt", 0, 1, 0, "", 0, "", 0, 0,
			))

		// 更新开关状态
		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `scan_crontab_tasks` SET `switch`=\\? WHERE id = \\? AND user_id = \\?").
			WithArgs(1, uint64(1), uint64(1)).
			WillReturnResult(sqlmock.NewResult(0, 1))
		mock.ExpectCommit()

		req := &pb.CrontabTaskSwitchRequest{
			UserId: 1,
			Id:     1,
			Switch: 1,
		}
		rsp := &pb.CrontabTaskSwitchResponse{}

		err := CrontabTaskSwitch(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.True(t, rsp.Success)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("task not found", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `scan_crontab_tasks` WHERE id = \\? AND user_id = \\? AND `scan_crontab_tasks`\\.`deleted_at` IS NULL ORDER BY `scan_crontab_tasks`\\.`id` LIMIT \\?").
			WithArgs(uint64(999), uint64(1), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		req := &pb.CrontabTaskSwitchRequest{
			UserId: 1,
			Id:     999,
			Switch: 1,
		}
		rsp := &pb.CrontabTaskSwitchResponse{}

		err := CrontabTaskSwitch(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("update failure", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		// 查询任务存在
		mock.ExpectQuery("SELECT \\* FROM `scan_crontab_tasks` WHERE id = \\? AND user_id = \\? AND `scan_crontab_tasks`\\.`deleted_at` IS NULL ORDER BY `scan_crontab_tasks`\\.`id` LIMIT \\?").
			WithArgs(uint64(1), uint64(1), sqlmock.AnyArg()).
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
				"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
				"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
				"switch", "created_at", "updated_at", "deleted_at", "file_name",
				"task_from", "op_id", "is_audit", "reason", "is_define_port",
				"scan_engine", "engine_role", "table_assets_type",
			}).AddRow(
				1, 1, 1, "测试任务", "100M", 1, "2", 1, 1, 0, 0, 0, 6, "10:00", "01", 1, 0,
				time.Now(), time.Now(), nil, "test.txt", 0, 1, 0, "", 0, "", 0, 0,
			))

		// 更新失败
		mock.ExpectBegin()
		mock.ExpectExec("UPDATE `scan_crontab_tasks` SET `switch`=\\? WHERE id = \\? AND user_id = \\?").
			WithArgs(1, uint64(1), uint64(1)).
			WillReturnError(errors.New("database error"))
		mock.ExpectRollback()

		req := &pb.CrontabTaskSwitchRequest{
			UserId: 1,
			Id:     1,
			Switch: 1,
		}
		rsp := &pb.CrontabTaskSwitchResponse{}

		err := CrontabTaskSwitch(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestCrontabTaskDetail(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		// Mock查询任务详情
		mock.ExpectQuery("SELECT \\* FROM `scan_crontab_tasks`").
			WillReturnRows(sqlmock.NewRows([]string{
				"id", "user_id", "company_id", "name", "bandwidth", "poc_scan_type",
				"protocol_concurrency", "task_type", "ip_type", "scan_range", "ping_switch",
				"web_logo_switch", "type", "schedule_time", "day_of_x", "scan_type",
				"switch", "created_at", "updated_at", "deleted_at", "file_name",
				"task_from", "op_id", "is_audit", "reason", "is_define_port",
				"scan_engine", "engine_role", "table_assets_type",
			}).AddRow(
				1, 1, 1, "测试任务", "100M", 1, "2", 1, 1, 0, 0, 0, 6, "10:00", "01", 1, 0,
				time.Now(), time.Now(), nil, "test.txt", 0, 2, 0, "", 0, "", 0, 0,
			))

		// Mock查询操作者信息
		mock.ExpectQuery("SELECT \\* FROM `users`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).AddRow(2, "操作者"))

		// Mock查询IP列表
		mock.ExpectQuery("SELECT \\* FROM `cron_task_ips`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "cron_task_id", "ip", "created_at", "updated_at"}).
				AddRow(1, 1, "***********", time.Now(), time.Now()).
				AddRow(2, 1, "***********", time.Now(), time.Now()))

		// Mock查询端口列表
		mock.ExpectQuery("SELECT \\* FROM `cron_task_ports`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "cron_task_id", "cronports_type", "cronports_id", "created_at", "updated_at"}).
				AddRow(1, 1, "App\\Models\\MySql\\PortGroup", 1, time.Now(), time.Now()))

		// Mock查询端口组信息
		mock.ExpectQuery("SELECT \\* FROM `port_groups`").
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "company_id", "name", "can_del", "created_at", "updated_at"}).
				AddRow(1, 1, 1, "常用端口", 0, time.Now(), time.Now()))

		// Mock查询自定义端口
		mock.ExpectQuery("SELECT .* FROM `define_ports`").
			WillReturnRows(sqlmock.NewRows([]string{"port", "port_protocol_id"}).
				AddRow(80, 1).AddRow(443, 1).AddRow(8080, 2))

		req := &pb.CrontabTaskDetailRequest{
			UserId: 1,
			Id:     1,
		}
		rsp := &pb.CrontabTaskDetailResponse{}

		err := CrontabTaskDetail(context.Background(), req, rsp)
		assert.NoError(t, err)
		assert.Equal(t, uint64(1), rsp.Id)
		assert.Equal(t, "测试任务", rsp.Name)
		assert.Equal(t, uint64(1), rsp.UserId)
		assert.NotNil(t, rsp.Op)
		assert.Equal(t, int64(2), rsp.Op.Id)
		assert.Equal(t, "操作者", rsp.Op.Name)
		assert.Len(t, rsp.Ips, 2)
		assert.Len(t, rsp.Ports, 1)
	})

	t.Run("task not found", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		mock.ExpectQuery("SELECT \\* FROM `scan_crontab_tasks`").
			WillReturnError(gorm.ErrRecordNotFound)

		req := &pb.CrontabTaskDetailRequest{
			UserId: 1,
			Id:     999,
		}
		rsp := &pb.CrontabTaskDetailResponse{}

		err := CrontabTaskDetail(context.Background(), req, rsp)
		assert.Error(t, err)
	})
}

func TestCrontabTaskCreate(t *testing.T) {
	t.Run("success_asset_scan", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		// Mock查询禁扫IP
		mock.ExpectQuery("SELECT \\* FROM `forbid_ips` WHERE user_id = \\?").
			WithArgs(uint64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "ip_segment"}))

		// Mock事务
		mock.ExpectBegin()

		// Mock创建周期任务主表
		mock.ExpectExec("INSERT INTO `scan_crontab_tasks`").
			WithArgs(
				uint64(1),        // user_id
				uint64(1),        // company_id
				"测试任务",           // name
				"100M",           // bandwidth
				int32(1),         // poc_scan_type
				"2",              // protocol_concurrency
				int32(1),         // task_type
				int32(1),         // ip_type
				int32(0),         // scan_range
				int32(0),         // ping_switch
				int32(0),         // web_logo_switch
				int32(6),         // type
				"",               // schedule_time
				"",               // day_of_x
				int32(1),         // scan_type
				int32(0),         // switch
				sqlmock.AnyArg(), // created_at
				sqlmock.AnyArg(), // updated_at
				nil,              // deleted_at
				"",               // file_name
				int32(0),         // task_from
				int64(0),         // op_id
				int32(0),         // is_audit
				"",               // reason
				int32(0),         // is_define_port
				"",               // scan_engine
				int32(0),         // engine_role
				int32(0),         // table_assets_type
			).
			WillReturnResult(sqlmock.NewResult(1, 1))

		// 创建周期任务IP
		mock.ExpectExec("INSERT INTO cron_task_ips \\(cron_task_id, ip, created_at\\) VALUES \\(\\?, \\?, \\?\\),\\(\\?, \\?, \\?\\)").
			WithArgs(
				uint64(1), "***********", sqlmock.AnyArg(),
				uint64(1), "***********", sqlmock.AnyArg(),
			).
			WillReturnResult(sqlmock.NewResult(1, 2))

		// 创建周期任务端口
		mock.ExpectExec("INSERT INTO cron_task_ports \\(cron_task_id, cronports_type, cronports_id, created_at, updated_at\\) VALUES \\(\\?, \\?, \\?, NOW\\(\\), NOW\\(\\)\\)").
			WithArgs(
				uint64(1),                       // cron_task_id
				"App\\Models\\MySql\\PortGroup", // cronports_type
				uint64(1),                       // cronports_id
			).
			WillReturnResult(sqlmock.NewResult(1, 1))

		mock.ExpectCommit()

		req := &pb.CrontabTaskCreateRequest{
			UserId:              1,
			CompanyId:           1,
			Name:                "测试任务",
			Bandwidth:           "100M",
			TaskType:            1,
			IpType:              1,
			ProtocolConcurrency: "2",
			ScanType:            1,
			Type:                6,
			PortGroupIds:        uint64(1),
			Ips:                 []string{"***********", "***********"},
		}
		rsp := &pb.CrontabTaskCreateResponse{}

		// 创建带有 metadata 的 context
		ctx := context.Background()
		ctx = metadata.Set(ctx, "client_ip", "127.0.0.1")

		err := CrontabTaskCreate(ctx, req, rsp)
		assert.NoError(t, err)
		assert.True(t, rsp.Success)
		assert.Len(t, rsp.ErrorList, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("database_error", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		// Mock查询禁扫IP
		mock.ExpectQuery("SELECT \\* FROM `forbid_ips` WHERE user_id = \\?").
			WithArgs(uint64(1)).
			WillReturnRows(sqlmock.NewRows([]string{"id", "user_id", "ip_segment"}))

		mock.ExpectBegin()
		mock.ExpectExec("INSERT INTO `scan_crontab_tasks`").
			WillReturnError(errors.New("database error"))
		mock.ExpectRollback()

		req := &pb.CrontabTaskCreateRequest{
			UserId:              1,
			CompanyId:           1,
			Name:                "测试任务",
			Bandwidth:           "100M",
			TaskType:            1,
			IpType:              1,
			ProtocolConcurrency: "2",
			ScanType:            1,
			Type:                6,
			ScanRange:           0,
			Ips:                 []string{"***********"},
			PortGroupIds:        1,
			IsDefinePort:        0,
		}
		rsp := &pb.CrontabTaskCreateResponse{}

		// 创建带有 metadata 的 context
		ctx := context.Background()
		ctx = metadata.Set(ctx, "client_ip", "127.0.0.1")

		err := CrontabTaskCreate(ctx, req, rsp)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestEditCrontabTask(t *testing.T) {
	t.Run("task not found", func(t *testing.T) {
		Init()
		mock := initmysql.GetMockInstance()

		// 只期望查询任务是否存在，然后返回记录未找到错误
		mock.ExpectQuery("SELECT \\* FROM `scan_crontab_tasks` WHERE id = \\? AND user_id = \\? AND `scan_crontab_tasks`\\.`deleted_at` IS NULL ORDER BY `scan_crontab_tasks`\\.`id` LIMIT \\?").
			WithArgs(uint64(999), uint64(1), sqlmock.AnyArg()).
			WillReturnError(gorm.ErrRecordNotFound)

		req := &pb.CrontabTaskEditRequest{
			UserId: 1,
			Id:     999,
		}
		rsp := &pb.CrontabTaskEditResponse{}

		err := EditCrontabTask(context.Background(), req, rsp)
		assert.Error(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
