package scan_crontab

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"micro-service/middleware/mysql/port_group"
)

// TestPortDeduplication 测试端口去重逻辑
func TestPortDeduplication(t *testing.T) {
	t.Run("test_port_deduplication_logic", func(t *testing.T) {
		// 模拟数据库查询结果：端口12配置了协议1和2
		definePorts := []*port_group.DefinePort{
			{Port: 12, PortProtocolId: 1},
			{Port: 12, PortProtocolId: 2},
		}

		// 模拟修复后的去重逻辑
		portMap := make(map[int32]bool)
		protocolMap := make(map[int32]bool)
		for _, dp := range definePorts {
			portMap[int32(dp.Port)] = true
			protocolMap[int32(dp.PortProtocolId)] = true
		}

		// 将去重后的数据转换为切片
		var portList []int32
		var protocolList []int32
		for port := range portMap {
			portList = append(portList, port)
		}
		for protocol := range protocolMap {
			protocolList = append(protocolList, protocol)
		}

		// 验证结果
		assert.Equal(t, 1, len(portList), "端口应该去重为1个")
		assert.Equal(t, int32(12), portList[0], "端口应该是12")
		assert.Equal(t, 2, len(protocolList), "协议应该保持2个")
		assert.Contains(t, protocolList, int32(1), "应该包含协议1")
		assert.Contains(t, protocolList, int32(2), "应该包含协议2")
	})

	t.Run("test_multiple_ports_deduplication", func(t *testing.T) {
		// 模拟更复杂的场景：多个端口，每个端口配置多个协议
		definePorts := []*port_group.DefinePort{
			{Port: 12, PortProtocolId: 1},
			{Port: 12, PortProtocolId: 2},
			{Port: 80, PortProtocolId: 1},
			{Port: 80, PortProtocolId: 2},
			{Port: 443, PortProtocolId: 1},
		}

		// 模拟修复后的去重逻辑
		portMap := make(map[int32]bool)
		protocolMap := make(map[int32]bool)
		for _, dp := range definePorts {
			portMap[int32(dp.Port)] = true
			protocolMap[int32(dp.PortProtocolId)] = true
		}

		// 将去重后的数据转换为切片
		var portList []int32
		var protocolList []int32
		for port := range portMap {
			portList = append(portList, port)
		}
		for protocol := range protocolMap {
			protocolList = append(protocolList, protocol)
		}

		// 验证结果
		assert.Equal(t, 3, len(portList), "应该有3个不同的端口")
		assert.Equal(t, 2, len(protocolList), "应该有2个不同的协议")
		
		// 验证端口包含正确的值
		portSet := make(map[int32]bool)
		for _, port := range portList {
			portSet[port] = true
		}
		assert.True(t, portSet[12], "应该包含端口12")
		assert.True(t, portSet[80], "应该包含端口80")
		assert.True(t, portSet[443], "应该包含端口443")

		// 验证协议包含正确的值
		protocolSet := make(map[int32]bool)
		for _, protocol := range protocolList {
			protocolSet[protocol] = true
		}
		assert.True(t, protocolSet[1], "应该包含协议1")
		assert.True(t, protocolSet[2], "应该包含协议2")
	})

	t.Run("test_empty_data", func(t *testing.T) {
		// 测试空数据的情况
		var definePorts []*port_group.DefinePort

		// 模拟修复后的去重逻辑
		portMap := make(map[int32]bool)
		protocolMap := make(map[int32]bool)
		for _, dp := range definePorts {
			portMap[int32(dp.Port)] = true
			protocolMap[int32(dp.PortProtocolId)] = true
		}

		// 将去重后的数据转换为切片
		var portList []int32
		var protocolList []int32
		for port := range portMap {
			portList = append(portList, port)
		}
		for protocol := range protocolMap {
			protocolList = append(protocolList, protocol)
		}

		// 确保即使没有数据也返回空数组而不是null
		if portList == nil {
			portList = []int32{}
		}
		if protocolList == nil {
			protocolList = []int32{}
		}

		// 验证结果
		assert.NotNil(t, portList, "端口列表不应该为nil")
		assert.NotNil(t, protocolList, "协议列表不应该为nil")
		assert.Equal(t, 0, len(portList), "端口列表应该为空")
		assert.Equal(t, 0, len(protocolList), "协议列表应该为空")
	})
}
