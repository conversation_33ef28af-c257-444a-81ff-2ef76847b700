syntax = "proto3";

package web;

option go_package = "./proto;web";

service Web {
	// 资产探知模块 / 资产核查功能
	rpc AssetAuditFileCheck(AssetAuditTaskCreateRequest) returns (Empty) {} // 检查上传的核查文件
	rpc AssetAuditTaskInfo(AssetAuditTaskInfoRequest) returns (AssetAuditTaskInfoResponse) {} // 获取核查任务详情
	rpc AssetAuditTaskCreate(AssetAuditTaskCreateRequest) returns (AssetAuditTaskCreateResponse) {} // 新建核查任务
	rpc AssetAuditTaskList(AssetAuditTaskListRequest) returns (AssetAuditTaskListResponse) {} // 核查任务列表
	rpc AssetAuditTaskDelete(AssetAuditTaskDeleteRequest) returns (Empty) {} // 核查任务删除
	rpc AssetAuditResultDownload(AssetAuditResultDownloadRequest) returns (AssetAuditResultDownloadResponse) {} // 核查结果下载
	rpc AssetAuditResultList(AssetAuditTaskResultListRequest) returns (AssetAuditTaskResultListResponse) {} // 核查结果列表
	rpc AssetAuditSyncAssets(AssetAuditSyncAssetsRequest) returns (AssetAuditSyncAssetsResponse) {} // 核查结果同步台账
	rpc AssetAuditTaskFinished(AssetAuditSyncAssetsRequest) returns (Empty) {} // 更新任务进度为完成
	
	// IP详情接口
	rpc IpDetail(IpDetailRequest) returns (IpDetailResponse) {} // 获取IP详情信息

	// 资产探知模块 / 资产状态检测功能
	rpc StartAssetsStatusDetect(StartAssetsStatusDetectRequest) returns (StartAssetsStatusDetectResponse) {} // 开始资产状态检测
	rpc GetAssetsStatusDetectInfo(GetAssetsStatusDetectInfoRequest) returns (GetAssetsStatusDetectInfoResponse) {} // 资产详情
	rpc GetAssetsStatusDetectList(GetAssetsStatusDetectListRequest) returns (GetAssetsStatusDetectListResponse) {} // 任务列表
	rpc DelAssetsStatusDetectTask(DelAssetsStatusDetectTaskRequest) returns (Empty) {} // 任务删除

	// url探知模块 / url状态检测功能
	rpc StartUrlStatusDetect(StartUrlStatusDetectRequest) returns (StartUrlStatusDetectResponse) {} // 开始url状态检测
	rpc GetUrlStatusDetectInfo(GetUrlStatusDetectInfoRequest) returns (GetUrlStatusDetectInfoResponse) {} // url详情
	rpc GetUrlStatusDetectList(GetUrlStatusDetectListRequest) returns (GetUrlStatusDetectListResponse) {} // 任务列表
	rpc DelUrlStatusDetectTask(DelUrlStatusDetectTaskRequest) returns (Empty) {} // 任务删除

	// 云端推荐结果
	rpc CloudRecommendResults(CloudRecommendResultsRequest) returns (CloudRecommendResultsResponse) {} // 云端推荐结果列表
	rpc CloudRecommendResultsClues(CloudRecommendResultsRequest) returns (RecommendResultSearchMapResponse) {} // 云端推荐结果线索展示
	rpc CloudRecommendResultsExport(CloudRecommendExportRequest) returns (CloudRecommendExportResponse) {} // 云端推荐结果导出
	rpc GroupIpResults(GroupIpResultsRequest) returns (GroupIpResultsResponse) {} // IP维度推荐结果
	rpc HunterSql(HunterSqlRequest) returns (HunterSqlResponse) {} // Hunter SQL生成
	rpc ImportResultsConfirm(ImportResultsConfirmRequest) returns (ImportResultsConfirmResponse) {} // 导入推荐资产数据
	rpc TaskRelate(TaskRelateRequest) returns (Empty) {} // 资产测绘关联异步任务

	// 公告
	rpc HasLoggedIn(HasLoggedInRequest) returns (HasLoggedInResponse) {}
	rpc PublicNoticeAdd(PublicNoticeAddRequest) returns (PublicNoticeAddResponse) {}
	rpc PublicNoticeDel(PublicNoticeDelRequest) returns (Empty) {}
	rpc PublicNoticeSave(PublicNoticeSaveRequest) returns (Empty) {}
	rpc PublicNoticeList(PublicNoticeListRequest) returns (PublicNoticeListResponse) {}
	rpc FindLatestNotice(Empty) returns (FindLatestNoticeResponse) {}

	// 任务概览
	rpc TaskOverviewCount(TaskOverviewCountRequest) returns (TaskOverviewCountResponse) {} // 任务统计
	rpc TaskOverviewCountByCategory(TaskOverviewCountByCategoryRequest) returns (TaskOverviewCountByCategoryResponse) {} // 任务统计按类别

	// 资产台账
	rpc IpAssetIpProfile(IpAssetProfileRequest) returns (IpAssetProfileResponse) {} // IP画像
	rpc IpAssetVulns(IpAssetIpVulnsRequest) returns (IpAssetIpVulnsResponse) {} // IP资产漏洞信息
	// 业务系统功能
	rpc SystemDetectCreate(SystemDetectCreateRequest) returns(Empty) {} // 新建
	rpc SystemDetectUpdate(SystemDetectUpdateRequest) returns(Empty) {} // 更新
	rpc SystemDetectList(SystemDetectListRequest) returns(SystemDetectListResponse) {} // 结果
	rpc SystemDetectDelete(SystemDetectListRequest) returns(Empty) {} // 删除
	rpc SystemDetectIgnoreConfirm(SystemDetectListRequest) returns(Empty) {} // 确认/忽略
	rpc SystemDetectDownload(SystemDetectListRequest) returns(SystemDetectDownloadResponse) {} // 下载
	rpc SystemDetectIpAssociation(SystemDetectListRequest) returns(Empty) {} // IP关联台账
	rpc SystemDetectSyncAssets(SystemDetectListRequest) returns(Empty) {} // 同步资产至台账
	rpc SystemDetectProgress(SystemDetectProgressRequest) returns(SystemDetectProgressResponse) {} // 进度
	// 域名资产
	rpc DomainAssetUpdateByCron(DomainAssetsUpdateByCronRequest) returns (Empty) {}
	rpc DomainAssetCronInfo(DomainAssetsUpdateByCronRequest) returns (DomainAssetsCronInfoResponse) {} // 定时任务详情
	rpc DomainAssetDomainFilter(DomainAssetsDomainFilterRequest) returns(DomainAssetsDomainFilterResponse) {}

	// 资产概览
	rpc AssetsOverviewRuleCount(AssetsOverviewRuleCountRequest) returns (AssetsOverviewRuleCountResponse) {} // 组件统计
	// 数字资产
	rpc AssetsOverviewDigitalAssets(AssetsOverviewDigitalAssetsRequest) returns (AssetsOverviewDigitalAssetsResponse) {}
	rpc AssetsOverviewComparedLastMonth(ComparedLastMonthRequest) returns (ComparedLastMonthResponse) {} // 环比数据
	// 资产动态
	rpc  AssetsOverviewAssetsDynamic(AssetsOverviewAssetsDynamicRequest) returns (AssetsOverviewAssetsDynamicResponse) {}
	// 资产动态-统计
	rpc AssetsOverviewAssetsDynamicCount(AssetsOverviewAssetsDynamicCountRequest) returns (AssetsOverviewAssetsDynamicCountResponse) {}
	// 用户在线列表
	rpc UserOnlineList(UserTokenListRequest) returns (UserTokenListResponse) {}
	// 用户首次测绘任务通知
	rpc SetFirstTaskNotice(SetFirstTaskNoticeRequest) returns (SetFirstTaskNoticeResponse) {}

	// 数据泄露-任务管理
	rpc DlpTaskResultList(DlpTaskResultListRequest) returns (DlpTaskResultListResponse) {} // 任务列表
	rpc DlpTaskResultUpdate(DlpResultUpdateRequest) returns (Empty) {} // 更新任务
	rpc DlpTaskAgg(DlpTaskAggRequest) returns (DlpTaskAggResponse) {} // 统计
	// 数据泄露-GitHub
	rpc DlpGitHubTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpGitHubTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpGitHubTaskResult(GitHubCodeRequest) returns (GitHubCodeResponse) {} // 获取任务结果
	// 数据泄露-Gitee
	// rpc DlpGiteeTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	// rpc DlpGiteeTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	// rpc DlpGiteeTaskResult(GitHubCodeRequest) returns (GitHubCodeResponse) {} // 获取任务结果
	// 数据泄露-百度文库
	rpc DlpBaiduLibraryTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpBaiduLibraryTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpBaiduLibraryTaskResult(GitHubCodeRequest) returns (BaiduLibraryResponse) {} // 获取任务结果
	// 数据泄露-豆丁
	rpc DlpDocinTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpDocinTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpDocinTaskResult(GitHubCodeRequest) returns (DocinResultResponse) {} // 获取任务结果
	// 数据泄露-56网盘
	rpc Dlp56WangpanTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc Dlp56WangpanTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc Dlp56WangpanTaskResult(GitHubCodeRequest) returns (Wangpan56ResultResponse) {} // 获取任务结果
	// 数据泄露-PanSoso
	rpc DlpPanSosoTaskCreate(GitHubCodeRequest) returns (GitHubCodeRequest) {} // 创建任务
	rpc DlpPanSosoTaskInfo(GitHubCodeRequest) returns (GitHubTaskInfoResponse) {} // 获取任务信息
	rpc DlpPanSosoTaskResult(GitHubCodeRequest) returns (Wangpan56ResultResponse) {} // 获取任务结果
	
	//---------------------------端口--端口分组--------
	rpc PortIndex(PortIndexRequest) returns (PortIndexResponse) {}
	rpc PortList(PortListRequest) returns (PortListResponse) {}
	rpc PortAdd(PortAddRequest) returns (PortAddResponse) {}
	rpc PortEdit(PortEditRequest) returns (PortEditResponse) {}
	rpc PortDel(PortDelRequest) returns (PortDelResponse) {}
	rpc PortUpdateStatus(PortUpdateStatusRequest) returns (PortUpdateStatusResponse) {}
	rpc PortProtocolIndex(PortProtocolIndexRequest) returns (PortProtocolIndexResponse) {}
	rpc PortDetail(PortDetailRequest) returns (PortDetailResponse) {}
	rpc PortGroupIndex(PortGroupIndexRequest) returns (PortGroupIndexResponse) {}
	rpc PortGroupAdd(PortGroupAddRequest) returns (PortGroupAddResponse) {}
	rpc PortGroupDel(PortGroupDelRequest) returns (PortGroupDelResponse) {}
	rpc PortGroupList(PortGroupListRequest) returns (PortGroupListResponse) {}
	rpc PortGroupEdit(PortGroupEditRequest) returns (PortGroupEditResponse) {}
	rpc PortGroupDetail(PortGroupDetailRequest) returns (PortGroupDetailResponse) {}
	// 钓鱼仿冒模块
	rpc CounterfeitTaskList(CounterfeitTaskListRequest) returns (CounterfeitTaskListResponse) {} // 仿冒任务列表
	rpc CounterfeitTaskDelete(CounterfeitTaskDelRequest) returns (Empty) {} // 仿冒任务删除
	rpc CounterfeitAssetList(CounterfeitAssetListRequest) returns (CounterfeitAssetListResponse) {} // 仿冒数据列表
	rpc CounterfeitAssetsAgg(CounterfeitAssetsAggRequest) returns (CounterfeitAssetsAggResponse) {} // 资产仿冒聚合
	rpc CounterfeitAssetsFind(CounterfeitAssetFindRequest) returns (Empty) {} // 仿冒资产发现
	rpc CounterfeitAssetsCount(CounterfeitAssetDetectRequest) returns (CounterfeitAssetsCountResponse) {} // 资产统计
	rpc CounterfeitAssetsImport(CounterfeitAssetsImportRequest) returns (Empty) {} // 资产导入
	rpc CounterfeitAssetsDetect(CounterfeitAssetDetectRequest) returns (Empty) {} // 仿冒资产探活
	rpc CounterfeitAssetsDeepDetect(CounterfeitAssetListRequest) returns (CounterfeitAssetsSyncResponse) {} // 仿冒资产入账&深度探测
	rpc CounterfeitAssetsRetryScreenshot(CounterfeitAssetListRequest) returns (Empty) {} // 仿冒资产重试截图
	rpc CounterfeitAssetsSync(CounterfeitAssetListRequest) returns (CounterfeitAssetsSyncResponse) {} // 仿冒数据入账威胁资产
	rpc CounterfeitAssetsDownload (CounterfeitAssetDetectRequest) returns (AssetAuditResultDownloadResponse); // 数据下载
	//	API 请求计数统计
	rpc ApiRequestCountList(ApiRequestCountListRequest) returns (ApiRequestCountListResponse) {}
	//	管理后台
	// ICP备案总库
	rpc ManageBeianList(ManageBeianListRequest) returns (ManageBeianListResponse) {}
	rpc ManageBeianCreate(ManageBeianCreateRequest) returns (Empty) {}
	rpc ManageBeianUpdate(ManageBeianCreateRequest) returns (Empty) {}
	rpc ManageIcpAppList(IcpAppListRequest) returns (IcpAppListResponse) {} // icp app列表
	rpc ManageIcpAppCreate(IcpAppListItem) returns (Empty) {} // icp app新建
	rpc ManageIcpAppUpdate(IcpAppListItem) returns (Empty) {} // icp app更新
	// 数字资产总库
	rpc ManageDigitalKeywordInfo(ManageDigitalKeywordUpsertRequest) returns (ManageDigitalKeywordInfoResponse) {} // 关键词任务详情查询
	rpc ManageDigitalKeywordUpsert(ManageDigitalKeywordUpsertRequest) returns (Empty) {} // 任务管理：数字资产新建/更新关键词
	rpc ManageDigitalKeywordList(ManageDigitalKeywordListRequest) returns (ManageDigitalKeywordListResponse) {} // 任务管理： 关键词列表
	rpc ManageDigitalKeywordDelete(ManageDigitalKeywordListRequest) returns (Empty) {} // 任务管理：关键词删除
	// 任务详情：结果列表
	rpc ManageDigitalKeywordResultList(ManageDigitalAssetsResultListRequest) returns (ManageDigitalAssetsResultListResponse) {}
	rpc ManageDigitalAssetsResultCreate(ManageDigitalAssetsResultItem) returns (Empty) {} // 资产管理：新建总库资产记录
	rpc ManageDigitalAssetsResultImport(ManageDigitalAssetsResultImportRequest) returns (Empty) {} // 资产管理：总库数据导入
	rpc ManageDigitalAssetsResultList(ManageDigitalAssetsResultListRequest) returns (ManageDigitalAssetsResultListResponse) {} // 数字资产结果列表
	rpc ManageDigitalAssetsResultUpdate(ManageDigitalAssetsResultItem) returns (Empty) {} // 更新数字资产信息
	rpc ManageDigitalAssetsResultDelete(ManageDigitalAssetsResultListRequest) returns (Empty) {} // 删除数字资产总库信息
	rpc ManageDigitalFilterGroup(ManageDigitalAssetsResultImportRequest) returns (ManageDigitalFilterGroupResponse) {} // 筛选
	// 威胁词库
	rpc BlackKeywordList(BlackKeywordListRequest) returns (BlackKeywordListResponse) {} // 审核-列表
	rpc BlackKeywordUpdate(BlackKeywordUpdateRequest) returns (Empty) {} // 审核-审核
	rpc ManageBlackKeywordTypeList(Empty) returns (BlackKeywordListResponse) {} // 分类-列表
	rpc ManageBlackKeywordTypeCreate(BlackKeywordTypeCreateRequest) returns (Empty) {} // 分类-新建
	rpc ManageBlackKeywordList(BlackKeywordListRequest) returns (BlackKeywordListResponse) {} // 总库-列表
	rpc ManageBlackKeywordCreate(ManageBlackKeywordCreateRequest) returns (Empty) {} // 总库-新建
	rpc ManageBlackKeywordUpdate(ManageBlackKeywordUpdateRequest) returns (Empty) {} // 总库更新
	rpc ManageBlackKeywordDelete(BlackKeywordListRequest) returns (Empty) {} // 总库-删除
	// 资产测绘
	rpc DetectAssetCluesUpdate(DetectAssetCluesUpdateRequest) returns (Empty) {} // 编辑资产线索
	rpc DetectAssetTaskCreate(DetectAssetTaskCreateRequest) returns (DetectAssetTaskCreateResponse) {} // 单位资产测绘-新建任务
	rpc DetectAssetTaskReport(DetectAssetTaskReportRequest) returns (DetectAssetTaskReportResponse) {} // 单位资产测绘-测绘简报
	rpc DetectAssetTaskReportImportAssetDownload(DetectAssetTaskReportRequest) returns (AssetAuditResultDownloadResponse) {} // 导入已知资产下载
	rpc DetectAssetTaskReportClueList(DetectAssetTaskReportClueListRequest) returns (DetectAssetTaskReportClueListResponse) {} // 资产线索列表
	rpc TaskIndex(TaskIndexRequest) returns (TaskIndexResponse) {} // 任务简报列表
	rpc ConfirmAllClues(ConfirmAllCluesRequest) returns (ExpandCluesResponse) {} // 确认所有线索
	rpc ClueBlack(ClueBlackRequest) returns (Empty) {} // 标记线索到黑名单
	rpc PassClue(PassClueRequest) returns (Empty) {} // 审核线索状态
	rpc ImportClues(ImportCluesRequest) returns (Empty) {} // 导入线索
	rpc GetDetectAssetsResultList(DetectAssetsResultListRequest) returns (DetectAssetsResultListResponse) {} // 资产测绘结果列表
	rpc UnsureAssetsList(UnsureAssetsListRequest) returns (UnsureAssetsListResponse) {} // 疑似资产列表
	rpc SurePassetsList(SurePassetsListRequest) returns (PassetsListResponse) {} // 疑似资产列表【IP端口维度】
	rpc PassetsList(PassetsListRequest) returns (PassetsListResponse) {} // 疑似资产列表【IP端口维度】
	rpc IgnorePassetsList(IgnorePassetsListRequest) returns (PassetsListResponse) {} // 忽略资产列表【IP端口维度】
	rpc ThreatenPassetsList(ThreatPassetsListRequest) returns (PassetsListResponse) {} // 威胁资产列表【IP端口维度】
	// IP端口-台账维度导出
	rpc ExportSurePassetsList(IpPortActionRequest) returns (FileExportResponse) {}
	// IP端口-疑似维度导出
	rpc ExportPassetsList(IpPortActionRequest) returns (FileExportResponse) {}
	// IP端口-忽略维度导出
	rpc ExportIgnorePassetsList(IpPortActionRequest) returns (FileExportResponse) {}
	// IP端口-威胁维度导出
	rpc ExportThreatenPassetsList(IpPortActionRequest) returns (FileExportResponse) {}
	// api申请记录入库
	rpc ApplyApiSave(ApplyApiRequest) returns (Empty) {}
	rpc ManageApplyAudit(ApplyAuditRequest) returns (Empty) {}
	rpc ManageApplyList(ApplyListRequest) returns (ApplyListResponse) {}
	// 用户管理
	rpc UserList(UserListRequest) returns (UserListResponse) {} // 用户列表

	// --------------------------------情报中心
	// 情报模块数量统计
	rpc IntelligenceCount(Empty) returns (IntelligenceCountResponse){}
	// 情报中心-威胁情报-列表
	rpc IntelligenceUserThreatList(IntelligenceUserThreatListRequest) returns (IntelligenceUserThreatListResponse) {}
	// 情报中心-威胁情报-一键匹配
	rpc IntelligenceUserThreatMatch(IntelligenceUserThreatMatchRequest) returns (Empty) {}
	// 情报中心-威胁情报-一键匹配-进度查询
	rpc IntelligenceUserThreatMatchProcess(IntelligenceUserThreatMatchProcessRequest) returns (IntelligenceUserThreatMatchProcessResponse) {}
	// 情报中心-其他情报-列表
	rpc IntelligenceOtherUserList(IntelligenceOtherUserListRequest) returns (IntelligenceOtherUserListResponse) {}
	// 情报中心-钓鱼仿冒-列表
	rpc IntelligenceFakeUserList(IntelligenceFakeUserListRequest) returns (IntelligenceFakeUserListResponse) {}
	// 情报中心-热点POC-级别数量
	rpc IntelligenceHotPocCountByLevel(Empty) returns (IntelligenceHotPocCountByLevelResponse){}
	// 情报中心-热点POC-列表
	rpc IntelligenceHotPocUserList(IntelligenceHotPocUserListRequest) returns (IntelligenceHotPocUserListResponse) {}
	// 情报中心-热点POC-高级筛选数据源
	rpc IntelligenceHotPocCondition(IntelligenceHotPocConditionRequest) returns (IntelligenceHotPocConditionResponse) {}
	// 情报中心-热点POC-自定义风险等级
	rpc IntelligenceHotPocUserUpdate(IntelligenceHotPocUserUpdateRequest) returns (Empty) {}
	// 情报中心-热点POC-检测
	rpc IntelligenceHotPocCheck(IntelligenceHotPocCheckRequest) returns (Empty) {}
	// 情报中心-热点POC-详情
	rpc IntelligenceHotPocInfoAsset(IntelligenceHotPocInfoAssetRequest) returns (IntelligenceHotPocInfoAssetResponse) {}
	// 情报中心-热点POC-IP列表
	rpc IntelligenceHotPocAssetList(IntelligenceHotPocAssetListRequest) returns (IntelligenceHotPocAssetListResponse) {}
	// 情报中心-热点POC-IP列表-导出
	rpc IntelligenceHotPocAssetExport(IntelligenceHotPocAssetListRequest) returns (IntelligenceHotPocAssetExportResponse) {}
	// 情报中心-热点漏洞-检测-进度查询
	rpc IntelligenceUserHotPocMatchProcess(IntelligenceUserHotPocMatchProcessRequest) returns (IntelligenceUserHotPocMatchProcessResponse) {}
	// 情报中心-热点漏洞-上传报告
	rpc IntelligenceHotPocUploadReport(IntelligenceHotPocUploadReportRequest) returns (IntelligenceUploadReportResponse){}
	// 情报中心-热点漏洞-下载脱敏报告
	rpc IntelligenceHotPocDownloadMaskedReport(IntelligenceHotPocDownloadReportRequest) returns (IntelligenceDownloadReportResponse){}
	// 情报中心-热点漏洞-下载原始报告
	rpc IntelligenceHotPocDownloadReport(IntelligenceHotPocDownloadReportRequest) returns (IntelligenceDownloadReportResponse){}
	// 情报中心-关联企业列表
	rpc IntelligenceCompanyList(Empty) returns (IntelligenceCompanyListResponse) {}
	// 情报中心-事件专项-数量
	rpc IntelligenceEventCount(Empty) returns (IntelligenceEventCountResponse) {}
	// 情报中心-事件专项-上次登录后新产生的数量
	rpc IntelligenceEventCountAfterLastLogin(IntelligenceEventCountAfterLastLoginRequest) returns (IntelligenceEventCountAfterLastLoginResponse){}
	// 情报中心-事件专项-告警
	rpc IntelligenceEventWarning(IntelligenceEventWarningRequest) returns (IntelligenceEventWarningResponse) {}
	// 情报中心-事件专项-IP数量
	rpc IntelligenceEventIPCount(Empty) returns (IntelligenceEventIPCountResponse){}
	// 情报中心-事件专项-用户列表
	rpc IntelligenceEventListForUser(IntelligenceEventListForUserRequest) returns (IntelligenceEventListResponse) {}
	// 情报中心-事件专项-列表
	rpc IntelligenceEventList(IntelligenceEventListRequest) returns (IntelligenceEventListResponse){}
	// 情报中心-事件专项-高级筛选数据源
	rpc IntelligenceEventCondition(Empty) returns (IntelligenceEventConditionResponse){}
	// 情报中心-事件专项-详情
	rpc IntelligenceEventDetail(IntelligenceEventDetailRequest) returns (IntelligenceEventDetailResponse){}
	// 情报中心-事件专项-所有类别
	rpc IntelligenceEventCategoryList(Empty) returns (IntelligenceEventCategoryListResponse){}
	// 情报中心-事件专项-修改类别
	rpc IntelligenceEventUpdateCategory(IntelligenceEventUpdateCategoryRequest) returns (Empty){}
	// 情报中心-事件专项-上传报告
	rpc IntelligenceEventUploadReport(IntelligenceEventUploadReportRequest) returns (IntelligenceUploadReportResponse){}
	// 情报中心-事件专项-下载脱敏报告
	rpc IntelligenceEventDownloadMaskedReport(IntelligenceEventDownloadReportRequest) returns (IntelligenceDownloadReportResponse){}
	// 情报中心-事件专项-下载原始报告
	rpc IntelligenceEventDownloadReport(IntelligenceEventDownloadReportRequest) returns (IntelligenceDownloadReportResponse){}
	// 情报中心-事件专项-一键匹配
	rpc IntelligenceEventUserMatch (IntelligenceEventUserMatchRequest) returns (Empty) {}
	// 情报中心-事件专项-一键匹配-进度查询
	rpc IntelligenceEventUserMatchProgress (IntelligenceEventUserMatchProgressRequest) returns (IntelligenceEventUserMatchProgressResponse);
	// 情报中心-数据专项-数量
	rpc IntelligenceDataCount(Empty) returns (IntelligenceDataCountResponse){}
	// 情报中心-数据专项-总览列表
	rpc IntelligenceDataSummaryListForUser(IntelligenceDataSummaryListForUserRequest) returns (IntelligenceDataSummaryListResponse){}
	// 情报中心-数据专项-列表
	rpc IntelligenceDataSummaryList(IntelligenceDataSummaryListRequest) returns (IntelligenceDataSummaryListResponse){}
	// 情报中心-数据专项-高级筛选数据源
	rpc IntelligenceDataSummaryCondition(IntelligenceDataSummaryConditionRequest) returns (IntelligenceDataSummaryConditionResponse){}
	// 情报中心-数据专项-用户详情列表
	rpc IntelligenceDataListForUser(IntelligenceDataListForUserRequest) returns (IntelligenceDataListResponse){}
	// 情报中心-数据专项-详情列表
	rpc IntelligenceDataList(IntelligenceDataListRequest) returns (IntelligenceDataListResponse){}
	// 情报中心-数据专项-详情
	rpc IntelligenceDataDetail(IntelligenceDataDetailRequest) returns (IntelligenceData){}
	// 情报中心-数据专项-上传报告
	rpc IntelligenceDataUploadReport(IntelligenceDataUploadReportRequest) returns (IntelligenceUploadReportResponse){}
	// 情报中心-数据专项-下载脱敏报告
	rpc IntelligenceDataDownloadMaskedReport(IntelligenceDataDownloadReportRequest) returns (IntelligenceDownloadReportResponse){}
	// 情报中心-数据专项-下载报告
	rpc IntelligenceDataDownloadReport(IntelligenceDataDownloadReportRequest) returns (IntelligenceDataDownloadReportResponse){}
	// 情报中心-数据专项-一键匹配
	rpc IntelligenceDataUserMatch (IntelligenceDataUserMatchRequest) returns (Empty) {}
	// 情报中心-数据专项-一键匹配-进度查询
	rpc IntelligenceDataUserMatchProgress (IntelligenceDataUserMatchProgressRequest) returns (IntelligenceDataUserMatchProgressResponse);

	// 情报中心-关联情报-列表
	rpc IntelligenceRelatedList(IntelligenceRelatedListRequest) returns (IntelligenceRelatedListResponse) {}
	// 情报中心-关联情报-导出
	rpc IntelligenceRelatedExport(IntelligenceRelatedExportRequest) returns (IntelligenceRelatedExportResponse) {}
	// 情报中心-关联情报-高级搜索条件数据源
	rpc IntelligenceRelatedCondition(IntelligenceRelatedConditionRequest) returns (IntelligenceRelatedConditionResponse) {}

	// 规则引擎-本地化同步规则
	rpc EngineRuleDataSync(Empty) returns (Empty) {}
	// 规则引擎-saas平台暴露规则
	rpc EngineRuleExpose(EngineRuleListRequest) returns (EngineRuleListResponse) {}

	// --------------------------------站内信
	// 站内信-上次登录以来产生的新数据
	rpc WebsiteMessageNewList(WebsiteMessageNewListRequest) returns (WebsiteMessageNewListResponse) {}
	// 站内信-列表展示
	rpc WebsiteMessageList(WebsiteMessageListRequest) returns (WebsiteMessageListResponse) {}
	// 站内信-标记已读
	rpc WebsiteMessageRead(WebsiteMessageUpdateRequest) returns (Empty) {}
	// 站内信-删除
	rpc WebsiteMessageDelete(WebsiteMessageUpdateRequest) returns (Empty) {}
	// 站内信-通知策略展示
	rpc WebsiteMessageNotify(WebsiteMessageNotifyRequest) returns (WebsiteMessageNotifyResponse) {}
	// 站内信-通知策略更新
	rpc WebsiteMessageUpdate(WebsiteMessageNotifyRequest) returns (Empty) {}

	// 手动开通账户-暂时，一所用
	rpc AccountOpenByHand(AccountOpenByHandRequest) returns (AccountOpenByHandResponse) {}
	// 列表
	rpc ListAccountOpenByHand(ListAccountOpenByHandRequest) returns (ListAccountOpenByHandResponse) {}

	// JS深层目录获取
	rpc GetApiAnalyzeResult(ApiAnalyzeResultRequest) returns (ApiAnalyzeResultResponse) {}
	rpc GetApiAnalyzeDetail(ApiAnalyzeResultRequest) returns (ApiAnalyzeDetailResponse) {}
	rpc GetApiAnalyzeTaskList(APiAnalyzeTaskListRequest) returns (ApiAnalyzeUserTaskListResponse) {}
	rpc GetApiAnalyzeUserTaskInfo(ApiAnalyzeUserTaskInfoRequest) returns (ApiAnalyzeUserTaskInfoResponse) {}
	rpc DeleteApiAnalyzeUserTask(ApiAnalyzeDeleteRequest) returns (ApiAnalyzeDeleteResponse) {}
	rpc DeleteApiAnalyzeTask(ApiAnalyzeDeleteRequest) returns (ApiAnalyzeDeleteResponse) {}
	rpc ApiAnalyzeResultExport(ApiAnalyzeResultExportRequest) returns (SystemDetectDownloadResponse) {}

	// 单位资产测绘-单位列表
	rpc GetCompanyDropList(CompanyDropListRequest) returns (CompanyDropListResponse);
	// 获取任务详情
	rpc Info(DetectAssetsInfoRequest) returns (DetectAssetsInfoResponse) {}
	rpc StartSyncJob(StartSyncJobRequest) returns (StartSyncJobResponse) {}

	// 查询子级股权分布信息
	rpc GetCompanyCascadeEquity(GetCompanyCascadeEquityRequest) returns (GetCompanyCascadeEquityResponse);

	// 线索数量统计请求
	rpc GetClueCount(ClueCountRequest) returns (ClueCountResponse);

	// 获取线索列表请求-whole
	rpc WholeClueList(ClueListRequest) returns (ClueListWholeResponse);

	// 获取线索列表请求-nowhole
	rpc NoWholeClueList(ClueListRequest) returns (ClueListPageResponse);

	// 资产测绘-重新获取原始线索
	rpc GetRepeatOriginClues(StartSyncJobRequest) returns (StartSyncJobResponse) {}

	// 资产测绘-资产评估
	rpc AssetsEvaluate (AssetsEvaluateRequest) returns (AssetsEvaluateResponse);

	// 资产测绘-结束任务
	rpc StopAndDeleteTask (DetectTaskStopRequest) returns (AssetsEvaluateResponse);

	// 删除资产测绘任务
	rpc DelDetectAssetsTask (DelDetectAssetsTaskRequest) returns (DelDetectAssetsTaskResponse);

	// 资产测绘-扫描资产
	rpc ScanRecommend (ScanRecommendRequest) returns (ScanRecommendResponse);

	rpc ExpandClues(ExpandCluesRequest) returns (ExpandCluesResponse) {} // 扩展资产线索

	// 资产台账 - 登录入口
	// 登录入口列表
	rpc LoginAssetsList (LoginAssetsListRequest) returns (LoginAssetsListResponse);
	// 登录入口删除
	rpc LoginAssetsDelete (LoginAssetsDeleteRequest) returns (Empty);
	// 更改登录资产状态
	rpc LoginAssetsUpdate (LoginAssetsUpdateRequest) returns (Empty);
	// 登录入口统计状态
	rpc LoginAssetsCountStatus (LoginAssetsCountStatusRequest) returns (LoginAssetsCountStatusResponse);
	// 登录入口导出
	rpc LoginAssetsExport (LoginAssetsDeleteRequest) returns (FileExportResponse);
	// 登录入口编辑
	rpc LoginAssetsEdit (LoginAssetsEditRequest) returns (Empty);
	// 登录入口批量导入
	rpc LoginAssetsImport (LoginAssetsImportRequest) returns (Empty);

	// 根据线索计算fofa资产的数量
	rpc GetFofaAssetsNum(GetFofaAssetsNumRequest) returns (GetFofaAssetsNumResponse) {}
	
	// 获取域名的Whois信息
	rpc WhoisInfo(WhoisInfoRequest) returns (WhoisInfoResponse) {}

	// 推荐资产请求
	rpc RecommendAssets(RecommendAssetsRequest) returns (RecommendAssetsResponse) {}

	// 资产台账 - 证书资产
	// 证书资产列表
	rpc CertAssetList(CertAssetListRequest) returns (CertAssetListResponse) {}
	// 证书资产导出
	rpc CertAssetExport(CertAssetExportRequest) returns (FileExportResponse) {}
	// 证书资产删除
	rpc CertAssetDelete(CertAssetExportRequest) returns (Empty) {}
	// 证书资产详情
	rpc CertAssetDetail(CertAssetDetailRequest) returns (CertAssetItem) {}

	// 创建测绘报告
	rpc CreateReport(CreateReportRequest) returns (Empty) {}

	// 域名总资产列表
	rpc DomainAssetsList(DomainAssetsListRequest) returns (DomainAssetsListResponse) {}

	// 域名总资产导出
	rpc DomainAssetsExport(DomainAssetsExportRequest) returns (DomainAssetsExportResponse) {}

	// 域名资产删除请求
	rpc DomainAssetsDelete(DomainAssetsDeleteRequest) returns (DomainAssetsDeleteResponse) {} // 域名资产删除

	rpc GetTaskList(GetTaskListRequest) returns (GetTaskListResponse) {} // 获取扫描任务列表
	rpc GetTaskDetail(GetTaskDetailRequest) returns (GetTaskDetailResponse) {} // 获取扫描任务详情
	rpc DeleteTask(DeleteTaskRequest) returns (Empty) {} // 删除扫描任务

	// GetTaskResultRequest 获取扫描任务结果请求
	rpc GetTaskResult(GetTaskResultRequest) returns (GetTaskResultResponse) {} // 获取扫描任务结果

	// 获取扫描任务结果筛选条件
	rpc GetTaskResultCondition(GetTaskResultConditionRequest) returns (GetTaskResultConditionResponse) {}

	// 资产台账 - IP资产
	// 列表
	rpc IpAssetList(IpAssetListRequest) returns (IpAssetListResponse) {}
	// 删除
	rpc IpAssetDelete(IpAssetActionRequest) returns (Empty) {}
	// 疑似IP删除
	rpc IpUnsureAssetDelete(IpAssetActionRequest) returns (Empty) {}
	// 忽略IP删除
	rpc IpIgnoreAssetDelete(IpAssetActionRequest) returns (Empty) {}
	// 威胁IP删除
	rpc IpThreatenAssetDelete(IpAssetActionRequest) returns (Empty) {}
	// 导出
	rpc IpAssetExport(IpAssetActionRequest) returns (FileExportResponse) {}
	// 筛选条件
	rpc IpAssetCondition(IpAssetConditionRequest) returns (IpAssetConditionResponse) {}
	// 筛选条件
	rpc IpPortAssetCondition(IpPortAssetConditionRequest) returns (IpPortAssetConditionResponse) {}
	// 确认
	rpc IpAssetConfirm(IpAssetActionRequest) returns (Empty) {}
	// 自定义标签
	rpc IpAssetCustomerTag(IpAssetActionRequest) returns (Empty) {}
	// 资产处置
	rpc IpAssetSetStatus(IpAssetActionRequest) returns (Empty) {}
	// 疑似资产处置
	rpc UnsureAssetSetStatus(IpAssetActionRequest) returns (Empty) {}
	// 忽略资产处置
	rpc IgnoreAssetSetStatus(IpAssetActionRequest) returns (Empty) {}
	// 威胁资产处置
	rpc ThreatenAssetSetStatus(IpAssetActionRequest) returns (Empty) {}

	// 任务结果导出
	rpc TaskResultExport(TaskResultExportRequest) returns (TaskResultExportResponse) {}

	// 资产扫描任务相关
	rpc TaskResultAnalyse(TaskResultAnalyseRequest) returns (TaskResultAnalyseResponse) {} // 扫描结果数据汇总

	// 删除推荐结果请求
	rpc DeleteRecommendResult(DeleteRecommendResultRequest) returns (DeleteRecommendResultResponse) {} // 删除推荐结果
	rpc UpdateAssetsConfidenceLevel(UpdateAssetsConfidenceLevelRequest) returns (UpdateAssetsConfidenceLevelResponse) {} // 更新资产可信度

	// 周期任务列表
	rpc CrontabTaskList(CrontabTaskListRequest) returns (CrontabTaskListResponse) {} // 周期任务列表

	// 忽略资产列表
	rpc IgnoreAssetsList(IgnoreAssetsListRequest) returns (IgnoreAssetsListResponse) {}

	// 威胁资产列表
	rpc ThreatenAssetsList(ThreatenAssetsListRequest) returns (ThreatenAssetsListResponse) {}

	// 统计资产数据维度请求
	rpc TableAssetsStatistics(TableAssetsStatisticsRequest) returns (TableAssetsStatisticsResponse) {}

	// 核对台账模版请求
	rpc CheckBookData(CheckBookDataRequest) returns (CheckBookDataResponse) {}

	// 删除周期任务
  rpc CrontabTaskDelete(CrontabTaskDeleteRequest) returns (CrontabTaskDeleteResponse) {}
  
  // 周期任务开关设置
  rpc CrontabTaskSwitch(CrontabTaskSwitchRequest) returns (CrontabTaskSwitchResponse) {}
  
  // 周期任务详情
  rpc CrontabTaskDetail(CrontabTaskDetailRequest) returns (CrontabTaskDetailResponse) {}
  
  // 创建周期任务
  rpc CrontabTaskCreate(CrontabTaskCreateRequest) returns (CrontabTaskCreateResponse) {}
  rpc CrontabTaskEdit(CrontabTaskEditRequest) returns (CrontabTaskEditResponse) {} // 编辑周期任务

  // 禁扫IP列表请求
  rpc ForbidIpsList(ForbidIpsListRequest) returns (ForbidIpsListResponse) {}

  // 添加禁扫IP请求
  rpc AddForbidIps(AddForbidIpsRequest) returns (AddForbidIpsResponse) {}

  // 删除禁扫IP请求
  rpc DeleteForbidIps(DeleteForbidIpsRequest) returns (DeleteForbidIpsResponse) {}

  // 导出疑似资产 - IP维度
  rpc UnclaimAssetsIPExport(IpAssetActionRequest) returns (FileExportResponse) {}

  // BatchMatchBlackWord 批量匹配未知资产的黄赌毒关键词规则
  rpc BatchMatchBlackWord(IpAssetActionRequest) returns (BatchResponse) {}

  // SetThreatenType 威胁资产列表/IP维度批量修改威胁资产类型
  rpc SetThreatenType(IpAssetActionRequest) returns (Empty) {}

  // 导出威胁资产 - IP维度
  rpc ThreatenAssetsIPExport(IpAssetActionRequest) returns (FileExportResponse) {}

  // 导出忽略资产 - IP维度
  rpc IgnoreAssetsIPExport(IpAssetActionRequest) returns (FileExportResponse) {}

  // 添加标题黑名单词汇
  rpc AddTitleBlackKeyword(TitleBlackKeywordRequest) returns (Empty) {}

  // Fofa更新
  rpc FofaUpdate(FofaUpdateRequest) returns (Empty) {}

  // 批量编辑IP资产的企业名称
  rpc IpAssetUpdateCompanyName(IpAssetActionRequest) returns (Empty) {}

  // 全局台账、疑似数据的导入：IP+端口+协议
  rpc AssetsImportData(AssetsImportDataRequest) returns (Empty) {}

  // 上传域名数据保存
  rpc PushDomainData (PushDomainDataRequest) returns (Empty) {}

  // 创建扫描任务
  rpc CreateScanTask(CreateScanTaskRequest) returns (CreateScanTaskResponse) {}

	// 台账-数据导入并扫描
	rpc AssetAccountImportScan(AssetAccountImportScanRequest) returns (AssetAccountImportScanResponse) {}


	// 创建自定义poc
	rpc CreateCustomPoc(CustomPoc) returns (CreateCustomPocResponse) {}
	// 更新自定义poc
	rpc UpdateCustomPoc(CustomPoc) returns (Empty) {}
	// 删除自定义poc
	rpc DeleteCustomPoc(DeleteCustomPocRequest) returns (Empty) {}
	// 查询自定义poc详情
	rpc GetCustomPocDetail(CustomPocSearch) returns (CustomPoc) {}
	// 获取自定义poc列表
	rpc GetCustomPocList(CustomPocQueryListAndSearch) returns (CustomPocList) {}
	// 发布/下线 自定义poc
	rpc PublishCustomPoc(CustomPocSearch) returns (Empty) {}
	// 风险资产更新
	rpc RiskAssetsUpdate(RiskAssetsUpdateRequest) returns (RiskAssetsUpdateResponse) {}

	// 云端推荐
	rpc CloudRecommend(CloudRecommendRequest) returns (CloudRecommendResponse) {} // 云端推荐

}

message IntelligenceEventConditionResponse{
	repeated string category=1; // 类别
}

message IntelligenceDataSummaryConditionRequest{
	uint64 user_id = 1; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
}

message IntelligenceDataSummaryConditionResponse{
	repeated string special_project_name=1; // 专项名称
	repeated string entities=2; // 实体
}

message IntelligenceRelatedConditionRequest{
	int64 operate_company_id=1; // @tag form:"operate_company_id" validate:"omitempty" zh:"操作公司ID"
	string risk_type=2; // @tag form:"risk_type" validate:"omitempty,oneof=1 2 3 4" zh:"风险类型"
	uint64 user_id = 3; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
}

message IntelligenceRelatedConditionResponse{
	// fieldList := []string{"asset_port", "asset_status", "risk_name", "enterprise_name", "asset_title", "asset_domain", "asset_url", "special_project_name", 
	// "intelligence_type", "intelligence_tags", "intelligence_country", "service_component"}
	
	repeated string asset_port=1; // 资产端口
	repeated string asset_status=2; // 资产状态
	repeated string risk_name=3; // 风险名称
	repeated string enterprise_name=4; // 企业名称
	repeated string asset_title=5; // 资产标题
	repeated string asset_domain=6; // 资产域名
	repeated string asset_url=7; // 资产URL
	repeated string special_project_name=8; // 专项名称
	repeated string intelligence_type=10; // 情报类型
	repeated string intelligence_tags=11; // 情报标签
	repeated string intelligence_country=12; // 情报国家
	repeated string service_component=13; // 受影响的服务或组件
}

message IntelligenceEventWarningRequest{
	uint64 user_id=1; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	int64 operate_company_id=2; // @tag form:"operate_company_id" validate:"omitempty" zh:"操作公司ID"
}

message IntelligenceEventWarningResponse{
	string data_type=1; // 数据类型，1:用户相关,2:登录相关,3:无数据
	uint32 total=2; // 总数
	repeated IntelligenceEventWarning items=3;
	message IntelligenceEventWarning{
		uint64 id=1;
		string user_id=4;
		string risk_name=10;
	}
}

message IntelligenceRelatedExportRequest{
	repeated uint64 id=1; // @tag form:"id" validate:"omitempty,dive,number" zh:"ID"
	repeated string asset_ip=3; // @tag form:"asset_ip" validate:"omitempty" zh:"资产IP"
	repeated string asset_port=4; // @tag form:"asset_port" validate:"omitempty" zh:"资产端口"
	repeated string asset_protocol=5; // @tag form:"asset_protocol" validate:"omitempty" zh:"资产协议"
	string risk_type=6; // @tag form:"risk_type" validate:"omitempty,oneof=1 2 3 4" zh:"风险类型"
	repeated string risk_name=7; // @tag form:"risk_name" validate:"omitempty" zh:"风险名称"
	repeated string asset_url=8; // @tag form:"asset_url" validate:"omitempty" zh:"资产URL"
	repeated string asset_title=9; // @tag form:"asset_title" validate:"omitempty" zh:"资产标题"
	repeated string sort=10; // @tag form:"sort" validate:"omitempty" zh:"排序条件"
	repeated string asset_domain=11; // @tag form:"asset_domain" validate:"omitempty" zh:"资产域名"
	string asset_status=12; // @tag form:"asset_status" validate:"omitempty" zh:"资产状态"
	repeated string intelligence_type=13; // @tag form:"intelligence_type" validate:"omitempty" zh:"情报类型"
	repeated string intelligence_tags=14; // @tag form:"intelligence_tags" validate:"omitempty" zh:"情报标签"
	repeated string intelligence_country=15; // @tag form:"intelligence_country" validate:"omitempty" zh:"情报国家"
	repeated string service_component=16; // @tag form:"service_component" validate:"omitempty" zh:"受影响的服务或组件"
	repeated string found_time=17; // @tag form:"found_time" validate:"omitempty" zh:"第一次检测发现，发现时间"
	repeated string update_time=18; // @tag form:"update_time" validate:"omitempty" zh:"最近一次检测发现，更新时间"
	uint64 user_id=19; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
}

message IntelligenceRelatedExportResponse{
	string file_path=1;
	string file_name=2;
	string url=3;
}

message IntelligenceRelatedListRequest{
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	repeated string asset_ip=3; // @tag form:"asset_ip" validate:"omitempty" zh:"资产IP"
	repeated string asset_port=4; // @tag form:"asset_port" validate:"omitempty" zh:"资产端口"
	repeated string asset_protocol=5; // @tag form:"asset_protocol" validate:"omitempty" zh:"资产协议"
	string risk_type=6; // @tag form:"risk_type" validate:"omitempty,oneof=1 2 3 4" zh:"风险类型"
	repeated string special_project_name=7; // @tag form:"special_project_name" validate:"omitempty" zh:"专项名称"
	repeated string asset_url=8; // @tag form:"asset_url" validate:"omitempty" zh:"资产URL"
	repeated string asset_title=9; // @tag form:"asset_title" validate:"omitempty" zh:"资产标题"
	repeated string sort=10; // @tag form:"sort" validate:"omitempty" zh:"排序条件"
	repeated string asset_domain=11; // @tag form:"asset_domain" validate:"omitempty" zh:"资产域名"
	string asset_status=12; // @tag form:"asset_status" validate:"omitempty" zh:"资产状态"
	repeated string intelligence_type=13; // @tag form:"intelligence_type" validate:"omitempty" zh:"情报类型"
	repeated string intelligence_tags=14; // @tag form:"intelligence_tags" validate:"omitempty" zh:"情报标签"
	repeated string intelligence_country=15; // @tag form:"intelligence_country" validate:"omitempty" zh:"情报国家"
	repeated string service_component=16; // @tag form:"service_component" validate:"omitempty" zh:"受影响的服务或组件"
	repeated string found_time=17; // @tag form:"found_time" validate:"omitempty" zh:"第一次检测发现，发现时间"
	repeated string update_time=18; // @tag form:"update_time" validate:"omitempty" zh:"最近一次检测发现，更新时间"
	string keyword=19; // @tag form:"keyword" validate:"omitempty" zh:"搜索关键字"
	int64 operate_company_id=20; // @tag form:"operate_company_id" validate:"omitempty" zh:"操作公司ID"
	uint64 user_id = 21; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	repeated string risk_name=22; // @tag form:"risk_name" validate:"omitempty" zh:"风险名称"
}

message IntelligenceRelatedListResponse{
	int64 total = 1;
	uint32 current_page = 2;
	uint32 page = 3;
	repeated IntelligenceRelatedItem items=4;
}

message IntelligenceRelatedItem{
	uint64 id=1;
	string intelligence_id=2;
	string enterprise_id=3;
	string user_id=4;
	string asset_id=5;
	string asset_ip=6;
	string asset_port=7;
	string asset_protocol=8;
	string risk_type=9;
	string risk_name=10;
	string asset_url=11;
	string asset_title=12;
	string service_component=13;
	string found_time=14;
	string update_time=15;
	string enterprise_name=16;
	string asset_status=17;
	string intelligence_tags=18;
	string intelligence_country=19;
	string intelligence_type=20;
	string asset_domain=21;
	string special_project_name=22;
}

message IntelligenceDataUserMatchRequest{
	uint64 user_id = 1; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	uint64 operator_id = 2; // @tag form:"operator_id" validate:"omitempty,gt=0,number" zh:"操作者ID"
	repeated uint64 id = 3;  //@tag form:"id" validate:"omitempty,dive,number" zh:"ID"
	string name=4; //tag form:"name" validate:"omitempty" zh:"漏洞名称"
	repeated string update_time = 5; // @tag form:"update_time" validate:"omitempty,dive" zh:"更新时间"
}

message IntelligenceDataUserMatchProgressRequest{
	uint64 user_id = 1; // 用户
}

message IntelligenceDataUserMatchProgressResponse{
	float progress = 1; // 进度
}

message IntelligenceEventUserMatchRequest{
	uint64 user_id = 1; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	uint64 operator_id = 2; // @tag form:"operator_id" validate:"omitempty,gt=0,number" zh:"操作者ID"
	repeated uint64 id = 3;  //@tag form:"id" validate:"omitempty,dive,number" zh:"ID"
	string name=4; //tag form:"name" validate:"omitempty" zh:"漏洞名称"
	string category = 5; // @tag form:"Category" validate:"omitempty" zh:"事件类型"
	repeated string disclosure_time = 6; // @tag form:"disclosure_time" validate:"omitempty,dive" zh:"披露时间"
}

message IntelligenceEventUserMatchProgressRequest{
	uint64 user_id = 1; // 用户
}

message IntelligenceEventUserMatchProgressResponse{
	float progress = 1; // 进度
}

message IntelligenceCountForUserRequest{
	uint64 user_id = 1; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"

}

message IntelligenceCountResponse{
	repeated IntelligenceCountItem items=1;
}

message IntelligenceCountItem{
	string module=1;
	int64 count=2;
}

message IntelligenceHotPocUploadReportRequest {
	uint32 poc_id = 1; // @tag from:"poc_id" validate:"required,gt=0" zh:"漏洞ID"
	string file_name=2; // @tag form:"file_name" validate:"required,endswith=pdf" zh:"文件名"
	bytes file_content=3; // @tag form:"file_content" validate:"required" zh:"文件内容"
	uint64 user_id=4; // @tag form:"user_id" validate:"required" zh:"上传人ID"
}

message IntelligenceDataUploadReportRequest{
	uint32 data_id = 1; // @tag from:"data_id" validate:"required,gt=0" zh:"事件ID"
	string file_name=2; // @tag form:"file_name" validate:"required,endswith=pdf" zh:"文件名"
	bytes file_content=3; // @tag form:"file_content" validate:"required" zh:"文件内容"
	uint64 user_id=4; // @tag form:"user_id" validate:"required" zh:"上传人ID"
}

message IntelligenceEventUploadReportRequest{
	uint32 event_id = 1; // @tag from:"event_id" validate:"required,gt=0" zh:"事件ID"
	string file_name=2; // @tag form:"file_name" validate:"required,endswith=pdf" zh:"文件名"
	bytes file_content=3; // @tag form:"file_content" validate:"required" zh:"文件内容"
	uint64 user_id=4; // @tag form:"user_id" validate:"required" zh:"上传人ID"
}

message IntelligenceUploadReportResponse{
	string masked_report_local_link=1;
}

message IntelligenceDataDownloadReportRequest{
	uint32 data_id = 1; // @tag from:"data_id" validate:"required,gt=0" zh:"专项ID"
}

message IntelligenceDataDownloadReportResponse{
	string file_path=1;
	string file_name=2;
}

message  IntelligenceDataDetailRequest{
  uint32 data_id = 1; // @tag form:"data_id" validate:"required" zh:"数据ID"
}

message IntelligenceDataSummaryListForUserRequest{
	uint64 user_id = 1; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	uint32 page = 2; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 3; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	repeated string special_project_name=4; // @tag form:"special_project_name" validate:"omitempty" zh:"专项名称"
	repeated string entities=5; // @tag form:"entities" validate:"omitempty" zh:"实体名称"
	string keyword=6; // @tag form:"keyword" validate:"omitempty" zh:"搜索关键字"
	repeated string last_update_time=7; // @tag form:"last_update_time" validate:"omitempty" zh:"最后更新时间"
}

message IntelligenceDataSummaryListRequest{
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	repeated string special_project_name=4; // @tag form:"special_project_name" validate:"omitempty" zh:"专项名称"
	repeated string entities=5; // @tag form:"entities" validate:"omitempty" zh:"实体名称"
	string keyword=6; // @tag form:"keyword" validate:"omitempty" zh:"搜索关键字"
	uint64 user_id = 7; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	repeated string last_update_time=8; // @tag form:"last_update_time" validate:"omitempty" zh:"最后更新时间"
}

message IntelligenceDataSummaryListResponse{
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	repeated IntelligenceDataSummary items = 4;
}

message IntelligenceDataListForUserRequest{
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string ip=4; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string ip_related_location=5; // @tag form:"ip_related_location" validate:"omitempty" zh:"IP关联地址"
	string entity=6; // @tag form:"entity" validate:"omitempty" zh:"实体"
	string entity_location=7; // @tag form:"entity_location" validate:"omitempty" zh:"实体关联地址"
	uint32 data_summary_id=8; // @tag form:"data_summary_id" validate:"omitempty" zh:"汇总数据ID"
	uint64 user_id = 9; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
}

message IntelligenceDataListRequest{
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string ip=4; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string ip_related_location=5; // @tag form:"ip_related_location" validate:"omitempty" zh:"IP关联地址"
	string entity=6; // @tag form:"entity" validate:"omitempty" zh:"实体"
	string entity_location=7; // @tag form:"entity_location" validate:"omitempty" zh:"实体关联地址"
	uint32 data_summary_id=8; // @tag form:"data_summary_id" validate:"omitempty" zh:"汇总数据ID"
}

message IntelligenceDataListResponse{
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	repeated IntelligenceData items = 4;
}

message IntelligenceDataSummary{
	uint32 id = 1;
	string special_project_name = 2;
	string entities=3;
	int64 data_volume = 4;
	int32 company_num=5;
	int32 asset_num=6;
	string last_update_time=7;
}

message  IntelligenceData{
	// 主键ID
	uint32 id = 1;
	// 专项名称
	string special_project_name = 2;
	// 事件名称
	string event_name = 3;
	// 专项分类
	string special_project_category = 4;
	// 概述
	string overview = 5;
	// 披露时间
	string disclosure_time = 6;
	// 泄漏源地址
	string leak_source_ip = 7;
	// IP关联地点
	string ip_related_location = 8;
	// 泄露服务/组件
	string leak_service_component = 9;
	// 数据量
	int32 data_volume = 10;
	// 数据内容
	string data_content = 11;
	// 数据所属实体
	string data_entity = 12;
	// 数据所属实体地点
	string data_entity_location = 13;
	// 泄露原因
	string leak_reason = 14;
	// 修复方案
	string fix_solution = 15;
	// 是否存在脱敏报告
	bool has_masked_report = 16;
}

message IntelligenceDownloadReportResponse{
	string file_path=1;
	string file_name=2;
}

message IntelligenceHotPocDownloadReportRequest{
	uint32 poc_id = 1; // @tag from:"poc_id" validate:"required,gt=0" zh:"漏洞ID"	
}

message  IntelligenceEventDownloadReportRequest{
	uint32 event_id = 1; // @tag from:"event_id" validate:"required,gt=0" zh:"事件ID"
}

message IntelligenceEventCategoryListResponse{
	repeated string category=1;
}

message IntelligenceEventUpdateCategoryRequest{
	uint32 event_id = 1; // @tag from:"event_id" validate:"required,gt=0" zh:"事件ID"
	string category = 2; // @tag form:"category" validate:"required,gte=0" zh:"事件类别"
}

message IntelligenceEventDetailRequest{
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	uint32 event_id = 3; // @tag form:"event_id" validate:"required" zh:"事件ID"
	uint64 last_update_at_start=4; // @tag form:"last_update_at_start" validate:"omitempty" zh:"创建时间"
	uint64 last_update_at_end=5; // @tag form:"last_update_at_end" validate:"omitempty" zh:"创建时间"
	string ip=6; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string city=7; // @tag form:"city" validate:"omitempty" zh:"城市"
	string related_device=8; // @tag form:"related_device" validate:"omitempty" zh:"关联设备"
}
message IntelligenceEventDetailResponse{
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	repeated IntelligenceEventDetail items = 4;
}
message IntelligenceEventDetail{
	uint32 event_id = 1;
	string related_device = 2;
	string vulnerability = 3;
	string risk_level = 4;
	string risk_type = 5;
	bool is_vulnerable = 6;
	string first_detected = 7;
	string last_detected = 8;
	string ip_address = 9;
	string port = 10;
	string protocol = 11;
	string country = 12;
	string city = 13;
	string url = 14;
	string case = 15;
	string object = 16;
	bool is_cdn = 17;
	string tags = 18;
	string os = 19;
	string status_code = 20;
	string title = 21;
	string domain = 22;
	string certificate = 23;
	string organization = 24;
	string institution = 25;
	string component = 26;
	string category = 27;
	string icon = 28;
	string fingerprint = 29;
	string asset_count = 30;
	string vuln_asset_cnt = 31;
	string last_updated = 32;
}

message IntelligenceEventListForUserRequest{
	uint64 user_id = 1; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	uint32 page = 2; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 3; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string keyword = 4; // @tag form:"keyword" validate:"omitempty" zh:"搜索关键字"
	repeated string category = 5; // @tag form:"category" validate:"omitempty" zh:"事件类别"
	repeated string disclosure_time=6; // @tag form:"disclosure_time" validate:"omitempty" zh:"时间"
}

message IntelligenceEventListRequest{
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string keyword = 4; // @tag form:"keyword" validate:"omitempty" zh:"搜索关键字"
	repeated string category = 5; // @tag form:"category" validate:"omitempty" zh:"事件类别"
	repeated string disclosure_time=6; // @tag form:"disclosure_time" validate:"omitempty" zh:"时间"
	uint64 user_id = 7; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
}

message IntelligenceEventListResponse{
	int64 total = 1;
	uint32 current_page = 2;
	uint32 page = 3;
  repeated IntelligenceEvent items = 4;
}

message IntelligenceEvent{
	uint32 id = 1;
	string name=2;
	string creation_time=3;
	string tags = 4;
	int64 ip_count = 5;
	string local_link = 6;
	string category = 7;
	string associated_device=8;
	string summary=9;
	string company_list=10;
	uint64 company_num=11;
	uint64 asset_num=12;
	bool has_masked_report = 14;
	string disclosure_time=15;
}

message  IntelligenceEventIPCountResponse{
	int64 all_count = 1; // 所有关联IP数量
	int64 latest_count=2; // 最新事件关联IP数量
}

message IntelligenceEventCountAfterLastLoginRequest{
	uint64 user_id = 1; // 用户
}

message  IntelligenceDataCountResponse{
	  int64 count=1;
}

message	IntelligenceEventCountResponse{
	int64 count=1;
}

message IntelligenceEventCountAfterLastLoginResponse{
	int64 count=1;
}

message IntelligenceCompanyListResponse {
	repeated string name = 1;
}

message IntelligenceHotPocAssetExportResponse {
	string url = 1;
}

message Rule {
	string category = 1;
	string cn_category = 2;
	string cn_company = 3;
	string cn_parent_category = 4;
	string cn_product = 5;
	string company = 6;
	string parent_category = 7;
	string product = 8;
	string softhard = 9;
	string level = 10;
}

message IntelligenceHotPocAssetList {
	string id = 10;
	string ip = 1;
	uint32 port = 2;
	string url = 3;
	string protocol = 4;
	string title = 5;
	string status = 6;
	message HotPocInfo {
			uint64 hot_poc_id = 1;
			string hot_poc_name = 2;
	}
	repeated HotPocInfo hot_pocs = 7;
	repeated Rule rules = 9;
}

message IntelligenceHotPocAssetListResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	repeated IntelligenceHotPocAssetList items = 5;
}

message IntelligenceHotPocAssetListRequest {
	uint64 user_id = 2; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	uint32 page = 3; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 4; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	repeated string id = 10; //@tag form:"id" validate:"omitempty" zh:"ID"
	string ip = 5; //@tag form:"ip" validate:"omitempty" zh:"IP"
	int32 port = 6; //@tag form:"port" validate:"omitempty,number" zh:"端口"
	string url = 7; //@tag form:"url" validate:"omitempty" zh:"URL"
	string title = 8; //@tag form:"title" validate:"omitempty" zh:"标题"
	int32 status = 11; //@tag form:"status" validate:"omitempty,number" zh:"资产状态"
	string hot_poc_name = 9; //@tag form:"hot_poc_name" validate:"omitempty" zh:"漏洞名称"
}

message IntelligenceUserHotPocMatchProcessResponse {
	float process = 1; // 进度
}

message IntelligenceUserHotPocMatchProcessRequest {
	uint64 user_id = 1; // 用户
}

message IntelligenceHotPocInfoAsset {
	string id = 6;
	string ip = 1;
	uint32 port = 2;
	string url = 3;
	string title = 4;
	string status = 5;
}

message IntelligenceHotPocInfoAssetResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	IntelligenceHotPoc poc_info = 5;
	repeated IntelligenceHotPocInfoAsset items = 6;
}

message IntelligenceHotPocInfoAssetRequest {
	uint64 id = 1; // @tag form:"id" validate:"required,gt=0" zh:"POC ID"
	uint64 user_id = 2; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	uint32 page = 3; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 4; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
}

message IntelligenceHotPocCheckRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	uint64 operator_id = 2; // @tag form:"operator_id" validate:"omitempty,gt=0,number" zh:"操作者ID"
	repeated uint64 id = 3;  //@tag form:"id" validate:"omitempty,dive,number" zh:"ID"
	string name = 10; // @tag form:"name" validate:"omitempty" zh:"漏洞名称"
	string cve = 4; // @tag form:"cve" validate:"omitempty" zh:"CEV"
	string cnnvd = 5; // @tag form:"cnnvd" validate:"omitempty" zh:"CNNVD"
	string risk_level = 6; // @tag form:"risk_level" validate:"omitempty" zh:"风险级别"
	repeated string created_at = 7; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 8; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	repeated string found_at = 9; // @tag form:"found_at" validate:"omitempty,dive" zh:"发现时间"
}

message IntelligenceHotPocUserListRequest {
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string name = 3; // @tag form:"name" validate:"omitempty" zh:"漏洞名称"
	string cve = 4; // @tag form:"cve" validate:"omitempty" zh:"CEV"
	string cnnvd = 5; // @tag form:"cnnvd" validate:"omitempty" zh:"CNNVD"
	string risk_level = 6; // @tag form:"risk_level" validate:"omitempty" zh:"风险级别"
	uint64 user_id = 18; // @tag form:"user_id" validate:"required,gt=0,number" zh:"用户ID"
	int64 operator_id = 20; // @tag form:"operator_id" validate:"omitempty,number" zh:"操作者ID"
	repeated string created_at = 7; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 8; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	repeated string found_at = 9; // @tag form:"found_at" validate:"omitempty,dive" zh:"发现时间"
	bool get_es_fields = 10; // @tag form:"get_es_fields" validate:"omitempty" zh:"是否获取es字段"
	repeated string sort=11; // @tag form:"sort" validate:"omitempty" zh:"排序条件"
	repeated string tag=12; // @tag form:"tag" validate:"omitempty,dive" zh:"标签"
}

message IntelligenceHotPocUserUpdateRequest {
	string name = 3; // @tag form:"name" validate:"omitempty" zh:"漏洞名称"
	string cve = 4; // @tag form:"cve" validate:"omitempty" zh:"CEV"
	string cnnvd = 5; // @tag form:"cnnvd" validate:"omitempty" zh:"CNNVD"
	string risk_level = 6; // @tag form:"risk_level" validate:"omitempty" zh:"风险级别"
	repeated string created_at = 7; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 8; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	repeated string found_at = 9; // @tag form:"found_at" validate:"omitempty,dive" zh:"发现时间"
	uint64 user_id = 18; // @tag form:"user_id" validate:"required,gt=0,number" zh:"用户ID"
	int64 operator_id = 20; // @tag form:"operator_id" validate:"omitempty,number" zh:"操作者ID"
	string set_risk_level = 10; // @tag form:"set_risk_level" validate:"omitempty" zh:"风险级别"
	repeated uint64 id = 11; //@tag form:"id" validate:"omitempty,dive,number" zh:"ID"
}

message IntelligenceHotPoc {
	uint64 id = 1;
	string name = 2;
	string cve = 3;
	string cnnvd = 4;
	string impact_range = 5;
	string impact_product = 6;
	string impact_version = 7;
	string solution = 8;
	string introduce = 9;
	uint64 fofa_count = 11;
	string risk_level = 12;
	string created_at = 13;
	string updated_at = 14;
	string found_at = 15;
	int64 risk_count = 16;
	string es_query=17;
	string es_index=18;
	string es_keywords=19;
	string fofa_query=20;
	bool has_masked_report=21;
	repeated string tag=22;
}

message IntelligenceHotPocConditionRequest{
	uint64 user_id = 1; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
}

message IntelligenceHotPocConditionResponse{
	repeated string tag=1;
}

message IntelligenceHotPocUserListResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	repeated IntelligenceHotPoc items = 5;
}

message IntelligenceHotPocCountByLevelResponse{
 repeated IntelligenceHotPocCountInfo items=1;
}

message IntelligenceHotPocCountInfo{
	string level=1;
	int64 count=2;
	float percent=3;
}

message IntelligenceFakeUserListRequest {
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string url = 3; // @tag form:"url" validate:"omitempty" zh:"钓鱼URL"
	string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string country = 5; // @tag form:"country" validate:"omitempty" zh:"国家"
	string target = 6; // @tag form:"target" validate:"omitempty" zh:"仿冒目标"
	string title = 10; // @tag form:"title" validate:"omitempty" zh:"标题"
	string cloud_name = 11; // @tag form:"cloud_name" validate:"omitempty" zh:"云厂商"
	uint32 status = 12; // @tag form:"status" validate:"omitempty" zh:"是否在线"
	string source = 13; // @tag form:"source" validate:"omitempty" zh:"数据来源"
	repeated string created_at = 7; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 8; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	repeated string found_at = 9; // @tag form:"found_at" validate:"omitempty,dive" zh:"发现时间"
	repeated string sort = 14; // @tag form:"sort" validate:"omitempty,dive" zh:"排序规则"
}

message IntelligenceFake {
	uint64 id = 1;
	string url = 2;
	string ip = 3;
	string title = 4;
	string country = 5;
	string target = 6;
	string cloud_name = 7;
	uint32 status = 8;
	string source = 9;
	string found_at = 10;
	string created_at = 13;
	string updated_at = 14;
}

message IntelligenceFakeUserListResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	repeated IntelligenceFake items = 5;
}

message IntelligenceOtherUserListRequest {
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string url = 3; // @tag form:"url" validate:"omitempty" zh:"url"
	string platform = 4; // @tag form:"platform" validate:"omitempty" zh:"平台"
	string keyword = 5; // @tag form:"keyword" validate:"omitempty" zh:"关键词"
	string poster = 6; // @tag form:"poster" validate:"omitempty" zh:"发帖人"
	string title = 7; // @tag form:"title" validate:"omitempty" zh:"标题"
	string sample = 8; // @tag form:"sample" validate:"omitempty" zh:"样本"
	string screenshot = 11; // @tag form:"screenshot" validate:"omitempty" zh:"截图"
	string article_id = 12; // @tag form:"article_id" validate:"omitempty" zh:"文章ID"
	string article_context = 13; // @tag form:"article_context" validate:"omitempty" zh:"文章内容"
	string company = 16; // @tag form:"company" validate:"omitempty" zh:"关联企业"
	uint64 user_id = 18; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	uint64 company_id = 19; // @tag form:"company_id" validate:"omitempty" zh:"企业ID"
	int64 operator_id = 20; // @tag form:"operator_id" validate:"omitempty" zh:"操作者ID"
	repeated string article_created_at = 14; // @tag form:"article_created_at" validate:"omitempty" zh:"收录时间"
	repeated string found_at = 15; // @tag form:"found_at" validate:"omitempty" zh:"发现时间"
	repeated string created_at = 9; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 10; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
}

message IntelligenceOther {
	uint64 id = 16;
	string url = 1;
	string platform = 2;
	string keyword = 3;
	string poster = 4;
	string title = 5;
	string sample = 6;
	string sample_file_name = 17;
	string screenshot = 7;
	string article_id = 8;
	string article_context = 9;
	string article_created_at = 10;
	string found_at = 11;
	string created_at = 12;
	string updated_at = 13;
	string company = 14;
	uint32 is_public = 15;
}

message IntelligenceOtherUserListResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	repeated IntelligenceOther items = 5;
}

message IntelligenceUserThreatMatchProcessRequest {
	uint64 user_id = 1; // 用户
}
message IntelligenceUserThreatMatchProcessResponse {
	float process = 1; // 进度
}

message IntelligenceUserThreatMatchRequest {
	repeated uint64 id = 1; //@tag form:"id" validate:"omitempty,dive,number" zh:"ID"
	uint64 user_id = 2; // 用户
	uint64 operator_id = 3; // 发起人
	string url = 16; // @tag form:"url" validate:"omitempty" zh:"风险URL"
	string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string country = 5; // @tag form:"country" validate:"omitempty" zh:"国家"
	string domain = 6; // @tag form:"domain" validate:"omitempty" zh:"域名"
	string type = 7; // @tag form:"type" validate:"omitempty" zh:"风险类型"
	string tags = 8; // @tag form:"tags" validate:"omitempty" zh:"标签"
	uint32 status = 12; // @tag form:"status" validate:"omitempty" zh:"是否在线"
	uint32 hit = 17; // @tag form:"hit" validate:"omitempty" zh:"是否命中"
	string source = 13; // @tag form:"source" validate:"omitempty" zh:"数据来源"
	repeated string created_at = 9; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 10; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	repeated string found_at = 15; // @tag form:"found_at" validate:"omitempty,dive" zh:"发现时间"
}

message IntelligenceThreat {
	uint64 id = 1;
	string url = 2;
	string ip = 3;
	string domain = 4;
	string type = 5;
	string country = 6;
	string tags = 7;
	uint32 status = 9;
	uint32 hit = 10;
	string source = 11;
	string found_at = 12;
	string created_at = 13;
	string updated_at = 14;
}

message IntelligenceUserThreatListResponse {
	int64 total = 1;
	int64 current_page = 2;
	uint32 page = 3;
	uint32 per_page = 4;
	repeated IntelligenceThreat items = 5;
}

message IntelligenceUserThreatListRequest {
	uint32 page = 1; //@tag form:"page" validate:"required,gt=0" zh:"页数"
	uint32 per_page = 2; //@tag form:"per_page" validate:"required,gt=0" zh:"条数"
	string url = 3; // @tag form:"url" validate:"omitempty" zh:"风险URL"
	string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP"
	string country = 5; // @tag form:"country" validate:"omitempty" zh:"国家"
	string domain = 6; // @tag form:"domain" validate:"omitempty" zh:"域名"
	string type = 7; // @tag form:"type" validate:"omitempty" zh:"风险类型"
	string tags = 8; // @tag form:"tags" validate:"omitempty" zh:"标签"
	uint32 status = 12; // @tag form:"status" validate:"omitempty" zh:"是否在线"
	uint32 hit = 16; // @tag form:"hit" validate:"omitempty" zh:"是否命中"
	string source = 13; // @tag form:"source" validate:"omitempty" zh:"数据来源"
	uint64 user_id = 17; // @tag form:"user_id" validate:"required,gt=0" zh:"用户ID"
	repeated string created_at = 9; // @tag form:"created_at" validate:"omitempty,dive" zh:"创建时间"
	repeated string updated_at = 10; // @tag form:"updated_at" validate:"omitempty,dive" zh:"更新时间"
	repeated string found_at = 15; // @tag form:"found_at" validate:"omitempty,dive" zh:"发现时间"
}

message Empty {}

message AssetAuditTaskCreateRequest {
	int64 type = 1; // 核查任务类型
	uint64 user_id = 2; // 任务归属用户
	uint64 operator_id = 3; // 发起人
	string file_path = 4; // 文件路径
	uint64 company_id = 5; // 企业ID
}

message AssetAuditTaskCreateResponse {
	uint64 task_id = 1; // 任务id
}

message AssetAuditTaskListRequest {
	int64 audit_type = 1;
	int64 date_after = 2;
	int64 date_before = 3;
	int64 page = 4;
	int64 size = 5;
	int64 user_id = 6;
	int64 operator_id = 7;
	string user_name = 8;
}

message AssetAuditTaskListResponse {
	message BaseInfo {
		uint64 id = 1;
		int64 status = 2;
		int64 import_assets = 3;
		int64 not_included_assets = 4; // 未纳入管理资产
		int64 included_assets = 5; // 已纳入管理资产
		int64 port_assets = 6; // 端口数据
		int64 system_find = 7; // 系统发现数据
		int64 data_table = 8; // 数据总表
		uint64 user_id = 9; // 用户（名称）
		int64 operator_id = 10; // 发起人ID
		string operator = 11; // 发起人(名称)
		string date = 12; // 日期(YYYY-MM-DD HH24:mm:ss)
		string file_path = 13; // 文件路径
	}
	repeated BaseInfo items = 1;
	int64 total = 2;
	int64 current_page = 3;
	int64 per_page = 4;
}

message AssetAuditResultDownloadRequest {
	uint64 user_id = 1;
	uint64 task_id = 2;
}

message AssetAuditResultDownloadResponse {
	string file_path = 1;
}

message AssetAuditTaskDeleteRequest {
	uint64 user_id = 1;
	repeated uint64 task_ids = 2;
	int64 operator_id = 3; // 操作人id
	int64 date_after = 4;
	int64 date_before = 5;
	string user_name = 6; // 发起人模糊匹配
}

message StartAssetsStatusDetectRequest {
	uint64 current_user_id = 1;
	uint64 company_user_id = 2;
	string file = 3;
}

message StartAssetsStatusDetectResponse {
	uint64 task_id = 1;
	repeated string ips = 2;
}

message StartUrlStatusDetectRequest {
	uint64 current_user_id = 1;
	uint64 company_user_id = 2;
	string file = 3;
}

message StartUrlStatusDetectResponse {
	uint64 task_id = 1;
	repeated string ips = 2;
}

message GetAssetsStatusDetectListRequest {
	uint64 user_id = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	uint64 task_id = 4;
	repeated string created_at_range = 5;
	string user_name = 6;
	bool get_file_path=7;
}

message GetUrlStatusDetectListRequest {
	uint64 user_id = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	uint64 task_id = 4;
	repeated string created_at_range = 5;
	string user_name = 6;
	bool get_file_path=7;
}

message GetAssetsStatusDetectListResponse {
	int64 total = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	repeated GetAssetsStatusDetectList items = 4;
}

message GetUrlStatusDetectListResponse {
	int64 total = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	repeated GetUrlStatusDetectList items = 4;
}

message GetUrlStatusDetectList {
	int64 import_assets = 1; // 总数据
	int64 processed_data = 2; // 被处理过的数据
	int64 status = 3; // 处理状态 1:进行中, 2:完成 3 err
	int64 online_assets = 4; // 在线资产
	int64 unonline_assets = 5 ;// 离线资产
	string operator = 6;// 发起人
	uint64 user_id = 7; //
	string file_path = 8;// 在线资产下载路径
	uint64 id = 10;
	string date = 11;
}

message GetAssetsStatusDetectList {
	int64 import_assets = 1; // 总数据
	int64 processed_data = 2; // 被处理过的数据
	int64 status = 3; // 处理状态 1:进行中, 2:完成 3 err
	int64 online_assets = 4; // 在线资产
	int64 unonline_assets = 5 ;// 离线资产
	string operator = 6;// 发起人
	uint64 user_id = 7; //
	string file_path = 8;// 在线资产下载路径
	uint64 id = 10;
	string date = 11;
	string task_type = 12; // 任务类型 1/资产状态检测任务 2/url状态检查任务
}

message GetAssetsStatusDetectInfoRequest {
	int32 online_status = 1;
	uint64 task_id = 2;
	int64 current_page = 3;
	int64 per_page = 4;
	uint64 user_id = 5;
}

message GetUrlStatusDetectInfoRequest {
	int32 online_status = 1;
	uint64 task_id = 2;
	int64 current_page = 3;
	int64 per_page = 4;
	uint64 user_id = 5;
}

message GetAssetsStatusDetectInfoResponse {
	int64 total = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	repeated GetAssetsStatusDetectInfo items = 4;
}

message GetUrlStatusDetectInfoResponse {
	int64 total = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	int64 online_num = 4;
	int64 offline_num = 5;
	repeated GetUrlStatusDetectInfo items = 6;
}

message GetUrlStatusDetectInfo {
	string ip = 1;
	string port = 2;
	string url = 3;
	string protocol = 4;
	int64  online_state = 5;
	string title = 6;
	string status_code = 7;
	string created_at = 8;
}

message GetAssetsStatusDetectInfo {
	string ip = 1;
	int64 port = 2;
}

message AssetAuditTaskResultListRequest {
	uint64 task_id = 1;
	uint64 user_id = 2;
	uint64 is_included = 3;
	int64 page = 4;
	int64 size = 5;
	uint64 result_type = 6; // 核查结果类型: 1核查结果 2系统发现 3数据总表
}

message AssetAuditSyncAssetsRequest {
	uint64 task_id = 1;
	uint64 user_id = 2;
	uint64 company_id = 3;
	uint64 operator_id = 4;
}

message AssetAuditSyncAssetsResponse {
	int64 assets = 1; // 本次入台账新增了多少
	int64 limit = 2; // 本次入账超出多少限制
}

message AssetAuditTaskResultListResponse {
	message ItemUnit {
		message PortUnit {
			string system_name = 1;
			int64 port = 2;
			string protocol = 3;
			string domain = 4;
			string url = 5;
			string component = 6; // 组件
			string status_code = 7; // 状态码
			int64 is_included = 8; // 资产标签
		}
		string ip = 1;
		repeated PortUnit ports = 2;
	}
	uint64 task_id = 1;
	repeated ItemUnit items = 2;
	int64  total = 3;
	int64 current_page = 4;
	int64 per_page = 5;
	int64 ip = 6; // IP数量
	int64 port = 7; // 端口数量
	int64 included = 8; // 纳入管理
	int64 not_included = 9; // 未纳入管理
	int64 new_port = 10; // 新增端口资产
 }

message DelAssetsStatusDetectTaskRequest {
	repeated uint64 task_ids = 1;
	uint64 user_id = 2;
	repeated string created_at_range = 5;
}

message DelUrlStatusDetectTaskRequest {
	repeated uint64 task_ids = 1;
	uint64 user_id = 2;
	repeated string created_at_range = 5;
}

message AssetAuditTaskInfoRequest {
	uint64 user_id = 1;
	uint64 task_id = 2;
	uint64 status = 3;
	bool first_task_doing = 4; // 获取第一个正在进行的任务
}

message AssetAuditTaskInfoResponse {
	uint64 id = 1;
	uint64 audit_type = 2;
	uint64 status = 3;
	uint64 import_assets_total = 4;
	uint64 assets_not_included = 5;
	uint64 assets_included = 6;
	string export_path = 7;
}

message HasLoggedInRequest {
	uint64 user_id = 1;
}

message HasLoggedInResponse {
	bool has = 1;
}

message PublicNoticeAddRequest {
	string notice = 1;
	string up_at_start = 2;
	string up_at_end = 3;
}

message PublicNoticeAddResponse {
	uint64 id = 1;
}

message PublicNoticeSaveRequest {
	string notice = 1;
	string up_at_start = 2;
	string up_at_end = 3;
	uint64 id = 4;
}

message PublicNoticeListRequest {
	int64 current_page = 1;
	int64 per_page = 2;
}

message PublicNoticeListResponse {
	message BaseInfo {
		string notice = 1;
		string up_at_start = 2;
		string up_at_end = 3;
		uint64 id = 4;
		string created_at = 5;
	}
	repeated BaseInfo items = 1;
	int64 total = 2;
	int64 current_page = 3;
	int64 per_page = 4;
}

message FindLatestNoticeResponse {
	string notice = 1;
	string up_at_start = 2;
	string up_at_end = 3;
}

message PublicNoticeDelRequest {
	uint64 id = 1;
}

message ScanTaskOverviewRequest {
	uint64 user_id = 1;
	uint64 company_id = 2;
}

message	ScanTaskOverviewResponse {
	int64 task = 1;
	int64 ip = 2;
	int64 rules = 3;
}

message AssetRecommendationRequst {
	string user_id = 1;
}

message AssetRecommendationResponse {
	int64 task = 1;
}

message TaskOverviewCountRequest {
	int64 user_id = 1;
}

message TaskOverviewCountResponse {
	int64 total = 1;
	int64 doing = 2;
	int64 wait = 3;
}

message TaskOverviewCountByCategoryRequest {
	int64 user_id = 1;
	int64 count_type = 2; // 统计类型：1周、2月、3季
}

message TaskOverviewCountByCategoryResponse {
	message AssetRecommendation { // 单位资产测绘/云端资产推荐
		int64 total = 1;
		int64 account_assets = 2; // 资产台账
		int64 suspected_assets = 3; // 疑似资产
		int64 threat_assets = 4; // 威胁资产
	}
	message ScanTask {
		int64 total = 1;
		int64 ip = 2; // ip
		int64 component = 3; // 组件
	}
	message AssetAudit {
		int64 total = 1;
		int64 included_assets = 2;
		int64 not_included_assets = 3;
	}
	message AssetStatus {
		int64 total = 1;
		int64 online_assets = 2;
		int64 offline_assets = 3;
	}
	message DomainFind {
		int64 total = 1;
		int64 enum_type = 2; // 枚举模式
		int64 verify_type = 3; // 验证模式
	}

	AssetRecommendation recommend_record = 1; // 云端资产推荐
	AssetRecommendation detect_asset_task = 2; // 单位资产测绘
	ScanTask scan_task = 3; // 资产扫描任务
	AssetAudit asset_audit = 4; // 资产核查任务
	AssetStatus asset_status = 5; // 资产状态监测
	DomainFind domain_find = 6; // 域名发现任务v_task_view
}

message IpAssetProfileRequest {
	string ip = 1;
	uint64 user_id = 2;
}

message IpAssetProfileResponse {
	message Unit {
		string port = 1;
		string protocol = 2;
		repeated Layer port_layer = 3;
	}
	message Layer {
		string level = 1; // 层
		int64 count = 2; // 组件统计结果
		repeated Component components = 3; // 组件名
	}
	message Component {
		string category = 1;
		string cn_category = 2;
		string cn_company = 3;
		string cn_parent_category = 4;
		string cn_product = 5;
		string company = 6;
		string parent_category = 7;
		string product = 8;
		string softhard = 9;
		string level = 10;
	}
	string ip = 1;
	repeated Unit items = 2;
}

message	AssetsOverviewRuleCountRequest {
	uint64 user_id = 1;
}

message AssetsOverviewRuleCountResponse {
	message ip_unit {
		string ip = 1;
		string time = 2;
	}
	message BaseInfo {
		string name = 1;
		int64 count = 2;
		repeated ip_unit ips = 3;
	}
	repeated BaseInfo items = 1;
}

message AssetsOverviewDigitalAssetsRequest {
	uint64 user_id = 1;
}

message AssetsOverviewDigitalAssetsResponse {
	message BaseInfo {
		uint64 id = 1;
		uint64 user_id = 2;
		uint64 company_id =3;
		uint64 operator_id=4;
		int64 type=5;
		string name=6;
		string account=7;
		string url=8;
		string img=9;
		string keyword=10;
		string owner=11;
		string logo = 12;
		string first_found_time=13;
		string lastest_found_time=14;
		int32  status=15;
		string created_at=16;
		string updated_at=17;
	}
	repeated BaseInfo items = 1;
}

message ComparedLastMonthRequest {
	uint64 user_id = 1;
}

message ComparedLastMonthResponse {
	double ledger =1;// 台账
	double digital=2;// 数字
	double doubt=3;// 疑似
	double threaten=4;// 威胁
	ComparedLastMonthResponse_LoginInfo login = 5;
	ComparedLastMonthResponse_DomainInfo domain = 6;
	ComparedLastMonthResponse_CertInfo cert = 7;
}

message ComparedLastMonthResponse_LoginInfo {
	int64 total = 1;
	int64	to_confirmed = 2;// 待确认
	int64	confirmed=3;// 已确认
	int64	ignore=4;// 忽略
	double rate=5;
}

message ComparedLastMonthResponse_DomainInfo {
	int64 total = 1;
	int64	father = 2;// 主域名
	int64	child=3;// 子域名
	double rate=4;
}

message ComparedLastMonthResponse_CertInfo {
	int64 total = 1;
	int64 valid = 2;
	int64 notValid = 3;
	double rate = 5;
}

message AssetsOverviewAssetsDynamicCountRequest{
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	repeated string created_at_range = 2; // @tag form:"created_at_range" validate:"omitempty,dive" zh:"创建时间"
}

message AssetsOverviewAssetsDynamicRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	repeated string created_at_range = 2; // @tag form:"created_at_range" validate:"omitempty,dive" zh:"创建时间"
	bool has_threat_data = 3; // @tag form:"has_threat_data" validate:"omitempty" zh:"是否有威胁数据"
}

message AssetsOverviewAssetsDynamicCountResponse{
	map<string, int64> items = 2;
}

message AssetsOverviewAssetsDynamicResponse {
	repeated AssetsOverviewAssetsDynamicBaseInfo items = 1;
}

message AssetsOverviewAssetsDynamicBaseInfo {
	int32 status = 1;// 1-增加 2-删除
	string ip = 2;
	int32 data_type = 3; // 1-rule_tags 2-port_list
	repeated string data = 4;
	string time = 5;
	string id = 6;
}

message IpAssetIpVulnsRequest {
	string ip = 1;
	int64 user_id = 2;
}

message IpAssetIpVulnsResponse {
	message VulnInfo {
		string cve_id = 1; // CVE编号
		string name = 2; // 漏洞名称
		string cvss_version = 3; // cvss版本
		string level = 4; // 风险等级: LOW, MEDIUM, HIGH
		double score = 5; // 得分
		string description = 6; // 漏洞描述
		string published_date = 7; // 发布日期
		string ip = 8; // ip
		string port = 9; // 端口
		string find_date = 10; // 发现日期
	}
	repeated VulnInfo items = 1;
}

message SystemDetectCreateRequest {
	uint64 user_id = 1;
	uint64 operator_id = 2;
	string file_path = 3;
}

message SystemDetectListRequest {
	uint64 user_id = 1;
	string ip = 2;
	repeated string port = 3;
	string address = 4;
	string system_name = 5;
	string domain = 6;
	repeated string protocol = 7;
	int64 system_status = 8; // @tag validate:"omitempty,oneof=1 2" zh:"系统状态"`
	repeated int64 source = 9;
	string belongs = 10;
	int64 page = 11; // 请求页
	int64 per_page = 12; // 页大小
	string keyword = 13; // 关键字
	repeated string created_at = 14;
	int64 created_at_pre = 15; // 创建时间-前
	int64 created_at_next = 16; // 创建时间-后
	repeated string updated_at = 17;
	int64 updated_at_pre = 18; // 更新时间-前
	int64 updated_at_next = 19;  // 更新时间-后
	int64 operate_company_id = 20; // 操作企业
	uint64 operator_id = 21;
	repeated int64 ids = 22; // 勾选的批量资产
	bool redetect = 23; // 是否重新探测，下载功能用
	uint64 website_message_id = 24; // 站内信详情表的id-website_message
	int64 status = 25; //url业务系统资产，url业务系统资产的状态  1/确认 2/忽略 3/待确认
	int64 set_status = 26; //url业务系统资产，要设置成url业务系统资产的状态  1/确认 2/忽略
	repeated string last_live_at = 27;
	int64 last_live_at_pre = 28; // 最后一次存活时间-前
	int64 last_live_at_next = 29; // 最后一次存活时间-后
	string status_code = 30;
	repeated string top_domain = 31;
}

message SystemDetectCondition {
	repeated string port = 1;
	repeated string protocol = 2;
	repeated int64 source = 3;
	repeated string status_code = 4;
	repeated string top_domain = 5;
}

message SystemDetectListResponse {
	message ListUnit {
		string system_name = 1; // 系统名称
		string address = 2; // 访问地址
		string ip = 3;
		string port = 4;
		string domain = 5; // 域名
		string protocol = 6; // 协议
		string belongs = 7; // 归属
		string created_at = 8; // 导入时间
		string updated_at = 9; // 更新时间
		string note = 10; // 备注
		uint64 id = 11;
		int64 system_status = 12; // 系统状态
		int64 source = 13; // 来源
		int64 status = 14; // url业务系统资产，url业务系统资产的状态 0/待确认 1/确认 2/忽略
		string last_live_at = 15; // 上次存活时间
		string status_code = 16;
		string top_domain = 17;
	}
	int64 total = 1;
	int64 page = 2;
	int64 per_page = 3;
	repeated ListUnit items = 4;
	SystemDetectCondition condition = 5;
}

message SystemDetectUpdateRequest {
	string system_name = 1;
	string address = 2;
	string ip = 3; //@tag validate:"required,ip" zh:"ip"
	string port = 4; //@tag validate:"required,port" zh:"端口"
	string domain = 5;
	string protocol = 6;
	string belongs = 7;
	string note = 8; // 备注
	uint64 id = 9; //@tag validate:"required" zh:"记录"
	uint64 user_id = 10;
	uint64 operator_id = 11; // 操作人ID
}

message SystemDetectDownloadResponse {
	string path = 1;
}

message SystemDetectProgressRequest {
	uint64 user_id = 1;
}

message SystemDetectProgressResponse {
	float progress = 1;
	int64 stauts = 2;
	string file_path = 3; // 下载文件路径
	int64 type = 4; // 进度类型: 1 ,2
}

message UserTokenListRequest {
	int64 current_page = 1;
	int64 per_page = 2;
}

message SetFirstTaskNoticeRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"required,number" zh:"用户ID"
}

message SetFirstTaskNoticeResponse {
	bool success = 1;
	string message = 2;
}

message UserTokenListResponse {
	message BaseInfo {
		uint64 id = 1;
		string name = 2;
		string email = 3;
		string level = 4;
		string mobile = 5;
		string token = 6;  // 登录的token
	}
	repeated BaseInfo items = 1;
	int64 total = 2;
	int64 current_page = 3;
	int64 per_page = 4;
}

message DlpTaskResultListRequest {
	uint64 user_id = 1;
	int64 operate_company_id = 2;
	int64 page = 3; // @tag validate:"required,number,gt=0" zh:"页"
	int64 per_page = 4; // @tag validate:"required,number,gt=0,lte=100" zh:"页大小"
	int64 source_type = 5; // @tag validate:"required,oneof=1 2 3 4 5 6 7 8 9 10 11 12 13" zh:"数据泄露来源"
	string search = 6;
	string name = 7;
	string url = 8;
	string keyword = 9;
	string language = 10;
	repeated string created_at = 11; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" zh:"日期范围"
	repeated string updated_at = 12; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" zh:"日期范围"
}

message DlpTaskResultListItem {
	uint64 id = 1;
	int64 source_type = 2;
	repeated string keywords = 3;
	string name = 4;
	string url = 5;
	string created_at = 6;
	string updated_at = 7;
	string language = 8;
	string code_spippet = 9;
}

message DlpTaskResultListResponse {
	int64 total = 1;
	int64 page = 2;
	int64 per_page = 3;
	repeated DlpTaskResultListItem items = 4;
}

message DlpResultUpdateRequest {
	uint64 id = 1; // @tag validate:"required,number" zh:"记录"
	uint64 source_type = 2; // @tag validate:"required,oneof=1 2 3 4 5 6 7 8 9 10 11 12 13" zh:"数据泄露来源"
	string name = 3; // @tag validate:"required" zh:"名称"
	string url = 4; // @tag validate:"required" zh:"地址"
	string language = 5;
}

message DlpTaskAggRequest {
	uint64 source_type = 1; // @tag form:"source_type" validate:"required,oneof=1 2 3 4 5 6 7" zh:"数据泄露来源"
}

message DlpTaskAggResponse {
	repeated string keywords = 1;
}

message GitHubCodeRequest{
	repeated string keyword = 1;
	uint64 max_total = 2;
	uint64 force = 3; // 是否忽略7天更新, 1是0否
	uint64 task_id = 4; // 任务ID
}

message GitHubTaskInfoResponse{
	int64 task_id = 1; // 任务ID
	repeated string keywords = 2;
	int64 status = 3; // 任务状态
	float progress = 4; // 任务当前进度
}

message GitHubCode {
	string repo_url = 1;
	string repo_desc = 2;
	string repo_name = 3;
	string code_url = 4;
	string screenshot = 5;
	string sha = 6;
	string language = 7;
	string code_snippet = 8;
}

message GitHubCodeResponse{
	repeated GitHubCode items = 1;
}

message BaiduLibraryResponse {
	message item {
		uint64 id = 1;
		string address = 2;
		string title = 3;
		string url = 4;
		string screenshot = 5;
	}
	repeated item items = 1;
}

message DocinResultResponse {
	message item {
		uint64 id = 1;
		string title = 2;
		string url = 3;
		string screenshot = 4;
	}
	repeated item items = 1;
}

message Wangpan56ResultResponse {
	message item {
		uint64 id = 1;
		string origin_url = 2;
		string file_name = 3;
		string file_url = 4;
		string screenshot = 5;
	}
	repeated item items = 1;
}

message PortIndexRequest {
	repeated uint64 protocol_id = 1;//@tag form:"protocol_id"
	repeated uint64 port_group_id = 2;//@tag form:"port_group_id"
	string  keyword = 3;//@tag form:"keyword"
	uint64  user_id = 4;//@tag form:"user_id"
	uint64  operate_company_id = 5;//@tag form:"operate_company_id"
	uint32  size = 6;//@tag form:"size"
	uint32  page = 7;//@tag form:"page"
	uint32  per_page = 8;//@tag form:"per_page"
}

message PortInfo {
	uint64 id = 1;
	string key = 2;
	uint64 port = 3;
	string title = 4;
	string protocols = 5;
	string groups = 6;
	string status = 7;
	uint32 source = 8;
	string created_at = 9;
	string updated_at = 10;
}

message PortIndexResponse {
	int64 total = 1;
	int64 size = 2;
	int64 page = 3;
	repeated PortInfo items = 4;
}

message PortListRequest {
	uint64  operate_company_id = 1;//@tag form:"operate_company_id"
	uint64  user_id = 2;//@tag form:"user_id"
}

message PortListInfo {
	uint64 id = 1;
	uint64 port = 2;
}

message PortListResponse {
	repeated PortListInfo items = 1;
}

message PortAddRequest {
	uint64  operate_company_id = 1;// @tag validate:"omitempty,number" zh:"operate_company_id"
	repeated uint64 port_groups = 2;//@tag validate:"required,array" zh:"端口分组ID"
	repeated uint64 ports = 3;//@tag validate:"required,array" zh:"端口ID"
	repeated uint64 protocols = 4;//@tag validate:"required,array" zh:"协议ID"
	uint64  user_id = 5;
}

message PortAddResponse {
}

message PortEditRequest {
	uint64  operate_company_id = 1;// @tag validate:"omitempty,number" zh:"operate_company_id"
	repeated uint64 port_groups = 2;//@tag validate:"required,array" zh:"端口分组ID"
	repeated uint64 ports = 3;//@tag validate:"required,array" zh:"端口ID"
	repeated uint64 protocols = 4;//@tag validate:"required,array" zh:"协议ID"
	uint64  user_id = 5;
	uint64 id = 6;//@tag validate:"required,number" zh:"端口ID"
}

message PortEditResponse {

}

message PortDelRequest {
	repeated uint64 ids = 1;//@tag validate:"required,array" zh:"端口ID"
	string  keyword = 2;//@tag validate:"omitempty,string" zh:"keyword"
	uint64  operate_company_id = 3;// @tag validate:"omitempty,number" zh:"operate_company_id"
	uint64  user_id = 5;
}

message PortDelResponse {

}

message PortUpdateStatusRequest {
	repeated uint64 ids = 1;//@tag validate:"required,array" zh:"端口ID"
	string  keyword = 2;//@tag validate:"omitempty,string" zh:"keyword"
	uint64  operate_company_id = 3;// @tag validate:"omitempty,number" zh:"operate_company_id"
	repeated uint64 port_group_id = 4;//@tag validate:"required,array" zh:"端口分组ID"
	repeated uint64 protocol_id = 5;//@tag validate:"required,array" zh:"协议ID"
	uint32 status = 6;//@tag validate:"required,number,oneof=0 1" zh:"端口更新状态"
	uint64  user_id = 7;
}

message PortUpdateStatusResponse {
}

message PortProtocolIndexRequest {
	string  keyword = 1;//@tag validate:"omitempty,string" zh:"keyword"
}

message PortProtocolListInfo {
	uint64 id = 1;
	string protocol = 2;
	uint32 type =3;
	string created_at = 4;
	string updated_at = 5;
}
message PortProtocolIndexResponse {
	repeated PortProtocolListInfo items = 1;
}

message PortDetailRequest {
	uint64 id = 1;// @tag validate:"required,number" zh:"端口ID"
	uint64  operate_company_id = 2;
	uint64  user_id = 3;
}

message PivotInfo{
	uint64 port_id = 1;
	uint64 port_protocol_id = 2;
}

message ProtocolInfo{
	uint64 id = 1;
	string protocol = 2;
	PivotInfo pivot = 3;
}

message PortDetailResponse {
	uint64 id = 1;
	uint64 port = 2;
	repeated ProtocolInfo protocols= 5;
	repeated uint64 groups = 4;
}

message PortGroupIndexRequest {
	string  keyword = 1;//@tag validate:"omitempty,string" zh:"keyword"
	uint64  user_id = 2;
	uint64  operate_company_id = 3;//@tag form:"operate_company_id"
	uint32  size = 4;//@tag form:"size"
	uint32  page = 5;//@tag form:"page"
	uint32  per_page = 6;//@tag form:"per_page"
}

message PortGroupInfo{
	uint64 id = 1;
	uint64 user_id = 2;
	uint64 company_id = 3;
	string name =4;
	int32 can_del = 5;
	string created_at = 6;
	string updated_at = 7;
	string port = 8;
	uint64 count_ports = 9;
	repeated uint64 portlist = 10;
}

message PortGroupIndexResponse {
	int64 total = 1;
	int64 size = 2;
	int64 page = 3;
	repeated PortGroupInfo items = 4;
}

message PortGroupAddRequest {
	uint64  operate_company_id = 1;// @tag validate:"omitempty,number" zh:"operate_company_id"
	string name = 2;//@tag validate:"required,string" zh:"端口分组名称"
	repeated uint64 ports = 3;//@tag validate:"required,array" zh:"端口ID"
	uint64  user_id = 5;
}

message PortGroupAddResponse {}

message PortGroupDelRequest {
	repeated uint64 id = 1;//@tag validate:"required,array" zh:"端口分组ID"
	string  keyword = 2;//@tag validate:"omitempty,string" zh:"keyword"
	uint64  operate_company_id = 3;// @tag validate:"omitempty,number" zh:"operate_company_id"
	uint64  user_id = 5;
}

message PortGroupDelResponse {}

message PortGroupListRequest {
	uint64  operate_company_id = 1;//@tag form:"operate_company_id"
	uint64  user_id = 2;//@tag form:"user_id"
	string  keyword = 3;//@tag form:"keyword"
}

message PortGroupListInfo{
	uint64 id = 1;
	string name = 2;
}

message PortGroupListResponse {
	repeated PortGroupListInfo items = 1;
}

message PortGroupEditRequest {
	uint64  operate_company_id = 1;// @tag validate:"omitempty,number" zh:"operate_company_id"
	string name = 2;//@tag validate:"required,string" zh:"端口分组名称"
	repeated uint64 ports = 3;//@tag validate:"required,array" zh:"端口ID"
	uint64  user_id = 4;
	uint64 id = 5;
}

message PortGroupEditResponse {}

message PortGroupDetailRequest {
	uint64 id = 1;// @tag validate:"required,number" zh:"端口分组ID"
	uint64  operate_company_id = 2;
	uint64  user_id = 3;
}

message PortGroupDetailResponse {
	uint64 id = 1;
	string name = 2;
	repeated PortInfo ports= 5;
}

message CounterfeitTaskListRequest {
	uint64 user_id = 1;
	uint64 company_id = 2; // 用户企业ID
	string keyword = 3; // 仿冒企业名称
	uint64 operator_id = 4; // 发起人ID
	uint64 date_before = 5;
	uint64 date_after = 6;
	repeated string created_at = 7;
	uint64 page = 8; // @tag validate:"required,number,gt=0" zh:"页码"
	uint64 per_page = 9; // @tag validate:"required,number,gt=0,lte=100" zh:"页大小"
}

message CounterfeitTaskListUnit {
	uint64 id = 1;
	string name = 2; // 仿冒企业名称
	string operator = 3; // 发起人(名称)
	uint64 status = 4;
	uint64 step = 5;
	uint64 step_detail = 6;
	uint64 step_status = 7;
	string clues_count = 8;
	uint64 phishing_online = 9;
	uint64 phishing_offline = 10;
	uint64 domain_online = 11;
	uint64 domain_offline = 12;
	string created_at = 13;
	string updated_at = 14;
	string file_path = 15;
	string expend_flags = 16;
}

message CounterfeitTaskListResponse {
	repeated CounterfeitTaskListUnit items = 1;
	int64 total = 2;
	int64 page = 3;
	int64 per_page = 4;
	uint64 task_id = 5;
}

message CounterfeitTaskDelRequest {
	uint64 user_id = 1;
	uint64 operator_id = 2;
	string keyword = 3;
	repeated uint64 ids = 4;
	uint64 date_before = 5;
	uint64 date_after = 6;
	repeated string created_at = 7;
	int64 operate_company_id = 8;
}

message CounterfeitAssetFindRequest {
	uint64 user_id = 1;
	uint64 task_id = 2; // @tag validate:"required,number,gt=0" zh:"任务"
	uint64 operate_company_id = 3;
}

message CounterfeitAssetListRequest {
	uint64 user_id = 1;
	uint64 operator_id = 2;
	int64 operate_company_id = 3;
	uint64 asset_type = 4; // @tag validate:"required,oneof=1 2 3" zh:"结果类型"
	uint64 task_id = 5; // @tag validate:"required,number" zh:"任务"
	uint64 page = 6;
	uint64 per_page = 7;
	string keyword = 8;
	string ip = 9;
	repeated string subdomain = 10;
	string title = 11;
	string chain_type = 12; // 证据链类型 @tag validate:"omitempty,oneof=0 3 4" zh:"证据链类型"
	string url = 13;
	string online_state = 14; // @tag validate:"omitempty,oneof=true false" zh:"是否在线"
	repeated string ids = 15;
	repeated string port = 16;
	repeated string domain = 17;
	repeated string protocol = 18;
	repeated int64 logo = 19;
	repeated string name = 20; // 仿冒目标
	repeated string icp_company = 21;
	repeated string created_at = 22;
	repeated string updated_at = 23;
	bool is_finished = 24;
}

message CounterfeitAssetInfo {
	string id = 1;
	string ip = 2;
	string name = 3; // 仿冒目标
	string port = 4;
	string protocol = 5;
	string domain = 6;
	string url = 7;
	string icon_hash = 8;
	string icon_content = 9;
	string title = 10;
	string icp_company = 11;
	bool online_state = 12;
	repeated CounterfeitAssetChain chain_list = 13;
	string screenshot = 14;
	string created_at = 15;
	string updated_at = 16;
	string fake_asset_from = 17; // 仿冒资产来源
}

message CounterfeitAssetChain {
	uint64 id = 1;
	uint64 type = 2;
	string content = 3;
	uint64 group_id = 4;
	uint64 source = 5;
	string clue_company_name = 6;
}

message CounterfeitAssetListResponse {
	int64 total = 1;
	int64 page = 2;
	int64 per_page = 3;
	repeated CounterfeitAssetInfo items = 4;
}

message CounterfeitAssetsAggRequest {
	uint64 user_id = 1;
	uint64 asset_type = 2; // @tag form:"asset_type" validate:"required,oneof=1 2" zh:"结果类型"
	uint64 task_id = 3; // @tag form:"task_id" validate:"required,number" zh:"任务"
	int64 operate_company_id = 4; // @tag form:"operate_company_id"
}

message icon {
	int64 hash = 1;
	string content = 2;
}

message CounterfeitAssetsAggResponse {
	repeated string name = 1;
	repeated string port = 2;
	repeated string protocol = 3;
	repeated string domain = 4;
	repeated string icp_company = 5;
	repeated icon logo = 6;
	repeated string subdomain = 7;
}

message CounterfeitAssetsSyncResponse {
	repeated string ip = 1;
}

message CounterfeitAssetDetectRequest {
	uint64 user_id = 1;
	uint64 task_id = 2; // @tag form:"task_id" validate:"required,number,gt=0" zh:"任务"
	int64 operate_company_id = 3; // @tag form:"operate_company_id"
}

message CounterfeitAssetsImportRequest {
	uint64 user_id = 1;
	uint64 task_id = 2; // @tag validate:"required,gt=0" zh:"任务"
	string file_path = 3; // @tag validate:"required" zh:"上传文件"
	int64 operate_company_id = 4;
}

message CounterfeitAssetsCountResponse {
	int64 fake_total = 1;
	int64 domain_total = 2;
	int64 other_total = 3;
}

message DomainAssetsUpdateByCronRequest {
	uint64 user_id = 1;
	bool update = 2;
	string keyword = 3;
	string open_parse = 4; // tag validate:"omitempty,oneof=0 1" zh:"是否泛解析"
	string type = 5; // tag validate:"omitempty,oneof=0 1" zh:"域名类型"
	repeated int64 ids = 6; // tag validate:"omitempty,dive,required"
	repeated int64 source = 7;
	repeated string company_name = 8;
	repeated string domain = 9;
	repeated string created_at_range = 10; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" zh:"发现时间"
	repeated string updated_at_range = 11; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" zh:"更新时间"
	int64 ex = 12; // @tag validate:"omitempty,oneof=30 60 90"
	int64 cron_status = 13; // @tag validate:"omitempty,oneof=1 2"
	int64 company_id = 14;
	int64 operate_user_id = 15;
	uint64 domain_find_task_id = 16;
	uint64 domain_find_go_task_id = 17;
	bool call_by_cron = 18;
	repeated string f_domain = 19;
	repeated string top_domain = 20;
}

message DomainAssetsCronInfoResponse {
	int64 ex = 1;
	int64 status = 2;
	string created_at = 3;
	string updated_at = 4;
}

message DomainAssetsDomainFilterRequest {
	uint64 user_id = 1;
	string keyword = 2;
	string type = 5; // @tag form:"type" validate:"omitempty,oneof=0 1" zh:"域名类型"
}

message DomainAssetsDomainFilterResponse {
	repeated string domains = 1;
}

message ApiRequestCountListRequest {
	string client_id = 1; // @tag form:"client_id"
	string search = 2; // @tag form:"search"
	int64 year = 3; // @tag form:"year"
	int64 month = 4; // @tag form:"month" validate:"omitempty,gte=1,lte=12" zh:"月份"
	int64 page = 5; // @tag form:"page" validate:"required,gte=1" zh:"页码"
	int64 per_page = 6; // @tag form:"per_page" validate:"required,gte=1,lte=100" zh:"每页数量"
}

message ApiRequestCountListResponse {
	message Item {
		uint64 id = 1;
		string created_at = 2;
		string updated_at = 3;
		uint64 year = 4;
		uint64 month = 5;
		uint64 total = 6;
		string path = 7;
	}
	repeated Item items = 1;
	int64 total = 2;
	int64 page = 3;
	int64 per_page = 4;
}

message ManageBeianListRequest {
	string keyword = 1; // @tag form:"keyword"
	int64 page = 2; // @tag form:"page" validate:"required" zh:"页码"
	int64 per_page = 3; // @tag form:"per_page" validate:"required,lte=100" zh:"页大小"
	string company_name = 4; // @tag form:"company_name"
	string icp = 5; // @tag form:"icp"
	string name = 6; // @tag form:"name"
	repeated uint64 company_type = 7; // @tag validate:"omitempty,dive,required,gte=1,lte=16" zh:"单位性质"
	string status = 8; // @tag form:"status" validate:"omitempty,oneof=1 2" zh:"状态"
	repeated string source = 9; // @tag form:"source" validate:"omitempty,dive,required,oneof=miit chinaz_api import" zh:"数据来源"
	int64 parent_id = 10; // @tag form:"parent_id"
	repeated string record_time = 11; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" zh:"审核时间"
}

message ManageBeianListResponse {
	message BeianInfo {
		uint64 id = 1;
		string company_name = 2;
		uint64 parent_id = 3;
		int64 company_type = 4;
		string icp = 5;
		string name = 6;
		string record_time = 7;
		string created_at = 8;
		string updated_at = 9;
		int32 status = 10;
		string source = 11;
	}
	int64 total = 1;
	int64 page = 2;
	int64 per_page = 3;
	repeated BeianInfo items = 4;
}

message ManageBeianCreateRequest {
	repeated uint64 ids = 1;
	string company_name = 2; // @tag validate:"omitempty,max=250" zh:"主办单位"
	uint64 company_type = 3; // @tag validate:"omitempty,gte=1,lte=16" zh:"单位性质"
	string icp = 4; // @tag validate:"required" zh:"备案号"
	string name = 5;
	int32 status = 6; // @tag validate:"omitempty,oneof=1 2" zh:"状态"
	string source = 7; // @tag validate:"omitempty,oneof=miit chinaz_api import" zh:"数据来源"
	string record_time = 8; // @tag validate:"omitempty,datetime=2006-01-02 15:04:05" zh:"审核时间"
	int64 parent_id = 9;
	int32 force = 10; // @tag validate:"omitempty,oneof=0 1" zh:"强制更新"
}

message ManageDigitalKeywordListRequest {
	uint64 source = 1; // @tag form:"source" validate:"required,oneof=1 2" zh:"任务来源"
	string search = 2; // @tag form:"search"
	string keyword = 3; // @tag form:"keyword"
	int64 progress = 4; // @tag form:"progress" validate:"omitempty,oneof=1 2" zh:"任务进度" // 1-未完成 2-已完成
	int64 page = 5; // @tag form:"page" validate:"omitempty" zh:"页码"
	int64 per_page = 6; // @tag form:"per_page" validate:"omitempty,lte=100" zh:"页大小"
	repeated string created_at = 7; // @tag validate:"omitempty,dive,datetime=2006-01-02" zh:"创建时间"
	repeated string updated_at = 8; // @tag validate:"omitempty,dive,datetime=2006-01-02" zh:"更新时间"
	repeated uint64 ids = 9;
}

message ManageDigitalKeywordInfoResponse {
	uint64 id = 1;
	string name = 2;
	float progress = 3;
	string created_at = 4;
	string updated_at = 5;
}

message ManageDigitalKeywordListResponse {
	int64 total = 1;
	int64 page = 2;
	int64 per_page = 3;
	repeated ManageDigitalKeywordInfoResponse items = 4;
}

message ManageDigitalKeywordUpsertRequest {
	uint64 id = 1; // @tag form:"id"
	string force = 2; // @tag validate:"omitempty,eq=1" zh:"强制更新"
	uint64 source = 3; // @tag form:"source" validate:"required,oneof=1 2" zh:"来源"
	repeated string name = 4; // @tag validate:"omitempty,unique,dive,required" zh:"关键词"
	repeated uint64 ids = 5; // @tag validate:"omitempty,unique,dive,required" zh:"关键词ID"
}

message ManageDigitalAssetsResultImportRequest {
	uint64 task_id = 1;
	string file_path = 2; // @tag validate:"required" zh:"上传文件"
	int64 source = 3; // @tag validate:"required,oneof=1 2" zh:"来源"
}

message ManageDigitalAssetsResultListRequest {
	string search = 1; // @tag form:"search" zh:"搜索"
	string platform = 2; // @tag form:"platform" validate:"omitempty,oneof=1 2" zh:"应用分类"
	uint32 is_online = 3; // @tag form:"is_online" validate:"omitempty,oneof=1 2" zh:"是否在线"
	string name = 4; // @tag form:"name" zh:"名称" // 公众号名称/应用名称
	string account = 5; // @tag form:"account" zh:"微信号"
	repeated string company_name = 6; // 账号主体
	int64 page = 8; // @tag form:"page"
	int64 per_page = 9; // @tag form:"per_page" validate:"omitempty,lte=100" zh:"页大小"
	int64 source = 10; // @tag form:"source" validate:"required,oneof=1 2" zh:"来源"
	uint64 task_id = 11; // @tag form:"task_id"
	repeated uint64 ids = 12; // @tag validate:"omitempty,unique,dive,required" zh:"ID"
	repeated string created_at = 13; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" // 创建时间
	repeated string updated_at = 14; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" // 更新时间
}

message ManageDigitalAssetsResultItem {
	uint64 id = 1; // @tag validate:"omitempty" zh:"资产id"
	uint64 source = 2; // @tag validate:"required,oneof=1 2" zh:"来源"
	string name = 3; // @tag validate:"required" zh:"名称"
	string account = 4; // 微信号
	string logo = 5;
	string company_name = 6; // @tag validate:"required" zh:"账号主体"
	string platform = 7; // 数据来源
	string is_online = 8; // @tag validate:"omitempty,oneof=1 2" zh:"是否在线"
	string description = 9;
	string url = 10; // app下载地址
	string created_at = 11;
	string updated_at = 12;
}

message ManageDigitalAssetsResultListResponse {
	int64 page = 1;
	int64 per_page = 2;
	int64 total = 3;
	repeated ManageDigitalAssetsResultItem items = 4;
}

message ManageDigitalKeywordResultListRequest {
	int64 page = 1; // @tag form:"page" validate:"omitempty,gt=0" zh:"页码"
	int64 per_page = 2; // @tag form:"per_page" validate:"omitempty,lte=100" zh:"页大小"
	string search = 3; // @tag form:"search"
	uint64 task_id = 4; // @tag form:"task_id" validate:"required" zh:"任务"
	uint64 source = 5; // @tag form:"source" validate:"required,oneof=1 2" zh:"来源"
	string name = 6; // @tag form:"name"
	string account = 7; // @tag form:"account"
	string company_name = 8; // @tag form:"company_name"
	string is_online = 9; // @tag form:"is_online" validate:"omitempty,oneof=1 2" zh:"是否在线"
}

message ManageDigitalFilterGroupResponse {
	repeated string company_name = 1;
}

message DetectAssetCluesUpdateRequest {
	uint64 user_id = 1;
	uint64 operate_user_id = 2;
	repeated uint64 ids = 3; // @tag validate:"omitempty,dive,required" zh:"ID"
	bool sync = 4;
	string clue_company_name = 5; // @tag validate:"required" zh:"企业名称"
}

message ApplyApiRequest {
	string email = 1;
	string description = 2;
}

message ApplyApiAddResponse {}

message ApplyAuditRequest {
	uint64 id = 1; // @tag validate:"required" zh:"审核记录"
	string audit_status = 2; // @tag validate:"required,oneof=1 2" zh:"审核状态"
	string reason = 3;
}

message ApplyListRequest {
	int64 page = 1; // @tag form:"page" validate:"required,gt=0" zh:"页码"
	int64 per_page = 2; // @tag form:"per_page" validate:"required,lt=50" zh:"页大小"
	string search = 3; // @tag form:"search"
	string email = 4; // @tag form:"email"
	string audit_status = 5; // @tag form:"audit_status" validate:"omitempty,oneof=0 1 2" zh:"审核状态"
	string email_status = 6; // @tag form:"email_status" validate:"omitempty,oneof=1 2 3" zh:"邮件状态"
}

message ApplyListResponse {
	message item {
		uint64 id = 1;
		string email = 2;
		string description = 3;
		string reason = 4;
		string email_status = 5;
		string audit_status = 6;
		string created_at = 7;
		string updated_at = 8;
	}
	int64 page = 1;
	int64 per_page = 2;
	int64 total = 3;
	repeated item items = 4;
}

message IcpAppListRequest {
	int64 page = 1; // @tag form:"page" validate:"required,gt=0" zh:"页码"
	int64 per_page = 2; // @tag form:"per_page" validate:"required,lte=100" zh:"页大小"
	uint32 type = 3; // @tag form:"type" validate:"required,oneof=1 2 3" zh:"ICP类型"
	int64 company_type = 4; // @tag form:"company_type" validate:"omitempty,gte=1,lte=16" zh:"单位性质"
	int64 status = 5; // @tag form:"status" validate:"omitempty,oneof=1 2" zh:"备案状态"
	string source = 6; // @tag form:"source" validate:"omitempty,oneof=miit chinaz import" zh:"数据来源"
	string search = 7; // @tag form:"search"
	repeated string record_time = 8; // @tag validate:"omitempty,dive,required,datetime=2006-01-02" zh:"审核日期"
}

message IcpAppListItem {
	uint64 id = 1;
	uint64 parent_id = 2;
	string company_name = 3;
	string name = 4;
	string icp = 5; // @tag validate:"required" zh:"ICP备案号"
	int32 company_type = 6; // @tag validate:"required,gte=1,lte=16" zh:"单位性质"
	int32 status = 7; // @tag validate:"omitempty,oneof=1 2" zh:"备案状态"
	string source = 8;
	string record_time = 9; // @tag validate:"omitempty,datetime=2006-01-02 15:04:05" zh:"审核日期"
	string created_at = 10;
	string updated_at = 11;
	bool force_update = 12;
	int32 force = 13;
	int32 type = 14; // @tag validate:"omitempty,oneof=1 2 3" zh:"ICP类型"
}

message IcpAppListResponse {
	int64 page = 1;
	int64 per_page = 2;
	int64 total = 3;
	repeated IcpAppListItem items = 4;
}

message BlackKeywordListRequest {
	string search = 1; // @tag form:"search"
	string keyword = 2; // @tag form:"keyword"
	int64 page = 3; // @tag form:"page" validate:"required,gt=0" zh:"页码"
	int64 per_page = 4; // @tag form:"per_page" validate:"required,gt=0,lte=100" zh:"页大小"
	repeated string created_at = 5; // @tag validate:"omitempty,dive,datetime=2006-01-02" zh:"创建日期"
	repeated uint64 user_id = 6; // @tag validate:"omitempty,unique,dive,required" zh:"用户"
	repeated uint64 type_id = 7; // @tag validate:"omitempty,unique,dive,required" zh:"分类"
	repeated string status = 8; // @tag validate:"omitempty,unique,dive,oneof=0 1 2" zh:"审核状态"
	repeated uint64 ids = 9; // @tag validate:"omitempty,unique,dive,required" zh:"勾选记录"
}

message BlackKeywordListResponse {
	message item {
		int64 id = 1;
		uint64 user_id = 2;
		uint64 company_id = 3;
		int64 type_id = 4;
		uint64 enable = 5;
		string status = 6;
		string username = 7;
		string keyword = 8;
		string type_name = 9;
		string created_at = 10;
		string updated_at = 11;
	}
	repeated item items = 1;
	int64 page = 2;
	int64 per_page = 3;
	int64 total = 4;
}

message BlackKeywordUpdateRequest {
	string search = 1;
	repeated string created_at = 2; // @tag validate:"omitempty,dive,datetime=2006-01-02" zh:"创建日期"
	repeated uint64 user_id = 3; // @tag validate:"omitempty,unique,dive,required" zh:"用户"
	repeated uint64 type_id = 4; // @tag validate:"omitempty,unique,dive,required" zh:"分类"
	repeated uint64 ids = 5; // @tag validate:"omitempty,unique,dive,required" zh:"勾选记录"
	string status = 6; // @tag validate:"omitempty,oneof=1 2" zh:"审核状态"
	string keyword = 7;
	uint64 audit_type_id = 8;
	bool is_audit = 9;
}

message BlackKeywordTypeCreateRequest {
	repeated string names = 1; // @tag validate:"gt=0,dive,required" zh:"分类名称"
}

message ManageBlackKeywordCreateRequest {
	repeated string keyword = 1; // @tag validate:"required,dive,required" zh:"关键词"
	int64 type_id = 2; // @tag validate:"required" zh:"分类"
}

message ManageBlackKeywordUpdateRequest {
	repeated uint64 ids = 1;
	repeated int64 type_id = 2;
	uint64 enable = 3; // @tag validate:"required,oneof=1 2" zh:"关键词启用状态"
	string search = 4;
}

message UserListRequest {
	int64 page = 1; // @tag form:"page" validate:"required" zh:"页码"
	int64 per_page = 2; // @tag form:"per_page" validate:"required,lte=50" zh:"页大小"
	int32 get_all = 3; // @tag form:"get_all"
}

message UserListResponse {
	message userItem {
		uint64 id = 1;
		string name  = 2;
		string email = 3;
		string mobile = 4;
		uint64 company_id = 5;
		uint32 level = 6;
		uint32 role = 7;
		uint32 status = 8;
		uint64 agree_status = 9;
		string expires_at = 10;
	}
	int64 page = 1;
	int64 per_page = 2;
	int64 total = 3;
	repeated userItem items = 4;
}

message DetectAssetTaskCreateRequest {
	message clueItem {
		repeated string content = 1; // @tag validate:"omitempty,dive,required" zh:"线索内容"
		uint64 type = 2; // @tag validate:"oneof=0 1 2 3 6" zh:"线索类型"
	}
	uint64 user_id = 1;
	uint64 safe_user_id = 2;
	uint64 company_id = 3;
	string name = 4; // @tag validate:"required,max=250" zh:"企业名称"
	repeated clueItem data = 5; // @tag validate:"omitempty,dive"
	uint64 scan_type = 6; // @tag validate:"oneof=0 1 2" zh:"扫描类型"
	uint64 is_auto_domain_brust = 7; // @tag validate:"oneof=0 1" zh:"是否自动域名爆破"
	uint64 is_auto_leak_assets = 8; // @tag validate:"oneof=0 1" zh:"是否自动获取数据泄露"
	uint64 is_auto_data_assets = 9; // @tag validate:"oneof=0 1" zh:"是否自动获取数据资产"
	uint64 is_auto_url_api = 10; // @tag validate:"oneof=0 1" zh:"是否自动URL搜索"
	uint64 is_check_risk = 11; // @tag validate:"oneof=0 1" zh:"是否自动监测风险事件"
	uint64 off_assets_to = 12; // @tag validate:"required,oneof=1 2" zh:"离线资产去向"
	uint64 fofa_range = 13;	// @tag validate:"oneof=0 1 2" zh:"资产梳理范围"
	uint64 detect_mode = 14; // @tag validate:"required,oneof=1 2 3" zh:"模式设置"
	uint32 bandwidth = 15; // @tag validate:"required,gte=100,lte=5000" zh:"扫描带宽"
	uint32 percent = 16; // @tag validate:"omitempty,number,lte=100" zh:"控股比例"
	repeated string import_sure_ips = 17; // @tag validate:"omitempty,max=5000,dive,required,ip"
	repeated string company_list = 18; // @tag validate:"omitempty,dive,required" zh:"控股公司"
	repeated string other_company_list = 19; // @tag validate:"omitempty,dive,required" zh:"其他测绘企业"
	string no_need_controy_company = 20; // @tag validate:"omitempty,oneof=0 1" zh:"是否仅测绘主体"
	uint64 is_auto_business_api = 21; // @tag validate:"oneof=0 1" zh:"是否把url资产自动关联到业务系统"
	uint64 is_need_hunter = 22; // @tag validate:"oneof=0 1" zh:"是否对接hunter"
	uint64 is_need_dnschecker = 23; // @tag validate:"oneof=0 1" zh:"是否对接dnschecker"
	uint64 is_auto_expend_ip = 24; // @tag validate:"oneof=0 1" zh:"是否对接自动根据发现的推荐ip进行关联 已经发现的推荐资产ip里面的其他ip线索"
}

message DetectAssetTaskCreateResponse {
	uint64 detect_task_id = 1;
	uint64 group_id = 2;
}

message DetectAssetTaskReportRequest {
	uint64 user_id = 1;
	uint64 task_id = 2; // @tag form:"task_id" validate:"required" zh:"测绘任务"
}

message DetectAssetTaskReportClueUnit {
	string content = 1;
	string type = 2;
}

message DetectAssetTaskReportResponse {
	message report {
		repeated string company = 1;
		string asset_ip = 2;
		string asset_domain = 3;
		string asset_cert = 4;
		string asset_login = 5;
		string asset_shadow = 6;
		string digital = 7;
		string digital_wechat = 8;
		string digital_app = 9;
		string risk_event = 10;
		string data_leak = 11;
		string data_leak_net_disk = 12;
		string data_leak_library = 13;
		string data_leak_code = 14;
	}
	report result_stat = 1; // 测绘结果汇总

	message detect_task_param {
		message company_unit {
			string name = 1; // 企业名称
			string percent = 2; // 控股百分比
			string company_flag = 3; // master/slave 主测绘企业/其他测绘企业
			repeated company_unit children = 4; // 控股企业
		}
		repeated company_unit detect_company_list = 1;
		repeated DetectAssetTaskReportClueUnit input_clues = 2;
		string scan_type = 3;
		string fofa_range = 4;
		string percent = 5;
		int32 off_assets_to = 6;
		int32 detect_mode = 7;
		string bandwidth = 8;
		bool is_import_sure_ips = 9;
	}
	detect_task_param detect_task_stat = 2; // 任务参数展示

	message clue_type_num {
		string ip = 1;
		string domain = 2;
		string cert = 3;
		string icp = 4;
		string icon = 5;
		string keyword = 6;
		string sum = 7;
	}
	message clue_stat_type {
		clue_type_num input = 1;
		clue_type_num icp = 2;
		clue_type_num expend = 3;
		clue_type_num all = 4;
	}
	clue_stat_type clue_stat = 3; // 线索详情

	message asset_discovery {
		string ip_discovery = 1;
		string ip_import = 2;
		string recommend_high = 3; // 信任度-高
		string recommend_middle = 4; // 信任度-中
		string recommend_low = 5; // 信任度-低
		string asset_table = 6; // 台账数量
		string asset_unsure = 7; // 疑似数量
		string asset_threaten = 8; // 威胁数量
		string url_api_num = 9; // URL-API数量
	}
	asset_discovery asset_discovery_stat = 4; // 资产发现

	message digital_asset {
		int32 type = 1;
		string wechat = 2;
		string app = 3;
		string net_disk = 4;
		string library = 5;
		string code = 6;
		string domain = 7;
		string risk_event = 8;
		repeated string match_ip = 9;
		repeated string match_ip_full = 10;
		repeated string keyword = 11;
		int32 is_show = 12;
		string url_api_num = 13;
	}
	repeated digital_asset task_stat = 5; // 关联任务
}

message DetectAssetTaskReportClueListRequest {
	int32 clue_step = 1; // @tag form:"clue_step" validate:"required,oneof=1 2 3 4" zh:"线索步骤"
	int32 page = 2; // @tag form:"page" validate:"required" zh:"页码"
	int32 per_page = 3; // @tag form:"per_page" validate:"required" zh:"页大小"
	uint64 task_id = 4; // @tag form:"task_id" validate:"required" zh:"测绘任务"
	uint64 user_id = 5;
}

message DetectAssetTaskReportClueListResponse {
	repeated DetectAssetTaskReportClueUnit items = 1;
	int32 page = 2;
	int32 per_page = 3;
	int64 total = 4;
}
message EngineRuleListRequest {
	int64 page = 1;
	int64 per_page = 2;
}
message EngineRuleListResponse {
	int64 page = 1;
	int64 per_page = 2;
	int64 total = 3;
	repeated EngineRuleItem items = 4;
}
message EngineRuleItem {
	uint64 id = 1;
	string created_at = 2;
	string updated_at = 3;
	string deleted_at = 4;
	uint64 user_id = 5;
	uint64 operate_user_id = 6;
	string name = 7; // 规则名称
	string rule_name = 8;
	string event_impact = 9; // 事件影响
	string event_solution = 10; // 事件解决方案
	string event_desc = 11; // 事件描述
	string content = 12; // 规则内容
	string rule_content = 13;
	uint64 type = 14;
	int64 tag = 15;  // 风险规则等级
	int64 priority = 16;
	int64 status = 17;
	int64 audit_status = 18;
	string reason = 19;
	string event_type = 20;
}

message WebsiteMessageItem {
	uint64 id = 1;
	string created_at = 2;
	string msg_content = 3;
	uint64 msg_type = 4;
	uint64 msg_subtype = 5;
	uint64 msg_status = 6;
	string last_time_affect_asset_id = 7;
	string this_time_affect_asset_id = 8;
	string affect_asset_id = 9;
	uint64 is_read = 10;
	uint64 app_type = 11;
}

message WebsiteMessageNewListResponse {
	repeated WebsiteMessageNewListItem items = 2;
}

message WebsiteMessageNewListItem{
	string msg_type=1;
	int64 total=2;
}

message WebsiteMessageNewListRequest{
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty,number"
	int64 operate_company_id = 9; // @tag form:"operate_company_id" validate:"omitempty,number"
}

message WebsiteMessageListRequest {
	uint64 user_id = 1;
	int64 operate_company_id = 9; // @tag form:"operate_company_id"
	int64 page = 2; // @tag form:"page" validate:"number" zh:"页数"
	int64 per_page = 3; // @tag form:"per_page" validate:"number" zh:"条数"
	string keyword = 5; // 关键字检索 @tag form:"keyword"
	uint64 msg_type = 6; // 消息类型 @tag form:"msg_type" validate:"omitempty,oneof=1 2 3"
	uint64 msg_subtype = 7; // 消息子类型 @tag form:"msg_subtype" validate:"omitempty,number"
	uint64 msg_read = 8; // 消息已读未读 @tag form:"msg_read" validate:"omitempty,oneof=0 1"
	repeated string created_at = 10; // 创建时间 @tag form:"created_at"
}
message WebsiteMessageListResponse {
	int64 total = 1;
	int64 page = 2;
	int64 per_page = 3;
	repeated WebsiteMessageItem items = 4;
}

message WebsiteMessageUpdateRequest {
	repeated uint64 ids = 1;
	uint64 user_id = 2;
	int64 operate_company_id = 3; // @tag form:"operate_company_id"
	string keyword = 5; // 关键字检索 @tag form:"keyword"
	uint64 msg_type = 6; // 消息类型 @tag form:"msg_type" validate:"omitempty,oneof=1 2 3"
	uint64 msg_subtype = 7; // 消息子类型 @tag form:"msg_subtype" validate:"omitempty,number"
	uint64 msg_read = 8; // 消息已读未读 @tag form:"msg_read" validate:"omitempty,oneof=0 1"
	repeated string created_at = 9; // 创建时间 @tag form:"created_at"
}
message WebsiteMessageNotifyRequest {
	uint64 id = 1;
	uint64 user_id = 2;
	int64 operate_company_id = 3; // @tag form:"operate_company_id"
	string asset_change_json = 4; // 资产变化 @tag form:"asset_change_json"
	string task_json = 5;  // 任务发现 @tag form:"task_json"
	string risk_found_json = 6;  // 风险发现 @tag form:"risk_found_json"
	uint64 is_website_msg = 7; // 站内信通知 @tag form:"is_website_msg" validate:"omitempty,oneof=1 2"
	uint64 is_email = 8; // 邮箱通知 @tag form:"is_email" validate:"omitempty,oneof=1 2"
	string email = 9; // 邮箱 @tag form:"email"
}
message WebsiteMessageNotifyResponse {
	uint64 id = 1;
	string asset_change_json = 2;
	string task_json = 3;
	string risk_found_json = 4;
	uint64 is_website_msg = 5;
	uint64 is_email = 6;
	string email = 7;
}

message AccountOpenByHandRequest {
	string company_name		= 1; // @tag form:"company_name"
	string username			= 2; // @tag form:"username"
	string email			= 3; // @tag form:"email"
	string mobile			= 4; // @tag form:"mobile"
	int64 user_id 			= 5; // @tag form:"user_id"
	bool is_formal			= 6; // @tag form:"is_formal"
}

message AccountOpenByHandResponse {
	string passwd	= 1;
}

message ListAccountOpenByHandRequest {
	int64 user_id	= 1;
}
message ListAccountOpenByHandResponse {
	repeated AccountOpenByHandInfo data	= 1;
}

message AccountOpenByHandInfo {
	string company_name			= 1;
	string username				= 2;
	string email				= 3;
	string mobile				= 4;
	string initial_password		= 5;
	int64 status				= 6; // 状态【0账号生成中、1启用、2禁用】
	bool is_formal				= 7; // 用户属性【0测试用户、1正式用户】
	string expires_at			= 8; // 授权到期时间
	string created_at			= 9;
	string updated_at			= 10;
	string black_ip_switch		= 11; // 黑ip封禁
	CompaniesInfo company_info	= 12;
}
message CompaniesInfo {
	int64 data_leak_rate			= 1; // 数据泄露推荐频率 0/1/2/3 关闭/每月一次/每两个月一次/每季度一次
	int64 new_asset_rate			= 2; // 新型资产推荐频率 0/1/2/3 关闭/每月一次/每两个月一次/每季度一次
	int64 limit_cloud_recommend		= 3; // 云端推荐限制次数
	int64 limit_ip_asset			= 4; // IP资产限制数量/个
	int64 limit_new_asset			= 5; // 新型资产限制次数
	int64 limit_poc_scan   			= 6; // 漏洞管理次数
	int64 limit_data_leak  			= 7; // 数据泄露推荐次数
	int64 limit_monitor_keyword  	= 8; // 关键字监控/个
	int64 used_ip_asset  			= 9; // 已使用IP资产/个
	int64 used_cloud_recommend  	= 10; // 已使用云端推荐/次
	int64 used_new_asset  			= 11; // 已使用新型资产/次
	int64 used_poc_scan  			= 12; // 已使用漏洞管理/次
	int64 used_data_leak  			= 13; // 已使用数据泄露推荐/次
	int64 used_monitor_keyword  	= 14; // 已使用关键字监控/个
}

// JS深层目录爬取
message ApiAnalyzeResultRequest {
	uint64 task_id = 1; // @tag form:"task_id" validate:"required" zh:"任务ID"
	uint64 userId = 2; // @tag form:"userId" validate:"required" zh:"用户ID"
	int64 page = 3; // @tag form:"page" validate:"omitempty" zh:"页码"
	int64 per_page = 4; // @tag form:"per_page" validate:"omitempty" zh:"每页大小"
	string url = 5; // @tag form:"url" validate:"required" zh:"地址过滤"
	int64 type = 6; // @tag form:"type" validate:"omitempty,oneof=1 2 3 4" zh:"风险类型"
	int64 status_code = 7; // @tag form:"status_code" validate:"omitempty" zh:"状态码"
}

message ApiAnalyzeResultExportRequest {
	repeated uint64 task_ids = 1; // @tag form:"task_ids" validate:"required" zh:"任务ID"
	uint64 userId = 2; // @tag form:"userId" validate:"required" zh:"用户ID"
}

message ApiAnalyzeResultResponse {
	uint64 task_id = 1; // @tag form:"task_id" validate:"required" zh:"任务ID"
	int64 status = 2; // @tag form:"status" validate:"omitempty" zh:"任务状态"
	int64 total = 3; // @tag form:"total" validate:"required" zh:"总数"
	repeated int64 status_code = 4; // @tag form:"status_code" validate:"omitempty" zh:"状态码"
	repeated ApiAnalyzeResult results = 5; // @tag form:"results" validate:"omitempty" zh:"结果"
}

message ApiAnalyzeResult {
	string url = 1; // @tag form:"url" validate:"required" zh:"请求地址"
	string api = 2; // @tag form:"api" validate:"required" zh:"API"
	int64 risk_type = 3; // @tag form:"risk_type" validate:"omitempty" zh:"风险类型"
	string tag = 4; // @tag form:"tag" validate:"omitempty" zh:"标签"
	string description = 5; // @tag form:"description" validate:"omitempty" zh:"描述"
	int64 status_code = 6; // @tag form:"status_code" validate:"omitempty" zh:"状态码"
	int64 size = 7; // @tag form:"size" validate:"omitempty" zh:"大小"
	string response_type = 8; // @tag form:"response_type" validate:"omitempty" zh:"响应类型"
	int64 count = 9; // @tag form:"count" validate:"omitempty" zh:"匹配次数"
	repeated string matches = 10; // @tag form:"match" validate:"omitempty" zh:"匹配字段"
	string risk_point = 11; // @tag form:"risk_point" validate:"omitempty" zh:"风险指向"
}

message ApiAnalyzeDetailResponse {
	uint64 task_id = 1; // @tag form:"task_id" validate:"required" zh:"任务ID"
	int64 status = 2; // @tag form:"status" validate:"omitempty" zh:"任务状态"
	repeated string url_list = 3; // @tag form:"url_list" validate:"omitempty" zh:"URL列表"
	repeated string js_list = 4; // @tag form:"js_list" validate:"omitempty" zh:"JS列表"
	ApiAnalyzeExternalList external_list = 5; // @tag form:"external_list" validate:"omitempty" zh:"外部链接列表"
}

message ApiAnalyzeExternalList {
	repeated string url = 1; // @tag form:"url" validate:"omitempty" zh:"外部链接列表"
	repeated string domain = 2; // @tag form:"domain" validate:"omitempty" zh:"域名列表"
}

message APiAnalyzeTaskListRequest {
	uint64 userId = 1; // @tag form:"userId" validate:"required" zh:"用户ID"
	int64 page = 2; // @tag form:"page" validate:"omitempty" zh:"页码"
	int64 per_page = 3; // @tag form:"per_page" validate:"omitempty" zh:"每页大小"
	string name = 4; // @tag form:"name" validate:"omitempty" zh:"任务名称"
	int64 status = 5; // @tag form:"status" validate:"omitempty,oneof=0 1 2 3" zh:"任务状态"
	string operator = 6; // @tag form:"operator" validate:"omitempty" zh:"操作人"
	repeated string created_at_range = 7; // @tag form:"created_at_range" validate:"omitempty" zh:"开始时间"
	repeated string end_at_range = 8; // @tag form:"end_at_range" validate:"omitempty" zh:"结束时间"

}

message ApiAnalyzeUserTaskInfoRequest {
	uint64 user_task_id = 1; // @tag form:"user_task_id" validate:"required" zh:"用户任务ID"
	uint64 userId = 2; // @tag form:"userId" validate:"required" zh:"用户ID"
	string url = 3; // @tag form:"url" validate:"required" zh:"URL筛选"
}

message ApiAnalyzeTaskInfoRequest {
	uint64 task_id = 1; // @tag form:"user_task_id" validate:"required" zh:"用户任务ID"
	uint64 userId = 2; // @tag form:"userId" validate:"required" zh:"用户ID"
}

message ApiAnalyzeDeleteRequest {
	repeated uint64 task_id = 1; // @tag form:"task_id" validate:"required" zh:"任务ID"
	uint64 userId = 2; // @tag form:"userId" validate:"required" zh:"用户ID"
	string name = 3; // @tag form:"name" validate:"omitempty" zh:"任务名称"
	int64 status = 4; // @tag form:"status" validate:"omitempty,oneof=0 1 2 3" zh:"任务状态"
	string operator = 5; // @tag form:"operator" validate:"omitempty" zh:"操作人"
	repeated string created_at_range = 6; // @tag form:"created_at_range" validate:"omitempty" zh:"开始时间"
	repeated string end_at_range = 7; // @tag form:"end_at_range" validate:"omitempty" zh:"结束时间"
	string url = 8; // @tag form:"url" validate:"omitempty" zh:"URL筛选"
	uint64 user_task_id = 9; // @tag form:"user_task_id" validate:"omitempty" zh:"用户任务ID"
}

message ApiAnalyzeDeleteResponse {
	int64 status = 1; // @tag form:"status" validate:"omitempty" zh:"任务状态"
}

message ApiAnalyzeUserTaskInfoResponse {
	int64 total = 1; // @tag form:"total" validate:"required" zh:"总数"
	repeated ApiAnalyzeTaskInfo task_list = 2; // @tag form:"task_list" validate:"omitempty" zh:"任务列表"
}

message ApiAnalyzeTaskInfo {
	uint64 task_id = 1; // @tag form:"task_id" validate:"required" zh:"任务ID"
	int64 status = 2; // @tag form:"status" validate:"omitempty" zh:"任务状态"
	float  progress = 3; // @tag form:"progress" validate:"omitempty" zh:"进度"
	string url = 4; // @tag form:"url" validate:"required" zh:"目标URL"
	string domain = 5; // @tag form:"domain" validate:"omitempty" zh:"域名"
	string user_agent = 6; // @tag form:"user_agent" validate:"omitempty" zh:"UserAgent"
	bool fuzzy = 7; // @tag form:"fuzzy" validate:"omitempty" zh:"模糊测试"
	int64  js_count = 8; // @tag form:"js_count" validate:"omitempty" zh:"JS数量"
	int64 api_count = 9; // @tag form:"api_count" validate:"omitempty" zh:"API数量"
	string start_time = 10; // @tag form:"start_time" validate:"omitempty" zh:"开始时间"
	int64 risk_count = 11; // @tag form:"risk_count" validate:"omitempty" zh:"风险数量"
}

message ApiAnalyzeUserTaskListResponse {
	int64 total = 1; // @tag form:"total" validate:"required" zh:"总数"
	repeated ApiAnalyzeUserTaskInfo task_list = 2; // @tag form:"task_list" validate:"omitempty" zh:"任务列表"
}

message ApiAnalyzeUserTaskInfo {
	uint64 user_task_id = 1; // @tag form:"user_task_id" validate:"required" zh:"用户任务ID"
	string name = 2; // @tag form:"name" validate:"omitempty" zh:"任务名称"
	int64 status = 3; // @tag form:"status" validate:"omitempty" zh:"任务状态"
	float progress = 4; // @tag form:"progress" default:"0" validate:"omitempty" zh:"进度"
	int64 sub_tasks_num = 5; // @tag form:"sub_tasks_num" validate:"omitempty" zh:"子任务数量"
	int64 total_risks = 6; // @tag form:"total_risks" validate:"omitempty" zh:"总风险"
	string start_time = 7; // @tag form:"start_time" validate:"omitempty" zh:"开始时间"
	string end_time = 8; // @tag form:"end_time" validate:"omitempty" zh:"结束时间"
	string used_time = 9; // @tag form:"used_time" validate:"omitempty" zh:"使用时间"
	string operator = 10; // @tag form:"operator" validate:"omitempty" zh:"发起人"
}

message CompanyDropListRequest {
	string name = 1; // @tag form:"name" validate:"required" zh:"模糊搜索关键字"
	int64 operate_company_id = 2; // @tag form:"operate_company_id" validate:"omitempty" zh:"企业ID"
}
message CompanyDropListResponse {
	repeated CompanyDetail company_details = 1;
}
message CompanyDetail {
	string name = 1;
	string reason = 2;
}

// 任务详情请求
message InfoRequest {
	uint64 id = 1;                // 任务ID
	uint64 operate_company_id = 2; // 企业ID
	int32 expand_source = 3;      // 扩展来源
}

// 任务详情响应
message InfoResponse {
	uint64 id = 1;                // 任务ID
	uint64 user_id = 2;           // 用户ID
	uint64 company_id = 3;        // 企业ID
	uint64 safe_user_id = 4;      // 安服用户ID
	string name = 5;              // 任务名称
	int32 step = 6;              // 资产测绘步骤
	int32 step_detail = 7;       // 资产测绘详细步骤
	int32 detect_type = 8;       // 线索模式
	uint64 group_id = 9;         // 线索分组ID
	int32 step_status = 10;      // 状态值
	int32 status = 11;           // 状态
	string company_json = 12;     // 企业JSON
	string expend_flags = 13;     // 资产推荐flag
	double expend_progress = 14;  // 资产推荐进度
	double clue_progress = 15;    // 极速线索扩展任务进度
	double progress = 16;         // 任务进度
	double update_assets_level_progress = 17; // 资产定级进度
	string return_json = 18;      // 资产测绘第二步线索返回ids
	string sure_ip_num = 19;      // 台账的数量
	string unsure_ip_num = 20;    // 疑似资产的数量
	string threaten_ip_num = 21;  // 威胁资产的数量
	string expend_first_step_clue_node = 22; // 单位测绘初始线索获取执行的节点名称
	int32 is_extract_clue = 23;   // 是否自动提取线索
	int32 is_check_risk = 24;     // 是否自动监测风险事件
	int32 expand_source = 25;     // 资产测绘扩展来源
	int32 is_intellect_mode = 26; // 是否是智能模式
	int32 is_intellect_failed = 27; // 智能模式是否失败
	string clues_count = 28;      // 该场景的扩展资产用的线索数量
	repeated uint64 scan_task_ids = 29;    // 关联的资产扫描任务ID列表
	repeated uint64 domain_task_ids = 30;  // 关联的域名爆破任务ID列表
	string created_at = 31;
	string username = 32;
	string updated_at = 33;
	int32 is_show = 34; // 这个测绘任务是否可以显示简报  1/显示 2/不显示
	int32 is_auto_url_api = 35; // 是否自动去搜索引擎获取url-api资产 0/1  不自动/自动
	int32 is_finish_url_api = 36; // 是否完成url-api资产获取 0/1  未完成/已完成
	int32 is_finish_shadow_assets = 37; // 是否完成影子资产检测 0/1  未完成/已完成
	int32 is_finish_check_risk = 38; // 是否完成风险事件检测 0/1  未完成/已完成
	int32 is_auto_business_api = 39; // 是否自动同步url资产到业务系统 0/1  不自动/自动
	int32 is_need_dnschecker = 40; // 是否需要dnschecker检测 0/1  不需要/需要
	int32 is_need_hunter = 41; // 是否需要hunter检测 0/1
	int32 is_auto_expend_ip = 42; // 是否自动根据发现的推荐ip进行关联 已经发现的推荐资产ip里面的其他ip线索 0/1  不需要/需要
	int32 is_auto_leak_assets = 43; // 是否自动获取数据泄露 0/1  不需要/需要
	int32 is_auto_data_assets = 44; // 是否自动获取数据资产 0/1  不需要/需要
	int32 is_auto_domain_brust = 45; // 是否自动域名爆破 0/1  不需要/需要
}

// InfoRequest 请求参数
message DetectAssetsInfoRequest {
  uint64 id = 1;
  uint64 operate_company_id = 2;
	uint64 expand_source = 3;
  uint64 user_id = 4;  // 添加用户ID字段
}

// InfoResponse 响应数据
message DetectAssetsInfoResponse {
  uint64 id = 1;
  uint64 user_id = 2;
  uint64 company_id = 3;
  uint64 safe_user_id = 4;
  string name = 5;
  int32 step = 6;
  int32 step_detail = 7;
  int32 detect_type = 8;
  uint64 group_id = 9;
  int32 step_status = 10;
  int32 status = 11;
  string expend_flags = 12;
  double expend_progress = 13;
  double progress = 14;
  double clue_progress = 15;
  string created_at = 16;
  string updated_at = 17;
  double update_assets_level_progress = 18;
  string return_json = 19;
  string sure_ip_num = 20;
  string unsure_ip_num = 21;
  string threaten_ip_num = 22;
  string expend_first_step_clue_node = 23;
  int32 is_extract_clue = 24;
  int32 is_check_risk = 25;
  int32 expand_source = 26;
  int32 is_intellect_mode = 27;
  int32 is_intellect_failed = 28;
  string clues_count = 29;
  repeated uint64 scan_task_ids = 30;
  repeated uint64 domain_task_ids = 31;
  string company_json = 32;
}

message StartSyncJobRequest {
	uint64 user_id = 1;      // 用户ID
	uint64 company_id = 2;   // 企业ID
	uint64 operator_id = 3;  // 操作者ID
	uint64 detect_task_id = 4; // 检测任务ID
	string client_ip = 5;     // 操作者的IP地址
}

message StartSyncJobResponse {
	uint64 detect_task_id = 1; // 检测任务ID
	uint64 group_id = 2;      // 分组ID
}

message DetectAssetsTask {
	int64 id = 1;
	int64 user_id = 2;
	int64 group_id = 3;
	string return_json = 4;
	bool is_auto_leak_assets = 5;
	bool is_auto_data_assets = 6;
	string created_at = 7;
	string updated_at = 8;
}

message Clue {
	int64 id = 1;
	int64 user_id = 2;
	int64 group_id = 3;
	int32 status = 4;
	int32 is_from_check_table = 5;
	int32 is_deleted = 6;
}

message SensitiveKeyword {
	int64 id = 1;
	string name = 2;
	int32 type = 3;
	int32 status = 4;
	int64 user_id = 5;
	int64 detect_task_id = 6;
	int64 company_id = 7;
	int64 operator_id = 8;
	string created_at = 9;
	string updated_at = 10;
}

message DetectAssetsTaskReport {
	int64 id = 1;
	int64 user_id = 2;
	int64 company_id = 3;
	int64 detect_task_id = 4;
	string created_at = 5;
}

message CheckCompanyLimitRequest {
  int64 company_id = 1;
  string limit_type = 2;
}

message CheckCompanyLimitResponse {
  int32 limit_count = 1;
}

message GetDetectAssetsTaskRequest {
  int64 user_id = 1;
  int64 detect_task_id = 2;
}

message GetCluePreIdsRequest {
  int64 user_id = 1;
  int64 group_id = 2;
}
message GetCluePreIdsResponse {
  repeated int64 pre_ids = 1;
}

message InsertDetectAssetsTaskReportRequest {
  int64 user_id = 1;
  int64 company_id = 2;
  int64 detect_task_id = 3;
}

message GetSensitiveKeywordByNameRequest {
  int64 user_id = 1;
  string name = 2;
}

message UpdateSensitiveKeywordRequest {
  int64 id = 1;
  SensitiveKeyword keyword = 2;
}

message InsertSensitiveKeywordRequest {
  SensitiveKeyword keyword = 1;
}

message DispatchDetectGolangCluesJobRequest {
  repeated string company_list = 1;
  int64 user_id = 2;
  int64 company_id = 3;
  int64 task_id = 4;
  int64 group_id = 5;
  bool flag = 6;
  repeated int64 pre_ids = 7;
}

message CreateLogEventRequest {
  int64 user_id = 1;
  string action = 2;
  string status = 3;
  string client_ip = 4;
  int64 company_id = 5;
  string operate_type = 6;
}


// 获取企业股权结构
message GetCompanyCascadeEquityRequest {
	string company_id = 1; // @tag form:"company_id" validate:"required" zh:"企业ID"
	string operate_company_id = 2; // @tag form:"operate_company_id" validate:"omitempty" zh:"操作企业ID"
	int32 not_need_icp = 3; // @tag form:"not_need_icp" validate:"omitempty" zh:"是否需要ICP"
	int32 no_cache = 4; // @tag form:"no_cache" validate:"omitempty" zh:"是否不使用缓存"
}

// 获取企业股权结构响应
message GetCompanyCascadeEquityResponse {
	repeated CompanyEquityResult result = 1; // @tag json:"result" zh:"结果列表"
}

// 企业股权结构结果
message CompanyEquityResult {
	string company_id = 1; // @tag json:"company_id" zh:"企业ID"
	string company_name = 2; // @tag json:"company_name" zh:"企业名称"
	string reg_status = 3; // @tag json:"reg_status" zh:"注册状态"
	string rate = 4; // @tag json:"rate" zh:"股权比例"
	bool color_flag = 5; // @tag json:"color_flag" zh:"颜色标志"
}

// 线索数量统计请求
message ClueCountRequest {
    uint64 group_id = 1;
    uint64 operate_company_id = 2;
    int32 status = 3;
    uint64 detect_task_id = 4;
    uint64 fake_detect_task_id = 5;
		uint64 user_id = 6;      // 用户ID
		uint64 company_id = 7;   // 企业ID
}

// 线索数量统计响应
message ClueCountResponse {
    repeated ClueTypeCount clues_count = 1;
    int32 expand_finish = 2;
}

message ClueTypeCount {
    int32 type = 1;
    string label = 2;
    int32 count = 3;
}

// 获取线索列表请求
message ClueListRequest {
    uint64 operate_company_id = 1; // 企业ID
    string keyword = 2;            // 关键词
    uint64 group_id = 3;           // 分组ID
    int32 status = 4;              // 状态
    int32 type = 5;                // 类型
    int32 is_whole = 6;            // 是否获取所有数据
    uint64 detect_task_id = 7;     // 检测任务ID
		uint64 user_id = 8;      // 用户ID
		uint64 company_id = 9;   // 企业ID
	  int64 page = 10; 			// 页码
	  int64 per_page = 11; // 每页大小
}

// 线索信息
message ClueInfo {
    uint64 id = 1;                 // 线索ID
    string content = 2;            // 内容
    int32 type = 3;                // 类型
    string hash = 4;               // 哈希值
    string from_ip = 5;            // 来源IP
    uint64 parent_id = 6;          // 父ID
    int32 source = 7;             // 来源
    string clue_company_name = 8;  // 公司名称
    bool is_highlight = 9;         // 是否高亮
    string equity_percent = 10;    // 股权比例
    string parent_company_name = 11; // 父公司名称
    string parent_percent = 12;    // 父公司股权比例
    repeated ClueChain chain_list = 13; // 链式列表
	  FofaAssetsNum fofa_assets_num = 14;    // FOFA资产信息
	  string created_at = 15;
	  string updated_at = 16;
		string source_label = 17;
		int32 is_expand = 18;
	  int32 status = 19;
}

// FOFA资产信息
message FofaAssetsNum {
    uint32 num = 1;        // FOFA资产数量
    string fofa_url = 2;   // FOFA查询URL
}

// 线索链式信息
message ClueChain {
	int64 id = 1;
	int64 user_id = 2;
	int64 company_id = 3;
	int64 parent_id = 4;
	string content = 5;
	int64 group_id = 6;
	string comment = 7;
	string clue_company_name = 8;
	string hash = 9;
	int32 source = 10;
	int32 count = 11;
	int32 type = 12;
	int32 status = 13;
	string created_at = 14;
	string updated_at = 15;
	int64 safe_user_id = 16;
	int32 is_expand = 17;
	string from_ip = 18;
	int32 is_deleted = 19;
	string punycode_domain = 20;
	int32 is_from_check_table = 21;
	int32 is_supply_chain = 22;
	int32 is_fake_icp = 23;
	string source_url = 24;
}


// 分页响应结构
message ClueListPageResponse {
  repeated ClueInfo items = 1;
  int32 total = 2;
  int32 current_page = 3;
  int32 per_page = 4;
  int32 last_page = 5;
}

// 全量响应结构
message ClueListWholeResponse {
  repeated ClueWholeInfo domain = 1;
  repeated ClueWholeInfo icp = 2;
  repeated ClueWholeInfo cert = 3;
  repeated ClueWholeInfo icon = 4;
  repeated ClueWholeInfo ip = 5;
}

// 全量查询的线索信息
message ClueWholeInfo {
    int64 id = 1;
    int64 user_id = 2;
    int64 company_id = 3;
    int64 parent_id = 4;
    string content = 5;
    int64 group_id = 6;
    string comment = 7;
    string clue_company_name = 8;
    string hash = 9;
    int32 source = 10;
    int32 count = 11;
    int32 type = 12;
    int32 status = 13;
    string created_at = 14;
    string updated_at = 15;
    int64 safe_user_id = 16;
    int32 is_expand = 17;
    string from_ip = 18;
    int32 is_deleted = 19;
    string punycode_domain = 20;
    int32 is_from_check_table = 21;
    int32 is_supply_chain = 22;
    int32 is_fake_icp = 23;
    string source_url = 24;
    bool is_highlight = 25;
    string equity_percent = 26;
    string parent_company_name = 27;
    string parent_percent = 28;
    repeated ClueChain chain_list = 29;
		FofaAssetsNum fofa_assets_num = 30;    // FOFA资产信息
}

// 资产测绘评估请求
message AssetsEvaluateRequest {
	int64 operate_company_id = 1; // @tag form:"operate_company_id" validate:"omitempty" zh:"企业ID"
	uint64 expend_id = 2; // @tag form:"expend_id" validate:"required" zh:"任务ID"
	uint64 user_id = 3; // @tag form:"user_id" validate:"required" zh:"用户ID"
	uint64 operator_id = 4; // @tag form:"operator_id" validate:"required" zh:"操作人ID"
}

// 资产测绘停止任务
message DetectTaskStopRequest {
	int64  operate_company_id = 1;
	uint64 expend_id = 2;
	uint64 user_id = 3;
}


message AssetsEvaluateResponse {
	bool success = 1;
}

// 资产测绘删除任务请求
message DelDetectAssetsTaskRequest {
	repeated uint64 id = 1; // @tag form:"id" validate:"required" zh:"任务ID列表"
	uint64 user_id = 2; // @tag form:"user_id" validate:"required" zh:"用户ID"
	int64 operate_company_id = 3; // @tag form:"operate_company_id" validate:"omitempty" zh:"企业ID"
	string client_ip = 4; // @tag form:"client_ip" validate:"omitempty" zh:"客户端IP"
	uint64 company_id = 5; // @tag form:"company_id" validate:"omitempty" zh:"公司ID"
}

// 资产测绘删除任务响应
message DelDetectAssetsTaskResponse {
	bool success = 1;
}
message ScanRecommendRequest {
	uint64 id = 1; // @tag form:"id" validate:"required" zh:"任务ID,DetectAssetsTask表"
	uint64 user_id = 2; // @tag form:"user_id" validate:"required" zh:"用户ID"
	int64 operate_company_id = 3; // @tag form:"operate_company_id" validate:"required" zh:"企业ID"
}

message ScanRecommendResponse {
	string warn_message = 1;
}
message ExpandCluesRequest {
	uint64 user_id = 1; // 用户ID
	uint64 operate_company_id = 2; // 企业ID
	uint64 detect_task_id = 3; // 任务ID
	int32 detect_type = 4; // 1:极速模式/2:深度模式
	int32 expand_clue_type = 5; // 0:域名/1:证书/2:icp/6:ip段，仅深度模式需要
	int32 is_intellect_mode = 6; // 0:非智能模式/1:智能模式，仅极速模式需要
	string client_ip = 7; // 客户端IP
}

message ExpandCluesResponse {
	bool success = 1; // 是否成功
	bool is_empty = 2; // 是否为空
}

// 确认所有线索的请求参数
message ConfirmAllCluesRequest {
	uint64 detect_task_id = 1;           // 任务ID
	int64 operate_company_id = 2;        // 企业ID
	int32 use_init_group = 3;            // 是否扩充默认分组 0不扩充/1扩充
	uint64 user_id = 4;                  // 用户ID
	string client_ip = 5;                // 客户端IP
	uint64 operator_id = 6;              // 操作者ID
	uint64 company_id = 7;               // 公司ID
}

// 标记线索到黑名单的请求参数
message ClueBlackRequest {
	repeated ClueBlackData data = 1;   // 线索ID列表
	uint64 user_id = 2;                // 用户ID
	uint64 company_id = 3;             // 公司ID
	uint64 group_id = 4;               // 分组ID
	string client_ip = 5;              // 客户端IP
	uint64 operator_id = 6;            // 操作者ID
	ClueBlackKeyword keyword = 7; 	   // 搜索关键词
}

message ClueBlackData {
	repeated uint64 id = 1;
	optional int32 is_all = 2;
	int64 type = 3;
}

message ClueBlackKeyword {
	string cert_keyword = 1;  			// @tag form:"cert_keyword" validate:"omitempty" zh:"证书关键词"
	string domain_keyword = 2; 		// @tag form:"domain_keyword" validate:"omitempty" zh:"域名关键词"
	string icp_keyword = 3; 				// @tag form:"icp_keyword" validate:"omitempty" zh:"ICP备案关键词"
	string key_keyword = 4; 				// @tag form:"key_keyword" validate:"omitempty" zh:"关键词"
	string ip_keyword = 5; 		 		// @tag form:"ip_keyword" validate:"omitempty" zh:"IP关键词"
	string subdomain_keyword = 6; 	// @tag form:"subdomain_keyword" validate:"omitempty" zh:"子域名关键词"
}

// 登录入口列表请求
message LoginAssetsListRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	string keyword = 2; // @tag form:"keyword" validate:"omitempty" zh:"关键词"
	repeated string created_at_range = 3; // @tag form:"created_at_range" validate:"omitempty" zh:"创建时间范围"
	repeated string updated_at_range = 4; // @tag form:"updated_at_range" validate:"omitempty" zh:"更新时间范围"
	string url = 5; // @tag form:"url" validate:"omitempty" zh:"URL"
	string ip = 6; // @tag form:"ip" validate:"omitempty" zh:"IP"
	uint32 status = 7; // @tag form:"status" validate:"omitempty,oneof=0 1 2" zh:"状态"
	int64 page = 8; // @tag form:"page" validate:"omitempty" zh:"页码"
	int64 per_page = 9; // @tag form:"per_page" validate:"omitempty" zh:"每页数量"
	repeated string title = 10; // @tag form:"title" validate:"omitempty" zh:"标题"
	uint64 website_message_id = 11; // @tag form:"website_message_id" validate:"omitempty" zh:"网站消息ID"
}

// 登录入口列表响应
message LoginAssetsListResponse {
	repeated LoginPageAsset items = 1;
	int64 current_page = 2;
	int64 per_page = 3;
	int64 total = 4;
	LoginAssetsListCondition condition = 5;
}

// 登录入口资产信息
message LoginPageAsset {
	uint64 id = 1;
	string title = 2;
	string url = 3;
	string ip = 4;
	string port = 5;
	string img_url = 6;
	int64 status = 7;
	string node = 8;
	string created_at = 9;
	string updated_at = 10;
	uint64 user_id = 11;
	int64 company_id = 12;
	string unique_key = 13;
	string batch_id = 14;
	string detect_task_id = 15;
	uint64 website_message_id = 16;
}
// 登录入口列表筛选条件
message LoginAssetsListCondition {
	repeated string title = 1;
}

// 登录入口删除请求
message LoginAssetsDeleteRequest {
	repeated string created_at_range = 1; // @tag form:"created_at_range" validate:"omitempty" zh:"创建时间范围"
	repeated string updated_at_range = 2; // @tag form:"updated_at_range" validate:"omitempty" zh:"更新时间范围"
	string keyword = 3; // @tag form:"keyword" validate:"omitempty,max=300" zh:"关键词"
	int64 status = 4; // @tag form:"status" validate:"omitempty,oneof=0 1 2" zh:"状态"
	string url = 5; // @tag form:"url" validate:"omitempty" zh:"URL"
	string ip = 6; // @tag form:"ip" validate:"omitempty" zh:"IP"
	repeated uint64 id = 7; // @tag form:"id" validate:"omitempty" zh:"ID列表"
	uint64 website_message_id = 8; // @tag form:"website_message_id" validate:"omitempty" zh:"站内信ID"
	repeated string title = 9; // @tag form:"title" validate:"omitempty" zh:"标题列表"
	uint64 user_id = 10; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
}

// 登录入口更新状态
message LoginAssetsUpdateRequest {
	repeated string created_at_range = 1; // @tag form:"created_at_range" validate:"omitempty" zh:"创建时间范围"
	repeated string updated_at_range = 2; // @tag form:"updated_at_range" validate:"omitempty" zh:"更新时间范围"
	string keyword = 3; // @tag form:"keyword" validate:"omitempty,max=300" zh:"关键词"
	int64 status = 4; // @tag form:"status" validate:"omitempty" zh:"状态"
	int64 set_status = 5; // @tag form:"set_status" validate:"required,oneof=1 2" zh:"设置状态"
	uint64 user_id = 6; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	repeated uint64 id = 7; // @tag form:"id" validate:"omitempty" zh:"ID列表"
	string url = 8; // @tag form:"url" validate:"omitempty" zh:"URL"
	string ip = 9; // @tag form:"ip" validate:"omitempty" zh:"IP"
	uint64 website_message_id = 10; // @tag form:"website_message_id" validate:"omitempty" zh:"网站消息ID"
	repeated string title = 11; // @tag form:"title" validate:"omitempty" zh:"标题"
}

// 登录入口统计状态
message LoginAssetsCountStatusRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
}

message LoginAssetsCountStatusResponse {
	int64 confirm = 1; // @tag json:"confirm" zh:"确认数量"
	int64 default = 2; // @tag json:"default" zh:"默认数量"
	int64 ingore = 3; // @tag json:"ingore" zh:"忽略数量"
}

// 审核线索状态请求参数
message PassClueRequest {
	message DataItem {
		repeated uint64 id = 1;        // 线索ID列表
		int32 type = 2;                // 线索类型
		int32 is_all = 3;              // 是否全选 0否1是
	}

	message Keyword {
		string domain_keyword = 1;     // 域名关键词
		string cert_keyword = 2;       // 证书关键词
		string icp_keyword = 3;        // ICP关键词
		string key_keyword = 4;        // 关键词
		string ip_keyword = 5;         // IP关键词
		string subdomain_keyword = 6;  // 子域名关键词
	}

	repeated DataItem data = 1;        // 数据项
	uint64 group_id = 2;               // 分组ID
	int64 operate_company_id = 3;      // 企业ID
	Keyword keyword = 4;               // 关键词
	int32 tab_status = 5;              // 标签状态 0待确认/1已确认/2已忽略
	int32 status = 6;                  // 目标状态
	uint64 user_id = 7;                // 用户ID
	string client_ip = 8;              // 客户端IP
	uint64 operator_id = 9;            // 操作者ID
}

// 导入线索请求参数
message ImportCluesRequest {
	message ClueItem {
		int32 type = 1;                // 线索类型
		repeated string content = 2;    // 线索内容
		string clue_company_name = 3;   // 线索公司名称
	}

	repeated ClueItem data = 1;        // 线索数据
	uint64 group_id = 2;               // 分组ID
	int64 operate_company_id = 3;      // 企业ID
	int64 is_auto_expend = 4;          // 是否自动扩展
	string file = 5;                   // 文件路径
	int32 set_status = 6;              // 设置状态
	int32 is_test = 7;                 // 是否测试
	int32 is_fake_assets_task = 8;     // 是否是仿冒资产任务
	int32 is_task = 9;                 // 是否是测绘任务中添加的线索
	string company_name = 10;          // 公司名称
	uint64 user_id = 11;               // 用户ID
	string client_ip = 12;             // 客户端IP
	uint64 operator_id = 13;           // 操作者ID
}

// TaskIndexRequest 任务简报列表请求
message TaskIndexRequest {
	int64 user_id = 1; // 用户ID
	int64 operate_company_id = 2; // 操作公司ID
	string keyword = 3; // 关键词搜索
	int32 status = 4; // 状态筛选
	int32 detect_type = 5; // 检测类型
	int64 group_id = 6; // 分组ID
	string expand_source = 7; // 扩展来源
	int32 page = 8; // 页码
	int32 per_page = 9; // 每页数量
	string client_ip = 10;     // 操作者的IP地址
	repeated string created_at_range = 11; // 创建时间范围
}

// TaskIndexResponse 任务简报列表响应
message TaskIndexResponse {
	int32 current_page = 1; // 当前页
	int32 per_page = 2; // 每页数量
	int32 total = 3; // 总数
	repeated InfoResponse items = 4; // 数据列表
}

message FileExportResponse {
	string url = 1; // @tag json:"url" zh:"下载地址"
}

// 登录入口编辑
message LoginAssetsEditRequest {
	uint64 id = 1; // @tag form:"id" validate:"required" zh:"ID"
	uint64 user_id = 2; // @tag form:"user_id" validate:"required" zh:"用户ID"
	string title = 3; // @tag form:"title" validate:"omitempty" zh:"标题"
	string img = 4; // @tag form:"img" validate:"omitempty" zh:"图片"
}

// 登录入口批量导入
message LoginAssetsImportRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	repeated string url = 2; // @tag form:"url" validate:"required" zh:"URL列表"
	int64 company_id = 3; // @tag form:"company_id" validate:"omitempty" zh:"公司ID"
}

// 根据线索计算fofa资产的数量请求
message GetFofaAssetsNumRequest {
	uint64 id = 1; // 线索ID
	uint64 operate_company_id = 2; // 操作公司ID
	uint64 user_id = 3; // 用户ID
}

// 根据线索计算fofa资产的数量响应
message GetFofaAssetsNumResponse {
	uint32 num = 1; // 资产数量
	string fofa_url = 2; // FOFA URL
}

// 获取域名的Whois信息
message WhoisInfoRequest {
	uint64 id = 1; // @tag form:"id" validate:"required" zh:"线索ID"
	uint64 user_id = 2; // @tag form:"user_id" validate:"required" zh:"用户ID"
	uint64 operate_company_id = 3; // @tag form:"operate_company_id" validate:"omitempty" zh:"操作公司ID"
	int32 from = 4; // @tag form:"from" validate:"omitempty" zh:"来源类型，0:默认clues表的域名线索，1:企业线索库的域名，2:组织架构企业的域名线索"
}

message WhoisInfoResponse {
	string sponsoring_registrar = 1; // @tag json:"sponsoring_registrar" zh:"注册商"
	string registrant_name = 2; // @tag json:"registrant_name" zh:"注册人"
	string registrant_mobile = 3; // @tag json:"registrant_mobile" zh:"注册人电话"
	string registrant_org = 4; // @tag json:"registrant_org" zh:"注册组织"
	string registration_date = 5; // @tag json:"registration_date" zh:"注册日期"
	string expiration_date = 6; // @tag json:"expiration_date" zh:"到期日期"
}

// 推荐资产请求
message RecommendAssetsRequest {
	uint64 expend_id = 1; // @tag form:"expend_id" validate:"required" zh:"测绘任务ID"
	uint64 group_id = 2; // @tag form:"group_id" validate:"required" zh:"分组ID"
	uint64 user_id = 3; // @tag form:"user_id" validate:"required" zh:"用户ID"
	uint64 company_id = 4; // @tag form:"company_id" validate:"omitempty" zh:"公司ID"
	uint64 operator_id = 5; // @tag form:"operator_id" validate:"omitempty" zh:"操作者ID"
	string client_ip = 6; // @tag form:"client_ip" validate:"omitempty" zh:"客户端IP"
}

// 推荐资产响应
message RecommendAssetsResponse {
	string flag = 1; // @tag json:"flag" zh:"推荐任务标识"
}

// 证书资产列表
message CertAssetListRequest {
	int64 page = 1; // @tag form:"page" validate:"omitempty,min=1" zh:"页码"
	int64 per_page = 2; // @tag form:"per_page" validate:"omitempty,min=10,max=500" zh:"每页数量"
	string keyword = 3; // @tag form:"keyword" validate:"omitempty,max=30" zh:"关键词"
	uint64 website_message_id = 4; // @tag form:"website_message_id" validate:"omitempty" zh:"网站消息ID"
	string issuer_cn = 5; // @tag form:"issuer_cn" validate:"omitempty" zh:"颁发者"
	string issuer_org = 6; // @tag form:"issuer_org" validate:"omitempty" zh:"颁发者组织"
	string subject_cn = 7; // @tag form:"subject_cn" validate:"omitempty" zh:"使用者"
	string subject_org = 8; // @tag form:"subject_org" validate:"omitempty" zh:"使用组织"
	string ip = 9; // @tag form:"ip" validate:"omitempty" zh:"关联IP"
	string domain = 10; // @tag form:"domain" validate:"omitempty" zh:"关联域名"
	string port = 11; // @tag form:"port" validate:"omitempty" zh:"关联端口"
	repeated string company_name = 12; // @tag form:"company_name" validate:"omitempty" zh:"企业名称"
	string version = 13; // @tag form:"version" validate:"omitempty" zh:"版本"
	int64 is_self_sign = 14; // @tag form:"is_self_sign" validate:"omitempty" zh:"是否是自签名"
	int64 is_valid = 15; // @tag form:"is_valid" validate:"omitempty" zh:"证书状态"
	int64 not_after = 16; // @tag form:"not_after" validate:"omitempty" zh:"证书是否过期"
	repeated string created_at_range = 18; // @tag form:"created_at" validate:"omitempty" zh:"创建时间范围"
	repeated string updated_at_range = 19; // @tag form:"updated_at" validate:"omitempty" zh:"更新时间范围"
	uint64 user_id = 20; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	uint64 company_id = 21; // @tag form:"company_id" validate:"omitempty" zh:"企业ID"
}

message CertAssetListResponse {
	repeated CertAssetItem items = 1;
	CertAssetListCondition condition = 2;
	int64 total = 3;
	int64 current_page = 4;
	int64 per_page = 5;
}

message CertAssetListCondition {
	repeated string company_name = 1; // @tag json:"company_name" zh:"企业名称"
}

message CertAssetItem {
	uint64 id = 1;
	uint64 user_id = 2;
	uint64 company_id = 3;
	string company_name = 4;
	string sn = 5;
	string issuer_cn = 6;
	string issuer_cns = 7;
	string issuer_org = 8;
	string issuer_ou = 9;
	string cert = 10;
	int64 cert_num = 11;
	string sig_alth = 12;
	string subject_cn = 13;
	string subject_org = 14;
	string subject_ou = 15;
	string subject_key = 16;
	string valid_type = 17;
	int64 is_valid = 18;
	string version = 19;
	string sha256 = 20;
	string sha1 = 21;
	string cert_date = 22;
	string not_before = 23;
	string not_after = 24;
	string created_at = 25;
	string updated_at = 26;
	uint64 detect_task_id = 27;
	uint64 website_message_id = 28;
	int64 is_self_sign = 29;
	repeated CertAssetIpDomain ip_domain = 30;
	map<string, CertAssetDomainArray> ip_domain_list = 31;
}

message CertAssetIpDomain {
	uint64 id = 1;
	uint64 user_id = 2;
	uint64 company_id = 3;
	uint64 cert_assets_id = 4;
	string ip = 5;
	string domain = 6;
	string source = 7;
	string created_at = 8;
	string updated_at = 9;
	string deleted_at = 10;
	int64 port = 11;
	int64 is_valid = 12;
}

message CertAssetDomainList {
	repeated string domains = 1;
}

message CertAssetDomainArray {
	repeated string values = 1;
}

message KnownAssets {
	string ip = 1; // IP地址
	string domain = 2; // 域名
}

// 创建测绘报告请求
message CreateReportRequest {
	uint64 user_id = 1;
	uint64 company_id = 2;
	uint64 operate_company_id = 3;
	uint64 expend_id = 4;
	string name = 5;
	uint64 operator_id = 6;
	string client_ip = 7;
	bool is_guangfa = 8;
}

// 云端推荐结果请求
message CloudRecommendResultsRequest {
	int64 user_id = 1;
	string flag = 2;
	int32 page = 3;
	int32 per_page = 4;
	string keyword = 5;
	string ip = 6;
	repeated string domain = 7;
	repeated string cert = 8;
	repeated string icp = 9;
	repeated string logo = 10;
	repeated string title = 11;
	int32 level = 12;
	int32 date_range_type = 13;
	int32 assets_confidence_level = 14;
	repeated string port = 15;
	repeated string protocol = 16;
	int32 confirm = 17;
	repeated int32 audit = 18;
	int64 operate_company_id = 19;
	repeated string subdomain = 20;
	repeated string created_at = 21;
	repeated string updated_at = 22;
	repeated int64 op_id = 23;
	repeated int32 status = 24;
	repeated string id = 25;
	repeated string ip_array = 26;
	string whole = 27;
	repeated string cloud_name = 28;
	repeated string url = 29;
	repeated string clue_company_name = 30;
	repeated string all_company_name = 31;
	string set_level = 32;
	int32 set_assets_confidence_level = 33;
	string reason_type = 34;
	int32 chain_type = 35;
	int64 organization_task_id = 36;
	string assets_level = 37;
	int64 organization_company_id = 38;
	repeated string organization_company_name = 39;
	bool is_cycle = 40;
}

// 云端推荐结果响应
message CloudRecommendResultsResponse {
	int32 total = 1;
	int32 page = 2;
	int32 per_page = 3;
	repeated CloudRecommendResultItem items = 4;
}

// 云端推荐结果项
message CloudRecommendResultItem {
	string id = 1;
	string ip = 2;
	string port = 3;
	string protocol = 4;
	string title = 5;
	string domain = 6;
	string subdomain = 7;
	string cert = 8;
	string icp = 9;
	int32 status = 10;
	int32 audit = 11;
	bool online_state = 12;
	string source_updated_at = 13;
	string created_at = 14;
	string updated_at = 15;
	int32 ip_status = 16;
	repeated string reasons = 17;
	repeated ClueChain chain_list = 18;  // 修改为直接包含ClueChain
	// 添加新字段
	int32 group_id = 19;
	string group_name = 20;
	bool is_ipv6 = 21;
	string url = 22;
	string base_protocol = 23;
	string banner = 24;
	logo logo = 25;
	bool certs_valid = 26;
	string cloud_name = 27;
	bool is_cdn = 28;
	string cert_raw = 29;
	string product = 30;
	int32 assets_source = 31;
	string oneforall_source = 32;
	string cname = 33;
	repeated string all_company_name = 34;
	repeated string clue_company_name = 35;
	string flag = 36;
	int32 assets_confidence_level = 37;
	repeated int32 all_assets_source = 38; // 所有命中的数据源
}

// Logo定义
message logo {
	int32 hash = 1;
	string content = 2;
}

// 线索链列表
message ClueChainList {
	repeated ClueChain chains = 1;
}

// 云端推荐结果线索映射响应
message RecommendResultSearchMapResponse {
	repeated string logo = 1;
	repeated string domain = 2;
	repeated string cert = 3;
	repeated string icp = 4;
	repeated string subdomain = 5;
	repeated string port = 6;
	repeated string protocol = 7;
	repeated string title = 8;
	repeated string clue_company_name = 9;
	repeated string cloud_name = 10;
	repeated string organization_company_name = 11;
	repeated string url = 12;
}

message CloudRecommendExportRequest {
    uint64 user_id = 1; // 用户ID
    string flag = 2; // 标识
    repeated string id = 3; // 推荐结果ID
    uint32 whole = 4; // 整体导出（0表示按ID导出，1表示按条件导出）
    string keyword = 5; // 关键字
    string ip = 6; // IP
    repeated string domain = 7; // 域名
    repeated string cert = 8; // 证书
    repeated string icp = 9; // ICP备案
    repeated string logo = 10; // Logo
    string title = 11; // 标题
    repeated string port = 12; // 端口
    repeated string protocol = 13; // 协议
    repeated string subdomain = 14; // 子域名
    repeated string ip_array = 17; // IP数组
    string cloud_name = 18; // 云厂商名称
    string url = 19; // URL
    string clue_company_name = 20; // 线索公司名称
    string all_company_name = 21; // 所有公司名称
    string organization_company_name = 22; // 组织公司名称
    repeated string created_at = 23; // 创建时间范围
    repeated string updated_at = 24; // 更新时间范围
}

message CloudRecommendExportResponse {
    string url = 1; // 下载URL
}

// Hunter SQL生成请求
message HunterSqlRequest {
	int64 user_id = 1; // 用户ID
	int64 id = 2; // 任务ID
}

// Hunter SQL生成响应
message HunterSqlResponse {
	repeated string sql = 1; // SQL语句列表
}

// 证书资产列表
// 证书资产带ID列表筛选
message CertAssetExportRequest {
	repeated uint64 ids = 1; // @tag form:"ids" validate:"omitempty" zh:"ID列表"
	string keyword = 2; // @tag form:"keyword" validate:"omitempty,max=30" zh:"关键词"
	uint64 website_message_id = 3; // @tag form:"website_message_id" validate:"omitempty" zh:"网站消息ID"
	string issuer_cn = 4; // @tag form:"issuer_cn" validate:"omitempty" zh:"颁发者"
	string issuer_org = 5; // @tag form:"issuer_org" validate:"omitempty" zh:"颁发者组织"
	string subject_cn = 6; // @tag form:"subject_cn" validate:"omitempty" zh:"使用者"
	string subject_org = 7; // @tag form:"subject_org" validate:"omitempty" zh:"使用组织"
	string ip = 8; // @tag form:"ip" validate:"omitempty" zh:"关联IP"
	string domain = 9; // @tag form:"domain" validate:"omitempty" zh:"关联域名"
	string port = 10; // @tag form:"port" validate:"omitempty" zh:"关联端口"
	repeated string company_name = 11; // @tag form:"company_name" validate:"omitempty" zh:"企业名称"
	string version = 12; // @tag form:"version" validate:"omitempty" zh:"版本"
	int64 is_self_sign = 13; // @tag form:"is_self_sign" validate:"omitempty" zh:"是否是自签名"
	int64 is_valid = 14; // @tag form:"is_valid" validate:"omitempty" zh:"证书状态"
	int64 not_after = 15; // @tag form:"not_after" validate:"omitempty" zh:"证书是否过期"
	repeated string not_after_range = 16; // @tag form:"not_after_range" validate:"omitempty" zh:"证书截止日期范围"
	repeated string created_at_range = 17; // @tag form:"created_at" validate:"omitempty" zh:"创建时间范围"
	repeated string updated_at_range = 18; // @tag form:"updated_at" validate:"omitempty" zh:"更新时间范围"
	uint64 user_id = 19; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	uint64 company_id = 20; // @tag form:"company_id" validate:"omitempty" zh:"企业ID"
}

// 证书资产详情
message CertAssetDetailRequest {
	uint64 id = 1; // @tag form:"id" validate:"required" zh:"证书资产ID"
	uint64 user_id = 2; // @tag form:"user_id" validate:"required" zh:"用户ID"
}

// 导入推荐资产数据请求
message ImportResultsConfirmRequest {
	int64 user_id = 1;             // 用户ID
	int64 company_id = 2;          // 企业ID
	int64 detect_task_id = 3;      // 资产测绘任务ID
	repeated string file = 4;      // 文件路径列表
}

// 导入推荐资产数据响应
message ImportResultsConfirmResponse {
	int32 count_ips = 1;           // IP数量
	int32 count_total = 2;         // 总数
	int32 count_success_ips = 3;   // 成功导入的IP数量
	int32 count_success_total = 4; // 成功导入的总数
}

// 域名总资产列表请求
message DomainAssetsListRequest {
    int64 user_id = 1;                // 用户ID
    int64 operate_company_id = 2;     // 操作企业ID
    string keyword = 3;               // 关键字
    int32 per_page = 4;               // 每页数量
    repeated string company_name = 5;  // 企业名称
    repeated string title = 6;         // 标题
    repeated string icp = 7;           // ICP备案
    repeated string status_code = 8;   // 状态码
    repeated string created_at_range = 9; // 创建时间范围
    repeated string updated_at_range = 10; // 更新时间范围
    int32 status = 11;                // 域名状态 0/1 已失效/可解析
    repeated int32 source = 12;       // 域名来源
    repeated string domain = 13;      // 域名
    repeated string top_domain = 14;  // 顶级域名
    repeated string f_domain = 15;    // 父级域名
    string show_condition = 16;       // 是否显示筛选条件 1/不显示 0/显示
    int64 website_message_id = 17;    // 网站信息ID
    int32 type = 18;                  // 域名类型 0/1 子域名/主域名
    int32 open_parse = 19;            // 是否是泛解析  0/不是   1/是
    int32 page = 20;                  // 页码
}

// 域名总资产列表响应
message DomainAssetsListResponse {
    int64 total = 1;                  // 总数
    int32 per_page = 2;               // 每页数量
    int32 current_page = 3;           // 当前页
    int32 last_page = 4;              // 最后一页
    int32 from = 5;                   // 起始
    int32 to = 6;                     // 结束
    repeated DomainAssetsItem items = 7; // 域名资产列表
    DomainAssetsCondition condition = 8; // 筛选条件
}

// 域名资产项
message DomainAssetsItem {
    int64 id = 1;                     // ID
    int64 user_id = 2;                // 用户ID
    int64 company_id = 3;             // 企业ID
    string domain = 4;                // 域名
    string company_name = 5;          // 企业名称
    string f_domain = 6;              // 父级域名
    string top_domain = 7;            // 顶级域名
    string dns_a = 8;                 // DNS A记录
    string dns_aaaa = 9;              // DNS AAAA记录
    string cname = 10;                // CNAME
    string source = 11;               // 来源
    int32 depth = 12;                 // 深度
    int32 status = 13;                // 状态
    int32 type = 14;                  // 类型
    string created_at = 15;           // 创建时间
    string updated_at = 16;           // 更新时间
    int32 open_parse = 17;            // 是否泛解析
    string detect_task_id = 18;       // 检测任务ID
    string status_code = 19;          // 状态码
    string title = 20;                // 标题
    string icp = 21;                  // ICP备案
    int64 organization_discover_task_id = 22; // 组织发现任务ID
    int64 website_message_id = 23;    // 网站信息ID
    int64 organization_id = 24;       // 组织ID
    string custom_tags = 25;          // 自定义标签
    string uniqud = 26;               // 唯一标识
    int32 has_next = 27;              // 是否有下级 0/无 1/有
}

// 域名资产筛选条件
message DomainAssetsCondition {
    repeated string company_list = 1; // 企业列表
    repeated string domain = 2;       // 域名列表
    repeated string top_domain = 3;   // 顶级域名列表
    repeated string f_domain = 4;     // 父级域名列表
    repeated string title = 5;        // 标题列表
    repeated string status_code = 6;  // 状态码列表
}

// 域名总资产导出请求
message DomainAssetsExportRequest {
    int64 user_id = 1;                // 用户ID
    int64 operate_user_id = 2;        // 操作用户ID
    int64 company_id = 3;             // 企业ID
    int64 operate_company_id = 4;     // 操作企业ID
    string keyword = 5;               // 关键字
    repeated string company_name = 6;  // 企业名称
    repeated string title = 7;         // 标题
    repeated string icp = 8;           // ICP备案
    repeated string status_code = 9;   // 状态码
    repeated string created_at_range = 10; // 创建时间范围
    repeated string updated_at_range = 11; // 更新时间范围
    int32 status = 12;                // 域名状态 0/1 已失效/可解析
    repeated int32 source = 13;       // 域名来源
    repeated string domain = 14;      // 域名
    repeated string domain_arr = 15;  // 域名数组
    repeated string top_domain = 16;  // 顶级域名
    repeated string f_domain = 17;    // 父级域名
    int64 website_message_id = 18;    // 网站信息ID
    int32 type = 19;                  // 域名类型 0/1 子域名/主域名
    int32 open_parse = 20;            // 是否是泛解析  0/不是   1/是
    string menu = 21;                 // 菜单名称
}

// 域名总资产导出响应
message DomainAssetsExportResponse {
    string url = 1;                   // 导出文件URL
}

// 域名资产删除请求
message DomainAssetsDeleteRequest {
  int64 user_id = 1;                    // 用户ID
  int64 company_id = 2;                 // 企业ID
  string keyword = 3;                   // 关键词
  repeated string company_name = 4;     // 公司名称
  repeated string title = 5;            // 网站标题
  repeated string icp = 6;              // ICP备案信息
  string website_message_id = 7;        // 站内信ID
  repeated string status_code = 8;      // 状态码
  repeated string created_at = 9;       // 创建时间范围
  repeated string updated_at = 10;      // 更新时间范围
  string status = 11;                   // 是否可以解析 域名状态 0/1 已失效/可解析
  repeated string source = 12;          // 域名来源
  repeated string domain = 13;          // 域名列表
  string type = 14;                     // 域名类型 0/1 子域名/主域名
  string open_parse = 15;               // 是否是泛解析 0/不是 1/是
  repeated string domain_arr = 16;      // 域名数组
  repeated string top_domain = 17;      // 顶级域名
  repeated string f_domain = 18;        // 父级域名
}

// 域名资产删除响应
message DomainAssetsDeleteResponse {
  int32 delete_count = 1;               // 删除数量
}

// 资产台账 - IP资产 - IP维度列表请求
message IpAssetListRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 page = 2; // @tag form:"page" validate:"omitempty,min=1" zh:"页码" query:"-"
	int64 per_page = 3; // @tag form:"per_page" validate:"omitempty,number" zh:"每页数量" query:"-"
	optional string ip = 4; // @tag form:"ip" validate:"omitempty,min=2,max=50" zh:"IP地址" query:"ip.keyword,like"
	repeated string clue_company_name = 5; // @tag form:"clue_company_name" validate:"omitempty" zh:"线索公司名称" query:"-"
	repeated string province = 6; // @tag form:"province" validate:"omitempty" zh:"省份" query:"geo.province,in"
	repeated string title = 7; // @tag form:"title" validate:"omitempty,max=50" zh:"标题" query:"-"
	repeated string protocol = 8; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"host_list.protocol,in"
	repeated string port = 9; // @tag form:"port" validate:"omitempty" zh:"端口" query:"port_list.port,in"
	repeated string domain = 10; // @tag form:"domain" validate:"omitempty,max=50" zh:"域名" query:"-"
	optional int64 reason_type = 11; // @tag form:"reason_type" validate:"omitempty,oneof=0 1 2 3 4 5 6" zh:"原因类型" query:"port_list.reason.type"
	repeated int64 tags = 12; // @tag form:"tags" validate:"omitempty" zh:"标签" query:"tags,in"
	optional string hosts = 13; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	repeated string rule_tags = 14; // @tag form:"rule_tags" validate:"omitempty" zh:"组件信息" query:"rule_tags.cn_product.raw,in"
	optional int64 ip_match = 15; // @tag form:"ip_match" validate:"omitempty,oneof=1 2" zh:"IP匹配"
	optional int64 company_match = 16; // @tag form:"company_match" validate:"omitempty,oneof=1 2" zh:"公司匹配"
	repeated string not_in_clue_domain = 17; // @tag form:"not_in_clue_domain" validate:"omitempty,max=50" zh:"不在线索中的域名" query:"-"
	repeated string customer_tags = 18; // @tag form:"customer_tags" validate:"omitempty" zh:"客户标签" query:"customer_tags.keyword,in"
	optional uint64 website_message_id = 19; // @tag form:"website_message_id" validate:"omitempty" zh:"消息ID"
	repeated int64 http_status_code = 20; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"port_list.http_status_code,in"
	repeated string assets_source = 21; // @tag form:"assets_source" validate:"omitempty" zh:"资产来源" query:"-"
	repeated string subdomain = 22; // @tag form:"subdomain" validate:"omitempty,max=50" zh:"子域名" query:"host_list.subdomain.keyword,in"
	repeated int64 online_state = 23; // @tag form:"online_state" validate:"omitempty,oneof=0 1" zh:"在线状态" query:"online_state,in"
	repeated string updated_at = 24; // @tag form:"updated_at" validate:"omitempty" zh:"创建时间范围" query:"updated_at,range"
	repeated string created_at = 25; // @tag form:"created_at" validate:"omitempty" zh:"更新时间范围" query:"created_at,range"
	optional int64 is_cdn = 26; // @tag form:"is_cdn" validate:"omitempty,oneof=0 1" zh:"是否CDN" query:"is_cdn,bool"
	optional int64 open_parse = 27; // @tag form:"open_parse" validate:"omitempty,oneof=0 1" zh:"泛解析" query:"port_list.open_parse,bool"
	optional string cloud_name = 28; // @tag form:"cloud_name" validate:"omitempty,max=50" zh:"云厂商名称" query:"-"
	optional int64 sort = 29; // @tag form:"sort" validate:"omitempty,number" zh:"排序" query:"-"
	optional string keyword = 30; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional int64 second_confirm = 31; // @tag form:"second_confirm" validate:"omitempty,oneof=0 1" zh:"二次确认" query:"-"
}

message IpAssetListResponse {
	int64 total = 1;
	int64 per_page = 2;
	int64 current_page = 3;
	bool has_data = 4;
	bytes items = 5;
}

// GetTaskListRequest 获取扫描任务列表请求
message GetTaskListRequest {
	int64 user_id = 1;                // 用户ID
	int64 company_id = 2;             // 企业ID
	int32 page = 3;                   // 页码
	int32 per_page = 4;               // 每页数量
	int32 task_type = 5;              // 任务类型：1 资产扫描 2 漏洞扫描
	int32 status = 6;                 // 状态：0 等待中 1 扫描中 2 扫描完成 3 扫描失败 4 扫描暂停 -1 未设置
	repeated string created_at_range = 7;    // 创建时间范围
	repeated string dispatched_at_range = 8; // 下发时间范围
	repeated string end_at_range = 9;       // 结束时间范围
	string sort_field = 10;           // 排序字段：created_at, updated_at, dispatched_at, order, status
	string sort_order = 11;           // 排序方向：asc, desc
	string name = 13;                 // 任务名称
	int64 op_id = 14;                 // 操作人ID
	int32 type = 15;                  // 是否为周期任务：0 否 1 是 -1 未设置
	int64 operate_company_id = 16;    // 操作公司ID
	int64 detect_assets_tasks_id = 17; // 资产测绘任务ID
}

// GetTaskListResponse 获取扫描任务列表响应
message GetTaskListResponse {
	int32 total = 1;                  // 总数
	int32 per_page = 2;               // 每页数量
	int32 current_page = 3;           // 当前页
	int32 last_page = 4;              // 最后一页
	int32 from = 5;                   // 起始位置
	int32 to = 6;                     // 结束位置
	int32 wait_num = 7;               // 等待中的任务数量
	repeated TaskItem items = 8;      // 任务列表
}

// TaskItem 任务项
message TaskItem {
	int64 id = 1;                     // 任务ID
	int64 user_id = 2;                // 用户ID
	int64 company_id = 3;             // 企业ID
	string name = 4;                  // 任务名称
	string bandwidth = 5;             // 带宽
	string node = 6;                  // 节点
	string protocol_concurrency = 7;  // 协议并发
	int32 task_type = 8;              // 任务类型
	int32 asset_type = 9;             // 资产类型
	int32 ip_type = 10;               // IP类型
	int32 type = 11;                  // 任务类型
	string progress = 12;             // 进度
	string asset_num = 13;            // 资产数量
	string threat_num = 14;           // 威胁数量
	string rule_num = 15;             // 规则数量
	int64 order = 16;                 // 顺序
	int32 ping_switch = 17;           // Ping开关
	int32 web_logo_switch = 18;       // Web截图开关
	string use_seconds = 19;          // 用时（秒）
	int32 status = 20;                // 状态
	int32 step = 21;                  // 步骤
	int32 scan_type = 22;             // 扫描类型
	int32 poc_scan_type = 23;         // POC扫描类型
	int32 scan_range = 24;            // 扫描范围
	string start_at = 25;             // 开始时间
	string end_at = 26;               // 结束时间
	string puase_at = 27;             // 暂停时间
	string created_at = 28;           // 创建时间
	string updated_at = 29;           // 更新时间
	string deleted_at = 30;           // 删除时间
	string file_name = 31;            // 文件名
	int32 task_from = 32;             // 任务来源
	int32 cron_id = 33;               // 周期任务ID
	int64 op_id = 34;                 // 操作人ID
	int32 is_audit = 35;              // 是否审核
	int32 forbid_status = 36;         // 禁止状态
	string reason = 37;               // 原因
	int32 is_define_port = 38;        // 是否自定义端口
	string port_range = 39;           // 端口范围
	string flag = 40;                 // 标记
	int64 detect_assets_tasks_id = 41; // 资产测绘任务ID
	string audit_time = 42;           // 审核时间
	string scan_engine = 43;          // 扫描引擎
	string batch_id = 44;             // 批次ID
	int32 is_transcanner = 45;        // 是否为天融信扫描
	string tianrongxin_task_id = 46;  // 天融信任务ID
	string organization_discover_task_id = 47; // 组织发现任务ID
	string assets_sync_node = 48;     // 资产同步节点
	UserInfo user = 49;               // 用户信息
	UserInfo op = 50;                 // 操作人信息
}

// UserInfo 用户信息
message UserInfo {
	int64 id = 1;                     // 用户ID
	string name = 2;                  // 用户名称
}

message DeleteTaskRequest {
	int64 user_id = 1;                // 用户ID
	int64 company_id = 2;             // 企业ID
	int32 task_type = 3;              // 任务类型：1 资产扫描 2 漏洞扫描
	int32 status = 4;                 // 状态：0 等待中 1 扫描中 2 扫描完成 3 扫描失败 4 扫描暂停 -1 未设置
	repeated string created_at_range = 5;    // 创建时间范围
	repeated string dispatched_at_range = 6; // 下发时间范围
	int32 is_schedule = 7;           // 是否为周期任务：0 否 1 是 -1 未设置
	string name = 8;                 // 任务名称
	int64 operate_company_id = 9;    // 操作公司ID
	repeated int64 id = 10;          // 任务ID列表
	int32 type = 11;                  // 是否为周期任务：0 否 1 是 -1 未设置
}

// GetTaskDetailRequest 获取扫描任务详情请求
message GetTaskDetailRequest {
	int64 user_id = 1;                // 用户ID
	int64 company_id = 2;             // 企业ID
	int64 task_id = 3;                // 任务ID
	int64 operate_company_id = 4;     // 操作公司ID
}

// GetTaskDetailResponse 获取扫描任务详情响应
message GetTaskDetailResponse {
	int64 id = 1;                     // 任务ID
	int64 user_id = 2;                // 用户ID
	int64 company_id = 3;             // 企业ID
	string name = 4;                  // 任务名称
	string bandwidth = 5;             // 带宽
	string node = 6;                  // 节点
	string protocol_concurrency = 7;  // 协议并发
	int32 task_type = 8;              // 任务类型
	int32 asset_type = 9;             // 资产类型
	int32 ip_type = 10;               // IP类型
	int32 type = 11;                  // 任务类型
	string progress = 12;             // 进度
	string asset_num = 13;            // 资产数量
	string threat_num = 14;           // 威胁数量
	string rule_num = 15;             // 规则数量
	int64 order = 16;                 // 顺序
	int32 ping_switch = 17;           // Ping开关
	int32 web_logo_switch = 18;       // Web截图开关
	string use_seconds = 19;          // 用时（秒）
	int32 status = 20;                // 状态
	int32 step = 21;                  // 步骤
	int32 scan_type = 22;             // 扫描类型
	int32 poc_scan_type = 23;         // POC扫描类型
	int32 scan_range = 24;            // 扫描范围
	string start_at = 25;             // 开始时间
	string end_at = 26;               // 结束时间
	string puase_at = 27;             // 暂停时间
	string created_at = 28;           // 创建时间
	string updated_at = 29;           // 更新时间
	string deleted_at = 30;           // 删除时间
	string file_name = 31;            // 文件名
	int32 task_from = 32;             // 任务来源
	int32 cron_id = 33;               // 周期任务ID
	int64 op_id = 34;                 // 操作人ID
	int32 is_audit = 35;              // 是否审核
	int32 forbid_status = 36;         // 禁止状态
	string reason = 37;               // 原因
	int32 is_define_port = 38;        // 是否自定义端口
	string port_range = 39;           // 端口范围
	string flag = 40;                 // 标记
	int64 detect_assets_tasks_id = 41; // 资产测绘任务ID
	string audit_time = 42;           // 审核时间
	string scan_engine = 43;          // 扫描引擎
	string batch_id = 44;             // 批次ID
	int32 is_transcanner = 45;        // 是否为天融信扫描
	string tianrongxin_task_id = 46;  // 天融信任务ID
	string organization_discover_task_id = 47; // 组织发现任务ID
	string assets_sync_node = 48;     // 资产同步节点
	repeated int32 define_ports = 49;  // 自定义端口列表
	repeated int32 define_port_protocols = 50; // 自定义端口协议列表
	repeated PortInfo port = 51;       // 端口列表
	repeated PortGroupInfo port_group = 52; // 端口组列表
	repeated TaskIpInfo ips = 53;          // IP列表
	repeated TaskPocInfo poc = 54;         // POC列表
	repeated TaskPocGroupInfo poc_group = 55; // POC组列表
	repeated TaskHostInfo hosts = 56;      // 主机列表
	UserInfo op = 57;                  // 操作人信息
	repeated TaskProbeInfo probe_infos = 58; // 探测信息列表
	int32 table_assets_type = 59;      // 台账资产类型
}

// TaskIpInfo 任务IP信息
message TaskIpInfo {
	int64 id = 1;                     // ID
	int64 task_id = 2;                // 任务ID
	string ip = 3;                    // IP地址
}

// TaskPocInfo 任务POC信息
message TaskPocInfo {
	int64 id = 1;                     // ID
	string filename = 2;              // 文件名
	string name = 3;                  // 名称
}

// TaskPocGroupInfo 任务POC组信息
message TaskPocGroupInfo {
	int64 id = 1;                     // ID
	string name = 2;                  // 名称
	string description = 3;           // 描述
	repeated TaskPocInfo pocs = 4;    // POC列表
}

// TaskHostInfo 任务主机信息
message TaskHostInfo {
	int64 id = 1;                     // ID
	int64 task_id = 2;                // 任务ID
	repeated string urls = 3;         // URL列表
	string created_at = 4;            // 创建时间
	string updated_at = 5;            // 更新时间
}

// TaskProbeInfo 任务探测信息
message TaskProbeInfo {
	int64 id = 1;                     // ID
	int64 task_id = 2;                // 任务ID
	string ip = 3;                    // IP地址
	int32 port = 4;                   // 端口
	string base_protocol = 5;         // 基础协议
}

// GetTaskResultRequest 获取扫描任务结果请求
message GetTaskResultRequest {
	int64 user_id = 1;                // 用户ID
	int64 company_id = 2;             // 企业ID
	int64 id = 3;                     // 任务ID
	repeated int32 state = 4;         // 状态：0 离线 1 在线
	repeated string rule_tags = 5;    // 规则标签
	repeated string city = 6;         // 城市
	repeated string ports = 7;        // 端口
	repeated string protocols = 8;    // 协议
	repeated string company_tags = 9; // 公司标签
	repeated string second_cat_tag = 10; // 二级分类标签
	string keyword = 11;              // 关键词
	int64 operate_company_id = 12;    // 操作公司ID
	int32 page = 13;                  // 页码
	int32 per_page = 14;              // 每页数量
}

// GetTaskResultResponse 获取扫描任务结果响应
message GetTaskResultResponse {
	int64 total = 1;                  // 总数
	int32 per_page = 2;               // 每页数量
	int32 current_page = 3;           // 当前页
	int32 last_page = 4;              // 最后一页
	int32 from = 5;                   // 起始位置
	int32 to = 6;                     // 结束位置
	bytes items = 7;                  // 结果列表，JSON格式
	bool has_data = 8;                // 是否有数据
}

// 获取扫描任务结果筛选条件请求
message GetTaskResultConditionRequest {
	int64 id = 1;                // 任务ID
	int64 user_id = 2;           // 用户ID
	string ip = 3;               // IP地址
	repeated int32 state = 4;    // 状态
	repeated string rule_tags = 5;   // 规则标签
	repeated string city = 6;    // 城市
	repeated string ports = 7;   // 端口
	repeated string protocols = 8;   // 协议
	repeated string company_tags = 9;   // 公司标签
	repeated string second_cat_tag = 10;   // 二级分类标签
	repeated string keyword = 11;   // 关键词
	int64 operate_company_id = 12;   // 操作公司ID
}

// 获取扫描任务结果筛选条件响应
message GetTaskResultConditionResponse {
	repeated string rule_name = 1;   // 组件名称
	repeated int32 state = 2;    // 资产状态
	repeated string port_list = 3;   // 开放端口
	repeated string open_service = 4;   // 开放服务
	repeated string company_list = 5;   // 厂商
	repeated string rule_type = 6;   // 规则类型
	repeated AreaProvinceInfo area = 7;   // 地理位置
}

// 地区省份信息
message AreaProvinceInfo {
	int64 id = 1;        // 省份ID
	string name = 2;     // 省份名称
	repeated AreaCityInfo list = 3;  // 城市列表
}

// 地区城市信息
message AreaCityInfo {
	int64 id = 1;        // 城市ID
	int64 parent_id = 2; // 父级ID
	string name = 3;     // 城市名称
}

// 资产台账 - IP资产 - IP维度列表请求
message IpAssetActionRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID" query:"-"
	uint64 company_id = 2; // @tag form:"company_id" validate:"omitempty" zh:"企业ID" query:"-"
	repeated string id = 3; // @tag form:"id" validate:"omitempty" zh:"ID列表" query:"-"
	repeated string select_ip = 4; // @tag form:"select_ip" validate:"omitempty" zh:"选择的IP" query:"-"
	optional string ip = 5; // @tag form:"ip" validate:"omitempty,min=2,max=50" zh:"IP地址" query:"ip.keyword,like"
	repeated string clue_company_name = 6; // @tag form:"clue_company_name" validate:"omitempty" zh:"线索公司名称" query:"-"
	repeated string province = 7; // @tag form:"province" validate:"omitempty" zh:"省份" query:"geo.province,in"
	repeated string title = 8; // @tag form:"title" validate:"omitempty,max=50" zh:"标题" query:"-"
	repeated string protocol = 9; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"host_list.protocol,in"
	repeated string port = 10; // @tag form:"port" validate:"omitempty" zh:"端口" query:"port_list.port,in"
	repeated string domain = 11; // @tag form:"domain" validate:"omitempty,max=50" zh:"域名" query:"-"
	optional int64 reason_type = 12; // @tag form:"reason_type" validate:"omitempty,oneof=0 1 2 3 4 5 6" zh:"原因类型" query:"port_list.reason.type"
	repeated int64 tags = 13; // @tag form:"tags" validate:"omitempty" zh:"标签" query:"tags,in"
	optional string hosts = 14; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	repeated string rule_tags = 15; // @tag form:"rule_tags" validate:"omitempty" zh:"组件信息" query:"rule_tags.cn_product.raw,in"
	optional int64 ip_match = 16; // @tag form:"ip_match" validate:"omitempty,oneof=1 2" zh:"IP匹配"
	optional int64 company_match = 17; // @tag form:"company_match" validate:"omitempty,oneof=1 2" zh:"公司匹配"
	repeated string not_in_clue_domain = 18; // @tag form:"not_in_clue_domain" validate:"omitempty,max=50" zh:"不在线索中的域名" query:"-"
	repeated string customer_tags = 19; // @tag form:"customer_tags" validate:"omitempty" zh:"客户标签" query:"customer_tags.keyword,in"
	optional uint64 website_message_id = 20; // @tag form:"website_message_id" validate:"omitempty" zh:"消息ID"
	repeated int64 http_status_code = 21; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"port_list.http_status_code,in"
	repeated string assets_source = 22; // @tag form:"assets_source" validate:"omitempty" zh:"资产来源" query:"-"
	repeated string subdomain = 23; // @tag form:"subdomain" validate:"omitempty,max=50" zh:"子域名" query:"host_list.subdomain.keyword,in"
	repeated int64 online_state = 24; // @tag form:"online_state" validate:"omitempty,oneof=0 1" zh:"在线状态" query:"online_state,in"
	repeated string updated_at = 25; // @tag form:"updated_at" validate:"omitempty" zh:"创建时间范围" query:"updated_at,range"
	repeated string created_at = 26; // @tag form:"created_at" validate:"omitempty" zh:"更新时间范围" query:"created_at,range"
	optional int64 is_cdn = 27; // @tag form:"is_cdn" validate:"omitempty,oneof=0 1" zh:"是否CDN" query:"is_cdn,bool"
	optional int64 open_parse = 28; // @tag form:"open_parse" validate:"omitempty,oneof=0 1" zh:"泛解析" query:"port_list.open_parse,bool"
	optional string cloud_name = 29; // @tag form:"cloud_name" validate:"omitempty,max=50" zh:"云厂商名称" query:"-"
	optional int64 sort = 30; // @tag form:"sort" validate:"omitempty,number" zh:"排序" query:"-"
	optional string keyword = 31; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional int64 second_confirm = 32; // @tag form:"second_confirm" validate:"omitempty,oneof=0 1" zh:"二次确认" query:"-"
	optional int64 push_second_confirm = 33; // @tag form:"push_second_confirm" validate:"omitempty,oneof=0 1" zh:"设置确权" query:"-"
	optional string set_clue_company_name = 34; // @tag form:"set_clue_company_name" validate:"omitempty,max=50" zh:"要设置的企业名称" query:"-"
	optional int64 set_status = 35; // @tag form:"set_status" validate:"omitempty,oneof=0 1 2 3" zh:"设置状态" query:"-"
	optional string threaten_type = 36; // @tag form:"threaten_type"  zh:"威胁类型" query:"-"
	optional string threaten_type_name = 37; // @tag form:"threaten_type_name" validate:"omitempty,max=50" zh:"威胁类型名称" query:"-"
	optional int64 is_all = 38; // @tag form:"is_all" validate:"omitempty,oneof=0 1" zh:"是否全选" query:"-"
}

// 任务结果导出请求
message TaskResultExportRequest {
	int64 id = 1;                     // 任务ID
	int64 user_id = 2;                // 用户ID
	int64 company_id = 3;             // 企业ID
	repeated string ip_id = 4;        // IP ID列表
	string ip = 5;                    // IP地址
	repeated int32 state = 6;         // 状态：0 离线 1 在线
	repeated string rule_tags = 7;    // 规则标签
	repeated string city = 8;         // 城市
	repeated string ports = 9;        // 端口
	repeated string protocols = 10;   // 协议
	repeated string company_tags = 11;// 公司标签
	repeated string second_cat_tag = 12;// 二级分类标签
	string keyword = 13;              // 关键词
	int64 operate_company_id = 14;    // 操作公司ID
}

// 任务结果导出响应
message TaskResultExportResponse {
	string url = 1;                   // 下载URL
	string file_path = 2;             // 文件路径
	string file_name = 3;             // 文件名
}

// 扫描结果数据汇总请求
message TaskResultAnalyseRequest {
	string id = 1;                // 任务ID
	string ip = 2;                // IP过滤
	repeated int32 state = 3;     // 状态过滤 [0,1]
	repeated string rule_tags = 4;// 规则标签过滤
	repeated string city = 5;     // 城市过滤
	repeated string ports = 6;    // 端口过滤
	repeated string protocols = 7;// 协议过滤
	repeated string company_tags = 8; // 厂商标签过滤
	repeated string second_cat_tag = 9; // 二级分类标签过滤
	repeated string keyword = 10;  // 关键词过滤
	int64 user_id = 11;          // 用户ID
	int64 operate_company_id = 12; // 操作企业ID
}

// 扫描结果数据汇总响应
message TaskResultAnalyseResponse {
	int64 ip_count = 1;           // IP数量
	repeated CompanyCount company = 2; // 厂商统计
	repeated TagCount tag = 3;    // 标签统计
	repeated PortCount port = 4;  // 端口统计

	message CompanyCount {
		string name = 1;          // 厂商名称
		int32 num = 2;            // 数量
	}

	message TagCount {
		string name = 1;          // 标签名称
		int32 num = 2;            // 数量
	}

	message PortCount {
		string name = 1;          // 端口名称
		int32 num = 2;            // 数量
	}
}

// IP资产筛选条件请求
message IpAssetConditionRequest {
	optional int32 type = 1;               // @tag form:"type" validate:"omitempty,integer" zh:"类型"
	optional int32 second_confirm = 2;    // @tag form:"second_confirm" validate:"omitempty" zh:"二次确认" query:"-"
	repeated int32 status = 3;    // @tag form:"status" validate:"required" zh:"状态" query:"status,in"
	uint64 user_id = 4;            // @tag form:"user_id" validate:"required" zh:"用户ID"
}
// IP资产筛选条件响应
message IpAssetConditionResponse {
	repeated string protocol = 1;            // @tag json:"protocol" zh:"协议"
	repeated int64 port = 2;                // @tag json:"port" zh:"端口"
	repeated int64 online_state = 3;        // @tag json:"online_state" zh:"在线状态"
	repeated string subdomain = 4;           // @tag json:"subdomain" zh:"子域名"
	repeated string domain = 5;              // @tag json:"domain" zh:"域名"
	repeated string not_in_clue_domain = 6;  // @tag json:"not_in_clue_domain" zh:"不在线索库域名"
	repeated string cert = 7;                // @tag json:"cert" zh:"证书"
	repeated string icp = 8;                 // @tag json:"icp" zh:"ICP备案号"
	repeated string title = 9;               // @tag json:"title" zh:"标题"
	repeated int64 http_status_code = 10;   // @tag json:"http_status_code" zh:"HTTP状态码"
	repeated string rule_tags = 11;          // @tag json:"rule_tags" zh:"规则标签"
	repeated string cname = 12;              // @tag json:"cname" zh:"CNAME"
	repeated string asn = 13;                // @tag json:"asn" zh:"ASN"
	repeated string province = 14;           // @tag json:"province" zh:"省份"
	repeated string isp = 15;                // @tag json:"isp" zh:"ISP"
	repeated string cloud_name = 16;         // @tag json:"cloud_name" zh:"云厂商"
	repeated string customer_tags = 17;      // @tag json:"customer_tags" zh:"客户标签"
	repeated string clue_company_name = 18;  // @tag json:"clue_company_name" zh:"线索公司名称"
}


// IP资产筛选条件请求
message IpPortAssetConditionRequest {
	optional int32 type = 1;               // @tag form:"type" validate:"omitempty,integer" zh:"类型"
	repeated int32 status = 2;    // @tag form:"status" validate:"required" zh:"状态" query:"status,in"
	uint64 user_id = 3;            // @tag form:"user_id" validate:"required" zh:"用户ID"
}
// IP资产筛选条件响应
message IpPortAssetConditionResponse {
	repeated string protocol = 1;            // @tag json:"protocol" zh:"协议"
	repeated int64 port = 2;                // @tag json:"port" zh:"端口"
	repeated int64 online_state = 3;        // @tag json:"online_state" zh:"在线状态"
	repeated string subdomain = 4;           // @tag json:"subdomain" zh:"子域名"
	repeated string domain = 5;              // @tag json:"domain" zh:"域名"
	repeated string not_in_clue_domain = 6;  // @tag json:"not_in_clue_domain" zh:"不在线索库域名"
	repeated string cert = 7;                // @tag json:"cert" zh:"证书"
	repeated string icp = 8;                 // @tag json:"icp" zh:"ICP备案号"
	repeated string title = 9;               // @tag json:"title" zh:"标题"
	repeated int64 http_status_code = 10;   // @tag json:"http_status_code" zh:"HTTP状态码"
	repeated string rule_tags = 11;          // @tag json:"rule_tags" zh:"规则标签"
	repeated string cname = 12;              // @tag json:"cname" zh:"CNAME"
	repeated string asn = 13;                // @tag json:"asn" zh:"ASN"
	repeated string province = 14;           // @tag json:"province" zh:"省份"
	repeated string isp = 15;                // @tag json:"isp" zh:"ISP"
	repeated string cloud_name = 16;         // @tag json:"cloud_name" zh:"云厂商"
	repeated string customer_tags = 17;      // @tag json:"customer_tags" zh:"客户标签"
	repeated string clue_company_name = 18;  // @tag json:"clue_company_name" zh:"线索公司名称"
	repeated string url = 19;                // @tag json:"url" zh:"URL"
}

// IP维度推荐结果请求
message GroupIpResultsRequest {
	int64 user_id = 1;             // 用户ID
	int64 company_id = 2;          // 企业ID
	string flag = 3;               // 标识
	int32 page = 4;                // 页码
	int32 per_page = 5;            // 每页数量
	bool is_cycle = 6;             // 是否循环取数据
	string ip = 7;                 // IP
	string keyword = 8;            // 关键词
	repeated string domain = 9;    // 域名
	repeated string cert = 10;     // 证书
	repeated string icp = 11;      // ICP备案
	repeated string logo = 12;     // Logo
	repeated string title = 13;    // 标题
	repeated string port = 14;     // 端口
	repeated string protocol = 15; // 协议
	repeated string subdomain = 16;// 子域名
	string clue_company_name = 17; // 线索公司名称
	int32 date_range_type = 18;    // 日期范围类型：1=一年内，2=半年内，3=三个月内
	repeated string ip_array = 19; // IP数组
	int32 reason_type = 20;        // 原因类型
	int64 organization_company_id = 21; // 组织公司ID
	int32 assets_level = 22;       // 资产级别
	int32 chain_type = 23;         // 链类型
	repeated string all_company_name = 24; // 所有公司名称
	repeated string title_not = 25; // 标题不包含
	repeated string domain_not = 26; // 域名不包含
	string cloud_name = 27;        // 云厂商名称
	string organization_company_name = 28; // 组织公司名称
	repeated int32 level = 29;     // 级别
	repeated int32 assets_confidence_level = 30; // 资产置信度级别
	string url = 31;               // URL
	repeated string created_at = 32; // 创建时间范围
	repeated string updated_at = 33; // 更新时间范围
	bool whole = 34;               // 是否全部
}

// IP维度推荐结果响应
message GroupIpResultsResponse {
	int64 total = 1;               // 总数
	int32 per_page = 2;            // 每页数量
	int32 current_page = 3;        // 当前页
	int32 last_page = 4;           // 最后一页
	int32 from = 5;                // 起始位置
	int32 to = 6;                  // 结束位置
	string items = 7;              // JSON格式的结果项
}

// 删除推荐结果请求
message DeleteRecommendResultRequest {
	int64 user_id = 1;             // 用户ID
	int64 company_id = 2;          // 企业ID
	string flag = 3;               // 标识
	repeated string ip_array = 4;  // IP数组
	bool whole = 5;                // 是否全部
	int64 operate_company_id = 6;  // 操作企业ID
}

// 删除推荐结果响应
message DeleteRecommendResultResponse {
	int32 count = 1;               // 删除数量
}

// 更新资产可信度请求
message UpdateAssetsConfidenceLevelRequest {
	int64 user_id = 1;             // 用户ID
	int64 company_id = 2;          // 企业ID
	string flag = 3;               // 标识
	repeated string ip_array = 4;  // IP数组
	int32 set_assets_confidence_level = 5; // 设置的可信度级别
	int64 operate_company_id = 6;  // 操作企业ID
}

// 更新资产可信度响应
message UpdateAssetsConfidenceLevelResponse {
	int32 count = 1;               // 更新数量
}

// 资产测绘关联异步任务请求
message TaskRelateRequest {
	int64 user_id = 1;                // 用户ID
	int64 company_id = 2;             // 企业ID
	int64 expend_id = 3;              // 资产测绘任务ID
	int32 is_extract_clue = 4;        // 是否自动提取线索 0/1 不自动提取/自动提取
	int32 is_check_risk = 5;          // 是否自动监测风险事件 0/1 不自动/自动
	int32 is_auto_business_api = 6;   // 是否自动同步url资产到业务系统 0/1 不自动/自动
}

// 资产测绘结果列表请求
message DetectAssetsResultListRequest {
	int64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID" query:"-"
	int64 company_id = 2; // @tag form:"company_id" validate:"omitempty" zh:"企业ID" query:"-"
	int64 expend_id = 3;                  // @tag form:"expend_id" validate:"required" zh:"资产测绘任务ID" query:"-"
	int32 status = 4;                     // @tag form:"status" validate:"omitempty,oneof=0 1 3" zh:"状态" query:"-"
	int32 page = 5;                       // @tag form:"page" validate:"omitempty,min=1" zh:"页码" query:"-"
	int32 per_page = 6;                   // @tag form:"per_page" validate:"omitempty,number" zh:"每页数量" query:"-"
	optional string ip = 7;               // @tag form:"ip" validate:"omitempty" zh:"IP地址" query:"ip.keyword,like"
	repeated string clue_company_name = 8; // @tag form:"clue_company_name" validate:"omitempty" zh:"企业名称过滤" query:"-"
	repeated string province = 9;         // @tag form:"province" validate:"omitempty" zh:"省份过滤" query:"geo.province,in"
	optional string keyword = 10;         // @tag form:"keyword" validate:"omitempty" zh:"关键词过滤" query:"-"
	optional string asn = 11;             // @tag form:"asn" validate:"omitempty" zh:"ASN过滤" query:"asn.keyword,like"
	repeated string cert = 12;            // @tag form:"cert" validate:"omitempty" zh:"证书过滤" query:"-"
	repeated string icp = 13;             // @tag form:"icp" validate:"omitempty" zh:"ICP过滤" query:"-"
	optional string url = 14;             // @tag form:"url" validate:"omitempty" zh:"URL过滤" query:"port_list.url.keyword,like"
	repeated string title = 15;           // @tag form:"title" validate:"omitempty" zh:"标题过滤" query:"-"
	repeated string protocol = 16;        // @tag form:"protocol" validate:"omitempty" zh:"协议过滤" query:"port_list.protocol,in"
	repeated string logo = 17;            // @tag form:"logo" validate:"omitempty" zh:"Logo过滤" query:"-"
	repeated string port = 18;            // @tag form:"port" validate:"omitempty" zh:"端口过滤" query:"port_list.port,in"
	optional string cname = 19;           // @tag form:"cname" validate:"omitempty" zh:"CNAME过滤" query:"cname.keyword,like"
	optional string latitude = 20;        // @tag form:"latitude" validate:"omitempty" zh:"纬度过滤" query:"geo.latitude,like"
	optional string longitude = 21;       // @tag form:"longitude" validate:"omitempty" zh:"经度过滤" query:"geo.longitude,like"
	optional string state = 22;           // @tag form:"state" validate:"omitempty" zh:"状态过滤" query:"geo.state,like"
	optional string reason = 23;          // @tag form:"reason" validate:"omitempty" zh:"原因过滤" query:"port_list.reason.content,like"
	optional string reason_type = 24;     // @tag form:"reason_type" validate:"omitempty" zh:"原因类型过滤" query:"port_list.reason.type,like"
	optional string tags = 25;            // @tag form:"tags" validate:"omitempty" zh:"标签过滤" query:"tags,like"
	optional string hosts = 26;           // @tag form:"hosts" validate:"omitempty" zh:"主机过滤" query:"-"
	optional int32 second_confirm = 27;   // @tag form:"second_confirm" validate:"omitempty,oneof=0 1" zh:"二次确认" query:"-"
	optional int64 operate_company_id = 28; // @tag form:"operate_company_id" validate:"omitempty" zh:"操作企业ID" query:"-"
	optional bool is_all = 29;            // @tag form:"is_all" validate:"omitempty" zh:"是否全部" query:"-"
	repeated string domain = 30;          // @tag form:"domain" validate:"omitempty" zh:"域名过滤" query:"-"
	repeated string subdomain = 31;       // @tag form:"subdomain" validate:"omitempty" zh:"子域名过滤" query:"host_list.subdomain.keyword,in"
}

// 资产测绘结果列表响应
message DetectAssetsResultListResponse {
	int64 konwn_table_num = 1;     // 已知资产数量
	int64 unknown_table_num = 2;   // 未知资产数量
	int64 threat_table_num = 3;    // 威胁资产数量
	int32 current_page = 4;        // 当前页
	int32 per_page = 5;            // 每页数量
	int64 total = 6;               // 总数量
	string data = 7;               // 数据（JSON格式）
	map<string, FilterCondition> condition = 8; // 筛选条件
}

// 筛选条件
message FilterCondition {
	repeated string values = 1;    // 条件值列表
}

// 疑似资产列表请求
message UnsureAssetsListRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 page = 2; // @tag form:"page" validate:"omitempty,min=1" zh:"页码" query:"-"
	int64 per_page = 3; // @tag form:"per_page" validate:"omitempty,number" zh:"每页数量" query:"-"
	optional string ip = 4; // @tag form:"ip" validate:"omitempty,min=2,max=50" zh:"IP地址" query:"ip.keyword,like"
	repeated string clue_company_name = 5; // @tag form:"clue_company_name" validate:"omitempty" zh:"线索公司名称" query:"-"
	repeated string province = 6; // @tag form:"province" validate:"omitempty" zh:"省份" query:"geo.province,in"
	repeated string title = 7; // @tag form:"title" validate:"omitempty,max=50" zh:"标题" query:"-"
	repeated string protocol = 8; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"host_list.protocol,in"
	repeated string port = 9; // @tag form:"port" validate:"omitempty" zh:"端口" query:"port_list.port,in"
	repeated string domain = 10; // @tag form:"domain" validate:"omitempty,max=50" zh:"域名" query:"-"
	optional int64 reason_type = 11; // @tag form:"reason_type" validate:"omitempty,oneof=0 1 2 3 4 5 6" zh:"原因类型" query:"port_list.reason.type"
	repeated int64 tags = 12; // @tag form:"tags" validate:"omitempty" zh:"标签" query:"tags,in"
	optional string hosts = 13; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	repeated string rule_tags = 14; // @tag form:"rule_tags" validate:"omitempty" zh:"组件信息" query:"rule_tags.cn_product.raw,in"
	optional int64 ip_match = 15; // @tag form:"ip_match" validate:"omitempty,oneof=1 2" zh:"IP匹配"
	optional int64 company_match = 16; // @tag form:"company_match" validate:"omitempty,oneof=1 2" zh:"公司匹配"
	repeated string not_in_clue_domain = 17; // @tag form:"not_in_clue_domain" validate:"omitempty,max=50" zh:"不在线索中的域名" query:"-"
	repeated string customer_tags = 18; // @tag form:"customer_tags" validate:"omitempty" zh:"客户标签" query:"customer_tags.keyword,in"
	optional uint64 website_message_id = 19; // @tag form:"website_message_id" validate:"omitempty" zh:"消息ID"
	repeated int64 http_status_code = 20; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"port_list.http_status_code,in"
	repeated string assets_source = 21; // @tag form:"assets_source" validate:"omitempty" zh:"资产来源" query:"-"
	repeated string subdomain = 22; // @tag form:"subdomain" validate:"omitempty,max=50" zh:"子域名" query:"host_list.subdomain.keyword,in"
	repeated int64 online_state = 23; // @tag form:"online_state" validate:"omitempty,oneof=0 1" zh:"在线状态" query:"online_state,in"
	repeated string updated_at = 24; // @tag form:"updated_at" validate:"omitempty" zh:"创建时间范围" query:"updated_at,range"
	repeated string created_at = 25; // @tag form:"created_at" validate:"omitempty" zh:"更新时间范围" query:"created_at,range"
	optional int64 is_cdn = 26; // @tag form:"is_cdn" validate:"omitempty,oneof=0 1" zh:"是否CDN" query:"is_cdn,bool"
	optional int64 open_parse = 27; // @tag form:"open_parse" validate:"omitempty,oneof=0 1" zh:"泛解析" query:"port_list.open_parse,bool"
	optional string cloud_name = 28; // @tag form:"cloud_name" validate:"omitempty,max=50" zh:"云厂商名称" query:"-"
	optional int64 sort = 29; // @tag form:"sort" validate:"omitempty,number" zh:"排序" query:"-"
	optional string keyword = 30; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional int64 second_confirm = 31; // @tag form:"second_confirm" validate:"omitempty,oneof=0 1" zh:"二次确认" query:"-"
}

// 疑似资产列表响应
message UnsureAssetsListResponse {
	int64 total = 1;               // 总数
	int32 per_page = 2;            // 每页数量
	int32 current_page = 3;        // 当前页
	int32 last_page = 4;           // 最后一页
	int32 from = 5;                // 起始位置
	int32 to = 6;                  // 结束位置
	string data = 7;               // JSON格式的结果项
}

// 忽略资产列表请求
message IgnoreAssetsListRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 page = 2; // @tag form:"page" validate:"omitempty,min=1" zh:"页码" query:"-"
	int64 per_page = 3; // @tag form:"per_page" validate:"omitempty,number" zh:"每页数量" query:"-"
	optional string ip = 4; // @tag form:"ip" validate:"omitempty,min=2,max=50" zh:"IP地址" query:"ip.keyword,like"
	repeated string clue_company_name = 5; // @tag form:"clue_company_name" validate:"omitempty" zh:"线索公司名称" query:"-"
	repeated string province = 6; // @tag form:"province" validate:"omitempty" zh:"省份" query:"geo.province,in"
	repeated string title = 7; // @tag form:"title" validate:"omitempty,max=50" zh:"标题" query:"-"
	repeated string protocol = 8; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"host_list.protocol,in"
	repeated string port = 9; // @tag form:"port" validate:"omitempty" zh:"端口" query:"port_list.port,in"
	repeated string domain = 10; // @tag form:"domain" validate:"omitempty,max=50" zh:"域名" query:"-"
	optional int64 reason_type = 11; // @tag form:"reason_type" validate:"omitempty,oneof=0 1 2 3 4 5 6" zh:"原因类型" query:"port_list.reason.type"
	repeated int64 tags = 12; // @tag form:"tags" validate:"omitempty" zh:"标签" query:"tags,in"
	optional string hosts = 13; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	repeated string rule_tags = 14; // @tag form:"rule_tags" validate:"omitempty" zh:"组件信息" query:"rule_tags.cn_product.raw,in"
	optional int64 ip_match = 15; // @tag form:"ip_match" validate:"omitempty,oneof=1 2" zh:"IP匹配"
	optional int64 company_match = 16; // @tag form:"company_match" validate:"omitempty,oneof=1 2" zh:"公司匹配"
	repeated string not_in_clue_domain = 17; // @tag form:"not_in_clue_domain" validate:"omitempty,max=50" zh:"不在线索中的域名" query:"-"
	repeated string customer_tags = 18; // @tag form:"customer_tags" validate:"omitempty" zh:"客户标签" query:"customer_tags.keyword,in"
	optional uint64 website_message_id = 19; // @tag form:"website_message_id" validate:"omitempty" zh:"消息ID"
	repeated int64 http_status_code = 20; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"port_list.http_status_code,in"
	repeated string assets_source = 21; // @tag form:"assets_source" validate:"omitempty" zh:"资产来源" query:"-"
	repeated string subdomain = 22; // @tag form:"subdomain" validate:"omitempty,max=50" zh:"子域名" query:"host_list.subdomain.keyword,in"
	repeated int64 online_state = 23; // @tag form:"online_state" validate:"omitempty,oneof=0 1" zh:"在线状态" query:"online_state,in"
	repeated string updated_at = 24; // @tag form:"updated_at" validate:"omitempty" zh:"创建时间范围" query:"updated_at,range"
	repeated string created_at = 25; // @tag form:"created_at" validate:"omitempty" zh:"更新时间范围" query:"created_at,range"
	optional int64 is_cdn = 26; // @tag form:"is_cdn" validate:"omitempty,oneof=0 1" zh:"是否CDN" query:"is_cdn,bool"
	optional int64 open_parse = 27; // @tag form:"open_parse" validate:"omitempty,oneof=0 1" zh:"泛解析" query:"port_list.open_parse,bool"
	optional string cloud_name = 28; // @tag form:"cloud_name" validate:"omitempty,max=50" zh:"云厂商名称" query:"-"
	optional int64 sort = 29; // @tag form:"sort" validate:"omitempty,number" zh:"排序" query:"-"
	optional string keyword = 30; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional int64 second_confirm = 31; // @tag form:"second_confirm" validate:"omitempty,oneof=0 1" zh:"二次确认" query:"-"
}

// 忽略资产列表响应
message IgnoreAssetsListResponse {
  int64 total = 1;        // 总数
  int32 current_page = 2; // 当前页
  int32 per_page = 3;     // 每页数量
  int32 last_page = 4;    // 最后一页页码
  int32 from = 5;         // 起始记录
  int32 to = 6;           // 结束记录
  string data = 7;        // 数据，JSON格式
}

// 威胁资产列表请求
message ThreatenAssetsListRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 page = 2; // @tag form:"page" validate:"omitempty,min=1" zh:"页码" query:"-"
	int64 per_page = 3; // @tag form:"per_page" validate:"omitempty,number" zh:"每页数量" query:"-"
	optional string ip = 4; // @tag form:"ip" validate:"omitempty,min=2,max=50" zh:"IP地址" query:"ip.keyword,like"
	repeated string clue_company_name = 5; // @tag form:"clue_company_name" validate:"omitempty" zh:"线索公司名称" query:"-"
	repeated string province = 6; // @tag form:"province" validate:"omitempty" zh:"省份" query:"geo.province,in"
	repeated string title = 7; // @tag form:"title" validate:"omitempty,max=50" zh:"标题" query:"-"
	repeated string protocol = 8; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"host_list.protocol,in"
	repeated string port = 9; // @tag form:"port" validate:"omitempty" zh:"端口" query:"port_list.port,in"
	repeated string domain = 10; // @tag form:"domain" validate:"omitempty,max=50" zh:"域名" query:"-"
	optional int64 reason_type = 11; // @tag form:"reason_type" validate:"omitempty,oneof=0 1 2 3 4 5 6" zh:"原因类型" query:"port_list.reason.type"
	repeated int64 tags = 12; // @tag form:"tags" validate:"omitempty" zh:"标签" query:"tags,in"
	optional string hosts = 13; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	repeated string rule_tags = 14; // @tag form:"rule_tags" validate:"omitempty" zh:"组件信息" query:"rule_tags.cn_product.raw,in"
	optional int64 ip_match = 15; // @tag form:"ip_match" validate:"omitempty,oneof=1 2" zh:"IP匹配"
	optional int64 company_match = 16; // @tag form:"company_match" validate:"omitempty,oneof=1 2" zh:"公司匹配"
	repeated string not_in_clue_domain = 17; // @tag form:"not_in_clue_domain" validate:"omitempty,max=50" zh:"不在线索中的域名" query:"-"
	repeated string customer_tags = 18; // @tag form:"customer_tags" validate:"omitempty" zh:"客户标签" query:"customer_tags.keyword,in"
	optional uint64 website_message_id = 19; // @tag form:"website_message_id" validate:"omitempty" zh:"消息ID"
	repeated int64 http_status_code = 20; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"port_list.http_status_code,in"
	repeated string assets_source = 21; // @tag form:"assets_source" validate:"omitempty" zh:"资产来源" query:"-"
	repeated string subdomain = 22; // @tag form:"subdomain" validate:"omitempty,max=50" zh:"子域名" query:"host_list.subdomain.keyword,in"
	repeated int64 online_state = 23; // @tag form:"online_state" validate:"omitempty,oneof=0 1" zh:"在线状态" query:"online_state,in"
	repeated string updated_at = 24; // @tag form:"updated_at" validate:"omitempty" zh:"创建时间范围" query:"updated_at,range"
	repeated string created_at = 25; // @tag form:"created_at" validate:"omitempty" zh:"更新时间范围" query:"created_at,range"
	optional int64 is_cdn = 26; // @tag form:"is_cdn" validate:"omitempty,oneof=0 1" zh:"是否CDN" query:"is_cdn,bool"
	optional int64 open_parse = 27; // @tag form:"open_parse" validate:"omitempty,oneof=0 1" zh:"泛解析" query:"port_list.open_parse,bool"
	optional string cloud_name = 28; // @tag form:"cloud_name" validate:"omitempty,max=50" zh:"云厂商名称" query:"-"
	optional int64 sort = 29; // @tag form:"sort" validate:"omitempty,number" zh:"排序" query:"-"
	optional string keyword = 30; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional int64 second_confirm = 31; // @tag form:"second_confirm" validate:"omitempty,oneof=0 1" zh:"二次确认" query:"-"
	repeated int64 threaten_type_arr = 32; // @tag form:"threaten_type_arr" validate:"omitempty" zh:"威胁类型" query:"threaten_type,in"
}

// 威胁资产列表响应
message ThreatenAssetsListResponse {
  int64 total = 1;           // 总记录数
  int32 per_page = 2;        // 每页数量
  int32 current_page = 3;    // 当前页码
  int32 last_page = 4;       // 最后一页页码
  int32 from = 5;            // 起始记录
  int32 to = 6;              // 结束记录
  string data = 7;           // 数据，JSON格式
}


// 台账资产列表(新版)请求
message SurePassetsListRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 page = 2; // @tag form:"page" validate:"omitempty,min=1" zh:"页码" query:"-"
	int64 per_page = 3; // @tag form:"per_page" validate:"omitempty,number" zh:"每页数量" query:"-"
	optional string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP地址" query:"ip.keyword,like"
	repeated string province = 5; // @tag query:"geo.province,in" zh:"省份"
	repeated string title = 6; // @tag query:"title,in" zh:"标题" query:"-"
	repeated string protocol = 7; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"protocol,in"
	repeated string port = 8; // @tag query:"port,in" zh:"端口"
	repeated string domain = 9; // @tag query:"domain,in" zh:"域名" query:"-"
	repeated string subdomain = 10; // @tag query:"subdomain,in" zh:"子域名"
	repeated string url = 11; // @tag query:"url.keyword,in" zh:"URL"
	repeated int32 http_status_code = 12; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"http_status_code,in"
	repeated int32 online_state = 14; // @tag form:"online_state" validate:"omitempty" query:"online_state,in" zh:"在线状态"
	optional string keyword = 15; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional string hosts = 16; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	optional string cert = 17; // @tag query:"cert.subject_key,like" zh:"证书"
	optional string icp = 18; // @tag form:"icp" validate:"omitempty" zh:"ICP备案" query:"icp.no,like"
	optional string cname = 19; // @tag form:"cname" validate:"omitempty" zh:"CNAME" query:"cname,like"
	optional string cloud_name = 20; // @tag query:"cloud_name,like" zh:"云厂商名称" query:"-"
	repeated string clue_company_name = 21; // @tag form:"clue_company_name" zh:"线索公司名称" query:"-"
	repeated string rule_tags = 22; // @tag form:"rule_tags" query:"rule_tags.cn_product.raw,in" zh:"组件信息"
	repeated string assets_source = 23; // @tag query:"assets_source,in" zh:"资产来源" query:"-"
	repeated string customer_tags = 24; // @tag query:"customer_tags,in" zh:"客户标签"
	optional int32 sort = 25; // zh:"排序方式 0:IP升序 1:更新时间降序"
	optional int32 second_confirm = 26; // @tag query:"second_confirm,in" zh:"二次确认"
	optional int32 type = 27; // @tag query:"type" zh:"资产类型"
	optional string asn = 28; // @tag form:"asn" validate:"omitempty" zh:"ASN" query:"geo.asn"
	optional string logo = 29; // @tag form:"logo" validate:"omitempty" zh:"Logo哈希" query:"logo.hash"
	optional int32 is_cdn = 31; // @tag query:"is_cdn,bool" zh:"是否CDN"
	optional int32 open_parse = 32; // @tag query:"open_parse,bool" zh:"泛解析"
	repeated string updated_at = 33; // @tag form:"updated_at" validate:"omitempty" zh:"更新时间范围" query:"updated_at,range"
	repeated string created_at = 34; // @tag form:"created_at" validate:"omitempty" zh:"创建时间范围" query:"created_at,range"
	optional string latitude = 35; // @tag form:"latitude" validate:"omitempty" zh:"纬度" query:"geo.lat"
	optional string longitude = 36; // @tag form:"longitude" validate:"omitempty" zh:"经度" query:"geo.lon"
	repeated string isp = 37; // @tag query:"geo.isp,in" zh:"ISP"
	optional int32 reason = 38; // @tag form:"reason" validate:"omitempty" zh:"原因" query:"reason.type"
}

// 疑似资产列表(新版)请求
message PassetsListRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 page = 2; // @tag form:"page" validate:"omitempty,min=1" zh:"页码" query:"-"
	int64 per_page = 3; // @tag form:"per_page" validate:"omitempty,number" zh:"每页数量" query:"-"
	optional string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP地址" query:"ip.keyword,like"
	repeated string province = 5; // @tag query:"geo.province,in" zh:"省份"
	repeated string title = 6; // @tag query:"title,in" zh:"标题" query:"-"
	repeated string protocol = 7; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"protocol,in"
	repeated string port = 8; // @tag query:"port,in" zh:"端口"
	repeated string domain = 9; // @tag query:"domain,in" zh:"域名" query:"-"
	repeated string subdomain = 10; // @tag query:"subdomain,in" zh:"子域名"
	repeated string url = 11; // @tag query:"url.keyword,in" zh:"URL"
	repeated int32 http_status_code = 12; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"http_status_code,in"
	repeated int32 online_state = 14; // @tag form:"online_state" validate:"omitempty" query:"online_state,in" zh:"在线状态"
	optional string keyword = 15; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional string hosts = 16; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	optional string cert = 17; // @tag query:"cert.subject_key,like" zh:"证书"
	optional string icp = 18; // @tag form:"icp" validate:"omitempty" zh:"ICP备案" query:"icp.no,like"
	optional string cname = 19; // @tag form:"cname" validate:"omitempty" zh:"CNAME" query:"cname,like"
	optional string cloud_name = 20; // @tag query:"cloud_name,like" zh:"云厂商名称" query:"-"
	repeated string clue_company_name = 21; // @tag form:"clue_company_name" zh:"线索公司名称" query:"-"
	repeated string rule_tags = 22; // @tag form:"rule_tags" query:"rule_tags.cn_product.raw,in" zh:"组件信息"
	repeated string assets_source = 23; // @tag query:"assets_source,in" zh:"资产来源" query:"-"
	repeated string customer_tags = 24; // @tag query:"customer_tags,in" zh:"客户标签"
	optional int32 sort = 25; // zh:"排序方式 0:IP升序 1:更新时间降序"
	optional int32 second_confirm = 26; // @tag query:"second_confirm,in" zh:"二次确认"
	optional int32 type = 27; // @tag query:"type" zh:"资产类型"
	optional string asn = 28; // @tag form:"asn" validate:"omitempty" zh:"ASN" query:"geo.asn"
	optional string logo = 29; // @tag form:"logo" validate:"omitempty" zh:"Logo哈希" query:"logo.hash"
	optional int32 is_cdn = 31; // @tag query:"is_cdn,bool" zh:"是否CDN"
	optional int32 open_parse = 32; // @tag query:"open_parse,bool" zh:"泛解析"
	repeated string updated_at = 33; // @tag form:"updated_at" validate:"omitempty" zh:"更新时间范围" query:"updated_at,range"
	repeated string created_at = 34; // @tag form:"created_at" validate:"omitempty" zh:"创建时间范围" query:"created_at,range"
	optional string latitude = 35; // @tag form:"latitude" validate:"omitempty" zh:"纬度" query:"geo.lat"
	optional string longitude = 36; // @tag form:"longitude" validate:"omitempty" zh:"经度" query:"geo.lon"
	repeated string isp = 37; // @tag query:"geo.isp,in" zh:"ISP"
	optional int32 reason = 38; // @tag form:"reason" validate:"omitempty" zh:"原因" query:"reason.type"
}

// IP端口维度post类的操作请求体
message IpPortActionRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	repeated string id = 2; // @tag form:"id" validate:"omitempty" zh:"ID列表" query:"-"
	optional string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP地址" query:"ip.keyword,like"
	repeated string province = 5; // @tag query:"geo.province,in" zh:"省份"
	repeated string title = 6; // @tag query:"title,in" zh:"标题" query:"-"
	repeated string protocol = 7; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"protocol,in"
	repeated string port = 8; // @tag query:"port,in" zh:"端口"
	repeated string domain = 9; // @tag query:"domain,in" zh:"域名" query:"-"
	repeated string subdomain = 10; // @tag query:"subdomain,in" zh:"子域名"
	repeated string url = 11; // @tag query:"url.keyword,in" zh:"URL"
	repeated int32 http_status_code = 12; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"http_status_code,in"
	repeated int32 online_state = 14; // @tag form:"online_state" validate:"omitempty" query:"online_state,in" zh:"在线状态"
	optional string keyword = 15; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional string hosts = 16; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	optional string cert = 17; // @tag query:"cert.subject_key,like" zh:"证书"
	optional string icp = 18; // @tag form:"icp" validate:"omitempty" zh:"ICP备案" query:"icp.no,like"
	optional string cname = 19; // @tag form:"cname" validate:"omitempty" zh:"CNAME" query:"cname,like"
	optional string cloud_name = 20; // @tag query:"cloud_name,like" zh:"云厂商名称" query:"-"
	repeated string clue_company_name = 21; // @tag form:"clue_company_name" zh:"线索公司名称" query:"-"
	repeated string rule_tags = 22; // @tag form:"rule_tags" query:"rule_tags.cn_product.raw,in" zh:"组件信息"
	repeated string assets_source = 23; // @tag query:"assets_source,in" zh:"资产来源" query:"-"
	repeated string customer_tags = 24; // @tag query:"customer_tags,in" zh:"客户标签"
	optional int32 sort = 25; // zh:"排序方式 0:IP升序 1:更新时间降序"
	optional int32 second_confirm = 26; // @tag query:"second_confirm,in" zh:"二次确认"
	optional int32 type = 27; // @tag query:"type" zh:"资产类型"
	optional string asn = 28; // @tag form:"asn" validate:"omitempty" zh:"ASN" query:"geo.asn"
	optional string logo = 29; // @tag form:"logo" validate:"omitempty" zh:"Logo哈希" query:"logo.hash"
	optional int32 is_cdn = 31; // @tag query:"is_cdn,bool" zh:"是否CDN"
	optional int32 open_parse = 32; // @tag query:"open_parse,bool" zh:"泛解析"
	repeated string updated_at = 33; // @tag form:"updated_at" validate:"omitempty" zh:"更新时间范围" query:"updated_at,range"
	repeated string created_at = 34; // @tag form:"created_at" validate:"omitempty" zh:"创建时间范围" query:"created_at,range"
	optional string latitude = 35; // @tag form:"latitude" validate:"omitempty" zh:"纬度" query:"geo.lat"
	optional string longitude = 36; // @tag form:"longitude" validate:"omitempty" zh:"经度" query:"geo.lon"
	repeated string isp = 37; // @tag query:"geo.isp,in" zh:"ISP"
	optional int32 reason = 38; // @tag form:"reason" validate:"omitempty" zh:"原因" query:"reason.type"
}

// 忽略资产列表(新版)请求
message IgnorePassetsListRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 page = 2; // @tag form:"page" validate:"omitempty,min=1" zh:"页码" query:"-"
	int64 per_page = 3; // @tag form:"per_page" validate:"omitempty,number" zh:"每页数量" query:"-"
	optional string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP地址" query:"ip.keyword,like"
	repeated string province = 5; // @tag query:"geo.province,in" zh:"省份"
	repeated string title = 6; // @tag query:"title,in" zh:"标题" query:"-"
	repeated string protocol = 7; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"protocol,in"
	repeated string port = 8; // @tag query:"port,in" zh:"端口"
	repeated string domain = 9; // @tag query:"domain,in" zh:"域名" query:"-"
	repeated string subdomain = 10; // @tag query:"subdomain,in" zh:"子域名"
	repeated string url = 11; // @tag query:"url.keyword,in" zh:"URL"
	repeated int32 http_status_code = 12; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"http_status_code,in"
	repeated int32 online_state = 14; // @tag form:"online_state" validate:"omitempty" query:"online_state,in" zh:"在线状态"
	optional string keyword = 15; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional string hosts = 16; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	optional string cert = 17; // @tag query:"cert.subject_key,like" zh:"证书"
	optional string icp = 18; // @tag form:"icp" validate:"omitempty" zh:"ICP备案" query:"icp.no,like"
	optional string cname = 19; // @tag form:"cname" validate:"omitempty" zh:"CNAME" query:"cname,like"
	optional string cloud_name = 20; // @tag query:"cloud_name,like" zh:"云厂商名称" query:"-"
	repeated string clue_company_name = 21; // @tag form:"clue_company_name" zh:"线索公司名称" query:"-"
	repeated string rule_tags = 22; // @tag form:"rule_tags" query:"rule_tags.cn_product.raw,in" zh:"组件信息"
	repeated string assets_source = 23; // @tag query:"assets_source,in" zh:"资产来源" query:"-"
	repeated string customer_tags = 24; // @tag query:"customer_tags,in" zh:"客户标签"
	optional int32 sort = 25; // zh:"排序方式 0:IP升序 1:更新时间降序"
	optional int32 second_confirm = 26; // @tag query:"second_confirm,in" zh:"二次确认"
	optional int32 type = 27; // @tag query:"type" zh:"资产类型"
	optional string asn = 28; // @tag form:"asn" validate:"omitempty" zh:"ASN" query:"geo.asn"
	optional string logo = 29; // @tag form:"logo" validate:"omitempty" zh:"Logo哈希" query:"logo.hash"
	optional int32 is_cdn = 31; // @tag query:"is_cdn,bool" zh:"是否CDN"
	optional int32 open_parse = 32; // @tag query:"open_parse,bool" zh:"泛解析"
	repeated string updated_at = 33; // @tag form:"updated_at" validate:"omitempty" zh:"更新时间范围" query:"updated_at,range"
	repeated string created_at = 34; // @tag form:"created_at" validate:"omitempty" zh:"创建时间范围" query:"created_at,range"
	optional string latitude = 35; // @tag form:"latitude" validate:"omitempty" zh:"纬度" query:"geo.lat"
	optional string longitude = 36; // @tag form:"longitude" validate:"omitempty" zh:"经度" query:"geo.lon"
	repeated string isp = 37; // @tag query:"geo.isp,in" zh:"ISP"
	optional int32 reason = 38; // @tag form:"reason" validate:"omitempty" zh:"原因" query:"reason.type"
}

// 威胁资产列表(新版)请求

message ThreatPassetsListRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 page = 2; // @tag form:"page" validate:"omitempty,min=1" zh:"页码" query:"-"
	int64 per_page = 3; // @tag form:"per_page" validate:"omitempty,number" zh:"每页数量" query:"-"
	optional string ip = 4; // @tag form:"ip" validate:"omitempty" zh:"IP地址" query:"ip.keyword,like"
	repeated string province = 5; // @tag query:"geo.province,in" zh:"省份"
	repeated string title = 6; // @tag query:"title,in" zh:"标题" query:"-"
	repeated string protocol = 7; // @tag form:"protocol" validate:"omitempty" zh:"协议" query:"protocol,in"
	repeated string port = 8; // @tag query:"port,in" zh:"端口"
	repeated string domain = 9; // @tag query:"domain,in" zh:"域名" query:"-"
	repeated string subdomain = 10; // @tag query:"subdomain,in" zh:"子域名"
	repeated string url = 11; // @tag query:"url.keyword,in" zh:"URL"
	repeated int32 http_status_code = 12; // @tag form:"http_status_code" validate:"omitempty" zh:"HTTP状态码" query:"http_status_code,in"
	repeated int32 online_state = 14; // @tag form:"online_state" validate:"omitempty" query:"online_state,in" zh:"在线状态"
	optional string keyword = 15; // @tag form:"keyword" validate:"omitempty,max=50" zh:"关键词" query:"-"
	optional string hosts = 16; // @tag form:"hosts" validate:"omitempty,min=1,max=30" zh:"主机" query:"-"
	optional string cert = 17; // @tag query:"cert.subject_key,like" zh:"证书"
	optional string icp = 18; // @tag form:"icp" validate:"omitempty" zh:"ICP备案" query:"icp.no,like"
	optional string cname = 19; // @tag form:"cname" validate:"omitempty" zh:"CNAME" query:"cname,like"
	optional string cloud_name = 20; // @tag query:"cloud_name,like" zh:"云厂商名称" query:"-"
	repeated string clue_company_name = 21; // @tag form:"clue_company_name" zh:"线索公司名称" query:"-"
	repeated string rule_tags = 22; // @tag form:"rule_tags" query:"rule_tags.cn_product.raw,in" zh:"组件信息"
	repeated string assets_source = 23; // @tag query:"assets_source,in" zh:"资产来源" query:"-"
	repeated string customer_tags = 24; // @tag query:"customer_tags,in" zh:"客户标签"
	optional int32 sort = 25; // zh:"排序方式 0:IP升序 1:更新时间降序"
	optional int32 second_confirm = 26; // @tag query:"second_confirm,in" zh:"二次确认"
	optional int32 type = 27; // @tag query:"type" zh:"资产类型"
	optional string asn = 28; // @tag form:"asn" validate:"omitempty" zh:"ASN" query:"geo.asn"
	optional string logo = 29; // @tag form:"logo" validate:"omitempty" zh:"Logo哈希" query:"logo.hash"
	optional int32 is_cdn = 31; // @tag query:"is_cdn,bool" zh:"是否CDN"
	optional int32 open_parse = 32; // @tag query:"open_parse,bool" zh:"泛解析"
	repeated string updated_at = 33; // @tag form:"updated_at" validate:"omitempty" zh:"更新时间范围" query:"updated_at,range"
	repeated string created_at = 34; // @tag form:"created_at" validate:"omitempty" zh:"创建时间范围" query:"created_at,range"
	optional string latitude = 35; // @tag form:"latitude" validate:"omitempty" zh:"纬度" query:"geo.lat"
	optional string longitude = 36; // @tag form:"longitude" validate:"omitempty" zh:"经度" query:"geo.lon"
	repeated string isp = 37; // @tag query:"geo.isp,in" zh:"ISP"
	optional int32 reason = 38; // @tag form:"reason" validate:"omitempty" zh:"原因" query:"reason.type"
}


// 疑似资产列表(新版)响应
message PassetsListResponse {
	int64 total = 1;               // 总数
	int32 per_page = 2;            // 每页数量
	int32 current_page = 3;        // 当前页
	int32 last_page = 4;           // 最后一页
	int32 from = 5;                // 起始位置
	int32 to = 6;                  // 结束位置
	string data = 7;               // JSON格式的结果项
}

// 忽略资产列表(新版)响应
message IngorePassetsListResponse {
	int64 total = 1;               // 总数
	int32 per_page = 2;            // 每页数量
	int32 current_page = 3;        // 当前页
	int32 last_page = 4;           // 最后一页
	int32 from = 5;                // 起始位置
	int32 to = 6;                  // 结束位置
	string data = 7;               // JSON格式的结果项
}

// 威胁资产列表(新版)响应
message ThreatenPassetsListResponse {
	int64 total = 1;               // 总数
	int32 per_page = 2;            // 每页数量
	int32 current_page = 3;        // 当前页
	int32 last_page = 4;           // 最后一页
	int32 from = 5;                // 起始位置
	int32 to = 6;                  // 结束位置
	string data = 7;               // JSON格式的结果项
}

// 统计资产数据维度请求
message TableAssetsStatisticsRequest {
  int64 user_id = 1;         // 用户ID
  int64 company_id = 2;      // 企业ID
  int32 status = 3;          // 状态 0/1/2 默认/已认领/忽略
  int32 type = 4;            // 资产类型
}

// 统计资产数据维度响应
message TableAssetsStatisticsResponse {
  int32 rule_num = 1;        // 规则数量
  int32 port_num = 2;        // 端口数量
  int32 ip_num = 3;          // IP数量
}

// 周期任务列表请求
message CrontabTaskListRequest {
  uint64 user_id = 1;             // 用户ID
  uint64 company_id = 2;          // 企业ID
  int32 page = 3;                 // 页码
  int32 per_page = 4;             // 每页数量
  optional int32 task_type = 5;   // 任务类型 0/1/2 默认/资产扫描/漏洞扫描
  optional string name = 6;       // 任务名称(模糊查询)
  optional int64 op_id = 7;       // 操作人ID
  repeated string created_at = 8; // 创建时间范围
  string sort_field = 9;          // 排序字段
  string sort_order = 10;         // 排序方向 asc/desc
  int64 operate_company_id = 11;  // 操作企业ID
}

// 周期任务列表响应
message CrontabTaskListResponse {
  int64 total = 1;                // 总数
  int32 current_page = 2;         // 当前页
  int32 per_page = 3;             // 每页数量
  int32 last_page = 4;            // 最后一页
  int32 from = 5;                 // 起始记录
  int32 to = 6;                   // 结束记录
  repeated CrontabTaskItem items = 7; // 任务列表
}

// 删除周期任务请求
message CrontabTaskDeleteRequest {
  int64 user_id = 1;              // 用户ID
  int64 company_id = 2;           // 企业ID
  repeated uint64 id = 3;         // 任务ID列表
  int64 operate_company_id = 4;   // 操作企业ID
}

// 删除周期任务响应
message CrontabTaskDeleteResponse {
  bool success = 1;               // 是否成功
}

// 周期任务开关设置请求
message CrontabTaskSwitchRequest {
  int64 user_id = 1;              // 用户ID
  int64 company_id = 2;           // 企业ID
  uint64 id = 3;                  // 任务ID
  int32 switch = 4;               // 开关状态 0开启 1关闭
  int64 operate_company_id = 5;   // 操作企业ID
}

// 周期任务开关设置响应
message CrontabTaskSwitchResponse {
  bool success = 1;               // 是否成功
}

// 周期任务详情请求
message CrontabTaskDetailRequest {
  int64 user_id = 1;              // 用户ID
  int64 company_id = 2;           // 企业ID
  uint64 id = 3;                  // 任务ID
  int64 operate_company_id = 4;   // 操作企业ID
}

// 任务IP信息
message CrontabTaskIpItem {
  uint64 id = 1;                  // ID
  uint64 cron_task_id = 2;        // 任务ID
  string ip = 3;                  // IP地址
  string created_at = 4;          // 创建时间
  string updated_at = 5;          // 更新时间
}

// 端口组信息
message PortGroupItem {
  uint64 id = 1;                  // ID
  uint64 user_id = 2;             // 用户ID
  uint64 company_id = 3;          // 企业ID
  string name = 4;                // 名称
  int32 can_del = 5;              // 是否可删除
  string created_at = 6;          // 创建时间
  string updated_at = 7;          // 更新时间
}

// 任务端口信息
message CrontabTaskPortItem {
  uint64 id = 1;                  // ID
  uint64 cron_task_id = 2;        // 任务ID
  string cronports_type = 3;      // 端口类型
  uint64 cronports_id = 4;        // 端口ID
  string created_at = 5;          // 创建时间
  string updated_at = 6;          // 更新时间
  PortGroupItem cronports = 7;    // 端口组信息
}

// 周期任务详情响应
message CrontabTaskDetailResponse {
  uint64 id = 1;                  // 任务ID
  uint64 user_id = 2;             // 用户ID
  uint64 company_id = 3;          // 企业ID
  string name = 4;                // 任务名称
  string bandwidth = 5;           // 带宽
  int32 poc_scan_type = 6;        // POC扫描类型
  string protocol_concurrency = 7;// 协议并发
  int32 task_type = 8;            // 任务类型
  int32 ip_type = 9;              // IP类型
  int32 scan_range = 10;          // 扫描范围
  int32 ping_switch = 11;         // Ping开关
  int32 web_logo_switch = 12;     // Web截图开关
  int32 type = 13;                // 周期类型
  string schedule_time = 14;      // 任务开始时间
  string day_of_x = 15;           // 周期任务日期
  int32 scan_type = 16;           // 扫描模式
  int32 switch = 17;              // 是否开启
  string created_at = 18;         // 创建时间
  string updated_at = 19;         // 更新时间
  string deleted_at = 20;         // 删除时间
  string file_name = 21;          // 文件名
  int32 task_from = 22;           // 任务来源
  int64 op_id = 23;               // 操作者ID
  int32 is_audit = 24;            // 是否审核
  string reason = 25;             // 原因
  int32 is_define_port = 26;      // 是否自定义端口
  string scan_engine = 27;        // 扫描引擎
  int32 engine_role = 28;         // 引擎角色
  int32 table_assets_type = 29;   // 台账资产类型
  repeated CrontabTaskIpItem ips = 30;                // IP列表
  repeated CrontabTaskPortItem ports = 31;            // 端口列表
  UserInfo op = 32;                                   // 操作者信息
  repeated int32 define_ports = 33;                   // 自定义端口列表
  repeated int32 define_port_protocols = 34;          // 自定义端口协议列表
  repeated string pocs = 35; // 漏洞数据
}

// 周期任务项
message CrontabTaskItem {
  uint64 id = 1;                  // 任务ID
  uint64 user_id = 2;             // 用户ID
  uint64 company_id = 3;          // 企业ID
  string name = 4;                // 任务名称
  string bandwidth = 5;           // 带宽
  string protocol_concurrency = 6;// 协议并发
  int32 task_type = 7;            // 任务类型
  int32 ip_type = 8;              // IP类型
  int32 scan_range = 9;           // 扫描范围
  int32 ping_switch = 10;         // Ping开关
  int32 web_logo_switch = 11;     // Web截图开关
  int32 type = 12;                // 周期类型 3月 4周 5天 6一次
  string schedule_time = 13;      // 任务开始时间
  string day_of_x = 14;           // 周期任务日期
  int32 scan_type = 15;           // 扫描模式
  int32 switch = 16;              // 是否开启 0开启 1关闭
  string created_at = 17;         // 创建时间
  string updated_at = 18;         // 更新时间
  string file_name = 19;          // 文件名
  int32 task_from = 20;           // 任务来源
  int64 op_id = 21;               // 操作人ID
  int32 is_audit = 22;            // 是否审核
  string reason = 23;             // 原因
  int32 is_define_port = 24;      // 是否自定义端口
  string scan_engine = 25;        // 扫描引擎
  int32 engine_role = 26;         // 使用的扫描器
  int32 table_assets_type = 27;   // 台账资产类型
  UserInfo user = 28;             // 用户信息
  UserInfo op = 29;               // 操作者信息
}

// 周期任务创建请求
message CrontabTaskCreateRequest {
  int64 user_id = 1;                // 用户ID
  int64 company_id = 2;             // 企业ID
  string name = 3;                  // 任务名称
  string bandwidth = 4;             // 带宽
  int32 task_type = 5;              // 任务类型 1:资产扫描 2:漏洞扫描
  int32 ip_type = 6;                // IP类型 1:IPv4 2:IPv6
  string protocol_concurrency = 7;  // 协议并发
  int32 scan_type = 8;              // 扫描模式 0:精准 1:极速
  int32 type = 9;                   // 周期类型 3:月 4:周 5:天 6:一次
  string day_of_x = 10;             // 周期任务日期
  string schedule_time = 11;        // 任务开始时间
  repeated uint64 poc_ids = 12;     // POC ID列表
  uint64 port_group_ids = 13;       // 端口组ID
  int32 poc_scan_type = 14;         // POC扫描类型
  repeated string ips = 15;         // IP列表
  string file_name = 16;            // 文件名
  int32 scan_range = 17;            // 扫描范围
  int32 ping_switch = 18;           // Ping开关
  int32 web_logo_switch = 19;       // Web截图开关
  int64 operate_company_id = 20;    // 操作企业ID
  repeated int32 define_port_protocols = 21; // 自定义端口协议列表
  repeated int32 define_ports = 22;  // 自定义端口列表
  int32 is_define_port = 23;        // 是否自定义端口
  string scan_engine = 24;          // 扫描引擎
  int32 table_assets_type = 25;     // 台账资产类型
}

// 周期任务创建响应
message CrontabTaskCreateResponse {
  bool success = 1;                 // 是否成功
  repeated string data_list = 2;    // 成功列表
  repeated string error_list = 3;   // 错误列表
}

// 周期任务编辑请求
message CrontabTaskEditRequest {
  int64 user_id = 1;                // 用户ID
  int64 company_id = 2;             // 企业ID
  uint64 id = 3;                    // 任务ID
  string name = 4;                  // 任务名称
  string bandwidth = 5;             // 带宽
  int32 task_type = 6;              // 任务类型 1:资产扫描 2:漏洞扫描
  int32 ip_type = 7;                // IP类型 1:IPv4 2:IPv6
  string protocol_concurrency = 8;  // 协议并发
  int32 scan_type = 9;              // 扫描模式 0:精准 1:极速
  int32 type = 10;                  // 周期类型 3:月 4:周 5:天 6:一次
  string day_of_x = 11;             // 周期任务日期
  string schedule_time = 12;        // 任务开始时间
  repeated uint64 poc_ids = 13;     // POC ID列表
  uint64 port_group_ids = 14;       // 端口组ID
  int32 poc_scan_type = 15;         // POC扫描类型
  repeated string ips = 16;         // IP列表
  string file_name = 17;            // 文件名
  int32 scan_range = 18;            // 扫描范围
  int32 ping_switch = 19;           // Ping开关
  int32 web_logo_switch = 20;       // Web截图开关
  int64 operate_company_id = 21;    // 操作企业ID
  repeated int32 define_port_protocols = 22; // 自定义端口协议列表
  repeated int32 define_ports = 23;  // 自定义端口列表
  int32 is_define_port = 24;        // 是否自定义端口
  string scan_engine = 25;          // 扫描引擎
  int32 table_assets_type = 26;     // 台账资产类型
  int32 close_old = 27;             // 是否关闭旧任务 0:否 1:是
}

// 周期任务编辑响应
message CrontabTaskEditResponse {
  bool success = 1;                 // 是否成功
  uint64 task_id = 2;               // 任务ID
}

// 禁扫IP列表请求
message ForbidIpsListRequest {
  int64 user_id = 1;                // 用户ID
  int32 page = 2;                   // 页码
  int32 per_page = 3;               // 每页数量
  optional string keyword = 4;      // 关键词过滤
  optional int64 operate_company_id = 5; // 操作企业ID
}

// 禁扫IP列表响应
message ForbidIpsListResponse {
  int64 total = 1;                  // 总数
  int32 per_page = 2;               // 每页数量
  int32 current_page = 3;           // 当前页
  int32 last_page = 4;              // 最后一页
  int32 from = 5;                   // 起始位置
  int32 to = 6;                     // 结束位置
  repeated ForbidIpItem items = 7;  // 禁扫IP列表项
}

// 禁扫IP项
message ForbidIpItem {
  int64 id = 1;                     // ID
  string ip_segment = 2;            // IP段
  int64 user_id = 3;                // 用户ID
  optional int64 company_id = 4;    // 企业ID
  string created_at = 5;            // 创建时间
  string updated_at = 6;            // 更新时间
  string deleted_at = 7;            // 删除时间
}

// 添加禁扫IP请求
message AddForbidIpsRequest {
  int64 user_id = 1;                // 用户ID
  repeated string ips = 2;  // IP段列表
  optional int64 operate_company_id = 3; // 操作企业ID
}

// 添加禁扫IP响应
message AddForbidIpsResponse {
  bool success = 1;                 // 是否成功
  int32 count = 2;                  // 添加数量
}

// 删除禁扫IP请求
message DeleteForbidIpsRequest {
  int64 user_id = 1;                // 用户ID
  repeated int64 ids = 2;           // ID列表
  optional int64 operate_company_id = 3; // 操作企业ID
}

// 删除禁扫IP响应
message DeleteForbidIpsResponse {
  bool success = 1;                 // 是否成功
}

// IP详情请求
message IpDetailRequest {
  string id = 1;               // IP资产ID
  int64 user_id = 2;           // 用户ID
  int64 company_id = 3;        // 企业ID
  int32 from = 4;              // 来源类型：0-IP资产，1-任务资产
  int32 is_from_search = 5;    // 是否来自搜索：0-否，1-是
  int64 operate_company_id = 6; // 操作企业ID
}

// IP详情响应
message IpDetailResponse {
  string id = 1;                      // IP资产ID
  string ip = 2;                      // IP地址
  bool is_ipv6 = 3;                   // 是否IPv6
  repeated IpPortInfo port_list = 4;    // 端口详细信息
  repeated RuleTag rule_tags = 5;     // 规则标签
  repeated string hosts = 6;          // 主机列表
  string domains = 7;                 // 域名列表
  repeated string clue_company_name = 8; // 线索公司名称
  bool online_state = 9;              // 在线状态
  repeated IpDomainHistory histyory = 10; // 历史记录
  repeated ThreatInfo threats = 11;   // 威胁信息
  string chain_list = 12; // 线索链列表（JSON字符串）
  GeoInfo geo = 13;              // 地理位置信息
  int32 port_size = 14;               // 端口数量
  repeated int32 org_detect_assets_tasks_id = 15; // 组织检测资产任务ID
  repeated string all_title = 16;     // 所有标题
  string created_at = 17;             // 创建时间
  repeated int32 task_id = 18;        // 任务ID
  int32 type = 19;                    // 类型
  repeated IpDomainHistoryItem ip_domain_history = 20; // IP域名历史记录
  string lateast_parse_domain_time = 21; // 最新解析域名时间
  string updated_at = 22;             // 更新时间
  int32 is_shadow = 23;               // 是否影子资产
  repeated IpHostInfo host_list = 24; // 主机列表详情
  int32 level = 25;                   // 级别
  repeated string cloud_name = 26;    // 云名称
  string lateast_parse_domain = 27;   // 最新解析域名
  repeated string tags = 28;          // 标签
  repeated string reason_arr = 29;    // 原因数组
  repeated int32 detect_assets_tasks_id = 30; // 检测资产任务ID
  int32 ip_match = 31;                // IP匹配
  int64 user_id = 32;                 // 用户ID
  bool is_cdn = 33;                   // 是否CDN
  int32 website_message_id = 34;      // 网站消息ID
  repeated int32 organization_id = 35; // 组织ID
  int32 reliability_score = 36;       // 可靠性评分
  string threaten_type = 37;          // 威胁类型
  string threaten_type_name = 43;     // 威胁类型名称
  int32 company_match = 38;           // 公司匹配
  int32 status = 39;                  // 状态
  string doc_id = 40;                 // ES文档ID
  repeated RuleTag rules = 41;        // 规则列表
  repeated string all_domain = 42;    // 所有域名
	repeated string customer_tags = 44; // 自定义标签
}

// 端口详情信息
message IpPortInfo {
  string port = 1;              // 端口号
  string protocol = 2;          // 协议
  string service = 3;           // 服务
  string banner = 4;            // 横幅
  string cert = 5;              // 证书（JSON字符串）
  repeated RuleTag rules = 6;   // 规则标签
  repeated string reason = 7;   // 原因
  string fid = 8;               // FID
  string assets_source_domain = 9; // 资产来源域名
  int32 assets_source = 10;     // 资产来源
  string source_updated_at = 11; // 源更新时间
  repeated string cname = 12;   // CNAME
  string screenshot = 13;       // 截图
  string title = 14;            // 标题
  int32 type = 15;              // 类型
  repeated string clue_company_name = 16; // 线索公司名称
  string oneforall_source = 17; // OneForAll来源
  string updated_at = 18;       // 更新时间
  string icp = 19;              // ICP信息（JSON字符串）
  string logo = 20;             // Logo信息（JSON字符串）
  string is_login = 21;         // 是否登录
  int32 http_status_code = 22;  // HTTP状态码
  int32 is_open = 23;           // 是否开放
  string url = 24;              // URL
  int32 online_state = 25;      // 在线状态
  string domain = 26;           // 域名
  string reliability_score = 27; // 可靠性评分
  string header = 28;           // 头部信息
  string subdomain = 29;        // 子域名
  bool open_parse = 30;         // 是否开放解析
  repeated string url_arr = 31; // URL数组
}

// 规则标签
message RuleTag {
  string cn_product = 1;        // 中文产品名
  string en_product = 2;        // 英文产品名
  string cn_company = 3;        // 中文公司名
  string en_company = 4;        // 英文公司名
  string category = 5;          // 分类
  string port = 6;              // 端口
  string rule_id = 7;           // 规则ID
  string product = 8;           // 产品
  string cn_category = 9;       // 中文分类
  string level = 10;            // 级别
  string parent_category = 11;  // 父分类
  string softhard = 12;         // 软硬件
  string company = 13;          // 公司
  string cn_parent_category = 14; // 中文父分类
  repeated int32 port_arr = 15; // 端口数组
}

// IP域名历史记录
message IpDomainHistory {
  string time = 1;              // 时间
  int32 id = 2;                 // ID
  repeated HistoryItem children = 3; // 子项
}

// IP域名历史记录项
message IpDomainHistoryItem {
  string found_time = 1;        // 发现时间
  string domain = 2;            // 域名
  int32 source = 3;             // 来源
  string icp_company = 4;       // ICP公司
}

// IP主机信息
message IpHostInfo {
  string fid = 1;               // FID
  repeated string reason = 2;   // 原因
  string assets_source_domain = 3; // 资产来源域名
  int32 assets_source = 4;      // 资产来源
  string source_updated_at = 5; // 源更新时间
  repeated string cname = 6;    // CNAME
  string cert = 7;              // 证书（JSON字符串）
  string screenshot = 8;        // 截图
  string title = 9;             // 标题
  int32 type = 10;              // 类型
  repeated string clue_company_name = 11; // 线索公司名称
  string oneforall_source = 12; // OneForAll来源
  string protocol = 13;         // 协议
  string updated_at = 14;       // 更新时间
  string icp = 15;              // ICP信息（JSON字符串）
  string logo = 16;             // Logo信息（JSON字符串）
  string is_login = 17;         // 是否登录
  int32 http_status_code = 18;  // HTTP状态码
  int32 is_open = 19;           // 是否开放
  string banner = 20;           // Banner
  string url = 21;              // URL
  int32 online_state = 22;      // 在线状态
  string port = 23;             // 端口
  string domain = 24;           // 域名
  string reliability_score = 25; // 可靠性评分
  string header = 26;           // 头部信息
  string subdomain = 27;        // 子域名
  bool open_parse = 28;         // 是否开放解析
}

// 地理位置信息
message GeoInfo {
  string continent = 1;         // 大洲
  string zip = 2;               // 邮编
  string country = 3;           // 国家
  string city = 4;              // 城市
  string org = 5;               // 组织
  string isp = 6;               // ISP
  string lon = 7;               // 经度
  string as = 8;                // AS
  string province = 9;          // 省份
  string district = 10;         // 区域
  string asn = 11;              // ASN
  string lat = 12;              // 纬度
  string as_name = 13;          // AS名称
}



// 历史记录项
message HistoryItem {
  string title = 1;             // 标题
  int32 type = 2;               // 类型
  string detail = 3;            // 详情
  string tip = 4;               // 提示
  repeated HistoryItem children = 5; // 子项
}

// 威胁信息
message ThreatInfo {
  int64 id = 1;                 // ID
  string name = 2;              // 名称
  string level = 3;             // 等级
  int32 state = 4;              // 状态
  string created_at = 5;        // 创建时间
  string updated_at = 6;        // 更新时间
}

message TitleBlackKeywordRequest {
	string keyword = 1; // @tag form:"keyword" validate:"required" zh:"关键词"
	uint64 user_id = 2; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 type_id = 3; // @tag form:"type_id" validate:"required" zh:"类型ID"
}

message FofaUpdateRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	int64 type = 2; // @tag form:"type" validate:"omitempty,oneof=0 1" zh:"类型"
	int64 operate_company_id = 3; // @tag form:"operate_company_id" validate:"omitempty" zh:"操作企业ID"
}

message PushDomainDataRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	repeated string domain_list = 2; // @tag form:"domain_list" validate:"required" zh:"域名列表"
	uint64 company_id = 3; // @tag form:"company_id" validate:"omitempty" zh:"企业ID"
}

// 资产导入请求
message AssetsImportDataRequest {
	int64 operate_company_id = 1;  // @tag form:"operate_company_id" validate:"omitempty" zh:"企业ID"
	repeated string file = 2;      // @tag form:"file" validate:"required" zh:"文件路径"
	string assets_type = 3;        // @tag form:"assets_type" validate:"required,oneof=account_assets unsure_assetss threaten_asset" zh:"资产类型"
	uint64 user_id = 4;           // @tag form:"user_id" validate:"required" zh:"用户ID"
	int64 company_id = 5;         // @tag form:"company_id" validate:"required" zh:"企业ID"
	string client_ip = 6;     // 操作者的IP地址
}

// 批量匹配威胁词库响应
message BatchResponse {
  int32 num = 1;        // 命中的数量
}

// 核对台账模版请求
message CheckBookDataRequest {
  int64 user_id = 1;             // 用户ID
  int64 operate_company_id = 2;  // 操作企业ID
  repeated string file = 3;      // 文件路径
}

// 核对台账模版响应
message CheckBookDataResponse {
  string url = 1;               // 下载URL
  int32 assets_count = 2;       // 资产总数
  int32 not_assets_count = 3;   // 未纳入管理的资产数
}// 下发扫描任务请求
message CreateScanTaskRequest {
	int64 user_id = 1;                // 用户ID
	int64 company_id = 2;             // 企业ID
	string name = 3;                  // 任务名称
	int32 task_type = 4;              // 任务类型：1-资产扫描，2-漏洞扫描
	int32 web_logo_switch = 5;        // 截图功能开关：0-关闭，1-开启
	int32 bandwidth = 6;              // 带宽限制
	int32 scan_range = 7;             // 扫描范围类型
	int32 poc_scan_type = 8;          // POC扫描类型
	repeated string ips = 9;          // IP列表
	int32 ping_switch = 10;           // Ping探测开关
	int32 scan_type = 11;             // 扫描类型
	int32 protocol_concurrency = 12;  // 协议并发数
	repeated int64 poc_ids = 13;      // POC ID列表
	string poc_group_ids = 14;        // POC分组ID
	repeated int64 port_ids = 15;     // 端口ID列表
	int64 port_group_ids = 16;        // 端口分组ID
	int32 ip_type = 17;               // IP类型：1-IPv4，2-IPv6
	int64 operate_company_id = 18;    // 操作企业ID
	string file_name = 19;            // 文件名
	repeated string urls = 20;        // URL列表
	int32 port_range = 21;            // 端口范围类型
	string rawgrab_ports = 22;        // 原始抓取端口JSON
	int32 is_define_port = 23;        // 是否自定义端口
	repeated int32 define_ports = 24; // 自定义端口列表
	repeated int32 define_port_protocols = 25; // 自定义端口协议列表
	int32 type = 26;                  // 任务类型
	int32 table_assets_type = 27;     // 台账资产类型
	string scan_engine = 28;          // 扫描引擎
	string day_of_x = 29;             // 周期任务日期
	string schedule_time = 30;        // 调度时间
}

message AssetAccountImportScanRequest {
	repeated AssetAccountImportScanIpDataItem data = 1; // @tag form:"data" validate:"omitempty" zh:"数据列表"
	uint64 port_group_ids = 2; // @tag form:"port_group_ids" validate:"omitempty" zh:"端口组ID"
	string source = 3; // @tag form:"source" validate:"required,oneof=input file domain" zh:"来源"
	AssetAccountImportScanTaskParam task_param = 4; // @tag form:"task_param" validate:"required" zh:"任务参数"
	int32 is_define_port = 5; // @tag form:"is_define_port" validate:"required,oneof=0 1" zh:"是否自定义端口"
	repeated uint64 define_ports = 6; // @tag form:"define_ports" validate:"required_if=IsDefinePort 1,dive,min=0,max=65536" zh:"自定义端口"
	repeated uint64 define_port_protocols = 7; // @tag form:"define_port_protocols" validate:"required_if=IsDefinePort 1" zh:"自定义端口协议"
	uint64 user_id = 8; // @tag form:"user_id" validate:"omitempty" zh:"用户ID"
	uint64 operator_id = 9; // @tag form:"operator_id" validate:"omitempty" zh:"操作者ID"
	uint64 company_id = 10; // @tag form:"company_id" validate:"omitempty" zh:"企业ID"
}
message AssetAccountImportScanTaskParam {
	uint32 ip_type = 1; // @tag form:"ip_type" validate:"required" zh:"IP类型"
	uint32 scan_type = 2; // @tag form:"scan_type" validate:"omitempty" zh:"扫描类型"
	uint32 task_from = 3; // @tag form:"task_from" validate:"omitempty" zh:"任务来源"
	uint64 bandwidth = 4; // @tag form:"bandwidth" validate:"omitempty" zh:"带宽"
	uint32 ping_switch = 5; // @tag form:"ping_switch" validate:"omitempty" zh:"Ping开关"
	uint32 protocol_concurrency = 6; // @tag form:"protocol_concurrency" validate:"omitempty" zh:"协议并发"
	uint32 task_type = 7; // @tag form:"task_type" validate:"omitempty" zh:"任务类型"
	uint32 type = 8; // @tag form:"type" validate:"omitempty" zh:"类型"
	uint32 web_logo_switch = 9; // @tag form:"web_logo_switch" validate:"omitempty" zh:"WebLogo开关"
}
message AssetAccountImportScanResponse {
	repeated string data_list = 1; // @tag json:"data_list" zh:"数据列表"
	repeated string error_list = 2; // @tag json:"error_list" zh:"错误列表"
	string warn_message = 3; // @tag json:"warn_message" zh:"警告信息"
}
message AssetAccountImportScanIpDataItem {
	string ip = 1; // @tag form:"ip" validate:"required" zh:"IP地址"
	string domain = 2; // @tag form:"domain" validate:"omitempty" zh:"域名"
	uint32 type = 3; // @tag form:"type" validate:"omitempty" zh:"类型"
}

// 下发扫描任务响应
message CreateScanTaskResponse {
	int64 task_id = 1;                // 任务ID
	string warn_message = 2;          // 警告信息
	repeated string data_list = 3;          // IP列表
	repeated string error_list = 4;          // 错误的扫描目标
}

// PoC 搜索参数
message CustomPocSearch {
	repeated uint64 ids = 1;
	repeated uint32 level = 2; // @tag json:"level" form:"level" zh:"PoC 等级"
	repeated string vul_type = 3; // @tag json:"vul_type" form:"vul_type" zh:"PoC 类型"
	string keyword = 4; // @tag json:"keyword" form:"keyword" zh:"PoC 搜索关键字"
	repeated uint32 publish = 5; // @tag json:"publish" form:"publish" zh:"PoC 状态 1发布/ 0未发布"
	uint32 change_state = 6; // @tag json:"change_state" form:"change_state" zh:"PoC 要修改的状态"
	string category = 7; // @tag json:"category" form:"category" zh:"PoC 的分类"
	string opterater = 8; // @tag json:"opterater" form:"opterater" zh:"操作"
	repeated uint32 group_id = 9; // @tag json:"group_id" form:"group_id" zh:"PoC分组ID"
	string sort = 10; // @tag json:"sort" form:"sort" zh:"排序字段"
	string has_exp = 11; // @tag json:"has_exp" form:"has_exp" zh:"HasEXP"
	string name = 12; // @tag json:"name" form:"name" zh:"PoC 名称"
	uint64 user_id = 13; // @tag json:"user_id" form:"user_id" zh:"用户ID"
	int64 operate_company_id = 14; // @tag json:"operate_company_id" form:"operate_company_id" zh:"操作企业ID"
}

// 通用分页参数
message CustomPocQueryList {
	uint32 page = 1; // @tag json:"page" form:"number,default=1" zh:"页码"
	uint32 page_size = 2;   // @tag json:"page_size" form:"size,default=15" zh:"分页大小"
	bool all = 3;      // @tag json:"all" form:"all,default=false" zh:"是否查询所有"
}

// 查询+搜索综合结构
message CustomPocQueryListAndSearch {
	CustomPocQueryList list = 1; // @embed zh:"分页参数"
	CustomPocSearch search = 2; // @embed zh:"PoC搜索参数"
}
// 自定义POC参数
message CustomPoc {
	uint64 id = 1; // @tag json:"id" zh:"记录ID(修改时此字段不为空)"
	uint64 user_id = 2; // @tag json:"user_id" zh:"用户ID"
	string name = 3; // @tag json:"name" zh:"poc名称"
	string fofa_query = 4; // @tag json:"fofa_query" zh:"查询规则"
	uint32 level = 5; // @tag json:"level,required" zh:"等级, 0:低危，1:中危，2:高危，3:严重"
	string description = 6; // @tag json:"description" zh:"poc描述"
	string product = 7; // @tag json:"product" zh:"产品"
	string homepage = 8; // @tag json:"homepage" zh:"产品主页"
	string author = 9; // @tag json:"author" zh:"作者"
	string references = 10; // @tag json:"references" zh:"来源"
	repeated string vul_type = 11; // @tag json:"vul_type" zh:"漏洞类型"
	bool is0day = 12; // @tag json:"is0day" zh:"是否是0day"
	repeated string cve = 13; // @tag json:"cve" zh:"Cve编号"
	repeated string cnnvd = 14; // @tag json:"cnnvd" zh:"CNNVD编号"
	repeated string cnvd = 15; // @tag json:"cnvd" zh:"CNVD编号"
	string cvss_score = 16; // @tag json:"cvss_score" zh:"CVSS评分"
	string impact = 17; // @tag json:"impact" zh:"漏洞危害"
	string recommendation = 18; // @tag json:"recommendation" zh:"修复建议"
	string disclosure_date = 19; // @tag json:"disclosure_date" zh:"创建时间"
	string editor = 20; // @tag json:"editor" zh:"编辑器内容"
	string scan_steps = 21; // @tag json:"scan_steps" zh:"测试内容"
	bool save_and_publish = 22; // @tag json:"save_and_publish" zh:"保存并发布"
	string update_time = 23; // @tag json:"update_time" zh:"更新时间"
	int32 risk_count = 24; // @tag json:"risk_count" zh:"风险数量"
	string attack_surfaces = 25; // @tag json:"attack_surfaces" zh:"攻击面"
	bool state = 26; // @tag json:"state" zh:"状态"
	uint64 poc_id = 27; // @tag json:"poc_id" zh:"poc_id zh:"scan_poc表的id""
	int64 operate_company_id= 28; // @tag json:"operate_company_id" zh:"企业ID"

}

// 自定义poc列表
message CustomPocList {
	repeated CustomPoc items = 1; // @tag json:"items" zh:"PoC列表"
	int64 total = 2; // @tag json:"total" zh:"总数"
}

// 创建自定义poc返回
message CreateCustomPocResponse {
	uint64 id = 1; // @tag json:"id" zh:"记录ID"
}
// 删除自定义poc请求参数
message DeleteCustomPocRequest {
	repeated uint64 ids = 1;
	uint64 user_id = 2;
	int64 operate_company_id = 3;
}
// 风险资产更新请求参数
message RiskAssetsUpdateRequest {
	uint64 user_id = 1;
	int64 operate_company_id = 2; // @tag form:"operate_company_id" validate:"required" zh:"企业ID"
	bool click = 3; // @tag form:"click" validate:"required" zh:"是否点击"
}

// 风险资产更新返回参数
message RiskAssetsUpdateResponse {
	string update_time = 1; // @tag json:"update_time" zh:"更新时间"
}

// 云端推荐数据项
message CloudRecommendDataItem {
	repeated uint64 id = 1;
	int32 is_all = 2;
	int32 type = 3;
}

// 云端推荐请求
message CloudRecommendRequest {
	uint64 user_id = 1;
	uint64 company_id = 2;
	repeated CloudRecommendDataItem data = 3;
	uint64 group_id = 4;
	int32 is_need_hunter = 5;
	int32 is_need_dnschecker = 6;
	int32 is_auto_expend_ip = 7;
	int32 fofa_range = 8;
	optional int64 operate_company_id = 9;
	map<string, string> keyword = 10;
	string client_ip = 11;
}

// 云端推荐响应
message CloudRecommendResponse {
	string flag = 1;
	uint64 expend_id = 2;
}