package main

import (
	"fmt"
	"micro-service/apiService/wsocket"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/webService/asyncq/handlers"
	"time"

	"micro-service/initialize/es"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dingtalk"
	"micro-service/pkg/log"
	"micro-service/pkg/traces"
	"micro-service/pkg/utils"
	"micro-service/webService/handler"
	pb "micro-service/webService/proto"

	microZap "github.com/go-micro/plugins/v4/logger/zap"
	ocplugin "github.com/go-micro/plugins/v4/wrapper/trace/opentracing"
	"github.com/hashicorp/go-hclog"
	"github.com/opentracing/opentracing-go"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"
)

func onInit() {
	cfg.InitLoadCfg()

	// 初始化日志
	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// 非本地化环境并且这个key不为空的话，初始化aesgcm
	if !cfg.IsLocalClient() && cfg.LoadCommon().AES256Key != "" {
		utils.InitAesgcm(cfg.LoadCommon().AES256Key, cfg.LoadCommon().AES256Nonce)
		dingtalk.InitDingRobot(dingtalk.AccountApplyRobot, cfg.LoadCommon().DingtalkAccessToken, cfg.LoadCommon().DingtalkSecret, false)
	}
	// Mysql & Redis
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = es.GetInstance(cfg.LoadElastic())
	_ = redis.GetInstance(cfg.LoadRedis())
}

func main() {
	onInit()
	// jaeger 链路追踪
	if jaegerTrace, io, err := traces.GetJaegerTracer(pb.ServiceName, cfg.LoadCommon().JaegerAdder); err != nil {
		log.Errorf("Jaeger: %s Error:", cfg.LoadCommon().JaegerAdder, err.Error())
		panic(err)
	} else {
		defer io.Close()
		opentracing.SetGlobalTracer(jaegerTrace)
		log.Infof("Jaeger: %s Connection successful", cfg.LoadCommon().JaegerAdder)
	}
	// 设置日志组件
	consulReg := cfg.GetInstance().GetConsulReg()
	// Create service
	srv := micro.NewService(
		// micro.Server(grpc.NewServer()),
		micro.Name(pb.ServiceName),
		micro.Version(pb.ServiceVersion),
		micro.RegisterTTL(5*time.Second),      // TTL指定从上一次心跳间隔起，超过这个时间服务会被服务发现移除
		micro.RegisterInterval(2*time.Second), // 让服务在指定时间内重新注册，保持TTL获取的注册时间有效
		micro.Logger(logger.DefaultLogger),
		micro.Address(utils.GetListenAddress(cfg.LoadCommon().Network)),
		micro.WrapHandler(ocplugin.NewHandlerWrapper(opentracing.GlobalTracer())),
		micro.WrapClient(ocplugin.NewClientWrapper(opentracing.GlobalTracer())),
	)

	srv.Init(
		micro.Registry(consulReg),
	)
	// 注册异步任务
	asyncq.AddHandler(asyncq.CreateLogEvent, handlers.CreateLogEventHandler)
	asyncq.AddHandler(asyncq.DetectGolangCluesJob, handlers.DetectGolangCluesEventHandler)
	asyncq.AddHandler(asyncq.UpdateAssetsLevel, handlers.HandleUpdateAssetsLevel)
	asyncq.AddHandler(asyncq.UpdateAssetsLevelCode, handlers.UpdateAssetsLevelCodeHandler)
	asyncq.AddHandler(asyncq.ExpandCluesJob, handlers.ExpandCluesJobHandler)
	asyncq.AddHandler(asyncq.ScanAssetJob, handlers.ScanAssetJobHandler)
	asyncq.AddHandler(asyncq.RecommendAssetJob, handlers.RecommendAssetJobHandler)
	asyncq.AddHandler(asyncq.OneForAllJob, handlers.OneForAllJob)
	asyncq.AddHandler(asyncq.ConfirmGeneralCluesJob, handlers.ConfirmGeneralCluesJob)
	asyncq.AddHandler(asyncq.DeleteTaskAssetsJob, handlers.DeleteTaskAssetsJob)
	asyncq.AddHandler(asyncq.FofaDomainUpdateJob, handlers.FofaDomainUpdateJob)
	asyncq.AddHandler(asyncq.RecordIpHistoryJob, handlers.RecordIpHistoryJob)
	asyncq.AddHandler(asyncq.DeleteTypeWebsiteMsgJob, handlers.DeleteTypeWebsiteMsgJob)
	asyncq.AddHandler(asyncq.UpdateIpAndPortOfflineStateJob, handlers.UpdateIpAndPortOfflineStateJob)
	asyncq.AddHandler(asyncq.UpdateIpAndPortOnlineStateJob, handlers.UpdateIpAndPortOnlineStateJob)
	asyncq.AddHandler(asyncq.ShadowAssetsTagJob, handlers.ShadowAssetsTagJob)
	asyncq.AddHandler(asyncq.StatisticsLoginAssetsJob, handlers.StatisticsLoginAssetsJob)
	asyncq.AddHandler(asyncq.TagAssetsJob, handlers.TagAssetsJob)
	asyncq.AddHandler(asyncq.UpdateIpAndPortAssetCompanyNameJob, handlers.UpdateIpAndPortAssetCompanyNameJob)
	asyncq.AddHandler(asyncq.UpdateRiskIpCountJob, handlers.UpdateRiskIpCountJob)
	asyncq.AddHandler(asyncq.UpdateRiskTypeAssetJob, handlers.UpdateRiskTypeAssetJob)
	asyncq.AddHandler(asyncq.StandingAssetsScanJob, handlers.StandingAssetsScanJob)
	asyncq.AddHandler(asyncq.ExtractAssetCluesJobName, handlers.ExtractAssetCluesJob)
	asyncq.AddHandler(asyncq.AssetsImportDataJob, handlers.AssetsImportDataJob)
	asyncq.AddHandler(asyncq.TableAssetsDomainsSyncJob, handlers.TableAssetsDomainsSyncEventHandler)
	asyncq.AddHandler(asyncq.ScanForadarAssetsJob, handlers.ScanForadarAssetHandler)
	asyncq.AddHandler(asyncq.PushDomainIcpJob, handlers.PushDomainIcpJob)
	asyncq.AddHandler(asyncq.DetectDirectOperateJob, handlers.DetectDirectOperateJobHandler)
	asyncq.AddHandler(asyncq.ICPQueryJob, handlers.ICPQueryJobHandler)
	asyncq.AddHandler(asyncq.CreateDetectTaskJob, handlers.CreateDetectTaskJobHandler)
	// 启动异步任务
	go asyncq.Start(pb.ServiceName)
	// Register service handler
	// web handler
	if err := pb.RegisterWebHandler(srv.Server(), new(handler.Web)); err != nil {
		panic(err)
	}

	// 启动websocket订阅
	if err := wsocket.StartSubscribeAgent(); err != nil {
		fmt.Println("webService websocket订阅启动失败", err)
		panic(err)
	}
	fmt.Println("webService websocket订阅启动成功")

	// Run service
	if err := srv.Run(); err != nil {
		panic(err)
	}
	// close asyncq
	asyncq.CloseServer(pb.ServiceName)
}
