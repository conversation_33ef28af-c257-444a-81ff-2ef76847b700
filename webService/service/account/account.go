package account

import (
	"fmt"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/account"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dingtalk"
	"micro-service/pkg/errx"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type AccountService struct {
	//
}

func NewAccountService() *AccountService {
	return &AccountService{}
}

func (s *AccountService) AccountOpenByHand(req *pb.AccountOpenByHandRequest) (string, error) {
	if req.UserId != cfg.LoadCommon().AccountApplyByHandUid {
		return "", errx.ErrFrontEndNoPermissions
	}
	//重复校验
	err := account.NewModel().CheckUniqueFields(req.Username, req.CompanyName, req.Mobile, req.Email)
	if err != nil {
		return "", err
	}
	err = user.NewUserModel().CheckUniqueFields(req.Username, req.CompanyName, req.Mobile, req.Email)
	if err != nil {
		return "", err
	}

	passwd := utils.RandStringWithSpecialChar(16)
	encryptPasswd, err := utils.Encrypt(passwd)
	if err != nil {
		return "", errors.WithMessage(err, "密码加密失败")
	}
	dingCli, err := dingtalk.GetDingTalkClient(dingtalk.AccountApplyRobot)
	if err != nil {
		return "", errors.WithMessage(err, "获取dingtalk client 失败")
	}
	var userAttr = "正式用户"
	if !req.IsFormal {
		userAttr = "测试用户"
	}
	dingMsg := fmt.Sprintf("企业名称:%s, 用户名:%s, email:%s, 手机:%s, 初始密码:%s, %s",
		req.CompanyName, req.Username, req.Email, req.Mobile, passwd, userAttr)

	err = dingCli.SendMsg(dingMsg)
	if err != nil {
		return "", errors.WithMessage(err, "钉钉消息发送失败")
	}

	record := account.TUsersApplyRelation{
		OpUserID:    req.UserId,
		CompanyName: req.CompanyName,
		Username:    req.Username,
		Email:       req.Email,
		Mobile:      req.Mobile,
		IsFormal:    req.IsFormal,
		Status:      account.TAccountApplyStatusApplying,
		Password:    encryptPasswd,
	}
	err = account.NewModel().Create(&record)
	if err != nil {
		return "", errors.WithMessage(err, "账户关联表记录失败")
	}
	return encryptPasswd, nil
}

func (s *AccountService) ListAccountOpenByHand(uid int64) ([]*pb.AccountOpenByHandInfo, error) {
	if uid != cfg.LoadCommon().AccountApplyByHandUid {
		return nil, errors.New("该用户无权限")
	}
	records, err := account.NewModel().List(uid)
	if err != nil {
		return nil, err
	}
	var companyName = make([]string, len(records))
	var mobiles = make([]string, len(records))
	for i, r := range records {
		mobiles[i] = r.Mobile
		companyName[i] = r.CompanyName
	}

	userList, _, err := user.NewUserModel().Find(0, 0, mysql.WithWhere("mobile in (?)", mobiles))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	var userMap = make(map[string]*user.User)
	for i := 0; i < len(userList); i++ {
		userMap[userList[i].Mobile.String] = userList[i]
	}

	companies, err := company.NewCompanyModel().ListAll(mysql.WithWhere("name in (?)", companyName))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	var companyMap = make(map[string]*company.Company, len(companies))
	for i := 0; i < len(companies); i++ {
		companyMap[companies[i].Name] = companies[i]
	}
	var result []*pb.AccountOpenByHandInfo
	for _, r := range records {
		userRecord, ok := userMap[r.Mobile]
		t := &pb.AccountOpenByHandInfo{
			CompanyName:     r.CompanyName,
			Username:        r.Username,
			Email:           r.Email,
			Mobile:          r.Mobile,
			InitialPassword: r.Password,
			Status:          r.Status,
			IsFormal:        r.IsFormal,
			CreatedAt:       r.CreatedAt.Format(utils.DateTimeLayout),
			UpdatedAt:       r.UpdatedAt.Format(utils.DateTimeLayout),
		}
		if ok && userRecord != nil {
			if userRecord.Status == 1 {
				t.Status = account.TAccountApplyStatusActive
			} else {
				t.Status = account.TAccountApplyStatusDisable
			}
			if t.Status != r.Status {
				go account.NewModel().Update(map[string]interface{}{
					"status": t.Status,
				}, mysql.WithWhere("id = ?", r.Id))
			}

			t.ExpiresAt = userRecord.ExpiresAt.Time.Format(utils.DateTimeLayout)
			t.BlackIpSwitch = userRecord.BlackIPSwitch
		}
		companyInfo, ok := companyMap[r.CompanyName]
		if ok && companyInfo != nil {
			t.CompanyInfo = &pb.CompaniesInfo{
				DataLeakRate:        int64(companyInfo.DataLeakRate),
				NewAssetRate:        int64(companyInfo.NewAssetRate),
				LimitCloudRecommend: int64(companyInfo.LimitCloudRecommend),
				LimitIpAsset:        int64(companyInfo.LimitIpAsset),
				LimitNewAsset:       int64(companyInfo.LimitNewAsset),
				LimitPocScan:        int64(companyInfo.LimitPocScan),
				LimitDataLeak:       int64(companyInfo.LimitDataLeak),
				LimitMonitorKeyword: int64(companyInfo.LimitMonitorKeyword),
				UsedIpAsset:         int64(companyInfo.UsedIpAsset),
				UsedCloudRecommend:  int64(companyInfo.UsedCloudRecommend),
				UsedNewAsset:        int64(companyInfo.UsedNewAsset),
				UsedPocScan:         int64(companyInfo.UsedPocScan),
				UsedDataLeak:        int64(companyInfo.UsedDataLeak),
				UsedMonitorKeyword:  int64(companyInfo.UsedMonitorKeyword),
			}
		}
		result = append(result, t)
	}

	return result, nil
}
