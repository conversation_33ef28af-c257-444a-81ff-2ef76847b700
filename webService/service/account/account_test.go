package account

import (
	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dingtalk"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"
	"testing"
)

func initCfg() {
	cfg.InitLoadCfg()
	mysql.GetInstance(cfg.LoadMysql())
}

func TestAccountOpenByHand(t *testing.T) {
	initCfg()
	utils.InitAesgcm(cfg.LoadCommon().AES256Key, cfg.LoadCommon().AES256Nonce)
	dingtalk.InitDingRobot(dingtalk.AccountApplyRobot, cfg.<PERSON><PERSON><PERSON><PERSON><PERSON>().DingtalkAccessToken, cfg.LoadCommon().DingtalkSecret, true)
	_, err := NewAccountService().AccountOpenByHand(&pb.AccountOpenByHandRequest{
		CompanyName: "撒记得",
		Username:    "大风那倒是",
		Email:       "<EMAIL>",
		Mobile:      "**********",
		UserId:      2322132,
		IsFormal:    false,
	})
	if err == nil {
		t.Fatal("权限校验失败")
	}

	passwd, err := NewAccountService().AccountOpenByHand(&pb.AccountOpenByHandRequest{
		CompanyName: "阿斯顿发顺丰",
		Username:    "桑奇怪",
		Email:       "<EMAIL>",
		Mobile:      "************",
		UserId:      659,
		IsFormal:    false,
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Log(passwd)
}

func TestListAccountOpenByHand(t *testing.T) {
	initCfg()
	_, err := NewAccountService().ListAccountOpenByHand(2322132)
	if err == nil {
		t.Fatal("权限校验失败")
	}
	info, err := NewAccountService().ListAccountOpenByHand(659)
	if err != nil {
		t.Fatal(err)
	}
	for _, v := range info {
		t.Logf("%+v\n", *v)
	}
}
