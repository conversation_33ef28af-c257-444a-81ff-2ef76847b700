package utils

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"
	"unsafe"

	"github.com/gin-gonic/gin"
	pkgerr "github.com/pkg/errors"
	"github.com/weppos/publicsuffix-go/publicsuffix"
	"go-micro.dev/v4/client"
)

// 线索类型常量
const (
	TYPE_DOMAIN    = 0
	TYPE_CERT      = 1
	TYPE_ICP       = 2
	TYPE_LOGO      = 3
	TYPE_KEYWORD   = 4
	TYPE_SUBDOMAIN = 5
	TYPE_IP        = 6
	TYPE_FID       = 10
)

func GZipEncode(data []byte) []byte {
	var zip bytes.Buffer
	gz := gzip.NewWriter(&zip)
	if _, err := gz.Write(data); err != nil {
		panic(err)
	}
	if err := gz.Flush(); err != nil {
		panic(err)
	}
	if err := gz.Close(); err != nil {
		panic(err)
	}
	return zip.Bytes()
}

func GZipDecode(str string) string {
	reader := bytes.NewReader([]byte(str))
	gzReader, err := gzip.NewReader(reader)
	if err != nil {
		return ""
	}
	output, err := io.ReadAll(gzReader)
	if err != nil {
		return ""
	}
	return string(output)
}

func GetQueryTimeRange(key string, ctx *gin.Context) []string {
	startAt := ctx.Query(key + "[0]")
	endAt := ctx.Query(key + "[1]")
	if startAt == "" && endAt == "" {
		return nil
	}
	return []string{startAt, endAt}
}

// SetRpcTimeoutOpt rpc超时设置（秒）
func SetRpcTimeoutOpt(timeout int) client.CallOption {
	return func(o *client.CallOptions) {
		o.RequestTimeout = time.Second * time.Duration(timeout)
		//o.DialTimeout = time.Second * time.Duration(timeout)
		o.ConnectionTimeout = time.Second * time.Duration(timeout)
	}
}

func RpcTimeoutDur(d time.Duration) client.CallOption {
	return func(o *client.CallOptions) {
		o.RequestTimeout = d
		o.ConnectionTimeout = d
		//o.DialTimeout = d
	}
}

// PathExists 判断文件是否存在
func PathExists(path string) (bool, error) {
	if path == "" {
		return false, errors.New("路径为空,请检查")
	}
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func GetListenAddress(Network string) string {
	ip := GetNetworkIp(Network)
	if ip == "" {
		return "0.0.0.0:0"
	} else {
		return ip + ":0"
	}
}

func GetNetworkIp(Network string) string {
	ief, err := net.InterfaceByName(Network)
	if err != nil {
		panic(err)
	}
	adders, err := ief.Addrs()
	if err != nil {
		panic(err)
	}
	if len(adders) == 0 {
		panic(fmt.Errorf("获取 %s 网卡IP失败", Network))
	}
	// 优先使用IPV4
	for _, ipNet := range adders {
		if strings.Count(ipNet.(*net.IPNet).IP.String(), ":") < 2 {
			return ipNet.(*net.IPNet).IP.String()
		}
	}
	return adders[0].(*net.IPNet).IP.String()
}

func SubDayTime(day time.Duration) string {
	return time.Now().Add(day * -24 * time.Hour).Format("2006-01-02 15:04:05")
}

func StrContains(str string, search ...string) bool {
	for _, v := range search {
		if strings.Contains(str, v) {
			return true
		}
	}
	return false
}

func TimeStringToGoTime(tm string) time.Time {
	var timeTemplates = []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006/01/02 15:04:05",
		"2006-01-02T15:04:05-0700",
		"2006-01-02",
		"2006/01/02",
		"2006-01-02 15:04:05 UTC",
	}
	for i := range timeTemplates {
		t, err := time.ParseInLocation(timeTemplates[i], tm, time.Local)
		if nil == err && !t.IsZero() {
			return t
		}
	}
	return time.Time{}
}

func String2Bytes(s string) (b []byte) {
	bh := (*reflect.SliceHeader)(unsafe.Pointer(&b))
	sh := (*reflect.StringHeader)(unsafe.Pointer(&s))
	bh.Data = sh.Data
	bh.Len = sh.Len
	bh.Cap = sh.Len

	return b
}

// RemoveIcpNumber 移除ICP后面编号
func RemoveIcpNumber(icp string) string {
	if icp == "" {
		return icp
	}
	icpArr := strings.Split(icp, "-")
	if len(icpArr) != 0 {
		sn := icpArr[len(icpArr)-1]
		if snInt, err := strconv.Atoi(sn); err != nil {
			return icp
		} else {
			if snInt < 9999 {
				icpArr = icpArr[:len(icpArr)-1]
			}
		}
	}
	return strings.Join(icpArr, "-")
}

// GetTopDomain  获取定级域名
func GetTopDomain(urls string) string {
	if urls == "" {
		return urls
	}
	// 解析链接
	if strings.Contains(urls, "://") || strings.Contains(urls, "/") || strings.Contains(urls, ":") {
		if !(strings.Contains(urls, "://") || strings.Contains(urls, "/")) {
			urls = "https://" + urls
		}
		u, err := url.Parse(urls)
		if err != nil {
			return ""
		}
		urls = u.Hostname()
	} else {
		if strings.Contains(urls, `"`) {
			urls = strings.ReplaceAll(urls, `"`, "")
		}
		if strings.HasPrefix(urls, `CN=`) || strings.HasPrefix(urls, `cn=`) {
			urls = strings.ReplaceAll(urls, `CN=`, "")
			urls = strings.ReplaceAll(urls, `cn=`, "")
		}
	}
	// 判断是不是IP
	if net.ParseIP(urls) != nil {
		return ""
	}
	parse, err := publicsuffix.ParseFromListWithOptions(
		publicsuffix.DefaultList,
		urls,
		&publicsuffix.FindOptions{DefaultRule: publicsuffix.DefaultRule},
	)
	if err != nil {
		return urls
	} else {
		if parse.TLD == "" {
			domainArr := strings.Split(urls, ".")
			urls = strings.Join(domainArr[len(domainArr)-2:], ".")
		} else {
			// 超过三级的私有域,按开放的顶级处理
			if parse.Rule.Private && parse.Rule.Length > 2 {
				// 超过三级,取公共定级
				if top, topErr := publicsuffix.ParseFromListWithOptions(
					publicsuffix.DefaultList, parse.Rule.Value,
					&publicsuffix.FindOptions{IgnorePrivate: true, DefaultRule: publicsuffix.DefaultRule},
				); topErr != nil {
					domainArr := strings.Split(parse.Rule.Value, ".")
					urls = strings.Join(domainArr[len(domainArr)-2:], ".")
				} else {
					if top.TLD == "" {
						domainArr := strings.Split(parse.Rule.Value, ".")
						urls = strings.Join(domainArr[len(domainArr)-2:], ".")
					} else {
						urls = top.SLD + "." + top.TLD
					}
				}
			} else {
				if parse.Rule.Length >= 2 {
					domainArr := strings.Split(urls, ".")
					if len(domainArr) >= parse.Rule.Length+1 {
						urls = strings.Join(domainArr[len(domainArr)-(parse.Rule.Length+1):], ".")
					}
				} else {
					urls = parse.SLD + "." + parse.TLD
				}
			}
		}
	}
	return urls
}

func GetParentDomain(domain string) string {
	parentDomain := domain
	index := strings.Index(domain, ".")
	if index != -1 {
		parts := strings.Split(domain[index+1:], ".")
		parentDomain = strings.Join(parts, ".")
	}
	return parentDomain
}

// GetSubdomain 获取子域名
func GetSubdomain(urls string) string {
	// 解析链接
	if strings.Contains(urls, "://") || strings.Contains(urls, "/") || strings.Contains(urls, ":") {
		if !(strings.Contains(urls, "://") || strings.Contains(urls, "/")) {
			urls = "https://" + urls
		}
		u, err := url.Parse(urls)
		if err != nil {
			return ""
		}
		urls = u.Hostname()
	}
	// 判断是不是IP
	address := net.ParseIP(urls)
	if address != nil {
		return ""
	}
	if strings.Contains(urls, ":") {
		urls = strings.Split(urls, ":")[0]
	}
	if strings.HasSuffix(urls, `"`) {
		urls = strings.ReplaceAll(urls, `"`, "")
	}
	urls = strings.TrimPrefix(urls, `*.`)

	// 返回子域名
	return urls
}

// ResolveDomain 解析域名为ip
func ResolveDomain(name string) string {
	addr, err := net.ResolveIPAddr("ip", name)
	if err != nil {
		return ""
	}
	return addr.String()
}

// GetIcpNumber 获取ICP
func GetIcpNumber(str string) string {
	if len(strings.TrimSpace(str)) == 0 {
		return ""
	}
	icp := regexp.MustCompile(`[滇陇京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新蜀黔]ICP[备]\d*号-\d*`).FindString(str)
	if len(icp) > 0 {
		icp = strings.ReplaceAll(icp, " ", "")
		if !strings.Contains(icp, "号") {
			return icp + "号"
		}
		return icp
	}
	icp = regexp.MustCompile(`[滇陇京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新蜀黔]ICP[备]\s?\d*`).FindString(str)
	if len(icp) > 0 {
		icp = strings.ReplaceAll(icp, " ", "")
		if !strings.Contains(icp, "号") {
			return icp + "号"
		}
		return icp
	}
	icp = regexp.MustCompile(`[滇陇京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新蜀黔][a-z|A-Z]\d-(\d*-\d*|\s?\d*)`).FindString(str)
	if len(icp) > 0 {
		icp = strings.ReplaceAll(icp, " ", "")
		if !strings.Contains(icp, "号") {
			return icp + "号"
		}
		return icp
	}
	icp = regexp.MustCompile(`[滇陇京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新蜀黔]ICP[证]\s?\d*`).FindString(str)
	if len(icp) > 0 {
		icp = strings.ReplaceAll(icp, " ", "")
		if !strings.Contains(icp, "号") {
			return icp + "号"
		}
		return icp
	}
	return ""
}

func CheckPrivateIp(ipStr string) bool {
	// 内网地址
	if ip := net.ParseIP(ipStr); ip == nil {
		return false
	} else {
		if ip.To4() == nil { // IPv6
			return ip.IsLinkLocalUnicast() || !ip.IsGlobalUnicast()
		} else { // IPv4
			return ip.IsPrivate()
		}
	}
}

// CheckChinese 检查是否包含中文
func CheckChinese(s string) bool {
	for _, r := range s {
		if unicode.Is(unicode.Han, r) {
			return true
		}
	}
	return false
}

// CheckOpenIpOrDomain 检查是否是公网IP或者是域名
func CheckOpenIpOrDomain(str string) bool {
	if ip := net.ParseIP(str); ip != nil {
		return !CheckPrivateIp(str)
	} else {
		return IsDomain(str)
	}
}

func CompanyNameEquals(str1, str2 string) bool {
	str1 = strings.ReplaceAll(strings.TrimSpace(strings.ToLower(str1)), "(", "")
	str1 = strings.ReplaceAll(strings.TrimSpace(strings.ToLower(str1)), "（", "")
	str1 = strings.ReplaceAll(strings.TrimSpace(strings.ToLower(str1)), ")", "")
	str1 = strings.ReplaceAll(strings.TrimSpace(strings.ToLower(str1)), "）", "")
	str2 = strings.ReplaceAll(strings.TrimSpace(strings.ToLower(str2)), "(", "")
	str2 = strings.ReplaceAll(strings.TrimSpace(strings.ToLower(str2)), "（", "")
	str2 = strings.ReplaceAll(strings.TrimSpace(strings.ToLower(str2)), ")", "")
	str2 = strings.ReplaceAll(strings.TrimSpace(strings.ToLower(str2)), "）", "")
	return str1 == str2
}

func StructToMap(in any, tagName string) (map[string]any, error) {
	out := make(map[string]any)

	v := reflect.ValueOf(in)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return nil, fmt.Errorf("StructToMap only accepts struct or struct pointer; got %T", v)
	}

	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		fi := t.Field(i)

		if fi.PkgPath != "" { // 跳过非导出字段
			continue
		}

		tagValue := fi.Tag.Get(tagName)
		if tagValue == "" || tagValue == "-" {
			continue
		}

		key := strings.Split(tagValue, ",")[0]
		out[key] = v.Field(i).Interface()
	}

	return out, nil
}

// IsChineseChar 检查是否包含中文字符
func IsChineseChar(str string) bool {
	for _, r := range str {
		if unicode.Is(unicode.Han, r) {
			return true
		}
	}
	return false
}

// StringToInt32 将字符串转换为int32，如果转换失败则返回默认值
func StringToInt32(s string, defaultVal int32) int32 {
	if s == "" {
		return defaultVal
	}
	val, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		return defaultVal
	}
	return int32(val)
}

// IsWildDomain 判断是否为泛域名
func IsWildDomain(domain string) bool {
	for i := 0; i < 2; i++ {
		subdomain := Md5Hash("泛解析验证") + RandString(5) + "." + domain
		_, err := net.LookupIP(subdomain)
		if err != nil {
			continue
		}
		return true
	}
	return false
}

// 校验邮箱格式
func IsValidEmail(email string) bool {
	// 定义邮箱的正则表达式
	pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`

	// 使用正则表达式校验邮箱
	isValid := regexp.MustCompile(pattern).MatchString(email)
	return isValid
}

// ExtractCve 解析名称中包含的Cve
func ExtractCve(name string) string {

	res := []rune(name)
	// (CVE-
	var index int = len(res)
	flag := false
	for i := 0; i < len(res)-4; i++ {
		if (res[i] == '(' || res[i] == '（') && res[i+1] == 'C' && res[i+2] == 'V' && res[i+3] == 'E' && res[i+4] == '-' {
			index = i
			flag = true
			break
		}
	}
	if flag {
		return string(res[index+1 : len(res)-1])
	}
	return ""
}

func StringToIntArray(s string) ([]int64, error) {
	if s == "" {
		return []int64{}, nil
	}
	strArr := strings.Split(strings.Trim(s, "[]"), ",")
	var ids []int64
	for _, v := range strArr {
		id, err := strconv.Atoi(v)
		if err != nil {
			return ids, pkgerr.WithMessage(err, "有非int的id")
		}
		ids = append(ids, int64(id))
	}
	return ids, nil
}
func ArrayStringToInt(s []string) ([]int64, error) {
	var ids []int64
	for _, v := range s {
		id, err := strconv.Atoi(v)
		if err != nil {
			return ids, pkgerr.WithMessage(err, "有非int的id")
		}
		ids = append(ids, int64(id))
	}
	return ids, nil
}

func StringToStringArray(s string) []string {
	return strings.Split(strings.Trim(s, "[]"), ",")
}

func IsTestEnv() bool {
	return os.Getenv("go_test") == "true"
}

// StringToUint64 将字符串转换为uint64
func StringToUint64(s string) uint64 {
	if s == "" {
		return 0
	}
	i, err := strconv.ParseUint(s, 10, 64)
	if err != nil {
		return 0
	}
	return i
}

// GetClientIP 获取客户端真实IP
func GetClientIP(r *http.Request) string {
	// 优先取 X-Forwarded-For
	ip := r.Header.Get("X-Forwarded-For")
	if ip != "" {
		// 可能有多个IP，取第一个
		ips := strings.Split(ip, ",")
		if len(ips) > 0 && strings.TrimSpace(ips[0]) != "" {
			return strings.TrimSpace(ips[0])
		}
	}
	// 再取 X-Real-IP
	ip = r.Header.Get("X-Real-IP")
	if ip != "" {
		return ip
	}
	// 最后取 RemoteAddr
	ip, _, err := net.SplitHostPort(r.RemoteAddr)
	if err == nil {
		return ip
	}
	return r.RemoteAddr
}
func ToPointer[T any](v T) *T {
	return &v
}

// IsValidDomain 检查是否为有效域名
func IsValidDomain(domain string) bool {
	// 移除可能的协议前缀
	domain = strings.TrimPrefix(domain, "http://")
	domain = strings.TrimPrefix(domain, "https://")
	domain = strings.TrimPrefix(domain, "www.")

	// 检查域名格式
	pattern := `^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$`
	matched, err := regexp.MatchString(pattern, domain)
	if err != nil {
		return false
	}
	return matched
}

func SafeString(data any) string {
	if data == nil {
		return ""
	}
	switch v := data.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case int16:
		return strconv.FormatInt(int64(v), 10)
	case int8:
		return strconv.FormatInt(int64(v), 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32)
	}
	return fmt.Sprintf("%v", data)
}

func SafeInt(data any) int {
	if data == nil {
		return 0
	}
	// 如果是指针类型，先解引用
	if reflect.TypeOf(data).Kind() == reflect.Ptr {
		if reflect.ValueOf(data).IsNil() {
			return 0
		}
		data = reflect.ValueOf(data).Elem().Interface()
	}
	switch v := data.(type) {
	case string:
		i, err := strconv.Atoi(v)
		if err != nil {
			return 0
		}
		return i
	case int:
		return v
	case int64:
		return int(v)
	case int32:
		return int(v)
	case int16:
		return int(v)
	case int8:
		return int(v)
	case float64:
		return int(v)
	case float32:
		return int(v)
	case uint:
		return int(v)
	case uint64:
		return int(v)
	case uint32:
		return int(v)
	case uint16:
		return int(v)
	case uint8:
		return int(v)
	case bool:
		if v {
			return 1
		}
		return 0
	}
	return 0
}

// CheckIsChinese 检查字符串是否包含中文字符
func CheckIsChinese(s string) bool {
	if s == "" {
		return false
	}

	for _, r := range s {
		// 检查是否在中文Unicode范围内
		if unicode.Is(unicode.Scripts["Han"], r) {
			return true
		}
	}
	return false
}

// StringSliceToInterfaceSlice 将字符串切片转换为interface切片
func StringSliceToInterfaceSlice(strSlice []string) []interface{} {
	result := make([]interface{}, len(strSlice))
	for i, v := range strSlice {
		result[i] = v
	}
	return result
}

// FilterSliceEmpty 过滤切片中的空字符串并去重
func FilterSliceEmpty(slice []string) []string {
	if len(slice) == 0 {
		return slice
	}

	// 使用map去重并过滤空字符串
	m := make(map[string]bool)
	for _, v := range slice {
		if v != "" {
			m[v] = true
		}
	}

	// 创建结果切片
	result := make([]string, 0, len(m))
	for k := range m {
		result = append(result, k)
	}

	return result
}

// TypeToCn 将线索类型转换为中文
// 对应PHP中的typeToCn函数
func TypeToCn(clueType int) string {
	switch clueType {
	case TYPE_DOMAIN:
		return "根域"
	case TYPE_CERT:
		return "证书"
	case TYPE_ICP:
		return "ICP"
	case TYPE_LOGO:
		return "ICON"
	case TYPE_KEYWORD:
		return "关键词"
	case TYPE_SUBDOMAIN:
		return "子域名"
	case TYPE_IP:
		return "IP"
	case TYPE_FID:
		return "FID"
	default:
		return "未知"
	}
}

// UniqueStringSlice 去除字符串切片中的重复元素
func UniqueStringSlice(slice []string) []string {
	keys := make(map[string]bool)
	result := make([]string, 0, len(slice))
	for _, item := range slice {
		if _, exists := keys[item]; !exists {
			keys[item] = true
			result = append(result, item)
		}
	}
	return result
}

// 省份列表
var aereList = map[string]string{
	"Beijing":                           "北京市",
	"Guangdong":                         "广东省",
	"Zhejiang":                          "浙江省",
	"Shanghai":                          "上海市",
	"Jiangsu":                           "江苏省",
	"Shandong":                          "山东省",
	"Sichuan":                           "四川省",
	"Fujian":                            "福建省",
	"Liaoning":                          "辽宁省",
	"Henan":                             "河南省",
	"Anhui":                             "安徽省",
	"Hubei":                             "湖北省",
	"Hunan":                             "湖南省",
	"Chongqing":                         "重庆市",
	"Jiangxi":                           "江西省",
	"Hebei":                             "河北省",
	"Shaanxi":                           "陕西省",
	"Tianjin":                           "天津市",
	"Jilin":                             "吉林省",
	"Guangxi":                           "广西壮族自治区",
	"Yunnan":                            "云南省",
	"Heilongjiang":                      "黑龙江省",
	"Shanxi":                            "山西省",
	"Inner Mongolia Autonomous":         "内蒙古自治区",
	"Inner Mongolia Autonomous Region":  "内蒙古自治区",
	"Hainan":                            "海南省",
	"Gansu":                             "甘肃省",
	"Guizhou":                           "贵州省",
	"Ningxia Hui Autonomous":            "宁夏回族自治区",
	"Ningxia Hui Autonomous Region":     "宁夏回族自治区",
	"Qinghai":                           "青海省",
	"Xinjiang Uyghur Autonomous":        "新疆维吾尔自治区",
	"Xinjiang Uyghur Autonomous Region": "新疆维吾尔自治区",
	"Xinjiang":                          "新疆维吾尔自治区",
	"Tibet":                             "西藏自治区",
	"Hong Kong":                         "香港",
	"Taiwan(China)":                     "台湾",
	"Taiwan":                            "台湾",
	"Taipei City":                       "台湾",
	"广西":                              "广西壮族自治区",
	"新疆":                              "新疆维吾尔自治区",
	"西藏":                              "西藏自治区",
	"宁夏":                              "宁夏回族自治区",
	"内蒙古":                            "内蒙古自治区",
	"北京":                              "北京市",
	"广东":                              "广东省",
	"浙江":                              "浙江省",
	"上海":                              "上海市",
	"江苏":                              "江苏省",
	"山东":                              "山东省",
	"四川":                              "四川省",
	"福建":                              "福建省",
	"辽宁":                              "辽宁省",
	"河南":                              "河南省",
	"安徽":                              "安徽省",
	"湖北":                              "湖北省",
	"湖南":                              "湖南省",
	"重庆":                              "重庆市",
	"江西":                              "江西省",
	"河北":                              "河北省",
	"陕西":                              "陕西省",
	"天津":                              "天津市",
	"吉林":                              "吉林省",
	"云南":                              "云南省",
	"黑龙江":                            "黑龙江省",
	"山西":                              "山西省",
	"海南":                              "海南省",
	"甘肃":                              "甘肃省",
	"贵州":                              "贵州省",
	"青海":                              "青海省",
}

// RegionToProvince 将地区名称转换为省份名称
func RegionToProvince(name string) string {
	if name == "" {
		return ""
	}
	value := aereList[name]
	return value
}

// isPrivateIP 检查是否为私有IP
func isPrivateIP(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}
	return parsedIP.IsPrivate()
}

// GenerateAssetId 生成资产ID，兼容PHP中的generateAssetId逻辑
func GenerateAssetId(ip, port, protocol, subdomain string, userId uint64) string {
	// 对应PHP: md5(completeIPV6($ip).$port.$protocol.$subdomain.$user_id)
	completeIP := CompleteIPV6(ip)
	data := fmt.Sprintf("%s%s%s%s%d", completeIP, port, protocol, subdomain, userId)
	return Md5Hash(data)
}

// GetAssetSource 获取资产来源，根据推荐结果的assets_source字段
func GetAssetSource(source interface{}) string {
	if source == nil {
		return ""
	}

	switch v := source.(type) {
	case string:
		return v
	case int:
		switch v {
		case 1:
			return "FOFA"
		case 2:
			return "空间测绘"
		case 3:
			return "其他"
		default:
			return "未知"
		}
	}
	return ""
}

// GetIntValue 获取interface{}的int值
func GetIntValue(data interface{}) int {
	if data == nil {
		return 0
	}
	switch v := data.(type) {
	case string:
		i, err := strconv.Atoi(v)
		if err != nil {
			return 0
		}
		return i
	case int:
		return v
	case int64:
		return int(v)
	case int32:
		return int(v)
	case int16:
		return int(v)
	case int8:
		return int(v)
	case float64:
		return int(v)
	case float32:
		return int(v)
	case uint:
		return int(v)
	case uint64:
		return int(v)
	case uint32:
		return int(v)
	case uint16:
		return int(v)
	case uint8:
		return int(v)
	}
	return 0
}

// ToJSON 将对象转换为JSON字节数组
func ToJSON(v interface{}) *json.RawMessage {
	data, _ := json.Marshal(v)
	raw := json.RawMessage(data)
	return &raw
}
