#!/bin/sh
if [ "$1" = "api" ]; then
cat >bin/dockerfile_$1<<EOF
FROM centos:centos7
EXPOSE 8080
WORKDIR /data
VOLUME ['/etc/foradar','/data']
COPY ./foradar_cli_linux_amd64 /bin/foradar_cli
COPY ./foradar_$1_linux_amd64 /bin/foradar_$1
ENTRYPOINT ["/bin/bash", "-c", "/usr/bin/foradar_cli migrate run && /usr/bin/foradar_$1"]
CMD []
EOF
elif [ "$1" = "crawler" ]; then
cat >bin/dockerfile_$1<<EOF
FROM chromedp/headless-shell:114.0.5735.199
ENV LANG zh_CN.UTF-8
ENV LANGUAGE zh_CN.UTF-8
WORKDIR /data
VOLUME ['/etc/foradar','/data']
COPY ./foradar_cli_linux_amd64 /bin/foradar_cli
COPY ./foradar_$1_linux_amd64 /bin/foradar_$1
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && apt-get update -y && apt install -y --no-install-recommends ca-certificates curl xfonts-intl-chinese ttf-wqy-microhei xfonts-wqy && apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
ENTRYPOINT ["/bin/foradar_$1"]
CMD []
EOF
elif [ "$1" = "scan" ]; then
cat >bin/dockerfile_$1<<EOF
FROM centos:foradar
WORKDIR /data
VOLUME ['/etc/foradar','/data']
COPY ./foradar_cli_linux_amd64 /bin/foradar_cli
COPY ./foradar_$1_linux_amd64 /bin/foradar_$1
COPY ./bin/ksubdomain  /bin/ksubdomain
COPY ./bin/subdomain_dic.txt ./subdomain_files/subdomain_dic.txt
ENTRYPOINT ["/bin/foradar_$1"]
CMD []
EOF
elif [ "$1" = "web" ]; then
cat >bin/dockerfile_$1<<EOF
FROM centos:oneforall
WORKDIR /data
VOLUME ['/etc/foradar','/data']
COPY ./foradar_cli_linux_amd64 /bin/foradar_cli
COPY ./foradar_$1_linux_amd64 /bin/foradar_$1
ENTRYPOINT ["/bin/foradar_$1"]
CMD []
EOF
else
cat >bin/dockerfile_$1<<EOF
FROM centos:centos7
WORKDIR /data
VOLUME ['/etc/foradar','/data']
COPY ./foradar_cli_linux_amd64 /bin/foradar_cli
COPY ./foradar_$1_linux_amd64 /bin/foradar_$1
ENTRYPOINT ["/bin/foradar_$1"]
CMD []
EOF
fi

