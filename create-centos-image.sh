#!/bin/bash

# 检查是否存在 centos:foradar 镜像，如果不存在则构建
# 构建过程依赖同目录下的 dockerfile_centos 文件和 CentOS-Base.repo 文件

# 镜像名称和标签
IMAGE_NAME="centos:foradar"
DOCKERFILE_PATH="./dockerfile_centos"
BUILD_CONTEXT="."

# 检查镜像是否存在
if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^${IMAGE_NAME}$"; then
    echo "镜像 ${IMAGE_NAME} 已存在。"
else
    echo "镜像 ${IMAGE_NAME} 不存在，开始构建..."
    docker build -t ${IMAGE_NAME} -f ${DOCKERFILE_PATH} ${BUILD_CONTEXT}
    
    if [ $? -eq 0 ]; then
        echo "镜像 ${IMAGE_NAME} 构建成功。"
    else
        echo "镜像 ${IMAGE_NAME} 构建失败。"
        exit 1
    fi
fi


