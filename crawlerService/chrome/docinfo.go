package chrome

import (
	"context"
	"fmt"
	"github.com/chromedp/cdproto/network"
	"micro-service/pkg/log"
)

type DocumentInfo struct {
	// 服务器IP
	Ip string
	// 服务器端口
	Port int
	// 响应url
	RespUrl string
	// DNS加载时间,毫秒
	DnsTime int
	// 页面加载时间,毫秒
	LoadTime int
	// 网站响应时间,毫秒
	ResponseTime int
	// 状态码
	StatusCode int
	// 资源类型
	ResourceType network.ResourceType
	// 可以做筛选
	Resources map[string]Resource
}

type resourceMap struct {
	referUrl   string
	tp         network.ResourceType
	requestID  network.RequestID
	statusCode int
}

type Resource struct {
	Referer    string
	Url        string
	StatusCode int
	Type       network.ResourceType
	Value      []byte
}

type resourceParams struct {
	disableResource bool // 是否捕获网页资源，默认为false
	blockImage      bool
	blockCss        bool
	blockJs         bool
	blockFont       bool
	blockMedia      bool
}

func (p *resourceParams) BlockImage() *resourceParams {
	p.blockImage = true
	return p
}

func (p *resourceParams) BlockCss() *resourceParams {
	p.blockCss = true
	return p
}

// BlockJs todo 启用后有时会影响主页源码获取
func (p *resourceParams) BlockJs() *resourceParams {
	p.blockJs = true
	return p
}

func (p *resourceParams) BlockFont() *resourceParams {
	p.blockFont = true
	return p
}

func (p *resourceParams) BlockMedia() *resourceParams {
	p.blockMedia = true
	return p
}

func (d *DocumentInfo) PrintLogCtx(ctx context.Context) {
	log.WithContextInfof(ctx, fmt.Sprintf("Chrome加载状态: Ip:%s,Port:%d,DnsTime:%d,LoadTime:%d,StatusCode:%d", d.Ip, d.Port, d.DnsTime, d.LoadTime, d.StatusCode))
}
func (d *DocumentInfo) PrintLog() {
	log.Infof(fmt.Sprintf("Chrome加载状态: Ip:%s,Port:%d,DnsTime:%d,LoadTime:%d,StatusCode:%d", d.Ip, d.Port, d.DnsTime, d.LoadTime, d.StatusCode))
}
