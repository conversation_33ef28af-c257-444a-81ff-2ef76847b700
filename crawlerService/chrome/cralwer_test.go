package chrome

import (
	"testing"
)

func navigate(t *testing.T) {
	CrawlerHeadless = false
	CrawlerLoadTimeOut = 60
	var tab = InstanceMobile().NewTab()
	defer tab.Close()
	//屏蔽某些资源
	//tab.DisableCrawlResource().BlockImage().BlockFont()
	doc, er := tab.Navigate("https://micp.chinaz.com/?query=%E7%B2%A4B2-20090059")
	if er != nil {
		t.Log("获取文档失败", er.<PERSON>rror())
	}
	tab.GetDocument()

	t.Log("IP:", doc.Ip)
	t.Log("端口:", doc.Port)
	t.Log("dns响应时间", doc.DnsTime)
	t.Log("响应时间:", doc.ResponseTime)
	t.Log("加载时间:", doc.LoadTime)
	t.Log("状态码:", doc.StatusCode)

	//t.Log("\n" + string(text))
}

// 获取所有链接
//func TestTab_GetAllLinks(t *testing.T) {
//	CrawlerHeadless = false
//	CrawlerLoadTimeOut = 30
//	var browser = Instance()
//	defer browser.Close()
//	var tab = browser.NewTab()
//	defer tab.Close()
//	_, err := tab.Navigate("https://wenku.baidu.com/view/13d0f7623f1ec5da50e2524de518964bce84d25e.html?fr=income3-doc-search&_wkts_=1686195722483")
//	if err != nil {
//		t.Fatal(err.Error())
//	}
//	//fmt.Println(rs)
//	list, err := tab.GetAllLinks()
//	if err != nil {
//		t.Fatal(err.Error())
//	}
//	t.Log(list)
//}

//func TestChrome(t *testing.T) {
//	//var b chromedp.Browser
//	CrawlerHeadless = false
//	for i := 0; i < 2; i++ {
//		var tab1 = Instance().NewTab()
//		_, _ = tab1.Navigate("http://www.baidu.com")
//		var tab2 = Instance().NewTab()
//		_, _ = tab2.Navigate("http://www.qq.com")
//		Instance().Close()
//	}
//}
