package chrome

import (
	"context"
	"errors"
	"fmt"
	"github.com/chromedp/cdproto/network"
	"github.com/chromedp/chromedp"
	"micro-service/pkg/cfg"
	"micro-service/pkg/kuaidaili"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"os"
	"os/exec"
	"strings"
	"sync"
)

// CrawlerHeadless 全局配置参数
//
//	Crawler_Headless 无头模式
var CrawlerHeadless = true

// CrawlerLoadTimeOut 全局超时时间(秒)
var CrawlerLoadTimeOut = 15

// CrawlerWaitTime 全局页面加载完成后等待时间(毫秒)
var CrawlerWaitTime = 0

// CrawlerCapacity 标签页面上限
var CrawlerCapacity int = 40

// CrawlerCacheDirectory 指定缓存目录
var CrawlerCacheDirectory = "/tmp/chrome"

var DefaultResourceTypeAllow = map[network.ResourceType]struct{}{
	network.ResourceTypeImage:      struct{}{},
	network.ResourceTypeScript:     struct{}{},
	network.ResourceTypeStylesheet: struct{}{},
	network.ResourceTypeFont:       struct{}{},
}

var ErrUrlTimeout error = errors.New("网站已超时")
var ErrLoadFail error = errors.New("无效的网站")
var ErrInValidResponse error = errors.New("无效的响应")

const ProxyDisable = "disable"

const (
	objectTypeVpn     = 1
	objectTypeProxy   = 2
	objectTypeDefault = 3
)

// 单例对象
var _instance *browser
var locker sync.RWMutex

// VPN单例对象
var _vpnInstance *browser
var vpnLocker sync.RWMutex

// Mobile单例对象
var _mobileInstance *browser
var mobileLocker sync.RWMutex

type browser struct {
	ctx        context.Context
	cancel     context.CancelFunc
	objectType int
	ConcurrencyPool
}

// Close 关闭
func (bro *browser) Close() {
	bro.cancel()
}

// CloseProxyBrowser Close 关闭
func (bro *browser) CloseProxyBrowser() {
	if bro.objectType == objectTypeProxy {
		bro.cancel()
	}
}

func GetBrowserInstance(vpn int32, proxy string) *browser {
	socks := cfg.LoadSocks()
	if vpn != 0 && socks.Enable {
		return InstanceVpn()
	}
	if proxy != ProxyDisable {
		return NewProxyBrowser(proxy)
	}
	return Instance()
}

// NewProxyBrowser 实例对象 todo: 需要手动close关闭浏览器,每个代理会生成一个新的浏览器,
func NewProxyBrowser(proxy string) *browser {
	opts := getProxy(proxy)
	if len(opts) == 0 {
		return Instance()
	} else {
		ctx, cancel := newctx(opts)
		var res = &browser{
			ctx:        ctx,
			cancel:     cancel,
			objectType: objectTypeProxy,
		}
		res.Initial(CrawlerCapacity)
		return res
	}
}

// InstanceVpn VPN单例对象
func InstanceVpn() *browser {
	if _vpnInstance == nil {
		vpnLocker.Lock()
		if _vpnInstance == nil {
			ctx, cancel := newctx(getVpnOption())
			_vpnInstance = &browser{
				ctx:        ctx,
				cancel:     cancel,
				objectType: objectTypeVpn,
			}
			_vpnInstance.Initial(CrawlerCapacity)
		}
		vpnLocker.Unlock()
	}
	return _vpnInstance
}

// InstanceMobile Mobile单例对象
func InstanceMobile() *browser {
	if _mobileInstance == nil {
		mobileLocker.Lock()
		if _mobileInstance == nil {
			var actions = make([]chromedp.ExecAllocatorOption, 0)
			actions = append(actions, chromedp.UserAgent("Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1"))
			ctx, cancel := newctx(actions)
			_mobileInstance = &browser{
				ctx:        ctx,
				cancel:     cancel,
				objectType: objectTypeDefault,
			}
			_mobileInstance.Initial(CrawlerCapacity)
		}
		mobileLocker.Unlock()
	}
	return _mobileInstance
}

// Instance 单例对象
func Instance() *browser {
	if _instance == nil {
		locker.Lock()
		if _instance == nil {
			ctx, cancel := newctx(nil)
			_instance = &browser{
				ctx:        ctx,
				cancel:     cancel,
				objectType: objectTypeDefault,
			}
			_instance.Initial(CrawlerCapacity)
		}
		locker.Unlock()
	}
	return _instance
}

func newctx(options []chromedp.ExecAllocatorOption) (context.Context, context.CancelFunc) {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.DisableGPU,
		chromedp.NoDefaultBrowserCheck,
		chromedp.NoSandbox,
		chromedp.Flag("headless", CrawlerHeadless),
		chromedp.Flag("ignore-certificate-errors", true),
		chromedp.UserAgent(GetUserAgent()), // 设置User-Agent
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("disable-setuid-sandbox", true),
		chromedp.Flag("no-first-run", true),
		chromedp.Flag("ignore-certificate-errors", true),
		chromedp.Flag("ignore-ssl-errors", true),
		chromedp.Flag("no-zygote", true),
		chromedp.Flag("homedir", "/tmp/chrome"),
		chromedp.Flag("data-path", "/tmp/chrome/data-path"),
		chromedp.Flag("disk-cache-dir", "/tmp/chrome/cache-dir"),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.WindowSize(1920, 1080),
		chromedp.Flag("disable-breakpad", true),              // 禁用崩溃报告
		chromedp.Flag("disable-crash-reporter", true),        // 禁止无界面崩溃报告
		chromedp.Flag("disable-component-update", true),      // 禁止组件更新
		chromedp.Flag("disable-logging", true),               // 禁止记录日志
		chromedp.Flag("disable-notifications", true),         // 禁止网页通知
		chromedp.Flag("disable-notifications", true),         // 禁止网页通知
		chromedp.Flag("disable-background-networking", true), // 禁止后台联网
		chromedp.Flag("disable-web-security", true),          // 禁用网络安全标志
		chromedp.Flag("disable-extensions", true),            // 禁用扩展

		chromedp.Flag("disable-sync", true),
		chromedp.Flag("no-default-browser-check", true),
		chromedp.Flag("mute-audio", false),
		chromedp.Flag("enable-features", "NetworkService,NetworkServiceInProcess"),
		chromedp.Flag("disable-background-timer-throttling", true),
		chromedp.Flag("disable-backgrounding-occluded-windows", true),
		chromedp.Flag("disable-breakpad", true),
		chromedp.Flag("disable-client-side-phishing-detection", true),
		chromedp.Flag("disable-default-apps", true),
		chromedp.Flag("excludeSwitches", "enable-automation"),
		chromedp.Flag("disable-features", "site-per-process,Translate,BlinkGenPropertyTrees,AutomationControlled"),
		chromedp.Flag("disable-hang-monitor", true),
		chromedp.Flag("disable-ipc-flooding-protection", true),
		chromedp.Flag("disable-popup-blocking", true),
		chromedp.Flag("disable-prompt-on-repost", true),
		chromedp.Flag("safebrowsing-disable-auto-update", true),
		chromedp.Flag("password-store", "basic"),
	)

	//  设置浏览器位置
	if exists, err := utils.PathExists("/headless-shell/headless-shell"); err == nil && exists {
		opts = append(opts, chromedp.ExecPath("/headless-shell/headless-shell"))
	} else {
		// 未找到时,使用配置文件路径
		if exists, err = utils.PathExists(cfg.LoadCommon().ChromePath); err == nil && exists {
			opts = append(opts, chromedp.ExecPath(cfg.LoadCommon().ChromePath))
		}
	}
	if len(options) != 0 {
		opts = append(opts, options...)
	}
	allocCtx, _ := chromedp.NewExecAllocator(context.Background(), opts...)
	br, cancel := chromedp.NewContext(allocCtx)
	err := chromedp.Run(br)
	if err != nil {
		log.Fatal("无法启动Chrome,原因可能是未安装或安装在非默认位置;也有可能是浏览器崩溃了。" + err.Error())
	}
	return br, cancel
}

func getVpnOption() []chromedp.ExecAllocatorOption {
	var actions = make([]chromedp.ExecAllocatorOption, 0)
	socks := cfg.LoadSocks()
	// 设置VPN代理
	if socks.Enable {
		actions = append(actions, chromedp.ProxyServer(fmt.Sprintf("socks5://%s:%d", socks.LocalAddr, socks.LocalPort)))
		return actions
	}
	return actions
}

func getProxy(proxy string) []chromedp.ExecAllocatorOption {
	var actions = make([]chromedp.ExecAllocatorOption, 0)
	if proxy != ProxyDisable {
		if proxy == "" {
			if proxyUrl := kuaidaili.GetProxyIp(context.Background()); proxyUrl != "" {
				actions = append(actions, chromedp.ProxyServer("http://"+proxyUrl))
			}
		} else {
			if strings.Contains(proxy, "://") {
				actions = append(actions, chromedp.ProxyServer(proxy))
			} else {
				actions = append(actions, chromedp.ProxyServer("http://"+proxy))
			}
		}
		return actions
	}
	return actions
}

// NewTab 新开一个标签页
func (bro *browser) NewTab() *Tab {
	bro.Wait()
	var ers = bro.ctx.Err()
	if ers != nil {
		log.Warnf("浏览器被关闭，强制重开一个")
		if bro.objectType == objectTypeVpn {
			_vpnInstance = nil
			_ = ClearCache()
			bro = InstanceVpn()
		} else if bro.objectType == objectTypeProxy {
			_ = ClearCache()
			bro = NewProxyBrowser("")
		} else {
			_instance = nil
			bro = Instance()
		}
	}
	taskCtx, cancel := chromedp.NewContext(bro.ctx)
	var tab = Tab{
		ctx:     taskCtx,
		cancel:  cancel,
		browser: bro,
	}
	go func() {
		<-tab.ctx.Done()
		bro.Done()
	}()
	return &tab
}

// ClearCache 清理缓存，预防第一次爬取就304
func ClearCache() error {
	kill := exec.Command("pkill", "chrome")
	// 有奇怪的错误， exit status 128
	if kill != nil {
		_ = kill.Run()
	}
	return os.RemoveAll(CrawlerCacheDirectory)
}
