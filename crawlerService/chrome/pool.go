package chrome

import "micro-service/pkg/log"

type ConcurrencyPool struct {
	capacity int
	worker   chan struct{}
}

func (t *ConcurrencyPool) Initial(capacity int) *ConcurrencyPool {
	if capacity < 1 {
		log.Fatal("并发池的容量小于1")
	}
	t.capacity = capacity
	t.worker = make(chan struct{}, capacity)
	for i := 0; i < capacity; i++ {
		t.worker <- struct{}{}
	}
	return t
}

func (t *ConcurrencyPool) GetIdleCount() int {
	return len(t.worker)
}

func (t *ConcurrencyPool) Wait() {
	<-t.worker
}

func (t *ConcurrencyPool) Done() {
	t.worker <- struct{}{}
}
