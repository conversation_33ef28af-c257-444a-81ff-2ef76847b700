package chrome

import (
	"github.com/likexian/gokit/assert"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

func init() {
	cfg.InitLoadCfg()
	log.Init()
	redis.GetInstance(cfg.LoadRedis())
}

func Test_getProxy(t *testing.T) {
	type args struct {
		proxy string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		//{
		//	name: "empty",
		//	args: args{proxy: ""},
		//	want: 1,
		//},
		{
			name: "disable",
			args: args{proxy: ProxyDisable},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getProxy(tt.args.proxy)
			assert.Ge(t, len(got), tt.want)
		})
	}
}
