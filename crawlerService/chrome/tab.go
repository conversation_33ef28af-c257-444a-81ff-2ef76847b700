package chrome

import (
	"context"
	"errors"
	"micro-service/pkg/log"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/chromedp/cdproto/cdp"
	"github.com/chromedp/cdproto/dom"
	"github.com/chromedp/cdproto/fetch"
	"github.com/chromedp/cdproto/network"
	"github.com/chromedp/cdproto/page"
	"github.com/chromedp/chromedp"
)

type Tab struct {
	ctx            context.Context
	cancel         context.CancelFunc
	browser        *browser
	LoadTimeOut    int  // 秒
	WaitTime       int  // 毫秒
	AcceptDialog   bool // true表示在js弹出窗中按确认， false表示取消(默认)
	FaviconUrl     string
	resourceparams resourceParams
}

func (tb *Tab) DisableCrawlResource() *resourceParams {
	tb.resourceparams.disableResource = true
	return &tb.resourceparams
}

func (tb *Tab) Close() {
	tb.cancel()
}

// SetLoadTimeOut 默认为0，为0时取browser的时间
func (tb *Tab) SetLoadTimeOut(loadtime int) *Tab {
	tb.LoadTimeOut = loadtime
	return tb
}

// SetWaitTime 默认为0，为0时取browser的时间
func (tb *Tab) SetWaitTime(waittime int) *Tab {
	tb.WaitTime = waittime
	return tb
}

// GetPdfBytes 获取pdf字节数组
func (tb *Tab) GetPdfBytes() ([]byte, error) {
	var er error
	var pdfBuffer []byte
	er = chromedp.Run(tb.ctx,
		chromedp.ActionFunc(func(ctx context.Context) error {
			var err error
			pdfBuffer, _, err = page.PrintToPDF().WithPrintBackground(true).Do(ctx)
			return err
		}),
	)
	return pdfBuffer, er
}

// GetSnapShot 页面截图
func (tb *Tab) GetSnapShot(quality int) (*[]byte, error) {
	var er error
	var res []byte
	er = chromedp.Run(tb.ctx,
		chromedp.ActionFunc(func(ctx context.Context) error {
			var err error
			res, err = page.CaptureScreenshot().
				WithCaptureBeyondViewport(true).
				WithFromSurface(true).
				WithFormat(page.CaptureScreenshotFormatJpeg).
				WithQuality(int64(quality)).
				Do(ctx)
			return err
		}),
	)
	if len(res) == 0 {
		return nil, errors.New("截图失败")
	}
	return &res, er
}

// GetDocument 获取页面元素文本
func (tb *Tab) GetDocument() (res []byte, err error) {
	err = chromedp.Run(tb.ctx,
		chromedp.ActionFunc(func(ctx context.Context) error {
			doc, er := dom.GetDocument().Do(ctx)
			if er != nil {
				return er
			}
			str, dErr := dom.GetOuterHTML().WithNodeID(doc.NodeID).Do(ctx)
			if dErr != nil {
				return dErr
			}
			dom.QuerySelectorAll(doc.NodeID, "script")
			res = []byte(str)
			return nil
		}),
	)
	return res, err
}

// NavigateEvaluate 跳转一个页面，并执行脚本，返回数据给v
func (tb *Tab) NavigateEvaluate(rawUrl, rule string, v interface{}) (err error) {
	_, err = url.Parse(rawUrl)
	if err != nil {
		return ErrLoadFail
	}
	if !strings.HasPrefix(strings.TrimSpace(rawUrl), "http") {
		rawUrl = "http://" + rawUrl
	}
	err = chromedp.Run(tb.ctx, chromedp.Navigate(rawUrl))
	if err != nil {
		return err
	}
	err = chromedp.Run(tb.ctx, chromedp.Evaluate(rule, &v))
	return err
}

// Evaluate 在当前页面执行脚本
func (tb *Tab) Evaluate(rule string, v interface{}) error {
	return chromedp.Run(tb.ctx, chromedp.Evaluate(rule, &v))
}

func (tb *Tab) getBlockPatterns() []*fetch.RequestPattern {
	var blockpatterns = make([]*fetch.RequestPattern, 0)
	if tb.resourceparams.disableResource {
		if tb.resourceparams.blockImage {
			blockpatterns = append(blockpatterns, &fetch.RequestPattern{
				ResourceType: network.ResourceTypeImage,
				RequestStage: "Request",
			})
		}
		if tb.resourceparams.blockJs {
			blockpatterns = append(blockpatterns, &fetch.RequestPattern{
				ResourceType: network.ResourceTypeScript,
				RequestStage: "Request",
			})
		}
		if tb.resourceparams.blockCss {
			blockpatterns = append(blockpatterns, &fetch.RequestPattern{
				ResourceType: network.ResourceTypeStylesheet,
				RequestStage: "Request",
			})
		}
		if tb.resourceparams.blockFont {
			blockpatterns = append(blockpatterns, &fetch.RequestPattern{
				ResourceType: network.ResourceTypeFont,
				RequestStage: "Request",
			})
		}
		if tb.resourceparams.blockMedia {
			blockpatterns = append(blockpatterns, &fetch.RequestPattern{
				ResourceType: network.ResourceTypeMedia,
				RequestStage: "Request",
			})
		}
	}
	return blockpatterns
}

// handlerAuthRequired 取消认证
func (tb *Tab) handlerAuthRequired(req *fetch.EventAuthRequired) {
	_ = fetch.ContinueWithAuth(req.RequestID, &fetch.AuthChallengeResponse{
		Response: fetch.AuthChallengeResponseResponseProvideCredentials,
		Username: "admin",
		Password: "12345678",
	}).Do(tb.ctx)
}

// handlerResponseReceived 请求返回处理
func (tb *Tab) handlerResponseReceived(evt *network.EventResponseReceived, documentReceived *bool, doc *DocumentInfo, res *sync.Map) {
	var resp = evt.Response
	// 获取favicon
	if strings.Contains(resp.URL, "favicon.ico") || strings.Contains(resp.URL, "favicon.") {
		tb.FaviconUrl = resp.URL
	}
	if !*documentReceived && evt.Type == network.ResourceTypeDocument {
		*documentReceived = true // 第一个下载成功的document为未渲染的源码
		doc.StatusCode = int(resp.Status)
		doc.Ip = resp.RemoteIPAddress   // 网站IP
		doc.Port = int(resp.RemotePort) // 网站端口
		doc.RespUrl = resp.URL
		doc.ResourceType = evt.Type
		if resp.Timing != nil {
			doc.DnsTime = int(resp.Timing.DNSEnd - resp.Timing.DNSStart)
			doc.ResponseTime = int(resp.Timing.ReceiveHeadersEnd)
		}
	} else {
		// 页面资源类型的舍弃
		if strings.HasPrefix(strings.ToLower(strings.TrimSpace(resp.URL)), "data:") {
			return
		}
		val, ok := res.Load(resp.URL)
		if ok {
			res.Store(resp.URL, resourceMap{
				tp:         evt.Type,
				requestID:  evt.RequestID,
				referUrl:   val.(resourceMap).referUrl,
				statusCode: int(evt.Response.Status),
			})
		} else {
			res.Store(resp.URL, resourceMap{
				tp:         evt.Type,
				requestID:  evt.RequestID,
				statusCode: int(evt.Response.Status),
			})
		}
	}
}

// handlerJavascriptDialogOpening 弹窗自动关闭，不太好用，不能正确匹配确认或者取消
func (tb *Tab) handlerJavascriptDialogOpening() {
	t := page.HandleJavaScriptDialog(tb.AcceptDialog)
	nctx := chromedp.FromContext(tb.ctx)
	lctx := cdp.WithExecutor(tb.ctx, nctx.Target)
	go func() {
		_ = chromedp.Run(lctx, t)
	}()
}

func (tb *Tab) handlerRequestPaused(evt *fetch.EventRequestPaused) {
	nctx := chromedp.FromContext(tb.ctx)
	lctx := cdp.WithExecutor(tb.ctx, nctx.Target)
	_ = fetch.FailRequest(evt.RequestID, network.ErrorReasonAborted).Do(lctx)
}

func (tb *Tab) handlerRequestWillBeSent(evt *network.EventRequestWillBeSent, res *sync.Map) {
	// 页面资源类型,XHR异步数据的舍弃
	if strings.HasPrefix(strings.ToLower(strings.TrimSpace(evt.Request.URL)), "data:") {
		return
	}
	if tb.resourceparams.disableResource {
		if tb.resourceparams.blockImage && evt.Type == network.ResourceTypeImage {
			return
		}
		if tb.resourceparams.blockJs && evt.Type == network.ResourceTypeScript {
			return
		}
		if tb.resourceparams.blockCss && evt.Type == network.ResourceTypeStylesheet {
			return
		}
		if tb.resourceparams.blockFont && evt.Type == network.ResourceTypeFont {
			return
		}
		if tb.resourceparams.blockMedia && evt.Type == network.ResourceTypeMedia {
			return
		}
	}
	refer, ok := evt.Request.Headers["Referer"]
	if ok {
		val, lOk := res.Load(evt.Request.URL)
		if lOk {
			res.Store(evt.Request.URL, resourceMap{
				referUrl: val.(resourceMap).referUrl,
			})
		} else {
			res.Store(evt.Request.URL, resourceMap{referUrl: refer.(string)})
		}
	}
}

// Navigate 跳转页面并获取各种页面信息
func (tb *Tab) Navigate(rawUrl string, headers ...network.Headers) (doc DocumentInfo, err error) {
	_, err = url.Parse(rawUrl)
	if err != nil {
		return doc, ErrLoadFail
	}
	if !strings.HasPrefix(strings.TrimSpace(rawUrl), "http") {
		rawUrl = "http://" + rawUrl
	}
	doc = DocumentInfo{Resources: make(map[string]Resource, 0)}
	var res sync.Map

	var start = time.Now()
	// 计算加载时长
	defer func() {
		doc.LoadTime = int(time.Since(start).Milliseconds())
	}()
	if tb.LoadTimeOut == 0 {
		tb.LoadTimeOut = CrawlerLoadTimeOut
	}
	var done = make(chan struct{})
	var documentReceived = false
	var doneOnce sync.Once
	{
		blockPatterns := tb.getBlockPatterns()
		var actions = make([]chromedp.Action, 0)
		if tb.resourceparams.disableResource && len(blockPatterns) > 0 {
			actions = append(actions, fetch.Enable().WithPatterns(blockPatterns))
		}
		actions = append(actions, network.Enable(), chromedp.Navigate(rawUrl))
		if len(headers) != 0 {
			network.SetExtraHTTPHeaders(headers[0])
		}
		var ctxErr error
		go func() {
			defer func() {
				// 使用sync.Once确保channel只被关闭一次，避免panic
				doneOnce.Do(func() {
					close(done)
				})
			}()

			chromedp.ListenTarget(tb.ctx, func(ev interface{}) {
				// 退出则放弃
				ctxErr = tb.ctx.Err()
				if ctxErr != nil && err == context.Canceled {
					log.Info("Chrome Table 已退出")
					return
				}
				switch event := ev.(type) {
				case *fetch.EventAuthRequired:
					// 取消认证
					go tb.handlerAuthRequired(event)
				case *network.EventResponseReceived:
					go tb.handlerResponseReceived(event, &documentReceived, &doc, &res)
				case *fetch.EventRequestPaused:
					// 通过fetch屏蔽资源
					go tb.handlerRequestPaused(event)
				case *network.EventRequestWillBeSent:
					go tb.handlerRequestWillBeSent(event, &res)
				case *page.EventJavascriptDialogOpening:
					// 弹窗自动关闭，不太好用，不能正确匹配确认或者取消
					go tb.handlerJavascriptDialogOpening()
				}
			})
			// network.enable必须在navigate之前
			errr := chromedp.Run(tb.ctx, actions...)
			if errr != nil {
				log.Info("Chrome访问失败,Url:" + rawUrl + ",ERR:" + errr.Error())
			}
		}()
	}

	select {
	case <-time.After(time.Second * time.Duration(tb.LoadTimeOut)):
		// 超时时使用sync.Once确保channel只被关闭一次
		doneOnce.Do(func() {
			close(done)
		})
		// 加载失败
		if !documentReceived || doc.Ip == "" {
			return doc, ErrLoadFail
		}
		// 超时
		return doc, ErrUrlTimeout
	case <-done:
		// 加载失败
		if !documentReceived || doc.Ip == "" {
			return doc, ErrLoadFail
		}
		// 强制等待时间
		if tb.WaitTime == 0 {
			tb.WaitTime = CrawlerWaitTime
		}
		time.Sleep(time.Duration(tb.WaitTime) * time.Millisecond)
	}

	// 为资源赋值responsebody
	nctx := chromedp.FromContext(tb.ctx)
	lctx := cdp.WithExecutor(tb.ctx, nctx.Target)
	err = chromedp.Run(lctx, chromedp.ActionFunc(func(ctx context.Context) error {
		// 暂时不选择并行，因为有丢失的问题，当前采用单协程重试机制
		res.Range(func(key, value interface{}) bool {
			var tp = value.(resourceMap).tp
			if _, ok := DefaultResourceTypeAllow[tp]; !ok {
				return true // 不存在则返回并继续遍历
			}
			var newval = Resource{
				Type:       tp,
				Url:        key.(string),
				StatusCode: value.(resourceMap).statusCode,
				Referer:    value.(resourceMap).referUrl,
			}
			body, er := network.GetResponseBody(value.(resourceMap).requestID).Do(lctx)
			if er == nil && body != nil && len(body) > 0 {
				newval.Value = body
			}
			doc.Resources[key.(string)] = newval
			return true
		})
		return nil
	}))
	return doc, err
}

// GetAllLinks 获取当前页面的所有链接
func (tb *Tab) GetAllLinks() ([]string, error) {
	var list = make([]string, 0)
	err := tb.Evaluate(`
			var ls = [];
			for(i=0;i<document.links.length;i++){
				ls.push(document.links[i].href);
			};
			ls;
		`, &list)
	return list, err
}
