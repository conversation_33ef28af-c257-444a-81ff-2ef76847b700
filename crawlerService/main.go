package main

import (
	"micro-service/crawlerService/brokers"
	handler2 "micro-service/crawlerService/handler"
	crawler "micro-service/crawlerService/proto"
	"micro-service/crawlerService/ssr"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/traces"
	"micro-service/pkg/utils"
	"time"

	"github.com/opentracing/opentracing-go"

	microZap "github.com/go-micro/plugins/v4/logger/zap"
	ocplugin "github.com/go-micro/plugins/v4/wrapper/trace/opentracing"
	"github.com/hashicorp/go-hclog"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"
)

func onInit() {
	cfg.InitLoadCfg()

	// 初始化日志
	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// Mysql & Reids
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

func main() {
	onInit()
	// jaeger 链路追踪
	if jaegerTrace, io, err := traces.GetJaegerTracer(crawler.ServiceName, cfg.LoadCommon().JaegerAdder); err != nil {
		log.Errorf("Jaeger: %s Error:", cfg.LoadCommon().JaegerAdder, err.Error())
		panic(err)
	} else {
		defer io.Close()
		opentracing.SetGlobalTracer(jaegerTrace)
		log.Infof("Jaeger: %s Connection successful", cfg.LoadCommon().JaegerAdder)
	}
	consulReg := cfg.GetInstance().GetConsulReg()
	// 启动VPN代理
	go startShadowSocks()
	// Create service
	srv := micro.NewService(
		micro.Name(crawler.ServiceName),
		micro.Version(crawler.ServiceVersion),
		micro.RegisterTTL(15*time.Second),      // TTL指定从上一次心跳间隔起，超过这个时间服务会被服务发现移除
		micro.RegisterInterval(10*time.Second), // 让服务在指定时间内重新注册，保持TTL获取的注册时间有效
		micro.Logger(logger.DefaultLogger),
		micro.Address(utils.GetListenAddress(cfg.LoadCommon().Network)),
		micro.Broker(brokers.NewCrawlerBroker()),
		micro.WrapHandler(ocplugin.NewHandlerWrapper(opentracing.GlobalTracer())),
		micro.WrapClient(ocplugin.NewClientWrapper(opentracing.GlobalTracer())),
		micro.AfterStart(brokers.RegisterApiAnalyzeSubscriber),
	)
	srv.Init(
		micro.Registry(consulReg),
	)

	// Register handler
	if err := crawler.RegisterCrawlerHandler(srv.Server(), new(handler2.Crawler)); err != nil {
		log.Errorf("Failed to register handler: %v", err)
		panic(err)
	}

	brokers.SetServerInst(srv.Server())

	// Run service
	if err := srv.Run(); err != nil {
		panic(err)
	}
}

func startShadowSocks() {
	opts := cfg.LoadSocks()
	ssrClient, err := ssr.NewSSRClient(
		opts.ServerAddr,
		opts.ServerPort,
		opts.LocalAddr,
		opts.LocalPort,
		opts.Password,
		opts.Method,
		opts.Obfs,
		opts.ObfsParam,
		opts.Protocol,
		opts.ProtocolParam,
		time.Duration(opts.SocketTimeout)*time.Second,
	)

	if err != nil {
		log.Fatal(err)
	}
	log.Infof("ShadowSocks [tcp|udp] Listening on %s:%d", opts.LocalAddr, opts.LocalPort)
	ssrClient.ListenAndServe()
}
