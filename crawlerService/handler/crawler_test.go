package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/pkg/log"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/chromedp/chromedp"

	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
)

func initCfg() {
	cfg.InitLoadCfg()
	log.Init()
	_ = redis.GetInstance(cfg.LoadRedis())
}

const (
	maxBatchSize    = 30    // 每批处理的最大URL数量
	cacheExpiration = 86400 // Redis缓存过期时间（24小时）
)

// 全局单例
var (
	globalBrowserManager *BrowserManager
	once                 sync.Once
)

// 浏览器实例管理结构体
type BrowserManager struct {
	ctx        context.Context
	cancel     context.CancelFunc
	count      int
	lastAccess time.Time
	mutex      sync.Mutex
}

func NewBrowserManager() *BrowserManager {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", false),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"),
		chromedp.Flag("ignore-certificate-errors", true),
	)

	allocatorCtx, _ := chromedp.NewExecAllocator(context.Background(), opts...)
	ctx, cancel := chromedp.NewContext(allocatorCtx)

	return &BrowserManager{
		ctx:        ctx,
		cancel:     cancel,
		count:      0,
		lastAccess: time.Now(),
		mutex:      sync.Mutex{},
	}
}

// 获取浏览器管理器实例
func GetBrowserManager() *BrowserManager {
	once.Do(func() {
		globalBrowserManager = NewBrowserManager()

		// 添加定时检查，如果长时间未使用则重置
		go func() {
			ticker := time.NewTicker(10 * time.Minute)
			for range ticker.C {
				globalBrowserManager.mutex.Lock()
				if time.Since(globalBrowserManager.lastAccess) > time.Hour {
					globalBrowserManager.Reset()
				}
				globalBrowserManager.mutex.Unlock()
			}
		}()
	})
	return globalBrowserManager
}

func (bm *BrowserManager) Reset() {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	bm.cancel()
	allocatorCtx, _ := chromedp.NewExecAllocator(context.Background(), chromedp.DefaultExecAllocatorOptions[:]...)
	ctx, cancel := chromedp.NewContext(allocatorCtx)
	bm.ctx = ctx
	bm.cancel = cancel
	bm.count = 0
	bm.lastAccess = time.Now()
}

func (bm *BrowserManager) GetDNSResults(domain string) ([]string, error) {

	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	// 检查是否需要重置浏览器（超过30次请求或最后访问时间超过1小时）
	if bm.count >= maxBatchSize || time.Since(bm.lastAccess) > time.Hour {
		bm.Reset()
	}

	bm.count++
	bm.lastAccess = time.Now()

	// 先从Redis获取缓存
	redisClient := redis.GetInstance(cfg.LoadRedis())
	cacheKey := fmt.Sprintf("dns:resolve:%s", domain)

	// 检查缓存
	if cached, err := redisClient.Get(context.Background(), cacheKey).Result(); err == nil {
		var ips []string
		if err := json.Unmarshal([]byte(cached), &ips); err == nil {
			return ips, nil
		}
	}

	// 检查是否需要重置浏览器
	if bm.count >= maxBatchSize {
		bm.Reset()
	}
	bm.count++

	ipMap := make(map[string]struct{})
	var htmlContent string

	err := chromedp.Run(bm.ctx,
		// 导航到主页
		chromedp.Navigate("https://dnschecker.org"),
		chromedp.SetValue(`#q`, domain, chromedp.ByID),
		chromedp.Sleep(1*time.Second),
		chromedp.Click(`#s`, chromedp.ByID),
		chromedp.Sleep(1*time.Second),
		chromedp.WaitVisible(`.main_page_dns_servers`, chromedp.ByQuery),
		chromedp.Sleep(3*time.Second),
		chromedp.OuterHTML(`.main_page_dns_servers`, &htmlContent, chromedp.ByQuery),
	)

	if err != nil {
		return nil, fmt.Errorf("DNS查询失败: %v", err)
	}

	// 解析IP地址
	ipRegex := regexp.MustCompile(`ip=([^"]+)`)
	matches := ipRegex.FindAllStringSubmatch(htmlContent, -1)
	ipMatcher := regexp.MustCompile(`\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b`)

	var results []string
	for _, match := range matches {
		if len(match) > 1 {
			ips := ipMatcher.FindAllString(match[1], -1)
			for _, ip := range ips {
				if isValidIP(ip) {
					ipMap[ip] = struct{}{}
				}
			}
		}
	}

	// 转换结果为切片
	for ip := range ipMap {
		results = append(results, ip)
	}

	// 缓存结果
	if len(results) > 0 {
		if cached, err := json.Marshal(results); err == nil {
			redisClient.Set(context.Background(), cacheKey, cached, time.Duration(cacheExpiration)*time.Second)
		}
	}

	return results, nil
}

// 使用示例
func TestSyncUpdateEventIntelligence(t *testing.T) {
	initCfg()

	// 使用全局浏览器管理器
	bm := GetBrowserManager()
	// 不需要手动调用 defer bm.cancel()，因为这是全局单例

	// 测试域名
	domains := []string{"fofa.info", "example.com", "google.com"}

	for _, domain := range domains {
		ips, err := bm.GetDNSResults(domain)
		if err != nil {
			fmt.Printf("解析 %s 失败: %v\n", domain, err)
			continue
		}

		fmt.Printf("域名 %s 解析结果:\n", domain)
		for _, ip := range ips {
			fmt.Println(ip)
		}
	}
}

// 简单IP验证函数
func isValidIP(ip string) bool {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}
	for i, part := range parts {
		if len(part) == 0 || len(part) > 3 {
			return false
		}
		// 检查每部分是否为有效数字
		num, err := strconv.Atoi(part)
		if err != nil || num < 0 || num > 255 {
			return false
		}
		// 检查前导零
		if part[0] == '0' && len(part) > 1 {
			return false
		}
		// 检查私有和回环地址
		if i == 0 {
			switch num {
			case 10:
				return false
			case 172:
				if len(parts) > 1 {
					secondOctet, _ := strconv.Atoi(parts[1])
					if secondOctet >= 16 && secondOctet <= 31 {
						return false
					}
				}
			case 192:
				if len(parts) > 1 && parts[1] == "168" {
					return false
				}
			case 127:
				return false
			}
		}
	}
	return true
}
