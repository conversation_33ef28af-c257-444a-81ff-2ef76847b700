package handler

import (
	pb "micro-service/crawlerService/proto"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDetectByIpPort(t *testing.T) {
	req := &pb.DetectByIpPortRequest{Network: "tcp", Timeout: 3}
	for i := 100; i < 5000; i++ {
		req.IpPort = append(req.IpPort, &pb.IpPort{Ip: "127.0.0.1", Port: strconv.Itoa(i)})
	}

	rsp := &pb.DetectByIpPortResponse{}
	err := DetectByIpPort(req, rsp)
	assert.Nil(t, err)
}
