package url_crawler

import (
	"io"
	"micro-service/crawlerService/handler/api_analyzer/result"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/util"
	"net/http"
	"regexp"
	"strconv"
	"strings"
)

// UrlFuzz URL模糊测试
func UrlFuzz() {
	var host string
	re := regexp.MustCompile("([a-z0-9\\-]+\\.)*([a-z0-9\\-]+\\.[a-z0-9\\-]+)(:[0-9]+)?")
	hosts := re.FindAllString(conf.TargetURL, 1)
	if len(hosts) == 0 {
		host = conf.TargetURL
	} else {
		host = hosts[0]
	}
	if conf.Domain != "" {
		host = conf.Domain
	}
	disposes, _ := util.UrlDispose(append(result.ResultUrl, structs.Link{URL: conf.TargetURL, Status: "200"}), host, "", conf)
	if conf.Fuzz == 2 || conf.Fuzz == 3 {
		fuzz2(disposes)
	} else if conf.Fuzz != 0 {
		fuzz1(disposes)
	}
}

// fuzz请求
func fuzzGet(u string) {
	defer func() {
		structs.Wg.Done()
		<-structs.Ch
	}()
	if conf.Mode == 3 {
		for _, v := range structs.Risks {
			if strings.Contains(u, v) {
				return
			}
		}
	}
	request, err := http.NewRequest("GET", u, nil)
	if err != nil {
		return
	}
	if conf.Cookie != "" {
		request.Header.Set("Cookie", conf.Cookie)
	}
	//增加header选项
	request.Header.Set("User-Agent", util.GetUserAgent(conf))
	request.Header.Set("Accept", "*/*")
	//处理返回结果
	response, err := client.Do(request)
	if err != nil {
		return
	} else {
		defer response.Body.Close()
	}
	code := response.StatusCode
	if strings.Contains(conf.StatusCode, strconv.Itoa(code)) || conf.StatusCode == "all" {
		var length int
		dataBytes, err := io.ReadAll(response.Body)
		if err != nil {
			length = 0
		} else {
			length = len(dataBytes)
		}
		body := string(dataBytes)
		re := regexp.MustCompile("<Title>(.*?)</Title>")
		title := re.FindAllStringSubmatch(body, -1)
		structs.Lock.Lock()
		defer structs.Lock.Unlock()
		if len(title) != 0 {
			result.Fuzzs = append(result.Fuzzs, structs.Link{URL: u, Status: strconv.Itoa(code), Body: structs.LinkBody{Code: code, Size: length, Data: body}, Title: title[0][1], Source: "Fuzz"})
		} else {
			result.Fuzzs = append(result.Fuzzs, structs.Link{URL: u, Status: strconv.Itoa(code), Body: structs.LinkBody{Code: code, Size: length, Data: body}, Title: "", Source: "Fuzz"})
		}

	}

}
func fuzz1(disposes []structs.Link) {
	dispose404 := []string{}
	for _, v := range disposes {
		if v.Status == "404" {
			dispose404 = append(dispose404, v.URL)
		}
	}
	fuzz1s := []string{}
	host := ""
	if len(dispose404) != 0 {
		host = regexp.MustCompile("(http.{0,1}://.+?)/").FindAllStringSubmatch(dispose404[0]+"/", -1)[0][1]
	}

	for _, v := range dispose404 {
		submatch := regexp.MustCompile("http.{0,1}://.+?(/.*)").FindAllStringSubmatch(v, -1)
		if len(submatch) != 0 {
			v = submatch[0][1]
		} else {
			continue
		}
		v1 := v
		v2 := v
		reh2 := ""
		if !strings.HasSuffix(v, "/") {
			_submatch := regexp.MustCompile("/.+(/[^/]+)").FindAllStringSubmatch(v, -1)
			if len(_submatch) != 0 {
				reh2 = _submatch[0][1]
			} else {
				continue
			}
		}
		for {
			re1 := regexp.MustCompile("/.+?(/.+)").FindAllStringSubmatch(v1, -1)
			re2 := regexp.MustCompile("(/.+)/[^/]+").FindAllStringSubmatch(v2, -1)
			if len(re1) == 0 && len(re2) == 0 {
				break
			}
			if len(re1) > 0 {
				v1 = re1[0][1]
				fuzz1s = append(fuzz1s, host+v1)
			}
			if len(re2) > 0 {
				v2 = re2[0][1]
				fuzz1s = append(fuzz1s, host+v2+reh2)
			}
		}
	}
	fuzz1s = util.UniqueArr(fuzz1s)
	structs.FuzzNum = len(fuzz1s)
	structs.Progress = 1
	for _, v := range fuzz1s {
		structs.Wg.Add(1)
		structs.Ch <- 1
		go fuzzGet(v)
	}
	structs.Wg.Wait()
	result.Fuzzs = util.Del404(result.Fuzzs)
}

func fuzz2(disposes []structs.Link) {
	disposex := []string{}
	dispose404 := []string{}
	for _, v := range disposes {
		if v.Status == "404" {
			dispose404 = append(dispose404, v.URL)
		}
		//防止太多跑不完
		if len(dispose404) > 20 {
			if v.Status != "timeout" && v.Status != "404" {
				disposex = append(disposex, v.URL)
			}
		} else {
			if v.Status != "timeout" {
				disposex = append(disposex, v.URL)
			}
		}

	}
	dispose, _ := util.PathExtract(disposex, conf)
	_, targets := util.PathExtract(dispose404, conf)

	structs.FuzzNum = len(dispose) * len(targets)
	structs.Progress = 1
	for _, v := range dispose {
		for _, vv := range targets {
			structs.Wg.Add(1)
			structs.Ch <- 1
			go fuzzGet(v + vv)
		}
	}
	structs.Wg.Wait()
	result.Fuzzs = util.Del404(result.Fuzzs)
}
