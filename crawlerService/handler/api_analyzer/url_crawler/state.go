package url_crawler

import (
	"io"
	"micro-service/crawlerService/handler/api_analyzer/result"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/util"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
)

// 检测js访问状态码
func JsState(u string, i int, sou string) {

	defer func() {
		structs.Wg.Done()
		<-structs.Jsch
	}()
	if conf.StatusCode == "" {
		result.ResultJs[i].URL = u
		return
	}
	if conf.Mode == 3 {
		for _, v := range structs.Risks {
			if strings.Contains(u, v) {
				result.ResultJs[i] = structs.Link{URL: u, Status: "疑似危险路由"}
				return
			}
		}
	}

	//配置代理
	var redirect string
	ur, err2 := url.Parse(u)
	if err2 != nil {
		return
	}
	request, err := http.NewRequest("GET", ur.String(), nil)
	if err != nil {
		result.ResultJs[i].URL = ""
		return
	}
	if conf.Cookie != "" {
		request.Header.Set("Cookie", conf.<PERSON>ie)
	}
	//增加header选项
	request.Header.Set("User-Agent", util.GetUserAgent(conf))
	request.Header.Set("Accept", "*/*")

	//处理返回结果
	response, err := client.Do(request)
	if err != nil {
		if strings.Contains(err.Error(), "Client.Timeout") && conf.StatusCode == "" {
			result.ResultJs[i] = structs.Link{URL: u, Status: "timeout"}

		} else {
			result.ResultJs[i].URL = ""
		}
		return
	}
	defer response.Body.Close()

	code := response.StatusCode
	if strings.Contains(conf.StatusCode, strconv.Itoa(code)) || conf.StatusCode == "all" && (sou != "Fuzz" && code == 200) {
		var length int
		dataBytes, err := io.ReadAll(response.Body)
		if err != nil {
			length = 0
		} else {
			length = len(dataBytes)
		}
		structs.Lock.Lock()
		if result.Redirect[ur.String()] {
			code = 302
			redirect = response.Request.URL.String()
		}
		structs.Lock.Unlock()
		result.ResultJs[i] = structs.Link{URL: u, Status: strconv.Itoa(code), Body: structs.LinkBody{Code: code, Size: length, Type: ""}, Redirect: redirect}
	} else {
		result.ResultJs[i].URL = ""
	}
	structs.PrintJsProgress(conf.TaskID)
}

// 检测url访问状态码
func UrlState(u string, i int) {
	defer func() {
		structs.Wg.Done()
		<-structs.Urlch
		//PrintProgress()
	}()
	if conf.StatusCode == "" {
		result.ResultUrl[i].URL = u
		return
	}
	if conf.Mode == 3 {
		for _, v := range structs.Risks {
			if strings.Contains(u, v) {
				result.ResultUrl[i] = structs.Link{URL: u, Status: "0", Body: structs.LinkBody{Code: 0, Size: 0, Type: "skip"}, Title: "疑似危险路由,已跳过验证"}
				return
			}
		}
	}

	var redirect string
	ur, err2 := url.Parse(u)
	if err2 != nil {
		return
	}
	request, err := http.NewRequest("GET", ur.String(), nil)
	if err != nil {
		result.ResultUrl[i].URL = ""
		return
	}

	if conf.Cookie != "" {
		request.Header.Set("Cookie", conf.Cookie)
	}
	//增加header选项
	request.Header.Set("User-Agent", util.GetUserAgent(conf))
	request.Header.Set("Accept", "*/*")

	//处理返回结果
	response, err := client.Do(request)
	if err != nil {
		if strings.Contains(err.Error(), "Client.Timeout") && conf.StatusCode == "all" {
			result.ResultUrl[i] = structs.Link{URL: u, Status: "timeout"}
		} else {
			result.ResultUrl[i].URL = ""
		}
		return
	}
	defer response.Body.Close()

	code := response.StatusCode
	if strings.Contains(conf.StatusCode, strconv.Itoa(code)) || conf.StatusCode == "all" {
		var length int
		dataBytes, err := io.ReadAll(response.Body)
		if err != nil {
			length = 0
		} else {
			length = len(dataBytes)
		}
		body := string(dataBytes)
		re := regexp.MustCompile("<[tT]itle>(.*?)</[tT]itle>")
		title := re.FindAllStringSubmatch(body, -1)
		structs.Lock.Lock()
		if result.Redirect[ur.String()] {
			code = 302
			redirect = response.Request.URL.String()
		}
		structs.Lock.Unlock()
		if len(title) != 0 {
			result.ResultUrl[i] = structs.Link{
				URL:      u,
				Status:   strconv.Itoa(code),
				Body:     structs.LinkBody{Code: code, Size: length, Data: body, Type: formatContentType(response.Header.Get("Content-Type"))},
				Title:    title[0][1],
				Redirect: redirect,
			}
		} else {
			result.ResultUrl[i] = structs.Link{
				URL:      u,
				Status:   strconv.Itoa(code),
				Body:     structs.LinkBody{Code: code, Size: length, Data: body, Type: formatContentType(response.Header.Get("Content-Type"))},
				Redirect: redirect,
			}
		}
	} else {
		result.ResultUrl[i].URL = ""
	}
	structs.PrintUrlProgress(conf.TaskID)
}

func formatContentType(text string) string {
	return text[strings.LastIndex(text, "/")+1:]
}
