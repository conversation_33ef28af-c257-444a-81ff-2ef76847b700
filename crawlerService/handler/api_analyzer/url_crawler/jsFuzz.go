package url_crawler

import (
	"micro-service/crawlerService/handler/api_analyzer/result"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/util"
	"regexp"
)

// JsFuzz JS模糊测试
func JsFuzz() {
	paths := []string{}
	for i := range result.ResultJs {
		re := regexp.MustCompile("(.+/)[^/]+.js").FindAllStringSubmatch(result.ResultJs[i].URL, -1)
		if len(re) != 0 {
			paths = append(paths, re[0][1])
		}
		re2 := regexp.MustCompile("(https{0,1}://([a-z0-9\\-]+\\.)*([a-z0-9\\-]+\\.[a-z0-9\\-]+)(:[0-9]+)?/)").FindAllStringSubmatch(result.ResultJs[i].URL, -1)
		if len(re2) != 0 {
			paths = append(paths, re2[0][1])
		}
	}
	paths = util.UniqueArr(paths)
	for i := range paths {
		for i2 := range structs.JsFuzzPath {
			result.ResultJs = append(result.ResultJs, structs.Link{
				URL:    paths[i] + structs.JsFuzzPath[i2],
				Source: "Fuzz",
			})
		}
	}
}
