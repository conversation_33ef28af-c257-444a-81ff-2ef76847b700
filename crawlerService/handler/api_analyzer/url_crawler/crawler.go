package url_crawler

import (
	"compress/gzip"
	"io"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/util"
	"net/http"
	"net/url"
	"regexp"
	"strings"
)

// Spider 抓取页面内容
func Spider(u string, num int) {
	is := true
	defer func() {
		structs.Wg.Done()
		if is {
			<-structs.Ch
		}

	}()
	structs.Mux.Lock()
	structs.Progress++
	structs.Mux.Unlock()

	//标记完成
	u, _ = url.QueryUnescape(u)
	if num > 1 && conf.Domain != "" && !regexp.MustCompile(conf.Domain).MatchString(u) {
		return
	}
	if GetEndUrl(u) {
		return
	}
	if conf.Mode == 3 {
		for _, v := range structs.Risks {
			if strings.Contains(u, v) {
				return
			}
		}
	}
	AppendEndUrl(u)
	request, err := http.NewRequest("GET", u, nil)
	if err != nil {
		return
	}

	request.Header.Set("Accept-Encoding", "gzip") //使用gzip压缩传输数据让访问更快
	request.Header.Set("User-Agent", util.GetUserAgent(conf))
	request.Header.Set("Accept", "*/*")
	//增加header选项
	if conf.Cookie != "" {
		request.Header.Set("Cookie", conf.Cookie)
	}

	response, err := client.Do(request)
	if err != nil {
		return
	}
	defer response.Body.Close()

	res := ""
	//解压
	if response.Header.Get("Content-Encoding") == "gzip" {
		reader, err := gzip.NewReader(response.Body) // gzip解压缩
		if err != nil {
			return
		}
		defer reader.Close()
		con, err := io.ReadAll(reader)
		if err != nil {
			return
		}
		res = string(con)
	} else {
		//提取url用于拼接其他url或js
		dataBytes, err := io.ReadAll(response.Body)
		if err != nil {
			return
		}
		//字节数组 转换成 字符串
		res = string(dataBytes)
	}
	path := response.Request.URL.Path
	host := response.Request.URL.Host
	scheme := response.Request.URL.Scheme
	//处理base标签
	re := regexp.MustCompile("base.{1,5}href.{1,5}(http.+?//[^\\s]+?)[\"'‘“]")
	base := re.FindAllStringSubmatch(res, -1)
	if len(base) > 0 {
		host = regexp.MustCompile("http.*?//([^/]+)").FindAllStringSubmatch(base[0][1], -1)[0][1]
		scheme = regexp.MustCompile("(http.*?)://").FindAllStringSubmatch(base[0][1], -1)[0][1]
		paths := regexp.MustCompile("http.*?//.*?(/.*)").FindAllStringSubmatch(base[0][1], -1)
		if len(paths) > 0 {
			path = paths[0][1]
		} else {
			path = `/`
		}
	}
	is = false
	<-structs.Ch
	//提取js
	jsFind(res, host, scheme, path, u, num)
	//提取url
	urlFind(res, host, scheme, path, u, num)
}
