package url_crawler

import (
	"crypto/tls"
	"errors"
	"fmt"
	"micro-service/crawlerService/handler/api_analyzer/result"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/util"
	sql "micro-service/middleware/mysql/api_analyze"
	"micro-service/pkg/log"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
)

var client *http.Client
var conf *structs.FetchConfig

func load(c *structs.FetchConfig) error {
	conf = c
	if conf.TargetURL == "" {
		log.Errorf("api_analyzer - Task %d TargetURL不能为空", conf.TaskID)
		return errors.New("target url is empty")
	}
	u, err := url.Parse(conf.TargetURL)
	if err != nil {
		log.Errorf("api_analyzer - Task %d 解析URL失败: %s", conf.TaskID, err.Error())
		return err
	}
	conf.TargetURL = u.String()

	if conf.Thread != 50 {
		structs.Ch = make(chan int, conf.Thread)
		structs.Jsch = make(chan int, conf.Thread/10*3)
		structs.Urlch = make(chan int, conf.Thread/10*7)
	}

	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		Proxy:           http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   time.Second * 30,
			KeepAlive: time.Second * 30,
		}).DialContext,
		MaxIdleConns:          conf.Thread / 2,
		MaxIdleConnsPerHost:   conf.Thread + 10,
		IdleConnTimeout:       time.Second * 90,
		TLSHandshakeTimeout:   time.Second * 90,
		ExpectContinueTimeout: time.Second * 10,
	}

	if conf.Proxy != "" {
		tr.DisableKeepAlives = true
		proxyUrl, parseErr := url.Parse(conf.Proxy)
		if parseErr != nil {
			log.Warnf("api_analyzer - Task %d 解析代理URL失败: %s", conf.TaskID, parseErr.Error())
		} else {
			tr.Proxy = http.ProxyURL(proxyUrl)
		}
	}

	client = &http.Client{Timeout: time.Duration(conf.TimeOut) * time.Second,
		Transport: tr,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 10 {
				return fmt.Errorf("too many redirects")
			}
			if len(via) > 0 {
				if via[0] != nil && via[0].URL != nil {
					AddRedirect(via[0].URL.String())
				} else {
					AddRedirect(req.URL.String())
				}

			}
			return nil
		},
	}
	return nil
}

func Run(c *structs.FetchConfig) error {
	err := load(c)
	if err != nil {
		log.Errorf("api_analyzer - Task %d 初始化失败: %s", conf.TaskID, err)
		return err
	}
	Initialization()
	conf.TargetURL = util.GetProtocol(conf.TargetURL)
	start(conf.TargetURL)
	if len(result.ResultJs) == 0 && len(result.ResultUrl) == 0 {
		log.Warnf("api_analyzer - Task %d 未获取到数据", conf.TaskID)
	}
	// 检测&输出
	err = result.FormatAndDetect(conf)
	if err != nil {
		return err
	}
	return nil
}

func start(u string) {
	log.Infof("api_analyzer - Task %d 任务开始，Target URL: %s", conf.TaskID, u)
	err := sql.NewTaskModel().UpdateProcess(conf.TaskID, 10)
	if err != nil {
		log.Warnf("api_analyzer - Task %d 更新任务进度失败: %s", conf.TaskID, err)
	}
	structs.Wg.Add(1)
	structs.Ch <- 1
	go Spider(u, 1)
	structs.Wg.Wait()
	structs.Progress = 1
	result.ResultUrl = util.RemoveRepeatElement(result.ResultUrl, conf)
	result.ResultJs = util.RemoveRepeatElement(result.ResultJs, conf)
	log.Infof("api_analyze - Task %d 获取到数据: %d个JS，%d个URL", conf.TaskID, len(result.ResultJs), len(result.ResultUrl))
	err = sql.NewTaskModel().UpdateProcess(conf.TaskID, 40)
	if err != nil {
		log.Warnf("api_analyzer - Task %d 更新任务进度失败: %s", conf.TaskID, err)
	}
	if conf.StatusCode != "" {
		JsFuzz()
		structs.TotalJs = len(result.ResultJs)
		structs.TotalURL = len(result.ResultUrl)
		//验证JS状态
		for i, s := range result.ResultJs {
			structs.Wg.Add(1)
			structs.Jsch <- 1
			go JsState(s.URL, i, result.ResultJs[i].Source)
		}
		//验证URL状态
		for i, s := range result.ResultUrl {
			structs.Wg.Add(1)
			structs.Urlch <- 1
			go UrlState(s.URL, i)
		}
		structs.Wg.Wait()

		log.Infof("api_analyze - Task %d Url状态验证完成", conf.TaskID)
		time.Sleep(1 * time.Second)

		if conf.Fuzz != 0 {
			UrlFuzz()
			time.Sleep(1 * time.Second)
			log.Infof("api_analyze - Task %d 模糊测试完成", conf.TaskID)
		}
	}
	err = sql.NewTaskModel().UpdateProcess(conf.TaskID, 70)
	if err != nil {
		log.Warnf("api_analyzer - Task %d 更新任务进度失败: %s", err)
	}
	AddSource()
}

func AppendJs(ur string, urltjs string) int {
	structs.Lock.Lock()
	defer structs.Lock.Unlock()
	if len(result.ResultUrl)+len(result.ResultJs) >= conf.MaxUrlCount {
		return 1
	}
	_, err := url.Parse(ur)
	if err != nil {
		return 2
	}
	for _, eachItem := range result.ResultJs {
		if eachItem.URL == ur {
			return 0
		}
	}
	result.ResultJs = append(result.ResultJs, structs.Link{URL: ur})
	if strings.HasSuffix(urltjs, ".js") {
		result.Jsinurl[ur] = result.Jsinurl[urltjs]
	} else {
		re := regexp.MustCompile("[a-zA-z]+://[^\\s]*/|[a-zA-z]+://[^\\s]*")
		u := re.FindAllStringSubmatch(urltjs, -1)
		result.Jsinurl[ur] = u[0][0]
	}
	result.Jstourl[ur] = urltjs
	return 0

}

func AppendUrl(ur string, urlturl string) int {
	structs.Lock.Lock()
	defer structs.Lock.Unlock()
	if len(result.ResultUrl)+len(result.ResultJs) >= conf.MaxUrlCount {
		return 1
	}
	_, err := url.Parse(ur)
	if err != nil {
		return 2
	}
	for _, eachItem := range result.ResultUrl {
		if eachItem.URL == ur {
			return 0
		}
	}
	url.Parse(ur)
	result.ResultUrl = append(result.ResultUrl, structs.Link{URL: ur})
	result.Urltourl[ur] = urlturl
	return 0
}

func AppendEndUrl(url string) {
	structs.Lock.Lock()
	defer structs.Lock.Unlock()
	for _, eachItem := range result.EndUrl {
		if eachItem == url {
			return
		}
	}
	result.EndUrl = append(result.EndUrl, url)

}

func GetEndUrl(url string) bool {
	structs.Lock.Lock()
	defer structs.Lock.Unlock()
	for _, eachItem := range result.EndUrl {
		if eachItem == url {
			return true
		}
	}
	return false

}

func AddRedirect(url string) {
	structs.Lock.Lock()
	defer structs.Lock.Unlock()
	result.Redirect[url] = true
}

func AddSource() {
	for i := range result.ResultJs {
		result.ResultJs[i].Source = result.Jstourl[result.ResultJs[i].URL]
	}
	for i := range result.ResultUrl {
		result.ResultUrl[i].Source = result.Urltourl[result.ResultUrl[i].URL]
	}

}

func Initialization() {
	result.ResultJs = []structs.Link{}
	result.ResultUrl = []structs.Link{}
	result.Fuzzs = []structs.Link{}
	result.EndUrl = []string{}
	result.Jsinurl = make(map[string]string)
	result.Jstourl = make(map[string]string)
	result.Urltourl = make(map[string]string)
	result.Redirect = make(map[string]bool)
	structs.ProcessJS = 0
	structs.ProcessURL = 0
}
