package url_crawler

import (
	"micro-service/crawlerService/handler/api_analyzer/result"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"regexp"
	"strings"
)

// 分析内容中的js
func jsFind(cont, host, scheme, path, source string, num int) {
	var cata string
	care := regexp.MustCompile("/.*/{1}|/")
	catae := care.FindAllString(path, -1)
	if len(catae) == 0 {
		cata = "/"
	} else {
		cata = catae[0]
	}
	//js匹配正则
	host = scheme + "://" + host
	for _, re := range structs.JsFind {
		reg := regexp.MustCompile(re)
		jss := reg.FindAllStringSubmatch(cont, -1)
		//return
		jss = jsFilter(jss)
		//循环提取js放到结果中
		for _, js := range jss {
			if js[0] == "" {
				continue
			}
			if strings.HasPrefix(js[0], "https:") || strings.HasPrefix(js[0], "http:") {
				switch AppendJs(js[0], source) {
				case 0:
					if num <= structs.JsSteps && (conf.Mode == 2 || conf.Mode == 3) {
						structs.Wg.Add(1)
						structs.Ch <- 1
						go Spider(js[0], num+1)
					}
				case 1:
					return
				case 2:
					continue
				}

			} else if strings.HasPrefix(js[0], "//") {
				switch AppendJs(scheme+":"+js[0], source) {
				case 0:
					if num <= structs.JsSteps && (conf.Mode == 2 || conf.Mode == 3) {
						structs.Wg.Add(1)
						structs.Ch <- 1
						go Spider(scheme+":"+js[0], num+1)
					}
				case 1:
					return
				case 2:
					continue
				}

			} else if strings.HasPrefix(js[0], "/") {
				switch AppendJs(host+js[0], source) {
				case 0:
					if num <= structs.JsSteps && (conf.Mode == 2 || conf.Mode == 3) {
						structs.Wg.Add(1)
						structs.Ch <- 1
						go Spider(host+js[0], num+1)
					}
				case 1:
					return
				case 2:
					continue
				}

			} else {
				switch AppendJs(host+cata+js[0], source) {
				case 0:
					if num <= structs.JsSteps && (conf.Mode == 2 || conf.Mode == 3) {
						structs.Wg.Add(1)
						structs.Ch <- 1
						go Spider(host+cata+js[0], num+1)
					}
				case 1:
					return
				case 2:
					continue
				}

			}
		}

	}

}

// 分析内容中的url
func urlFind(cont, host, scheme, path, source string, num int) {
	var cata string
	care := regexp.MustCompile("/.*/{1}|/")
	catae := care.FindAllString(path, -1)
	if len(catae) == 0 {
		cata = "/"
	} else {
		cata = catae[0]
	}
	host = scheme + "://" + host

	//url匹配正则

	for _, re := range structs.UrlFind {
		reg := regexp.MustCompile(re)
		urls := reg.FindAllStringSubmatch(cont, -1)
		urls = urlFilter(urls)

		//循环提取url放到结果中
		for _, url := range urls {
			if url[0] == "" {
				continue
			}
			if strings.HasPrefix(url[0], "https:") || strings.HasPrefix(url[0], "http:") {
				switch AppendUrl(url[0], source) {
				case 0:
					if num <= structs.UrlSteps && (conf.Mode == 2 || conf.Mode == 3) {
						structs.Wg.Add(1)
						structs.Ch <- 1
						go Spider(url[0], num+1)
					}
				case 1:
					return
				case 2:
					continue
				}
			} else if strings.HasPrefix(url[0], "//") {
				switch AppendUrl(scheme+":"+url[0], source) {
				case 0:
					if num <= structs.UrlSteps && (conf.Mode == 2 || conf.Mode == 3) {
						structs.Wg.Add(1)
						structs.Ch <- 1
						go Spider(scheme+":"+url[0], num+1)
					}
				case 1:
					return
				case 2:
					continue
				}

			} else if strings.HasPrefix(url[0], "/") {
				urlz := ""
				if conf.BaseURL != "" {
					urlz = conf.BaseURL + url[0]
				} else {
					urlz = host + url[0]
				}
				switch AppendUrl(urlz, source) {
				case 0:
					if num <= structs.UrlSteps && (conf.Mode == 2 || conf.Mode == 3) {
						structs.Wg.Add(1)
						structs.Ch <- 1
						go Spider(urlz, num+1)
					}
				case 1:
					return
				case 2:
					continue
				}
			} else if !strings.HasSuffix(source, ".js") {
				urlz := ""
				if conf.BaseURL != "" {
					if strings.HasSuffix(conf.BaseURL, "/") {
						urlz = conf.BaseURL + url[0]
					} else {
						urlz = conf.BaseURL + "/" + url[0]
					}
				} else {
					urlz = host + cata + url[0]
				}
				switch AppendUrl(urlz, source) {
				case 0:
					if num <= structs.UrlSteps && (conf.Mode == 2 || conf.Mode == 3) {
						structs.Wg.Add(1)
						structs.Ch <- 1
						go Spider(urlz, num+1)
					}
				case 1:
					return
				case 2:
					continue
				}

			} else if strings.HasSuffix(source, ".js") {
				urlz := ""
				if conf.BaseURL != "" {
					if strings.HasSuffix(conf.BaseURL, "/") {
						urlz = conf.BaseURL + url[0]
					} else {
						urlz = conf.BaseURL + "/" + url[0]
					}
				} else {
					structs.Lock.Lock()
					su := result.Jsinurl[source]
					structs.Lock.Unlock()
					if strings.HasSuffix(su, "/") {
						urlz = su + url[0]
					} else {
						urlz = su + "/" + url[0]
					}
				}
				switch AppendUrl(urlz, source) {
				case 0:
					if num <= structs.UrlSteps && (conf.Mode == 2 || conf.Mode == 3) {
						structs.Wg.Add(1)
						structs.Ch <- 1
						go Spider(urlz, num+1)
					}
				case 1:
					return
				case 2:
					continue
				}

			}
		}
	}
}
