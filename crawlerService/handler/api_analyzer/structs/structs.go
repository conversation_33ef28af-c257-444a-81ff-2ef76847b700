package structs

import (
	"micro-service/pkg/log"
	"sync"
)

// Link API/JS信息
type Link struct {
	URL      string   // 完整URL
	API      string   // API
	Status   string   // 状态码
	Title    string   // 标题
	Body     LinkBody // 响应体
	Source   string   // 来源
	Redirect string   // 重定向链接
}

// LinkBody API请求体信息
type LinkBody struct {
	Code int    // 状态码
	Size int    // 响应体大小
	Type string // 响应体类型
	Data string // 响应体数据
}

// FetchConfig 抓取配置
type FetchConfig struct {
	TaskID      uint64 // 任务ID
	Mode        int    // 模式 1 单层 2 深度 3 安全深入（屏蔽delete，remove等敏感路由）
	StatusCode  string // 显示指定状态码  all 显示所有状态码
	TargetURL   string // 目标URL 必须
	Domain      string // 筛选指定域名
	Cookie      string // 设置Cookie
	UserAgent   string // 设置UserAgent
	BaseURL     string // BaseURL 拼接在URL前
	Proxy       string // 设置代理
	Thread      int    // 线程数 50
	TimeOut     int    // 超时时间（秒） 5
	MaxUrlCount int    // 最大抓取链接数 99999
	Fuzz        int    // 开启Fuzz 0/1/2
}

// DetectedWarningInfo 检测到的风险信息
type DetectedWarningInfo struct {
	Url     string   // 完整URL
	Api     string   // API
	Tag     string   // 标签
	Desc    string   // 描述
	Code    int      // 状态码
	Size    int      // 响应体大小
	Type    string   // 响应体类型
	Count   int      // 匹配次数
	Matches []string // 匹配字段
}

var Progress = 1
var FuzzNum int
var TotalURL, ProcessURL, TotalJs, ProcessJS int
var ul, jl sync.Mutex

func PrintUrlProgress(taskID uint64) {
	ul.Lock()
	ProcessURL++
	ul.Unlock()
	if ProcessURL%500 == 0 && TotalURL > 0 {
		log.Infof("Task %d - URL验证进度：%0.2f", taskID, float64(ProcessURL)/float64(TotalURL)*100)
	}
}

func PrintJsProgress(taskID uint64) {
	jl.Lock()
	ProcessJS++
	jl.Unlock()
	if ProcessJS%500 == 0 && TotalJs > 0 {
		log.Infof("Task %d - JS验证进度：%0.2f", taskID, float64(ProcessJS)/float64(TotalJs)*100)
	}
}

// 正则表达式
var (
	Risks      = []string{"remove", "delete", "insert", "update", "logout"}
	JsFuzzPath = []string{
		"login.js",
		"app.js",
		"main.js",
		"structs.js",
		"admin.js",
		"info.js",
		"open.js",
		"user.js",
		"input.js",
		"list.js",
		"upload.js",
	}
	JsFind = []string{
		"(https{0,1}:[-a-zA-Z0-9（）@:%_\\+.~#?&//=]{2,250}?[-a-zA-Z0-9（）@:%_\\+.~#?&//=]{3}[.]js)",
		"[\"'‘“`]\\s{0,6}(/{0,1}[-a-zA-Z0-9（）@:%_\\+.~#?&//=]{2,250}?[-a-zA-Z0-9（）@:%_\\+.~#?&//=]{3}[.]js)",
		"=\\s{0,6}[\",',’,”]{0,1}\\s{0,6}(/{0,1}[-a-zA-Z0-9（）@:%_\\+.~#?&//=]{2,250}?[-a-zA-Z0-9（）@:%_\\+.~#?&//=]{3}[.]js)",
	}
	UrlFind = []string{
		"[\"'‘“`]\\s{0,6}(https{0,1}:[-a-zA-Z0-9()@:%_\\+.~#?&//={}]{2,250}?)\\s{0,6}[\"'‘“`]",
		"=\\s{0,6}(https{0,1}:[-a-zA-Z0-9()@:%_\\+.~#?&//={}]{2,250})",
		"[\"'‘“`]\\s{0,6}([#,.]{0,2}/[-a-zA-Z0-9()@:%_\\+.~#?&//={}]{2,250}?)\\s{0,6}[\"'‘“`]",
		"\"([-a-zA-Z0-9()@:%_\\+.~#?&//={}]+?[/]{1}[-a-zA-Z0-9()@:%_\\+.~#?&//={}]+?)\"",
		"href\\s{0,6}=\\s{0,6}[\"'‘“`]{0,1}\\s{0,6}([-a-zA-Z0-9()@:%_\\+.~#?&//={}]{2,250})|action\\s{0,6}=\\s{0,6}[\"'‘“`]{0,1}\\s{0,6}([-a-zA-Z0-9()@:%_\\+.~#?&//={}]{2,250})",
	}
	JsFiler = []string{
		"www\\.w3\\.org",
		"example\\.com",
	}
	UrlFiler = []string{
		"\\.js\\?|\\.css\\?|\\.jpeg\\?|\\.jpg\\?|\\.png\\?|.gif\\?|www\\.w3\\.org|example\\.com|\\<|\\>|\\{|\\}|\\[|\\]|\\||\\^|;|/js/|\\.src|\\.replace|\\.url|\\.att|\\.href|location\\.href|javascript:|location:|application/x-www-form-urlencoded|\\.createObject|:location|\\.path|\\*#__PURE__\\*|\\*\\$0\\*|\\n",
		".*\\.js$|.*\\.css$|.*\\.scss$|.*,$|.*\\.jpeg$|.*\\.jpg$|.*\\.png$|.*\\.gif$|.*\\.ico$|.*\\.svg$|.*\\.vue$|.*\\.ts$",
	}
)

var (
	UrlSteps = 1
	JsSteps  = 3
)

var (
	Lock  sync.Mutex
	Wg    sync.WaitGroup
	Mux   sync.Mutex
	Ch    = make(chan int, 50)
	Jsch  = make(chan int, 50/10*3)
	Urlch = make(chan int, 50/10*7)
)
