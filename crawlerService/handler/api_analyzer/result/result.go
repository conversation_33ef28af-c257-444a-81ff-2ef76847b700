package result

import (
	_ "embed"
	"errors"
	"gorm.io/gorm"
	detect2 "micro-service/crawlerService/handler/api_analyzer/detect"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/util"
	"micro-service/middleware/mysql"
	sql "micro-service/middleware/mysql/api_analyze"
	"micro-service/pkg/log"
	"regexp"
)

var (
	ResultJs  []structs.Link
	ResultUrl []structs.Link
	Fuzzs     []structs.Link

	EndUrl   []string
	Jsinurl  map[string]string
	Jstourl  map[string]string
	Urltourl map[string]string
	Redirect map[string]bool
)

const (
	AnalyzeRiskTypeSuspiciousFile = iota + 1
	AnalyzeRiskTypeSuspiciousApi
	AnalyzeRiskTypeWonderfulInfo
	AnalyzeRiskTypeApisPossibleConstruct
)

/*
	表结构：
		任务表
		｜TaskID｜UserID｜Url｜Domain｜UserAgent｜Fuzz｜TaskStatus｜TaskProcess｜JSCount｜UrlCount｜
		JS/Url列表存
		｜TaskID｜UrlList（LongText）｜JsList（LongText）｜ExternalUrl（LongText）｜
		风险存储表
		｜TaskID｜RiskType｜Url｜API｜Tag｜Describe｜Status｜Size｜ResponseType｜Count｜Matches｜
*/

// FormatAndDetect 格式化结果并检测风险
func FormatAndDetect(conf *structs.FetchConfig) error {
	log.Infof("api_analyzer - Task %d 正在格式化输出数据", conf.TaskID)
	//获取域名
	var host string
	re := regexp.MustCompile("([a-z0-9\\-]+\\.)*([a-z0-9\\-]+\\.[a-z0-9\\-]+)(:[0-9]+)?")
	hosts := re.FindAllString(conf.TargetURL, 1)
	if len(hosts) == 0 {
		host = conf.TargetURL
	} else {
		host = hosts[0]
	}
	// 加入模糊测试结果
	if conf.Fuzz > 0 {
		ResultUrl = append(ResultUrl, Fuzzs...)
	}
	// 获取任务数据
	task, err := sql.NewTaskModel().First(mysql.WithWhere("id = ?", conf.TaskID))
	if err != nil {
		log.Errorf("api_analyzer - Task %d 获取任务信息失败: %s", conf.TaskID, err)
		return err
	}
	task.UrlCount = len(ResultUrl)
	// 按API去重
	ResultUrl = util.FormatResult(ResultUrl)
	ResultJs = util.FormatResult(ResultJs)
	// 按抓取的域名优先排序
	if conf.StatusCode != "" {
		ResultUrl = util.SelectSort(ResultUrl)
		ResultJs = util.SelectSort(ResultJs)
	}
	ResultUrlHost, ResultUrlOther := util.UrlDispose(ResultUrl, host, util.GetHost(conf.TargetURL), conf)
	// 检测
	log.Infof("api_analyzer - Task %d 正在执行可疑文件检测", conf.TaskID)
	res := detect2.GetSuspiciousFileFromFuzzResult(ResultUrlHost)
	log.Infof("api_analyzer - Task %d 正在执行可疑API检测", conf.TaskID)
	ant := detect2.GetApiWithoutTokenAnchor(ResultUrlHost)
	res2 := detect2.GetSuspiciousApiFromFuzzResult(ant, ResultUrlHost)
	log.Infof("api_analyzer - Task %d 正在执行敏感信息检测", conf.TaskID)
	inf := detect2.GetWonderfulRespFromFuzzResult(ResultUrlHost, conf.TaskID)
	log.Infof("api_analyzer - Task %d 正在执行可构造包检测", conf.TaskID)
	con := detect2.GetApisPossibleConstructResult(ResultUrlHost)
	totalRisks := len(res) + len(res2) + len(inf) + len(con)
	log.Infof("api_analyze - Task %d 风险检测完成，共 %d 个API，发现 %d 个风险", task.Id, len(ResultUrl), totalRisks)
	// 存储结果
	task.Status = 2
	task.ApiCount = len(ResultUrl)
	task.JsCount = len(ResultJs)
	task.Progress = 100
	_, err = sql.NewResultModel().Upsert(util.ConvertResult(res, int(task.Id), AnalyzeRiskTypeSuspiciousFile))
	if err != nil {
		log.Errorf("api_analyzer - Task %d 存储结果失败: %s", task.Id, err)
		return err
	}
	_, err = sql.NewResultModel().Upsert(util.ConvertResult(res2, int(task.Id), AnalyzeRiskTypeSuspiciousApi))
	if err != nil {
		log.Errorf("api_analyzer - Task %d 存储结果失败: %s", task.Id, err)
		return err
	}
	_, err = sql.NewResultModel().Upsert(util.ConvertResult(inf, int(task.Id), AnalyzeRiskTypeWonderfulInfo))
	if err != nil {
		log.Errorf("api_analyzer - Task %d 存储结果失败: %s", task.Id, err)
		return err
	}
	_, err = sql.NewResultModel().Upsert(util.ConvertResult(con, int(task.Id), AnalyzeRiskTypeApisPossibleConstruct))
	if err != nil {
		log.Errorf("api_analyzer - Task %d 存储结果失败: %s", task.Id, err)
		return err
	}
	detail := sql.Detail{
		TaskId:      int(task.Id),
		ApiList:     util.ApiJoin(ResultUrl),
		JsList:      util.UrlJoin(ResultJs),
		ExternalUrl: util.ExternalFormat(ResultUrlOther),
	}
	err = sql.NewDetailModel().Upsert(detail)
	if err != nil {
		log.Errorf("api_analyzer - Task %d 存储详情失败: %s", task.Id, err)
		return err
	}
	err = sql.NewTaskModel().Update(task)
	if err != nil {
		log.Errorf("api_analyzer - Task %d 更新任务状态失败: %s", task.Id, err)
		return err
	}
	err = sql.NewUserTaskModel().UpdateProcess(task.Id)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Errorf("api_analyzer - Task %d 更新任务进度失败: %s", task.Id, err)
		return err
	}
	err = sql.NewUserTaskModel().AddRisksCount(task.Id, int64(totalRisks))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Errorf("api_analyzer - Task %d 更新风险数量失败: %s", task.Id, err)
		return err
	}
	return nil
}
