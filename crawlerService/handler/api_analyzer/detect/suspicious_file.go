package detect

import (
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"sort"
	"strings"
)

// 全局变量定义
var (
	// 状态码黑名单
	blackStatusCode = map[int]bool{502: true, 500: true, 403: true, 401: true, 404: true}
	juicyFileExtSet = map[string]bool{"xls": true, "xlsx": true, "doc": true, "docx": true, "txt": true, "xml": true, "json": true}
	zipFileExtSet   = map[string]bool{"zip": true, "rar": true, "7z": true, "tar.gz": true, "tar": true, "gz": true, "xz": true, "jar": true, "tgz": true}
)

// GetSuspiciousFileFromFuzzResult 可疑文件检测
func GetSuspiciousFileFromFuzzResult(results []structs.Link) []structs.DetectedWarningInfo {
	var suspiciousFiles []structs.DetectedWarningInfo
	// 过滤无效状态码
	filtered := make([]structs.Link, 0, len(results))
	for _, res := range results {
		if !blackStatusCode[res.Body.Code] {
			filtered = append(filtered, res)
		}
	}
	// 处理过滤后的结果
	for _, res := range filtered {
		if file := getSuspiciousFileFromResp(res); file != nil {
			suspiciousFiles = append(suspiciousFiles, *file)
		}
	}
	// 按大小降序排序
	sort.Slice(suspiciousFiles, func(i, j int) bool {
		return suspiciousFiles[i].Size > suspiciousFiles[j].Size
	})
	return suspiciousFiles
}

// 辅助检测函数
func getSuspiciousFileFromResp(res structs.Link) *structs.DetectedWarningInfo {
	parts := strings.Split(res.API, ".")
	if len(parts) < 2 {
		return nil
	}
	ext := parts[len(parts)-1]
	// 检查文件扩展名
	if juicyFileExtSet[ext] || zipFileExtSet[ext] {
		return &structs.DetectedWarningInfo{
			Url:   res.URL,
			Api:   res.API,
			Tag:   ext,
			Desc:  ext,
			Code:  res.Body.Code,
			Size:  res.Body.Size,
			Type:  res.Body.Type,
			Count: 1,
		}
	}
	return nil
}
