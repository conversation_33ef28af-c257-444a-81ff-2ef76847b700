package detect

import (
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/util"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
)

// 全局变量声明
var (
	missingRegex = []struct {
		Tag  string
		Desc string
		Re   *regexp.Regexp
	}{
		{"missing", "参数不能为空", regexp.MustCompile(`参数.+不能为空`)},
		{"missing", "不支持的方法", regexp.MustCompile(`(不支持\w*?(请求)|(方式)|(方法))`)},
		{"missing", "缺少参数", regexp.MustCompile(`缺少参数`)},
		{"missing", "缺少参数", regexp.MustCompile(`is missing`)},
		{"stacktrace", "错误堆栈", regexp.MustCompile(`(?i)(stack\s+trace|exception\s+at|.go:\d+|panic:|sql syntax)`)},
		{"debug", "调试信息", regexp.MustCompile(`(?i)(debug|trace_mode|verbose_log|diagnostic|profiling)`)},
	}
	zipFileExtList = []string{"zip", "rar", "7z", "tar.gz", "tar", "gz", "xz", "jar", "tgz"}
)

// GetApisPossibleConstructResult 检查可构造包
func GetApisPossibleConstructResult(resultList []structs.Link) []structs.DetectedWarningInfo {
	var filtered []structs.Link
	// 过滤文件扩展名
	for _, item := range resultList {
		ext := filepath.Ext(strings.Trim(item.URL, "."))
		if !util.Contains(append(zipFileExtList, officeFileExtList...), ext) {
			filtered = append(filtered, item)
		}
	}

	var constructs []structs.DetectedWarningInfo
	for _, info := range filtered {
		if info.Body.Code == 405 {
			constructs = append(constructs, structs.DetectedWarningInfo{
				Url:   info.URL,
				Api:   info.API,
				Tag:   "methodchange",
				Desc:  "切换method",
				Code:  info.Body.Code,
				Size:  info.Body.Size,
				Type:  info.Body.Type,
				Count: 1,
			})
			continue
		}

		for _, regex := range missingRegex {
			matches := regex.Re.FindAllString(strings.ToLower(info.Body.Data), -1)
			if len(matches) > 0 {
				constructs = append(constructs, structs.DetectedWarningInfo{
					Url:   info.URL,
					Api:   info.API,
					Tag:   regex.Tag,
					Desc:  regex.Desc,
					Code:  info.Body.Code,
					Size:  info.Body.Size,
					Type:  info.Body.Type,
					Count: len(matches),
				})
			}
		}
	}

	// 按size排序
	sort.Slice(constructs, func(i, j int) bool {
		return constructs[i].Size > constructs[j].Size
	})

	return constructs
}
