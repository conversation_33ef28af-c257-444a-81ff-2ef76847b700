package detect

import (
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"sort"
	"strings"
)

var (
	juicyApiListKeyWords = []string{"upload", "download", "config", "conf", "import", "export", "query", "list", "customer", "register", "reg", "info", "reset", "password", "pass", "pwd", "credential", "actuator", "refresh", "druid", "metrics", "httptrace", "swagger-ui", "redis", "user", "sys", "system", "adm", "admin", "datasource", "database", "edit", "manage", "forget", "v1", "v2", "docs"}
	juicyFileExtList     = []string{"xls", "xlsx", "doc", "docx", "txt", "xml", "json"}
)

// GetApiWithoutTokenAnchor 二次锚点检测
func GetApiWithoutTokenAnchor(fuzzRespList []structs.Link) map[string]int {
	// 复制并过滤列表
	tmp := make([]structs.Link, 0, len(fuzzRespList))
	for _, res := range fuzzRespList {
		if !blackStatusCode[res.Body.Code] && res.Body.Size != 0 {
			tmp = append(tmp, res)
		}
	}
	// 统计出现频率
	sizeCount := make(map[int]int)
	for _, res := range tmp {
		sizeCount[res.Body.Size]++
	}
	// 找出最大值
	maxSize, maxCount := -1, 0
	for size, count := range sizeCount {
		if count > maxCount {
			maxSize = size
			maxCount = count
		}
	}
	// 条件判断
	if maxSize != -1 {
		if float64(maxCount) >= float64(len(tmp))/2 && maxCount >= 8 {
			return map[string]int{"small": maxSize, "big": maxSize}
		}
	}
	return nil
}

// GetSuspiciousApiFromFuzzResult 可疑API检测
func GetSuspiciousApiFromFuzzResult(anchors map[string]int, fuzzResultList []structs.Link) []structs.DetectedWarningInfo {
	var apiList []structs.DetectedWarningInfo
	juicyKeywords := append(append([]string{}, juicyApiListKeyWords...), juicyFileExtList...)

	// 深拷贝处理
	tmp := make([]structs.Link, len(fuzzResultList))
	copy(tmp, fuzzResultList)

	// 过滤处理
	var filtered []structs.Link
	for _, x := range tmp {
		if x.Body.Size != 0 && !blackStatusCode[x.Body.Code] && x.Body.Type != "html" {
			// 关键字匹配
			for _, kw := range juicyKeywords {
				if strings.Contains(strings.ToLower(x.API), kw) {
					filtered = append(filtered, x)
					break
				}
			}
		}
	}
	// 锚点过滤
	if anchors != nil {
		var finalFiltered []structs.Link
		for _, x := range filtered {
			if x.Body.Size != anchors["small"] {
				finalFiltered = append(finalFiltered, x)
			}
		}
		filtered = finalFiltered
	}
	// 构建结果
	for _, resp := range filtered {
		var info structs.DetectedWarningInfo
		var tag strings.Builder
		count := 0
		for _, key := range juicyKeywords {
			if strings.Contains(strings.ToLower(resp.API), key) {
				tag.WriteString(key + ",")
				count++
			}
		}
		if tag.Len() > 0 {
			infoTag := strings.TrimSuffix(tag.String(), ",")
			info.Url = resp.URL
			info.Api = resp.API
			info.Tag = infoTag
			info.Desc = infoTag
			info.Code = resp.Body.Code
			info.Size = resp.Body.Size
			info.Type = resp.Body.Type
			info.Count = count
			apiList = append(apiList, info)
		}
	}
	// 排序
	sort.Slice(apiList, func(i, j int) bool {
		return apiList[i].Size > apiList[j].Size
	})
	return apiList
}
