package detect

import (
	"fmt"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/util"
	"micro-service/pkg/log"
	"regexp"
	"sort"
	"strings"
)

var (
	officeFileExtList  = []string{"doc", "docx", "docm", "dot", "dotx", "dotm", "docb", "xls", "xlsx", "xlsm", "xlsb", "xltx", "xltm", "xlam", "ppt", "pptx", "pptm", "pot", "potx", "potm", "ppa", "accdb", "accde", "accdt", "accdr", "pst", "ost", "pub", "vsd", "vsdx", "vss", "vssx", "vst", "vstx", "vsw", "vstm"}
	sensitiveInfoRegex = []struct {
		Regex *regexp.Regexp
		Tag   string
		Desc  string
	}{
		{regexp.MustCompile(`[^0-9]((\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)))[^0-9]`), "idcard", "身份证"},
		{regexp.MustCompile(`[^0-9A-Za-z](1(3([0-35-9]\d|4[1-8])|4[14-9]\d|5([\d]\d|7[1-79])|66\d|7[2-35-8]\d|8\d{2}|9[89]\d)\d{7})[^0-9A-Za-z]`), "phone", "手机号"},
		{regexp.MustCompile(`ey[A-Za-z0-9_-]{10,}\.[A-Za-z0-9._-]{10,}|ey[A-Za-z0-9_\/+-]{10,}\.[A-Za-z0-9._\/+-]{10,}`), "jwt", "JWT"},
		{regexp.MustCompile(`([A|a]ccess[K|k]ey[I|i][d|D]|[A|a]ccess[K|k]ey[S|s]ecret)`), "accessKey", "Access Key"},
		{regexp.MustCompile(`(((access)(|-|_)(key)(|-|_)(id|secret))|(LTAI[a-z0-9]{12,20}))`), "cloudKey", "Cloud Key"},
		{regexp.MustCompile(`([A|a]pi[K|k]ey[I|i][d|D]|[A|a]pi[K|k]ey[S|s]ecret)`), "apiKey", "Api Key"},
		{regexp.MustCompile(`["\']?((p|P)assword|PASSWORD|(c|C)redential|CREDENTIAL)["\']?[^\S\r\n]*[=:][^\S\r\n]*["\']?[\w-]+["\']?|["\']?[\w_-]*?password[\w_-]*?["\']?[^\S\r\n]*[=:][^\S\r\n]*["\']?[\w-]+["\']?`), "password", "password"},
		{regexp.MustCompile(`([a-zA-Z0-9][_\w\.]*[a-zA-Z0-9]+@[a-zA-Z0-9][-\w\.]*[a-zA-Z0-9]\.[a-zA-Z]{2,})`), "email", "邮箱"},
		{regexp.MustCompile(`[^0-9]((127\.0\.0\.1)|(localhost)|(10\.\d{1,3}\.\d{1,3}\.\d{1,3})|(172\.((1[6-9])|(2\d)|(3[01]))\.\d{1,3}\.\d{1,3})|(192\.168\.\d{1,3}\.\d{1,3}))`), "internalIP", "内网IP"},
		{regexp.MustCompile(`(minioadmin/minioadmin)|(=\s?\'?"?minioadmin)`), "minioPass", "minio账号密码(minioadmin)"},
		{regexp.MustCompile(`(^([a-fA-F0-9]{2}(:[a-fA-F0-9]{2}){5})|[^a-zA-Z0-9]([a-fA-F0-9]{2}(:[a-fA-F0-9]{2}){5}))`), "MAC Address", "MAC地址"},
		{regexp.MustCompile(`["\']?((u|U)ser(n|N)ame|USERNAME)["\']?[^\S\r\n]*[=:][^\S\r\n]*["\']?[\w-]+["\']?|["\']?[\w_-]*?username[\w_-]*?["\']?[^\S\r\n]*[=:][^\S\r\n]*["\']?[\w-]+["\']?`), "username", "username"},
		{regexp.MustCompile(`(jdbc:[a-z:]+://[a-z0-9\.\-_:;=/@?,&]+)`), "jdbc", "JDBC 连接字符串"},
		{regexp.MustCompile(`((basic [a-z0-9=:_\+\/-]{5,100})|(bearer [a-z0-9_.=:_\+\/-]{5,100}))`), "authHeader", "认证头信息"},
	}
)

// GetWonderfulRespFromFuzzResult 敏感信息发现
func GetWonderfulRespFromFuzzResult(fuzzResultList []structs.Link, taskID uint64) []structs.DetectedWarningInfo {
	var infoList []structs.DetectedWarningInfo
	seen := make(map[string]bool)

	for i, resp := range fuzzResultList {
		results := GetWonderfulInfoFromSingleResult(resp)
		for _, res := range results {
			// 去重逻辑（针对phone类型）
			if res.Tag == "phone" {
				key := fmt.Sprintf("%s|%d", res.Tag, res.Size)
				if seen[key] {
					continue
				}
				seen[key] = true
			}
			infoList = append(infoList, res)
		}
		if i%500 == 0 {
			log.Infof("api_analyzer - Task %d 正在检测敏感信息，当前进度：%.2f", taskID, float64(i)/float64(len(fuzzResultList))*100)
		}
	}

	// 按Size降序排序
	sort.Slice(infoList, func(i, j int) bool {
		return infoList[i].Size > infoList[j].Size
	})

	return infoList
}

// GetWonderfulInfoFromSingleResult 辅助处理函数
func GetWonderfulInfoFromSingleResult(resp structs.Link) []structs.DetectedWarningInfo {
	ext := strings.Split(resp.URL, ".")
	if len(ext) > 0 {
		fileExt := strings.ToLower(ext[len(ext)-1])
		// 排除压缩文件和办公文档
		if util.Contains(zipFileExtList, fileExt) || util.Contains(officeFileExtList, fileExt) {
			return nil
		}
	}

	var tmp []structs.DetectedWarningInfo
	body := resp.Body.Data
	for _, regex := range sensitiveInfoRegex {
		matches := regex.Regex.FindAllString(body, -1)
		if len(matches) > 0 {
			// 邮箱过滤器（适配RE2）
			if regex.Tag == "email" {
				var filterList []string
				for _, match := range matches {
					parts := strings.Split(match, "@")
					if len(parts) < 2 {
						continue
					}
					domainParts := strings.Split(parts[1], ".")
					tld := strings.ToLower(domainParts[len(domainParts)-1])
					// 过滤不需要的后缀
					if !util.Contains([]string{"js", "css", "jpg", "jpeg", "png", "ico"}, tld) {
						filterList = append(filterList, match)
					}
				}
				matches = filterList
				if len(matches) == 0 {
					continue
				}
			}
			info := structs.DetectedWarningInfo{
				Url:     resp.URL,
				Api:     resp.API,
				Tag:     regex.Tag,
				Desc:    regex.Desc,
				Code:    resp.Body.Code,
				Size:    resp.Body.Size,
				Type:    resp.Body.Type,
				Count:   len(matches),
				Matches: util.Unique(matches),
			}
			tmp = append(tmp, info)
		}
	}
	return tmp
}
