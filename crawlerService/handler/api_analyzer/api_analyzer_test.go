package api_analyzer

import (
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/url_crawler"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"testing"
)

func Init() {
	cfg.InitLoadCfg()
	log.Init()
}

func TestAnalyzer(t *testing.T) {
	Init()
	configs := []structs.FetchConfig{
		{
			Mode:        2,
			StatusCode:  "all",
			TargetURL:   "https://10.10.10.189",
			Domain:      "",
			Cookie:      "",
			UserAgent:   "",
			BaseURL:     "",
			Proxy:       "",
			Thread:      50,
			TimeOut:     5,
			MaxUrlCount: 99999,
			Fuzz:        1,
		},
	}
	for _, config := range configs {
		url_crawler.Run(&config)
	}
}
