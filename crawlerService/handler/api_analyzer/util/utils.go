package util

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	sql "micro-service/middleware/mysql/api_analyze"
	"micro-service/pkg/log"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
)

// SelectSort 对结果进行状态码排序
func SelectSort(arr []structs.Link) []structs.Link {
	length := len(arr)
	var sort []int
	for _, v := range arr {
		if v.URL == "" || v.Body.Size == 0 || v.Status == "timeout" {
			sort = append(sort, 999)
		} else {
			in, _ := strconv.Atoi(v.Status)
			sort = append(sort, in)
		}
	}
	if length <= 1 {
		return arr
	} else {
		for i := 0; i < length-1; i++ { //只剩一个元素不需要索引
			min := i                          //标记索引
			for j := i + 1; j < length; j++ { //每次选出一个极小值
				if sort[min] > sort[j] {
					min = j //保存极小值的索引
				}
			}
			if i != min {
				sort[i], sort[min] = sort[min], sort[i] //数据交换
				arr[i], arr[min] = arr[min], arr[i]     //数据交换
			}
		}
		return arr
	}
}

// UrlDispose 对结果进行URL排序
func UrlDispose(arr []structs.Link, url, host string, conf *structs.FetchConfig) ([]structs.Link, []structs.Link) {
	var urls []structs.Link
	var urlts []structs.Link
	var other []structs.Link
	for _, v := range arr {
		if strings.Contains(v.URL, url) {
			urls = append(urls, v)
		} else {
			if host != "" && regexp.MustCompile(host).MatchString(v.URL) {
				urlts = append(urlts, v)
			} else {
				other = append(other, v)
			}
		}
	}

	for _, v := range urlts {
		urls = append(urls, v)
	}

	return RemoveRepeatElement(urls, conf), RemoveRepeatElement(other, conf)
}

// GetProtocol 判断http协议
func GetProtocol(domain string) string {
	if strings.HasPrefix(domain, "http") {
		return domain
	}

	response, err := http.Get("https://" + domain)
	if err == nil {
		return "https://" + domain
	}
	response, err = http.Get("http://" + domain)
	if err == nil {
		return "http://" + domain
	}
	defer func(resp *http.Response) {
		if resp != nil {
			_ = resp.Body.Close()
		}
	}(response)
	if response != nil && response.TLS == nil {
		return "http://" + domain
	}
	return ""
}

// GetHost 提取顶级域名
func GetHost(u string) string {
	re := regexp.MustCompile("([a-z0-9\\-]+\\.)*([a-z0-9\\-]+\\.[a-z0-9\\-]+)(:[0-9]+)?")
	var host string
	hosts := re.FindAllString(u, 1)
	if len(hosts) == 0 {
		host = u
	} else {
		host = hosts[0]
	}
	re2 := regexp.MustCompile("[^.]*?\\.[^.,^:]*")
	host2 := re2.FindAllString(host, -1)
	re3 := regexp.MustCompile("(([01]?[0-9]{1,3}|2[0-4][0-9]|25[0-5])\\.){3}([01]?[0-9]{1,3}|2[0-4][0-9]|25[0-5])")
	hostIp := re3.FindAllString(u, -1)
	if len(hostIp) == 0 {
		if len(host2) == 1 {
			host = host2[0]
		} else {
			re3 := regexp.MustCompile("\\.[^.]*?\\.[^.,^:]*")
			var ho string
			hos := re3.FindAllString(host, -1)

			if len(hos) == 0 {
				ho = u
			} else {
				ho = hos[len(hos)-1]
			}
			host = strings.Replace(ho, ".", "", 1)
		}
	} else {
		return hostIp[0]
	}
	return host
}

// RemoveRepeatElement 去重+去除错误url
func RemoveRepeatElement(list []structs.Link, conf *structs.FetchConfig) []structs.Link {
	// 创建一个临时map用来存储数组元素
	temp := make(map[string]bool)
	var list2 []structs.Link
	index := 0
	for _, v := range list {

		//处理-d参数
		if conf.Domain != "" {
			v.URL = domainNameFilter(v.URL, conf)
		}
		if len(v.URL) > 10 {
			re := regexp.MustCompile("://([a-z0-9\\-]+\\.)*([a-z0-9\\-]+\\.[a-z0-9\\-]+)(:[0-9]+)?")
			hosts := re.FindAllString(v.URL, 1)
			if len(hosts) != 0 {
				// 遍历数组元素,判断此元素是否已经存在map中
				_, ok := temp[v.URL]
				if !ok {
					v.URL = strings.Replace(v.URL, "/./", "/", -1)
					list2 = append(list2, v)
					temp[v.URL] = true
				}
			}
		}
		index++

	}
	return list2
}

func RemoveRepeatAPI(list []structs.Link) []structs.Link {
	// 创建一个临时map用来存储数组元素
	temp := make(map[string]bool)
	var list2 []structs.Link
	for _, v := range list {
		// 遍历数组元素,判断此元素是否已经存在map中
		_, ok := temp[v.API]
		if !ok {
			list2 = append(list2, v)
			temp[v.API] = true
		}
	}
	return list2
}

// 处理-d
func domainNameFilter(url string, conf *structs.FetchConfig) string {
	re := regexp.MustCompile("://([a-z0-9\\-]+\\.)*([a-z0-9\\-]+\\.[a-z0-9\\-]+)(:[0-9]+)?")
	hosts := re.FindAllString(url, 1)
	if len(hosts) != 0 {
		if !regexp.MustCompile(conf.Domain).MatchString(hosts[0]) {
			url = ""
		}
	}
	return url
}

// UniqueArr 数组去重
func UniqueArr(arr []string) []string {
	newArr := make([]string, 0)
	tempArr := make(map[string]bool, len(newArr))
	for _, v := range arr {
		if tempArr[v] == false {
			tempArr[v] = true
			newArr = append(newArr, v)
		}
	}
	return newArr
}

// GetDomain 提取域名
func GetDomain(lis string) string {
	re := regexp.MustCompile("([a-z0-9\\-]+\\.)*([a-z0-9\\-]+\\.[a-z0-9\\-]+)(:[0-9]+)?")
	hosts := re.FindAllString(lis, 1)
	return hosts[0]
}

// PathExtract 提取fuzz的目录结构
func PathExtract(urls []string, conf *structs.FetchConfig) ([]string, []string) {
	var catalogues []string
	var targets []string
	if len(urls) == 0 {
		return nil, nil
	}
	par, _ := url.Parse(urls[0])
	host := par.Scheme + "://" + par.Host
	for _, v := range urls {
		parse, _ := url.Parse(v)
		catalogue := regexp.MustCompile("([^/]+?)/").FindAllStringSubmatch(parse.Path, -1)
		if !strings.HasSuffix(parse.Path, "/") {
			target := regexp.MustCompile(".*/([^/]+)").FindAllStringSubmatch(parse.Path, -1)
			if len(target) > 0 {
				targets = append(targets, target[0][1])
			}
		}
		for _, v := range catalogue {
			if !strings.Contains(v[1], "..") {
				catalogues = append(catalogues, v[1])
			}
		}

	}
	targets = append(targets, "upload")
	catalogues = UniqueArr(catalogues)
	targets = UniqueArr(targets)
	url1 := catalogues
	url2 := []string{}
	url3 := []string{}
	var path []string
	for _, v1 := range url1 {
		for _, v2 := range url1 {
			if !strings.Contains(v2, v1) {
				url2 = append(url2, "/"+v2+"/"+v1)
			}
		}
	}
	if conf.Fuzz == 3 {
		for _, v1 := range url1 {
			for _, v3 := range url2 {
				if !strings.Contains(v3, v1) {
					url3 = append(url3, v3+"/"+v1)
				}
			}
		}
	}
	for i := range url1 {
		url1[i] = "/" + url1[i]
	}
	if conf.Fuzz == 3 {
		path = make([]string, len(url1)+len(url2)+len(url3))
	} else {
		path = make([]string, len(url1)+len(url2))
	}
	copy(path, url1)
	copy(path[len(url1):], url2)
	if conf.Fuzz == 3 {
		copy(path[len(url1)+len(url2):], url3)
	}
	for i := range path {
		path[i] = host + path[i] + "/"
	}
	path = append(path, host+"/")
	return path, targets
}

// Del404 去除状态码非404的404链接
func Del404(urls []structs.Link) []structs.Link {
	is := make(map[int]int)
	//根据长度分别存放
	for _, v := range urls {
		arr, ok := is[v.Body.Size]
		if ok {
			is[v.Body.Size] = arr + 1
		} else {
			is[v.Body.Size] = 1
		}
	}
	var res []structs.Link
	//如果某个长度的数量大于全部的3分之2,那么就判定它是404页面
	for i, v := range is {
		if v > len(urls)/2 {
			for _, vv := range urls {
				if vv.Body.Size != i {
					res = append(res, vv)
				}
			}
		}
	}
	return res

}

var (
	// UA列表，每次请求随机选择UA
	uas = [...]string{
		"Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.68 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.5249.61 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.71 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.71 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.62 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.107 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.121 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.88 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.71 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.72 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.94 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.98 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.98 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.63 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.95 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.106 Safari/537.36",
		"Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.87 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.82 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36 Edg/99.0.1150.46",
		"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Safari/537.36 SE 2.Proxy MetaSr 1.0",
		"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.25 Safari/537.36 Core/1.70.3883.400 QQBrowser/10.8.4559.400",
	}
	nuas = len(uas)
)

// GetUserAgent 随机获取UserAgent
func GetUserAgent(conf *structs.FetchConfig) string {
	if conf.UserAgent == "" {
		conf.UserAgent = uas[rand.Intn(nuas)]
	}
	return conf.UserAgent
}

// FormatResult 格式化URL与API
func FormatResult(raw []structs.Link) []structs.Link {
	for i, n := range raw {
		var err error
		raw[i].API, err = ExtractAPIPath(n.URL)
		raw[i].API = strings.ToValidUTF8(raw[i].API, "")
		raw[i].URL = strings.ToValidUTF8(n.URL, "")
		if len(raw[i].URL) > 254 {
			raw[i].URL = raw[i].URL[:254]
		}
		if len(raw[i].API) > 254 {
			raw[i].API = raw[i].API[:254]
		}
		if err != nil {
			log.Warnf("api_analyze - 提取API失败: %v", err)
		}
	}
	return RemoveRepeatAPI(raw)
}

// ExtractAPIPath 提取API路径
func ExtractAPIPath(rawURL string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return "", fmt.Errorf("解析URL失败: %v", err)
	}

	// 获取路径部分
	path := parsedURL.Path

	return path, nil
}

func Contains(slice []string, str string) bool {
	for _, v := range slice {
		if v == str {
			return true
		}
	}
	return false
}

func Unique(input []string) []string {
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range input {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

// ConvertConfig 将Task转换为FetchConfig
func ConvertConfig(req *sql.Task) structs.FetchConfig {
	return structs.FetchConfig{
		TaskID:      req.Id,
		Mode:        3,
		StatusCode:  "all",
		TargetURL:   req.TargetURL,
		Domain:      req.Domain,
		Cookie:      "",
		UserAgent:   req.UserAgent,
		BaseURL:     "",
		Proxy:       "",
		Thread:      50,
		TimeOut:     10,
		MaxUrlCount: 5000,
		Fuzz:        req.Fuzz,
	}
}

// ConvertResult 将检测结果转换为ApiAnalyzerResult
func ConvertResult(ori []structs.DetectedWarningInfo, taskId int, typ int) []*sql.ApiAnalyzerResult {
	var result []*sql.ApiAnalyzerResult
	for _, i := range ori {
		temp := sql.ApiAnalyzerResult{
			TaskId:       taskId,
			RiskType:     typ,
			Url:          i.Url,
			Api:          i.Api,
			Tag:          i.Tag,
			Describe:     i.Desc,
			Code:         i.Code,
			Size:         i.Size,
			ResponseType: i.Type,
			Count:        i.Count,
			Matches:      strings.Join(i.Matches, ","),
		}
		result = append(result, &temp)
	}
	return result
}

func ApiJoin(api []structs.Link) string {
	var apiStr strings.Builder
	for _, v := range api {
		if v.API != "" {
			apiStr.WriteString(v.API)
			apiStr.WriteString(",")
		}
	}
	return strings.TrimSuffix(apiStr.String(), ",")
}

func UrlJoin(api []structs.Link) string {
	var apiStr strings.Builder
	for _, v := range api {
		apiStr.WriteString(v.URL)
		apiStr.WriteString(",")
	}
	return strings.TrimSuffix(apiStr.String(), ",")
}

type ExternalStorage struct {
	Url    []string `json:"url"`
	Domain []string `json:"domain"`
}

func ExternalFormat(api []structs.Link) string {
	var urls []string
	var domains []string
	// 提取URL和域名
	for _, v := range api {
		urls = append(urls, v.URL)
		domains = append(domains, GetDomain(v.URL))
	}
	// 去重
	urls = Unique(urls)
	domains = Unique(domains)
	// 转换为JSON
	marshal, err := json.Marshal(ExternalStorage{
		Url:    urls,
		Domain: domains,
	})
	if err != nil {
		log.Errorf("api_analyzer - 外部URL Marshal失败: %v", err)
	}
	return string(marshal)
}

func RemoveNullStringList(strs []string) []string {
	var result []string
	for _, str := range strs {
		str = strings.TrimSpace(str)
		if str != "" {
			result = append(result, str)
		}
	}
	return result
}

// IsInternalURL 检查URL是否为内网地址
func IsInternalURL(rawURL string) (bool, error) {
	// 解析URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return false, err
	}
	// 获取主机部分
	host := parsedURL.Host
	// 去除端口号
	if strings.Contains(host, ":") {
		host, _, err = net.SplitHostPort(host)
		if err != nil {
			return false, err
		}
	}
	// 解析主机为IP地址
	ip := net.ParseIP(host)
	if ip == nil {
		// 尝试解析为IP
		ips, err := net.LookupIP(host)
		if err != nil {
			return false, err
		}
		// 使用第一个IP地址
		ip = ips[0]
	}
	// 检查是否为内网地址
	return ip.IsPrivate(), nil
}
