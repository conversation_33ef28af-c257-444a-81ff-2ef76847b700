package api_analyzer

/*
	源码库版权声明，MIT协议
	This package is originally created by pingc0y.
	At https://github.com/pingc0y/URLFinder
*/

import (
	"context"
	"errors"
	"go-micro.dev/v4"
	"gorm.io/gorm"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	"micro-service/crawlerService/handler/api_analyzer/url_crawler"
	"micro-service/crawlerService/handler/api_analyzer/util"
	pb "micro-service/crawlerService/proto"
	"micro-service/initialize/mysql"
	sql "micro-service/middleware/mysql/api_analyze"
	"micro-service/middleware/mysql/user"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"time"
)

const ApiAnalyzerQueueName = "foradar.crawler.api_analyzer"

func GoAnalyze(conf structs.FetchConfig) error {
	err := url_crawler.Run(&conf)
	if err != nil {
		_ = sql.NewTaskModel().UpdateAny(conf.TaskID, map[string]any{"status": 3})
		return err
	}
	return nil
}

// CreateApiAnalyzeTask 创建分析任务
func CreateApiAnalyzeTask(ctx context.Context, req *pb.ApiAnalyzeTaskRequest, rsp *pb.ApiAnalyzeTaskResponse) error {
	switch req.ScanType {
	case 1:
		// TODO 从台账获取地址
		return nil
	case 2:
		return CreateAnalyzeTaskLink(ctx, req, rsp)
	default:
		return errors.New("scan_type无效")
	}
}

func CreateAnalyzeTaskLink(ctx context.Context, req *pb.ApiAnalyzeTaskRequest, rsp *pb.ApiAnalyzeTaskResponse) error {
	// 初始化&保存任务
	var taskList []*sql.Task
	if req.TaskId == 0 {
		// 检查参数
		req.Url = util.RemoveNullStringList(req.Url)
		if len(req.Url) == 0 {
			return errors.New("url不能为空")
		}
		for _, u := range req.Url {
			ok, err := util.IsInternalURL(u)
			if ok && err != nil {
				return errors.New("url列表包含内网IP地址")
			}
		}
		start := time.Now()
		u, err := user.NewUserModel(mysql.GetInstance()).FindById(req.OperatorId)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("未查询到用户信息")
		}
		if err != nil {
			return err
		}
		// 创建用户任务
		userTask := sql.UserTask{
			UserId:       req.UserId,
			Name:         req.Name,
			OperatorName: u.Name.String,
			SubTaskNum:   int64(len(req.Url)),
			TotalRisks:   0,
			Progress:     0,
			Status:       0,
			StartTime:    &start,
			EndTime:      nil,
		}
		err = sql.NewUserTaskModel().Create(&userTask)
		if err != nil {
			log.Errorf("api_analyze - 创建任务失败: %v", err)
			return err
		}
		// 创建系统任务
		for _, url := range util.UniqueArr(req.Url) {
			task := sql.Task{
				UserId:    req.UserId,
				TargetURL: url,
				Domain:    req.Domain,
				UserAgent: req.UserAgent,
				Fuzz:      utils.If(req.Fuzzy, 1, 0),
				Progress:  0,
				Status:    0,
			}
			err = sql.NewTaskModel().Create(&task)
			if err != nil {
				log.Errorf("api_analyze - 创建任务失败: %v", err)
				return err
			}
			taskList = append(taskList, &task)
		}
		var relationList []*sql.Relation
		// 构建&保存任务关系
		for _, t := range taskList {
			relation := sql.Relation{UserTaskId: userTask.Id, TaskId: t.Id}
			relationList = append(relationList, &relation)
		}
		err = sql.NewResultModel().RelationSave(relationList)
		if err != nil {
			log.Errorf("api_analyze - 保存任务关系失败: %v", err)
			return err
		}
		rsp.TaskId = userTask.Id
		rsp.Status = userTask.Status
	} else {
		// 已有任务ID则重置进度
		tasks, err := sql.NewTaskModel().ListByUserTaskId(req.TaskId, req.UserId)
		if err != nil {
			return err
		}
		if len(tasks) == 0 {
			return errors.New("任务不存在")
		}
		update := make(map[string]any, 3)
		update["status"] = 0
		update["progress"] = 0
		for i, task := range tasks {
			err = sql.NewTaskModel().UpdateAny(task.Id, update)
			if err != nil {
				return nil
			}
			err = sql.NewResultModel().DeleteByTask(task.Id)
			if err != nil {
				return nil
			}
			taskList = append(taskList, &tasks[i])
		}
		update["start_time"] = time.Now()
		update["total_risks"] = 0
		err = sql.NewUserTaskModel().UpdateAny(req.TaskId, update)
		if err != nil {
			return err
		}
		rsp.TaskId = req.TaskId
		rsp.Status = 1
	}
	// 提交任务到消息队列
	for _, t := range taskList {
		err := micro.NewEvent(ApiAnalyzerQueueName, pb.GetMicroClient()).Publish(context.TODO(), util.ConvertConfig(t))
		if err != nil {
			log.Errorf("api_analyze - 提交任务到队列失败: %v", err)
			return err
		}
	}
	return nil
}
