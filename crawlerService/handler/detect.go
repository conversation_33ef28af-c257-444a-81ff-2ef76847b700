package handler

import (
	"crypto/tls"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/panjf2000/ants/v2"

	pb "micro-service/crawlerService/proto"
	"micro-service/pkg/utils"
)

func UrlOnlineDetect(req *pb.UrlDetectRequest, rsp *pb.UrlDetectResponse) error {
	urls := utils.ListDistinctNonZero(req.Urls)
	if len(urls) == 0 {
		return nil
	}

	syncMap := sync.Map{}
	wg := sync.WaitGroup{}

	poolNum := utils.If(len(urls)/20 > 100, 100, len(urls)/20)
	poolNum = utils.If(poolNum == 0, 1, poolNum)
	pool, _ := ants.NewPool(poolNum)
	defer pool.Release()

	c := NewCurl()
	for _, v := range urls {
		wg.Add(1)
		address := v
		_ = pool.Submit(func() {
			defer wg.Done()
			p := detectDef{
				address:    address,
				userAgent:  c.GetUserAgent(),
				skipVerify: req.SkipVerify,
				timeout:    time.Duration(req.Timeout) * time.Second,
			}
			result := detectByUrl(p)
			syncMap.Store(address, result)
		})
	}
	wg.Wait()

	for _, v := range urls {
		if value, ok := syncMap.Load(v); ok {
			rsp.Results = append(rsp.Results, value.(*pb.UrlDetectResult))
		}
	}
	return nil
}

type detectDef struct {
	address    string
	userAgent  string
	timeout    time.Duration
	skipVerify bool
}

func detectByUrl(param detectDef) *pb.UrlDetectResult {
	if param.address == "" {
		return &pb.UrlDetectResult{}
	}
	client := http.Client{
		Transport: &http.Transport{
			IdleConnTimeout:       param.timeout,
			MaxIdleConns:          100,
			TLSClientConfig:       &tls.Config{InsecureSkipVerify: param.skipVerify}, //nolint:gosec,gocritic
			TLSHandshakeTimeout:   param.timeout,
			ExpectContinueTimeout: param.timeout,
		},
		Timeout: param.timeout, // 超时时间
	}

	req, _ := http.NewRequest(http.MethodHead, param.address, nil) //nolint:gocritic
	req.Header.Add("User-Agent", param.userAgent)
	rsp, err := client.Do(req)

	result := &pb.UrlDetectResult{Url: param.address}
	if err != nil {
		return result
	}
	defer rsp.Body.Close()

	result.DetectSuccess = true
	result.StatusCode = uint64(rsp.StatusCode)
	return result
}

func DetectByIpPort(req *pb.DetectByIpPortRequest, rsp *pb.DetectByIpPortResponse) error {
	if len(req.IpPort) == 0 {
		return nil
	}

	if req.Timeout == 0 {
		req.Timeout = 3
	}

	syncMap := sync.Map{}
	wg := sync.WaitGroup{}

	poolNum := utils.If(len(req.IpPort)/20 > 100, 100, len(req.IpPort)/20)
	poolNum = utils.If(poolNum == 0, 1, poolNum)
	pool, _ := ants.NewPool(poolNum)
	defer pool.Release()

	for _, v := range req.IpPort {
		wg.Add(1)
		unit := v
		_ = pool.Submit(func() {
			defer wg.Done()
			result := detectByIpPort(req.Network, unit.Ip, unit.Port, time.Duration(req.Timeout)*time.Second)
			syncMap.Store(unit.Ip+"_"+unit.Port, result)
		})
	}
	wg.Wait()

	for _, v := range req.IpPort {
		if value, ok := syncMap.Load(v.Ip + "_" + v.Port); ok {
			v.IsOnline = value.(bool)
			rsp.Results = append(rsp.Results, v)
		}
	}
	return nil
}

func detectByIpPort(network, ip, port string, timeout time.Duration) bool {
	if network == "" {
		network = "tcp"
	}
	address := net.JoinHostPort(ip, port)
	conn, err := net.DialTimeout(network, address, timeout)
	if err != nil {
		return false
	}
	defer conn.Close()

	return conn != nil
}
