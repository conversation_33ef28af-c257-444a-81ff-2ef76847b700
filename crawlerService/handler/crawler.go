package handler

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"micro-service/crawlerService/chrome"
	"micro-service/crawlerService/handler/api_analyzer"
	crawler2 "micro-service/crawlerService/proto"
	"micro-service/middleware/redis"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"micro-service/pkg/utils"
	"regexp"
	"strings"
	"time"

	"github.com/antchfx/htmlquery"
	"github.com/spf13/cast"

	"github.com/chromedp/chromedp"
	"go-micro.dev/v4/errors"
)

type Crawler struct{}

// ChromeGet ChromeGet请求
func (c *Crawler) ChromeGet(ctx context.Context, req *crawler2.ChromeGetRequest, rsp *crawler2.ChromeGetResponse) error {
	// cacheKey := "chromeGet:" + utils.Md5Hash(req.Url)
	// 有缓存时取环境数据
	// if redis.GetCache(cacheKey, &rsp) {
	//	log.WithContextInfof(ctx, "ChromeGet: response cache ok cacheKey:%s,request: %v", cacheKey, req)
	//	return nil
	// }
	bro := chrome.GetBrowserInstance(req.Vpn, req.Proxy)
	// 关闭代理浏览器
	defer bro.CloseProxyBrowser()
	// 创建table页
	tab := bro.NewTab()
	// 关闭table页
	defer tab.Close()
	if req.Wait != 0 {
		tab.SetLoadTimeOut(int(req.Wait))
		tab.SetWaitTime(int((req.Wait + 1) * 1000))
	}
	// 跳转指定地址
	doc, err := tab.Navigate(req.Url)
	// 超时的重试一下
	if err != nil {
		if strings.Contains(err.Error(), "网站已超时") {
			doc, err = tab.Navigate(req.Url)
		}
	}
	// 失败退出
	if err != nil {
		log.Warnf(fmt.Sprintf("ChromeGet: Params:%+v,加载页面失败:%s", req, err.Error()))
		return fmt.Errorf("加载页面失败:%s", err.Error())
	}
	log.WithContextInfof(ctx, fmt.Sprintf("ChromeGet: Params:%+v,加载完成", req))
	doc.PrintLogCtx(ctx)
	// 获取html
	docHtml, sErr := tab.GetDocument()
	if sErr != nil {
		return sErr
	}
	if len(docHtml) != 0 {
		xpath, pErr := htmlquery.Parse(bytes.NewReader(docHtml))
		if pErr == nil {
			if titleNode := htmlquery.Find(xpath, "//title"); titleNode != nil {
				rsp.Title = htmlquery.InnerText(titleNode[0])
			}
		}
	}
	// 获取状态码
	rsp.Code = cast.ToUint64(doc.StatusCode)
	// 返回页面信息
	rsp.Body = base64.StdEncoding.EncodeToString(utils.GZipEncode(docHtml))
	// 写入缓存, 缓存3分钟
	// redis.SetCache(cacheKey, 3*60*time.Second, rsp)
	return nil
}

// Screenshot URL截图
func (c *Crawler) Screenshot(ctx context.Context, req *crawler2.ScreenshotRequest, rsp *crawler2.ScreenshotResponse) error {
	cacheKey := "screenshot:" + utils.Md5Hash(req.Url)
	// 有缓存时取环境数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "Screenshot: response cache ok cacheKey:%s,request: %v", cacheKey, req)
		return nil
	}
	bro := chrome.GetBrowserInstance(req.Vpn, req.Proxy)
	// 关闭代理浏览器
	defer bro.CloseProxyBrowser()
	// 创建table页
	tab := bro.NewTab()
	// 关闭table页
	defer tab.Close()
	if req.Wait != 0 {
		tab.SetLoadTimeOut(int(req.Wait))
		tab.SetWaitTime(int((req.Wait + 1) * 1000))
	}
	// 跳转指定地址
	doc, err := tab.Navigate(req.Url)
	// 超时的重试一下
	if err != nil {
		if strings.Contains(err.Error(), "网站已超时") {
			doc, err = tab.Navigate(req.Url)
		}
	}
	// 失败退出
	if err != nil {
		log.Warnf(fmt.Sprintf("Screenshot: Params:%+v,加载页面失败:%s", req, err.Error()))
		return fmt.Errorf("加载页面失败:%s", err.Error())
	}
	log.WithContextInfof(ctx, fmt.Sprintf("Screenshot: Params:%+v,加载完成", req))
	doc.PrintLogCtx(ctx)
	if req.Quality <= 0 {
		req.Quality = 40
	}
	// 截图
	image, sErr := tab.GetSnapShot(int(req.Quality))
	if sErr != nil {
		return sErr
	}
	// 返回图片信息
	rsp.Code = cast.ToInt32(doc.StatusCode)
	rsp.Url = doc.RespUrl
	rsp.Img = base64.StdEncoding.EncodeToString(utils.GZipEncode(*image))
	// 写入缓存,缓存3分钟
	redis.SetCache(cacheKey, 3*60*time.Second, rsp)
	return nil
}

// Icon ICON爬取
func (c *Crawler) Icon(ctx context.Context, req *crawler2.IconRequest, rsp *crawler2.IconResponse) error {
	cacheKey := "c:icon:" + utils.Md5Hash(req.Url)
	if req.Url == "" {
		return nil
	}
	// 有缓存时取缓存数据
	if redis.GetCache(cacheKey, &rsp) {
		log.WithContextInfof(ctx, "Icon: response cache ok cacheKey:%s,request: %v", cacheKey, req)
		return nil
	}
	bro := chrome.GetBrowserInstance(0, chrome.ProxyDisable)
	// 关闭代理浏览器
	defer bro.CloseProxyBrowser()
	// 创建table页
	tab := bro.NewTab()
	tab.DisableCrawlResource()
	// 关闭table页
	defer tab.Close()
	// 跳转指定地址
	doc, err := tab.Navigate(req.Url)
	// 超时的重试一下
	if err != nil {
		if strings.Contains(err.Error(), "网站已超时") {
			doc, err = tab.Navigate(req.Url)
		}
	}
	// 失败退出
	if err != nil {
		log.Warnf(fmt.Sprintf("Icon: Params:%+v,加载页面失败:%s", req, err.Error()))
		return fmt.Errorf("加载页面失败:%s", err.Error())
	}
	log.WithContextInfof(ctx, fmt.Sprintf("Icon: Params:%+v,加载完成", req))
	doc.PrintLogCtx(ctx)
	// 截图
	body, sErr := tab.GetDocument()
	if sErr != nil {
		return sErr
	}
	// 提取Favicon
	if tab.FaviconUrl != "" {
		// 从请求连接中提取
		rsp.Img, rsp.Hash = storage.SaveUrlMmh3(tab.FaviconUrl, "", true)
		// 写入缓存,缓存3分钟
		redis.SetCache(cacheKey, 10*time.Minute, rsp)
		// 有数据,直接返回
		if rsp.Img != "" && rsp.Hash != 0 {
			return nil
		}
	}
	// 提取Body中的Favicon
	hDoc, err := htmlquery.Parse(strings.NewReader(string(body)))
	if err != nil {
		return err
	}
	// 获取Link元素
	iconNode, _ := htmlquery.Query(hDoc, `//link[contains(@rel, "icon")]`)
	if iconNode == nil {
		iconNode, _ = htmlquery.Query(hDoc, `//link[contains(@href, ".ico")]`)
	}
	// 提取link中的ICON
	if iconNode != nil {
		linkStr := htmlquery.OutputHTML(iconNode, true)
		// 提取Base64,Ico
		if strings.Contains(linkStr, ";base64,") {
			// 跳过空数据
			if strings.Contains(linkStr, "data:;base64,=") {
				redis.SetCache(cacheKey, 10*time.Minute, rsp)
				return nil
			}
			str := strings.Split(linkStr, `href="`)
			if len(str) >= 1 {
				str = strings.Split(str[1], `"`)
				if len(str) != 0 {
					rsp.Img, rsp.Hash = storage.SaveUrlMmh3(str[0], "", true)
					// 写入缓存,缓存10分钟
					redis.SetCache(cacheKey, 10*time.Minute, rsp)
					if rsp.Img != "" && rsp.Hash != 0 {
						return nil
					}
				}
			}
		}
		// 提取正常Ico
		links := regexp.MustCompile(`<link\s.*?\bhref="(.*?\.(ico|png|jpeg|jpg|bmp|gif).*?)".*?>`).FindStringSubmatch(linkStr)
		if len(links) >= 1 {
			// 补充完整链接
			if !(strings.HasPrefix(links[1], "https://") || strings.HasPrefix(links[1], "http://")) {
				// 判断是否以//开始的域名地址
				if strings.HasPrefix(links[1], "//") {
					if strings.HasPrefix(doc.RespUrl, "https://") {
						links[1] = "https:" + links[1]
					} else {
						links[1] = "http:" + links[1]
					}
				} else {
					// 没有协议时,使用请求元素的地址
					links[1] = strings.TrimSuffix(doc.RespUrl, "/") + "/" + strings.TrimPrefix(links[1], "/")
				}
			}
			// 获取ICON
			rsp.Img, rsp.Hash = storage.SaveUrlMmh3(links[1], "", true)
			// 写入缓存,缓存10分钟
			redis.SetCache(cacheKey, 10*time.Minute, rsp)
			// 有数据,直接返回
			if rsp.Img != "" && rsp.Hash != 0 {
				return nil
			}
		}
	}
	// 获取ICON
	if !(strings.HasPrefix(req.Url, "https://") || strings.HasPrefix(req.Url, "http://")) {
		req.Url = "https://" + req.Url
	}
	rsp.Img, rsp.Hash = storage.SaveUrlMmh3(strings.TrimSuffix(req.Url, "/")+"/favicon.ico", "", true)
	// 写入缓存,缓存10分钟
	redis.SetCache(cacheKey, 10*time.Minute, rsp)
	return nil
}

func (c *Crawler) Get(ctx context.Context, req *crawler2.GetRequest, rsp *crawler2.GetResponse) error {
	reqHeaders := make(map[string]interface{})
	rsp.Headers = make(map[string]string)
	cacheKey := "crawler:p:" + utils.Md5Hash(req)
	for k, v := range req.Headers {
		reqHeaders[k] = v
	}
	if strings.EqualFold(req.Method, crawler2.MethodChromeGet) {
		//  有缓存时取环境数据
		if redis.GetCache("crawler:p:"+utils.Md5Hash(req), &rsp) {
			log.WithContextInfof(ctx, "Received Crawler.Get cacheKey:%s,request: %v", cacheKey, req)
			return nil
		}
		log.WithContextInfof(ctx, "Received Crawler.Get NotCache:%s,request: %v", cacheKey, req)
		// Chrome Get 请求
		cu := NewCurl()
		for _, script := range req.Scripts {
			cu.SetScript(script.JsCode, int(script.WaitTime))
		}
		for _, click := range req.Clicks {
			cu.SetClick(click.Selector, int(click.WaitTime), chromedp.NodeVisible)
		}
		if req.Timeout == 0 {
			req.Timeout = 10
		}
		log.WithContextInfof(ctx, "Received--GetByChrome-POST--1-开始 Crawler.Get NotCache:%s,request: %v", cacheKey, req)
		cu.GetByChrome(ctx, reqHeaders, req, rsp)
		log.WithContextInfof(ctx, "Received--GetByChrome-POST--1-结束 Crawler.Get NotCache:%s,request: %v", cacheKey, req)
		// 写入缓存,缓存3分钟
		if rsp.Code != 0 {
			redis.SetCache(cacheKey, 3*60*time.Second, rsp)
		}
	} else if strings.EqualFold(req.Method, crawler2.MethodCurlGet) || strings.EqualFold(req.Method, crawler2.MethodCurlPost) {
		// 有缓存时取环境数据
		if redis.GetCache("crawler:p:"+utils.Md5Hash(req), &rsp) {
			log.WithContextInfof(ctx, "Received Crawler.Get cacheKey:%s,request: %v", cacheKey, req)
			return nil
		}
		log.WithContextInfof(ctx, "Received Crawler.Get NotCache:%s,request: %v", cacheKey, req)
		// Curl 请求
		cu := NewCurl()
		if strings.EqualFold(req.Method, crawler2.MethodCurlPost) {
			log.WithContextInfof(ctx, "Received--GetByCurl-POST--1-开始 Crawler.Get NotCache:%s,request: %v", cacheKey, req)
			cu.GetByCurl(ctx, req, "POST", rsp)
			log.WithContextInfof(ctx, "Received--GetByCurl-POST--2-结束 Crawler.Get NotCache:%s,request: %v", cacheKey, req)
		} else {
			log.WithContextInfof(ctx, "Received--GetByCurl-GET--3-开始 Crawler.Get NotCache:%s,request: %v", cacheKey, req)
			cu.GetByCurl(ctx, req, "GET", rsp)
			log.WithContextInfof(ctx, "Received--GetByCurl-GET--4-结束 Crawler.Get NotCache:%s,request: %v", cacheKey, req)
		}
		// 写入缓存,缓存3分钟
		if rsp.Code != 0 {
			redis.SetCache(cacheKey, 3*time.Minute, rsp)
		}
	} else if strings.EqualFold(req.Method, crawler2.MethodChromePost) {
		return errors.New(crawler2.ServiceName, "Chrome 不支持POST请求!", 400)
	} else {
		return errors.New(crawler2.ServiceName, "未知的请求方式!", 400)
	}
	return nil
}

func (c *Crawler) UrlDetect(ctx context.Context, req *crawler2.UrlDetectRequest, rsp *crawler2.UrlDetectResponse) error {
	err := UrlOnlineDetect(req, rsp)
	if err != nil {
		return err
	}
	return nil
}

func (c *Crawler) DetectByIpPort(ctx context.Context, req *crawler2.DetectByIpPortRequest, rsp *crawler2.DetectByIpPortResponse) error {
	err := DetectByIpPort(req, rsp)
	if err != nil {
		return err
	}
	return nil
}

// CreateApiAnalyzeTask 创建分析任务
func (c *Crawler) CreateApiAnalyzeTask(ctx context.Context, req *crawler2.ApiAnalyzeTaskRequest, rsp *crawler2.ApiAnalyzeTaskResponse) error {
	err := api_analyzer.CreateApiAnalyzeTask(ctx, req, rsp)
	if err != nil {
		return err
	}
	return nil
}
