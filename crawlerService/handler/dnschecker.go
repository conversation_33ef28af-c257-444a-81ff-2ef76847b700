package handler

import (
	"context"
	"fmt"
	pb "micro-service/crawlerService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/chromedp/chromedp"
)

// CheckDNS 实现 DNS 检查的 RPC 方法
func (c *Crawler) CheckDNS(ctx context.Context, req *pb.DnsCheckRequest, rsp *pb.DnsCheckResponse) error {
	if req == nil || req.Domain == "" {
		return fmt.Errorf("域名不能为空")
	}

	ips, err := getDNSResults(req.Domain)
	if err != nil {
		return fmt.Errorf("DNS查询失败: %v", err)
	}

	rsp.Ips = ips
	return nil
}

func getDNSResults(domain string) ([]string, error) {
	// 创建新的浏览器实例
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"),
		chromedp.Flag("ignore-certificate-errors", true),
	)
	//  设置浏览器位置
	if exists, err := utils.PathExists("/headless-shell/headless-shell"); err == nil && exists {
		opts = append(opts, chromedp.ExecPath("/headless-shell/headless-shell"))
	} else {
		// 未找到时,使用配置文件路径
		if exists, err = utils.PathExists(cfg.LoadCommon().ChromePath); err == nil && exists {
			opts = append(opts, chromedp.ExecPath(cfg.LoadCommon().ChromePath))
		}
	}
	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()

	ctx, cancel := chromedp.NewContext(allocCtx)
	defer cancel()

	// 设置超时时间
	ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	ipMap := make(map[string]struct{})
	var htmlContent string

	err := chromedp.Run(ctx,
		chromedp.Navigate("https://dnschecker.org"),
		chromedp.WaitVisible(`#q`, chromedp.ByID),
		chromedp.SetValue(`#q`, domain, chromedp.ByID),
		chromedp.Click(`#s`, chromedp.ByID),
		chromedp.WaitVisible(`.main_page_dns_servers`, chromedp.ByQuery),
		chromedp.Sleep(2*time.Second),
		chromedp.OuterHTML(`.main_page_dns_servers`, &htmlContent, chromedp.ByQuery),
	)

	if err != nil {
		return nil, fmt.Errorf("DNS查询失败: %v", err)
	}

	// 解析IP地址
	ipRegex := regexp.MustCompile(`ip=([^"]+)`)
	matches := ipRegex.FindAllStringSubmatch(htmlContent, -1)
	ipMatcher := regexp.MustCompile(`\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b`)

	var results []string
	for _, match := range matches {
		if len(match) > 1 {
			ips := ipMatcher.FindAllString(match[1], -1)
			for _, ip := range ips {
				if validateIP(ip) {
					ipMap[ip] = struct{}{}
				}
			}
		}
	}

	// 转换结果为切片
	for ip := range ipMap {
		results = append(results, ip)
	}

	return results, nil
}

// validateIP IP地址验证函数
func validateIP(ip string) bool {
	if ip == "0.0.0.0" {
		return false
	}

	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}
	for i, part := range parts {
		if len(part) == 0 || len(part) > 3 {
			return false
		}
		num, err := strconv.Atoi(part)
		if err != nil || num < 0 || num > 255 {
			return false
		}
		if part[0] == '0' && len(part) > 1 {
			return false
		}
		if i == 0 {
			switch num {
			case 10:
				return false
			case 172:
				if len(parts) > 1 {
					secondOctet, _ := strconv.Atoi(parts[1])
					if secondOctet >= 16 && secondOctet <= 31 {
						return false
					}
				}
			case 192:
				if len(parts) > 1 && parts[1] == "168" {
					return false
				}
			case 127:
				return false
			}
		}
	}
	return true
}
