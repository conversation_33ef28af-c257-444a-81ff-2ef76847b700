syntax = "proto3";

package crawler;

option go_package = "./proto;crawler";

service Crawler {
	rpc Get(GetRequest) returns (GetResponse) {}
	rpc UrlDetect(UrlDetectRequest) returns (UrlDetectResponse) {}
	rpc DetectByIpPort(DetectByIpPortRequest) returns (DetectByIpPortResponse) {}
	// 截图
	rpc Screenshot(ScreenshotRequest) returns (ScreenshotResponse) {}
	// ChromeGet请求
	rpc ChromeGet(ChromeGetRequest) returns (ChromeGetResponse) {}
	// ICON爬取
	rpc Icon(IconRequest) returns (IconResponse) {}
	// DNS检查
	rpc CheckDNS(DnsCheckRequest) returns (DnsCheckResponse) {}
	// API Analyze
	rpc CreateApiAnalyzeTask(ApiAnalyzeTaskRequest) returns (ApiAnalyzeTaskResponse) {}
}

message IconRequest {
	string url = 1; // @tag form:"url" validate:"required" zh:"网址"
}

message IconResponse {
	string img = 1;
	int64 hash = 2;
}

message ChromeGetRequest {
	string url = 1; // @tag form:"url" validate:"required" zh:"请求地址"
	int32 vpn = 3;// @tag form:"quality" validate:"omitempty,number,oneof=0 1" zh:"VPN"
	string proxy = 4; // @tag form:"proxy" validate:"omitempty" zh:"代理"
	uint64 wait = 5; // @tag form:"wait" validate:"omitempty" zh:"页面等待时间(/s)"
}

message ChromeGetResponse {
	string title = 1;
	uint64 code = 2;
	string body = 3;
}


message ScreenshotRequest {
	string url = 1; // @tag form:"url" validate:"required" zh:"截图地址"
	int64 quality = 2; // @tag form:"quality" validate:"omitempty,number,min=0,max=100" zh:"分辨率,0-100,默认40"
	int32 vpn = 3;// @tag form:"quality" validate:"omitempty,number,oneof=0 1" zh:"VPN"
	string proxy = 4; // @tag form:"proxy" validate:"omitempty" zh:"代理"
	uint64 wait = 5; // @tag form:"wait" validate:"omitempty" zh:"页面等待时间(/s)"
}

message ScreenshotResponse {
	string url = 1;
	int32  code = 2;
	string img = 3;
}

message Click{
	string selector = 1;
	int64 wait_time = 2;
}

message Script{
	string js_code = 1;
	int64 wait_time = 2;
}

message GetRequest {
	string url = 1;
	int64 timeout = 2;
	repeated Script scripts = 3;
	repeated Click clicks = 4;
	string method = 5;
	map<string,string> headers = 8;
	map<string,string> options = 9;
	int64 screenshot = 10;
	string body = 11;
	int64 wait_time = 12;
	int64 vpn = 13;
}

message GetResponse {
	int64 code = 1;
	map<string,string> headers = 2;
	string body = 3;
	repeated string script_res = 4;
	string favicon = 5;
	string screenshot = 6;
	string url30x = 7;
}

message UrlDetectRequest {
	repeated string urls = 1;
	int64 timeout = 2;
	bool skip_verify = 3;
}

message UrlDetectResult {
	string url = 1;
	uint64 status_code = 2;
	bool detect_success = 3;
}

message UrlDetectResponse {
	repeated UrlDetectResult results = 1;
}

message IpPort {
	string ip = 1;
	string port = 2;
	bool is_online = 3;
}

message DetectByIpPortRequest {
	string network = 1;
	repeated IpPort ip_port = 2;
	int64 timeout = 3;
}

message DetectByIpPortResponse {
	repeated IpPort results = 1;
}

// API分析任务

message ApiAnalyzeTaskRequest {
	uint64 user_id = 1; // @tag form:"user_id" validate:"required" zh:"用户ID"
	uint64 operator_id = 2; // @tag form:"operator_id" validate:"required" zh:"操作人ID"
	repeated string url = 3; // @tag form:"url" validate:"required" zh:"请求地址"
	string domain = 4; // @tag form:"domain" validate:"omitempty" zh:"域名"
	string user_agent = 5; // @tag form:"user_agent" validate:"omitempty" zh:"UserAgent"
	bool fuzzy = 6; // @tag form:"fuzzy" validate:"omitempty" zh:"模糊测试"
	uint64 task_id = 7; // @tag form:"task_id" validate:"omitempty" zh:"任务ID"
	string name = 8; // @tag form:"name" validate:"omitempty" zh:"任务名称"
	int64 scan_type = 9; // @tag form:"scan_type" validate:"omitempty" zh:"扫描类型"
}

message ApiAnalyzeTaskResponse {
	uint64 task_id = 1; // @tag form:"task_id" validate:"required" zh:"任务ID"
	int64 status = 2; // @tag form:"status" validate:"omitempty" zh:"任务状态"
}

message DnsCheckRequest {
	string domain = 1; // @tag form:"domain" validate:"required" zh:"域名"
}

message DnsCheckResponse {
	repeated string ips = 1; // @tag form:"ips" zh:"IP地址列表"
}