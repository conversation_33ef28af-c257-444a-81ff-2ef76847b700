package ssr

import (
	"fmt"
	zlog "micro-service/pkg/log"
	"time"

	"github.com/doorbash/bridge/adapter/outbound"
	"github.com/nadoo/glider/log"
	"github.com/nadoo/glider/proxy"
	"github.com/nadoo/glider/proxy/mixed"
)

type SSRClient struct {
	mixedServer proxy.Server
}

func (s *SSRClient) ListenAndServe() {
	s.mixedServer.ListenAndServe()
}

func NewSSRClient(
	serverAddr string,
	serverPort int,
	localAddr string,
	localPort int,
	password string,
	method string,
	obfs string,
	obfsParam string,
	protocol string,
	protocolParam string,
	socketTimeout time.Duration,
) (*SSRClient, error) {
	log.F = zlog.Debugf
	// SSR
	ssrNode := map[string]interface{}{
		"name":           "ssr",
		"type":           "ssr",
		"server":         serverAddr,
		"port":           serverPort,
		"password":       password,
		"cipher":         method,
		"obfs":           obfs,
		"obfs-param":     obfsParam,
		"protocol":       protocol,
		"protocol-param": protocolParam,
		"udp":            true,
	}
	p, _ := outbound.ParseProxy(ssrNode)

	pr, err := NewProxyDialer(p, socketTimeout)
	if err != nil {
		panic(err)
	}
	// Client
	client := &SSRClient{}
	client.mixedServer, err = mixed.NewMixedServer(fmt.Sprintf("mixed://%s:%d", localAddr, localPort), &SSRProxy{
		dialer: pr,
	})
	return client, err
}
