package brokers

import (
	"encoding/json"
	"github.com/go-micro/plugins/v4/broker/rabbitmq"
	"go-micro.dev/v4/broker"
	"go-micro.dev/v4/server"
	"micro-service/crawlerService/handler/api_analyzer"
	"micro-service/crawlerService/handler/api_analyzer/structs"
	sql "micro-service/middleware/mysql/api_analyze"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"strings"
)

var serverInst server.Server

const ApiAnalyzerQueueName = "foradar.crawler.api_analyzer" // 队列名称
const CrawlerExchangerName = "foradar.crawler.exchange"     // 队列名称

func SetServerInst(srv server.Server) {
	serverInst = srv
}

func NewCrawlerBroker() broker.Broker {
	mqConfig := cfg.LoadRabbitMq()
	// 创建 RabbitMQ Broker
	return rabbitmq.NewBroker(
		broker.Addrs(cfg.GetRabbitMqAddr(mqConfig)),           // RabbitMQ访问地址，含VHost
		rabbitmq.ExchangeName(CrawlerExchangerName),           // 交换机的名称
		rabbitmq.DurableExchange(),                            // 消息在Exchange中时会进行持久化处理
		rabbitmq.PrefetchCount(mqConfig.PrefetchCount),        // 使用配置的预取数量，提高并发性能
	)
}

func RegisterApiAnalyzeSubscriber() error {
	_, err := serverInst.Options().Broker.Subscribe(ApiAnalyzerQueueName, HandleApiAnalyze,
		broker.Queue(ApiAnalyzerQueueName), // 队列名称
		rabbitmq.DurableQueue(),            // 队列持久化，消费者断开连接后，消息仍然保存到队列中
		rabbitmq.RequeueOnError(),          // 消息处理函数返回error时，消息再次入队列
		broker.DisableAutoAck(),            // 手动发送Ack给mq
	)
	if err != nil {
		log.Errorf("api_analyze - 消费者注册失败: %v", err)
		return err
	}
	log.Infof("api_analyze - 消费者注册成功")
	return nil
}

func HandleApiAnalyze(msg broker.Event) error {
	log.Infof("api_analyze - 收到任务消息 %s", msg.Message().Body)
	var tsk structs.FetchConfig
	// 解析消息体
	if err := json.Unmarshal(msg.Message().Body, &tsk); err != nil || strings.TrimSpace(tsk.TargetURL) == "" {
		log.Errorf("api_analyze - Task %d 消息解析失败，已确认无效消息: %v", tsk.TaskID, err)
		if tsk.TaskID != 0 {
			_ = sql.NewTaskModel().UpdateAny(tsk.TaskID, map[string]any{"status": 3})
		}
		// 确认消息（避免无效消息阻塞队列）
		_ = msg.Ack()
		return nil
	}
	// 更新进度
	update := make(map[string]any, 3)
	update["status"] = 1
	update["progress"] = 1
	_ = sql.NewTaskModel().UpdateAny(tsk.TaskID, update)
	_ = sql.NewUserTaskModel().UpdateStatus(tsk.TaskID, 1)
	// 同步执行任务并获取错误
	var err error
	for i := 0; i < 3; i++ {
		err = api_analyzer.GoAnalyze(tsk)
		if err == nil {
			break
		}
		log.Errorf("Retry %d - api_analyze - Task %d 处理失败: %v", i, tsk.TaskID, err)
	}
	if err != nil {
		// 更新任务状态
		if err = sql.NewTaskModel().UpdateAny(tsk.TaskID, map[string]any{"status": 3}); err != nil {
			log.Errorf("api_analyze - Task %d 更新任务状态失败: %v", tsk.TaskID, err)
		}
		if err = sql.NewUserTaskModel().UpdateProcess(tsk.TaskID); err != nil {
			log.Errorf("api_analyze - Task %d 更新任务状态失败: %v", tsk.TaskID, err)
		}
	}
	// 手动确认消息
	if err = msg.Ack(); err != nil {
		log.Errorf("api_analyze - Task %d 消息确认失败: %v", tsk.TaskID, err)
		return err
	}
	return nil
}
