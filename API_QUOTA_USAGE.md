# API配额扣费系统使用说明

## 概述

本系统为本地化客户端提供API调用次数的配额管理和扣费功能。系统支持：

1. 灵活的API扣费倍率配置
2. 高性能的Redis缓存机制
3. 批量数据库更新减少性能影响
4. 自动禁用配额耗尽的客户端

## 数据库表结构

### 1. auth_access_clients 表新增字段

```sql
ALTER TABLE `auth_access_clients` ADD COLUMN `api_quota_remaining` int(11) unsigned NOT NULL DEFAULT '5000000' COMMENT 'API剩余次数';
```

### 2. api_quota_config 配置表

```sql
CREATE TABLE `api_quota_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `api_path` varchar(255) NOT NULL COMMENT 'API路径（支持通配符）',
  `method` varchar(10) NOT NULL DEFAULT '*' COMMENT 'HTTP方法（GET/POST/*）',
  `cost_multiplier` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '扣费倍率',
  `description` varchar(500) DEFAULT NULL COMMENT '配置说明',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1启用，2禁用',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_api_path` (`api_path`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API配额扣费配置表';
```

## 配置示例

### 主要API扣费倍率配置

```sql
-- 所有API统一使用1倍扣费倍率
('/api/v1/hunter/query', 'POST', 1, 'Hunter查询'),
('/api/v1/qcc/investment_through/:search', 'GET', 1, '企查查投资穿透'),
('/api/v1/beian/company/:company_name', 'GET', 1, '备案企业查询'),
('/api/v1/beian/domain/:domain', 'GET', 1, '备案域名查询'),
('/api/v1/beian/icp/:icp', 'GET', 1, '备案ICP查询'),
('/api/v1/qcc/basic_detail/:search', 'GET', 1, '企查查基本详情'),
('/api/v1/fofa/query', 'POST', 1, 'FOFA查询'),
('/api/v1/clue/expand/*', 'POST', 1, '线索扩展类API'),
('/api/v1/fofa/puredns', 'GET', 1, 'FOFA纯DNS查询'),
('/api/v1/dnschecker/:domain', 'GET', 1, 'DNS检查'),
('/api/v1/crawler/screenshot', 'POST', 1, '网页截图'),
('*', '*', 1, '默认扣费倍率');
```

## 当前扣费倍率详细说明

### 💡 统一标准API（1倍）
- **Hunter查询** (`/api/v1/hunter/query`): 1倍
- **企查查投资穿透** (`/api/v1/qcc/investment_through/:search`): 1倍
- **备案企业查询** (`/api/v1/beian/company/:company_name`): 1倍
- **备案域名查询** (`/api/v1/beian/domain/:domain`): 1倍
- **备案ICP查询** (`/api/v1/beian/icp/:icp`): 1倍
- **企查查基本详情** (`/api/v1/qcc/basic_detail/:search`): 1倍
- **FOFA查询** (`/api/v1/fofa/query`): 1倍 - 高价值网络空间数据查询
- **线索扩展-证书** (`/api/v1/clue/expand/cert`): 1倍
- **线索扩展-企业** (`/api/v1/clue/expand/company`): 1倍
- **线索扩展-域名** (`/api/v1/clue/expand/domain`): 1倍
- **线索扩展-ICP** (`/api/v1/clue/expand/icp`): 1倍
- **线索扩展-IP** (`/api/v1/clue/expand/ip`): 1倍
- **线索扩展-子域名** (`/api/v1/clue/expand/subdomain`): 1倍
- **FOFA纯DNS查询** (`/api/v1/fofa/puredns`): 1倍
- **所有DLP检测API**: 1倍（包括百度、GitHub、Gitee等各平台）
- **数字资产检测API**: 1倍（安卓应用、小程序、微信等）
- **查询结果类API**: 1倍（各种任务状态和结果查询）
- **其他工具API**: 1倍（域名搜索、IP138、Whois等）
- **DNS检查** (`/api/v1/dnschecker/:domain`): 1倍
- **网页截图** (`/api/v1/crawler/screenshot`): 1倍
- **默认规则**: 1倍（未明确配置的API）

### 配置规则说明

1. **api_path**: 支持多种匹配模式
   - `/api/v1/fofa/query` - 精确匹配
   - `/api/v1/beian/company/:company_name` - 路径参数匹配
   - `/api/v1/search/*` - 通配符匹配所有搜索相关API
   - `*` - 匹配所有API（作为默认规则）

2. **method**: HTTP方法
   - `GET`, `POST`, `PUT`, `DELETE` - 具体方法
   - `*` - 匹配所有方法

3. **cost_multiplier**: 扣费倍率
   - `1` - 每次调用扣1次（所有API统一使用1倍扣费）

## 系统工作流程

### 1. API调用流程

```
API请求 → recordApiHistory() → 异步扣费 → 更新Redis缓存 → 批量更新数据库
```

### 2. 扣费逻辑

1. **获取扣费倍率**: 从 `api_quota_config` 表查询匹配的配置
2. **检查客户端**: 验证 `scope != "*"` 的客户端才需要扣费
3. **缓存操作**: 使用Redis缓存减少数据库压力
4. **批量更新**: 累计到阈值后批量更新数据库
5. **自动禁用**: 配额不足时自动禁用客户端

### 3. 性能优化

- **异步处理**: 扣费操作不影响API响应速度
- **Redis缓存**: 减少数据库查询压力
- **批量更新**: 减少数据库写入频率
- **计数器机制**: 累计扣费次数达到阈值才更新数据库
- **配置缓存**: API扣费倍率配置缓存1小时，大幅减少数据库查询

## 使用方法

### 1. 运行数据库迁移

```bash
# 创建配置表并导入所有API配置数据
go run cliService/databases/2025_07_04_create_api_quota_config_table.go

# 添加配额字段
go run cliService/databases/2025_07_04_alter_auth_access_clients_add_quota.go
```

### 2. 配置API扣费规则

```sql
-- 添加新的扣费规则
INSERT INTO `api_quota_config` (`api_path`, `method`, `cost_multiplier`, `description`, `status`)
VALUES ('/api/v1/new/endpoint', 'POST', 10, '新API接口扣费10倍', 1);

-- 更新现有规则（例如调整FOFA查询倍率）
UPDATE `api_quota_config` SET `cost_multiplier` = 50 WHERE `api_path` = '/api/v1/fofa/query';

-- 禁用某个规则
UPDATE `api_quota_config` SET `status` = 2 WHERE `id` = 1;

-- 更新配置后清除缓存（可选，系统会自动清除）
-- 清除指定API缓存: 在Go代码中调用 ClearSpecificCache
-- 清除所有缓存: 在Go代码中调用 ClearAllCache
```

### 3. 监控配额使用情况

```sql
-- 查看客户端剩余配额
SELECT client_id, company_name, api_quota_remaining, status 
FROM auth_access_clients 
WHERE scope != '*' 
ORDER BY api_quota_remaining ASC;

-- 查看配额不足的客户端
SELECT client_id, company_name, api_quota_remaining 
FROM auth_access_clients 
WHERE api_quota_remaining < 1000 AND scope != '*';
```

## 注意事项

1. **scope字段**: 只有 `scope != "*"` 的客户端才会被扣费
2. **默认配额**: 新客户端默认500万次调用配额
3. **默认倍率**: 如果API路径在配置表中找不到匹配规则，默认使用1倍率扣费
4. **路径匹配优先级**: 精确匹配 > 路径参数匹配 > 通配符匹配 > 默认规则
5. **高消耗API注意**: FOFA查询API扣费100倍，请谨慎使用，建议客户端做好频率控制
6. **自动禁用**: 配额耗尽时客户端状态自动设为0（禁用）
7. **缓存一致性**:
   - 配额缓存5分钟过期，确保数据一致性
   - 配置缓存1小时过期，配置变更时自动清除
8. **批量阈值**: 累计扣费10次后批量更新数据库

## 故障排查

### 1. 查看扣费日志

```bash
# 查看扣费成功日志
grep "ApiQuota.*扣费成功" /path/to/logs

# 查看扣费失败日志
grep "ApiQuota.*扣费失败" /path/to/logs

# 查看客户端禁用日志
grep "客户端配额耗尽" /path/to/logs
```

### 2. 检查Redis缓存

```bash
# 查看客户端配额缓存
redis-cli GET "api_quota:your_client_id"

# 查看扣费计数器
redis-cli GET "api_quota_counter:your_client_id"

# 查看API配置缓存
redis-cli GET "api_cost_multiplier:/api/v1/fofa/query:POST"

# 查看所有配置缓存
redis-cli KEYS "api_cost_multiplier:*"

# 清除所有配置缓存
redis-cli DEL $(redis-cli KEYS "api_cost_multiplier:*")
```

### 3. 手动重置配额

```sql
-- 重置客户端配额
UPDATE auth_access_clients SET api_quota_remaining = 5000000 WHERE client_id = 'your_client_id';

-- 重新启用客户端
UPDATE auth_access_clients SET status = 1 WHERE client_id = 'your_client_id';
```
