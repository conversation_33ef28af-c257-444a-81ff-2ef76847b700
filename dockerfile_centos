# 基于centos7镜像，安装libpcap-devel
# 依赖于CentOS-Base.repo文件
FROM centos:centos7
RUN mv /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.bak
COPY ../CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo
COPY ../python3.12.10_rpms /tmp/rpms
RUN yum clean all && yum makecache && \
    yum -y install /tmp/rpms/*.rpm && \
    if [ ! -e /usr/bin/python3 ]; then ln -s /usr/bin/python3.12 /usr/bin/python3; fi && \
    if [ ! -e /usr/bin/pip ]; then ln -s /usr/bin/pip3 /usr/bin/pip; fi && \
    yum install -y libpcap-devel && \
    ln -snf /usr/lib64/libpcap.so.1.5.3 /usr/lib64/libpcap.so.0.8 && \
    yum clean all && \
    rm -rf /var/cache/yum /tmp/rpms