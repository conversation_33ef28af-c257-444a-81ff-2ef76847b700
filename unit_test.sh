#!/bin/bash

# 各微服务子目录，设为空串表示跑整个目录
api_service_dir=("")
web_service_dir=("")
cli_service_dir=("")
crawler_service_dir=("")
core_service_dir=("")
cron_service_dir=("")
scan_service_dir=("")
pkg_dir=("/microx")

DIR="micro-service"
root_dir=""
service_dir=()

if [[ $# -eq 0 ]]; then
  echo "Usage: $0 [api|web|cli|crawler|core|cron|scan|pkg]"
  exit 1
fi

function print_title() {
  echo -e "\e[44m========== FORADAR MICRO SERVICE UNIT TEST ==========\e[0m"
}

function get_pkg() {
  case "$1" in
    api)      service_dir=("${api_service_dir[@]}");      root_dir="$DIR/apiService";;
    web)      service_dir=("${web_service_dir[@]}");      root_dir="$DIR/webService";;
    cli)      service_dir=("${cli_service_dir[@]}");      root_dir="$DIR/cliService";;
    crawler)  service_dir=("${crawler_service_dir[@]}");  root_dir="$DIR/crawlerService";;
    core)     service_dir=("${core_service_dir[@]}");     root_dir="$DIR/coreService";;
    cron)     service_dir=("${cron_service_dir[@]}");     root_dir="$DIR/cronService";;
    scan)     service_dir=("${scan_service_dir[@]}");     root_dir="$DIR/scanService";;
    pkg)      service_dir=("${pkg_dir[@]}");              root_dir="$DIR/pkg";;
    *)        echo "Invalid package: $1"; exit 1;;
  esac
}

function unit_test_by_pkg() {
  go test "$1/..." -timeout 1m -v -gcflags=-l -cover p 4 -failfast
}

function run_tests() {
  print_title
  for item in "$@"; do
    get_pkg "$item"

    if [[ ${#service_dir[@]} -eq 0 ]]; then
      continue
    fi

    for dir in "${service_dir[@]}"; do
      full_path="$root_dir$dir"
      echo -e "\n\e[1;34m>>> Testing: $full_path\e[0m"
      if unit_test_by_pkg "$full_path"; then
        echo -e "\e[32m[PASS]\e[0m"
      else
        echo -e "\e[31m[FAIL]\e[0m"
      fi
    done
  done
}

function show_coverage_summary() {
  echo -e "\n\e[1;33m>>> Coverage Summary:\e[0m"

  awk_script='
    BEGIN {
      total_statements = 0;
      total_covered = 0;
    }
    /^[^:]+:[0-9]+\.[0-9]+,[0-9]+\.[0-9]+ [0-9]+ [0-9]+$/ {
      split($0, parts, " ");
      statements = parts[2];
      count = parts[3];
      total_statements += statements;
      if (count > 0) {
        total_covered += statements;
      }
    }
    END {
      if (total_statements > 0) {
        coverage = (total_covered / total_statements) * 100;
        printf "\n\033[1;36m>>> Total Coverage: %.1f%% (%d/%d)\033[0m\n", coverage, total_covered, total_statements;
      } else {
        print "No coverage data found.";
      }
    }
  '

  for item in "$@"; do
    get_pkg "$item"
    if [[ ${#service_dir[@]} -eq 0 ]]; then
      continue
    fi

    for dir in "${service_dir[@]}"; do
      full_path="$root_dir$dir"
      echo -e "\n\e[1;35m[$full_path]\e[0m"
      go list "$full_path/..." 2>/dev/null | while read pkg; do
        go test -covermode=set -coverpkg="$pkg" -coverprofile=/dev/stdout "$pkg" 2>/dev/null \
        | grep -E 'coverage:.* of statements'
      done
    done
  done | tee /dev/tty | awk "$awk_script"
}

# 主流程
run_tests "$@"
show_coverage_summary "$@"
